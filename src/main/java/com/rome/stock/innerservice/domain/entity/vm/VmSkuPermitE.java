package com.rome.stock.innerservice.domain.entity.vm;

/**
 * @Description: ScVmSkuPermitE
 * <p>
 * @Author: chuwenchao  2020/2/3
 */

import com.rome.stock.innerservice.domain.entity.BaseE;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Component;

/**
 * @Description: ChangeLogDO
 * <p>
 * @Author: chuwenchao  2019/10/12
 */
@Data
@Slf4j
@Component
@Scope("prototype")
@EqualsAndHashCode(callSuper = false)
public class VmSkuPermitE extends BaseE {
    //columns START
    /**
     * 唯一主键
     */
    private Long id;
    /**
     * 虚仓ID
     */
    private Long virtualWarehouseGroupId;
    /**
     * 商品ID
     */
    private Long skuId;
    /**
     * 商品编码
     */
    private String skuCode;
    /**
     * 虚仓是否有商品进货权 0:没权限 1:有权限
     */
    private Integer isPermit;

}
