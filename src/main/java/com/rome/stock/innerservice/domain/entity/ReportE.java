package com.rome.stock.innerservice.domain.entity;

import com.rome.arch.core.domain.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Component;

import java.util.Date;

/**
 * @Program: stock-core-service
 * @Description:
 * @Author: Cocoa
 * @Date: 2020/7/21 16:11
 * @Version: v1.0.0
 */

@Data
@Slf4j
@Component
@Scope("prototype")
@EqualsAndHashCode(callSuper = false)
public class ReportE extends BaseEntity {

    /**
     * 主键
     */
    private Long id;

    //创建时间
    private Date createTime;
    //更新时间
    private Date updateTime;
    //创建人
    private Long creator;
    //更新人
    private Long modifier;
    //是否可用 0-否，1-是
    private Byte isAvailable;
    //是否逻辑删除 0-否，1-是
    private Byte isDeleted;
    //数据版本号
    private Integer versionNo;
    //租户ID
    private Long tenantId;
    //业务应用ID
    private String appId;


}
