package com.rome.stock.innerservice.domain.entity;

import java.math.BigDecimal;
import java.util.Date;

import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Component;

import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * @Program: stock-core-service
 * @Description:
 * @Author: Cocoa
 * @Date: 2020/7/21 16:18
 * @Version: v1.0.0
 */
@Component
@Scope("prototype")
@Data
@EqualsAndHashCode(callSuper = false)
public class WmsStockReportE extends ReportE {

    // 工厂
    private String factory;
    // 物料
    private String materials;
    // 描述
    private String descrip;
    // 库存地点
    private String stockLocation;
    // 存储类型
    private String storageType;
    // 仓位
    private String stockPosition;
    // 库存类别
    private String stockType;
    // 数量
    private BigDecimal quantity;
    // 单位
    private String unit;
    // 仓库数量
    private BigDecimal warehouseNum;
    // 仓库单位
    private String warehouseUnit;
    // 标记日期
    private Date remarkDate;
    // 收货日期
    private Date acceptDate;
    // 生产日期
    private Date productionDate;
    // 最晚发货日期
    private Date latestDeliveryDate;
    // 不得销售日期
    private Date noSaleDate;
    // 最佳销售日期
    private Date bestSellingDate;
    // 库龄
    private Long storageAge;


}
