package com.rome.stock.innerservice.domain.entity.frontrecord;//package com.rome.stock.innerservice.domain.entity.frontrecord;
//
//import com.rome.stock.common.constants.front.WhAllocationConstant;
//import com.rome.stock.innerservice.common.AlikAssert;
//import com.rome.stock.innerservice.common.ResCode;
//import com.rome.stock.innerservice.common.SkuQtyUnitTools;
//import com.rome.stock.innerservice.constant.FrontRecordStatusVO;
//import com.rome.stock.innerservice.constant.FrontRecordTypeVO;
//import com.rome.stock.innerservice.domain.repository.frontrecord.FrWhAllocationRepository;
//import lombok.Data;
//import lombok.EqualsAndHashCode;
//import org.springframework.context.annotation.Scope;
//import org.springframework.stereotype.Component;
//
//import javax.annotation.Resource;
//import java.math.BigDecimal;
//import java.util.Date;
//import java.util.List;
//
///**
// * 类WhAllocationRecordE的实现描述：仓库调拨
// *
// * <AUTHOR> 2019/5/13 11:58
// */
//@Component
//@Scope("prototype")
//@Data
//@EqualsAndHashCode(callSuper = false)
//public class WhAllocationRecordE extends AbstractFrontRecord {
//
//    @Resource
//    private FrWhAllocationRepository frWhAllocationRepository;
//
//    @Resource
//    private SkuQtyUnitTools skuQtyUnitTools;
//
//    /**
//     * 生成仓库调拨单
//     */
//    public void addAllocationRecord(boolean isDisparity) {
//        this.setRecordType(FrontRecordTypeVO.WAREHOUSE_ALLOCATION_RECORD.getType());
//        if (null == this.getRecordStatus()) {
//            this.setRecordStatus(FrontRecordStatusVO.INIT.getStatus());
//        }
//        if (null == this.getIsDiffIn()) {
//            this.setIsDiffIn(NOT_DIFF_IN);
//        }
//        if (null == this.getSyncStatus()) {
//            this.setSyncStatus(WhAllocationConstant.NOT_NEED_SYNC_STATUS);
//        }
//        if (null == this.getWhType()) {
//            this.setWhType(WhAllocationConstant.WH_TYPE_INIT);
//        }
//        //单据生成
//        Long id = frWhAllocationRepository.addWhAllocationRecord(this);
//        this.setId(id);
//        this.frontRecordDetails.forEach(record -> record.setFrontRecordDetail(this) );
//        for (int j = 0; j < this.frontRecordDetails.size(); j++) {
//            WhAllocationRecordDetailE detailE = this.frontRecordDetails.get(j);
//            detailE.setFrontRecordId(id);
//            detailE.setInQty(BigDecimal.ZERO);
//            detailE.setOutQty(BigDecimal.ZERO);
//            if (isDisparity) {
//                //差异的单子的行号保持跟原单一致，方便更新原单
//                detailE.setOriginLineNo(detailE.getLineNo());
//            }
//            Integer lineNo = (j + 1) * 10;
//            detailE.setLineNo(String.format("%05d", lineNo));
//            detailE.setRecordCode(this.getRecordCode());
//        }
//        frWhAllocationRepository.saveWhAllocationDetail(this.frontRecordDetails);
//    }
//
//    /**
//     * 更新仓库调拨单
//     */
//    public void updateAllocationRecord() {
//        //单据生成
//        Integer updateNum = frWhAllocationRepository.updateFrWhAllocationById(this);
//        AlikAssert.isTrue(updateNum > 0, ResCode.STOCK_ERROR_1026, ResCode.STOCK_ERROR_1026_DESC);
//        this.frontRecordDetails.forEach(record -> record.setFrontRecordDetail(this) );
//        for (int j = 0; j < this.frontRecordDetails.size(); j++) {
//            WhAllocationRecordDetailE detailE = this.frontRecordDetails.get(j);
//            detailE.setFrontRecordId(this.getId());
//            detailE.setInQty(BigDecimal.ZERO);
//            detailE.setOutQty(BigDecimal.ZERO);
//            Integer lineNo = (j + 1) * 10;
//            detailE.setLineNo(String.format("%05d", lineNo));
//            detailE.setRecordCode(this.getRecordCode());
//        }
//        frWhAllocationRepository.saveWhAllocationDetail(this.frontRecordDetails);
//    }
//
//    /**
//     * 转化实出的数量
//     * @param frontRecord
//     */
//    public  void transformOutActualQty(){
//        frontRecordDetails.forEach(wrRecord -> wrRecord.setBasicSkuQty(wrRecord.getOutQty()));
//        skuQtyUnitTools.convertBasicToReal(frontRecordDetails);
//        for(WhAllocationRecordDetailE detailE : frontRecordDetails){
//            detailE.setOutQty(detailE.getSkuQty());
//        }
//    }
//
//    /**
//     * 转化实入的数量
//     * @param frontRecord
//     */
//    public  void transformInActualQty(){
//        frontRecordDetails.forEach(wrRecord -> wrRecord.setBasicSkuQty(wrRecord.getInQty()));
//        skuQtyUnitTools.convertBasicToReal(frontRecordDetails);
//        for(WhAllocationRecordDetailE detailE : frontRecordDetails){
//            detailE.setInQty(detailE.getSkuQty());
//        }
//    }
//
//
//    /**
//     * 差异入库
//     */
//    private final Integer DIFF_IN = 1;
//
//    /**
//     * 没有差异
//     */
//    private final Integer NOT_DIFF_IN = 0;
//
//    /**
//     * 单据类型：22: 调拨单
//     */
//    private Integer recordType;
//    /**
//     * 0-已新建，1-已确认  2.已取消  5-已派车 10.已出库 11.已入库
//     */
//    private Integer recordStatus;
//    /**
//     * 1.内部调拨 2.RDC调拨 3.退货调拨 4.电商仓调拨 4.质量问题调拨
//     */
//    private Integer businessType;
//    /**
//     * 是否差异入库：0-没有差异，1-有差异
//     */
//    private Integer isDiffIn;
//    /**
//     * 入向仓库id
//     */
//    private Long inWarehouseId;
//    /**
//     * 入向仓库联系人
//     */
//    private String inWarehouseName;
//    /**
//     * 入向联系电话
//     */
//    private String inWarehouseMobile;
//    /**
//     * 出向仓库id
//     */
//    private Long outWarehouseId;
//    /**
//     * 出向仓库联系人
//     */
//    private String outWarehouseName;
//    /**
//     * 出向仓库电话
//     */
//    private String outWarehouseMobile;
//    /**
//     * 调拨日期
//     */
//    private Date allotTime;
//    /**
//     * 预计到货日期
//     */
//    private Date expeAogTime;
//    /**
//     * 审核人
//     */
//    private Long auditor;
//    /**
//     * 审核时间
//     */
//    private Date auditTime;
//    /**
//     * 备注
//     */
//    private String remark;
//
//    /**
//     * 商品数量
//     */
//    private List<WhAllocationRecordDetailE> frontRecordDetails;
//
//    /**
//     * SAP下发状态：0-无需下发 1-待下发 2-已下发
//     */
//    private Integer syncStatus;
//
//    /**
//     * sap采购单号
//     */
//    private String sapPoNo;
//
//    /**
//     * 1.出入都是中台 2.出中台入非中台  3. 出非中台入中台
//     */
//    private Integer whType;
//
//    /**
//     * 是否退货调拨 1.是 0 不是
//     */
//    private Integer isReturnAllotcate;
//
//    /***
//     * 新增类型(1.页面新增 2. excel导入 3.差异调拨)
//     */
//    private Integer addType;
//
//    /***
//     * 是否存在差异(1. 存在差异 0: 不存在差异)
//     */
//    private Integer isDisparity;
//
//    /***
//     * 原始单据Id
//     */
//    private Long orginId;
//
//    /***
//     * 原始单据号
//     */
//    private String orginRecord;
//
//    /**
//     * 是否质量问题调拨 1.是 0 不是
//     */
//    private Integer isQualityAllotcate;
//}
