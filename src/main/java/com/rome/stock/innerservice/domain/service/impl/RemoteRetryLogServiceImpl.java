package com.rome.stock.innerservice.domain.service.impl;

import com.alibaba.fastjson.JSON;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.rome.arch.core.clientobject.Response;
import com.rome.stock.common.enums.log.RemoteRetryMethodType;
import com.rome.stock.innerservice.api.dto.RemoteInterfaceLogDTO;
import com.rome.stock.innerservice.api.dto.RemoteInterfaceLogQuery;
import com.rome.stock.innerservice.api.dto.RemoteInterfaceLogRetryDTO;
import com.rome.stock.innerservice.api.dto.RemoteInterfacePackageMaterialDTO;
import com.rome.stock.innerservice.common.AlikAssert;
import com.rome.stock.innerservice.common.StringUtils;
import com.rome.stock.innerservice.domain.repository.RemoteInterfaceLogRepository;
import com.rome.stock.innerservice.domain.service.RemoteRetryLogService;
import com.rome.stock.innerservice.infrastructure.dataobject.RemoteInterfaceLogDO;
import com.rome.stock.innerservice.remote.ResponseValidator;
import com.rome.stock.innerservice.remote.strategy.RemoteRetryHandler;
import com.rome.stock.innerservice.remote.strategy.RemoteStrategyRegister;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;

/**
 * @Description :
 * @Author: nyacc
 * @Date: 2022/1/19 17:32
 */

@Service
@Slf4j
public class RemoteRetryLogServiceImpl implements RemoteRetryLogService {

    @Resource
    private RemoteInterfaceLogRepository remoteInterfaceLogRepository;

    @Override
    public PageInfo<RemoteInterfaceLogDTO> getRemoteLogPageInfo(RemoteInterfaceLogQuery query) {
        if(StringUtils.isBlank(query.getMethod())){
            query.setMethod(null);
        }
        if(StringUtils.isBlank(query.getRecordCode())){
            query.setRecordCode(null);
        }
        PageInfo<RemoteInterfaceLogDTO> pageInfo = remoteInterfaceLogRepository.getRemoteInterfacePageInfo(query);
        if (pageInfo == null || CollectionUtils.isEmpty(pageInfo.getList())) {
            return pageInfo;
        }
        List<RemoteInterfaceLogDTO> list = pageInfo.getList();
        for (RemoteInterfaceLogDTO remoteInterfaceLogDTO : list) {
            remoteInterfaceLogDTO.setBizName(RemoteRetryMethodType.getNameByCode(remoteInterfaceLogDTO.getRequestService()));
        }
        return pageInfo;
    }

    @Override
    public List<Long> retry(RemoteInterfaceLogRetryDTO remoteInterfaceLogRetryDTO) {
        List<RemoteInterfaceLogDO> remoteInterfaceLogByIds = remoteInterfaceLogRepository.getRemoteInterfaceLogByIds(remoteInterfaceLogRetryDTO.getIds());
        if(CollectionUtils.isEmpty(remoteInterfaceLogByIds)){
            return null;
        }
        List<Long> resultList=new ArrayList<>();
        for (RemoteInterfaceLogDO remoteInterfaceLogById : remoteInterfaceLogByIds) {
            RemoteRetryHandler remoteRetryHandler = RemoteStrategyRegister.getRemoteRetryHandler(remoteInterfaceLogById.getRequestService());
            if(remoteRetryHandler == null){
                log.warn("id:{},method:{},无对应handler处理!!",remoteInterfaceLogById.getId(),remoteInterfaceLogById.getRequestService());
                continue;
            }
            Response response=remoteRetryHandler.doRetry(remoteInterfaceLogById);
            if(ResponseValidator.validResponse(response)){
                resultList.add(remoteInterfaceLogById.getId());
            }
            remoteInterfaceLogRepository.updateRemoteInterFaceLogStatus(remoteInterfaceLogById.getId(),ResponseValidator.validResponse(response)? 1: 0, JSON.toJSONString(response), remoteInterfaceLogRetryDTO.getModifier());
        }
        return resultList;
    }

    @Override
    public PageInfo<RemoteInterfacePackageMaterialDTO> expertPackageMaterialOut(RemoteInterfaceLogQuery query) {
        AlikAssert.notNull(query.getStartTime(), "1","开始时间不能为空");
        AlikAssert.notNull(query.getEndTime(), "1","结束时间不能为空");
        Page page = PageHelper.startPage(query.getPageNum(), query.getPageSize());
        List<RemoteInterfacePackageMaterialDTO> list = remoteInterfaceLogRepository.queryPackageMaterialOutList(query);
        for (RemoteInterfacePackageMaterialDTO item : list) {
            item.setFactoryCode(item.getFactoryCode().replace("\"", ""));
            item.setRealWarehouseOutCode(item.getRealWarehouseOutCode().replace("\"", ""));
            item.setSkuCode(item.getSkuCode().replace("\"", ""));
            if (StringUtils.isNotBlank(item.getErrorMsg())) {
                item.setErrorMsg(item.getErrorMsg().replace("\"", ""));
            }
            item.setRealWarehouseCode(item.getFactoryCode() + "-" + item.getRealWarehouseOutCode());
        }
        PageInfo<RemoteInterfacePackageMaterialDTO> pageList = new PageInfo(list);
        return pageList;
    }

    @Override
    public List<Long> getRetryIds(Long minId, Integer limit) {
        return remoteInterfaceLogRepository.getRetryIds(minId, limit);
    }
}
