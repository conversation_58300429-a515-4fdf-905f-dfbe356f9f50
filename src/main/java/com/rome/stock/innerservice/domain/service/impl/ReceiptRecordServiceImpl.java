package com.rome.stock.innerservice.domain.service.impl;

import com.rome.stock.innerservice.domain.entity.ReceiptRecordE;
import com.rome.stock.innerservice.domain.repository.ReceiptRecordRepository;
import com.rome.stock.innerservice.domain.service.ReceiptRecordService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * @Description: ReceiptRecordService
 * <p>
 * @Author: chuwenchao  2019/5/31
 */
@Slf4j
@Service
public class ReceiptRecordServiceImpl implements ReceiptRecordService {

    @Resource
    private ReceiptRecordRepository receiptRecordRepository;

    /**
     * @Description: 保存收货单
     * <br>
     * <AUTHOR> 2019/6/2
     * @param receiptRecordE
     * @return
     */
    @Override
    public int saveReceiptRecord(ReceiptRecordE receiptRecordE) {
        return receiptRecordRepository.saveReceiptRecord(receiptRecordE);
    }

    /**
     * 根据wmsRecordCode查询收货信息
     * @param recordCode
     * @return
     */
    @Override
    public ReceiptRecordE selectReceiptRecordBywmsRecordCode(String recordCode) {
        return receiptRecordRepository.selectReceiptRecordBywmsRecordCode(recordCode);
    }

}
