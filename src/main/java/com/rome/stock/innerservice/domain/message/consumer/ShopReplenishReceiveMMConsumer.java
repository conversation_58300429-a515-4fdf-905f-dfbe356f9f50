package com.rome.stock.innerservice.domain.message.consumer;

import com.alibaba.fastjson.JSONObject;
import com.rome.scm.common.rocketmq.CustomConsumerListener;
import com.rome.stock.common.enums.CustomRocketMQEnum;
import com.rome.stock.innerservice.api.dto.warehouserecord.ReceiveOrderStockForm;
import com.rome.stock.innerservice.remote.mm.facade.MmOrderIssuesFacade;
import lombok.extern.slf4j.Slf4j;
import org.apache.rocketmq.common.message.MessageExt;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;

/**
 * @Description: 收货结果通知MM推送MQ
 */
@Slf4j
@Service
public class ShopReplenishReceiveMMConsumer implements CustomConsumerListener<MessageExt> {

    @Resource
    private MmOrderIssuesFacade mmOrderIssuesFacade;

    @Override
    public void onMessage(MessageExt messageExt, String businessNo, String msgKey) {
        log.info("收货结果通知MM推送MQ消息消费，msgID: {}，消费次数: {}", messageExt.getMsgId(), messageExt.getReconsumeTimes());
        String json = new String(messageExt.getBody());
        if(StringUtils.isEmpty(json)) {
            log.error("收货结果通知MM推送MQ消息消费内容为空，msgID: {}", messageExt.getMsgId());
            return ;
        }
        ReceiveOrderStockForm receiveOrder= JSONObject.parseObject(json, ReceiveOrderStockForm.class);
        mmOrderIssuesFacade.stockReceive(receiveOrder);
    }

	@Override
	public String getRocketMQCode() {
		return CustomRocketMQEnum.MQ_STOCK_RECEIVE_MM_PUSH.getCode();
	}



}
