package com.rome.stock.innerservice.domain.service.impl;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import javax.annotation.Resource;

import com.rome.stock.common.utils.DateUtil;
import com.rome.stock.innerservice.domain.convertor.CmTransferLogConvertor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.rome.arch.core.exception.RomeException;
import com.rome.stock.common.constants.SAPConstants;
import com.rome.stock.innerservice.api.dto.RealWarehouse;
import com.rome.stock.innerservice.api.dto.cmtransferlog.CmTransferDetailExportDTO;
import com.rome.stock.innerservice.api.dto.cmtransferlog.CmTransferLogPageDTO;
import com.rome.stock.innerservice.common.AlikAssert;
import com.rome.stock.innerservice.common.ResCode;
import com.rome.stock.innerservice.constant.PostingAccountMappingVO;
import com.rome.stock.innerservice.common.RomeCollectionUtil;
import com.rome.stock.innerservice.domain.entity.warehouserecord.WarehouseRecordE;
import com.rome.stock.innerservice.domain.service.CmTransferLogService;
import com.rome.stock.innerservice.domain.service.RealWarehouseService;
import com.rome.stock.innerservice.infrastructure.dataobject.CmTransferDetailLogDO;
import com.rome.stock.innerservice.infrastructure.dataobject.CmTransferLogDO;
import com.rome.stock.innerservice.infrastructure.mapper.CmTransferDetailLogMapper;
import com.rome.stock.innerservice.infrastructure.mapper.CmTransferLogMapper;
import com.rome.stock.innerservice.remote.base.dto.StoreDTO;
import com.rome.stock.innerservice.remote.base.facade.ShopFacade;
import com.rome.stock.innerservice.remote.item.dto.SkuInfoExtDTO;
import com.rome.stock.innerservice.remote.item.dto.SkuSupplierDTO;
import com.rome.stock.innerservice.remote.item.facade.SkuFacade;
import com.rome.stock.innerservice.remote.sap.dto.OnlinePostingAccountDTO;
import com.rome.stock.innerservice.remote.sap.dto.ZMMTMDQH3010;
import com.rome.stock.innerservice.remote.sap.dto.cmTransfer301.ZMMTMDDB0010;
import com.rome.stock.innerservice.remote.sap.dto.cmTransfer301.ZMMTMDDB0020;
import com.rome.stock.innerservice.remote.sap.facade.SapFacade;

/**
 * 类CmTransferLogServiceImpl的实现描述：云商301接口过账日志实现
 *
 * <AUTHOR> 2020/11/5 4:37
 */
@Service
@Slf4j
public class CmTransferLogServiceImpl implements CmTransferLogService{

    @Resource
    private CmTransferLogMapper cmTransferLogMapper;

	@Resource
    private CmTransferDetailLogMapper cmTransferDetailLogMapper;
	@Resource
	private SapFacade sapFacade;
	@Resource
	private ShopFacade shopFacade;
	@Resource
	private SkuFacade skuFacade;
	@Resource
	private RealWarehouseService realWarehouseService;
	@Autowired
	private CmTransferLogConvertor cmTransferLogConvertor;


    @Override
    public void deleteLogByRecord(String recordCode) {
        AlikAssert.isNotBlank(recordCode, ResCode.STOCK_ERROR_9031, ResCode.STOCK_ERROR_9031_DESC);
        cmTransferLogMapper.deleteCmTransferLogByRecordCode(recordCode);
		cmTransferDetailLogMapper.deleteDetailByRecordCode(recordCode);
    }

    @Override
    public CmTransferLogDO getCmTransferByRecordCode(String recordCode) {
        return cmTransferLogMapper.getCmTransferByRecordCode(recordCode);
    }

	@Override
	public List<CmTransferLogDO> getCmTransferByPushDate(String guid) {
		return cmTransferLogMapper.getCmTransferByPushDate(guid);
	}

	@Override
	public void savePostAccountResult(ZMMTMDQH3010 logResult) {
		CmTransferLogDO log = cmTransferLogMapper.getCmTransferByRecordCode(logResult.getGuid());
		log.setTransferStatus(SAPConstants.SAP_SUCCESS.equals(logResult.getMsgty()) ? 1 : 2);
		log.setErrorMsg(JSON.toJSONString(logResult));
		cmTransferLogMapper.savePostAccountResult(log);
	}

    @Override
    public void saveCmTransferLog(String postingDate, String pushDate , List<OnlinePostingAccountDTO> detailList) {
        AlikAssert.isNotEmpty(detailList, ResCode.STOCK_ERROR_1019, ResCode.STOCK_ERROR_1019_DESC);
		Map<String, List<OnlinePostingAccountDTO>> map = new HashMap<>();
		//对数据进行分组
		for (OnlinePostingAccountDTO detailDO : detailList) {
			if (map.containsKey(detailDO.queryKey())) {
				map.get(detailDO.queryKey()).add(detailDO);
			} else {
				List<OnlinePostingAccountDTO> temp = new ArrayList<>();
				temp.add(detailDO);
				map.put(detailDO.queryKey(), temp);
			}
		}
		List<CmTransferDetailLogDO> list;
		for (Map.Entry<String ,List<OnlinePostingAccountDTO> > entry : map.entrySet()) {
			list = new ArrayList<>();
			CmTransferLogDO log = new CmTransferLogDO();
			log.setRecordCode(postingDate + "_" + entry.getKey());
			log.setTransferStatus(0);
			log.setLineTotal(entry.getValue().size());
			log.setPostingDate(postingDate);
			log.setPushDate(pushDate);
			int i = cmTransferLogMapper.addCmTransferLogDO(log);
			AlikAssert.isTrue(i>0, ResCode.STOCK_ERROR_1001 , ResCode.STOCK_ERROR_1001 + ": 保存301日志表失败");
			int lineNo = 1;
			for (OnlinePostingAccountDTO dto : entry.getValue()) {
				CmTransferDetailLogDO detail = new CmTransferDetailLogDO();
				detail.setRecordCode(postingDate + "_" + entry.getKey());
				detail.setLineNo(String.valueOf(lineNo * 10));
				lineNo++;
				detail.setTransferStatus(0);
				detail.setInFactoryCode(dto.getInFactoryCode());
				detail.setInWarehouseCode(dto.getInWarehouseCode());
				detail.setOutFactoryCode(dto.getOutFactoryCode());
				detail.setOutWarehouseCode(dto.getOutwarehouseCode());
				detail.setSkuCode(dto.getSkuCode());
				detail.setUnitCode(dto.getUnitCode());
				detail.setSkuQty(dto.getSkuQty());
				list.add(detail);
			}
			cmTransferDetailLogMapper.batchSaveCmTransferLogDetail(list);

		}



    }

    @Override
    public List<CmTransferDetailLogDO> queryDetailByRecordCode(String recordCode) {
        return cmTransferDetailLogMapper.queryDetailByRecordCode(recordCode);
    }

	@Override
	public void postAccountByRecordCode(String recordCode) {
		//查询过账结果
		//按天推送，这里以日期作为recordCode
		CmTransferLogDO logDO = this.getCmTransferByRecordCode(recordCode);
		List<CmTransferLogDO> logs = new ArrayList<>();
		if (logDO != null) {
			logs.add(logDO);
		}
		this.postAccountByLog(logs ,2);
	}

	/**
	 * 301 按日期过账
	 * @param pushDate
	 * @param isBatch true 批量  false 单个 ，job过来是true，页面过来是false
	 */
	@Override
	public void postAccountByPushDate(String pushDate, boolean isBatch) {
		//查询过账结果
		//按天推送
		List<CmTransferLogDO> logDos = this.getCmTransferByPushDate(pushDate);
		if (isBatch) {
			this.postAccountByLog(logDos, 1);
		} else {
			for (CmTransferLogDO logDO : logDos) {
				try {
					//每推一单，休眠0.2秒 担心sap接口扛不住
					Thread.sleep(200L);
				} catch (InterruptedException e) {
					log.error(e.getMessage(), e);
				}
				List<CmTransferLogDO> logs = new ArrayList<>();
				logs.add(logDO);
				this.postAccountByLog(logs, 2);
			}
		}
	}

	/**
	 * 云商过账
	 * @param logDos
	 * @param type 1 是job 2是手动
	 */
	private void postAccountByLog(List<CmTransferLogDO> logDos , int type) {
		if (logDos == null || logDos.size() == 0) {
			throw new RomeException(ResCode.STOCK_ERROR_1002, "请先处理云商过账的计算");
		}
		for (int i = logDos.size() - 1; i >= 0; i--) {
			if (1 == logDos.get(i).getTransferStatus()) {
				//已过账成功
				logDos.remove(i);
			}
		}
		if (logDos.size() == 0) {
			return;
		}
		WarehouseRecordE warehouseRecordE = new WarehouseRecordE();
		//手动过账单号记guid，否则按天job过账就用日期记为单号
		warehouseRecordE.setRecordCode(type == 1 ? logDos.get(0).getPushDate() : logDos.get(0).getRecordCode());
		warehouseRecordE.setRecordType(301);
		JSONObject jsonObject = transferDoToSapDTO(logDos);
		PostingAccountMappingVO postingAccountMappingVO = PostingAccountMappingVO.getElementByRecordType(warehouseRecordE.getRecordType() ,false);
		sapFacade.pushDataToSAP(jsonObject, warehouseRecordE, postingAccountMappingVO, null);
	}

	@Override
	public void postUnSucceedAccount() {
		Date startDate = DateUtil.offsiteDate(new Date(), Calendar.MONTH, -1);
		List<CmTransferLogDO> logDos = this.cmTransferLogMapper.selectUnSucceedListFromDate(startDate);
		if (CollectionUtils.isEmpty(logDos)) {
			return;
		}
		//单个过账
		for (CmTransferLogDO logDO : logDos) {
			try {
				//每推一单，休眠0.2秒 担心sap接口扛不住
				Thread.sleep(200L);
				List<CmTransferLogDO> logs = new ArrayList<>();
				logs.add(logDO);
				this.postAccountByLog(logs, 2);
			} catch (InterruptedException e) {
				log.error("休眠异常", e);
			} catch (RomeException e) {
				log.error("过账推送异常，recordCode：{}", logDO.getRecordCode(), e);
			} catch (Exception e) {
				log.error("过账推送异常，recordCode：{}", logDO.getRecordCode(), e);
			}
		}
	}

    /**
     * 转化日志表DO为sapDTO
     * @param logDos
     * @return
     */
	private JSONObject transferDoToSapDTO(List<CmTransferLogDO> logDos) {
		List<ZMMTMDDB0010> headList = new ArrayList<>();
		List<ZMMTMDDB0020> detailList = new ArrayList<>();
		Map<String , StoreDTO> storeMap = new HashMap<>();
		for (CmTransferLogDO logDO : logDos) {
			List<CmTransferDetailLogDO> details = this.queryDetailByRecordCode(logDO.getRecordCode());
			if (CollectionUtils.isEmpty(details)) {
				log.error("云商过账 recordCode="+logDO.getRecordCode()+"，明细信息为空");
				continue;
			}
			ZMMTMDDB0010 head = new ZMMTMDDB0010();
			CmTransferDetailLogDO master = details.get(0);
			head.setGuid(logDO.getRecordCode());
			head.setSysid("ZT01");
			//跟sap确认，这个budat就是过账的日期
			head.setBudat(logDO.getPostingDate().replaceAll("-",""));
			//跟sap确认，这个bldat就是第一次的推送日期
			head.setBldat(logDO.getPushDate().replaceAll("-",""));
			head.setZmdfcMd(master.getOutFactoryCode());
			head.setZmdfcMdk(master.getOutWarehouseCode());
			head.setZmdsrMd(master.getInFactoryCode());
			head.setZmdsrMdk(master.getInWarehouseCode());
			//退货的给X标志
			if(master.getIsReturn() == 1){
				head.setZth("X");
			}
			//单条数据处理异常，继续下一条，不影响其他数据过账
			try {
				if (!storeMap.containsKey(master.getOutFactoryCode())) {
					StoreDTO outStore = shopFacade.searchByCode(master.getOutFactoryCode());
					storeMap.put(master.getOutFactoryCode(), outStore);
				}
				if (!storeMap.containsKey(master.getInFactoryCode())) {
					StoreDTO inStore = shopFacade.searchByCode(master.getInFactoryCode());
					storeMap.put(master.getInFactoryCode(), inStore);
				}
			} catch (RomeException e) {
				log.error("云商过账查询店铺信息异常", e);
				continue;
			}

			head.setZmdfcMdg(storeMap.get(master.getOutFactoryCode()).getCompanyCode());
			head.setZmdsrMdg(storeMap.get(master.getInFactoryCode()).getCompanyCode());
			headList.add(head);
			for (CmTransferDetailLogDO detailLogDO : details) {
				ZMMTMDDB0020 item = new ZMMTMDDB0020();
				item.setGuid(detailLogDO.getRecordCode());
				item.setZitem(detailLogDO.getLineNo());
				item.setMatnr(detailLogDO.getSkuCode());
				item.setErfmg(detailLogDO.getSkuQty().abs());
				item.setErfme(detailLogDO.getUnitCode());
				detailList.add(item);
			}
		}
		AlikAssert.notEmpty(headList, "999", "云商过账数据异常");
		JSONObject jsonObject = new JSONObject();
		jsonObject.put("IT_HEAD", headList);
		jsonObject.put("IT_ITEM", detailList);
		return jsonObject;

	}
	@Override
	public PageInfo<CmTransferLogPageDTO> selectByPushDateList(CmTransferLogPageDTO cmTransferLogDO) {
		List<String> rwCodeList = new ArrayList<>();
		List<String> shopCodeList = new ArrayList<>();
		Page page = PageHelper.startPage(cmTransferLogDO.getPageIndex(), cmTransferLogDO.getPageSize());
		List<CmTransferLogPageDTO> list = cmTransferLogMapper.selectForAdmin(cmTransferLogDO);
		for (CmTransferLogPageDTO transferLogDO : list) {
			rwCodeList.add(transferLogDO.getOutFactoryCode() + "-" + transferLogDO.getOutWarehouseCode());
			shopCodeList.add(transferLogDO.getInFactoryCode());
		}
		List<RealWarehouse> rwList = realWarehouseService.findListByRealWarehouseCode(rwCodeList);
		Map<String, RealWarehouse> rwMap = RomeCollectionUtil.listforMap(rwList, "realWarehouseCode");
		List<StoreDTO> shopList = shopFacade.searchByCodeList(shopCodeList);
		Map<String, StoreDTO> shopMap = RomeCollectionUtil.listforMap(shopList,"code");
		for (CmTransferLogPageDTO logDo : list) {
			RealWarehouse rw = rwMap.get(logDo.getOutFactoryCode() + "-" + logDo.getOutWarehouseCode());
			StoreDTO shop = shopMap.get(logDo.getInFactoryCode());
			if(rw != null){
				logDo.setOutWarehouseName(rw.getRealWarehouseName());
			}
			if(shop != null){
				logDo.setInFactoryName(shop.getName());
			}
		}
		PageInfo<CmTransferLogPageDTO> pageInfo = new PageInfo<>(list);
		return pageInfo;
	}

	@Override
	public List<CmTransferLogDO> selectListByCmTransferLogDTO(CmTransferLogDO cmTransferLogDO) {
		List<CmTransferLogDO> list = cmTransferLogMapper.selectListByCmTransferLogDTO(cmTransferLogDO);
		return list;
	}

	@Override
	public List<CmTransferLogDO> exportExcelAllCmTransferLog(CmTransferLogDO cmTransferLogDO) {
		List<CmTransferLogDO> list = cmTransferLogMapper.selectByPushDate(cmTransferLogDO);
		List<CmTransferLogDO> resList=new ArrayList<>();
		for(CmTransferLogDO temp:list){
			cmTransferLogDO.setPushDate(temp.getPushDate());
			List<CmTransferLogDO> tempList = cmTransferLogMapper.selectListByCmTransferLogDTO(cmTransferLogDO);
			resList.addAll(tempList);
		}
		return resList;
	}

	@Override
	public List<CmTransferDetailExportDTO> exportDetailList(CmTransferLogPageDTO cmTransferLogDO) {
		List<String> rwCodeList = new ArrayList<>();
		List<String> shopCodeList = new ArrayList<>();
		List<String> skuCodeList = new ArrayList<>();
		List<CmTransferDetailExportDTO> list = cmTransferLogMapper.queryAllDetailList(cmTransferLogDO);
		for (CmTransferDetailExportDTO transferLogDO : list) {
			rwCodeList.add(transferLogDO.getOutFactoryCode() + "-" + transferLogDO.getOutWarehouseCode());
			skuCodeList.add(transferLogDO.getSkuCode());
		}
		List<RealWarehouse> rwList = realWarehouseService.findListByRealWarehouseCode(rwCodeList);
		Map<String, RealWarehouse> rwMap = RomeCollectionUtil.listforMap(rwList, "realWarehouseCode");
		List<SkuInfoExtDTO> skuList = skuFacade.skusBySkuCode(skuCodeList);
		Map<String, SkuInfoExtDTO> skuMap = RomeCollectionUtil.listforMap(skuList, "skuCode");
		for (CmTransferDetailExportDTO logDo : list) {
			RealWarehouse rw = rwMap.get(logDo.getOutFactoryCode() + "-" + logDo.getOutWarehouseCode());
			SkuInfoExtDTO skuInfo = skuMap.get(logDo.getSkuCode());
			if(rw != null){
				logDo.setOutWarehouseName(rw.getRealWarehouseName());
			}
			if(skuInfo != null){
				logDo.setSkuName(skuInfo.getName());
				if(CollectionUtils.isNotEmpty(skuInfo.getSkuSupplierDTOList())){
					//来伊份一品一商
					SkuSupplierDTO skuSupplier = skuInfo.getSkuSupplierDTOList().get(0);
					logDo.setSupplierCode(skuSupplier.getSupplierCode());
					logDo.setSupplierName(skuSupplier.getSupplierName());
				}
			}
			String transferStatusName = "";
			switch (logDo.getTransferStatus()) {
				case 0:
					transferStatusName ="SAP未回传";
					break;
				case 1:
					transferStatusName ="过账成功";
					break;
				case 2:
					transferStatusName ="过账失败";
					break;
				case 3:
					transferStatusName ="过账异常";
					break;
				default:
					break;
			}
			logDo.setTransferStatusName(transferStatusName);
			if(logDo.getSkuQty().compareTo(BigDecimal.ZERO) > 0 ){
				logDo.setIsReturn("否");
			}else{
				logDo.setIsReturn("是");
			}
		}
		return list;
	}

	@Override
	public PageInfo<CmTransferDetailExportDTO> selectCmTransferDetailPage(CmTransferLogPageDTO cmTransferLogDO) {
		List<String> rwCodeList = new ArrayList<>();
		List<String> shopCodeList = new ArrayList<>();
		List<String> skuCodeList = new ArrayList<>();

		Page page = PageHelper.startPage(cmTransferLogDO.getPageIndex(), cmTransferLogDO.getPageSize());
		List<CmTransferDetailLogDO> list = cmTransferDetailLogMapper.queryDetailByRecordCode(cmTransferLogDO.getRecordCode());
		PageInfo<CmTransferDetailLogDO> doPageInfo = new PageInfo<>(list);
		List<CmTransferDetailExportDTO> cmTransferDetailExportDTOS = cmTransferLogConvertor.doDetailListToExpertDTOList(list);
		for (CmTransferDetailExportDTO transferLogDO : cmTransferDetailExportDTOS) {
			rwCodeList.add(transferLogDO.getOutFactoryCode() + "-" + transferLogDO.getOutWarehouseCode());
			skuCodeList.add(transferLogDO.getSkuCode());
		}

		List<RealWarehouse> rwList = realWarehouseService.findListByRealWarehouseCode(rwCodeList);
		Map<String, RealWarehouse> rwMap = RomeCollectionUtil.listforMap(rwList, "realWarehouseCode");
		List<SkuInfoExtDTO> skuList = skuFacade.skusBySkuCode(skuCodeList);
		Map<String, SkuInfoExtDTO> skuMap = RomeCollectionUtil.listforMap(skuList, "skuCode");
		for (CmTransferDetailExportDTO logDo : cmTransferDetailExportDTOS) {
			RealWarehouse rw = rwMap.get(logDo.getOutFactoryCode() + "-" + logDo.getOutWarehouseCode());
			SkuInfoExtDTO skuInfo = skuMap.get(logDo.getSkuCode());
			if(rw != null){
				logDo.setOutWarehouseName(rw.getRealWarehouseName());
			}
			if(skuInfo != null){
				logDo.setSkuName(skuInfo.getName());
				if(CollectionUtils.isNotEmpty(skuInfo.getSkuSupplierDTOList())){
					//来伊份一品一商
					SkuSupplierDTO skuSupplier = skuInfo.getSkuSupplierDTOList().get(0);
					logDo.setSupplierCode(skuSupplier.getSupplierCode());
					logDo.setSupplierName(skuSupplier.getSupplierName());
				}
			}
			String transferStatusName = "";
			switch (logDo.getTransferStatus()) {
				case 0:
					transferStatusName ="SAP未回传";
					break;
				case 1:
					transferStatusName ="过账成功";
					break;
				case 2:
					transferStatusName ="过账失败";
					break;
				case 3:
					transferStatusName ="过账异常";
					break;
				default:
					break;
			}
			logDo.setTransferStatusName(transferStatusName);
			if(logDo.getSkuQty().compareTo(BigDecimal.ZERO) > 0 ){
				logDo.setIsReturn("否");
			}else{
				logDo.setIsReturn("是");
			}
		}
		PageInfo<CmTransferDetailExportDTO> pageInfo = new PageInfo<>(cmTransferDetailExportDTOS);
		pageInfo.setTotal(doPageInfo.getTotal());
		return pageInfo;
	}
}
