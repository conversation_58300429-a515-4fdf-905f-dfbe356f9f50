package com.rome.stock.innerservice.domain.service.impl;

import com.alibaba.fastjson.JSON;
import com.rome.arch.core.domain.EntityFactory;
import com.rome.arch.core.exception.RomeException;
import com.rome.stock.common.constants.warehouse.WarehouseRecordConstant;
import com.rome.stock.innerservice.api.dto.WarehouseRecordDTO;
import com.rome.stock.innerservice.api.dto.warehouserecord.CancelRecordDTO;
import com.rome.stock.innerservice.api.dto.warehouserecord.InWarehouseRecordDTO;
import com.rome.stock.innerservice.api.dto.warehouserecord.OutWarehouseRecordDTO;
import com.rome.stock.innerservice.common.AlikAssert;
import com.rome.stock.innerservice.common.ResCode;
import com.rome.stock.innerservice.constant.WarehouseRecordBusinessTypeVO;
import com.rome.stock.innerservice.constant.WarehouseRecordStatusVO;
import com.rome.stock.innerservice.constant.WmsSyncStatusVO;
import com.rome.stock.innerservice.domain.convertor.frontrecord.WarehouseReverseConvertor;
import com.rome.stock.innerservice.domain.entity.RealWarehouseE;
import com.rome.stock.innerservice.domain.entity.warehouserecord.WarehouseRecordDetail;
import com.rome.stock.innerservice.domain.entity.warehouserecord.WarehouseRecordE;
import com.rome.stock.innerservice.domain.entity.warehouserecord.WarehouseReverseRecordE;
import com.rome.stock.core.domain.repository.CoreRealWarehouseStockRepository;
import com.rome.stock.innerservice.domain.repository.RealWarehouseRepository;
import com.rome.stock.innerservice.domain.repository.warehouserecord.WarehouseRecordRepository;
import com.rome.stock.innerservice.domain.service.ReverseService;
import com.rome.stock.innerservice.domain.service.WarehouseRecordService;
import com.rome.stock.innerservice.domain.service.WmsOutService;
import com.rome.stock.core.facade.RedisRollBackFacade;
import com.rome.stock.core.infrastructure.dataobject.core.CoreRealStockOpDO;
import com.rome.stock.core.infrastructure.dataobject.core.CoreRealStockOpDetailDO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;

/**
 * 类ReverseServiceImpl的实现描述：冲销
 */
@Service
@Slf4j
public class ReverseServiceImpl implements ReverseService {

    @Resource
    private RealWarehouseRepository realWarehouseRepository;
    @Resource
    private WarehouseRecordRepository warehouseRecordRepository;
    @Resource
    private EntityFactory entityFactory;
    @Resource
    private CoreRealWarehouseStockRepository coreRealWarehouseStockRepository;
    @Resource
    private WarehouseRecordService warehouseRecordService;
    @Resource
    private WmsOutService wmsOutService;
    @Resource
    private WarehouseReverseConvertor warehouseReverseConvertor;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void addReverseOutRecord(OutWarehouseRecordDTO dto) {
        //幂等校验
        WarehouseRecordE warehouseRecordE=warehouseRecordRepository.queryWarehouseRecordByRecordCode(dto.getRecordCode());
        if(null != warehouseRecordE){
            return;
        }
        RealWarehouseE outWarehouse = realWarehouseRepository.getRealWarehouseByOutCodeAndFactoryCode(dto.getWarehouseCode(),dto.getFactoryCode());
        if(null == outWarehouse){
            throw new RomeException(ResCode.STOCK_ERROR_1002,"出库仓库不存在");
        }
        boolean isSuccess = false;
        CoreRealStockOpDO decreaseRealStockOpDO = null;
        try {
            //3.生成出库单
            WarehouseReverseRecordE outRecord = this.entityFactory.createEntity(WarehouseReverseRecordE.class);
            outRecord.createReverseOutRecord(dto,outWarehouse);
            outRecord.setRecordStatus(WarehouseRecordStatusVO.INIT.getStatus());
            outRecord.addRecord();
            //4.锁定仓库库存
            decreaseRealStockOpDO = outRecord.initLockStockObj(outWarehouse.getId());
            coreRealWarehouseStockRepository.lockStock(decreaseRealStockOpDO);
            isSuccess = true;
        }catch (RomeException e) {
            throw e;
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            throw new RomeException(ResCode.STOCK_ERROR_1003, e.getMessage());
        }finally {
            //业务未处理成功，释放库存
            if(!isSuccess){
                //2、回滚redis
                RedisRollBackFacade.redisRollBack(decreaseRealStockOpDO);
            }
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void addReverseInRecord(InWarehouseRecordDTO inWarehouseRecordDTO) {
        WarehouseRecordE warehouseRecordE=warehouseRecordRepository.queryWarehouseRecordByRecordCode(inWarehouseRecordDTO.getRecordCode());
        if(null != warehouseRecordE){
            return;
        }
        RealWarehouseE inWarehouse = realWarehouseRepository.getRealWarehouseByOutCodeAndFactoryCode(inWarehouseRecordDTO.getWarehouseCode(),inWarehouseRecordDTO.getFactoryCode());
        if(null == inWarehouse){
            throw new RomeException(ResCode.STOCK_ERROR_1002,"入仓库不存在");
        }
        boolean isSuccess = false;
        CoreRealStockOpDO coreOnroadRealStockOpDO = null;
        try {
            //生成入库单
            WarehouseReverseRecordE inRecordE = entityFactory.createEntity(WarehouseReverseRecordE.class);
            inRecordE.createReverseInRecord(inWarehouseRecordDTO,inWarehouse);
            //增加入仓在途库存
            log.info("冲销入库单明细信息:" + JSON.toJSONString(inRecordE.getWarehouseRecordDetails()));
            coreOnroadRealStockOpDO = inRecordE.initOnRoadStockObj(inWarehouse.getId());
            coreRealWarehouseStockRepository.increaseOnroadStock(coreOnroadRealStockOpDO);
            isSuccess = true;
        } catch (RomeException e) {
            log.error(e.getMessage(), e);
            throw new RomeException(e.getCode(), e.getMessage());
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            throw new RomeException(ResCode.STOCK_ERROR_1003, ResCode.STOCK_ERROR_1003_DESC);
        } finally {
            if (!isSuccess) {
                RedisRollBackFacade.redisRollBack(coreOnroadRealStockOpDO);
            }
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void warehouseOutNotify(Long id) {
        WarehouseRecordE outRecordE = warehouseRecordRepository.getRecordWithDetailById(id);
        AlikAssert.isNotNull(outRecordE, ResCode.STOCK_ERROR_1017, ResCode.STOCK_ERROR_1017_DESC);
        //更新出库单为待过账
        int i = warehouseRecordRepository.updateToWaitTransfer(id);
        AlikAssert.isTrue(i > 0, ResCode.STOCK_ERROR_1066, ResCode.STOCK_ERROR_1066_DESC);
        CoreRealStockOpDO coreRealStockOpDO = null;
        boolean isSuccess = false;
        try {
            //出仓库存减少  减少锁定库存
            coreRealStockOpDO = this.initOutWarehouseStockObj(outRecordE);
            coreRealWarehouseStockRepository.outAndUnlockStock(coreRealStockOpDO);
            isSuccess = true;
        } catch (RomeException e) {
            log.error(e.getMessage(), e);
            throw new RomeException(e.getCode(), e.getMessage());
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            throw new RomeException(ResCode.STOCK_ERROR_1003, ResCode.STOCK_ERROR_1003_DESC);
        } finally {
            if (!isSuccess) {
                RedisRollBackFacade.redisRollBack(coreRealStockOpDO);
            }
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean warehouseInNotify(Long warehouseRecordId, WarehouseRecordDTO warehouseRecord) {
        WarehouseRecordE recordE = warehouseRecordRepository.getRecordWithDetailById(warehouseRecordId);
        AlikAssert.isNotNull(recordE, ResCode.STOCK_ERROR_1017, ResCode.STOCK_ERROR_1017_DESC);
        //保存发货单
        warehouseRecordService.saveReceipt(recordE,warehouseRecord, WarehouseRecordConstant.NEED_SYNC_TRADE);
        CoreRealStockOpDO coreRealStockOpDO = null;
        CoreRealStockOpDO coreOnroadRealStockOpDO = null;
        boolean isSuccess = false;
        try {
            //减少在途
            coreOnroadRealStockOpDO = recordE.initOnRoadStockObj(recordE.getRealWarehouseId());
            coreRealWarehouseStockRepository.decreaseOnroadStock(coreOnroadRealStockOpDO);
            //增加大仓库存(按照真实库存)
            coreRealStockOpDO = this.initStockObj(recordE);
            coreRealWarehouseStockRepository.increaseRealQty(coreRealStockOpDO);
            isSuccess = true;
        }catch (RomeException e){
            log.error(e.getMessage(), e);
            throw new RomeException(e.getCode(),e.getMessage());
        }catch (Exception e){
            log.error(e.getMessage(), e);
            throw new RomeException(ResCode.STOCK_ERROR_1003,ResCode.STOCK_ERROR_1003_DESC);
        }finally {
            if(!isSuccess) {
                RedisRollBackFacade.redisRollBack(coreRealStockOpDO);
            }
            if(!isSuccess) {
                RedisRollBackFacade.redisRollBack(coreOnroadRealStockOpDO);
            }
        }
        return true;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void cancelReverseRecord(CancelRecordDTO cancelRecordDTO) {
        WarehouseRecordE warehouseRecordE = warehouseRecordRepository.queryWarehouseRecordByRecordCode(cancelRecordDTO.getRecordCode());
        //单据不存在直接返回取消成功
        if(null == warehouseRecordE){
            return;
        }
        if(WarehouseRecordStatusVO.DISABLED.getStatus().equals(warehouseRecordE.getRecordStatus())){
            return;
        }
        if(WarehouseRecordStatusVO.OUT_ALLOCATION.getStatus().equals(warehouseRecordE.getRecordStatus()) || WarehouseRecordStatusVO.IN_ALLOCATION.getStatus().equals(warehouseRecordE.getRecordStatus())){
            throw new RomeException(ResCode.STOCK_ERROR_1028, "取消失败：单据已出库或入库无法取消");
        }
        boolean isSuccess = false;
        CoreRealStockOpDO coreRealStockOpDO = null;
        try{
            int j =warehouseRecordRepository.updateToCanceled(warehouseRecordE.getId());
            AlikAssert.isTrue(j > 0, ResCode.STOCK_ERROR_1028, ResCode.STOCK_ERROR_1028_DESC);
            if(WmsSyncStatusVO.SYNCHRONIZED.getStatus().equals(warehouseRecordE.getSyncWmsStatus()) && cancelRecordDTO.getIsForceCancel()) {
                boolean flag = wmsOutService.orderCancel(cancelRecordDTO.getRecordCode());
                AlikAssert.isTrue(flag, ResCode.STOCK_ERROR_1205, ResCode.STOCK_ERROR_1205_DESC);
            }
            WarehouseRecordE recordE = warehouseRecordRepository.getRecordWithDetailById(warehouseRecordE.getId());
            WarehouseReverseRecordE warehouseReverseRecordE = warehouseReverseConvertor.warehouseRecordEToReverseEntity(recordE);
            if (WarehouseRecordBusinessTypeVO.IN_WAREHOUSE_RECORD.getType().equals(warehouseRecordE.getBusinessType())) {
                //入库取消
                coreRealStockOpDO = warehouseReverseRecordE.packUnlockOnRoadStock();
                coreRealWarehouseStockRepository.decreaseOnroadStock(coreRealStockOpDO);
                isSuccess = true;
            } else {
                //出库取消
                coreRealStockOpDO = warehouseReverseRecordE.packUnlockStockObjForDown();
                coreRealWarehouseStockRepository.unlockStock(coreRealStockOpDO);
                isSuccess = true;
            }
        }catch (RomeException e) {
            log.error(e.getMessage(), e);
            throw new RomeException(e.getCode(), e.getMessage());
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            throw new RomeException(ResCode.STOCK_ERROR_1003, e.getMessage());
        } finally {
            //模型成功，业务等处理失败，需要回滚
            if (!isSuccess) {
                    RedisRollBackFacade.redisRollBack(coreRealStockOpDO);
            }
        }

    }

    /**
     * 增加实体仓库库存的对象
     */
    private CoreRealStockOpDO initStockObj(WarehouseRecordE recordE){
        List<CoreRealStockOpDetailDO> increaseDetails = new ArrayList<>();
        for (WarehouseRecordDetail detail: recordE.getWarehouseRecordDetails()) {
            //数量为0的不处理库存的增加和减少
            if(BigDecimal.ZERO.compareTo(detail.getActualQty()) == 0){
                continue;
            }
            CoreRealStockOpDetailDO coreRealStockOpDetailDO = new CoreRealStockOpDetailDO();
            coreRealStockOpDetailDO.setSkuId(detail.getSkuId());
            coreRealStockOpDetailDO.setSkuCode(detail.getSkuCode());
            coreRealStockOpDetailDO.setRealQty(detail.getActualQty());
            coreRealStockOpDetailDO.setRealWarehouseId(recordE.getRealWarehouseId());
            increaseDetails.add(coreRealStockOpDetailDO);
        }
        CoreRealStockOpDO coreRealStockOpDO = new CoreRealStockOpDO();
        coreRealStockOpDO.setRecordCode(recordE.getRecordCode());
        coreRealStockOpDO.setTransType(recordE.getRecordType());
        coreRealStockOpDO.setDetailDos(increaseDetails);
        return coreRealStockOpDO;
    }

    private CoreRealStockOpDO initOutWarehouseStockObj(WarehouseRecordE outRecord) {
        List<CoreRealStockOpDetailDO> increaseDetails = new ArrayList<>();
        for (WarehouseRecordDetail detail : outRecord.getWarehouseRecordDetails()) {
            CoreRealStockOpDetailDO coreRealStockOpDetailDO = new CoreRealStockOpDetailDO();
            coreRealStockOpDetailDO.setSkuId(detail.getSkuId());
            coreRealStockOpDetailDO.setSkuCode(detail.getSkuCode());
            coreRealStockOpDetailDO.setUnlockQty(detail.getPlanQty());
            coreRealStockOpDetailDO.setRealQty(detail.getActualQty());
            coreRealStockOpDetailDO.setRealWarehouseId(outRecord.getRealWarehouseId());
            coreRealStockOpDetailDO.setCheckBeforeOp(true);
            increaseDetails.add(coreRealStockOpDetailDO);
        }
        CoreRealStockOpDO coreRealStockOpDO = new CoreRealStockOpDO();
        coreRealStockOpDO.setRecordCode(outRecord.getRecordCode());
        coreRealStockOpDO.setTransType(outRecord.getRecordType());
        coreRealStockOpDO.setDetailDos(increaseDetails);
        return coreRealStockOpDO;
    }
}
