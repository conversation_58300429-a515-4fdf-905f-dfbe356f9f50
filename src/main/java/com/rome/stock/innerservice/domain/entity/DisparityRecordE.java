package com.rome.stock.innerservice.domain.entity;

import java.util.List;

import javax.annotation.Resource;

import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Component;

import com.rome.stock.innerservice.constant.FrontRecordTypeVO;
import com.rome.stock.innerservice.domain.convertor.DisparityConvertor;
import com.rome.stock.innerservice.domain.entity.frontrecord.AbstractFrontRecord;
import com.rome.stock.innerservice.domain.repository.DisparityRecordRepository;
import com.rome.stock.innerservice.domain.service.OrderUtilService;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.extern.slf4j.Slf4j;

@Data
@Slf4j
@Component
@Scope("prototype")
@EqualsAndHashCode(callSuper = false)
public class DisparityRecordE extends AbstractFrontRecord {

    @Resource
    private DisparityRecordRepository disparityRecordRepository;
    @Resource
    private DisparityConvertor disparityConvertor;
    @Resource
    private OrderUtilService orderUtilService;

    public Long addRecord(FrontRecordTypeVO frontRecordTypeVO) {
        String recordCode = orderUtilService.queryOrderCode(frontRecordTypeVO.getCode());
        this.setRecordCode(recordCode);
        return disparityRecordRepository.addRecord(this);
    }

    public void addDetails(List<DisparityDetailE> details) {
        disparityRecordRepository.addDetails(details);
    }

    /**
     * 唯一主键
     */
    private Long id;
    /**
     * 差异单编码
     */
    private String recordCode;
    /**
     * 前置单据关联主键
     */
    private Long frontRecordId;
    /**
     * 前置单据编码
     */
    private String frontRecordCode;
    /**
     * 出入库单据关联主键
     */
    private Long inWarehouseRecordId;
    /**
     * 出入库单据编码
     */
    private String inWarehouseRecordCode;
    /**
     * 出入库单据关联主键
     */
    private Long outWarehouseRecordId;
    /**
     * 出入库单据编码
     */
    private String outWarehouseRecordCode;
    /**
     * 单据类型
     */
    private Integer recordType;
    /**
     * 入向实体仓库id
     */
    private Long inRealWarehouseId;
    /**
     * 出向实体仓库id
     */
    private Long outRealWarehouseId;
    /**
     * 单据状态
     */
    private Integer recordStatus;

    /**
     * 运输方
     */
    private String transporter;

    /**
     * sap采购单号
     */
    private String sapPoNo;

    /**
     * sap交货单号
     */
    private String sapDeliveryCode;

    //sku数量及明细
//    private List<DisparityDetailE> frontRecordDetails;

}
