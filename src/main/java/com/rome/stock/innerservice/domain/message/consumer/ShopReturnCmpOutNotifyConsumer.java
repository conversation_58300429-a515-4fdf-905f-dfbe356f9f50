package com.rome.stock.innerservice.domain.message.consumer;

import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import com.rome.scm.common.rocketmq.CustomConsumerListener;
import com.rome.stock.common.enums.CustomRocketMQEnum;
import com.rome.stock.common.enums.warehouse.WarehouseRecordTypeVO;
import com.rome.stock.innerservice.api.dto.warehouserecord.OutCmpMQDTO;
import com.rome.stock.innerservice.domain.service.ShopReturnService;
import lombok.extern.slf4j.Slf4j;
import org.apache.rocketmq.common.message.MessageExt;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.util.Objects;

/**
 * @Description: 门店退货出库确认结果推送CMP消息消费mq
 */
@Slf4j
@Service
public class ShopReturnCmpOutNotifyConsumer implements CustomConsumerListener<MessageExt> {

    @Resource
    private ShopReturnService shopReturnService;

    @Override
    public void onMessage(MessageExt messageExt, String businessNo, String msgKey) {
        log.info("出库结果推送CMP消息消费，msgID: {}，消费次数: {}", messageExt.getMsgId(), messageExt.getReconsumeTimes());
        String json = new String(messageExt.getBody());
        OutCmpMQDTO outCmpMQDTO= JSONObject.parseObject(json, OutCmpMQDTO.class);
        if(StringUtils.isEmpty(outCmpMQDTO.getRecordCode())) {
            log.error("出库结果推送CMP消息消费内容为空，msgID: {}", messageExt.getMsgId());
            return ;
        }
        //门店退货出库单
        if (WarehouseRecordTypeVO.getShopReturnOutTypes().containsKey(outCmpMQDTO.getRecordType())) {
            if (Objects.equals(outCmpMQDTO.getRwBusinessType(),2)){
                shopReturnService.pushShopReturnResultCmp6(outCmpMQDTO.getRecordCode(), Lists.newArrayList());
            }else {
                shopReturnService.pushShopReturnResultCmp5(outCmpMQDTO.getRecordCode(), Lists.newArrayList());
            }
        }
    }

	@Override
	public String getRocketMQCode() {
		return CustomRocketMQEnum.MQ_SHOP_RETURN_CMP_OUT_NOTIFY_PUSH.getCode();
	}


}
