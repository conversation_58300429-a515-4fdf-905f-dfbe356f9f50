/**
 * Filename StockOpServiceImpl.java
 * Company 上海来伊份科技有限公司。
 * <AUTHOR>
 * @version
 */
package com.rome.stock.innerservice.domain.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import com.rome.arch.core.clientobject.Response;
import com.rome.arch.core.exception.RomeException;
import com.rome.arch.util.SpringBeanUtil;
import com.rome.scm.common.rocketmq.infrastructure.CustomRocketMQMessageController;
import com.rome.stock.common.enums.ResponseMsg;
import com.rome.stock.common.enums.redis.RedisCacheKeyEnum;
import com.rome.stock.common.enums.redis.WmsCacheKeyEnum;
import com.rome.stock.core.constant.StockToolConsts;
import com.rome.stock.core.domain.repository.CoreRealWarehouseStockRepository;
import com.rome.stock.core.domain.repository.CoreStockToolRepository;
import com.rome.stock.core.domain.service.core.CoreStockOpService;
import com.rome.stock.core.infrastructure.dataobject.core.CoreRealStockOpDO;
import com.rome.stock.core.infrastructure.dataobject.core.CoreRealStockOpDetailDO;
import com.rome.stock.core.infrastructure.dataobject.core.CoreStockOpFactoryDO;
import com.rome.stock.innerservice.api.controller.BatchStockController;
import com.rome.stock.innerservice.api.controller.FixDataConroller;
import com.rome.stock.innerservice.api.controller.MqMessageController;
import com.rome.stock.innerservice.api.controller.RefreshDataForQualityController;
import com.rome.stock.innerservice.api.dto.RefreshBatchStockDTO;
import com.rome.stock.innerservice.api.dto.RefreshDataForQualityParam;
import com.rome.stock.innerservice.common.RedisUtil;
import com.rome.stock.innerservice.common.StringUtils;
import com.rome.stock.innerservice.domain.repository.SapInterfaceLogRepository;
import com.rome.stock.innerservice.domain.repository.StockOpToolRepository;
import com.rome.stock.innerservice.domain.service.StockOpToolService;
import com.rome.stock.innerservice.facade.StockToolFacade;
import com.rome.stock.innerservice.facade.VmSkuPermitFacade;
import com.rome.stock.innerservice.infrastructure.mapper.BatchStockMapper;
import com.rome.stock.innerservice.infrastructure.redis.cache.GroupInfoCacheRedis;
import com.rome.stock.innerservice.remote.item.dto.SkuInfoExtDTO;
import com.rome.stock.innerservice.remote.item.facade.SkuFacade;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.env.Environment;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.RequestBody;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * 库存操作工具类
 * <AUTHOR>
 * @since 2019年5月13日 上午11:46:43
 */
@Slf4j
@Service
public class StockOpToolServiceImpl implements StockOpToolService {

	@Autowired
	private StockOpToolRepository stockOpToolRepository;

	@Autowired
	private CoreStockToolRepository coreStockToolRepository;

	@Autowired
	private CoreRealWarehouseStockRepository coreRealWarehouseStockRepository;

	@Autowired
	private CoreStockOpService stockOpService;

	@Resource
	private BatchStockMapper batchStockMapper;
	@Resource
	private SkuFacade skuFacade;
	@Autowired
	private SapInterfaceLogRepository sapInterfaceLogRepository;


	/**
	 * 库存数量操作工具
	 * @param requestVO
	 * @return
	 */
	@Override
	public String stockSkuNumCacheOp(@RequestBody JSONObject requestVO) {
		// 反回结果信息
		String msg="成功";

		// 清除路由信息 电商仓库路由覆盖范围和集中销售sku,根据实仓id列表,批量清除路由数据缓存
		if(requestVO.getIntValue(StockToolConsts.FLAG_TAG) == StockToolConsts.FLAG_TAG_VALUE_8) {
			stockOpToolRepository.clearWarehouseRouteCoverFocusSaleByWIds(requestVO.getString("warehouseId"));
		}
		// 清除路由信息 电商渠道仓库优先级和路由模板,根据渠道code列表,批量清除路由数据缓存
		else if(requestVO.getIntValue(StockToolConsts.FLAG_TAG) == StockToolConsts.FLAG_TAG_VALUE_9) {
			stockOpToolRepository.clearRoutePriorityTempleByChannelCodes(requestVO.getString("remark"));
		}
		// 清除路由信息 电商路由信息,根据渠道code列表,批量清除所有的路由数据缓存
		else if(requestVO.getIntValue(StockToolConsts.FLAG_TAG) == StockToolConsts.FLAG_TAG_VALUE_10) {
			stockOpToolRepository.clearRouteInfoAllByChannelCodes(requestVO.getString("remark"));
		}
		// 获取路由信息 电商仓库路由覆盖范围和集中销售sku,根据实仓id列表,批量获取路由数据缓存
		else if(requestVO.getIntValue(StockToolConsts.FLAG_TAG) == StockToolConsts.FLAG_TAG_VALUE_22) {
			msg = stockOpToolRepository.getWarehouseRouteCoverFocusSaleByWIds(requestVO.getString("warehouseId"));
		}
		// 获取路由信息 电商渠道仓库优先级和路由模板,根据渠道code列表,批量获取路由数据缓存
		else if(requestVO.getIntValue(StockToolConsts.FLAG_TAG) == StockToolConsts.FLAG_TAG_VALUE_23) {
			msg = stockOpToolRepository.getRoutePriorityTempleByChannelCodes(requestVO.getString("remark"));
		}
		// 其他通用清缓存
		else if(requestVO.getIntValue(StockToolConsts.FLAG_TAG) == -1) {
			msg = clearCacheByOther(requestVO.getString("remark"));
		}
		// 其他接口工具操作
		else if(requestVO.getIntValue(StockToolConsts.FLAG_TAG) == -2) {
			msg = otherApiToolOp(requestVO);
		}
		// 批次库存与库存数量核对,实仓列表
		else if(requestVO.getIntValue(StockToolConsts.FLAG_TAG) == StockToolConsts.FLAG_TAG_VALUE_25) {
			stockOpToolRepository.batchStockNumCheckByRealWarehouses(requestVO.getString("remark"));
		}else {
			msg = stockOpService.stockSkuNumCacheOp(requestVO);
		}
		return msg;
	}

	@Override
	public String stockSkuNumCacheOpSkuCode(JSONObject requestVO) {
		//skuCode转为SkuId tag:(4,5,6,7,15,16,17)
		this.skuCodeToSkuId(requestVO);
		// 反回结果信息
		return stockOpService.stockSkuNumCacheOp(requestVO);
	}

	/**
	 * json中skuCode转为skuId
	 *
	 * @param requestVO
	 */
    private void skuCodeToSkuId(JSONObject requestVO) {
        if (Objects.isNull(requestVO)) {
            throw new RomeException(ResponseMsg.PARAM_ERROR.getCode(), "参数不能为空");
        }
        Object remarkValue = requestVO.get("remark");
        if (Objects.isNull(remarkValue)) {
            throw new RomeException(ResponseMsg.PARAM_ERROR.getCode(), "参数格式不正确");
        }
        String[] remarkArray = remarkValue.toString().split("#");
        List<String> remarkList = Lists.newArrayList(remarkArray);
        //根据skuCode查询对应的skuId
        Set<String> skuCodeSet = Sets.newHashSet();
        for (String remark : remarkList) {
            String[] realWarehouseIdAndSkuCode = remark.split(",");
            if (realWarehouseIdAndSkuCode.length < 2) {
                throw new RomeException(ResponseMsg.PARAM_ERROR.getCode(), "参数格式不正确");
            }
            skuCodeSet.add(realWarehouseIdAndSkuCode[1]);
        }
        //查询主数据skuId
        List<SkuInfoExtDTO> skuInfoExtDTOs = skuFacade.skuListBySkuCodes(Lists.newArrayList(skuCodeSet));
        Map<String, Long> skuIdMap = skuInfoExtDTOs.stream().collect(Collectors.toMap(SkuInfoExtDTO::getSkuCode, SkuInfoExtDTO::getId, (e1, e2) -> e2));
        if (CollectionUtil.isEmpty(skuIdMap)) {
            throw new RomeException(ResponseMsg.PARAM_ERROR.getCode(), "skuCode商品中心未查到!");
        }
        StringBuilder remarkBuilder = new StringBuilder();
        for (String remark : remarkList) {
            String[] realWarehouseIdAndSkuCode = remark.split(",");
            if (!skuIdMap.containsKey(realWarehouseIdAndSkuCode[1])) {
                throw new RomeException(ResponseMsg.PARAM_ERROR.getCode(), "存在商品中心未查到的skuCode!");
            }
            remarkBuilder.append(realWarehouseIdAndSkuCode[0] + "," + skuIdMap.get(realWarehouseIdAndSkuCode[1]));
            remarkBuilder.append("#");
        }
        if (remarkBuilder.length() > 0) {
            remarkBuilder.deleteCharAt(remarkBuilder.length() - 1);
        }
        requestVO.put("remark", remarkBuilder.toString());
    }
	/**
	 * 库存数量核对,job
	 */
	@Override
	public void stockNumRedisDBCheckJob() {
		// 2025-05-16 批次校验job优化,修改异步方法为同步方法
		log.info("开始,库存数量核对所有");
		coreStockToolRepository.stockNumCheckByAll();
		log.info("成功,库存数量核对所有");

//		coreStockToolRepository.stockNumCheckByAllByAsyn();
	}

	/**
	 * 其他操作
	 * @param requestVO
	 * @return
	 */
	@Override
	@Transactional(rollbackFor = Exception.class)
	public String stockNumOperationOther(JSONObject requestVO) {
		return coreStockToolRepository.stockNumOperationOther(requestVO);
	}

	/**
	 * 门店初始化库存
	 * @param requestVO
	 * @return
	 */
	@Override
	public List<String> shopInitTool(JSONObject requestVO) {
		final JSONArray channelConfig = requestVO.getJSONArray("channelConfig") == null ? new JSONArray() : requestVO.getJSONArray("channelConfig");
		return StockToolFacade.shopInitTool(requestVO.getJSONArray("content"), channelConfig);
	}

	/**
	 * 仓库初始化库存
	 * @param requestVO
	 * @return
	 */
	@Override
	public List<String> warehouseInitTool(JSONObject requestVO) {
		return StockToolFacade.warehouseInitTool(requestVO.getJSONArray("content"), requestVO.getString("recordCode"), requestVO.getInteger("transType"));
	}

	/**
	 * 运行门店初始化工具
	 */
	@Override
	@Transactional(rollbackFor = Exception.class)
	public Integer runShopInitInsertSql(List<String> sqlList) {
		return coreStockToolRepository.runShopInitInsertSql(sqlList);
	}
	/**
	 * 仓库加减实物库存初始化
	 * @param recordCode
	 * @param transType
	 * @param detailDosByIn
	 * @param detailDosByDe
	 */
	@Override
	@Transactional(rollbackFor = Exception.class)
	public void warehouseInitStock(String recordCode, Integer transType, List<CoreRealStockOpDetailDO> detailDosByIn, List<CoreRealStockOpDetailDO> detailDosByDe) {
		if((detailDosByIn == null || detailDosByIn.size() == 0) && (detailDosByDe == null || detailDosByDe.size() == 0)) {
			return;
		}
		boolean isSuccess=false;
		CoreStockOpFactoryDO stockOpFactoryDO = new CoreStockOpFactoryDO();
		try {
			CoreRealStockOpDO stockOpDO;
			if(detailDosByIn.size() > 0) {
				stockOpDO = stockOpFactoryDO.createCoreRealStockOpDO();
				stockOpDO.setRecordCode(recordCode);
				stockOpDO.setTransType(transType);
				stockOpDO.setDetailDos(detailDosByIn);
				VmSkuPermitFacade.skuSyncRateCalculate(stockOpDO);
				coreRealWarehouseStockRepository.increaseRealQty(stockOpDO);
			}
			if(detailDosByDe.size() > 0) {
				stockOpDO = stockOpFactoryDO.createCoreRealStockOpDO();
				stockOpDO.setRecordCode(recordCode);
				stockOpDO.setTransType(transType);
				stockOpDO.setDetailDos(detailDosByDe);
				coreRealWarehouseStockRepository.decreaseRealQty(stockOpDO);
			}
			stockOpFactoryDO.commit();
			isSuccess = true;
		} catch (Exception e) {
			throw e;
		}finally {
			if(!isSuccess) {
				stockOpFactoryDO.redisRollBack();
			}
		}
	}

	/**
	 * 批次库存与库存数量核对,job
	 */
	@Override
	public void batchStockNumCheckJob() {
		// 2025-05-16 批次校验job优化,修改异步方法为同步方法
		log.info("开始,批次库存与库存数量核对所有");
		stockOpToolRepository.batchStockNumCheckByAll();
		log.info("成功,批次库存与库存数量核对所有");

//		stockOpToolRepository.batchStockNumCheckByAllByAsyn();
	}

	/**
	 * 其他通用清缓存
	 * @param remark
	 * @return
	 */
	public String clearCacheByOther(String remark) {
		if(remark == null || "".equals(remark)) {
			return "失败";
		}
		String[] arr = remark.split("@");
		if("1".equals(arr[0])) { // 参数例子：1@1,2,3
			// 组到渠道信息相关操作缓存redis
			String[] kk = arr[1].split(",");
			for(String k : kk) {
				SpringBeanUtil.getBean(GroupInfoCacheRedis.class).delByGroupId(Long.valueOf(k));
			}
		} else if("2".equals(arr[0])) { // 其他通用清除
			// 参数例子：2@config_template_data_find_stockOut_wms_1_49#config_template_data_find_stockIn_wms_1_91
			String[] kk = arr[1].split("#");
			RedisUtil redisUtil = SpringBeanUtil.getBean(RedisUtil.class);
			// 失败返回
			List<String> failList = new ArrayList<>();
			for(String k : kk) {
				if(isAllowClearCache(k)){
					redisUtil.del(k);
				} else {
					failList.add(k);
				}
			}
			if(failList.size() > 0) {
				return "不在支持的枚举内，删除失败如下：" + JSON.toJSONString(failList);
			}
		}
		return "成功";
	}

	/**
	 * 是否允许清除缓存，目前只有RedisCacheKeyEnum\WmsCacheKeyEnum的才让清除
	 * @param key
	 * @return
	 */
	private boolean isAllowClearCache(String key){
		if(key == null || "".equals(key)) {
			return false;
		}
		for(RedisCacheKeyEnum keyEnum : RedisCacheKeyEnum.values()) {
			if(key.startsWith(keyEnum.getKey())){
				return true;
			}
		}
		for(WmsCacheKeyEnum keyEnum : WmsCacheKeyEnum.values()) {
			if(key.startsWith(keyEnum.getKey())){
				return true;
			}
		}
		return false;
	}


	/**
	 * 其他接口工具操作
	 * @param requestVO
	 * @return
	 */
	private String otherApiToolOp(JSONObject requestVO) {
		// 账号信息
		String accountName = requestVO.getString("accountName");
		if(StringUtils.isBlank(accountName)) {
			return "失败，获取用户账号为空";
		}
		Environment env = SpringBeanUtil.getBean(Environment.class);
		String account = env.getProperty("tool.op.account");
		if(StringUtils.isNotBlank(account)) {
			List<String> accountList = Arrays.asList(account.split(","));
			if(!accountList.contains(accountName)) {
				return "失败，不允许操作";
			}
		}
		// 操作内容，为json 结构 type-类型，param为参数，数组  {"type":"1","param":[{"c":"数组内容1"},{"c":"数组内容2"},{"c":"数组内容3"}]}
		String remark = requestVO.getString("remark");
		if(StringUtils.isBlank(remark)) {
			return "失败，操作传参为空";
		}
		JSONObject context = JSON.parseObject(remark);
		String type = context.getString("type");
		if(StringUtils.isBlank(type)) {
			return "失败，操作类型为空";
		}
		String response = null;
		boolean flag = false;
		try {
			response = innerOtherToolOp(type, context, requestVO.getLong("userId"));
			flag = response!=null && response.startsWith("成功");
		} catch (Throwable e) {
			log.error(e.getMessage(), e);
			response = e.getMessage();
			throw e;
		} finally {
			//记录日志信息
			//成功的 不保存返回值，返回值太大 保存不下
			sapInterfaceLogRepository.saveSapInterFaceLog("otherToolOp", "/otherToolOp", "otherToolOp",
					requestVO.toJSONString(), response.length()>65535?response.substring(0,65535)+"...":response, flag);
		}
		return response;
	}

	/**
	 * 其他工具操作，内部
	 * @param type
	 * @param context 结构 type-类型，param 为参数，数组  {"type":"1","param":[{"c":"数组内容1"},{"c":"数组内容2"},{"c":"数组内容3"}]}
	 * @param userId
	 * @return
	 */
	private String innerOtherToolOp(String type, JSONObject context, Long userId) {
		StringBuffer result = new StringBuffer();
		Response response;
		JSONArray jsonArray = context.getJSONArray("param");
		String url = null;
		for(int i=0;i<jsonArray.size();i++) {
			response = null;
			// 调整批次库存	/stock/v1/batch_stock/refreshForBatchStock
			if("1".equals(type)) {
				RefreshBatchStockDTO p = jsonArray.getObject(i, RefreshBatchStockDTO.class);
				p.setUserId(userId);
				response = SpringBeanUtil.getBean(BatchStockController.class).refreshForBatchStock(p);
				url = "refreshForBatchStock";
			}
			// 调整库存数据	/stock/refresh/refreshSkuQtyByTestVirtual
			else if("2".equals(type)) {
				RefreshDataForQualityParam p = jsonArray.getObject(i, RefreshDataForQualityParam.class);
				p.setUserId(userId);
				response = SpringBeanUtil.getBean(RefreshDataForQualityController.class).refreshSkuQtyByTest(p);
				url = "refreshSkuQtyByTestVirtual";
			}
			// 修改待过账财务日期单据	/stock/v1/stock_mail/updateCostFinanceDate
			else if("3".equals(type)) {
				JSONObject jsonObject = jsonArray.getJSONObject(i);
				response = SpringBeanUtil.getBean(FixDataConroller.class).updateCostFinanceDate(jsonObject.getString("ztRecordCodeStr"),
						jsonObject.getString("ztBusinessCodeStr"), jsonObject.getInteger("businessType"), jsonObject.getString("financDate"));
				url = "updateCostFinanceDate";
			}
			// 根据消息Id,重试消费失败的MQ	/customMq/v1/mq_message/retryConsumerByMsgId
			else if("4".equals(type)) {
				response = SpringBeanUtil.getBean(CustomRocketMQMessageController.class).retryConsumerByMsgId(jsonArray.getLong(i));
				url = "retryConsumerByMsgId";
			}
			// 根据消息Id,重试消费失败的MQ-core里面的	/stock/v1/mq_message/retryConsumerByMsgIdCore
			else if("5".equals(type)) {
				response = SpringBeanUtil.getBean(MqMessageController.class).retryConsumerByMsgIdCore(jsonArray.getLong(i));
				url = "retryConsumerByMsgIdCore";
			}
			// 强制转为消费成功状态	/customMq/v1/mq_message/forceConsumerToSuccessByMsgIds
			else if("6".equals(type)) {
				response = SpringBeanUtil.getBean(CustomRocketMQMessageController.class).forceConsumerToSuccessByMsgIds(jsonArray.getJSONArray(i).toJavaList(Long.class));
				url = "forceConsumerToSuccessByMsgIds";
			}
			// 根据消息Id,重试消费MQ,仅重试	/customMq/v1/mq_message/retryConsumerOnlyByMsgId
			else if("7".equals(type)) {
				response = SpringBeanUtil.getBean(CustomRocketMQMessageController.class).retryConsumerOnlyByMsgId(jsonArray.getLong(i));
				url = "retryConsumerOnlyByMsgId";
			} else {
				return "失败，没有此操作类型";
			}
			result.append("，第").append(i+1).append("个，传参结果：").append(JSON.toJSONString(response));
		}
		result.insert(0,"成功，" + url);
		return result.toString();
	}


}
