package com.rome.stock.innerservice.domain.entity;

import com.rome.arch.core.domain.BaseEntity;
import com.rome.stock.innerservice.domain.repository.SkuGroupChannelStockRelationRepository;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Component;

import java.util.Date;
import java.util.List;

@Component
@Scope("prototype")
@Data
@EqualsAndHashCode(callSuper = false)
public class SkuGroupChannelStockRelationE extends BaseEntity {
	@Autowired
	private SkuGroupChannelStockRelationRepository skuGroupChannelStockRelationRepository;

	private SkuGroupChannelStockRelationE() {

	}

	/**
	 * 唯一主键
	 */
	private Long id;
	/**
	 * 商品sku编码
	 */
	private Long skuId;
	/**
	 * 虚拟仓库组id   所属虚拟仓库组
	 */
	private Long virtualWarehouseGroupId;
	/**
	 * 渠道ID
	 */
	private Long channelId;
	/**
	 * 共享放大比率（百分比），如1-1000区间可选数字
	 */
	private Integer showRate;
	/**
	 * 关系类型：1-共享，2-独享指定数量
	 */
	private Integer relationType;
	/**
	 * 真实库存
	 */
	private Long realQty;
	/**
	 * 锁定库存
	 */
	private Long lockQty;
	/**
	 * 商家id
	 */
	private Long merchantId;
	/**
	 * 创建时间
	 */
	private Date createTime;
	/**
	 * 更新时间
	 */
	private Date updateTime;
	/**
	 * 创建人
	 */
	private Long creator;
	/**
	 * 更新人
	 */
	private Long modifier;
	/**
	 * 是否可用：0-否，1-是
	 */
	private byte isAvailable;
	/**
	 * 是否逻辑删除：0-否，1-是
	 */
	private byte isDeleted;
	/**
	 * 版本号:默认0,每次更新+1
	 */
	private Integer versionNo;
	/**
	 * 租户ID
	 */
	private Long tenantId;
	/**
	 * 业务应用ID
	 */
	private String appId;

	/**
	 * 创建渠道与策略组关系
	 *
	 * @param skuGroupChannelE
	 * @return
	 */
	public Integer saveSkuGroupChannelStockRelation(SkuGroupChannelStockRelationE skuGroupChannelE) {
		return skuGroupChannelStockRelationRepository.saveSkuGroupChannelStockRelation(skuGroupChannelE);
	}

	/**
	 * 刪除渠道与策略组关系
	 *
	 * @param id
	 * @return
	 */
	public Integer deleteSkuGroupChannelStockRelation(long id) {
		return skuGroupChannelStockRelationRepository.deleteSkuGroupChannelStockRelation(id);
	}

	/**
	 * 修改渠道与策略组配置
	 *
	 * @param skuGroupChannelE
	 * @return
	 */
	public Integer updateSkuGroupChannelStockRelation(SkuGroupChannelStockRelationE skuGroupChannelE) {
		return skuGroupChannelStockRelationRepository.updateSkuGroupChannelStockRelation(skuGroupChannelE);
	}

	/**
	 * 策略组ID查询对应渠道
	 *
	 * @param virtualWarehouseGroupId
	 * @return
	 */
	public List<SkuGroupChannelStockRelationE> selectSkuGroupChannelStockRelationByVGId(long virtualWarehouseGroupId) {
		return skuGroupChannelStockRelationRepository.selectSkuGroupChannelStockRelationByVGId(virtualWarehouseGroupId);
	}

	/**
	 * 渠道ID查询对应策略组
	 *
	 * @param channelId
	 * @return
	 */
	public SkuGroupChannelStockRelationE selectSkuGroupChannelStockRelationBychannelId(long channelId) {
		return skuGroupChannelStockRelationRepository.selectSkuGroupChannelStockRelationBychannelId(channelId);
	}

	/**
	 * 获取全部渠道与策略组关系
	 *
	 * @return
	 */
	public List<SkuGroupChannelStockRelationE> selectSkuGroupChannelStockRelationAll() {
		return skuGroupChannelStockRelationRepository.selectSkuGroupChannelStockRelationAll();
	}
}
