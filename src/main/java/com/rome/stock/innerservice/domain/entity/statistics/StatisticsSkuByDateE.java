package com.rome.stock.innerservice.domain.entity.statistics;

import com.rome.stock.innerservice.domain.entity.BaseE;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 商品按照时间统计 对象
 *
 * <AUTHOR>
 * @date 2022/2/24   13:54
 * @since 1.0.0
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class StatisticsSkuByDateE extends BaseE {

    /**
     * 商品编码
     */
    private String skuCode;

    /**
     * 商品id
     */
    private String skuId;

    /**
     * 商品名
     */
    private String skuName;

    /**
     * 单位编码
     */
    private String unitCode;

    /**
     * 单位名称
     */
    private String unitName;

    /**
     * 供应商编码
     */
    private String supplierCode;

    /**
     * 供应商名称
     */
    private String supplierName;

    /**
     * 销售门店数
     */
    private  BigDecimal saleShopStatisticsNumber;

    /**
     * 仓库库存
     */
    private BigDecimal warehouseStockNumber;

    /**
     * 门店库存
     */
    private BigDecimal shopStockNumber;
    /**
     * 商品总销量
     */
    private BigDecimal saleSkuNumber;

    /**
     * 统计日期
     */
    private Date statisticsDate;
}
