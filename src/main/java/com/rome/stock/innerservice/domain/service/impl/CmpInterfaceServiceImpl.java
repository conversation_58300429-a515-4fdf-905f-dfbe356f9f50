package com.rome.stock.innerservice.domain.service.impl;

import com.rome.stock.innerservice.api.dto.CmpInterfaceLogDTO;
import com.rome.stock.innerservice.domain.convertor.CmpInterfaceConvertor;
import com.rome.stock.innerservice.domain.entity.CmpInterfaceE;
import com.rome.stock.innerservice.domain.repository.CmpInterfaceRepository;
import com.rome.stock.innerservice.domain.service.CmpInterfaceService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * cmp管理
 * <AUTHOR>
 */
@Slf4j
@Service
public class CmpInterfaceServiceImpl implements CmpInterfaceService {

    @Resource
    private CmpInterfaceConvertor cmpInterfaceConvertor;

    @Override
    public void saveCmpInterfaceLog(CmpInterfaceLogDTO cmpInterfaceLog) {
        CmpInterfaceE cmp = cmpInterfaceConvertor.dtoToEntity(cmpInterfaceLog);
        cmp.addCmpInterfaceLog();
    }
}
