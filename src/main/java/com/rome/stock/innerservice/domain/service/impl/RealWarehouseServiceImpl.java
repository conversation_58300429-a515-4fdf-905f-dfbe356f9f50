package com.rome.stock.innerservice.domain.service.impl;

import cn.hutool.core.collection.CollUtil;
import com.alibaba.fastjson.JSON;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.google.common.collect.Lists;
import com.rome.arch.core.domain.EntityFactory;
import com.rome.arch.core.exception.RomeException;
import com.rome.arch.util.dynamic.DynamicDataSourceEnum;
import com.rome.arch.util.dynamic.TargetDataSource;
import com.rome.stock.common.enums.warehouse.VirtualWarehouseTypeVO;
import com.rome.stock.common.enums.warehouse.WarehouseMaterialStorageTypeVO;
import com.rome.stock.core.domain.repository.CoreRealWarehouseStockRepository;
import com.rome.stock.core.facade.RedisRollBackFacade;
import com.rome.stock.core.infrastructure.dataobject.core.CoreRealStockOpDO;
import com.rome.stock.core.infrastructure.dataobject.core.CoreRealStockOpDetailDO;
import com.rome.stock.innerservice.api.dto.*;
import com.rome.stock.innerservice.api.dto.groupbuy.QueryRealWarehouse;
import com.rome.stock.innerservice.common.AlikAssert;
import com.rome.stock.innerservice.common.ResCode;
import com.rome.stock.innerservice.common.RomeCollectionUtil;
import com.rome.stock.innerservice.constant.RealWarehouseRankVO;
import com.rome.stock.innerservice.constant.RealWarehouseTypeVO;
import com.rome.stock.innerservice.domain.convertor.RealWarehouseAreaConvertor;
import com.rome.stock.innerservice.domain.convertor.RealWarehouseConvertor;
import com.rome.stock.innerservice.domain.entity.RealWarehouseE;
import com.rome.stock.innerservice.domain.entity.VirtualWarehouseE;
import com.rome.stock.innerservice.domain.repository.*;
import com.rome.stock.innerservice.domain.repository.warehouserecord.KpRwRelationRepository;
import com.rome.stock.innerservice.domain.service.RealWarehouseService;
import com.rome.stock.innerservice.domain.service.VirtualWarehouseService;
import com.rome.stock.innerservice.infrastructure.dataobject.KpRwRelationDo;
import com.rome.stock.innerservice.infrastructure.dataobject.RealWarehouseDO;
import com.rome.stock.innerservice.infrastructure.dataobject.RealWarehouseWmsConfigDO;
import com.rome.stock.innerservice.infrastructure.mapper.RealWarehouseMapper;
import com.rome.stock.innerservice.remote.base.dto.ChannelDTO;
import com.rome.stock.innerservice.remote.base.dto.StoreDTO;
import com.rome.stock.innerservice.remote.base.facade.ChannelFacade;
import com.rome.stock.innerservice.remote.base.facade.ShopFacade;
import com.rome.stock.innerservice.remote.item.dto.SkuInfoExtDTO;
import com.rome.stock.innerservice.remote.item.facade.SkuFacade;
import com.rome.stock.wms.config.BaseinfoConfiguration;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Nullable;
import javax.annotation.Resource;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

@Slf4j
@Service
public class RealWarehouseServiceImpl implements RealWarehouseService {

    @Autowired
    private EntityFactory entityFactory;
    @Autowired
    private RealWarehouseConvertor realWarehouseConvertor;
    @Autowired
    private RealWarehouseAreaConvertor realWarehouseAreaConvertor;
    @Autowired
    private RealWarehouseRepository realWarehouseRepository;
    @Autowired
    private VirtualWarehouseService virtualWarehouseService;
    @Autowired
    private SkuFacade skuFacade;
    @Autowired
    private ShopFacade shopFacade;
    @Resource
    private ZdeliverRelationRepository zdeliverRelationRepository;
    @Resource
    private CoreRealWarehouseStockRepository coreRealWarehouseStockRepository;
    @Resource
    private ChannelFacade channelFacade;
    @Resource
    private RealWarehouseWmsConfigRepository realWarehouseWmsConfigRepository;
    @Resource
    private KpRwRelationRepository kpRwRelationRepository;
    @Resource
    private RealWarehouseAdditionRepository realWarehouseAdditionRepository;
    @Resource
    private RealWarehouseMapper realWarehouseMapper;
    @Resource
    private VirtualWarehouseRepository virtualWarehouseRepository;
    /**
     * 根据主键ID查询实仓信息
     *
     * @param id 主键ID
     * @return 实仓实体
     */
    @Override
    public RealWarehouse findByRealWarehouseId(Long id) {
        RealWarehouseE result = realWarehouseRepository.getRealWarehouseById(id);
        return realWarehouseConvertor.entityToDto(result);
    }

    /**
     * 根据主键查询实仓信息
     * @param ids 主键
     * @return 实仓Dto
     */
    @Override
    public List<RealWarehouse> findByRealWarehouseIds(List<Long> ids) {
        if(CollectionUtils.isEmpty(ids)) {
            return new ArrayList<>();
        }
        List<RealWarehouseE> result = realWarehouseRepository.queryWarehouseByIds(ids);
        return realWarehouseConvertor.entityToDto(result);
    }

    /**
     * 根据仓库编码查询实仓信息
     *
     * @param code 仓库编码
     * @return 实仓实体
     */
    @Override
    public RealWarehouse findByRealWarehouseCode(String code) {
        RealWarehouseE result = realWarehouseRepository.getRealWarehouseByCode(code);
        return realWarehouseConvertor.entityToDto(result);
    }

    @Override
    public RealWarehouse findByRealWarehouseOutCodeAndFactoryCode(String outCode, String factoryCode) {
        RealWarehouseE result = realWarehouseRepository.getRealWarehouseByOutCodeAndFactoryCode(outCode, factoryCode);
        return realWarehouseConvertor.entityToDto(result);
    }

    @Override
    public List<RealWarehouse> findByRealWarehouseOutCodeAndFactoryCodeList(List<QueryRealWarehouse> list){
        List<RealWarehouseE> result = realWarehouseRepository.getRealWarehouseByOutCodeAndFactoryCodeList(list);
        return realWarehouseConvertor.entityToDto(result);
    }


    /**
     * 根据门店编码查询实仓信息
     *
     * @param shopCode 仓库编码
     * @return 实仓实体
     */
    @Override
    public RealWarehouse findByRealWarehouseShopCode(String shopCode) {
        List<RealWarehouseE> list = realWarehouseRepository.getRwListByShopCodes(Arrays.asList(shopCode));
        AlikAssert.isTrue(list != null && list.size() == 1, "999", "当前门店未绑定唯一的仓库");
        RealWarehouseE result = list.get(0);
        return realWarehouseConvertor.entityToDto(result);
    }

    /**
     * 根据仓库id批量查询实仓信息
     * @param ids
     * @return
     */
    @Override
    public List<RealWarehouse> getRealWarehouseByIds(List<Long> ids){
        List<RealWarehouseE> list = realWarehouseRepository.getRealWarehouseByIds(ids);
        return realWarehouseConvertor.entityToDto(list);
    }

    @Override
    public List<RealWarehouse> findByRwListShopCodes(List<String> codes) {
        List<RealWarehouseE> list = realWarehouseRepository.getRwListByShopCodes(codes);
        return realWarehouseConvertor.entityToDto(list);
    }

    /**
     * 根据条件查询实仓信息
     *
     * @param paramDTO 查询条件参数Dto
     * @return 实仓Dto列表
     */
    @Override
    public PageInfo<RealWarehouse> findByRealWarehouseCondition(RealWarehouseParamDTO paramDTO) {
        if(StringUtils.isNotEmpty(paramDTO.getRealWarehouseCode())){
            String[]  realWarehouseCodeArr = paramDTO.getRealWarehouseCode().split("\\,");
            paramDTO.setRealWarehouseCodeList(Arrays.asList(realWarehouseCodeArr));
        }
        
        Page page = PageHelper.startPage(paramDTO.getPageIndex(), paramDTO.getPageSize());
        List<RealWarehouseE> realWarehouseEList = realWarehouseRepository.getRealWarehouseByCondition(paramDTO);
        List<RealWarehouse> realWarehouseList = realWarehouseConvertor.entityToDto(realWarehouseEList);
        List<RealWarehouseWmsConfigDO> configDOList = new ArrayList<RealWarehouseWmsConfigDO>();
        //获取实仓ID集合
        List<Long> rwIds = realWarehouseList.stream().map(RealWarehouse :: getId).distinct().collect(Collectors.toList());
        if(CollectionUtils.isNotEmpty(rwIds)) {
        	configDOList = realWarehouseWmsConfigRepository.findWmsConfigByWarehouseIds(rwIds);
        }
        Map<Long, RealWarehouseWmsConfigDO> configDOMap = configDOList.stream().collect(Collectors.toMap(RealWarehouseWmsConfigDO :: getRealWarehouseId, Function.identity(), (v1, v2) -> v1));
        realWarehouseList.forEach(item -> {
        	if(configDOMap.containsKey(item.getId())) {
        		item.setHasConfig(1);
        	}else {
        		Integer realWarehouseType = item.getRealWarehouseType();
        		//仓库类型1非门店仓、15非虚拟物品仓、21非商家仓的仓库
        		if(!Integer.valueOf(1).equals(realWarehouseType) && !Integer.valueOf(15).equals(realWarehouseType) && !Integer.valueOf(21).equals(realWarehouseType)) {
        			item.setHasConfig(0);
        		}else {
        			item.setHasConfig(1);
        		}
        	}
        	//仓库类型名称
            if(null != RealWarehouseTypeVO.getTypeVOByType(item.getRealWarehouseType())){
                item.setRealWarehouseTypeName(RealWarehouseTypeVO.getTypeVOByType(item.getRealWarehouseType()).getDesc());
            }
            //仓库类型名称
            if(null != RealWarehouseRankVO.getTypeVOByRank(item.getRealWarehouseRank())){
                item.setRealWarehouseRankName(RealWarehouseRankVO.getTypeVOByRank(item.getRealWarehouseRank()).getDesc());
            }
            //物资存储类型名称
            item.setMaterialStorageName(WarehouseMaterialStorageTypeVO.getDescByType(item.getMaterialStorageType()));
        });
        PageInfo<RealWarehouse> pageInfo = new PageInfo<>(page.getResult());
        pageInfo.setList(realWarehouseList);
        pageInfo.setTotal(page.getTotal());
        return pageInfo;
    }

    /**
     * 查询所有实仓信息
     *
     * @return 所有实仓信息列表
     */
    @Override
    public List<RealWarehouse> findRealWarehouseAllList() {
        List<RealWarehouseE> result = realWarehouseRepository.getRealWarehouseAllList();
        return realWarehouseConvertor.entityToDto(result);
    }

    /**
     * 新增实仓信息
     *
     * @param realWarehouseAddDTO 待新增实仓信息
     */
    @Override
    public Long addRealWarehouse(RealWarehouseAddDTO realWarehouseAddDTO) {
        //基本参数校验
        if (!validAddRealWarehouseDataInfo(realWarehouseAddDTO)) {
            throw new RomeException(ResCode.STOCK_ERROR_1004, ResCode.STOCK_ERROR_1004_DESC);
        }
        RealWarehouseTypeVO typeVO = RealWarehouseTypeVO.getTypeVOByType(realWarehouseAddDTO.getRealWarehouseType());
        if (null == typeVO) {
            throw new RomeException(ResCode.STOCK_ERROR_1004, ResCode.STOCK_ERROR_1004_DESC);
        }
        //判断仓库工厂属性是否唯一，如果唯一，则查询数据库，检查是否已存在
		/*if (typeVO.getUnique()) {
			List<RealWarehouseE> realWarehouses = realWarehouseRepository
					.queryRealWarehouseByFactoryCodeAndRWType(realWarehouseAddDTO.getFactoryCode(), Arrays.asList(typeVO.getType()));
			AlikAssert.empty(realWarehouses, ResCode.STOCK_ERROR_1064, ResCode.STOCK_ERROR_1064_DESC);
		}*/
        //检查是否存在相同编号的实仓信息
        RealWarehouseE resultRealWarehouseE = realWarehouseRepository.getRealWarehouseByCode(realWarehouseAddDTO.getRealWarehouseCode());
        if (null != resultRealWarehouseE) {
            throw new RomeException(ResCode.STOCK_ERROR_1004, ResCode.STOCK_ERROR_1004_DESC);
        }
        if(realWarehouseAddDTO.getRealWarehouseStatus() == null) {
        	realWarehouseAddDTO.setRealWarehouseStatus(1);
        }
        if(realWarehouseAddDTO.getAllowNegtiveStock() == null) {
        	realWarehouseAddDTO.setAllowNegtiveStock(0);
        }
        if(realWarehouseAddDTO.getWarehouseStoreIdenti() == null) {
            realWarehouseAddDTO.setWarehouseStoreIdenti(0);
        }
        RealWarehouseE realWarehouseE = realWarehouseConvertor.dtoToEntity(realWarehouseAddDTO);
        //非门店仓，门店编号都置为空
        if (!RealWarehouseTypeVO.RW_TYPE_1.getType().equals(realWarehouseE.getRealWarehouseType())) {
            realWarehouseE.setShopCode(null);
        }
        realWarehouseE.setCreator(realWarehouseAddDTO.getUserId());
        if(StringUtils.isEmpty(realWarehouseE.getRealWarehouseMobile())){
            realWarehouseE.setRealWarehouseMobile(realWarehouseE.getRealWarehousePhone());
        }
        boolean executeResult = realWarehouseRepository.saveRealWarehouse(realWarehouseE);
        if (!executeResult) {
            throw new RomeException(ResCode.STOCK_ERROR_1004, ResCode.STOCK_ERROR_1004_DESC);
        }
        //查询实仓类型配置
        String realWarehouseTypeArrStr = BaseinfoConfiguration.getInstance().get("realwarhouse", "kpRelationTypeArr");
        List<Integer> realWarehouseTypeArr = JSON.parseArray(realWarehouseTypeArrStr, Integer.class);
        //设置鲲鹏渠道关联
        if(realWarehouseAddDTO.getRealWarehouseRank() != null &&
                realWarehouseTypeArr.contains(realWarehouseAddDTO.getRealWarehouseType())){
            executeResult = kpRwRelationRepository.saveRelation(realWarehouseE);
            if (!executeResult) {
                throw new RomeException(ResCode.STOCK_ERROR_1092, ResCode.STOCK_ERROR_1092_DESC);
            }
        }
        return realWarehouseE.getId();
    }

    /**
     * 修改实仓信息
     *
     * @param realWarehouse 待修改实仓信息实体
     */
    @Override
    public void updateRealWarehouse(RealWarehouse realWarehouse) {
        //基本参数校验
        if (!validUpdateRealWarehouseDataInfo(realWarehouse)) {
            throw new RomeException(ResCode.STOCK_ERROR_1005, ResCode.STOCK_ERROR_1005_DESC);
        }
        RealWarehouseTypeVO typeVO = RealWarehouseTypeVO.getTypeVOByType(realWarehouse.getRealWarehouseType());
        if (null == typeVO) {
            throw new RomeException(ResCode.STOCK_ERROR_1005, ResCode.STOCK_ERROR_1005_DESC);
        }
        //判断仓库工厂属性是否唯一，如果唯一，则查询数据库，检查是否已存在
		/*if (typeVO.getUnique()) {
			RealWarehouseE tempRealWarehouseE = realWarehouseRepository.getRealWarehouseById(realWarehouse.getId());
			if (! tempRealWarehouseE.getRealWarehouseType().equals(realWarehouse.getRealWarehouseType()) ||
					! tempRealWarehouseE.getFactoryCode().equals(realWarehouse.getFactoryCode())) {
				// 如果仓库类型或工厂编码发生变化
				List<RealWarehouseE> realWarehouses = realWarehouseRepository
						.queryRealWarehouseByFactoryCodeAndRWType(realWarehouse.getFactoryCode(), Arrays.asList(typeVO.getType()));
				AlikAssert.empty(realWarehouses, ResCode.STOCK_ERROR_1064, ResCode.STOCK_ERROR_1064_DESC);
			}
		}*/
        RealWarehouseE realWarehouseE = realWarehouseConvertor.dtoToEntity(realWarehouse);
        realWarehouseE.setModifier(realWarehouse.getUserId());
        //非门店仓，门店编号都置为空
        if (!RealWarehouseTypeVO.RW_TYPE_1.getType().equals(realWarehouse.getRealWarehouseType())) {
            realWarehouseE.setShopCode(null);
        }
        boolean executeResult = realWarehouseRepository.updateRealWarehouseByWhere(realWarehouseE);
        if (!executeResult) {
            throw new RomeException(ResCode.STOCK_ERROR_1005, ResCode.STOCK_ERROR_1005_DESC);
        }
        //查询实仓类型配置
        String realWarehouseTypeArrStr = BaseinfoConfiguration.getInstance().get("realwarhouse", "kpRelationTypeArr");
        List<Integer> realWarehouseTypeArr = JSON.parseArray(realWarehouseTypeArrStr, Integer.class);
        if(realWarehouse.getRealWarehouseRank() != null &&
                realWarehouseTypeArr.contains(realWarehouse.getRealWarehouseType())){
            this.createKpRwRelation(realWarehouse.getId().toString());
        } else {
            this.deleteKpRwRelation(realWarehouse.getId().toString());
        }
    }

    /**
     * 启用实仓
     *
     * @param realWarehouseId 主键ID
     */
    @Override
    public void enableRealWarehouse(Long realWarehouseId, Long userId) {
        realWarehouseRepository.updateRealWarehouseStatusEnable(realWarehouseId, userId);
    }

    /**
     * 停用实仓
     *
     * @param realWarehouseId 主键ID
     */
    @Override
    public void disableRealWarehouse(Long realWarehouseId, Long userId) {
        realWarehouseRepository.updateRealWarehouseStatusDisable(realWarehouseId, userId);
    }

    /**
     * 实仓分配虚仓
     *
     * @param paramDTO
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void allotVirtualWarehouse(RealWarehouseParamDTO paramDTO) {
//		virtualWarehouseService.relateRealWarehouse(paramDTO.getVirtualWarehouseIdList(),
//				Long.parseLong(paramDTO.getModifier()), realWarehouseId);
        CoreRealStockOpDO stockDO = null;
        boolean isSuccess = false;
        try {
            for (VWIdSyncRateDTO dto : paramDTO.getVwIdSyncRateDTOList()) {
                virtualWarehouseService.relateRealWarehouse(dto.getId(), paramDTO.getUserId(),
                        paramDTO.getRealWarehouseId(), dto.getSyncRate());
            }
            List<VirtualWarehouse> list =virtualWarehouseService.queryByRealWarehouseId(paramDTO.getRealWarehouseId());
            if(list!=null && list.size()>0) {
            	List<Integer> typeList = new ArrayList<>();
            	for(VirtualWarehouse dto : list) {
            		if(dto.getVirtualWarehouseType() != null && !VirtualWarehouseTypeVO.VW_TYPE_0.getType().equals(dto.getVirtualWarehouseType())) {
            			if(typeList.contains(dto.getVirtualWarehouseType())) {
            				throw new RomeException(ResCode.STOCK_ERROR_1008, ResCode.STOCK_ERROR_1008_DESC +
            						",虚仓类型重复：" + VirtualWarehouseTypeVO.getDescByType(dto.getVirtualWarehouseType()));
            			}
            			typeList.add(dto.getVirtualWarehouseType());
            		}
            	}
            }
            //包装参数
            stockDO = new CoreRealStockOpDO();
            CoreRealStockOpDetailDO detailDO = new CoreRealStockOpDetailDO();
            detailDO.setRealWarehouseId(paramDTO.getRealWarehouseId());
            List<CoreRealStockOpDetailDO> detailDos = Arrays.asList(detailDO);
            stockDO.setDetailDos(detailDos);
            coreRealWarehouseStockRepository.updateVirtualSyncRate(stockDO);
            isSuccess = true;
        } catch (RomeException e) {
            log.error("分配虚仓失败" + e.getMessage() + ",参数：{}", paramDTO, e);
            throw e;
        } catch (Exception e) {
        	log.error(e.getMessage(), e);
            throw new RomeException(ResCode.STOCK_ERROR_1008, ResCode.STOCK_ERROR_1008_DESC);
        } finally {
            if (!isSuccess) {
                if (stockDO != null) {
                    RedisRollBackFacade.redisRollBack(stockDO);
                }
            }
        }
    }

    /**
     * 根据实仓主键ID查询实仓覆盖范围
     *
     * @param realWarehouse
     * @return
     */
    @Override
    public List<RealWarehouseArea> findAreaListByRealWarehouseId(Long realWarehouse) {
        List<RealWarehouseE> result = realWarehouseRepository.queryAreaListByRealWarehouseId(realWarehouse);
        return realWarehouseAreaConvertor.entityToDto(result);
    }

    /**
     * 分配实仓覆盖区域
     *
     * @param realWarehouseId 实仓主键ID
     * @param paramDTO        覆盖区域实体
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void allotRealWarehouseArea(Long realWarehouseId, RealWarehouseParamDTO paramDTO) {
        for (RealWarehouseAreaAddDTO area : paramDTO.getRealWarehouseAreaAddDTOList()) {
            area.setRealWarehouseId(realWarehouseId);
        }
        realWarehouseRepository.deleteRealWarehouseAreaByRWId(realWarehouseId, paramDTO.getUserId());
        if (null != paramDTO.getRealWarehouseAreaAddDTOList() && !paramDTO.getRealWarehouseAreaAddDTOList().isEmpty()) {
            for (RealWarehouseAreaAddDTO areaAddDTO : paramDTO.getRealWarehouseAreaAddDTOList()) {
                areaAddDTO.setCreator(paramDTO.getUserId());
            }
        }
        boolean executeResult = realWarehouseRepository.insertRealWarehouseArea(paramDTO.getRealWarehouseAreaAddDTOList());
        if (!executeResult) {
            throw new RomeException(ResCode.STOCK_ERROR_1009, ResCode.STOCK_ERROR_1009_DESC);
        }
    }

    /**
     * 实体仓库实例信息校验
     *
     * @param realWarehouse
     * @return 校验结果
     */
    private boolean validUpdateRealWarehouseDataInfo(RealWarehouse realWarehouse) {
        if (null == realWarehouse ||
                null == realWarehouse.getId() || 0 == realWarehouse.getId()) {
            return false;
        }
        if (StringUtils.isBlank(realWarehouse.getFactoryCode()) ||
                StringUtils.isBlank(realWarehouse.getRealWarehouseName()) ||
                StringUtils.isBlank(realWarehouse.getRealWarehouseAddress()) ||
//				StringUtils.isBlank(realWarehouseAddDTO.getRealWarehouseCountryCode()) ||
//				StringUtils.isBlank(realWarehouseAddDTO.getRealWarehouseProvinceCode()) ||
//				StringUtils.isBlank(realWarehouseAddDTO.getRealWarehouseCityCode()) ||
                null == realWarehouse.getRealWarehouseType()) {
            return false;
        }
        if (RealWarehouseTypeVO.RW_TYPE_1.getType().equals(realWarehouse.getRealWarehouseType())) {
            if (StringUtils.isBlank(realWarehouse.getShopCode())) {
                return false;
            }
        }
        return true;
    }

    /**
     * 实体仓库基本信息校验
     *
     * @param realWarehouseAddDTO
     * @return 校验结果
     */
    private boolean validAddRealWarehouseDataInfo(RealWarehouseAddDTO realWarehouseAddDTO) {
        if (realWarehouseAddDTO == null ||
                StringUtils.isBlank(realWarehouseAddDTO.getRealWarehouseCode()) ||
                StringUtils.isBlank(realWarehouseAddDTO.getRealWarehouseOutCode()) ||
                StringUtils.isBlank(realWarehouseAddDTO.getFactoryCode()) ||
                StringUtils.isBlank(realWarehouseAddDTO.getRealWarehouseName()) ||
                StringUtils.isBlank(realWarehouseAddDTO.getRealWarehouseAddress()) ||
//				StringUtils.isBlank(realWarehouseAddDTO.getRealWarehouseCountryCode()) ||
//				StringUtils.isBlank(realWarehouseAddDTO.getRealWarehouseProvinceCode()) ||
//				StringUtils.isBlank(realWarehouseAddDTO.getRealWarehouseCityCode()) ||
                null == realWarehouseAddDTO.getRealWarehouseType()
                ) {
            return false;
        }
        if (RealWarehouseTypeVO.RW_TYPE_1.getType().equals(realWarehouseAddDTO.getRealWarehouseType())) {
            if (StringUtils.isBlank(realWarehouseAddDTO.getShopCode())) {
                return false;
            }
        }
        return true;
    }

    @Override
    public List<RealWarehouse> findListByRealWarehouseCode(List<String> codes) {
        if(CollectionUtils.isEmpty(codes)){
            return new ArrayList<>();
        }
        List<RealWarehouseE> result = realWarehouseRepository.getRealWarehousesByCode(codes);
        return realWarehouseConvertor.entityToDto(result);
    }

    @Override
    public RealWarehouse findByRwCodeAndFactoryCode(String realWarehouseCode, String factoryCode) {
        RealWarehouseE result = realWarehouseRepository.getByRwCodeAndFactoryCode(realWarehouseCode, factoryCode);
        return realWarehouseConvertor.entityToDto(result);
    }

    @Override
    public List<RealWarehouse> queryWhByConditionForAdmin(RealWarehouseParamDTO paramDTO) {
        AlikAssert.isNotNull(paramDTO, ResCode.STOCK_ERROR_1002, ResCode.STOCK_ERROR_1002_DESC);
        List<RealWarehouseE> realWarehouseEList = realWarehouseRepository.getRealWarehouseByCondition(paramDTO);
        return realWarehouseConvertor.entityToDto(realWarehouseEList);
    }

    @Override
    public List<RealWarehouse> queryRealWarehouseByFactoryCode(String factoryCode) {
        List<RealWarehouse> list = realWarehouseRepository.queryRealWarehouseByFactoryCode(factoryCode);
        return list;
    }

    /**
     * 根据工厂code查询仓库-非门店
     *
     * @param factoryCode
     * @return
     */
    @Override
    public List<RealWarehouse> queryRealWarehouseByFactoryCodeNoShop(String factoryCode) {
        //根据类型判断设置仓库类型
        Map<Integer, String> realWarehouseTypeList = RealWarehouseTypeVO.getRealWarehouseTypeList();
        realWarehouseTypeList.remove(RealWarehouseTypeVO.RW_TYPE_1.getType());
        Set<Integer> typeSet = realWarehouseTypeList.keySet();
        List<Integer> types = new ArrayList<>(typeSet);
        return queryRealWarehouseByFactoryCodeAndRWType(factoryCode, types);
    }

    /**
     * 查询非门店仓
     *
     * @return
     */
    @Override
    public List<RealWarehouse> queryRealWarehouseNoShop() {
        //根据类型判断设置仓库类型
        Map<Integer, String> realWarehouseTypeList = RealWarehouseTypeVO.getRealWarehouseTypeList();
        realWarehouseTypeList.remove(RealWarehouseTypeVO.RW_TYPE_1.getType());
        Set<Integer> typeSet = realWarehouseTypeList.keySet();
        List<Integer> types = new ArrayList<>(typeSet);
        return getRealWarehouseFactory(types);
    }

    @Override
    public List<RealWarehouse> queryRWByCondition(QueryAreaRWarehouse queryRealWarehouse) {
        List<RealWarehouseE> realWarehouseES = realWarehouseRepository.queryRWByCondition(queryRealWarehouse);
        return realWarehouseConvertor.entityToDto(realWarehouseES);
    }

    /**
     * 根据门店编码和仓库类型查询仓库，必须要求指定类型的仓库在对应的工厂下是唯一的
     *
     * @param shopCode        门店编码
     * @param warehouseTypeVO 仓库类型
     * @return
     */
    @Override
    public RealWarehouse queryRealWarehouseByShopCodeAndType(String shopCode, RealWarehouseTypeVO warehouseTypeVO) {
        //if (!warehouseTypeVO.getUnique()) {
        //	throw new RomeException(ResCode.STOCK_ERROR_1046,ResCode.STOCK_ERROR_1046_DESC);
        //}
        StoreDTO storeDTO = shopFacade.searchByCode(shopCode);
        List<RealWarehouse> list = realWarehouseRepository.queryRealWarehouseByFactoryCode(storeDTO.getDeliveryFactory());
        for (RealWarehouse warehouse : list) {
            if (warehouseTypeVO.getType().equals(warehouse.getRealWarehouseType())) {
                return warehouse;
            }
        }
        return null;
    }

    /**
     * 根据门店号以及是否加盟查询转置仓
     * @param shopCode
     * @param isJoin 是否加盟门店，对于加盟门店的话，配置表必须要有，对于直营的，不一定有配置
     * @return
     */
    @Override
    public RealWarehouse queryRealWarehouseByShopCodeAndTypeForJoinConvert(String shopCode,boolean isJoin) {
        StoreDTO storeDTO = shopFacade.searchByCode(shopCode);
        String deliverFactory = zdeliverRelationRepository.getDeliverFactory(storeDTO.getDeliveryFactory());
        if (!isJoin && StringUtils.isBlank(deliverFactory)) {
            //如果是直营的话，在配置表无数据的，就用商品返回的工厂即可
            deliverFactory = storeDTO.getDeliveryFactory();
        }
        if (StringUtils.isNotBlank(deliverFactory)) {
            List<RealWarehouse> list = realWarehouseRepository.queryRealWarehouseByFactoryCode(deliverFactory);
            for (RealWarehouse warehouse : list) {
                if (RealWarehouseTypeVO.RW_TYPE_24.getType().equals(warehouse.getRealWarehouseType())) {
                    return warehouse;
                }
            }
        }
        return null;
    }


    /**
     * 获取加盟门店的退货仓库
     *
     * @param shopCode
     * @return
     */
    @Override
    public RealWarehouse getJoinReturnWarehouse(String shopCode) {
        RealWarehouse realWarehouse = null;
        StoreDTO storeDTO = shopFacade.searchByCode(shopCode);
        //获取加盟门店的z工厂
        if (storeDTO != null) {
            String deliverFactory = zdeliverRelationRepository.getDeliverFactory(storeDTO.getDeliveryFactory());
            if (org.apache.commons.lang.StringUtils.isNotBlank(deliverFactory)) {
                List<RealWarehouse> list = realWarehouseRepository.queryRealWarehouseByFactoryCode(deliverFactory);
                for (RealWarehouse warehouse : list) {
                    if (RealWarehouseTypeVO.RW_TYPE_12.getType().equals(warehouse.getRealWarehouseType())) {
                        realWarehouse = warehouse;
                    }
                }
            }
        }
        return realWarehouse;
    }

    /**
     * 根据仓库内部编号查询仓库信息
     *
     * @param code
     * @return
     */
    @Override
    public RealWarehouse queryRealWarehouseByInCode(String code) {
        RealWarehouseE result = realWarehouseRepository.queryRealWarehouseByInCode(code);
        return realWarehouseConvertor.entityToDto(result);
    }

    /**
     * 根据实仓id查询实仓的所有skuId
     *
     * @param realWarehouseId
     * @return
     */
    @Override
    public List<RealWarehouseStockDTO> querySkuIdByWhId(String realWarehouseId) {
        List<RealWarehouseStockDTO> stockList = realWarehouseRepository.querySkuIdByWhId(Long.parseLong(realWarehouseId));
        //遍历集合根据skuId获取skuName和skuCode
        List<Long> skuIds = RomeCollectionUtil.getValueList(stockList, "skuId");
        List<SkuInfoExtDTO> skuInfoExtDTOS = skuFacade.skusBySkuId(skuIds);
        Map<Long, SkuInfoExtDTO> skuInfoExtDTOMap = RomeCollectionUtil.listforMap(skuInfoExtDTOS, "id");

        for (RealWarehouseStockDTO dto : stockList) {
            if (skuInfoExtDTOMap.containsKey(dto.getSkuId())) {
                dto.setSkuCode(skuInfoExtDTOMap.get(dto.getSkuId()).getSkuCode());
                dto.setSkuName(skuInfoExtDTOMap.get(dto.getSkuId()).getName());
            }
        }
        return stockList;
    }

    /**
     * 根据仓库类型查询仓库
     *
     * @param types
     * @return
     */
    @Override
    public List<RealWarehouse> getRealWarehouseFactory(List<Integer> types) {
        List<RealWarehouseE> warehouseES = realWarehouseRepository.getRealWarehouseFactory(types);
        return realWarehouseConvertor.entityToDto(warehouseES);
    }

    /**
     * 根据工厂code和仓库类型查询仓库信息
     *
     * @param factoryCode
     * @param types
     * @return
     */
    @Override
    public List<RealWarehouse> queryRealWarehouseByFactoryCodeAndRWType(String factoryCode, List<Integer> types) {
        List<RealWarehouseE> warehouseES = realWarehouseRepository.queryRealWarehouseByFactoryCodeAndRWType(factoryCode, types);
        return realWarehouseConvertor.entityToDto(warehouseES);
    }

    @Override
    public List<RealWarehouse> queryEnableRealWarehouseByFactoryCodeAndRWType(String factoryCode, List<Integer> types) {
        List<RealWarehouseE> warehouseES = realWarehouseRepository.queryEnableRealWarehouseByFactoryCodeAndRWType(factoryCode, types);
        return realWarehouseConvertor.entityToDto(warehouseES);
    }

    /**
     * 根据仓库类型查仓库
     *
     * @param type
     * @return
     */
    @Override
    public List<RealWarehouse> queryRealWarehouseByRWType(Integer type) {
        List<RealWarehouseE> warehouseES = realWarehouseRepository.queryRealWarehouseByRWType(type);
        return realWarehouseConvertor.entityToDto(warehouseES);
    }

    /**
     * 根据仓库标识查仓库
     *
     * @param warehouseStoreIdenti
     * @return
     */
    @Override
    public List<RealWarehouse> queryRealWarehouseByWarehouseStoreIdenti(Integer warehouseStoreIdenti) {
        List<RealWarehouseE> warehouseES = realWarehouseRepository.queryRealWarehouseByWarehouseStoreIdenti(warehouseStoreIdenti);
        return realWarehouseConvertor.entityToDto(warehouseES);
    }




    /**
     * 根据批量工厂编码查询实仓信息
     *
     * @param factoryCodes
     * @return
     */
    @Override
    public List<RealWarehouse> queryRealWarehousesByFactoryCodes(List<String> factoryCodes) {
        if (factoryCodes == null || factoryCodes.size() == 0) {
            return new LinkedList<RealWarehouse>();
        }
        List<RealWarehouseE> realWarehouseES = realWarehouseRepository.queryRealWarehousesByFactoryCodes(factoryCodes);
        return realWarehouseConvertor.entityToDto(realWarehouseES);
    }

    @Override
    public RealWarehouse queryVirtualSkuRealWarehouse() {
        return realWarehouseConvertor.entityToDto(realWarehouseRepository.queryVirtualSkuRealWarehouse());
    }

	@Override
	public Map<String, String> queryDefaultRealWarehouseByShopCode(String shopCode) {
		List<RealWarehouseE> realWarehouseEList = realWarehouseRepository.getRwListByShopCodes(Arrays.asList(shopCode));
		if(CollectionUtils.isEmpty(realWarehouseEList)) {
			log.error("根据门店编码[{}]查询门店仓不存在", shopCode);
			throw new RomeException(ResCode.STOCK_ERROR_1010, ResCode.STOCK_ERROR_1010_DESC);
		}
		StoreDTO storeDTO = shopFacade.queryStoreByCode(shopCode);
		if(storeDTO == null) {
			log.error("根据门店编码[{}]查询门店不存在", shopCode);
			throw new RomeException(ResCode.STOCK_ERROR_1037, ResCode.STOCK_ERROR_1037_DESC);
		}
		Map<String, String> map = null;
		if("3".equals(storeDTO.getStoreProperties())) {//加盟
			if(StringUtils.isBlank(storeDTO.getFranchisee())) {
        		log.error("门店[{}]所属加盟商不存在", shopCode);
        		throw new RomeException(ResCode.STOCK_ERROR_4018, ResCode.STOCK_ERROR_4018_DESC);
        	}
        	//根据加盟商编码查询加盟商渠道
			List<ChannelDTO> channelDTOs = channelFacade.queryChannelByCodeAndType("120", storeDTO.getFranchisee(), "3");
			if(CollectionUtils.isEmpty(channelDTOs)) {
				log.error("根据加盟商编码[{}]查询加盟商渠道不存在", storeDTO.getFranchisee());
				throw new RomeException(ResCode.STOCK_ERROR_5028, ResCode.STOCK_ERROR_5028_DESC);
			}
			List<String> channelCodes = channelDTOs.stream().distinct().map(ChannelDTO :: getChannelCode).collect(Collectors.toList());
			map = realWarehouseRepository.queryRealWarehouseByChannelCode(channelCodes.get(0));
			if(MapUtils.isEmpty(map)) {
				String channelCodeJM = storeDTO.getCode() + "_120";
				//根据公司编码查询加盟商渠道
				ChannelDTO channelDTO = channelFacade.queryChannelGroupByCode(channelCodeJM);
				if(channelDTO != null) {
					map = realWarehouseRepository.queryRealWarehouseByChannelCode(channelDTO.getChannelCode());
				}
			}
        } else {//非加盟门店当作直营门店处理
        	String channelCodeZY = storeDTO.getCode() + "_119";
			map = realWarehouseRepository.queryRealWarehouseByChannelCode(channelCodeZY);
			if(MapUtils.isEmpty(map)) {
				//根据公司编码查询直营渠道
				ChannelDTO channelDTO = channelFacade.queryChannelGroupByCode(channelCodeZY);
				if(channelDTO != null) {
					map = realWarehouseRepository.queryRealWarehouseByChannelCode(channelDTO.getChannelCode());
				}
			}
        }
		return map;
	}

	/**
	 * 查询实仓信息列表（排除门店）
	 */
	@Override
	public List<RealWarehouse> queryRWList(@Nullable String nameOrCode) {
		List<RealWarehouseE> result = realWarehouseRepository.queryRWList(nameOrCode);
        return realWarehouseConvertor.entityToDto(result);
	}

    @Override
    public List<RealWarehouse> queryRealWarehouseByFactoryCodeAndType(String factoryCode, Integer type) {
	    if(StringUtils.isEmpty(factoryCode)){
            throw new RomeException(ResCode.STOCK_ERROR_1002, "工厂编号不能为空");
        }
        if(null == type){
            throw new RomeException(ResCode.STOCK_ERROR_1002, "仓库类型不能为空");
        }
	    int queryType=RealWarehouseTypeVO.RW_TYPE_14.getType();
	    //0.发货仓（不需要包装）；1.包装仓
	    if(type==1){
            queryType=RealWarehouseTypeVO.RW_TYPE_2.getType();
        }
        List<RealWarehouseE> warehouseEList=realWarehouseRepository.queryRealWarehouseByFactoryCodeAndRWType(factoryCode,Arrays.asList(queryType));
        return realWarehouseConvertor.entityToDto(warehouseEList);
    }

    @Override
    public List<RealWarehouse> queryRealWarehouseByFactoryCodeAndRealWarehouseType(String factoryCode, Integer type) {
        if(StringUtils.isEmpty(factoryCode)){
            throw new RomeException(ResCode.STOCK_ERROR_1002, "工厂编号不能为空");
        }
        if(null == type){
            throw new RomeException(ResCode.STOCK_ERROR_1002, "仓库类型不能为空");
        }
        List<RealWarehouseE> warehouseE=realWarehouseRepository.queryRealWarehouseByFactoryCodeAndRWType(factoryCode,Arrays.asList(type));
        return realWarehouseConvertor.entityToDto(warehouseE);
    }

    @Override
    public List<Long> queryRealWarehouseIdByShopCodes(List<String> shopCodes) {
	    if(CollectionUtils.isEmpty(shopCodes)){
            throw new RomeException(ResCode.STOCK_ERROR_1002, "门店编号不能为空");
        }
        List<RealWarehouse> list=this.findByRwListShopCodes(shopCodes);
        List<Long> res=list.stream().map(RealWarehouse :: getId).distinct().collect(Collectors.toList());
        return res;
    }

    @Override
    public List<RealWarehouse> queryRealWarehouseByShopCodes(List<String> shopCodes) {
        if(CollectionUtils.isEmpty(shopCodes)){
            throw new RomeException(ResCode.STOCK_ERROR_1002, "门店编号不能为空");
        }
        return this.findByRwListShopCodes(shopCodes);
    }

    @Override
    public RealWarehouse queryRealWarehouseByVmCode(String virtualWarehouseCode) {
        VirtualWarehouse virtualWarehouse = virtualWarehouseService.getVirtualWarehouseByCode(virtualWarehouseCode);
        AlikAssert.isNotNull(virtualWarehouse,ResCode.STOCK_ERROR_1002,"未查到虚仓code为"+virtualWarehouseCode+"的虚仓");
        RealWarehouseE realWarehouse = realWarehouseRepository.getRealWarehouseById(virtualWarehouse.getRealWarehouseId());
        return realWarehouseConvertor.entityToDto(realWarehouse);
    }

    @Override
    public RealWarehouse queryRealWarehouseByRealWarehouseCode(String realWarehouseCode) {
        RealWarehouseDO realWarehouseDO = realWarehouseRepository.queryRealWarehouseByRealWarehouseCode(realWarehouseCode);
        return realWarehouseConvertor.doToDTO(realWarehouseDO);
    }

    @Override
    public List<RealWarehouse> queryRealWarehouseByRealWarehouseCodeList(List<String> realWarehouseCodeList) {
        List<RealWarehouseDO> realWarehouseDOList = realWarehouseRepository.queryRealWarehouseByRealWarehouseCodeList(realWarehouseCodeList);
        if(CollectionUtils.isEmpty(realWarehouseDOList)){
            return Lists.newArrayList();
        }
        List<RealWarehouse> resultList=realWarehouseConvertor.dosToDTOs(realWarehouseDOList);
        List<Long> realWarehouseIds=realWarehouseDOList.stream().map(RealWarehouseDO::getId).distinct().collect(Collectors.toList());
        List<VirtualWarehouse> virtualWarehouseList=virtualWarehouseRepository.queryByRealWarehouseIdList(realWarehouseIds);
        //设置虚拟仓库类型名称
        virtualWarehouseList.forEach(v->v.setVirtualWarehouseTypeName(VirtualWarehouseTypeVO.getDescByType(v.getVirtualWarehouseType())));
        Map<Long,List<VirtualWarehouse>> realWarehouseMap=virtualWarehouseList.stream().collect(Collectors.groupingBy(VirtualWarehouse::getRealWarehouseId));
        for (RealWarehouse realWarehouse : resultList) {
            if(realWarehouseMap.containsKey(realWarehouse.getId())){
                realWarehouse.setVirtualWarehouseList(realWarehouseMap.get(realWarehouse.getId()));
            }
        }
        return resultList;
    }

    @Override
    public List<RealWarehouse> queryRealWarehouseByChannelCode(String channelCode) {
        List<VirtualWarehouseE> vwListByChannelCode = virtualWarehouseService.getVwListByChannelCode(channelCode);
        if(CollectionUtils.isNotEmpty(vwListByChannelCode)){
            List<Long> realWarehouseIdList = vwListByChannelCode.stream().map(x -> x.getRealWarehouseId()).distinct().collect(Collectors.toList());
            List<RealWarehouseE> realWarehouseList = realWarehouseRepository.getRealWarehouseByIds(realWarehouseIdList);
            return realWarehouseConvertor.entityToDto(realWarehouseList);
        }
        return null;
    }

    @Override
    public List<StoreDTO> queryFactoryByRwType(Integer realWarehouseType) {
        List<RealWarehouseDO> realWarehouseDOList = realWarehouseRepository.queryFactoryByRwType(realWarehouseType);
        List<String> factoryCodes = new ArrayList<>();
        realWarehouseDOList.forEach(realWarehouseE -> factoryCodes.add(realWarehouseE.getFactoryCode()));
        List<StoreDTO> storeDTOList = shopFacade.searchByCodeList(factoryCodes);
        return storeDTOList;
    }

    @Override
    public void addOrUpdateInformation(RealWarehouseAddition realWarehouseAdditionDTO) {
        RealWarehouseAddition realWarehouseAddition= realWarehouseAdditionRepository.getByRealWarehouseId(realWarehouseAdditionDTO.getRealWarehouseId());
        if(StringUtils.isEmpty(realWarehouseAdditionDTO.getName())){
            throw new RomeException(ResCode.STOCK_ERROR_1017,"保税仓名称不能为空");
        }
        if(StringUtils.isEmpty(realWarehouseAdditionDTO.getStoreCode())){
            throw new RomeException(ResCode.STOCK_ERROR_1017,"保税仓编号不能为空");
        }
        if(StringUtils.isEmpty(realWarehouseAdditionDTO.getAreaCode())){
            throw new RomeException(ResCode.STOCK_ERROR_1017,"保税区编号不能为空");
        }
        if(StringUtils.isEmpty(realWarehouseAdditionDTO.getAreaName())){
            throw new RomeException(ResCode.STOCK_ERROR_1017,"保税区名称不能为空");
        }
        if(StringUtils.isEmpty(realWarehouseAdditionDTO.getCustomsCode())){
            throw new RomeException(ResCode.STOCK_ERROR_1017,"海关编号不能为空");
        }
        if(StringUtils.isEmpty(realWarehouseAdditionDTO.getCustomsName())){
            throw new RomeException(ResCode.STOCK_ERROR_1017,"海关名称不能为空");
        }
        if (null == realWarehouseAddition && null == realWarehouseAdditionDTO.getId()) {
            int j=realWarehouseAdditionRepository.addRealWarehouseAddition(realWarehouseAdditionDTO);
            if(j==0){
                throw new RomeException(ResCode.STOCK_ERROR_1017,"新增数据失败");
            }
        }else{
            int j=realWarehouseAdditionRepository.updateRealWarehouseAddition(realWarehouseAdditionDTO);
            if(j==0){
                throw new RomeException(ResCode.STOCK_ERROR_1017,"修改数据失败");
            }
        }
    }

    @Override
    public RealWarehouseAddition getByRealWarehouseId(Long realWarehouseId) {
        RealWarehouseAddition realWarehouseAddition= realWarehouseAdditionRepository.getByRealWarehouseId(realWarehouseId);
	    return realWarehouseAddition;
    }

    @Override
    public String createKpRwRelation(String realWarehouseIds) {
	    if (StringUtils.isBlank(realWarehouseIds)) {
	        return null;
        }
	    StringBuilder result = new StringBuilder("失败的仓库id:\n");
        String[] realWarehouseIdArr = StringUtils.split(realWarehouseIds, ",");
        boolean hasFail = false;
        for (String realWarehouseIdStr : realWarehouseIdArr) {
            if (StringUtils.isBlank(realWarehouseIdStr)) {
                continue;
            }
            //判断是否存在.存在直接下一个
            KpRwRelationDo kpRwRelationDo = kpRwRelationRepository.getByRealWarehouseId(Long.parseLong(realWarehouseIdStr));
            if (kpRwRelationDo != null) {
                continue;
            }
            try {
                //不存在的做新增操作
                RealWarehouseE realWarehouseE = realWarehouseRepository.getRealWarehouseById(Long.parseLong(realWarehouseIdStr));
                kpRwRelationRepository.saveRelation(realWarehouseE);
            } catch (Exception e) {
                log.error("保存关联关系失败", e);
                hasFail = true;
                result.append(realWarehouseIdStr);
                result.append(",");
            }
        }
        return hasFail ? result.toString() : null;
    }

    @Override
    public void deleteKpRwRelation(String realWarehouseIds) {
        if (StringUtils.isBlank(realWarehouseIds)) {
            return;
        }
        String[] realWarehouseIdArr = StringUtils.split(realWarehouseIds, ",");
        for (String realWarehouseIdStr : realWarehouseIdArr) {
            if (StringUtils.isBlank(realWarehouseIdStr)) {
                continue;
            }
            kpRwRelationRepository.deleteByRealWarehouseId(Long.parseLong(realWarehouseIdStr));
        }
    }

    @Override
    public List<StoreDTO> queryFactoryByRwTypeList(List<Integer> realWarehouseTypeList) {
        if (CollectionUtils.isEmpty(realWarehouseTypeList)) {
            return Collections.EMPTY_LIST;
        }
        List<RealWarehouseDO> realWarehouseDOList = realWarehouseRepository.queryFactoryByRwTypeList(realWarehouseTypeList);
        if (CollectionUtils.isEmpty(realWarehouseDOList)) {
            return Collections.EMPTY_LIST;
        }
        List<String> factoryCodes = new ArrayList<>();
        realWarehouseDOList.forEach(realWarehouseE -> factoryCodes.add(realWarehouseE.getFactoryCode()));
        List<StoreDTO> storeDTOList = shopFacade.searchByCodeList(factoryCodes);
        return storeDTOList;
    }

    @Override
    public List<RealWarehouse> queryRealReturnWarehouses() {
        return realWarehouseMapper.queryRealReturnWarehouses();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void addOrUpdateReturnConfig(RealReturnWarehouseDTO realReturnWarehouseDTO) {
        if (null == realReturnWarehouseDTO.getReturnWarehouseId()) {
            throw new RomeException(ResCode.STOCK_ERROR_1001, "退货仓编号不能为空");
        }
        if (null == realReturnWarehouseDTO.getRealWarehouseId()) {
            throw new RomeException(ResCode.STOCK_ERROR_1001, "仓编号不能为空");
        }
        //当前仓库的退货仓必须和当前仓库的公司一致
        RealWarehouseE realWarehouse = realWarehouseRepository.getRealWarehouseById(realReturnWarehouseDTO.getRealWarehouseId());
        if (null == realWarehouse) {
            throw new RomeException(ResCode.STOCK_ERROR_1001, "当前仓库不存在");
        }
        RealWarehouseE realWarehouseReturn = realWarehouseRepository.getRealWarehouseById(realReturnWarehouseDTO.getReturnWarehouseId());
        if (null == realWarehouseReturn) {
            throw new RomeException(ResCode.STOCK_ERROR_1001, "当前仓库对应的退货仓不存在");
        }
        if (!StringUtils.isEmpty(realWarehouse.getCompanyCode()) && !Objects.equals(realWarehouseReturn.getCompanyCode(), realWarehouse.getCompanyCode())) {
            throw new RomeException(ResCode.STOCK_ERROR_1001, "当前仓库与对应的退货仓公司编码不一样");
        }
        int j = realWarehouseRepository.updateReturnWarehouse(realReturnWarehouseDTO);
        if (j == 0) {
            throw new RomeException(ResCode.STOCK_ERROR_1001, "操作失败");
        }
    }

    @Override
    public List<RealWarehouse> queryByTypeForFrontend(Integer type) {
        return realWarehouseRepository.queryByTypeForFrontend(type);
    }

	@Override
	public List<RealWarehouse> queryByCodeListAndFactoryCodeList(RealWarehouseAdminParamDTO paramDTO) {
		Integer pageSize = paramDTO.getPageSize();
		if(pageSize == null) {
			pageSize = 200;
		}
		List<RealWarehouseE> result = realWarehouseRepository.queryByCodeListAndFactoryCodeList(paramDTO.getRealWarehouseCodeList(), paramDTO.getFactoryCodeList(), pageSize);
		return realWarehouseConvertor.entityToDto(result);
	}

    @Override
    public List<RealWarehouse> queryRealWarehouseLimitWarehouse(List<String> factoryCodes) {
        List<RealWarehouseE> realWarehouseES = realWarehouseRepository.queryRealWarehouseLimitWarehouse(factoryCodes);
        return realWarehouseConvertor.entityToDto(realWarehouseES);
    }

    /**
     * MDM依据SKU编码、物资存储类型查询仓库库存传参DTO
     * @param list
     * @return
     */
    @Override
    @TargetDataSource(DynamicDataSourceEnum.READ)
    public List<RealWarehouseStockDTO> queryRWStockBySkuCodeAndMaterialStorageType(List<QueryRealWarehouseStock> list) {
        if (CollUtil.isEmpty(list)) {
            return Collections.EMPTY_LIST;
        }
        if (list.size() > 100) {
            throw new RomeException(ResCode.STOCK_ERROR_1002, "每次处理数量不大于100条！");
        }
        List<String> transportUnitSkuCodeList = new ArrayList<>();
        List<String> basicUnitSkuCodeList = new ArrayList<>();
        for (QueryRealWarehouseStock queryRealWarehouseStock : list) {
            if (Objects.equals(queryRealWarehouseStock.getMaterialStorageType(), WarehouseMaterialStorageTypeVO.TRANSPORT_UNIT.getType())) {
                transportUnitSkuCodeList.add(queryRealWarehouseStock.getSkuCode());
            } else if (Objects.equals(queryRealWarehouseStock.getMaterialStorageType(), WarehouseMaterialStorageTypeVO.BASIC_UNIT.getType())) {
                basicUnitSkuCodeList.add(queryRealWarehouseStock.getSkuCode());
            } else {
                throw new RomeException(ResCode.STOCK_ERROR_1002, "物资存储类型传参错误！");
            }
        }
        List<RealWarehouseE> realWarehouseEList = realWarehouseRepository.queryAllMaterialStorageTypeWarehouse();
        Map<Long, RealWarehouseE> realWarehouseMap = new HashMap<>();
        List<Long> transportUnitRealWarehouseIdList = new ArrayList<>();
        List<Long> basicUnitRealWarehouseIdList  = new ArrayList<>();
        for(RealWarehouseE realWarehouse : realWarehouseEList){
            realWarehouseMap.put(realWarehouse.getId(),realWarehouse);
            // 存储运输单位仓的仓
            if (Objects.equals(realWarehouse.getMaterialStorageType(), WarehouseMaterialStorageTypeVO.TRANSPORT_UNIT.getType())) {
                transportUnitRealWarehouseIdList.add(realWarehouse.getId());
            } else if (Objects.equals(realWarehouse.getMaterialStorageType(), WarehouseMaterialStorageTypeVO.BASIC_UNIT.getType())) {
                // 存储基本单位仓的仓id
                basicUnitRealWarehouseIdList.add(realWarehouse.getId());
            }
        }
        //查询sku库存信息 (仓库有651个，物料最多100个，则最多会查询出 65100条记录)，线上查询多为一个物料，所以暂时先不分页
        List<RealWarehouseStockDTO> result = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(transportUnitSkuCodeList) && CollectionUtils.isNotEmpty(transportUnitRealWarehouseIdList)) {
            List<RealWarehouseStockDTO> realWarehouseStockList = realWarehouseRepository.queryRealWarehouseStockBySkuCodeAndRealWarehouseId(transportUnitSkuCodeList, transportUnitRealWarehouseIdList);
            if (CollectionUtils.isNotEmpty(realWarehouseStockList)) {
                result.addAll(realWarehouseStockList);
            }
        }
        if (CollectionUtils.isNotEmpty(basicUnitSkuCodeList) && CollectionUtils.isNotEmpty(basicUnitRealWarehouseIdList)) {
            List<RealWarehouseStockDTO> realWarehouseStockList = realWarehouseRepository.queryRealWarehouseStockBySkuCodeAndRealWarehouseId(basicUnitSkuCodeList, basicUnitRealWarehouseIdList);
            if (CollectionUtils.isNotEmpty(realWarehouseStockList)) {
                result.addAll(realWarehouseStockList);
            }
        }
        for (RealWarehouseStockDTO realWarehouseStockDTO : result) {
            if (realWarehouseMap.containsKey(realWarehouseStockDTO.getRealWarehouseId())) {
                RealWarehouseE realWarehouse = realWarehouseMap.get(realWarehouseStockDTO.getRealWarehouseId());
                realWarehouseStockDTO.setFactoryCode(realWarehouse.getFactoryCode());
                realWarehouseStockDTO.setRealWarehouseOutCode(realWarehouse.getRealWarehouseOutCode());
            }
        }
        return result;
    }

    @Override
    @TargetDataSource(DynamicDataSourceEnum.READ)
    public List<RealWarehouse> queryWarehousrForInner(QueryForInnerProDto query) {
        List<RealWarehouseE> realWarehouseES = realWarehouseRepository.queryWarehousrForInner(query);
        return realWarehouseConvertor.entityToDto(realWarehouseES);
    }

    @Override
    public PageInfo<RealWarehouse> queryNotInTypesFactory(QueryNotInTypesFactoryDto paramDTO){
        Page page = PageHelper.startPage(paramDTO.getPageIndex(), paramDTO.getPageSize());
        List<RealWarehouseE> realWarehouseES = realWarehouseRepository.queryFactoryByNotRwTypeList(paramDTO.getNotInTypes());
        List<RealWarehouse> list=realWarehouseConvertor.entityToDto(realWarehouseES);
        PageInfo<RealWarehouse> pageInfo = new PageInfo<>(page.getResult());
        pageInfo.setList(list);
        pageInfo.setTotal(page.getTotal());
        return pageInfo;
    }

    @Override
    public List<RealWarehouse> queryRealWarehouseByWmsCode(Integer wmsCode) {
        List<Long> realWarehouseIds = realWarehouseWmsConfigRepository.getRealWarehouseWmsConfigByWmsCodes(Arrays.asList(wmsCode));
        if (CollectionUtils.isEmpty(realWarehouseIds)) {
            return null;
        }
        List<RealWarehouseDO> realWarehouseDOS = realWarehouseMapper.queryWarehouseByIds(realWarehouseIds);
        return realWarehouseConvertor.dosToDTOs(realWarehouseDOS);
    }

    @Override
    public List<RealWarehouse> queryRealWarehouseListByCompanyCode(String companyCode) {
        List<RealWarehouseDO> realWarehouseDOS = realWarehouseMapper.queryRealWarehouseListByCompanyCode(companyCode);
        return realWarehouseConvertor.dosToDTOs(realWarehouseDOS);
    }

    @Override
    public List<RealWarehouse> queryRealWarehouseByIdList(List<Long> warehouseIdList) {
        if (CollectionUtils.isNotEmpty(warehouseIdList)) {
            List<RealWarehouseDO> realWarehouseDOS = realWarehouseMapper.queryWarehouseByIds(warehouseIdList);
            return realWarehouseConvertor.dosToDTOs(realWarehouseDOS);
        }
        return Collections.emptyList();
    }

    @Override
    public List<Long> querySpecialWarehouseIdList() {
        List<Long> realWarehouseIdList = realWarehouseMapper.querySpecialWarehouseIdList();
        return realWarehouseIdList;
    }

    @Override
    public List<RealWarehouse> querySpecialRealWarehouseList() {
        List<RealWarehouseDO> realWarehouseDOS = realWarehouseMapper.querySpecialRealWarehouseList();
        return realWarehouseConvertor.dosToDTOs(realWarehouseDOS);
    }

    @Override
    public List<RealWarehouse> queryRealWarehouseListLikeName(String realWarehouseName) {
        List<RealWarehouse> list = realWarehouseMapper.queryRealWarehouseListLikeName(realWarehouseName);
        return list;
    }
}
