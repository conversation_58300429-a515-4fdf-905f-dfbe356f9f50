package com.rome.stock.innerservice.domain.entity.frontrecord;

import com.rome.arch.core.domain.EntityFactory;
import com.rome.stock.common.constants.front.InventoryAdjustConsts;
import com.rome.stock.innerservice.common.AlikAssert;
import com.rome.stock.innerservice.common.SkuQtyUnitTools;
import com.rome.stock.innerservice.constant.FrontRecordTypeVO;
import com.rome.stock.core.domain.repository.CoreRealWarehouseStockRepository;
import com.rome.stock.innerservice.domain.repository.frontrecord.FrInventoryAdjustRepository;
import com.rome.stock.innerservice.domain.repository.warehouserecord.WarehouseRecordRepository;
import com.rome.stock.innerservice.domain.service.RealWarehouseService;
import com.rome.stock.innerservice.remote.sap.facade.SapFacade;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;

/**
 * 库存损益调整单
 */
@Component
@Scope("prototype")
@Data
@Slf4j
@EqualsAndHashCode(callSuper = false)
public class InventoryAdjustRecordE extends AbstractFrontRecord{

    @Resource
    private EntityFactory entityFactory;
    @Resource
    private SkuQtyUnitTools skuQtyUnitTools;
    @Autowired
    private CoreRealWarehouseStockRepository coreRealWarehouseStockRepository;
    @Resource
    private RealWarehouseService realWarehouseService;
    @Resource
    private FrInventoryAdjustRepository frInventoryAdjustRepository;
    @Resource
    private SapFacade sapFacade;
    @Resource
    private WarehouseRecordRepository warehouseRecordRepository;

    /**
     * 生成损益调整单
     */
    public void addFrontRecord() {
        //设置单据类型
        if (this.getReasonCode() == InventoryAdjustConsts.WIODAR_REASONCODE){
            this.setRecordType(FrontRecordTypeVO.WAREHOUSE_INCREASE_OR_DECREASE_ADJUST_RECORD.getType());
            initFrontRecord(FrontRecordTypeVO.WAREHOUSE_INCREASE_OR_DECREASE_ADJUST_RECORD.getCode(),this.frontRecordDetails);
        } else if (this.getReasonCode() == InventoryAdjustConsts.QCAR_REASONCODE){
            this.setRecordType(FrontRecordTypeVO.QUALITY_CHANGE_ADJUST_RECORD.getType());
            initFrontRecord(FrontRecordTypeVO.QUALITY_CHANGE_ADJUST_RECORD.getCode(),this.frontRecordDetails);
        } else if(this.getReasonCode() == InventoryAdjustConsts.PMOR_REASONCODE) {
            this.setRecordType(FrontRecordTypeVO.PACKAGE_MATERIAL_OUT_RECORD.getType());
            initFrontRecord(FrontRecordTypeVO.PACKAGE_MATERIAL_OUT_RECORD.getCode(),this.frontRecordDetails);
        } else if(this.getReasonCode() == InventoryAdjustConsts.PMIR_REASONCODE) {
            this.setRecordType(FrontRecordTypeVO.PACKAGE_MATERIAL_IN_RECORD.getType());
            initFrontRecord(FrontRecordTypeVO.PACKAGE_MATERIAL_IN_RECORD.getCode(),this.frontRecordDetails);
        } else if(this.getReasonCode() == InventoryAdjustConsts.SRSW_REASONCODE) {
            this.setRecordType(FrontRecordTypeVO.SHOP_RETURN_INVENTORY_RECORD.getType());
            initFrontRecord(FrontRecordTypeVO.SHOP_RETURN_INVENTORY_RECORD.getCode(),this.frontRecordDetails);
        }

        Long id = frInventoryAdjustRepository.addInventoryAdjustRecord(this);
        AlikAssert.isTrue(id > 0, "999", "创建损益调整单失败");
        this.setId(id);
        this.frontRecordDetails.forEach(record ->record.setFrontRecordDetail(this));
        //生成损益调整详情
        frInventoryAdjustRepository.addInventoryAdjustRecordDetail(this.frontRecordDetails);
    }

    /**
     * 将前置单信息封装为wms需要的参数
     * @param recordE
     * @return
     */
    /*public ZtInventoryAdjustDTO frRecordTOZtAdjustDto(InventoryAdjustRecordE recordE){
        SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        ZtInventoryAdjustDTO ztInventoryAdjustDTO = new ZtInventoryAdjustDTO();
        if(recordE.getRecordType().equals(FrontRecordTypeVO.WAREHOUSE_INCREASE_OR_DECREASE_ADJUST_RECORD.getType())){
            ztInventoryAdjustDTO.setReason(FrontRecordTypeVO.WAREHOUSE_INCREASE_OR_DECREASE_ADJUST_RECORD.getCode());
        }
        ztInventoryAdjustDTO.setRemark(recordE.getRemark());
        ztInventoryAdjustDTO.setCreateTime(dateFormat.format(recordE.getCreateTime()));
        ztInventoryAdjustDTO.setAdjustCode("库存调整编码");

        //根据仓库id查询仓库信息
        RealWarehouse realWarehouse = realWarehouseService.findByRealWarehouseId(recordE.getRealWarehouseId());
        ztInventoryAdjustDTO.setOwnerCode(realWarehouse.getFactoryCode());
        ztInventoryAdjustDTO.setWarehouseCode(realWarehouse.getRealWarehouseOutCode());

        List<ZtInventoryAdjustItem> items = new ArrayList<>();
        for (InventoryAdjustRecordDetailE detailE : recordE.getFrontRecordDetails()) {
            ZtInventoryAdjustItem item = new ZtInventoryAdjustItem();
            item.setItemCode(detailE.getSkuCode());
            item.setUnit(detailE.getUnitCode());
            item.setInventoryType("ZP");
            if(detailE.getAdjustType().equals(FrontRecordDetailStatusVO.WAREHOUSE_ADJUST_INCREASE.getStatus())){
                item.setQty(detailE.getSkuQty().setScale(0, BigDecimal.ROUND_DOWN).intValue());            }
            if(detailE.getAdjustType().equals(FrontRecordDetailStatusVO.WAREHOUSE_ADJUST_DISCREASE.getStatus())){
                item.setQty(-detailE.getSkuQty().setScale(0,BigDecimal.ROUND_DOWN).intValue());
            }

            items.add(item);
        }

        return ztInventoryAdjustDTO;
    }*/



    /**
     * 实仓ID
     */
    private Long realWarehouseId;

    /**
     *  业务原因
     */
    private Integer reasonCode;

    /**
     * 备注
     */
    private String remark;

    /**
     * OA审批号
     */
    private String approveOACode;

    /**
     * 开始时间
     */
    private Date startTime;

    /**
     * 结束时间
     */
    private Date endTime;

    /**
     * 外部系统单据编号
     */
    private String outRecordCode;

    /**
     * SAP过账单号
     */
    private String sapRecordCode;

    /**
     * 门店编码
     */
    private String shopCode;

    /**
     * 调整明细
     */
    private List<InventoryAdjustRecordDetailE> frontRecordDetails;
}
