package com.rome.stock.innerservice.domain.entity.warehouserecord;

import com.alibaba.fastjson.JSON;
import com.rome.arch.core.domain.EntityFactory;
import com.rome.arch.core.exception.RomeException;
import com.rome.stock.common.enums.warehouse.WarehouseRecordTypeVO;
import com.rome.stock.innerservice.api.dto.warehouserecord.*;
import com.rome.stock.innerservice.common.AlikAssert;
import com.rome.stock.innerservice.common.ResCode;
import com.rome.stock.innerservice.common.RomeCollectionUtil;
import com.rome.stock.innerservice.common.SkuQtyUnitTools;
import com.rome.stock.innerservice.constant.WarehouseRecordBusinessTypeVO;
import com.rome.stock.innerservice.constant.WarehouseRecordStatusVO;
import com.rome.stock.innerservice.constant.WmsSyncStatusVO;
import com.rome.stock.core.domain.repository.CoreRealWarehouseStockRepository;
import com.rome.stock.innerservice.domain.repository.RealWarehouseRepository;
import com.rome.stock.innerservice.domain.repository.warehouserecord.ShopReplenishWarehouseRepository;
import com.rome.stock.innerservice.domain.repository.warehouserecord.WarehouseRecordRepository;
import com.rome.stock.core.infrastructure.dataobject.core.*;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 门店补货单
 *
 * <AUTHOR> 2019/4/17 20:52
 */
@Data
@Slf4j
@Component
@Scope("prototype")
@EqualsAndHashCode(callSuper = false)
public class ShopReplenishWarehouseRecordE extends AbstractWarehouseRecord{

    @Resource
    private EntityFactory entityFactory;

    @Resource
    private ShopReplenishWarehouseRepository shopReplenishWarehouseRepository;
    @Autowired
    private CoreRealWarehouseStockRepository coreRealWarehouseStockRepository;
    @Resource
    private SkuQtyUnitTools skuQtyUnitTools;
    @Resource
    private RealWarehouseRepository realWarehouseRepository;
    @Resource
    private WarehouseRecordRepository warehouseRecordRepository;

    /**
     * 加盟门店---大仓出库单
     */
    public void initRWOutRecord(OutWarehouseRecordDTO dto) {
        this.setBusinessType(WarehouseRecordBusinessTypeVO.OUT_WAREHOUSE_RECORD.getType());
        this.setRecordType(dto.getRecordType());
        this.setRecordStatus(WarehouseRecordStatusVO.INIT.getStatus());
        this.setChannelCode(dto.getChannelCode());
        this.setOutCreateTime(new Date());
        //设置wms下发状态,在派车后修改为待下发
        this.setSyncWmsStatus(WmsSyncStatusVO.NO_REQUIRED.getStatus());
        this.setWarehouseRecordDetails(new ArrayList<>(dto.getDetailList().size()));
        //单位换算
        for(RecordDetailDTO detailE : dto.getDetailList()){
            WarehouseRecordDetail warehouseRecordDetail = entityFactory.createEntity(WarehouseRecordDetail.class);
            warehouseRecordDetail.setSkuCode(detailE.getSkuCode());
            //此处设置计划数量和实际数量一致，wms回调再更新实际数量，无需wms回调的业务，直接设置成一致即可
            warehouseRecordDetail.setPlanQty(detailE.getBasicSkuQty());
            warehouseRecordDetail.setActualQty(BigDecimal.ZERO);
            warehouseRecordDetail.setUnit(detailE.getBasicUnit());
            warehouseRecordDetail.setUnitCode(detailE.getBasicUnitCode());
            warehouseRecordDetail.setSapPoNo(detailE.getSapPoNo());
			warehouseRecordDetail.setLineNo(detailE.getLineNo());
            warehouseRecordDetail.setDeliveryLineNo(detailE.getDeliveryLineNo());
            this.warehouseRecordDetails.add(warehouseRecordDetail);
        }
        //设置skuCode和skuID
        this.initWarehouseRecodeDetail();
    }

    /**
     * 组装数据，插入数据库
     * @param warehouseRecordE
     */
    private void addRecord(WarehouseRecordE warehouseRecordE) {
        this.setWarehouseRecordDetails(new ArrayList<>());
        for(WarehouseRecordDetail detailE : warehouseRecordE.getWarehouseRecordDetails()){
            WarehouseRecordDetail warehouseRecordDetail = entityFactory.createEntity(WarehouseRecordDetail.class);
            warehouseRecordDetail.createRecordDetail(detailE);
            //Z仓的应收和实收数量一致
            warehouseRecordDetail.setPlanQty(detailE.getActualQty());
            warehouseRecordDetail.setSapPoNo(detailE.getSapPoNo());
            this.warehouseRecordDetails.add(warehouseRecordDetail);
        }
        //设置skuCode和skuID
        this.initWarehouseRecodeDetail();
        addWarehouseRecord();
    }

    /**
     * 创建入库单
     */
    public void createInRecord(InWarehouseRecordDTO dto, Long realWarehouseId){
        this.setRealWarehouseId(realWarehouseId);
        this.setBusinessType(WarehouseRecordBusinessTypeVO.IN_WAREHOUSE_RECORD.getType());
        this.setRecordStatus(WarehouseRecordStatusVO.INIT.getStatus());
        this.setChannelCode("");
        this.setOutCreateTime(new Date());
        //设置wms下发状态,在派车后修改为待下发
        this.setSyncWmsStatus(WmsSyncStatusVO.NO_REQUIRED.getStatus());
        //设置明细
        this.setWarehouseRecordDetails(new ArrayList<>(dto.getDetailList().size()));
        //单位换算
        String sapPoNo = "";
        for(RecordDetailDTO detailE : dto.getDetailList()){
            WarehouseRecordDetail warehouseRecordDetail = entityFactory.createEntity(WarehouseRecordDetail.class);
            //设置为分配数量
            warehouseRecordDetail.setSkuCode(detailE.getSkuCode());
            warehouseRecordDetail.setPlanQty(detailE.getBasicSkuQty());
            warehouseRecordDetail.setActualQty(BigDecimal.ZERO);
            warehouseRecordDetail.setUnit(detailE.getBasicUnit());
            warehouseRecordDetail.setUnitCode(detailE.getBasicUnitCode());
            warehouseRecordDetail.setSapPoNo(detailE.getSapPoNo());
            warehouseRecordDetail.setLineNo(detailE.getLineNo());
            warehouseRecordDetail.setDeliveryData(detailE.getDeliveryData());
            warehouseRecordDetail.setDeliveryLineNo(detailE.getDeliveryLineNo());
            this.warehouseRecordDetails.add(warehouseRecordDetail);
            sapPoNo = detailE.getSapPoNo();
        }
        if(WarehouseRecordTypeVO.SHOP_CHAIN_DIRECT_IN_RECORD.getType().equals(this.getRecordType())){
            this.setSapOrderCode(sapPoNo);
        }else {
            this.setSapOrderCode(dto.getSapOrderCode());
        }
        //设置skuCode和skuID
        this.initWarehouseRecodeDetail();
    }


    /**
     * 直营门店指定仓库补货创建大仓出库单
     */
    public void partLockStockAndReturn(OutWarehouseRecordDTO dto, Long realWarehouseId,Long virtualWarehouseId){
        this.setRecordType(dto.getRecordType());
        this.setRealWarehouseId(realWarehouseId);
        this.setBusinessType(WarehouseRecordBusinessTypeVO.OUT_WAREHOUSE_RECORD.getType());
        this.setRecordStatus(WarehouseRecordStatusVO.INIT.getStatus());
        this.setChannelCode(dto.getChannelCode());
        this.setOutCreateTime(new Date());
        //设置wms下发状态,在派车后修改为待下发
        this.setSyncWmsStatus(WmsSyncStatusVO.NO_REQUIRED.getStatus());
        this.setVirtualWarehouseId(virtualWarehouseId);
        //设置明细
        this.setWarehouseRecordDetails(new ArrayList<>(dto.getDetailList().size()));
        //单位换算
        for(RecordDetailDTO detailE : dto.getDetailList()){
            WarehouseRecordDetail warehouseRecordDetail = entityFactory.createEntity(WarehouseRecordDetail.class);
            //设置为分配数量
            warehouseRecordDetail.setSkuCode(detailE.getSkuCode());
            warehouseRecordDetail.setPlanQty(detailE.getBasicSkuQty());
            warehouseRecordDetail.setActualQty(BigDecimal.ZERO);
            warehouseRecordDetail.setUnit(detailE.getBasicUnit());
            warehouseRecordDetail.setUnitCode(detailE.getBasicUnitCode());
            warehouseRecordDetail.setSapPoNo(detailE.getSapPoNo());
            warehouseRecordDetail.setLineNo(detailE.getLineNo());
            warehouseRecordDetail.setRealWarehouseId(realWarehouseId);
            warehouseRecordDetail.setDeliveryLineNo(detailE.getDeliveryLineNo());
            warehouseRecordDetail.setVirtualWarehouseId(virtualWarehouseId);
            this.warehouseRecordDetails.add(warehouseRecordDetail);
        }
        //设置skuCode和skuID
        this.initWarehouseRecodeDetail();
    }


    /**
     * 直营门店指定仓库补货创建大仓出库单
     */
    public void dsOutRecordAssignWh(OutWarehouseRecordDTO dto, Long realWarehouseId){
        this.setRecordType(dto.getRecordType());
        this.setRealWarehouseId(realWarehouseId);
        this.setBusinessType(WarehouseRecordBusinessTypeVO.OUT_WAREHOUSE_RECORD.getType());
        this.setRecordStatus(WarehouseRecordStatusVO.INIT.getStatus());
        this.setChannelCode("");
        this.setOutCreateTime(new Date());
        //设置wms下发状态,在派车后修改为待下发
        this.setSyncWmsStatus(WmsSyncStatusVO.NO_REQUIRED.getStatus());
        //设置明细
        this.setWarehouseRecordDetails(new ArrayList<>(dto.getDetailList().size()));
        //单位换算
        for(RecordDetailDTO detailE : dto.getDetailList()){
            WarehouseRecordDetail warehouseRecordDetail = entityFactory.createEntity(WarehouseRecordDetail.class);
            //设置为分配数量
            warehouseRecordDetail.setSkuCode(detailE.getSkuCode());
            warehouseRecordDetail.setPlanQty(detailE.getBasicSkuQty());
            warehouseRecordDetail.setActualQty(BigDecimal.ZERO);
            warehouseRecordDetail.setUnit(detailE.getBasicUnit());
            warehouseRecordDetail.setUnitCode(detailE.getBasicUnitCode());
            warehouseRecordDetail.setSapPoNo(detailE.getSapPoNo());
            warehouseRecordDetail.setLineNo(detailE.getLineNo());
            warehouseRecordDetail.setDeliveryLineNo(detailE.getDeliveryLineNo());
            this.warehouseRecordDetails.add(warehouseRecordDetail);
        }
        //设置skuCode和skuID
        this.initWarehouseRecodeDetail();
    }



    public void addWarehouseRecord(){
        long id = shopReplenishWarehouseRepository.saveReplenishWarehouseRecord(this);
        this.setId(id);
        this.warehouseRecordDetails.forEach(wrRecord -> wrRecord.setWarehouseRecordDetail(this));
        shopReplenishWarehouseRepository.saveWarehouseRecordDetails(this.warehouseRecordDetails);
    }

    /**
     * 冷链直送大仓出库单
     */
    public void addCodeChainOutOrder(OutWarehouseRecordDTO dto, Long realWarehouseId){
        //冷链
        this.setRecordType(dto.getRecordType());
        //设置wms下发状态,在派车后修改为待下发
        this.setSyncWmsStatus(WmsSyncStatusVO.NO_REQUIRED.getStatus());
        this.setBusinessType(WarehouseRecordBusinessTypeVO.OUT_WAREHOUSE_RECORD.getType());
        this.setRealWarehouseId(realWarehouseId);
        this.setRecordStatus(WarehouseRecordStatusVO.INIT.getStatus());
        this.setChannelCode("");
        this.setOutCreateTime(new Date());
        //设置明细
        this.setWarehouseRecordDetails(new ArrayList<>(dto.getDetailList().size()));
        //单位换算
        for(RecordDetailDTO detailE : dto.getDetailList()){
            WarehouseRecordDetail warehouseRecordDetail = entityFactory.createEntity(WarehouseRecordDetail.class);
            //设置为分配数量
            warehouseRecordDetail.setSkuCode(detailE.getSkuCode());
            warehouseRecordDetail.setPlanQty(detailE.getBasicSkuQty());
            warehouseRecordDetail.setActualQty(BigDecimal.ZERO);
            warehouseRecordDetail.setUnit(detailE.getBasicUnit());
            warehouseRecordDetail.setUnitCode(detailE.getBasicUnitCode());
            warehouseRecordDetail.setSapPoNo(detailE.getSapPoNo());
            warehouseRecordDetail.setLineNo(detailE.getLineNo());
            warehouseRecordDetail.setDeliveryLineNo(detailE.getDeliveryLineNo());
            this.warehouseRecordDetails.add(warehouseRecordDetail);
        }
        //设置skuCode和skuID
        this.initWarehouseRecodeDetail();
    }


    /**
     * 供应商直送---大仓出库单
     */
    public void createSupplierDirectOutRecord(OutWarehouseRecordDTO dto, Long realWarehouseId) {
         //冷链
        this.setRecordType(dto.getRecordType());
        //设置wms下发状态,在派车后修改为待下发
        this.setSyncWmsStatus(WmsSyncStatusVO.NO_REQUIRED.getStatus());
        this.setBusinessType(WarehouseRecordBusinessTypeVO.OUT_WAREHOUSE_RECORD.getType());
        this.setRealWarehouseId(realWarehouseId);
        this.setRecordStatus(WarehouseRecordStatusVO.OUT_ALLOCATION.getStatus());
        this.setChannelCode(dto.getChannelCode());
        this.setOutCreateTime(new Date());
        //设置明细
        this.setWarehouseRecordDetails(new ArrayList<>(dto.getDetailList().size()));
        //单位换算
        String sapPoNo = "";
        for(RecordDetailDTO detailE : dto.getDetailList()){
            WarehouseRecordDetail warehouseRecordDetail = entityFactory.createEntity(WarehouseRecordDetail.class);
            //设置为分配数量
            warehouseRecordDetail.setSkuCode(detailE.getSkuCode());
            warehouseRecordDetail.setPlanQty(detailE.getBasicSkuQty());
            warehouseRecordDetail.setActualQty(BigDecimal.ZERO);
            warehouseRecordDetail.setUnit(detailE.getBasicUnit());
            warehouseRecordDetail.setUnitCode(detailE.getBasicUnitCode());
            warehouseRecordDetail.setSapPoNo(detailE.getSapPoNo());
            warehouseRecordDetail.setLineNo(detailE.getLineNo());
            warehouseRecordDetail.setDeliveryLineNo(detailE.getDeliveryLineNo());
            this.warehouseRecordDetails.add(warehouseRecordDetail);
            sapPoNo = detailE.getSapPoNo();
        }
        this.setSapOrderCode(sapPoNo);
        //设置skuCode和skuID
        this.initWarehouseRecodeDetail();
    }

    /**
     * 更新出库单状态为已出库
     */
    public void updateToOutAllocation(WarehouseRecordE warehouseRecordE) {
        int executeResult = shopReplenishWarehouseRepository.updateToOutAllocation(warehouseRecordE.getId());
        AlikAssert.isTrue(executeResult == 1, ResCode.STOCK_ERROR_1016, ResCode.STOCK_ERROR_1016_DESC);
    }

    /**
     * 门店确认收货
     * @param replenishWarehouseRecordE: 收货单
     * @param record : 交货单
     */
    public void shopConfirmReceipt(ShopReceiptOrderDTO receiptOrder){
       Map<String, ShopReceiptDetailDTO> receiptMap =  RomeCollectionUtil.listforMap(receiptOrder.getWarehouseRecordDetails(), "skuCode");
       //累加修改后置单的收货明细数量
        for (WarehouseRecordDetail recordDetail : this.getWarehouseRecordDetails()) {
            ShopReceiptDetailDTO receiptDetai  = receiptMap.get(recordDetail.getSkuCode());
            if(receiptDetai != null){
                recordDetail.setActualQty(receiptDetai.getActualQty());
            }
            boolean flag = warehouseRecordRepository.increaseActualQtyById(recordDetail.getActualQty(), recordDetail.getId(),
                    recordDetail.getSkuCode(), recordDetail.getRecordCode());
            AlikAssert.isTrue(flag, ResCode.STOCK_ERROR_1002, ResCode.STOCK_ERROR_1002_DESC+"：行号错误");
        }
    }


    /**
     * 封装从上往下出库和释放多锁的库存
     */
    public CoreChannelOrderDO packOutAndUnlockStockObjForUp(WarehouseRecordE warehouseRecordE) {
        CoreChannelOrderDO coreChannelOrderDO=new CoreChannelOrderDO();
        coreChannelOrderDO.setRecordCode(warehouseRecordE.getRecordCode());
        coreChannelOrderDO.setTransType(warehouseRecordE.getRecordType());
        coreChannelOrderDO.setMerchantId(warehouseRecordE.getMerchantId());
        coreChannelOrderDO.setChannelCode(warehouseRecordE.getChannelCode());
        //虚仓信息
        List<CoreVirtualStockOpDO> virtualStockOpDetailDOs = new ArrayList<>();
        List<CoreRealStockOpDetailDO> realStockOpDetailDOs = new ArrayList<>();
        for (WarehouseRecordDetail detail : warehouseRecordE.getWarehouseRecordDetails()) {
            //数量为0的不处理库存的增加和减少
            if(BigDecimal.ZERO.compareTo(detail.getPlanQty()) == 0){
                continue;
            }
            CoreVirtualStockOpDO vmDetailDO = new CoreVirtualStockOpDO();
            vmDetailDO.setUnlockQty(detail.getPlanQty());
            vmDetailDO.setRealQty(detail.getActualQty());
            vmDetailDO.setSkuId(detail.getSkuId());
            vmDetailDO.setSkuCode(detail.getSkuCode());
            vmDetailDO.setVirtualWarehouseId(warehouseRecordE.getVirtualWarehouseId());
            //尽量传
            vmDetailDO.setRealWarehouseId(warehouseRecordE.getRealWarehouseId());
            virtualStockOpDetailDOs.add(vmDetailDO);

            CoreRealStockOpDetailDO realDetailDO = new CoreRealStockOpDetailDO();
            realDetailDO.setUnlockQty(detail.getPlanQty());
            realDetailDO.setRealQty(detail.getActualQty());
            realDetailDO.setSkuId(detail.getSkuId());
            realDetailDO.setSkuCode(detail.getSkuCode());
            realDetailDO.setRealWarehouseId(warehouseRecordE.getRealWarehouseId());
            realStockOpDetailDOs.add(realDetailDO);
        }
        coreChannelOrderDO.setVirtualStockOpDetailDOs(virtualStockOpDetailDOs);
        coreChannelOrderDO.setRealStockOpDetailDOs(realStockOpDetailDOs);
        return coreChannelOrderDO;
    }



    /**
     * 封装从上往下出库和释放多锁的库存
     */
    public CoreRealStockOpDO packOutAndUnlockStockObjForBigOrder(WarehouseRecordE warehouseRecordE,List<RecordDetailDTO> detailList,CoreRealStockOpDO bigCoreRealStockOpDO) {
        List<CoreRealStockOpDetailDO> detailDos = new ArrayList<>();
        List<CoreVirtualStockOpDO> virtualStockByCalculateDOs=new ArrayList<>();
        bigCoreRealStockOpDO.setRecordCode(warehouseRecordE.getRecordCode());
        bigCoreRealStockOpDO.setTransType(warehouseRecordE.getRecordType());
        for (RecordDetailDTO detail : detailList) {
            //数量为0的不处理库存的增加和减少
            if(BigDecimal.ZERO.compareTo(detail.getBasicSkuQty()) == 0){
                continue;
            }
            CoreRealStockOpDetailDO detailDO = new CoreRealStockOpDetailDO();
            detailDO.setUnlockQty(detail.getBasicSkuQty());
            detailDO.setSkuId(detail.getSkuId());
            detailDO.setSkuCode(detail.getSkuCode());
            detailDO.setRealWarehouseId(warehouseRecordE.getRealWarehouseId());
            detailDO.setChannelCode(warehouseRecordE.getChannelCode());
            detailDos.add(detailDO);
            if(null!=warehouseRecordE.getVirtualWarehouseId()){
                CoreVirtualStockOpDO coreVirtualStockOpDO = new CoreVirtualStockOpDO();
                coreVirtualStockOpDO.setRecordCode(warehouseRecordE.getRecordCode());
                coreVirtualStockOpDO.setTransType(warehouseRecordE.getRecordType());
                coreVirtualStockOpDO.setSkuCode(detail.getSkuCode());
                coreVirtualStockOpDO.setSkuId(detail.getSkuId());
                coreVirtualStockOpDO.setUnlockQty(detail.getBasicSkuQty());
                coreVirtualStockOpDO.setVirtualWarehouseId(warehouseRecordE.getVirtualWarehouseId());
                virtualStockByCalculateDOs.add(coreVirtualStockOpDO);
            }
        }
        if(!CollectionUtils.isEmpty(virtualStockByCalculateDOs)){
            bigCoreRealStockOpDO.setVirtualStockByCalculateDOs(virtualStockByCalculateDOs);
        }
        bigCoreRealStockOpDO.setDetailDos(detailDos);
        return bigCoreRealStockOpDO;
    }


    /**
     * 封装从上往下出库和释放多锁的库存
     */
    public CoreRealStockOpDO unlockStockObjForBigOrder(WarehouseRecordE warehouseRecordE,List<RecordDetailDTO> detailList,CoreRealStockOpDO bigCoreRealStockOpDO) {
        List<CoreRealStockOpDetailDO> detailDos = new ArrayList<>();
        List<CoreVirtualStockOpDO> virtualStockByCalculateDOs=new ArrayList<>();
        bigCoreRealStockOpDO.setRecordCode(warehouseRecordE.getRecordCode());
        bigCoreRealStockOpDO.setTransType(warehouseRecordE.getRecordType());
        for (RecordDetailDTO detail : detailList) {
            //数量为0的不处理库存的增加和减少
            if(BigDecimal.ZERO.compareTo(detail.getBasicSkuQty()) == 0){
                continue;
            }
            CoreRealStockOpDetailDO detailDO = new CoreRealStockOpDetailDO();
            detailDO.setUnlockQty(detail.getBasicSkuQty());
            detailDO.setSkuId(detail.getSkuId());
            detailDO.setSkuCode(detail.getSkuCode());
            detailDO.setRealWarehouseId(warehouseRecordE.getRealWarehouseId());
            detailDO.setChannelCode(warehouseRecordE.getChannelCode());
            detailDos.add(detailDO);
            if(null!=warehouseRecordE.getVirtualWarehouseId()){
                CoreVirtualStockOpDO coreVirtualStockOpDO = new CoreVirtualStockOpDO();
                coreVirtualStockOpDO.setRecordCode(warehouseRecordE.getRecordCode());
                coreVirtualStockOpDO.setTransType(warehouseRecordE.getRecordType());
                coreVirtualStockOpDO.setSkuCode(detail.getSkuCode());
                coreVirtualStockOpDO.setSkuId(detail.getSkuId());
                coreVirtualStockOpDO.setUnlockQty(detail.getBasicSkuQty());
                coreVirtualStockOpDO.setVirtualWarehouseId(warehouseRecordE.getVirtualWarehouseId());
                virtualStockByCalculateDOs.add(coreVirtualStockOpDO);
            }
        }
        if(!CollectionUtils.isEmpty(virtualStockByCalculateDOs)){
            bigCoreRealStockOpDO.setVirtualStockByCalculateDOs(virtualStockByCalculateDOs);
        }
        bigCoreRealStockOpDO.setDetailDos(detailDos);
        return bigCoreRealStockOpDO;
    }


    /**
     * 封装从上往下出库和释放多锁的库存
     */
    public CoreRealStockOpDO packUnlockStockObjForBigOrder(WarehouseRecordE warehouseRecordE,CoreRealStockOpDO bigCoreRealStockOpDO) {
        List<CoreRealStockOpDetailDO> detailDos = new ArrayList<>();
        List<CoreVirtualStockOpDO> virtualStockByCalculateDOs=new ArrayList<>();
        bigCoreRealStockOpDO.setRecordCode(warehouseRecordE.getRecordCode());
        bigCoreRealStockOpDO.setTransType(warehouseRecordE.getRecordType());
        for (WarehouseRecordDetail detail : warehouseRecordE.getWarehouseRecordDetails()) {
            CoreRealStockOpDetailDO detailDO = new CoreRealStockOpDetailDO();
            if (this.checkUnLockQty()) {
                detailDO.setUnlockQty(detail.getActualQty());
            }else{
                detailDO.setUnlockQty(detail.getPlanQty());
            }
            if(BigDecimal.ZERO.compareTo(detailDO.getUnlockQty()) == 0){
                continue;
            }
            detailDO.setSkuId(detail.getSkuId());
            detailDO.setSkuCode(detail.getSkuCode());
            detailDO.setRealWarehouseId(warehouseRecordE.getRealWarehouseId());
            detailDO.setChannelCode(warehouseRecordE.getChannelCode());
            detailDos.add(detailDO);

            if(null != warehouseRecordE.getVirtualWarehouseId()){
                CoreVirtualStockOpDO coreVirtualStockOpDO = new CoreVirtualStockOpDO();
                coreVirtualStockOpDO.setRecordCode(warehouseRecordE.getRecordCode());
                coreVirtualStockOpDO.setTransType(warehouseRecordE.getRecordType());
                coreVirtualStockOpDO.setSkuCode(detail.getSkuCode());
                coreVirtualStockOpDO.setSkuId(detail.getSkuId());
                coreVirtualStockOpDO.setUnlockQty(detailDO.getUnlockQty());
                coreVirtualStockOpDO.setVirtualWarehouseId(warehouseRecordE.getVirtualWarehouseId());
                virtualStockByCalculateDOs.add(coreVirtualStockOpDO);
            }
        }
        if(!CollectionUtils.isEmpty(virtualStockByCalculateDOs)){
            bigCoreRealStockOpDO.setVirtualStockByCalculateDOs(virtualStockByCalculateDOs);
        }
        bigCoreRealStockOpDO.setDetailDos(detailDos);
        return bigCoreRealStockOpDO;
    }



    /**
     * 封装从上往下出库和释放锁的库存
     */
    public CoreRealStockOpDO packUnlockStockObj(OutWarehouseRecordDTO outWarehouseRecordDTO) {
        CoreRealStockOpDO coreRealStockOpDO = new CoreRealStockOpDO();
        List<CoreRealStockOpDetailDO> detailDos = new ArrayList<>();
        List<CoreVirtualStockOpDO> virtualStockByCalculateDOs = new ArrayList<>();
        coreRealStockOpDO.setRecordCode(this.getRecordCode());
        coreRealStockOpDO.setTransType(this.getRecordType());
        Map<String, WarehouseRecordDetail> detailMap = this.getWarehouseRecordDetails().stream().collect(Collectors.toMap(WarehouseRecordDetail::getSkuCode, Function.identity(), (v1, v2) -> v1));
        for (RecordDetailDTO detail : outWarehouseRecordDTO.getDetailList()) {
            CoreRealStockOpDetailDO detailDO = new CoreRealStockOpDetailDO();
            if (!detailMap.containsKey(detail.getSkuCode())) {
                throw new RomeException(ResCode.STOCK_ERROR_1002, "skuCode:" + detail.getSkuCode() + "在锁定明细中不存在");
            }
            WarehouseRecordDetail warehouseRecordDetail = detailMap.get(detail.getSkuCode());
            if (detail.getBasicSkuQty().compareTo(warehouseRecordDetail.getActualQty()) > 0) {
                throw new RomeException(ResCode.STOCK_ERROR_1002, "skuCode:" + detail.getSkuCode() + "解锁数量不能大于实际锁定数量");
            }
            detailDO.setUnlockQty(detail.getBasicSkuQty());
            if (BigDecimal.ZERO.compareTo(detailDO.getUnlockQty()) == 0) {
                continue;
            }
            //更新锁定库存
            warehouseRecordDetail.setActualQty(warehouseRecordDetail.getActualQty().subtract(detail.getBasicSkuQty()));
            int j = warehouseRecordRepository.updateActualQtyByDetailId(warehouseRecordDetail);
            if (j == 0) {
                throw new RomeException(ResCode.STOCK_ERROR_1003, "更新明细失败");
            }
            detailDO.setSkuId(warehouseRecordDetail.getSkuId());
            detailDO.setSkuCode(warehouseRecordDetail.getSkuCode());
            detailDO.setRealWarehouseId(this.getRealWarehouseId());
            detailDO.setChannelCode(this.getChannelCode());
            detailDos.add(detailDO);
            if (null != this.getVirtualWarehouseId()) {
                CoreVirtualStockOpDO coreVirtualStockOpDO = new CoreVirtualStockOpDO();
                coreVirtualStockOpDO.setRecordCode(this.getRecordCode());
                coreVirtualStockOpDO.setTransType(this.getRecordType());
                coreVirtualStockOpDO.setSkuCode(warehouseRecordDetail.getSkuCode());
                coreVirtualStockOpDO.setSkuId(warehouseRecordDetail.getSkuId());
                coreVirtualStockOpDO.setUnlockQty(detailDO.getUnlockQty());
                coreVirtualStockOpDO.setVirtualWarehouseId(this.getVirtualWarehouseId());
                virtualStockByCalculateDOs.add(coreVirtualStockOpDO);
            }
        }
        if (!CollectionUtils.isEmpty(virtualStockByCalculateDOs)) {
            coreRealStockOpDO.setVirtualStockByCalculateDOs(virtualStockByCalculateDOs);
        }
        coreRealStockOpDO.setDetailDos(detailDos);
        return coreRealStockOpDO;
    }


    /**
     * 封装从下往上出库和释放多锁的库存
     */
    public CoreRealStockOpDO packOutAndUnlockStockObjForDown(WarehouseRecordE warehouseRecordE){
        List<CoreRealStockOpDetailDO> increaseDetails = new ArrayList<>();
        List<CoreVirtualStockOpDO>  cvsList = new ArrayList<>();
        for (WarehouseRecordDetail detail: warehouseRecordE.getWarehouseRecordDetails()) {
            //数量为0的不处理库存的增加和减少
            if(BigDecimal.ZERO.compareTo(detail.getPlanQty()) == 0){
                continue;
            }
            CoreRealStockOpDetailDO coreRealStockOpDetailDO = new CoreRealStockOpDetailDO();
            coreRealStockOpDetailDO.setSkuId(detail.getSkuId());
            coreRealStockOpDetailDO.setSkuCode(detail.getSkuCode());
            coreRealStockOpDetailDO.setUnlockQty(detail.getPlanQty());
            coreRealStockOpDetailDO.setRealQty(detail.getActualQty());
            coreRealStockOpDetailDO.setRealWarehouseId(warehouseRecordE.getRealWarehouseId());
            coreRealStockOpDetailDO.setCheckBeforeOp(true);
            increaseDetails.add(coreRealStockOpDetailDO);
            if(null != warehouseRecordE.getVirtualWarehouseId()){
                CoreVirtualStockOpDO coreVirtualStockOpDO = new CoreVirtualStockOpDO();
                coreVirtualStockOpDO.setRecordCode(warehouseRecordE.getRecordCode());
                coreVirtualStockOpDO.setTransType(warehouseRecordE.getRecordType());
                coreVirtualStockOpDO.setSkuCode(detail.getSkuCode());
                coreVirtualStockOpDO.setSkuId(detail.getSkuId());
                coreVirtualStockOpDO.setUnlockQty(detail.getPlanQty());
                coreVirtualStockOpDO.setRealQty(detail.getActualQty());
                coreVirtualStockOpDO.setVirtualWarehouseId(warehouseRecordE.getVirtualWarehouseId());
                cvsList.add(coreVirtualStockOpDO);
            }
        }
        CoreRealStockOpDO coreRealStockOpDO = new CoreRealStockOpDO();
        coreRealStockOpDO.setRecordCode(warehouseRecordE.getRecordCode());
        coreRealStockOpDO.setTransType(warehouseRecordE.getRecordType());
        coreRealStockOpDO.setDetailDos(increaseDetails);
        if(!CollectionUtils.isEmpty(cvsList)){
            coreRealStockOpDO.setVirtualStockByCalculateDOs(cvsList);
        }
        return coreRealStockOpDO;
    }
    /**
     * 解锁库存,由上往下
     */
//    public CoreChannelOrderDO packUnlockStockObjForUp() {
//        return packUnlockStockObjForUp(WarehouseRecordConstant.NEED_SYNC_WMS);
//    }

    /**
     * 解锁库存,由上往下
     */
    public CoreChannelOrderDO packUnlockStockObjForUp() {
        CoreChannelOrderDO coreChannelOrderDO=new CoreChannelOrderDO();
        coreChannelOrderDO.setRecordCode(this.getRecordCode());
        coreChannelOrderDO.setTransType(this.getRecordType());
        coreChannelOrderDO.setMerchantId(this.getMerchantId());
        coreChannelOrderDO.setChannelCode(this.getChannelCode());
        //虚仓信息
        List<CoreVirtualStockOpDO> virtualStockOpDetailDOs = new ArrayList<>();
        List<CoreRealStockOpDetailDO> realStockOpDetailDOs = new ArrayList<>();
        for (WarehouseRecordDetail detail : this.getWarehouseRecordDetails()) {
            CoreVirtualStockOpDO vmDetailDO = new CoreVirtualStockOpDO();
            if (this.checkUnLockQty()) {
                vmDetailDO.setUnlockQty(detail.getActualQty());
                vmDetailDO.setRealQty(detail.getActualQty());
            }else{
                vmDetailDO.setUnlockQty(detail.getPlanQty());
                vmDetailDO.setRealQty(detail.getPlanQty());
            }
            //数量为0的不处理库存的增加和减少
            if(BigDecimal.ZERO.compareTo(vmDetailDO.getUnlockQty()) == 0){
                continue;
            }
            vmDetailDO.setSkuId(detail.getSkuId());
            vmDetailDO.setSkuCode(detail.getSkuCode());
            vmDetailDO.setVirtualWarehouseId(this.getVirtualWarehouseId());
            vmDetailDO.setRealWarehouseId(this.getRealWarehouseId());
            virtualStockOpDetailDOs.add(vmDetailDO);

            CoreRealStockOpDetailDO realDetailDO = new CoreRealStockOpDetailDO();
            if (this.checkUnLockQty()) {
                realDetailDO.setUnlockQty(detail.getActualQty());
                realDetailDO.setRealQty(detail.getActualQty());
            }else{
                realDetailDO.setUnlockQty(detail.getPlanQty());
                realDetailDO.setRealQty(detail.getPlanQty());
            }
            //数量为0的不处理库存的增加和减少
            if(BigDecimal.ZERO.compareTo(realDetailDO.getUnlockQty()) == 0){
                continue;
            }
            realDetailDO.setSkuId(detail.getSkuId());
            realDetailDO.setSkuCode(detail.getSkuCode());
            realDetailDO.setRealWarehouseId(this.getRealWarehouseId());
            realStockOpDetailDOs.add(realDetailDO);
        }
        coreChannelOrderDO.setVirtualStockOpDetailDOs(virtualStockOpDetailDOs);
        coreChannelOrderDO.setRealStockOpDetailDOs(realStockOpDetailDOs);
        return coreChannelOrderDO;
    }
    /**
     * 解锁库存,由下往上
     */
//    public CoreRealStockOpDO packUnlockStockObjForDown(){
//        return this.packUnlockStockObjForDown(WarehouseRecordConstant.NEED_SYNC_WMS);
//    }
    /**
     * 解锁库存,由下往上
     */
    public CoreRealStockOpDO packUnlockStockObjForDown(){
        CoreRealStockOpDO stockDO = new CoreRealStockOpDO();
        List<CoreRealStockOpDetailDO> detailDos = new ArrayList<>();
        stockDO.setRecordCode(this.getRecordCode());
        stockDO.setTransType(this.getRecordType());
        stockDO.setDetailDos(detailDos);
        for (WarehouseRecordDetail detail : this.getWarehouseRecordDetails()) {
            CoreRealStockOpDetailDO detailDO = new CoreRealStockOpDetailDO();
            //部分锁定时，未完全锁定
            if (this.checkUnLockQty()) {
                detailDO.setUnlockQty(detail.getActualQty());
            } else {
                detailDO.setUnlockQty(detail.getPlanQty());
            }
            //数量为0的不处理库存的增加和减少
            if(BigDecimal.ZERO.compareTo(detailDO.getUnlockQty()) == 0){
                continue;
            }
            detailDO.setSkuId(detail.getSkuId());
            detailDO.setSkuCode(detail.getSkuCode());
            detailDO.setRealWarehouseId(this.getRealWarehouseId());
            detailDO.setChannelCode(detail.getChannelCode());
            detailDos.add(detailDO);
        }
        return stockDO;
    }





    /**
     * 锁定实体仓库库存对象(按照planQty锁定,特殊需求在serviceImpl中自行封装)
     */
    public CoreChannelOrderDO initLockStockObjWithVm(Long realWarehouseId, Long wmId){
        List<CoreVirtualStockOpDO>  cvsList = new ArrayList<>();
        CoreChannelOrderDO coreChannelOrderDO = new CoreChannelOrderDO();
        //库存交易类型
        coreChannelOrderDO.setRecordCode(this.getRecordCode());
        coreChannelOrderDO.setTransType(this.getRecordType());
        coreChannelOrderDO.setMerchantId(this.getMerchantId());
        coreChannelOrderDO.setChannelCode(this.getChannelCode());
        List<CoreOrderDetailDO> details = new ArrayList<>();
        coreChannelOrderDO.setOrderDetailDOs(details);
        CoreOrderDetailDO detailDO;
        for (WarehouseRecordDetail detailE : this.getWarehouseRecordDetails()) {
            detailDO = new CoreOrderDetailDO();
            detailDO.setLockQty(detailE.getPlanQty());
            detailDO.setSkuId(detailE.getSkuId());
            detailDO.setSkuCode(detailE.getSkuCode());
            details.add(detailDO);
            CoreVirtualStockOpDO coreStockDO = new CoreVirtualStockOpDO();
            coreStockDO.setLockQty(detailE.getPlanQty());
            coreStockDO.setVirtualWarehouseId(wmId);
            coreStockDO.setRealWarehouseId(realWarehouseId);
            coreStockDO.setRecordCode(detailE.getRecordCode());
            coreStockDO.setTransType(coreChannelOrderDO.getTransType());
            coreStockDO.setChannelCode(coreChannelOrderDO.getChannelCode());
            coreStockDO.setMerchantId(detailE.getMerchantId());
            coreStockDO.setSkuId(detailE.getSkuId());
            coreStockDO.setSkuCode(detailE.getSkuCode());
            cvsList.add(coreStockDO);
        }
        if(CollectionUtils.isNotEmpty(cvsList)) {
            coreChannelOrderDO.setVirtualStockOpDetailDOs(cvsList);
        }
        return coreChannelOrderDO;
    }

    /**
     * 增加门店补偿门店单据接口
     * @param receiptOrder
     */
    public void shopConfirmReceiptForCompensate(ShopReceiptOrderDTO receiptOrder, Long realWarehouseId) {
        this.setRealWarehouseId(realWarehouseId);
        this.createRecodeCode(WarehouseRecordTypeVO.COMPENSATE_REPLENISH_IN_WAREHOUSE_RECORD.getCode());
        this.setBusinessType(WarehouseRecordBusinessTypeVO.IN_WAREHOUSE_RECORD.getType());
        this.setRecordStatus(WarehouseRecordStatusVO.INIT.getStatus());
        this.setChannelCode("");
        this.setOutCreateTime(new Date());
        //设置wms下发状态,在派车后修改为待下发
        this.setSyncWmsStatus(WmsSyncStatusVO.NO_REQUIRED.getStatus());
        //设置明细
        this.setWarehouseRecordDetails(new ArrayList<>(receiptOrder.getWarehouseRecordDetails().size()));
        //单位换算
        for(ShopReceiptDetailDTO detailE : receiptOrder.getWarehouseRecordDetails()){
            WarehouseRecordDetail warehouseRecordDetail = entityFactory.createEntity(WarehouseRecordDetail.class);
            //设置为分配数量
            warehouseRecordDetail.setSkuCode(detailE.getSkuCode());
            warehouseRecordDetail.setPlanQty(detailE.getActualQty());
            warehouseRecordDetail.setActualQty(detailE.getActualQty());
            warehouseRecordDetail.setUnit(detailE.getUnit());
            warehouseRecordDetail.setUnitCode(detailE.getUnitCode());
            warehouseRecordDetail.setSapPoNo(detailE.getSapPoNo());
            warehouseRecordDetail.setLineNo(detailE.getLineNo());
            warehouseRecordDetail.setDeliveryData(new Date());
            this.warehouseRecordDetails.add(warehouseRecordDetail);
        }
        //设置skuCode和skuID
        this.initWarehouseRecodeDetail();

    }

    /**
     * 封装从上往下锁实仓DO对象
     */
    public CoreRealStockOpDO  buildMaxableLockStockOrderDo() {
        List<CoreVirtualStockOpDO>  cvsList = new ArrayList<>();
        CoreRealStockOpDO coreChannelOrderDO =new CoreRealStockOpDO();
        coreChannelOrderDO.setRecordCode(this.getRecordCode());
        coreChannelOrderDO.setTransType(this.getRecordType());
        List<CoreRealStockOpDetailDO> coreOrderDetailDOList=new ArrayList<>();
        List<WarehouseRecordDetail> warehouseRecordDetailList=warehouseRecordRepository.queryDetailListByRecordCode(this.getRecordCode());
        Map<String, WarehouseRecordDetail> warehouseRecordDetailMap = warehouseRecordDetailList.stream().collect(Collectors.toMap(x -> x.getSkuCode(), Function.identity(),(v1, v2)->v2));
        for (WarehouseRecordDetail detail : warehouseRecordDetailList) {
            if(detail.getPlanQty().compareTo(BigDecimal.ZERO)>0){
                CoreRealStockOpDetailDO detailDO=new CoreRealStockOpDetailDO();
                detailDO.setSkuId(detail.getSkuId());
                detailDO.setSkuCode(detail.getSkuCode());
                if(warehouseRecordDetailMap.containsKey(detail.getSkuCode())){
                    BigDecimal actualQty=warehouseRecordDetailMap.get(detail.getSkuCode()).getActualQty();
                    detailDO.setLockQty(detail.getPlanQty().subtract(actualQty));
                }else{
                    detailDO.setLockQty(detail.getPlanQty());
                }
                if(detailDO.getLockQty().compareTo(BigDecimal.ZERO)==0){
                    continue;
                }
                detailDO.setRealWarehouseId(detail.getRealWarehouseId());
                coreOrderDetailDOList.add(detailDO);
                //如果虚仓编号存在，使用指定虚仓
//                if(this.getVirtualWarehouseId()!=null){
//                    CoreVirtualStockOpDO coreVirtualStockDO = new CoreVirtualStockOpDO();
//                    coreVirtualStockDO.setLockQty(detail.getPlanQty());
//                    coreVirtualStockDO.setVirtualWarehouseId(detail.getVirtualWarehouseId());
//                    coreVirtualStockDO.setRealWarehouseId(detail.getRealWarehouseId());
//                    coreVirtualStockDO.setRecordCode(this.getRecordCode());
//                    coreVirtualStockDO.setTransType(this.getRecordType());
//                    coreVirtualStockDO.setSkuId(detail.getSkuId());
//                    coreVirtualStockDO.setSkuCode(detail.getSkuCode());
//                    cvsList.add(coreVirtualStockDO);
//                }
            }
        }
        coreChannelOrderDO.setDetailDos(coreOrderDetailDOList);
        coreChannelOrderDO.setVirtualStockByCalculateDOs(cvsList);
        log.info("构建锁定最大库存入参:{}", JSON.toJSONString(coreChannelOrderDO));
        return coreChannelOrderDO;
    }





    /**
     * 封装从上往下锁实仓DO对象
     */
    public CoreRealStockOpDO  packMaxableLockStockOrderDo(List<RecordDetailDTO> lockDetail,WarehouseRecordE warehouseRecordE) {
        List<CoreVirtualStockOpDO>  cvsList = new ArrayList<>();
        CoreRealStockOpDO coreChannelOrderDO =new CoreRealStockOpDO();
        coreChannelOrderDO.setRecordCode(warehouseRecordE.getRecordCode());
        coreChannelOrderDO.setTransType(warehouseRecordE.getRecordType());
        List<CoreRealStockOpDetailDO> coreOrderDetailDOList=new ArrayList<>();
        for (RecordDetailDTO detail : lockDetail) {
            if(detail.getBasicSkuQty().compareTo(BigDecimal.ZERO)>0){
                CoreRealStockOpDetailDO detailDO=new CoreRealStockOpDetailDO();
                detailDO.setSkuId(detail.getSkuId());
                detailDO.setSkuCode(detail.getSkuCode());
                detailDO.setLockQty(detail.getBasicSkuQty());
                if(detailDO.getLockQty().compareTo(BigDecimal.ZERO)==0){
                    continue;
                }
                detailDO.setRealWarehouseId(warehouseRecordE.getRealWarehouseId());
                coreOrderDetailDOList.add(detailDO);
            }
        }
        coreChannelOrderDO.setDetailDos(coreOrderDetailDOList);
        coreChannelOrderDO.setVirtualStockByCalculateDOs(cvsList);
        log.info("构建锁定最大库存入参:{}", JSON.toJSONString(coreChannelOrderDO));
        return coreChannelOrderDO;
    }



    /**
     * 封装库存锁定信息
     */
    public CoreChannelOrderDO warpLockStockDoForChangeInfo() {
        //库存交易类型
        CoreChannelOrderDO coreChannelOrderDO =new CoreChannelOrderDO();
        coreChannelOrderDO.setChannelCode(this.getChannelCode());
        coreChannelOrderDO.setRecordCode(this.getRecordCode());
        coreChannelOrderDO.setTransType(this.getRecordType());
        coreChannelOrderDO.setMerchantId(this.getMerchantId());
        List<CoreVirtualStockOpDO> coreOrderDetailDOList=new ArrayList<>();
        for (WarehouseRecordDetail sku : this.getWarehouseRecordDetails()) {
            if(sku.getPlanQty().compareTo(BigDecimal.ZERO)>0){
                CoreVirtualStockOpDO vmDetailDO = new CoreVirtualStockOpDO();
                vmDetailDO.setSkuId(sku.getSkuId());
                vmDetailDO.setSkuCode(sku.getSkuCode());
                vmDetailDO.setLockQty(sku.getPlanQty());
                vmDetailDO.setRealWarehouseId(sku.getRealWarehouseId());
                vmDetailDO.setRecordCode(this.getRecordCode());
                vmDetailDO.setTransType(this.getRecordType());
                coreOrderDetailDOList.add(vmDetailDO);
            }
        }
        coreChannelOrderDO.setVirtualStockOpDetailDOs(coreOrderDetailDOList);
        log.info("构建锁定负库存入参:{}", JSON.toJSONString(coreChannelOrderDO));
        return coreChannelOrderDO;
    }

}
