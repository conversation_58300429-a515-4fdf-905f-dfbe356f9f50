/**
 * Filename StockChangeNoticeChannelSalesAvailableProducer.java
 * Company 上海来伊份科技有限公司。
 * <AUTHOR>
 * @version 
 */
package com.rome.stock.innerservice.domain.message;

import org.springframework.stereotype.Service;

import com.rome.stock.common.enums.CustomRocketMQEnum;
import com.rome.scm.common.rocketmq.CustomRocketMQProducerClient;
import lombok.extern.slf4j.Slf4j;

/**
 * 库存变化通知,渠道有无可用库存通知
 * <AUTHOR>
 * @since 2021-11-24 15:58:30
 */
@Slf4j
@Service
public class StockChangeNoticeChannelSalesAvailableProducer {
    
    /**
     * 发送消息
     * @return
     */
    public boolean sendMQ(String msg) {
        try {
        	boolean result = CustomRocketMQProducerClient.send(msg, CustomRocketMQEnum.MQ_STOCK_CHANGE_NOTICE_CHANNEL_AVAILABLE_PUSH.getCode(), null, null);
        	// 发送结果
            if(result){
                return true;
            }else {
            	log.error("库存变化通知,渠道有无可用库存通知,发送消息,失败,结果:{}",result);
            }
        } catch (Exception e) {
            log.error("库存变化通知,渠道有无可用库存通知,发送消息,出错:msg={}", msg, e);
        }
        return false;
    }
	
}
