package com.rome.stock.innerservice.domain.entity;

import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Component;

import com.rome.stock.innerservice.api.dto.RealWarehouseAddition;
import com.rome.stock.innerservice.domain.convertor.RealWarehouseConvertor;
import com.rome.stock.innerservice.domain.entity.warehouserecord.AbstractWarehouseRecord;
import com.rome.stock.innerservice.domain.repository.RealWarehouseRepository;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.extern.slf4j.Slf4j;

@Data
@Slf4j
@Component
@Scope("prototype")
@EqualsAndHashCode(callSuper = false)
public class RealWarehouseE extends BaseE {

    @Autowired
    private RealWarehouseRepository realWarehouseRepository;
    @Autowired
    private RealWarehouseConvertor realWarehouseConvertor;

    /**
     * 增加实仓库存
     * @param warehouseRecord
     * @param type 1:增加在途 2: 增加质检 3: 增加账面库存
     *
     */
    public boolean addRealWarehouseStock(AbstractWarehouseRecord warehouseRecord, int type) {
        //依赖单据明细驱动库存变化

        return true;
    }


    /**
     * 减少实仓库存
     * @param warehouseRecord
     * @param type 1:减少在途 增加账面库存 2: 减少质检 3: 减少账面库存
     *
     */
    public boolean reduceRealWarehouseStock(AbstractWarehouseRecord warehouseRecord, int type) {
        //依赖单据明细驱动库存变化
        return true;
    }


    //检查是否允许下发wms
    public boolean allowToDownWms(RealWarehouseE e) throws Exception{
        //检查是否允许下发wms
        return true;
    }

    //检查仓库的状态
    public boolean vaildWarehouseStatus(RealWarehouseE e) throws Exception{
        //检查是否允许下发wms
        return true;
    }

    //获取实仓与虚仓的关联关系
    public List<?> getVirtualStockSyncRelation(){
        return null;
    }

    public List<StockE> getRealWarehouseStock(){
        //查询库存
        return null;
    }




    //=================================实体仓领域相关属性===================================
    //实仓实仓主键
    private Long realWarehouseId;
    //实仓仓库编码-来源可能是sap或其他
    private String realWarehouseCode;
    //实仓仓库外部编号-wms
    private String realWarehouseOutCode;
    //工厂编码
    private String factoryCode;
    //工厂名称他
    private String factoryName;
    //实仓仓库名称
    private String realWarehouseName;
    //实仓仓库类型：1-电商仓，2-门店仓，3-采购仓，4-退货仓，5-虚拟产品仓，6-生鲜仓，7-行政仓
    private Integer realWarehouseType;
    //实仓如果为门店仓，店铺编码不能为空
    private String shopCode;
    //实仓仓库状态：0-初始，1-启用，2-停用
    private Integer realWarehouseStatus;
    //实仓虚拟仓库库存操作优先级 1为最高
    private Integer realWarehousePriority;
    //实仓邮编
    private String realWarehousePostcode;
    //实仓联系手机
    private String realWarehouseMobile;
    //实仓联系电话
    private String realWarehousePhone;
    //实仓联系邮箱
    private String realWarehouseEmail;
    //实仓国家
    private String realWarehouseCountry;
    //实仓省份
    private String realWarehouseProvince;
    //实仓城市
    private String realWarehouseCity;
    //实仓区、县城市  暂时只维护到省市级别
    private String realWarehouseCounty;
    //实仓四级区域
    private String realWarehouseArea;
    //实仓国家code
    private String realWarehouseCountryCode;
    //实仓省份code
    private String realWarehouseProvinceCode;
    //实仓城市code
    private String realWarehouseCityCode;
    //实仓区、县城市code 暂时只维护到省市级别
    private String realWarehouseCountyCode;
    //实仓四级区域code
    private String realWarehouseAreaCode;
    //实仓详细地址
    private String realWarehouseAddress;
    //实仓联系人姓名
    private String realWarehouseContactName;
    //实仓备注信息
    private String realWarehouseRemark;

    //仓库层级: 1-一级，2.二级
    private Integer realWarehouseRank;
    //行政区域
    private String regionName;
    private Long regionId;
    //财务中台相关字段
    //成本中心编码")
    private String costCenterCode;
    //成本中心名称")
    private String costCenterName;
    //仓库业务类型: 1: 直营门店 2: 加盟门店 3: 仓库 4. 其他仓库"
    private Integer rwBusinessType;
    //公司编码")
    private String companyCode;
    //公司名称")
    private String companyName;
    //是否允许负库存(0: 不允许 1: 允许)"
    private Integer allowNegtiveStock;
    //对应退货仓ID
    private Long returnWarehouseId;
    /**
     * 仓库附加信息
     */
    private RealWarehouseAddition realWarehouseAddition;

    private Integer solidEmptyWarehouseStatus;
    //物资存储类型 1:运输单位 2:基本单位
    private Integer materialStorageType;
    // 是否收入计费:[0-否,1-是]")
    private Integer revenueStat;
    // 是否成本计费:[0-否,1-是]")
    private Integer costStat;
    // 仓店一体标识 0 -非仓店一体   1仓店一体
    private Integer warehouseStoreIdenti;
}
