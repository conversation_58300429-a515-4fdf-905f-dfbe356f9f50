package com.rome.stock.innerservice.domain.service.impl;

import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.rome.arch.core.exception.RomeException;
import com.rome.stock.common.utils.DateUtil;
import com.rome.stock.innerservice.api.dto.FileInfoDto;
import com.rome.stock.innerservice.api.dto.FrSaleWineLogDTO;
import com.rome.stock.innerservice.api.dto.FrSaleWineLogParamDTO;
import com.rome.stock.innerservice.api.dto.LinkUrlDTO;
import com.rome.stock.innerservice.common.StringUtils;
import com.rome.stock.innerservice.constant.BizTypeEnum;
import com.rome.stock.innerservice.domain.convertor.FileInfoConvertor;
import com.rome.stock.innerservice.domain.entity.frontrecord.WDTOnlineRetailE;
import com.rome.stock.innerservice.domain.repository.FileInfoRepository;
import com.rome.stock.innerservice.domain.repository.FrSaleWineLogRepository;
import com.rome.stock.innerservice.domain.repository.frontrecord.FrWDTSaleRepository;
import com.rome.stock.innerservice.domain.service.FrSaleWineLogService;
import com.rome.stock.innerservice.remote.item.dto.SkuInfoExtDTO;
import com.rome.stock.innerservice.remote.item.facade.SkuFacade;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

@Slf4j
@Service
public class FrSaleWineLogImpl implements FrSaleWineLogService {

    @Resource
    private FrSaleWineLogRepository frSaleWineLogRepository;
    @Resource
    private FileInfoRepository fileInfoRepository;
    @Resource
    private SkuFacade skuFacade;
    @Resource
    private FrWDTSaleRepository frWDTSaleRepository;
    @Resource
    private FileInfoConvertor fileInfoConvertor;

    @Override
    public PageInfo queryFrSaleWineLog(FrSaleWineLogParamDTO frSaleWineLogParamDTO) {
        Page page = PageHelper.startPage(frSaleWineLogParamDTO.getPageIndex(), frSaleWineLogParamDTO.getPageSize());
        List<FrSaleWineLogDTO> frSaleWineLogDTOS = frSaleWineLogRepository.queryFrSaleWineLog(frSaleWineLogParamDTO);

        WDTOnlineRetailE wdtOnlineRetailE = frWDTSaleRepository.queryByRecordCode(frSaleWineLogParamDTO.getFrontRecordCode());
        if (wdtOnlineRetailE == null) {
            throw new RomeException("999", "前置单不存在");
        }
        //查询url
        List<Long> idList = new ArrayList<>();
        Set<String> skuCodeList = new HashSet<>();
        for (FrSaleWineLogDTO frSaleWineLogDTO : frSaleWineLogDTOS) {
            idList.add(frSaleWineLogDTO.getId());
            skuCodeList.add(frSaleWineLogDTO.getSkuCode());
            if (StringUtils.isNotEmpty(frSaleWineLogDTO.getBucketSkuCode())) {
                skuCodeList.add(frSaleWineLogDTO.getBucketSkuCode());
            }
            if ( StringUtils.isNotEmpty(frSaleWineLogDTO.getBeforeBucketSkuCode())) {
                skuCodeList.add(frSaleWineLogDTO.getBeforeBucketSkuCode());
            }
        }
        List<FileInfoDto> fileInfoDtoList = fileInfoRepository.queryFileInfoList(idList, BizTypeEnum.WINE_LOG.getCode());
        Map<Long, List<FileInfoDto>> linkUrlMap = fileInfoDtoList.stream().collect(Collectors.groupingBy(FileInfoDto::getBizId));
        //查询商品名称
        List<SkuInfoExtDTO> skuInfoExtDTOS = skuFacade.skusBySkuCode(new ArrayList<>(skuCodeList));
        Map<String, String> skuNameMap = skuInfoExtDTOS.stream().collect(Collectors.toMap(SkuInfoExtDTO::getSkuCode, SkuInfoExtDTO::getName, (e1, e2) -> e2));
        //查询付款时间
        for (FrSaleWineLogDTO saleWineLogDTO : frSaleWineLogDTOS) {
            saleWineLogDTO.setPayTime(wdtOnlineRetailE.getPayTime());
            if(Objects.nonNull(saleWineLogDTO.getStartRipeTime())){
                int diff = (int) DateUtil.diff(saleWineLogDTO.getStartRipeTime(), new Date(), 24 * 60 * 60 * 1000);
                saleWineLogDTO.setAfterRipeDays(Math.max(0, diff));
            }
            if (linkUrlMap.containsKey(saleWineLogDTO.getId())) {
                saleWineLogDTO.setLinkUrl(linkUrlMap.get(saleWineLogDTO.getId()));
            }
            if (skuNameMap.containsKey(saleWineLogDTO.getSkuCode())) {
                saleWineLogDTO.setSkuCodeName(skuNameMap.get(saleWineLogDTO.getSkuCode()));
            }
            if (skuNameMap.containsKey(saleWineLogDTO.getBucketSkuCode())) {
                saleWineLogDTO.setBucketSkuName(skuNameMap.get(saleWineLogDTO.getBucketSkuCode()));
            }
            if ( skuNameMap.containsKey(saleWineLogDTO.getBeforeBucketSkuCode())) {
                saleWineLogDTO.setBeforeBucketSkuName(skuNameMap.get(saleWineLogDTO.getBeforeBucketSkuCode()));
            }
            saleWineLogDTO.setBeforeBucketRecordCode(saleWineLogDTO.getBucketRecordCode());
        }
        PageInfo<FrSaleWineLogDTO> pageInfo = new PageInfo<>(frSaleWineLogDTOS);
        pageInfo.setTotal(page.getTotal());
        return pageInfo;
    }
}
