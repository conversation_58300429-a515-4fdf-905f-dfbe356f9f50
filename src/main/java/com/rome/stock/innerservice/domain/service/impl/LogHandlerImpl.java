package com.rome.stock.innerservice.domain.service.impl;

import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import com.rome.arch.util.dynamic.DynamicDataSourceEnum;
import com.rome.arch.util.dynamic.TargetDataSource;
import com.rome.stock.common.enums.warehouse.WarehouseRecordTypeVO;
import com.rome.stock.innerservice.api.dto.LogManagerDTO;
import com.rome.stock.innerservice.api.dto.LogManagerParamDTO;
import com.rome.stock.innerservice.common.RomeCollectionUtil;
import com.rome.stock.innerservice.common.StringUtils;
import com.rome.stock.innerservice.context.distributedlock.log.AbstractLogHandler;
import com.rome.stock.innerservice.domain.entity.FrontWarehouseRecordRelationE;
import com.rome.stock.innerservice.domain.entity.RwBatchE;
import com.rome.stock.innerservice.domain.entity.warehouserecord.WarehouseRecordE;
import com.rome.stock.innerservice.domain.repository.RwBatchRepository;
import com.rome.stock.innerservice.domain.repository.frontrecord.FrWarehouseInventoryStartRepository;
import com.rome.stock.innerservice.domain.repository.frontrecord.FrontWarehouseRecordRelationRepository;
import com.rome.stock.innerservice.domain.repository.warehouserecord.WarehouseRecordRepository;
import com.rome.stock.innerservice.domain.service.LogHandlerManagerService;
import com.rome.stock.innerservice.infrastructure.dataobject.frontrecord.FrWarehouseInventoryStartDO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;

@Slf4j
@Service(value = "LogHandlerImpl")
public class LogHandlerImpl extends AbstractLogHandler  implements LogHandlerManagerService {
    @Resource
    private FrontWarehouseRecordRelationRepository frontWarehouseRecordRelationRepository;
    @Resource
    private FrWarehouseInventoryStartRepository frWarehouseInventoryStartRepository;
    @Resource
    private WarehouseRecordRepository warehouseRecordRepository;
    @Resource
    private RwBatchRepository rwBatchRepository;

    @Override
    @TargetDataSource(DynamicDataSourceEnum.READ)
    public List<LogManagerDTO> queryLogs(LogManagerParamDTO paramDTO) {
        List<LogManagerDTO> pageList= Lists.newArrayList();
        List<String> recordCodeList=Lists.newArrayList();
        if(StringUtils.isNotEmpty(paramDTO.getPostOrderCode())){
            recordCodeList.add(paramDTO.getPostOrderCode());
        }
        if(StringUtils.isNotEmpty(paramDTO.getBusinessOrderCode())){
            recordCodeList.add(paramDTO.getBusinessOrderCode());
        }
        if(StringUtils.isNotEmpty(paramDTO.getFrontOutRecordCode())){
            recordCodeList.add(paramDTO.getFrontOutRecordCode());
        }
        if (CollectionUtils.isNotEmpty(recordCodeList)){
            pageList=handleLogQuery(recordCodeList,paramDTO.getStatus());
        }
        if (CollectionUtils.isNotEmpty(pageList)) {
            pageList = pageList.stream().sorted((l1, l2) -> Long.compare(l1.getCreateTime().getTime(), l2.getCreateTime().getTime())).collect(Collectors.toList());
        }
        return pageList;
    }

    @TargetDataSource(DynamicDataSourceEnum.READ)
    public List<LogManagerDTO> queryLogsByWarehouseRecord(LogManagerParamDTO paramDTO,WarehouseRecordE warehouseRecordE) {
        List<LogManagerDTO> pageList= Lists.newArrayList();
        Set<String> recordCodeList=Sets.newHashSet();
        if (Objects.nonNull(warehouseRecordE)){
            List<String> recordCodes=queryRecordCodeListByRecordCode(warehouseRecordE);
            if (CollectionUtils.isNotEmpty(recordCodes)){
                recordCodeList.addAll(recordCodes);
            }
            List<String> wmsCodes=queryWmsCode(warehouseRecordE.getRecordCode());
            if (CollectionUtils.isNotEmpty(wmsCodes)){
                recordCodeList.addAll(wmsCodes);
            }
        }
        pageList=handleLogQuery(new ArrayList<>(recordCodeList),paramDTO.getStatus());
        if (CollectionUtils.isNotEmpty(pageList)) {
            pageList = pageList.stream().sorted((l1, l2) -> Long.compare(l1.getCreateTime().getTime(), l2.getCreateTime().getTime())).collect(Collectors.toList());
        }
        return pageList;
    }

    private List<String> queryRecordCodeListByRecordCode(WarehouseRecordE warehouseRecordE) {
        Set<String> set= Sets.newHashSet(warehouseRecordE.getRecordCode());
        Integer recordType=warehouseRecordE.getRecordType();
        if (WarehouseRecordTypeVO.WAREHOUSE_INVENTORY_OUT_WAREHOUSE_RECORD.getType().equals(recordType)||
                WarehouseRecordTypeVO.WAREHOUSE_INVENTORY_IN_WAREHOUSE_RECORD.getType().equals(recordType)){
            List<FrontWarehouseRecordRelationE> frontWarehouseRecordRelationList=frontWarehouseRecordRelationRepository.getFrontRecordCodesByRecordCodes(Lists.newArrayList(warehouseRecordE.getRecordCode()));
            if (CollectionUtils.isNotEmpty(frontWarehouseRecordRelationList)){
                List<Long> frontRecordIds= RomeCollectionUtil.getDistinctValueList(frontWarehouseRecordRelationList,"frontRecordId");
                List<FrWarehouseInventoryStartDO> inventoryStartList=frWarehouseInventoryStartRepository.queryByIdList(frontRecordIds);
                if (CollectionUtils.isNotEmpty(inventoryStartList)){
                    inventoryStartList.stream().forEach(start->{
                        if (StringUtils.isNotEmpty(start.getRecordCode())){
                            set.add(start.getRecordCode());
                        }
                        if (StringUtils.isNotEmpty(start.getSerialNo())){
                            set.add(start.getSerialNo());
                        }
                    });
                }
            }
        }
        if (StringUtils.isNotEmpty(warehouseRecordE.getSapOrderCode())){
            set.add(warehouseRecordE.getSapOrderCode());
        }
        set.add(warehouseRecordE.getRecordCode());
        return Lists.newArrayList(set);
    }

    @TargetDataSource(DynamicDataSourceEnum.READ)
    public List<WarehouseRecordE> queryBySapAndRecordCode(String sapOrderCode, String recordCode){
        return  warehouseRecordRepository
                .queryWarehouseRecordListBySapAndRecordCode(sapOrderCode,recordCode);

    }

    @TargetDataSource(DynamicDataSourceEnum.READ)
    public List<String> queryWmsCode(String recordCode){
        List<String> list=Lists.newArrayList();
        List<RwBatchE> rwBatchES=rwBatchRepository.queryRwInfoByRecordCode(recordCode);
        if (CollectionUtils.isNotEmpty(rwBatchES)){
            List<String> wmsRecordCodes=rwBatchES.stream().filter(rwBatchE -> StringUtils.isNotEmpty(rwBatchE.getWmsRecordCode())).map(RwBatchE::getWmsRecordCode).collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(wmsRecordCodes)){
                list.addAll(wmsRecordCodes);
            }
        }
        return list;
    }
}
