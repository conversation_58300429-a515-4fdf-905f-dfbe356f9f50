package com.rome.stock.innerservice.domain.entity;

import java.util.Date;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Component;
import com.rome.arch.core.domain.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.extern.slf4j.Slf4j;

/**
 * 类BaseE的实现描述：基础领域对象
 *
 * <AUTHOR> 2019/4/17 19:53
 */
@Data
@Slf4j
@Component
@Scope("prototype")
@EqualsAndHashCode(callSuper = false)
public class BaseE extends BaseEntity {


    /**
     * 唯一主键
     */
    private Long id;
    /**
     * 商家id
     */
    private Long merchantId;
    //创建时间
    private Date createTime;
    //更新时间
    private Date updateTime;
    //创建人
    private Long creator;
    //更新人
    private Long modifier;
    //是否可用 0-否，1-是
    private Byte isAvailable;
    //是否逻辑删除 0-否，1-是
    private Byte isDeleted;
    //数据版本号
    private Integer versionNo;
    //租户ID
    private Long tenantId;
    //业务应用ID 1:补货用2:采购用
    private String appId;
}
