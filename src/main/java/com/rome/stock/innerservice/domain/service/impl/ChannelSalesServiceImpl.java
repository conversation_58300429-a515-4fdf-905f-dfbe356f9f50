package com.rome.stock.innerservice.domain.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.rome.arch.core.exception.RomeException;
import com.rome.arch.util.SpringBeanUtil;
import com.rome.stock.core.infrastructure.redis.ChannelSalesRedis;
import com.rome.stock.innerservice.api.dto.ChannelSales;
import com.rome.stock.innerservice.common.ResCode;
import com.rome.stock.innerservice.common.StringUtils;
import com.rome.stock.innerservice.constant.RealWarehouseTypeVO;
import com.rome.stock.innerservice.domain.convertor.ChannelSalesConvertor;
import com.rome.stock.innerservice.domain.entity.ChannelSalesE;
import com.rome.stock.innerservice.domain.entity.VirtualWarehouseE;
import com.rome.stock.innerservice.domain.entity.VirtualWarehouseGroupE;
import com.rome.stock.innerservice.domain.repository.ChannelSalesRepository;
import com.rome.stock.innerservice.domain.repository.VirtualWarehouseGroupRepository;
import com.rome.stock.innerservice.domain.repository.VirtualWarehouseRepository;
import com.rome.stock.innerservice.domain.service.ChannelSalesService;
import com.rome.stock.innerservice.domain.service.VirtualWarehouseService;
import com.rome.stock.innerservice.facade.StockOnlineOrderFacade;
import com.rome.stock.innerservice.infrastructure.dataobject.RealWarehouseDO;
import com.rome.stock.innerservice.infrastructure.dataobject.VirtualWarehouseGroupDo;
import com.rome.stock.innerservice.infrastructure.dataobject.VirtualWarehouseStockDo;
import com.rome.stock.innerservice.infrastructure.mapper.RealWarehouseMapper;
import com.rome.stock.innerservice.infrastructure.mapper.VirtualWarehouseGroupMapper;
import com.rome.stock.innerservice.infrastructure.redis.cache.GroupInfoCacheRedis;
import com.rome.stock.innerservice.remote.base.dto.ChannelDTO;
import com.rome.stock.innerservice.remote.base.facade.ChannelFacade;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.Function;
import java.util.stream.Collectors;

@Service
@Slf4j
public class ChannelSalesServiceImpl implements ChannelSalesService {
	@Autowired
	private ChannelSalesRepository channelSalesRepository;
	@Autowired
	private ChannelSalesConvertor channelSalesConvertor;
	@Resource
	private VirtualWarehouseService virtualWarehouseService;
	@Resource
	private VirtualWarehouseRepository virtualWarehouseRepository;
	@Resource
	private VirtualWarehouseGroupRepository virtualWarehouseGroupRepository;

	@Autowired
	private ChannelSalesRedis channelSalesRedis;
    @Autowired
    private RealWarehouseMapper realWarehouseMapper;
	@Resource
	private ChannelSalesService channelSalesService;
	@Resource
	private VirtualWarehouseGroupMapper virtualWarehouseGroupMapper;
    @Autowired
    private ChannelFacade channelFacade;

	/**新增渠道
	 *
	 * @param channelSalesDto
	 */
	@Override
	public void addChannelSales(ChannelSales channelSalesDto) {
		ChannelSalesE channelSalesE = channelSalesRepository.queryByChannelCode(channelSalesDto.getChannelCode());
		if (channelSalesE == null) {
			Integer saveResponse = channelSalesRepository.saveChannelSales(channelSalesConvertor.dtoToEntity(channelSalesDto));
			if (saveResponse < 1) {
				throw new RomeException(ResCode.STOCK_ERROR_1003, ResCode.STOCK_ERROR_1003_DESC);
			}
			// 清理或更新渠道数据
			StockOnlineOrderFacade.clearOrUpdateChannelSales(channelSalesDto.getChannelCode());
		}
	}

	/**删除渠道
	 *
	 * @param id
	 */
	@Override
	public void removeChannelSales(long id,Long modifier) {
		Integer delResponse = channelSalesRepository.deleteChannelSales(id,modifier);
		if (delResponse < 1) {
			throw new RomeException(ResCode.STOCK_ERROR_1003, ResCode.STOCK_ERROR_1003_DESC);
		}
	}

	@Override
	public void modfiyChannelSales(ChannelSales channelSalesDto) {
		Integer modfiyResponse = channelSalesRepository.updateChannelSales(channelSalesConvertor.dtoToEntity(channelSalesDto));
		if (modfiyResponse < 1) {
			throw new RomeException(ResCode.STOCK_ERROR_1003, ResCode.STOCK_ERROR_1003_DESC);
		}
	}

	/**批量获取渠道
	 *
	 * @return
	 */
	@Override
	public List<ChannelSales> getChannelSalesAll() {
		return channelSalesConvertor.entityToDto(channelSalesRepository.selectChannelSalesAll());
	}

	/**根据策略组id查询渠道
	 *
	 * @param channelSalesDto
	 * @return
	 */
	@Override
	public PageInfo<ChannelSales> getChannelSales(ChannelSales channelSalesDto) {
		ArrayList<Long> ids = new ArrayList<>();
		ids.add(channelSalesDto.getVirtualWarehouseGroupId());

		Page page = PageHelper.startPage(channelSalesDto.getPageIndex(), channelSalesDto.getPageSize());
		List<ChannelSalesE> channelSalesE = channelSalesRepository.selectChannelSalesByGId(ids);
		List<ChannelSales> channelSales = channelSalesConvertor.entityToDto(channelSalesE);

		PageInfo<ChannelSales> pageList = new PageInfo<>(channelSales);
		pageList.setTotal(page.getTotal());

		return pageList;
	}

	/**查询所有未关联策略组的渠道
	 *
	 * @return
	 */
	@Override
	public List<ChannelSales> getNoRelateChannelSales() {
		List<ChannelSales> noRelateChannelSalesList = channelSalesConvertor.entityToDto(channelSalesRepository.selectChannelSaleNoVWGroupId());
		return noRelateChannelSalesList;
	}

	/**根据渠道code查询渠道
	 *
	 * @param channelSalesDto
	 * @return
	 */
	@Override
	public List<ChannelSales> getChannelSalesByChannelCode(ChannelSales channelSalesDto) {
		List<ChannelSales> pageList = channelSalesConvertor.entityToDto(channelSalesRepository.selectChannelSalesByChannelCode(channelSalesConvertor.dtoToEntity(channelSalesDto)));
		return pageList;
	}

	@Override
	public List<ChannelSales> selectChannelSalesByChannelCodes(List<String> channelCodes) {
		List<ChannelSalesE> channelSalesE = channelSalesRepository.selectChannelSalesByChannelCodes(channelCodes);
		if(CollectionUtil.isNotEmpty(channelSalesE)){
			List<Long> virtualWarehouseGroupIds = channelSalesE.stream().map(x -> x.getVirtualWarehouseGroupId()).collect(Collectors.toList());
			List<VirtualWarehouseGroupE> virtualWarehouseGroupES = virtualWarehouseGroupRepository.selectVirtualWarehouseGroupByIds(virtualWarehouseGroupIds);
			if(CollectionUtil.isNotEmpty(virtualWarehouseGroupES)){
				Map<Long, VirtualWarehouseGroupE> virtualGroupMap = virtualWarehouseGroupES.stream().collect(Collectors.toMap(x -> x.getId(), Function.identity(), (v1, v2) -> v2));
				channelSalesE.forEach(x->{
					VirtualWarehouseGroupE virtualWarehouseGroupE = virtualGroupMap.get(x.getVirtualWarehouseGroupId());
					if(Objects.nonNull(virtualWarehouseGroupE)){
						x.setVirtualWarehouseGroupCode(virtualWarehouseGroupE.getVirtualWarehouseGroupCode());
					}
				});
				return channelSalesConvertor.entityToDto(channelSalesE);
			}
		}
		return null;
	}

	/**
	 * 根据id删除渠道
	 * @param id
	 */
	@Override
	@Transactional(rollbackFor = Exception.class)
	public Long deleteChannelSales(Long id,Long modifier) {
		//判断关联策略组关联的虚仓是否有库存，若有，抛异常，无法删除
		//1.根据id查询策略组id
		ChannelSalesE channelSalesE = new ChannelSalesE();
		channelSalesE.setId(id);
		List<ChannelSalesE> salesEList = channelSalesRepository.selectChannelSalesByChannelCode(channelSalesE);
		ChannelSalesE salesE = salesEList.get(0);
		//2.根据策略组id查询虚仓id
		List<VirtualWarehouseE> warehouseEList = virtualWarehouseRepository.queryByGroupId(salesE.getVirtualWarehouseGroupId());
		//3.根据虚仓id查询虚仓库存
		boolean isSuccess = true;
		for (VirtualWarehouseE warehouseE:warehouseEList) {
			if(isSuccess == false){
				break;
			}
			List<VirtualWarehouseStockDo> stockDos = virtualWarehouseService.getVWStockByVirtualWarehouseId(warehouseE.getId());
			if(null != stockDos && 0 != stockDos.size()){
				for (VirtualWarehouseStockDo stockDo:stockDos) {
					if(stockDo.getRealQty().longValue() != 0){
						isSuccess = false;
						break;
					}
				}
			}

		}

//		if(isSuccess == false){
//			throw new RomeException(ResCode.STOCK_ERROR_7003,ResCode.STOCK_ERROR_7003_DESC);
//		}
		channelSalesRepository.deleteChannelSalesV2(id,modifier);
		//更改数据库后删除缓存
		StockOnlineOrderFacade.clearOrUpdateChannelSales(salesE.getChannelCode());
		StockOnlineOrderFacade.delRouteInfoByPriorityTemple(salesE.getChannelCode());
		return salesE.getVirtualWarehouseGroupId();
	}

	/**
	 * 根据商家ID查询销售渠道
	 */
	@Override
	public List<ChannelSalesE> queryByMerchantId(Long merchantId) {
		return channelSalesRepository.queryByMerchantId(merchantId);
	}

	/**
	 * 根据商家ID和渠道编码查询销售渠道
	 */
	@Override
	public List<ChannelSalesE> queryByMerchantIdAndChannelCode(Long merchantId, String channelCode) {
		return channelSalesRepository.queryByMerchantIdAndChannelCode(merchantId, channelCode);
	}

	/**
	 * 查询渠道列表（排除门店）
	 */
	@Override
	public List<ChannelSales> queryChannelSalesList() {
		return channelSalesConvertor.entityToDto(channelSalesRepository.queryChannelSalesList());
	}

	@Override
	//不加事务
	public Map<String, Object> batchBindChannelSalesForAllShop(ChannelSales channelSale) {
		Map<String, Object> result = new HashMap<>();
		int successCount = 0;
		int failCount = 0;
		List<String> failedShopCodes = new ArrayList<>();
		if(StringUtils.isEmpty(channelSale.getChannelCode())){
			throw new RomeException(ResCode.STOCK_ERROR_1001, "渠道编号不能为空");
		}
		List<String> shopCodes;
		if(CollectionUtils.isNotEmpty(channelSale.getShopCodes())){
			//查询指定门店的数据
			shopCodes=channelSale.getShopCodes().stream().distinct().collect(Collectors.toList());
			List<RealWarehouseDO> shopList =realWarehouseMapper.queryByShopCodeList(shopCodes);
			shopCodes=shopList.stream().map(x -> x.getShopCode()).collect(Collectors.toList());
			if(CollectionUtils.isEmpty(shopCodes)){
				throw new RomeException(ResCode.STOCK_ERROR_1001, "查询门店数据为空");
			}
		}else{
			//批量查询出所有门店
			List<RealWarehouseDO> realWarehouseDOList = realWarehouseMapper.queryRealWarehouseByRWType(RealWarehouseTypeVO.RW_TYPE_1.getType());
			shopCodes=realWarehouseDOList.stream().map(x -> x.getShopCode()).collect(Collectors.toList());
		}
		List<String> channelCodeList = new ArrayList<>();
		for (String shopCode : shopCodes) {
			channelCodeList.add(shopCode+"_"+channelSale.getChannelCode());
		}
		//批量查询渠道表
		List<ChannelSalesE> channelSalesEList = channelSalesRepository.selectChannelSalesByChannelCodes(channelCodeList);
		Map<String, ChannelSalesE> channelSalesMap = channelSalesEList.stream().collect(Collectors.toMap(x -> x.getChannelCode(), Function.identity(), (v1, v2) -> v2));
		//去除渠道表里面已经存在的渠道
		channelCodeList=channelCodeList.stream().filter(x->!channelSalesMap.containsKey(x)).collect(Collectors.toList());
		if(CollectionUtil.isEmpty(channelCodeList)) {
			throw new RomeException(ResCode.STOCK_ERROR_1001, "无需绑定的渠道关系，所有渠道已存在");
		}

		//需要查询基础数据中心的渠道,获取渠道名称
		List<ChannelDTO> channelDTOList = channelFacade.batchQueryByChannelCodes(channelCodeList);
		Map<String, ChannelDTO> channelDTOMap = channelDTOList.stream().collect(Collectors.toMap(x -> x.getChannelCode(), Function.identity(), (v1, v2) -> v2));
		
		//查询出对应的门店策略组
		List<VirtualWarehouseGroupDo>  virtualWarehouseGroupList =virtualWarehouseGroupMapper.selectVirtualWarehouseGroupByCodes(shopCodes);
		if(CollectionUtil.isEmpty(virtualWarehouseGroupList)) {
			throw new RomeException(ResCode.STOCK_ERROR_1001, "未找到门店对应的虚仓策略组");
		}
		
		Map<String,Long> virtualWarehouseGroupMap = virtualWarehouseGroupList.stream().collect(Collectors.toMap(x -> x.getVirtualWarehouseGroupCode(), x -> x.getId(), (v1, v2) -> v2));
		for (String channelCode : channelCodeList) {
			String shopCode = channelCode.split("_")[0];
			try {
				if(!virtualWarehouseGroupMap.containsKey(shopCode)){
					log.warn("门店对应的虚仓策略组不存在,跳过处理,shopCode:{}", shopCode);
					failCount++;
					failedShopCodes.add(shopCode);
					continue;
				}
				if(!channelDTOMap.containsKey(channelCode)){
					log.warn("门店对应的渠道在基础数据中心查不到,跳过处理,shopCode:{},channelCode:{}", shopCode,channelCode);
					failCount++;
					failedShopCodes.add(shopCode);
					continue;
				}
				String channelName = channelDTOMap.get(channelCode).getChannelName();
				Long virtualWarehouseGroupId = virtualWarehouseGroupMap.get(shopCode);
				channelSale.setVirtualWarehouseGroupId(virtualWarehouseGroupId);
				channelSale.setChannelCode(channelCode);
				channelSale.setChannelName(channelName);
				channelSalesService.addChannelSales(channelSale);
				// 清理组信息
				SpringBeanUtil.getBean(GroupInfoCacheRedis.class).delByGroupId(virtualWarehouseGroupId);
				StockOnlineOrderFacade.delRouteInfoByPriorityTemple(channelCode);
				// 清理或更新渠道数据
				StockOnlineOrderFacade.clearOrUpdateChannelSales(channelCode);
				successCount++;
			} catch (RomeException e) {
				log.error("batchBindChannelSalesForAllShop失败,门店编号:{},异常:{}", shopCode, e.getMessage(), e);
				failCount++;
				failedShopCodes.add(shopCode);
			} catch (Exception e) {
				log.error("batchBindChannelSalesForAllShop失败,门店编号:{},异常:{}", shopCode, e.getMessage(), e);
				failCount++;
				failedShopCodes.add(shopCode);
			}
		}
		log.info("批量绑定门店和渠道关系完成，总数:{}, 成功:{}, 失败:{}, 失败门店:{}", channelCodeList.size(), successCount, failCount, failedShopCodes);
		
		result.put("successCount", successCount);
		result.put("failCount", failCount);
		result.put("failedShopCodes", failedShopCodes);
		return result;
	}

}
