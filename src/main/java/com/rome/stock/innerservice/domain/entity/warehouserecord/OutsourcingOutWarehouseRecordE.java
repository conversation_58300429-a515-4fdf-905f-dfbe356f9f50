package com.rome.stock.innerservice.domain.entity.warehouserecord;

import com.rome.arch.core.domain.EntityFactory;
import com.rome.stock.common.enums.warehouse.WarehouseRecordTypeVO;
import com.rome.stock.innerservice.constant.WarehouseRecordBusinessTypeVO;

import com.rome.stock.innerservice.constant.WmsSyncStatusVO;
import com.rome.stock.innerservice.domain.entity.frontrecord.OutsourcingOutDetailE;
import com.rome.stock.innerservice.domain.entity.frontrecord.OutsourcingOutE;
import com.rome.stock.innerservice.domain.repository.warehouserecord.OutsourcingOutWarehouseRepository;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;

@Data
@Slf4j
@Component
@Scope("prototype")
@EqualsAndHashCode(callSuper = false)
public class OutsourcingOutWarehouseRecordE extends AbstractWarehouseRecord {
	@Resource
	private EntityFactory entityFactory;
	@Resource
	private OutsourcingOutWarehouseRepository outsourcingOutWarehouseRepository;

	/**
	 * 创建仓库单据
	 */
	public void addWarehouseRecord() {

		long id = outsourcingOutWarehouseRepository.saveWarehouseRecord(this);
		this.setId(id);
		this.warehouseRecordDetails.forEach(record -> record.setWarehouseRecordDetail(this));
		outsourcingOutWarehouseRepository.saveWarehouseRecordDetails(this.warehouseRecordDetails);
		//存储前置单与仓库单关系
		this.addFrontRecordAndWarehouseRelation();
	}


	/**
	 * 根据前置单生成出库单据
	 */
	public void createOutRecordByFrontRecord(OutsourcingOutE frontRecord) {
		this.setRecordType(WarehouseRecordTypeVO.OUTSOURCE_OUT_RECORD.getType());
		//生成单据编号
		this.createRecodeCode(WarehouseRecordTypeVO.OUTSOURCE_OUT_RECORD.getCode());
		this.setBusinessType(WarehouseRecordBusinessTypeVO.OUT_WAREHOUSE_RECORD.getType());
		//需要wms拉取，设置状态为未同步
		this.setSyncWmsStatus(WmsSyncStatusVO.UNSYNCHRONIZED.getStatus());
		this.setSapOrderCode(frontRecord.getOutRecordCode());
		this.setAbstractFrontRecord(frontRecord);
		this.setRealWarehouseId(frontRecord.getRealWarehouseId());
//		this.setChannelCode(frontRecord.getChannelCode());
//		this.setChannelType(frontRecord.getChannelType());
		this.setMerchantId(frontRecord.getMerchantId());
		this.setOutCreateTime(frontRecord.getOutCreateTime());
		List<OutsourcingOutDetailE> frontRecordDetails = frontRecord.getDetails();
		this.setWarehouseRecordDetails(new ArrayList<>(frontRecordDetails.size()));
		for (OutsourcingOutDetailE detailE : frontRecordDetails) {
			WarehouseRecordDetail warehouseRecordDetail = entityFactory.createEntity(WarehouseRecordDetail.class);
			warehouseRecordDetail.createRecordDetailByFrontRecord(detailE);
			warehouseRecordDetail.setLineNo(detailE.getLineNo());
			warehouseRecordDetail.setActualQty(BigDecimal.ZERO);
			this.warehouseRecordDetails.add(warehouseRecordDetail);
		}
		//设置skuCode和skuID
		this.initWarehouseRecodeDetail();
	}
}
