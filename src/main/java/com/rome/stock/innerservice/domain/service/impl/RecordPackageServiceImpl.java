package com.rome.stock.innerservice.domain.service.impl;

import com.rome.stock.innerservice.api.dto.warehouserecord.RecordPackageDTO;
import com.rome.stock.innerservice.common.AlikAssert;
import com.rome.stock.innerservice.common.ResCode;
import com.rome.stock.innerservice.common.SkuInfoTools;
import com.rome.stock.innerservice.domain.convertor.record.RecordPackageConvertor;
import com.rome.stock.innerservice.domain.entity.record.RecordPackageDetailE;
import com.rome.stock.innerservice.domain.entity.record.RecordPackageE;
import com.rome.stock.innerservice.domain.repository.warehouserecord.RecordPackageRepository;
import com.rome.stock.innerservice.domain.service.RecordPackageService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.List;
import java.util.Objects;

/**
 * @Description: RecordPackageService
 * <p>
 * @Author: chuwenchao  2019/9/5
 */
@Slf4j
@Service
public class RecordPackageServiceImpl implements RecordPackageService {

    @Resource
    private RecordPackageRepository recordPackageRepository;
    @Resource
    private RecordPackageConvertor recordPackageConvertor;
    @Autowired
    private SkuInfoTools skuInfoTools;

    /**
     * @Description: 通过出入库单号查询信息
     * <br>
     * <AUTHOR> 2019/9/5
     * @param recordCode
     * @return
     */
    @Override
    public List<RecordPackageE> getRecordPackageByRecordCode(String recordCode) {
        return recordPackageRepository.getRecordPackageByRecordCode(recordCode);
    }

    /**
     * @Description: 保存包裹信息
     * <br>
     * <AUTHOR> 2019/9/5
     * @param packageDTOS
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public void saveRecordPackage(List<RecordPackageDTO> packageDTOS ,Long merchantId) {
        AlikAssert.isNotEmpty(packageDTOS, ResCode.STOCK_ERROR_1002, ResCode.STOCK_ERROR_1002_DESC + ":包裹信息不能为空");
        List<RecordPackageE> packageES = recordPackageConvertor.convertDTOListToEntityList(packageDTOS);
        for(RecordPackageE packageE : packageES) {
            List<RecordPackageDetailE> packageEDetails = packageE.getDetails();
            skuInfoTools.convertSkuCode(packageEDetails,merchantId);
            packageE.setDetails(packageEDetails);
            recordPackageRepository.saveRecordPackage(packageE);
        }
    }

    @Override
    public List<String> queryRecordCode(String expressCode) {
        return recordPackageRepository.queryRecordCode(expressCode);
    }

}
