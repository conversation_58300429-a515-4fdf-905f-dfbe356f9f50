package com.rome.stock.innerservice.domain.entity.frontrecord;

import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Component;

import java.util.Date;

@Component
@Scope("prototype")
@Data
@EqualsAndHashCode(callSuper = false)
public class PurchaseOrderDetailE extends AbstractFrontRecordDetail {

	/**
	 * 行号
	 */
	private String lineNo;

	/**
	 * 超收比例，1-100区间可选数字
	 */
	private Integer overReceiveRatio;
	/**
	 * 交货不足比例 1-100区间可选数字
	 */
	private Integer insufficientRatio;
	/**
	 * 行状态 0-未删除，1-已删除，3-交货完成 4、冻结
	 */
	private Integer status;
	/**
	 * 期望交货时间
	 */
	private Date deliveryData;

	/**
	 * 商品sku编码
	 */
	private Long skuId;

	/**
	 * 商品sku编码
	 */
	private String skuCode;

	private Integer isFree;

	/**
	 * 是否需要质检 0--不需要 1--需要
	 */
	private Integer isNeedQuality;

	/**
	 * 过账到质检库存
	 */
	private Integer isPostQcInventory;

	/**
	 * "物料类型 0--正常商品 1--委外原料 2---委外成品
	 */
	private Integer skuType;
}
