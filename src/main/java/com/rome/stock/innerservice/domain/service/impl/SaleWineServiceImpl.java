package com.rome.stock.innerservice.domain.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.rome.arch.core.exception.RomeException;
import com.rome.stock.common.utils.DateUtil;
import com.rome.stock.innerservice.api.dto.FileInfoDto;
import com.rome.stock.innerservice.api.dto.wine.WineInfoUpdateBySerialNoParamDTO;
import com.rome.stock.innerservice.api.dto.wine.WineInfoUpdateParamDTO;
import com.rome.stock.innerservice.api.dto.wine.WinePageInfoResultDTO;
import com.rome.stock.innerservice.api.dto.wine.WinePageParamDTO;
import com.rome.stock.innerservice.common.AlikAssert;
import com.rome.stock.innerservice.common.BeanHelper;
import com.rome.stock.innerservice.common.RomeCollectionUtil;
import com.rome.stock.innerservice.common.StringUtils;
import com.rome.stock.innerservice.constant.*;
import com.rome.stock.innerservice.domain.convertor.FileInfoConvertor;
import com.rome.stock.innerservice.domain.convertor.frontrecord.FrWDTSaleWineConvertor;
import com.rome.stock.innerservice.domain.entity.frontrecord.WDTOnlineRetailE;
import com.rome.stock.innerservice.domain.entity.frontrecord.WDTOnlineRetailRecordDetailE;
import com.rome.stock.innerservice.domain.repository.FileInfoRepository;
import com.rome.stock.innerservice.domain.repository.FrSaleWineLogRepository;
import com.rome.stock.innerservice.domain.repository.SaleWineRepository;
import com.rome.stock.innerservice.domain.repository.frontrecord.FrWDTSaleRepository;
import com.rome.stock.innerservice.domain.repository.frontrecord.FrontWarehouseRecordRelationRepository;
import com.rome.stock.innerservice.domain.service.SaleWineService;
import com.rome.stock.innerservice.infrastructure.dataobject.FileInfoDO;
import com.rome.stock.innerservice.infrastructure.dataobject.FrWDTSaleWineLogDO;
import com.rome.stock.innerservice.infrastructure.dataobject.frontrecord.FrWDTSaleWineDO;
import com.rome.stock.innerservice.remote.item.dto.SkuInfoExtDTO;
import com.rome.stock.innerservice.remote.item.facade.SkuFacade;
import com.rome.stock.innerservice.remote.stockCore.facade.StockCoreFacade;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

@Slf4j
@Service
public class SaleWineServiceImpl implements SaleWineService {


    @Resource
    private FrWDTSaleRepository frWDTSaleRepository;
    @Resource
    private SaleWineRepository saleWineRepository;
    @Resource
    private FileInfoRepository fileInfoRepository;
    @Resource
    private SkuFacade skuFacade;
    @Resource
    private FrWDTSaleWineConvertor frWDTSaleWineConvertor;
    @Resource
    private FrSaleWineLogRepository frSaleWineLogRepository;
    @Resource
    private StockCoreFacade stockCoreFacade;
    @Resource
    private FileInfoConvertor fileInfoConvertor;
    @Resource
    private FrontWarehouseRecordRelationRepository frontWarehouseRecordRelationRepository;


    @Override
    public PageInfo querySaleWineByFrontRecordCode(WinePageParamDTO winePageParamDTO) {
        if (StringUtils.isEmpty(winePageParamDTO.getFrontRecordCode())) {
            throw new RomeException(ResponseMsg.PARAM_ERROR.getCode(), "酒单据号不能为空");
        }
        Page page = PageHelper.startPage(winePageParamDTO.getPageIndex(), winePageParamDTO.getPageSize());
        List<WinePageInfoResultDTO> winePageInfoResultDTOList = saleWineRepository.querySaleWineByFrontRecordCode(winePageParamDTO.getFrontRecordCode());
        if (CollUtil.isEmpty(winePageInfoResultDTOList)) {
            return new PageInfo();
        }
        List<Long> idList = new ArrayList<>();
        List<String> skuCodeList = new ArrayList<>();
        List<String> hasSerialNoOutRecordList = new ArrayList<>();
        for (WinePageInfoResultDTO winePageInfoResultDTO : winePageInfoResultDTOList) {
            idList.add(winePageInfoResultDTO.getId());
            skuCodeList.add(winePageInfoResultDTO.getSkuCode());
            //存在序列号的，才设置数据
            if (StringUtils.isNotEmpty(winePageInfoResultDTO.getSerialNo())) {
                hasSerialNoOutRecordList.add(winePageInfoResultDTO.getOutRecordCode());
            }
            if (StringUtils.isNotEmpty(winePageInfoResultDTO.getBucketSkuCode())) {
                skuCodeList.add(winePageInfoResultDTO.getBucketSkuCode());
            }

        }
        //酒交易单号+序列号 与桶订单号关系
        Map<String, WDTOnlineRetailRecordDetailE> serialNoAndOutRecordCodeMap = this.getBucketRecordCode(hasSerialNoOutRecordList,true);
        for (WDTOnlineRetailRecordDetailE detailE : serialNoAndOutRecordCodeMap.values()) {
            //桶型为桶商品名称
            skuCodeList.add(detailE.getSkuCode());
        }
        //查询url
        List<FileInfoDto> fileInfoDtoList = fileInfoRepository.queryFileInfoList(idList, BizTypeEnum.WINE.getCode());
        Map<Long, List<FileInfoDto>> linkUrlMap = fileInfoDtoList.stream().collect(Collectors.groupingBy(FileInfoDto::getBizId));
        //查询商品
        List<SkuInfoExtDTO> skuInfoExtDTOS = skuFacade.skusBySkuCode(skuCodeList);
        Map<String, String> skuNameMap = skuInfoExtDTOS.stream().collect(Collectors.toMap(SkuInfoExtDTO::getSkuCode, SkuInfoExtDTO::getName, (e1, e2) -> e2));
        for (WinePageInfoResultDTO winePageInfoResultDTO : winePageInfoResultDTOList) {
            winePageInfoResultDTO.setVolName(winePageInfoResultDTO.getVol() + "%");
            winePageInfoResultDTO.setLastSampleTime(winePageInfoResultDTO.getSampleTime());
            if (linkUrlMap.containsKey(winePageInfoResultDTO.getId())) {
                winePageInfoResultDTO.setLinkUrl(linkUrlMap.get(winePageInfoResultDTO.getId()));
            }
            if (skuNameMap.containsKey(winePageInfoResultDTO.getSkuCode())) {
                winePageInfoResultDTO.setSkuName(skuNameMap.get(winePageInfoResultDTO.getSkuCode()));
            }
            if (skuNameMap.containsKey(winePageInfoResultDTO.getBucketSkuCode())) {
                winePageInfoResultDTO.setBucketSkuName(skuNameMap.get(winePageInfoResultDTO.getBucketSkuCode()));
            }
            if(Objects.nonNull(winePageInfoResultDTO.getStartRipeTime())){
                int diff = (int)DateUtil.diff(winePageInfoResultDTO.getStartRipeTime(), new Date(), 24 * 60 * 60 * 1000);
                winePageInfoResultDTO.setAfterRipeDays(Math.max(0, diff));
            }
            if (serialNoAndOutRecordCodeMap.containsKey(winePageInfoResultDTO.getKey())) {
                WDTOnlineRetailRecordDetailE bucketFrSaleWdtDetail = serialNoAndOutRecordCodeMap.get(winePageInfoResultDTO.getKey());
                if(Objects.nonNull(bucketFrSaleWdtDetail)){
                    //设置空桶订单号
                    winePageInfoResultDTO.setBucketRecordCode(bucketFrSaleWdtDetail.getOutRecordCode());
                    winePageInfoResultDTO.setNewBucketSkuCode(bucketFrSaleWdtDetail.getSkuCode());
                    //设置桶型，为桶的商品名称
                    if (skuNameMap.containsKey(bucketFrSaleWdtDetail.getSkuCode())) {
                        winePageInfoResultDTO.setNewBucketSkuName(skuNameMap.get(bucketFrSaleWdtDetail.getSkuCode()));
                    }
                    //设置桶支付状态
                    winePageInfoResultDTO.setRecordStatus(bucketFrSaleWdtDetail.getRecordStatus());
                }
            }
        }
        PageInfo<WinePageInfoResultDTO> pageInfo = new PageInfo<>(winePageInfoResultDTOList);
        pageInfo.setTotal(page.getTotal());
        return pageInfo;
    }

    /**
     * 根据序列号获取桶订单号
     *
     * @param outRecordCodeAndSerialNoList 酒交易单号+#+序列号
     * @flag true:附加信息页面查询   false：其他
     * @return Map<String, String> key:酒交易单号+#+序列号  value:桶交易单号
     */
    private Map<String, WDTOnlineRetailRecordDetailE> getBucketRecordCode(List<String> outRecordCodeList, boolean flag) {
        if (CollUtil.isEmpty(outRecordCodeList)) {
            return Maps.newHashMap();
        }
        //酒订单订单号#序列号  与  桶交易明细关系
        Map<String, WDTOnlineRetailRecordDetailE> serialNoAndOutRecordCodeMap = Maps.newHashMap();
        //使用批量查询，查询出所有的桶订单编号
        List<WDTOnlineRetailE> bucketFrSaleWdt = Lists.newArrayList();
        if (flag) {
            //附加信息表查询-桶订单号取消或完成不展示【空桶订单号】
            bucketFrSaleWdt = frWDTSaleRepository.queryByOriginOrderCodes(outRecordCodeList)
                    .stream().filter(e -> !Objects.equals(e.getRecordStatus(), FrontRecordStatusVO.OUT_ALLOCATION.getStatus())
                            && !Objects.equals(e.getRecordStatus(), FrontRecordStatusVO.DISABLED.getStatus())).collect(Collectors.toList());
        } else {
            bucketFrSaleWdt = frWDTSaleRepository.queryByOriginOrderCodes(outRecordCodeList).stream().filter(e -> Objects.equals(e.getRecordStatus(), FrontRecordStatusVO.SO_PAID.getStatus())).collect(Collectors.toList());
        }
        bucketFrSaleWdt.removeIf(item -> !Objects.equals(item.getRecordType(), FrontRecordTypeVO.WINE_BUCKET_SALE_RECORD.getType()));
        if (CollectionUtils.isEmpty(bucketFrSaleWdt)) {
            return Maps.newHashMap();
        }
        //桶订单主单，封装参数，查询明细数据
        Map<Long, WDTOnlineRetailE> idBucketFrSaleWdtListMap = new HashMap<>();
        List<Long> idLists = new ArrayList<>();
        for (WDTOnlineRetailE wdtOnlineRetailE : bucketFrSaleWdt) {
            idLists.add(wdtOnlineRetailE.getId());
            idBucketFrSaleWdtListMap.put(wdtOnlineRetailE.getId(), wdtOnlineRetailE);
        }
        //批量查询明细数据
        List<WDTOnlineRetailRecordDetailE> retailRecordDetailES = frWDTSaleRepository.selectFrSaleRecordDetailByIds(idLists);
        if (CollUtil.isEmpty(retailRecordDetailES)) {
            return Maps.newHashMap();
        }
        //将明细与酒订单号#序列号对应为map
        for (WDTOnlineRetailRecordDetailE retailRecordDetailE : retailRecordDetailES) {
            WDTOnlineRetailE wdtOnlineRetailE = idBucketFrSaleWdtListMap.get(retailRecordDetailE.getFrontRecordId());
            retailRecordDetailE.setOutRecordCode(wdtOnlineRetailE.getOutRecordCode());
            retailRecordDetailE.setRecordStatus(wdtOnlineRetailE.getRecordStatus());
            serialNoAndOutRecordCodeMap.put(wdtOnlineRetailE.getOriginOrderCode() + "#" + retailRecordDetailE.getSerialNo(),retailRecordDetailE);
        }
        return serialNoAndOutRecordCodeMap;
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void updateSaleWineById(List<WineInfoUpdateParamDTO> paramDTOList) {
        if (CollUtil.isEmpty(paramDTOList)) {
            throw new RomeException(ResponseMsg.FAIL.getCode(), "更新参数不能为空");
        }
        List<Long> ids = new ArrayList<>();
        Map<Long, WineInfoUpdateParamDTO> wineInfoUpdateParamDTOMap = new HashMap<>();
        for (WineInfoUpdateParamDTO wineInfoUpdateParamDTO : paramDTOList) {
            ids.add(wineInfoUpdateParamDTO.getId());
            wineInfoUpdateParamDTOMap.put(wineInfoUpdateParamDTO.getId(), wineInfoUpdateParamDTO);
        }
        List<FrWDTSaleWineDO> frWDTSaleWineDOS = saleWineRepository.querySaleWineByIdList(ids);
        if (frWDTSaleWineDOS.size() < ids.size()) {
            throw new RomeException(ResponseMsg.FAIL.getCode(), "更新附加信息参数存在数据为空id");
        }
        Map<Long, FrWDTSaleWineDO> idFrWDTSaleWineDOMap = RomeCollectionUtil.listforMap(frWDTSaleWineDOS, "id");
        String outRecordCode = frWDTSaleWineDOS.get(0).getOutRecordCode();
        //查询出 酒订单订单明细，对应的待换桶的明细数据
        Map<String, WDTOnlineRetailRecordDetailE> bucketRecordCodeMap = this.getBucketRecordCode(Arrays.asList(outRecordCode), true);
        for(WineInfoUpdateParamDTO wineInfoUpdateParamDTO : paramDTOList){
            if(StringUtils.isNotEmpty(wineInfoUpdateParamDTO.getNewSerialNo())){
                FrWDTSaleWineDO frWDTSaleWineDO = idFrWDTSaleWineDOMap.get(wineInfoUpdateParamDTO.getId());
                if(StringUtils.isEmpty(frWDTSaleWineDO.getSerialNo())){
                    continue;
                }
                //查询出当前明细，是否存在有待换桶的桶明细
                WDTOnlineRetailRecordDetailE wdtOnlineRetailRecordDetailE = bucketRecordCodeMap.get(frWDTSaleWineDO.getKey());
                //如果存在，则不允许更新序列号信息
                if(wdtOnlineRetailRecordDetailE != null){
                    throw new RomeException(ResponseMsg.FAIL.getCode(), "存在未完成的换桶订单，不允许更新桶序列号");
                }
            }
        }
        List<WinePageInfoResultDTO> winePageInfoResultDTOS = frWDTSaleWineConvertor.doTODTO(frWDTSaleWineDOS);
        //记录原始数据
        List<WinePageInfoResultDTO> oldWinePageInfoResultDTOS = BeanHelper.copyWithCollection(winePageInfoResultDTOS, WinePageInfoResultDTO.class);
        Map<Long, WinePageInfoResultDTO> oldWinePageInfoResultDTOMap = oldWinePageInfoResultDTOS.stream().collect(Collectors.toMap(WinePageInfoResultDTO::getId, Function.identity()));
        //日志
        Map<Long, FrWDTSaleWineLogDO> wineIdAndLogMap = new HashMap<>();
        Map<Long, List<FileInfoDto>> newWineIdAndFileListMap = new HashMap<>();
        for (WineInfoUpdateParamDTO wineInfoUpdateParamDTO : paramDTOList) {
            FrWDTSaleWineDO frWDTSaleWineDO = idFrWDTSaleWineDOMap.get(wineInfoUpdateParamDTO.getId());
            if (frWDTSaleWineDO == null) {
                throw new RomeException(ResponseMsg.FAIL.getCode(), "更新的数据不存在 id = " + wineInfoUpdateParamDTO.getId());
            }
            frWDTSaleWineDO.setStartRipeTime(wineInfoUpdateParamDTO.getStartRipeTime());
            frWDTSaleWineDO.setSampleTime(wineInfoUpdateParamDTO.getSampleTime());
            frWDTSaleWineDO.setVol(wineInfoUpdateParamDTO.getVol());
            frWDTSaleWineDO.setVolume(wineInfoUpdateParamDTO.getVolume());
            frWDTSaleWineDO.setVolumeUnit(wineInfoUpdateParamDTO.getVolumeUnit());
            //更新序列号为空时，不更新
            if (StringUtils.isNotEmpty(wineInfoUpdateParamDTO.getNewSerialNo())){
                frWDTSaleWineDO.setSerialNo(wineInfoUpdateParamDTO.getNewSerialNo());
            }
            frWDTSaleWineDO.setRemark(wineInfoUpdateParamDTO.getRemark());
            frWDTSaleWineDO.setModifier(wineInfoUpdateParamDTO.getModifier());
            FrWDTSaleWineLogDO frWDTSaleWineLogDO = this.getFrWDTSaleWineLogDOList(frWDTSaleWineDO, oldWinePageInfoResultDTOMap);
            wineIdAndLogMap.put(frWDTSaleWineDO.getId(), frWDTSaleWineLogDO);
            newWineIdAndFileListMap.put(frWDTSaleWineDO.getId(), wineInfoUpdateParamDTO.getLinkUrl());
        }


        List<FileInfoDto> fileInfoDtos = fileInfoRepository.queryFileInfoList(ids, BizTypeEnum.WINE.getCode());
        List<Long> logIds = new ArrayList<>();
        for (FileInfoDto fileInfoDto : fileInfoDtos) {
            logIds.add(fileInfoDto.getId());
        }
        //先把原来的记录删除掉，重新插入
        if (CollectionUtils.isNotEmpty(logIds)) {
            fileInfoRepository.deleteByIds(logIds, paramDTOList.get(0).getModifier());
        }
        //循环保存数据
        for (FrWDTSaleWineDO frWDTSaleWineDO : frWDTSaleWineDOS) {
            int result = saleWineRepository.update(frWDTSaleWineDO);
            if (result < 1) {
                throw new RomeException("999", "更新失败，请稍后重试");
            }
            log.info("更新附加明细数据：{}", result);
            //需要更新最新的附件，没有则新增，有的话，就更新数据
            FrWDTSaleWineLogDO frWDTSaleWineLogDO = wineIdAndLogMap.get(frWDTSaleWineDO.getId());
            frSaleWineLogRepository.save(frWDTSaleWineLogDO);
            List<FileInfoDO> saveFileList = new ArrayList<>();

            List<FileInfoDto> newLogFileDOList = newWineIdAndFileListMap.get(frWDTSaleWineDO.getId());
            if (CollectionUtils.isNotEmpty(newLogFileDOList)) {
                for (FileInfoDto fileInfoDto : newLogFileDOList) {
                    FileInfoDO fileInfoDO = fileInfoConvertor.dtoToDo(fileInfoDto);
                    fileInfoDO.setId(null);
                    fileInfoDO.setBizId(frWDTSaleWineDO.getId());
                    fileInfoDO.setBizType(BizTypeEnum.WINE.getCode());
                    saveFileList.add(fileInfoDO);

                    FileInfoDO logFileInfoDO = fileInfoConvertor.dtoToDo(fileInfoDto);
                    logFileInfoDO.setId(null);
                    logFileInfoDO.setBizId(frWDTSaleWineLogDO.getId());
                    logFileInfoDO.setBizType(BizTypeEnum.WINE_LOG.getCode());
                    saveFileList.add(logFileInfoDO);
                }
                //保存文件信息
                fileInfoRepository.saveFileInfoList(saveFileList);
            }
        }
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void updateSaleWineBySerialNo(List<WineInfoUpdateBySerialNoParamDTO> wineInfoUpdateBySerialNoParamDTOList, Long userId) {
        AlikAssert.notEmpty(wineInfoUpdateBySerialNoParamDTOList, "999", "换桶参数不能为空");
        List<Long> detailIds = new ArrayList<>();

        Map<Long, WineInfoUpdateBySerialNoParamDTO> idUpdateDTOMap = new HashMap<>();
        for (WineInfoUpdateBySerialNoParamDTO wineInfoUpdateBySerialNoParamDTO : wineInfoUpdateBySerialNoParamDTOList) {
            detailIds.add(wineInfoUpdateBySerialNoParamDTO.getId());
            idUpdateDTOMap.put(wineInfoUpdateBySerialNoParamDTO.getId(), wineInfoUpdateBySerialNoParamDTO);
            wineInfoUpdateBySerialNoParamDTO.setUserId(userId);
        }

        List<FrWDTSaleWineDO> frWDTSaleWineDOList = saleWineRepository.querySaleWineByIdList(detailIds);
        //id与原wine关系
        Map<Long, FrWDTSaleWineDO> oldSaleWineDOMap = new HashMap<>();
        Map<Long, FrWDTSaleWineDO> idDetailDOMap = new HashMap<>();
        for (FrWDTSaleWineDO frWDTSaleWineDO : frWDTSaleWineDOList) {
            idDetailDOMap.put(frWDTSaleWineDO.getId(), frWDTSaleWineDO);
            FrWDTSaleWineDO old = new FrWDTSaleWineDO();
            BeanUtils.copyProperties(frWDTSaleWineDO, old);
            oldSaleWineDOMap.put(frWDTSaleWineDO.getId(), old);
        }
        //酒交易单号+序列号 与桶订单号关系
        List<String> outRecordCodeList = new ArrayList<>();
        outRecordCodeList.add(frWDTSaleWineDOList.get(0).getOutRecordCode());
        Set<String> bucketFrontRecordCodeList = new HashSet<>();
        //查询出已支付的桶订单数据
        Map<String, WDTOnlineRetailRecordDetailE> serialNoAndOutRecordCodeMap = this.getBucketRecordCode(outRecordCodeList,false);
        for (WineInfoUpdateBySerialNoParamDTO wineInfoUpdateBySerialNoParamDTO : wineInfoUpdateBySerialNoParamDTOList) {
            FrWDTSaleWineDO frWDTSaleWineDO = idDetailDOMap.get(wineInfoUpdateBySerialNoParamDTO.getId());
            if (frWDTSaleWineDO == null) {
                throw new RomeException(ResponseMsg.PARAM_ERROR.getCode(),  "换桶的明细数据不存在， id：" + wineInfoUpdateBySerialNoParamDTO.getId());
            }

            if (StringUtils.isEmpty(frWDTSaleWineDO.getSerialNo())) {
                throw new RomeException(ResponseMsg.PARAM_ERROR.getCode(),  "序列号为空，不支持换桶, skuCode：" + frWDTSaleWineDO.getSkuCode());
            }

            //判断存在对应的桶订单
            WDTOnlineRetailRecordDetailE wdtOnlineRetailRecordDetailE = serialNoAndOutRecordCodeMap.get(frWDTSaleWineDO.getKey());
            if (wdtOnlineRetailRecordDetailE == null) {
                throw new RomeException(ResponseMsg.PARAM_ERROR.getCode(), "对应的桶订单，不存在或不是已支付状态, skuCode：" + frWDTSaleWineDO.getSkuCode());
            }
            wineInfoUpdateBySerialNoParamDTO.setNewBucketSkuCode(wdtOnlineRetailRecordDetailE.getSkuCode());
            //表示操作此条明细换桶
            wdtOnlineRetailRecordDetailE.setHasChangeBucket(1);
            bucketFrontRecordCodeList.add(wdtOnlineRetailRecordDetailE.getRecordCode());
        }

        //如果桶订单存在多条明细，则必须一次全部更换完成，否则部分出库下次校验会有问题
        Map<String, List<WDTOnlineRetailRecordDetailE>> outRecordCodeDetailMap = new HashMap<>();
        boolean hasAllChange = true;
        for (WDTOnlineRetailRecordDetailE onlineRetailRecordDetailE : serialNoAndOutRecordCodeMap.values()) {
            //通过桶交易单号，对桶订单明细进行分组
            List<WDTOnlineRetailRecordDetailE> wdtOnlineRetailRecordDetailES = outRecordCodeDetailMap.getOrDefault(onlineRetailRecordDetailE.getOutRecordCode(), new ArrayList<>());
            if (onlineRetailRecordDetailE.getHasChangeBucket() == null) {
                //设置未换桶标识
                onlineRetailRecordDetailE.setHasChangeBucket(0);
                //存在未更换的， 直接设置标识未false
                hasAllChange = false;
            }
            //如果不为空，新增之前，校验与第一个换桶标识是否一致，不一致则报错
            if (CollectionUtils.isNotEmpty(wdtOnlineRetailRecordDetailES)
                    && Objects.equals(wdtOnlineRetailRecordDetailES.get(0).getHasChangeBucket() , onlineRetailRecordDetailE.getHasChangeBucket())) {
                throw new RomeException(ResponseMsg.PARAM_ERROR.getCode(), "桶订单所有桶明细必须一次全部换完，不支持部分更换，桶订单号：" + onlineRetailRecordDetailE.getOutRecordCode());
            }
            wdtOnlineRetailRecordDetailES.add(onlineRetailRecordDetailE);
            outRecordCodeDetailMap.put(onlineRetailRecordDetailE.getOutRecordCode(), wdtOnlineRetailRecordDetailES);
        }

        //1.更新附加信息表数据
        for (WineInfoUpdateBySerialNoParamDTO wineInfoUpdateBySerialNoParamDTO : wineInfoUpdateBySerialNoParamDTOList) {
            FrWDTSaleWineDO frWDTSaleWineDO = idDetailDOMap.get(wineInfoUpdateBySerialNoParamDTO.getId());
            frWDTSaleWineDO.setBucketSkuCode(wineInfoUpdateBySerialNoParamDTO.getNewBucketSkuCode());
            frWDTSaleWineDO.setSerialNo(wineInfoUpdateBySerialNoParamDTO.getNewSerialNo());
            frWDTSaleWineDO.setRemark(wineInfoUpdateBySerialNoParamDTO.getRemark());
            frWDTSaleWineDO.setModifier(userId);
            int result = saleWineRepository.updateSaleWineBucketById(frWDTSaleWineDO);
           if (result < 1) {
               throw new RomeException(ResponseMsg.PARAM_ERROR.getCode(), "更新扩展表失败，请稍后再试");
           }
        }

        //查询酒订单
        WDTOnlineRetailE wineFrSaleWdt = frWDTSaleRepository.queryByOutRecordCode(outRecordCodeList.get(0));
        //3.判断酒订单是否为换桶完成
        if (hasAllChange) {
            //更新酒订单tenant_id=0换桶完成
            int result = frWDTSaleRepository.updateToTenantId(wineFrSaleWdt.getId(), 0, wineFrSaleWdt.getVersionNo());
            if (result < 1) {
                throw new RomeException(ResponseMsg.PARAM_ERROR.getCode(), "更新酒订单标识更新失败，请稍后再试");
            }
        }

        List<FileInfoDto> fileInfoDtos = fileInfoRepository.queryFileInfoList(detailIds, BizTypeEnum.WINE.getCode());
        Map<Long,List<FileInfoDto>> oldFileListMap = new HashMap<>();
        List<Long> logIds = new ArrayList<>();
        for (FileInfoDto fileInfoDto : fileInfoDtos) {
            List<FileInfoDto> oldFileList = oldFileListMap.getOrDefault(fileInfoDto.getBizId(), new ArrayList<>());
            oldFileList.add(fileInfoDto);
            oldFileListMap.put(fileInfoDto.getBizId(),oldFileList);
        }
        //5.存日志
        List<FrWDTSaleWineLogDO> frWDTSaleWineLogDOList = Lists.newArrayList();
        for (WineInfoUpdateBySerialNoParamDTO wineInfoUpdateBySerialNoParamDTO : wineInfoUpdateBySerialNoParamDTOList) {
            FrWDTSaleWineDO frWDTSaleWineDO = idDetailDOMap.get(wineInfoUpdateBySerialNoParamDTO.getId());
            FrWDTSaleWineDO oldSaleWineDO = oldSaleWineDOMap.get(frWDTSaleWineDO.getId());
            //封装日志参数
            FrWDTSaleWineLogDO frWDTSaleWineLogDO = frWDTSaleWineConvertor.doTOLogDto(frWDTSaleWineDO);;
            frWDTSaleWineLogDO.setId(null);
            frWDTSaleWineLogDO.setCreateTime(new Date());
            frWDTSaleWineLogDO.setUpdateTime(new Date());
            frWDTSaleWineLogDO.setOperateType(2);
            frWDTSaleWineLogDO.setVersionNo(0);
            frWDTSaleWineLogDO.setWineId(frWDTSaleWineDO.getId());
            frWDTSaleWineLogDO.setRecordDetailId(frWDTSaleWineDO.getRecordDetailId());
            frWDTSaleWineLogDO.setFrontRecordCode(frWDTSaleWineDO.getFrontRecordCode());
            frWDTSaleWineLogDO.setCreator(userId);
            frWDTSaleWineLogDO.setModifier(userId);
            if (Objects.nonNull(oldSaleWineDO)) {
                frWDTSaleWineLogDO.setBeforeBucketSkuCode(oldSaleWineDO.getBucketSkuCode());
                frWDTSaleWineLogDO.setBeforeSerialNo(oldSaleWineDO.getSerialNo());
            }
            //设置空桶订单号
            frWDTSaleWineLogDO.setBucketRecordCode(wineInfoUpdateBySerialNoParamDTO.getBucketRecordCode());
            //插入日志
            frSaleWineLogRepository.save(frWDTSaleWineLogDO);
            List<FileInfoDto> oldFileDOList = oldFileListMap.get(frWDTSaleWineDO.getId());
            if (CollectionUtils.isNotEmpty(oldFileDOList)) {
                List<FileInfoDO> saveFileList = new ArrayList<>();
                //保存日志
                List<FileInfoDto> logFileDOList = BeanHelper.copyWithCollection(oldFileDOList, FileInfoDto.class);
                for (FileInfoDto fileInfoDto : logFileDOList) {
                    FileInfoDO fileInfoDO = fileInfoConvertor.dtoToDo(fileInfoDto);
                    fileInfoDO.setId(null);
                    fileInfoDO.setBizId(frWDTSaleWineLogDO.getId());
                    fileInfoDO.setBizType(BizTypeEnum.WINE_LOG.getCode());
                    saveFileList.add(fileInfoDO);
                }
                fileInfoRepository.saveFileInfoList(saveFileList);
            }

        }
        //前置单列表，需要全部出库
        List<String> warehouseRecordCodes = frontWarehouseRecordRelationRepository.getRecordCodesByFrontRecordCodes(new ArrayList<>(bucketFrontRecordCodeList));
        if (CollectionUtils.isEmpty(warehouseRecordCodes)) {
            throw new RomeException(ResponseMsg.PARAM_ERROR.getCode(), "未查询到桶订单后置单列表");
        }
        stockCoreFacade.wineBucketWarehouseDelivery(warehouseRecordCodes);

    }


    /**
     * 封装日志参数
     *
     * @param frWDTSaleWineDO          新数据
     * @param winePageInfoResultDTOMap 老数据
     * @return
     */
    private FrWDTSaleWineLogDO getFrWDTSaleWineLogDOList(FrWDTSaleWineDO frWDTSaleWineDO, Map<Long, WinePageInfoResultDTO> winePageInfoResultDTOMap) {
        //封装日志参数
        FrWDTSaleWineLogDO frWDTSaleWineLogDO = frWDTSaleWineConvertor.doTOLogDto(frWDTSaleWineDO);
        frWDTSaleWineLogDO.setId(null);
        frWDTSaleWineLogDO.setCreateTime(new Date());
        frWDTSaleWineLogDO.setUpdateTime(new Date());
        frWDTSaleWineLogDO.setOperateType(1);
        frWDTSaleWineLogDO.setVersionNo(0);
        frWDTSaleWineLogDO.setWineId(frWDTSaleWineDO.getId());
        frWDTSaleWineLogDO.setRecordDetailId(frWDTSaleWineDO.getRecordDetailId());
        frWDTSaleWineLogDO.setFrontRecordCode(frWDTSaleWineDO.getFrontRecordCode());
        if (winePageInfoResultDTOMap.containsKey(frWDTSaleWineDO.getId())) {
            WinePageInfoResultDTO winePageInfoResultDTO = winePageInfoResultDTOMap.get(frWDTSaleWineDO.getId());
            frWDTSaleWineLogDO.setBeforeSerialNo(winePageInfoResultDTO.getSerialNo());
            frWDTSaleWineLogDO.setBeforeBucketSkuCode(winePageInfoResultDTO.getBucketSkuCode());
            frWDTSaleWineLogDO.setLastSampleTime(winePageInfoResultDTO.getSampleTime());
        }
        return frWDTSaleWineLogDO;
    }
}
