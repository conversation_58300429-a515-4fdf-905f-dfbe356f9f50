package com.rome.stock.innerservice.domain.entity.frontrecord;

import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Component;

/**
 * @Description
 * <AUTHOR>
 * @Date 2019/5/11 10:27
 * @Version 1.0
 */
@Component
@Scope("prototype")
@Data
@EqualsAndHashCode(callSuper = false)
public class ShopConsumeAdjustRecordDetailE extends AbstractFrontRecordDetail{

    /**
     * 门店报废单id
     */
    private Long frontRecordId;

    /**
     * 商品skuId
     */
    private Long skuId;

    /**
     * 调整单位
     */
    private String unit;

    /**
     * 调整单位编码
     */
    private String unitCode;

    /**
     * 商品规格
     */
    private String skuStandard;

    /**
     * 批次备注
     */
    private String remark;


}
