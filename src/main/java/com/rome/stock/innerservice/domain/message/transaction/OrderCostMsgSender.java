package com.rome.stock.innerservice.domain.message.transaction;

import com.alibaba.fastjson.JSON;
import com.rome.stock.innerservice.domain.entity.warehouserecord.WarehouseRecordE;
import com.rome.stock.innerservice.domain.repository.warehouserecord.WarehouseRecordRepository;
import com.rome.stock.innerservice.infrastructure.dataobject.TransactionMessageDO;
import com.rome.stock.innerservice.infrastructure.mapper.TransactionMessageMapper;
import lombok.extern.slf4j.Slf4j;
import org.apache.rocketmq.client.producer.SendResult;
import org.apache.rocketmq.client.producer.SendStatus;
import org.apache.rocketmq.spring.core.RocketMQTemplate;
import org.apache.rocketmq.spring.support.RocketMQHeaders;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationEvent;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.messaging.Message;
import org.springframework.messaging.support.MessageBuilder;
import org.springframework.stereotype.Component;
import org.springframework.transaction.event.TransactionPhase;
import org.springframework.transaction.event.TransactionalEventListener;

import javax.annotation.Resource;

/**
 * @Description MqTool
 * <AUTHOR>
 * @Date 2021/7/26 16:32
 * @Version 1.0
 **/
@Slf4j
@Component
public class OrderCostMsgSender {
	@Autowired
	private ApplicationEventPublisher applicationEventPublisher;
	@Resource
	private RocketMQTemplate rocketMQTemplate;
	@Resource
	private TransactionMessageMapper transactionMapper;
	@Resource
	private WarehouseRecordRepository warehouseRecordRepository;

	public void sendMsg(MessageDTO t) {
		String key = t.messageKey();
		String msg = JSON.toJSONString(t);
		//根据RecordCode查询后置单类型和实仓编号
		WarehouseRecordE warehouseRecordE=warehouseRecordRepository.queryWarehouseRecordByRecordCode(t.getRecordCode());
		Integer recordType=0;
		Long realWarehouseId=0L;
		if(null != warehouseRecordE){
			recordType=warehouseRecordE.getRecordType();
			realWarehouseId=warehouseRecordE.getRealWarehouseId();
		}
		//跟出入库在同一个事务中，所以这个记录保存跟出入库单更新一起成功或失败
		TransactionMessageDO transactionMsgDO=new TransactionMessageDO();
		transactionMsgDO.setTopic(TransactionConstant.TX_PRODUCER_TOPIC);
		transactionMsgDO.setKeyCode(key);
		transactionMsgDO.setMsg(msg);
		transactionMsgDO.setRecordCode(t.getRecordCode());
		transactionMsgDO.setStatus(0);
		transactionMsgDO.setErrorMsg("消息初始化");
		transactionMsgDO.setRecordType(recordType);
		transactionMsgDO.setRealWarehouseId(realWarehouseId);
		transactionMapper.saveforCommon(transactionMsgDO);
		applicationEventPublisher.publishEvent(new StockAfterTransactionEvent(t));
	}


	public static class StockAfterTransactionEvent extends ApplicationEvent {
		public StockAfterTransactionEvent(MessageDTO messageDTO) {
			super(messageDTO);
		}
	}

	@Component
	public class StockTransactionListener {
		@TransactionalEventListener(phase = TransactionPhase.AFTER_COMMIT)
		public void onHandleEvent(StockAfterTransactionEvent event) {
			MessageDTO t = (MessageDTO) event.getSource();
			//发送消息
			sendMQ(t);
		}

		/**
		 * 发送消息
		 *
		 * @return
		 */
		public void sendMQ(MessageDTO t) {
			String key = t.messageKey();
			String msg = JSON.toJSONString(t);
			try {
				String destination = TransactionConstant.TX_PRODUCER_TOPIC + ":" + TransactionConstant.TX_PRODUCER_TOPIC_TAGS;
				Message<String> message = MessageBuilder.withPayload(msg)
						.setHeader(RocketMQHeaders.KEYS, key)
						.build();
				//delayLevel=1s 5s 10s 30s 1m 2m 3m 4m 5m 6m 7m 8m 9m 10m 20m 30m 1h 2h
				SendResult sendResult = rocketMQTemplate.syncSend(destination, message, 3000, 16);
				// 发送结果
				if (sendResult.getSendStatus() == SendStatus.SEND_OK) {
					transactionMapper.updateSendStatus(TransactionConstant.TX_PRODUCER_TOPIC, key, 1, null);
				}
			} catch (Exception e) {
				log.error("======事件消息发送失败,原因={},单号={}", e.getMessage(), t.getRecordCode(), e);
				String errorMsg = e.getMessage();
				if (null != errorMsg && errorMsg.length() > 1000) {
					errorMsg = errorMsg.substring(0, 1000);
				}
				transactionMapper.updateSendStatus(TransactionConstant.TX_PRODUCER_TOPIC, key, 0, errorMsg);
			}
		}
	}
}
