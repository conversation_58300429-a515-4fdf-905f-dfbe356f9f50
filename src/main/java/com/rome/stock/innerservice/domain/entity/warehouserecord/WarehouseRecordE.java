package com.rome.stock.innerservice.domain.entity.warehouserecord;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

import com.rome.stock.innerservice.api.dto.Invoice;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Component;

import com.rome.stock.innerservice.domain.entity.AddressE;
import com.rome.stock.innerservice.domain.entity.RealWarehouseE;
import com.rome.stock.innerservice.domain.entity.frontrecord.PurchaseOrderDetailE;
import com.rome.stock.innerservice.remote.orderCneter.dto.CommonFrontRecordDTO;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.extern.slf4j.Slf4j;

/**
 * 出入库单
 *
 * <AUTHOR> 2019/4/17 20:52
 */
@Data
@Slf4j
@Component
@Scope("prototype")
@EqualsAndHashCode(callSuper = false)
public class WarehouseRecordE extends AbstractWarehouseRecord{

    /**
     * 实仓仓库名称
     */
    private String realWarehouseName;

    /**
     * 实仓仓库编号
     */
    private String realWarehouseCode;
    /**
     * 工厂代码
     */
    private String factoryCode;

    /**
     * 仓库类型
     */
    private Integer warehouseType;

    /**
     * 用户code
     */
    private String userCode;
    /**
     * 手机号
     */
    private String mobile;
    /**
     * 退货流程用：退货原因
     */
    private String reason;
    /**
     * 退货流程用：退货原因
     */
    private String reasons;
    /**
     * 退货流程用：退货原因code
     */
    private String reasonCode;

    /**
     * 开始日期
     */
    private Date startDate;

    /**
     * 结束日期
     */
    private Date endDate;
    /**
     * 仓库外部编号-wms
     */
    private String realWarehouseOutCode;

    /**
     * 前置单编码
     */
    private String frontRecordCode;

    /**
     * 采购前置单明细
     */
    private List<PurchaseOrderDetailE> purchaseOrderDetailList;

    /**
     * wms系统code
     */
    private Integer wmsCode;

    /**
     * 地址信息
     */
    private List<AddressE> addressList;

    /**
     * 期望收货日期_开始
     */
    private Date expectReceiveDateStart;

    /**
     * 期望收货日期_截止
     */
    private Date expectReceiveDateEnd;

    /**
     * 店铺编码
     */
    private String shopCode;
    /**
     * 店铺名称
     */
    private String shopName;

    /**
     * tms派车单号
     */
    private String tmsRecordCode;

    /**
     * 供应商编码
     */
    private String supplierCode;

    /**
     * 供应商名称
     */
    private String supplierName;

    /**
     * 出向仓库信息
     */
    private RealWarehouseE outWarehouseInfo;

    /**
     * 入向仓库信息
     */
    private RealWarehouseE inWarehouseInfo;

    /**
     * 出向工厂
     */
    private String outFactory;

    /**
     * 出向货仓编码
     */
    private String outWarehouse;

    /**
     * 入向工厂
     */
    private String inFactory;

    /**
     * 入向仓库编码
     */
    private String inWarehouse;

    /**
     * 仓库编码List
     */
    private List<Long> warehouseIdList;

    /**
     * 是否退货调拨  0.不是 1.是
     */
    private Integer isReturnAllotcate;

    /**
     * 单据拆分标识
     */
    private String splitFlag;

    /**
     * SAP派车状态：0-无需派车 1-待派车 2-已派车
     */
    private Integer syncDispatchStatus;

    /**
     * 单据类型List
     */
    private List<Integer> recordTypeList;

    /**
     * 是否是自营外卖
     */
    private Integer selfTakeout;

    private String outRecordCode;

    /**
     * 30表示收货完成标识(采购专用)
     */
    private Integer syncFulfillmentStatus;

    /**
     * 前置单创建时间
     */
    private Date FrontCreateTime;

    /**
     * 物流公司编码
     */
    private String logisticsCode;

    /**
     * 出库或入库完成时间
     */
    private Date outOrInTime;

    /**
     * 下单时间
     */
    private Date outCreateTime;

    /**
     * 支付时间
     */
    private Date payTime;

    /**
     * 前台订单号
     */
    private String soCode;

    /**
     * 订单实付金额
     */
    private BigDecimal orderAmount;

    /**
     * 订单金额
     */
    private BigDecimal orginAmount;

    /**
     * 渠道名称
     */
    private String channelName;


    /**
     * 鲲鹏渠道
     */
    private String kpChannelCode;

  /**
     * 领用日期(YYYY-MM-DD）
     */
    private Date receiveDate;

    /**
     * 创建人工号
     */
    private Long creator;

    /**
     * 运输方式 1自提 2快递 3内部通道
     */
    private Integer transWay;

    /**
     * 申请人工号
     */
    private String applier;

    /**
     * 申请人联系电话
     */
    private String applierMobile;

    //成本中心编号
    private String costCenterCode;

    //成本中心名称
    private String costCenterName;

    //差异过账专用
    private CommonFrontRecordDTO commonFrontRecord;

    @ApiModelProperty("客户")
    private String customerName;

    @ApiModelProperty("客户电话")
    private String customerTel;

    @ApiModelProperty("地址")
    private String address;

    @ApiModelProperty("创建人姓名")
    private String creatorName;

    @ApiModelProperty("下单方")
    private String orderCustCode;

    private String wmsRecordCode;

    /**
     * sap过账查询单号。过账收货单单号,收货单号+_+质检回调次数或者入库单号
     */
    private String receiptRecordCode;

    private Integer lineTotal;

    /**
     * job分片总数
     */
    private  Integer shardingTotalCount;
    /**
     * 当天任务实例的分配id
     */
    private  Integer shardingItem;

    private Long tenantId;
    /**
     * 备注
     */
    private String remark;

    /**
     * 卖家备注
     */
    private String sellerMessage;

    private String oaid;
    /**
     * 标签类型(1.门店自提，2.仓库次日达，3.供应商外卖，4.供应商送门店)',
     */
    private Integer labType;

    /**
     * 销售订单商品类型 1-普通,2-预售,3-拼团,4-拼券,5-旺店通,6-POS门店,7-外卖自营,8-外卖第三方营,9-电商超市,10-2B分销,11-加盟商 12 虚拟商品
     */
    private Integer transType;

    private String orderRemarkUser;

    /**
     * 销售系统单号
     */
    private String saleSystemCode;
    
    /**
     * 客户编码
     */
    private String customCode;
    
    /**
     * 客户名称
     */
    private String customName;
    
    /**
     * 经销的92单号
     */
    private String saleSapOrderCode;
    /**
     * 仓库业务类型: 1: 直营门店 2: 加盟门店 3: 仓库 4. 其他仓库"
     */
    private Integer rwBusinessType;

    /**
     * 商户信息，代发订单所属店铺appid-微信视频号-旺店通发货相关
     */
    private String buyerNick;

    /**
     * 代发订单密文-微信视频号-旺店通发货相关
     */
    private String ewayBillOrderCode;

    /**
     * 原箱批次要求( 0:无要求 1:要求原箱)
     */
    private Integer boxBatchTag;



    /**
     * 快递发送方式:1.电商仓直发
     */
    private Integer transTypeWay;
    /**
     * 单据状态集合
     */
    private List<Integer> recordStatusList;

    // 仓店一体标识 0 -非仓店一体   1仓店一体
    private Integer warehouseStoreIdenti;
    /**
     * 不查询对应的仓库ID
     */
    private List<Long> notRealWarehouseIdList;
    /**
     * 订单来源平台编码
     */
    private String sourcePlatformCode;

    @ApiModelProperty("拼团标识")
    private Boolean specialType=false;

    /**
     * 波次号
     */
    private String waveCode;

    /**
     * 车牌号
     */
    private String carNo;


    @ApiModelProperty(value = "发票信息")
    private List<Invoice> invoices;
    /**
     * 派车单号
     */
    private String expressCode;

}
