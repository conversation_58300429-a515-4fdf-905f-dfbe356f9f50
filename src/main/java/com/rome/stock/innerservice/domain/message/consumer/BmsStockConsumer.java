package com.rome.stock.innerservice.domain.message.consumer;

import com.rome.scm.common.rocketmq.CustomConsumerListener;
import com.rome.stock.common.enums.CustomRocketMQEnum;
import com.rome.stock.innerservice.domain.service.BmsService;
import lombok.extern.slf4j.Slf4j;
import org.apache.rocketmq.common.message.MessageExt;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * @Description: 出入库回传bms消费mq
 */
@Slf4j
@Service
public class BmsStockConsumer implements CustomConsumerListener<MessageExt> {

    @Resource
    private BmsService bmsService;

    @Override
    public void onMessage(MessageExt messageExt, String businessNo, String msgKey) {
        log.info("库存库龄bms消息消费，msgID: {}，消费次数: {}", messageExt.getMsgId(), messageExt.getReconsumeTimes());
        String serialNo = new String(messageExt.getBody());
        bmsService.pushDailyBatchStockToBmsBySerialNo(serialNo, null);

    }

    @Override
    public String getRocketMQCode() {
        return CustomRocketMQEnum.MQ_BMS_STOCK_PUSH.getCode();
    }


}
