package com.rome.stock.innerservice.domain.message.consumer;

import com.alibaba.fastjson.JSONObject;
import com.rome.stock.common.constants.KibanaLogConstants;
import com.rome.stock.common.enums.CustomRocketMQEnum;
import com.rome.scm.common.rocketmq.CustomConsumerListener;
import com.rome.stock.innerservice.common.CoreKibanaLog;
import com.rome.stock.innerservice.remote.orderCneter.dto.ReceiveDTO;
import com.rome.stock.innerservice.remote.orderCneter.facade.OrderCenterFacade;
import lombok.extern.slf4j.Slf4j;
import org.apache.rocketmq.common.message.MessageExt;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;

/**
 * @Description: 大仓采购周转箱调用订单中心创建领用单MQ消费
 */
@Slf4j
@Service
public class PurchaseInWarehouseReceiveConsumer implements CustomConsumerListener<MessageExt> {

    @Resource
    private OrderCenterFacade orderCenterFacade;

    @Override
    public void onMessage(MessageExt messageExt, String businessNo, String msgKey) {
        log.info("大仓采购周转箱调用订单中心创建领用单推送MQ消费，msgID: {}，消费次数: {}", messageExt.getMsgId(), messageExt.getReconsumeTimes());
        String json = new String(messageExt.getBody());
        if (StringUtils.isEmpty(json)) {
            log.error("大仓采购周转箱调用订单中心创建领用单推送MQ消费内容为空，msgID: {}", messageExt.getMsgId());
            return;
        }
        ReceiveDTO receiveDTO = JSONObject.parseObject(json, ReceiveDTO.class);
        log.warn(CoreKibanaLog.getJobLog(KibanaLogConstants.CONSUMER_MQ, "purchaseInWarehouseReceiveConsumer", messageExt.getMsgId(), json));
        orderCenterFacade.createReceiveByStock(receiveDTO);
    }

	@Override
	public String getRocketMQCode() {
		return CustomRocketMQEnum.MQ_STOCK_PURCHASE_IN_WAREHOUSE_RECEIVE_PUSH.getCode();
	}

}
