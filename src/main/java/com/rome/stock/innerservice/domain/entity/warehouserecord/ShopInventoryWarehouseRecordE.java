package com.rome.stock.innerservice.domain.entity.warehouserecord;

import com.rome.arch.core.domain.EntityFactory;
import com.rome.stock.common.enums.warehouse.WarehouseRecordTypeVO;
import com.rome.stock.innerservice.api.dto.warehouserecord.InWarehouseRecordDTO;
import com.rome.stock.innerservice.api.dto.warehouserecord.OutWarehouseRecordDTO;
import com.rome.stock.innerservice.api.dto.warehouserecord.RecordDetailDTO;
import com.rome.stock.innerservice.constant.WarehouseRecordBatchStatusVO;
import com.rome.stock.innerservice.constant.WarehouseRecordBusinessTypeVO;

import com.rome.stock.innerservice.constant.WmsSyncStatusVO;
import com.rome.stock.innerservice.domain.entity.RealWarehouseE;
import com.rome.stock.innerservice.domain.repository.RealWarehouseRepository;
import com.rome.stock.innerservice.domain.repository.warehouserecord.ShopInventoryWarehouseRepository;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;

/**
 * 门店盘点单
 *
 * <AUTHOR> 2019/4/17 20:52
 */
@Data
@Slf4j
@Component
@Scope("prototype")
@EqualsAndHashCode(callSuper = false)
public class ShopInventoryWarehouseRecordE extends AbstractWarehouseRecord{

    @Resource
    private ShopInventoryWarehouseRepository shopInventoryWarehouseRepository;
    @Resource
    private EntityFactory entityFactory;
    private RealWarehouseRepository realWarehouseRepository;
    /**
     * 保存入库单
     */
    public void addInWarehouseRecord() {
        //保存入库单
        long id = shopInventoryWarehouseRepository.saveInWarehouseRecord(this);
        this.setId(id);
        this.addWarehouseRecordDetail();
    }

    /**
     * 保存出库单
     */
    public void addOutWarehouseRecord() {
        //保存出库单
        long id = shopInventoryWarehouseRepository.saveOutWarehouseRecord(this);
        this.setId(id);
        this.addWarehouseRecordDetail();
    }

    /**
     * 保存盘点单明细
     */
    public void addWarehouseRecordDetail(){
        this.warehouseRecordDetails.forEach(record -> record.setWarehouseRecordDetail(this));
        shopInventoryWarehouseRepository.saveWarehouseRecordDetails(this.warehouseRecordDetails);
//        this.addFrontRecordAndWarehouseRelation();
    }

    /**
     * 根据前置单生成出库单
     */
    public void createOutRecordByFrontRecord(OutWarehouseRecordDTO dto,RealWarehouseE outWarehouse){
//        createRecodeCode(WarehouseRecordTypeVO.SHOP_INVENTORY_OUT_WAREHOUSE_RECORD.getCode());
//        this.setAbstractFrontRecord(frontRecord);
        this.setRecordCode(dto.getRecordCode());
        this.setRealWarehouseId(outWarehouse.getId());
        this.setBusinessType(WarehouseRecordBusinessTypeVO.OUT_WAREHOUSE_RECORD.getType());
        this.setRecordType(WarehouseRecordTypeVO.SHOP_INVENTORY_OUT_WAREHOUSE_RECORD.getType());
        this.setSyncWmsStatus(WmsSyncStatusVO.NO_REQUIRED.getStatus());
        this.setBatchStatus(WarehouseRecordBatchStatusVO.INIT.getStatus());
        List<RecordDetailDTO> frontRecordDetails=dto.getDetailList();
        this.setWarehouseRecordDetails(new ArrayList<>(frontRecordDetails.size()));
        if(frontRecordDetails!=null){
            for(RecordDetailDTO detailE:frontRecordDetails){
                WarehouseRecordDetail warehouseRecordDetail=entityFactory.createEntity(WarehouseRecordDetail.class);
//                warehouseRecordDetail.createRecordDetailByActualFrontRecord(detailE);
                warehouseRecordDetail.setSkuCode(detailE.getSkuCode());
                warehouseRecordDetail.setPlanQty(detailE.getBasicSkuQty());
                warehouseRecordDetail.setActualQty(detailE.getBasicSkuQty());
                warehouseRecordDetail.setUnit(detailE.getBasicUnit());
                warehouseRecordDetail.setUnitCode(detailE.getBasicUnitCode());
                this.warehouseRecordDetails.add(warehouseRecordDetail);
            }
            //设置skuCode和skuID
            this.initWarehouseRecodeDetail();
        }
    }

    /**
     * 根据前置单生成入库单
     */
    public void createInRecordByFrontRecord(InWarehouseRecordDTO dto,RealWarehouseE inWarehouse){
//        createRecodeCode(WarehouseRecordTypeVO.SHOP_INVENTORY_IN_WAREHOUSE_RECORD.getCode());
//        this.setAbstractFrontRecord(frontRecord);
        this.setRecordCode(dto.getRecordCode());
        this.setRealWarehouseId(inWarehouse.getId());
        this.setBusinessType(WarehouseRecordBusinessTypeVO.IN_WAREHOUSE_RECORD.getType());
        this.setRecordType(WarehouseRecordTypeVO.SHOP_INVENTORY_IN_WAREHOUSE_RECORD.getType());
        this.setSyncWmsStatus(WmsSyncStatusVO.NO_REQUIRED.getStatus());
        this.setBatchStatus(WarehouseRecordBatchStatusVO.INIT.getStatus());
        List<RecordDetailDTO> frontRecordDetails=dto.getDetailList();
        this.setWarehouseRecordDetails(new ArrayList<>(frontRecordDetails.size()));
        if(frontRecordDetails != null){
            for(RecordDetailDTO detailE:frontRecordDetails){
                WarehouseRecordDetail warehouseRecordDetail=entityFactory.createEntity(WarehouseRecordDetail.class);
//                warehouseRecordDetail.createRecordDetailByActualFrontRecord(detailE);
                warehouseRecordDetail.setSkuCode(detailE.getSkuCode());
                warehouseRecordDetail.setPlanQty(detailE.getBasicSkuQty());
                warehouseRecordDetail.setActualQty(detailE.getBasicSkuQty());
                warehouseRecordDetail.setUnit(detailE.getBasicUnit());
                warehouseRecordDetail.setUnitCode(detailE.getBasicUnitCode());
                this.warehouseRecordDetails.add(warehouseRecordDetail);
            }
            //设置skuCode和skuID
            this.initWarehouseRecodeDetail();
        }
    }


}
