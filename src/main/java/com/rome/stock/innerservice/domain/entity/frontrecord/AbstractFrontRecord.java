package com.rome.stock.innerservice.domain.entity.frontrecord;

import com.rome.stock.innerservice.common.SkuInfoTools;
import com.rome.stock.innerservice.common.SkuQtyUnitTools;
import com.rome.stock.innerservice.domain.entity.BaseE;
import com.rome.stock.innerservice.domain.entity.RealWarehouseE;
import com.rome.stock.innerservice.domain.service.OrderUtilService;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;

/**
 * 类FrontRecord的实现描述: 通用前置单
 *
 * <AUTHOR> 2019/4/17 21:46
 */
@Data
@Slf4j
@Component
@Scope("prototype")
@EqualsAndHashCode(callSuper = false)
public abstract class AbstractFrontRecord extends BaseE {

    @Resource
    private SkuQtyUnitTools skuQtyUnitTools;
    @Resource
    private SkuInfoTools skuInfoTools;
    @Resource
    private OrderUtilService orderUtilService;

    /**
     * 生成单号、明细单位换算
     */
    public void initFrontRecord(String frontCode, List<? extends AbstractFrontRecordDetail> frontRecordDetails,Long merchantId) {
        //获取单据编号
        String code = orderUtilService.queryOrderCode(frontCode);
        this.setRecordCode(code);
        //设置商品code或id
        skuInfoTools.convertSkuCode(frontRecordDetails,merchantId);
        //单位转换
        skuQtyUnitTools.convertRealToBasic(frontRecordDetails,merchantId);
        //批次单位转换
        for(AbstractFrontRecordDetail detail : frontRecordDetails){
            if(detail.getBatchStocks() != null && detail.getBatchStocks().size() > 0){
                detail.getBatchStocks().forEach( batchStock -> batchStock.changeBatchStockUnit(detail));
            }
        }
    }

    /**
     * 生成单号、明细单位换算
     */
    public void initFrontRecord(String frontCode, List<? extends AbstractFrontRecordDetail> frontRecordDetails) {
        //获取单据编号
        String code = orderUtilService.queryOrderCode(frontCode);
        this.setRecordCode(code);
        //设置商品code或id
        skuInfoTools.convertSkuCode(frontRecordDetails);
        //单位转换
        skuQtyUnitTools.convertRealToBasic(frontRecordDetails);
        //批次单位转换
        for(AbstractFrontRecordDetail detail : frontRecordDetails){
            if(detail.getBatchStocks() != null && detail.getBatchStocks().size() > 0){
                detail.getBatchStocks().forEach( batchStock -> batchStock.changeBatchStockUnit(detail));
            }
        }
    }

    /**
     * 生成单号、明细单位换算
     */
    public void initReturnFrontRecord(String frontCode, List<? extends AbstractFrontRecordDetail> frontRecordDetails) {
        //获取单据编号
        String code = orderUtilService.queryOrderCode(frontCode);
        this.setRecordCode(code);
        //设置商品code或id
        skuInfoTools.convertSkuCode(frontRecordDetails);
    }

    /**
     * 将查询出来的明细转换一下用于页面展示
     * @param frontRecordDetails
     */
    public void convertRealToBasic(List<? extends AbstractFrontRecordDetail> frontRecordDetails) {
        skuQtyUnitTools.convertRealToBasic(frontRecordDetails);
    }

    /**
     * 将查询出来的明细转换一下用于页面展示
     * @param frontRecordDetails
     */
    public void convertRealToBasic(List<? extends AbstractFrontRecordDetail> frontRecordDetails,Long merchantId) {
        skuQtyUnitTools.convertRealToBasic(frontRecordDetails,merchantId);
    }

    /**
     * 设置商品code或id
     * @param frontRecordDetails
     */
    public void convertSkuCode(List<? extends AbstractFrontRecordDetail> frontRecordDetails,Long merchantId ){
        skuInfoTools.convertSkuCode(frontRecordDetails,merchantId);
    }


    /**
     * 设置商品code或id
     * @param frontRecordDetails
     */
    public void convertSkuCode(List<? extends AbstractFrontRecordDetail> frontRecordDetails){
        skuInfoTools.convertSkuCode(frontRecordDetails);
    }


    /**
     * 根据sku明细，只需要得到换算比例，用于将实际收货数量的基本单位换算成采购单位
     * 无需计算
     * @param frontRecordDetails
     */
    public void setRealToBasicScale(List<? extends AbstractFrontRecordDetail> frontRecordDetails,Long merchantId) {
        skuQtyUnitTools.setRealToBasicScale(frontRecordDetails,merchantId);
    }


    /**
     * 根据sku明细，只需要得到换算比例，用于将实际收货数量的基本单位换算成采购单位
     * 无需计算
     * @param frontRecordDetails
     */
    public void setRealToBasicScale(List<? extends AbstractFrontRecordDetail> frontRecordDetails) {
        skuQtyUnitTools.setRealToBasicScale(frontRecordDetails);
    }

    /**
     * 单据编号
     */
    private String recordCode;
    /**
     * 单据类型
     */
    private Integer recordType;
    /**
     * 单据状态
     */
    private Integer recordStatus;
    /**
     * 外部单据编号
     */
    private String outRecordCode;
    /**
     * 外部单据时间
     */
    private Date outCreateTime;
    /**
     * 审核原因
     */
    private String recordStatusReason;

    /**
     * 入向仓库
     */
    private RealWarehouseE inWarehouse;

    /**
     * 出向仓库
     */
    private RealWarehouseE outWarehouse;

    /**
     * 供应商编码代码
     */
    private String supplierCode;
}
