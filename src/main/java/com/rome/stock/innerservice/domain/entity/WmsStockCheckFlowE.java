/**
 * Filename WmsStockCheckFlowE.java
 * Company 上海来伊份科技有限公司。
 * <AUTHOR>
 * @version 
 */
package com.rome.stock.innerservice.domain.entity;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Component;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.extern.slf4j.Slf4j;

/**
 * wms库存核对流水记录
 * <AUTHOR>
 * @since 2019年7月7日 下午8:29:32
 */
@Data
@Slf4j
@Component
@Scope("prototype")
@EqualsAndHashCode(callSuper = false)
public class WmsStockCheckFlowE extends BaseE {
    
    /**
     * 实仓仓库ID
     */
    private Long realWarehouseId;
    
    /**
     * wms 编码 1:大福  2:旺店通 3:SAP-WM 4:欧电云
     */
    private Integer wmsCode;
    
    /**
     * 商品sku编码
     */
    private Long skuId;

    /**
     * 商品sku编码集合
     */
    private List<Long> skuIds;

    /**
	 * 商品编码
	 */
	private String skuCode;
    
    /**
     * 真实库存-实仓
     */
    private BigDecimal realQty;
    
    /**
     * 锁定库存-实仓
     */
    private BigDecimal lockQty;
    
    /**
     * 质检库存
     */
    private BigDecimal qualityQty;
    
    /**
     * 不合格库存，注：一般是质检不合格库存
     */
    private BigDecimal unqualifiedQty;
    
    /**
     * wms真实库存
     */
    private BigDecimal wmsRealQty;
    
    /**
     * wms锁定库存
     */
    private BigDecimal wmsLockQty;
    
    /**
     * wms质检库存
     */
    private BigDecimal wmsQualityQty;
    
    /**
     * wms不合格库存，注：一般是质检不合格库存
     */
    private BigDecimal wmsUnqualifiedQty;
    
    /**
     * 类型
     */
    private Integer type;
    
    /**
     * 系统来源
     */
    private String sourceSystem;
    
    /**
     * 额外wms仓参与比较标记 0:不是 1:是有额外sap系统
     */
    private Byte extWms;
    
    /**
     * 单位
     */
    private String  unitCode;

    /**
     * 开始时间
     */
    private Date startTime;

    /**
     * 结束时间
     */
    private Date endTime;

    /**
     * 仓库id集合(条件查询用)
     */
    private List<Long> realWarehouseIds;
    
    /**
     * wms真实库存,额外wms仓参与比较
     */
    private BigDecimal extWmsRealQty;
    
    /**
     * wms质检库存,额外wms仓参与比较
     */
    private BigDecimal extWmsQualityQty;
    
    /**
     * wms不合格库存，注：一般是质检不合格库存,额外wms仓参与比较
     */
    private BigDecimal extWmsUnqualifiedQty;
    
    /**
     * 中台与wms差异，查询用
     * 0无
     * 1有
     */
    private Integer diffTypeWms;
    
    /**
     * 中台与额外sap系统差异，查询用
     * 0无
     * 1有
     */
    private Integer diffTypeExtWms;
    
}
