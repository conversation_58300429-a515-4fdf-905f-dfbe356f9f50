package com.rome.stock.innerservice.domain.entity.frontrecord;

import lombok.Data;
import lombok.EqualsAndHashCode;

import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Component;

/**
 * 前置单
 */
@Component
@Scope("prototype")
@Data
@EqualsAndHashCode(callSuper = false)
public class FrontRecordE extends AbstractFrontRecord{
    /**
     * 备注
     */
    private String remark;
    /**
     * 门店编号
     */
    private String shopCode;
    /**
     * 门店名称
     */
    private String shopName;

    private String originOrderCode;
}
