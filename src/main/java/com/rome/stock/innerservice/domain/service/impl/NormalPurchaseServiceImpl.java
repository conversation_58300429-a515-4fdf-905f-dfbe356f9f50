package com.rome.stock.innerservice.domain.service.impl;

import com.rome.stock.common.enums.warehouse.WarehouseRecordTypeVO;
import com.rome.stock.innerservice.api.dto.frontrecord.PurchaseOrderDTO;
import com.rome.stock.innerservice.constant.WarehouseRecordBusinessTypeVO;
import com.rome.stock.innerservice.constant.WarehouseRecordStatusVO;
import com.rome.stock.innerservice.constant.WmsSyncStatusVO;
import com.rome.stock.innerservice.domain.entity.frontrecord.PurchaseOrderE;
import com.rome.stock.innerservice.domain.entity.warehouserecord.PurchaseWarehouseRecordE;
import com.rome.stock.innerservice.domain.service.AbstractPurchase;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

/**
 *  针对 普通采购 紧急入库单 越库采购 折让入库单 更正入库单 手工单
 * <AUTHOR>
 * @Description 普通类型大仓采购定义
 * @date 2020/8/6 10:41
 */
@Service(value = "normalPurchase")
@Slf4j
public class NormalPurchaseServiceImpl extends AbstractPurchase {

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void createPurchaseIn(PurchaseOrderDTO purchaseOrderDTO,PurchaseWarehouseRecordE purchaseWarehouseRecordIn) {
        PurchaseOrderE purchaseOrderE = super.createPurchaseFrontRecordCode(purchaseOrderDTO);
        purchaseWarehouseRecordIn.createRecodeCode("PI");
        purchaseWarehouseRecordIn.setBusinessType(WarehouseRecordBusinessTypeVO.IN_WAREHOUSE_RECORD.getType());
        //需要wms拉取，设置状态为未同步
        purchaseWarehouseRecordIn.setSyncWmsStatus(WmsSyncStatusVO.UNSYNCHRONIZED.getStatus());
        purchaseWarehouseRecordIn.setRecordType(WarehouseRecordTypeVO.PURCHASE_IN_WAREHOUSE_RECORD.getType());
        //根据前置单生成入库单数据
        purchaseWarehouseRecordIn.createInRecordByFrontRecord(purchaseOrderE);
        purchaseWarehouseRecordIn.setRecordStatus(WarehouseRecordStatusVO.INIT.getStatus());
        purchaseWarehouseRecordIn.setAppId("1");
        super.createPurchaseIn(purchaseOrderDTO,purchaseWarehouseRecordIn);
    }
}
   