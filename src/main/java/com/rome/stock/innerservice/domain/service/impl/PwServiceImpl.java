package com.rome.stock.innerservice.domain.service.impl;

import cn.hutool.core.date.DateUtil;
import com.rome.arch.core.exception.RomeException;
import com.rome.stock.innerservice.api.dto.PwPrintDTO;
import com.rome.stock.innerservice.api.dto.frontrecord.PurchaseReceiptDTO;
import com.rome.stock.innerservice.common.ResCode;
import com.rome.stock.innerservice.domain.entity.RealWarehouseE;
import com.rome.stock.innerservice.domain.repository.RealWarehouseRepository;
import com.rome.stock.innerservice.domain.service.PurchaseOrderService;
import com.rome.stock.innerservice.domain.service.PwService;
import com.rome.stock.innerservice.infrastructure.dataobject.frontrecord.FrPurchaseOrderDO;
import com.rome.stock.innerservice.infrastructure.dataobject.frontrecord.FrPurchaseOrderDetailDO;
import com.rome.stock.innerservice.infrastructure.mapper.frontrecord.FrPurchaseOrderDetailMapper;
import com.rome.stock.innerservice.infrastructure.mapper.frontrecord.FrPurchaseOrderMapper;
import com.rome.stock.innerservice.remote.item.dto.SkuInfoExtDTO;
import com.rome.stock.innerservice.remote.item.dto.SkuUnitExtDTO;
import com.rome.stock.innerservice.remote.item.dto.UnitCodeExtParamDTO;
import com.rome.stock.innerservice.remote.item.facade.SkuFacade;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.text.DecimalFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 采购入库单
 *
 * <AUTHOR>
 * @date 2020/07/01
 */
@Slf4j
@Service
public class PwServiceImpl implements PwService {

    @Resource
    private SkuFacade skuFacade;
    @Resource
    private FrPurchaseOrderMapper frPurchaseOrderMapper;
    @Resource
    private FrPurchaseOrderDetailMapper frPurchaseOrderDetailMapper;
    @Resource
    private PurchaseOrderService purchaseOrderService;
    @Resource
    private RealWarehouseRepository realWarehouseRepository;


    @Override
    public List<PwPrintDTO> print(String purchaseEntryNo) {
        FrPurchaseOrderDO frPurchaseOrderDO=frPurchaseOrderMapper.queryByOutRecordCode(purchaseEntryNo);
        if(null == frPurchaseOrderDO){
            throw new RomeException(ResCode.STOCK_ERROR_1001, ResCode.STOCK_ERROR_1001_DESC+ "SAP采购单号不存在");
        }
        List<FrPurchaseOrderDetailDO> pwDetailDOList = frPurchaseOrderDetailMapper.queryByRecordId(frPurchaseOrderDO.getId());
        if (CollectionUtils.isEmpty(pwDetailDOList)) {
            throw new RomeException(ResCode.STOCK_ERROR_1001, "采购单明细为空");
        }
        List<PurchaseReceiptDTO> purchaseReceiptDTOList = purchaseOrderService.queryRwBatchInfoByPWCode(purchaseEntryNo);
        if (CollectionUtils.isEmpty(purchaseReceiptDTOList)) {
            throw new RomeException(ResCode.STOCK_ERROR_1001, "批次信息为空");
        }
        List<UnitCodeExtParamDTO> unitCodeExtParamDTOList = new ArrayList<>();
        // 查询单位转换比
        pwDetailDOList.forEach(pwDetailDO -> unitCodeExtParamDTOList.add(new UnitCodeExtParamDTO(pwDetailDO.getSkuCode(), pwDetailDO.getUnitCode())));
        List<SkuUnitExtDTO> skuUnitExtDTOList = skuFacade.unitsBySkuCodeAndUnitCodeAndMerchantId(unitCodeExtParamDTOList);
        Map<String, SkuUnitExtDTO> skuUnitExtDTOMap=skuUnitExtDTOList.stream().collect(Collectors.toMap(SkuUnitExtDTO::getSkuCode, Function.identity(), (v1, v2) -> v1));

        List<String> skuCodes=pwDetailDOList.stream().map(FrPurchaseOrderDetailDO::getSkuCode).collect(Collectors.toList());
        List<SkuInfoExtDTO> skuInfoExtDTOList= skuFacade.skusBySkuCode(skuCodes);

        Map<String, SkuInfoExtDTO> skuInfoExtDTOMap=skuInfoExtDTOList.stream().collect(Collectors.toMap(SkuInfoExtDTO::getSkuCode, Function.identity(), (v1, v2) -> v1));

        List<PwPrintDTO> pwPrintDTOList = new ArrayList<>();
        // 总金额
        BigDecimal totalAmount = BigDecimal.ZERO;
        // 总数量
        BigDecimal totalQty = BigDecimal.ZERO;
        // 总计件数量
        BigDecimal totalPieceQty = BigDecimal.ZERO;
        RealWarehouseE realWarehouseE=realWarehouseRepository.getRealWarehouseById(frPurchaseOrderDO.getRealWarehouseId());

        PwPrintDTO temp = new PwPrintDTO();
        temp.setSupplierCode(frPurchaseOrderDO.getSupplierCode() + " " + frPurchaseOrderDO.getSupplierName());
        temp.setWarehouseCode(realWarehouseE.getRealWarehouseCode() + " " + realWarehouseE.getRealWarehouseName());
        temp.setCreateTime(DateUtil.format(frPurchaseOrderDO.getOutCreateTime(), "yyyyMMdd"));
        temp.setPrintDate(DateUtil.format(new Date(), "yyyyMMdd"));
        temp.setPurchaseOrderNo(frPurchaseOrderDO.getOutRecordCode());
        int number = 1;
        for (PurchaseReceiptDTO pwDetailDO : purchaseReceiptDTOList) {
            PwPrintDTO pwPrintDTO = new PwPrintDTO();
            BeanUtils.copyProperties(temp,pwPrintDTO);
            if(null !=pwDetailDO.getReceiveDate()){
                pwPrintDTO.setWarehousingDate(DateUtil.format(com.rome.stock.common.utils.DateUtil.parse(pwDetailDO.getReceiveDate(), "yyyyMMdd"), "yyyyMMdd"));
            }
            pwPrintDTO.setWmsRecordCode(pwDetailDO.getWmsRecordCode());
            pwPrintDTO.setProductDate(pwDetailDO.getProductDate());
            pwPrintDTO.setFactoryName(pwDetailDO.getFactoryName());
            pwPrintDTO.setSkuCode(pwDetailDO.getSkuCode());
            pwPrintDTO.setQuantity(pwDetailDO.getActualQty());
            pwPrintDTO.setPurchaseEntryNo(pwDetailDO.getWarehouseRecordCode());
            if (skuUnitExtDTOMap.containsKey(pwDetailDO.getSkuCode())) {
                SkuUnitExtDTO skuUnitExtDTO=skuUnitExtDTOMap.get(pwDetailDO.getSkuCode());
                pwPrintDTO.setReceivedQty(pwDetailDO.getActualQty().divide(skuUnitExtDTO.getScale(),3,BigDecimal.ROUND_HALF_UP));
                pwPrintDTO.setUnit(skuUnitExtDTO.getBasicUnitName());
                BigDecimal bigDecimal = skuUnitExtDTO.getScale().setScale(2, RoundingMode.HALF_DOWN);
                pwPrintDTO.setSpecification("1X" + new DecimalFormat("#.###").format(bigDecimal) + skuUnitExtDTO.getBasicUnitName());
            }
            if(skuInfoExtDTOMap.containsKey(pwDetailDO.getSkuCode())){
                SkuInfoExtDTO skuInfoExtDTO=skuInfoExtDTOMap.get(pwDetailDO.getSkuCode());
                pwPrintDTO.setSkuName(skuInfoExtDTO.getName());
            }
            totalQty = totalQty.add(pwPrintDTO.getQuantity());
            pwPrintDTO.setOrderNumber(number);
            totalPieceQty = totalPieceQty.add(pwPrintDTO.getReceivedQty());
            pwPrintDTOList.add(pwPrintDTO);
            number++;
        }
        for (PwPrintDTO tempPrint :  pwPrintDTOList) {
            tempPrint.setTotalQty(totalQty);
            tempPrint.setTotalAmount(totalAmount);
            tempPrint.setTotalPieceQty(totalPieceQty);
        }
        return pwPrintDTOList;
    }


}
