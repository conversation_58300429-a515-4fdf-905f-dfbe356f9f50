package com.rome.stock.innerservice.domain.entity.frontrecord;

import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Component;

/**
 * 库存对比调整详情
 */
@Component
@Scope("prototype")
@Data
@EqualsAndHashCode(callSuper = false)
public class AdjustCheckStockRecordDetailE extends AbstractFrontRecordDetail{
    /**
     * 库存对比id
     */
    private Long checkFlowId;
}
