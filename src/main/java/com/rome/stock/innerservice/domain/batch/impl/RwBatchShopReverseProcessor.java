package com.rome.stock.innerservice.domain.batch.impl;

import com.google.common.collect.Lists;
import com.rome.stock.common.constants.ResCode;
import com.rome.stock.common.enums.warehouse.WarehouseRecordTypeVO;
import com.rome.stock.common.utils.AlikAssert;
import com.rome.stock.innerservice.domain.batch.RwBatchProcessRecordType;
import com.rome.stock.innerservice.domain.batch.RwBatchStockRelationProcessor;
import com.rome.stock.innerservice.domain.batch.dto.RwRelationQueryDTO;
import com.rome.stock.innerservice.remote.orderCneter.dto.CommonFrontRecordDTO;
import com.rome.stock.innerservice.remote.orderCneter.facade.OrderCenterFacade;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Service
@RwBatchProcessRecordType(recordVOTypes={
        WarehouseRecordTypeVO.SHOP_RECEIVE_REVERSE//门店领用冲销
})
public class RwBatchShopReverseProcessor implements RwBatchStockRelationProcessor {

    @Resource
    private OrderCenterFacade orderCenterFacade;

    @Override
    public Map<String, List<String>> getRelationRecordList(RwRelationQueryDTO rwRelationQueryDTO) {
        List<String> recordCodeList = rwRelationQueryDTO.getRecordCodeList();
        Map<String, List<String>> result = new HashMap<>();
//        recordCodeList.forEach(recordCode->{
//            List<CommonFrontRecordDTO> frontRecordList = orderCenterFacade.queryCommonFrontRecordInfoByRecordCode(recordCode);
//            AlikAssert.notEmpty(frontRecordList, "999", "未查询到订单中心前置单");
//            CommonFrontRecordDTO commonFrontRecordDTO = frontRecordList.get(0);
//            AlikAssert.isNotEmpty(commonFrontRecordDTO.getDependRecordCode(), ResCode.STOCK_ERROR_1001, "没有关联的出库单");
//            if (result.containsKey(recordCode)){
//                result.get(recordCode).add(commonFrontRecordDTO.getDependRecordCode());
//            }else {
//                result.put(recordCode,Lists.newArrayList(commonFrontRecordDTO.getDependRecordCode()));
//            }
//        });
//        rwRelationQueryDTO.setNeedQueryFlow(1);
        return result;
    }
}
