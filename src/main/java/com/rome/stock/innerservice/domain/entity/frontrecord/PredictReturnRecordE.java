package com.rome.stock.innerservice.domain.entity.frontrecord;

import com.rome.arch.core.exception.RomeException;
import com.rome.stock.innerservice.common.AlikAssert;
import com.rome.stock.innerservice.common.ResCode;
import com.rome.stock.innerservice.common.SkuInfoTools;
import com.rome.stock.innerservice.common.SkuQtyUnitTools;
import com.rome.stock.innerservice.constant.FrontRecordTypeVO;
import com.rome.stock.innerservice.domain.repository.frontrecord.FrPredictReturnRepository;
import com.rome.stock.innerservice.remote.item.dto.SkuUnitExtDTO;
import com.rome.stock.innerservice.remote.item.facade.SkuFacade;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

@Component
@Scope("prototype")
@Data
@EqualsAndHashCode(callSuper = false)
public class PredictReturnRecordE extends AbstractFrontRecord {

    @Autowired
    private FrPredictReturnRepository frPredictReturnRepository;
    @Autowired
    private SkuQtyUnitTools skuQtyUnitTools;
    @Autowired
    private SkuInfoTools skuInfoTools;
    @Autowired
    private SkuFacade skuFacade;

    /**
     * 创建退货预入库单
     */
    public void addFrontRecord() {
        //生成单据编号
        initReturnFrontRecord(FrontRecordTypeVO.PREDICT_RETURN_RECORD.getCode(), this.frontRecordDetails);
        //插入退货预入库单
        Long id = frPredictReturnRepository.savePredictReturnRecord(this);
        AlikAssert.isTrue(id > 0, "999", "创建退货预入库单失败");
        this.setId(id);
        this.frontRecordDetails.forEach(detail -> detail.setFrontRecordDetail(this));
        //补齐skuId
//        skuInfoTools.convertSkuCode(this.frontRecordDetails);
        //没有单位会报错
//        skuQtyUnitTools.queryBasicUnitWithNoChange(this.frontRecordDetails);
        //查询sku基本单位
        List<String> skuCodeList=this.frontRecordDetails.stream().map(PredictReturnRecordDetailE::getSkuCode).distinct().collect(Collectors.toList());
        List<SkuUnitExtDTO> skuUnitExtDTOList = skuFacade.querySkuUnitsBySkuCodeAndType(skuCodeList, 5L);
        Map<String,SkuUnitExtDTO> skuUnitExtDTOMap=skuUnitExtDTOList.stream().collect(Collectors.toMap(SkuUnitExtDTO::getSkuCode, Function.identity(), (v1, v2) -> v1));
        for(PredictReturnRecordDetailE detail:this.frontRecordDetails){
            SkuUnitExtDTO skuUnitExtDTO=skuUnitExtDTOMap.get(detail.getSkuCode());
            if(null == skuUnitExtDTO){
                throw new RomeException(ResCode.STOCK_ERROR_1002, "skuCode:"+detail.getSkuCode()+"基础单位不存在");
            }
            detail.setUnitCode(skuUnitExtDTO.getUnitCode());
            detail.setUnit(skuUnitExtDTO.getUnitName());
        }
        frPredictReturnRepository.savePredictReturnRecordDetail(this.frontRecordDetails);
    }

    /**
     * 实体仓库id
     */
    private Long realWarehouseId;

    /**
     * 入库单号
     */
    private String entryOrderCode;

    /**
     * 运单号
     */
    private String expressCode;

    /**
     * 物流公司编码
     */
    private String logisticsCode;

    /**
     * 物流公司名称
     */
    private String logisticsName;

    /**
     * 备注
     */
    private String remark;

    /**
     * 单据状态 0-初始 19-部分匹配 20-完全匹配
     */
    private Integer recordStatus;
    /**
     * 创建人
     */
    private Long creator;
    /**
     * 更新人
     */
    private Long modifier;

    /**
     * 退货预入库单据明细
     */
    private List<PredictReturnRecordDetailE> frontRecordDetails;

    /**
     * 定时任务处理标识：0-无需处理(默认)，1-待处理，2-已处理
     */
    private Integer needJobHandle;

}
