package com.rome.stock.innerservice.domain.service.impl;

import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.rome.stock.innerservice.api.dto.AdjustSkuStockDTO;
import com.rome.stock.innerservice.api.dto.SkuDetailDTO;
import com.rome.stock.innerservice.api.dto.UnStandardDTO;
import com.rome.stock.innerservice.domain.convertor.UnStandardConvertor;
import com.rome.stock.innerservice.domain.entity.RealWarehouseStockE;
import com.rome.stock.innerservice.domain.service.UnstandardService;
import com.rome.stock.innerservice.domain.service.XTStockService;
import com.rome.stock.innerservice.infrastructure.dataobject.RealWarehouseDO;
import com.rome.stock.innerservice.infrastructure.dataobject.RealWarehouseStockDO;
import com.rome.stock.innerservice.infrastructure.dataobject.UnStandardDO;
import com.rome.stock.innerservice.infrastructure.mapper.RealWarehouseMapper;
import com.rome.stock.innerservice.infrastructure.mapper.RealWarehouseStockMapper;
import com.rome.stock.innerservice.infrastructure.mapper.UnStandardMapper;
import com.rome.stock.innerservice.remote.item.dto.SkuInfoExtDTO;
import com.rome.stock.innerservice.remote.item.facade.SkuFacade;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.Arrays;
import java.util.Date;
import java.util.List;

@Slf4j
@Service
public class UnstandardServiceImpl implements UnstandardService {

    @Autowired
    private UnStandardMapper unStandardMapper;
    @Resource
    private UnStandardConvertor unStandardConvertor;
    @Autowired
    private SkuFacade skuFacade;
    @Autowired
    private RealWarehouseStockMapper realWarehouseStockMapper;
    @Autowired
    private RealWarehouseMapper realWarehouseMapper;
    @Autowired
    private XTStockService xTStockService;

    @Override
    public PageInfo<UnStandardDTO> queryUnStandardByCondition(UnStandardDTO unstandardDTO) {
        Page page = PageHelper.startPage(unstandardDTO.getPageIndex(), unstandardDTO.getPageSize());
        List<UnStandardDTO> list=unStandardConvertor.unStandardDoToDTo(unStandardMapper.queryByCondition(unstandardDTO.getSkuIds()));
        for(UnStandardDTO unStandardDTO: list){
            if(unStandardDTO.getSkuId()==null){
                continue;
            }
            SkuInfoExtDTO skuInfoExtDTO= skuFacade.getSkuInfoBySkuId(unStandardDTO.getSkuId());
            if(skuInfoExtDTO!=null){
                unStandardDTO.setBasicUnit(skuInfoExtDTO.getSpuUnitName());
                unStandardDTO.setSkuName(skuInfoExtDTO.getName());
            }
        }
        PageInfo<UnStandardDTO> pageList = new PageInfo<>(list);
        pageList.setTotal(page.getTotal());
        return pageList;
    }

    @Override
    public void saveUnStandard(List<UnStandardDTO> unStandardDTOList) {
        for (UnStandardDTO unStandardDTO : unStandardDTOList){
            UnStandardDO unstandardDO=unStandardConvertor.unStandardDtoToDo(unStandardDTO);
            Long k=unStandardMapper.countUnStandardByWhere(unstandardDO);
            if(k!=null && k>0){
//                throw new RomeException(ResCode.STOCK_ERROR_9019, ResCode.STOCK_ERROR_9019_DESC);
                unstandardDO.setId(k);
                unStandardMapper.updateUnStandardByWhere(unstandardDO);
            }else{
                unStandardMapper.saveUnStandard(unstandardDO);
            }
        }
    }

    @Override
    public void updateUnStandardByWhere(UnStandardDTO unStandardDTO) {
        UnStandardDO unstandardDO = unStandardConvertor.unStandardDtoToDo(unStandardDTO);
        unStandardMapper.updateUnStandardByWhere(unstandardDO);
    }

    @Value("${sku.merchantId}")
    private Long defaultMerchantId;
    @Override
    public void unStandardStockAdjust() {
        List<Long> list=unStandardMapper.queryUnStandardList();
        List<RealWarehouseStockDO> realWarehouseStockDOList=realWarehouseStockMapper.queryWarehouseStockByUnStandardList(list);
        for(RealWarehouseStockDO realWarehouseStockDO:realWarehouseStockDOList){
            Long skuId=realWarehouseStockDO.getSkuId();
            RealWarehouseDO realWarehouseDO=realWarehouseMapper.queryById(realWarehouseStockDO.getRealWarehouseId());
            if(realWarehouseDO ==null || realWarehouseDO.getRealWarehouseType()!=1){
                continue;
            }
            AdjustSkuStockDTO adjustSkuStockDTO=new AdjustSkuStockDTO();
            adjustSkuStockDTO.setShopCode(realWarehouseDO.getShopCode());
            adjustSkuStockDTO.setWarehouseCode(realWarehouseDO.getRealWarehouseOutCode());
            adjustSkuStockDTO.setFactoryCode(realWarehouseDO.getFactoryCode());
            adjustSkuStockDTO.setOutCreateTime(new Date());
            adjustSkuStockDTO.setRemark("非标品库存调整");
            adjustSkuStockDTO.setMerchantId(defaultMerchantId);
            adjustSkuStockDTO.setUnStandardFlag("1");

            SkuDetailDTO skuDetailDTO=new SkuDetailDTO();
            skuDetailDTO.setSkuId(skuId);
            //通过单个skuId获取sku信息
            SkuInfoExtDTO skuInfoExtDTO=skuFacade.getSkuInfoBySkuId(skuId);
            if(skuInfoExtDTO!=null){
                skuDetailDTO.setUnitCode(skuInfoExtDTO.getSpuUnitCode());
            }
            UnStandardDO unStandardDO=unStandardMapper.selectOneBySkuId(skuId);
            BigDecimal standardQty=unStandardDO.getStandardQty();
            BigDecimal skuQty=standardQty.subtract(realWarehouseStockDO.getRealQty()).setScale(3,BigDecimal.ROUND_HALF_UP);
            if(skuQty.compareTo(BigDecimal.ZERO)==0){
                continue;
            }
            skuDetailDTO.setSkuQty(skuQty);
            adjustSkuStockDTO.setDetails(Arrays.asList(skuDetailDTO));
            //调整仓库库存
            xTStockService.adjustSkuStock(adjustSkuStockDTO);
        }
    }
}
