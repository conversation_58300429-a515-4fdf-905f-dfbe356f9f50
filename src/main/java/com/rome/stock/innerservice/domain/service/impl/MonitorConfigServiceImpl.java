package com.rome.stock.innerservice.domain.service.impl;

import com.alibaba.fastjson.JSON;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.rome.arch.core.exception.RomeException;
import com.rome.stock.innerservice.api.dto.MonitorConfigDTO;
import com.rome.stock.innerservice.common.AlikAssert;
import com.rome.stock.innerservice.common.BaseInfoConfigTool;
import com.rome.stock.innerservice.common.ResCode;
import com.rome.stock.innerservice.domain.entity.BaseinfoConfigE;
import com.rome.stock.innerservice.domain.repository.BaseinfoConfigRepository;
import com.rome.stock.innerservice.domain.service.MonitorConfigService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * MonitorConfigService 实现
 * 
 * <AUTHOR>
@Slf4j
@Service
public class MonitorConfigServiceImpl implements MonitorConfigService {

    @Resource
    private BaseinfoConfigRepository baseinfoConfigRepository;


    @Override
    public PageInfo<MonitorConfigDTO> queryPage(MonitorConfigDTO monitorConfigDTO) {
        List<BaseinfoConfigE> pointUrlConfigEs=Lists.newArrayList();
        Map<String, BaseinfoConfigE> poolMap= Maps.newHashMap();
        long total=0;
        String code=StringUtils.isEmpty(monitorConfigDTO.getDomain())?"monitor":String.format("%s.monitor",monitorConfigDTO.getDomain());
        List<String> poolNames=BaseInfoConfigTool.getByCodeAndParamNameNoCache(code,"names");
        if (CollectionUtils.isNotEmpty(poolNames)){
            List<String> paramPoolNames=Lists.newArrayList();
            for (String poolName:poolNames){
                paramPoolNames.add(poolName+".pool");
            }
            List<BaseinfoConfigE> poolConfigEs=baseinfoConfigRepository
                    .getListByCodeAndParams(code,paramPoolNames, monitorConfigDTO.getCallMode(), monitorConfigDTO.getPoolName());
            if (CollectionUtils.isNotEmpty(poolConfigEs)){
                List<String> paramPointUrlNames=Lists.newArrayList();
                for (BaseinfoConfigE configE:poolConfigEs){
                    MonitorConfigDTO dto=MonitorConfigDTO.parseJson(configE.getParamValue());
                    if (!poolMap.containsKey(configE.getParamName())){
                        poolMap.put(configE.getParamName(),configE);
                    }
                    if (StringUtils.isNotEmpty(dto.getUrlKeys())){
                        String[] urls=dto.getUrlKeys().split(",");
                        for(String url:urls){
                            String pointName=configE.getParamName().replace("pool","")+url;
                            paramPointUrlNames.add(pointName);
                        }
                    }
                }
                if (StringUtils.isNotEmpty(monitorConfigDTO.getParamName())){
                    paramPointUrlNames=paramPointUrlNames.stream().filter(it->it.contains(monitorConfigDTO.getParamName()))
                            .collect(Collectors.toList());
                }
                if (CollectionUtils.isNotEmpty(paramPointUrlNames)){
                    //分页查询列表数据
                    Page page = PageHelper.startPage(monitorConfigDTO.getPageIndex(), monitorConfigDTO.getPageSize());
                    pointUrlConfigEs=baseinfoConfigRepository.getBaseInfoConfigByCodeAndParams(code,paramPointUrlNames
                            ,monitorConfigDTO.getUrl());
                    total=page.getTotal();
                }
            }
        }

        if (CollectionUtils.isEmpty(pointUrlConfigEs)) {
            PageInfo<MonitorConfigDTO> pageInfo = new PageInfo<>(Lists.newArrayList());
            pageInfo.setTotal(0);
            return pageInfo;
        }
        List<MonitorConfigDTO> list=Lists.newArrayList();
        for (BaseinfoConfigE pointUrlConfig:pointUrlConfigEs){
            MonitorConfigDTO dto=MonitorConfigDTO.parseJson(pointUrlConfig.getParamValue());
            dto.setId(pointUrlConfig.getId());
            dto.setDomain(pointUrlConfig.getCode().replace(".monitor","").replace("monitor",""));
            dto.setParamName(pointUrlConfig.getParamName());
            dto.setCreateTime(pointUrlConfig.getCreateTime());
            dto.setCreator(pointUrlConfig.getCreator());
            dto.setModifier(pointUrlConfig.getModifier());
            dto.setUpdateTime(pointUrlConfig.getUpdateTime());
            // url飞书地址
            dto.setNodeFeishuUrl(dto.getFeishuUrl());
            String poolName=pointUrlConfig.getParamName().contains(".")?
                    pointUrlConfig.getParamName().substring(0,pointUrlConfig.getParamName().lastIndexOf("."))+".pool":pointUrlConfig.getParamName();
            if (poolMap.containsKey(poolName)){
                BaseinfoConfigE poolConfig=poolMap.get(poolName);
                MonitorConfigDTO poolMonitor=MonitorConfigDTO.parseJson(poolConfig.getParamValue());
                dto.setPoolId(poolConfig.getId());
                dto.setPoolAlias(poolConfig.getParamName().replace(".pool",""));
                dto.setPoolName(poolMonitor.getPoolName());
                dto.setCallMode(poolMonitor.getCallMode());
                dto.setFeishuUrl(poolMonitor.getFeishuUrl());
                dto.setNoticeTimeGap(poolMonitor.getNoticeTimeGap());
                dto.setTeam(poolMonitor.getTeam());
            }
            list.add(dto);
        }
        PageInfo<MonitorConfigDTO> pageInfo = new PageInfo<>(list);
        pageInfo.setTotal(total);
        return pageInfo;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void saveOrUpdate(MonitorConfigDTO monitorDTO) {
        if (Objects.nonNull(monitorDTO.getId()) &&
                !Objects.equals(monitorDTO.getId(),0)){
            BaseinfoConfigE pointConfigE=baseinfoConfigRepository.selectBaseinfoConfigById(monitorDTO.getId());
            AlikAssert.notNull(pointConfigE,ResCode.STOCK_ERROR_1003,"当前监控端点信息不存在");
            AlikAssert.isTrue(Objects.nonNull(monitorDTO.getPoolId())
                    && !Objects.equals(monitorDTO.getPoolId(),0),ResCode.STOCK_ERROR_1003,"修改操作不可新增Pool");
            BaseinfoConfigE poolConfigE=baseinfoConfigRepository.selectBaseinfoConfigById(monitorDTO.getPoolId());
            AlikAssert.notNull(poolConfigE,ResCode.STOCK_ERROR_1003,"当前监控端点信息不存在");
            if (StringUtils.isEmpty(monitorDTO.getItems())){
                throw new RomeException(ResCode.STOCK_ERROR_1003,"监控端点配置参数不可为空");
            }
            if (StringUtils.isEmpty(monitorDTO.getUrl())){
                throw new RomeException(ResCode.STOCK_ERROR_1003,"监控端点Url不可为空");
            }
            Map<String,Object> pointParamValueMap=Maps.newHashMap();
            pointParamValueMap.put("url", monitorDTO.getUrl());
            pointParamValueMap.put("items", monitorDTO.saveItemJsonArray());
            if (StringUtils.isNotBlank(monitorDTO.getCallPoolNameList())){
                pointParamValueMap.put("callPoolNameList", JSON.parseArray(monitorDTO.getCallPoolNameList()));
            }
            if (StringUtils.isNotBlank(monitorDTO.getDesc())){
                pointParamValueMap.put("desc", monitorDTO.getDesc());
            }
            if (StringUtils.isNotBlank(monitorDTO.getAt())){
                pointParamValueMap.put("at", monitorDTO.getAt());
            }
            if (StringUtils.isNotBlank(monitorDTO.getNodeFeishuUrl())){
                pointParamValueMap.put("feishuUrl", monitorDTO.getNodeFeishuUrl());
            }
            pointConfigE.setParamValue(JSON.toJSONString(pointParamValueMap));
            pointConfigE.setModifier(monitorDTO.getUserId());
            baseinfoConfigRepository.modifyBaseinfoConfig(pointConfigE);
            MonitorConfigDTO poolDto=MonitorConfigDTO.parseJson(poolConfigE.getParamValue());
            Map<String,Object> poolMap=modifyPoolParamValue(poolDto.getCallMode(),poolDto.getPoolName(),
                    poolDto.getUrlKeys(),monitorDTO);
            poolConfigE.setParamValue(JSON.toJSONString(poolMap));
            baseinfoConfigRepository.modifyBaseinfoConfig(poolConfigE);
        }else {
            BaseinfoConfigE pointE=new BaseinfoConfigE();
            String code=StringUtils.isEmpty(monitorDTO.getDomain())?"monitor":String.format("%s.monitor",monitorDTO.getDomain());
            pointE.setCode(code);
            Map<String,Object> pointParamValueMap=Maps.newHashMap();
            pointParamValueMap.put("url", monitorDTO.getUrl());
            pointParamValueMap.put("items", monitorDTO.saveItemJsonArray());
            if (StringUtils.isNotEmpty(monitorDTO.getCallPoolNameList())){
                pointParamValueMap.put("callPoolNameList", JSON.parseArray(monitorDTO.getCallPoolNameList()));
            }
            if (StringUtils.isNotEmpty(monitorDTO.getDesc())){
                pointParamValueMap.put("desc", monitorDTO.getDesc());
            }
            if (StringUtils.isNotEmpty(monitorDTO.getAt())){
                pointParamValueMap.put("at", monitorDTO.getAt());
            }
            if (StringUtils.isNotBlank(monitorDTO.getNodeFeishuUrl())){
                pointParamValueMap.put("feishuUrl", monitorDTO.getNodeFeishuUrl());
            }
            pointE.setParamValue(JSON.toJSONString(pointParamValueMap));
            pointE.setCreator(monitorDTO.getUserId());
            pointE.setCreateTime(new Date());
            BaseinfoConfigE poolConfigE=new BaseinfoConfigE();
            String poolAlias=generatePoolName(monitorDTO.getCallMode(),monitorDTO.getPoolName());
            if (Objects.nonNull(monitorDTO.getPoolId()) && !Objects.equals(monitorDTO.getPoolId(),0)){
                poolConfigE=baseinfoConfigRepository.selectBaseinfoConfigById(monitorDTO.getPoolId());
                AlikAssert.notNull(poolConfigE,ResCode.STOCK_ERROR_1003,"当前监控端点信息不存在");
                poolAlias=poolConfigE.getParamName().replace(".pool","");
                MonitorConfigDTO poolDto=MonitorConfigDTO.parseJson(poolConfigE.getParamValue());
                String urlKeys=StringUtils.isEmpty(poolDto.getUrlKeys())?poolDto.getUrlKeys():poolDto.getUrlKeys()+","+monitorDTO.getParamName();
                Map<String,Object> poolMap=modifyPoolParamValue(poolDto.getCallMode(),poolDto.getPoolName(),urlKeys,poolDto);
                poolConfigE.setParamValue(JSON.toJSONString(poolMap));
                poolConfigE.setModifier(monitorDTO.getUserId());
                baseinfoConfigRepository.modifyBaseinfoConfig(poolConfigE);
            }else {
                poolConfigE.setCode(code);
                poolConfigE.setParamName(poolAlias+".pool");
                List<BaseinfoConfigE> points=baseinfoConfigRepository.getBaseInfoConfigByCodeAndParams(poolConfigE.getCode(),Lists.newArrayList(poolConfigE.getParamName()));
                if (CollectionUtils.isNotEmpty(points)){
                    throw new RomeException(ResCode.STOCK_ERROR_1003,"该Pool【"+poolAlias+"】已存在");
                }
                Map<String,Object> poolMap=modifyPoolParamValue(monitorDTO.getCallMode(),monitorDTO.getPoolName(),
                        monitorDTO.getParamName(),monitorDTO);
                poolConfigE.setParamValue(JSON.toJSONString(poolMap));
                poolConfigE.setModifier(monitorDTO.getUserId());
                poolConfigE.setExt("");
                baseinfoConfigRepository.saveBaseinfoConfig(poolConfigE);
                //修改monitor,names对应的paramValue值
                List<BaseinfoConfigE> namesData=baseinfoConfigRepository.getBaseInfoConfigByCodeAndParams(code,Lists.newArrayList("names"));
                if (CollectionUtils.isNotEmpty(namesData)) {
                    BaseinfoConfigE nameConfig=namesData.get(0);
                    String names=nameConfig.getParamValue()+","+poolAlias;
                    nameConfig.setParamValue(names);
                    baseinfoConfigRepository.modifyBaseinfoConfig(nameConfig);
                }else {
                    BaseinfoConfigE nameConfig=new BaseinfoConfigE();
                    nameConfig.setCode(code);
                    nameConfig.setParamName("names");
                    nameConfig.setParamValue(poolAlias);
                    nameConfig.setExt("");
                    nameConfig.setParamDesc("监控配置信息-pool结点，多个以英文逗号隔开");
                    baseinfoConfigRepository.saveBaseinfoConfig(nameConfig);
                }
            }
            pointE.setParamName(poolAlias+"."+monitorDTO.getParamName());
            pointE.setExt("");
            List<BaseinfoConfigE> points=baseinfoConfigRepository.getBaseInfoConfigByCodeAndParams(code,Lists.newArrayList(pointE.getParamName()));
            if (CollectionUtils.isNotEmpty(points)){
                throw new RomeException(ResCode.STOCK_ERROR_1003,"该Pool【"+poolAlias+"】已存在监控端点【"+pointE.getParamName()+"】");
            }
            baseinfoConfigRepository.saveBaseinfoConfig(pointE);
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void delete(MonitorConfigDTO monitorConfigDTO) {
        BaseinfoConfigE pointConfig=baseinfoConfigRepository.selectBaseinfoConfigById(monitorConfigDTO.getId());
        AlikAssert.notNull(pointConfig,ResCode.STOCK_ERROR_1003,"监控端点配置不存在");
        BaseinfoConfigE poolConfig=baseinfoConfigRepository.selectBaseinfoConfigById(monitorConfigDTO.getPoolId());
        AlikAssert.notNull(poolConfig,ResCode.STOCK_ERROR_1003,"监控端点对应的Pool配置不存在");
        baseinfoConfigRepository.deleteBaseinfoConfigById(pointConfig.getId());
        MonitorConfigDTO poolDTO=MonitorConfigDTO.parseJson(poolConfig.getParamValue());
        List<String> pointNames=Lists.newArrayList();
        if (StringUtils.isNotEmpty(poolDTO.getUrlKeys())){
            for(String url:poolDTO.getUrlKeys().split(",")){
                String pointName=poolConfig.getParamName().replace("pool","")+url;
                if (!Objects.equals(pointConfig.getParamName(),pointName)){
                    pointNames.add(url);
                }
            }
        }
        if (CollectionUtils.isNotEmpty(pointNames)){
            Map<String,Object> poolMap=modifyPoolParamValue(poolDTO.getCallMode(),poolDTO.getPoolName(),
                    String.join(",",pointNames),poolDTO);
            poolConfig.setParamValue(JSON.toJSONString(poolMap));
            poolConfig.setModifier(monitorConfigDTO.getUserId());
            baseinfoConfigRepository.modifyBaseinfoConfig(poolConfig);
        }else {
            baseinfoConfigRepository.deleteBaseinfoConfigById(poolConfig.getId());
            String poolAlias=poolConfig.getParamName().replace(".pool","");
            List<BaseinfoConfigE> namesData=baseinfoConfigRepository.getBaseInfoConfigByCodeAndParams(pointConfig.getCode(),Lists.newArrayList("names"));
            if (CollectionUtils.isNotEmpty(namesData)) {
                BaseinfoConfigE nameConfig=namesData.get(0);
                List<String> newNames=Lists.newArrayList(nameConfig.getParamValue().split(","));
                newNames.removeIf(name->Objects.equals(poolAlias,name));
                nameConfig.setParamValue(String.join(",",newNames));
                baseinfoConfigRepository.modifyBaseinfoConfig(nameConfig);
            }
        }
    }

    @Override
    public Map<String, String> listPools() {
        Map<String, String> poolMap=Maps.newHashMap();
        List<String> list=BaseInfoConfigTool.getByCodeAndParamNameNoCache("monitor","pools");
        for (String pool:list){
            poolMap.put(pool,pool);
        }
        return poolMap;
    }


    @Override
    public MonitorConfigDTO getPoolByCallModeAndPool(String domain,String callMode, String poolName) {
        MonitorConfigDTO dto=new MonitorConfigDTO();
        String code=StringUtils.isEmpty(domain)?"monitor":String.format("%s.monitor",domain);
        List<String> poolNames=BaseInfoConfigTool.getByCodeAndParamNameNoCache(code,"names");
        if (CollectionUtils.isNotEmpty(poolNames)){
            List<String> paramPoolNames=Lists.newArrayList();
            for (String name:poolNames){
                paramPoolNames.add(name+".pool");
            }
            BaseinfoConfigE poolConfigE=baseinfoConfigRepository
                    .getByCodeAndParams(code,paramPoolNames,callMode,poolName);
            if (Objects.nonNull(poolConfigE)){
                dto=MonitorConfigDTO.parseJson(poolConfigE.getParamValue());
                dto.setPoolId(poolConfigE.getId());
                dto.setPoolAlias(poolConfigE.getParamName().replace(".pool",""));
            }
        }
        return dto;
    }

    private Map<String,Object> modifyPoolParamValue(String callMode,String poolName,String urlKeys,
                                                   MonitorConfigDTO monitorDTO){
        Map<String,Object> poolParamValueMap=Maps.newHashMap();
        poolParamValueMap.put("callMode",callMode);
        poolParamValueMap.put("poolName", poolName);
        poolParamValueMap.put("urlKeys", urlKeys);
        poolParamValueMap.put("team", monitorDTO.getTeam());
        poolParamValueMap.put("feishuUrl", monitorDTO.getFeishuUrl());
        if (Objects.nonNull(monitorDTO.getNoticeTimeGap())){
            poolParamValueMap.put("noticeTimeGap", monitorDTO.getNoticeTimeGap());
        }
        return poolParamValueMap;
    }

    private String generatePoolName(String callMode,String poolName){
        String poolN="";
        for (String pool:poolName.split("-")){
            poolN=poolN+StringUtils.capitalize(pool);
        }
        return callMode+poolN;
    }
}
