package com.rome.stock.innerservice.domain.message;

import com.alibaba.fastjson.JSON;
import com.rome.scm.common.rocketmq.CustomConsumerListener;
import com.rome.stock.common.enums.CustomRocketMQEnum;
import com.rome.stock.innerservice.remote.smd.dto.SmdOrderTO;
import com.rome.stock.innerservice.remote.smd.facade.SmdFacade;
import lombok.extern.slf4j.Slf4j;
import org.apache.rocketmq.common.message.MessageExt;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Objects;

/**
 * @Description: 下发给新上铁消费mq
 */
@Slf4j
@Service
public class ShopNewIronConsumer implements CustomConsumerListener<MessageExt> {

    @Resource
    private SmdFacade mdFacade;

    @Override
    public void onMessage(MessageExt msg, String businessNo, String msgKey) {
        log.info("下发给新上铁消息消费，msgID: {}，消费次数: {}", msg.getMsgId(), msg.getReconsumeTimes());
        SmdOrderTO smdOrderTO = JSON.parseObject(msg.getBody(), SmdOrderTO.class);
        if(Objects.isNull(smdOrderTO)) {
            log.error("下发给新上铁消息消费内容为空，msgID: {}", msg.getMsgId());
            return ;
        }
        //门店补货入库单推送到新上铁
        mdFacade.deliveryOrder(smdOrderTO);
    }

	@Override
	public String getRocketMQCode() {
		return CustomRocketMQEnum.MQ_SHOP_DELIVERY_NEW_IRON.getCode();
	}

}
