package com.rome.stock.innerservice.domain.service.impl;

import com.alibaba.fastjson.JSON;
import com.rome.stock.common.enums.redis.RedisCacheKeyEnum;
import com.rome.stock.innerservice.api.dto.cloudshop.ServiceLabDTO;
import com.rome.stock.innerservice.common.RedisUtil;
import com.rome.stock.innerservice.domain.convertor.cloudshop.CloudShopConvertor;
import com.rome.stock.innerservice.domain.repository.ServiceLabRepository;
import com.rome.stock.innerservice.domain.service.ServiceLabService;
import com.rome.stock.innerservice.infrastructure.dataobject.ServiceLabDO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;

/**
 * ServiceLabService 实现
 * 
 * <AUTHOR>
 * @date 2021-09-14
 */
@Slf4j
@Service
public class ServiceLabServiceImpl implements ServiceLabService {

    @Resource
    private ServiceLabRepository serviceLabRepository;

    @Resource
    private RedisUtil redisUtil;

    @Resource
    private CloudShopConvertor cloudShopConvertor;

    /**
     * 查询所有的服务标签
     * @return
     */
    @Override
    public List<ServiceLabDTO> listAllByType(Integer type){
        List<ServiceLabDTO> result = new ArrayList<>(10);
        List<ServiceLabDO> list = new ArrayList<>(10);
        Object  object =  redisUtil.get(RedisCacheKeyEnum.SERVICE_LAB.getKey() + type);
        if(object == null){
            //redis 没有就查库设置进去
            list = serviceLabRepository.listAllByType(type);
            if(CollectionUtils.isNotEmpty(list)){
                String jsonStr = JSON.toJSONString(list);
                redisUtil.set(RedisCacheKeyEnum.SERVICE_LAB.getKey() + type, jsonStr, RedisCacheKeyEnum.SERVICE_LAB.getExpireTime());
            }
        }else{
            String res = (String) object;
            list = JSON.parseArray(res, ServiceLabDO.class);
        }
        result = cloudShopConvertor.coverLabDoListToDtoList(list);
        return result;
    }

    @Override
    public List<ServiceLabDTO> getByIds(List<Long> ids) {
        List<ServiceLabDTO> result = new ArrayList<>(10);
        List<ServiceLabDO> list = serviceLabRepository.getByIds(ids);
        result = cloudShopConvertor.coverLabDoListToDtoList(list);
        return result;
    }

    @Override
    public List<ServiceLabDTO> listAll() {
        String type = "999";
        List<ServiceLabDTO> result = new ArrayList<>(10);
        List<ServiceLabDO> list = new ArrayList<>(10);
        Object  object =  redisUtil.get(RedisCacheKeyEnum.SERVICE_LAB.getKey() + type);
        if(object == null){
            //redis 没有就查库设置进去
            list = serviceLabRepository.listAll();
            if(CollectionUtils.isNotEmpty(list)){
                String jsonStr = JSON.toJSONString(list);
                redisUtil.set(RedisCacheKeyEnum.SERVICE_LAB.getKey() + type, jsonStr, RedisCacheKeyEnum.SERVICE_LAB.getExpireTime());
            }
        }else{
            String res = (String) object;
            list = JSON.parseArray(res, ServiceLabDO.class);
        }
        result = cloudShopConvertor.coverLabDoListToDtoList(list);
        return result;
    }

    @Override
    public List<ServiceLabDTO> getServiceLabByType(Integer businessType, Integer skuType) {
        List<ServiceLabDO> list=serviceLabRepository.getServiceLabByType(businessType,skuType);
        return cloudShopConvertor.coverLabDoListToDtoList(list);
    }


}
