package com.rome.stock.innerservice.domain.entity.record;

import com.rome.stock.innerservice.domain.entity.SkuQtyUnitBaseE;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Component;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * @Description: RecordPackageDetail
 * <p>
 * @Author: chuwenchao  2019/9/5
 */
@Data
@Slf4j
@Component
@Scope("prototype")
@EqualsAndHashCode(callSuper = false)
public class RecordPackageDetailE extends SkuQtyUnitBaseE implements Serializable {

    /**
     * 唯一主键
     */
    private Long id;
    /**
     * 包裹ID
     */
    private Long packageId;
    /**
     * 包裹编号
     */
    private String packageCode;
    /**
     * 出入库单据编号
     */
    private String recordCode;
    /**
     * 商品条码
     */
    private String barCode;
    /**
     * 商品数量
     */
    private BigDecimal skuQty;

}
