package com.rome.stock.innerservice.domain.message.consumer;

import com.alibaba.fastjson.JSONObject;
import com.rome.scm.common.rocketmq.CustomConsumerListener;
import com.rome.stock.common.enums.CustomRocketMQEnum;
import com.rome.stock.innerservice.api.dto.ReceiptRecordDTO;
import com.rome.stock.innerservice.api.dto.warehouserecord.ShopConfirmMQDTO;
import com.rome.stock.innerservice.domain.service.WhAllocationService;
import lombok.extern.slf4j.Slf4j;
import org.apache.rocketmq.common.message.MessageExt;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.util.Objects;

/**
 * @Description: 门店确认收货回传订单中心消费mq-非收货平台
 */
@Slf4j
@Service
public class ShopConfirmInNotifyConsumer implements CustomConsumerListener<MessageExt> {


    @Resource
    private WhAllocationService whAllocationService;


    @Override
    public void onMessage(MessageExt messageExt, String businessNo, String msgKey) {
        log.info("门店确认收货回传订单中心消息消费-非收货平台，msgID: {}，消费次数: {}", messageExt.getMsgId(), messageExt.getReconsumeTimes());
        String json = new String(messageExt.getBody());
        ShopConfirmMQDTO shopConfirmMQDTO= JSONObject.parseObject(json,ShopConfirmMQDTO.class);
        if(StringUtils.isEmpty(shopConfirmMQDTO.getRecordCode())) {
            log.error("门店确认收货回传订单中心消息消费内容为空-非收货平台，msgID: {}", messageExt.getMsgId());
            return ;
        }
        ReceiptRecordDTO receiptRecordDTO=new ReceiptRecordDTO();
        receiptRecordDTO.setWarehouseRecordCode(shopConfirmMQDTO.getRecordCode());
        //当 batchType =1时，是空批次，不设值
        if (!Objects.equals(1, shopConfirmMQDTO.getBatchType())) {
            receiptRecordDTO.setShopBatchList(shopConfirmMQDTO.getShopBatchList());
        }
        receiptRecordDTO.setOperatorCode(shopConfirmMQDTO.getOperatorCode());
        receiptRecordDTO.setOperatorName(shopConfirmMQDTO.getOperatorName());
        whAllocationService.pushInNotifyForShop(receiptRecordDTO);
    }

	@Override
	public String getRocketMQCode() {
		return CustomRocketMQEnum.MQ_SHOP_CONFIRM_IN_NOTIFY_PUSH.getCode();
	}

}
