package com.rome.stock.innerservice.domain.entity.warehouserecord;

import com.rome.arch.core.domain.EntityFactory;
import com.rome.stock.common.enums.warehouse.WarehouseRecordTypeVO;
import com.rome.stock.innerservice.common.SkuQtyUnitTools;
import com.rome.stock.innerservice.constant.WarehouseRecordBusinessTypeVO;

import com.rome.stock.innerservice.domain.entity.frontrecord.WarehouseInventoryRecordDetailE;
import com.rome.stock.innerservice.domain.entity.frontrecord.WarehouseInventoryRecordE;
import com.rome.stock.innerservice.domain.repository.warehouserecord.WarehouseInventoryWarehouseRepository;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;

/**
 * 仓库盘点单
 */
@Data
@Slf4j
@Component
@Scope("prototype")
@EqualsAndHashCode(callSuper = false)
public class WarehouseInventoryWarehouseRecordE extends AbstractWarehouseRecord{

    @Autowired
    private WarehouseInventoryWarehouseRepository warehouseInventoryWarehouseRepository;
    @Resource
    private EntityFactory entityFactory;
    @Resource
    private SkuQtyUnitTools skuQtyUnitTools;
    /**
     * 创建仓库单据
     */
    public void addWarehouseRecord() {
        long id = warehouseInventoryWarehouseRepository.saveWarehouseRecord(this);
        this.setId(id);
        this.warehouseRecordDetails.forEach(record -> record.setWarehouseRecordDetail(this));
        warehouseInventoryWarehouseRepository.saveWarehouseRecordDetails(this.warehouseRecordDetails);
        this.addFrontRecordAndWarehouseRelation();
    }

    /**
     * 根据前置单生成出库单
     */
    public void createOutRecordByFrontRecord(WarehouseInventoryRecordE frontRecord){
        createRecodeCode(WarehouseRecordTypeVO.WAREHOUSE_INVENTORY_OUT_WAREHOUSE_RECORD.getCode());
        this.setAbstractFrontRecord(frontRecord);
        this.setRealWarehouseId(frontRecord.getRealWarehouseId());
        this.setBusinessType(WarehouseRecordBusinessTypeVO.OUT_WAREHOUSE_RECORD.getType());
        this.setRecordType(WarehouseRecordTypeVO.WAREHOUSE_INVENTORY_OUT_WAREHOUSE_RECORD.getType());
        this.setMerchantId(frontRecord.getMerchantId());
        this.setOutCreateTime(frontRecord.getOutCreateTime());
        List<WarehouseInventoryRecordDetailE> frontRecordDetails=frontRecord.getFrontRecordDetails();
        this.setWarehouseRecordDetails(new ArrayList<>(frontRecordDetails.size()));
        //单位换算处理
        frontRecordDetails.forEach(wrRecord -> wrRecord.setBasicSkuQty(wrRecord.getSkuQty()));
        skuQtyUnitTools.convertRealToBasic(frontRecordDetails);
        if(frontRecordDetails!=null){
            for(WarehouseInventoryRecordDetailE detailE:frontRecordDetails){
                WarehouseRecordDetail warehouseRecordDetail=entityFactory.createEntity(WarehouseRecordDetail.class);
                warehouseRecordDetail.createRecordDetailByFrontRecord(detailE);
                //仓库盘点,计划与实际是一样的
                warehouseRecordDetail.setActualQty(detailE.getBasicDiff());
                warehouseRecordDetail.setPlanQty(detailE.getBasicDiff());
                this.warehouseRecordDetails.add(warehouseRecordDetail);
            }
            //设置skuCode和skuID
            this.initWarehouseRecodeDetail();
        }
    }

    /**
     * 根据前置单生成入库单
     */
    public void createInRecordByFrontRecord(WarehouseInventoryRecordE frontRecord){
        createRecodeCode(WarehouseRecordTypeVO.WAREHOUSE_INVENTORY_IN_WAREHOUSE_RECORD.getCode());
        this.setAbstractFrontRecord(frontRecord);
        this.setRealWarehouseId(frontRecord.getRealWarehouseId());
        this.setBusinessType(WarehouseRecordBusinessTypeVO.IN_WAREHOUSE_RECORD.getType());
        this.setRecordType(WarehouseRecordTypeVO.WAREHOUSE_INVENTORY_IN_WAREHOUSE_RECORD.getType());
        this.setMerchantId(frontRecord.getMerchantId());
        this.setOutCreateTime(frontRecord.getOutCreateTime());
        List<WarehouseInventoryRecordDetailE> frontRecordDetails=frontRecord.getFrontRecordDetails();
        this.setWarehouseRecordDetails(new ArrayList<>(frontRecordDetails.size()));
        //单位换算处理
        frontRecordDetails.forEach(wrRecord -> wrRecord.setBasicSkuQty(wrRecord.getSkuQty()));
        skuQtyUnitTools.convertRealToBasic(frontRecordDetails);
        if(frontRecordDetails!=null){
            for(WarehouseInventoryRecordDetailE detailE:frontRecordDetails){
                WarehouseRecordDetail warehouseRecordDetail=entityFactory.createEntity(WarehouseRecordDetail.class);
                warehouseRecordDetail.createRecordDetailByFrontRecord(detailE);
                //仓库盘点，计划入库数量与实际入库数量是一致的
                warehouseRecordDetail.setActualQty(detailE.getBasicDiff());
                warehouseRecordDetail.setPlanQty(detailE.getBasicDiff());
                this.warehouseRecordDetails.add(warehouseRecordDetail);
            }
            //设置skuCode和skuID
            this.initWarehouseRecodeDetail();
        }
    }

}
