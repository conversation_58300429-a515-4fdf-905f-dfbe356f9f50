package com.rome.stock.innerservice.domain.entity.frontrecord;

import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;

@Component
@Scope("prototype")
@Data
@EqualsAndHashCode(callSuper = false)
public class ShopForetasteRecordDetailE extends AbstractFrontRecordDetail{

    /**
     * id
     */
    private Long id;
    /**
     * 所属单据编码
     */
    private String recordCode;
    /**
     * 单据id
     */
    private Long frontRecordId;
    /**
     * 商品sku编码
     */
    private Long skuId;
    /**
     * 商品名称
     */
    private String skuName;
    /**
     * 单位
     */
    private String unit;
    /**
     * 单位code
     */
    private String unitCode;
    /**
     * 转换比例
     */
    private BigDecimal scale;
    /**
     * 规格
     */
    private String skuStandard;
    /**
     * 基本数量
     */
    private BigDecimal basicQty;

    /**
     * 实际出库数量
     */
    private BigDecimal actualQty;

    /**
     * 剩余数量
     */
    private BigDecimal remindQty;

}
