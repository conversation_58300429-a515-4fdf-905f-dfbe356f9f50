package com.rome.stock.innerservice.domain.entity.frontrecord;

import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;

@Component
@Scope("prototype")
@Data
@EqualsAndHashCode(callSuper = false)
public class PredictReturnRecordDetailE extends AbstractFrontRecordDetail {

    /**
     * 实收商品数量
     */
    private BigDecimal skuQty;

    /**
     * 备注
     */
    private String remark;

    private BigDecimal matchQty;
}
