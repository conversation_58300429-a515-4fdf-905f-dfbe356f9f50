package com.rome.stock.innerservice.domain.entity;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Component;

@Data
@Slf4j
@Component
@Scope("prototype")
@EqualsAndHashCode(callSuper = false)
public class FrontWarehouseRecordRelationE extends BaseE{
	/**
	 * 前置单据id
	 */
	private Long frontRecordId;

	/**
	 * 出入单据id
	 */
	private Long warehouseRecordId;

	/**
	 * 前置单类型
	 */
	private Integer frontRecordType;

	/**
	 * 出入库单编号
	 */
	private String recordCode;

	/**
	 * 前置单号
	 */
	private String frontRecordCode;

}
