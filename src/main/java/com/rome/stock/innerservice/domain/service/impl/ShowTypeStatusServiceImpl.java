package com.rome.stock.innerservice.domain.service.impl;

import com.rome.stock.common.enums.warehouse.WarehouseRecordTypeVO;
import com.rome.stock.innerservice.constant.*;
import com.rome.stock.innerservice.domain.repository.warehouserecord.WarehouseRecordRepository;
import com.rome.stock.innerservice.domain.service.ShowTypeStatusService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Map;

/**
 * 获取状态和类型集合
 */

@Service
public class ShowTypeStatusServiceImpl implements ShowTypeStatusService {
    @Resource
    private WarehouseRecordRepository warehouseRecordRepository;

    @Override
    public Map<Integer, String> getFrontRecordStatusList() {
        Map<Integer, String> map = FrontRecordStatusVO.getFrontRecordStatusList();
        return map;
    }

    @Override
    public Map<Integer, String> getFrontRecordTypeList() {
        Map<Integer, String> map = FrontRecordTypeVO.getFrontRecordTypeList();
        return map;
    }

    @Override
    public Map<Integer, String> getWarehouseRecordStatusList() {
        Map<Integer, String> map = WarehouseRecordStatusVO.getWarehouseRecordStatusList();
        return map;
    }

    @Override
    public Map<Integer, String> getInWarehouseRecordStatusList() {
        return this.getWarehouseRecordStatusList();
    }

    @Override
    public Map<Integer, String> getOutWarehouseRecordStatusList() {
        return this.getWarehouseRecordStatusList();
    }

    /**
     * 查找数据库中已存在的所有非Z补货出库单的状态
     *
     * @return
     */
    @Override
    public Map<Integer, String> getReplenishOutWarehouseRecordStatusList() {
        return this.getWarehouseRecordStatusList();
    }

    @Override
    public Map<Integer, String> getWarehouseRecordTypeList() {
        Map<Integer, String> map = WarehouseRecordTypeVO.getWarehouseRecordTypeList();
        for (Map.Entry<Integer, String> entry : map.entrySet()) {
            entry.setValue(entry.getValue() + "（"+ entry.getKey() +"）");
        }
        return map;
    }

    @Override
    public Map<Integer, String> getInWarehouseRecordTypeList() {
        Map<Integer, String> map = WarehouseRecordTypeVO.getInWarehouseRecordPageTypeList();
        return map;
    }

    @Override
    public Map<Integer, String> getOutWarehouseRecordTypeList() {
        Map<Integer, String> map = WarehouseRecordTypeVO.getOutWarehouseRecordPageTypeList();
        return map;
    }

    @Override
    public Map<Integer, String> getWarehouseAssembleRecordTypeList() {
        Map<Integer, String> map = FrontRecordTypeVO.getWarehouseAssembleRecordTypeList();
        return map;
    }

    /**
     * 非Z补货出库单列表
     *
     * @return
     */
    @Override
    public Map<Integer, String> getReplenishOutWarehouseRecordList() {
        Map<Integer, String> map = WarehouseRecordTypeVO.getReplenishOutWarehouseRecordList();
        return map;
    }

    /**
     *获取实仓流水库存类型(出库)
     * @return
     */
    @Override
    public Map<Integer, String> getOutStockType() {
        Map<Integer, String> map = StockTypeVO.getOutStockTypeList();
        return map;
    }
    /**
     *获取实仓流水库存类型(入库)
     * @return
     */
    @Override
    public Map<Integer, String> getInStockType() {
        Map<Integer, String> map = StockTypeVO.getInStockTypeList();
        return map;
    }
    
    /**
     *获取实仓流水库存类型
     * @return
     */
    @Override
    public Map<Integer, String> getAllStockType() {
        Map<Integer, String> map = StockTypeVO.getAllStockTypeList();
        return map;
    }
    
    /**
     *获取所有库存类型枚举
     * @return
     */
    @Override
    public Map<Integer, String> getInventoryType() {
        Map<Integer, String> map = InventoryTypeVO.getInventoryType();
        return map;
    }
    /**
     *获取所有质检状态枚举
     * @return
     */
    @Override
    public Map<Integer, String> getQualityStatus() {
        Map<Integer, String> map = QualityStatusVO.getQualityStatus();
        return map;
    }

    /**
     * 获取wms同步状态列表
     *
     * @return
     */
    @Override
    public Map<Integer, String> getWmsSyncStatusList() {
        Map<Integer, String> map = WmsSyncStatusVO.getWmsSyncStatusList();
        return map;
    }
}
