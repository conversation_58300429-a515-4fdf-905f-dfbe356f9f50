package com.rome.stock.innerservice.domain.entity;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Component;

/**
 * @Description: ChangeLogDO
 * <p>
 * @Author: chuwenchao  2019/10/12
 */
@Data
@Slf4j
@Component
@Scope("prototype")
@EqualsAndHashCode(callSuper = false)
public class ChangeLogE extends BaseE {

    /**
     * 业务名称
     */
    private String businessName;
    /**
     * 方法名
     */
    private String serviceName;
    /**
     * 变更前内容
     */
    private String beforeContent;
    /**
     * 变更后内容
     */
    private String afterContent;

}
