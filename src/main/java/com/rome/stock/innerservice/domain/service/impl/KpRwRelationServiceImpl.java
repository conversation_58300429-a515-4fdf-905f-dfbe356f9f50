/**
 * Filename KpRwRelationServiceImpl.java
 * Company 上海来伊份科技有限公司。
 * <AUTHOR>
 * @version 
 */
package com.rome.stock.innerservice.domain.service.impl;

import com.alibaba.fastjson.JSON;
import com.rome.arch.core.exception.RomeException;
import com.rome.stock.common.enums.redis.RedisCacheKeyEnum;
import com.rome.stock.common.enums.warehouse.CombineSkuTypeVO;
import com.rome.stock.common.enums.warehouse.WarehouseStoreIdentiEnum;
import com.rome.stock.core.constant.StockCoreConsts;
import com.rome.stock.innerservice.api.dto.message.BigdataKpRwRelationChangeStockDTO;
import com.rome.stock.innerservice.common.RedisUtil;
import com.rome.stock.innerservice.common.ResCode;
import com.rome.stock.innerservice.domain.entity.RealWarehouseE;
import com.rome.stock.innerservice.domain.message.KpRwChangeStockByBigdataProducer;
import com.rome.stock.innerservice.domain.message.KpRwChangeStockBySaleOutProducer;
import com.rome.stock.innerservice.domain.repository.RealWarehouseRepository;
import com.rome.stock.innerservice.domain.repository.warehouserecord.KpRwRelationRepository;
import com.rome.stock.innerservice.domain.service.KpRwRelationService;
import com.rome.stock.innerservice.remote.item.dto.CombineSkuInfoResultDTO;
import com.rome.stock.innerservice.remote.item.facade.SkuFacade;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.MapUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 鲲鹏实仓关联
 * <AUTHOR>
 * @since 2020-9-11 15:04:15
 */
@Service
@Slf4j
public class KpRwRelationServiceImpl implements KpRwRelationService {

	@Resource
    private KpRwRelationRepository kpRwRelationRepository;
	
	@Autowired
    private RedisUtil redisUtil;
	
	@Resource(name = "coreStockTask")
	private ThreadPoolTaskExecutor coreStockTask;
	
	@Resource
    private KpRwChangeStockByBigdataProducer kpRwChangeStockByBigdataProducer;

	@Resource
	private KpRwChangeStockBySaleOutProducer kpRwChangeStockBySaleOutProducer;
	@Resource
	private RealWarehouseRepository realWarehouseRepository;
	@Resource
	private SkuFacade skuFacade;
	/**
	 * 鲲鹏实仓库存变化推到大数据,异步
	 */
	@Override
	public void pushChangeStockByBigdataMsgByAsyn() {
		log.info("开始,异步,鲲鹏实仓库存变化推到大数据");
		coreStockTask.submit(new Runnable() {
			@Override
			public void run() {
				pushChangeStockByBigdataMsg();
			}
			});
		log.info("成功,加入线程池任务异步,鲲鹏实仓库存变化推到大数据");
	}
	
	/**
	 * 鲲鹏实仓库存变化推到大数据,同步
	 */
	@Override
	public void pushChangeStockByBigdataMsg() {
		boolean isLock = false;
		RedisCacheKeyEnum cacheKeyEnum = RedisCacheKeyEnum.BIGDATA_KP_RW_CHANGE_STOCK;
		try {
			isLock = redisUtil.lock(cacheKeyEnum.getKeyLock(), "1", cacheKeyEnum.getExpireTimeLock());
			if(isLock == false) {
				throw new RomeException(ResCode.STOCK_ERROR_1001, "鲲鹏实仓库存变化推到大数据,正在推送中");
			}
			// 数据处理 start
			int start = 0;
			final int pageSize = 1000;
			List<BigdataKpRwRelationChangeStockDTO> list = null;
			do {
				list = kpRwRelationRepository.queryChangeStockByBigdataMsg(start, pageSize,null,null);
				if(CollectionUtils.isEmpty(list)) {
	                break;
	            }
				for(BigdataKpRwRelationChangeStockDTO dto : list) {
					if(dto.getAvailableQty() == null || dto.getAvailableQty().compareTo(StockCoreConsts.MIN_VALUE_ZERO)<0) {
						dto.setAvailableQty(BigDecimal.ZERO);
					}
					dto.setSendTime(new Date());
				}
				kpRwChangeStockByBigdataProducer.sendMQ(JSON.toJSONString(list));
				start += list.size();
			} while(!CollectionUtils.isEmpty(list) && list.size() == pageSize);
			// 数据处理 end
		} catch (RomeException e) {
			log.error("鲲鹏实仓库存变化推到大数据,正在推送中,出错,{}", e);
			throw e;
		}catch (Exception e) {
			log.error("鲲鹏实仓库存变化推到大数据,正在推送中,出错,{}", e);
			throw new RomeException(ResCode.STOCK_ERROR_1003, e.getMessage());
		} finally {
			if(isLock) {
				redisUtil.unLock(cacheKeyEnum.getKeyLock(), "1");
			}
		}
	}


	/**
	 * 鲲鹏实仓库存变化推到外卖,同步
	 */
	@Override
	public void pushChangeStockBySaleOut(List<String> shopCodes) {
		boolean isLock = false;
		RedisCacheKeyEnum cacheKeyEnum = RedisCacheKeyEnum.SALEOUT_KP_RW_CHANGE_STOCK;
		try {
			isLock = redisUtil.lock(cacheKeyEnum.getKeyLock(), "1", cacheKeyEnum.getExpireTimeLock());
			if(isLock == false) {
				throw new RomeException(ResCode.STOCK_ERROR_1001, "鲲鹏实仓库存变化推到外卖,正在推送中");
			}
			// 数据处理 start
			int start = 0;
			final int pageSize = 1000;
			List<BigdataKpRwRelationChangeStockDTO> list = null;
			List<Long> realWarehouseIds=new ArrayList<>();
			if(!CollectionUtils.isEmpty(shopCodes)){
				shopCodes=shopCodes.stream().distinct().collect(Collectors.toList());
				List<RealWarehouseE> realWarehouseEList = realWarehouseRepository.getRwListByShopCodes(shopCodes);
				if(!CollectionUtils.isEmpty(realWarehouseEList)){
					realWarehouseIds=realWarehouseEList.stream().map(RealWarehouseE::getId).distinct().collect(Collectors.toList());
				}
			}
			Date dateTime=new Date();
			do {
				list = kpRwRelationRepository.queryChangeStockBySaleOut(start, pageSize,dateTime,realWarehouseIds);
				if(CollectionUtils.isEmpty(list)) {
					break;
				}
				//过滤出仓店一体的数据
				List<String> skuCodeList = list.stream().filter(v-> Objects.equals(WarehouseStoreIdentiEnum.WAREHOUSESTORE.getCode(), v.getWarehouseStoreIdenti())).map(BigdataKpRwRelationChangeStockDTO::getSkuCode).collect(Collectors.toList());
				Map<String, CombineSkuInfoResultDTO> combineSkuInfoMap = new HashMap<>();
				if(!CollectionUtils.isEmpty(skuCodeList)){
					List<CombineSkuInfoResultDTO> combineSkuInfoList = skuFacade.queryCombineInfoSkuBySkuCodes(skuCodeList);
					//过滤出大包装组合的品
					combineSkuInfoMap = combineSkuInfoList.stream()
							.filter(v->Objects.equals(CombineSkuTypeVO.LARGE_COMBINE.getCombineType(), v.getCombineType()))
							.collect(Collectors.toMap(CombineSkuInfoResultDTO::getCombineSkuCode, Function.identity(), (v1, v2) -> v1));
				}
				for(BigdataKpRwRelationChangeStockDTO dto : list) {
					if(dto.getAvailableQty() == null || dto.getAvailableQty().compareTo(StockCoreConsts.MIN_VALUE_ZERO)<0) {
						dto.setAvailableQty(BigDecimal.ZERO);
					}
					if(combineSkuInfoMap.containsKey(dto.getSkuCode())){
						CombineSkuInfoResultDTO combineSkuInfo = combineSkuInfoMap.get(dto.getSkuCode());
						//使用大包装品的物料来替换掉当前子品的物料和数量
						dto.setSkuCode(combineSkuInfo.getSkuCode());
						if(Objects.nonNull(combineSkuInfo.getNum()) && combineSkuInfo.getNum().compareTo(BigDecimal.ZERO)>0){
							dto.setRealQty(dto.getRealQty().divide(combineSkuInfo.getNum(), 3, BigDecimal.ROUND_DOWN));
							dto.setOnroadQty(dto.getOnroadQty().divide(combineSkuInfo.getNum(), 3, BigDecimal.ROUND_DOWN));
							dto.setLockQty(dto.getLockQty().divide(combineSkuInfo.getNum(), 3, BigDecimal.ROUND_DOWN));
							dto.setQualityQty(dto.getQualityQty().divide(combineSkuInfo.getNum(), 3, BigDecimal.ROUND_DOWN));
							dto.setUnqualifiedQty(dto.getUnqualifiedQty().divide(combineSkuInfo.getNum(), 3, BigDecimal.ROUND_DOWN));
							dto.setAvailableQty(dto.getAvailableQty().divide(combineSkuInfo.getNum(), 3, BigDecimal.ROUND_DOWN));
						}
					}
					dto.setSendTime(new Date());
				}
				kpRwChangeStockBySaleOutProducer.sendMQ(JSON.toJSONString(list));
				start += list.size();
			} while(!CollectionUtils.isEmpty(list) && list.size() == pageSize);
			// 数据处理 end
		} catch (RomeException e) {
			log.error("鲲鹏实仓库存变化推到外卖,正在推送中,出错,{}", e);
			throw e;
		}catch (Exception e) {
			log.error("鲲鹏实仓库存变化推到外卖,正在推送中,出错,{}", e);
			throw new RomeException(ResCode.STOCK_ERROR_1003, e.getMessage());
		} finally {
			if(isLock) {
				redisUtil.unLock(cacheKeyEnum.getKeyLock(), "1");
			}
		}
	}



	/**
	 * 鲲鹏实仓库存变化推到大数据,异步
	 */
	@Override
	public void pushChangeStockBySaleOutAsync() {
		log.info("开始,异步,鲲鹏实仓库存变化推到外卖");
		coreStockTask.submit(() -> pushChangeStockBySaleOut(new ArrayList<>()));
		log.info("成功,加入线程池任务异步,鲲鹏实仓库存变化推到外卖");
	}

	@Override
	public void pushChangeStockBySaleOutByHand(List<String> shopCodes) {
		pushChangeStockBySaleOut(shopCodes);
	}
}
