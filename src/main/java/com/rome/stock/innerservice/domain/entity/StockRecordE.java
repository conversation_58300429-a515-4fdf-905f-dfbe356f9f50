package com.rome.stock.innerservice.domain.entity;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.util.List;

@Component
@Scope("prototype")
@Data
@EqualsAndHashCode(callSuper = false)
public class StockRecordE extends BaseE {
    /**
     * 仓储类型
     */
    private Integer storageType;
    /**
     * 工厂编码
     */
    private String factoryCode;
    /**
     * 工厂名称
     */
    private String factoryName;
    /**
     * 仓库id
     */
    private Long warehouseId;
    /**
     * 仓库编码
     */
    private String warehouseCode;
    /**
     * 仓库名称
     */
    private String warehouseName;
    /**
     * 渠道编码
     */
    private String channelCode;
    /**
     * 渠道字符串集合
     */
    private String channelCodes;
    /**
     * 渠道名称
     */
    private String channelName;
    /**
     * 商品编码
     */
    private Long skuId;
    /**
     * 商品skuId集合
     */
    private List<Long> skuIds;
    /**
     * 商品编号
     */
    private String skuCode;
    /**
     * 商品名称
     */
    private String skuName;
    /**
     * 商品标题
     */
    private String skuTitle;
    /**
     * 商品基本单位
     */
    private String skuUnit;
    /**
     * 商品基本单位编号
     */
    private String skuUnitCode;
    /**
     * 商品品类
     */
    private String skuType;
    /**
     * 库存现有量
     */
    private BigDecimal realQty;
    /**
     * 库存可用量
     */
    private BigDecimal availableQty;


    /**
     * 安全库存
     */
    private BigDecimal safeConfigQty;
    /**
     *库存锁定量
     */
    private BigDecimal lockQty;
    /**
     * 库存质检锁定量
     */
    private BigDecimal qualityQty;
    /**
     * 库存质检锁定量(箱子)
     */
    private BigDecimal qualityQtyBox;
    /**
     * 冻结库存
     */
    private BigDecimal unqualifiedQty;
    /**
     * 公司编号
     */
    private String corpCode;
    /**
     * 公司名称
     */
    private String corpName;
    /**
     * 仓库类型名称
     */
    private String warehouseTypeName;
    /**
     * 仓库类型
     */
    private Integer warehouseType;

    /**
     * 箱单位名称
     */
    private String boxUnitName;

    /**
     * 箱单位数量
     */
    private BigDecimal boxUnitCount;

    /**
     * 发货单位
     */
    private String deliveryUnitName;

    /**
     * 发货单位数量
     */
    private BigDecimal deliveryUnitCount;

    private BigDecimal onroadQty;

    /**
     * 销售状态名称
     */
    private String saleStatusName;

    /**
     * 实仓类型
     */
    private Integer realWarehouseType;

    /**
     * 商品条码
     */
    private String barCode;

    /**
     * 未出库完成的在途库存
     */
    private BigDecimal unCompleteOnRoadStock=BigDecimal.ZERO;

    /**
     * 采购在途库存
     */
    private BigDecimal purchaseOnRoadStock=BigDecimal.ZERO;
}



