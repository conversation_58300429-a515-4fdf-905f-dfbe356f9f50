package com.rome.stock.innerservice.domain.entity.frontrecord;

import com.rome.stock.innerservice.constant.FrontRecordTypeVO;
import com.rome.stock.innerservice.domain.repository.frontrecord.FrShopConsumeDetailRepository;
import com.rome.stock.innerservice.domain.repository.frontrecord.FrShopConsumeRepository;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.apache.commons.lang3.StringUtils;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;

/**
 * @Description
 * <AUTHOR>
 * @Date 2019/5/10 20:27
 * @Version 1.0
 */
@Component
@Scope("prototype")
@Data
@EqualsAndHashCode(callSuper = false)
public class ShopConsumeAdjustRecordE extends AbstractFrontRecord{

    @Resource
    private FrShopConsumeRepository frShopConsumeRepository;

    @Resource
    private FrShopConsumeDetailRepository frShopConsumeDetailRepository;
    /**
     * 创建门店报废单
     */
    public void addFrontRecord(){
        //生成单据编号
        this.initFrontRecord(FrontRecordTypeVO.SHOP_CONSUME_ADJUST_RECORD.getCode(), this.frontRecordDetails);
        if(StringUtils.isBlank(this.getRemark())) {
            this.setRemark("");
        }
        //插入仓库损耗单据
        long id=frShopConsumeRepository.saveConsumeAdjustRecord(this);
        this.setId(id);

        //损耗明细关联损耗单
        for (ShopConsumeAdjustRecordDetailE detailE:this.frontRecordDetails) {
            detailE.setFrontRecordId(this.getId());
            detailE.setRecordCode(this.recordCode);
        }

        //插入调整单据详情
        frShopConsumeDetailRepository.saveConsumeAdjustRecord(this.frontRecordDetails);
    }

    /**
     * 调整单单号
     */
    private String recordCode;

    /**
     * SAP过账单号
     */
    private String sapRecordCode;

    /**
     * 调整单状态
     */
    private Integer recordStatus;

    /**
     * 单据类型
     */
    private Integer recordType;

    /**
     * 业务原因编号
     */
    private String reasonCode;

    /**
     * 归属组织编号
     */
    private String organizationCode;

    /**
     * 归属组织名称
     */
    private String organizationName;

    /**
     * 工厂code
     */
    private String factoryCode;

    /**
     * 门店仓ID
     */
    private Long realWarehouseId;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新时间
     */
    private Date updateTime;

    /**
     * 开始时间
     */
    private Date startTime;

    /**
     * 结束时间
     */
    private Date endTime;

    /**
     * 备注
     */
    private String remark;

    /**
     * OA审批号
     */
    private String approveOACode;

    /**
     * 前置单明细集合
     */
    private List<ShopConsumeAdjustRecordDetailE> frontRecordDetails;



}
