package com.rome.stock.innerservice.domain.entity.warehouserecord;

import com.rome.stock.innerservice.api.dto.BatchStockDTO;
import com.rome.stock.innerservice.domain.entity.SkuQtyUnitBaseE;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2022年08月16日 16:56
 */
/**
 * 类WarehouseRecordDetail的实现描述：出入库单详情
 *
 * <AUTHOR> 2019/4/17 21:27
 */
@Data
@Slf4j
@Component
@Scope("prototype")
@EqualsAndHashCode(callSuper = false)
public class WarehouseRecordDetailE extends SkuQtyUnitBaseE {

    /**
     * 所属单据编码
     */
    private String recordCode;

    /**
     * 所属单据id
     */
    private Long warehouseRecordId;

    /**
     * 商品名称
     */
    private String skuName;


    private String skuPicPath;

    private String skuCname;

    private String skuEname;

    /**
     *  商品数量
     */
    private BigDecimal planQty;

    /**
     * 实际收货数量 包括待质检的
     */
    private BigDecimal actualQty;

    public BigDecimal getActualQty(){
        return this.actualQty;
    }

    public void setActualQty(BigDecimal actualQty){
        this.actualQty=actualQty;
    }

    /**
     * sap采购单号
     */
    private String sapPoNo;

    /**
     * 行号
     * */
    private String lineNo;

    /**
     * 中台行号
     */
    private String ztLineNo;

    /**
     * 交货单行号
     * */
    private String deliveryLineNo;

    /**
     * 虚拟仓库ID
     */
    private Long virtualWarehouseId;

    /**
     * 实仓ID
     */
    private Long realWarehouseId;

    /**
     * 渠道编码
     */
    private String channelCode;

    /**
     * 用户编号
     */
    private String userCode;

    /**
     * 期望交货时间
     */
    private Date deliveryData;

    /**
     * 商品条码
     */
    private String barCode;

    /**
     * 是否免费sku行 0:不免费  1:免费
     */
    private Integer isFree;

    /**
     * 采购单位
     */
    private String purchaseUnit;

    /**
     * 退货原因
     */
    private String reason;

    /**
     * 设置超收比例
     */
    private Integer exceedRaio;

    /**
     * 成品和组合品转换关系(外采专用字段)
     */
    private Integer num;

    /**
     * 基础单位转换总数
     */
    private BigDecimal totalBaseSkuQty;

    /**
     * 是否需要质检，外采用
     */
    private Integer needInspect;

    private String reasonCode;
}
