package com.rome.stock.innerservice.domain.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.rome.arch.core.domain.EntityFactory;
import com.rome.arch.core.exception.RomeException;
import com.rome.arch.util.SpringBeanUtil;
import com.rome.arch.util.dynamic.DynamicDataSourceEnum;
import com.rome.arch.util.dynamic.TargetDataSource;
import com.rome.stock.common.enums.warehouse.WarehouseRecordTypeVO;
import com.rome.stock.common.utils.DateUtil;
import com.rome.stock.core.domain.repository.CoreChannelSalesRepository;
import com.rome.stock.core.domain.repository.CoreRealWarehouseStockRepository;
import com.rome.stock.core.domain.repository.CoreVirtualWarehouseStockRepository;
import com.rome.stock.core.facade.RedisRollBackFacade;
import com.rome.stock.core.infrastructure.dataobject.core.*;
import com.rome.stock.core.infrastructure.dataobject.core.db.DbRwStockChangeFlowDO;
import com.rome.stock.innerservice.api.dto.*;
import com.rome.stock.innerservice.api.dto.frontrecord.GroupAddressDTO;
import com.rome.stock.innerservice.api.dto.groupbuy.GroupWarehouseRecordDetailTemplate;
import com.rome.stock.innerservice.api.dto.groupbuy.GroupWarehouseRecordTemplate;
import com.rome.stock.innerservice.api.dto.qry.GroupWarehouseRecordCondition;
import com.rome.stock.innerservice.api.dto.qry.SaleWarehouseRecordCondition;
import com.rome.stock.innerservice.api.dto.replenish.BatchCancleDTO;
import com.rome.stock.innerservice.api.dto.replenish.CancelReasonDTO;
import com.rome.stock.innerservice.api.dto.replenish.ReCalculateDTO;
import com.rome.stock.innerservice.api.dto.warehouserecord.*;
import com.rome.stock.innerservice.common.*;
import com.rome.stock.innerservice.constant.*;
import com.rome.stock.innerservice.domain.convertor.BatchStockConvertor;
import com.rome.stock.innerservice.domain.convertor.EsFrRecordConvertor;
import com.rome.stock.innerservice.domain.convertor.WarehouseRecordConvertor;
import com.rome.stock.innerservice.domain.convertor.WarehouseRecordDetailConvertor;
import com.rome.stock.innerservice.domain.convertor.record.RecordPackageConvertor;
import com.rome.stock.innerservice.domain.convertor.warehouserecord.ShopRetailWarehouseConvertor;
import com.rome.stock.innerservice.domain.entity.*;
import com.rome.stock.innerservice.domain.entity.AddressE;
import com.rome.stock.innerservice.domain.entity.FrontWarehouseRecordRelationE;
import com.rome.stock.innerservice.domain.entity.RwRecordPoolDetailE;
import com.rome.stock.innerservice.domain.entity.RwRecordPoolE;
import com.rome.stock.innerservice.domain.entity.frontrecord.FrSaleWdtExtE;
import com.rome.stock.innerservice.domain.entity.frontrecord.FrontRecordE;
import com.rome.stock.innerservice.domain.entity.frontrecord.OnlineRetailE;
import com.rome.stock.innerservice.domain.entity.frontrecord.ReservationRecordE;
import com.rome.stock.innerservice.domain.entity.warehouserecord.*;
import com.rome.stock.innerservice.domain.repository.*;
import com.rome.stock.innerservice.domain.repository.elasticsearch.EsWarehouseRecordRepository;
import com.rome.stock.innerservice.domain.repository.frontrecord.FrReservationRepository;
import com.rome.stock.innerservice.domain.repository.frontrecord.FrSaleRepository;
import com.rome.stock.innerservice.domain.repository.frontrecord.FrWDTSaleRepository;
import com.rome.stock.innerservice.domain.repository.frontrecord.FrontWarehouseRecordRelationRepository;
import com.rome.stock.innerservice.domain.repository.warehouserecord.RecordPackageRepository;
import com.rome.stock.innerservice.domain.repository.warehouserecord.WarehouseRecordRepository;
import com.rome.stock.innerservice.domain.service.RealWarehouseService;
import com.rome.stock.innerservice.domain.service.ShopRetailService;
import com.rome.stock.innerservice.domain.service.VirtualWarehouseService;
import com.rome.stock.innerservice.domain.service.WmsOutService;
import com.rome.stock.innerservice.facade.StockChangeDataExtFacade;
import com.rome.stock.innerservice.facade.StockCostFacade;
import com.rome.stock.innerservice.infrastructure.dataobject.BatchStockChangeFlowDO;
import com.rome.stock.innerservice.infrastructure.dataobject.FrontWarehouseRecordRelationDO;
import com.rome.stock.innerservice.infrastructure.dataobject.RwRecordPoolDo;
import com.rome.stock.innerservice.infrastructure.dataobject.WarehouseRecordDo;
import com.rome.stock.innerservice.infrastructure.dataobject.elasticsearch.EsFrRecordDO;
import com.rome.stock.innerservice.infrastructure.dataobject.elasticsearch.EsWarehouseRecordDO;
import com.rome.stock.innerservice.infrastructure.dataobject.frontrecord.FrReservationDO;
import com.rome.stock.innerservice.infrastructure.dataobject.record.RecordPackageDO;
import com.rome.stock.innerservice.infrastructure.dataobject.record.RecordPackageDetailDO;
import com.rome.stock.innerservice.infrastructure.mapper.frontrecord.FrSaleMapper;
import com.rome.stock.innerservice.infrastructure.mapper.frontrecord.FrWDTSaleMapper;
import com.rome.stock.innerservice.infrastructure.redis.WarehouseRouteRedis;
import com.rome.stock.innerservice.infrastructure.mapper.frontrecord.FrSaleWdtExtMapper;
import com.rome.stock.innerservice.remote.base.dto.ChannelDTO;
import com.rome.stock.innerservice.remote.base.dto.StoreDTO;
import com.rome.stock.innerservice.remote.base.facade.BaseDataFacade;
import com.rome.stock.innerservice.remote.base.facade.ChannelFacade;
import com.rome.stock.innerservice.remote.base.facade.ShopFacade;
import com.rome.stock.innerservice.remote.item.dto.SkuInfoExtDTO;
import com.rome.stock.innerservice.remote.item.dto.SkuUnitExtDTO;
import com.rome.stock.innerservice.remote.item.dto.SkuUnitParamExtDTO;
import com.rome.stock.innerservice.remote.item.facade.SkuFacade;
import com.rome.stock.innerservice.remote.orderTrack.facade.OrderTrackFacade;
import com.rome.stock.wms.config.BaseinfoConfiguration;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.lucene.search.join.ScoreMode;
import org.elasticsearch.index.IndexNotFoundException;
import org.elasticsearch.index.query.BoolQueryBuilder;
import org.elasticsearch.index.query.QueryBuilders;
import org.elasticsearch.index.query.RangeQueryBuilder;
import org.elasticsearch.search.sort.SortBuilders;
import org.elasticsearch.search.sort.SortOrder;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.elasticsearch.core.query.NativeSearchQuery;
import org.springframework.data.elasticsearch.core.query.NativeSearchQueryBuilder;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.Map.Entry;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 门店销售
 */
@Service
@Slf4j
public class ShopRetailServiceImpl implements ShopRetailService {

    @Resource
    private CoreVirtualWarehouseStockRepository coreVirtualWarehouseStockRepository;

    @Resource
    private ShopRetailWarehouseConvertor shopRetailWarehouseConvertor;

    @Resource
    private WarehouseRecordRepository warehouseRecordRepository;

    @Resource
    private SkuFacade skuFacade;

    @Resource
    private ChannelFacade channelFacade;

    @Resource
    private EntityFactory entityFactory;
    @Resource
    private RealWarehouseService realWarehouseService;
    @Resource
    private FrSaleRepository frSaleRepository;

    @Resource
    private FrWDTSaleRepository frWDTSaleRepository;

    @Resource
    private BatchStockRepository batchStockRepository;

    @Resource
    private CoreChannelSalesRepository coreChannelSalesRepository;


    @Resource
    private CoreRealWarehouseStockRepository coreRealWarehouseStockRepository;
    
    @Resource
	private EsWarehouseRecordRepository esWarehouseRecordRepository;

    @Resource
    private EsFrRecordConvertor esFrRecordConvertor;
    @Resource
    private WmsOutService wmsOutService;
    @Resource
    private VirtualWarehouseService virtualWarehouseService;
    @Resource
    private RwRecordPoolRepository rwRecordPoolRepository;
    @Resource
    private RecordPackageRepository recordPackageRepository;

    @Resource
    private FrReservationRepository frReservationRepository;

    @Resource
    private AddressRepository addressRepository;
    @Resource
    private FrontWarehouseRecordRelationRepository frontWarehouseRecordRelationRepository;

    @Resource
    private RealWarehouseWmsConfigRepository realWarehouseWmsConfigRepository;

    @Autowired
    private BatchStockConvertor batchStockConvertor;

    @Resource
    private OrderTrackFacade orderTrackFacade;
    @Resource
    private TmsTools tmsTools;
    @Resource
    private VirtualWarehouseRepository virtualWarehouseRepository;
    @Resource
    private FrWDTSaleMapper frWDTSaleMapper;
    @Resource
    private FrSaleMapper frSaleMapper;
    @Resource
    private FrSaleWdtExtMapper frSaleWdtExtMapper;
    @Resource
    private WarehouseRecordDetailConvertor warehouseRecordDetailConvertor;
    @Resource
    private WarehouseRouteRedis warehouseRouteRedis;
    @Resource
    private BaseDataFacade baseDataFacade;
    @Resource
    private WarehouseRecordConvertor warehouseRecordConvertor;
    @Resource
    private RecordPackageConvertor recordPackageConvertor;

    /**
     * 取消门店零售单
     *
     * @param outRecordCode
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void cancelShopRetailRecord(String outRecordCode) {
        List<WarehouseRecordE> warehouseRecordList = warehouseRecordRepository.getRecordTypeByRecordCodes(Arrays.asList(outRecordCode));
        if(CollectionUtil.isEmpty(warehouseRecordList)) {
            throw new RomeException(ResCode.STOCK_ERROR_1002, ResCode.STOCK_ERROR_1002_DESC + " 后置单不存在");
        }
        WarehouseRecordE warehouseRecordE =warehouseRecordList.get(0);
        if(Objects.equals(warehouseRecordE.getRecordStatus(),WarehouseRecordStatusVO.DISABLED.getStatus())){
            return;
        }
        warehouseRecordRepository.updateToCanceledFromComplete(warehouseRecordE.getId());
        CoreRealStockOpDO coreRecordRealStockIncreaseDO = this.initIncreaseStockObj(warehouseRecordE);
        boolean isSuccess = false;
        try {
            coreRealWarehouseStockRepository.increaseRealQty(coreRecordRealStockIncreaseDO);
            isSuccess = true;
        } catch (RomeException e) {
            throw e;
        } catch (Exception e) {
            throw new RomeException(ResCode.STOCK_ERROR_1003, ResCode.STOCK_ERROR_1003_DESC);
        } finally {
            if (!isSuccess) {
                RedisRollBackFacade.redisRollBack(coreRecordRealStockIncreaseDO);
            }
        }
    }


    /**
     * 初始化增加实体仓库库存的对象
     */
    private CoreRealStockOpDO initIncreaseStockObj(AbstractWarehouseRecord recordE) {
        List<CoreRealStockOpDetailDO> increaseDetails = new ArrayList<>();
        for (WarehouseRecordDetail detail : recordE.getWarehouseRecordDetails()) {
            CoreRealStockOpDetailDO coreRealStockOpDetailDO = new CoreRealStockOpDetailDO();
            coreRealStockOpDetailDO.setSkuId(detail.getSkuId());
            coreRealStockOpDetailDO.setChannelCode(recordE.getChannelCode());
            coreRealStockOpDetailDO.setSkuCode(detail.getSkuCode());
            coreRealStockOpDetailDO.setRealQty(detail.getActualQty());
            coreRealStockOpDetailDO.setRealWarehouseId(recordE.getRealWarehouseId());
            increaseDetails.add(coreRealStockOpDetailDO);
        }
        CoreRealStockOpDO coreRealStockOpDO = new CoreRealStockOpDO();
        coreRealStockOpDO.setRecordCode(recordE.getRecordCode());
        coreRealStockOpDO.setTransType(recordE.getRecordType());
        coreRealStockOpDO.setDetailDos(increaseDetails);
        return coreRealStockOpDO;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void addShopRetailRecord(OutWarehouseRecordDTO outWarehouseRecordDTO) {
        RealWarehouse realWarehouse =realWarehouseService.findByRealWarehouseOutCodeAndFactoryCode(outWarehouseRecordDTO.getWarehouseCode(),outWarehouseRecordDTO.getFactoryCode());
        AlikAssert.isNotNull(realWarehouse, "999", "当前仓库不存在");
        //幂等校验
        WarehouseRecordE warehouseRecordE = warehouseRecordRepository.queryWarehouseRecordByRecordCode(outWarehouseRecordDTO.getRecordCode());
        if(Objects.nonNull(warehouseRecordE)){
            return;
        }
        ShopRetailWarehouseRecordE warehouseRecord = entityFactory.createEntity(ShopRetailWarehouseRecordE.class);
        //根据前置单生成出库单数据
        warehouseRecord.createOutRecordByFrontRecord(outWarehouseRecordDTO,realWarehouse.getId());
        warehouseRecord.setRecordStatus(WarehouseRecordStatusVO.OUT_ALLOCATION.getStatus());
        if(StringUtils.isNotBlank(outWarehouseRecordDTO.getOutRecordCode())){
            warehouseRecord.setSapOrderCode(outWarehouseRecordDTO.getOutRecordCode());
        }

        Date currentDate = new Date();
        warehouseRecord.setOutOrInTime(currentDate);
        if (StringUtils.isNotBlank(outWarehouseRecordDTO.getOutCreateTime())) {
            warehouseRecord.setDeliveryTime(DateUtil.parseDateTime(outWarehouseRecordDTO.getOutCreateTime()));
        }
        if (warehouseRecord.getDeliveryTime() == null) {
            warehouseRecord.setDeliveryTime(currentDate);
        }

        //需要处理批次库存
        warehouseRecord.setBatchStatus(WarehouseRecordBatchStatusVO.INIT.getStatus());
        //创建门店销售出库单
        log.info(outWarehouseRecordDTO.getRecordCode()+":创建门店销售出库单："+System.currentTimeMillis());
        //合并相同的sku明细信息
        warehouseRecord.mergeSameSkuDetails();
        warehouseRecord.addWarehouseRecord();
        boolean isSuccess = false;
        CoreStockOpFactoryDO stockOpFactoryDO = new CoreStockOpFactoryDO();
        CoreRealStockOpDO decreaseRealStockOpDO = null;
        try {
            //减少门店实仓库存
            log.info(outWarehouseRecordDTO.getRecordCode()+":减少门店实仓库存,开始："+System.currentTimeMillis());
            decreaseRealStockOpDO = warehouseRecord.initDecreaseStockObj(realWarehouse.getId(), false);
            stockOpFactoryDO.reset(decreaseRealStockOpDO);
            coreRealWarehouseStockRepository.decreaseRealQty(decreaseRealStockOpDO);
            log.info(outWarehouseRecordDTO.getRecordCode()+":减少门店实仓库存,结束："+System.currentTimeMillis());
            //出库单推送到库存成本中心
            StockCostFacade.sendOutOrderToCostMsg(warehouseRecord);
            stockOpFactoryDO.commit();
            isSuccess = true;
        } catch (RomeException e) {
            throw e;
        } catch (Exception e) {
            throw e;
        } finally {
            if (!isSuccess) {
                stockOpFactoryDO.redisRollBack();
            }
        }
    }


    private CoreChannelOrderDO convertOrderDOFromShopRetailRecordE(ShopRetailWarehouseRecordE warehouseRecord) {
        CoreChannelOrderDO coreChannelOrderDO = new CoreChannelOrderDO();
        coreChannelOrderDO.setChannelCode(warehouseRecord.getChannelCode());
        coreChannelOrderDO.setRecordCode(warehouseRecord.getRecordCode());
        coreChannelOrderDO.setTransType(warehouseRecord.getRecordType());
        coreChannelOrderDO.setMerchantId(warehouseRecord.getMerchantId());
        List<CoreOrderDetailDO> coreOrderDetailDOList = new ArrayList<>();
        for (WarehouseRecordDetail sku : warehouseRecord.getWarehouseRecordDetails()) {
            CoreOrderDetailDO detailDO = new CoreOrderDetailDO();
            detailDO.setSkuId(sku.getSkuId());
            detailDO.setSkuCode(sku.getSkuCode());
            detailDO.setRealQty(sku.getPlanQty());
            coreOrderDetailDOList.add(detailDO);
        }
        coreChannelOrderDO.setOrderDetailDOs(coreOrderDetailDOList);
        return coreChannelOrderDO;
    }

    /**
     * 查询出库单编码跟前置单的关系map
     * key为出库单id，value为对应前置单外码【这里为订单编码】的list
     *
     * @param orderCode
     * @param warehouseRecordIds
     * @return
     */
    private Map queryFrSaleFrontAndWarehouseRelation(String orderCode, List<Long> warehouseRecordIds) {
        return frSaleRepository.queryFrSaleFrontAndWarehouseRelation(orderCode, warehouseRecordIds);
    }


    /**
     * 集合查询取交集
     * @param idsOne
     * @param idsTwo
     * @param idsThree
     * @return
     */
    private List<String> retainAll(List<String> idsOne, List<String> idsTwo, List<String> idsThree, List<String> idsFour, List<String> idsFive) {
        List<String> idsAll = Lists.newArrayList();
        if ( !CollectionUtils.isEmpty(idsOne) || !CollectionUtils.isEmpty(idsTwo) || !CollectionUtils.isEmpty(idsThree) || !CollectionUtils.isEmpty(idsFour) || !CollectionUtils.isEmpty(idsFive)) {
            if (!CollectionUtils.isEmpty(idsOne)) {
                idsAll = idsOne;
            } else if (!CollectionUtils.isEmpty(idsTwo)) {
                idsAll = idsTwo;
            } else if (!CollectionUtils.isEmpty(idsThree)) {
                idsAll = idsThree;
            } else if (!CollectionUtils.isEmpty(idsFour)) {
                idsAll = idsFour;
            } else if (!CollectionUtils.isEmpty(idsFive)) {
                idsAll = idsFive;
            } else {
                return idsAll;
            }
            if (!CollectionUtils.isEmpty(idsOne)) {
                idsAll.retainAll(idsOne);
            }
            if (!CollectionUtils.isEmpty(idsTwo)) {
                idsAll.retainAll(idsTwo);
            }
            if (!CollectionUtils.isEmpty(idsThree)) {
                idsAll.retainAll(idsThree);
            }
            if (!CollectionUtils.isEmpty(idsFour)) {
                idsAll.retainAll(idsFour);
            }
            if (!CollectionUtils.isEmpty(idsFive)) {
                idsAll.retainAll(idsFive);
            }
        }
        return idsAll;
    }

    /**
     * 根据门店销售出库单查询详情
     *
     * @param warehouseRecordId
     * @return
     */
    @Override
    public SaleWarehouseRecordDTO querySaleWarehouseRecordInfoById(Long warehouseRecordId) {
        WarehouseRecordE record = warehouseRecordRepository.getRecordWithDetailById(warehouseRecordId);
        Long realWarehouseId = record.getRealWarehouseId();
        List<WarehouseRecordDetail> warehouseRecordDetails = record.getWarehouseRecordDetails();
        SaleWarehouseRecordDTO result = shopRetailWarehouseConvertor.retailEntityToPageDto(record);
        List<SaleWarehouseRecordDetailDTO> details = shopRetailWarehouseConvertor.retailEntityDetailListToPageDtoDetailList(warehouseRecordDetails);
        RealWarehouse realWarehouse = realWarehouseService.findByRealWarehouseId(realWarehouseId);
        if (Objects.nonNull(realWarehouse)){
            result.setRealWarehouseName(realWarehouse.getRealWarehouseName());
            result.setRealWarehouseAddress(realWarehouse.getRealWarehouseAddress());
            result.setRealWarehouseCode(realWarehouse.getRealWarehouseCode());
        }
        result.setRecordTypeName(WarehouseRecordTypeVO.getDescByType(record.getRecordType()));
        result.setRecordStatusName(WarehouseRecordStatusVO.getDescByType(record.getRecordStatus()));
        if (StringUtils.isNotBlank(result.getChannelCode())) {
            try {
                //第三方接口异常，只打印错误日志，不影响主流程
                result.setChannelCodeName(channelFacade.queryByChannelcode(result.getChannelCode()).getChannelName());
            } catch (Exception e) {
                log.error(e.getMessage(), e);
            }
        }
        //查询批次信息
        List<BatchStockChangeFlowDO> batchInfoList = batchStockRepository.queryBatchStockChangeFlowByRecordCode(record.getRecordCode());
        Map<Long, List<BatchStockChangeFlowDO>> batchInfoMap = RomeCollectionUtil.listforListMap(batchInfoList, "skuId");
        //查询商品名称信息
        List<SkuInfoExtDTO> skuInfoList = null;
        try {
            //第三方接口异常 ，只打印错误日志，不影响主流程
            skuInfoList = skuFacade.skusBySkuId(RomeCollectionUtil.getValueList(details, "skuId"));
        } catch (Exception e) {
            log.error(e.getMessage(), e);
        }
        Map<Long, SkuInfoExtDTO> skuInfoMap = RomeCollectionUtil.listforMap(skuInfoList, "id");

        for (SaleWarehouseRecordDetailDTO detail : details) {
            Long skuId = detail.getSkuId();
            if (Objects.nonNull(realWarehouse)) {
                detail.setRealWarehouseName(realWarehouse.getRealWarehouseName());
            }
            if (skuInfoMap.containsKey(skuId)) {
                detail.setSkuName(skuInfoMap.get(skuId).getName());
            }
            if (batchInfoMap.containsKey(skuId)) {
                detail.setBatchStockChangeFlowList(batchInfoMap.get(skuId));
            }
        }
        result.setDetails(details);
        if(WarehouseRecordTypeVO.ONLINE_RETAILERS_OUT_RECORD.getType().equals(record.getRecordType())
                ||WarehouseRecordTypeVO.WDT_ONLINE_RETAILERS_OUT_RECORD.getType().equals(record.getRecordType())){
            List<RwRecordPoolE> poolList = rwRecordPoolRepository.queryKeysWithDetailsByWarehouseId(record.getId());
            if(CollectionUtil.isNotEmpty(poolList)){
                String frontRecordCode = poolList.get(0).getFrontRecordCode();
                FrSaleWdtExtE frSaleWdtExtE = frSaleWdtExtMapper.queryExtInfoByRecordCode(frontRecordCode);
                if(frSaleWdtExtE != null){
                    result.setSellerMessage(frSaleWdtExtE.getRemark());
                }
            }

        }

        return result;
    }


    /**
     * 根据团购发货出库单查询详情
     * @param groupWarehouseRecordCondition
     * @return
     */
    @Override
    public GroupWarehouseRecordDTO queryGroupWarehouseRecordInfoByRecordId(GroupWarehouseRecordCondition groupWarehouseRecordCondition) {
        WarehouseRecordE record = warehouseRecordRepository.queryWarehouseRecordById(groupWarehouseRecordCondition.getRecordId());
        Long realWarehouseId = record.getRealWarehouseId();
        GroupWarehouseRecordDTO result = shopRetailWarehouseConvertor.retailWarehouseGroupEntityToDto(record);
        Page page = PageHelper.startPage(groupWarehouseRecordCondition.getPageIndex(), groupWarehouseRecordCondition.getPageSize());
        List<SaleWarehouseRecordDetailDTO> details = shopRetailWarehouseConvertor.retailEntityDetailListToPageDtoDetailList(warehouseRecordRepository.queryDetailListByRecordId(groupWarehouseRecordCondition.getRecordId()));

        RealWarehouse realWarehouse = realWarehouseService.findByRealWarehouseId(realWarehouseId);
        result.setRealWarehouseName(realWarehouse.getRealWarehouseName());
        result.setRealWarehouseAddress(realWarehouse.getRealWarehouseAddress());
        result.setRealWarehouseCode(realWarehouse.getRealWarehouseCode());
        result.setRecordTypeName(WarehouseRecordTypeVO.getDescByType(record.getRecordType()));
        result.setRecordStatusName(WarehouseRecordStatusVO.getDescByType(record.getRecordStatus()));
        FrontWarehouseRecordRelationDO frontWarehouseRecordRelationDO=warehouseRecordRepository.getFrontWarehouseRecordRelationByWrId(record.getId());
        if(frontWarehouseRecordRelationDO!=null){
            result.setOutRecordCode(frontWarehouseRecordRelationDO.getFrontRecordCode());
            AddressE addressE=addressRepository.queryByRecordCode(frontWarehouseRecordRelationDO.getFrontRecordCode());
            if(null != addressE){
                result.setAddress(addressE.getAddress());
            }
            ReservationRecordE reservationRecordE=frReservationRepository.queryFrReservationByCode(frontWarehouseRecordRelationDO.getFrontRecordCode());
            if(reservationRecordE !=null){
                result.setOutCreateTime(reservationRecordE.getOutCreateTime());
                result.setExpectReceiveDateStart(reservationRecordE.getExpectReceiveDateStart());
                result.setUserCode(reservationRecordE.getUserCode());
            }
        }
        //查询商品名称信息
        List<SkuInfoExtDTO> skuInfoList = null;
        try {
            //第三方接口异常 ，只打印错误日志，不影响主流程
            skuInfoList = skuFacade.skusBySkuId(RomeCollectionUtil.getValueList(details, "skuId"));
        } catch (Exception e) {
            log.error(e.getMessage(), e);
        }
        Map<Long, SkuInfoExtDTO> skuInfoMap = RomeCollectionUtil.listforMap(skuInfoList, "id");

        for (SaleWarehouseRecordDetailDTO detail : details) {
            Long skuId = detail.getSkuId();
            detail.setRealWarehouseName(realWarehouse.getRealWarehouseName());
            if (skuInfoMap.containsKey(skuId)) {
                detail.setSkuName(skuInfoMap.get(skuId).getName());
            }
        }
        result.setDetails(details);
        result.setTotal(page.getTotal());
        return result;
    }


    /**
     * 查询销售出库单列表，历史数据
     * @param condition
     * @return
     */
    @Override
    public PageInfo<SaleWarehouseRecordDTO> queryWarehouseRecordListHistory(SaleWarehouseRecordCondition condition) {
    	// 校验
    	validateDateByHistory(condition);
    	EsIndexTypeConfig.setIndexNameSuffixByDate("1", condition.getStartTime());
        try {
        	// 仓库编码
            if (StringUtils.isNotBlank(condition.getRealWarehouseCode())) {
                RealWarehouse warehouse = realWarehouseService.queryRealWarehouseByInCode(condition.getRealWarehouseCode());
                if (warehouse != null) {
                    condition.setRealWarehouseId(warehouse.getId());
                } else {
                    //用户输入仓库编码不存在，则直接返回无结果
                    PageInfo<SaleWarehouseRecordDTO> personPageInfo = new PageInfo<>(new ArrayList<>());
                    personPageInfo.setTotal(0);
                    return personPageInfo;
                }
            }
            // 请求参数
            NativeSearchQuery searchQuery = getQueryWarehouseRecordListHistoryParam(condition);
    		org.springframework.data.domain.Page<EsFrRecordDO> resultPage = esWarehouseRecordRepository.search(searchQuery);
            if(resultPage == null || resultPage.getContent() == null || resultPage.getContent().size() == 0) {
            	PageInfo<SaleWarehouseRecordDTO> personPageInfo = new PageInfo<>(new ArrayList<>());
                personPageInfo.setTotal(0);
                return personPageInfo;
            }
            List<SaleWarehouseRecordDTO> result = new ArrayList<>(resultPage.getContent().size());
            for(EsFrRecordDO dto : resultPage.getContent()) {
            	if(dto.getWarehouseRecordDOs() != null && dto.getWarehouseRecordDOs().size() > 0) {
            		result.addAll(esFrRecordConvertor.warehouseRecordToDTOList(dto.getWarehouseRecordDOs()));
            	}
            }
            PageInfo<SaleWarehouseRecordDTO> recordDTOPageInfo = new PageInfo<>(result);
            recordDTOPageInfo.setTotal(resultPage.getTotalElements());

            // 合并其他数据
            if (!CollectionUtils.isEmpty(recordDTOPageInfo.getList())) {
            	combineDataQueryWarehouseRecordHistory(recordDTOPageInfo.getList());
            }
            return recordDTOPageInfo;
		} catch (IndexNotFoundException e) {
			log.error("查询历史数据", e);
			throw new RomeException(ResCode.STOCK_ERROR_1002, "参数有误,该月,无法查询到数据");
		} finally {
			EsIndexTypeConfig.clearIndexNameSuffix();
		}
    }
    
    /**
     * 根据销售出库单查询详情，历史数据
     * @param recordCode
     * @param date
     * @return
     */
    @Override
    public SaleWarehouseRecordDTO querySaleWarehouseRecordInfoByRecordCodeHistory(String recordCode,Date date) {
    	if (StringUtils.isBlank(recordCode)) {
    		throw new RomeException(ResCode.STOCK_ERROR_1002, "单据号为空");
    	}
    	if(date != null) {
    		EsIndexTypeConfig.setIndexNameSuffixByDate("1", date);
    	}else {
    		EsIndexTypeConfig.setIndexNameSuffix("");
    	}
    	try {
    		Iterable<EsFrRecordDO> kkk = esWarehouseRecordRepository.search(QueryBuilders.nestedQuery("warehouseRecordDOs", 
    				QueryBuilders.boolQuery().must(QueryBuilders.termQuery("warehouseRecordDOs.recordCode.keyWord", recordCode)), ScoreMode.Total));
        	if(kkk == null) {
        		throw new RomeException(ResCode.STOCK_ERROR_1002, "要查的单据不存在");
        	}
        	Iterator<EsFrRecordDO> iterator = kkk.iterator();
        	if(!iterator.hasNext()) {
        		throw new RomeException(ResCode.STOCK_ERROR_1002, "要查的单据不存在");
        	}
        	EsFrRecordDO frRecordDO = iterator.next();
        	List<EsWarehouseRecordDO> list = frRecordDO.getWarehouseRecordDOs();
        	EsWarehouseRecordDO record = null;
        	for(EsWarehouseRecordDO dto : list) {
        		if(recordCode.equals(dto.getRecordCode())) {
        			record = dto;
        		}
        	}
            Long realWarehouseId = record.getRealWarehouseId();
            SaleWarehouseRecordDTO result = esFrRecordConvertor.warehouseRecordToDTO(record);
            List<SaleWarehouseRecordDetailDTO> details = esFrRecordConvertor.warehouseRecordDetailToDTOList(record.getDetailDOs());
            RealWarehouse realWarehouse = realWarehouseService.findByRealWarehouseId(realWarehouseId);
            result.setRealWarehouseName(realWarehouse.getRealWarehouseName());
            result.setRealWarehouseAddress(realWarehouse.getRealWarehouseAddress());
            result.setRealWarehouseCode(realWarehouse.getRealWarehouseCode());
            result.setRecordTypeName(WarehouseRecordTypeVO.getDescByType(record.getRecordType()));
            result.setRecordStatusName(WarehouseRecordStatusVO.getDescByType(record.getRecordStatus()));
            if (StringUtils.isNotBlank(result.getChannelCode())) {
                try {
                    //第三方接口异常，只打印错误日志，不影响主流程
                    result.setChannelCodeName(channelFacade.queryByChannelcode(result.getChannelCode()).getChannelName());
                } catch (Exception e) {
                    log.error(e.getMessage(), e);
                }
            }
            if(details != null) {
            	//查询批次信息
                List<BatchStockChangeFlowDO> batchInfoList = batchStockRepository.queryBatchStockChangeFlowByRecordCode(record.getRecordCode());
                Map<Long, List<BatchStockChangeFlowDO>> batchInfoMap = RomeCollectionUtil.listforListMap(batchInfoList, "skuId");
                //查询商品名称信息
                List<SkuInfoExtDTO> skuInfoList = null;
                try {
                    //第三方接口异常 ，只打印错误日志，不影响主流程
                    skuInfoList = skuFacade.skusBySkuId(RomeCollectionUtil.getValueList(details, "skuId"));
                } catch (Exception e) {
                    log.error(e.getMessage(), e);
                }
                Map<Long, SkuInfoExtDTO> skuInfoMap = RomeCollectionUtil.listforMap(skuInfoList, "id");

                for (SaleWarehouseRecordDetailDTO detail : details) {
                    Long skuId = detail.getSkuId();
                    detail.setRealWarehouseName(realWarehouse.getRealWarehouseName());
                    if (skuInfoMap.containsKey(skuId)) {
                        detail.setSkuName(skuInfoMap.get(skuId).getName());
                    }
                    if (batchInfoMap.containsKey(skuId)) {
                        detail.setBatchStockChangeFlowList(batchInfoMap.get(skuId));
                    }
                }
                result.setDetails(details);
            }
            return result;
		} finally {
			EsIndexTypeConfig.clearIndexNameSuffix();
		}
    }

    @Override
    public PageInfo<GroupWarehouseRecordDTO> queryGroupWarehouseRecordList(GroupWarehouseRecordCondition condition) {
        if (StringUtils.isNotBlank(condition.getOrderCode())) {
            List<FrontWarehouseRecordRelationE> relationDOS=frontWarehouseRecordRelationRepository.getRecordRelationByFrontRecordCodes(Arrays.asList(condition.getOrderCode()));
            if(CollectionUtils.isEmpty(relationDOS)){
                //用户输入的订单 编号不存在，则直接返回无结果
                PageInfo<GroupWarehouseRecordDTO> personPageInfo = new PageInfo<>(new ArrayList<>());
                personPageInfo.setTotal(0);
                return personPageInfo;
            }
            List<Long> ids=relationDOS.stream().distinct().map(FrontWarehouseRecordRelationE::getWarehouseRecordId).distinct().collect(Collectors.toList());
            condition.setWarehouseRecordIds(ids);
        }
        //团购出库单类型
		condition.setRecordType(WarehouseRecordTypeVO.RESERVATION_DO_RECORD.getType());
        //2.带条件分页查询
        if (StringUtils.isNotBlank(condition.getRealWarehouseCode())) {
            RealWarehouse warehouse = realWarehouseService.queryRealWarehouseByInCode(condition.getRealWarehouseCode());
            if (warehouse != null) {
                condition.setRealWarehouseId(warehouse.getId());
            } else {
                //用户输入仓库编码不存在，则直接返回无结果
                PageInfo<GroupWarehouseRecordDTO> personPageInfo = new PageInfo<>(new ArrayList<>());
                personPageInfo.setTotal(0);
                return personPageInfo;
            }
        }
        Page page = PageHelper.startPage(condition.getPageIndex(), condition.getPageSize());
        List<GroupWarehouseRecordDTO> result = warehouseRecordRepository.queryGroupWarehouseRecordList(condition);
        PageInfo<GroupWarehouseRecordDTO> recordDTOPageInfo = new PageInfo<>(result);
        recordDTOPageInfo.setTotal(page.getTotal());
        //3、将出库单关联的订单编号赋值给页面dto,实仓名称赋值给页面DTO
        if (!CollectionUtils.isEmpty(recordDTOPageInfo.getList())) {
            List<Long> rwIds = RomeCollectionUtil.getValueList(recordDTOPageInfo.getList(),"realWarehouseId");
            rwIds = rwIds.stream().distinct().collect(Collectors.toList());
            List<RealWarehouse> realWarehouseList = realWarehouseService.findByRealWarehouseIds(rwIds);
            Map<Long, String> realWarehouseServiceMap = RomeCollectionUtil.listforMap(realWarehouseList, "id", "realWarehouseName");
            Map<Long, String> realWarehouseCodeMap = RomeCollectionUtil.listforMap(realWarehouseList, "id", "realWarehouseCode");
            Map<Long, String> factorCodeMap = RomeCollectionUtil.listforMap(realWarehouseList, "id", "factoryCode");
            List<Long> warehouseRecordIds=recordDTOPageInfo.getList().stream().distinct().map(GroupWarehouseRecordDTO::getId).distinct().collect(Collectors.toList());
            List<FrontWarehouseRecordRelationDO> frontWarehouseRecordRelationDOList=warehouseRecordRepository.getFrontWarehouseRecordRelationByWrIds(warehouseRecordIds);
            Map<Long, String> relation=frontWarehouseRecordRelationDOList.stream().distinct().collect(Collectors.toMap(FrontWarehouseRecordRelationDO:: getWarehouseRecordId, FrontWarehouseRecordRelationDO:: getFrontRecordCode, (v1, v2) -> v1));

            //前置单号
            for (GroupWarehouseRecordDTO dto : recordDTOPageInfo.getList()) {
                String frontRecordCode=relation.get(dto.getId());
                if(frontRecordCode !=null){
                    ReservationRecordE reservationRecordE=frReservationRepository.queryFrReservationByCode(frontRecordCode);
                    if(reservationRecordE !=null){
                        dto.setOutCreateTime(reservationRecordE.getOutCreateTime());
                        dto.setExpectReceiveDateStart(reservationRecordE.getExpectReceiveDateStart());
                        dto.setUserCode(reservationRecordE.getUserCode());
                    }
                }
                dto.setOutRecordCode(relation.get(dto.getId()));
                dto.setRecordStatusName(WarehouseRecordStatusVO.getDescByType(dto.getRecordStatus()));
                dto.setRecordTypeName(WarehouseRecordTypeVO.getDescByType(dto.getRecordType()));
                dto.setRealWarehouseName(realWarehouseServiceMap.get(dto.getRealWarehouseId()));
                dto.setFactoryCode(factorCodeMap.get(dto.getRealWarehouseId()));
                dto.setRealWarehouseCode(realWarehouseCodeMap.get(dto.getRealWarehouseId()));
            }
        }
        return recordDTOPageInfo;
    }



    @Override
    public void addOrUpdateExpressCode(GroupAddExpressCodeDTO groupAddExpressCodeDTO) {
        this.addOrUpdateExpressCodeValid(groupAddExpressCodeDTO);
        WarehouseRecordE warehouseRecordE=warehouseRecordRepository.queryWarehouseRecordByRecordCode(groupAddExpressCodeDTO.getWarehouseCode());
        AlikAssert.isNotNull(warehouseRecordE, ResCode.STOCK_ERROR_1002, "出库单据不存在");
        if(!warehouseRecordE.getRecordType().equals(WarehouseRecordTypeVO.RESERVATION_DO_RECORD.getType())){
            throw new RomeException(ResCode.STOCK_ERROR_1002, "单据类型不是销售中心团购仓出库单");
        }
        if(warehouseRecordE.getRecordStatus().equals(WarehouseRecordStatusVO.DISABLED.getStatus()) || warehouseRecordE.getRecordStatus().equals(WarehouseRecordStatusVO.OUT_ALLOCATION.getStatus()) ){
            throw new RomeException(ResCode.STOCK_ERROR_1002, "单据已取消或已发货,不能更新运单号");
        }
        //更新派车单号
        int j=warehouseRecordRepository.updateTmsRecordCodeByRecordCode(warehouseRecordE.getRecordCode(),groupAddExpressCodeDTO.getExpressCode());
        if(j==0){
            throw new RomeException(ResCode.STOCK_ERROR_1001, "更新派车单号失败");
        }
    }

    @Override
    public List<GroupWarehouseRecordTemplate> exportGroupWarehouseRecord(GroupWarehouseRecordCondition condition) {
        if (StringUtils.isNotBlank(condition.getOrderCode())) {
            List<FrontWarehouseRecordRelationE> relationDOS=frontWarehouseRecordRelationRepository.getRecordRelationByFrontRecordCodes(Arrays.asList(condition.getOrderCode()));
            if(CollectionUtils.isEmpty(relationDOS)){
                //用户输入的订单 编号不存在，则直接返回无结果
                List<GroupWarehouseRecordTemplate> personPageInfo =new ArrayList<>();
                return personPageInfo;
            }
            List<Long> ids=relationDOS.stream().distinct().map(FrontWarehouseRecordRelationE::getWarehouseRecordId).distinct().collect(Collectors.toList());
            condition.setWarehouseRecordIds(ids);
        }
        //团购出库单类型
        condition.setRecordType(WarehouseRecordTypeVO.RESERVATION_DO_RECORD.getType());
        //2.带条件分页查询
        if (StringUtils.isNotBlank(condition.getRealWarehouseCode())) {
            RealWarehouse warehouse = realWarehouseService.queryRealWarehouseByInCode(condition.getRealWarehouseCode());
            if (warehouse != null) {
                condition.setRealWarehouseId(warehouse.getId());
            } else {
                //用户输入仓库编码不存在，则直接返回无结果
                List<GroupWarehouseRecordTemplate> personPageInfo =new ArrayList<>();
                return personPageInfo;
            }
        }
        int count=warehouseRecordRepository.queryGroupWarehouseRecordCodeList(condition);
        if(count>1000){
            throw new RomeException(ResCode.STOCK_ERROR_1001, "导出数据量不能大于1000");
        }
        List<GroupWarehouseRecordTemplate> resultList = warehouseRecordRepository.queryGroupWarehouseRecordTemplateList(condition);
        //3、将出库单关联的订单编号赋值给页面dto,实仓名称赋值给页面DTO
        if (!CollectionUtils.isEmpty(resultList)) {
            List<Long> rwIds = RomeCollectionUtil.getValueList(resultList,"realWarehouseId");
            rwIds = rwIds.stream().distinct().collect(Collectors.toList());
            List<RealWarehouse> realWarehouseList = realWarehouseService.findByRealWarehouseIds(rwIds);
            Map<Long, String> realWarehouseServiceMap = RomeCollectionUtil.listforMap(realWarehouseList, "id", "realWarehouseName");
            Map<Long, String> realWarehouseCodeMap = RomeCollectionUtil.listforMap(realWarehouseList, "id", "realWarehouseCode");
            Map<Long, String> factorCodeMap = RomeCollectionUtil.listforMap(realWarehouseList, "id", "factoryCode");
            List<Long> warehouseRecordIds=resultList.stream().distinct().map(GroupWarehouseRecordTemplate::getId).distinct().collect(Collectors.toList());
            List<FrontWarehouseRecordRelationDO> frontWarehouseRecordRelationDOList=warehouseRecordRepository.getFrontWarehouseRecordRelationByWrIds(warehouseRecordIds);
            Map<Long, String> relation=frontWarehouseRecordRelationDOList.stream().distinct().collect(Collectors.toMap(FrontWarehouseRecordRelationDO:: getWarehouseRecordId, FrontWarehouseRecordRelationDO:: getFrontRecordCode, (v1, v2) -> v1));

            //前置单号
            for (GroupWarehouseRecordTemplate dto : resultList) {
                String frontRecordCode=relation.get(dto.getId());
                if(frontRecordCode !=null){
                    ReservationRecordE reservationRecordE=frReservationRepository.queryFrReservationByCode(frontRecordCode);
                    if(reservationRecordE !=null){
                        dto.setOutCreateTime(reservationRecordE.getOutCreateTime());
                        dto.setExpectReceiveDateStart(reservationRecordE.getExpectReceiveDateStart());
                        dto.setUserCode(reservationRecordE.getUserCode());
                    }
                }
                dto.setOutRecordCode(relation.get(dto.getId()));
                dto.setRecordStatusName(WarehouseRecordStatusVO.getDescByType(dto.getRecordStatus()));
                dto.setRecordTypeName(WarehouseRecordTypeVO.getDescByType(dto.getRecordType()));
                dto.setRealWarehouseName(realWarehouseServiceMap.get(dto.getRealWarehouseId()));
                dto.setFactoryCode(factorCodeMap.get(dto.getRealWarehouseId()));
                dto.setRealWarehouseCode(realWarehouseCodeMap.get(dto.getRealWarehouseId()));
            }
        }
        return resultList;
    }

    @Override
    public List<GroupWarehouseRecordDetailTemplate> exportGroupWarehouseRecordDetail(GroupWarehouseRecordCondition condition) {
        if (StringUtils.isNotBlank(condition.getOrderCode())) {
            List<FrontWarehouseRecordRelationE> relationDOS=frontWarehouseRecordRelationRepository.getRecordRelationByFrontRecordCodes(Arrays.asList(condition.getOrderCode()));
            if(CollectionUtils.isEmpty(relationDOS)){
                //用户输入的订单 编号不存在，则直接返回无结果
                List<GroupWarehouseRecordDetailTemplate> personPageInfo =new ArrayList<>();
                return personPageInfo;
            }
            List<Long> ids=relationDOS.stream().distinct().map(FrontWarehouseRecordRelationE::getWarehouseRecordId).distinct().collect(Collectors.toList());
            condition.setWarehouseRecordIds(ids);
        }
        //团购出库单类型
        condition.setRecordType(WarehouseRecordTypeVO.RESERVATION_DO_RECORD.getType());
        //2.带条件分页查询
        if (StringUtils.isNotBlank(condition.getRealWarehouseCode())) {
            RealWarehouse warehouse = realWarehouseService.queryRealWarehouseByInCode(condition.getRealWarehouseCode());
            if (warehouse != null) {
                condition.setRealWarehouseId(warehouse.getId());
            } else {
                //用户输入仓库编码不存在，则直接返回无结果
                List<GroupWarehouseRecordDetailTemplate> personPageInfo =new ArrayList<>();
                return personPageInfo;
            }
        }
        int count=warehouseRecordRepository.queryGroupWarehouseRecordCodeList(condition);
        if(count>1000){
            throw new RomeException(ResCode.STOCK_ERROR_1001, "导出数据量不能大于1000");
        }
        List<GroupWarehouseRecordDetailTemplate> resultList = warehouseRecordRepository.queryGroupWarehouseRecordDetailTemplateList(condition);
        List<Long> warehouseRecordList=resultList.stream().distinct().map(GroupWarehouseRecordDetailTemplate::getId).collect(Collectors.toList());
        List<WarehouseRecordDetail> detailList=warehouseRecordRepository.queryDetailListByRecordIds(warehouseRecordList);
        //查询详情
        Map<String,List<WarehouseRecordDetail>> warehouseRecordDetailMap=detailList.stream().collect(Collectors.groupingBy(WarehouseRecordDetail::getRecordCode));
        //查询商品名称信息
        List<SkuInfoExtDTO> skuInfoList = null;
        try {
            //第三方接口异常 ，只打印错误日志，不影响主流程
            skuInfoList = skuFacade.skusBySkuId(RomeCollectionUtil.getValueList(detailList, "skuId"));
        } catch (Exception e) {
            log.error(e.getMessage(), e);
        }
        Map<Long, SkuInfoExtDTO> skuInfoMap = RomeCollectionUtil.listforMap(skuInfoList, "id");
        List<GroupWarehouseRecordDetailTemplate> res=new ArrayList<>();
        //3、将出库单关联的订单编号赋值给页面dto,实仓名称赋值给页面DTO
        if (!CollectionUtils.isEmpty(resultList)) {
            List<Long> rwIds = RomeCollectionUtil.getValueList(resultList,"realWarehouseId");
            rwIds = rwIds.stream().distinct().collect(Collectors.toList());
            List<RealWarehouse> realWarehouseList = realWarehouseService.findByRealWarehouseIds(rwIds);
            Map<Long, String> realWarehouseServiceMap = RomeCollectionUtil.listforMap(realWarehouseList, "id", "realWarehouseName");
            Map<Long, String> realWarehouseCodeMap = RomeCollectionUtil.listforMap(realWarehouseList, "id", "realWarehouseCode");
            Map<Long, String> factorCodeMap = RomeCollectionUtil.listforMap(realWarehouseList, "id", "factoryCode");
            List<Long> warehouseRecordIds=resultList.stream().distinct().map(GroupWarehouseRecordDetailTemplate::getId).distinct().collect(Collectors.toList());
            List<FrontWarehouseRecordRelationDO> frontWarehouseRecordRelationDOList=warehouseRecordRepository.getFrontWarehouseRecordRelationByWrIds(warehouseRecordIds);
            Map<Long, String> relation=frontWarehouseRecordRelationDOList.stream().distinct().collect(Collectors.toMap(FrontWarehouseRecordRelationDO:: getWarehouseRecordId, FrontWarehouseRecordRelationDO:: getFrontRecordCode, (v1, v2) -> v1));
            //前置单号
            for (GroupWarehouseRecordDetailTemplate dto : resultList) {
                String frontRecordCode=relation.get(dto.getId());
                if(frontRecordCode !=null){
                    ReservationRecordE reservationRecordE=frReservationRepository.queryFrReservationByCode(frontRecordCode);
                    if(reservationRecordE !=null){
                        dto.setOutCreateTime(reservationRecordE.getOutCreateTime());
                        dto.setExpectReceiveDateStart(reservationRecordE.getExpectReceiveDateStart());
                        dto.setUserCode(reservationRecordE.getUserCode());
                    }
                }
                dto.setOutRecordCode(relation.get(dto.getId()));
                dto.setRecordStatusName(WarehouseRecordStatusVO.getDescByType(dto.getRecordStatus()));
                dto.setRecordTypeName(WarehouseRecordTypeVO.getDescByType(dto.getRecordType()));
                dto.setRealWarehouseName(realWarehouseServiceMap.get(dto.getRealWarehouseId()));
                dto.setFactoryCode(factorCodeMap.get(dto.getRealWarehouseId()));
                dto.setRealWarehouseCode(realWarehouseCodeMap.get(dto.getRealWarehouseId()));
                //填充出库单明细
                List<WarehouseRecordDetail> warehouseRecordDetailList=warehouseRecordDetailMap.get(dto.getRecordCode());
                for(WarehouseRecordDetail warehouseRecordDetail:warehouseRecordDetailList){
                    GroupWarehouseRecordDetailTemplate groupWarehouseRecordDetailTemplate=new GroupWarehouseRecordDetailTemplate();
                    BeanUtils.copyProperties(dto,groupWarehouseRecordDetailTemplate);
                    groupWarehouseRecordDetailTemplate.setSkuCode(warehouseRecordDetail.getSkuCode());
                    groupWarehouseRecordDetailTemplate.setSkuId(warehouseRecordDetail.getSkuId());
                    groupWarehouseRecordDetailTemplate.setActualQty(warehouseRecordDetail.getActualQty());
                    groupWarehouseRecordDetailTemplate.setPlanQty(warehouseRecordDetail.getPlanQty());
                    SkuInfoExtDTO skuInfoExtDTO=skuInfoMap.get(warehouseRecordDetail.getSkuId());
                    if(null !=skuInfoExtDTO){
                        groupWarehouseRecordDetailTemplate.setSkuName(skuInfoExtDTO.getName());
                        groupWarehouseRecordDetailTemplate.setUnit(skuInfoExtDTO.getSpuUnitName());
                    }
                    res.add(groupWarehouseRecordDetailTemplate);
                }
            }
        }
        return res;
    }

    @Override
    public List<PrintItem> groupWarehousePrintDetail(String recordCode) {
        WarehouseRecordE warehouseRecordE=warehouseRecordRepository.getRecordWithDetailByCode(recordCode,WarehouseRecordTypeVO.RESERVATION_DO_RECORD.getType());
        if(warehouseRecordE == null){
            throw new RomeException(ResCode.STOCK_ERROR_1002, "出库单号不存在");
        }
        List<PrintItem> resList=new ArrayList<>();
        GroupWarehousePrintDTO groupWarehousePrint=new GroupWarehousePrintDTO();
        groupWarehousePrint.setRecordCode(warehouseRecordE.getRecordCode());
        groupWarehousePrint.setCreateTime(warehouseRecordE.getCreateTime());
        RealWarehouse realWarehouse=realWarehouseService.findByRealWarehouseId(warehouseRecordE.getRealWarehouseId());
        if(null != realWarehouse){
            groupWarehousePrint.setWarehouseName(realWarehouse.getRealWarehouseName());
        }
        FrontWarehouseRecordRelationDO frontWarehouseRecordRelationDO=warehouseRecordRepository.getFrontWarehouseRecordRelationByWrId(warehouseRecordE.getId());
        if(frontWarehouseRecordRelationDO!=null) {
            AddressE addressE = addressRepository.queryByRecordCode(frontWarehouseRecordRelationDO.getFrontRecordCode());
            if (null != addressE) {
                groupWarehousePrint.setAddress(addressE.getAddress());
            }
            FrReservationDO frReservationDO=frReservationRepository.selectRecordById(frontWarehouseRecordRelationDO.getFrontRecordId());
            if(null !=frReservationDO){
                groupWarehousePrint.setMobile(frReservationDO.getMobile());
                groupWarehousePrint.setOutCreateTime(frReservationDO.getOutCreateTime());
                groupWarehousePrint.setUserCode(frReservationDO.getUserCode());
                groupWarehousePrint.setDeliveryTime(frReservationDO.getExpectReceiveDateStart());
                groupWarehousePrint.setUserCode(frReservationDO.getUserCode());
            }
        }
        List<WarehouseRecordDetail> detailList =warehouseRecordE.getWarehouseRecordDetails();
        //查询商品名称信息
        List<SkuInfoExtDTO> skuInfoList = null;
        try {
            //第三方接口异常 ，只打印错误日志，不影响主流程
            skuInfoList = skuFacade.skusBySkuId(RomeCollectionUtil.getValueList(detailList, "skuId"));
        } catch (Exception e) {
            log.error(e.getMessage(), e);
        }
        //查询sku信息
        Map<Long, SkuInfoExtDTO> skuInfoMap = RomeCollectionUtil.listforMap(skuInfoList, "id");
        //查询sku箱单位
        List<SkuUnitParamExtDTO> paramList=new ArrayList<>();
        for(SkuInfoExtDTO skuInfoExtDTO:skuInfoList){
            SkuUnitParamExtDTO skuUnitParamExtDTO=new SkuUnitParamExtDTO();
            skuUnitParamExtDTO.setSkuId(skuInfoExtDTO.getId());
            //箱单位
            skuUnitParamExtDTO.setUnitCode("KAR");
            paramList.add(skuUnitParamExtDTO);
        }
        //查单位
        List<SkuUnitExtDTO> skuUnitExtDTOList=null;
        try {
            //第三方接口异常 ，只打印错误日志，不影响主流程
            skuUnitExtDTOList = skuFacade.unitsBySkuIdAndUnitCode(paramList);
        } catch (Exception e) {
            log.error(e.getMessage(), e);
        }
        //查询sku单位信息
        Map<Long, SkuUnitExtDTO> skuUnitExtMap = RomeCollectionUtil.listforMap(skuUnitExtDTOList, "skuId");
        int unFoodCount=0,foodCount=0;
        //填充明细
        for(WarehouseRecordDetail detail :detailList){
            PrintItem printItem=new PrintItem();
            SkuInfoExtDTO skuInfoExtDTO=skuInfoMap.get(detail.getSkuId());
            if(skuInfoExtDTO!=null){
                printItem.setSkuName(skuInfoExtDTO.getName());
                if("Z001".equals(skuInfoExtDTO.getSpuType()) || "Z003".equals(skuInfoExtDTO.getSpuType())){
                    unFoodCount++;
                }else {
                    foodCount++;
                }
            }
            SkuUnitExtDTO skuUnitExtDTO=skuUnitExtMap.get(detail.getSkuId());
            if(skuUnitExtDTO!=null && skuUnitExtDTO.getScale()!=null){
                printItem.setCountQty(BigDecimal.ZERO.compareTo(detail.getActualQty())==0?BigDecimal.ZERO:detail.getActualQty().divide(skuUnitExtDTO.getScale(),1,BigDecimal.ROUND_DOWN));
                printItem.setSkuSpec(String.valueOf(skuUnitExtDTO.getScale()));
                printItem.setUnitName(skuUnitExtDTO.getUnitName());
            }
            printItem.setSkuQty(detail.getActualQty());
            printItem.setSkuCode(detail.getSkuCode());
            resList.add(printItem);
        }
        //数据包装返回
        for(PrintItem printItem:resList){
            printItem.setNonFoodTotal(unFoodCount);
            printItem.setCarTotal(foodCount);
            printItem.setCount(resList.size());
            printItem.setCustomAddress(groupWarehousePrint.getAddress());
            printItem.setCreateTime(groupWarehousePrint.getCreateTime()==null ? "":DateUtil.dateToString(groupWarehousePrint.getCreateTime(),DateUtil.NORM_DATETIME_PATTERN));
            printItem.setCustomMobile(groupWarehousePrint.getMobile());
            printItem.setDeliveryDate(groupWarehousePrint.getDeliveryTime()==null ? "": DateUtil.dateToString(groupWarehousePrint.getDeliveryTime(),DateUtil.NORM_DATE_PATTERN));
            printItem.setCustomName(groupWarehousePrint.getUserCode());
            printItem.setDoCode(groupWarehousePrint.getRecordCode());
            printItem.setFactoryName(groupWarehousePrint.getWarehouseName());
        }
        return resList;
    }

    @Override
    public List<BatchStockChangeFlowDTO> queryBatchInfoByRecordCode(String recordCode) {
        List<BatchStockChangeFlowDO> batchInfoList = batchStockRepository.queryBatchStockChangeFlowByRecordCode(recordCode);
        return  batchStockConvertor.doToDTO(batchInfoList);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public LockStockResp negativeLockStockAndReturn(OutWarehouseRecordDTO outWarehouseRecordDTO) {
        RealWarehouse realWarehouse =realWarehouseService.findByRealWarehouseOutCodeAndFactoryCode(outWarehouseRecordDTO.getWarehouseCode(),outWarehouseRecordDTO.getFactoryCode());
        AlikAssert.isNotNull(realWarehouse, "999", "当前仓库不存在");
        //幂等校验
        WarehouseRecordE warehouseRecordE = warehouseRecordRepository.queryWarehouseRecordByRecordCode(outWarehouseRecordDTO.getRecordCode());
        if(Objects.nonNull(warehouseRecordE)){
            return null;
        }
        if(!WarehouseRecordTypeVO.SHOP_RETAIL_WAREHOUSE_RECORD.getType().equals(outWarehouseRecordDTO.getRecordType())){
            throw new RomeException(ResCode.STOCK_ERROR_1002, "单据类型不是门店零售出库单");
        }
        ShopRetailWarehouseRecordE warehouseRecord = entityFactory.createEntity(ShopRetailWarehouseRecordE.class);
        //根据前置单生成出库单数据
        warehouseRecord.createOutRecordByFrontRecord(outWarehouseRecordDTO,realWarehouse.getId());
        warehouseRecord.setRecordStatus(WarehouseRecordStatusVO.OUT_ALLOCATION.getStatus());
        //需要处理批次库存
        warehouseRecord.setBatchStatus(WarehouseRecordBatchStatusVO.INIT.getStatus());
        //创建门店销售出库单
        warehouseRecord.addWarehouseRecord();
        List<DbRwStockChangeFlowDO> resList=new ArrayList<>();
        boolean isSuccess = false;
        CoreChannelOrderDO coreChannelOrderDO = null;
        CoreStockOpFactoryDO stockOpFactoryDO = new CoreStockOpFactoryDO();
        stockOpFactoryDO.createCoreRealStockOpDO();
        try {
            //减少门店实仓库存
            coreChannelOrderDO=convertOrderDOFromShopRetailRecordE(warehouseRecord);
            stockOpFactoryDO.reset(coreChannelOrderDO);
            coreChannelOrderDO = coreChannelSalesRepository.decreaseStockRetailOrO2O(coreChannelOrderDO);
            //获取负库存流水
            resList=StockChangeDataExtFacade.getChangeByDecreaseStockRetailOrO2O(coreChannelOrderDO);
            LockStockResp res=this.getNegativeLockRes(coreChannelOrderDO,resList);
            stockOpFactoryDO.commit();
            isSuccess = true;
            return res;
        } catch (RomeException e) {
            throw e;
        } catch (Exception e) {
            throw new RomeException(ResCode.STOCK_ERROR_1003, e.getMessage());
        } finally {
            if (!isSuccess) {
                RedisRollBackFacade.redisRollBack(coreChannelOrderDO);
            }
        }
    }

    @Resource
    private ShopFacade shopFacade;
    private boolean judgeShopIsDirectStore(String shopCode){
        StoreDTO store = shopFacade.searchByCode(shopCode);
        if("1".equals(store.getStoreProperties()) ||
                "2".equals(store.getStoreProperties()) ||
                "4".equals(store.getStoreProperties())) {
            return true;
        }
        return false;
    }
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void createShopReceiveInRecord(InWarehouseRecordDTO inWarehouseRecordDTO) {
        RealWarehouse realWarehouse =realWarehouseService.findByRealWarehouseOutCodeAndFactoryCode(inWarehouseRecordDTO.getWarehouseCode(),inWarehouseRecordDTO.getFactoryCode());
        AlikAssert.isNotNull(realWarehouse, "999", "当前仓库不存在");
        //幂等校验
        WarehouseRecordE warehouseRecordE = warehouseRecordRepository.queryWarehouseRecordByRecordCode(inWarehouseRecordDTO.getRecordCode());
        if(Objects.nonNull(warehouseRecordE)){
            return;
        }
        ShopRetailWarehouseRecordE warehouseRecord = entityFactory.createEntity(ShopRetailWarehouseRecordE.class);
        //根据前置单生成出库单数据
        warehouseRecord.createInRecordByFrontRecord(inWarehouseRecordDTO,realWarehouse.getId());
        warehouseRecord.setRecordStatus(WarehouseRecordStatusVO.IN_ALLOCATION.getStatus());
        warehouseRecord.setBatchStatus(WarehouseRecordBatchStatusVO.INIT.getStatus());
        log.info(inWarehouseRecordDTO.getRecordCode()+":创建门店领用入库单："+System.currentTimeMillis());
        if (judgeShopIsDirectStore(realWarehouse.getShopCode())) {
            //过账状态改为待过账
            warehouseRecord.setSyncTransferStatus(WmsSyncTransferStatusVO.NEED_TRANSFER.getStatus());
        }
        warehouseRecord.addWarehouseRecord();
        CoreRealStockOpDO coreRecordRealStockIncreaseDO = this.initIncreaseStockObj(warehouseRecord);
        boolean isSuccess = false;
        try {
            coreRealWarehouseStockRepository.increaseRealQty(coreRecordRealStockIncreaseDO);
            isSuccess = true;
        } catch (RomeException e) {
            throw e;
        } catch (Exception e) {
            throw new RomeException(ResCode.STOCK_ERROR_1003, e.getMessage());
        } finally {
            if (!isSuccess) {
                RedisRollBackFacade.redisRollBack(coreRecordRealStockIncreaseDO);
            }
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void createShopReceiveOutRecord(OutWarehouseRecordDTO outWarehouseRecordDTO) {
        RealWarehouse realWarehouse =realWarehouseService.findByRealWarehouseOutCodeAndFactoryCode(outWarehouseRecordDTO.getWarehouseCode(),outWarehouseRecordDTO.getFactoryCode());
        AlikAssert.isNotNull(realWarehouse, "999", "当前仓库不存在");
        WarehouseRecordE warehouseRecordE = warehouseRecordRepository.queryWarehouseRecordByRecordCode(outWarehouseRecordDTO.getRecordCode());
        if(Objects.nonNull(warehouseRecordE)){
            return;
        }
        ShopRetailWarehouseRecordE warehouseRecord = entityFactory.createEntity(ShopRetailWarehouseRecordE.class);
        warehouseRecord.createOutRecordByFrontRecord(outWarehouseRecordDTO,realWarehouse.getId());
        warehouseRecord.setRecordStatus(WarehouseRecordStatusVO.OUT_ALLOCATION.getStatus());
        //需要处理批次库存
        warehouseRecord.setBatchStatus(WarehouseRecordBatchStatusVO.INIT.getStatus());
        if (judgeShopIsDirectStore(realWarehouse.getShopCode())) {
            //过账状态改为待过账
            warehouseRecord.setSyncTransferStatus(WmsSyncTransferStatusVO.NEED_TRANSFER.getStatus());
        }
        warehouseRecord.addWarehouseRecord();
        boolean isSuccess = false;
        CoreRealStockOpDO decreaseRealStockOpDO = null;
        try {
            decreaseRealStockOpDO = warehouseRecord.initDecreaseStockObj(realWarehouse.getId(), false);
            coreRealWarehouseStockRepository.decreaseRealQty(decreaseRealStockOpDO);
            isSuccess = true;
        } catch (RomeException e) {
            throw e;
        } catch (Exception e) {
            throw new RomeException(ResCode.STOCK_ERROR_1003, e.getMessage());
        } finally {
            if (!isSuccess) {
                RedisRollBackFacade.redisRollBack(decreaseRealStockOpDO);
            }
        }
    }


    private LockStockResp getNegativeLockRes(CoreChannelOrderDO coreChannelOrderDO,List<DbRwStockChangeFlowDO> resList){
        LockStockResp lockStockResp=new LockStockResp();
        lockStockResp.setRecordCode(coreChannelOrderDO.getRecordCode());
        List<LockStockDetailResp> resDetailList=new ArrayList<>();
        Map<String, DbRwStockChangeFlowDO> dbRwStockChangeFlowDOMap = resList.stream().collect(Collectors.toMap(x -> x.getSkuCode(), Function.identity(),(v1,v2)->v2));
        List<WarehouseRecordDetail> warehouseRecordDetailList=warehouseRecordRepository.queryDetailListByRecordCode(coreChannelOrderDO.getRecordCode());
        for(WarehouseRecordDetail warehouseRecordDetail:warehouseRecordDetailList){
            LockStockDetailResp lockStockDetailResp=new LockStockDetailResp();
            lockStockDetailResp.setSkuCode(warehouseRecordDetail.getSkuCode());
            lockStockDetailResp.setLockQty(warehouseRecordDetail.getPlanQty());
            lockStockDetailResp.setPlanQty(warehouseRecordDetail.getPlanQty());
            lockStockDetailResp.setUnitCode(warehouseRecordDetail.getUnitCode());
            BigDecimal outLockQty=BigDecimal.ZERO;
            if(dbRwStockChangeFlowDOMap.containsKey(warehouseRecordDetail.getSkuCode())){
                DbRwStockChangeFlowDO dbRwStockChangeFlowDO=dbRwStockChangeFlowDOMap.get(warehouseRecordDetail.getSkuCode());
                if(dbRwStockChangeFlowDO.getBeforeChangeQty().compareTo(BigDecimal.ZERO)>0 && dbRwStockChangeFlowDO.getAfterChangeQty().compareTo(BigDecimal.ZERO)<=0){
                    outLockQty=dbRwStockChangeFlowDO.getAfterChangeQty().abs();
                }else if(dbRwStockChangeFlowDO.getBeforeChangeQty().compareTo(BigDecimal.ZERO)<=0 && dbRwStockChangeFlowDO.getAfterChangeQty().compareTo(BigDecimal.ZERO)<=0){
                    outLockQty=dbRwStockChangeFlowDO.getBeforeChangeQty().subtract(dbRwStockChangeFlowDO.getAfterChangeQty());
                }
            }
            lockStockDetailResp.setOutLockQty(outLockQty);
            resDetailList.add(lockStockDetailResp);
        }
        lockStockResp.setDetailList(resDetailList);
        return lockStockResp;
    }

    /**
     * 添加或修改运单号参数校验
     * @param groupAddExpressCodeDTO
     */
    private void addOrUpdateExpressCodeValid(GroupAddExpressCodeDTO groupAddExpressCodeDTO){
        if(StringUtils.isEmpty(groupAddExpressCodeDTO.getExpressCode())){
            throw new RomeException(ResCode.STOCK_ERROR_1002, "运单号不能为空");
        }
        if(StringUtils.isEmpty(groupAddExpressCodeDTO.getWarehouseCode())){
            throw new RomeException(ResCode.STOCK_ERROR_1002, "出库单号不能为空");
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void cancleOrderBatch(CancelReasonDTO dto) {
        String orderCode=dto.getRecordCode();
        Long userId = dto.getUserId();
        AlikAssert.isNotBlank(orderCode, ResCode.STOCK_ERROR_1001, ResCode.STOCK_ERROR_1001_DESC);
        //先根据单据取出后置单
        WarehouseRecordE record = warehouseRecordRepository.queryWarehouseRecordByRecordCode(orderCode);
        AlikAssert.isNotNull(record, ResCode.STOCK_ERROR_1017, ResCode.STOCK_ERROR_1017_DESC);
        // 已撤回、无需同步，不需要撤回
        if (WmsSyncStatusVO.BACKCANCLE.getStatus().equals(record.getSyncWmsStatus()) ||
                WmsSyncStatusVO.NO_REQUIRED.getStatus().equals(record.getSyncWmsStatus())) {
            return;
        }
        if (WarehouseRecordTypeVO.WINE_BUCKET_SALE_WAREHOUSE_RECORD.getType().equals(record.getRecordType())) {
            throw new RomeException(ResCode.STOCK_ERROR_2015, "沪威酒【桶订单】不可操作" + orderCode);
        }
        RealWarehouse realWarehouse = realWarehouseService.findByRealWarehouseId(record.getRealWarehouseId());
        //单据为初始化,且仓库为电商发货仓才允许撤回
        if (WarehouseRecordStatusVO.INIT.getStatus().equals(record.getRecordStatus()) &&
                RealWarehouseTypeVO.RW_TYPE_17.getType().equals(realWarehouse.getRealWarehouseType())) {
            //修改单据为已撤回
            Integer isSucc = warehouseRecordRepository.updateWmsStatusToBackCancle(record.getId(), userId, dto.getReasons(), dto.getOrderRemarkUser(), record.getVersionNo());
            AlikAssert.isTrue(isSucc > 0, ResCode.STOCK_ERROR_2014, ResCode.STOCK_ERROR_2014_DESC);
            //如果下发了wms  需要同步取消wms
            if (WmsSyncStatusVO.SYNCHRONIZED.getStatus().equals(record.getSyncWmsStatus()) || WmsSyncStatusVO.FORCESYNCH.getStatus().equals(record.getSyncWmsStatus())) {
                boolean cancleStatus = wmsOutService.orderCancel(record.getRecordCode());
                AlikAssert.isTrue(cancleStatus, ResCode.STOCK_ERROR_1205, ResCode.STOCK_ERROR_1205_DESC);
            }
        }else{
            throw new  RomeException(ResCode.STOCK_ERROR_2015, ResCode.STOCK_ERROR_2015_DESC);
        }

        //保存订单轨迹以及操作日志
        try {
            List<OnlineRetailE> retailEList = orderTrackFacade.queryOnLineRetailByRecordCode(orderCode);
            for (OnlineRetailE retailE : retailEList) {
                orderTrackFacade.save(retailE.getOutRecordCode(), "出库单撤单", "撤销出库单" + orderCode);
                //记录操作日志
                WDTLogDTO wdtLogDTO = new WDTLogDTO();
                wdtLogDTO.setOriginOrderCode(retailE.getOriginOrderCode());
                wdtLogDTO.setRecordCode(retailE.getRecordCode());
                wdtLogDTO.setOutRecordCode(retailE.getOutRecordCode());
                wdtLogDTO.setType(WDTRecordConst.OPERATE_CANCEL_WAREHOUSE_RECORD);
                wdtLogDTO.addChangedKeyValue(orderCode ,"撤回");
                wdtLogDTO.setCreator(userId);
                frWDTSaleRepository.saveLog(wdtLogDTO);
    }
        } catch (Exception e) {
            log.error(e.getMessage(), e);
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void pushAgain(String orderCode, Long userId) {
        AlikAssert.isNotBlank(orderCode, ResCode.STOCK_ERROR_1001, ResCode.STOCK_ERROR_1001_DESC);
        //先根据单据取出后置单
        WarehouseRecordE record = warehouseRecordRepository.getRecordWithDetailByCode(orderCode, null);
        AlikAssert.isNotNull(record, ResCode.STOCK_ERROR_1017, ResCode.STOCK_ERROR_1017_DESC);
        if (WarehouseRecordTypeVO.WINE_BUCKET_SALE_WAREHOUSE_RECORD.getType().equals(record.getRecordType())) {
            throw new RomeException(ResCode.STOCK_ERROR_2015, "沪威酒【桶订单】不可操作" + orderCode);
        }
        RealWarehouse realWarehouse = realWarehouseService.findByRealWarehouseId(record.getRealWarehouseId());
        //单据为初始化,且仓库为电商发货仓才允许重新推送
        if (WarehouseRecordStatusVO.INIT.getStatus().equals(record.getRecordStatus()) &&
                RealWarehouseTypeVO.RW_TYPE_17.getType().equals(realWarehouse.getRealWarehouseType())) {
            //只有撤回的单子才允许重新推送  停发的也可以重推
            AlikAssert.isTrue(WmsSyncStatusVO.BACKCANCLE.getStatus().equals(record.getSyncWmsStatus())
                    || WmsSyncStatusVO.STOPDELIVERY.getStatus().equals(record.getSyncWmsStatus()), ResCode.STOCK_ERROR_2016, ResCode.STOCK_ERROR_2016_DESC);
            //取消原单并生成新的单据
            record.setCreator(userId);
            record.setModifier(userId);
            // 停发的单子
            if(WmsSyncStatusVO.STOPDELIVERY.getStatus().equals(record.getSyncWmsStatus())) {
            	int i = warehouseRecordRepository.updateWmsStatusToWaitForceSynch(record.getId(), userId, WmsSyncStatusVO.STOPDELIVERY.getStatus(), record.getVersionNo());
            	AlikAssert.isTrue(i > 0, ResCode.STOCK_ERROR_1026, "停发到下发wms," + ResCode.STOCK_ERROR_1026_DESC);
            } else {
            	// 撤回的单子
            	//从池子中取出虚仓ID
                List<RwRecordPoolE> pools = rwRecordPoolRepository.queryKeysByWarehouseId(record.getId());
                if(!CollectionUtils.isEmpty(pools)){
                    RwRecordPoolE pool  = pools.get(0);
                    // 这里需要为强制同步wms
                    this.cancleAndCreta(pool.getRealWarehouseId(), pool.getVirtualWarehouseId(), record,null, true);
                }else {
                    throw new RomeException(ResCode.STOCK_ERROR_2019, ResCode.STOCK_ERROR_2019_DESC);
                }
            }
            
        }else{
            throw new  RomeException(ResCode.STOCK_ERROR_2017, ResCode.STOCK_ERROR_2017_DESC);
        }


        //保存订单轨迹以及操作日志
        try {
            List<OnlineRetailE> retailEList = orderTrackFacade.queryOnLineRetailByRecordCode(record.getRecordCode());
            for (OnlineRetailE retailE : retailEList) {
                orderTrackFacade.save(retailE.getOutRecordCode(), "重新推送出库单", "原单:" + orderCode + "，新单：" + record.getRecordCode());
                //记录操作日志
                WDTLogDTO wdtLogDTO = new WDTLogDTO();
                wdtLogDTO.setOriginOrderCode(retailE.getOriginOrderCode());
                wdtLogDTO.setRecordCode(retailE.getRecordCode());
                wdtLogDTO.setOutRecordCode(retailE.getOutRecordCode());
                wdtLogDTO.setType(WDTRecordConst.OPERATE_PUSH_AGAIN);
                wdtLogDTO.addChangedKeyValue(orderCode ,record.getRecordCode());
                wdtLogDTO.setCreator(userId);
                frWDTSaleRepository.saveLog(wdtLogDTO);
    }
        } catch (Exception e) {
            log.error(e.getMessage(), e);
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void changeWarehouse(String orderCode, BatchCancleDTO dto) {
        Integer dealType = -1;
        Long userId = dto.getUserId();
        String oldLogisticsCode = null;
        String newLogisticsCode = dto.getLogisticsCode();
        List<FrontRecordE> frontList = new ArrayList<>();
        AlikAssert.isNotBlank(orderCode, ResCode.STOCK_ERROR_1001, ResCode.STOCK_ERROR_1001_DESC);
        //先根据单据取出后置单
        WarehouseRecordE record = warehouseRecordRepository.getRecordWithDetailByCode(orderCode, null);
        AlikAssert.isNotNull(record, ResCode.STOCK_ERROR_1017, ResCode.STOCK_ERROR_1017_DESC);
        if (WarehouseRecordTypeVO.WINE_BUCKET_SALE_WAREHOUSE_RECORD.getType().equals(record.getRecordType())) {
            throw new RomeException(ResCode.STOCK_ERROR_2015, "沪威酒【桶订单】不可操作" + orderCode);
        }
        //原仓库Id
        Long realWarehouseId = record.getRealWarehouseId();
        //新单仓库Id
        Long updateRealWarehouseId = dto.getRealWarehouseId();
        if (null == updateRealWarehouseId) {
            updateRealWarehouseId = realWarehouseId;
        }

        //查询修改的仓库是否存在
        RealWarehouse changeRw = realWarehouseService.findByRealWarehouseId(updateRealWarehouseId);
        AlikAssert.isNotNull(changeRw, ResCode.STOCK_ERROR_4002, ResCode.STOCK_ERROR_4002_DESC);

        if (StringUtils.isBlank(newLogisticsCode) && Objects.equals(1, dto.getIsDefault())) {
            String frontRecordCode = frontWarehouseRecordRelationRepository.getFrontRecordCodeByRecordCode(record.getRecordCode());
            AddressE addr = addressRepository.queryByRecordCode(frontRecordCode);
            AlikAssert.isNotNull(addr, "999", "获取仓库默认物流公司，查询单据地址信息失败");
            List<RwRecordPoolE> rwRecordPoolES = rwRecordPoolRepository.queryByFrontRecordCode(frontRecordCode);
            AlikAssert.notEmpty(rwRecordPoolES, "999", "获取仓库默认物流公司，查询前置单do单号失败");
            String newDoCode = rwRecordPoolES.get(0).getDoCode() + "_" + changeRw.getRealWarehouseCode();
            //查询doCode
            TmsLogisticInfoDTO tmsLogisticInfoDTO = tmsTools.queryLogisticByAddress(addr, newDoCode, record.getChannelCode(), changeRw.getRealWarehouseCode());
            AlikAssert.isNotNull(tmsLogisticInfoDTO, "999", "获取仓库默认物流公司失败");
            newLogisticsCode = tmsLogisticInfoDTO.getLogisticsCode();
            dto.setLogisticsCode(tmsLogisticInfoDTO.getLogisticsCode());
        }

        //判断是否改仓了
        if(!changeRw.getId().equals(realWarehouseId)){
            if(StringUtils.isBlank(dto.getLogisticsCode())){
                //只改仓库
                dealType = WDTRecordConst.OPERATE_EDIT_HOUSE_AFTER_CANCEL;
            }else{
                //都进行了更改
                dealType = WDTRecordConst.OPERATE_EDIT_HOUSE_AND_LOGISTICS_AFTER_CANCEL;
            }
        }else{
            if(StringUtils.isNotBlank(dto.getLogisticsCode())){
                //只改物流
                dealType = WDTRecordConst.OPERATE_EDIT_LOGISTICS_AFTER_CANCEL;
            }else{
                //都没改
                dealType = -1;
                return;
            }
        }
        //判断当前仓库是否属于该渠道
        Long vmId = virtualWarehouseService.getVwIdByRwAndChannel(updateRealWarehouseId, record.getChannelCode());
        AlikAssert.isNotNull(vmId, ResCode.STOCK_ERROR_2018, ResCode.STOCK_ERROR_2018_DESC);
        //单据为初始化,且仓库为电商发货仓才允许重新推送
        RealWarehouse realWarehouse = realWarehouseService.findByRealWarehouseId(record.getRealWarehouseId());
        if(WarehouseRecordStatusVO.INIT.getStatus().equals(record.getRecordStatus()) &&
                RealWarehouseTypeVO.RW_TYPE_17.getType().equals(realWarehouse.getRealWarehouseType())){
            //查询出池子表及前置单
            List<RwRecordPoolE> poolList = rwRecordPoolRepository.queryKeysWithDetailsByWarehouseId(record.getId());
            oldLogisticsCode=poolList.get(0).getLogisticsCode();
            // 同一个实仓不同虚仓情况
            if(changeRw.getId().equals(realWarehouseId)){
            	boolean flag = false;
            	for (RwRecordPoolE pool : poolList) {
            		if(!vmId.equals(pool.getVirtualWarehouseId())) {
            			flag = true;
            			break;
            		}
            	}
            	if(flag) {
            		if(StringUtils.isBlank(dto.getLogisticsCode())){
                        //只改仓库
                        dealType = WDTRecordConst.OPERATE_EDIT_HOUSE_AFTER_CANCEL;
                    }else{
                        //都进行了更改
                        dealType = WDTRecordConst.OPERATE_EDIT_HOUSE_AND_LOGISTICS_AFTER_CANCEL;
                    }
            	}
            }
            List<String> frontRecordCodes = poolList.stream().map(RwRecordPoolE::getFrontRecordCode).distinct().collect(Collectors.toList());
            frontList  = frWDTSaleRepository.queryBasesicInfoBycodes(frontRecordCodes);
            if (CollectionUtils.isEmpty(frontList)) {
                frontList  = frSaleRepository.queryRecordByRecordCodeList(frontRecordCodes);
            }
            Map<Long, FrontRecordE> frontRecordMap = RomeCollectionUtil.listforMap(frontList, "id", null);
            //只有撤回的单子才允许重新推送 停发的也可以
            AlikAssert.isTrue(WmsSyncStatusVO.BACKCANCLE.getStatus().equals(record.getSyncWmsStatus()) 
            		|| WmsSyncStatusVO.STOPDELIVERY.getStatus().equals(record.getSyncWmsStatus()), ResCode.STOCK_ERROR_2016, ResCode.STOCK_ERROR_2016_DESC);
            //取消原单并生成新的单据
            record.setCreator(userId);
            record.setModifier(userId);
            this.cancleAndCreta(updateRealWarehouseId, vmId , record,dto.getLogisticsCode(), false);
            //释放原始仓库库存 锁定新仓库库存
            List<CoreChannelOrderDO> lockCoreList = new ArrayList<>();
            List<CoreChannelOrderDO> unLockCoreList = new ArrayList<>();
            //如果是只修改物流公司，不在释放原始仓库库存锁定新仓库库存
            if(!dealType.equals(WDTRecordConst.OPERATE_EDIT_LOGISTICS_AFTER_CANCEL)) {
                for (RwRecordPoolE pool : poolList) {
                    CoreChannelOrderDO lockDo = null;
                    CoreChannelOrderDO unLockDo = null;
                    boolean isSuccess = false;
                    try {
                        FrontRecordE frontRecord = frontRecordMap.get(pool.getFrontRecordId());
                        //解锁原始仓库库存
                        unLockDo = this.warpUnLockOutStockDo(pool, frontRecord, pool.getRealWarehouseId(), pool.getVirtualWarehouseId());
                        coreChannelSalesRepository.unlockStock(unLockDo);
                        unLockCoreList.add(unLockDo);
                        //锁定新仓库存
                        lockDo = this.warpLockOutStockDo(pool, frontRecord, updateRealWarehouseId, vmId);
                        coreChannelSalesRepository.lockStockVirtual(lockDo);
                        lockCoreList.add(lockDo);
                        isSuccess = true;
                    } catch (RomeException e) {
                        log.error(e.getMessage(), e);
                        throw e;
                    } catch (Exception e) {
                        log.error(e.getMessage(), e);
                        throw e;
                    } finally {
                        //模型成功，业务等处理失败，需要回滚
                        if (!isSuccess) {
                            for (CoreChannelOrderDO lockCore : lockCoreList) {
                                RedisRollBackFacade.redisRollBack(lockCore);
                            }
                            for (CoreChannelOrderDO unLockCore : unLockCoreList) {
                                RedisRollBackFacade.redisRollBack(unLockCore);
                            }
                        }
                    }
                }
            }
        }else{
            throw new  RomeException(ResCode.STOCK_ERROR_2017, ResCode.STOCK_ERROR_2017_DESC);
        }
        try {
            for (FrontRecordE retailE : frontList) {
                //记录操作日志
                WDTLogDTO wdtLogDTO = new WDTLogDTO();
                wdtLogDTO.setOriginOrderCode(retailE.getOriginOrderCode() == null ? retailE.getOutRecordCode() : retailE.getOriginOrderCode());
                wdtLogDTO.setRecordCode(retailE.getRecordCode());
                wdtLogDTO.setOutRecordCode(retailE.getOutRecordCode());
                wdtLogDTO.addChangedKeyValue(orderCode, record.getRecordCode());
                wdtLogDTO.setCreator(userId);
                String ext = "";
                String eventName = "";
                //保存订单轨迹以及操作日志
                if(WDTRecordConst.OPERATE_EDIT_HOUSE_AFTER_CANCEL.equals(dealType)){
                    ext = "原仓：" + realWarehouse.getRealWarehouseCode() + ",原单：" + orderCode+ "，修改为新仓：" + changeRw.getRealWarehouseCode() + "，产生新单：" + record.getRecordCode();
                    eventName = "撤单后的改仓" ;
                    wdtLogDTO.addChangedKeyValue(realWarehouse.getRealWarehouseCode(), changeRw.getRealWarehouseCode());
                    wdtLogDTO.setType(WDTRecordConst.OPERATE_EDIT_HOUSE_AFTER_CANCEL);
                } else if(WDTRecordConst.OPERATE_EDIT_LOGISTICS_AFTER_CANCEL.equals(dealType)){
                    wdtLogDTO.addChangedKeyValue(oldLogisticsCode, newLogisticsCode);
                    ext = "原物流编码为" + wdtLogDTO.getBeforeValue() + ",原单：" + orderCode+   ",修改为新物流编码为" + wdtLogDTO.getAfterValue() + "，产生新单：" + record.getRecordCode();
                    eventName = "撤单后的改物流" ;
                    wdtLogDTO.setType(WDTRecordConst.OPERATE_EDIT_LOGISTICS_AFTER_CANCEL);
                } else if(WDTRecordConst.OPERATE_EDIT_HOUSE_AND_LOGISTICS_AFTER_CANCEL.equals(dealType)){
                    wdtLogDTO.addChangedKeyValue(realWarehouse.getRealWarehouseCode(), changeRw.getRealWarehouseCode());
                    wdtLogDTO.addChangedKeyValue(oldLogisticsCode, newLogisticsCode);
                    ext = "原仓库和物流编码为" + wdtLogDTO.getBeforeValue() + ",原单：" + orderCode + ",修改为新仓库和物流编码为" + wdtLogDTO.getAfterValue()+ "，产生新单：" + record.getRecordCode();
                    eventName = "撤单后的改仓库和物流" ;
                    wdtLogDTO.setType(WDTRecordConst.OPERATE_EDIT_HOUSE_AND_LOGISTICS_AFTER_CANCEL);
    }
                wdtLogDTO.setModifier(dto.getModifier());
                frWDTSaleRepository.saveLog(wdtLogDTO);
                orderTrackFacade.save(retailE.getOutRecordCode(), eventName, ext);
            }
        } catch (Exception e) {
            log.error(e.getMessage(), e);
        }
    }

    /**
     * 封装锁定库存参数
     * @param pool
     * @param realWarehouseId
     * @param vmId
     * @return
     */
    private CoreChannelOrderDO warpLockOutStockDo(RwRecordPoolE pool, FrontRecordE frontRecord, Long realWarehouseId, Long vmId) {
        List<CoreVirtualStockOpDO>  cvsList = new ArrayList<>();
        CoreChannelOrderDO coreChannelOrderDO = new CoreChannelOrderDO();
        //库存交易类型
        coreChannelOrderDO.setRecordCode(frontRecord.getRecordCode());
        coreChannelOrderDO.setTransType(frontRecord.getRecordType());
        coreChannelOrderDO.setMerchantId(frontRecord.getMerchantId());
        coreChannelOrderDO.setChannelCode(pool.getChannelCode());
        List<CoreOrderDetailDO> details = new ArrayList<>();
        coreChannelOrderDO.setOrderDetailDOs(details);
        CoreOrderDetailDO detailDO;
        for (RwRecordPoolDetailE detailE : pool.getRwRecordPoolDetails()) {
            if (detailE.getBasicSkuQty().compareTo(BigDecimal.ZERO) > 0) {
                detailDO = new CoreOrderDetailDO();
                detailDO.setLockQty(detailE.getBasicSkuQty());
                detailDO.setSkuId(detailE.getSkuId());
                detailDO.setSkuCode(detailE.getSkuCode());
                details.add(detailDO);
                CoreVirtualStockOpDO coreStockDO = new CoreVirtualStockOpDO();
                coreStockDO.setLockQty(detailE.getBasicSkuQty());
                coreStockDO.setVirtualWarehouseId(vmId);
                coreStockDO.setRealWarehouseId(realWarehouseId);
                coreStockDO.setRecordCode(detailE.getRecordCode());
                coreStockDO.setTransType(coreChannelOrderDO.getTransType());
                coreStockDO.setChannelCode(coreChannelOrderDO.getChannelCode());
                coreStockDO.setMerchantId(detailE.getMerchantId());
                coreStockDO.setSkuId(detailE.getSkuId());
                coreStockDO.setSkuCode(detailE.getSkuCode());
                cvsList.add(coreStockDO);
            }
        }
        coreChannelOrderDO.setVirtualStockOpDetailDOs(cvsList);
        return coreChannelOrderDO;
    }


    /**
     * 包装取消订单/出库的接口参数
     */
    private CoreChannelOrderDO warpUnLockOutStockDo(RwRecordPoolE pool, FrontRecordE frontRecord, Long realWarehouseId, Long vmId) {
        CoreChannelOrderDO cco = new CoreChannelOrderDO();
        cco.setRecordCode(frontRecord.getRecordCode());
        cco.setTransType(frontRecord.getRecordType());
        cco.setMerchantId(frontRecord.getMerchantId());
        cco.setChannelCode(pool.getChannelCode());
        List<CoreOrderDetailDO> details = new ArrayList<>();
        cco.setOrderDetailDOs(details);
        //虚仓信息
        cco.setVirtualStockOpDetailDOs(new ArrayList<>());
        CoreVirtualStockOpDO vwDo;
        CoreOrderDetailDO detailDO;
        for (RwRecordPoolDetailE detailE : pool.getRwRecordPoolDetails()) {
            if(detailE.getBasicSkuQty().compareTo(BigDecimal.ZERO)> 0) {
                detailDO = new CoreOrderDetailDO();
                detailDO.setUnlockQty(detailE.getBasicSkuQty());
                detailDO.setSkuId(detailE.getSkuId());
                detailDO.setSkuCode(detailE.getSkuCode());
                detailDO.setRealWarehouseId(realWarehouseId);
                details.add(detailDO);
                //虚仓
                vwDo = new CoreVirtualStockOpDO();
                vwDo.setSkuId(detailE.getSkuId());
                vwDo.setSkuCode(detailE.getSkuCode());
                vwDo.setUnlockQty(detailE.getBasicSkuQty());
                vwDo.setVirtualWarehouseId(vmId);
                vwDo.setRealWarehouseId(realWarehouseId);
                cco.getVirtualStockOpDetailDOs().add(vwDo);
            }
        }
        return cco;
    }

    /**
     * 取消原单据创建新单据
     * @param record
     * @param needForceSyncWms 需要强制同步wms的
     */
    private void cancleAndCreta(Long realWarehouseId, Long virtualWarehouseId, WarehouseRecordE record,String logisticsCode, boolean needForceSyncWms) {
        OnlineRetailWarehouseRecordE outRecordE = entityFactory.createEntity(OnlineRetailWarehouseRecordE.class);
        //查询对应的前置单单号
        List<FrontWarehouseRecordRelationDO> relationList  = warehouseRecordRepository.getFrontWarehouseRecordsRelationByWrId(record.getId());
        //取消单据并删除关联关系
        AlikAssert.isTrue(record.getSyncWmsStatus().equals(WmsSyncStatusVO.BACKCANCLE.getStatus()) || record.getSyncWmsStatus().equals(WmsSyncStatusVO.STOPDELIVERY.getStatus()), ResCode.STOCK_ERROR_1028, ResCode.STOCK_ERROR_1028_DESC);
        outRecordE.cancleWarehouseRecord(record.getId() , true);
        //生成新的出入库单据 修改池子的后置单id和实仓ID  创建关联表关系
        outRecordE.createNewByOld(relationList, record, realWarehouseId, virtualWarehouseId,logisticsCode, needForceSyncWms);
    }

    /**
     * 合并其他数据
     * 1.外部订单号
     * 2.渠道名称
     * 3.单据状态名
     * 4.单据类型名
     * @param list
     */
    private void combineDataQueryWarehouseRecordHistory(List<SaleWarehouseRecordDTO> list) {
    	List<Long> ids = new ArrayList<>();
    	List<String> channelCodeList = new ArrayList<>();
    	List<String> recordCodes = new ArrayList<>(list.size());
    	for(SaleWarehouseRecordDTO dto : list) {
    		if(dto.getRealWarehouseId() != null && ids.contains(dto.getRealWarehouseId()) == false) {
    			ids.add(dto.getRealWarehouseId());
    		}
    		if(dto.getChannelCode() != null && channelCodeList.contains(dto.getChannelCode()) == false) {
    			channelCodeList.add(dto.getChannelCode());
    		}
    		recordCodes.add(dto.getRecordCode());
    	}
    	List<RealWarehouse> realWarehouseList = null;
    	if(ids.size() > 0) {
    		realWarehouseList = realWarehouseService.findByRealWarehouseIds(ids);
    	}
        
        Map<Long, String> realWarehouseServiceMap = RomeCollectionUtil.listforMap(realWarehouseList, "id", "realWarehouseName");
        Map<Long, String> realWarehouseCodeMap = RomeCollectionUtil.listforMap(realWarehouseList, "id", "realWarehouseCode");
        Iterable<EsFrRecordDO> kkk = esWarehouseRecordRepository.search(QueryBuilders.nestedQuery("warehouseRecordDOs", 
				QueryBuilders.boolQuery().must(QueryBuilders.termsQuery("warehouseRecordDOs.recordCode.keyWord", recordCodes)), ScoreMode.Total));
        Map<String, List<String>> outRecordCodeMap = new HashMap<>(list.size() * 100 / 70);
        if(kkk != null) {
        	for(EsFrRecordDO dto : kkk) {
        		if(dto.getOutRecordCode() != null && dto.getWarehouseRecordDOs() != null) {
        			for(EsWarehouseRecordDO rs : dto.getWarehouseRecordDOs()) {
        				if(rs.getRecordCode() != null) {
        					if(!outRecordCodeMap.containsKey(rs.getRecordCode())) {
        						outRecordCodeMap.put(rs.getRecordCode(), new ArrayList<>());
        					}
        					outRecordCodeMap.get(rs.getRecordCode()).add(dto.getOutRecordCode());
        				}
        			}
        		}
        	}
        }
        //批量查询渠道信息
        List<ChannelDTO> channelInfos = null;
        try {
            //第三方接口异常，只打印错误日志，不影响主流程
        	if(channelCodeList.size() > 0) {
        		channelInfos = channelFacade.batchQueryByChannelcodes(channelCodeList);
        		channelCodeList.clear();
        		channelCodeList = null;
        	}
        } catch (Exception e) {
            log.error(e.getMessage(), e);
        }
        Map<String, ChannelDTO> channelInfoMap = RomeCollectionUtil.listforMap(channelInfos, "channelCode");
        for (SaleWarehouseRecordDTO dto : list) {
            dto.setOutRecordCode(outRecordCodeMap.get(dto.getRecordCode()));
            dto.setRecordStatusName(WarehouseRecordStatusVO.getDescByType(dto.getRecordStatus()));
            dto.setRecordTypeName(WarehouseRecordTypeVO.getDescByType(dto.getRecordType()));
            dto.setRealWarehouseName(realWarehouseServiceMap.get(dto.getRealWarehouseId()));
            dto.setRealWarehouseCode(realWarehouseCodeMap.get(dto.getRealWarehouseId()));
            if (channelInfoMap.containsKey(dto.getChannelCode())) {
                dto.setChannelCodeName(channelInfoMap.get(dto.getChannelCode()).getChannelName());
            }
        }
        channelInfoMap.clear();
        channelInfoMap = null;
        outRecordCodeMap.clear();
        outRecordCodeMap = null;
        channelInfos = null;
    }
    
    /**
     * 获取请求参数
     * 出库单,历史数据
     */
    private NativeSearchQuery getQueryWarehouseRecordListHistoryParam(SaleWarehouseRecordCondition condition){
    	BoolQueryBuilder boolQueryBuilder = QueryBuilders.boolQuery();
    	// 订单号 外部单号
    	if (StringUtils.isNotBlank(condition.getOrderCode())) {
    		boolQueryBuilder.must(QueryBuilders.termsQuery("outRecordCode.keyWord", condition.getOrderCode().split("\n|\r")));
    	}
    	BoolQueryBuilder boolQueryBuilderByWarehouseRecord = QueryBuilders.boolQuery();
        // 仓库编码
        if (StringUtils.isNotBlank(condition.getRealWarehouseCode()) && condition.getRealWarehouseId() != null) {
        	boolQueryBuilderByWarehouseRecord.must(QueryBuilders.termQuery("warehouseRecordDOs.realWarehouseId", condition.getRealWarehouseId()));
        }
        
		// 条件 单据类型recordType
        if(condition.getRecordType() != null) {
        	boolQueryBuilderByWarehouseRecord.must(QueryBuilders.termQuery("warehouseRecordDOs.recordType", condition.getRecordType()));
        }
		// 条件 userCode
        if(StringUtils.isNotBlank(condition.getUserCode())) {
        	boolQueryBuilderByWarehouseRecord.must(QueryBuilders.termQuery("warehouseRecordDOs.userCode.keyWord", condition.getUserCode()));
        }
		// 条件 recordCode
        if(StringUtils.isNotBlank(condition.getRecordCode())) {
        	boolQueryBuilderByWarehouseRecord.must(QueryBuilders.termQuery("warehouseRecordDOs.recordCode.keyWord", condition.getRecordCode()));
        }
		// 条件 recordStatus
        if(condition.getRecordStatus() != null) {
        	boolQueryBuilderByWarehouseRecord.must(QueryBuilders.termQuery("warehouseRecordDOs.recordStatus", condition.getRecordStatus()));
        }
		// 条件 syncWmsStatus
        if(condition.getSyncWmsStatus() != null) {
        	boolQueryBuilderByWarehouseRecord.must(QueryBuilders.termQuery("warehouseRecordDOs.syncWmsStatus", condition.getSyncWmsStatus()));
        }
		// 条件 syncFulfillmentStatus
        if(condition.getSyncFulfillmentStatus() != null) {
        	boolQueryBuilderByWarehouseRecord.must(QueryBuilders.termQuery("warehouseRecordDOs.syncFulfillmentStatus", condition.getSyncFulfillmentStatus()));
        }
		// 条件 channelCode
        if(condition.getChannelCodeList() != null && condition.getChannelCodeList().size() > 0) {
        	boolQueryBuilderByWarehouseRecord.must(QueryBuilders.termsQuery("warehouseRecordDOs.channelCode.keyWord", 
        			condition.getChannelCodeList()));
        }
        if(!CollectionUtils.isEmpty(boolQueryBuilderByWarehouseRecord.must())) {
        	boolQueryBuilder.must(QueryBuilders.nestedQuery("warehouseRecordDOs", 
    				boolQueryBuilderByWarehouseRecord, ScoreMode.Total));
        }
		// 条件 skuCode
		if(StringUtils.isNotBlank(condition.getSkuCode())) {
			BoolQueryBuilder boolQueryBuilderByDetails = QueryBuilders.boolQuery();	
			boolQueryBuilderByDetails.must(QueryBuilders.termQuery("warehouseRecordDOs.detailDOs.skuCode", condition.getSkuCode()));
			boolQueryBuilder.must(QueryBuilders.nestedQuery("warehouseRecordDOs.detailDOs", 
					boolQueryBuilderByDetails, ScoreMode.Total));
		}
		//时间范围的设定
		if(condition.getStartTime() != null || condition.getEndTime() != null) {
			RangeQueryBuilder rangeQueryBuilder = QueryBuilders.rangeQuery("createTime");
			if(condition.getStartTime() != null) {
				rangeQueryBuilder.from(condition.getStartTime().getTime());
			}
			if(condition.getEndTime() != null) {
				rangeQueryBuilder.to(condition.getEndTime().getTime());
			}
			boolQueryBuilder.must(rangeQueryBuilder);
		}
		Pageable pageable = PageRequest.of(condition.getPageIndex() - 1, condition.getPageSize() > 100000 ? 100000 : condition.getPageSize());
		NativeSearchQueryBuilder builder = new NativeSearchQueryBuilder();
		builder.withQuery(boolQueryBuilder);
		builder.withPageable(pageable);
		builder.withSort(SortBuilders.fieldSort("createTime").order(SortOrder.DESC));
		NativeSearchQuery query = builder.build();
		query.setTrackTotalHits(true);
		return query;
    }
    
    /**
     * 校验,历史数据,时间
     * @param condition
     */
    private void validateDateByHistory(SaleWarehouseRecordCondition condition) {
    	if(condition.getStartTime() == null || condition.getEndTime() == null) {
    		throw new RomeException(ResCode.STOCK_ERROR_1002, "参数有误,时间不能为空,并且时间范围不能跨月");
    	}
    	SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM");
    	if(!dateFormat.format(condition.getStartTime()).equals(dateFormat.format(condition.getEndTime()))) {
    		throw new RomeException(ResCode.STOCK_ERROR_1002, "参数有误,时间不能为空,并且时间范围不能跨月");
    	}
	}

    @Override
    @TargetDataSource(DynamicDataSourceEnum.READ)
    public PageInfo<SaleWarehouseRecordDTO> queryNewWarehouseRecordList(SaleWarehouseRecordCondition condition) {
        if (StringUtils.isBlank(condition.getOrderCode())
                && CollectionUtils.isEmpty(condition.getOriginOrderCodeList())
        && CollectionUtils.isEmpty(condition.getRecordCodeList())
        && (condition.getStartTime() == null || condition.getEndTime() == null)
        && (condition.getStartOutOrInTime() == null || condition.getEndOutOrInTime() == null)
       ) {
            throw new RomeException("999", "创建时间、发货时间不能同时为空");
        }
        List<String> idsAll = Lists.newArrayList();
        List<String> idsOne = Lists.newArrayList();
        List<String> idsTwo = Lists.newArrayList();
        List<String> idsThree = Lists.newArrayList();
        List<String> idsFour = Lists.newArrayList();
        boolean flag=false;
        if (!CollectionUtils.isEmpty(condition.getExpressCodeList())) {
            flag=true;
            idsTwo=recordPackageRepository.queryPackageWarehouseRecordIdByExpressCodeList(condition.getExpressCodeList());
        }
        if (!CollectionUtils.isEmpty(condition.getLogisticsCodeList())) {
            flag=true;
            idsFour=recordPackageRepository.queryPackageWarehouseRecordIdByLogisticsCodeList(condition.getLogisticsCodeList(),condition.getWmsCode());
        }
        if (StringUtils.isNotBlank(condition.getOrderCode()) || !CollectionUtils.isEmpty(condition.getOriginOrderCodeList())
                || !CollectionUtils.isEmpty(condition.getIsPreSaleList()) || condition.getPromiseStartTime() !=null) {
            flag=true;
            if(StringUtils.isNotBlank(condition.getOrderCode())){
                List<String> orderCodes=Arrays.asList(condition.getOrderCode().split("\n|\r"));
                condition.setOrderCodeList(orderCodes);
                List<String> warehouseRecordCodes = frSaleRepository.listWarehouseRecordIdsByFrSaleFrontCodes(orderCodes);
                idsThree=new ArrayList<>(warehouseRecordCodes);
            }
            //1、如果查询条件里面有订单编码，则先根据订单编码查询出旺店通符合条件的出库单ids
            List<WdtWarehouseRecordDTO> res = queryWdtFrSaleFrontAndWarehouseRelation(condition);
            if (!CollectionUtils.isEmpty(res)) {
                Map<String,List<WdtWarehouseRecordDTO>> idList=res.stream().collect(Collectors.groupingBy(WdtWarehouseRecordDTO::getRecordCode));
               if (idsThree == null) {
                   idsThree = new ArrayList<>(idList.keySet());
               } else {
                   idsThree.addAll(idList.keySet());
               }
            }
        }

        idsAll=this.retainAll(idsOne,idsTwo, idsThree, idsFour,condition.getRecordCodeList());
        if (CollectionUtils.isEmpty(idsAll) && flag) {
            //用户输入的订单 编号不存在，则直接返回无结果
            PageInfo<SaleWarehouseRecordDTO> personPageInfo = new PageInfo<>(new ArrayList<>());
            personPageInfo.setTotal(0);
            return personPageInfo;
        } else {
            condition.setRecordCodeList(idsAll);
        }
        //2.带条件分页查询
        if (CollectionUtil.isEmpty(condition.getRealWarehouseIdList()) && condition.getRealWarehouseType() != null) {
            List<RealWarehouse> realWarehouses = realWarehouseService.queryRealWarehouseByRWType(condition.getRealWarehouseType());
            if (CollectionUtil.isEmpty(realWarehouses)) {
                //用户输入的订单 编号不存在，则直接返回无结果
                PageInfo<SaleWarehouseRecordDTO> personPageInfo = new PageInfo<>(new ArrayList<>());
                personPageInfo.setTotal(0);
                return personPageInfo;
            } else {
                List<Long> realWarehouseIds = RomeCollectionUtil.getValueList(realWarehouses, "id");
                condition.setRealWarehouseIdList(realWarehouseIds);
            }
        }
        Page page = PageHelper.startPage(condition.getPageIndex(), condition.getPageSize());
        List<SaleWarehouseRecordDTO> result = warehouseRecordRepository.queryOdyWarehouseRecordList(condition);
        PageInfo<SaleWarehouseRecordDTO> recordDTOPageInfo = new PageInfo<>(result);
        recordDTOPageInfo.setTotal(page.getTotal());

        //3、将出库单关联的订单编号赋值给页面dto,实仓名称赋值给页面DTO
        if (!CollectionUtils.isEmpty(recordDTOPageInfo.getList())) {
            List<Long> rwIds = RomeCollectionUtil.getValueList(recordDTOPageInfo.getList(),"realWarehouseId");
            rwIds = rwIds.stream().distinct().collect(Collectors.toList());
            List<RealWarehouse> realWarehouseList = realWarehouseService.findByRealWarehouseIds(rwIds);
            Map<Long, RealWarehouse> realWarehouseMap = RomeCollectionUtil.listforMap(realWarehouseList, "id", null);
            List<Long> warehouseRecordIds = RomeCollectionUtil.getValueList(recordDTOPageInfo.getList(), "id");
            Map<Long, List<String>> odyRelation = queryFrSaleFrontAndWarehouseRelation(null, warehouseRecordIds);
            SaleWarehouseRecordCondition conditionTemp=new SaleWarehouseRecordCondition();
            List<String> warehouseRecordCodeList = recordDTOPageInfo.getList().stream().map(SaleWarehouseRecordDTO::getRecordCode).distinct().collect(Collectors.toList());
            conditionTemp.setRecordCodeList(warehouseRecordCodeList);
            List<WdtWarehouseRecordDTO> res = queryWdtFrSaleFrontAndWarehouseRelation(conditionTemp);
            Map<String,List<WdtWarehouseRecordDTO>> wdtRelation=res.stream().collect(Collectors.groupingBy(WdtWarehouseRecordDTO::getRecordCode));
            //设置前置单IDs
            Map<Long,FrSaleWdtExtE> extInfomap = new HashMap<>();
            List<Long> frontRecordIds = new ArrayList<>();
            List<String> firstOriginCodeList = new ArrayList<>();
            if(CollectionUtil.isNotEmpty(wdtRelation)){
                for (String warehouseRecordCode : wdtRelation.keySet()) {
                    if(CollectionUtil.isNotEmpty(wdtRelation.get(warehouseRecordCode))){
                        frontRecordIds.add(wdtRelation.get(warehouseRecordCode).get(0).getFront());
                        firstOriginCodeList.add(wdtRelation.get(warehouseRecordCode).get(0).getOriginOrderCode());
                    }
                }
            }

            //批量查询渠道信息
            List<ChannelDTO> channelInfos = null;
            try {
                //第三方接口异常，只打印错误日志，不影响主流程
                channelInfos = channelFacade.batchQueryByChannelcodes(RomeCollectionUtil.getValueList(recordDTOPageInfo.getList(), "channelCode"));
            } catch (Exception e) {
                log.error(e.getMessage(), e);
            }
            Map<String, ChannelDTO> channelInfoMap = RomeCollectionUtil.listforMap(channelInfos, "channelCode");
            List<String> warehouseRecordCodes=result.stream().map(SaleWarehouseRecordDTO::getRecordCode).distinct().collect(Collectors.toList());
            List<RwRecordPoolDo> poolList=rwRecordPoolRepository.queryPoolByWarehouseRecordCodes(warehouseRecordCodes);
            Map<String,RwRecordPoolDo> poolMap=poolList.stream().collect(Collectors.toMap(RwRecordPoolDo::getWarehouseRecordCode, Function.identity(), (v1, v2) -> v1));

            //物流单号相关查询
            List<RecordPackageDO> recordPackageList=recordPackageRepository.getRecordPackageByRecordCodeList(warehouseRecordCodes);
           // Map<String,RecordPackageDO> recordPackageMap=recordPackageList.stream().collect(Collectors.toMap(RecordPackageDO::getRecordCode, Function.identity(), (v1, v2) -> v1));
            Map<String, List<RecordPackageDO>> recordPackageMap = RomeCollectionUtil.listforListMap(recordPackageList, "recordCode");
            //省市区、收货地址相关查询
            List<String> doCodeList=poolList.stream().map(RwRecordPoolDo::getDoCode).collect(Collectors.toList());
            List<AddressE> addressEList=addressRepository.queryByRecordCodes(doCodeList);
            Map<String,AddressE> addressEMap=addressEList.stream().collect(Collectors.toMap(AddressE::getRecordCode, Function.identity(), (v1, v2) -> v1));
            //获取团长的收货地址
            Map<String,AddressE> groupAddressMap = new HashMap<>();
            if(CollectionUtil.isNotEmpty(firstOriginCodeList)){
                List<AddressE> groupAddressList = addressRepository.queryByRecordCodes(firstOriginCodeList);
                groupAddressMap = groupAddressList.stream().collect(Collectors.toMap(AddressE::getRecordCode, Function.identity(), (v1, v2) -> v1));
            }
            //批量查询附加信息
            if(CollectionUtil.isNotEmpty(frontRecordIds)) {
                List<FrSaleWdtExtE> extInfoList = frSaleWdtExtMapper.queryExtInfoFrontIds(frontRecordIds);
                extInfomap = RomeCollectionUtil.listforMap(extInfoList, "frontRecordId");
            }
            //补全包裹明细
            if ("Y".equals(condition.getIsExportPackage())) {
                List<Long> ids = recordPackageList.stream().map(t -> t.getId()).collect(Collectors.toList());
                List<RecordPackageDetailDO> recordPackageDetailDOS = recordPackageRepository.queryDetailListForList(ids);
                Map<Long, List<RecordPackageDetailDO>> detailMap = recordPackageDetailDOS.stream().collect(Collectors.groupingBy(t -> t.getPackageId()));
                for (RecordPackageDO aDo : recordPackageList) {
                    if (detailMap.containsKey(aDo.getId())) {
                        aDo.setDetails(detailMap.get(aDo.getId()));
                    }
                }
                recordPackageMap = RomeCollectionUtil.listforListMap(recordPackageList, "recordCode");
                for (SaleWarehouseRecordDTO recordDTO : recordDTOPageInfo.getList()) {
                    if (recordPackageMap.containsKey(recordDTO.getRecordCode())) {
                        recordDTO.setPackages(recordPackageMap.get(recordDTO.getRecordCode()));
                    }
                }
            }

            //补全明细数据
            if ("Y".equals(condition.getIsExportDetail())) {
                List<WarehouseRecordDetail> warehouseRecordDetails = warehouseRecordRepository.queryDetailListByRecordIds(recordDTOPageInfo.getList().stream().map(t -> t.getId()).distinct().collect(Collectors.toList()));
                List<SkuInfoExtDTO> skuInfoExtDTOS = skuFacade.skusBySkuCode(warehouseRecordDetails.stream().map(t -> t.getSkuCode()).distinct().collect(Collectors.toList()));
                Map<Object, SkuInfoExtDTO> skuCodeMap = RomeCollectionUtil.listforMap(skuInfoExtDTOS, "skuCode");
                warehouseRecordDetails.forEach(e -> {
                    if (skuCodeMap.containsKey(e.getSkuCode())) {
                        SkuInfoExtDTO skuInfoExtDTO = skuCodeMap.get(e.getSkuCode());
                        e.setBarCode(skuInfoExtDTO.getBarCode());
                        e.setSkuName(skuInfoExtDTO.getName());
                    }
                });
                Map<Long, List<WarehouseRecordDetail>> recordDetailMap = warehouseRecordDetails.stream().collect(Collectors.groupingBy(t -> t.getWarehouseRecordId()));
                for (SaleWarehouseRecordDTO recordDTO : recordDTOPageInfo.getList()) {
                    if (recordDetailMap.containsKey(recordDTO.getId())) {
                        recordDTO.setWarehouseRecordDetailList(warehouseRecordDetailConvertor.detailListToDetailE(recordDetailMap.get(recordDTO.getId())));
                    }
                }
            }

            for (SaleWarehouseRecordDTO dto : recordDTOPageInfo.getList()) {
                RealWarehouse outRealWarehouse  = realWarehouseMap.get(dto.getRealWarehouseId());
                dto.setOutRecordCode(odyRelation.get(dto.getId()));
                dto.setRecordStatusName(WarehouseRecordStatusVO.getDescByType(dto.getRecordStatus()));
                dto.setRecordTypeName(WarehouseRecordTypeVO.getDescByType(dto.getRecordType()));
                if (Objects.nonNull(outRealWarehouse)){
                    dto.setRealWarehouseName(outRealWarehouse.getRealWarehouseName());
                    dto.setRealWarehouseCode(outRealWarehouse.getRealWarehouseCode());
                    dto.setRealWarehouseType(outRealWarehouse.getRealWarehouseType());
                }
                if(wdtRelation.containsKey(dto.getRecordCode())){
                    List<String> outRecordCodeList=wdtRelation.get(dto.getRecordCode()).stream().map(WdtWarehouseRecordDTO::getOrderCode).distinct().collect(Collectors.toList());
                    List<String> originOrderCodeList=wdtRelation.get(dto.getRecordCode()).stream().map(WdtWarehouseRecordDTO::getOriginOrderCode).distinct().collect(Collectors.toList());
                    dto.setOutRecordCode(outRecordCodeList);
                    dto.setIsPreSale(wdtRelation.get(dto.getRecordCode()).get(0).getIsPreSale());
                    dto.setOriginOrderCode(CollectionUtils.isEmpty(originOrderCodeList)?"":StringUtils.join(originOrderCodeList.toArray(), ","));
                    for (WdtWarehouseRecordDTO wdtWarehouseRecordDTO : wdtRelation.get(dto.getRecordCode())) {
                        FrSaleWdtExtE extInfo  = extInfomap.get(wdtWarehouseRecordDTO.getFront());
                        if(extInfo != null){
                            dto.setSellerMessage(extInfo.getRemark());
                            dto.setTotalAmount(extInfo.getTotalAmount());
                            dto.setPromiseTime(extInfo.getPromiseTime());
                        }
                    }
                    for (String originOrder : originOrderCodeList) {
                        AddressE groupAddress = groupAddressMap.get(originOrder);
                        if(StringUtils.isNotBlank(originOrder) && groupAddress != null){
                            //设置团长信息
                            GroupAddressDTO groupAddressDTO = new GroupAddressDTO();
                            groupAddressDTO.setArea(groupAddress.getProvince()+"/"+groupAddress.getCity()+"/"+groupAddress.getCounty());
                            groupAddressDTO.setMobile(groupAddress.getMobile());
                            groupAddressDTO.setName(groupAddress.getName());
                            groupAddressDTO.setAddress(groupAddress.getAddress());
                            groupAddressDTO.setGroupInfo(groupAddress.getName() +"/"+ groupAddress.getMobile());
                            dto.setGroupAddress(groupAddressDTO);
                        }
                    }


                }
                if (channelInfoMap.containsKey(dto.getChannelCode())) {
                    dto.setChannelCodeName(channelInfoMap.get(dto.getChannelCode()).getChannelName());
                }
                if(poolMap.containsKey(dto.getRecordCode())){
                    RwRecordPoolDo doRecord=poolMap.get(dto.getRecordCode());
                    //更新人
                    dto.setModifier(doRecord.getModifier());
                    dto.setUpdateTime(doRecord.getUpdateTime());
                    //物流公司
                    dto.setLogisticsCode(doRecord.getLogisticsCode());
                    //地址，省市区，详细地址
                    if(addressEMap.containsKey(doRecord.getDoCode())){
                        AddressE  addressE=addressEMap.get(doRecord.getDoCode());
                        dto.setArea(addressE.getProvince()+"/"+addressE.getCity()+"/"+addressE.getCounty());
                        dto.setProvince(addressE.getProvince());
                        dto.setCity(addressE.getCity());
                        dto.setCounty(addressE.getCounty());
                        dto.setAddress(addressE.getAddress());
                        dto.setMobile(addressE.getMobile());
                        dto.setName(addressE.getName());
                    }
                }
                if(recordPackageMap.containsKey(dto.getRecordCode())){
                    List<String> expressCodeList = new ArrayList<>();
                    Set<String> logisticsCode = new HashSet<>();
                    for (RecordPackageDO recordPackageDO : recordPackageMap.get(dto.getRecordCode())) {
                        expressCodeList.add(recordPackageDO.getExpressCode() + "（" + recordPackageDO.getLogisticsCode() + "）");
                        logisticsCode.add(recordPackageDO.getLogisticsCode());
                    }
                    //运单号
                    dto.setExpressCode(StringUtils.join(expressCodeList, "，"));
                    dto.setRealLogisticsCode(StringUtils.join(logisticsCode, "，"));
                }
            }
        }
        return recordDTOPageInfo;
    }

    @Override
    @TargetDataSource(DynamicDataSourceEnum.READ)
    public PageInfo<SaleWarehouseRecordDTO> queryNewByConditionToSynergy(SaleWarehouseRecordCondition condition) {
        if(Objects.nonNull(condition.getStartTime()) && Objects.nonNull(condition.getEndTime())){
            long interval = MyDateFormat.getBetweenDays(condition.getStartTime(),condition.getEndTime());
            if(interval>45){
                throw new RomeException("999", "创建时间范围不能大于45天");
            }
        }
        //wms_code = 15为协同仓
        List<Long> realWarehouseIdList = realWarehouseWmsConfigRepository.getRealWarehouseWmsConfigByWmsCodes(Arrays.asList(15));
        if (CollectionUtil.isEmpty(condition.getRealWarehouseCodeList())) {
            condition.setRealWarehouseIdList(realWarehouseIdList);
        } else {
            List<RealWarehouse> realWarehouseResult = realWarehouseService.queryRealWarehouseByRealWarehouseCodeList(condition.getRealWarehouseCodeList());
            List<Long> realWarehouseIdParamList = realWarehouseResult.stream().map(RealWarehouse::getId).filter(item -> realWarehouseIdList.contains(item)).collect(Collectors.toList());
            if (CollectionUtil.isEmpty(realWarehouseIdParamList)) {
                return new PageInfo<>();
            } else {
                condition.setRealWarehouseIdList(realWarehouseIdParamList);
            }
        }
        condition.setWmsCode(15);
        return queryNewWarehouseRecordList(condition);
    }

    @Override
    public List<WarehouseRecordPostDTO> queryWarehouseRecordDetailByCode(List<String> warehouseRecordCodeList) {
        List<WarehouseRecordPostDTO> list = Lists.newArrayList();
        List<WarehouseRecordDo> warehouseRecordEList = warehouseRecordRepository.queryWarehouseByRecordCodes(warehouseRecordCodeList);
        //已出库单据
        List<String> recordCodeList = warehouseRecordEList.stream().filter(item->Objects.equals(item.getRecordStatus(),WarehouseRecordStatusVO.OUT_ALLOCATION.getStatus())).map(WarehouseRecordDo::getRecordCode).distinct().collect(Collectors.toList());

        //查询单据明细
        List<WarehouseRecordDetail> warehouseRecordDetails = warehouseRecordRepository.queryWarehouseRecordDetailListByCodes(warehouseRecordCodeList);
        List<SkuInfoExtDTO> skuInfoExtDTOS = skuFacade.skusBySkuCode(warehouseRecordDetails.stream().map(t -> t.getSkuCode()).distinct().collect(Collectors.toList()));
        Map<Object, SkuInfoExtDTO> skuCodeMap = RomeCollectionUtil.listforMap(skuInfoExtDTOS, "skuCode");
        warehouseRecordDetails.forEach(e -> {
            if (skuCodeMap.containsKey(e.getSkuCode())) {
                SkuInfoExtDTO skuInfoExtDTO = skuCodeMap.get(e.getSkuCode());
                e.setBarCode(skuInfoExtDTO.getBarCode());
                e.setSkuName(skuInfoExtDTO.getName());
            }
        });
        List<WarehouseRecordDetailDTO> detailDTOList = warehouseRecordDetailConvertor.entityToDto(warehouseRecordDetails);
        Map<String, List<WarehouseRecordDetailDTO>> warehouseRecordDetailMap = RomeCollectionUtil.listforListMap(detailDTOList, "recordCode");
        //物流单号相关查询
        Map<String, List<RecordPackageDTO>> recordPackageMap = Maps.newHashMap();
        if(CollectionUtil.isNotEmpty(recordCodeList)){
            //查询包裹明细
            List<RecordPackageDO> recordPackageList = recordPackageRepository.getRecordPackageByRecordCodeList(recordCodeList);
            List<Long> ids = recordPackageList.stream().map(t -> t.getId()).collect(Collectors.toList());
            List<RecordPackageDetailDO> recordPackageDetailDOS = recordPackageRepository.queryDetailListForList(ids);
            Map<Long, List<RecordPackageDetailDO>> detailMap = recordPackageDetailDOS.stream().collect(Collectors.groupingBy(t -> t.getPackageId()));
            for (RecordPackageDO aDo : recordPackageList) {
                if (detailMap.containsKey(aDo.getId())) {
                    aDo.setDetails(detailMap.get(aDo.getId()));
                }
            }
            List<RecordPackageDTO> recordPackageDTOList = recordPackageConvertor.convertDOList2DTOList(recordPackageList);
            recordPackageMap = RomeCollectionUtil.listforListMap(recordPackageDTOList, "recordCode");
        }
        //封装返回值
        for (String key : warehouseRecordDetailMap.keySet()) {
            WarehouseRecordPostDTO warehouseRecordPostDTO = new WarehouseRecordPostDTO();
            warehouseRecordPostDTO.setWarehouseRecordDetailDTOList(warehouseRecordDetailMap.get(key));
            if (recordPackageMap.containsKey(key)) {
                warehouseRecordPostDTO.setPackages(recordPackageMap.get(key));
            }
            list.add(warehouseRecordPostDTO);
        }
        return list;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void reCalculateWarehouse(ReCalculateDTO dto) {
        String orderCode=dto.getRecordCode();
        //先根据单据取出后置单
        WarehouseRecordE record = warehouseRecordRepository.getRecordWithDetailByCode(orderCode, null);
        AlikAssert.isNotNull(record, ResCode.STOCK_ERROR_1017, ResCode.STOCK_ERROR_1017_DESC + ":" + orderCode);
        //判断后置单是否已撤回
        if (!(WmsSyncStatusVO.BACKCANCLE.getStatus().equals(record.getSyncWmsStatus()) || WmsSyncStatusVO.STOPDELIVERY.getStatus().equals(record.getSyncWmsStatus()))) {
            throw new RomeException(ResCode.STOCK_ERROR_1003, "单据下发状态不是已撤回或者停发：" + orderCode);
        }
        //单据不是初始状态
        if (!WarehouseRecordStatusVO.INIT.getStatus().equals(record.getRecordStatus())) {
            throw new RomeException(ResCode.STOCK_ERROR_1003, "单据不是初始状态：" + orderCode);
        }
        if (WarehouseRecordTypeVO.WINE_BUCKET_SALE_WAREHOUSE_RECORD.getType().equals(record.getRecordType())) {
            throw new RomeException(ResCode.STOCK_ERROR_2015, "沪威酒【桶订单】不可操作" + orderCode);
        }
        //根据后置单查询对应未取消的pool表及明细和对应前置单单号及版本号
        List<RwRecordPoolE> pools = rwRecordPoolRepository.queryKeysWithDetailsByWarehouseId(record.getId());
        //查询当前渠道下的所有虚仓库存，包装查询虚仓库存,去除当前后置单对应虚仓，遍历多个虚仓的库存，同时满足当前所有物料即可
        Long vwId = pools.get(0).getVirtualWarehouseId();
        VirtualWarehouseE virtualWarehouseE = virtualWarehouseRepository.getVirtualWarehouseById(vwId);
        if (null == virtualWarehouseE) {
            throw new RomeException(ResCode.STOCK_ERROR_1003, "虚仓不存在");
        }
//        List<VirtualWarehouseE> groupList=virtualWarehouseRepository.queryByGroupId(virtualWarehouseE.getVirtualWarehouseGroupId());
        List<VirtualWarehouseE> groupList = virtualWarehouseService.getVwListByChannelCode(record.getChannelCode());
        // 过滤掉停发仓
        AddressE addr = addressRepository.queryByRecordCode(pools.get(0).getDoCode());
        if(groupList != null && groupList.size() > 0 && addr != null) {
        	VirtualWarehouseE temp;
        	RouteWarehouseDTO routeWarehouseDTO;
        	for(int i = 0; i < groupList.size(); i++) {
        		temp = groupList.get(i);
        		routeWarehouseDTO = warehouseRouteRedis.getWarehouseRouteInfoByCoverFocusSale(temp.getRealWarehouseId(), addr.getProvinceCode(), addr.getCityCode(), addr.getCountyCode(), addr.getAreaCode(), null);
        		if(routeWarehouseDTO != null && routeWarehouseDTO.isRwStop()) {
        			groupList.remove(i);
    				i--;
        		}
        	}
        }
        List<Long> virtualdList=groupList.stream().filter(v->!v.getId().equals(vwId)).map(VirtualWarehouseE::getId).collect(Collectors.toList());
        List<WarehouseRecordDetail> details=record.getWarehouseRecordDetails();
        //重新锁定虚仓ID
        Long resultVirtualId=null;
        for(Long virtualId:virtualdList){
            boolean sufficient = true;
            List<CoreVirtualWarehouseStockDO> coreVirtualWarehouseStockDOs = new ArrayList<>();
            CoreVirtualWarehouseStockDO coreVirtualWarehouseStockDO;
            for (WarehouseRecordDetail item : details) {
                coreVirtualWarehouseStockDO = new CoreVirtualWarehouseStockDO();
                coreVirtualWarehouseStockDO.setVirtualWarehouseId(virtualId);
                coreVirtualWarehouseStockDO.setSkuId(item.getSkuId());
                coreVirtualWarehouseStockDOs.add(coreVirtualWarehouseStockDO);
            }
            coreVirtualWarehouseStockDOs = coreVirtualWarehouseStockRepository.getVWStock(coreVirtualWarehouseStockDOs);
            if(CollectionUtils.isEmpty(coreVirtualWarehouseStockDOs)){
                continue;
            }
            Map<Long, CoreVirtualWarehouseStockDO> stockMap = RomeCollectionUtil.listforMap(coreVirtualWarehouseStockDOs, "skuId");
            for (WarehouseRecordDetail item : details) {
                CoreVirtualWarehouseStockDO vStock = stockMap.get(item.getSkuId());
                if (vStock == null || vStock.getAvailableQty().compareTo(item.getPlanQty()) < 0) {
                    //有一个库存不满足都不行
                    sufficient = false;
                    break;
                }
            }
            if(sufficient){
                resultVirtualId=virtualId;
                break;
            }
        }
        if(null == resultVirtualId){
            throw new RomeException(ResCode.STOCK_ERROR_1001 , "仓库无法满足整单发货");
        }
        VirtualWarehouseE resultVirtualWarehouseE=virtualWarehouseRepository.getVirtualWarehouseById(resultVirtualId);
        BatchCancleDTO batchCancleDTO=new BatchCancleDTO();
        batchCancleDTO.setRealWarehouseId(resultVirtualWarehouseE.getRealWarehouseId());
        batchCancleDTO.setIsDefault(1);
        batchCancleDTO.setUserId(dto.getUserId());
        batchCancleDTO.setModifier(-1L);
        this.changeWarehouse(orderCode, batchCancleDTO);
    }


    /**
     * 查询出库单编码跟前置单的关系map
     * key为出库单id，value为对应前置单外码【这里为订单编码】的list
     *
     * condition
     * @return
     */
    private List<WdtWarehouseRecordDTO> queryWdtFrSaleFrontAndWarehouseRelation(SaleWarehouseRecordCondition condition) {
        if(CollectionUtils.isEmpty(condition.getOriginOrderCodeList())
                && CollectionUtils.isEmpty(condition.getOrderCodeList())
                && CollectionUtils.isEmpty(condition.getRecordCodeList())
                && CollectionUtils.isEmpty(condition.getIsPreSaleList())
                && condition.getPromiseStartTime() ==null
               ){
            return new ArrayList<>();
        }
        return frWDTSaleRepository.queryWdtFrSaleFrontAndWarehouseRelation(condition);
    }
    
    /**
     * 计算电商停发,根据仓库Id,只计算创建时间，近6个月的单子,异步方法
     * @param realWarehouseId
     * @param userId
     */
    @Override
    @Async("coreStockTask")
    public void calculateStopDeliveryByRwIdAsyn(Long realWarehouseId, Long userId) {
    	calculateStopDeliveryByRwId(realWarehouseId, userId);
    }
    
    /**
     * 计算电商停发,根据仓库Id,只计算创建时间，近6个月的单子
     * @param realWarehouseId
     * @param userId
     */
    @Override
    public void calculateStopDeliveryByRwId(Long realWarehouseId, Long userId) {
    	if(realWarehouseId == null) {
    		return;
    	}
    	RealWarehouse realWarehouse = realWarehouseService.findByRealWarehouseId(realWarehouseId);
    	if(realWarehouse == null) {
    		return;
    	}
    	// 非电商发货仓
    	if(!RealWarehouseTypeVO.RW_TYPE_17.getType().equals(realWarehouse.getRealWarehouseType())) {
    		log.error("计算电商停发,根据仓库Id,此仓不支持,只有电商发货仓才支持,Id={},code={},type={}", realWarehouseId, realWarehouse.getRealWarehouseCode(), realWarehouse.getRealWarehouseType());
    		return;
    	}
    	log.error("计算电商停发,根据仓库Id,开始,记录一下,Id={},code={}", realWarehouseId, realWarehouse.getRealWarehouseCode());
    	// 电商路由-仓库停发地址自动计算单据时间,单位月数，比如3，代表，最近3个月的单据
    	int gapMonth = Integer.parseInt(BaseinfoConfiguration.getInstance().get("route.stopdelivery", "calmonthnum"));
    	Date now = new Date();
    	Date startTime = DateUtil.offsiteDate(now, Calendar.MONTH, -gapMonth);
    	Date endTime = DateUtil.offsiteDate(startTime, Calendar.DAY_OF_YEAR, 10);
    	int currentPage = 1;
		// 每页大小 
		final int pageSize = 1000;
		List<WarehouseRecordE> list;
		String reasonsByStopKey = getCancleReasonsByStopKey();
		List<Integer> syncWmsStatusList = new ArrayList<>(); // 1,2,3,4
		syncWmsStatusList.add(WmsSyncStatusVO.UNSYNCHRONIZED.getStatus());
		syncWmsStatusList.add(WmsSyncStatusVO.SYNCHRONIZED.getStatus());
		syncWmsStatusList.add(WmsSyncStatusVO.BACKCANCLE.getStatus());
		syncWmsStatusList.add(WmsSyncStatusVO.STOPDELIVERY.getStatus());
    	while(startTime.getTime() < now.getTime()) {
//    		System.out.println("startTime = " + DateUtil.dateToString(startTime, "yyyy-MM-dd HH:mm:ss")
//    		+ ", endTime = " + DateUtil.dateToString(endTime, "yyyy-MM-dd HH:mm:ss"));
    		currentPage = 1;
    		do {
    			list = warehouseRecordRepository.queryNeedCalculateStopDeliveryList(realWarehouseId, syncWmsStatusList, startTime, endTime, (currentPage-1) * pageSize, pageSize);
    			currentPage++;
    			innerCalculateStopDeliveryByList(list, userId, reasonsByStopKey, realWarehouse);
    			
    		} while (list != null && list.size() == pageSize);
    		startTime = endTime;
    		endTime = DateUtil.offsiteDate(startTime, Calendar.DAY_OF_YEAR, 10);
    		startTime.setTime(startTime.getTime() + 1000);
    		if(endTime.getTime() > now.getTime()) {
    			endTime = now;
    		}
    	}
    	log.error("计算电商停发,根据仓库Id,结束,记录一下,Id={},code={}", realWarehouseId, realWarehouse.getRealWarehouseCode());
    }
    
    /**
     * 计算电商停发,根据单据列表,异步方法
     * @param recordCodeList
     * @param userId
     */
    @Override
    @Async("coreStockTask")
    public void calculateStopDeliveryByRecordCodeListAsyn(List<String> recordCodeList, Long userId) {
    	calculateStopDeliveryByRecordCodeList(recordCodeList, userId);
    }
    
    /**
     * 计算电商停发,根据单据列表
     * @param recordCodeList
     * @param userId
     */
    @Override
    public String calculateStopDeliveryByRecordCodeList(List<String> recordCodeList, Long userId) {
    	if(recordCodeList == null || recordCodeList.size() == 0) {
    		return "";
    	}
    	log.error("计算电商停发,根据单据列表,开始,记录一下,codeList={}", recordCodeList);
    	StringBuffer errorMsg = new StringBuffer();
    	List<WarehouseRecordE> list = warehouseRecordRepository.listRecordByRecordCodes(recordCodeList);
    	Map<String,WarehouseRecordE> recordMap = list.stream().collect(Collectors.toMap(WarehouseRecordE::getRecordCode, Function.identity(), (v1, v2) -> v1));
    	WarehouseRecordE record;
    	Map<Long, RealWarehouse> rwMap = new HashMap<>(8);
    	List<WarehouseRecordE> listData = new ArrayList<>();
    	for(String recordCode : recordCodeList) {
    		record = recordMap.get(recordCode);
    		if(record == null) {
    			errorMsg.append("不存在：").append(recordCode).append(",");
    			continue;
    		}
    		// 单据类型
    		if(!(WarehouseRecordTypeVO.ONLINE_RETAILERS_OUT_RECORD.getType().equals(record.getRecordType())
        	    	|| WarehouseRecordTypeVO.WDT_ONLINE_RETAILERS_OUT_RECORD.getType().equals(record.getRecordType())
        	    	|| WarehouseRecordTypeVO.CROSS_RETAILERS_OUT_RECORD.getType().equals(record.getRecordType()))) {
    			errorMsg.append("单据类型不支持：").append(recordCode).append(",");
    			continue;
    		}
    		// 单据状态
    		if(!WarehouseRecordStatusVO.INIT.getStatus().equals(record.getRecordStatus())) {
    			errorMsg.append("单据状态已发货或者取消：").append(recordCode).append(",");
    			continue;
    		}
    		// 同步WMS状态
    		if(!(WmsSyncStatusVO.UNSYNCHRONIZED.getStatus().equals(record.getSyncWmsStatus())
					 || WmsSyncStatusVO.SYNCHRONIZED.getStatus().equals(record.getSyncWmsStatus())
					 || WmsSyncStatusVO.BACKCANCLE.getStatus().equals(record.getSyncWmsStatus())
					 || WmsSyncStatusVO.STOPDELIVERY.getStatus().equals(record.getSyncWmsStatus()))) {
    			errorMsg.append("单据同步WMS状态不支持：").append(recordCode).append(",");
    			continue;
    		}
    		// 仓库
    		RealWarehouse realWarehouse = rwMap.get(record.getRealWarehouseId());
			if(realWarehouse == null) {
				realWarehouse = realWarehouseService.findByRealWarehouseId(record.getRealWarehouseId());
				if(realWarehouse != null) {
					rwMap.put(record.getRealWarehouseId(), realWarehouse);
				} else {
					errorMsg.append("单据对应仓库不存在：").append(recordCode).append(",");
					continue;
				}
			}
			if(!RealWarehouseTypeVO.RW_TYPE_17.getType().equals(realWarehouse.getRealWarehouseType())) {
				errorMsg.append("单据对应仓不支持，只有电商发货仓才支持：").append(recordCode).append(",");
				continue;
    		}
			listData.add(record);
    	}
    	if(listData.size() == 0) {
    		return errorMsg.toString();
    	}
    	Map<Long, List<WarehouseRecordE>> rwIdMap = listData.stream().collect(Collectors.groupingBy(WarehouseRecordE::getRealWarehouseId));
    	String reasonsByStopKey = getCancleReasonsByStopKey();
    	Set<Entry<Long, List<WarehouseRecordE>>> set = rwIdMap.entrySet();
    	for(Entry<Long, List<WarehouseRecordE>> data : set){
    		innerCalculateStopDeliveryByList(data.getValue(), userId, reasonsByStopKey, rwMap.get(data.getKey()));
    	}
    	return errorMsg.toString();
    }
    
    /**
     * 计算电商停发，根据单据
     * @param list 单据list
     * @param userId 停发操作用户
     * @param reasonsByStopKey 停发key (撤单时填的原因)
     * @param realWarehouse 仓库
     */
    private void innerCalculateStopDeliveryByList(List<WarehouseRecordE> list, Long userId, String reasonsByStopKey, RealWarehouse realWarehouse) {
    	if(list == null || list.size() == 0) {
    		return;
    	}
    	List<String> recordCodeList = list.stream().map(WarehouseRecordE::getRecordCode).collect(Collectors.toList());
    	List<RwRecordPoolDo> poolList = rwRecordPoolRepository.queryPoolByWarehouseRecordCodes(recordCodeList);
    	Map<String, List<RwRecordPoolDo>> poolMap = poolList.stream().filter(dto->dto.getDoCode() != null).collect(Collectors.groupingBy(RwRecordPoolDo::getWarehouseRecordCode));
    	List<String> doCodeList = poolList.stream().filter(dto->dto.getDoCode() != null).map(RwRecordPoolDo::getDoCode).collect(Collectors.toList());
    	if(doCodeList == null || doCodeList.size() == 0) {
    		return;
    	}
    	List<AddressE> addressEList = addressRepository.queryByRecordCodes(doCodeList);
        Map<String,AddressE> addressEMap = addressEList.stream().collect(Collectors.toMap(AddressE::getRecordCode, Function.identity(), (v1, v2) -> v1));
        for(WarehouseRecordE record : list) {
        	try {
        		if(!poolMap.containsKey(record.getRecordCode())) {
        			log.error("计算电商停发,池子里没有此后置单，记录一下，recordCode={}", record.getRecordCode());
        			continue;
        		}
        		if(!addressEMap.containsKey(poolMap.get(record.getRecordCode()).get(0).getDoCode())) {
        			log.error("计算电商停发,此后置单对应地址信息没有，记录一下，recordCode={}", record.getRecordCode());
        			continue;
        		}
        		SpringBeanUtil.getBean(ShopRetailService.class).innerCalculateStopDelivery(record, poolMap.get(record.getRecordCode()), addressEMap.get(poolMap.get(record.getRecordCode()).get(0).getDoCode()), userId, reasonsByStopKey, realWarehouse);
			} catch (Exception e) {
				log.error("计算电商停发,处理数据出错，recordCode={},{}", record.getRecordCode(), e);
			}
        }
    }
    
    /**
     * 内部方法，计算电商停发，并处理
     * @param record 单据
     * @param pools 
     * @param addr 地址
     * @param userId 用户Id
     * @param reasonsByStopKey 停发原因key
     * @param realWarehouse 仓库
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void innerCalculateStopDelivery(WarehouseRecordE record, List<RwRecordPoolDo> pools, AddressE addr, Long userId, String reasonsByStopKey, RealWarehouse realWarehouse) {
    	if(!WarehouseRecordStatusVO.INIT.getStatus().equals(record.getRecordStatus())) {
    		throw new RomeException(ResCode.STOCK_ERROR_1003, "单据不是初始状态：" + record.getRecordCode());
    	}
    	// 停发标识
    	boolean rwStop = false;
    	// 停发人
    	Long rwStopper = null;
    	Long opterId = userId;
    	RouteWarehouseDTO routeWarehouseDTO = warehouseRouteRedis.getWarehouseRouteInfoByCoverFocusSale(record.getRealWarehouseId(), addr.getProvinceCode(), addr.getCityCode(), addr.getCountyCode(), addr.getAreaCode(), null);
    	if(routeWarehouseDTO != null && routeWarehouseDTO.isRwStop()) {
    		rwStop = routeWarehouseDTO.isRwStop();
    		rwStopper = routeWarehouseDTO.getRwStopper();
    		if(rwStopper != null && rwStopper.longValue() > 0L) {
    			opterId = rwStopper;
    		}
    	}
    	// 日志类型
    	Integer logType;
    	String orderCode = record.getRecordCode();
    	String logTxt;
    	String trackEventName;
    	String trackExt;
    	// 同步WMS状态：1-未同步    停发时1->4,否则不变
    	if(WmsSyncStatusVO.UNSYNCHRONIZED.getStatus().equals(record.getSyncWmsStatus())) {
    		if(!rwStop) {
    			return;
    		}
    		int i = warehouseRecordRepository.updateWmsStatusByStopDelivery(record.getId(), opterId, record.getSyncWmsStatus(), WmsSyncStatusVO.STOPDELIVERY.getStatus(), record.getVersionNo());
        	AlikAssert.isTrue(i > 0, ResCode.STOCK_ERROR_1026, "计算电商停发," + ResCode.STOCK_ERROR_1026_DESC);
        	logType = WDTRecordConst.OPERATE_STOP_DELIVERY;
        	logTxt = realWarehouse.getRealWarehouseCode();
        	trackEventName = "仓库停发";
        	trackExt = "出库单：" + orderCode + "，仓库：" + realWarehouse.getRealWarehouseCode();
    	} else if(WmsSyncStatusVO.SYNCHRONIZED.getStatus().equals(record.getSyncWmsStatus())) {
    		// 同步WMS状态：2-已同步    停发时2->3,否则不变
    		if(!rwStop) {
    			return;
    		}
    		if(reasonsByStopKey == null) {
    			throw new RomeException(ResCode.STOCK_ERROR_1003, "停发原因没有配：" + record.getRecordCode());
    		}
    		// 验证仓库是否此项操作，在配置中,即支持下发到wms撤回，已同步到撤回， 不支持直接返回
    		String syncToCancleRwCodes = BaseinfoConfiguration.getInstance().get("route.stopdelivery", "synctocanclerwcodes");
    		if(StringUtils.isBlank(syncToCancleRwCodes)) {
    			return;
    		}
    		if(syncToCancleRwCodes.indexOf(realWarehouse.getRealWarehouseCode()) < 0) {
    			return;
    		}
    		List<String> codeList = Arrays.asList(syncToCancleRwCodes.split(","));
    		if(!codeList.contains(realWarehouse.getRealWarehouseCode())) {
    			return;
    		}
    		//修改单据为已撤回
            Integer isSucc = warehouseRecordRepository.updateWmsStatusToBackCancle(record.getId(), opterId, reasonsByStopKey, "停发", record.getVersionNo());
            AlikAssert.isTrue(isSucc > 0, ResCode.STOCK_ERROR_2014, ResCode.STOCK_ERROR_2014_DESC);
            //如果下发了wms  需要同步取消wms
            boolean cancleStatus = wmsOutService.orderCancel(record.getRecordCode());
            AlikAssert.isTrue(cancleStatus, ResCode.STOCK_ERROR_1205, ResCode.STOCK_ERROR_1205_DESC);
            logType = WDTRecordConst.OPERATE_CANCEL_WAREHOUSE_RECORD;
        	logTxt = "撤回";
        	trackEventName = "出库单撤单";
        	trackExt = "撤销出库单" + orderCode;
    	} else if(WmsSyncStatusVO.BACKCANCLE.getStatus().equals(record.getSyncWmsStatus())) {
    		// 同步WMS状态：3-已撤回  撤回原因=停发  不是停发时3->1,否则不变
    		if(rwStop) {
    			return;
    		}
    		if(reasonsByStopKey == null || !reasonsByStopKey.equals(record.getReasons())) {
    			return;
    		}
    		RwRecordPoolDo pool  = pools.get(0);
    		// 查一下明细塞进去
    		record.setWarehouseRecordDetails(warehouseRecordRepository.queryDetailListByRecordId(record.getId()));
            // 这里需要为强制同步wms
            this.cancleAndCreta(pool.getRealWarehouseId(), pool.getVirtualWarehouseId(), record,null, false);
            logType = WDTRecordConst.OPERATE_PUSH_AGAIN;
        	logTxt = record.getRecordCode();
        	trackEventName = "重新推送出库单";
        	trackExt = "原单:" + orderCode + "，新单：" + record.getRecordCode();
    	} else if(WmsSyncStatusVO.STOPDELIVERY.getStatus().equals(record.getSyncWmsStatus())) {
    		// 同步WMS状态：4-停发  不是停发时4->1,否则不变
    		if(rwStop) {
    			return;
    		}
    		int i = warehouseRecordRepository.updateWmsStatusByStopDelivery(record.getId(), opterId, record.getSyncWmsStatus(), WmsSyncStatusVO.UNSYNCHRONIZED.getStatus(), record.getVersionNo());
        	AlikAssert.isTrue(i > 0, ResCode.STOCK_ERROR_1026, "计算电商停发," + ResCode.STOCK_ERROR_1026_DESC);
        	logType = WDTRecordConst.OPERATE_STOP_DELIVERY_CANCEL;
        	logTxt = realWarehouse.getRealWarehouseCode();
        	trackEventName = "仓库停发恢复发货";
        	trackExt = "出库单：" + orderCode + "，仓库：" + realWarehouse.getRealWarehouseCode();
    	} else {
    		// 其他不处理
    		return;
    	}
    	//保存订单轨迹以及操作日志
        try {
            List<OnlineRetailE> retailEList = orderTrackFacade.queryOnLineRetailByRecordCode(record.getRecordCode());
            for (OnlineRetailE retailE : retailEList) {
                orderTrackFacade.save(retailE.getOutRecordCode(), trackEventName, trackExt);
                //记录操作日志
                WDTLogDTO wdtLogDTO = new WDTLogDTO();
                wdtLogDTO.setOriginOrderCode(retailE.getOriginOrderCode());
                wdtLogDTO.setRecordCode(retailE.getRecordCode());
                wdtLogDTO.setOutRecordCode(retailE.getOutRecordCode());
                wdtLogDTO.setType(logType);
                wdtLogDTO.addChangedKeyValue(orderCode ,logTxt);
                wdtLogDTO.setCreator(opterId);
                wdtLogDTO.setModifier(-2L);
                frWDTSaleRepository.saveLog(wdtLogDTO);
            }
        } catch (Exception e) {
            log.error(e.getMessage(), e);
        }
    }
    
    /**
     * 获取，取消原因为停发的key
     * @return
     */
    @Override
    public String getCancleReasonsByStopKey() {
    	Map<String, String> map = baseDataFacade.selectDictionaryByTypeCodeForMap("APP_CANCEL_REASON");
    	if(map == null || map.size() == 0) {
    		return null;
    	}
    	Set<Entry<String, String>> set = map.entrySet();
    	for(Entry<String, String> entry : set) {
    		if("停发".equals(entry.getValue())) {
    			return entry.getKey();
    		}
    	}
    	return null;
    }


    /**
	 * 查询需要重新计算撤回或停发后置单数据的列表数据
	 * @param condition
	 * @return
	 */
	@Override
	public List<WarehouseRecordPageDTO> queryNeedReCalWrCancelList(WarehouseRecordPageDTO condition) {
		List<Integer> syncWmsStatusList = new ArrayList<>(); // 3,4
		syncWmsStatusList.add(WmsSyncStatusVO.BACKCANCLE.getStatus());
		syncWmsStatusList.add(WmsSyncStatusVO.STOPDELIVERY.getStatus());
		List<WarehouseRecordE> list = warehouseRecordRepository.queryNeedCalculateStopDeliveryList(condition.getRealWarehouseId(), syncWmsStatusList, condition.getStartDate(), condition.getEndDate(), (condition.getPageIndex()-1) * condition.getPageSize(), condition.getPageSize());
		return warehouseRecordConvertor.entityToWarehouseDto(list);
	}


}
