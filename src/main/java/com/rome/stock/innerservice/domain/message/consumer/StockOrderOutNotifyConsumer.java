package com.rome.stock.innerservice.domain.message.consumer;

import com.alibaba.fastjson.JSONObject;
import com.rome.scm.common.rocketmq.CustomConsumerListener;
import com.rome.stock.common.enums.CustomRocketMQEnum;
import com.rome.stock.innerservice.api.dto.warehouserecord.OutConfirmMQDTO;
import com.rome.stock.innerservice.common.StringUtils;
import com.rome.stock.innerservice.domain.service.WhAllocationService;
import lombok.extern.slf4j.Slf4j;
import org.apache.rocketmq.common.message.MessageExt;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;

/**
 * @Description: 发货回传订单中心消费mq
 */
@Slf4j
@Service
public class StockOrderOutNotifyConsumer implements CustomConsumerListener<MessageExt> {

    @Resource
    private WhAllocationService whAllocationService;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void onMessage(MessageExt messageExt, String businessNo, String msgKey) {
        log.info("发货回传订单中心消息消费，msgID: {}，消费次数: {}", messageExt.getMsgId(), messageExt.getReconsumeTimes());
        String json = new String(messageExt.getBody());
        OutConfirmMQDTO outConfirmMQDTO;
        //兼容messageExt.getBody()新老消息内容，老消息record,新消息OutConfirmMQDTO
        if (StringUtils.isNotEmpty(json) && json.startsWith("{")){
            outConfirmMQDTO= JSONObject.parseObject(json, OutConfirmMQDTO.class);
        }else {
            outConfirmMQDTO=new OutConfirmMQDTO();
            outConfirmMQDTO.setRecordCode(json);
        }
        if(StringUtils.isEmpty(outConfirmMQDTO.getRecordCode())) {
            log.error("发货回传订单中心消息消费内容为空，msgID: {}", messageExt.getMsgId());
            return ;
        }
        //单据发货完成通知订单中心
        whAllocationService.pushOutNotify(outConfirmMQDTO);
    }

    @Override
    public String getRocketMQCode() {
        return CustomRocketMQEnum.MQ_STOCK_ORDER_OUT_NOTIFY_PUSH.getCode();
    }

}
