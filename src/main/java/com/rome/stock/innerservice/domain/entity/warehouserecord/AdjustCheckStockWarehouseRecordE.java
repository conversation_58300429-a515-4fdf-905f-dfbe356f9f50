package com.rome.stock.innerservice.domain.entity.warehouserecord;

import com.rome.arch.core.domain.EntityFactory;
import com.rome.stock.common.enums.warehouse.WarehouseRecordTypeVO;
import com.rome.stock.innerservice.constant.WarehouseRecordBusinessTypeVO;

import com.rome.stock.innerservice.constant.WmsSyncStatusVO;
import com.rome.stock.innerservice.domain.entity.frontrecord.AdjustCheckStockRecordDetailE;
import com.rome.stock.innerservice.domain.entity.frontrecord.AdjustCheckStockRecordE;
import com.rome.stock.innerservice.domain.entity.frontrecord.AdjustMerchantRecordDetailE;
import com.rome.stock.innerservice.domain.entity.frontrecord.AdjustMerchantRecordE;
import com.rome.stock.innerservice.domain.repository.frontrecord.FrAdjustCheckStockRepository;
import com.rome.stock.innerservice.domain.repository.frontrecord.FrAdjustMerchantRepository;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;

/**
 * 库存对比调整
 *
 */
@Data
@Slf4j
@Component
@Scope("prototype")
@EqualsAndHashCode(callSuper = false)
public class AdjustCheckStockWarehouseRecordE extends AbstractWarehouseRecord {


    @Resource
    private EntityFactory entityFactory;
    @Resource
    private FrAdjustCheckStockRepository frAdjustCheckStockRepository;

    /**
     * 保存入库单
     */
    public void addWarehouseRecord() {
        //保存入库单
        long id = frAdjustCheckStockRepository.saveWarehouseRecord(this);
        this.setId(id);
        this.warehouseRecordDetails.forEach(record -> record.setWarehouseRecordDetail(this));
        frAdjustCheckStockRepository.saveWarehouseRecordDetails(this.warehouseRecordDetails);
        this.addFrontRecordAndWarehouseRelation();
    }


    /**
     * 根据前置单生成出库单
     */
    public void createRecordByFrontRecord(AdjustCheckStockRecordE frontRecord , WarehouseRecordBusinessTypeVO typeVO ) {
        if (WarehouseRecordBusinessTypeVO.OUT_WAREHOUSE_RECORD.getType().equals(typeVO.getType())) {
            createRecodeCode(WarehouseRecordTypeVO.ADJUST_CHECK_WAREHOUSE_OUT_RECORD.getCode());
            this.setBusinessType(WarehouseRecordBusinessTypeVO.OUT_WAREHOUSE_RECORD.getType());
            this.setRecordType(WarehouseRecordTypeVO.ADJUST_CHECK_WAREHOUSE_OUT_RECORD.getType());
        }else if (WarehouseRecordBusinessTypeVO.IN_WAREHOUSE_RECORD.getType().equals(typeVO.getType())) {
            createRecodeCode(WarehouseRecordTypeVO.ADJUST_CHECK_WAREHOUSE_IN_RECORD.getCode());
            this.setBusinessType(WarehouseRecordBusinessTypeVO.IN_WAREHOUSE_RECORD.getType());
            this.setRecordType(WarehouseRecordTypeVO.ADJUST_CHECK_WAREHOUSE_IN_RECORD.getType());
        }
        this.setAbstractFrontRecord(frontRecord);
        this.setRealWarehouseId(frontRecord.getRealWarehouseId());
        this.setDeliveryTime(frontRecord.getOutCreateTime());
        this.setMerchantId(frontRecord.getMerchantId());
        this.setOutCreateTime(frontRecord.getOutCreateTime());
        this.setSyncWmsStatus(WmsSyncStatusVO.NO_REQUIRED.getStatus());
        List<AdjustCheckStockRecordDetailE> frontRecordDetails = frontRecord.getDetails();
        this.setWarehouseRecordDetails(new ArrayList<>(frontRecordDetails.size()));

        for (AdjustCheckStockRecordDetailE detailE : frontRecordDetails) {
            WarehouseRecordDetail warehouseRecordDetail = entityFactory.createEntity(WarehouseRecordDetail.class);
            warehouseRecordDetail.createRecordDetailByActualFrontRecord(detailE);
            this.warehouseRecordDetails.add(warehouseRecordDetail);
        }
        //设置skuCode和skuID
        this.initWarehouseRecodeDetail();

    }



}
