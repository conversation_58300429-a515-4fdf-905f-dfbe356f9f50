package com.rome.stock.innerservice.domain.message;

import cn.hutool.core.collection.CollectionUtil;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.rome.arch.core.clientobject.Response;
import com.rome.arch.core.exception.RomeException;
import com.rome.scm.common.rocketmq.CustomConsumerListener;
import com.rome.stock.common.enums.CustomRocketMQEnum;
import com.rome.stock.common.utils.DateUtil;
import com.rome.stock.innerservice.api.dto.WarehouseRecordDetailDTO;
import com.rome.stock.innerservice.common.ResCode;
import com.rome.stock.innerservice.domain.entity.RealWarehouseE;
import com.rome.stock.innerservice.domain.entity.warehouserecord.WarehouseRecordDetail;
import com.rome.stock.innerservice.domain.entity.warehouserecord.WarehouseRecordE;
import com.rome.stock.innerservice.domain.repository.RealWarehouseRepository;
import com.rome.stock.innerservice.domain.repository.SapInterfaceLogRepository;
import com.rome.stock.innerservice.domain.repository.warehouserecord.WarehouseRecordRepository;
import com.rome.stock.innerservice.remote.ResponseValidator;
import com.rome.stock.innerservice.remote.purchase.dto.OrderPoDTO;
import com.rome.stock.innerservice.remote.purchase.dto.PrCreateResultDTO;
import com.rome.stock.innerservice.remote.purchase.dto.PurchasePrCreateDTO;
import com.rome.stock.innerservice.remote.purchase.dto.PurchasePrCreateDetailDTO;
import com.rome.stock.innerservice.remote.purchase.facade.PurchaseCenterFacade;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.rocketmq.common.message.MessageExt;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * @Description: 电商见单退货通知采购创建退厂单
 */
@Slf4j
@Service
public class OnlineReturnPurchaseCreateConsumer implements CustomConsumerListener<MessageExt> {

    @Resource
    private PurchaseCenterFacade purchaseCenterFacade;
    @Resource
    private WarehouseRecordRepository warehouseRecordRepository;
    @Resource
    private RealWarehouseRepository realWarehouseRepository;

    @Override
    public void onMessage(MessageExt messageExt, String businessNo, String msgKey) {
        String recordCode = new String(messageExt.getBody());
        if(StringUtils.isEmpty(recordCode)) {
            log.error("电商见单退货通知采购创建退厂单，msgID: {}", messageExt.getMsgId());
            return ;
        }
        WarehouseRecordE warehouseRecordE = warehouseRecordRepository.getRecordWithDetailByCode(recordCode, null);
        if(Objects.isNull(warehouseRecordE)){
            log.error("电商见单退货通知采购创建退厂单，msgID: {},单据不存在,recordCode,{}", messageExt.getMsgId(),warehouseRecordE.getRecordCode());
            return ;
        }
        RealWarehouseE realWarehouseE=realWarehouseRepository.getRealWarehouseById(warehouseRecordE.getRealWarehouseId());
        PurchasePrCreateDTO purchasePrCreateDTO = new PurchasePrCreateDTO();
        purchasePrCreateDTO.setFactoryCode(realWarehouseE.getFactoryCode());
        purchasePrCreateDTO.setWarehouseCode(realWarehouseE.getRealWarehouseOutCode());
        purchasePrCreateDTO.setWarehouseName(realWarehouseE.getRealWarehouseName());
        purchasePrCreateDTO.setRemark("电商见单退货通知采购创建退厂单");
        purchasePrCreateDTO.setPlanRefundDate(DateUtil.now());
        //交易单号
        purchasePrCreateDTO.setSourceOrderNo(warehouseRecordE.getSapOrderCode());
        purchasePrCreateDTO.setRealWarehouseId(warehouseRecordE.getRealWarehouseId());
        List<PurchasePrCreateDetailDTO> skuDetails = Lists.newArrayList();
        for (WarehouseRecordDetail warehouseRecordDetail : warehouseRecordE.getWarehouseRecordDetails()) {
            PurchasePrCreateDetailDTO purchasePrCreateDetailDTO = new PurchasePrCreateDetailDTO();
            purchasePrCreateDetailDTO.setSkuCode(warehouseRecordDetail.getSkuCode());
            purchasePrCreateDetailDTO.setUnitCode(warehouseRecordDetail.getUnitCode());
            //直接使用计划数量
            purchasePrCreateDetailDTO.setPlanRefundQty(warehouseRecordDetail.getPlanQty());
            skuDetails.add(purchasePrCreateDetailDTO);
        }
        purchasePrCreateDTO.setSkuDetails(skuDetails);
        List<PurchasePrCreateDTO> list = Lists.newArrayList();
        list.add(purchasePrCreateDTO);
        Response<List<PrCreateResultDTO>> response = purchaseCenterFacade.createPr(list,recordCode);
        if(!ResponseValidator.validResponse(response)){
            log.error("电商见单退货通知采购创建退厂单，recordCode: {},报错:{}", warehouseRecordE.getRecordCode(),response.getMsg());
            throw new RomeException(ResCode.STOCK_ERROR_1001,response.getMsg());
        }
        List<PrCreateResultDTO> resultList = response.getData();
        Map<String,String> skuPrNoMap= Maps.newHashMap();
        if(CollectionUtil.isNotEmpty(resultList)){
            for (PrCreateResultDTO prCreateResultDTO : resultList) {
                List<PrCreateResultDTO.PrSkuDetail> prSkuList =prCreateResultDTO.getPrSkuList();
                for (PrCreateResultDTO.PrSkuDetail prSkuDetail : prSkuList) {
                    for (String skuCode : prSkuDetail.getSkuList()) {
                        skuPrNoMap.put(skuCode,prSkuDetail.getPrNo());
                    }
                }
            }
        }
        List<WarehouseRecordDetailDTO> detailDTOS = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(warehouseRecordE.getWarehouseRecordDetails())) {
            for (WarehouseRecordDetail warehouseRecordDetail : warehouseRecordE.getWarehouseRecordDetails()) {
                if(!skuPrNoMap.containsKey(warehouseRecordDetail.getSkuCode())){
                    continue;
                }
                WarehouseRecordDetailDTO warehouseRecordDetailDTO = new WarehouseRecordDetailDTO();
                warehouseRecordDetailDTO.setSkuCode(warehouseRecordDetail.getSkuCode());
                //退厂pr单号对应到退货单明细上
                warehouseRecordDetailDTO.setSkuPicPath(skuPrNoMap.get(warehouseRecordDetail.getSkuCode()));
                detailDTOS.add(warehouseRecordDetailDTO);
            }
            //单个单据推送到采购中心创建PR
            warehouseRecordRepository.updateOnePostDetail(detailDTOS, Lists.newArrayList(recordCode));
        }
    }

	@Override
	public String getRocketMQCode() {
		return CustomRocketMQEnum.MQ_ONLINE_RETURN_PURCHASE_CREATE_PUSH.getCode();
	}

}
