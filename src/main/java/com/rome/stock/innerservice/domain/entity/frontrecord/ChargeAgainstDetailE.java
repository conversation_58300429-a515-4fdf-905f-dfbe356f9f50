package com.rome.stock.innerservice.domain.entity.frontrecord;

import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;

@Component
@Scope("prototype")
@Data
@EqualsAndHashCode(callSuper = false)
public class ChargeAgainstDetailE extends AbstractFrontRecordDetail {


    /**
     * 实际冲销数量
     */
    private BigDecimal accQty;
    /**
     * 批次表id
     */
    private Long rwBatchId;

    /**
     * 批次编号
     */
    private String batchCode;

    /**
     * 原出入库单明细id
     */
    private Long refDetailId;

    /**
     * 备注
     */
    private String remark;
}
