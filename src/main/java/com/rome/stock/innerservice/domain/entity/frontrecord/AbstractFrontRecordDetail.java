package com.rome.stock.innerservice.domain.entity.frontrecord;

import com.rome.stock.innerservice.domain.entity.SkuQtyUnitBaseE;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Component;

import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

/**
 * 前置单详情
 * <AUTHOR>
 */
@Component
@Scope("prototype")
@Data
@EqualsAndHashCode(callSuper = false)
public abstract class AbstractFrontRecordDetail extends SkuQtyUnitBaseE {

    /**
     * 详情放入前置单据相关数据
     * @param frontRecordE
     */
    public void setFrontRecordDetail(AbstractFrontRecord frontRecordE){
        this.setFrontRecordId(frontRecordE.getId());
        this.setRecordCode(frontRecordE.getRecordCode());
    }
    /**
     * 唯一主键
     */
    private Long id;

    /**
     * sku名称
     */
    private String skuName;

    /**
     * 所属单据编码
     */
    private String recordCode;
    /**
     * 前置单据id
     */
    private Long frontRecordId;
    /**
     * 商品批次
     */
    private List<FrontBatchStockE> batchStocks;

}
