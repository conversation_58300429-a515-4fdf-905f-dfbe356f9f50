package com.rome.stock.innerservice.domain.entity;

import com.google.common.collect.Maps;
import com.rome.arch.core.exception.RomeException;
import com.rome.stock.common.constants.ResCode;
import com.rome.stock.common.enums.bms.SkuTypeVo;
import com.rome.stock.common.enums.bms.WorkModelVO;
import com.rome.stock.common.enums.bms.WorkTypeVO;
import com.rome.stock.common.enums.warehouse.WarehouseRecordTypeVO;
import com.rome.stock.innerservice.api.dto.bms.BmsDTO;
import com.rome.stock.innerservice.api.dto.bms.BmsRecordDTO;
import com.rome.stock.innerservice.common.RomeCollectionUtil;
import com.rome.stock.innerservice.common.StringUtils;
import com.rome.stock.innerservice.domain.entity.warehouserecord.WarehouseRecordDetail;
import com.rome.stock.innerservice.domain.entity.warehouserecord.WarehouseRecordE;
import com.rome.stock.innerservice.domain.repository.ExtInfoRepository;
import com.rome.stock.innerservice.domain.repository.RealWarehouseRepository;
import com.rome.stock.innerservice.domain.repository.WarehouseWorkRepository;
import com.rome.stock.innerservice.facade.StockBmsFacade;
import com.rome.stock.innerservice.infrastructure.dataobject.RealWarehouseWmsConfigDO;
import com.rome.stock.innerservice.remote.base.facade.BaseDataFacade;
import com.rome.stock.innerservice.remote.item.dto.SkuInfoExtDTO;
import com.rome.stock.innerservice.remote.item.dto.SkuUnitExtDTO;
import com.rome.stock.innerservice.remote.item.facade.SkuFacade;
import com.rome.stock.innerservice.remote.orderCneter.dto.CommonFrontRecordDTO;
import com.rome.stock.innerservice.remote.orderCneter.facade.OrderCenterFacade;
import com.rome.stock.innerservice.remote.sap.dto.sapDto930.DoOrderDTO;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;

/**
 * 仓库作业数据对应实体
 */
@Data
@Slf4j
@Component
@Scope("prototype")
@EqualsAndHashCode(callSuper = false)
public class WarehouseWorkE extends BaseE{
    @Resource
    private ExtInfoRepository extInfoRepository;
    @Resource
    private WarehouseWorkRepository warehouseWorkRepository;
    @Resource
    private RealWarehouseRepository realWarehouseRepository;
    @Resource
    private OrderCenterFacade orderCenterFacade;
    @Resource
    private SkuFacade skuFacade;
    @Resource
    private BaseDataFacade baseDataFacade;

    /**
     * 来源系统
     */
    private String sourceSys;
    /**
     * 后置单单据编号
     */
    private String warehouseRecordCode;

    /**
     * 作业单号	ORDER_NO	Y
     */
    private String orderNo;

    private Integer isToc;

    /**
     * 物流产品	logistic_product
     */
    private String logisticProduct;

    /**
     * 仓库编码	warehouse_code	Y
     */
    private String warehouseCode;

    /**
     * 库区编码	zone_code	Y
     */
    private String zoneCode;

    /**
     * 作业环节	work_type	Y
     */
    private String workType;

    /**
     * 作业时间	generate_time	Y
     */
    private Date generateTime;

    /**
     * 作业模式	work_model	Y
     */
    private String workModel;

    /**
     * 服务商编码	tdlnr
     */
    private String tdlnr;

    /**
     * 服务商描述	tdlnr_name
     */
    private String tdlnrName;

    /**
     * 作业人员	user_code
     */
    private String userCode;

    /**
     * 人员类型	user_type
     */
    private String userType;

    /**
     * 岗位	user_position
     */
    private String userPosition;

    /**
     * 班组	class_group
     */
    private String classGroup;

    /**
     * 门店	next_site		varchar(12)	TOB门店场景赋值	出库环节赋值
     */
    private String nextSite;

    /**
     * 收货人地址-省	RECEIVE_PROVINCE		varchar(12)	TOC场景赋值
     */
    private String receiveProvince;

    /**
     * 收货人地址-市	RECEIVE_CITY		varchar(12)	TOC场景赋值
     */
    private String receiveCity;

    /**
     * 收货人地址-区	RECEIVE_AREA		varchar(12)	TOC场景赋值
     */
    private String receiveArea;

    /**
     * 运输单号	transport_no
     */
    private String transportNo;

    /**
     * 业务订单	SOURCE_ORDER_NO
     */
    private String sourceOrderNo;

    /**
     * 订单渠道	ORDER_CHANNEL	Y
     */
    private String orderChannel;

    /**
     * 下单方	cust_code	Y
     */
    private String custCode;

    /**
     * 直播订单标识	ORDER_FLAG			TOC电商仓配一体场景赋值
     */
    private String orderFlag="0";
    /**
     * 单据类型	order_type
     */
    private Integer orderType;

    private List<WarehouseWorkSkuDetailE> warehouseWorkSkuDetails;

    /**
     * 添加仓库作业数据
     */
    public void addWarehouseWork() {
        long id = warehouseWorkRepository.insertWarehouseWork(this);
        this.setId(id);
        this.warehouseWorkSkuDetails.forEach(detailE -> detailE.setWarehouseWork(this));
        if (CollectionUtils.isNotEmpty(this.warehouseWorkSkuDetails)){
            warehouseWorkRepository.insertWarehouseWorkSkuDetails(this.warehouseWorkSkuDetails);
        }
    }

    /**
     *
     * @param orderNo
     * @param bmsRecordDTO
     * @param realWarehouseE
     * @param wmsConfigDO
     */
    public void initWarehouseWork(String orderNo, BmsRecordDTO bmsRecordDTO,
                                  RealWarehouseE realWarehouseE, RealWarehouseWmsConfigDO wmsConfigDO) {
        WarehouseRecordE recordE = bmsRecordDTO.getWarehouseRecordE();
        this.setSourceSys(String.valueOf(wmsConfigDO.getWmsCode()));
        this.setOrderNo(orderNo);
        this.setWarehouseRecordCode(recordE.getRecordCode());
        this.setIsToc(0);
        this.setWarehouseCode(realWarehouseE.getFactoryCode());
        this.setZoneCode(realWarehouseE.getRealWarehouseOutCode());
        Date generateDate=Objects.nonNull(recordE.getDeliveryTime())?recordE.getDeliveryTime():recordE.getOutOrInTime();
        this.setGenerateTime(Objects.nonNull(generateDate)?generateDate:recordE.getCreateTime());
        this.setWorkModel(Objects.equals(wmsConfigDO.getSupplierCode(),"V1000")? WorkModelVO.ZY.getType() :WorkModelVO.WB.getType());
        //运输单号
        this.setTransportNo(recordE.getTmsRecordCode());
        //服务商编码
        this.setTdlnr(wmsConfigDO.getSupplierCode());
        //服务商描述
        this.setTdlnrName(wmsConfigDO.getSupplierName());
        //作业人员冗余附加信息
        ExtInfoE extInfoE=extInfoRepository.queryByBusinessCode(this.orderNo);
        if (Objects.nonNull(extInfoE)){
            this.setUserCode(extInfoE.getWorkerCode());
            this.setUserType(extInfoE.getWorkerType());
            this.setUserPosition(extInfoE.getWorkerPosition());
            this.setClassGroup(extInfoE.getClassGroup());
        }
        //业务单号(92单号)
        this.setSourceOrderNo(recordE.getSapOrderCode());
        //订单类型
        this.setOrderType(recordE.getRecordType());
        //下单方(公司，1.领用是成本中心公司，2.其他是送达方公司 3.采购-组织对应的公司)
        BmsDTO bmsDTO=bmsRecordDTO.getBmsDTO();
        if (Objects.isNull(bmsDTO)){
            bmsDTO = StockBmsFacade.findOrderCustomCodeDTO(recordE);
        }
        if (Objects.nonNull(bmsDTO)){
            if (StringUtils.isNotEmpty(bmsDTO.getOrderCustCode())){
                this.setCustCode(bmsDTO.getOrderCustCode());
                if (Objects.equals(WarehouseRecordTypeVO.WAREHOUSE_OUT_USE_RECORD.getType(),recordE.getRecordType())){
                    this.setNextSite(bmsDTO.getOrderCustCode());
                }
            }
            this.setOrderChannel(bmsDTO.getKpChannelCode());
        }
    }

    /**
     *
     * @param recordE
     * @param detailList
     * @param workSkuDetailES
     */
    public void initWarehouseWorkSkuDetail(WarehouseRecordE recordE,
                                           List<WarehouseRecordDetail> detailList,
                                           List<WarehouseWorkSkuDetailE> workSkuDetailES) {
        List<String> skuCodes= RomeCollectionUtil.getDistinctValueList(detailList,"skuCode");
        Long merchantId=Objects.nonNull(recordE.getMerchantId())?recordE.getMerchantId():skuFacade.getDefaultMerchantId();
        List<SkuUnitExtDTO> skuUnitList = skuFacade.querySkuUnits(skuCodes,merchantId);
        List<SkuInfoExtDTO> skuInfoList = skuFacade.skuListBySkuCodes(skuCodes);
        //商品信息map，key:skuCode
        Map<String,SkuInfoExtDTO> infoMap = RomeCollectionUtil.listforMap(skuInfoList,"skuCode");
        //商品单位map,key:skuCode+"_"+unitCode
        Map<String,SkuUnitExtDTO> unitMap = Maps.newHashMap();
        skuUnitList.forEach(item->unitMap.put(item.getSkuCode()+"_"+item.getUnitCode(),item));
        for (WarehouseRecordDetail detail:detailList){
            if (detail.getActualQty().compareTo(BigDecimal.ZERO)<=0){
                continue;
            }
            SkuInfoExtDTO skuInfo=infoMap.get(detail.getSkuCode());
            if (Objects.isNull(skuInfo) || StringUtils.isEmpty(skuInfo.getSkuType())){
                throw new RomeException(ResCode.STOCK_ERROR_1001,String.format("商品%s对应的商品类型不存在",detail.getSkuCode()));
            }
            if (Objects.equals("Z016",skuInfo.getSkuType())){
                throw new RomeException(ResCode.STOCK_ERROR_1001,String.format("商品%s对应的商品类型为周转箱,不可操作",detail.getSkuCode()));
            }
            String boxUnitKey= String.format("%s_KAR",detail.getSkuCode());
            boolean hasBoxUnit = unitMap.containsKey(boxUnitKey);
            WarehouseWorkSkuDetailE workSkuDetailE = new WarehouseWorkSkuDetailE();
            workSkuDetailE.setSkuType(parseSkuType(skuInfo.getSkuType(),hasBoxUnit));
            workSkuDetailE.setSkuCode(detail.getSkuCode());
            workSkuDetailE.setSkuId(detail.getSkuId());
            workSkuDetailE.setSkuName(detail.getSkuName());
            workSkuDetailE.setBasicUnitCode(detail.getUnitCode());
            workSkuDetailE.setBasicUnit(detail.getUnit());
            workSkuDetailE.setSkuNum(1);
            workSkuDetailE.setSkuQty(detail.getActualQty());
            if (hasBoxUnit){
                SkuUnitExtDTO unitExt=unitMap.get(boxUnitKey);
                workSkuDetailE.setBoxScale(Objects.nonNull(unitExt.getScale())?unitExt.getScale():BigDecimal.ZERO);
                workSkuDetailE.setBoxWeight(Objects.nonNull(unitExt.getGrossWeight())?unitExt.getGrossWeight():BigDecimal.ZERO);
                workSkuDetailE.setBoxVolume(Objects.nonNull(unitExt.getVolume())?unitExt.getVolume():BigDecimal.ZERO);
                //整箱数量
                BigDecimal[] divideAndRemainder=detail.getActualQty().divideAndRemainder(unitExt.getScale());
                workSkuDetailE.setFullBoxQty(divideAndRemainder[0]);
                //拆零件数
                workSkuDetailE.setSplitQty(Objects.equals(unitExt.getScale(),BigDecimal.ZERO)?detail.getActualQty():divideAndRemainder[1]);
                //箱数
                workSkuDetailE.setBoxQty(BigDecimal.ZERO.compareTo(workSkuDetailE.getSplitQty())<0?
                        workSkuDetailE.getFullBoxQty().add(BigDecimal.ONE):workSkuDetailE.getFullBoxQty());
            }
            //拆零商品毛重
            if (Objects.equals(detail.getUnitCode(),"KG")){
                workSkuDetailE.setBasicWeight(BigDecimal.ONE);
            }else {
                if (unitMap.containsKey(detail.getSkuCode()+"_"+detail.getUnitCode())){
                    BigDecimal basicGrossWeight=unitMap.get(detail.getSkuCode()+"_"+detail.getUnitCode()).getGrossWeight();
                    workSkuDetailE.setBasicWeight(Objects.nonNull(basicGrossWeight)?basicGrossWeight:BigDecimal.ZERO);
                }
            }
            BigDecimal splitWeight=workSkuDetailE.getBasicWeight().multiply(workSkuDetailE.getSplitQty());
            //毛重
            BigDecimal grossWeight=workSkuDetailE.getBoxWeight().multiply(workSkuDetailE.getFullBoxQty()).add(splitWeight);
            workSkuDetailE.setWeight(setScaleHalfUp(grossWeight,3));
            //拆零体积
            BigDecimal splitVolume=BigDecimal.ZERO;
            if (Objects.nonNull(workSkuDetailE.getBoxScale()) && workSkuDetailE.getBoxScale().compareTo(BigDecimal.ZERO)>0){
                //出于精度原因，先乘后除
                splitVolume=workSkuDetailE.getBoxVolume().multiply(workSkuDetailE.getSplitQty()).divide(workSkuDetailE.getBoxScale(), RoundingMode.HALF_UP);
            }
            BigDecimal totalVolume = workSkuDetailE.getBoxVolume().multiply(workSkuDetailE.getFullBoxQty()).add(splitVolume);
            workSkuDetailE.setVolume(setScaleHalfUp(totalVolume,3));
            workSkuDetailES.add(workSkuDetailE);
        }
    }

    /**
     * toc逻辑重新设置体积和重量，原箱数
     */
    public void reCalTocWork(BigDecimal packageTotalVolume, BigDecimal packageTotalWeight, int packageSize) {
        //如果是仓配一体，则使用包裹上的数据
        boolean needPackage = Objects.equals(this.workType, WorkTypeVO.RETRIEVAL_CK2.getType());
        for (int i = 0; i < this.getWarehouseWorkSkuDetails().size(); i++) {
            WarehouseWorkSkuDetailE workSkuDetailE = this.getWarehouseWorkSkuDetails().get(i);
            //都设置为食品
            workSkuDetailE.setSkuType(SkuTypeVo.FOOD.getType());
            if (i == 0) {
                //重量体积取包裹表
                workSkuDetailE.setVolume(setScaleHalfUp(packageTotalVolume,3));
                workSkuDetailE.setBoxVolume(setScaleHalfUp(packageTotalVolume,3));
                workSkuDetailE.setWeight(setScaleHalfUp(packageTotalWeight,3));
                workSkuDetailE.setBoxWeight(setScaleHalfUp(packageTotalWeight,3));
                //需要使用包裹数据
                if (needPackage) {
                    //如果是仓配一体，整箱，拆零箱数取包裹, 全部设置到第一条记录上
                    workSkuDetailE.setFullBoxQty(new BigDecimal(packageSize));
                    workSkuDetailE.setBoxQty(new BigDecimal(packageSize));
                    //拆零箱数为0
                    workSkuDetailE.setSplitQty(BigDecimal.ZERO);
                }
            } else {
                //后面的清0
                workSkuDetailE.setVolume(BigDecimal.ZERO);
                workSkuDetailE.setBoxVolume(BigDecimal.ZERO);
                workSkuDetailE.setWeight(BigDecimal.ZERO);
                workSkuDetailE.setBoxWeight(BigDecimal.ZERO);
                //需要使用包裹数据
                if (needPackage) {
                    workSkuDetailE.setFullBoxQty(BigDecimal.ZERO);
                    workSkuDetailE.setBoxQty(BigDecimal.ZERO);
                    workSkuDetailE.setSplitQty(BigDecimal.ZERO);
                }
            }
        }

    }

    /**
     * 解析转换商品类型
     * @param skuType
     * @param hasBoxUnit
     * @return
     */
    private String parseSkuType(String skuType, boolean hasBoxUnit){
        //03:食品
        if (Objects.equals("Z001",skuType)){
            skuType= SkuTypeVo.FOOD.getType();
        }//04:礼盒
        else if (Objects.equals("Z003",skuType)){
            skuType= SkuTypeVo.GIFT.getType();
        }//非ZOO1,Z003的为辅料;有箱单位:02大件辅料,无箱单位:01小件辅料;
        else {
            skuType= hasBoxUnit? SkuTypeVo.BIG_MATERIAL.getType():SkuTypeVo.SMALL_MATERIAL.getType();
        }
        return skuType;
    }

    /**
     *
     * @param source
     * @param newScale
     * @return
     */
    private BigDecimal setScaleHalfUp(BigDecimal source, int newScale){
        if (source == null || source.scale() == newScale) {
            return source;
        }
        return source.setScale(newScale, RoundingMode.HALF_UP);
    }


    /**
     * 获取门店code
     * @param recordE
     * @return
     */
    public void initNextSite(WarehouseRecordE recordE,WarehouseWorkE workE){
        //领用出库单送达方为成本公司,获取下单方时赋值
        if (Objects.equals(WarehouseRecordTypeVO.WAREHOUSE_OUT_USE_RECORD.getType(),recordE.getRecordType())){
            return;
        }
        //送达方
        String nextSite=null;
        if (WarehouseRecordTypeVO.getShopReplenishTypes().containsKey(recordE.getRecordType())
                || Objects.equals(WarehouseRecordTypeVO.RESERVATION_DO_RECORD.getType(),recordE.getRecordType())
                || Objects.equals(WarehouseRecordTypeVO.LAI_PURCHASE_RECORD.getType(),recordE.getRecordType())
                || Objects.equals(WarehouseRecordTypeVO.SALE_PRESALE_DELIVERY_OUT_RECORD.getType(),recordE.getRecordType())
        ){
            DoOrderDTO doOrderDTO = orderCenterFacade.queryDoWithDetailByDoCode(recordE.getRecordCode());
            if (Objects.isNull(doOrderDTO)){
                return;
            }
            RealWarehouseE outRealWarehouseE = realWarehouseRepository.getRealWarehouseById(doOrderDTO.getRealWarehouseId());
            if (null == outRealWarehouseE) {
                log.error("单据【{}】库仓未找到", recordE.getRecordCode());
            } else {
                nextSite = outRealWarehouseE.getFactoryCode();
            }
            if (!StringUtils.isEmpty(doOrderDTO.getShopCode()) && !WarehouseRecordTypeVO.RESERVATION_DO_RECORD.getType().equals(recordE.getRecordType())) {
                //92类型单据的shopCode不是库存这边的真实shopCode，而是wms需要的送达方
                RealWarehouseE inRealWarehouseE = realWarehouseRepository.getRealWarehouseByCode(doOrderDTO.getShopCode());
                if (inRealWarehouseE != null) {
                    nextSite=inRealWarehouseE.getFactoryCode();
                } else {
                    log.error("单据【{}】shopCode库仓未找到", recordE.getRecordCode());
                }
            }
        } else {
            WarehouseRecordTypeVO recordTypeVO = WarehouseRecordTypeVO.getByType(recordE.getRecordType());
            //特定类型查询单据中心
            if (Objects.nonNull(recordTypeVO) && Objects.equals(recordTypeVO.getQueryType(),2)){
                //查询前置单信息
                List<CommonFrontRecordDTO> resList = orderCenterFacade.queryCommonFrontRecordInfoByRecordCode(recordE.getRecordCode());
                if (!CollectionUtils.isEmpty(resList)) {
                    CommonFrontRecordDTO commonFrontRecordDTO = resList.get(0);
                    RealWarehouseE inRealWarehouseE = realWarehouseRepository.getRealWarehouseByOutCodeAndFactoryCode(commonFrontRecordDTO.getInRealWarehouseCode(), commonFrontRecordDTO.getInFactoryCode());
                    if (null == inRealWarehouseE) {
                        log.error("单据【{}】入库仓未找到", recordE.getRecordCode());
                    } else {
                        nextSite = inRealWarehouseE.getFactoryCode();
                    }
                }
            }
        }
        if (StringUtils.isNotEmpty(nextSite)){
            workE.setNextSite(nextSite);
        }
    }
}
