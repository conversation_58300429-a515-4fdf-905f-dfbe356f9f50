package com.rome.stock.innerservice.domain.entity.warehouserecord;

import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import javax.annotation.Resource;

import com.rome.stock.innerservice.infrastructure.dataobject.BatchStockOpDO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Component;

import com.alibaba.fastjson.JSON;
import com.rome.arch.core.domain.EntityFactory;
import com.rome.stock.common.enums.warehouse.WarehouseRecordTypeVO;
import com.rome.stock.innerservice.api.dto.BatchStockDTO;
import com.rome.stock.innerservice.common.RomeCollectionUtil;
import com.rome.stock.innerservice.common.SkuInfoTools;
import com.rome.stock.innerservice.common.SkuQtyUnitTools;
import com.rome.stock.innerservice.constant.WarehouseRecordBusinessTypeVO;
import com.rome.stock.innerservice.constant.WarehouseRecordStatusVO;
import com.rome.stock.innerservice.constant.WmsSyncStatusVO;
import com.rome.stock.innerservice.constant.WmsSyncTransferStatusVO;
import com.rome.stock.innerservice.domain.convertor.RwBatchConvertor;
import com.rome.stock.innerservice.domain.entity.RwBatchE;
import com.rome.stock.innerservice.domain.entity.frontrecord.BatchAdjustRecordDetailE;
import com.rome.stock.innerservice.domain.entity.frontrecord.BatchAdjustRecordE;
import com.rome.stock.innerservice.domain.repository.RwBatchRepository;
import com.rome.stock.innerservice.domain.repository.warehouserecord.BatchAdjustWarehouseRecordRepository;
import com.rome.stock.innerservice.infrastructure.dataobject.WarehouseRecordDetailDo;
import com.rome.stock.innerservice.infrastructure.mapper.WarehouseRecordDetailMapper;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.extern.slf4j.Slf4j;

/**
 * 损益调整出入库单
 */
@Data
@Slf4j
@Component
@Scope("prototype")
@EqualsAndHashCode(callSuper = false)
public class BatchAdjustWarehouseRecordE extends AbstractWarehouseRecord{

    @Autowired
    private EntityFactory entityFactory;

    @Autowired
    private BatchAdjustWarehouseRecordRepository batchAdjustWarehouseRecordRepository;
    @Resource
    private SkuQtyUnitTools skuQtyUnitTools;
    @Resource
    private WarehouseRecordDetailMapper warehouseRecordDetailMapper;
    @Resource
    private RwBatchConvertor rwBatchConvertor;
    @Resource
    private SkuInfoTools skuInfoTools;
    @Resource
    private RwBatchRepository rwBatchRepository;

    /**
     * 创建仓库单据
     */
    public void addWarehouseRecord() {
        long id = batchAdjustWarehouseRecordRepository.saveWarehouseRecord(this);
        this.setId(id);
        this.warehouseRecordDetails.forEach(record -> record.setWarehouseRecordDetail(this));
        List<List<WarehouseRecordDetail>> splitList = RomeCollectionUtil.splitList(this.warehouseRecordDetails, 1000);
        for (List<WarehouseRecordDetail> subList : splitList) {
            batchAdjustWarehouseRecordRepository.saveWarehouseRecordDetails(subList);
        }
        this.addFrontRecordAndWarehouseRelation();
    }
    /**
     * 根据前置单生成入库单
     * @param batchAdjustRecordE
     */
    public void createInRecordByFrontRecord(BatchAdjustRecordE batchAdjustRecordE, List<BatchAdjustRecordDetailE> frontRecordDetails) {
        //合并重复行
        List<BatchAdjustRecordDetailE> newDetail = this.mergeRepeatSku(frontRecordDetails);
        createRecodeCode(WarehouseRecordTypeVO.BATCH_PROFIT_ADJUST_IN_RECORD.getCode());
        this.setAbstractFrontRecord(batchAdjustRecordE);
        this.setRecordStatus(WarehouseRecordStatusVO.IN_ALLOCATION.getStatus());
        this.setRealWarehouseId(batchAdjustRecordE.getRealWarehouseId());
        this.setBusinessType(WarehouseRecordBusinessTypeVO.IN_WAREHOUSE_RECORD.getType());
        this.setRecordType(WarehouseRecordTypeVO.BATCH_PROFIT_ADJUST_IN_RECORD.getType());
        this.setMerchantId(batchAdjustRecordE.getMerchantId());
        if(null == batchAdjustRecordE.getOutCreateTime()){
            this.setOutCreateTime(new Date());
        }else {
            this.setOutCreateTime(batchAdjustRecordE.getOutCreateTime());
        }
        this.setSyncWmsStatus(WmsSyncStatusVO.NO_REQUIRED.getStatus());
        this.setWarehouseRecordDetails(new ArrayList<>(newDetail.size()));
        //单位换算处理
        newDetail.forEach(wrRecord -> wrRecord.setBasicSkuQty(wrRecord.getSkuQty()));
        skuQtyUnitTools.convertRealToBasic(newDetail);
        if(newDetail!=null){
            for(BatchAdjustRecordDetailE detailE:newDetail){
                WarehouseRecordDetail warehouseRecordDetail=entityFactory.createEntity(WarehouseRecordDetail.class);
                warehouseRecordDetail.createRecordDetailByFrontRecord(detailE);
                //库存调整，计划入库数量与实际入库数量是一致的
                warehouseRecordDetail.setActualQty(detailE.getBasicSkuQty());
                warehouseRecordDetail.setPlanQty(detailE.getBasicSkuQty());
                this.warehouseRecordDetails.add(warehouseRecordDetail);
            }
        }
    }

    /**
     * 保存出入库批次信息
     * @param frontRecordDetails
     */
    public void saveRwBatch(List<BatchAdjustRecordDetailE> frontRecordDetails){
        List<WarehouseRecordDetailDo> detailList = warehouseRecordDetailMapper.queryListByRecordcode(this.getRecordCode());
        Map<String, WarehouseRecordDetailDo> detailDoMap = RomeCollectionUtil.listforMap(detailList, "skuCode");
        List<RwBatchE> rwBatchEList = new ArrayList<>();
        for (BatchAdjustRecordDetailE frontRecordDetail : frontRecordDetails) {
            RwBatchE rwBatchE = new RwBatchE();
            rwBatchE.setSkuId(frontRecordDetail.getSkuId());
            rwBatchE.setSkuCode(frontRecordDetail.getSkuCode());
            rwBatchE.setActualQty(frontRecordDetail.getBasicSkuQty());
            rwBatchE.setProductDate(frontRecordDetail.getProductDate());
            rwBatchE.setBatchCode(frontRecordDetail.getBatchCode());
            rwBatchE.setSyncTransferStatus(WmsSyncTransferStatusVO.INIT_TRANSFER.getStatus());
            rwBatchE.setRecordCode(this.getRecordCode());
            rwBatchE.setWmsRecordCode(this.getRecordCode());
            rwBatchE.setInventoryType(1);
            rwBatchE.setBusinessType(this.getBusinessType());
            WarehouseRecordDetailDo detailDo = detailDoMap.get(rwBatchE.getSkuCode());
            if(detailDo != null){
                rwBatchE.setLineNo(String.valueOf(detailDo.getId()));
            }
            rwBatchEList.add(rwBatchE);
        }

        List<List<RwBatchE>> splitList = RomeCollectionUtil.splitList(rwBatchEList, 1000);
        for (List<RwBatchE> subList : splitList) {
            rwBatchRepository.batchSave(subList, this.getRealWarehouseId());
        }
    }

    /**
     * 根据前置单生成出库单
     * @param batchAdjustRecordE
     */
    public void createOutRecordByFrontRecord(BatchAdjustRecordE batchAdjustRecordE, List<BatchAdjustRecordDetailE> frontRecordDetails) {
        //合并重复行
        List<BatchAdjustRecordDetailE> newDetail = this.mergeRepeatSku(frontRecordDetails);
        createRecodeCode(WarehouseRecordTypeVO.BATCH_PROFIT_ADJUST_OUT_RECORD.getCode());
        this.setAbstractFrontRecord(batchAdjustRecordE);
        this.setRecordStatus(WarehouseRecordStatusVO.OUT_ALLOCATION.getStatus());
        this.setRealWarehouseId(batchAdjustRecordE.getRealWarehouseId());
        this.setBusinessType(WarehouseRecordBusinessTypeVO.OUT_WAREHOUSE_RECORD.getType());
        this.setRecordType(WarehouseRecordTypeVO.BATCH_PROFIT_ADJUST_OUT_RECORD.getType());
        this.setMerchantId(batchAdjustRecordE.getMerchantId());
        if(null == batchAdjustRecordE.getOutCreateTime()){
            this.setOutCreateTime(new Date());
        }else {
            this.setOutCreateTime(batchAdjustRecordE.getOutCreateTime());
        }
        this.setSyncWmsStatus(WmsSyncStatusVO.NO_REQUIRED.getStatus());
        this.setWarehouseRecordDetails(new ArrayList<>(newDetail.size()));
        //单位换算处理
        newDetail.forEach(wrRecord -> wrRecord.setBasicSkuQty(wrRecord.getSkuQty()));
        skuQtyUnitTools.convertRealToBasic(newDetail);
        if(newDetail!=null){
            for(BatchAdjustRecordDetailE detailE:newDetail){
                WarehouseRecordDetail warehouseRecordDetail=entityFactory.createEntity(WarehouseRecordDetail.class);
                warehouseRecordDetail.createRecordDetailByFrontRecord(detailE);
                //库存调整，计划入库数量与实际入库数量是一致的
                warehouseRecordDetail.setActualQty(detailE.getBasicSkuQty());
                warehouseRecordDetail.setPlanQty(detailE.getBasicSkuQty());
                this.warehouseRecordDetails.add(warehouseRecordDetail);
            }
            //设置skuCode和skuID
            this.initWarehouseRecodeDetail();
        }
    }

    /**
     * 转化前置单对象为批次操作对象
     * @param frontRecordDetails
     * @param rwId
     * @param stockType
     * @param rwType 实仓类型
     * @return
     */
    public List<BatchStockOpDO> converToBatchStockInfo(List<BatchAdjustRecordDetailE> frontRecordDetails, Long rwId, Integer stockType, Integer rwType){
        List<BatchStockOpDO> list = new ArrayList<>(frontRecordDetails.size());
        for (BatchAdjustRecordDetailE dto : frontRecordDetails) {
            BatchStockOpDO batchStockDTO = new BatchStockOpDO();
            batchStockDTO.setSkuCode(dto.getSkuCode());
            batchStockDTO.setSkuId(dto.getSkuId());
            // 有效期,天数
            batchStockDTO.setValidity(dto.getValidity());
            batchStockDTO.setRealWarehouseId(rwId);
            batchStockDTO.setRealWarehouseType(rwType);
            batchStockDTO.setRecordCode(this.getRecordCode());
            batchStockDTO.setRecordType(this.getRecordType());
            batchStockDTO.setSkuQty(dto.getSkuQty());
            batchStockDTO.setBatchCode(dto.getBatchCode());
            batchStockDTO.setProductDate(dto.getProductDate());
            batchStockDTO.setStockType(stockType);
            list.add(batchStockDTO);
        }
        return list;
    }

    /**
     * 合并重复行
     * @param frontRecordDetails
     * @return
     */
    private List<BatchAdjustRecordDetailE> mergeRepeatSku(List<BatchAdjustRecordDetailE> frontRecordDetails){
        //拷贝一个新的
        List<BatchAdjustRecordDetailE> batchAdjustDetailList = JSON.parseArray(JSON.toJSONString(frontRecordDetails), BatchAdjustRecordDetailE.class);
        List<BatchAdjustRecordDetailE> newDetail = new ArrayList<>();
        Map<String, BatchAdjustRecordDetailE> detailSkuMap = new HashMap<>();
        for (BatchAdjustRecordDetailE frontRecordDetail : batchAdjustDetailList) {
            BatchAdjustRecordDetailE temp = detailSkuMap.get(frontRecordDetail.getSkuCode());
            if(temp != null){
                temp.setSkuQty(temp.getSkuQty().add(frontRecordDetail.getSkuQty()));
                detailSkuMap.put(temp.getSkuCode() , temp);
            }else{
                detailSkuMap.put(frontRecordDetail.getSkuCode() , frontRecordDetail);
            }
        }
        for (BatchAdjustRecordDetailE batchAdjustRecordDetailE : detailSkuMap.values()) {
            newDetail.add(batchAdjustRecordDetailE);
        }
        return newDetail;
    }
}
