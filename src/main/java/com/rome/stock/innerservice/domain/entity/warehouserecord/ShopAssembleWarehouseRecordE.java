package com.rome.stock.innerservice.domain.entity.warehouserecord;

import com.rome.arch.core.domain.EntityFactory;
import com.rome.stock.common.enums.warehouse.WarehouseRecordTypeVO;
import com.rome.stock.innerservice.common.SkuQtyUnitTools;
import com.rome.stock.innerservice.constant.WarehouseRecordBusinessTypeVO;
import com.rome.stock.innerservice.constant.WarehouseRecordStatusVO;
import com.rome.stock.innerservice.constant.WmsSyncStatusVO;
import com.rome.stock.innerservice.domain.entity.frontrecord.ShopAssembleRecordDetailE;
import com.rome.stock.innerservice.domain.entity.frontrecord.ShopAssembleRecordE;
import com.rome.stock.innerservice.domain.repository.warehouserecord.ShopAssembleWarehouseRepository;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;

/**
 * 门店加工出入库单
 *
 * <AUTHOR> 2019/4/17 20:52
 */
@Data
@Slf4j
@Component
@Scope("prototype")
@EqualsAndHashCode(callSuper = false)
public class ShopAssembleWarehouseRecordE extends AbstractWarehouseRecord{

    @Autowired
    private ShopAssembleWarehouseRepository shopAssembleWarehouseRepository;
    @Resource
    private EntityFactory entityFactory;

    @Resource
    private SkuQtyUnitTools skuQtyUnitTools;

    /**
     * 创建仓库单据
     */
    public void addWarehouseRecord() {
        long id = shopAssembleWarehouseRepository.saveAssembleWarehouseRecord(this);
        this.setId(id);
        this.warehouseRecordDetails.forEach(record -> record.setWarehouseRecordDetail(this));
        shopAssembleWarehouseRepository.saveWarehouseRecordDetails(this.warehouseRecordDetails);
        this.addFrontRecordAndWarehouseRelation();
    }

    /**
     * 根据前置单生成出库单
     */
    public void createOutRecordByFrontRecord(ShopAssembleRecordE frontRecord){
        createRecodeCode(WarehouseRecordTypeVO.SHOP_ASSEMBLE_OUT_WAREHOUSE_RECORD.getCode());
        this.setAbstractFrontRecord(frontRecord);
        this.setRealWarehouseId(frontRecord.getRealWarehouseId());
        this.setBusinessType(WarehouseRecordBusinessTypeVO.OUT_WAREHOUSE_RECORD.getType());
        this.setRecordType(WarehouseRecordTypeVO.SHOP_ASSEMBLE_OUT_WAREHOUSE_RECORD.getType());
        this.setRecordStatus(WarehouseRecordStatusVO.OUT_ALLOCATION.getStatus());
        this.setMerchantId(frontRecord.getMerchantId());
        this.setOutCreateTime(frontRecord.getOutCreateTime());
        this.setSyncWmsStatus(WmsSyncStatusVO.NO_REQUIRED.getStatus());
        List<ShopAssembleRecordDetailE> frontRecordDetails = frontRecord.getFrontRecordDetails();
        this.setWarehouseRecordDetails(new ArrayList<>(frontRecordDetails.size()));
        skuQtyUnitTools.convertRealToBasic(frontRecordDetails);
        for(ShopAssembleRecordDetailE detailE : frontRecordDetails){
            WarehouseRecordDetail warehouseRecordDetail=entityFactory.createEntity(WarehouseRecordDetail.class);
            warehouseRecordDetail.createRecordDetailByFrontRecord(detailE);
            warehouseRecordDetail.setPlanQty(detailE.getBasicSkuQty());
            //因为是一步法,设置为一致即可
            warehouseRecordDetail.setActualQty(detailE.getBasicSkuQty());
            this.warehouseRecordDetails.add(warehouseRecordDetail);
        }
        //设置skuCode和skuID
        this.initWarehouseRecodeDetail();
    }

    /**
     * 根据前置单生成入库单
     */
    public void createInRecordByFrontRecord(ShopAssembleRecordE frontRecord){
        createRecodeCode(WarehouseRecordTypeVO.SHOP_ASSEMBLE_IN_WAREHOUSE_RECORD.getCode());
        this.setAbstractFrontRecord(frontRecord);
        this.setRealWarehouseId(frontRecord.getRealWarehouseId());
        this.setBusinessType(WarehouseRecordBusinessTypeVO.IN_WAREHOUSE_RECORD.getType());
        this.setRecordType(WarehouseRecordTypeVO.SHOP_ASSEMBLE_IN_WAREHOUSE_RECORD.getType());
        this.setRecordStatus(WarehouseRecordStatusVO.IN_ALLOCATION.getStatus());
        this.setMerchantId(frontRecord.getMerchantId());
        this.setOutCreateTime(frontRecord.getOutCreateTime());
        this.setSyncWmsStatus(WmsSyncStatusVO.NO_REQUIRED.getStatus());
        List<ShopAssembleRecordDetailE> frontRecordDetails = frontRecord.getFrontRecordDetails();
        this.setWarehouseRecordDetails(new ArrayList<>(frontRecordDetails.size()));
        skuQtyUnitTools.convertRealToBasic(frontRecordDetails);
        for(ShopAssembleRecordDetailE detailE:frontRecordDetails){
            WarehouseRecordDetail warehouseRecordDetail=entityFactory.createEntity(WarehouseRecordDetail.class);
            warehouseRecordDetail.createRecordDetailByFrontRecord(detailE);
            warehouseRecordDetail.setPlanQty(detailE.getBasicSkuQty());
            //因为是一步法,设置为一致即可
            warehouseRecordDetail.setActualQty(detailE.getBasicSkuQty());
            this.warehouseRecordDetails.add(warehouseRecordDetail);
        }
        //设置skuCode和skuID
        this.initWarehouseRecodeDetail();
    }



}
