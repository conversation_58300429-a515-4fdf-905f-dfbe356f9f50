package com.rome.stock.innerservice.domain.service.impl;

import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.google.common.collect.Lists;
import com.rome.stock.innerservice.api.dto.LogEnumDTO;
import com.rome.stock.innerservice.common.AlikAssert;
import com.rome.stock.innerservice.common.ResCode;
import com.rome.stock.innerservice.common.StringUtils;
import com.rome.stock.innerservice.domain.convertor.LogEnumConvertor;
import com.rome.stock.innerservice.domain.repository.LogEnumRepository;
import com.rome.stock.innerservice.domain.service.LogEnumService;
import com.rome.stock.innerservice.infrastructure.dataobject.LogEnumDO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;
import java.util.Objects;

@Slf4j
@Service
public class LogEnumServiceImpl implements LogEnumService {

    @Resource
    private LogEnumRepository logEnumRepository;

    @Resource
    private LogEnumConvertor logEnumConvertor;

    @Override
    public PageInfo<LogEnumDTO> getLogEnum(LogEnumDTO logEnumDTO) {
        Page page = PageHelper.startPage(logEnumDTO.getPageIndex(), logEnumDTO.getPageSize());
        List<String> requestServices = StringUtils.isNotEmpty(logEnumDTO.getRequestService()) ? Lists.newArrayList(logEnumDTO.getRequestService()) : Lists.newArrayList();
        List<LogEnumDTO> logEnumDTOS = logEnumRepository.queryByRequestServiceAndType(requestServices, logEnumDTO.getRequestType());
        if (CollectionUtils.isEmpty(logEnumDTOS)) {
            return new PageInfo();
        }
        PageInfo<LogEnumDTO> pageList = new PageInfo(logEnumDTOS);
        pageList.setTotal(page.getTotal());
        return pageList;
    }

    @Override
    public void saveOrUpdate(LogEnumDTO logEnumDTO) {
        if (Objects.nonNull(logEnumDTO.getId())) {
            LogEnumDO logEnumDO = logEnumRepository.queryById(logEnumDTO.getId());
            AlikAssert.isTrue(Objects.nonNull(logEnumDO), ResCode.STOCK_ERROR_2010, "当前数据不存在，不可更新");
            logEnumDO = logEnumConvertor.dtoToDo(logEnumDTO);
            int result = logEnumRepository.updateByIdNonRequestService(logEnumDO);
            AlikAssert.isTrue(result > 0, ResCode.STOCK_ERROR_2010, "更新失败");
        } else {
            AlikAssert.isTrue(StringUtils.isNotEmpty(logEnumDTO.getRequestService()), ResCode.STOCK_ERROR_2010, "请求服务名不可为空");
            logEnumDTO.setRequestService(logEnumDTO.getRequestService().trim());
            List<LogEnumDO> logEnumDOS = logEnumRepository.queryByRequestServices(Lists.newArrayList(logEnumDTO.getRequestService()));
            AlikAssert.isTrue(CollectionUtils.isEmpty(logEnumDOS), ResCode.STOCK_ERROR_2010, "请求服务名对应的枚举配置数据已存在，不可重复创建");
            LogEnumDO logEnumDO = logEnumConvertor.dtoToDo(logEnumDTO);
            logEnumDO.setTableField(Objects.isNull(logEnumDO.getTableField()) ? "" : logEnumDO.getTableField());
            logEnumDO.setTableFieldBeforeValue(Objects.isNull(logEnumDO.getTableFieldBeforeValue()) ? "" : logEnumDO.getTableFieldBeforeValue());
            logEnumDO.setTableFieldAfterValue(Objects.isNull(logEnumDO.getTableFieldAfterValue()) ? "" : logEnumDO.getTableFieldAfterValue());
            logEnumDO.setCreateTime(new Date());
            logEnumDO.setUpdateTime(new Date());
            int result = logEnumRepository.save(logEnumDO);
            AlikAssert.isTrue(result > 0, ResCode.STOCK_ERROR_2010, "新增失败");
        }
    }

    @Override
    public void deleteLogEnum(LogEnumDTO logEnumDTO) {
        AlikAssert.isTrue(Objects.nonNull(logEnumDTO.getId()), ResCode.STOCK_ERROR_2010, "日志枚举ID不可为空");
        LogEnumDO logEnumDO = logEnumRepository.queryById(logEnumDTO.getId());
        AlikAssert.isTrue(Objects.nonNull(logEnumDO), ResCode.STOCK_ERROR_2010, "当前数据不存在或已删除，不可重复操作");
        int result = logEnumRepository.deleteB(logEnumDTO);
        AlikAssert.isTrue(result > 0, ResCode.STOCK_ERROR_2010, "删除失败");
    }

}
