package com.rome.stock.innerservice.domain.entity.frontrecord;

import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Component;

/**
 * 商家库存调整详情
 * <AUTHOR>
 */
@Component
@Scope("prototype")
@Data
@EqualsAndHashCode(callSuper = false)
public class AdjustMerchantRecordDetailE extends AbstractFrontRecordDetail{

}
