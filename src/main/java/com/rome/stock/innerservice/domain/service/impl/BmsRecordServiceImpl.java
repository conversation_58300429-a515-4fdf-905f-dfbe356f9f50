package com.rome.stock.innerservice.domain.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import com.alibaba.fastjson.JSON;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.rome.arch.core.domain.EntityFactory;
import com.rome.arch.core.exception.RomeException;
import com.rome.arch.util.dynamic.DynamicDataSourceEnum;
import com.rome.arch.util.dynamic.TargetDataSource;
import com.rome.stock.common.constants.ResCode;
import com.rome.stock.common.constants.WmsConfigConstants;
import com.rome.stock.common.enums.bms.WorkTypeVO;
import com.rome.stock.common.enums.warehouse.WarehouseRecordTypeVO;
import com.rome.stock.common.utils.AlikAssert;
import com.rome.stock.common.utils.DateUtil;
import com.rome.stock.innerservice.api.dto.bms.BmsDTO;
import com.rome.stock.innerservice.api.dto.bms.BmsRecordDTO;
import com.rome.stock.innerservice.api.dto.bms.BmsRecordPageDTO;
import com.rome.stock.innerservice.api.dto.bms.BmsRecordSkuDetailDTO;
import com.rome.stock.innerservice.constant.WarehouseRecordBusinessTypeVO;
import com.rome.stock.innerservice.domain.convertor.BmsRecordConvertor;
import com.rome.stock.innerservice.domain.entity.*;
import com.rome.stock.innerservice.domain.entity.frontrecord.OnlineRetailE;
import com.rome.stock.innerservice.domain.entity.record.RecordPackageE;
import com.rome.stock.innerservice.domain.entity.warehouserecord.WarehouseRecordDetail;
import com.rome.stock.innerservice.domain.entity.warehouserecord.WarehouseRecordE;
import com.rome.stock.innerservice.domain.repository.*;
import com.rome.stock.innerservice.domain.repository.warehouserecord.WarehouseRecordRepository;
import com.rome.stock.innerservice.domain.service.BmsRecordService;
import com.rome.stock.innerservice.domain.service.BmsService;
import com.rome.stock.innerservice.domain.service.RecordPackageService;
import com.rome.stock.innerservice.facade.StockBmsFacade;
import com.rome.stock.innerservice.infrastructure.dataobject.RealWarehouseWmsConfigDO;
import com.rome.stock.innerservice.infrastructure.redis.RealWarehouseWmsRedis;
import com.rome.stock.innerservice.remote.base.dto.KpChannelAreaInfoDTO;
import com.rome.stock.innerservice.remote.base.facade.BaseDataFacade;
import com.rome.stock.innerservice.remote.bms.BmsPushRule;
import com.rome.stock.innerservice.remote.bms.dto.BmsInoutRecordDTO;
import com.rome.stock.innerservice.remote.bms.facade.BmsFacade;
import com.rome.stock.innerservice.remote.orderCneter.dto.CommonFrontRecordDTO;
import com.rome.stock.innerservice.remote.orderCneter.dto.DoOrderDTO;
import com.rome.stock.innerservice.remote.orderCneter.facade.OrderCenterFacade;
import com.rome.stock.wms.constants.WarehouseWmsConfigEnum;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.stream.Collectors;

import static com.rome.stock.common.enums.configuration.OperateStockVO.NON_LOCK_UN_STEP_STOCK;

@Slf4j
@Service
public class BmsRecordServiceImpl implements BmsRecordService{
    @Resource
    private WarehouseRecordRepository warehouseRecordRepository;
    @Resource
    private RealWarehouseRepository realWarehouseRepository;
    @Resource
    private RealWarehouseWmsRedis realWarehouseWmsRedis;
    @Resource
    private RwRecordPoolRepository rwRecordPoolRepository;
    @Resource
    private RecordPackageService recordPackageService;
    @Resource
    private ReceiptRecordRepository receiptRecordRepository;
    @Resource
    private RwBatchRepository rwBatchRepository;
    @Resource
    private BmsRecordRepository bmsRecordRepository;
    @Resource
    private OrderCenterFacade orderCenterFacade;
    @Resource
    private BmsFacade bmsFacade;
    @Resource
    private EntityFactory entityFactory;
    @Resource
    private BmsRecordConvertor bmsRecordConvertor;
    @Resource
    private BmsService bmsService;

    @Override
    @TargetDataSource(DynamicDataSourceEnum.READ)
    public PageInfo<BmsRecordPageDTO> getListByQueryCondition(BmsRecordPageDTO recordPageDTO) {
        Page page = PageHelper.startPage(recordPageDTO.getPageIndex(), recordPageDTO.getPageSize(), !recordPageDTO.isImportExcel());
        List<BmsRecordPageDTO> result=bmsRecordRepository.getListByQueryCondition(recordPageDTO);
        result.forEach(dto->{
            dto.setOriSys(WarehouseWmsConfigEnum.getDescByType(Integer.valueOf(dto.getOriSys())));
            dto.setTotalValue(dto.getTotalValue().setScale(3, RoundingMode.HALF_UP));
        });
        PageInfo<BmsRecordPageDTO> pageList = new PageInfo(result);
        pageList.setTotal(page.getTotal());
        return pageList;
    }

    @Override
    @TargetDataSource(DynamicDataSourceEnum.READ)
    public List<BmsRecordSkuDetailDTO> querySkuDetail(BmsRecordPageDTO pageDTO) {
        return bmsRecordRepository.queryBySerialNo(pageDTO.getSerialNo());
    }


    @Override
    @Transactional(rollbackFor = Exception.class)
    public void pushOutNotify(WarehouseRecordE recordE) {
        String recordCode = recordE.getRecordCode();
        BmsRecordDTO bmsRecordDTO = BmsRecordDTO.init(recordE);
        if (checkIdempotent(bmsRecordDTO,recordCode)){
            return;
        }
        RealWarehouseE realWarehouseE=realWarehouseRepository.getRealWarehouseById(recordE.getRealWarehouseId());
        AlikAssert.isNotNull(realWarehouseE, ResCode.STOCK_ERROR_1002,"单据对应的实仓数据不存在:"+recordCode);
        recordE.setWarehouseRecordDetails(warehouseRecordRepository.queryDetailListByRecordId(recordE.getId()));
        RealWarehouseWmsConfigDO wmsConfigDO=realWarehouseWmsRedis.findWmsInformationById(realWarehouseE.getId());
        BmsRecordE bmsRecordE = entityFactory.createEntity(BmsRecordE.class);
        bmsRecordE.initBmsRecord(recordCode,bmsRecordDTO,realWarehouseE,wmsConfigDO);
        bmsRecordE.setConfirmType(WorkTypeVO.RETRIEVAL.getType());
        List<BmsRecordSkuDetailE> bmsSkuDetailEs = Lists.newArrayList();
        bmsRecordE.setBmsSkuDetails(bmsSkuDetailEs);
        CommonFrontRecordDTO commonFrontRecordDTO=new CommonFrontRecordDTO();
        boolean isWdtPost = false;
        if (Objects.equals(wmsConfigDO.getWmsCode(), WarehouseWmsConfigEnum.WDT.getType())
                && Objects.equals(WarehouseRecordTypeVO.WAREHOUSE_OUT_USE_RECORD.getType(), recordE.getRecordType())) {
            //仓库领用
            List<CommonFrontRecordDTO> resList = orderCenterFacade.queryCommonFrontRecordInfoByRecordCode(recordE.getRecordCode());
            if (CollectionUtil.isNotEmpty(resList)) {
                commonFrontRecordDTO = resList.get(0);
                log.error("单据【{}】查询订单中心common接口返回值{}", commonFrontRecordDTO.getRecordCode(), JSON.toJSONString(commonFrontRecordDTO));
            }
            isWdtPost = Objects.equals(commonFrontRecordDTO.getTransWay(),2);
        }
        if(WarehouseRecordTypeVO.getOnlineRetailTypes().containsKey(recordE.getRecordType())
                || isWdtPost) {
            String sapOrderCode=recordE.getSapOrderCode();
            //电商toc
            if (isWdtPost) {
                //电商仓领用快递直发
                sapOrderCode = commonFrontRecordDTO.getPlatformCode();
            }else if (Objects.equals(recordE.getSyncTransferStatus(),NON_LOCK_UN_STEP_STOCK.getTypeCode())){
                //新TOC单据查询订单中心
                List<DoOrderDTO> frontOrders=orderCenterFacade.queryDOByOutCode(recordCode);
                if (CollectionUtils.isNotEmpty(frontOrders)){
                    if(StringUtils.isEmpty(sapOrderCode)){
                        sapOrderCode = StringUtils.isNotEmpty(frontOrders.get(0).getOriginOrderCode())?
                                frontOrders.get(0).getOriginOrderCode():frontOrders.get(0).getOutRecordCode();
                    }
                }
            }else {
                //旧TOC单据
                List<RwRecordPoolE> doPools=rwRecordPoolRepository.queryKeysByWarehouseId(recordE.getId());
                if (CollectionUtils.isNotEmpty(doPools)){
                    if (StringUtils.isEmpty(sapOrderCode)){
                        OnlineRetailE onlineRetailE=doPools.get(0).queryFrontRecord();
                        if (Objects.nonNull(onlineRetailE)){
                            sapOrderCode = StringUtils.isNotEmpty(onlineRetailE.getOriginOrderCode())?
                                    onlineRetailE.getOriginOrderCode() : onlineRetailE.getOutRecordCode();
                        }
                    }
                }
            }
            List<RecordPackageE> recordPackages=recordPackageService.getRecordPackageByRecordCode(recordCode);
            BigDecimal packageTotalWeight=BigDecimal.ZERO;
            BigDecimal packageTotalVolume=BigDecimal.ZERO;
            for(RecordPackageE recordPackageE:recordPackages){
                packageTotalWeight=packageTotalWeight.add(Objects.isNull(recordPackageE.getWeight())?BigDecimal.ZERO:recordPackageE.getWeight());
                packageTotalVolume=packageTotalVolume.add(Objects.isNull(recordPackageE.getVolume())?BigDecimal.ZERO:recordPackageE.getVolume());
            }
            bmsRecordE.setIsToc(1);
            //电商取前置单原始单号，如果原始单号为空，则取外部单号
            bmsRecordE.setCustOrderNo(sapOrderCode);

            bmsRecordE.initBmsSkuDetail(recordE,realWarehouseE,recordE.getWarehouseRecordDetails(),bmsSkuDetailEs);

            //仓配分离区分
            String logisticCode = recordE.getLogisticsCode();
            String packageLogisticCode = null;
            if (Objects.isNull(logisticCode) && CollectionUtils.isNotEmpty(recordPackages)){
                packageLogisticCode = recordPackages.get(0).getLogisticsCode();
            }

            WorkTypeVO workTypeVO = bmsService.findWorkType(logisticCode, packageLogisticCode, recordE, realWarehouseE, wmsConfigDO);
            bmsRecordE.setConfirmType(workTypeVO.getType());
            //作业明细数据
//            bmsRecordE.initTocBmsRecordSkuDetail(recordE.getWarehouseRecordDetails(),realWarehouseE,totalWeight,totalVolume,
//                    BigDecimal.valueOf(recordPackages.size()),bmsSkuDetailEs);
            bmsRecordE.reCalTocWork(workTypeVO, packageTotalVolume, packageTotalWeight, recordPackages.size());
        }
        //toB单据
        else {
            //作业明细数据
            bmsRecordE.initBmsSkuDetail(recordE,realWarehouseE,recordE.getWarehouseRecordDetails(),bmsSkuDetailEs);
        }
        saveAndPushBms(bmsRecordE,realWarehouseE);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void pushInNotify(WarehouseRecordE recordE, String receiptCode) {
        ReceiptRecordE receiptRecordE = null;
        if (StringUtils.isNotEmpty(receiptCode)){
            receiptRecordE = receiptRecordRepository.selectReceiptRecordBywmsRecordCode(receiptCode);
        }
        if (Objects.isNull(receiptRecordE)){
            receiptCode = recordE.getRecordCode();
        }
        BmsRecordDTO bmsRecordDTO = BmsRecordDTO.init(recordE);
        if (checkIdempotent(bmsRecordDTO,receiptCode)){
            return;
        }
        RealWarehouseE realWarehouseE=realWarehouseRepository.getRealWarehouseById(recordE.getRealWarehouseId());
        AlikAssert.isNotNull(realWarehouseE, ResCode.STOCK_ERROR_1002,"单据对应的实仓数据不存在:"+receiptCode);
        recordE.setWarehouseRecordDetails(warehouseRecordRepository.queryDetailListByRecordId(recordE.getId()));
        RealWarehouseWmsConfigDO wmsConfigDO=realWarehouseWmsRedis.findWmsInformationById(realWarehouseE.getId());
        BmsRecordE bmsRecordE = entityFactory.createEntity(BmsRecordE.class);
        bmsRecordE.initBmsRecord(receiptCode,bmsRecordDTO,realWarehouseE,wmsConfigDO);
        bmsRecordE.setConfirmType(WorkTypeVO.RECEIVING.getType());
        List<BmsRecordSkuDetailE> bmsRecordSkuDetailEs = Lists.newArrayList();
        bmsRecordE.setBmsSkuDetails(bmsRecordSkuDetailEs);
        List<WarehouseRecordDetail> detailList=recordE.getWarehouseRecordDetails();
        if (Objects.nonNull(receiptRecordE)){
            //批次
            List<RwBatchE> rwBatchsList=rwBatchRepository.queryBywmsRecordCode(recordE.getRecordCode(),receiptRecordE.getWmsRecordCode());
            //根据行号合并批次
            detailList = batchInfoToWarehouseRecordDetails(rwBatchsList,wmsConfigDO.getWmsCode(),recordE);
        }
        bmsRecordE.initBmsSkuDetail(recordE,realWarehouseE,detailList,bmsRecordSkuDetailEs);
        saveAndPushBms(bmsRecordE,realWarehouseE);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void pushAgain(Long bmsId) {
        AlikAssert.isNotNull(bmsId, ResCode.STOCK_ERROR_1002, ResCode.STOCK_ERROR_1002_DESC + ",id不能为null");
        BmsRecordE bmsRecordE=bmsRecordRepository.queryById(bmsId);
        AlikAssert.isNotNull(bmsRecordE, ResCode.STOCK_ERROR_1002, ResCode.STOCK_ERROR_1002_DESC + ",id:"+bmsId+"对应的仓库作业数据不存在");
        WarehouseRecordE recordE=warehouseRecordRepository.queryWarehouseRecordByRecordCode(bmsRecordE.getWarehouseRecordCode());
        AlikAssert.isNotNull(recordE, ResCode.STOCK_ERROR_1002,"出入库单据不存在:"+bmsRecordE.getWarehouseRecordCode());
        RealWarehouseE realWarehouseE=realWarehouseRepository.getRealWarehouseById(recordE.getRealWarehouseId());
        AlikAssert.isNotNull(realWarehouseE, ResCode.STOCK_ERROR_1002,"单据对应的实仓数据不存在:"+bmsRecordE.getWarehouseRecordCode());
        RealWarehouseWmsConfigDO wmsConfigDO=realWarehouseWmsRedis.findWmsInformationById(realWarehouseE.getId());
        AlikAssert.isNotNull(wmsConfigDO, ResCode.STOCK_ERROR_1002,"实仓wms配置数据不存在:"+realWarehouseE.getRealWarehouseCode());
        //是否推送bms
        if (!BmsPushRule.switchPushInOutRecord(recordE.getRecordType(), realWarehouseE, wmsConfigDO)){
            throw new RomeException(ResCode.STOCK_ERROR_1002,"该记录不符合推送规则");
        }
        bmsRecordRepository.updateWithSkuDetailDeleted(bmsId,bmsRecordE.getSerialNo()+"_"+bmsRecordE.getId());
        if (Objects.equals(recordE.getBusinessType(), WarehouseRecordBusinessTypeVO.OUT_WAREHOUSE_RECORD.getType())){
            pushOutNotify(recordE);
        }else {
            pushInNotify(recordE,bmsRecordE.getSerialNo());
        }
    }


    /**
     * 幂等校验
     * @param bmsRecordDTO
     * @param businessCode
     * @return
     */
    private boolean checkIdempotent(BmsRecordDTO bmsRecordDTO, String businessCode) {
        WarehouseRecordE recordE = bmsRecordDTO.getWarehouseRecordE();
        List<BmsRecordE> bmsRecords=bmsRecordRepository.getListBySerialNo(businessCode);
        if (CollectionUtils.isNotEmpty(bmsRecords)){
            //退货预入库,下单方不变则幂等
            if (Objects.equals(WarehouseRecordTypeVO.PREDICT_RETURN_DIRECT_IN_RECORD.getType(),recordE.getRecordType())) {
                BmsDTO bmsDTO= StockBmsFacade.findOrderCustomCodeDTO(recordE);
                boolean isCusDiff = Objects.nonNull(bmsDTO) && StringUtils.isNotEmpty(bmsDTO.getOrderCustCode()) && !Objects.equals(bmsDTO.getOrderCustCode(), bmsRecords.get(0).getCustCode());
                boolean isChannelDiff = !Objects.equals(bmsRecords.get(0).getSaleChannel(),bmsDTO.getKpChannelCode());
                if (isCusDiff || isChannelDiff){
                    //bms出入库数据逻辑删除
                    bmsRecordRepository.updateWithSkuDetailDeleted(bmsRecords.get(0).getId(),bmsRecords.get(0).getSerialNo()+"_"+bmsRecords.get(0).getId());
                    //后续使用，不再重复查询
                    bmsRecordDTO.setBmsDTO(bmsDTO);
                    return false;
                }
            } //toc及领用作业方式不变则幂等
            else if (WarehouseRecordTypeVO.getOnlineRetailTypes().containsKey(recordE.getRecordType())
                    ||Objects.equals(WarehouseRecordTypeVO.WAREHOUSE_OUT_USE_RECORD.getType(),recordE.getRecordType())){
                String logisticCode = recordE.getLogisticsCode();
                String packageLogisticCode = null;
                if (Objects.isNull(logisticCode) && WarehouseRecordTypeVO.getOnlineRetailTypes().containsKey(recordE.getRecordType())){
                    List<RecordPackageE> recordPackages=recordPackageService.getRecordPackageByRecordCode(recordE.getRecordCode());
                    if (CollectionUtils.isNotEmpty(recordPackages)){
                        packageLogisticCode = recordPackages.get(0).getLogisticsCode();
                    }
                }
                WorkTypeVO workTypeVO = bmsService.findWorkType(logisticCode,packageLogisticCode,recordE,null,null);
                if (!Objects.equals(workTypeVO.getType(), bmsRecords.get(0).getConfirmType())){
                    //仓库出入库数据逻辑删除
                    bmsRecordRepository.updateWithSkuDetailDeleted(bmsRecords.get(0).getId(),bmsRecords.get(0).getSerialNo()+"_"+bmsRecords.get(0).getId());
                    return false;
                }
            }
            return true;
        }
        return false;
    }

    /**
     * 批次维度转换为后置单明细维度
     * @param rwBatchsList
     * @param wmsCode
     * @param warehouseRecordE
     * @return
     */
    private List<WarehouseRecordDetail> batchInfoToWarehouseRecordDetails(List<RwBatchE> rwBatchsList, Integer wmsCode, WarehouseRecordE warehouseRecordE) {
        List<WarehouseRecordDetail> details=new ArrayList<>();
        if (CollectionUtils.isEmpty(warehouseRecordE.getWarehouseRecordDetails())){
            return details;
        }
        Map<Long, WarehouseRecordDetail> warehouseRecordEDetailMap = Maps.newHashMap();
        Map<String, WarehouseRecordDetail> deliveryLineNoMap = Maps.newHashMap();
        if (wmsCode == WmsConfigConstants.WMS_SAP){
            deliveryLineNoMap = warehouseRecordE.getWarehouseRecordDetails().stream().collect(Collectors.toMap(WarehouseRecordDetail::getDeliveryLineNo, item -> item));
        }else {
            warehouseRecordEDetailMap = warehouseRecordE.getWarehouseRecordDetails().stream().collect(Collectors.toMap(WarehouseRecordDetail::getId, item -> item));
        }
        //批次以行号合并
        Map<String,List<RwBatchE>> batchMap= rwBatchsList.stream().collect(Collectors.groupingBy(relation -> relation.getLineNo()));
        for(Map.Entry<String, List<RwBatchE>> mapEntry : batchMap.entrySet()){
            WarehouseRecordDetail detail = new WarehouseRecordDetail();
            BigDecimal actualQty = BigDecimal.ZERO;
            for (RwBatchE batchE : mapEntry.getValue()) {
                detail.setSkuId(batchE.getSkuId());
                detail.setSkuCode(batchE.getSkuCode());
                actualQty = actualQty.add(batchE.getActualQty());
                WarehouseRecordDetail warehouseRecordDetail=new WarehouseRecordDetail();
                if (wmsCode == WmsConfigConstants.WMS_SAP) {
                    //sap仓，调拨业务存入的是交货单行号
                    warehouseRecordDetail=deliveryLineNoMap.get(batchE.getLineNo());
                }else {
                    //大幅仓，批次表的line_no表示是中台行号  ， 需要根据中台行号和skuCode更新 批次表的 po_line_no
                    warehouseRecordDetail=warehouseRecordEDetailMap.get(Long.parseLong(batchE.getLineNo()));
                }
                detail.setSkuName(warehouseRecordDetail.getSkuName());
                detail.setUnit(warehouseRecordDetail.getUnit());
                detail.setUnitCode(warehouseRecordDetail.getUnitCode());
                detail.setLineNo(warehouseRecordDetail.getLineNo());
                detail.setDeliveryLineNo(warehouseRecordDetail.getDeliveryLineNo());
            }
            detail.setActualQty(actualQty);
            details.add(detail);
        }
        return details;
    }

    /**
     * 保存及推送bms出入库数据
     * @param bmsRecordE
     */
    private void saveAndPushBms(BmsRecordE bmsRecordE,RealWarehouseE realWarehouseE) {
        if (StringUtils.isEmpty(bmsRecordE.getCustOrderNo())){
            bmsRecordE.setCustOrderNo(bmsRecordE.getSerialNo()); //业务单号必填,不存在使用流水号代替
        }
        //保存持久化
        bmsRecordE.addBmsRecord();
        BmsInoutRecordDTO recordDTO=bmsRecordConvertor.entityToInoutRecordDTO(bmsRecordE);
        recordDTO.setOriSys("库存中心");
        bmsRecordE.getBmsSkuDetails().forEach(detail->{
            recordDTO.setTotalVolume(recordDTO.getTotalVolume().add(detail.getVolume()));
            recordDTO.setTotalWeight(recordDTO.getTotalWeight().add(detail.getWeight()));
            recordDTO.setTotalCmmdtyQty(recordDTO.getTotalCmmdtyQty().add(detail.getSkuQty()));
            BigDecimal totalValue=detail.getSkuPrice().multiply(detail.getSkuQty()).setScale(3, RoundingMode.HALF_UP);
            recordDTO.setTotalValue(recordDTO.getTotalValue().add(totalValue));
            recordDTO.setTotalExpressQty(recordDTO.getTotalExpressQty().add(detail.getBoxQty()));
            recordDTO.setTotalBox(recordDTO.getTotalBox().add(detail.getFullBoxQty()));
            recordDTO.setPieceNum(recordDTO.getPieceNum().add(detail.getSplitQty()));
        });
        recordDTO.setWarehouseCodeDesc(realWarehouseE.getRealWarehouseName());
        recordDTO.setStockDate(DateUtil.formatDateTime(bmsRecordE.getStockDate()));
        bmsFacade.bmsInoutRecordNotify(recordDTO);
    }

}
