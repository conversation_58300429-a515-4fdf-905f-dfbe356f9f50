package com.rome.stock.innerservice.domain.entity.frontrecord;

import com.rome.stock.innerservice.constant.FrontRecordDetailStatusVO;
import com.rome.stock.innerservice.constant.FrontRecordTypeVO;
import com.rome.stock.innerservice.domain.repository.frontrecord.FrAdjustConsumeDetailRepository;
import com.rome.stock.innerservice.domain.repository.frontrecord.FrAdjustConsumeRepository;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.apache.commons.lang3.StringUtils;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Component;
import javax.annotation.Resource;
import java.util.Date;
import java.util.List;

/**
 * @Description
 * <AUTHOR>
 * @Date 2019/5/10 20:27
 * @Version 1.0
 */
@Component
@Scope("prototype")
@Data
@EqualsAndHashCode(callSuper = false)
public class ConsumeAdjustRecordE extends AbstractFrontRecord{

    @Resource
    private FrAdjustConsumeRepository frAdjustConsumeRepository;

    @Resource
    private FrAdjustConsumeDetailRepository frAdjustConsumeDetailRepository;
    /**
     * 创建仓库损耗单
     */
    public void addFrontRecord(){
        //生成单据编号
        if(this.recordType.equals(FrontRecordTypeVO.WAREHOUSE_CONSUME_ADJUST_RECORD.getType())){
            this.initFrontRecord(FrontRecordTypeVO.WAREHOUSE_CONSUME_ADJUST_RECORD.getCode(), this.frontRecordDetails);
            this.setReasonCode("2001");
        }else if (this.recordType.equals(FrontRecordTypeVO.WAREHOUSE_USE_ADJUST_RECORD.getType())){
            this.initFrontRecord(FrontRecordTypeVO.WAREHOUSE_USE_ADJUST_RECORD.getCode(), this.frontRecordDetails);
        }
        if(StringUtils.isBlank(this.getRemark())) {
            this.setRemark("");
        }
        //插入仓库损耗单据
        long id=frAdjustConsumeRepository.saveConsumeAdjustRecord(this);
        this.setId(id);

        //损耗明细关联损耗单
        for (ConsumeAdjustRecordDetailE detailE:this.frontRecordDetails) {
            detailE.setFrontRecordId(this.getId());
            detailE.setRecordCode(this.recordCode);
        }

        //插入调整单据详情
        frAdjustConsumeDetailRepository.saveConsumeAdjustRecord(this.frontRecordDetails);
    }

    //调整单单号
    private String recordCode;

    //调整单状态
    private Integer recordStatus;

    //单据类型
    private Integer recordType;

    //业务原因编号
    private String reasonCode;

    //业务原因描述
    private String reasonName;

    //SAP过账单号
    private String sapRecordCode;

    //归属组织编号
    private String organizationCode;

    //归属组织名称
    private String organizationName;

    //成本中心编号
    private String costCenterCode;

    //成本中心名称
    private String costCenterName;

    //实仓ID
    private Long realWarehouseId;

    //创建时间
    private Date createTime;

    //更新时间
    private Date updateTime;

    //开始时间
    private Date startTime;

    //结束时间
    private Date endTime;

    //备注
    private String remark;

    //OA审批号
    private String approveOACode;

    //前置单明细集合
    private List<ConsumeAdjustRecordDetailE> frontRecordDetails;

    /**
     * 领用公司编号
     */
    private String receiveCompanyCode;

    /**
     * 出库公司编号
     */
    private String outCompanyCode;

    /**
     * 领用日期
     */
    private Date receiveDate;

    /**
     * 运输方式 1:自提 2:快递 3:内部通道
     */
    private Integer transWay;

    /**
     * 申请人工号
     */
    private String applier;

    /**
     * 申请人手机号
     */
    private String applierMobile;

    private Integer companyType;

}
