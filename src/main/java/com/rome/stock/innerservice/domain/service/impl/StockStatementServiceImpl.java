package com.rome.stock.innerservice.domain.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.github.pagehelper.PageInfo;
import com.rome.arch.core.exception.RomeException;
import com.rome.stock.common.enums.warehouse.WarehouseRecordTypeVO;
import com.rome.stock.common.utils.DateUtil;
import com.rome.stock.innerservice.api.dto.StockStatementDTO;
import com.rome.stock.innerservice.api.dto.frontrecord.RwStockChangeFlowParamDTO;
import com.rome.stock.innerservice.api.dto.frontrecord.RwStockChangeFlowResultDTO;
import com.rome.stock.innerservice.api.dto.frontrecord.RwStockStatementDTO;
import com.rome.stock.innerservice.common.*;
import com.rome.stock.innerservice.constant.WarehouseRecordBusinessTypeVO;
import com.rome.stock.innerservice.domain.service.RwStockChangeFlowService;
import com.rome.stock.innerservice.domain.service.StockStatementService;
import com.rome.stock.innerservice.infrastructure.dataobject.OrderCostDO;
import com.rome.stock.innerservice.infrastructure.dataobject.RealWarehouseDO;
import com.rome.stock.innerservice.infrastructure.mapper.RealWarehouseMapper;
import com.rome.stock.innerservice.infrastructure.mapper.WarehouseRecordMapper;
import com.rome.stock.innerservice.infrastructure.mapper.frontrecord.OrderCostMapper;
import com.rome.stock.innerservice.remote.base.dto.DictionaryDTO;
import com.rome.stock.innerservice.remote.base.dto.StoreDTO;
import com.rome.stock.innerservice.remote.base.facade.BaseDataFacade;
import com.rome.stock.innerservice.remote.base.facade.ShopFacade;
import com.rome.stock.innerservice.remote.item.dto.SkuInfoExtDTO;
import com.rome.stock.innerservice.remote.item.facade.SkuFacade;
import com.rome.stock.innerservice.remote.venus.dto.StockTotalPriceDTO;
import com.rome.stock.innerservice.remote.venus.dto.StockTotalPriceParamDTO;
import com.rome.stock.innerservice.remote.venus.facade.VenusStockFacade;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDate;
import java.time.temporal.ChronoUnit;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR> zk
 * @since : 2023-10-10
 */
@Slf4j
@Service
public class StockStatementServiceImpl implements StockStatementService {
    @Resource
    private SkuFacade skuFacade;

    @Resource
    private RwStockChangeFlowService rwStockChangeFlowService;

    @Resource
    private BaseDataFacade baseDataFacade;

    @Resource
    private ShopFacade shopFacade;

    @Resource
    private OrderCostMapper orderCostMapper;

    @Resource
    private VenusStockFacade venusStockFacade;
    @Resource
    private RealWarehouseMapper realWarehouseMapper;
    @Resource
    private WarehouseRecordMapper warehouseRecordMapper;

    @Override
    public PageInfo<RwStockStatementDTO> getStockStatement(StockStatementDTO stockStatementDTO) {
        List<RwStockStatementDTO> rwStockStatementDTOList = new ArrayList<>();
        List<String> shopCodeList = stockStatementDTO.getShopCodeList();
        List<String> dsCodeList = stockStatementDTO.getDsCodeList();
        if (CollectionUtil.isEmpty(shopCodeList) && CollectionUtil.isEmpty(dsCodeList)) {
            throw new RomeException(ResCode.STOCK_ERROR_1002, "加盟商和门店必须选择一个");
        }
        if (CollectionUtil.isNotEmpty(shopCodeList) && shopCodeList.size() > 100) {
            throw new RomeException(ResCode.STOCK_ERROR_1002, "门店最多选择100个");
        }
        if (CollectionUtil.isNotEmpty(dsCodeList) && dsCodeList.size() > 10) {
            throw new RomeException(ResCode.STOCK_ERROR_1002, "加盟商最多选择10个");
        }

        List<String> skuCodeList = stockStatementDTO.getSkuCodeList();
        // 查询库存流水
        RwStockChangeFlowParamDTO rwStockChangeFlowParamDTO = new RwStockChangeFlowParamDTO();
        String startTime = stockStatementDTO.getStartTime();
        String endTime = stockStatementDTO.getEndTime();
        List<StockTotalPriceDTO> stockTotalPrice = null;
        if (StringUtils.isNotEmpty(stockStatementDTO.getFinanceDate())) {
            if (CollectionUtil.isEmpty(stockStatementDTO.getShopCodeList()) || stockStatementDTO.getShopCodeList().size() !=1
                || CollectionUtil.isEmpty(stockStatementDTO.getSkuCodeList()) || stockStatementDTO.getSkuCodeList().size() !=1
            ) {
                throw new RomeException(ResCode.STOCK_ERROR_1002, "财务日期只支持单个门店，单个物料查询");
            }
            RealWarehouseDO realWarehouseDO = realWarehouseMapper.queryByCode(stockStatementDTO.getShopCodeList().get(0));
            AlikAssert.notNull(realWarehouseDO, ResCode.STOCK_ERROR_1010,  ResCode.STOCK_ERROR_1010_DESC);

            //先根据财务日期查询单号
            StockTotalPriceParamDTO stockTotalPriceParamDTO = new StockTotalPriceParamDTO();
            stockTotalPriceParamDTO.setMinFinanceDate(stockStatementDTO.getFinanceDate());
            stockTotalPriceParamDTO.setBusinessType(stockStatementDTO.getQueryType());
            stockTotalPriceParamDTO.setMaxFinanceDate(stockStatementDTO.getFinanceDate());
            stockTotalPriceParamDTO.setSkuCodeList(stockStatementDTO.getSkuCodeList());
            stockTotalPriceParamDTO.setStockOrgCodeList(stockStatementDTO.getShopCodeList());
            stockTotalPriceParamDTO.setCompanyCodeList(Arrays.asList(realWarehouseDO.getCompanyCode()));
            stockTotalPrice = venusStockFacade.getStockTotalPrice(stockTotalPriceParamDTO);
            // 查询财务中台的盘点单数据
            Set<String> ztBusinessCodeSet = new HashSet<>();
            for (StockTotalPriceDTO stockTotalPriceDTO : stockTotalPrice) {
                if (stockTotalPriceDTO.getZtRecordCode().startsWith(realWarehouseDO.getRealWarehouseCode())) {
                    ztBusinessCodeSet.add(stockTotalPriceDTO.getZtRecordCode());
                }
            }

            List<String> recordCodeList = new ArrayList<>();


            if (CollectionUtil.isNotEmpty(ztBusinessCodeSet)) {
                List<String> itemRecordCodeList = warehouseRecordMapper.queryRecordCodeBySapOrderCodes(new ArrayList<>(ztBusinessCodeSet));
                //出入库时间为 财务日期，或者财务日期的后一天
                recordCodeList.addAll(itemRecordCodeList);
            }

            Date startDate = DateUtil.parseDate(stockStatementDTO.getFinanceDate());
            Date endDate =  DateUtil.offsiteDate( DateUtil.parseDate(stockStatementDTO.getFinanceDate()),Calendar.DAY_OF_MONTH, 1);

            //直接查询财务中台的接口，获取单据列表数据
            List<OrderCostDO>  recordCodeCostDOList = orderCostMapper.queryRecordCodeByDate(stockStatementDTO.getFinanceDate() + " 00:00:00", stockStatementDTO.getFinanceDate()+ " 23:59:59",realWarehouseDO.getId(), stockStatementDTO.getSkuCodeList().get(0),stockStatementDTO.getQueryType());
            for (OrderCostDO orderCostDO : recordCodeCostDOList) {
                recordCodeList.add(orderCostDO.getRecordCode());
                if (startDate.after(orderCostDO.getOutOrInTime())) {
                    startDate = orderCostDO.getOutOrInTime();
                }
                if (endDate.before(orderCostDO.getOutOrInTime())) {
                    endDate = orderCostDO.getOutOrInTime();
                }
            }
            startTime = DateUtil.formatDate(startDate) + " 00:00:00";
            endTime = DateUtil.formatDate(endDate) + " 23:59:59";

            if (CollectionUtil.isEmpty(recordCodeList)) {
                return new PageInfo<>();
            }
            rwStockChangeFlowParamDTO.setRecordCodeList(recordCodeList);
            stockStatementDTO.setPageIndex(1);
            stockStatementDTO.setPageSize(100000);
        } else {
            if (StringUtils.isEmpty(startTime) || StringUtils.isEmpty(endTime)) {
                throw new RomeException(ResCode.STOCK_ERROR_1002, "参数有误,时间不能为空");
            }
            LocalDate startDate = DateFormatTools.parseLocalDate(startTime, DateFormatTools.DEFAULT_FORMAT_MODE_DATE);
            LocalDate endDate = DateFormatTools.parseLocalDate(endTime, DateFormatTools.DEFAULT_FORMAT_MODE_DATE);

            long betweenDays = DateFormatTools.getChronoUnitBetween(startDate, endDate, ChronoUnit.DAYS);
            if (CollectionUtil.isNotEmpty(skuCodeList) &&  betweenDays > 180) {
                throw new RomeException(ResCode.STOCK_ERROR_1002, "参数有误,时间最多只能查询180天");
            } else if (CollectionUtil.isEmpty(skuCodeList) &&  betweenDays > 31) {
                throw new RomeException(ResCode.STOCK_ERROR_1002, "参数有误,时间最多只能查询31天");
            }
        }

        List<String> storeDTOList = new ArrayList<>();
        if (CollectionUtil.isNotEmpty(dsCodeList)) {
            List<StoreDTO> baseStoreDTOList =
                    baseDataFacade.selectStoreByFranchiseeList(dsCodeList, false, 0, 100);
            storeDTOList = baseStoreDTOList.stream()
                    .map(storeDTO -> storeDTO.getCode()).collect(Collectors.toList());
            if (CollectionUtil.isEmpty(storeDTOList)) {
                throw new RomeException(ResCode.STOCK_ERROR_1002, "门店不存在");
            }
        }
        if (CollectionUtil.isNotEmpty(shopCodeList)) {
            if (CollectionUtil.isNotEmpty(storeDTOList)) {
                shopCodeList.retainAll(storeDTOList);
            }
        } else {
            shopCodeList = storeDTOList;
        }
        if (CollectionUtil.isEmpty(shopCodeList)) {
            throw new RomeException(ResCode.STOCK_ERROR_1002, "所选择门店不存在");
        }
        // 将skucode装换为skuid
        List<SkuInfoExtDTO> skuInfoExtDTOS = skuFacade.skuListBySkuCodes(skuCodeList);
        List<Long> skuIds = skuInfoExtDTOS.stream().map(item -> item.getId()).collect(Collectors.toList());
        if (CollectionUtil.isNotEmpty(skuCodeList) && CollectionUtil.isEmpty(skuIds)) {
            throw new RomeException(ResCode.STOCK_ERROR_1043, ResCode.STOCK_ERROR_1043_DESC);
        }

        Integer queryType = stockStatementDTO.getQueryType();
        if (queryType == null) {
            rwStockChangeFlowParamDTO.setStockTypes(new Integer[]{1, 2, 5});
        } else if (queryType == 1) {
            rwStockChangeFlowParamDTO.setStockTypes(new Integer[]{2, 5});
        } else if (queryType == 2) {
            rwStockChangeFlowParamDTO.setStockTypes(new Integer[]{1});
        } else {
            rwStockChangeFlowParamDTO.setStockTypes(new Integer[]{1, 2, 5});
        }
        rwStockChangeFlowParamDTO.setQueryType(queryType + "");

        rwStockChangeFlowParamDTO.setRealWarehouseCodes(shopCodeList);
        rwStockChangeFlowParamDTO.setSapOrderCodes(stockStatementDTO.getBusinessCodeList());
        rwStockChangeFlowParamDTO.setSkuIds(skuIds);
        rwStockChangeFlowParamDTO.setStartTime(startTime);
        rwStockChangeFlowParamDTO.setEndTime(endTime);
        rwStockChangeFlowParamDTO.setPageIndex(stockStatementDTO.getPageIndex());
        rwStockChangeFlowParamDTO.setPageSize(stockStatementDTO.getPageSize());
        String recordTypeCode = stockStatementDTO.getRecordTypeCode();
        if (StringUtils.isNotEmpty(recordTypeCode)) {
            rwStockChangeFlowParamDTO.setTransTypes(JSONObject.parseArray(recordTypeCode, Integer.class));
        }
        PageInfo<RwStockChangeFlowResultDTO> stockChangeFlowResultDTOPageInfo
                = rwStockChangeFlowService.queryRwStockChangeFlowHistoryForCrossYear(rwStockChangeFlowParamDTO);
        List<RwStockChangeFlowResultDTO> changeFlowResultDTOPageInfoList
                = stockChangeFlowResultDTOPageInfo.getList();
        // 获取库存流水数据
        getRwStockStatementDTO(changeFlowResultDTOPageInfoList, rwStockStatementDTOList, stockTotalPrice);
        PageInfo<RwStockStatementDTO> rwStockStatementDTOPageInfo = new PageInfo<>();
        BeanUtils.copyProperties(stockChangeFlowResultDTOPageInfo, rwStockStatementDTOPageInfo);
        rwStockStatementDTOPageInfo.setList(rwStockStatementDTOList);
        return rwStockStatementDTOPageInfo;
    }

    private List<RwStockStatementDTO> getRwStockStatementDTO(List<RwStockChangeFlowResultDTO> changeFlowResultDTOPageInfoList,
                                                             List<RwStockStatementDTO> rwStockStatementDTOList, List<StockTotalPriceDTO> stockTotalPrice) {

        if (CollectionUtil.isEmpty(changeFlowResultDTOPageInfoList)) {
            return rwStockStatementDTOList;
        }
        // 获取单据映射值
        List<DictionaryDTO> dictionaryDTOList = baseDataFacade.searchByTypeCode("STOCK_GC_TYPE_REF");
        Map<Integer, String> dictMap = new HashMap<>();
        // 映射业务类型
        for (DictionaryDTO dto : dictionaryDTOList) {
            String dicValue = dto.getDicValue();
            List<Integer> list = JSON.parseArray(dicValue, Integer.class);
            for (Integer integer : list) {
                dictMap.put(integer, dto.getDescription());
            }
        }

        // 通过门店获取加盟商信息
        Set<String> realWarehouseCodeSet = new HashSet<>();
        Set<String> recordCodeSet = new HashSet<>();
        Set<String> skuCodeSet = new HashSet<>();
        List<String> dateRangeList = new ArrayList<>();
         for (RwStockChangeFlowResultDTO rwStockChangeFlowResultDTO : changeFlowResultDTOPageInfoList) {
             realWarehouseCodeSet.add(rwStockChangeFlowResultDTO.getRealWarehouseCode());
             skuCodeSet.add(rwStockChangeFlowResultDTO.getSkuCode());
            //如果是盘点单
            if (Objects.equals(rwStockChangeFlowResultDTO.getTransType(), WarehouseRecordTypeVO.SHOP_INVENTORY_OUT_WAREHOUSE_RECORD.getType())
                    || Objects.equals(rwStockChangeFlowResultDTO.getTransType(), WarehouseRecordTypeVO.SHOP_INVENTORY_IN_WAREHOUSE_RECORD.getType())
            ) {
                //如果是盘点单，则取sap_order_no为ztRecordCode
                recordCodeSet.add(rwStockChangeFlowResultDTO.getSapOrderCode());
                //盘点单的财务日期为创建时间当天，或者往后推一天，两种情况
                dateRangeList.add(DateUtil.formatDate(DateUtil.offsiteDate(rwStockChangeFlowResultDTO.getCreateTime(), Calendar.DAY_OF_MONTH, -1)));
                dateRangeList.add(DateUtil.formatDate(rwStockChangeFlowResultDTO.getCreateTime()));
            } else {
                recordCodeSet.add(rwStockChangeFlowResultDTO.getRecordCode());
            }
        }
        //将出入库时间转化为财务日期

        List<StoreDTO> storeDTOS = shopFacade.searchByCodeList(new ArrayList<>(realWarehouseCodeSet));
        Map<String, StoreDTO> storeDTOMap = new HashMap<>();
        Set<String> companyCodeSet = new HashSet<>();
        for (StoreDTO storeDTO : storeDTOS) {
            storeDTOMap.put(storeDTO.getCode(), storeDTO);
            companyCodeSet.add(storeDTO.getCompanyCode());
        }
        Map<String, OrderCostDO> orderCostDOMap = new HashMap<>();
        if (stockTotalPrice == null) {
            List<OrderCostDO> financeDateList = orderCostMapper.getFinanceDate(new ArrayList<>(recordCodeSet));
            // 出入库单号

            if (CollectionUtil.isNotEmpty(financeDateList)) {
                orderCostDOMap = financeDateList.stream()
                        .collect(Collectors.toMap(OrderCostDO::getRecordCode, Function.identity(), (v1, v2) -> v1));
                dateRangeList.add(DateUtil.formatDate(financeDateList.get(0).getFinanceDate()));
                dateRangeList.add(DateUtil.formatDate(financeDateList.get(financeDateList.size() -1).getFinanceDate()));
            }
            recordCodeSet.addAll(orderCostDOMap.keySet());
            Collections.sort(dateRangeList);

            if (CollectionUtil.isNotEmpty(dateRangeList)) {
                // 获取库存变更金额和含税金额
                StockTotalPriceParamDTO stockTotalPriceParamDTO = new StockTotalPriceParamDTO();
                stockTotalPriceParamDTO.setMinFinanceDate(dateRangeList.get(0));
                stockTotalPriceParamDTO.setMaxFinanceDate(dateRangeList.get(dateRangeList.size() - 1));
                stockTotalPriceParamDTO.setRecordCodeList(new ArrayList<>(recordCodeSet));
                stockTotalPriceParamDTO.setSkuCodeList(new ArrayList<>(skuCodeSet));
                stockTotalPriceParamDTO.setCompanyCodeList(new ArrayList<>(companyCodeSet));
                stockTotalPrice = venusStockFacade.getStockTotalPrice(stockTotalPriceParamDTO);
            }
        }
        Map<String, StockTotalPriceDTO> stockTotalPriceDTOMap =  null;
        if (CollectionUtil.isNotEmpty(stockTotalPrice)) {
            stockTotalPriceDTOMap = stockTotalPrice.stream()
                    .collect(Collectors.toMap
                            (stockTotalPriceDTO ->
                                    stockTotalPriceDTO.getZtRecordCode()+stockTotalPriceDTO.getSkuCode(),
                                    Function.identity(), (v1, v2) -> v1));
        }
        // 组装返回参数 ， 价格都用单价处理
        for (RwStockChangeFlowResultDTO rwStockChangeFlowResultDTO : changeFlowResultDTOPageInfoList) {
            RwStockStatementDTO rwStockStatementDTO = new RwStockStatementDTO();
            BeanUtils.copyProperties(rwStockChangeFlowResultDTO, rwStockStatementDTO);
            // 映射记账类型
            WarehouseRecordTypeVO warehouseRecordTypeVO =
                    WarehouseRecordTypeVO.getByType(rwStockChangeFlowResultDTO.getRecordType());
            Integer businessType = warehouseRecordTypeVO.getBusinessType();
            String descByType = WarehouseRecordBusinessTypeVO.getDescByType(businessType);
            rwStockStatementDTO.setStockTypeDesc(descByType);
            // 映射业务类型
            rwStockStatementDTO.setRecordTypeDesc(dictMap.get(rwStockChangeFlowResultDTO.getRecordType()));
            // 映射加盟商和名单信息
            String realWarehouseCode = rwStockStatementDTO.getRealWarehouseCode();
            if (storeDTOMap != null &&!storeDTOMap.isEmpty()) {
                StoreDTO storeDTO = storeDTOMap.get(realWarehouseCode);
                if (storeDTO != null) {
                    rwStockStatementDTO.setStoreCode(storeDTO.getCode());
                    rwStockStatementDTO.setStoreName(storeDTO.getName());
                    rwStockStatementDTO.setFranchisee(storeDTO.getFranchisee());
                    rwStockStatementDTO.setFranchiseeName(storeDTO.getFranchiseeName());
                }
            }

            // 映射库存变更金额和含税金额和商品品类
            if (stockTotalPriceDTOMap != null && !stockTotalPriceDTOMap.isEmpty()) {
                StockTotalPriceDTO  stockTotalPriceDTO = null;
                if (Objects.equals(rwStockChangeFlowResultDTO.getTransType(), WarehouseRecordTypeVO.SHOP_INVENTORY_OUT_WAREHOUSE_RECORD.getType())
                        || Objects.equals(rwStockChangeFlowResultDTO.getTransType(), WarehouseRecordTypeVO.SHOP_INVENTORY_IN_WAREHOUSE_RECORD.getType())
                ) {
                    stockTotalPriceDTO = stockTotalPriceDTOMap.get(rwStockStatementDTO.getSapOrderCode() + rwStockStatementDTO.getSkuCode());

                } else {
                    stockTotalPriceDTO = stockTotalPriceDTOMap.get(rwStockStatementDTO.getRecordCode() + rwStockStatementDTO.getSkuCode());

                }
                if (stockTotalPriceDTO != null) {
                    BigDecimal totalPrice = stockTotalPriceDTO.getPurUnitPrice().multiply(rwStockChangeFlowResultDTO.getChangeQty()).setScale(2, RoundingMode.HALF_UP);
                    BigDecimal taxPur = stockTotalPriceDTO.getTaxSale();
                    if (taxPur == null) {
                        taxPur = BigDecimal.ZERO;
                    }
                    BigDecimal totalPriceTax = totalPrice.multiply(taxPur.add(new BigDecimal(100))).divide(new BigDecimal(100),  RoundingMode.HALF_UP);
                    rwStockStatementDTO.setTotalPrice( totalPrice);
                    rwStockStatementDTO.setTotalPriceTax( totalPriceTax);
                    rwStockStatementDTO.setCategoryName4(stockTotalPriceDTO.getCategoryName4());
                    rwStockStatementDTO.setFinanceDate(stockTotalPriceDTO.getFinancialDate());
                }
            }
            // 映射财务日期
            if (orderCostDOMap != null && !orderCostDOMap.isEmpty()) {
                OrderCostDO orderCostDO = orderCostDOMap.get(rwStockStatementDTO.getRecordCode());
                //收发明细不存在时，再次填充
                if (orderCostDO != null && rwStockStatementDTO.getFinanceDate() == null) {
                    rwStockStatementDTO.setFinanceDate(orderCostDO.getFinanceDate());
                }
            }

            rwStockStatementDTOList.add(rwStockStatementDTO);
        }
        return rwStockStatementDTOList;
    }
}
