/**
 * Filename TestStockServiceImpl.java
 * Company 上海来伊份科技有限公司。
 * <AUTHOR>
 * @version 
 */
package com.rome.stock.innerservice.domain.service.impl;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.UUID;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import com.rome.arch.core.exception.RomeException;
import com.rome.stock.core.api.dto.ChannelSalesStockDTO;
import com.rome.stock.core.domain.repository.CoreChannelSalesRepository;
import com.rome.stock.core.domain.repository.CoreGroupStockRepository;
import com.rome.stock.core.domain.repository.CoreRealWarehouseStockRepository;
import com.rome.stock.core.domain.repository.CoreVirtualWarehouseStockRepository;
import com.rome.stock.innerservice.domain.service.TestCoreStockService;
import com.rome.stock.core.facade.RedisRollBackFacade;
import com.rome.stock.innerservice.facade.StockOnlineOrderFacade;
import com.rome.stock.core.infrastructure.dataobject.core.CoreChannelOrderDO;
import com.rome.stock.core.infrastructure.dataobject.core.CoreChannelSalesBatchOpDO;
import com.rome.stock.core.infrastructure.dataobject.core.CoreGroupStockDO;
import com.rome.stock.core.infrastructure.dataobject.core.CoreRealStockOpDO;
import com.rome.stock.core.infrastructure.dataobject.core.CoreRealStockOpDetailDO;
import com.rome.stock.core.infrastructure.dataobject.core.CoreRealWarehouseStockDO;
import com.rome.stock.core.infrastructure.dataobject.core.CoreStockOpFactoryDO;
import com.rome.stock.core.infrastructure.dataobject.core.CoreVirtualStockOpDO;
import com.rome.stock.core.infrastructure.dataobject.core.CoreVirtualWarehouseStockDO;

/**
 * 库存模型测试类
 * <AUTHOR>
 * @since 2019年4月24日 上午11:19:26
 */
@Slf4j
@Service
public class TestCoreStockServiceImpl implements TestCoreStockService{   

	@Autowired
    private CoreRealWarehouseStockRepository coreRealWarehouseStockRepository;
	
	@Autowired
    private CoreChannelSalesRepository coreChannelSalesRepository;
	
	@Autowired
    private CoreVirtualWarehouseStockRepository coreVirtualWarehouseStockRepository;
	
	@Autowired
    private CoreGroupStockRepository coreGroupStockRepository;
	
	/**
	 * 添加库存
	 * 说明：此接口是从下向上添加库存，适应如盘盈，调拔入库，采购入库等，不适合销售类业务。
	 */
	@Override
    @Transactional(rollbackFor = Exception.class)
	public boolean realWarehouseIncreaseRealQty(CoreRealStockOpDO increaseDo) {		
		//所有的处理成功标识
		boolean isSuccess=false;
		Integer t=null;
		try {
			//业务处理1
			coreRealWarehouseStockRepository.increaseRealQty(increaseDo);
			//业务处理2
			{
				
				System.out.println();
				//以下为模拟出错
//				if(t==null) {
//					throw new RomeException("模拟出错测试","模拟出错测试");
//				}
			}
			isSuccess=true;
		} catch (Exception e) {
			log.error(e.getMessage(), e);
			throw e;
		}finally {
			//模型成功，业务等处理失败，需要回滚
			if(isSuccess==false) {
				RedisRollBackFacade.redisRollBack(increaseDo);
			}
		}		
		return true;
	}
	
	/**
	 * 减少库存
	 * 此接口是从下向上减少库存，适应如盘亏，调拔出库等等，不适合销售类业务。
	 */
	@Override
    @Transactional(rollbackFor = Exception.class)
	public boolean realWarehouseDecreaseRealQty(CoreRealStockOpDO decreaseDo) {	
		//所有的处理成功标识
		boolean isSuccess=false;
		Integer t=null;
		try {
			//业务处理1
			coreRealWarehouseStockRepository.decreaseRealQty(decreaseDo);
			//业务处理2
			{
				
				System.out.println();
				//以下为模拟出错
//				if(t==null) {
//					throw new RomeException("模拟出错测试","模拟出错测试");
//				}
			}
			isSuccess=true;
		} catch (Exception e) {
			log.error(e.getMessage(), e);
			throw e;
		}finally {
			//模型成功，业务等处理失败，需要回滚
			if(isSuccess==false) {
				RedisRollBackFacade.redisRollBack(decreaseDo);
			}
		}				
		return true;
	}
	
	/**
	 * 渠道
	 * 适应零售,外卖调用下单--减少库存
	 * 说明：此接口是从上向下减少库存，适应零售,外卖调用,等等只有一个虚仓的业务场景,销售类业务。
	 */
	@Override
    @Transactional(rollbackFor = Exception.class)
	public CoreChannelOrderDO channelDecreaseStockRetailOrO2O(CoreChannelOrderDO channelOrderDO) throws RomeException{
		//所有的处理成功标识
		boolean isSuccess=false;
		Integer t=null;		
		try {
			//业务处理1
			coreChannelSalesRepository.decreaseStockRetailOrO2O(channelOrderDO);
			//业务处理2
			{
				
				System.out.println();
				//以下为模拟出错
//				if(t==null) {
//					throw new RomeException("模拟出错测试","模拟出错测试");
//				}
				
			}
			isSuccess=true;
		} catch (Exception e) {
			log.error(e.getMessage(), e);
			throw e;
		}finally {
			//模型成功，业务等处理失败，需要回滚
			if(isSuccess==false) {
				RedisRollBackFacade.redisRollBack(channelOrderDO);
			}
		}
		return channelOrderDO;
	}
	
	/**
	 * 对外提供服务
	 * 查询实仓库存
	 * @param dtos
	 * @return
	 */
	@Override
	public List<CoreRealWarehouseStockDO> getRWStock(List<CoreRealWarehouseStockDO> dtos) {
		return coreRealWarehouseStockRepository.getRWStock(dtos);
	}

	/**
	 * 对外提供服务
	 * 获取渠道库存,获取渠道可售库存
	 * @param dtos
	 * @return
	 */
	@Override
	public List<ChannelSalesStockDTO> getChannelSaleAvailableStock(List<ChannelSalesStockDTO> dtos) {
		return coreChannelSalesRepository.getChannelSaleAvailableStock(dtos);
	}
	
	/**
	 * 对外提供服务
	 * 获取渠道库存
	 * @param dtos
	 * @return
	 */
	@Override
	public List<ChannelSalesStockDTO> getChannelSaleStock(List<ChannelSalesStockDTO> dtos) {
		return coreChannelSalesRepository.getChannelSaleStock(dtos);
	}
	
	/**
	 * 对外提供服务
	 * 锁定库存，即冻解库存
	 * 说明：此接口是从下向上锁定库存
	 * @param stockDO
	 * @return
	 * @throws RomeException
	 */
	@Override
    @Transactional(rollbackFor = Exception.class)
	public CoreRealStockOpDO realWarehouseLockStock(CoreRealStockOpDO stockDO) throws RomeException{
		//所有的处理成功标识
		boolean isSuccess=false;
		Integer t=null;
		try {
			//业务处理1
			coreRealWarehouseStockRepository.lockStock(stockDO);
			//业务处理2
			{
				
				System.out.println();
				//以下为模拟出错
//				if(t==null) {
//					throw new RomeException("模拟出错测试","模拟出错测试");
//				}
			}
			isSuccess=true;
		} catch (Exception e) {
			log.error(e.getMessage(), e);
			throw e;
		}finally {
			//模型成功，业务等处理失败，需要回滚
			if(isSuccess==false) {
				RedisRollBackFacade.redisRollBack(stockDO);
			}
		}
		return stockDO;
	}
	
	/**
	 * 对外提供服务
	 * 释放库存，即解冻库存
	 * 说明：此接口是从下向上释放库存
	 * @param stockDO
	 * @return
	 * @throws RomeException
	 */
	@Override
    @Transactional(rollbackFor = Exception.class)
	public CoreRealStockOpDO realWarehouseUnlockStock(CoreRealStockOpDO stockDO) throws RomeException{
		//所有的处理成功标识
		boolean isSuccess=false;
		Integer t=null;
		try {
			//业务处理1
			coreRealWarehouseStockRepository.unlockStock(stockDO);
			
			//业务处理2
			{
				
				System.out.println();
				//以下为模拟出错
//				if(t==null) {
//					throw new RomeException("模拟出错测试","模拟出错测试");
//				}
				
			}
			isSuccess=true;
		} catch (Exception e) {
			log.error(e.getMessage(), e);
			throw e;
		}finally {
			//模型成功，业务等处理失败，需要回滚
			if(isSuccess==false) {
				RedisRollBackFacade.redisRollBack(stockDO);
			}
		}
		return stockDO;
	}
	
	/**
	 * 对外提供服务
	 * 出库
	 * 说明：此接口是从下向上出库
	 * @param stockDO
	 * @return
	 * @throws RomeException
	 */
	@Override
    @Transactional(rollbackFor = Exception.class)
	public CoreRealStockOpDO realWarehouseOutStock(CoreRealStockOpDO stockDO) throws RomeException{
		//所有的处理成功标识
		boolean isSuccess=false;
		Integer t=null;
		try {
			//业务处理1
			coreRealWarehouseStockRepository.outStock(stockDO);
			
			//业务处理2
			{
				
				System.out.println();
				//以下为模拟出错
//				if(t==null) {
//					throw new RomeException("模拟出错测试","模拟出错测试");
//				}
				
			}
			isSuccess=true;
		} catch (Exception e) {
			log.error(e.getMessage(), e);
			throw e;
		}finally {
			//模型成功，业务等处理失败，需要回滚
			if(isSuccess==false) {
				RedisRollBackFacade.redisRollBack(stockDO);
			}
		}
		return stockDO;
	}
	
	/**
	 * 对外提供服务
	 * 出库和释放多锁的库存,本接口是针对锁定库存数大于出库数,且多锁的也一并释放掉，其他场景不适合
	 * 说明：此接口是从下向上
	 * @param stockDO
	 * @return
	 * @throws RomeException
	 */
	@Override
    @Transactional(rollbackFor = Exception.class)
	public CoreRealStockOpDO realWarehouseOutAndUnlockStock(CoreRealStockOpDO stockDO) throws RomeException{
		//所有的处理成功标识
		boolean isSuccess=false;   
		Integer t=null;
		try {
			//业务处理1
			coreRealWarehouseStockRepository.outAndUnlockStock(stockDO);
			
			//业务处理2
			{
				
				System.out.println();
				//以下为模拟出错
//				if(t==null) {
//					throw new RomeException("模拟出错测试","模拟出错测试");
//				}
				
			}
			isSuccess=true;
		} catch (Exception e) {
			log.error(e.getMessage(), e);
			throw e;
		}finally {
			//模型成功，业务等处理失败，需要回滚
			if(isSuccess==false) {
				RedisRollBackFacade.redisRollBack(stockDO);
			}
		}
		return stockDO;
	}
	
	/**
	 * 对外提供服务
	 * 添加在途库存
	 * 说明：此接口是添加实仓的在途库存，适应如门店向大仓采购在途等，不适合销售类业务。  
	 * @param stockDO
	 * @return
	 * @throws RomeException
	 */
	@Override
    @Transactional(rollbackFor = Exception.class)
	public CoreRealStockOpDO realWarehouseIncreaseOnroadStock(CoreRealStockOpDO stockDO) throws RomeException{
		//所有的处理成功标识
		boolean isSuccess=false;
		Integer t=null;
		try {
			//业务处理1
			coreRealWarehouseStockRepository.increaseOnroadStock(stockDO);
			
			//业务处理2
			{
				
				System.out.println();
				//以下为模拟出错
//				if(t==null) {
//					throw new RomeException("模拟出错测试","模拟出错测试");
//				}
				
			}
			isSuccess=true;
		} catch (Exception e) {
			log.error(e.getMessage(), e);
			throw e;
		}finally {
			//模型成功，业务等处理失败，需要回滚
			if(isSuccess==false) {
				RedisRollBackFacade.redisRollBack(stockDO);
			}
		}
		return stockDO;
	}
	
	/**
	 * 对外提供服务
	 * 减少在途库存
	 * 说明：此接口是减少实仓的在途库存，适应如门店向大仓采购在途等，不适合销售类业务。  
	 * @param stockDO
	 * @return
	 * @throws RomeException
	 */
	@Override
    @Transactional(rollbackFor = Exception.class)
	public CoreRealStockOpDO realWarehouseDecreaseOnroadStock(CoreRealStockOpDO stockDO) throws RomeException{
		//所有的处理成功标识
		boolean isSuccess=false;
		Integer t=null;
		try {
			//业务处理1
			coreRealWarehouseStockRepository.decreaseOnroadStock(stockDO);
			
			//业务处理2
			{
				
				System.out.println();
				//以下为模拟出错
//				if(t==null) {
//					throw new RomeException("模拟出错测试","模拟出错测试");
//				}
				
			}
			isSuccess=true;
		} catch (Exception e) {
			log.error(e.getMessage(), e);
			throw e;
		}finally {
			//模型成功，业务等处理失败，需要回滚
			if(isSuccess==false) {
				RedisRollBackFacade.redisRollBack(stockDO);
			}
		}
		return stockDO;
	}
	
	/**
	 * 对外提供服务
	 * 添加质检库存
	 * 说明：此接口是添加实仓的质检库存，适应如大仓向供应商采购需要质检等，不适合销售类业务。 
	 * @param stockDO
	 * @return
	 * @throws RomeException
	 */
	@Override
    @Transactional(rollbackFor = Exception.class)
	public CoreRealStockOpDO realWarehouseIncreaseQualityStock(CoreRealStockOpDO stockDO) throws RomeException{
		//所有的处理成功标识
		boolean isSuccess=false;
		Integer t=null;
		try {
			//业务处理1
			coreRealWarehouseStockRepository.increaseQualityStock(stockDO);
			
			//业务处理2
			{
				
				System.out.println();
				//以下为模拟出错
//				if(t==null) {
//					throw new RomeException("模拟出错测试","模拟出错测试");
//				}
				
			}
			isSuccess=true;
		} catch (Exception e) {
			log.error(e.getMessage(), e);
			throw e;
		}finally {
			//模型成功，业务等处理失败，需要回滚
			if(isSuccess==false) {
				RedisRollBackFacade.redisRollBack(stockDO);
			}
		}
		return stockDO;
	}
	
	/**
	 * 对外提供服务
	 * 减少质检库存
	 * 说明：此接口是减少实仓的质检库存，适应如大仓向供应商采购需要质检等，不适合销售类业务。
	 * @param stockDO
	 * @return
	 * @throws RomeException
	 */
	@Override
    @Transactional(rollbackFor = Exception.class)
	public CoreRealStockOpDO realWarehouseDecreaseQualityStock(CoreRealStockOpDO stockDO) throws RomeException{
		//所有的处理成功标识
		boolean isSuccess=false;
		Integer t=null;
		try {
			//业务处理1
			coreRealWarehouseStockRepository.decreaseQualityStock(stockDO);
			
			//业务处理2
			{
				
				System.out.println();
				//以下为模拟出错
//				if(t==null) {
//					throw new RomeException("模拟出错测试","模拟出错测试");
//				}
				
			}
			isSuccess=true;
		} catch (Exception e) {
			log.error(e.getMessage(), e);
			throw e;
		}finally {
			//模型成功，业务等处理失败，需要回滚
			if(isSuccess==false) {
				RedisRollBackFacade.redisRollBack(stockDO);
			}
		}
		return stockDO;
	}
	
	/**
	 * 对外提供服务
	 * 增加质检不合格库存
	 * 说明：此接口是增加实仓的质检不合格库存，适应如大仓向供应商采购增加质检不合格等，不适合销售类业务。
	 * @param stockDO
	 * @return
	 * @throws RomeException
	 */
	@Override
    @Transactional(rollbackFor = Exception.class)
	public CoreRealStockOpDO realWarehouseIncreaseUnqualifiedStock(CoreRealStockOpDO stockDO) throws RomeException{
		//所有的处理成功标识
		boolean isSuccess=false;
		Integer t=null;
		try {
			//业务处理1
			coreRealWarehouseStockRepository.increaseUnqualifiedStock(stockDO);
			
			//业务处理2
			{
				
				System.out.println();
				//以下为模拟出错
//				if(t==null) {
//					throw new RomeException("模拟出错测试","模拟出错测试");
//				}
				
			}
			isSuccess=true;
		} catch (Exception e) {
			log.error(e.getMessage(), e);
			throw e;
		}finally {
			//模型成功，业务等处理失败，需要回滚
			if(isSuccess==false) {
				RedisRollBackFacade.redisRollBack(stockDO);
			}
		}
		return stockDO;
	}
	
	/**
	 * 对外提供服务
	 * 减少质检不合格库存
	 * 说明：此接口是减少实仓的质检不合格库存，适应如大仓向供应商采购需要质检不合格等，不适合销售类业务。
	 * @param stockDO
	 * @return
	 * @throws RomeException
	 */
	@Override
    @Transactional(rollbackFor = Exception.class)
	public CoreRealStockOpDO realWarehouseDecreaseUnqualifiedStock(CoreRealStockOpDO stockDO) throws RomeException{
		//所有的处理成功标识
		boolean isSuccess=false;
		Integer t=null;
		try {
			//业务处理1
			coreRealWarehouseStockRepository.decreaseUnqualifiedStock(stockDO);
			
			//业务处理2
			{
				
				System.out.println();
				//以下为模拟出错
//				if(t==null) {
//					throw new RomeException("模拟出错测试","模拟出错测试");
//				}
				
			}
			isSuccess=true;
		} catch (Exception e) {
			log.error(e.getMessage(), e);
			throw e;
		}finally {
			//模型成功，业务等处理失败，需要回滚
			if(isSuccess==false) {
				RedisRollBackFacade.redisRollBack(stockDO);
			}
		}
		return stockDO;
	}
	
	/**
	 * 对外提供服务
	 * 从质检库存移动到不合格库存去
	 * 说明：此接口是实仓的移动从质检到不合格库存去，适应如大仓向供应商采购需要质检不合格等，不适合销售类业务。
	 * @param stockDO
	 * @return
	 * @throws RomeException
	 */
	@Override
    @Transactional(rollbackFor = Exception.class)
	public CoreRealStockOpDO realWarehouseMoveQualityToUnqualifiedStock(CoreRealStockOpDO stockDO) throws RomeException{
		//所有的处理成功标识
		boolean isSuccess=false;
		Integer t=null;
		try {
			//业务处理1
			coreRealWarehouseStockRepository.moveQualityToUnqualifiedStock(stockDO);
			
			//业务处理2
			{
				
				System.out.println();
				//以下为模拟出错
//				if(t==null) {
//					throw new RomeException("模拟出错测试","模拟出错测试");
//				}
				
			}
			isSuccess=true;
		} catch (Exception e) {
			log.error(e.getMessage(), e);
			throw e;
		}finally {
			//模型成功，业务等处理失败，需要回滚
			if(isSuccess==false) {
				RedisRollBackFacade.redisRollBack(stockDO);
			}
		}
		return stockDO;
	}
	
	/**
	 * 对外提供服务
	 * 从质检库存移动到销售库存去
	 * 说明：此接口是从质检库存移动到可售库存去，适应如大仓向供应商采购需要质检不合格等，不适合销售类业务。
	 * @param stockDO
	 * @return
	 * @throws RomeException
	 */
	@Override
    @Transactional(rollbackFor = Exception.class)
	public CoreRealStockOpDO realWarehouseMoveQualityToRealStock(CoreRealStockOpDO stockDO) throws RomeException{
		//所有的处理成功标识
		boolean isSuccess=false;
		Integer t=null;
		try {
			//业务处理1
			coreRealWarehouseStockRepository.moveQualityToRealStock(stockDO);
			
			//业务处理2
			{
				
				System.out.println();
				//以下为模拟出错
//				if(t==null) {
//					throw new RomeException("模拟出错测试","模拟出错测试");
//				}
				
			}
			isSuccess=true;
		} catch (Exception e) {
			log.error(e.getMessage(), e);
			throw e;
		}finally {
			//模型成功，业务等处理失败，需要回滚
			if(isSuccess==false) {
				RedisRollBackFacade.redisRollBack(stockDO);
			}
		}
		return stockDO;
	}
	
	/**
	 * 渠道
	 * 锁定库存，即冻结库存
	 * 说明：此接口是从上向下锁定库存，适合一个渠道只有一个虚仓的销售类业务，其他不适合。
	 * @param channelOrderDO
	 * @return
	 * @throws RomeException
	 */
	@Override
    @Transactional(rollbackFor = Exception.class)
	public CoreChannelOrderDO channelLockStockByOneVW(CoreChannelOrderDO channelOrderDO) throws RomeException{
		//所有的处理成功标识
		boolean isSuccess=false;
		Integer t=null;		
		try {
			//业务处理1
			coreChannelSalesRepository.lockStockByOneVW(channelOrderDO);
			//业务处理2
			{
				
				System.out.println();
				//以下为模拟出错
//				if(t==null) {
//					throw new RomeException("模拟出错测试","模拟出错测试");
//				}
				
			}
			isSuccess=true;
		} catch (Exception e) {
			log.error(e.getMessage(), e);
			throw e;
		}finally {
			//模型成功，业务等处理失败，需要回滚
			if(isSuccess==false) {
				RedisRollBackFacade.redisRollBack(channelOrderDO);
			}
		}
		return channelOrderDO;
	}
	
	/**
	 * 渠道
	 * 释放库存，即解冻库存
	 * 虚仓数据必须传进来
	 * 说明：此接口是从上往下释放库存，适合销售类业务，不适合app大流量的调用。
	 * @param channelOrderDO
	 * @return
	 * @throws RomeException
	 */
	@Override
    @Transactional(rollbackFor = Exception.class)
	public CoreChannelOrderDO channelUnlockStock(CoreChannelOrderDO channelOrderDO) throws RomeException{
		//所有的处理成功标识
		boolean isSuccess=false;
		Integer t=null;		
		try {
			//业务处理1
			coreChannelSalesRepository.unlockStock(channelOrderDO);
			//业务处理2
			{
				
				System.out.println();
				//以下为模拟出错
//				if(t==null) {
//					throw new RomeException("模拟出错测试","模拟出错测试");
//				}
				
			}
			isSuccess=true;
		} catch (Exception e) {
			log.error(e.getMessage(), e);
			throw e;
		}finally {
			//模型成功，业务等处理失败，需要回滚
			if(isSuccess==false) {
				RedisRollBackFacade.redisRollBack(channelOrderDO);
			}
		}
		return channelOrderDO;
	}

	/**
	 * 渠道
	 * 出库
	 * 虚仓数据必须传进来
	 * 说明：此接口是从上往下出库库存，适合销售类业务。
	 * @param channelOrderDO
	 * @return
	 * @throws RomeException
	 */
	@Override
	@Transactional(rollbackFor = Exception.class)
	public CoreChannelOrderDO channelOutStock(CoreChannelOrderDO channelOrderDO) throws RomeException {
		//所有的处理成功标识
		boolean isSuccess=false;
		Integer t=null;		
		try {
			//业务处理1
			coreChannelSalesRepository.outStock(channelOrderDO);
			//业务处理2
			{
				
				System.out.println();
				//以下为模拟出错
//				if(t==null) {
//					throw new RomeException("模拟出错测试","模拟出错测试");
//				}
				
			}
			isSuccess=true;
		} catch (Exception e) {
			log.error(e.getMessage(), e);
			throw e;
		}finally {
			//模型成功，业务等处理失败，需要回滚
			if(isSuccess==false) {
				RedisRollBackFacade.redisRollBack(channelOrderDO);
			}
		}
		return channelOrderDO;
	}

	/**
	 * 渠道
	 * 出库和释放多锁的库存,本接口从上往下出库库存，适合销售类业务
	 * @param channelOrderDO
	 * @return
	 * @throws RomeException
	 */
	@Override
	@Transactional(rollbackFor = Exception.class)
	public CoreChannelOrderDO channelOutAndUnlockStock(CoreChannelOrderDO channelOrderDO) throws RomeException {
		//所有的处理成功标识
		boolean isSuccess=false;
		Integer t=null;		
		try {
			//业务处理1
			coreChannelSalesRepository.outAndUnlockStock(channelOrderDO);
			//业务处理2
			{
				
				System.out.println();
				//以下为模拟出错
//				if(t==null) {
//					throw new RomeException("模拟出错测试","模拟出错测试");
//				}
				
			}
			isSuccess=true;
		} catch (Exception e) {
			log.error(e.getMessage(), e);
			throw e;
		}finally {
			//模型成功，业务等处理失败，需要回滚
			if(isSuccess==false) {
				RedisRollBackFacade.redisRollBack(channelOrderDO);
			}
		}
		return channelOrderDO;		
	}
	
	/**
	 * 设置实仓库存到虚仓同步比率sku级别的
	 * @param stockOpDO
	 * @return
	 * @throws RomeException
	 */
	@Override
	@Transactional(rollbackFor = Exception.class)
	public CoreRealStockOpDO realWarehouseSetSkuVirtualSyncRate(CoreRealStockOpDO stockOpDO) throws RomeException {
		//所有的处理成功标识
		boolean isSuccess=false;
		Integer t=null;		
		try {
			//业务处理1
			coreRealWarehouseStockRepository.setSkuVirtualSyncRate(stockOpDO);
			//业务处理2
			{
				
				System.out.println();
				//以下为模拟出错
//				if(t==null) {
//					throw new RomeException("模拟出错测试","模拟出错测试");
//				}
				
			}
			isSuccess=true;
		} catch (Exception e) {
			log.error(e.getMessage(), e);
			throw e;
		}finally {
			//模型成功，业务等处理失败，需要回滚
			if(isSuccess==false) {
				RedisRollBackFacade.redisRollBack(stockOpDO);
			}
		}
		return stockOpDO;		
	}
	
	/**
	 *  册除sku级别虚仓同步比率（百分比）使用仓库级别的
	 * @param stockOpDO
	 * @return
	 * @throws RomeException
	 */
	@Override
	@Transactional(rollbackFor = Exception.class)
	public CoreRealStockOpDO realWarehouseDelSkuVirtualSyncRate(CoreRealStockOpDO stockOpDO) throws RomeException {
		//所有的处理成功标识
		boolean isSuccess=false;
		Integer t=null;		
		try {
			//业务处理1
			coreRealWarehouseStockRepository.delSkuVirtualSyncRate(stockOpDO);
			//业务处理2
			{
				
				System.out.println();
				//以下为模拟出错
//				if(t==null) {
//					throw new RomeException("模拟出错测试","模拟出错测试");
//				}
				
			}
			isSuccess=true;
		} catch (Exception e) {
			log.error(e.getMessage(), e);
			throw e;
		}finally {
			//模型成功，业务等处理失败，需要回滚
			if(isSuccess==false) {
				RedisRollBackFacade.redisRollBack(stockOpDO);
			}
		}
		return stockOpDO;		
	}
	
	/**
	 * 虚仓间转移真实库存
	 * @param stockDO
	 * @return
	 * @throws RomeException
	 */
	@Override
	@Transactional(rollbackFor = Exception.class)
	public CoreRealStockOpDO virtualWarehouseMoveRealQtyToOtherVWarehouse(CoreRealStockOpDO stockDO) throws RomeException {
		//所有的处理成功标识
		boolean isSuccess=false;
		Integer t=null;		
		try {
			//业务处理1
			coreVirtualWarehouseStockRepository.moveRealQtyToOtherVWarehouse(stockDO);
			//业务处理2
			{
				
				System.out.println();
				//以下为模拟出错
//				if(t==null) {
//					throw new RomeException("模拟出错测试","模拟出错测试");
//				}
				
			}
			isSuccess=true;
		} catch (Exception e) {
			log.error(e.getMessage(), e);
			throw e;
		}finally {
			//模型成功，业务等处理失败，需要回滚
			if(isSuccess==false) {
				RedisRollBackFacade.redisRollBack(stockDO);
			}
		}
		return stockDO;		
	}
	
	/**
	 * 虚仓间转移真实库存
	 * @param stockDO
	 * @return
	 * @throws RomeException
	 */
	@Override
	@Transactional(rollbackFor = Exception.class)
	public CoreRealStockOpDO virtualWarehouseMinAvailMoveRealQtyToOtherVWarehouse(CoreRealStockOpDO stockDO) throws RomeException {
		//所有的处理成功标识
		boolean isSuccess=false;
		Integer t=null;		
		try {
			//业务处理1
			coreVirtualWarehouseStockRepository.minAvailMoveRealQtyToOtherVWarehouse(stockDO);
			//业务处理2
			{
				
				System.out.println();
				//以下为模拟出错
//				if(t==null) {
//					throw new RomeException("模拟出错测试","模拟出错测试");
//				}
				
			}
			isSuccess=true;
		} catch (Exception e) {
			log.error(e.getMessage(), e);
			throw e;
		}finally {
			//模型成功，业务等处理失败，需要回滚
			if(isSuccess==false) {
				RedisRollBackFacade.redisRollBack(stockDO);
			}
		}
		return stockDO;		
	}
	
	/**
	 * 渠道
	 * 锁定库存，即冻解库存
	 * 说明：此接口是从上向下锁定库存,普通锁定库存,调用前已寻源成功。
	 * @param channelOrderDO
	 * @return
	 * @throws RomeException
	 */
	@Override
    @Transactional(rollbackFor = Exception.class)
	public CoreChannelOrderDO channelLockStock(CoreChannelOrderDO channelOrderDO) throws RomeException{
		//所有的处理成功标识
		boolean isSuccess=false;
		Integer t=null;		
		try {
			//业务处理1
			coreChannelSalesRepository.lockStock(channelOrderDO);
			//业务处理2
			{
				
				System.out.println();
				//以下为模拟出错
//				if(t==null) {
//					throw new RomeException("模拟出错测试","模拟出错测试");
//				}
				
			}
			isSuccess=true;
		} catch (Exception e) {
			log.error(e.getMessage(), e);
			throw e;
		}finally {
			//模型成功，业务等处理失败，需要回滚
			if(isSuccess==false) {
				RedisRollBackFacade.redisRollBack(channelOrderDO);
			}
		}
		return channelOrderDO;
	}
	
	/**
	 * 电商仓库(寻源)路由
	 * 寻源无须回滚
	 * @param channelOrderDO
	 * @return
	 * @throws RomeException
	 */
	@Override
	public List<CoreVirtualStockOpDO> channelGetOnlineOrderWarehouseRoute(CoreChannelOrderDO channelOrderDO) throws RomeException{
		return StockOnlineOrderFacade.getWarehouseRoute(channelOrderDO);
	}
	
	/**
	 * 电商仓库(寻源)路由和锁库存，综合方法，适合电商销售，如app、天猫、京东等
	 * @param channelOrderDO
	 * @return
	 * @throws RomeException
	 */
	@Override
    @Transactional(rollbackFor = Exception.class)
	public CoreChannelOrderDO channelOnlineOrderRouteAndLockStock(CoreChannelOrderDO channelOrderDO) throws RomeException{
		//所有的处理成功标识
		boolean isSuccess=false;
		Integer t=null;		
		try {
			//业务处理1
			StockOnlineOrderFacade.routeAndLockStock(channelOrderDO);
			//业务处理2
			{
				
				System.out.println();
				//以下为模拟出错
//				if(t==null) {
//					throw new RomeException("模拟出错测试","模拟出错测试");
//				}
				
			}
			isSuccess=true;
		} catch (Exception e) {
			log.error(e.getMessage(), e);
			throw e;
		}finally {
			//模型成功，业务等处理失败，需要回滚
			if(isSuccess==false) {
				RedisRollBackFacade.redisRollBack(channelOrderDO);
			}
		}
		return channelOrderDO;
	}
	
	/**
	 * 设置渠道共享比例
	 * @param channelSalesBatchOpDO
	 * @return
	 * @throws RomeException
	 */
	@Override
	@Transactional(rollbackFor = Exception.class)
	public void channelSetShowRate(CoreChannelSalesBatchOpDO channelSalesBatchOpDO) throws RomeException {
		//所有的处理成功标识
		boolean isSuccess=false;
		Integer t=null;		
		try {
			//业务处理1
			coreChannelSalesRepository.setShowRate(channelSalesBatchOpDO);
			//业务处理2
			{
				
				System.out.println();
				//以下为模拟出错
//				if(t==null) {
//					throw new RomeException("模拟出错测试","模拟出错测试");
//				}
				
			}
			isSuccess=true;
		} catch (Exception e) {
			log.error(e.getMessage(), e);
			throw e;
		}finally {
			//模型成功，业务等处理失败，需要回滚
			if(isSuccess==false) {
				RedisRollBackFacade.redisRollBack(channelSalesBatchOpDO);
			}
		}	
	}
	
	/**
	 * 对外提供服务
	 * 查询虚仓库存
	 * @param dtos
	 * @return
	 */
	@Override
	public List<CoreVirtualWarehouseStockDO> getVWStock(List<CoreVirtualWarehouseStockDO> dtos) {
		return coreVirtualWarehouseStockRepository.getVWStock(dtos);
	}
	
	/**
	 * 对外提供服务
	 * 查询策略组库存
	 * @param dtos
	 * @return 返回值中vwIds为虚仓id列表
	 */
	@Override
	public List<CoreGroupStockDO> getGStock(List<CoreGroupStockDO> dtos) {
		return coreGroupStockRepository.getGStock(dtos);
	}	
	
	/**
	 * 更新实仓到虚仓同步比率（百分比）-仓库级别的,正确使用方法为同步比率或挂载虚仓改变后再调此接口
	 * @param stockOpDO
	 * @return
	 * @throws RomeException
	 */
	@Override
	@Transactional(rollbackFor = Exception.class)
	public CoreRealStockOpDO realWarehouseUpdateVirtualSyncRate(CoreRealStockOpDO stockOpDO) throws RomeException {
		//所有的处理成功标识
		boolean isSuccess=false;
		Integer t=null;		
		try {
			//业务处理1
			coreRealWarehouseStockRepository.updateVirtualSyncRate(stockOpDO);
			//业务处理2
			{
				
				System.out.println();
				//以下为模拟出错
//				if(t==null) {
//					throw new RomeException("模拟出错测试","模拟出错测试");
//				}
				
			}
			isSuccess=true;
		} catch (Exception e) {
			log.error(e.getMessage(), e);
			throw e;
		}finally {
			//模型成功，业务等处理失败，需要回滚
			if(isSuccess==false) {
				RedisRollBackFacade.redisRollBack(stockOpDO);
			}
		}
		return stockOpDO;		
	}

	/**
	 * 调用库存模型多个接口推荐方法 
	 * 示例为减库存和加库存等
	 * @throws RomeException
	 */
	@Override
	public void realWarehouseManyMetchod() throws RomeException {
		//所有的处理成功标识
		boolean isSuccess=false;
		Integer t=null;	
		CoreStockOpFactoryDO stockOpFactoryDO = new CoreStockOpFactoryDO();
		try {
			// 以下为多个方法调用......
			// 此方法也适合for里调，等等
			// 增加库存操作开始
			CoreRealStockOpDO increaseDo = stockOpFactoryDO.createCoreRealStockOpDO();
			
			//单据编码
			String recordCode=UUID.randomUUID().toString();
			//库存交易类型
		    Integer transType=101;
			List<CoreRealStockOpDetailDO> detailDos=new ArrayList<>();
			increaseDo.setRecordCode(recordCode);
			increaseDo.setTransType(transType);
			increaseDo.setDetailDos(detailDos);
			
			CoreRealStockOpDetailDO detailDO=new CoreRealStockOpDetailDO();
			detailDO.setRealQty(new BigDecimal(10L));
			detailDO.setSkuId(1L);
			detailDO.setSkuCode("商品编码1");
			detailDO.setRealWarehouseId(1L);
			detailDO.setChannelCode("3");
			detailDO.setMerchantId(101L);
			detailDos.add(detailDO);
			
			coreRealWarehouseStockRepository.increaseRealQty(increaseDo);
			// 增加库存操作结束
			
			// 减少库存操作开始
			CoreRealStockOpDO decreaseDo = stockOpFactoryDO.createCoreRealStockOpDO();
			//单据编码
			recordCode=UUID.randomUUID().toString();
			//库存交易类型
		    transType=101;
			detailDos=new ArrayList<>();
			decreaseDo.setRecordCode(recordCode);
			decreaseDo.setTransType(transType);
			decreaseDo.setDetailDos(detailDos);
			
			detailDO=new CoreRealStockOpDetailDO();
			detailDO.setRealQty(new BigDecimal(2));
			detailDO.setSkuId(1L);
			detailDO.setSkuCode("商品编码1");
			detailDO.setRealWarehouseId(1L);
			detailDO.setChannelCode("3");
			detailDO.setMerchantId(101L);
			detailDos.add(detailDO);
			coreRealWarehouseStockRepository.decreaseRealQty(decreaseDo);
			// 减少库存操作结束
			
			// 循环调用示例 开始
			for(int i = 0; i < 5; i++){
				CoreRealStockOpDO stockDO = stockOpFactoryDO.createCoreRealStockOpDO();;
				//单据编码
				recordCode=UUID.randomUUID().toString();
				//库存交易类型
			    transType=101;
				detailDos=new ArrayList<>();
				stockDO.setRecordCode(recordCode);
				stockDO.setTransType(transType);
				stockDO.setDetailDos(detailDos);
				
				detailDO=new CoreRealStockOpDetailDO();
				detailDO.setOnroadQty(new BigDecimal(1L));
				detailDO.setSkuId(2L);
				detailDO.setSkuCode("商品编码2");
				detailDO.setRealWarehouseId(1L);
				detailDO.setChannelCode("3");
				detailDO.setMerchantId(101L);
				detailDos.add(detailDO);
				coreRealWarehouseStockRepository.increaseOnroadStock(stockDO);
			}
			// 循环调用示例 结束
			//业务处理1
			
			//业务处理2
			{
				
				System.out.println();
				//以下为模拟出错
//				if(t==null) {
//					throw new RomeException("模拟出错测试","模拟出错测试");
//				}
				
			}
			
			// 提交模型数据，必须紧跟isSuccess=true;语句
			stockOpFactoryDO.commit();
			isSuccess=true;
		} catch (Exception e) {
			log.error(e.getMessage(), e);
			throw e;
		}finally {
			//模型成功，业务等处理失败，需要回滚
			if(isSuccess==false) {
				// 也可用 RedisRollBackFacade.redisRollBack(stockOpFactoryDO);
				stockOpFactoryDO.redisRollBack();
//				RedisRollBackFacade.redisRollBack(stockOpFactoryDO);
			}
		}		
	}
	
	/**
	 * 对外提供服务
	 * 锁定库存，即冻解库存 最大可能冻结库存，当不够冻结时，有多少冻结多少
	 * 说明：此接口是从下向上锁定库存
	 * @param stockDO
	 * @return
	 * @throws RomeException
	 */
	@Override
    @Transactional(rollbackFor = Exception.class)
	public CoreRealStockOpDO realWarehouseMaxableLockStock(CoreRealStockOpDO stockDO) throws RomeException{
		//所有的处理成功标识
		boolean isSuccess=false;
		Integer t=null;
		try {
			//业务处理1
			coreRealWarehouseStockRepository.maxableLockStock(stockDO);
			//业务处理2
			{
				
				System.out.println();
				//以下为模拟出错
//				if(t==null) {
//					throw new RomeException("模拟出错测试","模拟出错测试");
//				}
			}
			isSuccess=true;
		} catch (Exception e) {
			log.error(e.getMessage(), e);
			throw e;
		}finally {
			//模型成功，业务等处理失败，需要回滚
			if(isSuccess==false) {
				RedisRollBackFacade.redisRollBack(stockDO);
			}
		}
		return stockDO;
	}
	
	/**
	 * 对外提供服务
	 * 锁定库存，即冻结库存  ,允许冻结超过真实库存，即允许可用为负,不检查是否有可用库存
	 * 说明：此接口是从下向上锁定库存
	 * @param stockDO
	 * @return
	 * @throws RomeException
	 */
	@Override
    @Transactional(rollbackFor = Exception.class)
	public CoreRealStockOpDO realWarehouseAllowAvailNegativeLockStock(CoreRealStockOpDO stockDO) throws RomeException{
		//所有的处理成功标识
		boolean isSuccess=false;
		Integer t=null;
		try {
			//业务处理1
			coreRealWarehouseStockRepository.allowAvailNegativeLockStock(stockDO);
			//业务处理2
			{
				
				System.out.println();
				//以下为模拟出错
//				if(t==null) {
//					throw new RomeException("模拟出错测试","模拟出错测试");
//				}
			}
			isSuccess=true;
		} catch (Exception e) {
			log.error(e.getMessage(), e);
			throw e;
		}finally {
			//模型成功，业务等处理失败，需要回滚
			if(isSuccess==false) {
				RedisRollBackFacade.redisRollBack(stockDO);
			}
		}
		return stockDO;
	}
	
	/**
	 * 渠道
	 * 锁定库存，即冻结库存  ,允许冻结超过真实库存，即允许可用为负,不检查是否有可用库存
	 * 说明：此接口是从上向下锁定库存,普通锁定库存,调用前已寻源成功。
	 * @param channelOrderDO
	 * @return
	 * @throws RomeException
	 */
	@Override
    @Transactional(rollbackFor = Exception.class)
	public CoreChannelOrderDO channelAllowAvailNegativeLockStock(CoreChannelOrderDO channelOrderDO) throws RomeException{
		//所有的处理成功标识
		boolean isSuccess=false;
		Integer t=null;		
		try {
			//业务处理1
			coreChannelSalesRepository.allowAvailNegativeLockStock(channelOrderDO);
			//业务处理2
			{
				
				System.out.println();
				//以下为模拟出错
//				if(t==null) {
//					throw new RomeException("模拟出错测试","模拟出错测试");
//				}
				
			}
			isSuccess=true;
		} catch (Exception e) {
			log.error(e.getMessage(), e);
			throw e;
		}finally {
			//模型成功，业务等处理失败，需要回滚
			if(isSuccess==false) {
				RedisRollBackFacade.redisRollBack(channelOrderDO);
			}
		}
		return channelOrderDO;
	}
	
	/**
	 * 渠道
	 * 锁定库存，即冻结库存  ,允许冻结超过真实库存，即允许可用为负,不检查是否有可用库存
	 * 说明：此接口是从上向下锁定库存,普通锁定库存,调用前已寻源成功。
	 * @param channelOrderDO
	 * @return
	 * @throws RomeException
	 */
	@Override
    @Transactional(rollbackFor = Exception.class)
	public CoreChannelOrderDO channelAllowAvailNegativeLockStockAutoAdd(CoreChannelOrderDO channelOrderDO) throws RomeException{
		//所有的处理成功标识
		boolean isSuccess=false;
		Integer t=null;		
		try {
			//业务处理1
			coreChannelSalesRepository.allowAvailNegativeLockStockAutoAdd(channelOrderDO);
			//业务处理2
			{
				
				System.out.println();
				//以下为模拟出错
//				if(t==null) {
//					throw new RomeException("模拟出错测试","模拟出错测试");
//				}
				
			}
			isSuccess=true;
		} catch (Exception e) {
			log.error(e.getMessage(), e);
			throw e;
		}finally {
			//模型成功，业务等处理失败，需要回滚
			if(isSuccess==false) {
				RedisRollBackFacade.redisRollBack(channelOrderDO);
			}
		}
		return channelOrderDO;
	}
	
	/**
	 * 渠道
	 * 锁定库存，即冻解库存 ,最大可能冻结库存，当不够冻结时，有多少冻结多少  实际冻结数量传回字段lockQty
	 * 说明：此接口是从上向下锁定库存,调用前已寻源成功。
	 * @param channelOrderDO
	 * @param stockOpFactoryDO 
	 * @return
	 * @throws RomeException
	 */
	@Override
    @Transactional(rollbackFor = Exception.class)
	public CoreChannelOrderDO channelMaxableLockStock(CoreChannelOrderDO channelOrderDO, CoreStockOpFactoryDO stockOpFactoryDO) throws RomeException{
		//所有的处理成功标识
		boolean isSuccess=false;
		Integer t=null;		
		try {
			//业务处理1
			coreChannelSalesRepository.maxableLockStock(channelOrderDO);
			//业务处理2
			{
				
				System.out.println();
				//以下为模拟出错
//				if(t==null) {
//					throw new RomeException("模拟出错测试","模拟出错测试");
//				}
				
			}
			// 提交模型数据，必须紧跟isSuccess=true;语句
			stockOpFactoryDO.commit();
			isSuccess=true;
		} catch (Exception e) {
			log.error(e.getMessage(), e);
			throw e;
		}finally {
			//模型成功，业务等处理失败，需要回滚
			if(isSuccess==false) {
				// 也可用 RedisRollBackFacade.redisRollBack(stockOpFactoryDO);
				stockOpFactoryDO.redisRollBack();
//				RedisRollBackFacade.redisRollBack(channelOrderDO);
			}
		}
		return channelOrderDO;
	}
	
	/**
	 * 渠道
	 * 锁定库存，即冻解库存 ,组够锁定库存
	 * 说明：此接口是从上向下锁定库存,策略组满足数量情况下可以正常锁定,即组下的所有虚仓可用库存总和够的话可以锁定,调用前已寻源成功
	 * @param channelOrderDO
	 * @param stockOpFactoryDO 
	 * @return
	 * @throws RomeException
	 */
	@Override
    @Transactional(rollbackFor = Exception.class)
	public CoreChannelOrderDO channelLockStockGroup(CoreChannelOrderDO channelOrderDO, CoreStockOpFactoryDO stockOpFactoryDO) throws RomeException{
		//所有的处理成功标识
		boolean isSuccess=false;
		Integer t=null;		
		try {
			//业务处理1
			coreChannelSalesRepository.lockStockGroup(channelOrderDO);
			//业务处理2
			{
				
				System.out.println();
				//以下为模拟出错
//				if(t==null) {
//					throw new RomeException("模拟出错测试","模拟出错测试");
//				}
				
			}
			// 提交模型数据，必须紧跟isSuccess=true;语句
			stockOpFactoryDO.commit();
			isSuccess=true;
		} catch (Exception e) {
			log.error(e.getMessage(), e);
			throw e;
		}finally {
			//模型成功，业务等处理失败，需要回滚
			if(isSuccess==false) {
				// 也可用 RedisRollBackFacade.redisRollBack(stockOpFactoryDO);
				stockOpFactoryDO.redisRollBack();
//				RedisRollBackFacade.redisRollBack(channelOrderDO);
			}
		}
		return channelOrderDO;
	}
	
}
