package com.rome.stock.innerservice.domain.entity;

import com.rome.stock.innerservice.domain.entity.BaseE;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;

@Component
@Scope("prototype")
@Data
@EqualsAndHashCode(callSuper = false)
public class RealWarehouseStockE extends BaseE {
	/**
	 * 唯一主键
	 */
	private Long id;

	/**
	 * 实仓ID
	 */
	private Long realWarehouseId;

	/**
	 * sku编码
	 */
	private Long skuId;

	/**
	 * 商品编码
	 */
	private String skuCode;

	/**
	 * 真实库存
	 */
	private BigDecimal realQty;

	/**
	 * 可用库存
	 */
	private BigDecimal availableQty;


	/**
	 * 锁定库存
	 */
	private BigDecimal lockQty;

	/**
	 * 在途库存
	 */
	private BigDecimal onroadQty;

	/**
	 * 质检库存
	 */
	private BigDecimal qualityQty;

	/**
	 * 不合格库存，注：一般是质检不合格库存
	 */
	private BigDecimal unqualifiedQty;

	/**
	 * 商家id
	 */
	private Long merchantId;
}



