package com.rome.stock.innerservice.domain.service.impl;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.function.Function;
import java.util.stream.Collectors;

import javax.annotation.Resource;

import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.time.DateUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.alibaba.fastjson.JSON;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.rome.arch.core.clientobject.Response;
import com.rome.arch.core.domain.EntityFactory;
import com.rome.arch.core.exception.RomeException;
import com.rome.stock.common.constants.front.ShopReturnConsts;
import com.rome.stock.common.constants.warehouse.WarehouseRecordConstant;
import com.rome.stock.common.utils.DateUtil;
import com.rome.stock.common.utils.StockStringUtils;
import com.rome.stock.core.constant.StockCoreConsts;
import com.rome.stock.core.domain.repository.CoreRealWarehouseStockRepository;
import com.rome.stock.core.facade.RedisRollBackFacade;
import com.rome.stock.core.infrastructure.dataobject.core.CoreRealStockOpDO;
import com.rome.stock.core.infrastructure.dataobject.core.CoreRealStockOpDetailDO;
import com.rome.stock.innerservice.api.dto.WarehouseRecordDTO;
import com.rome.stock.innerservice.api.dto.WarehouseRecordPageDTO;
import com.rome.stock.innerservice.api.dto.cmp7.Cmp7ReturnDTO;
import com.rome.stock.innerservice.api.dto.cmp7.ShopReturnCmp7DTO;
import com.rome.stock.innerservice.api.dto.cmp7.ShopReturnDetailCmp7DTO;
import com.rome.stock.innerservice.api.dto.cmp7.ShopReturnResultDTO;
import com.rome.stock.innerservice.api.dto.frontrecord.DispatchNoticeDTO;
import com.rome.stock.innerservice.api.dto.frontrecord.ShopReturnDetailDTO;
import com.rome.stock.innerservice.api.dto.frontrecord.ShopReturnRecordDTO;
import com.rome.stock.innerservice.api.dto.warehouserecord.CancelRecordDTO;
import com.rome.stock.innerservice.api.dto.warehouserecord.ConfirmShopReturnDTO;
import com.rome.stock.innerservice.api.dto.warehouserecord.InWarehouseRecordDTO;
import com.rome.stock.innerservice.api.dto.warehouserecord.OutWarehouseRecordDTO;
import com.rome.stock.innerservice.common.AlikAssert;
import com.rome.stock.innerservice.common.KibanaLogUtils;
import com.rome.stock.innerservice.common.ResCode;
import com.rome.stock.innerservice.common.RomeCollectionUtil;
import com.rome.stock.innerservice.common.SignUtil;
import com.rome.stock.innerservice.constant.FrontRecordStatusVO;
import com.rome.stock.innerservice.constant.FrontRecordTypeVO;
import com.rome.stock.innerservice.constant.ShopReturnTransactionStatus;
import com.rome.stock.innerservice.constant.StockRecordInfoTypeVo;
import com.rome.stock.innerservice.constant.TaxCodeConsts;
import com.rome.stock.innerservice.constant.WarehouseRecordBusinessTypeVO;
import com.rome.stock.innerservice.constant.WarehouseRecordStatusVO;
import com.rome.stock.innerservice.constant.WmsSyncStatusVO;
import com.rome.stock.innerservice.domain.convertor.frontrecord.ShopReturnConvertor;
import com.rome.stock.innerservice.domain.entity.DisparityDetailE;
import com.rome.stock.innerservice.domain.entity.DisparityRecordE;
import com.rome.stock.innerservice.domain.entity.RealWarehouseE;
import com.rome.stock.innerservice.domain.entity.frontrecord.ShopReturnRecordDetailE;
import com.rome.stock.innerservice.domain.entity.frontrecord.ShopReturnRecordE;
import com.rome.stock.innerservice.domain.entity.warehouserecord.ShopReturnWarehouseRecordE;
import com.rome.stock.innerservice.domain.entity.warehouserecord.WarehouseRecordDetail;
import com.rome.stock.innerservice.domain.entity.warehouserecord.WarehouseRecordE;
import com.rome.stock.innerservice.domain.repository.RealWarehouseRepository;
import com.rome.stock.innerservice.domain.repository.SapInterfaceLogRepository;
import com.rome.stock.innerservice.domain.repository.frontrecord.FrShopReturnRepository;
import com.rome.stock.innerservice.domain.repository.warehouserecord.ShopReturnWarehouseRepository;
import com.rome.stock.innerservice.domain.repository.warehouserecord.WarehouseRecordRepository;
import com.rome.stock.innerservice.domain.repository.warehouserecord.WrAdditionInfoRepository;
import com.rome.stock.innerservice.domain.service.DispatchNoticeService;
import com.rome.stock.innerservice.domain.service.RealWarehouseService;
import com.rome.stock.innerservice.domain.service.ShopReturnService;
import com.rome.stock.innerservice.domain.service.WarehouseRecordService;
import com.rome.stock.innerservice.domain.service.WmsOutService;
import com.rome.stock.innerservice.infrastructure.dataobject.FrontWarehouseRecordRelationDO;
import com.rome.stock.innerservice.infrastructure.dataobject.WrAdditionInfoDo;
import com.rome.stock.innerservice.remote.base.dto.ChannelDTO;
import com.rome.stock.innerservice.remote.base.dto.StoreDTO;
import com.rome.stock.innerservice.remote.base.dto.UpdateReversePoDTO;
import com.rome.stock.innerservice.remote.base.dto.UpdateReversePoLineDTO;
import com.rome.stock.innerservice.remote.base.facade.ChannelFacade;
import com.rome.stock.innerservice.remote.base.facade.ShopFacade;
import com.rome.stock.innerservice.remote.base.facade.TransactionFacade;
import com.rome.stock.innerservice.remote.cmp.CmpRemoteService;
import com.rome.stock.innerservice.remote.cmp.dto.JoinReturnRecordDTO;
import com.rome.stock.innerservice.remote.cmp.facade.CmpFacade;
import com.rome.stock.innerservice.remote.cmp7.Cmp7Facade;
import com.rome.stock.innerservice.remote.item.dto.ChannelSkuPurchasePriceDTO;
import com.rome.stock.innerservice.remote.item.dto.QuerySalePurchasePricesExtDTO;
import com.rome.stock.innerservice.remote.item.dto.SkuInfoExtDTO;
import com.rome.stock.innerservice.remote.item.dto.SkuSalePurchasePrice;
import com.rome.stock.innerservice.remote.item.facade.SkuFacade;
import com.rome.stock.innerservice.remote.orderCneter.dto.CommonFrontRecordDTO;
import com.rome.stock.innerservice.remote.orderCneter.dto.CommonFrontRecordDetailDTO;
import com.rome.stock.innerservice.remote.orderCneter.facade.OrderCenterFacade;
import com.rome.stock.innerservice.remote.trade.facade.TradePriceFacade;

import cn.hutool.crypto.asymmetric.Sign;
import lombok.extern.slf4j.Slf4j;

/**
 * 类ShopReturnServiceImpl的实现描述：门店退货service
 *
 * <AUTHOR> 2019/4/29 11:28
 */
@Slf4j
@Service
public class ShopReturnServiceImpl implements ShopReturnService {

    @Resource
    private RealWarehouseService realWarehouseService;
    @Resource
    private FrShopReturnRepository frShopReturnRepository;
    @Resource
    private ShopReturnConvertor shopReturnConvertor;
    @Resource
    private EntityFactory entityFactory;
    @Autowired
    private DispatchNoticeService dispatchNoticeService;
    @Resource
    private WarehouseRecordRepository warehouseRecordRepository;
    @Resource
    private ShopReturnWarehouseRepository shopReturnWarehouseRepository;
    @Autowired
    private CoreRealWarehouseStockRepository coreRealWarehouseStockRepository;
    @Resource
    private TransactionFacade transactionFacade;
    @Resource
    private RealWarehouseRepository realWarehouseRepository;
    @Resource
    private SkuFacade skuFacade;
    @Resource
    private OrderCenterFacade orderCenterFacade;
    @Resource
    private ShopFacade shopFacade;
    @Resource
    private CmpFacade cmpFacade;
    @Resource
    private WarehouseRecordService warehouseRecordService;
    @Resource
    private WrAdditionInfoRepository wrAdditionInfoRepository;
    @Resource
    private WmsOutService wmsOutService;
    @Resource
    private ChannelFacade channelFacade;
    @Resource
    private TradePriceFacade tradePriceFacade;
    @Resource
    private SapInterfaceLogRepository sapInterfaceLogRepository;
    @Resource
    private Cmp7Facade cmp7Facade;
    @Resource
    private CmpRemoteService cmpRemoteService;

    private static final int PRICE_POINT_NUM = 2;

    public static final int CMP_SKU_NAME_LENGTH = 38;


    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean dispatchNotify(DispatchNoticeDTO dispatchNoticeDTO, FrontWarehouseRecordRelationDO relationDO) {
        int i = frShopReturnRepository.updateRecordToDispatching(relationDO.getFrontRecordId());
        AlikAssert.isTrue(i == 1, ResCode.STOCK_ERROR_1013, ResCode.STOCK_ERROR_1013_DESC);
        //加盟在派车回调后,自动操作货已提走
        if(FrontRecordTypeVO.JOIN_SHOP_RETURN_GOODS_RECORD.getType().equals(relationDO.getFrontRecordType())){
            ConfirmShopReturnDTO dto = new ConfirmShopReturnDTO();
            dto.setType(2);
            this.confirmShopReturn(dto);
        }
        return true;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void addShopReturn(OutWarehouseRecordDTO outWarehouseRecordDTO) {
        //幂等性判断
        WarehouseRecordE recordE = warehouseRecordRepository.queryWarehouseRecordByRecordCode(outWarehouseRecordDTO.getRecordCode());
        if(Objects.nonNull(recordE)){
            return;
        }
        //查询出库仓库
        RealWarehouseE realWarehouseE = realWarehouseRepository.getRealWarehouseByOutCodeAndFactoryCode(outWarehouseRecordDTO.getWarehouseCode(), outWarehouseRecordDTO.getFactoryCode());
        AlikAssert.isNotNull(realWarehouseE,ResCode.STOCK_ERROR_1002_DESC,"实仓不存在");
        ShopReturnWarehouseRecordE outRecordE = entityFactory.createEntity(ShopReturnWarehouseRecordE.class);
        //直营退货/加盟退货/加盟主配退货
        outRecordE.shopOutRecord(realWarehouseE,outWarehouseRecordDTO);
        //输出kibana日志信息
        KibanaLogUtils.printKibanaRecordInfo(outWarehouseRecordDTO.getRecordCode(),"",StockRecordInfoTypeVo.SHOP_RETURN_CREATE.getType(),StockRecordInfoTypeVo.SHOP_RETURN_CREATE.getDesc(),"addShopReturn",outWarehouseRecordDTO);

    }



    @Override
    @Transactional(rollbackFor = Exception.class)
    public void confirmShopReturn(ConfirmShopReturnDTO dto) {
        WarehouseRecordE outRecordE = warehouseRecordRepository.queryWarehouseRecordByRecordCode(dto.getRecordCode());
        //1.校验退货单是否存在(对应门店的出库单)
        AlikAssert.isTrue(outRecordE != null, ResCode.STOCK_ERROR_1017, ResCode.STOCK_ERROR_1017_DESC);
        //幂等判断
        if(WarehouseRecordStatusVO.OUT_ALLOCATION.getStatus().equals(outRecordE.getRecordStatus())){
            return;
        }
        List<WarehouseRecordDetail> detailList = warehouseRecordRepository.queryDetailListByRecordId(outRecordE.getId());
        outRecordE.setWarehouseRecordDetails(detailList);
        //加盟不存在在称上操作确认货已提走----前置单查询判断的
//        if(dto.getType() == 1 && WarehouseRecordTypeVO.LS_RETURN_OUT_WAREHOUSE_RECORD.getType().equals(outRecordE.getRecordType())){
//            return;
//        }
        //更新出库单为待过账
        //1030确认退货全部走老接口过账
//        if (WarehouseRecordTypeVO.LS_RETURN_OUT_WAREHOUSE_RECORD.getType().equals(outRecordE.getRecordType())
//                || WarehouseRecordTypeVO.LS_RETURN_COLD_OUT_WAREHOUSE_RECORD.getType().equals(outRecordE.getRecordType())) {
            //930只有加盟退货入库 以及 加盟冷链退货入库 需要单独过账 其他的走入库库过账
            int i = warehouseRecordRepository.updateToWaitTransfer(outRecordE.getId());
            AlikAssert.isTrue(i > 0, ResCode.STOCK_ERROR_1066, ResCode.STOCK_ERROR_1066_DESC);
//        }
        //判断是否是pos7过来的
        if(Objects.equals(1,dto.getSourceType())){
            int res = warehouseRecordRepository.updateCmp7Status(outRecordE.getId());
            AlikAssert.isTrue(res > 0, ResCode.STOCK_ERROR_1066, "更新pos7标识失败:recordCode="+dto.getRecordCode());
        }
        //更新用户编码
        if(StringUtils.isNotEmpty(dto.getUserCode())){
            int result = warehouseRecordRepository.updateUserCode(dto.getUserCode(), outRecordE.getId());
            AlikAssert.isTrue(result>0,ResCode.STOCK_ERROR_1002,"更新userCode失败 recordCode="+dto.getRecordCode());
        }

        //修改门店出库单批次库存为待扣减状态
        shopReturnWarehouseRepository.updateRecordBatchStatusToInit(outRecordE.getRecordCode());
        //出库确认后，更新实际出库数量，更改出库状态
        int j=outRecordE.updateOutAllocationInfo(DateUtil.now());
        if(j==0){
            throw  new RomeException(ResCode.STOCK_ERROR_1002_DESC, "更新出库状态失败");
        }
  		//更新实际数量为计划数量
        warehouseRecordRepository.updateActualQtyByPlanQty(outRecordE.getRecordCode());

        //MM仓操作或已提走，添加后置单附加信息
        this.saveMMWarehouseInfo(dto);

        boolean isSuccess = false;
        CoreRealStockOpDO coreRealStockOpDO = null;
        try {
            //门店库存减少
            coreRealStockOpDO = this.initOutWarehouseStockObj(outRecordE);
            coreRealWarehouseStockRepository.decreaseRealQty(coreRealStockOpDO);
            isSuccess = true;
        }catch (RomeException e){
            log.error(e.getMessage(), e);
            throw new RomeException(e.getCode(),e.getMessage());
        }catch (Exception e){
            log.error(e.getMessage(), e);
            throw new RomeException(ResCode.STOCK_ERROR_1003,ResCode.STOCK_ERROR_1003_DESC);
        }finally {
            if(!isSuccess) {
                RedisRollBackFacade.redisRollBack(coreRealStockOpDO);
            }
        }
    }


    /**
     * MM仓操作或已提走，添加后置单附加信息
     * @param confirmShopReturnDTO
     */
    private void saveMMWarehouseInfo(ConfirmShopReturnDTO confirmShopReturnDTO){
        //4个字段有一个有值就要写表  如果全是空就不写
        if(StringUtils.isEmpty(confirmShopReturnDTO.getRemark()) && StringUtils.isEmpty(confirmShopReturnDTO.getRemark1())
            && null==confirmShopReturnDTO.getColdChainNum() && null==confirmShopReturnDTO.getOtherContainer()){
            return;
        }
        //MM仓确认货已取走添加sc_wr_addition_info
        WrAdditionInfoDo wrAdditionInfoDo=new WrAdditionInfoDo();
        wrAdditionInfoDo.setColdChainNum(confirmShopReturnDTO.getColdChainNum());
        wrAdditionInfoDo.setRecordCode(confirmShopReturnDTO.getRecordCode());
        wrAdditionInfoDo.setOtherContainer(confirmShopReturnDTO.getOtherContainer());
        wrAdditionInfoDo.setRemark(confirmShopReturnDTO.getRemark());
        wrAdditionInfoDo.setRemark1(confirmShopReturnDTO.getRemark1());
        int j=wrAdditionInfoRepository.saveWrAdditionInfo(wrAdditionInfoDo);
        if(j==0){
            throw  new RomeException(ResCode.STOCK_ERROR_1002_DESC, "后置单附加信息新增异常");
        }
    }


    @Override
    @Transactional(rollbackFor = Exception.class)
    public void shopReturnInCreateRecord(InWarehouseRecordDTO inWarehouseRecordDTO) {
        WarehouseRecordE warehouseRecordE = warehouseRecordRepository.queryWarehouseRecordByRecordCode(inWarehouseRecordDTO.getRecordCode());
        //1.校验退货单是否存在(对应门店的出库单)
        //幂等判断
        if(null != warehouseRecordE){
            return;
        }
        //查询出库仓库
        RealWarehouseE realWarehouseE = realWarehouseRepository.getRealWarehouseByOutCodeAndFactoryCode(inWarehouseRecordDTO.getWarehouseCode(), inWarehouseRecordDTO.getFactoryCode());
        AlikAssert.isNotNull(realWarehouseE,ResCode.STOCK_ERROR_1002_DESC,"实仓不存在");
        //创建领域对象
        ShopReturnWarehouseRecordE inRecordE = entityFactory.createEntity(ShopReturnWarehouseRecordE.class);
        //设置大仓入库单派车信息
        //直营门店退货、加盟门店退货
        inRecordE.warehouseInRecord(realWarehouseE,inWarehouseRecordDTO);
        boolean isSuccess = false;
        CoreRealStockOpDO coreOnroadRealStockOpDO = null;
        try {
            //增加大仓在途库存
            coreOnroadRealStockOpDO = inRecordE.initOnRoadStockObj(realWarehouseE.getId());
            coreRealWarehouseStockRepository.increaseOnroadStock(coreOnroadRealStockOpDO);
            isSuccess = true;
        }catch (RomeException e){
            log.error(e.getMessage(), e);
            throw new RomeException(e.getCode(),e.getMessage());
        }catch (Exception e){
            log.error(e.getMessage(), e);
            throw new RomeException(ResCode.STOCK_ERROR_1003,ResCode.STOCK_ERROR_1003_DESC);
        }finally {
            if(!isSuccess) {
                RedisRollBackFacade.redisRollBack(coreOnroadRealStockOpDO);
            }
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean warehouseInNotify(Long warehouseRecordId,WarehouseRecordDTO warehouseRecord) {
        //查询单据及明细
        WarehouseRecordE recordE = warehouseRecordRepository.getRecordWithDetailById(warehouseRecordId);
        AlikAssert.isNotNull(recordE, ResCode.STOCK_ERROR_1017, ResCode.STOCK_ERROR_1017_DESC);
        //保存发货单
        warehouseRecordService.saveReceipt(recordE,warehouseRecord, WarehouseRecordConstant.INIT_SYNC_TRADE);
        //更新cmp下发状态
        int j=this.initCmpStatus(recordE.getRecordCode());
        if(j==1){
            warehouseRecordRepository.updateCmpStatusNeedPush(recordE.getId());
        }
        CoreRealStockOpDO coreRealStockOpDO = null;
        CoreRealStockOpDO coreOnroadRealStockOpDO = null;
        boolean isSuccess = false;
        try {
            //减少在途
            coreOnroadRealStockOpDO = recordE.initOnRoadStockObj(recordE.getRealWarehouseId());
            coreRealWarehouseStockRepository.decreaseOnroadStock(coreOnroadRealStockOpDO);
            //增加大仓库存(按照真实库存)
            coreRealStockOpDO = this.initStockObj(recordE);
            coreRealWarehouseStockRepository.increaseRealQty(coreRealStockOpDO);
            isSuccess = true;
        }catch (RomeException e){
            log.error(e.getMessage(), e);
            throw new RomeException(e.getCode(),e.getMessage());
        }catch (Exception e){
            log.error(e.getMessage(), e);
            throw new RomeException(ResCode.STOCK_ERROR_1003,ResCode.STOCK_ERROR_1003_DESC);
        }finally {
            if(!isSuccess) {
                RedisRollBackFacade.redisRollBack(coreRealStockOpDO);
            }
            if(!isSuccess) {
                RedisRollBackFacade.redisRollBack(coreOnroadRealStockOpDO);
            }
        }
        return true;
    }


    /**
     * 初始化CmpStatus
     * @param recordCode
     */
    private Integer initCmpStatus(String recordCode){
        List<CommonFrontRecordDTO> res=orderCenterFacade.queryCommonFrontRecordInfoByRecordCode(recordCode);
        if(CollectionUtils.isEmpty(res)){
            throw new RomeException(ResCode.STOCK_ERROR_1002,"查询订单中心前置单不存在");
        }
        CommonFrontRecordDTO commonFrontRecordDTO=res.get(0);
        if(commonFrontRecordDTO.getShopType() == 3){
            //单据来源于CMP则需要推送到CMP,加盟门店
            return WarehouseRecordConstant.NEED_CMP;
        }else{
            //单据来源于交易则不需要推送到CMP
            return WarehouseRecordConstant.INIT_CMP;
        }
    }



    @Override
    public ShopReturnRecordE getById(Long id) {
        return frShopReturnRepository.queryById(id);
    }

    /**
     * 计算并设置实收,并更新数据库
     * @param frontRecordE
     * @param recordE
     */
    private Integer calculateDetail(ShopReturnRecordE frontRecordE, WarehouseRecordE recordE) {
        List<ShopReturnRecordDetailE> frontDetailList = frontRecordE.getFrontRecordDetails();
        //得到换算比例
        frontRecordE.convertRealToBasic(frontDetailList);
        List<WarehouseRecordDetail> whDetailList = recordE.getWarehouseRecordDetails();
        Map<String, WarehouseRecordDetail> map = RomeCollectionUtil.listforMap(whDetailList, "lineNo", null);
        int isDiff = ShopReturnConsts.NOT_DIFF_IN;
        //差异单管理
        List<DisparityDetailE> disparityDetailEList = new ArrayList<>();
        for (ShopReturnRecordDetailE frontDetail :frontDetailList){
            AlikAssert.isNotNull(frontDetail.getLineNo(), ResCode.STOCK_ERROR_1003, ResCode.STOCK_ERROR_1003_DESC
                    +"：门店退货入库回调，行号为空" + frontRecordE.getRecordCode() + "--" + frontDetail.getSkuCode());
            WarehouseRecordDetail whDetail = map.get(frontDetail.getLineNo());
            if (whDetail != null) {
                if (frontDetail.getScale() != null) {
                    //计算实际收货数量【用基本库存单位换算为采购单位】
                    BigDecimal actualQty = whDetail.getActualQty().divide(frontDetail.getScale(),StockCoreConsts.DECIMAL_POINT_NUM, BigDecimal.ROUND_DOWN);
                    frontDetail.setRealSkuQty(actualQty);
                    // 应收与实收不一致,换算后因为有精度原因导致equal结果非预期
                    if (!frontDetail.getSkuQty().equals(actualQty)) {
                        isDiff = ShopReturnConsts.DIFF_IN;
                        //添加差异单，只有直营门店退货才会记录退货差异单
                        if (FrontRecordTypeVO.DIRECT_SHOP_RETURN_GOODS_RECORD.getType().equals(frontRecordE.getRecordType())) {
                            DisparityDetailE disparityDetailE = entityFactory.createEntity(DisparityDetailE.class);
                            disparityDetailE.setLineNo(frontDetail.getLineNo());
                            disparityDetailE.setFrontRecordCode(frontRecordE.getRecordCode());
                            disparityDetailE.setFrontRecordId(frontRecordE.getId());
                            disparityDetailE.setDeliveryLineNo(whDetail.getDeliveryLineNo());
                            disparityDetailE.setSkuId(frontDetail.getSkuId());
                            disparityDetailE.setSapPoNo(whDetail.getSapPoNo());
                            disparityDetailE.setSkuCode(frontDetail.getSkuCode());
                            disparityDetailE.setUnit(frontDetail.getBasicUnit());
                            disparityDetailE.setUnitCode(frontDetail.getBasicUnitCode());
                            disparityDetailE.setOutSkuQty(frontDetail.getBasicSkuQty());
                            disparityDetailE.setInSkuQty(whDetail.getActualQty());
                            disparityDetailE.setCreator(0L);
                            disparityDetailE.setScale(frontDetail.getScale());
                            disparityDetailE.setRealUnit(frontDetail.getUnit());
                            disparityDetailE.setRealUnitCode(frontDetail.getUnitCode());
                            disparityDetailE.setSkuQty(frontDetail.getBasicSkuQty().subtract(whDetail.getActualQty()));
                            disparityDetailEList.add(disparityDetailE);
                        }
                    }
                }
            } else {
                throw new RomeException(ResCode.STOCK_ERROR_1003, ResCode.STOCK_ERROR_1003_DESC + "：门店退货入库回调,入库单明细为空，行号" + frontDetail.getLineNo());
            }
//            else {
//                isDiff = ShopReturnConsts.DIFF_IN;
//                // 添加差异单
//                //添加差异单，只有直营门店退货才会记录退货差异单
//                if (FrontRecordTypeVO.DIRECT_SHOP_RETURN_GOODS_RECORD.getType().equals(frontRecordE.getRecordType())) {
//
//
//
//                    DisparityDetailE disparityDetailE = entityFactory.createEntity(DisparityDetailE.class);
//                    disparityDetailE.setLineNo(frontDetail.getLineNo());
//                    disparityDetailE.setDeliveryLineNo();
//                    disparityDetailE.setSkuId(frontDetail.getSkuId());
//                    disparityDetailE.setSkuCode(frontDetail.getSkuCode());
//                    disparityDetailE.setUnit(frontDetail.getBasicUnit());
//                    disparityDetailE.setUnitCode(frontDetail.getBasicUnitCode());
//                    disparityDetailE.setOutSkuQty(frontDetail.getBasicSkuQty());
//                    disparityDetailE.setInSkuQty(BigDecimal.ZERO);
//                    disparityDetailE.setSkuQty(frontDetail.getBasicSkuQty());
//                    disparityDetailEList.add(disparityDetailE);
//                }
//            }
        }
        //更新前置单明细实收
        frShopReturnRepository.updateReturnDetailQtyList(frontDetailList);
        if (! disparityDetailEList.isEmpty()) {
            List<Long> warehouseIds = warehouseRecordRepository.queryWarehouseIdByFrontId(frontRecordE.getId(), frontRecordE.getRecordType());
            AlikAssert.isTrue(warehouseIds != null && warehouseIds.size() > 0, ResCode.STOCK_ERROR_1017, ResCode.STOCK_ERROR_1017_DESC);
            List<WarehouseRecordE> warehouseRecordEList = warehouseRecordRepository.queryWarehouseRecordByIds(warehouseIds);
            WarehouseRecordE outWarehouseRecordE = null;
            for (WarehouseRecordE entity : warehouseRecordEList) {
                if (WarehouseRecordBusinessTypeVO.OUT_WAREHOUSE_RECORD.getType().equals(entity.getBusinessType())) {
                    outWarehouseRecordE = entity;
                    break;
                }
            }
            AlikAssert.isNotNull(outWarehouseRecordE, ResCode.STOCK_ERROR_1017, ResCode.STOCK_ERROR_1017_DESC);
            // 生成差异单
            DisparityRecordE disparityRecordE = entityFactory.createEntity(DisparityRecordE.class);
            disparityRecordE.setSapPoNo(frontRecordE.getSapReverseNo());
            disparityRecordE.setSapDeliveryCode(recordE.getSapOrderCode());
            disparityRecordE.setFrontRecordId(frontRecordE.getId());
            disparityRecordE.setFrontRecordCode(frontRecordE.getRecordCode());
            disparityRecordE.setInWarehouseRecordId(recordE.getId());
            disparityRecordE.setInWarehouseRecordCode(recordE.getRecordCode());
            disparityRecordE.setOutWarehouseRecordId(outWarehouseRecordE.getId());
            disparityRecordE.setOutWarehouseRecordCode(outWarehouseRecordE.getRecordCode());
            disparityRecordE.setRecordType(FrontRecordTypeVO.DIRECT_SHOP_RETURN_DISPARITY_RECORD.getType());
            disparityRecordE.setInRealWarehouseId(frontRecordE.getInRealWarehouseId());
            disparityRecordE.setOutRealWarehouseId(frontRecordE.getOutRealWarehouseId());
            disparityRecordE.setRecordStatus(FrontRecordStatusVO.INIT.getStatus());
            Long disparityId = disparityRecordE.addRecord(FrontRecordTypeVO.DIRECT_SHOP_RETURN_DISPARITY_RECORD);
            disparityDetailEList.forEach(detailE -> detailE.setDisparityId(disparityId));
            disparityRecordE.addDetails(disparityDetailEList);
        }
        return isDiff;
    }

    /**
     * 推送交易中心退货信息-单条
     * @param shopReturnRecordDTO
     */
    @Override
    public void pushDataTOTransaction(ShopReturnRecordDTO shopReturnRecordDTO) {
        ShopReturnRecordE returnRecordE = shopReturnConvertor.dtoToEntity(shopReturnRecordDTO);
        //1.根据前置单查询出入库单关系
        List<Long> longs = warehouseRecordRepository.queryWarehouseIdByFrontId(returnRecordE.getId(), returnRecordE.getRecordType());
        List<WarehouseRecordE> warehouseRecordES = warehouseRecordRepository.queryWarehouseRecordByIds(longs);

        UpdateReversePoDTO statusAndQuantityDTO = null;

        for (WarehouseRecordE warehouseRecordE : warehouseRecordES) {
            //如果是入库单(退货入库),封装参数
            if(WarehouseRecordBusinessTypeVO.IN_WAREHOUSE_RECORD.getType().equals(warehouseRecordE.getBusinessType())){
                statusAndQuantityDTO = new UpdateReversePoDTO();

                List<ShopReturnRecordDetailE> frDetails = frShopReturnRepository.queryDetailByOrderId(returnRecordE.getId());
                List<UpdateReversePoLineDTO> list = new ArrayList<>(frDetails.size());

                //发货单号 == 前置单外部单号
                statusAndQuantityDTO.setDoNo(warehouseRecordE.getRecordCode());
                for (ShopReturnRecordDetailE frDetail : frDetails) {
                    UpdateReversePoLineDTO reversePoLineQuantityDTO = new UpdateReversePoLineDTO();
                    reversePoLineQuantityDTO.setReversePoNo(returnRecordE.getOutRecordCode());
                    reversePoLineQuantityDTO.setLineNo(frDetail.getLineNo());
                    //退货数量
                    reversePoLineQuantityDTO.setActualDeliveryQuantity(frDetail.getSkuQty());
                    //收货数量
                    reversePoLineQuantityDTO.setActualReceiveQuantity(frDetail.getRealSkuQty());
                    //skuCode
                    reversePoLineQuantityDTO.setSkuCode(frDetail.getSkuCode());
                    //skuId
                    reversePoLineQuantityDTO.setSkuId(frDetail.getSkuId());
                    //销售单位
                    reversePoLineQuantityDTO.setSaleUnitCode(frDetail.getUnitCode());

                    list.add(reversePoLineQuantityDTO);
                }
                statusAndQuantityDTO.setReversePoLineQuantityDTOs(list);
                break;
            }
        }

        if(null != statusAndQuantityDTO){
            //3.调用交易中心接口
            boolean response  = transactionFacade.pushTransactionReverseStatus(statusAndQuantityDTO);
            if (!response) {
                throw new RomeException(ResCode.STOCK_ERROR_9008, ResCode.STOCK_ERROR_9008_DESC + ":" + returnRecordE.getRecordCode());
            }
            //2.更新前置单交易中心推送状态
            frShopReturnRepository.updateTransactionStatusById(returnRecordE.getId());
        }
    }

    /**
     * job批量查询门店退货单
     * @param page
     * @param pageSize
     * @return
     */
    @Override
    public List<ShopReturnRecordDTO> queryShopReturnRecord(Integer page, Integer pageSize) {
        ShopReturnRecordDTO paramDTO = new ShopReturnRecordDTO();
        Date endTime = new Date();
        Date startTime = DateUtils.addDays(endTime, -7);
        paramDTO.setStartTime(startTime);
        paramDTO.setEndTime(endTime);

        paramDTO.setPageIndex(page);
        paramDTO.setPageSize(pageSize);
        paramDTO.setTransStatus(ShopReturnTransactionStatus.TRANSACTION_STATUS_WAIT.getStatus());

        PageHelper.startPage(paramDTO.getPageIndex(), paramDTO.getPageSize());
        List<ShopReturnRecordE> recordES = frShopReturnRepository.queryShopReturnRecord(shopReturnConvertor.dtoToEntity(paramDTO));
        List<ShopReturnRecordDTO> recordDTOS = shopReturnConvertor.entityToDto(recordES);
        PageInfo<ShopReturnRecordDTO> pageList = new PageInfo<>(recordDTOS);
        return pageList.getList();
    }


    /**
     * 根据条件查询门店退货单
     * @param paramDTO
     * @return
     */
    @Override
    public PageInfo<ShopReturnRecordDTO> findShopReturnByCondition(ShopReturnRecordDTO paramDTO) {
        Page page = PageHelper.startPage(paramDTO.getPageIndex(), paramDTO.getPageSize());
        List<ShopReturnRecordE> recordES = frShopReturnRepository.queryRecordByCondition(shopReturnConvertor.dtoToEntity(paramDTO));
        List<ShopReturnRecordDTO> recordDTOS = shopReturnConvertor.entityToDto(recordES);

        //查询仓库id
        List<Long> inWhList = RomeCollectionUtil.getValueList(recordES, "inRealWarehouseId");
        List<Long> outWhList = RomeCollectionUtil.getValueList(recordES, "outRealWarehouseId");
        inWhList.addAll(outWhList);
        Set<Long> realWarehouseIds = new HashSet<>(inWhList);
        inWhList.clear();
        inWhList.addAll(realWarehouseIds);

        //查询所有仓库信息
        List<RealWarehouseE> warehouseES = realWarehouseRepository.queryWarehouseByIds(inWhList);
        Map<Long, RealWarehouseE> warehouseMap = RomeCollectionUtil.listforMap(warehouseES, "id");

        recordDTOS.forEach(returnRecordDTO -> {
            if(null != returnRecordDTO.getInRealWarehouseId()){
                returnRecordDTO.setInRealWarehouseName(warehouseMap.get(returnRecordDTO.getInRealWarehouseId()).getRealWarehouseName());
            }
            if(null != returnRecordDTO.getOutRealWarehouseId()){
                returnRecordDTO.setOutRealWarehouseName(warehouseMap.get(returnRecordDTO.getOutRealWarehouseId()).getRealWarehouseName());
            }
        });

        PageInfo<ShopReturnRecordDTO> pageList = new PageInfo<>(recordDTOS);
        pageList.setTotal(page.getTotal());
        return pageList;
    }


    /**
     * 根据id查明细
     * @param id
     * @return
     */
    @Override
    public ShopReturnRecordDTO queryShopReturnWithDetailById(String id) {
        ShopReturnRecordE recordE = frShopReturnRepository.queryWithDetailById(Long.parseLong(id));
        //查询退入仓库信息
        RealWarehouseE warehouse = realWarehouseRepository.getRealWarehouseById(recordE.getInRealWarehouseId());
        ShopReturnRecordDTO dto = shopReturnConvertor.entityToDto(recordE);
        dto.setInRealWarehouseName(warehouse.getRealWarehouseName());

        //遍历明细获取sku信息
        List<ShopReturnDetailDTO> details = dto.getFrontRecordDetails();
        List<Long> skuIds = RomeCollectionUtil.getValueList(details, "skuId");
        List<SkuInfoExtDTO> skuInfoExtDTOS = skuFacade.skusBySkuId(skuIds);
        Map<Long, SkuInfoExtDTO> skuInfoExtDTOMap = RomeCollectionUtil.listforMap(skuInfoExtDTOS, "id");

        details.forEach(shopReturnDetailDTO -> {
            if(skuInfoExtDTOMap.containsKey(shopReturnDetailDTO.getSkuId())){
                SkuInfoExtDTO infoExtDTO = skuInfoExtDTOMap.get(shopReturnDetailDTO.getSkuId());
                shopReturnDetailDTO.setSkuName(infoExtDTO.getName());
            }
        });
        return dto;
    }

    @Override
    public void handleJoinReturnPushCMP(String recordCode) {
        WarehouseRecordE recordE = warehouseRecordRepository.getRecordWithDetailByCode(recordCode, null);
        //根据后置单据查询前置单
        List<CommonFrontRecordDTO> frontRecordList = orderCenterFacade.queryCommonFrontRecordInfoByRecordCode(recordE.getRecordCode());
        AlikAssert.isNotEmpty(frontRecordList, ResCode.STOCK_ERROR_1014, ResCode.STOCK_ERROR_1014_DESC);
        CommonFrontRecordDTO frontRecord  = frontRecordList.get(0);
        String shopCode = frontRecord.getShopCode();
        //查询门店对应的加盟商
        StoreDTO store = shopFacade.searchByCode(shopCode);
        AlikAssert.isNotNull(store, ResCode.STOCK_ERROR_1037,ResCode.STOCK_ERROR_1037_DESC);
        //查询sku及sku的价格
        Map<String, SkuSalePurchasePrice> priceMap =  tradePriceFacade.getTradePriceByDetail(frontRecordList ,null, 2, "0");
        List<JoinReturnRecordDTO> joinDispatchList = new ArrayList<>();
        for (WarehouseRecordDetail detail : recordE.getWarehouseRecordDetails()) {
            SkuSalePurchasePrice priceDto = priceMap.get(detail.getSkuCode());
            AlikAssert.isNotNull(priceDto , ResCode.STOCK_ERROR_2010,"物料"+ detail.getSkuCode() + "价格" + ResCode.STOCK_ERROR_2010);
            String  taxCode = priceDto.getTaxCode();
            String taxRateStr = TaxCodeConsts.getTaxRateByCode(taxCode);
            BigDecimal taxRate = BigDecimal.ZERO;
            if(StringUtils.isNotBlank(taxRateStr)){
                taxRate = new BigDecimal(taxRateStr).divide(new BigDecimal("100"));
            }else{
                throw new RomeException(ResCode.STOCK_ERROR_1001, "物料"+  detail.getSkuCode()  +"税码" + ResCode.STOCK_ERROR_2010_DESC);
            }
            BigDecimal scale = priceDto.getScale();
            BigDecimal planQty = detail.getPlanQty();
            //含税价格
            BigDecimal inPrice = priceDto.getSkuPurchasePrice().setScale(PRICE_POINT_NUM, BigDecimal.ROUND_UP);
            //无税价格 = 含税价格除以（1 + 税率）
            BigDecimal price = inPrice.divide(BigDecimal.ONE.add(taxRate), PRICE_POINT_NUM, BigDecimal.ROUND_UP);
            //无税总金额 = 无税价格 * SKU采购数量 *包装换算比
            BigDecimal totalPrice = price.multiply(planQty).multiply(scale).setScale(PRICE_POINT_NUM, BigDecimal.ROUND_UP);
            //含税金额 = 无税价格 * （1 + 税率）* SKU采购数量 *包装换算比
            BigDecimal totalInPrice = inPrice.multiply(planQty).multiply(scale).setScale(PRICE_POINT_NUM, BigDecimal.ROUND_UP);
            JoinReturnRecordDTO joinReturnRecord = new JoinReturnRecordDTO();
            joinReturnRecord.setOprOrgCode(store.getFranchisee());
            joinReturnRecord.setShopCode(store.getCode());
            joinReturnRecord.setOutCreateTime(frontRecord.getOutCreateTime());
            joinReturnRecord.setPlanDeliveryTime(frontRecord.getReplenishTime());
            joinReturnRecord.setSkuCode(detail.getSkuCode());
            joinReturnRecord.setSkuQty(planQty);
            joinReturnRecord.setUnitCode(detail.getUnitCode());
            joinReturnRecord.setUnit(detail.getUnit());
            joinReturnRecord.setRecordCode(recordE.getSapOrderCode());
            joinReturnRecord.setSapPoNo(detail.getSapPoNo());
            joinReturnRecord.setLineNo(detail.getLineNo());
            joinReturnRecord.setPrice(price);
            joinReturnRecord.setInPrice(inPrice);
            joinReturnRecord.setTiaxRate(taxRate);
            joinReturnRecord.setTotal(totalPrice);
            joinReturnRecord.setInTotal(totalInPrice);
            joinReturnRecord.setPackRate(scale);
            joinReturnRecord.setCmpRecordCode(frontRecord.getCmpRecordCode());
            joinReturnRecord.setBillNo(detail.getSapPoNo());
            joinReturnRecord.setSerialNo(detail.getLineNo());
            joinReturnRecord.setNoteSerialNo(detail.getLineNo());
            joinDispatchList.add(joinReturnRecord);
        }
        //推送CMP
        boolean flag = cmpFacade.handleJoinReturnPushCMP(recordE.getRecordCode(), joinDispatchList);
        AlikAssert.isTrue(flag,ResCode.STOCK_ERROR_1003, "推送失败"+recordE.getRecordCode());
        recordE.updateCmpStatusFinish();
    }

    @Override
    public Response pushShopReturnResultCmp5(String recordCode,List<ShopReturnResultDTO> list) {
        WarehouseRecordE recordE = warehouseRecordRepository.getRecordWithDetailByCode(recordCode, null);
        //根据后置单据查询前置单-此查询无法删除，需要查询退货原因
        List<CommonFrontRecordDTO> frontRecordList = orderCenterFacade.queryCommonFrontRecordInfoByRecordCode(recordE.getRecordCode());
        AlikAssert.isNotEmpty(frontRecordList, ResCode.STOCK_ERROR_1014, ResCode.STOCK_ERROR_1014_DESC);
        CommonFrontRecordDTO frontRecord  = frontRecordList.get(0);
        List<CommonFrontRecordDetailDTO> frontRecordDetails = frontRecord.getFrontRecordDetails();
        Map<String, CommonFrontRecordDetailDTO> frontDetailMap = frontRecordDetails.stream().collect(Collectors.toMap(CommonFrontRecordDetailDTO::getSkuCode, Function.identity(), (v1, v2) -> v2));
        String shopCode = frontRecord.getShopCode();
        List<String> skuCodes =recordE.getWarehouseRecordDetails().stream().map(x -> x.getSkuCode()).collect(Collectors.toList());
        List<SkuInfoExtDTO> skuInfoExtDTOS = skuFacade.skusBySkuCode(skuCodes);
        AlikAssert.isNotEmpty(skuInfoExtDTOS,ResCode.STOCK_ERROR_1002,"未查询到当前商品信息:skuCodes"+JSON.toJSONString(skuCodes));
        Map<String, SkuInfoExtDTO> skuMap = skuInfoExtDTOS.stream().collect(Collectors.toMap(SkuInfoExtDTO::getSkuCode, Function.identity(), (v1, v2) -> v2));
        for(WarehouseRecordDetail detail:recordE.getWarehouseRecordDetails()){
            ShopReturnResultDTO shopReturnResultDTO=new ShopReturnResultDTO();
            shopReturnResultDTO.setBillNo(recordE.getSapOrderCode());
            shopReturnResultDTO.setLrDate(DateUtil.formatDateTime(frontRecord.getOutCreateTime()));
            shopReturnResultDTO.setOrgCode(shopCode);
            shopReturnResultDTO.setDepID(0L);
            shopReturnResultDTO.setSerialNo(Long.valueOf(detail.getLineNo()));
            SkuInfoExtDTO skuInfoExtDTO = skuMap.get(detail.getSkuCode());
            AlikAssert.isNotNull(skuInfoExtDTO,ResCode.STOCK_ERROR_1002,"商品中心未查询到该数据:skuCode="+detail.getSkuCode());
            shopReturnResultDTO.setPluID(detail.getSkuId());
            shopReturnResultDTO.setPluCode(detail.getSkuCode());
            shopReturnResultDTO.setPluName(StockStringUtils.subStringByteLengthByGBEncode(skuInfoExtDTO.getName(), CMP_SKU_NAME_LENGTH));
            shopReturnResultDTO.setThCount(detail.getActualQty());
            shopReturnResultDTO.setUserID(0L);
            //海信需要截取6位
            String  userCode = recordE.getUserCode();
            if(StringUtils.isNotBlank(userCode) && userCode.length() > 6){
                shopReturnResultDTO.setUserCode(userCode.substring(userCode.length()-6,userCode.length()));
            }
            shopReturnResultDTO.setYwType("3");
            shopReturnResultDTO.setCkCode(frontRecord.getOutRealWarehouseCode());
            shopReturnResultDTO.setDataState("1");
            CommonFrontRecordDetailDTO commonFrontRecordDetailDTO = frontDetailMap.get(detail.getSkuCode());
            AlikAssert.isNotNull(commonFrontRecordDetailDTO,ResCode.STOCK_ERROR_1003,"查询订单中心未获取到退货原因:recordCode="+recordCode);
            shopReturnResultDTO.setReason(commonFrontRecordDetailDTO.getReasonCode());
            shopReturnResultDTO.setTag(0);
            list.add(shopReturnResultDTO);
        }
        //调用webService
        Response response = cmpRemoteService.pushShopReturnResultCmp5(list);
        log.warn("通知cmp退货结果返回 :{}",response.toString());
        AlikAssert.isTrue(Objects.nonNull(response) && Objects.equals("0",response.getCode()),ResCode.STOCK_ERROR_1003,JSON.toJSONString(response));
        //调用完成 更新状态
        int result = warehouseRecordRepository.updateShopResultFinish(recordE.getId());
        AlikAssert.isTrue(result>0,ResCode.STOCK_ERROR_1002,"更新补货结果状态失败:recordCode="+recordCode);
        return response;
    }

    @Override
    public Response pushShopReturnResultCmp6(String recordCode,List<ShopReturnResultDTO> list) {
        WarehouseRecordE recordE = warehouseRecordRepository.getRecordWithDetailByCode(recordCode, null);
        //根据后置单据查询前置单-此查询无法删除，需要查询退货原因
        List<CommonFrontRecordDTO> frontRecordList = orderCenterFacade.queryCommonFrontRecordInfoByRecordCode(recordE.getRecordCode());
        AlikAssert.isNotEmpty(frontRecordList, ResCode.STOCK_ERROR_1014, ResCode.STOCK_ERROR_1014_DESC);
        CommonFrontRecordDTO frontRecord  = frontRecordList.get(0);
        List<CommonFrontRecordDetailDTO> frontRecordDetails = frontRecord.getFrontRecordDetails();
        Map<String, CommonFrontRecordDetailDTO> frontDetailMap = frontRecordDetails.stream().collect(Collectors.toMap(CommonFrontRecordDetailDTO::getSkuCode, Function.identity(), (v1, v2) -> v2));
        String shopCode = frontRecord.getShopCode();
        List<String> skuCodes =recordE.getWarehouseRecordDetails().stream().map(x -> x.getSkuCode()).collect(Collectors.toList());
        List<SkuInfoExtDTO> skuInfoExtDTOS = skuFacade.skusBySkuCode(skuCodes);
        AlikAssert.isNotEmpty(skuInfoExtDTOS,ResCode.STOCK_ERROR_1002,"未查询到当前商品信息:skuCodes"+JSON.toJSONString(skuCodes));
        Map<String, SkuInfoExtDTO> skuMap = skuInfoExtDTOS.stream().collect(Collectors.toMap(SkuInfoExtDTO::getSkuCode, Function.identity(), (v1, v2) -> v2));
        for(WarehouseRecordDetail detail:recordE.getWarehouseRecordDetails()){
            ShopReturnResultDTO shopReturnResultDTO=new ShopReturnResultDTO();
            shopReturnResultDTO.setBillNo(recordE.getSapOrderCode());
            shopReturnResultDTO.setLrDate(DateUtil.formatDateTime(frontRecord.getOutCreateTime()));
            shopReturnResultDTO.setOrgCode(shopCode);
            shopReturnResultDTO.setDepID(0L);
            shopReturnResultDTO.setSerialNo(Long.valueOf(detail.getLineNo()));
            SkuInfoExtDTO skuInfoExtDTO = skuMap.get(detail.getSkuCode());
            AlikAssert.isNotNull(skuInfoExtDTO,ResCode.STOCK_ERROR_1002,"商品中心未查询到该数据:skuCode="+detail.getSkuCode());
            shopReturnResultDTO.setPluID(detail.getSkuId());
            shopReturnResultDTO.setPluCode(detail.getSkuCode());
            shopReturnResultDTO.setPluName(StockStringUtils.subStringByteLengthByGBEncode(skuInfoExtDTO.getName(), CMP_SKU_NAME_LENGTH));
            shopReturnResultDTO.setThCount(detail.getActualQty());
            shopReturnResultDTO.setUserID(0L);
            String  userCode = recordE.getUserCode();
            if(StringUtils.isNotBlank(userCode) && userCode.length() > 6){
                shopReturnResultDTO.setUserCode(userCode.substring(userCode.length()-6,userCode.length()));
            }
            shopReturnResultDTO.setYwType("3");
            shopReturnResultDTO.setCkCode(frontRecord.getOutRealWarehouseCode());
            shopReturnResultDTO.setDataState("1");
            CommonFrontRecordDetailDTO commonFrontRecordDetailDTO = frontDetailMap.get(detail.getSkuCode());
            AlikAssert.isNotNull(commonFrontRecordDetailDTO,ResCode.STOCK_ERROR_1003,"查询订单中心未获取到退货原因:recordCode="+recordCode);
            shopReturnResultDTO.setReason(commonFrontRecordDetailDTO.getReasonCode());
            shopReturnResultDTO.setTag(0);
            list.add(shopReturnResultDTO);
        }
        //调用webService
        Response response = cmpFacade.pushShopReturnResultCmp6(recordCode,list);
        log.warn("通知cmp退货结果返回 :{}",response.toString());
        AlikAssert.isTrue(Objects.nonNull(response) && Objects.equals("0",response.getCode()),ResCode.STOCK_ERROR_1003,JSON.toJSONString(response));
        //调用完成 更新状态
        int result = warehouseRecordRepository.updateShopResultFinish(recordE.getId());
        AlikAssert.isTrue(result>0,ResCode.STOCK_ERROR_1002,"更新补货结果状态失败:recordCode="+recordCode);
        return response;
    }



    @Override
    public void handleJoinReturnPushCMP7(String recordCode) {
        WarehouseRecordE recordE = warehouseRecordRepository.getRecordWithDetailByCode(recordCode, null);
        //根据后置单据查询前置单
        List<CommonFrontRecordDTO> frontRecordList = orderCenterFacade.queryCommonFrontRecordInfoByRecordCode(recordE.getRecordCode());
        AlikAssert.isNotEmpty(frontRecordList, ResCode.STOCK_ERROR_1014, ResCode.STOCK_ERROR_1014_DESC);
        CommonFrontRecordDTO frontRecord  = frontRecordList.get(0);
        Map<Long,CommonFrontRecordDetailDTO> frontDetailMap=frontRecord.getFrontRecordDetails().stream().collect(Collectors.toMap(CommonFrontRecordDetailDTO::getId, Function.identity(), (v1, v2) -> v1));
        String shopCode = frontRecord.getShopCode();
        //查询门店对应的加盟商
        StoreDTO store = shopFacade.searchByCode(shopCode);
        AlikAssert.isNotNull(store, ResCode.STOCK_ERROR_1037,ResCode.STOCK_ERROR_1037_DESC);
        //查询sku及sku的价格
        Map<String, SkuSalePurchasePrice> priceMap =  tradePriceFacade.getTradePriceByDetail(frontRecordList ,null, 2, "0");
        Sign sign = SignUtil.getInstance().getSign();
        try{
            ShopReturnCmp7DTO shopReturnCmp7DTO = this.buildShopReturnCmp7DTO(sign, recordE, frontRecord, store, priceMap);
            if(CollectionUtils.isEmpty(shopReturnCmp7DTO.getData())){
                log.warn("组装cmp7退货过账单明细为空recordCode={},{}",recordE.getRecordCode(), JSON.toJSONString(shopReturnCmp7DTO));
            }
            Cmp7ReturnDTO result = cmp7Facade.returnAccount(shopReturnCmp7DTO, recordE.getRecordCode());
            if(Objects.nonNull(result) && Objects.equals(result.getCode(),1)){
                //AlikAssert.isTrue(sign.verify( recordE.getSapOrderCode().getBytes(),SignUtil.getInstance().Hex2Bytes(result.getSign())),ResCode.STOCK_ERROR_1002,"cmp7返回签名失败");
                recordE.updateCmp7StatusFinish();
            } else {
                throw new RomeException("999" , result == null ? "返回结果为空" : result.getMessage());
            }
        }catch (Exception e){
            log.error("加盟退货推cmp7异常:{}",e);
            throw new RomeException(ResCode.STOCK_ERROR_1003,"加盟退货推CMP7异常，" + e.getMessage());
        }
    }

    /**
     * 加盟退货cmp信息构建
     * @param recordE
     * @param frontRecord
     * @param store
     * @param priceMap
     * @return
     */
    private List<JoinReturnRecordDTO> buildJoinReturnRecordDTOS(WarehouseRecordE recordE, CommonFrontRecordDTO frontRecord, StoreDTO store, Map<String, SkuSalePurchasePrice> priceMap) {
        List<JoinReturnRecordDTO> joinDispatchList = new ArrayList<>();
        for (WarehouseRecordDetail detail : recordE.getWarehouseRecordDetails()) {
            SkuSalePurchasePrice priceDto = priceMap.get(detail.getSapPoNo() + "_" + detail.getLineNo());
            AlikAssert.isNotNull(priceDto , ResCode.STOCK_ERROR_2010,"物料"+ detail.getSkuCode() + "价格" + ResCode.STOCK_ERROR_2010_DESC);
            String  taxCode = priceDto.getTaxCode();
            String taxRateStr = TaxCodeConsts.getTaxRateByCode(taxCode);
            BigDecimal taxRate = BigDecimal.ZERO;
            if(StringUtils.isNotBlank(taxRateStr)){
                taxRate = new BigDecimal(taxRateStr).divide(new BigDecimal("100"));
            }else{
                throw new RomeException(ResCode.STOCK_ERROR_1001, "物料"+  detail.getSkuCode()  +"税码" + ResCode.STOCK_ERROR_2010_DESC);
            }
            BigDecimal scale = priceDto.getScale();
            BigDecimal planQty = detail.getPlanQty();
            //含税价格
            BigDecimal inPrice = priceDto.getSkuPurchasePrice().setScale(PRICE_POINT_NUM, BigDecimal.ROUND_UP);
            //无税价格 = 含税价格除以（1 + 税率）
            BigDecimal price = inPrice.divide(BigDecimal.ONE.add(taxRate), PRICE_POINT_NUM, BigDecimal.ROUND_UP);
            //无税总金额 = 无税价格 * SKU采购数量 *包装换算比
            BigDecimal totalPrice = price.multiply(planQty).multiply(scale).setScale(PRICE_POINT_NUM, BigDecimal.ROUND_UP);
            //含税金额 = 无税价格 * （1 + 税率）* SKU采购数量 *包装换算比
            BigDecimal totalInPrice = inPrice.multiply(planQty).multiply(scale).setScale(PRICE_POINT_NUM, BigDecimal.ROUND_UP);
            JoinReturnRecordDTO joinReturnRecord = new JoinReturnRecordDTO();
            joinReturnRecord.setOprOrgCode(store.getFranchisee());
            joinReturnRecord.setShopCode(store.getCode());
            joinReturnRecord.setOutCreateTime(frontRecord.getOutCreateTime());
            joinReturnRecord.setPlanDeliveryTime(frontRecord.getReplenishTime());
            joinReturnRecord.setSkuCode(detail.getSkuCode());
            joinReturnRecord.setSkuQty(planQty);
            joinReturnRecord.setUnitCode(detail.getUnitCode());
            joinReturnRecord.setUnit(detail.getUnit());
            joinReturnRecord.setRecordCode(recordE.getSapOrderCode());
            joinReturnRecord.setSapPoNo(detail.getSapPoNo());
            joinReturnRecord.setLineNo(detail.getLineNo());
            joinReturnRecord.setPrice(price);
            joinReturnRecord.setInPrice(inPrice);
            joinReturnRecord.setTiaxRate(taxRate);
            joinReturnRecord.setTotal(totalPrice);
            joinReturnRecord.setInTotal(totalInPrice);
            joinReturnRecord.setPackRate(scale);
            joinReturnRecord.setCmpRecordCode(frontRecord.getCmpRecordCode());
            joinReturnRecord.setBillNo(detail.getSapPoNo());
            joinReturnRecord.setSerialNo(detail.getLineNo());
            joinReturnRecord.setNoteSerialNo(detail.getLineNo());
            joinDispatchList.add(joinReturnRecord);
        }
        return joinDispatchList;
    }


    /**
     * 构建cmp7加盟退货对象
     * @param sign
     * @param recordE
     * @param frontRecord
     * @param store
     * @param priceMap
     * @return
     */
    private ShopReturnCmp7DTO buildShopReturnCmp7DTO(Sign sign, WarehouseRecordE recordE, CommonFrontRecordDTO frontRecord, StoreDTO store, Map<String, SkuSalePurchasePrice> priceMap){
        ShopReturnCmp7DTO shopReturnCmp7DTO=new ShopReturnCmp7DTO();
        shopReturnCmp7DTO.setTimestamp(System.currentTimeMillis()/1000);
        byte[] signByte = sign.sign(recordE.getSapOrderCode().getBytes());
        shopReturnCmp7DTO.setSign(SignUtil.getInstance().byte2String(signByte));
        List<ShopReturnDetailCmp7DTO> data=new ArrayList<>();
        for (WarehouseRecordDetail detail : recordE.getWarehouseRecordDetails()) {
            SkuSalePurchasePrice priceDto = priceMap.get(detail.getSkuCode());
            AlikAssert.isNotNull(priceDto , ResCode.STOCK_ERROR_2010,"物料"+ detail.getSkuCode() + "价格" + ResCode.STOCK_ERROR_2010_DESC);
            String  taxCode = priceDto.getTaxCode();
            String taxRateStr = TaxCodeConsts.getTaxRateByCode(taxCode);
            BigDecimal taxRate = BigDecimal.ZERO;
            if(StringUtils.isNotBlank(taxRateStr)){
                taxRate = new BigDecimal(taxRateStr).divide(new BigDecimal("100"));
            }else{
                throw new RomeException(ResCode.STOCK_ERROR_1001, "物料"+  detail.getSkuCode()  +"税码" + ResCode.STOCK_ERROR_2010_DESC);
            }
            BigDecimal scale = priceDto.getScale();
            BigDecimal planQty = detail.getPlanQty();
            //含税价格
            BigDecimal inPrice = priceDto.getSkuPurchasePrice().setScale(PRICE_POINT_NUM, BigDecimal.ROUND_UP);
            //无税价格 = 含税价格除以（1 + 税率）
            BigDecimal price = inPrice.divide(BigDecimal.ONE.add(taxRate), PRICE_POINT_NUM, BigDecimal.ROUND_UP);
            //无税总金额 = 无税价格 * SKU采购数量 *包装换算比
            BigDecimal totalPrice = price.multiply(planQty).multiply(scale).setScale(PRICE_POINT_NUM, BigDecimal.ROUND_UP);
            //含税金额 = 无税价格 * （1 + 税率）* SKU采购数量 *包装换算比
            BigDecimal totalInPrice = inPrice.multiply(planQty).multiply(scale).setScale(PRICE_POINT_NUM, BigDecimal.ROUND_UP);
            ShopReturnDetailCmp7DTO shopReturnDetailCmp7DTO = new ShopReturnDetailCmp7DTO();
            shopReturnDetailCmp7DTO.setPreOrgCode(store.getFranchisee());
            shopReturnDetailCmp7DTO.setOrgCode(store.getCode());
            shopReturnDetailCmp7DTO.setAccDate(cn.hutool.core.date.DateUtil.format(recordE.getCreateTime(),"yyyy-MM-dd"));
            shopReturnDetailCmp7DTO.setBillNo(frontRecord.getHisenseNo());
            shopReturnDetailCmp7DTO.setSerialNo("");
            shopReturnDetailCmp7DTO.setSapBillNo(detail.getSapPoNo());
            shopReturnDetailCmp7DTO.setKpBillNo(recordE.getSapOrderCode());
            shopReturnDetailCmp7DTO.setSapSerialNo(detail.getLineNo());
            shopReturnDetailCmp7DTO.setPluCode(detail.getSkuCode());
            shopReturnDetailCmp7DTO.setPackRate(scale);
            shopReturnDetailCmp7DTO.setQuantity(planQty);
            shopReturnDetailCmp7DTO.setUnit(detail.getUnit());
            shopReturnDetailCmp7DTO.setUnitCode(detail.getUnitCode());
            shopReturnDetailCmp7DTO.setPrice(price);
            shopReturnDetailCmp7DTO.setTotal(totalPrice);
            shopReturnDetailCmp7DTO.setTaxPrice(inPrice);
            shopReturnDetailCmp7DTO.setTaxTotal(totalInPrice);
            data.add(shopReturnDetailCmp7DTO);
        }
        shopReturnCmp7DTO.setData(data);
        return shopReturnCmp7DTO;
    }


    /**
     * 增加实体仓库库存的对象
     */
    private CoreRealStockOpDO initStockObj(WarehouseRecordE recordE){
        List<CoreRealStockOpDetailDO> increaseDetails = new ArrayList<>();
        for (WarehouseRecordDetail detail: recordE.getWarehouseRecordDetails()) {
            //数量为0的不处理库存的增加和减少
            if(BigDecimal.ZERO.compareTo(detail.getActualQty()) == 0){
                continue;
            }
            CoreRealStockOpDetailDO coreRealStockOpDetailDO = new CoreRealStockOpDetailDO();
            coreRealStockOpDetailDO.setSkuId(detail.getSkuId());
            coreRealStockOpDetailDO.setSkuCode(detail.getSkuCode());
            coreRealStockOpDetailDO.setRealQty(detail.getActualQty());
            coreRealStockOpDetailDO.setRealWarehouseId(recordE.getRealWarehouseId());
            increaseDetails.add(coreRealStockOpDetailDO);
        }
        CoreRealStockOpDO coreRealStockOpDO = new CoreRealStockOpDO();
        coreRealStockOpDO.setRecordCode(recordE.getRecordCode());
        coreRealStockOpDO.setTransType(recordE.getRecordType());
        coreRealStockOpDO.setDetailDos(increaseDetails);
        return coreRealStockOpDO;
    }

    /**
     * 初始化出库库存对象
     */
    private CoreRealStockOpDO initOutWarehouseStockObj(WarehouseRecordE outRecord){
        List<CoreRealStockOpDetailDO> increaseDetails = new ArrayList<>();
        for (WarehouseRecordDetail detail: outRecord.getWarehouseRecordDetails()) {
            //数量为0的不处理库存的增加和减少
            if(BigDecimal.ZERO.compareTo(detail.getPlanQty()) == 0){
                continue;
            }
            CoreRealStockOpDetailDO coreRealStockOpDetailDO = new CoreRealStockOpDetailDO();
            coreRealStockOpDetailDO.setSkuId(detail.getSkuId());
            coreRealStockOpDetailDO.setSkuCode(detail.getSkuCode());
            coreRealStockOpDetailDO.setRealQty(detail.getPlanQty());
            coreRealStockOpDetailDO.setRealWarehouseId(outRecord.getRealWarehouseId());
            coreRealStockOpDetailDO.setCheckBeforeOp(false);
            increaseDetails.add(coreRealStockOpDetailDO);
        }
        CoreRealStockOpDO coreRealStockOpDO = new CoreRealStockOpDO();
        coreRealStockOpDO.setRecordCode(outRecord.getRecordCode());
        coreRealStockOpDO.setTransType(outRecord.getRecordType());
        coreRealStockOpDO.setDetailDos(increaseDetails);
        return coreRealStockOpDO;
    }

    /**
     * 组装商品价格查询对象
     * @param store
     * @return
     */
    private Map<String, SkuSalePurchasePrice> getSkuPriceResult(List<WarehouseRecordDetail> frontRecordDetail, StoreDTO store){
        Map<String, SkuSalePurchasePrice> priceMap = new HashMap<>();
        List<QuerySalePurchasePricesExtDTO> parmList = new ArrayList<>();
        //查询加盟商的120渠道
        List<ChannelDTO> channelDTOList;
        channelDTOList = channelFacade.queryChannelByCodeAndType("120", store.getFranchisee(), "3");
        if (CollectionUtils.isEmpty(channelDTOList)) {
            throw new RomeException(ResCode.STOCK_ERROR_7324, ResCode.STOCK_ERROR_7324_DESC);
        }
        String channelCode = channelDTOList.get(0).getChannelCode();
        for (WarehouseRecordDetail commonFrontRecordDetailDTO : frontRecordDetail) {
            QuerySalePurchasePricesExtDTO storePurchase =  new QuerySalePurchasePricesExtDTO();
            storePurchase.setChannelCode(channelCode);
            storePurchase.setSkuCode(commonFrontRecordDetailDTO.getSkuCode());
            storePurchase.setSaleUnitCode(commonFrontRecordDetailDTO.getUnitCode());
            parmList.add(storePurchase);
        }
        List<ChannelSkuPurchasePriceDTO> skuResultList  = skuFacade.salePricesBySkuCodesAndChannelCodesAndSaleUnitCodes(parmList);
        for (ChannelSkuPurchasePriceDTO priceDto : skuResultList) {
            SkuSalePurchasePrice skuSalePurchasePrice = new SkuSalePurchasePrice();
            BigDecimal salePrice = priceDto.getSalePrice();
            skuSalePurchasePrice.setTaxCode(priceDto.getTaxCode());
            skuSalePurchasePrice.setSkuPurchasePrice(salePrice);
            skuSalePurchasePrice.setScale(BigDecimal.ONE);
                priceMap.put(priceDto.getSkuCode(), skuSalePurchasePrice);
            }
        return priceMap;
    }



    @Override
    public List<String> getWaitReturnNotifyToCmp(Integer page, Integer pageSize) {
        PageHelper.startPage(page, pageSize);
        return warehouseRecordRepository.getWaitReturnNotifyToCmp();
    }

    @Override
    public List<WarehouseRecordPageDTO> getWaitReturnReceiveToCmp(Integer page, Integer pageSize) {
        PageHelper.startPage(page, pageSize);
        return warehouseRecordRepository.getWaitReturnReceiveToCmp();
    }

    @Override
    public List<String> getWaitReturnNotifyToCmp7(Integer page, Integer pageSize) {
        PageHelper.startPage(page, pageSize);
        return warehouseRecordRepository.getWaitReturnNotifyToCmp7();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void cancelShopReturnRecord(CancelRecordDTO cancelRecordDTO) {
        WarehouseRecordE warehouseRecordE = warehouseRecordRepository.queryWarehouseRecordByRecordCode(cancelRecordDTO.getRecordCode());
        //单据不存在直接返回取消成功
        if(null == warehouseRecordE){
            throw new RomeException(ResCode.STOCK_ERROR_1017, ResCode.STOCK_ERROR_1017_DESC);
}
        if(WarehouseRecordStatusVO.DISABLED.getStatus().equals(warehouseRecordE.getRecordStatus())){
            return;
        }
        if(WarehouseRecordStatusVO.OUT_ALLOCATION.getStatus().equals(warehouseRecordE.getRecordStatus()) || WarehouseRecordStatusVO.IN_ALLOCATION.getStatus().equals(warehouseRecordE.getRecordStatus())){
            throw new RomeException(ResCode.STOCK_ERROR_1028, "取消失败：单据已出库或入库无法取消");
        }
        boolean isSuccess = false;
        CoreRealStockOpDO coreRealStockOpDO = null;
        try{
            int j =warehouseRecordRepository.updateToCanceled(warehouseRecordE.getId());
            AlikAssert.isTrue(j > 0, ResCode.STOCK_ERROR_1028, ResCode.STOCK_ERROR_1028_DESC);
            //出库取消不用处理库存
            if (WarehouseRecordBusinessTypeVO.OUT_WAREHOUSE_RECORD.getType().equals(warehouseRecordE.getBusinessType())) {
                return;
            }
            WarehouseRecordE recordE = warehouseRecordRepository.getRecordWithDetailById(warehouseRecordE.getId());
            ShopReturnWarehouseRecordE warehouseReverseRecordE = shopReturnConvertor.warehouseRecordEToShopReturnEntity(recordE);
            //入库取消
            coreRealStockOpDO = warehouseReverseRecordE.packUnlockOnRoadStock();
            coreRealWarehouseStockRepository.decreaseOnroadStock(coreRealStockOpDO);
            if(WmsSyncStatusVO.SYNCHRONIZED.getStatus().equals(warehouseRecordE.getSyncWmsStatus()) && cancelRecordDTO.getIsForceCancel()) {
                boolean flag = wmsOutService.orderCancel(cancelRecordDTO.getRecordCode());
                AlikAssert.isTrue(flag, ResCode.STOCK_ERROR_1205, ResCode.STOCK_ERROR_1205_DESC);
            }
            isSuccess = true;
        }catch (RomeException e) {
            log.error(e.getMessage(), e);
            throw new RomeException(e.getCode(), e.getMessage());
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            throw new RomeException(ResCode.STOCK_ERROR_1003, e.getMessage());
        } finally {
            //模型成功，业务等处理失败，需要回滚
            if (!isSuccess) {
                RedisRollBackFacade.redisRollBack(coreRealStockOpDO);
            }
        }
    }
}
