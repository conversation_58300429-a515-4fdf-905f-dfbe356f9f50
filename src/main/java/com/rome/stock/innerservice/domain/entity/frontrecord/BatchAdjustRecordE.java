package com.rome.stock.innerservice.domain.entity.frontrecord;

import java.util.Date;
import java.util.List;

import javax.annotation.Resource;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Component;

import com.rome.arch.core.domain.EntityFactory;
import com.rome.stock.core.domain.repository.CoreRealWarehouseStockRepository;
import com.rome.stock.innerservice.common.AlikAssert;
import com.rome.stock.innerservice.common.RomeCollectionUtil;
import com.rome.stock.innerservice.common.SkuQtyUnitTools;
import com.rome.stock.innerservice.constant.FrontRecordTypeVO;
import com.rome.stock.innerservice.domain.repository.frontrecord.FrBatchAdjustRepository;
import com.rome.stock.innerservice.domain.repository.warehouserecord.WarehouseRecordRepository;
import com.rome.stock.innerservice.domain.service.RealWarehouseService;
import com.rome.stock.innerservice.infrastructure.dataobject.frontrecord.FrWhInventoryBatchTempDO;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.extern.slf4j.Slf4j;

/**
 * 库存批次调整单
 */
@Component
@Scope("prototype")
@Data
@Slf4j
@EqualsAndHashCode(callSuper = false)
public class BatchAdjustRecordE extends AbstractFrontRecord{

    @Resource
    private EntityFactory entityFactory;
    @Resource
    private SkuQtyUnitTools skuQtyUnitTools;
    @Autowired
    private CoreRealWarehouseStockRepository coreRealWarehouseStockRepository;
    @Resource
    private RealWarehouseService realWarehouseService;
    @Resource
    private FrBatchAdjustRepository frBatchAdjustRepository;
    @Resource
    private WarehouseRecordRepository warehouseRecordRepository;

    /**
     * 生成批次调整单
     */
    public void addFrontRecord() {
        //设置单据类型
        this.setRecordType(FrontRecordTypeVO.BATCH_AJUST_RECORD.getType());
        initFrontRecord(FrontRecordTypeVO.BATCH_AJUST_RECORD.getCode(),this.frontRecordDetails);
        Long id = frBatchAdjustRepository.addBatchAdjustRecord(this);
        AlikAssert.isTrue(id > 0, "999", "创建批次调整单失败");
        this.setId(id);
        this.frontRecordDetails.forEach(record ->record.setFrontRecordDetail(this));
        //生成批次调整详情
        List<List<BatchAdjustRecordDetailE>> splitList = RomeCollectionUtil.splitList(this.frontRecordDetails, 1000);
        for (List<BatchAdjustRecordDetailE> subList : splitList) {
            frBatchAdjustRepository.addBatchAdjustRecordDetail(subList);
        }
    }


    /**
     * 实仓ID
     */
    private Long realWarehouseId;

    /**
     * 业务类型: 1: 损益批次调整 2: 盘点批次调整 3: wms批次调整
     */
    private Integer businessType;


    /**
     * 备注
     */
    private String remark;


    /**
     * 开始时间
     */
    private Date startTime;

    /**
     * 结束时间
     */
    private Date endTime;

    /**
     * 外部系统单据编号
     */
    private String outRecordCode;

    /**
     * 调整明细
     */
    private List<BatchAdjustRecordDetailE> frontRecordDetails;
}
