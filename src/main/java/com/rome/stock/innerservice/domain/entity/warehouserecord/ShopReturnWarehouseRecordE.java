package com.rome.stock.innerservice.domain.entity.warehouserecord;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

import javax.annotation.Resource;

import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Component;

import com.rome.arch.core.domain.EntityFactory;
import com.rome.stock.common.constants.warehouse.WarehouseRecordConstant;
import com.rome.stock.innerservice.api.dto.warehouserecord.InWarehouseRecordDTO;
import com.rome.stock.innerservice.api.dto.warehouserecord.OutWarehouseRecordDTO;
import com.rome.stock.innerservice.api.dto.warehouserecord.RecordDetailDTO;
import com.rome.stock.innerservice.common.SkuQtyUnitTools;
import com.rome.stock.innerservice.constant.WarehouseRecordBatchStatusVO;
import com.rome.stock.innerservice.constant.WarehouseRecordBusinessTypeVO;
import com.rome.stock.innerservice.constant.WarehouseRecordStatusVO;
import com.rome.stock.innerservice.constant.WmsSyncStatusVO;
import com.rome.stock.innerservice.domain.entity.RealWarehouseE;
import com.rome.stock.innerservice.domain.repository.warehouserecord.ShopReturnWarehouseRepository;
import com.rome.stock.core.infrastructure.dataobject.core.CoreRealStockOpDO;
import com.rome.stock.core.infrastructure.dataobject.core.CoreRealStockOpDetailDO;
import com.rome.stock.innerservice.remote.orderCneter.facade.OrderCenterFacade;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.extern.slf4j.Slf4j;

/**
 * 类ShopReturnWarehouseRecordE的实现描述：门店退货出入库单
 *
 * <AUTHOR> 2019/5/8 16:13
 */
@Data
@Slf4j
@Component
@Scope("prototype")
@EqualsAndHashCode(callSuper = false)
public class ShopReturnWarehouseRecordE extends AbstractWarehouseRecord {
    @Resource
    private EntityFactory entityFactory;

    @Resource
    private ShopReturnWarehouseRepository shopReturnWarehouseRepository;

    @Resource
    private SkuQtyUnitTools skuQtyUnitTools;

    @Resource
    private OrderCenterFacade orderCenterFacade;


    /**
     * 门店出库单
     */
    public void shopOutRecord(RealWarehouseE realWarehouseE, OutWarehouseRecordDTO outWarehouseRecordDTO) {
        this.setRecordCode(outWarehouseRecordDTO.getRecordCode());
        this.setRealWarehouseId(realWarehouseE.getId());
        this.setRecordType(outWarehouseRecordDTO.getRecordType());
        this.setBusinessType(WarehouseRecordBusinessTypeVO.OUT_WAREHOUSE_RECORD.getType());
        this.setRecordStatus(WarehouseRecordStatusVO.INIT.getStatus());
        this.setSapOrderCode(outWarehouseRecordDTO.getSapOrderCode());
//        this.setMerchantId(outWarehouseRecordDTO.getMerchantId());
//        this.setOutCreateTime(outWarehouseRecordDTO.getOutCreateTime());
        this.setSyncWmsStatus(WmsSyncStatusVO.NO_REQUIRED.getStatus());
        this.setSyncTradeStatus(WarehouseRecordConstant.NEED_SYNC_TRADE);
        //需要派车
        this.setSyncDispatchStatus(WarehouseRecordConstant.NEED_DISPATCH);
        //这里要改为在确认门店退货里面标记为待扣减状态,这里暂时标记为无需扣减
        this.setBatchStatus(WarehouseRecordBatchStatusVO.NO_HANDLING.getStatus());
        //插入单据
        this.addRecord(outWarehouseRecordDTO);
    }

    /**
     * 大仓入库单
     */
    public void warehouseInRecord(RealWarehouseE realWarehouseE, InWarehouseRecordDTO inWarehouseRecordDTO) {
        this.setRecordCode(inWarehouseRecordDTO.getRecordCode());
        this.setRecordType(inWarehouseRecordDTO.getRecordType());
        this.setRealWarehouseId(realWarehouseE.getId());
        this.setBusinessType(WarehouseRecordBusinessTypeVO.IN_WAREHOUSE_RECORD.getType());
        this.setRecordStatus(WarehouseRecordStatusVO.INIT.getStatus());
        this.setSyncWmsStatus(WmsSyncStatusVO.UNSYNCHRONIZED.getStatus());
        this.setSyncTradeStatus(WarehouseRecordConstant.NEED_SYNC_TRADE);
        this.setSapOrderCode(inWarehouseRecordDTO.getSapOrderCode());
        this.setCmpStatus(WarehouseRecordConstant.INIT_CMP);
        this.setTmsRecordCode(inWarehouseRecordDTO.getTmsRecordCode());
        this.setWarehouseRecordDetails(new ArrayList<>(inWarehouseRecordDTO.getDetailList().size()));
        //单位换算
        for (RecordDetailDTO detailE : inWarehouseRecordDTO.getDetailList()) {
            WarehouseRecordDetail warehouseRecordDetail = entityFactory.createEntity(WarehouseRecordDetail.class);
            warehouseRecordDetail.setSkuCode(detailE.getSkuCode());
            warehouseRecordDetail.setPlanQty(detailE.getBasicSkuQty());
            warehouseRecordDetail.setUnit(detailE.getBasicUnit());
            warehouseRecordDetail.setUnitCode(detailE.getBasicUnitCode());
            //无回调--直接加实际库存
            warehouseRecordDetail.setActualQty(BigDecimal.ZERO);
            warehouseRecordDetail.setSapPoNo(detailE.getSapPoNo());
            warehouseRecordDetail.setLineNo(detailE.getLineNo());
            warehouseRecordDetail.setDeliveryLineNo(detailE.getDeliveryLineNo());
            warehouseRecordDetail.setDeliveryData(new Date());
            this.warehouseRecordDetails.add(warehouseRecordDetail);
        }
        //设置skuCode和skuID
        this.initWarehouseRecodeDetail();
        long id = shopReturnWarehouseRepository.saveReturnWarehouseRecord(this);
        this.setId(id);
        this.warehouseRecordDetails.forEach(wrRecord -> wrRecord.setWarehouseRecordDetail(this));
        shopReturnWarehouseRepository.saveWarehouseRecordDetails(this.warehouseRecordDetails);
    }



    /**
     * 组装明细并插入数据库
     *
     * @param outWarehouseRecordDTO
     */
    private void addRecord(OutWarehouseRecordDTO outWarehouseRecordDTO) {
        List<RecordDetailDTO> frontRecordDetails = outWarehouseRecordDTO.getDetailList();
        this.setWarehouseRecordDetails(new ArrayList<>(frontRecordDetails.size()));
        for (RecordDetailDTO detailE : frontRecordDetails) {
            WarehouseRecordDetail warehouseRecordDetail = entityFactory.createEntity(WarehouseRecordDetail.class);
            warehouseRecordDetail.setSkuCode(detailE.getSkuCode());
            warehouseRecordDetail.setPlanQty(detailE.getBasicSkuQty());
            warehouseRecordDetail.setUnit(detailE.getBasicUnit());
            warehouseRecordDetail.setUnitCode(detailE.getBasicUnitCode());
            //批次信息没有用到，已删除
            //此处设置计划数量和实际数量一致，wms回调再更新实际数量，无需wms回调的业务，直接设置成一致即可
            warehouseRecordDetail.setActualQty(detailE.getBasicSkuQty());
            warehouseRecordDetail.setSapPoNo(detailE.getSapPoNo());
            warehouseRecordDetail.setDeliveryLineNo(detailE.getDeliveryLineNo());
            warehouseRecordDetail.setLineNo(detailE.getLineNo());
            //TODO 暂时写为空
            warehouseRecordDetail.setDeliveryData(null);
            this.warehouseRecordDetails.add(warehouseRecordDetail);
        }
        //设置skuCode和skuID
        this.initWarehouseRecodeDetail();
        long id = shopReturnWarehouseRepository.saveReturnWarehouseRecord(this);
        this.setId(id);
        this.warehouseRecordDetails.forEach(wrRecord -> wrRecord.setWarehouseRecordDetail(this));
        shopReturnWarehouseRepository.saveWarehouseRecordDetails(this.warehouseRecordDetails);
    }


    /**
     * 封装取消减少在途库存
     */
    public CoreRealStockOpDO packUnlockOnRoadStock(){
        List<CoreRealStockOpDetailDO> detailDos = new ArrayList<>();
        for (WarehouseRecordDetail detail : this.getWarehouseRecordDetails()) {
            //实收数量为0的不处理在途的增加和减少
            if(BigDecimal.ZERO.compareTo(detail.getPlanQty()) == 0){
                continue;
            }
            CoreRealStockOpDetailDO detailDO = new CoreRealStockOpDetailDO();
            detailDO.setOnroadQty(detail.getPlanQty());
            detailDO.setSkuId(detail.getSkuId());
            detailDO.setSkuCode(detail.getSkuCode());
            detailDO.setRealWarehouseId(this.getRealWarehouseId());
            detailDO.setChannelCode(detail.getChannelCode());
            detailDos.add(detailDO);
        }
        CoreRealStockOpDO stockDO = new CoreRealStockOpDO();
        stockDO.setRecordCode(this.getRecordCode());
        stockDO.setTransType(this.getRecordType());
        stockDO.setDetailDos(detailDos);
        return stockDO;
    }
    /**
     * SAP派车状态：0-无需派车 1-待下发派车 2-已下发派车
     */
    private Integer syncDispatchStatus;

}
