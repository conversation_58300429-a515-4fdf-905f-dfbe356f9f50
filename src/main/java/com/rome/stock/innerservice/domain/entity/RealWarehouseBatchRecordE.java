package com.rome.stock.innerservice.domain.entity;

import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

@Component
@Scope("prototype")
@Data
@EqualsAndHashCode(callSuper = false)
public class RealWarehouseBatchRecordE extends BaseE {
    /**
     * 主键
     */
    private Long id;
    /**
     * 出入库单据编号
     */
    private String recordCode;
    /**
     * 前置单号
     */
    private String frontRecordCode;
    /**
     * 检验单号
     */
    private String qualityCode;
    /**
     * wms收货单编号
     */
    private String wmsRecordCode;
    /**
     * 工厂编码
     */
    private String factoryCode;
    /**
     * 仓库id
     */
    private Long realWarehouseId;
    /**
     * 仓库code
     */
    private String realWarehouseCode;
    /**
     * 仓库名称
     */
    private String realWarehouseName;
    /**
     * 商品id
     */
    private Long skuId;
    /**
     * 商品编号
     */
    private String skuCode;
    /**
     * 商品名称
     */
    private String skuName;
    /**
     * 商品skuId集合
     */
    private List<Long> skuIds;
    /**
     * 商品实际出入库数量
     */
    private BigDecimal actualQty;
    /**
     * 批次编号
     */
    private String batchCode;
    /**
     * 质检状态
     */
    private Integer qualityStatus;
    /**
     * 业务类型
     */
    private Integer businessType;
    /**
     * 库存类型
     */
    private Integer inventoryType;
    /**
     * 生产日期
     */
    private Date productDate;
    /**
     * 开始时间
     */
    private Date startTime;
    /**
     * 结束时间
     */
    private Date endTime;

    private String sapLineNo;
}



