package com.rome.stock.innerservice.domain.entity;

import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Component;

@Component
@Scope("prototype")
@Data
@EqualsAndHashCode(callSuper = false)
public class DeliverRelationE extends BaseE {
	/**
	 * 单据子表id
	 */
	private Long id;
	/**
	 * Z工厂code
	 */
	private String factoryCode;
	/**
	 * Z工厂name
	 */
	private String factoryName;
	/**
	 * 发货工厂code
	 */
	private String deliverFactoryCode;
	/**
	 * 发货工厂name
	 */
	private String deliverFactoryName;
}



