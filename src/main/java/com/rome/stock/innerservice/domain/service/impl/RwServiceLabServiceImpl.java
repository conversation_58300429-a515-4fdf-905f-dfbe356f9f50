package com.rome.stock.innerservice.domain.service.impl;

import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.rome.arch.core.exception.RomeException;
import com.rome.stock.innerservice.api.dto.RealWarehouse;
import com.rome.stock.innerservice.api.dto.cloudshop.RwServiceLabDTO;
import com.rome.stock.innerservice.api.dto.cloudshop.RwServiceLabUpdateDTO;
import com.rome.stock.innerservice.api.dto.cloudshop.ServiceLabDTO;
import com.rome.stock.innerservice.common.RomeCollectionUtil;
import com.rome.stock.innerservice.constant.RealWarehouseTypeVO;
import com.rome.stock.innerservice.domain.convertor.RealWarehouseConvertor;
import com.rome.stock.innerservice.domain.convertor.cloudshop.CloudShopConvertor;
import com.rome.stock.innerservice.domain.entity.RealWarehouseE;
import com.rome.stock.innerservice.domain.repository.RealWarehouseRepository;
import com.rome.stock.innerservice.domain.repository.RwServiceLabRepository;
import com.rome.stock.innerservice.domain.service.RwServiceLabService;
import com.rome.stock.innerservice.domain.service.ServiceLabService;
import com.rome.stock.innerservice.infrastructure.dataobject.RealWarehouseDO;
import com.rome.stock.innerservice.infrastructure.dataobject.RwServiceLabDO;
import com.rome.stock.innerservice.infrastructure.mapper.RwServiceLabMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

/**
 * RwServiceLabService 实现
 *
 * <AUTHOR>
 * @date 2021-09-14
 */
@Slf4j
@Service
public class RwServiceLabServiceImpl implements RwServiceLabService {

    @Resource
    private RwServiceLabRepository rwServiceLabRepository;
    @Resource
    private RwServiceLabMapper rwServiceLabMapper;
    @Resource
    private ServiceLabService serviceLabService;
    @Resource
    private CloudShopConvertor cloudShopConvertor;
    @Resource
	private RealWarehouseRepository realWarehouseRepository;
	@Autowired
	private RealWarehouseConvertor realWarehouseConvertor;
    /**
     * 根据实仓id查询服务标签
     *
     * @param realWarehouseId
     * @return
     */
    @Override
    public List<RwServiceLabDTO> getByRwId(Long realWarehouseId) {
        return this.listAll(Arrays.asList(realWarehouseId));
    }

    @Override
    public List<RwServiceLabDTO> listAll(List<Long> ids) {
        List<RwServiceLabDO> rwServiceLabList = rwServiceLabRepository.listAll(ids);
        List<ServiceLabDTO> allService = serviceLabService.listAll();
        Map<Long, ServiceLabDTO> serviceLabMap = RomeCollectionUtil.listforMap(allService, "id");
        List<RwServiceLabDTO> result = cloudShopConvertor.coverRwLabDoListToDtoList(rwServiceLabList);
        for (RwServiceLabDTO dto : result) {
            ServiceLabDTO serviceLabDTO = serviceLabMap.get(dto.getLabId());
            if(serviceLabDTO != null){
                dto.setLabName(serviceLabDTO.getLabName());
                dto.setLabType(serviceLabDTO.getLabType());
                dto.setLabDesc(serviceLabDTO.getLabDesc());
                dto.setBusinessType(serviceLabDTO.getBusinessType());
            }
        }
        return result;
    }
    
    private static final BigDecimal MIN_VALUE_ZERO = new BigDecimal("0.09");

	@Override
	@Transactional(rollbackFor = Exception.class)
	public void addOrUpdate(List<RwServiceLabDTO> list) {
		if(list == null || list.size() == 0) {
			throw new RomeException("999","至少一条数据");
		}
		Long modifier=list.get(0).getModifier();
		List<RwServiceLabDO> doList = cloudShopConvertor.coverRwLabDtoListToDoList(list);
	    // 校验数据 start
		List<ServiceLabDTO> serviceLabDTOs = serviceLabService.listAll();
		Set<Long> ladIdSet = new HashSet<>(16);
		List<Long> realWarehouseIdList = new ArrayList<>();
		if(serviceLabDTOs != null) {
			for(ServiceLabDTO dto : serviceLabDTOs) {
				ladIdSet.add(dto.getId());
			}
		}
		for(RwServiceLabDO dto : doList) {
			if(dto.getRealWarehouseId() == null) {
				throw new RomeException("999","仓库Id不能为空");
			}
			if(dto.getLabId() == null) {
				throw new RomeException("999","服务标签为空");
			}
			if(!ladIdSet.contains(dto.getLabId())) {
				throw new RomeException("999","服务标签不存在或不支持");
			}
			if(dto.getPrescription() == null || MIN_VALUE_ZERO.compareTo(dto.getPrescription()) >= 0) {
				throw new RomeException("999","时效为空或小于最小值");
			}
			if(!realWarehouseIdList.contains(dto.getRealWarehouseId())) {
				realWarehouseIdList.add(dto.getRealWarehouseId());
			}
			if(dto.getStartOffTime() == null) {
				throw new RomeException("999","开始截单时间不能为空");
			}
			if(dto.getEndOffTime() == null) {
				throw new RomeException("999","结束截单时间不能为空");
			}
			if(dto.getDeliveryOffStatus() == null) {
				throw new RomeException("999","截单后配送状态不能为空");
			}
			if(dto.getDeliveryOffStatus() != 0 && dto.getDeliveryOffStatus() != 1) {
				throw new RomeException("999","截单后配送状态不正确");
			}
		}
		List<RealWarehouseE> warehouseES = realWarehouseRepository.getRealWarehouseByIds(realWarehouseIdList);
		Map<Long, RealWarehouseE> warehouseEMap = RomeCollectionUtil.listforMap(warehouseES, "id");
		RealWarehouseE realWarehouseE=null;
		for(RwServiceLabDO dto : doList) {
			realWarehouseE = warehouseEMap.get(dto.getRealWarehouseId());
			if(realWarehouseE == null) {
				throw new RomeException("999","仓库未查询到实体仓库");
			}
			if(!RealWarehouseTypeVO.CLOUD_SUPPLIER_TYPE_29.getType().equals(realWarehouseE.getRealWarehouseType()) && !RealWarehouseTypeVO.RW_TYPE_1.getType().equals(realWarehouseE.getRealWarehouseType())) {
				throw new RomeException("999","仓库编码对应的实体仓库不是云店供应商仓也不是门店仓:" + realWarehouseE.getRealWarehouseCode());
			}
			if(dto.getPrescription().scale() > 1) {
				dto.setPrescription(dto.getPrescription().setScale(1, BigDecimal.ROUND_DOWN));
			}
			if(dto.getPrescriptionOff() != null) {
				if(dto.getPrescriptionOff().scale() > 1) {
					dto.setPrescriptionOff(dto.getPrescriptionOff().setScale(1, BigDecimal.ROUND_DOWN));
				}
			}
		}
		// 校验数据 end
		// 已存在的
		List<RwServiceLabDO> existList = rwServiceLabMapper.getListAllByRwIds(realWarehouseIdList,list.get(0).getLabIds());
		List<RwServiceLabDO> updateList = new ArrayList<>();
		if(existList != null && existList.size() > 0) {
			// 数据库存在，现在不选了
			Long creator = doList.get(0).getCreator();
			for(RwServiceLabDO dto : existList) {
				if(existListByDo(doList, dto) == null) {
					dto.setIsAvailable((byte)0);
					dto.setIsDeleted((byte)1);
					dto.setCreator(creator);
					updateList.add(dto);
				}
			}
			RwServiceLabDO temp,temp2;
			for (int i = doList.size() - 1; i >= 0; i--) {
				temp2 = doList.get(i);
				temp = existListByDo(existList, temp2);
				if(temp != null) {
					temp2.setId(temp.getId());
					temp2.setIsAvailable((byte)1);
					temp2.setIsDeleted((byte)0);
					updateList.add(temp2);
					doList.remove(i);
				}
			}
		}
		if(updateList.size() > 0) {
			int index = rwServiceLabMapper.batchUpdate(updateList);
			if(index != updateList.size()) {
				throw new RomeException("999","失败");
			}
		}
        if(doList.size() > 0) {
        	rwServiceLabMapper.batchInsert(doList);
		}
	}

    @Override
    public PageInfo<RealWarehouse> queryServiceLabWarehouse(ServiceLabDTO dto) {
		Page page = PageHelper.startPage(dto.getPageIndex(), dto.getPageSize());
		List<RealWarehouseDO> realWarehouseIds=rwServiceLabMapper.queryListByPage(dto);
		if(CollectionUtils.isEmpty(realWarehouseIds)){
			PageInfo<RealWarehouse> personPageInfo = new PageInfo<>();
			return personPageInfo;
		}
		PageInfo<RealWarehouse> personPageInfo = new PageInfo<>(realWarehouseConvertor.dosToDTOs(realWarehouseIds));
		personPageInfo.setTotal(page.getTotal());
		return personPageInfo;
    }

	@Override
//	@Transactional(rollbackFor = Exception.class)
	public void addOrUpdateBatch(RwServiceLabUpdateDTO rwServiceLabUpdateDTO) {
		List<Long> realWarehouseIdList=rwServiceLabUpdateDTO.getRealWarehouseIdList();
		if(CollectionUtils.isEmpty(rwServiceLabUpdateDTO.getRealWarehouseIdList())){
			List<RealWarehouseE> realWarehouseList = realWarehouseRepository.queryRealWarehouseByRWType(rwServiceLabUpdateDTO.getBusinessType()==3?RealWarehouseTypeVO.CLOUD_SUPPLIER_TYPE_29.getType():RealWarehouseTypeVO.RW_TYPE_1.getType());
			realWarehouseIdList=realWarehouseList.stream().map(RealWarehouseE::getId).collect(Collectors.toList());
		}
		for(Long id:realWarehouseIdList){
			rwServiceLabUpdateDTO.getList().forEach(e->e.setRealWarehouseId(id));
			this.addOrUpdate(rwServiceLabUpdateDTO.getList());
		}
	}


	/**
     * 是否存在列表中
     * @param list
     * @param dto
     * @return
     */
    private RwServiceLabDO existListByDo(List<RwServiceLabDO> list, RwServiceLabDO dto) {
    	for(RwServiceLabDO dto2 : list) {
    		if(dto.getRealWarehouseId().equals(dto2.getRealWarehouseId()) && dto.getLabId().equals(dto2.getLabId())) {
    			return dto2;
    		}
    	}
    	return null;
    }
}
