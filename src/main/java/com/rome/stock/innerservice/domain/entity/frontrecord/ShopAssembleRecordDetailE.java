package com.rome.stock.innerservice.domain.entity.frontrecord;

import java.math.BigDecimal;

import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Component;

import lombok.Data;
import lombok.EqualsAndHashCode;

@Component
@Scope("prototype")
@Data
@EqualsAndHashCode(callSuper = false)
public class ShopAssembleRecordDetailE extends AbstractFrontRecordDetail{
    /**
     * 单位
     */
    private String unit;

    /**
     * 拆品数量
     */
    private BigDecimal spitSkuQty;

    /**
     * 拆品sku编号
     */
    private Long spitSkuId;

    /**
     * 拆品skucode
     */
    private String spitSkuCode;


    /**
     * 拆品单位
     */
    private String spitUnit;

    /**
     * 拆品单位code
     */
    private String spitUnitCode;
}
