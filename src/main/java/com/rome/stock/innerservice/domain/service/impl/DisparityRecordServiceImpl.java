package com.rome.stock.innerservice.domain.service.impl;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

import javax.annotation.Resource;

import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang.time.DateFormatUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.alibaba.fastjson.JSON;
import com.rome.arch.core.domain.EntityFactory;
import com.rome.arch.core.exception.RomeException;
import com.rome.stock.common.constants.warehouse.WarehouseRecordConstant;
import com.rome.stock.common.enums.warehouse.WarehouseRecordTypeVO;
import com.rome.stock.innerservice.api.dto.warehouserecord.BatchCreateRecordDTO;
import com.rome.stock.innerservice.api.dto.warehouserecord.CancelRecordDTO;
import com.rome.stock.innerservice.api.dto.warehouserecord.InWarehouseRecordDTO;
import com.rome.stock.innerservice.api.dto.warehouserecord.OutWarehouseRecordDTO;
import com.rome.stock.innerservice.common.AlikAssert;
import com.rome.stock.innerservice.common.ResCode;
import com.rome.stock.innerservice.common.RomeCollectionUtil;
import com.rome.stock.innerservice.constant.DisparityResponsibleTypeVO;
import com.rome.stock.innerservice.constant.WarehouseRecordBatchStatusVO;
import com.rome.stock.innerservice.domain.entity.DisparityWarehouseRecordE;
import com.rome.stock.innerservice.domain.entity.RealWarehouseE;
import com.rome.stock.innerservice.domain.entity.warehouserecord.WarehouseRecordDetail;
import com.rome.stock.innerservice.domain.entity.warehouserecord.WarehouseRecordE;
import com.rome.stock.core.domain.repository.CoreRealWarehouseStockRepository;
import com.rome.stock.innerservice.domain.repository.RealWarehouseRepository;
import com.rome.stock.innerservice.domain.service.DisparityRecordService;
import com.rome.stock.core.facade.RedisRollBackFacade;
import com.rome.stock.core.infrastructure.dataobject.core.CoreRealStockOpDO;
import com.rome.stock.innerservice.remote.item.dto.SkuInfoExtDTO;
import com.rome.stock.innerservice.remote.item.facade.SkuFacade;
import com.rome.stock.innerservice.remote.orderCneter.dto.CommonFrontRecordDTO;
import com.rome.stock.innerservice.remote.orderCneter.dto.CommonFrontRecordDetailDTO;
import com.rome.stock.innerservice.remote.sap.dto.KC027DTO;

import lombok.extern.slf4j.Slf4j;

@Service
@Slf4j
public class DisparityRecordServiceImpl implements DisparityRecordService {
    @Resource
    private EntityFactory entityFactory;
    @Resource
    private CoreRealWarehouseStockRepository coreRealWarehouseStockRepository;
    @Resource
    private RealWarehouseRepository realWarehouseRepository;


    @Resource
    private SkuFacade skuFacade;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void createOutAndInRecordBatchForDisparity(BatchCreateRecordDTO list) {
        String json = JSON.toJSONString(list);
        List<CoreRealStockOpDO> rollBackList = new ArrayList<>();
        boolean isSucc = false;
        try {
            if(CollectionUtils.isNotEmpty(list.getOutList())) {
                for (OutWarehouseRecordDTO out : list.getOutList()) {
                    handleDecreaseStock(out);
                }
            }
            if(CollectionUtils.isNotEmpty(list.getInList())) {
                for (InWarehouseRecordDTO in : list.getInList()) {
                    handleIncreaseStock(in, rollBackList);
                }
            }
            isSucc = true;
        } catch (Exception e) {
            log.error(json + e.getMessage(), e);
            throw e;
        } finally {
            if (!isSucc) {
                for (CoreRealStockOpDO coreRealStockOpDO : rollBackList) {
                    RedisRollBackFacade.redisRollBack(coreRealStockOpDO);
                }
            }
        }


    }

    @Override
    public List<KC027DTO> wrapDisparityData(WarehouseRecordE record){
        CommonFrontRecordDTO commonFrontRecord = record.getCommonFrontRecord();

        log.info("后置单单号{},差异过账查询的前置单信息{}", record.getRecordCode(), JSON.toJSONString(commonFrontRecord));
        boolean isReturn = commonFrontRecord.getIsReverse();
        List<KC027DTO> result = new ArrayList<>();
        String retpo = null ;
        String shopCode;
        String factory = null;
        String warehouseCode = null;
        if (isReturn) {
            retpo = "X";
            factory = commonFrontRecord.getInFactoryCode();
            warehouseCode = commonFrontRecord.getInRealWarehouseCode();
        } else {
            factory = commonFrontRecord.getOutFactoryCode();
            warehouseCode = commonFrontRecord.getOutRealWarehouseCode();
        }
        shopCode = commonFrontRecord.getShopCode();

        Map<String, SkuInfoExtDTO> skuInfoExtDTOMap = null;
        //过滤正向门店责任数据
        if(!isReturn && WarehouseRecordTypeVO.DISPARITY_SHOP_IN_RECOED11.getType().equals(record.getRecordType())) {
            if(CollectionUtils.isNotEmpty(record.getWarehouseRecordDetails())) {
                List<String> skuCodes = record.getWarehouseRecordDetails().stream().map(WarehouseRecordDetail:: getSkuCode).distinct().collect(Collectors.toList());
                if(CollectionUtils.isNotEmpty(skuCodes)) {
                    List<SkuInfoExtDTO> skuInfoExtDTOList = skuFacade.skusBySkuCode(skuCodes);
                    skuInfoExtDTOMap = skuInfoExtDTOList.stream().collect(Collectors.toMap(SkuInfoExtDTO :: getSkuCode, Function.identity(), (v1, v2) -> v1));
                }
            }
        }

        Map<String , CommonFrontRecordDetailDTO> frontMap = RomeCollectionUtil.listforMap(commonFrontRecord.getFrontRecordDetails(),"keyForDisparity");
        for (WarehouseRecordDetail detail : record.getWarehouseRecordDetails()) {
            KC027DTO disparitySapDTO = new KC027DTO();
            CommonFrontRecordDetailDTO frontDetail = frontMap.get(detail.getSapPoNo() + "_" + detail.getLineNo());
            if(frontDetail == null){
                throw  new RomeException(ResCode.STOCK_ERROR_1002 ,ResCode.STOCK_ERROR_1002_DESC + "后置单明细在前置单不存在：" + detail.getSapPoNo() + "_" + detail.getLineNo());
            }
            if(frontDetail.getInSkuQty() == null || frontDetail.getOutSkuQty() == null){
                throw  new RomeException(ResCode.STOCK_ERROR_1002 ,ResCode.STOCK_ERROR_1002_DESC + "前置端没有回传出库数量或入库数量" + detail.getSapPoNo() + "_" + detail.getLineNo());
            }
            //中台差异单号
            disparitySapDTO.setBillNo(commonFrontRecord.getRecordCode());
            //差异单行号
            disparitySapDTO.setBillNum(frontDetail.getId());
            //是否退货
            if (isReturn) {
                disparitySapDTO.setRetpo(retpo);
            }
            Integer responsibleType = frontDetail.getResponsibleType();
            if (DisparityResponsibleTypeVO.SHOP.getType().equals(responsibleType)) {
                //门店责任
                disparitySapDTO.setMode("01");
                if(MapUtils.isNotEmpty(skuInfoExtDTOMap)) {
                    if(skuInfoExtDTOMap.containsKey(detail.getSkuCode())) {
                        SkuInfoExtDTO skuInfoExtDTO = skuInfoExtDTOMap.get(detail.getSkuCode());
                        //物料类型非Z001,Z002,Z003,传值移动类型Y13-对应的成本中心为9+门店号
                        if(!"Z001".equals(skuInfoExtDTO.getSpuType()) && !"Z002".equals(skuInfoExtDTO.getSpuType()) && !"Z003".equals(skuInfoExtDTO.getSpuType())) {
                            disparitySapDTO.setKostl("9" + shopCode);
                        }
                    }
                }

            } else if (DisparityResponsibleTypeVO.WAREHOUSE.getType().equals(responsibleType)) {
                //仓库责任
                disparitySapDTO.setMode("02");
            } else if (DisparityResponsibleTypeVO.LOGISTIC.getType().equals(responsibleType)) {
                //物流责任
                disparitySapDTO.setMode("03");
                if (null == frontDetail.getCostCenter() || null == frontDetail.getRemark()) {
                    throw new RomeException(ResCode.STOCK_ERROR_1003, ResCode.STOCK_ERROR_1003_DESC + "物流责任时 备注和成本中心不能空：" + record.getRecordCode());
                }
                disparitySapDTO.setKostl(frontDetail.getCostCenter());
                //传承运商信息todo
                disparitySapDTO.setText4(frontDetail.getRemark());
            } else {
                throw new RomeException(ResCode.STOCK_ERROR_1003, ResCode.STOCK_ERROR_1003_DESC + ":责任类型值有问题[只能是1/2/3中一种]" + record.getRecordCode());
            }
            disparitySapDTO.setMaterial(detail.getSkuCode());
            disparitySapDTO.setDelivQtyDiff(detail.getActualQty());
            disparitySapDTO.setText1(DateFormatUtils.format(detail.getCreateTime(), "yyyyMMdd"));
            //正向的门店责任无需传仓库信息
            if (isReturn) {
                //出库数量
                disparitySapDTO.setErfmg(frontDetail.getInSkuQty());
                //入库数量
                disparitySapDTO.setMeins(frontDetail.getOutSkuQty());
                disparitySapDTO.setLgort(warehouseCode);
                disparitySapDTO.setWerks(factory);
            } else {
                //出库数量
                disparitySapDTO.setErfmg(frontDetail.getOutSkuQty());
                //入库数量
                disparitySapDTO.setMeins(frontDetail.getInSkuQty());
                if (!DisparityResponsibleTypeVO.SHOP.getType().equals(responsibleType)) {
                    if (null == frontDetail.getHandlerInRealWarehouseOutCode() || null == frontDetail.getHandlerInFactoryCode()) {
                        throw new RomeException(ResCode.STOCK_ERROR_1003, ResCode.STOCK_ERROR_1003_DESC + ":正向仓库或物流责任时 中转仓不能空：" + record.getRecordCode());
                    }
                    disparitySapDTO.setLgort(frontDetail.getHandlerInRealWarehouseOutCode());
                    disparitySapDTO.setWerks(frontDetail.getHandlerInFactoryCode());
                }
            }
            disparitySapDTO.setMenge(detail.getUnitCode());
            disparitySapDTO.setStore(shopCode);
            //930用po号换成do号
            disparitySapDTO.setEBELN(detail.getSapPoNo());
            disparitySapDTO.setEBELP(detail.getLineNo());
            //930用po号换成do号
            if (isReturn) {
                //对于退货来说，ZEBELN都要传do号，而差异明细表存的是po号和po行号
                disparitySapDTO.setZEBELN(commonFrontRecord.getOutWarehouseRecordCode());
                disparitySapDTO.setZEBELP(detail.getLineNo());
            } else {
                //ZEBELN都要传do号，而差异明细表存的是do号和do行号
                disparitySapDTO.setZEBELN(detail.getSapPoNo());
                disparitySapDTO.setZEBELP(detail.getLineNo());
            }

            result.add(disparitySapDTO);
        }
        return result;

    }
    /**
     * 批量取消do单
     * @param list
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void cancelBatch(List<CancelRecordDTO> list) {
        String json = JSON.toJSONString(list);
        List<CoreRealStockOpDO> rollBackList = new ArrayList<>();
        boolean isSucc = false;
        try {
            for (CancelRecordDTO cancelRecordDTO : list) {
                DisparityWarehouseRecordE warehouseRecordE = entityFactory.createEntity(DisparityWarehouseRecordE.class);
                warehouseRecordE.cancelWarehouseRecord(cancelRecordDTO, rollBackList);
            }
            isSucc = true;
        } catch (Exception e) {
            log.error( json + e.getMessage(), e);
            throw e;
        } finally {
            if (!isSucc) {
                for (CoreRealStockOpDO coreRealStockOpDO : rollBackList) {
                    RedisRollBackFacade.redisRollBack(coreRealStockOpDO);
                }
            }
        }
    }

    private void handleIncreaseStock(InWarehouseRecordDTO inDto, List<CoreRealStockOpDO> rollBackList) {
        DisparityWarehouseRecordE warehouseRecordE = entityFactory.createEntity(DisparityWarehouseRecordE.class);
        RealWarehouseE realWarehouseE = realWarehouseRepository.getRealWarehouseByOutCodeAndFactoryCode(inDto.getWarehouseCode(), inDto.getFactoryCode());
        AlikAssert.isNotNull(realWarehouseE, ResCode.STOCK_ERROR_1002, "实仓不存在");
        WarehouseRecordTypeVO typeVO = WarehouseRecordTypeVO.getByType(inDto.getRecordType());
        AlikAssert.isNotNull(typeVO, ResCode.STOCK_ERROR_1002, "单据类型不支持");
        if(!WarehouseRecordTypeVO.DISPARITY_SHOP_IN_RECOED11.getType().equals(typeVO.getType())
                && !WarehouseRecordTypeVO.DISPARITY_TRANSFER_WAREHOUSE_IN_RECOED12.getType().equals(typeVO.getType())
                && !WarehouseRecordTypeVO.DISPARITY_TRANSFER_WAREHOUSE_IN_RECOED13.getType().equals(typeVO.getType())
                && !WarehouseRecordTypeVO.DISPARITY_WAREHOUSE_IN_RECOED22.getType().equals(typeVO.getType())
                && !WarehouseRecordTypeVO.DISPARITY_WAREHOUSE_IN_RECOED23.getType().equals(typeVO.getType())
                && !WarehouseRecordTypeVO.DISPARITY_SHOP_IN_RECOED21.getType().equals(typeVO.getType())){
            throw  new RomeException(ResCode.STOCK_ERROR_1002, "单据类型不支持");
        }
        if (inDto.getOutWarehouseRecordCode() == null) {
            throw new RomeException(ResCode.STOCK_ERROR_1002, "差异单对应的大仓出库单单号必传");
        }
        warehouseRecordE.createWarehouseRecord(inDto.getRecordCode(), typeVO, realWarehouseE.getId(), inDto.getDetailList());
        //全部用入库单过账
        warehouseRecordE.setSyncTransferStatus(WarehouseRecordConstant.NEED_TRANSFER);
        warehouseRecordE.addRecord();
        if (typeVO.getType().equals(WarehouseRecordTypeVO.DISPARITY_TRANSFER_WAREHOUSE_IN_RECOED13.getType())
                || typeVO.getType().equals(WarehouseRecordTypeVO.DISPARITY_WAREHOUSE_IN_RECOED23.getType())) {
            //物流责任时不加库存
            return;
        }

        CoreRealStockOpDO coreRealStockOpDO = null;
        try {
            //增加门店库存(按照真实库存)
            coreRealStockOpDO = warehouseRecordE.initStockObj(warehouseRecordE);
            coreRealWarehouseStockRepository.increaseRealQty(coreRealStockOpDO);
            if (typeVO.getType().equals(WarehouseRecordTypeVO.DISPARITY_SHOP_IN_RECOED11.getType())
                    || typeVO.getType().equals(WarehouseRecordTypeVO.DISPARITY_SHOP_IN_RECOED21.getType())) {

                if (inDto.getOutWarehouseRecordCode() == null) {
                    throw new RomeException(ResCode.STOCK_ERROR_1002, "门店责任时差异单对应的大仓出库单单号必传");
                }

                //需要增加批次
                // 库存
                warehouseRecordE.setBatchStatus(WarehouseRecordBatchStatusVO.INIT.getStatus());
                //增加批次库存
//                WarehouseBatchStockE warehouseBatchStockE = entityFactory.createEntity(WarehouseBatchStockE.class);
//                warehouseBatchStockE.dealShopBatch(inDto.getOutWarehouseRecordCode(), warehouseRecordE, true);
//                warehouseBatchStockE.increaseBatchStock(warehouseRecordE);
            }
        } catch (RomeException e) {
            throw new RomeException(e.getCode(), e.getMessage());
        } catch (Exception e) {
            log.error(e.getMessage(),e);
            throw new RomeException(ResCode.STOCK_ERROR_1003, ResCode.STOCK_ERROR_1003_DESC);
        } finally {
            if (coreRealStockOpDO != null) {
                rollBackList.add(coreRealStockOpDO);
            }
        }
    }

    private void handleDecreaseStock(OutWarehouseRecordDTO outDto) {
        //只有物流责任时 才有可能走到这里，而物流责任又不需要真实的扣减库存。只需要记录一下单据即可，过账也是用入库单过账
        DisparityWarehouseRecordE warehouseRecordE = entityFactory.createEntity(DisparityWarehouseRecordE.class);
        RealWarehouseE realWarehouseE = realWarehouseRepository.getRealWarehouseByOutCodeAndFactoryCode(outDto.getWarehouseCode(), outDto.getFactoryCode());
        AlikAssert.isNotNull(realWarehouseE, ResCode.STOCK_ERROR_1002, "实仓不存在");
        WarehouseRecordTypeVO typeVO = WarehouseRecordTypeVO.getByType(outDto.getRecordType());
        AlikAssert.isNotNull(typeVO, ResCode.STOCK_ERROR_1002, "单据类型不支持");
        if(!WarehouseRecordTypeVO.DISPARITY_TRANSFER_WAREHOUSE_OUT_RECOED13.getType().equals(typeVO.getType())
                && !WarehouseRecordTypeVO.DISPARITY_WAREHOUSE_OUT_RECOED23.getType().equals(typeVO.getType())){
            throw  new RomeException(ResCode.STOCK_ERROR_1002, "单据类型不支持");
        }
        warehouseRecordE.createWarehouseRecord(outDto.getRecordCode(), typeVO, realWarehouseE.getId(), outDto.getDetailList());
        warehouseRecordE.addRecord();
    }
}
