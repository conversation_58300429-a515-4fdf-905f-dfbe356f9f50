package com.rome.stock.innerservice.domain.service.impl;

import com.rome.arch.core.exception.RomeException;
import com.rome.stock.innerservice.api.dto.WDTPageInfoDTO;
import com.rome.stock.innerservice.common.RedisUtil;
import com.rome.stock.innerservice.constant.WDTRecordConst;
import com.rome.stock.innerservice.domain.service.WDTStockService;
import com.rome.stock.innerservice.domain.service.WDTSyncHandleService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Comparator;
import java.util.List;
import java.util.stream.Collectors;

/**
 * @Doc:.....
 * @Author: lchy
 * @Date: 2020/7/6
 * @Version 1.0
 */
@Service
@Slf4j
public class WDTSyncHandleServiceImpl implements WDTSyncHandleService {
    @Resource
    private RedisUtil redisUtil;
    @Resource
    private WDTStockService wdtStockService;

    /**
     * 批量异步处理用户请求操作
     * @param list
     * @param type 1：拆单 2：重新寻源 3：改仓或改物流
     */
    @Override
    public List<String> handleOperate(List<WDTPageInfoDTO> list, Integer type) {
        List<String> res = new ArrayList<>();
        list = list.stream().sorted(Comparator.comparing(WDTPageInfoDTO::getPayTime)).collect(Collectors.toList());
        for (WDTPageInfoDTO wdtPageInfoDTO : list) {
            try {
                if (type == 1) {
                    wdtStockService.splitOrder(wdtPageInfoDTO);
                } else if (type == 2) {
                    wdtStockService.recalculateHouse(wdtPageInfoDTO);
                } else if (type == 3) {
                    wdtStockService.changeHouseAndLogistic(wdtPageInfoDTO);
                }
            } catch (RomeException e) {
                res.add("[" + wdtPageInfoDTO.getOriginOrderCode() + "] :" + e.getMessage());
            } catch (Exception e) {
                res.add("[" + wdtPageInfoDTO.getOriginOrderCode() + "] :" + e.getMessage());
                log.error(wdtPageInfoDTO.getOriginOrderCode() + e.getMessage(), e);
            }
        }
        redisUtil.unLock(WDTRecordConst.WDT_ONLINE_OPERATE_LOCK, WDTRecordConst.CLIENT_ID);
        return res;
    }
}
