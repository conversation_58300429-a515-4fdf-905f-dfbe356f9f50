package com.rome.stock.innerservice.domain.entity;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Component;

/**
 * @Description:
 * <p>
 */
@Data
@Slf4j
@Component
@Scope("prototype")
@EqualsAndHashCode(callSuper = false)
public class ExtInfoE extends BaseE {

    /**
     * 业务单号
     */
    private String businessCode;

    /**
     * 单据编码
     */
    private String recordCode;


    /**
     * 作业人员
     */
    private String workerCode;

    /**
     * 人员类型(自有/临时)
     */
    private String workerType;

    /**
     * 岗位(组长、叉车工...)
     */
    private String workerPosition;

    /**
     * 班组(收货组、白班库存组、辅料仓、退货仓、晚班库存组、晚班拆零组)
     */
    private String classGroup;


    public String getWorkerCode() {
        return jsonObject.getString("workerCode");
    }

    public void setWorkerCode(String workerCode) {
        jsonObject.put("workerCode",workerCode);
    }

    public String getWorkerType() {
        return jsonObject.getString("workerType");
    }

    public void setWorkerType(String workerType) {
        jsonObject.put("workerType",workerType);
    }

    public String getWorkerPosition() {
        return jsonObject.getString("workerPosition");
    }

    public void setWorkerPosition(String workerPosition) {
        jsonObject.put("workerPosition",workerPosition);
    }

    public String getClassGroup() {
        return jsonObject.getString("classGroup");
    }

    public void setClassGroup(String classGroup) {
        jsonObject.put("classGroup",classGroup);
    }

    private String ext="{}";

    private JSONObject jsonObject = JSON.parseObject(ext);

    /**
     *
     * @return json str
     */
    public String getExt(){
        return jsonObject.toJSONString();
    }

    /**
     * ext:json
     * @param ext
     */
    public void setExt(String ext){
        this.jsonObject = JSON.parseObject(ext);
    }

}
