/**
 * Filename StockOpServiceImpl.java
 * Company 上海来伊份科技有限公司。
 *
 * <AUTHOR>
 * @version
 */
package com.rome.stock.innerservice.domain.service.impl;

import com.rome.arch.core.exception.RomeException;
import com.rome.stock.common.utils.DateUtil;
import com.rome.stock.innerservice.api.dto.QueryRealTimeStock;
import com.rome.stock.innerservice.api.dto.RealTimeStock;
import com.rome.stock.innerservice.api.dto.RealWarehouse;
import com.rome.stock.innerservice.api.dto.bigdata.*;
import com.rome.stock.innerservice.common.ResCode;
import com.rome.stock.innerservice.domain.convertor.StockRecordConvertor;
import com.rome.stock.innerservice.domain.repository.RealWarehouseRepository;
import com.rome.stock.innerservice.domain.repository.warehouserecord.KpRwRelationRepository;
import com.rome.stock.innerservice.domain.service.StockPlanService;
import com.rome.stock.innerservice.remote.base.facade.TransactionFacade;
import com.rome.stock.innerservice.remote.bigdata.dto.ArrivalCountDTO;
import com.rome.stock.innerservice.remote.bigdata.dto.BaseQueryDTO;
import com.rome.stock.innerservice.remote.bigdata.dto.ProductSalesDTO;
import com.rome.stock.innerservice.remote.bigdata.dto.ProductStockDTO;
import com.rome.stock.innerservice.remote.bigdata.facade.BigDataFacade;
import com.rome.stock.innerservice.remote.bigdata.facade.BigDataReplenishFacade;
import com.rome.stock.innerservice.remote.item.dto.SkuInfoExtDTO;
import com.rome.stock.innerservice.remote.item.facade.SkuFacade;
import com.rome.stock.innerservice.remote.trade.dto.PoCalcDTO;
import com.rome.stock.innerservice.remote.trade.dto.PoCalcQueryDTO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.beanutils.BeanUtils;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang.time.DateUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.lang.reflect.InvocationTargetException;
import java.math.BigDecimal;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutionException;
import java.util.stream.Collectors;

/**
 * 库存计划查询
 */
@Slf4j
@Service
public class StockPlanServiceImpl implements StockPlanService {

    @Resource
    private BigDataFacade bigDataFacade;

    @Resource
    private RealWarehouseRepository realWarehouseRepository;

    @Resource
    private KpRwRelationRepository kpRwRelationRepository;

    @Resource
    private TransactionFacade transactionFacade;

    @Resource
    private BigDataReplenishFacade bigDataReplenishFacade;

    @Resource
    private SkuFacade skuFacade;

    @Resource
    private StockRecordConvertor stockRecordConvertor;

    @Override
    public List<StockPlanQueryResDTO> queryStockPlanListByPage(StockPlanQueryDTO stockPlanQueryDTO) throws InvocationTargetException, IllegalAccessException, ExecutionException, InterruptedException {
        if (StringUtils.isEmpty(stockPlanQueryDTO.getSkuKey())) {
            throw new RomeException(ResCode.STOCK_ERROR_1003, "商品编号不能为空");
        }
//        if (null == stockPlanQueryDTO.getSkuId()) {
//            throw new RomeException(ResCode.STOCK_ERROR_1003, "商品ID不能为空");
//        }
        if (StringUtils.isEmpty(stockPlanQueryDTO.getFactoryCode())) {
            throw new RomeException(ResCode.STOCK_ERROR_1003, "工厂编号不能为空");
        }
        int j = realWarehouseRepository.countRealWarehouseByFactoryCode(stockPlanQueryDTO.getFactoryCode());
        if (j == 0) {
            throw new RomeException(ResCode.STOCK_ERROR_1003, "工厂编号不存在");
        }
        //分类
        String cate = null;
        //查询商品分类
        List<SkuInfoExtDTO> skuInfoExtDTOList=skuFacade.skusBySkuCode(Arrays.asList(stockPlanQueryDTO.getSkuKey()));
        if(!CollectionUtils.isEmpty(skuInfoExtDTOList)){
            SkuInfoExtDTO skuInfoExtDTO=skuInfoExtDTOList.get(0);
            cate=skuInfoExtDTO.getCategoryName();
            stockPlanQueryDTO.setSkuId(skuInfoExtDTO.getId());
        }

        //渠道默认为ALL
        String channelCode = "ALL";
        stockPlanQueryDTO.setChannel(channelCode);
        stockPlanQueryDTO.setStore(stockPlanQueryDTO.getFactoryCode());
        StockPlanQueryResDTO targetStockDayQueryResDTO = new StockPlanQueryResDTO();
        targetStockDayQueryResDTO.setFactoryCode(stockPlanQueryDTO.getFactoryCode());
        String area = null;

        String skuCode = stockPlanQueryDTO.getSkuKey();
        //获取日期
        List<Map<String, Date>> intervalDate = stockPlanQueryDTO.getWeekDates();
        //目标库存天数
        List<String> targetStockDayList = new ArrayList<>();
        //目标库存
        List<String> targetStockList = new ArrayList<>();
        //安全库存
        List<String> safeStockList = new ArrayList<>();
        //销售预测
        List<String> predictSaleList = new ArrayList<>();
        //实际销量
        List<String> factSaleList = new ArrayList<>();
        //建议补货量
        List<String> execReplishmentList = new ArrayList<>();
        //确认补货量
        List<String> execReplenishList = new ArrayList<>();
        //实际到货量
        List<String> factStockList = new ArrayList<>();
        //在库库存
        List<String> instockList = new ArrayList<>();
        //在途库存
//        List<String> onRoadStockList = new ArrayList<>();
        //预计可销天数
        List<String> predictDayList = new ArrayList<>();
        //库存周转率
        List<String> stockChangeRateList = new ArrayList<>();

        for (int i = 0; i < intervalDate.size(); i++) {
            Map<String, Date> dateObject = intervalDate.get(i);
            int targetStockDay = 0;
            BigDecimal targetStock = BigDecimal.ZERO;
            BigDecimal safeStock = BigDecimal.ZERO;
            BigDecimal predictSale = BigDecimal.ZERO;
            BigDecimal execReplishment = BigDecimal.ZERO;
            String execReplenish = "";
            BigDecimal onRoadStock = BigDecimal.ZERO;
            String startDate = DateUtil.format(dateObject.get("startTime"), DateUtil.NORM_DATE_PATTERN);
            stockPlanQueryDTO.setStartDate(startDate);
            String endDate = DateUtil.format(dateObject.get("endTime"), DateUtil.NORM_DATE_PATTERN);
            stockPlanQueryDTO.setEndDate(endDate);

            //大数据中台接口调用--周转率
            BaseQueryDTO baseQueryDTO = new BaseQueryDTO();
            baseQueryDTO.setChannel(stockPlanQueryDTO.getChannel());
            baseQueryDTO.setStartTime(startDate.replaceAll("-",""));
            baseQueryDTO.setEndTime(endDate.replaceAll("-",""));
            baseQueryDTO.setSkuCodeList(Arrays.asList(skuCode));
            baseQueryDTO.setFactoryCode(stockPlanQueryDTO.getFactoryCode());
            baseQueryDTO.setStore(stockPlanQueryDTO.getFactoryCode());

            //交易中心--确认补货量
            PoCalcQueryDTO poCalcQueryDTO=new PoCalcQueryDTO();
            poCalcQueryDTO.setCreateTimeStart(DateUtil.format(dateObject.get("startTime"), DateUtil.NORM_DATETIME_PATTERN));
            poCalcQueryDTO.setCreateTimeEnd(DateUtil.format(dateObject.get("endTime"), DateUtil.NORM_DATETIME_PATTERN));
            poCalcQueryDTO.setFactoryCode(stockPlanQueryDTO.getFactoryCode());
            poCalcQueryDTO.setSkuCodeList(Arrays.asList(skuCode));

            //大数据预测补货接口调用
            CompletableFuture<DayInventoryPlanResponse> dayInventoryPlanResponseFuture = CompletableFuture.supplyAsync(() -> bigDataReplenishFacade.dayInventoryPlan(stockPlanQueryDTO));
            CompletableFuture<DayPredictReplenishmentResponse> dayPredictReplenishmentResponseFuture = CompletableFuture.supplyAsync(() -> bigDataReplenishFacade.dayPredictReplenishment(stockPlanQueryDTO));
            CompletableFuture<WeekPredictSaleResponse> weekPredictSaleResponseFuture = CompletableFuture.supplyAsync(() -> bigDataReplenishFacade.weekPredictSale(stockPlanQueryDTO));
            CompletableFuture<ArrivalCountDTO> arrivalCountDTOFuture = CompletableFuture.supplyAsync(() -> bigDataFacade.getStockInAndOut(baseQueryDTO));
            CompletableFuture<List<ProductSalesDTO>> productSalesDTOFuture = CompletableFuture.supplyAsync(() -> bigDataFacade.getBoxSalesDayAvg(baseQueryDTO));
            CompletableFuture<List<PoCalcDTO>> poCalcDTOFuture = CompletableFuture.supplyAsync(() -> transactionFacade.poCalc(poCalcQueryDTO));
            CompletableFuture<ProductStockDTO> productStockDTOFuture = CompletableFuture.supplyAsync(() -> bigDataFacade.getStockTurnOverDays(baseQueryDTO));
            DayInventoryPlanResponse res=dayInventoryPlanResponseFuture.get();
            if (!CollectionUtils.isEmpty(res.getList())) {
                //开始包装数据
                for (DayInventoryPlanResDTO dayInventoryPlanResDTO : res.getList()) {
                    area = dayInventoryPlanResDTO.getProvince() + "/" + dayInventoryPlanResDTO.getCity() + "/" + dayInventoryPlanResDTO.getDistrict();
//                    cate = dayInventoryPlanResDTO.getCateL1() + "/" + dayInventoryPlanResDTO.getCateL2() + "/" + dayInventoryPlanResDTO.getCateL3();
//                    safeStock = safeStock.add(dayInventoryPlanResDTO.getSs());
//                    targetStock = targetStock.add(dayInventoryPlanResDTO.getTi());
                    execReplishment = execReplishment.add(dayInventoryPlanResDTO.getExecReplishment());
                }
                //目标库存和安全库存，取最大值
                List<BigDecimal> ssList=res.getList().stream().filter(v->v.getSs()!=null).map(DayInventoryPlanResDTO::getSs).collect(Collectors.toList());
                List<BigDecimal> tiList=res.getList().stream().filter(v->v.getTi()!=null).map(DayInventoryPlanResDTO::getTi).collect(Collectors.toList());
                safeStock=Collections.max(ssList);
                targetStock=Collections.max(tiList);

            }
            //大数据预测补货接口调用
            //目标库存天数
            DayPredictReplenishmentResponse res2 = dayPredictReplenishmentResponseFuture.get();
            if (!CollectionUtils.isEmpty(res2.getList())) {
                //开始包装数据
                List<Integer> predictReplenishmentList=res2.getList().stream().filter(v->v.getTargetDays()!=null).map(DayPredictReplenishmentResDTO::getTargetDays).collect(Collectors.toList());
                targetStockDay=Collections.max(predictReplenishmentList);
            }
            //大数据预测补货接口调用--销售预测
            WeekPredictSaleResponse weekPredictSaleResponse = weekPredictSaleResponseFuture.get();
            if (!CollectionUtils.isEmpty(weekPredictSaleResponse.getList())) {
                predictSale = weekPredictSaleResponse.getList().get(0).getPredictSales();
            }
            ProductStockDTO productStockDTO = productStockDTOFuture.get();
            String predictDay = productStockDTO.getSkuTurnOver7Days() == null ? "-" : productStockDTO.getSkuTurnOver7Days();
            String stockChangeRate = productStockDTO.getTurnOverRate() == null ? "-" : productStockDTO.getTurnOverRate();

            //大数据中台接口调用--实际到货量
            ArrivalCountDTO arrivalCountDTO =  arrivalCountDTOFuture.get();
            String factStock = arrivalCountDTO.getArrivalCount()== null ? "-" : arrivalCountDTO.getArrivalCount();
            //大数据中台接口调用--实际销量
            List<ProductSalesDTO> productSalesDTOList = productSalesDTOFuture.get();
            BigDecimal productSalesTotal=BigDecimal.ZERO;
            for(ProductSalesDTO productSalesDTO : productSalesDTOList){
                if(!org.springframework.util.StringUtils.isEmpty(productSalesDTO.getSalesCount())){
                    BigDecimal salesCount=new BigDecimal(productSalesDTO.getSalesCount());
                    productSalesTotal=productSalesTotal.add(salesCount);
                }
            }
            String factSale =String.valueOf(productSalesTotal);

            //在库库存：当前查库存，历史查大数据
            String inStock = this.queryInStock(dateObject, stockPlanQueryDTO,arrivalCountDTO);

            List<PoCalcDTO> list=poCalcDTOFuture.get();
            if(!CollectionUtils.isEmpty(list)){
                execReplenish=String.valueOf(list.get(0).getSkuQuantity());
            }else{
                execReplenish="-";
            }

            targetStockDayList.add(String.valueOf(targetStockDay));
            targetStockList.add(String.valueOf(targetStock));
            safeStockList.add(String.valueOf(safeStock));
            predictSaleList.add(String.valueOf(predictSale));
            factSaleList.add(String.valueOf(factSale));
            execReplishmentList.add(String.valueOf(execReplishment));
            execReplenishList.add(String.valueOf(execReplenish));
            factStockList.add(String.valueOf(factStock));
            instockList.add(String.valueOf(inStock));
//            onRoadStockList.add(String.valueOf(onRoadStock));
            predictDayList.add(String.valueOf(predictDay));
            stockChangeRateList.add(String.valueOf(stockChangeRate));
        }
        //组装指标
        targetStockDayQueryResDTO.setArea(area);
        targetStockDayQueryResDTO.setCate(cate);
        targetStockDayQueryResDTO.setChannel(channelCode);
        targetStockDayQueryResDTO.setSkuCode(skuCode);
        targetStockDayQueryResDTO.setSkuName(stockPlanQueryDTO.getSkuName());

        //列表组装
        List<StockPlanQueryResDTO> resList = new ArrayList<>();
        targetStockDayQueryResDTO.setValueList(targetStockDayList);
        targetStockDayQueryResDTO.setIndexName("目标库存天数");
        resList.add(targetStockDayQueryResDTO);

        StockPlanQueryResDTO targetStockQueryResDTO = stockRecordConvertor.convertStockPlanQueryDTO(targetStockDayQueryResDTO);
        targetStockQueryResDTO.setValueList(targetStockList);
        targetStockQueryResDTO.setIndexName("目标库存");
        resList.add(targetStockQueryResDTO);

        StockPlanQueryResDTO safeStockQueryResDTO = stockRecordConvertor.convertStockPlanQueryDTO(targetStockDayQueryResDTO);
        safeStockQueryResDTO.setValueList(safeStockList);
        safeStockQueryResDTO.setIndexName("安全库存");
        resList.add(safeStockQueryResDTO);

        StockPlanQueryResDTO predictSalePlanQueryResDTO = stockRecordConvertor.convertStockPlanQueryDTO(targetStockDayQueryResDTO);
        predictSalePlanQueryResDTO.setValueList(predictSaleList);
        predictSalePlanQueryResDTO.setIndexName("销售预测");
        resList.add(predictSalePlanQueryResDTO);

        StockPlanQueryResDTO factSalePlanQueryResDTO = stockRecordConvertor.convertStockPlanQueryDTO(targetStockDayQueryResDTO);
        factSalePlanQueryResDTO.setValueList(factSaleList);
        factSalePlanQueryResDTO.setIndexName("实际销量");
        resList.add(factSalePlanQueryResDTO);

        StockPlanQueryResDTO execReplishmentPlanQueryResDTO = stockRecordConvertor.convertStockPlanQueryDTO(targetStockDayQueryResDTO);
        execReplishmentPlanQueryResDTO.setValueList(execReplishmentList);
        execReplishmentPlanQueryResDTO.setIndexName("建议补货量");
        resList.add(execReplishmentPlanQueryResDTO);

        StockPlanQueryResDTO execReplenishPlanQueryResDTO = stockRecordConvertor.convertStockPlanQueryDTO(targetStockDayQueryResDTO);
        execReplenishPlanQueryResDTO.setValueList(execReplenishList);
        execReplenishPlanQueryResDTO.setIndexName("确认补货量");
        resList.add(execReplenishPlanQueryResDTO);

        StockPlanQueryResDTO factStockPlanQueryResDTO = stockRecordConvertor.convertStockPlanQueryDTO(targetStockDayQueryResDTO);
        factStockPlanQueryResDTO.setValueList(factStockList);
        factStockPlanQueryResDTO.setIndexName("实际到货量");
        resList.add(factStockPlanQueryResDTO);

        StockPlanQueryResDTO instockPlanQueryResDTO = stockRecordConvertor.convertStockPlanQueryDTO(targetStockDayQueryResDTO);
        instockPlanQueryResDTO.setValueList(instockList);
        instockPlanQueryResDTO.setIndexName("在库库存");
        resList.add(instockPlanQueryResDTO);

//        StockPlanQueryResDTO onRoadStockPlanQueryResDTO = new StockPlanQueryResDTO();
//        BeanUtils.copyProperties(onRoadStockPlanQueryResDTO, targetStockDayQueryResDTO);
//        onRoadStockPlanQueryResDTO.setValueList(onRoadStockList);
//        onRoadStockPlanQueryResDTO.setIndexName("在途库存");
//        resList.add(onRoadStockPlanQueryResDTO);

        StockPlanQueryResDTO predictDayPlanQueryResDTO = stockRecordConvertor.convertStockPlanQueryDTO(targetStockDayQueryResDTO);
        predictDayPlanQueryResDTO.setValueList(predictDayList);
        predictDayPlanQueryResDTO.setIndexName("预计可销天数");
        resList.add(predictDayPlanQueryResDTO);

        StockPlanQueryResDTO stockChangeRatePlanQueryResDTO = stockRecordConvertor.convertStockPlanQueryDTO(targetStockDayQueryResDTO);
        stockChangeRatePlanQueryResDTO.setValueList(stockChangeRateList);
        stockChangeRatePlanQueryResDTO.setIndexName("库存周转率");
        resList.add(stockChangeRatePlanQueryResDTO);

        return resList;
    }


    /**
     * 查询在库库存：当前查库存，历史查大数据
     *
     * @return
     */
    private String queryInStock(Map<String, Date> dateObject, StockPlanQueryDTO stockPlanQueryDTO,ArrivalCountDTO arrivalCountDTO) {
        if (dateObject.get("startTime").compareTo(new Date()) < 0 && dateObject.get("endTime").compareTo(new Date()) > 0) {
            //工厂编号
            FactoryStockDTO factoryStockDTO = kpRwRelationRepository.queryRealWarehouseStockByFactoryCodeAndSkuCode(stockPlanQueryDTO.getFactoryCode(),stockPlanQueryDTO.getSkuKey());
            if(null !=factoryStockDTO){
                return String.valueOf(factoryStockDTO.getRealQty());
            }
            return "0";
        } else if (dateObject.get("startTime").compareTo(new Date()) > 0) {
            return "-";
        } else {
            //查大数据
            String stockHistory=arrivalCountDTO.getStockHistory()==null?"-":arrivalCountDTO.getStockHistory();
            return stockHistory;
        }
    }
    /**
     * 获取当前周数据
     * int beforeCount=4;int afterCount=7;int interval=7;
     *
     * @return
     */
    private static List<String> getIntervalDate(int beforeCount, int afterCount, int interval) {
        List<String> dayDate = new ArrayList<>();
        Date dd = new Date();
        int week = dd.getDay(); //获取时间的星期数
        for (int i = 0; i < beforeCount; i++) {
            Date beforeDate = new Date();
            beforeDate = DateUtils.addDays(dd, -(week + interval * (beforeCount - 1 - i)));
            Calendar ca = Calendar.getInstance();
            ca.setTime(beforeDate);
            dayDate.add(i, ca.get(Calendar.YEAR) + "-" + (beforeDate.getMonth() + 1) + "-" + beforeDate.getDate());
        }
        List<String> dayDate2 = new ArrayList<>();
        for (int i = 0; i < afterCount; i++) {
            Date beforeDate = new Date();
            beforeDate = DateUtils.addDays(dd, -(week - interval * (afterCount - i)));
            Calendar ca = Calendar.getInstance();
            ca.setTime(beforeDate);
            dayDate2.add(i, ca.get(Calendar.YEAR) + "-" + (beforeDate.getMonth() + 1) + "-" + beforeDate.getDate());
        }
        Collections.reverse(dayDate2);
        dayDate.addAll(dayDate2);
        return dayDate;
    }

    private List<String> getWeekDate(int beforeCount, int afterCount, int interval) {
        List<String> intervalDate = getIntervalDateForPage(beforeCount, afterCount, interval);
        List<String> weekDate = new ArrayList<>();
        for (int i = 0; i < intervalDate.size() - 1; i++) {
            weekDate.add(intervalDate.get(i) + "-" + intervalDate.get(i + 1));
        }
        return weekDate;
    }

    /**
     * 获取当前周数据
     * int beforeCount=4;int afterCount=7;int interval=7;
     *
     * @return
     */
    private static List<String> getIntervalDateForPage(int beforeCount, int afterCount, int interval) {
        List<String> dayDate = new ArrayList<>();
        Date dd = new Date();
        int week = dd.getDay(); //获取时间的星期数
        for (int i = 0; i < beforeCount; i++) {
            Date beforeDate = new Date();
            beforeDate = DateUtils.addDays(dd, -(week + interval * (beforeCount - 1 - i)));
            Calendar ca = Calendar.getInstance();
            ca.setTime(beforeDate);
            dayDate.add(i, (beforeDate.getMonth() + 1) + "." + beforeDate.getDate());
        }
        List<String> dayDate2 = new ArrayList<>();
        for (int i = 0; i < afterCount; i++) {
            Date beforeDate = new Date();
            beforeDate = DateUtils.addDays(dd, -(week - interval * (afterCount - i)));
            Calendar ca = Calendar.getInstance();
            ca.setTime(beforeDate);
            dayDate2.add(i, (beforeDate.getMonth() + 1) + "." + beforeDate.getDate());
        }
        Collections.reverse(dayDate2);
        dayDate.addAll(dayDate2);
        return dayDate;
    }


    @Override
    public List<RealTimeStock> queryRealTimeStock(QueryRealTimeStock queryRealTimeStock){
        return kpRwRelationRepository.queryRealTimeStock(queryRealTimeStock);
    }

    @Override
    public List<String> queryFactoryCodeByLike(String factoryCode) {
        if(StringUtils.isEmpty(factoryCode)){
            return new ArrayList<>();
        }
        return realWarehouseRepository.queryFactoryCodeByLike(factoryCode);
    }

    @Override
    public List<RealWarehouse> queryRealWarehouseCodeCodeByLike(String realWarehouseCode, String factoryCode) {
        if(StringUtils.isEmpty(realWarehouseCode)){
            return new ArrayList<>();
        }
        return realWarehouseRepository.queryRealWarehouseCodeCodeByLike(realWarehouseCode,factoryCode);
    }

}
