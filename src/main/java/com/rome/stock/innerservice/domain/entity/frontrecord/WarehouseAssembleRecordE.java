package com.rome.stock.innerservice.domain.entity.frontrecord;

import com.rome.stock.innerservice.constant.FrontRecordTypeVO;
import com.rome.stock.innerservice.domain.repository.frontrecord.FrWarehouseReverseRepository;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.apache.commons.lang3.StringUtils;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;

/**
 * 仓库加工
 */
@Component
@Scope("prototype")
@Data
@EqualsAndHashCode(callSuper = false)
public class WarehouseAssembleRecordE extends AbstractFrontRecord{
    @Resource
    private FrWarehouseReverseRepository frWarehouseReverseRepository;

    /**
     * 保存仓库组装加工前置单
     */
    public void addAssembleFrontRecord(){
        //设置前置单为仓库加工
        this.setRecordType(FrontRecordTypeVO.WAREHOUSE_ASSEMBLE_TASK_RECORD.getType());
        //生成单据编号
        this.initFrontRecord(FrontRecordTypeVO.WAREHOUSE_ASSEMBLE_TASK_RECORD.getCode(), this.frontRecordDetails);
        if(StringUtils.isBlank(this.getRecordStatusReason())) {
            this.setRecordStatusReason("");
        }
        //保存仓库加工单
        Long id = frWarehouseReverseRepository.saveAssembleFrontRecord(this);
        this.setId(id);
        //加工详情关联主数据
        this.frontRecordDetails.forEach(detail->detail.setFrontRecordDetail(this));
        //插入盘点单据详情
        frWarehouseReverseRepository.saveAssembleRecordDetails(this.frontRecordDetails);
    }

    /**
     * 保存仓库反拆加工前置单
     */
    public void addSpitFrontRecord(){
        //设置前置单为仓库加工
        this.setRecordType(FrontRecordTypeVO.WAREHOUSE_REVERSE_DISASSEMBLY_TASK_RECORD.getType());
        //生成单据编号
        this.initFrontRecord(FrontRecordTypeVO.WAREHOUSE_REVERSE_DISASSEMBLY_TASK_RECORD.getCode(), this.frontRecordDetails);
        if(StringUtils.isBlank(this.getRecordStatusReason())) {
            this.setRecordStatusReason("");
        }
        //保存仓库加工单
        Long id = frWarehouseReverseRepository.saveAssembleFrontRecord(this);
        this.setId(id);
        //加工详情关联主数据
        this.frontRecordDetails.forEach(detail->detail.setFrontRecordDetail(this));
        //插入盘点单据详情
        frWarehouseReverseRepository.saveAssembleRecordDetails(this.frontRecordDetails);
    }

    /**
     * 工厂编号
     */
    private String factoryCode;

    /**
     *仓库编号
     */
    private String warehouseCode;


    /**
     * 工厂名称
     */
    private String factoryName;

    /**
     * 仓库名称
     */
    private String warehouseName;

    /**
     *  实体仓库id
     */
    private Long realWarehouseId;

    /**
     * 开始日期
     */
    private Date startDate;

    /**
     * 结束日期
     */
    private Date endDate;

    /**
     * 备注信息
     */
    private String remark;

    /**
     * 加工单详情
     */
    private List<WarehouseAssembleRecordDetailE> frontRecordDetails;

}
