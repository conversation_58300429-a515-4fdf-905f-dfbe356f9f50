package com.rome.stock.innerservice.domain.entity;

import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Component;

import java.io.Serializable;

/**
 * @Description
 * <AUTHOR>
 * @Date 2019/5/18 16:54
 * @Version 1.0
 */
@Component
@Scope("prototype")
@Data
@EqualsAndHashCode(callSuper = false)
public class SkuRealVirtualStockSyncRelationE extends BaseE implements Serializable {
    /**
     * 唯一主键
     */
    private Long id;

    /**
     * skuId
     */
    private Long skuId;

    /**
     * sku编号
     */
    private String skuCode;

    /**
     * 虚仓id
     */
    private Long virtualWarehouseId;

    /**
     * 实仓id
     */
    private Long realWarehouseId;

    /**
     * sku虚仓分配比例
     */
    private Integer syncRate;
}
