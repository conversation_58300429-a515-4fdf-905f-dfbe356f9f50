package com.rome.stock.innerservice.domain.message.transaction;

import lombok.Data;
import org.apache.commons.lang3.StringUtils;

import java.io.Serializable;
import java.util.List;

/**
 * @Description MessageDTO
 * <AUTHOR>
 * @Date 2021/8/3 11:08
 * @Version 1.0
 **/
@Data
public class MessageDTO implements Serializable {
	/**
	 * 中台出入库单号，必须
	 */
	private String recordCode;
	/**
	 * 中台单据类型，必须
	 */
	private Integer recordType;
	/**
	 * 中台收获单号，大仓收获单必须
	 */
	private String receiveRecord;
	/**
	 * 中台唯一单号，自己根据自己业务确定该字段，
	 * 比如：对于外采：收获单号+"_"+质检回传次数
	 *       非外采入库：等于收获单号
	 *       出库单或门店入库单：等于出入库单就可以唯一
	 */
	private String ztRecordCode;

	/**
	 * 该出入库单关联的虚拟出入库单列表，有就传，没有就空
	 */
	private List<String> refRecordCodes;

	/**
	 * 渠道号
	 */
	private String channelCode;

	/**
	 * 业务无需关心此字段，主要用于手动重试【以防万一】
	 */
	private String retryKey;

	/**
	 * 是否确实交易路径 1是
	 */
	private Integer isMissTradePath;

	public String messageKey() {
		if (StringUtils.isBlank(retryKey)) {
			return ztRecordCode;
		}
		return "manual_" + retryKey;
	}
}
