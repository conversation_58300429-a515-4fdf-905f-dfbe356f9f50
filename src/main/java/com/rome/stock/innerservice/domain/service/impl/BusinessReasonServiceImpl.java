package com.rome.stock.innerservice.domain.service.impl;

import com.rome.stock.innerservice.api.dto.BusinessReasonDTO;
import com.rome.stock.innerservice.domain.convertor.BusinessReasonConvertor;
import com.rome.stock.innerservice.domain.repository.BusinessReasonRepository;
import com.rome.stock.innerservice.domain.service.BusinessReasonService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

/**
 * @Description
 * <AUTHOR>
 * @Date 2019/5/13 15:43
 * @Version 1.0
 */
@Service
public class BusinessReasonServiceImpl implements BusinessReasonService {

    @Resource
    BusinessReasonConvertor businessReasonConvertor;

    @Resource
    BusinessReasonRepository businessReasonRepository;
    //根据单据类型查询业务原因
    @Override
    public List<BusinessReasonDTO> queryBusinessReason(Integer recordType) {

        return businessReasonConvertor.businessReasonEntityTODto(businessReasonRepository.getReasonByBusinessType(recordType));
    }
}
