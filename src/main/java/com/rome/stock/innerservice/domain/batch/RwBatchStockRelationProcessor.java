package com.rome.stock.innerservice.domain.batch;

import com.rome.stock.innerservice.domain.batch.dto.RwRelationQueryDTO;
import com.rome.stock.innerservice.infrastructure.dataobject.*;

import java.util.List;
import java.util.Map;

/**
 * @Description 获取单据 关联关系表，不存在多次收货的
 * <AUTHOR>
 * @Date 2023/11/21
 * @Version 1.0
 */
public interface RwBatchStockRelationProcessor {

    /**
     * 获取和计算对应仓库批次数据，
     * 返回单据与仓库批次对应的map
     * @param recordType
     * @param recordCodeList
     * @return
     */
    Map<String, List<String>> getRelationRecordList(RwRelationQueryDTO rwRelationQueryDTO);



}
