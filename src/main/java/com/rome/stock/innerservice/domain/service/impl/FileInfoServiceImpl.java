package com.rome.stock.innerservice.domain.service.impl;

import com.rome.stock.innerservice.api.dto.FileInfoDto;
import com.rome.stock.innerservice.domain.repository.FileInfoRepository;
import com.rome.stock.innerservice.domain.service.FileInfoService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

/**
* FileInfoService实现类
*/
@Slf4j
@Service
public class FileInfoServiceImpl implements FileInfoService {

    @Resource
    private FileInfoRepository fileInfoRepository;

    @Override
    public List<FileInfoDto> queryFileInfoList(List<Long> bizIdList,Integer bizType) {
        return fileInfoRepository.queryFileInfoList(bizIdList,bizType);
    }

}
