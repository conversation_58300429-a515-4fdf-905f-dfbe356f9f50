package com.rome.stock.innerservice.domain.entity.frontrecord;


import com.rome.stock.innerservice.constant.FrontRecordTypeVO;
import com.rome.stock.innerservice.domain.repository.frontrecord.FrChargeAgainstConfigRepository;
import com.rome.stock.innerservice.domain.repository.frontrecord.FrChargeAgainstDetailRepository;
import com.rome.stock.innerservice.domain.repository.frontrecord.FrChargeAgainstRepository;
import com.rome.stock.innerservice.domain.service.OrderUtilService;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

@Component
@Scope("prototype")
@Data
@EqualsAndHashCode(callSuper = false)
public class ChargeAgainstE extends AbstractFrontRecord {
    @Resource
    private FrChargeAgainstRepository frChargeAgainstRepository;
    @Resource
    private FrChargeAgainstDetailRepository frChargeAgainstDetailRepository;
    @Resource
    private FrChargeAgainstConfigRepository frChargeAgainstConfigRepository;
    @Resource
    private OrderUtilService orderUtilService;
    /**
     * 业务类型：1:入库单冲销（出库单） 2:出库单冲销（入库单）
     */
    private Integer businessType;
    /**
     * 原单据实体仓库id
     */
    private Long originRealWarehouseId;
    /**
     * 冲销单据实体仓库id
     */
    private Long realWarehouseId;
    /**
     * 原出入库单号
     */
    private String originWarehouseRecordCode;
    /**
     * 原单号（入库单冲销为收货单号，出库单冲销为出库单号）
     */
    private String originRecordCode;
    /**
     * 原业务单号
     */
    private String originBusinessCode;
    /**
     * 业务单号
     */
    private String businessCode;
    /**
     * 备注
     */
    private String remark;
    /**
     * 冲销原因code
     */
    private String reasonCode;
    /**
     * 推送上游系统状态 0---无需推送 1--待推送 2--已推送
     */
    private Integer syncStatus;
    /**
     * 冲销操作人
     */
    private String operator;
    /**
     * 冲销财务时间
     */
    private Date chargeAgainstDate;
    /**
     * 明细列表数据
     */
    private List<ChargeAgainstDetailE> chargeAgainstDetails;

    /**
     * 创建采购入库前置单
     */
    public void addFrontRecord() {
        this.setRecordStatus(0);
        this.setSyncStatus(0);
        this.initFrontRecord("CA",this.getChargeAgainstDetails());
        this.setBusinessCode(orderUtilService.queryOrderCode("CB"));
        this.setCreateTime(new Date());
        Long frontRecordId = frChargeAgainstRepository.insert(this);
        this.getChargeAgainstDetails().forEach(item -> {
            item.setFrontRecordId(frontRecordId);
            item.setRecordCode(this.getRecordCode());
            item.setAccQty(BigDecimal.ZERO);
        });
        this.setId(frontRecordId);
        frChargeAgainstDetailRepository.batchInsert(this.getChargeAgainstDetails());
    }

    /**
     * 初始状态更新为已完成
     */
    public void updateToCompleteStatus() {
        frChargeAgainstRepository.updateToCompleteStatus(this.getId());
    }

    /**
     * 初始状态更新为部分完成
     */
    public void updateInitToInAllocation() {
        // frChargeAgainstRepository.updateInitToInAllocation(this.getId());
    }

    /**
     * 推送成功
     */
    public void notifySuccess() {
        frChargeAgainstRepository.updateToNotifySuccess(this.getId());
    }
}
