package com.rome.stock.innerservice.domain.entity.frontrecord;

import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;

@Component
@Scope("prototype")
@Data
@EqualsAndHashCode(callSuper = false)
public class WarehouseInventoryRecordDetailE extends AbstractFrontRecordDetail{

    /**
     * 账面数量
     */
    private BigDecimal accQty;

    /**
     *差异数量
     */
    private BigDecimal diffQty;

    /**
     * wms差异数量
     */
    private BigDecimal wmsDiffQty;

    /**
     * 实盘数量
     */
    private BigDecimal skuQty;

    /**
     * 盘点情况（1，盘盈，2，盘亏，3，平盘）
     */
    private Integer adjustType;

    /**
     * 库存状态（1，合格 2，质检不合格 3，待质检）
     */
    private Integer inventoryStatus;

    /**
     * 源系统差异数量
     */
    private BigDecimal basicDiff;

    private String inventoryCode;

    private String unit;

    private String unitCode;

    private BigDecimal totalQty;

    /**
     *唯一单号
     */
    private String uniqueKey;

    public String getUniqueKey(){
        return this.getSkuCode()+"_"+this.inventoryStatus;
    }
}
