/**
 * Filename StockOpServiceImpl.java
 * Company 上海来伊份科技有限公司。
 *
 * <AUTHOR>
 * @version
 */
package com.rome.stock.innerservice.domain.service.impl;

import com.rome.arch.core.exception.RomeException;
import com.rome.stock.innerservice.common.ResCode;
import com.rome.stock.innerservice.common.StringUtils;
import com.rome.stock.innerservice.domain.entity.RealWarehouseE;
import com.rome.stock.innerservice.domain.repository.RealWarehouseRepository;
import com.rome.stock.innerservice.domain.service.StockHealthService;
import com.rome.stock.innerservice.remote.base.dto.StoreDTO;
import com.rome.stock.innerservice.remote.base.facade.ShopFacade;
import com.rome.stock.innerservice.remote.bigdata.dto.health.*;
import com.rome.stock.innerservice.remote.bigdata.facade.BigDataFacade;
import com.rome.stock.innerservice.remote.item.dto.SkuInfoExtDTO;
import com.rome.stock.innerservice.remote.item.facade.SkuFacade;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 库存健康查询
 * <AUTHOR>
 */
@Slf4j
@Service
public class StockHealthServiceImpl implements StockHealthService {

    @Resource
    private BigDataFacade bigDataFacade;

    @Resource
    private RealWarehouseRepository realWarehouseRepository;

    @Resource
    private SkuFacade skuFacade;

    @Resource
    private ShopFacade shopFacade;

    @Override
    public HealthResDTO getAreaBySku(HealthQueryDTO healthQueryDTO) {
        this.addType(healthQueryDTO);
        HealthResDTO healthResDTO=bigDataFacade.getAreaBySku(healthQueryDTO);
        this.packSkuInfo(healthResDTO);
        return healthResDTO;
    }

    @Override
    public FactorySkuListVO getFactorySkuList(HealthSkuFactoryQueryDTO healthSkuFactoryQueryDTO) {
        RealWarehouseE realWarehouseE=realWarehouseRepository.queryRealWarehouseByInCode(healthSkuFactoryQueryDTO.getFactoryCode());
        if(null == realWarehouseE){
            throw new RomeException(ResCode.STOCK_ERROR_1003, "仓库或门店不存在");
        }
        if(null == healthSkuFactoryQueryDTO.getLayeredType()){
            throw new RomeException(ResCode.STOCK_ERROR_1003, "商品分层不能为空");
        }
        if(realWarehouseE.getRealWarehouseType()==1){
            healthSkuFactoryQueryDTO.setType(1);
        }else{
            healthSkuFactoryQueryDTO.setType(0);
        }
        FactorySkuListVO factorySkuListVO=bigDataFacade.getFactorySkuList(healthSkuFactoryQueryDTO);
        if(null!=factorySkuListVO && !CollectionUtils.isEmpty(factorySkuListVO.getSkuDetailList().getList())){
            for(FactorySkuDetailVO factorySkuDetailVO:factorySkuListVO.getSkuDetailList().getList()){
                factorySkuDetailVO.setStockStatusId(healthSkuFactoryQueryDTO.getStockStatusId());
                factorySkuDetailVO.setLayeredType(healthSkuFactoryQueryDTO.getLayeredType());
                Double total = 0d;
                try {
                    total = (new Double(factorySkuDetailVO.getSs()) + new Double(factorySkuDetailVO.getNrt()) + new Double(factorySkuDetailVO.getVlt()) + new Double(factorySkuDetailVO.getBp1()) + new Double(factorySkuDetailVO.getBp2())) + new Double(factorySkuDetailVO.getBp3());
                    factorySkuDetailVO.setTotalNum(String.valueOf(total));
                } catch (Exception e) {
                    log.error(e.getMessage(), e);
                }
                try {
                    factorySkuDetailVO.setOne(new Double(factorySkuDetailVO.getSs()) / total);
                } catch (Exception e) {
                    log.error(e.getMessage(), e);
                }
                try {
                    factorySkuDetailVO.setTwo((new Double(factorySkuDetailVO.getSs()) + new Double(factorySkuDetailVO.getNrt()) + new Double(factorySkuDetailVO.getVlt())) / total);
                } catch (Exception e) {
                    log.error(e.getMessage(), e);
                }
                try {
                    factorySkuDetailVO.setThree((new Double(factorySkuDetailVO.getSs()) + new Double(factorySkuDetailVO.getNrt()) + new Double(factorySkuDetailVO.getVlt()) + new Double(factorySkuDetailVO.getBp1())) / total);
                } catch (Exception e) {
                    log.error(e.getMessage(), e);
                }
                try {
                    factorySkuDetailVO.setFour((new Double(factorySkuDetailVO.getSs()) + new Double(factorySkuDetailVO.getNrt()) + new Double(factorySkuDetailVO.getVlt()) + new Double(factorySkuDetailVO.getBp1()) + new Double(factorySkuDetailVO.getBp2())) / total);
                } catch (Exception e) {
                    log.error(e.getMessage(), e);
                }
                factorySkuDetailVO.setFactoryCode(healthSkuFactoryQueryDTO.getFactoryCode());
            }
        }
        this.packSkuInfo(factorySkuListVO);
        return factorySkuListVO;
    }




    /**
     * 包装sku基本信息
     * @param factorySkuListVO
     */
    private void packSkuInfo(FactorySkuListVO factorySkuListVO){
        if(null !=factorySkuListVO && !CollectionUtils.isEmpty(factorySkuListVO.getSkuDetailList().getList())){
            List<String> skuCodes=factorySkuListVO.getSkuDetailList().getList().stream().map(FactorySkuDetailVO::getSkuCode).collect(Collectors.toList());
            //查询商品分类
            List<SkuInfoExtDTO> skuInfoExtDTOList=skuFacade.skusBySkuCode(skuCodes);
            Map<String,SkuInfoExtDTO> skuUnitExtDTOMap=skuInfoExtDTOList.stream().collect(Collectors.toMap(SkuInfoExtDTO::getSkuCode, Function.identity(), (v1, v2) -> v1));
            if(!CollectionUtils.isEmpty(skuInfoExtDTOList)){
                for(FactorySkuDetailVO healthListResDTO:factorySkuListVO.getSkuDetailList().getList()){
                    if(skuUnitExtDTOMap.containsKey(healthListResDTO.getSkuCode())){
                        SkuInfoExtDTO skuInfoExtDTO=skuUnitExtDTOMap.get(healthListResDTO.getSkuCode());
                        healthListResDTO.setSkuCate(skuInfoExtDTO.getCategoryName());
                        healthListResDTO.setSkuName(skuInfoExtDTO.getName());
                        healthListResDTO.setUnit(skuInfoExtDTO.getSpuUnitCode());
                    }
                }
            }
        }
    }

    @Override
    public HealthResDTO getHealthByFactoryCode(HealthQueryDTO healthQueryDTO) {
        this.addType(healthQueryDTO);
        HealthResDTO healthResDTO=bigDataFacade.getHealthByFactoryCode(healthQueryDTO);
        this.packFactoryInfo(healthResDTO);
        return healthResDTO;
    }


    /**
     * 包装sku基本信息
     * @param healthResDTO
     */
    private void packFactoryInfo(HealthResDTO healthResDTO){
        if(null !=healthResDTO && !CollectionUtils.isEmpty(healthResDTO.getStockHealthList())){
            //查询商品分类
                for(HealthListResDTO healthListResDTO:healthResDTO.getStockHealthList()){
                    healthListResDTO.setStockStatusName(this.getStockStatus(healthListResDTO.getStockStatusId()));
                    healthListResDTO.setFactoryCode(healthResDTO.getFactoryCode());
                }
        }
    }

    @Override
    public HealthResDTO getHealthBySku(HealthQueryDTO healthQueryDTO) {
        this.addType(healthQueryDTO);
        HealthResDTO healthResDTO=bigDataFacade.getHealthBySku(healthQueryDTO);
//        this.packProvinceInfo(healthResDTO,healthQueryDTO);
        this.packSkuInfo(healthResDTO);
        return healthResDTO;
    }

//    private void packProvinceInfo(HealthResDTO healthResDTO,HealthQueryDTO healthQueryDTO) throws InvocationTargetException, IllegalAccessException {
//        if(null !=healthResDTO && !CollectionUtils.isEmpty(healthResDTO.getStockHealthList())){
//            for(HealthListResDTO healthListResDTO:healthResDTO.getStockHealthList()){
//                HealthQueryDTO queryDTO=new HealthQueryDTO();
//                BeanUtils.copyProperties(queryDTO,healthQueryDTO);
//                queryDTO.setStockStatusId(healthListResDTO.getStockStatusId());
//                HealthResDTO provinceDTO=bigDataFacade.getAreaBySku(queryDTO);
//                if(null !=provinceDTO){
//                    healthListResDTO.setProvinceList(provinceDTO.getProvinceList());
//                }
//            }
//
//        }
//    }

    /**
     * 包装sku基本信息
     * @param healthResDTO
     */
    private void packSkuInfo(SkuProvinceDTO healthResDTO){
        if(null !=healthResDTO && !CollectionUtils.isEmpty(healthResDTO.getDetailList().getList())){
            //查询商品分类
            List<SkuInfoExtDTO> skuInfoExtDTOList=skuFacade.skusBySkuCode(Arrays.asList(healthResDTO.getSkuCode()));
            if(!CollectionUtils.isEmpty(skuInfoExtDTOList)){
                for(FactorySkuDetailVO healthListResDTO:healthResDTO.getDetailList().getList()){
                    SkuInfoExtDTO skuInfoExtDTO=skuInfoExtDTOList.get(0);
                    healthListResDTO.setSkuCate(skuInfoExtDTO.getCategoryName());
                    healthListResDTO.setSkuName(skuInfoExtDTO.getName());
                    healthListResDTO.setUnit(skuInfoExtDTO.getSpuUnitCode());
//                    healthListResDTO.setStockStatusName(this.getStockStatus(healthListResDTO.getStockStatusId()));
                    healthListResDTO.setSkuCode(healthResDTO.getSkuCode());
                }
            }
        }
    }


    /**
     * 包装sku基本信息
     * @param healthResDTO
     */
    private void packSkuInfo(HealthResDTO healthResDTO){
        if(null !=healthResDTO && !CollectionUtils.isEmpty(healthResDTO.getStockHealthList())){
            //查询商品分类
            List<SkuInfoExtDTO> skuInfoExtDTOList=skuFacade.skusBySkuCode(Arrays.asList(healthResDTO.getSkuCode()));
            if(!CollectionUtils.isEmpty(skuInfoExtDTOList)){
                for(HealthListResDTO healthListResDTO:healthResDTO.getStockHealthList()){
                    SkuInfoExtDTO skuInfoExtDTO=skuInfoExtDTOList.get(0);
                    healthListResDTO.setSkuCate(skuInfoExtDTO.getCategoryName());
                    healthListResDTO.setSkuName(skuInfoExtDTO.getName());
                    healthListResDTO.setUnit(skuInfoExtDTO.getSpuUnitCode());
                    healthListResDTO.setStockStatusName(this.getStockStatus(healthListResDTO.getStockStatusId()));
                    healthListResDTO.setSkuCode(healthResDTO.getSkuCode());
                    healthListResDTO.setFactoryCode(healthResDTO.getFactoryCode());
                    healthListResDTO.setLayeredType(healthResDTO.getLayeredType());
                }
            }
        }
    }

    private String getStockStatus(String stockStatusId){
        //库存状态ID 1断货 2严重缺货 3缺货 4正常 5滞销 6严重滞销
        if ("0".equals(stockStatusId)) {
            return "未知2";
        } else if ("1".equals(stockStatusId)) {
            return "断货";
        } else if ("2".equals(stockStatusId)) {
            return "严重缺货";
        } else if ("3".equals(stockStatusId)) {
            return "缺货";
        } else if ("4".equals(stockStatusId)) {
            return "正常";
        } else if ("5".equals(stockStatusId)) {
            return "滞销";
        } else if ("6".equals(stockStatusId)) {
            return "严重滞销";
        } else {
            return "未知";
        }
    }


    /**
     * 添加门店/工厂类型
     * @param healthQueryDTO
     */
    private void addType(HealthQueryDTO healthQueryDTO){
        if(StringUtils.isEmpty(healthQueryDTO.getFactoryCode())){
            return;
        }
        RealWarehouseE realWarehouseE=realWarehouseRepository.queryRealWarehouseByInCode(healthQueryDTO.getFactoryCode());
        if(null == realWarehouseE){
            throw new RomeException(ResCode.STOCK_ERROR_1003, "仓库或门店不存在");
        }
        if(realWarehouseE.getRealWarehouseType()==1){
            healthQueryDTO.setType(1);
        }else{
            healthQueryDTO.setType(0);
        }
    }

    @Override
    public FactorySkuDetailVO getHealthBySkuFactory(HealthQueryDTO healthQueryDTO) {
        this.addType(healthQueryDTO);
        FactorySkuDetailVO factorySkuDetailVO=bigDataFacade.getHealthBySkuFactory(healthQueryDTO);
        if(null!=factorySkuDetailVO){
            factorySkuDetailVO.setStockStatusId(healthQueryDTO.getStockStatusId());
            Double total = 0d;
            try {
                total = (new Double(factorySkuDetailVO.getSs()) + new Double(factorySkuDetailVO.getNrt()) + new Double(factorySkuDetailVO.getVlt()) + new Double(factorySkuDetailVO.getBp1()) + new Double(factorySkuDetailVO.getBp2())) + new Double(factorySkuDetailVO.getBp3());
                factorySkuDetailVO.setTotalNum(String.valueOf(total));
            } catch (Exception e) {
                log.error(e.getMessage(), e);
            }
            try {
                factorySkuDetailVO.setOne(new Double(factorySkuDetailVO.getSs()) / total);
            } catch (Exception e) {
                log.error(e.getMessage(), e);
            }
            try {
                factorySkuDetailVO.setTwo((new Double(factorySkuDetailVO.getSs()) + new Double(factorySkuDetailVO.getNrt()) + new Double(factorySkuDetailVO.getVlt())) / total);
            } catch (Exception e) {
                log.error(e.getMessage(), e);
            }
            try {
                factorySkuDetailVO.setThree((new Double(factorySkuDetailVO.getSs()) + new Double(factorySkuDetailVO.getNrt()) + new Double(factorySkuDetailVO.getVlt()) + new Double(factorySkuDetailVO.getBp1())) / total);
            } catch (Exception e) {
                log.error(e.getMessage(), e);
            }
            try {
                factorySkuDetailVO.setFour((new Double(factorySkuDetailVO.getSs()) + new Double(factorySkuDetailVO.getNrt()) + new Double(factorySkuDetailVO.getVlt()) + new Double(factorySkuDetailVO.getBp1()) + new Double(factorySkuDetailVO.getBp2())) / total);
            } catch (Exception e) {
                log.error(e.getMessage(), e);
            }
            factorySkuDetailVO.setFactoryCode(healthQueryDTO.getFactoryCode());
            factorySkuDetailVO.setSkuCode(healthQueryDTO.getSkuCode());
        }
        this.packSkuInfo(factorySkuDetailVO);
        return factorySkuDetailVO;
    }


    /**
     * 包装sku基本信息
     * @param healthListResDTO
     */
    private void packSkuInfo(FactorySkuDetailVO healthListResDTO){
        if(null !=healthListResDTO){
            //查询商品分类
            List<SkuInfoExtDTO> skuInfoExtDTOList=skuFacade.skusBySkuCode(Arrays.asList(healthListResDTO.getSkuCode()));
            Map<String,SkuInfoExtDTO> skuUnitExtDTOMap=skuInfoExtDTOList.stream().collect(Collectors.toMap(SkuInfoExtDTO::getSkuCode, Function.identity(), (v1, v2) -> v1));
            if(skuUnitExtDTOMap.containsKey(healthListResDTO.getSkuCode())){
                SkuInfoExtDTO skuInfoExtDTO=skuUnitExtDTOMap.get(healthListResDTO.getSkuCode());
                healthListResDTO.setSkuCate(skuInfoExtDTO.getCategoryName());
                healthListResDTO.setSkuName(skuInfoExtDTO.getName());
                healthListResDTO.setUnit(skuInfoExtDTO.getSpuUnitCode());
            }
        }
    }



    @Override
    public SkuProvinceDTO getHealthBySkuProvince(HealthQueryDTO healthQueryDTO) {
        SkuProvinceDTO healthResDTO=bigDataFacade.getHealthBySkuProvince(healthQueryDTO);
        if(null!=healthResDTO && !CollectionUtils.isEmpty(healthResDTO.getDetailList().getList())){
            for(FactorySkuDetailVO factorySkuDetailVO:healthResDTO.getDetailList().getList()){
                factorySkuDetailVO.setStockStatusId(healthQueryDTO.getStockStatusId());
                Double total = 0d;
                try {
                    total = (new Double(factorySkuDetailVO.getSs()) + new Double(factorySkuDetailVO.getNrt()) + new Double(factorySkuDetailVO.getVlt()) + new Double(factorySkuDetailVO.getBp1()) + new Double(factorySkuDetailVO.getBp2())) + new Double(factorySkuDetailVO.getBp3());
                    factorySkuDetailVO.setTotalNum(String.valueOf(total));
                } catch (Exception e) {
                    log.error(e.getMessage(), e);
                }
                try {
                    factorySkuDetailVO.setOne(new Double(factorySkuDetailVO.getSs()) / total);
                } catch (Exception e) {
                    log.error(e.getMessage(), e);
                }
                try {
                    factorySkuDetailVO.setTwo((new Double(factorySkuDetailVO.getSs()) + new Double(factorySkuDetailVO.getNrt()) + new Double(factorySkuDetailVO.getVlt())) / total);
                } catch (Exception e) {
                    log.error(e.getMessage(), e);
                }
                try {
                    factorySkuDetailVO.setThree((new Double(factorySkuDetailVO.getSs()) + new Double(factorySkuDetailVO.getNrt()) + new Double(factorySkuDetailVO.getVlt()) + new Double(factorySkuDetailVO.getBp1())) / total);
                } catch (Exception e) {
                    log.error(e.getMessage(), e);
                }
                try {
                    factorySkuDetailVO.setFour((new Double(factorySkuDetailVO.getSs()) + new Double(factorySkuDetailVO.getNrt()) + new Double(factorySkuDetailVO.getVlt()) + new Double(factorySkuDetailVO.getBp1()) + new Double(factorySkuDetailVO.getBp2())) / total);
                } catch (Exception e) {
                    log.error(e.getMessage(), e);
                }
            }
        }
        this.packSkuInfo(healthResDTO);
        return healthResDTO;
    }

    @Override
    public HealthSkuClassDTO getHealthClassByFactoryCode(HealthClassQueryDTO healthClassQueryDTO) {
        RealWarehouseE realWarehouseE=realWarehouseRepository.queryRealWarehouseByInCode(healthClassQueryDTO.getFactoryCode());
        if(null == realWarehouseE){
            throw new RomeException(ResCode.STOCK_ERROR_1003, "仓库或门店不存在");
        }
        if(realWarehouseE.getRealWarehouseType()==1){
            healthClassQueryDTO.setType(1);
        }else{
            healthClassQueryDTO.setType(0);
        }
        HealthSkuClassDTO healthSkuClassDTO=bigDataFacade.getHealthClassByFactoryCode(healthClassQueryDTO);
        List<StoreDTO> storeDTOS = shopFacade.searchByCodeList(Arrays.asList(healthClassQueryDTO.getFactoryCode()));
        Map<String, StoreDTO> stringStoreDTOMap = storeDTOS.stream().distinct().collect(Collectors.toMap(StoreDTO::getCode, item -> item));
        if(null !=healthSkuClassDTO){
            if(stringStoreDTOMap.containsKey(healthClassQueryDTO.getFactoryCode())){
                StoreDTO storeDTO=stringStoreDTOMap.get(healthClassQueryDTO.getFactoryCode());
                healthSkuClassDTO.setArea(storeDTO.getProvince());
            }
            healthSkuClassDTO.setFactoryCode(healthClassQueryDTO.getFactoryCode());
            healthSkuClassDTO.setStockStatusId(healthClassQueryDTO.getStockStatusId());
            healthSkuClassDTO.setArea(healthClassQueryDTO.getArea());
            healthSkuClassDTO.setCount(healthClassQueryDTO.getCount());
            healthSkuClassDTO.setRate(healthClassQueryDTO.getRate());
        }
        return healthSkuClassDTO;
    }
}
