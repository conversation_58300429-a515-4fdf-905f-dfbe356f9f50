package com.rome.stock.innerservice.domain.message;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

import javax.annotation.Resource;

import com.alibaba.fastjson.JSON;
import org.apache.commons.collections.CollectionUtils;
import org.apache.rocketmq.client.consumer.DefaultMQPushConsumer;
import org.apache.rocketmq.common.consumer.ConsumeFromWhere;
import org.apache.rocketmq.common.message.MessageExt;
import org.apache.rocketmq.spring.annotation.RocketMQMessageListener;
import org.apache.rocketmq.spring.core.RocketMQListener;
import org.apache.rocketmq.spring.core.RocketMQPushConsumerLifecycleListener;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.alibaba.fastjson.JSONObject;
import com.rome.arch.core.exception.RomeException;
import com.rome.stock.common.constants.KibanaLogConstants;
import com.rome.stock.common.constants.PageConstants;
import com.rome.stock.innerservice.api.dto.ChannelSales;
import com.rome.stock.innerservice.api.dto.RealWarehouse;
import com.rome.stock.innerservice.api.dto.RealWarehouseAddDTO;
import com.rome.stock.innerservice.api.dto.WarehouseDTO;
import com.rome.stock.innerservice.api.dto.message.StoreDTO;
import com.rome.stock.innerservice.common.AlikAssert;
import com.rome.stock.innerservice.common.CoreConfig;
import com.rome.stock.innerservice.common.CoreKibanaLog;
import com.rome.stock.innerservice.common.ResCode;
import com.rome.stock.innerservice.common.StringUtils;
import com.rome.stock.innerservice.constant.RealWarehouseRankVO;
import com.rome.stock.innerservice.constant.RealWarehouseTypeVO;
import com.rome.stock.innerservice.domain.entity.ChannelSalesE;
import com.rome.stock.innerservice.domain.entity.MailMessageE;
import com.rome.stock.innerservice.domain.entity.VirtualWarehouseE;
import com.rome.stock.innerservice.domain.entity.VirtualWarehouseGroupE;
import com.rome.stock.innerservice.domain.repository.ChannelSalesRepository;
import com.rome.stock.innerservice.domain.repository.MailRepository;
import com.rome.stock.innerservice.domain.repository.VirtualWarehouseGroupRelationRepository;
import com.rome.stock.innerservice.domain.repository.VirtualWarehouseGroupRepository;
import com.rome.stock.innerservice.domain.repository.VirtualWarehouseRepository;
import com.rome.stock.innerservice.domain.repository.ZdeliverRelationRepository;
import com.rome.stock.innerservice.domain.service.ChannelSalesService;
import com.rome.stock.innerservice.domain.service.RealWarehouseService;
import com.rome.stock.innerservice.domain.service.XTStockService;
import com.rome.stock.innerservice.remote.base.dto.ChannelDTO;
import com.rome.stock.innerservice.remote.base.dto.ChannelShowDTO;
import com.rome.stock.innerservice.remote.base.dto.RegionDivideInfosDTO;
import com.rome.stock.innerservice.remote.base.dto.StoreMdmAttributeExtDTO;
import com.rome.stock.innerservice.remote.base.facade.ChannelFacade;
import com.rome.stock.innerservice.remote.base.facade.ShopFacade;

import lombok.extern.slf4j.Slf4j;

/**
 * @Description: 新增店铺MQ消费
 * <p>
 * @Author: chuwenchao  2019/8/19
 */
@Slf4j
@Service
//@RocketMQMessageListener(topic = "${rocketmq.consumer.addShop.topic}", selectorExpression = "add-store", consumerGroup = "${rocketmq.consumer.addShop.group}")
public class AddShopConsumer implements RocketMQListener<MessageExt>, RocketMQPushConsumerLifecycleListener {

    @Resource
    private XTStockService xtStockService;
    @Resource
    private ChannelFacade channelFacade;
    @Resource
    private CoreConfig coreConfig;
    @Autowired
    private RealWarehouseService realWarehouseService;
    @Autowired
    private ShopFacade shopFacade;
    @Autowired
    private ChannelSalesRepository channelSalesRepository;
    @Autowired
    private VirtualWarehouseRepository virtualWarehouseRepository;
    @Autowired
    private VirtualWarehouseGroupRepository virtualWarehouseGroupRepository;
    @Autowired
    private ChannelSalesService channelSalesService;
    @Resource
    private MailRepository mailRepository;
    @Autowired
    private ZdeliverRelationRepository zdeliverRelationRepository;
    @Autowired
    private VirtualWarehouseGroupRelationRepository virtualWarehouseGroupRelationRepository;

    @Override
    public void onMessage(MessageExt messageExt) {
        log.info("新增店铺消息消费，msgID: {}，消费次数: {}", messageExt.getMsgId(), messageExt.getReconsumeTimes());
        List<JSONObject> storeDTOs = JSONObject.parseObject(messageExt.getBody(), List.class);
        log.warn(CoreKibanaLog.getJobLog(KibanaLogConstants.CONSUMER_MQ, "addShopConsumer", messageExt.getMsgId(), JSON.toJSONString(storeDTOs)));
        if(CollectionUtils.isEmpty(storeDTOs)) {
            log.error("新增店铺消息消费内容为空，msgID: {}", messageExt.getMsgId());
            return ;
        }
        boolean flag = false;
        StringBuffer emailMsg = new StringBuffer();
        for(JSONObject json : storeDTOs) {
            StoreDTO storeDTO = JSONObject.parseObject(json.toJSONString(), StoreDTO.class);
            try {
                if (this.addShop(emailMsg, storeDTO)) {
                    continue;
                }
            } catch (Exception e) {
                log.error("门店【{}】新增异常", storeDTO.getCode(), e);
                flag = true;
            }
        }
    }

    @Override
    public void prepareStart(DefaultMQPushConsumer defaultMQPushConsumer) {
        defaultMQPushConsumer.setConsumeFromWhere(ConsumeFromWhere.CONSUME_FROM_FIRST_OFFSET);
    }

    /**
     * @Description: 数据转换 <br>
     *
     * <AUTHOR> 2019/8/20
     * @param storeDTO
     * @param channelShowDTOS
     * @param storeMdmDTO  mdm信息
     * @return
     */
    private WarehouseDTO getWarehouseDTO(StoreDTO storeDTO, List<ChannelShowDTO> channelShowDTOS, StoreMdmAttributeExtDTO storeMdmDTO) {
        WarehouseDTO warehouseDTO = new WarehouseDTO();
        warehouseDTO.setFactoryCode(storeDTO.getCode());
        // ******** 仓库编码固定值
        warehouseDTO.setWarehouseOutCode("0001");
        warehouseDTO.setWarehouseType(3);
        warehouseDTO.setShopCode(storeDTO.getCode());
        warehouseDTO.setWarehouseName(storeDTO.getName());
        if(StringUtils.isNotBlank(storeDTO.getAddress())) {
            warehouseDTO.setRealWarehouseAddress(storeDTO.getAddress());
        } else {
            warehouseDTO.setRealWarehouseAddress(storeDTO.getName());
        }
        warehouseDTO.setUserId(0L);

        //新开店层级设置为前置仓
        warehouseDTO.setRealWarehouseRank(RealWarehouseRankVO.RW_RANK_3.getType());
        
        // 财务中台用 start
        warehouseDTO.setFactoryName(storeDTO.getName());//工厂名称
        if(storeMdmDTO != null) {
        	warehouseDTO.setCostCenterCode(storeMdmDTO.getCostCenterCode());//成本中心编码
            warehouseDTO.setCostCenterName(storeMdmDTO.getCostCenterName());// 成本中心名称
            warehouseDTO.setCompanyCode(storeMdmDTO.getCompanyCode());//公司编码
            warehouseDTO.setCompanyName(storeMdmDTO.getCompanyName());//公司名称
        }
        warehouseDTO.setAllowNegtiveStock(1);//是否允许负库存(0: 不允许 1: 允许)
        // 财务中台用 end

        String channels = coreConfig.getChannelTypes();
        String[] channelArr = channels.split(",");
        List<String> channelList = Arrays.asList(channelArr);
        List<ChannelShowDTO> channelShows = channelShowDTOS.stream().filter(r -> channelList.contains(r.getChannelType())).collect(Collectors.toList());
        List<ChannelDTO> channelDTOS = new ArrayList<>();
        for(ChannelShowDTO channel : channelShows) {
            ChannelDTO channelDTO = new ChannelDTO();
            channelDTO.setChannelCode(channel.getChannelCode());
            channelDTOS.add(channelDTO);
        }
        warehouseDTO.setChannelDTOList(channelDTOS);
        // 商家ID
        warehouseDTO.setMerchantId(channelShows.get(0).getMerchantId());
        return warehouseDTO;
    }

    /**
     * @Description: 新增叫货渠道 <br>
     *
     * <AUTHOR> 2019/10/11
     * @param storeDTO
     * @param channelShowDTOS
     * @return
     */
    private String addGoodsChannel(com.rome.stock.innerservice.remote.base.dto.StoreDTO storeDTO, List<ChannelShowDTO> channelShowDTOS) {
        String msg = "";
        if(StringUtils.isBlank(storeDTO.getDeliveryFactory())) {
            msg = "未查到门店【" + String.valueOf(storeDTO.getCode()) + "】主数据发货工厂，请手动配置该门店;<br/>";
            return msg;
        }
        String channelCode = "";
        String factoryCode = "";
        ChannelDTO parentChannel = null;
        ChannelDTO jmChannelDTO = null;
        if("1".equals(storeDTO.getStoreProperties()) ||
                "2".equals(storeDTO.getStoreProperties()) ||
                "4".equals(storeDTO.getStoreProperties())) {
            //直营
            factoryCode = storeDTO.getDeliveryFactory();
            channelCode = storeDTO.getCode() + "_119";
            parentChannel = channelFacade.queryParentChannelByCode(channelCode);
        } else if("3".equals(storeDTO.getStoreProperties())) {
            //加盟
            factoryCode = storeDTO.getActualWarehouse();
            if(StringUtils.isBlank(factoryCode)) {
                msg = "未查到门店【" + String.valueOf(storeDTO.getCode()) + "】Z工厂【"+storeDTO.getDeliveryFactory()+"】对应发货工厂，请手动配置该门店;<br/>";
                return msg;
            }
            if(StringUtils.isBlank(storeDTO.getFranchisee())) {
                msg = "未查到门店【" + String.valueOf(storeDTO.getCode()) + "】加盟商，请手动配置该门店;<br/>";
                return msg;
            }
            List<ChannelDTO> channelDTOS = channelFacade.queryChannelByCodeAndType("120", storeDTO.getFranchisee(), "3");
            if(CollectionUtils.isEmpty(channelDTOS)) {
                msg = "未查到门店【" + String.valueOf(storeDTO.getCode()) + "】加盟商渠道，请手动配置该门店;<br/>";
                return msg;
            }
            jmChannelDTO = channelDTOS.get(0);
            channelCode = storeDTO.getCode() + "_120";
            parentChannel = channelFacade.queryParentChannelByCode(channelCode);
        }
        AlikAssert.isNotNull(parentChannel, ResCode.STOCK_ERROR_4015, storeDTO.getCode() + ResCode.STOCK_ERROR_4015_DESC + channelCode);
        //查询渠道虚仓组
        ChannelSalesE channelSalesE = channelSalesRepository.queryByChannelCode(parentChannel.getChannelCode());
        if(channelSalesE == null) {
            msg = "未查到门店【" + String.valueOf(storeDTO.getCode()) + "】所属公司渠道【"+ parentChannel.getChannelCode() +"】关联的策略组，请手动配置该门店;<br/>";
            return msg;
        }
        //查询虚仓
        List<VirtualWarehouseE> virtualWarehouseES = virtualWarehouseRepository.queryByGroupId(channelSalesE.getVirtualWarehouseGroupId());
        List<Long> warehouseIds = virtualWarehouseES.stream().map(VirtualWarehouseE::getRealWarehouseId).distinct().collect(Collectors.toList());
        //查询实仓
        List<RealWarehouse> realWarehouses = realWarehouseService.findByRealWarehouseIds(warehouseIds);
        List<String> factoryCodes = realWarehouses.stream().map(RealWarehouse::getFactoryCode).distinct().collect(Collectors.toList());
        if(!factoryCodes.contains(factoryCode)) {
            //查询发货工厂总仓
            List<Integer> rwTypes = new ArrayList<>();
            rwTypes.add(RealWarehouseTypeVO.RW_TYPE_16.getType());
            List<RealWarehouse> warehouses = realWarehouseService.queryRealWarehouseByFactoryCodeAndRWType(factoryCode, rwTypes);
            if(CollectionUtils.isEmpty(warehouses)) {
                msg = "未查到门店【" + String.valueOf(storeDTO.getCode()) + "】叫货工厂【"+ storeDTO.getDeliveryFactory() +"】对应总仓数据，请手动配置该门店;<br/>";
                return msg;
            }
            //查询虚仓
            List<VirtualWarehouseE> virtualWarehouseEList = virtualWarehouseRepository.queryByRealWarehouseId(warehouses.get(0).getId());
            if(CollectionUtils.isEmpty(virtualWarehouseEList)) {
                msg = "未查到门店【" + String.valueOf(storeDTO.getCode()) + "】叫货工厂【"+ storeDTO.getDeliveryFactory() +"】对应总仓【"+warehouses.get(0).getId()+"】关联的虚仓数据，请手动配置该门店;<br/>";
                return msg;
            }
            List<Long> virtualGroupIds = virtualWarehouseGroupRelationRepository.queryGroupIdsByVmIdList(virtualWarehouseEList.stream().map(VirtualWarehouseE::getId).distinct().collect(Collectors.toList()));
//            List<Long> virtualGroupIds = virtualWarehouseEList.stream().filter(r -> r.getVirtualWarehouseGroupId() != null).map(VirtualWarehouseE::getVirtualWarehouseGroupId).distinct().collect(Collectors.toList());
            if(CollectionUtils.isEmpty(virtualGroupIds)) {
                msg = "未查到门店【" + String.valueOf(storeDTO.getCode()) + "】叫货工厂【"+ storeDTO.getDeliveryFactory() +"】对应总仓【"+warehouses.get(0).getId()+"】关联的虚仓的策略组数据，请手动配置该门店;<br/>";
                return msg;
            }
            //剔除新开店策略组：XKD
            VirtualWarehouseGroupE virtualWarehouseGroupE=virtualWarehouseGroupRepository.selectVirtualWarehouseGroupByCode("XKD");
            if(null !=virtualWarehouseGroupE && virtualGroupIds.contains(virtualWarehouseGroupE.getId())){
                virtualGroupIds.remove(virtualWarehouseGroupE.getId());
            }
            if(virtualGroupIds.size() != 1) {
                msg = "关联策略组不唯一，请手动配置门店【" + String.valueOf(storeDTO.getCode()) + "】叫货渠道;<br/>";
                return msg;
            }
            //新增门店叫货渠道
            ChannelSales channelSales = new ChannelSales();
            if("1".equals(storeDTO.getStoreProperties()) ||
                    "2".equals(storeDTO.getStoreProperties()) ||
                    "4".equals(storeDTO.getStoreProperties())) {
                String finalChannelCode = channelCode;
                String channelName = channelShowDTOS.stream().filter(r -> r.getChannelCode().equals(finalChannelCode)).map(ChannelShowDTO::getChannelName).findAny().orElse("");
                channelSales.setChannelCode(channelCode);
                channelSales.setChannelName(channelName);
                channelSales.setMerchantId(channelShowDTOS.get(0).getMerchantId());
            } else {
                channelSales.setChannelCode(jmChannelDTO.getChannelCode());
                channelSales.setChannelName(jmChannelDTO.getChannelName());
                channelSales.setMerchantId(jmChannelDTO.getMerchantId());
            }
            channelSales.setVirtualWarehouseGroupId(virtualGroupIds.get(0));
            channelSales.setShowRate(100);
            channelSalesService.addChannelSales(channelSales);
        }
        return msg;
    }

    public boolean addShop(StringBuffer emailMsg, StoreDTO storeDTO) {
        RealWarehouse realWarehouse = realWarehouseService.queryRealWarehouseByInCode(storeDTO.getCode());
        if(realWarehouse != null) {
            log.info("新增店铺【{}】已存在，无需处理", storeDTO.getCode());
            return true;
        }
        // 渠道编码
        ChannelShowDTO channelShowDTO = new ChannelShowDTO();
        channelShowDTO.setSaleUnitCode(storeDTO.getCode());
        List<ChannelShowDTO> channelShowDTOS = channelFacade.channelPageShow(channelShowDTO, 1, PageConstants.ROWS_50);
        // 叫货渠道
        com.rome.stock.innerservice.remote.base.dto.StoreDTO storeInfo = shopFacade.searchByCode(storeDTO.getCode());
        log.warn(CoreKibanaLog.getJobLog(KibanaLogConstants.CONSUMER_MQ, "storeDomainData", "新增门店消息消费门店【"+ storeDTO.getCode() +"】主数据", JSON.toJSONString(storeInfo)));
        if(storeInfo == null) {
            AlikAssert.isTrue(false, ResCode.STOCK_ERROR_1002, "门店"+storeDTO.getCode()+"主数据不存在");
        }
        StoreMdmAttributeExtDTO storeMdmDTO = shopFacade.selectMdmStoreInfoByCode(storeDTO.getCode());
        if(storeMdmDTO == null) {
        	log.error("门店【{}】新增selectMdmStoreInfoByCodes接口返回为null", storeDTO.getCode());
//            AlikAssert.isTrue(false, ResCode.STOCK_ERROR_1002, "门店"+storeDTO.getCode()+"主数据mdm不存在");
        }
//        if(StringUtils.isBlank(storeMdmDTO.getCompanyCode())) {
//			throw new RomeException(ResCode.STOCK_ERROR_1004, "新增店铺【"+storeDTO.getCode()+"】公司编码不能为空");
//		}
        // 最新判断虚拟门店的接口：/api/v1/storeMdmAttribute/selectMdmStoreInfoByCodes
        // 字段：storeExistenceForm  门店存在形式[0-线上门店,1-线下有执照门店,2-线下无执照门店（展会）
        // storeExistenceForm=0为虚拟门店,并且StoreProperties以这个接口为准
        if("0".equals(storeMdmDTO.getStoreExistenceForm())) {
            // 特殊逻辑，加盟线上店设置仓库类型为2， 贾妮已确认
            if (!"3".equals(storeMdmDTO.getStoreProperties())){
                storeDTO.setStoreProperties("5");
            }
        } else {
        	storeInfo.setStoreProperties(storeMdmDTO.getStoreProperties());
        }
        // app过账门店仓
        if("5".equals(storeInfo.getStoreProperties())) {
        	addAccountShop(storeDTO, storeInfo, storeMdmDTO);
        	return false;
        }
        // 去除叫货渠道和叫货渠道关联的仓逻辑 2022-11-18
//        String msgTemp = this.addGoodsChannel(storeInfo, channelShowDTOS);
//        if(StringUtils.isNotBlank(msgTemp)) {
//            emailMsg.append(msgTemp);
//            return true;
//        }
        // 数据转换
        WarehouseDTO warehouseDTO = this.getWarehouseDTO(storeDTO, channelShowDTOS, storeMdmDTO);
        warehouseDTO.setRwBusinessType(getRwBusinessTypeByStore(storeInfo));//仓库业务类型: 1: 直营门店 2: 加盟门店 3: 仓库 4. 其他仓库
        // 设置省市区
        warehouseDTO.setProvinceCode(storeInfo.getProvinceCode());
        warehouseDTO.setRealWarehouseProvince(storeInfo.getProvince());
        warehouseDTO.setCityCode(storeInfo.getCityCode());
        warehouseDTO.setRealWarehouseCity(storeInfo.getCity());
        warehouseDTO.setCountryCode(storeInfo.getDistrictCode());
        warehouseDTO.setRealWarehouseCounty(storeInfo.getCounty());
        //设置行政区域
        if(StringUtils.isNotBlank(warehouseDTO.getProvinceCode())){
            //根据省查询行政区域
            RegionDivideInfosDTO dto = shopFacade.queryRegionByProvinceCode(warehouseDTO.getProvinceCode());
            if(dto != null){
                warehouseDTO.setRegionId(dto.getAreaId());
                warehouseDTO.setRegionName(dto.getRegionName());
            }
        }
        // 新增仓库
        xtStockService.createWarehouse(warehouseDTO);
        return false;
    }

    /**
     * 获取门店仓的业务类型
     * @param storeDTO
     * @return
     */
    public Integer getRwBusinessTypeByStore(com.rome.stock.innerservice.remote.base.dto.StoreDTO storeDTO) {
    	if("1".equals(storeDTO.getStoreProperties()) ||
                "2".equals(storeDTO.getStoreProperties()) ||
                "4".equals(storeDTO.getStoreProperties())) {
            //直营
            return 1;
        } else if("3".equals(storeDTO.getStoreProperties())) {
            //加盟
            return 2;
        } else if("5".equals(storeDTO.getStoreProperties())) {
            //其他仓
            return 4;
        }
    	return null;
    }
    /**
     * app过账门店仓
     * @param storeDTO mq门店仓的信息
     * @param storeInfo 查询出的信息
     * @param storeMdmDTO mdm信息
     */
    private void addAccountShop(StoreDTO storeDTO, com.rome.stock.innerservice.remote.base.dto.StoreDTO storeInfo, StoreMdmAttributeExtDTO storeMdmDTO) {
    	RealWarehouseAddDTO warehouseDTO = new RealWarehouseAddDTO();
    	warehouseDTO.setRealWarehouseCode(storeDTO.getCode());
        warehouseDTO.setFactoryCode(storeDTO.getCode());
        // ******** 仓库编码固定值
        warehouseDTO.setRealWarehouseOutCode("0001");
//        warehouseDTO.setWarehouseType(3);
        warehouseDTO.setRealWarehouseType(RealWarehouseTypeVO.RW_TYPE_1.getType());
        warehouseDTO.setShopCode(storeDTO.getCode());
        warehouseDTO.setRealWarehouseName(storeDTO.getName());
        if(StringUtils.isNotBlank(storeDTO.getAddress())) {
            warehouseDTO.setRealWarehouseAddress(storeDTO.getAddress());
        } else {
            warehouseDTO.setRealWarehouseAddress(storeDTO.getName());
        }
        warehouseDTO.setUserId(0L);

        //新开店层级设置为前置仓
        warehouseDTO.setRealWarehouseRank(RealWarehouseRankVO.RW_RANK_3.getType());
        
        // 设置省市区
        warehouseDTO.setRealWarehouseProvinceCode(storeInfo.getProvinceCode());
        warehouseDTO.setRealWarehouseProvince(storeInfo.getProvince());
        warehouseDTO.setRealWarehouseCityCode(storeInfo.getCityCode());
        warehouseDTO.setRealWarehouseCity(storeInfo.getCity());
        warehouseDTO.setRealWarehouseCountryCode(storeInfo.getDistrictCode());
        warehouseDTO.setRealWarehouseCounty(storeInfo.getCounty());
        //设置行政区域
        if(StringUtils.isNotBlank(warehouseDTO.getRealWarehouseProvinceCode())){
            //根据省查询行政区域
            RegionDivideInfosDTO dto = shopFacade.queryRegionByProvinceCode(warehouseDTO.getRealWarehouseProvinceCode());
            if(dto != null){
                warehouseDTO.setRegionId(dto.getAreaId());
                warehouseDTO.setRegionName(dto.getRegionName());
            }
        }
        // 财务中台用 start
        warehouseDTO.setFactoryName(storeDTO.getName());//工厂名称
        if(storeMdmDTO != null) {
        	warehouseDTO.setCostCenterCode(storeMdmDTO.getCostCenterCode());//成本中心编码
            warehouseDTO.setCostCenterName(storeMdmDTO.getCostCenterName());// 成本中心名称
            warehouseDTO.setCompanyCode(storeMdmDTO.getCompanyCode());//公司编码
            warehouseDTO.setCompanyName(storeMdmDTO.getCompanyName());//公司名称
        }
        warehouseDTO.setRwBusinessType(getRwBusinessTypeByStore(storeInfo));//仓库业务类型: 1: 直营门店 2: 加盟门店 3: 仓库 4. 其他仓库
        warehouseDTO.setAllowNegtiveStock(1);//是否允许负库存(0: 不允许 1: 允许)
        // 保存
        realWarehouseService.addRealWarehouse(warehouseDTO);
    }
}
