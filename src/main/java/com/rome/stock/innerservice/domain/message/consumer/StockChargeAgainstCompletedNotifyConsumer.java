package com.rome.stock.innerservice.domain.message.consumer;

import com.alibaba.fastjson.JSON;
import com.rome.scm.common.rocketmq.CustomConsumerListener;
import com.rome.stock.common.enums.CustomRocketMQEnum;
import com.rome.stock.innerservice.api.dto.ChargeAgainstDTO;
import com.rome.stock.innerservice.domain.service.ChargeAgainstService;
import lombok.extern.slf4j.Slf4j;
import org.apache.rocketmq.common.message.MessageExt;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;

/**
 *  冲销收货、发货完成，消费消息
 */
@Slf4j
@Service
public class StockChargeAgainstCompletedNotifyConsumer implements CustomConsumerListener<MessageExt> {

    @Resource
    private ChargeAgainstService chargeAgainstService;

    @Override
    public void onMessage(MessageExt messageExt, String businessNo, String msgKey) {
        log.info("冲销收货、发货完成，消费消息，msgID: {}，消费次数: {}", messageExt.getMsgId(), messageExt.getReconsumeTimes());
        ChargeAgainstDTO chargeAgainstDTO = JSON.parseObject(messageExt.getBody(), ChargeAgainstDTO.class);
        if(chargeAgainstDTO == null) {
            log.error("冲销收货、发货完成，消费消息 消费内容为空，msgID: {}", messageExt.getMsgId());
            return ;
        }
        //单据发货完成通知订单中心
        chargeAgainstService.pushNotifyData(chargeAgainstDTO);
    }

    @Override
    public String getRocketMQCode() {
        return CustomRocketMQEnum.MQ_STOCK_CHARGE_AGAINST_COMPLETED_NOTIFY_PUSH.getCode();
    }

}
