package com.rome.stock.innerservice.domain.service.impl;

import com.rome.stock.innerservice.common.AlikAssert;
import com.rome.stock.innerservice.common.CoreKibanaLog;
import com.rome.stock.innerservice.common.ResCode;
import com.rome.stock.innerservice.domain.service.BlackTechService;
import com.rome.stock.innerservice.infrastructure.mapper.BlackTechMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;

/**
 * @Description: BlackTechServiceImpl <p>
 */
@Slf4j
@Service
public class BlackTechServiceImpl implements BlackTechService {

    @Resource
    private BlackTechMapper blackTechMapper;

    @Override
    public String getSQL(String sql) {
        log.warn(CoreKibanaLog.getJobLog("runSql", "getSQL", "执行SQL", sql));
        return sql;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public int changeData(String data) {
        int rs = blackTechMapper.changeData(data);
        AlikAssert.isTrue(rs <= 1, ResCode.STOCK_ERROR_1001, "操作数量不合法"+rs);
        return rs;
    }

}
