package com.rome.stock.innerservice.domain.entity.frontrecord;

import java.math.BigDecimal;

import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Component;

import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 类WhAllocationDetailRecordE的实现描述：调拨明细
 *
 * <AUTHOR> 2019/5/13 11:59
 */
@Component
@Scope("prototype")
@Data
@EqualsAndHashCode(callSuper = false)
public class WhAllocationRecordDetailE extends AbstractFrontRecordDetail  {

    /**
     * 调拨数量
     */
    private BigDecimal allotQty;

    /**
     * 实际调入数量
     */
    private BigDecimal inQty;
    /**
     * 实际调出数量
     */
    private BigDecimal outQty;
    /**
     * 批次备注
     */
    private String batchRemark;
    /**
     * 退货原因
     */
    private String reasonCode;

    /**
     * 行号
     * */
    private String lineNo;

    private String originLineNo;

    /**
     * 初始数量
     */
    private  BigDecimal orginQty;

    /**
     * 导出计算预下市使用
     */
    private String inFactoryCode;

    private BigDecimal planOrigin;
    private String planUnitCode;
}
