package com.rome.stock.innerservice.domain.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import com.rome.arch.core.exception.RomeException;
import com.rome.stock.common.constants.CommonConstants;
import com.rome.stock.common.constants.TmsConstants;
import com.rome.stock.common.enums.warehouse.WarehouseRecordTypeVO;
import com.rome.stock.innerservice.api.dto.WarehouseRecordPageDTO;
import com.rome.stock.innerservice.api.dto.frontrecord.UpdateBillCodeDTO;
import com.rome.stock.innerservice.api.dto.warehouserecord.RecordPackageDTO;
import com.rome.stock.innerservice.common.*;
import com.rome.stock.innerservice.domain.convertor.WarehouseRecordConvertor;
import com.rome.stock.innerservice.domain.convertor.record.RecordPackageConvertor;
import com.rome.stock.innerservice.domain.entity.AddressE;
import com.rome.stock.innerservice.domain.entity.RealWarehouseE;
import com.rome.stock.innerservice.domain.entity.RwRecordPoolDetailE;
import com.rome.stock.innerservice.domain.entity.RwRecordPoolE;
import com.rome.stock.innerservice.domain.entity.frontrecord.WDTOnlineRetailE;
import com.rome.stock.innerservice.domain.entity.record.RecordPackageDetailE;
import com.rome.stock.innerservice.domain.entity.warehouserecord.WarehouseRecordDetail;
import com.rome.stock.innerservice.domain.entity.warehouserecord.WarehouseRecordE;
import com.rome.stock.innerservice.domain.repository.AddressRepository;
import com.rome.stock.innerservice.domain.repository.RealWarehouseRepository;
import com.rome.stock.innerservice.domain.repository.RwRecordPoolRepository;
import com.rome.stock.innerservice.domain.repository.frontrecord.FrSaleRepository;
import com.rome.stock.innerservice.domain.repository.frontrecord.FrWDTSaleRepository;
import com.rome.stock.innerservice.domain.repository.warehouserecord.RecordPackageRepository;
import com.rome.stock.innerservice.domain.repository.warehouserecord.WarehouseRecordRepository;
import com.rome.stock.innerservice.domain.service.FulfillmentJobService;
import com.rome.stock.innerservice.remote.item.dto.SkuInfoExtDTO;
import com.rome.stock.innerservice.remote.item.facade.SkuFacade;
import com.rome.stock.innerservice.remote.orderCneter.dto.CommonFrontRecordDTO;
import com.rome.stock.innerservice.remote.orderCneter.dto.DoOrderDTO;
import com.rome.stock.innerservice.remote.orderCneter.facade.OrderCenterFacade;
import com.rome.stock.innerservice.remote.orderTrack.facade.OrderTrackFacade;
import com.rome.stock.innerservice.remote.tms.dto.TmsPackageItemVO;
import com.rome.stock.innerservice.remote.tms.dto.TmsPackageVO;
import com.rome.stock.innerservice.remote.trade.dto.DoCodeDeliveryDTO;
import com.rome.stock.innerservice.remote.trade.dto.DoCodeDeliveryDetailDTO;
import com.rome.stock.innerservice.remote.trade.facade.FulfillmentFacade;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * @Doc:.....
 * @Author: lchy
 * @Date: 2019/9/4
 * @Version 1.0
 */
@Service
@Slf4j
public class FulfillmentJobServiceImpl implements FulfillmentJobService {
    @Resource
    private WarehouseRecordRepository warehouseRecordRepository;
    @Resource
    private WarehouseRecordConvertor warehouseRecordConvertor;
    @Resource
    private RwRecordPoolRepository rwRecordPoolRepository;
    @Resource
    private FulfillmentFacade fulfillmentFacade;
    @Resource
    private OrderTrackFacade orderTrackFacade;
    @Resource
    private TmsTools tmsTools;
    @Autowired
    private CoreConfig coreConfig;


    @Resource
    private SkuQtyUnitTools skuQtyUnitTools;

    @Resource
    private RecordPackageRepository recordPackageRepository;

    @Resource
    private RecordPackageConvertor recordPackageConvertor;

    @Resource
    private AddressRepository addressRepository;
    @Resource
    private RealWarehouseRepository realWarehouseRepository;

    @Resource
    private FrWDTSaleRepository frWDTSaleRepository;

    @Resource
    private EhubTools ehubTools;
    @Resource
    private SkuFacade skuFacade;
    @Resource
    private OrderCenterFacade orderCenterFacade;

    /**
     * 查询待同步交货信息给捋单系统的单据
     *
     * @param page
     * @param maxResult
     * @return
     */
    @Override
    public List<WarehouseRecordPageDTO> getWaitSyncDeliveryToFulfillmentOrder(int page, int maxResult) {
        List<WarehouseRecordE> entityList = warehouseRecordRepository.getWaitSyncDeliveryToFulfillmentOrder(page, maxResult);
        return warehouseRecordConvertor.entityToWarehouseDto(entityList);
    }

    /**
     * 同步交货信息给捋单系统
     *
     * @param orderDto
     */
    @Override
    public void syncDeliveryFulfillment(WarehouseRecordPageDTO orderDto) {
        log.info("同步do交货信息给捋单系统：doCode=" + orderDto.getRecordCode());
        List<DoCodeDeliveryDTO> param = new ArrayList<>();
        List<RwRecordPoolE> poolEList = rwRecordPoolRepository.queryKeysWithDetailsByWarehouseId(orderDto.getId());
        if (poolEList.size() == 0) {
            log.info("====没有对应的子do：" + orderDto.getRecordCode());
            warehouseRecordRepository.updateToHasSyncDelivery(orderDto.getId());
            return;
        }
        Map<String, String> frontRecordMap = new HashMap<>();
        //旺店通的需要传订单号
        boolean isWdt = false ;
        if (orderDto.getRecordType().equals(WarehouseRecordTypeVO.ONLINE_RETAILERS_OUT_RECORD.getType())
                ||orderDto.getRecordType().equals(WarehouseRecordTypeVO.WDT_ONLINE_RETAILERS_OUT_RECORD.getType())
                ||orderDto.getRecordType().equals(WarehouseRecordTypeVO.WINE_SALE_WAREHOUSE_RECORD.getType())
                ||orderDto.getRecordType().equals(WarehouseRecordTypeVO.WINE_BUCKET_SALE_WAREHOUSE_RECORD.getType())) {
            List<String> frontRecordCodes = poolEList.stream().map(RwRecordPoolE::getFrontRecordCode).distinct().collect(Collectors.toList());
            List<WDTOnlineRetailE> frontRecordList = frWDTSaleRepository.queryByRecordCodes(frontRecordCodes);
            if(!CollectionUtils.isEmpty(frontRecordList)){
                frontRecordMap = frontRecordList.stream().collect(Collectors.toMap(WDTOnlineRetailE::getRecordCode,WDTOnlineRetailE::getOutRecordCode,(v1,v2)->v2));
                isWdt = true ;
            }
        }
        //从子do单中读取sku的销售单位，用于后面将基本单位的数量转为销售单位的数量
        for (RwRecordPoolE pool : poolEList) {
            DoCodeDeliveryDTO doCodeDeliveryDTO = new DoCodeDeliveryDTO();
            doCodeDeliveryDTO.setDoNo(pool.getDoCode());
            if (frontRecordMap.containsKey(pool.getFrontRecordCode())) {
                doCodeDeliveryDTO.setOrderNo(frontRecordMap.get(pool.getFrontRecordCode()));
            }
            doCodeDeliveryDTO.setWarehouseRecordCode(pool.getWarehouseRecordCode());
            List<DoCodeDeliveryDetailDTO> dtos = new ArrayList<>();
            for (RwRecordPoolDetailE detailPool : pool.getRwRecordPoolDetails()) {
                DoCodeDeliveryDetailDTO dto = new DoCodeDeliveryDetailDTO();
                dto.setSkuCode(detailPool.getSkuCode());
                dto.setSkuId(detailPool.getSkuId());
                dto.setSkuQty(detailPool.getSkuQty());
                if(detailPool.getLineNo() != null) {
                    dto.setOrderLineNo(detailPool.getLineNo());
                }
                dto.setSalesUnit(detailPool.getUnitCode());
                dtos.add(dto);
            }
            doCodeDeliveryDTO.setDoItemList(dtos);
            param.add(doCodeDeliveryDTO);
        }
        boolean res = fulfillmentFacade.deliverNotify(orderDto.getRecordCode() ,param ,isWdt);
        if (res) {
            warehouseRecordRepository.updateToHasSyncDelivery(orderDto.getId());
        }
        log.info("同步do交货信息给捋单系统结束：doCode=" + orderDto.getRecordCode() + ",结果=" + res);
    }

    /**
     * 同步拆单信息给捋单
     */
    @Override
    public void doSplitNotifyJob() {
        log.info("同步do拆单信息给捋单系统 job 开始");
        int page;
        int currentPage = 1;
        int maxResult = 100;
        List<RwRecordPoolE> tempResult;
        List<List<RwRecordPoolE>> result = new ArrayList<>();
        do {
            page = (currentPage - 1) * maxResult;
            tempResult = rwRecordPoolRepository.queryWaitSyncDoCodes(page, maxResult);
            if (tempResult.size() > 0) {
                result.add(tempResult);
            }
            currentPage++;
        } while (tempResult.size() == maxResult);

        if (result.size() > 0) {
            //分页推送
            List<DoCodeDeliveryDTO> param;
            List<String> frontRecordCodes;
            List<WDTOnlineRetailE> frontRecordList;
            Map<String, WDTOnlineRetailE> frontRecordMap;
            List<Long> ids;
            for (List<RwRecordPoolE> pools : result) {
                param = new ArrayList<>();
                frontRecordCodes = pools.stream().map(RwRecordPoolE::getFrontRecordCode).distinct().collect(Collectors.toList());
                frontRecordList = frWDTSaleRepository.queryByRecordCodes(frontRecordCodes);
                frontRecordMap = frontRecordList.stream().collect(Collectors.toMap(WDTOnlineRetailE::getRecordCode, item -> item));
                ids = new ArrayList<>();
                for (RwRecordPoolE pool : pools) {
                    DoCodeDeliveryDTO doCodeDeliveryDTO = new DoCodeDeliveryDTO();
                    doCodeDeliveryDTO.setDoNo(pool.getDoCode());
                    doCodeDeliveryDTO.setOrderNo(frontRecordMap.get(pool.getFrontRecordCode()).getOutRecordCode());
                    List<DoCodeDeliveryDetailDTO> dtos = new ArrayList<>();
                    for (RwRecordPoolDetailE detailPool : pool.getRwRecordPoolDetails()) {
                        DoCodeDeliveryDetailDTO dto = new DoCodeDeliveryDetailDTO();
                        dto.setSkuCode(detailPool.getSkuCode());
                        dto.setSkuId(detailPool.getSkuId());
                        dto.setSkuQty(detailPool.getSkuQty());
                        dto.setOrderLineNo(detailPool.getLineNo());
                        dto.setSalesUnit(detailPool.getUnitCode());
                        dtos.add(dto);
                    }
                    ids.add(pool.getId());
                    doCodeDeliveryDTO.setDoItemList(dtos);
                    param.add(doCodeDeliveryDTO);
                }
                boolean res = fulfillmentFacade.doSplitNotify(param);
                if (res) {
                    rwRecordPoolRepository.batchUpdateTohasSync(ids);
                }
            }
        }
        log.info("同步do拆单信息给捋单系统结束");
    }

    /**
     * 同步推送do单到交易
     * @param pool
     */
    @Override
    public void syncNotifyToTradeByOnePool(RwRecordPoolE pool , String orderCode){
        List<DoCodeDeliveryDTO> param = new ArrayList<>();
        List<Long> ids = new ArrayList<>();
        DoCodeDeliveryDTO doCodeDeliveryDTO = new DoCodeDeliveryDTO();
        doCodeDeliveryDTO.setDoNo(pool.getDoCode());
        doCodeDeliveryDTO.setOrderNo(orderCode);
        List<DoCodeDeliveryDetailDTO> dtos = new ArrayList<>();
        for (RwRecordPoolDetailE detailPool : pool.getRwRecordPoolDetails()) {
            DoCodeDeliveryDetailDTO dto = new DoCodeDeliveryDetailDTO();
            dto.setSkuCode(detailPool.getSkuCode());
            dto.setSkuId(detailPool.getSkuId());
            dto.setSkuQty(detailPool.getSkuQty());
            dto.setOrderLineNo(detailPool.getLineNo());
            dto.setSalesUnit(detailPool.getUnitCode());
            dtos.add(dto);
        }
        ids.add(pool.getId());
        doCodeDeliveryDTO.setDoItemList(dtos);
        param.add(doCodeDeliveryDTO);
        boolean res = fulfillmentFacade.doSplitNotify(param);
        if (res) {
            rwRecordPoolRepository.batchUpdateTohasSync(ids);
        }
    }

    /**
     * 查询待同步给tms的包裹单
     *
     * @param page
     * @param maxResult
     * @return
     */
    @Override
    public List<RecordPackageDTO> getWaitTransferToTMSPackage(int page, int maxResult,Integer flag) {
        List<RecordPackageDTO> result=recordPackageConvertor.convertEntityListToDTOList(recordPackageRepository.queryWaitSyncPackage(page, maxResult,flag));
        return result;
    }

    /**
     * 推送包裹信息给tms
     */
    @Override
    public void syncPackageInfoToTMS(RecordPackageDTO doPackage) {
        log.info("同步tms包裹信息开始:packageCode=" + doPackage.getPackageCode());
        WarehouseRecordE warehouseRecordE = warehouseRecordRepository.queryWarehouseRecordByRecordCode(doPackage.getRecordCode());
        AlikAssert.isNotNull(warehouseRecordE, ResCode.STOCK_ERROR_1017, ResCode.STOCK_ERROR_1017_DESC);
        TmsPackageVO tmsPackageVO = null;
        List<String> orderCodes = Lists.newArrayList();
        if (Objects.equals(WarehouseRecordTypeVO.WAREHOUSE_OUT_USE_RECORD.getType(), warehouseRecordE.getRecordType())) {
            //电商仓领用快递直发
            List<CommonFrontRecordDTO> resList = orderCenterFacade.queryCommonFrontRecordInfoByRecordCode(warehouseRecordE.getRecordCode());
            if (CollectionUtil.isNotEmpty(resList)) {
                CommonFrontRecordDTO commonFrontRecordDTO = resList.get(0);
                orderCodes.add(commonFrontRecordDTO.getPlatformCode());
                tmsPackageVO = this.buildTmsPackageForWarehouseReceive(doPackage, commonFrontRecordDTO);
            }
        } else if (Objects.equals(warehouseRecordE.getSyncTransferStatus(), 0)) {
            //非TOC配置化,走老代码
            orderCodes = orderTrackFacade.queryByRecordCode(doPackage.getRecordCode());
            tmsPackageVO = buildTmsPackage(doPackage);
        } else {
            List<DoOrderDTO> frontOrders = orderCenterFacade.queryDOByOutCodes(Arrays.asList(doPackage.getRecordCode()));
            if (CollectionUtils.isEmpty(frontOrders)) {
                throw new RomeException(ResCode.STOCK_ERROR_1001, "查询订单中心前置单信息为空,recordCode:" + doPackage.getRecordCode());
            }
            orderCodes = frontOrders.stream().map(DoOrderDTO::getOutRecordCode).collect(Collectors.toList());
            tmsPackageVO = buildTmsPackageNew(doPackage, frontOrders);
        }
        //新增发货人手机号字段
        RealWarehouseE realWarehouseE = realWarehouseRepository.getRealWarehouseById(warehouseRecordE.getRealWarehouseId());
        if(Objects.nonNull(realWarehouseE)){
            tmsPackageVO.setRealWarehouseMobile(realWarehouseE.getRealWarehouseMobile());
        }
        tmsPackageVO.setSoCodes(orderCodes);
        //同步包裹信息给tms
        JSONObject obj = tmsTools.postData(tmsPackageVO, coreConfig.getTmsUrl(), coreConfig.getTmsClientId(), TmsConstants.TMS_RECEIVE_PACKAGE);
        boolean result = obj != null && CommonConstants.CODE_SUCCESS.equals(obj.get("code").toString());
        if (result) {
            recordPackageRepository.updateSyncSucc(doPackage.getId());
            try {
                orderTrackFacade.save(orderCodes, "发货完成,推送包裹信息给TMS", "");
            } catch (Exception e) {
                log.error(e.getMessage(), e);
            }
        }
        log.info("同步tms包裹信息接收:packageCode={},结果={}", doPackage.getPackageCode(), result);
    }

    /**
     * 仓库领用回传包裹给TMS
     * @param doPackage
     * @param doOrderDTO
     * @return
     */
    private TmsPackageVO buildTmsPackageForWarehouseReceive(RecordPackageDTO doPackage, CommonFrontRecordDTO doOrderDTO) {
        WarehouseRecordE warehouseRecordE = warehouseRecordRepository.getRecordWithDetailByCode(doPackage.getRecordCode(), null);
        AlikAssert.isNotNull(warehouseRecordE, ResCode.STOCK_ERROR_1023, ResCode.STOCK_ERROR_1023_DESC + ",包裹号：" + doPackage.getPackageCode());
        Map<String, WarehouseRecordDetail> skuMap = RomeCollectionUtil.listforMap(warehouseRecordE.getWarehouseRecordDetails(), "skuCode");
        TmsPackageVO tmsPackageVO = new TmsPackageVO();
        tmsPackageVO.setBillCode(doPackage.getExpressCode());
        tmsPackageVO.setDeliveryCompanyId(doPackage.getLogisticsCode());
        tmsPackageVO.setDeliveryCompanyName(doPackage.getLogisticsName());
        tmsPackageVO.setGoodReceiverAddress(doOrderDTO.getAddress());
        tmsPackageVO.setOrderCode(doPackage.getRecordCode());
        tmsPackageVO.setOrderCreateTime(doOrderDTO.getPayTime()==null?warehouseRecordE.getOutCreateTime():doOrderDTO.getPayTime());
        tmsPackageVO.setOrderLogisticsTime(doPackage.getCreateTime());
        tmsPackageVO.setPackageCode(doPackage.getPackageCode());
        tmsPackageVO.setWeight(doPackage.getWeight());
        tmsPackageVO.setSource("zt");
        //添加部分省市区字段
        tmsPackageVO.setGoodReceiverMobile(doOrderDTO.getContacts());
        tmsPackageVO.setGoodReceiverName(doOrderDTO.getReceiverName());
        tmsPackageVO.setGoodReceiverProvince(doOrderDTO.getProvince());
        tmsPackageVO.setGoodReceiverCity(doOrderDTO.getCity());
        tmsPackageVO.setLogisticsCode(doPackage.getLogisticsCode());
        tmsPackageVO.setGoodReceiverArea(doOrderDTO.getCounty());
        try{
            if(null !=doOrderDTO.getCountyCode()){
                tmsPackageVO.setGoodReceiverAreaId(Long.valueOf(doOrderDTO.getCountyCode()));
            }
            if(null !=doOrderDTO.getCityCode()){
                tmsPackageVO.setGoodReceiverCityId(Long.valueOf(doOrderDTO.getCityCode()));
            }
            if(null !=doOrderDTO.getProvinceCode()){
                tmsPackageVO.setGoodReceiverProvinceId(Long.valueOf(doOrderDTO.getProvinceCode()));
            }
        }catch (Exception e){
            log.error("包裹信息转换错误:packageCode=" + doPackage.getPackageCode(), e);
        }
        List<RecordPackageDetailE> detailES = recordPackageRepository.queryDetailsByPackageId(doPackage.getId());
        List<String> skuCodes=detailES.stream().map(RecordPackageDetailE::getSkuCode).collect(Collectors.toList());
        List<SkuInfoExtDTO> skuInfoExtDTOList= skuFacade.skusBySkuCode(skuCodes);
        Map<String,SkuInfoExtDTO> skuInfoExtDTOSMap=skuInfoExtDTOList.stream().collect(Collectors.toMap(SkuInfoExtDTO::getSkuCode, Function.identity(), (v1, v2) -> v1));
        List<TmsPackageItemVO> itemVOS = new ArrayList<>();
        for (RecordPackageDetailE dto : detailES) {
            TmsPackageItemVO vo = new TmsPackageItemVO();
            vo.setOrderCode(doPackage.getRecordCode());
            vo.setCode(dto.getSkuCode());
            vo.setPackageCode(doPackage.getPackageCode());
            vo.setProductItemOutNum(dto.getSkuQty().intValue());
            if (!skuMap.containsKey(dto.getSkuCode())) {
                throw new RomeException(ResCode.STOCK_ERROR_1019, ResCode.STOCK_ERROR_1019_DESC + ",包裹号：" + doPackage.getPackageCode() + "sku:" + dto.getSkuCode());
            }
            vo.setWarehouseId(warehouseRecordE.getRealWarehouseId());
            vo.setUnit(skuMap.get(dto.getSkuCode()).getUnitCode());
            if(skuInfoExtDTOSMap.containsKey(dto.getSkuCode())){
                vo.setName(skuInfoExtDTOSMap.get(dto.getSkuCode()).getName());
            }
            itemVOS.add(vo);
        }
        tmsPackageVO.setRealWarehouseId(warehouseRecordE.getRealWarehouseId());
        tmsPackageVO.setTmsPackageItems(itemVOS);
        return tmsPackageVO;
    }


    /**
     * 推送包裹信息给ehub
     */
    @Override
    @Deprecated
    public void syncPackageInfoToEhub(RecordPackageDTO doPackage) {
        log.info("同步ehub包裹信息开始:packageCode=" + doPackage.getPackageCode());
        WarehouseRecordE warehouseRecordE = warehouseRecordRepository.queryWarehouseRecordByRecordCode(doPackage.getRecordCode());
        AlikAssert.isNotNull(warehouseRecordE, ResCode.STOCK_ERROR_1017, ResCode.STOCK_ERROR_1017_DESC);
        TmsPackageVO tmsPackageVO=null;
        List<String> orderCodes = Lists.newArrayList();
        if(Objects.equals(warehouseRecordE.getSyncTransferStatus(), 0)){
            //非TOC配置化,走老代码
            orderCodes = orderTrackFacade.queryByRecordCode(doPackage.getRecordCode());
            tmsPackageVO = buildTmsPackage(doPackage);
        }else{
            List<DoOrderDTO> frontOrders=orderCenterFacade.queryDOByOutCodes(Arrays.asList(doPackage.getRecordCode()));
            if(CollectionUtils.isEmpty(frontOrders)){
                throw new RomeException(ResCode.STOCK_ERROR_1001,"查询订单中心前置单信息为空,recordCode:"+doPackage.getRecordCode());
            }
            orderCodes = frontOrders.stream().map(DoOrderDTO::getOutRecordCode).collect(Collectors.toList());
            tmsPackageVO = buildTmsPackageNew(doPackage,frontOrders);
        }
        tmsPackageVO.setSoCodes(orderCodes);
        //同步包裹信息给ehub
        String result=ehubTools.postData(tmsPackageVO);
        if (result.contains("成功")) {
            recordPackageRepository.updateSyncSucc(doPackage.getId());
            try {
                orderTrackFacade.save(orderCodes,"发货完成,推送包裹信息给ehub" ,"");
            } catch (Exception e) {
                log.error(e.getMessage() ,e );
            }
        }
        log.info("同步ehub包裹信息接收:packageCode={},结果={}", doPackage.getPackageCode(), result);
    }


    /**
     * 推送TMS后修改运单号
     * @param updateBillCodeDTO
     * @return
     */
    @Override
    public Boolean updateBillCodeByPackageCode(UpdateBillCodeDTO updateBillCodeDTO) {
        String packageCode=updateBillCodeDTO.getPackageCode();
        String billCode=updateBillCodeDTO.getBillCode();
        log.info("推送TMS后修改运单号开始:packageCode=" + packageCode);
        TmsPackageVO tmsPackageVO = new TmsPackageVO();
        tmsPackageVO.setBillCode(billCode);
        tmsPackageVO.setPackageCode(packageCode);
        tmsPackageVO.setDeliveryCompanyId(updateBillCodeDTO.getLogisticsCode());
        tmsPackageVO.setDeliveryCompanyName(updateBillCodeDTO.getLogisticsName());
        JSONObject obj = tmsTools.postUpdateBillCodeData(tmsPackageVO, coreConfig.getTmsUrl(), coreConfig.getTmsClientId(), TmsConstants.TMS_UPDATE_BILLCODE);
        boolean result = obj != null && CommonConstants.CODE_SUCCESS.equals(obj.getString("code"));
        log.info("推送TMS后修改运单号:packageCode={},结果={}", packageCode, result);
        return result;
    }

    /**
     * 构建推送tms包裹信息的入参
     * @param doPackage
     * @return
     */
    private TmsPackageVO buildTmsPackageNew(RecordPackageDTO doPackage,List<DoOrderDTO> frontOrders){
        WarehouseRecordE warehouseRecordE = warehouseRecordRepository.getRecordWithDetailByCode(doPackage.getRecordCode(), null);
        AlikAssert.isNotNull(warehouseRecordE, ResCode.STOCK_ERROR_1023, ResCode.STOCK_ERROR_1023_DESC + ",包裹号：" + doPackage.getPackageCode());
        Map<String, WarehouseRecordDetail> skuMap = RomeCollectionUtil.listforMap(warehouseRecordE.getWarehouseRecordDetails(), "skuCode");
        DoOrderDTO doOrderDTO=frontOrders.get(0);
        TmsPackageVO tmsPackageVO = new TmsPackageVO();
        tmsPackageVO.setBillCode(doPackage.getExpressCode());
        tmsPackageVO.setDeliveryCompanyId(doPackage.getLogisticsCode());
        tmsPackageVO.setDeliveryCompanyName(doPackage.getLogisticsName());
        tmsPackageVO.setGoodReceiverAddress(doOrderDTO.getAddress());
        tmsPackageVO.setOrderCode(doPackage.getRecordCode());
        tmsPackageVO.setOrderCreateTime(warehouseRecordE.getPayTime()==null?warehouseRecordE.getOutCreateTime():warehouseRecordE.getPayTime());
        tmsPackageVO.setOrderLogisticsTime(doPackage.getCreateTime());
        tmsPackageVO.setPackageCode(doPackage.getPackageCode());
        tmsPackageVO.setWeight(doPackage.getWeight());
        tmsPackageVO.setSource("zt");
        //添加部分省市区字段
        tmsPackageVO.setGoodReceiverArea(doOrderDTO.getArea());
        tmsPackageVO.setGoodReceiverMobile(doOrderDTO.getMobile());
        tmsPackageVO.setGoodReceiverName(doOrderDTO.getName());
        tmsPackageVO.setGoodReceiverProvince(doOrderDTO.getProvince());
        tmsPackageVO.setGoodReceiverCity(doOrderDTO.getCity());
        tmsPackageVO.setLogisticsCode(doPackage.getLogisticsCode());
        try{
            if(null !=doOrderDTO.getCountyCode()){
                tmsPackageVO.setGoodReceiverAreaId(Long.valueOf(doOrderDTO.getCountyCode()));
            }
            if(null !=doOrderDTO.getCityCode()){
                tmsPackageVO.setGoodReceiverCityId(Long.valueOf(doOrderDTO.getCityCode()));
            }
            if(null !=doOrderDTO.getProvinceCode()){
                tmsPackageVO.setGoodReceiverProvinceId(Long.valueOf(doOrderDTO.getProvinceCode()));
            }
        }catch (Exception e){
            log.error("包裹信息转换错误:packageCode=" + doPackage.getPackageCode(), e);
        }
        tmsPackageVO.setGoodReceiverPostcode(doOrderDTO.getPostcode());

        List<RecordPackageDetailE> detailES = recordPackageRepository.queryDetailsByPackageId(doPackage.getId());

        List<String> skuCodes=detailES.stream().map(RecordPackageDetailE::getSkuCode).collect(Collectors.toList());
        List<SkuInfoExtDTO> skuInfoExtDTOList= skuFacade.skusBySkuCode(skuCodes);
        Map<String,SkuInfoExtDTO> skuInfoExtDTOSMap=skuInfoExtDTOList.stream().collect(Collectors.toMap(SkuInfoExtDTO::getSkuCode, Function.identity(), (v1, v2) -> v1));
        List<TmsPackageItemVO> itemVOS = new ArrayList<>();
        for (RecordPackageDetailE dto : detailES) {
            TmsPackageItemVO vo = new TmsPackageItemVO();
            vo.setOrderCode(doPackage.getRecordCode());
            vo.setCode(dto.getSkuCode());
            vo.setPackageCode(doPackage.getPackageCode());
            vo.setProductItemOutNum(dto.getSkuQty().intValue());
            if (skuMap.containsKey(dto.getSkuCode())) {
                vo.setUnit(skuMap.get(dto.getSkuCode()).getUnitCode());
            }else{
                if(!skuInfoExtDTOSMap.containsKey(dto.getSkuCode())){
                    throw new RomeException(ResCode.STOCK_ERROR_1019, ResCode.STOCK_ERROR_1019_DESC + ",包裹号：" + doPackage.getPackageCode() + "sku:" + dto.getSkuCode());
                }
                vo.setUnit(skuInfoExtDTOSMap.get(dto.getSkuCode()).getSpuUnitCode());
            }
            vo.setWarehouseId(warehouseRecordE.getRealWarehouseId());

            if(skuInfoExtDTOSMap.containsKey(dto.getSkuCode())){
                vo.setName(skuInfoExtDTOSMap.get(dto.getSkuCode()).getName());
            }
            itemVOS.add(vo);
        }
        tmsPackageVO.setRealWarehouseId(warehouseRecordE.getRealWarehouseId());
        tmsPackageVO.setTmsPackageItems(itemVOS);
        return tmsPackageVO;
    }



    /**
     * 构建推送tms包裹信息的入参
     * @param doPackage
     * @return
     */
    private TmsPackageVO buildTmsPackage(RecordPackageDTO doPackage){
        WarehouseRecordE warehouseRecordE = warehouseRecordRepository.getRecordWithDetailByCode(doPackage.getRecordCode(), null);
        AlikAssert.isNotNull(warehouseRecordE, ResCode.STOCK_ERROR_1023, ResCode.STOCK_ERROR_1023_DESC + ",包裹号：" + doPackage.getPackageCode());
        Map<String, WarehouseRecordDetail> skuMap = RomeCollectionUtil.listforMap(warehouseRecordE.getWarehouseRecordDetails(), "skuCode");
        List<RwRecordPoolE> pools = rwRecordPoolRepository.queryKeysByWarehouseId(warehouseRecordE.getId());
        AlikAssert.isTrue(pools != null && pools.size() > 0, ResCode.STOCK_ERROR_1003, "子do单不存在,包裹号：" + doPackage.getPackageCode());
        AddressE addressE = addressRepository.queryByRecordCode(pools.get(0).getDoCode());
        AlikAssert.isNotNull(addressE, ResCode.STOCK_ERROR_1031, ResCode.STOCK_ERROR_1031_DESC + ",包裹号：" + doPackage.getPackageCode());
        TmsPackageVO tmsPackageVO = new TmsPackageVO();
        tmsPackageVO.setBillCode(doPackage.getExpressCode());
        tmsPackageVO.setDeliveryCompanyId(doPackage.getLogisticsCode());
        tmsPackageVO.setDeliveryCompanyName(doPackage.getLogisticsName());
        tmsPackageVO.setGoodReceiverAddress(addressE.getAddress());
        tmsPackageVO.setOrderCode(doPackage.getRecordCode());
        tmsPackageVO.setOrderCreateTime(warehouseRecordE.getPayTime()==null?warehouseRecordE.getOutCreateTime():warehouseRecordE.getPayTime());
        tmsPackageVO.setOrderLogisticsTime(doPackage.getCreateTime());
        tmsPackageVO.setPackageCode(doPackage.getPackageCode());
        tmsPackageVO.setWeight(doPackage.getWeight());
        tmsPackageVO.setSource("zt");
        //添加部分省市区字段
        tmsPackageVO.setGoodReceiverArea(addressE.getArea());
        tmsPackageVO.setGoodReceiverMobile(addressE.getMobile());
        tmsPackageVO.setGoodReceiverName(addressE.getName());
        tmsPackageVO.setGoodReceiverProvince(addressE.getProvince());
        tmsPackageVO.setGoodReceiverCity(addressE.getCity());
        tmsPackageVO.setLogisticsCode(doPackage.getLogisticsCode());
        try{
            if(null !=addressE.getCountyCode()){
                tmsPackageVO.setGoodReceiverAreaId(Long.valueOf(addressE.getCountyCode()));
            }
            if(null !=addressE.getCityCode()){
                tmsPackageVO.setGoodReceiverCityId(Long.valueOf(addressE.getCityCode()));
            }
            if(null !=addressE.getProvinceCode()){
                tmsPackageVO.setGoodReceiverProvinceId(Long.valueOf(addressE.getProvinceCode()));
            }
        }catch (Exception e){
            log.error("包裹信息转换错误:packageCode=" + doPackage.getPackageCode(), e);
        }
        tmsPackageVO.setGoodReceiverPostcode(addressE.getPostcode());

        List<RecordPackageDetailE> detailES = recordPackageRepository.queryDetailsByPackageId(doPackage.getId());

        List<String> skuCodes=detailES.stream().map(RecordPackageDetailE::getSkuCode).collect(Collectors.toList());
        List<SkuInfoExtDTO> skuInfoExtDTOList= skuFacade.skusBySkuCode(skuCodes);
        Map<String,SkuInfoExtDTO> skuInfoExtDTOSMap=skuInfoExtDTOList.stream().collect(Collectors.toMap(SkuInfoExtDTO::getSkuCode, Function.identity(), (v1, v2) -> v1));
        List<TmsPackageItemVO> itemVOS = new ArrayList<>();
        for (RecordPackageDetailE dto : detailES) {
            TmsPackageItemVO vo = new TmsPackageItemVO();
            vo.setOrderCode(doPackage.getRecordCode());
            vo.setCode(dto.getSkuCode());
            vo.setPackageCode(doPackage.getPackageCode());
            vo.setProductItemOutNum(dto.getSkuQty().intValue());
            if (!skuMap.containsKey(dto.getSkuCode())) {
                throw new RomeException(ResCode.STOCK_ERROR_1019, ResCode.STOCK_ERROR_1019_DESC + ",包裹号：" + doPackage.getPackageCode() + "sku:" + dto.getSkuCode());
            }
            vo.setWarehouseId(warehouseRecordE.getRealWarehouseId());
            vo.setUnit(skuMap.get(dto.getSkuCode()).getUnitCode());
            if(skuInfoExtDTOSMap.containsKey(dto.getSkuCode())){
                vo.setName(skuInfoExtDTOSMap.get(dto.getSkuCode()).getName());
            }
            itemVOS.add(vo);
        }
        tmsPackageVO.setRealWarehouseId(warehouseRecordE.getRealWarehouseId());
        tmsPackageVO.setTmsPackageItems(itemVOS);
        return tmsPackageVO;
    }
}
