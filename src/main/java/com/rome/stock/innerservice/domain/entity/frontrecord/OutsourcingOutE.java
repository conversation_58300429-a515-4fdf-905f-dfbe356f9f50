package com.rome.stock.innerservice.domain.entity.frontrecord;


import com.rome.stock.innerservice.constant.FrontRecordTypeVO;
import com.rome.stock.innerservice.domain.repository.frontrecord.FrOutsourcingOutRepository;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.apache.commons.lang3.StringUtils;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;

@Component
@Scope("prototype")
@Data
@EqualsAndHashCode(callSuper = false)
public class OutsourcingOutE extends AbstractFrontRecord {

	@Resource
	private FrOutsourcingOutRepository frOutsourcingOutRepository;

	/**
	 * 创建委外出库前置单
	 */
	public void addFrontRecord() {
		//不考虑虚拟出库，统一处理
		this.setRecordType(FrontRecordTypeVO.OUTSOURCE_OUT_RECORD.getType());
		//生成单据编号
		this.initFrontRecord(FrontRecordTypeVO.OUTSOURCE_OUT_RECORD.getCode(), this.details);
		if (StringUtils.isBlank(this.getRemark())) {
			this.setRemark("");
		}
		//插入采购单据
		long id = frOutsourcingOutRepository.savePurchaseOrderRecord(this);
		this.setId(id);
		//前置单详情关联主数据
		this.details.forEach(detail -> detail.setFrontRecordDetail(this));
		//插入前置单单据详情
		frOutsourcingOutRepository.savePurchaseOrderRecordDetails(this.details);
	}

	/**
	 * 更新完成状态，
	 */
	public void updateOutAllocationStatus() {
		frOutsourcingOutRepository.updateOutAllocationStatus(this.getId());
	}


	/**
	 * 出向实体仓库id
	 */
	private Long realWarehouseId;

	/**
	 * 工产代码
	 */
	private String factoryCode;
	/**
	 * 工产名称
	 */
	private String factoryName;
	/**
	 * 供应商编码代码
	 */
	private String supplierCode;
	/**
	 * 供应商名称
	 */
	private String supplierName;
	/**
	 * 备注
	 */
	private String remark;
	/**
	 * 供应商联系人
	 */
	private String supplierContact;


	private List<OutsourcingOutDetailE> details;
}
