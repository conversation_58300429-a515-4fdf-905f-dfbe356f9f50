package com.rome.stock.innerservice.domain.entity.frontrecord;

import com.rome.stock.innerservice.constant.FrontRecordTypeVO;
import com.rome.stock.innerservice.domain.repository.frontrecord.FrAdjustCheckStockRepository;
import com.rome.stock.innerservice.domain.repository.frontrecord.FrAdjustMerchantRepository;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;

/**
 * 库存对比调整
 */
@Component
@Scope("prototype")
@Data
@EqualsAndHashCode(callSuper = false)
@Slf4j
public class AdjustCheckStockRecordE extends AbstractFrontRecord {
    /**
     * 出向实体仓库id
     */
    private Long realWarehouseId;

    /**
     * 库存调整备注
     */
    private String remark;

    private List<AdjustCheckStockRecordDetailE> details;

    @Resource
    private FrAdjustCheckStockRepository frAdjustCheckStockRepository;

    /**
     * 创建商家库存调整前置单
     */
    public void addFrontRecord() {
        //不考虑虚拟出库，统一处理
        this.setRecordType(FrontRecordTypeVO.ADJUST_CHECK_STOCK_RECORD.getType());
        //生成单据编号
        this.initFrontRecord(FrontRecordTypeVO.ADJUST_CHECK_STOCK_RECORD.getCode(), this.details, this.getMerchantId());
        if (StringUtils.isBlank(this.getRemark())) {
            this.setRemark("");
        }
        //插入采购单据
        long id = frAdjustCheckStockRepository.insertRecord(this);
        this.setId(id);
        //前置单详情关联主数据
        this.details.forEach(detail -> detail.setFrontRecordDetail(this));
        //插入前置单单据详情
        frAdjustCheckStockRepository.insertDetails(this.details);
    }

}
