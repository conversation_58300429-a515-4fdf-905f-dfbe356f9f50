package com.rome.stock.innerservice.domain.service.impl;

import cn.hutool.core.collection.CollUtil;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import com.rome.arch.core.exception.RomeException;
import com.rome.arch.util.dynamic.DynamicDataSourceEnum;
import com.rome.arch.util.dynamic.TargetDataSource;
import com.rome.stock.common.enums.SafeStockConfigSkuTypeEnum;
import com.rome.stock.common.enums.warehouse.CombineSkuTypeVO;
import com.rome.stock.common.enums.warehouse.SaleStatusVO;
import com.rome.stock.common.enums.warehouse.VirtualWarehouseTypeVO;
import com.rome.stock.core.api.dto.ChannelSalesStockDTO;
import com.rome.stock.core.constant.StockCoreConsts;
import com.rome.stock.core.domain.repository.CoreChannelSalesRepository;
import com.rome.stock.innerservice.api.dto.*;
import com.rome.stock.innerservice.api.dto.template.StockRecordExportTemplate;
import com.rome.stock.innerservice.common.CsvBigExportUtil;
import com.rome.stock.innerservice.common.ResCode;
import com.rome.stock.innerservice.common.RomeCollectionUtil;
import com.rome.stock.innerservice.constant.RealWarehouseTypeVO;
import com.rome.stock.innerservice.constant.StorageTypeVO;
import com.rome.stock.innerservice.domain.convertor.RealWarehouseConvertor;
import com.rome.stock.innerservice.domain.convertor.StockRecordConvertor;
import com.rome.stock.innerservice.domain.entity.ChannelSalesE;
import com.rome.stock.innerservice.domain.entity.RealWarehouseE;
import com.rome.stock.innerservice.domain.entity.StockRecordE;
import com.rome.stock.innerservice.domain.entity.VirtualWarehouseE;
import com.rome.stock.innerservice.domain.repository.*;
import com.rome.stock.innerservice.domain.service.RealWarehouseService;
import com.rome.stock.innerservice.domain.service.StockRecordService;
import com.rome.stock.innerservice.domain.service.StockService;
import com.rome.stock.innerservice.export.annotation.AsyncExcelAutoExport;
import com.rome.stock.innerservice.infrastructure.dataobject.BatchStockDO;
import com.rome.stock.innerservice.infrastructure.dataobject.SafeStockConfigDO;
import com.rome.stock.innerservice.infrastructure.redis.cache.SafeStockConfigCacheRedis;
import com.rome.stock.innerservice.remote.base.dto.StoreDTO;
import com.rome.stock.innerservice.remote.base.facade.ShopFacade;
import com.rome.stock.innerservice.remote.item.dto.*;
import com.rome.stock.innerservice.remote.item.facade.SkuFacade;
import com.rome.stock.innerservice.remote.orderCneter.dto.ReplenishShopSkuReqDTO;
import com.rome.stock.innerservice.remote.orderCneter.facade.OrderCenterFacade;
import com.rome.stock.innerservice.remote.purchase.dto.PurchaseInTransitDTO;
import com.rome.stock.innerservice.remote.purchase.facade.PurchaseCenterFacade;
import com.rome.stock.wms.constants.WarehouseWmsConfigEnum;
import com.rome.stock.wms.dto.request.StockQueryRequest;
import com.rome.stock.wms.dto.response.StockQueryResponse;
import com.rome.stock.wms.dto.response.StockQueryResponseItem;
import com.rome.stock.wms.request.RequestWmsClient;
import com.rome.stock.wms.util.WmsRequestUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.ApplicationContext;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.rome.stock.core.constant.StockCoreConsts.DECIMAL_POINT_NUM;
import static java.math.BigDecimal.ROUND_DOWN;

@Service
@Slf4j
public class StockRecordServiceImpl implements StockRecordService {
    @Resource
    private StockRecordConvertor stockRecordConvertor;
    @Resource
    private StockRecordRepository stockRecordRepository;
    @Resource
    private SkuFacade skuFacade;
    @Resource
    private CoreChannelSalesRepository coreChannelSalesRepository;
    @Resource
    private ChannelSalesRepository channelSalesRepository;
    @Resource
    private ApplicationContext applicationContext;
    @Autowired
    private RealWarehouseRepository realWarehouseRepository;
    @Resource
    private ShopFacade shopFacade;
    @Resource
    private RealWarehouseWmsConfigRepository realWarehouseWmsConfigRepository;
    @Autowired
    private VirtualWarehouseRepository virtualWarehouseRepository;
    @Resource
    private StockService stockService;
    @Autowired
    private VirtualWarehouseGroupRelationRepository virtualWarehouseGroupRelationRepository;
    @Resource
    private OrderCenterFacade orderCenterFacade;
    @Autowired
    private RealWarehouseConvertor realWarehouseConvertor;
    @Resource
    private PurchaseCenterFacade purchaseCenterFacade;
    @Resource
    private BatchStockRepository batchStockRepository;

    @Resource
    private SafeStockConfigCacheRedis safeStockConfigCacheRedis;

    @Resource
    private RealWarehouseService realWarehouseService;
    /**
     * 通过仓库编码查询所有实仓拥有的工厂信息
     *
     * @return
     */
    @Override
    public List<StockRecord> getFactoryInfoByAllRealWarehouse(String warehouseCode, String warehouseType) {
        //只有编码信息
        List<StockRecord> factoryCodesInfo = stockRecordConvertor.entityToDto(stockRecordRepository.getFactoryCodesByAllRealWarehouse(warehouseCode, warehouseType));
        //如果该类型仓库没有对应实体仓库
        if (factoryCodesInfo == null || factoryCodesInfo.size() == 0) {
            return new LinkedList<>();
        }
        //获取所有工厂code
        List<String> factoryCodes = factoryCodesInfo.stream().map(StockRecord::getFactoryCode).distinct().collect(Collectors.toList());
        List<StoreDTO> storeDTOS = shopFacade.searchByCodeList(factoryCodes);
        Map<String, StoreDTO> stringStoreDTOMap = storeDTOS.stream().collect(Collectors.toMap(StoreDTO::getCode, Function.identity(), (v1, v2) -> v1));
        for (StockRecord item : factoryCodesInfo) {
            if (stringStoreDTOMap.containsKey(item.getFactoryCode())) {
                item.setFactoryName(stringStoreDTOMap.get(item.getFactoryCode()).getName());
            }
        }
        return factoryCodesInfo;
    }

    /**
     * 查询所有实仓拥有的仓库编码和名称信息
     *
     * @return
     */
    @Override
    public List<StockRecord> getRealWarehouseCodesAndNames(String factoryCode, String warehouseType) {
        return stockRecordConvertor.entityToDto(stockRecordRepository.getRealWarehouseCodesAndNames(factoryCode, warehouseType));
    }

    /**
     * 根据查询条件查询所有商品信息
     *
     * @param stockRecord
     * @return
     */
    @Override
    @TargetDataSource(DynamicDataSourceEnum.READ)
    public PageInfo<StockRecord> getSkusInfoByQueryCondition(StockRecord stockRecord) {
        if (stockRecord.getStorageType() == null) {
            throw new RomeException(ResCode.STOCK_ERROR_1003, ResCode.STOCK_ERROR_1003_DESC);
        }
        List<StockRecordE> stockRecordEs = null;
        List<Long> barCodeSkuIds=new ArrayList<>();
        Page page = null;
        //支持多个仓库查询
        List<String> warehouseCodes=new ArrayList();
        if(!CollectionUtils.isEmpty(stockRecord.getWarehouseCodes())){
            warehouseCodes=stockRecord.getWarehouseCodes().stream().filter(code->StringUtils.isNotBlank(code)).distinct().collect(Collectors.toList());
        }
        if (!CollectionUtils.isEmpty(warehouseCodes) && !CollectionUtils.isEmpty(stockRecord.getRealWarehouseCodeList())) {
            warehouseCodes.retainAll(Optional.ofNullable(stockRecord).map(StockRecord::getRealWarehouseCodeList).orElse(Collections.emptyList()));
            if(CollectionUtils.isEmpty(warehouseCodes)){
                throw new RomeException(ResCode.STOCK_ERROR_1003, "实仓编码和仓库名称无法取到交集");
            }
        } else {
            if (!CollectionUtils.isEmpty(stockRecord.getRealWarehouseCodeList())) {
                warehouseCodes=stockRecord.getRealWarehouseCodeList();
            }
        }
        stockRecord.setWarehouseCodes(warehouseCodes);
        List<String> factoryCodes = new ArrayList();
        if (StringUtils.isNotBlank(stockRecord.getFactoryCode())) {
            factoryCodes = Arrays.asList(stockRecord.getFactoryCode().split("\n|\r"));
            factoryCodes = factoryCodes.stream().filter(code->StringUtils.isNotBlank(code)).distinct().collect(Collectors.toList());
            if(factoryCodes == null || factoryCodes.size() == 0) {
            	stockRecord.setFactoryCode("");
            }
        }
        //根据69码查询商品信息
        boolean res = this.getSkuInfoByBarCode(stockRecord, barCodeSkuIds);
        if(!res){
            return new PageInfo<>();
        }
        //两个都不为空的时候 取交集
        if(CollUtil.isNotEmpty(stockRecord.getSkuIds()) && CollUtil.isNotEmpty(barCodeSkuIds)){
            List<Long> skuIds = stockRecord.getSkuIds().stream().filter(x -> barCodeSkuIds.contains(x)).collect(Collectors.toList());
            if(CollUtil.isEmpty(skuIds)){
                return new PageInfo<>();
            }
            stockRecord.setSkuIds(skuIds);
        }
        if(CollUtil.isEmpty(stockRecord.getSkuIds()) && CollUtil.isNotEmpty(barCodeSkuIds)){
            stockRecord.setSkuIds(new ArrayList<>());
            stockRecord.getSkuIds().addAll(barCodeSkuIds);
        }
        //实仓类型
        if (StorageTypeVO.REAL_WAREHOUSE.getType().equals(stockRecord.getStorageType())) {
            List<RealWarehouseE> realWarehouseEs = new ArrayList<>();
            //根据仓库编码和工厂编码查询实仓信息
            if (CollectionUtils.isNotEmpty(stockRecord.getWarehouseCodes()) || StringUtils.isNotEmpty(stockRecord.getFactoryCode())) {
            	if(CollectionUtils.isNotEmpty(stockRecord.getWarehouseCodes()) && CollectionUtils.isNotEmpty(factoryCodes)) {
            		realWarehouseEs = realWarehouseRepository.querysByCodesAndFactoryCodes(stockRecord.getWarehouseCodes(), factoryCodes);
            	}else if (!CollectionUtils.isEmpty(stockRecord.getWarehouseCodes())) {
                	realWarehouseEs = realWarehouseRepository.getRealWarehousesByCode(stockRecord.getWarehouseCodes());
//                    for (String warehouseCode : warehouseCodes) {
//                        List<RealWarehouseE> realWarehouseESingle = stockRecordRepository.getRealwarehouseByFactoryCodeAndCode(factoryCodes, warehouseCode, null);
//                        realWarehouseEs.addAll(realWarehouseESingle);
//                    }
                } else {
                    realWarehouseEs = stockRecordRepository.getRealwarehouseByFactoryCodeAndCode(factoryCodes, null, null);
                }
                if(realWarehouseEs.size() == 0) {
                	return new PageInfo<>();
                }
            }

            //获取实体仓id集合
            List<Long> realWarehouseIds = realWarehouseEs.stream().map(RealWarehouseE::getId).collect(Collectors.toList());
//            if (realWarehouseIds.size() == 0) {
//                return new PageInfo<>();
//            }
            if (CollectionUtils.isNotEmpty(stockRecord.getAreaList())) {
                List<String> countyCode = new ArrayList<>();
                List<String> cityCode = new ArrayList<>();
                List<String> provinceCode = new ArrayList<>();
                for (List<String> areas : stockRecord.getAreaList()) {
                    if (areas.size() > 2) {
                        countyCode.add(areas.get(2));
                    } else if (areas.size() > 1) {
                        cityCode.add(areas.get(1));
                    } else if (areas.size() > 0) {
                        provinceCode.add(areas.get(0));
                    }
                }
                if (countyCode.size() > 0 || cityCode.size() > 0 || provinceCode.size() > 0) {
                    if (cityCode.size() > 0) {
                        countyCode.addAll(stockRecordRepository.getCountyCodeByCityCodeOrProvinceCode(cityCode, null));
                    }
                    if (provinceCode.size() > 0) {
                        countyCode.addAll(stockRecordRepository.getCountyCodeByCityCodeOrProvinceCode(null, provinceCode));
                    }
                    List<Long> tempIds = stockRecordRepository.getRealwarehouseByCountyCode(countyCode);
                    if (tempIds.size() == 0) {
                        return new PageInfo<>();
                    }
                    if (realWarehouseIds.size() == 0) {
                        realWarehouseIds = tempIds;
                    } else {
                        realWarehouseIds.retainAll(tempIds);
                        if (realWarehouseIds.size() == 0) {
                            return new PageInfo<>();
                        }
                    }
                }
            }
            page = PageHelper.startPage(stockRecord.getPageIndex(), stockRecord.getPageSize(), stockRecord.getQueryCount());
            //如果是按照sku编码精确查询
            if (CollUtil.isNotEmpty(stockRecord.getSkuIds())) {
                //如果是按照skuIds查找商品信息
                stockRecordEs = stockRecordRepository.getRealSkusInfoByQueryConditionName(stockRecordConvertor.entityToDo(stockRecordConvertor.dtoToEntity(stockRecord)), realWarehouseIds, stockRecord.getSkuIds(), stockRecord.getMoreThanZero()
                        , stockRecord.getMoreThanZeroQuality(), stockRecord.getMoreThanZeroLock()
                        , stockRecord.getMoreThanZeroReal(),stockRecord.getMoreThanZeroBaoYou(),stockRecord.getMoreThanZeroUnqualifiedQty(),
                        stockRecord.getId());
            } else {
                //可以直接使用第一种查询方案
                stockRecordEs = stockRecordRepository.getRealSkusInfoByQueryConditionId(stockRecordConvertor.entityToDo(stockRecordConvertor.dtoToEntity(stockRecord))
                        , realWarehouseIds, stockRecord.getMoreThanZero(), stockRecord.getMoreThanZeroQuality()
                        , stockRecord.getMoreThanZeroLock(), stockRecord.getMoreThanZeroReal(),stockRecord.getMoreThanZeroBaoYou(),stockRecord.getMoreThanZeroUnqualifiedQty(),stockRecord.getId());
            }
            //将上述获得的stockRecordEs的skuId统计出来，去查找其相关信息
            if (stockRecordEs == null || stockRecordEs.size() == 0) {
                return new PageInfo<>();
            }
            Map<String, SkuInfoExtDTO> skuInfoExtDTOMap= Maps.newHashMap();
            if(!stockRecord.getExport()){
                List<SkuInfoExtDTO> skuInfoExtDTOs = skuFacade.skuListBySkuCodes(stockRecordEs.stream().map(StockRecordE::getSkuCode).distinct().collect(Collectors.toList()));
                skuInfoExtDTOMap = skuInfoExtDTOs.stream().collect(Collectors.toMap(SkuInfoExtDTO::getSkuCode, item -> item));
            }
//            if(!(CollectionUtils.isNotEmpty(stockRecord.getWarehouseCodes()) || StringUtils.isNotEmpty(stockRecord.getFactoryCode()))){
            Set<Long> setIds = new HashSet<>(16);
            for(StockRecordE dto : stockRecordEs) {
                if(dto.getWarehouseId() != null) {
                    setIds.add(dto.getWarehouseId());
                }
            }
                realWarehouseEs = realWarehouseRepository.queryWarehouseByIds(new ArrayList<>(setIds));
//            }
            Map<Long, RealWarehouseE> realWarehouseEMap = realWarehouseEs.stream().collect(Collectors.toMap(RealWarehouseE::getId, item -> item));
            for (StockRecordE stockRecordE : stockRecordEs) {
                if(Objects.nonNull(realWarehouseEMap.get(stockRecordE.getWarehouseId()))){
                    stockRecordE.setWarehouseCode(realWarehouseEMap.get(stockRecordE.getWarehouseId()).getRealWarehouseCode());
                    stockRecordE.setWarehouseName(realWarehouseEMap.get(stockRecordE.getWarehouseId()).getRealWarehouseName());
                    stockRecordE.setFactoryCode(realWarehouseEMap.get(stockRecordE.getWarehouseId()).getFactoryCode());
                    stockRecordE.setRealWarehouseType(realWarehouseEMap.get(stockRecordE.getWarehouseId()).getRealWarehouseType());
                    if (skuInfoExtDTOMap.containsKey(stockRecordE.getSkuCode())) {
                        stockRecordE.setSkuCode(skuInfoExtDTOMap.get(stockRecordE.getSkuCode()).getSkuCode());
                        stockRecordE.setSkuName(skuInfoExtDTOMap.get(stockRecordE.getSkuCode()).getName());
                        stockRecordE.setSkuType(skuInfoExtDTOMap.get(stockRecordE.getSkuCode()).getCategoryName());
                        stockRecordE.setSkuTitle(skuInfoExtDTOMap.get(stockRecordE.getSkuCode()).getDescription());
                        stockRecordE.setSkuUnit(skuInfoExtDTOMap.get(stockRecordE.getSkuCode()).getSpuUnitName());
                        stockRecordE.setSkuUnitCode(skuInfoExtDTOMap.get(stockRecordE.getSkuCode()).getSpuUnitCode());
                        stockRecordE.setBarCode(skuInfoExtDTOMap.get(stockRecordE.getSkuCode()).getBarCode());
                    }
                }
            }
            if (!stockRecord.getExport()){
                this.addSkuUnitParams(stockRecordEs);
                this.addOnRoadQty(stockRecordEs);
            }
            PageInfo<StockRecord> pageList = new PageInfo(stockRecordConvertor.entityToDto(stockRecordEs));
            pageList.setTotal(page.getTotal());
            return pageList;
        }
        //虚仓类型
        if (StorageTypeVO.VIRTUAL_WAREHOUSE.getType().equals(stockRecord.getStorageType())) {
            List<VirtualWarehouseE> virtualWarehouseEs=new ArrayList<>();
            //虚仓编号不为空
            if(CollectionUtils.isNotEmpty(stockRecord.getVirtualWarehouseCodeList()) || stockRecord.getRealWarehouseId() != null
                    || CollectionUtils.isNotEmpty(stockRecord.getWarehouseTypeList())) {
            	virtualWarehouseEs = stockRecordRepository.getVirtualWarehouseByCodeList(stockRecord.getVirtualWarehouseCodeList(),stockRecord.getRealWarehouseId(), stockRecord.getWarehouseTypeList());
            }else {
                throw new RomeException(ResCode.STOCK_ERROR_1002, ResCode.STOCK_ERROR_1002_DESC + "请选择【仓库】或【虚仓类型】条件");
            }
            if(CollectionUtils.isEmpty(virtualWarehouseEs)) {
            	throw new RomeException(ResCode.STOCK_ERROR_1002, ResCode.STOCK_ERROR_1002_DESC + "【仓库】或【虚仓类型】对应的数据不存在");
            }
            //获取虚仓id集合
            List<Long> virtualWarehouseIds = virtualWarehouseEs.stream().map(VirtualWarehouseE::getId).collect(Collectors.toList());
            List<Long> realWarehouseIds = virtualWarehouseEs.stream().map(VirtualWarehouseE::getRealWarehouseId).collect(Collectors.toList());
            if (virtualWarehouseIds.size() == 0) {
                return new PageInfo<>();
            }
            page = PageHelper.startPage(stockRecord.getPageIndex(), stockRecord.getPageSize());
            if (CollUtil.isNotEmpty(stockRecord.getSkuIds())) {
                stockRecordEs = stockRecordRepository.getVirSkusInfoByQueryConditionName(
                        stockRecordConvertor.entityToDo(stockRecordConvertor.dtoToEntity(stockRecord)),
                        virtualWarehouseIds, stockRecord.getSkuIds(),stockRecord.getMoreThanZero(),stockRecord.getMoreThanZeroLock(),stockRecord.getMoreThanZeroBaoYou());
            } else {
                stockRecordEs = stockRecordRepository.getVirSkusInfoByQueryConditionId(
                        stockRecordConvertor.entityToDo(stockRecordConvertor.dtoToEntity(stockRecord)),
                        virtualWarehouseIds,stockRecord.getMoreThanZero(),stockRecord.getMoreThanZeroLock(),stockRecord.getMoreThanZeroBaoYou());
            }
            List<RealWarehouseE> realWarehouseES = realWarehouseRepository.queryWarehouseByIds(realWarehouseIds);
            Map<Long, RealWarehouseE> realWarehouseEMap = RomeCollectionUtil.listforMap(realWarehouseES, "id");
            List<SkuInfoExtDTO> skuInfoExtDTOs = skuFacade.skuListBySkuCodes(stockRecordEs.stream().map(StockRecordE::getSkuCode).distinct().collect(Collectors.toList()));
            Map<String, SkuInfoExtDTO> skuInfoExtDTOMap = skuInfoExtDTOs.stream().distinct().collect(Collectors.toMap(SkuInfoExtDTO::getSkuCode, item -> item));
            Map<Long, VirtualWarehouseE> virtualWarehouseEMap = virtualWarehouseEs.stream().collect(Collectors.toMap(VirtualWarehouseE::getId, item -> item));
            for (StockRecordE stockRecordE : stockRecordEs) {
                stockRecordE.setWarehouseCode(virtualWarehouseEMap.get(stockRecordE.getWarehouseId()).getVirtualWarehouseCode());
                stockRecordE.setWarehouseName(virtualWarehouseEMap.get(stockRecordE.getWarehouseId()).getVirtualWarehouseName());
                stockRecordE.setWarehouseTypeName(VirtualWarehouseTypeVO.getDescByType(virtualWarehouseEMap.get(stockRecordE.getWarehouseId()).getVirtualWarehouseType()));
                if (skuInfoExtDTOMap.containsKey(stockRecordE.getSkuCode())) {
                    stockRecordE.setSkuCode(skuInfoExtDTOMap.get(stockRecordE.getSkuCode()).getSkuCode());
                    stockRecordE.setSkuName(skuInfoExtDTOMap.get(stockRecordE.getSkuCode()).getName());
                    stockRecordE.setSkuType(skuInfoExtDTOMap.get(stockRecordE.getSkuCode()).getCategoryName());
                    stockRecordE.setSkuTitle(skuInfoExtDTOMap.get(stockRecordE.getSkuCode()).getDescription());
                    stockRecordE.setSkuUnit(skuInfoExtDTOMap.get(stockRecordE.getSkuCode()).getSpuUnitName());
                    stockRecordE.setSkuUnitCode(skuInfoExtDTOMap.get(stockRecordE.getSkuCode()).getSpuUnitCode());
                    stockRecordE.setBarCode(skuInfoExtDTOMap.get(stockRecordE.getSkuCode()).getBarCode());

                }
                RealWarehouseE realWarehouseE = realWarehouseEMap.get(virtualWarehouseEMap.get(stockRecordE.getWarehouseId()).getRealWarehouseId());
                if (realWarehouseE != null) {
                    stockRecordE.setFactoryCode(realWarehouseE.getFactoryCode());
                }

            }
            this.addSkuUnitParams(stockRecordEs);
            PageInfo<StockRecord> pageList = new PageInfo(stockRecordConvertor.entityToDto(stockRecordEs));
            pageList.setTotal(page.getTotal());
            return pageList;
        }
        //渠道类型
        if (StorageTypeVO.CHANNEL.getType().equals(stockRecord.getStorageType())) {
            List<Long> skuIds = null;
            //如果含有商品编码，证明是按照SkuId的精确查询
            if (stockRecord.getSkuIds() != null && stockRecord.getSkuIds().size() > 0) {
                skuIds = stockRecord.getSkuIds().stream().distinct().collect(Collectors.toList());
            } else {
                //抛出异常：渠道库存查询必须传入sku信息
                throw new RomeException(ResCode.STOCK_ERROR_1003, ResCode.STOCK_ERROR_1003_DESC);
            }

            List<ChannelSalesStockDTO> channelSalesStockDTOs = new LinkedList<>();

            if (skuIds == null || skuIds.size() == 0) {
                return new PageInfo<>();
            }
            //获取渠道Code集合
            String[] channelCodes = stockRecord.getChannelCodes().split(",");
            if (channelCodes == null || channelCodes.length == 0) {
                return new PageInfo<>();
            }
            //根据渠道集合和skuIds获取笛卡尔积对象
            for (String channelCode : channelCodes) {
                for (Long skuId : skuIds) {
                    ChannelSalesStockDTO channelSalesStockDTO = new ChannelSalesStockDTO();
                    channelSalesStockDTO.setChannelCode(channelCode);
                    channelSalesStockDTO.setSkuId(skuId);
                    channelSalesStockDTOs.add(channelSalesStockDTO);
                }
            }
            List<ChannelSalesStockDTO> channelSalesStockDTOsList = coreChannelSalesRepository.getChannelSaleStock(channelSalesStockDTOs);
            List<ChannelSalesStockDTO> channelSalesStockDTOsListPage = new LinkedList<>();
            Map<String, SafeStockConfigDO> safeQueryMap = new HashMap<>();
            for (int i = (stockRecord.getPageIndex() - 1) * stockRecord.getPageSize(); i < stockRecord.getPageIndex() * stockRecord.getPageSize(); i++) {
                if (channelSalesStockDTOsList.size() > i) {
                    ChannelSalesStockDTO channelSalesStockDTO = channelSalesStockDTOsList.get(i);
                    channelSalesStockDTOsListPage.add(channelSalesStockDTO);
                    if(!safeQueryMap.containsKey(channelSalesStockDTO.getChannelCode())) {
                        SafeStockConfigDO safeStockConfigDO = new SafeStockConfigDO();
                        safeStockConfigDO.setChannelCode(channelSalesStockDTO.getChannelCode());
                        safeQueryMap.put(channelSalesStockDTO.getChannelCode(), safeStockConfigDO);
                    }
                }
            }
            //去涉及到的sku的skuId----sku的map集合
            List<SkuInfoExtDTO> skuInfoExtDTOs = skuFacade.skusBySkuId(skuIds);
            Map<Long, SkuInfoExtDTO> skuInfoExtDTOMap = skuInfoExtDTOs.stream().distinct().collect(Collectors.toMap(SkuInfoExtDTO::getId, item -> item));
            //根据渠道codes查询渠道信息
            Map<String, ChannelSalesE> channelSalesEsMap = Maps.newHashMap();
            if (CollectionUtils.isNotEmpty(channelSalesStockDTOsListPage)){
                List<ChannelSalesE> channelSalesEs = channelSalesRepository.selectChannelSalesByChannelCodes(channelSalesStockDTOsListPage.stream().map(ChannelSalesStockDTO::getChannelCode).distinct().collect(Collectors.toList()));
                if (CollectionUtils.isNotEmpty(channelSalesEs)){
                    channelSalesEsMap = channelSalesEs.stream().collect(Collectors.toMap(ChannelSalesE::getChannelCode, item -> item));
                }
            }

            //查询安全库存信息
            Map<String, BigDecimal> safeStockToMap = safeStockConfigCacheRedis.getSafeStockToMap(new ArrayList<>(safeQueryMap.values()));
            //将所得集合转成StockRecord的集合
            stockRecordEs = new LinkedList<>();
            for (ChannelSalesStockDTO channelSalesStockDTO : channelSalesStockDTOsListPage) {
                StockRecordE itemStockRecord = new StockRecordE();
                //默认设置为计件
                Integer skuType =  SafeStockConfigSkuTypeEnum.PIECEWORK.getType();
                //设置sku属性
                itemStockRecord.setSkuId(channelSalesStockDTO.getSkuId());
                SkuInfoExtDTO skuInfoExtDTO = skuInfoExtDTOMap.get(channelSalesStockDTO.getSkuId());
                if (skuInfoExtDTO != null) {
                    itemStockRecord.setSkuCode(skuInfoExtDTO.getSkuCode());
                    itemStockRecord.setSkuName(skuInfoExtDTO.getName());
                    itemStockRecord.setSkuTitle(skuInfoExtDTO.getDescription());
                    itemStockRecord.setSkuUnit(skuInfoExtDTO.getSpuUnitName());
                    itemStockRecord.setSkuType(skuInfoExtDTO.getCategoryName());
                    itemStockRecord.setBarCode(skuInfoExtDTO.getBarCode());
                    if ("KG".equalsIgnoreCase(skuInfoExtDTO.getSpuUnitCode())) {
                        //称重
                        skuType = SafeStockConfigSkuTypeEnum.WEIGHT.getType();
                    }
                }
                BigDecimal safeConfigQty = safeStockToMap.get(channelSalesStockDTO.getChannelCode() + "-" + skuType);
                if (safeConfigQty != null) {
                    itemStockRecord.setSafeConfigQty(safeConfigQty);
                } else {
                    itemStockRecord.setSafeConfigQty(BigDecimal.ZERO);
                }
                itemStockRecord.setAvailableQty(channelSalesStockDTO.getAvailableQty().subtract(itemStockRecord.getSafeConfigQty()));
                itemStockRecord.setChannelCode(channelSalesStockDTO.getChannelCode());

                itemStockRecord.setLockQty(channelSalesStockDTO.getLockQty());
                //设置渠道信息
                if (channelSalesEsMap.containsKey(itemStockRecord.getChannelCode())){
                    itemStockRecord.setChannelName(channelSalesEsMap.get(itemStockRecord.getChannelCode()).getChannelName());
                }

                stockRecordEs.add(itemStockRecord);
            }
            this.addSkuUnitParams(stockRecordEs);
            PageInfo<StockRecord> pageList = new PageInfo(stockRecordConvertor.entityToDto(stockRecordEs));
            pageList.setTotal(channelSalesStockDTOsList.size());
            return pageList;
        }
        throw new RomeException(ResCode.STOCK_ERROR_1003, ResCode.STOCK_ERROR_1003_DESC);
    }

    /**
     * 根据69码查询商品信息
     * @param stockRecord
     * @param barCodeSkuIds
     * @return
     */
    private boolean getSkuInfoByBarCode(StockRecord stockRecord, List<Long> barCodeSkuIds) {
        if(CollUtil.isNotEmpty(stockRecord.getBarCodeList())){
            List<SkusByBarCodeExtDTO> skusByBarCodeExtDTOS = skuFacade.skusByBarCode(stockRecord.getBarCodeList());
            if(CollUtil.isNotEmpty(skusByBarCodeExtDTOS)){
                List<Long> skuIds = skusByBarCodeExtDTOS.stream().map(x -> x.getSkuId()).distinct().collect(Collectors.toList());
                barCodeSkuIds.addAll(skuIds);
            }else{
                return false;
            }
        }
        return true;
    }


    /**
     * 添加单位相关参数
     * @param stockRecordEs
     */
    private void addSkuUnitParams(List<StockRecordE> stockRecordEs){
        List<String> skuCodes=stockRecordEs.stream().map(StockRecordE::getSkuCode).distinct().collect(Collectors.toList());
//        List<SkuUnitExtDTO> saleOutSkuUnitExtDTOLists=skuFacade.querySkuUnits(skuCodes);
//        Map<Long, List<SkuUnitExtDTO>> skuInfoExtDTOMap = saleOutSkuUnitExtDTOLists.stream().distinct().collect(Collectors.groupingBy(item->item.getSkuId()));
        Map<String, SkuAllUnitDTO> skuAllUnitDTOMap = skuFacade.selectSkuWarehouseUnitBySkuCode(skuCodes);

        //获取商品的经营属性
        List<SkuAttributeInfoDTO> skuAttributeList = skuFacade.getSkuAttributeBySkuCodes(skuCodes);
        Map<String, SkuAttributeInfoDTO> skuAttributeMap = RomeCollectionUtil.listforMap(skuAttributeList, "skuCode");
        for (StockRecordE stockRecordE:stockRecordEs){
            try {
                SkuAllUnitDTO skuAllUnitDTO=skuAllUnitDTOMap.get(stockRecordE.getSkuCode());
                if(skuAllUnitDTO == null){
                    continue;
                }
                String boxUnitCode="KAR";
                List<SkuUnitExtDTO> skuCodeUnits = skuAllUnitDTO.getSkuCodeUnits();
                List<SkuUnitExtDTO> boxSkuUnitExtDTOList= null;
                if (skuCodeUnits != null) {
                    boxSkuUnitExtDTOList=skuCodeUnits.stream().filter((v)->boxUnitCode.equals(v.getUnitCode())).collect(Collectors.toList());
                }
                if(!CollectionUtils.isEmpty(boxSkuUnitExtDTOList)){
                    //箱单位
                    SkuUnitExtDTO boxSkuUnitExtDTO=boxSkuUnitExtDTOList.get(0);
                    if(boxSkuUnitExtDTO.getScale().compareTo(BigDecimal.ZERO)!=0){
                        BigDecimal boxUnitCount = stockRecordE.getAvailableQty().divide(boxSkuUnitExtDTO.getScale(), DECIMAL_POINT_NUM,BigDecimal.ROUND_DOWN);
                        stockRecordE.setBoxUnitName(boxSkuUnitExtDTO.getUnitName());
                        stockRecordE.setBoxUnitCount(boxUnitCount);
                        if(stockRecordE.getQualityQty() != null){
                            BigDecimal qualityQtyBox = stockRecordE.getQualityQty().divide(boxSkuUnitExtDTO.getScale(), DECIMAL_POINT_NUM,BigDecimal.ROUND_DOWN);
                            stockRecordE.setQualityQtyBox(qualityQtyBox);
                        }

                    }
                }
                SkuUnitExtDTO deliveryUnitDTO = skuFacade.getUnitByTypeAndFactory(skuAllUnitDTO, 3L, stockRecordE.getFactoryCode());
                if(deliveryUnitDTO != null){
                    //发货单位
                    if(deliveryUnitDTO.getScale().compareTo(BigDecimal.ZERO)==0){
                        continue;
                    }
                    BigDecimal saleOutUnitCount = stockRecordE.getAvailableQty().divide(deliveryUnitDTO.getScale(), DECIMAL_POINT_NUM,BigDecimal.ROUND_DOWN);
                    stockRecordE.setDeliveryUnitName(deliveryUnitDTO.getUnitName());
                    stockRecordE.setDeliveryUnitCount(saleOutUnitCount);
                }
                //匹配设置销售经营状态
                if(skuAttributeMap.containsKey(stockRecordE.getSkuCode())){
                    SkuAttributeInfoDTO skuAttribute  = skuAttributeMap.get(stockRecordE.getSkuCode());
                    stockRecordE.setSaleStatusName(SaleStatusVO.getDescByStatusCode(skuAttribute.getSaleStatus()));
                }
            }catch (Exception e){
               log.error("设置商品属性信息异常", e);
            }
        }
    }

    /**
     * 查询在途数量
     * @param stockRecordEs
     */
    private void addOnRoadQty(List<StockRecordE> stockRecordEs){
        if(CollectionUtils.isEmpty(stockRecordEs)){
            return;
        }
        //分开查询仓库在途数据、分开查询门店在途数据
        List<StockRecord> stockRecords=stockRecordConvertor.entityToDto(stockRecordEs);
        List<ReplenishShopSkuReqDTO> factoryResList=this.queryInTransitNoLock(stockRecords);
        List<ReplenishShopSkuReqDTO> shopResList=this.querySkuCountByShopCodeList(stockRecords);
        Map<String,ReplenishShopSkuReqDTO> factoryResMap=factoryResList.stream().collect(Collectors.toMap(a->a.getFactoryCode()+"-"+a.getRealWarehouseCode()+"_"+a.getSkuCode(), Function.identity(), (v1, v2) -> v1));
        Map<String,ReplenishShopSkuReqDTO> shopResMap=shopResList.stream().collect(Collectors.toMap(a->a.getFactoryCode()+"_"+a.getSkuCode(), Function.identity(), (v1, v2) -> v1));
        //查询采购在途数量
        List<PurchaseInTransitDTO> purchaseInTransitList=queryPurchaseQtyInTransit(stockRecords);
        Map<String,PurchaseInTransitDTO> purchaseInTransitResMap=purchaseInTransitList.stream().collect(Collectors.toMap(a->a.getFactoryCode()+"-"+a.getWarehouseCode()+"_"+a.getSkuCode(), Function.identity(), (v1, v2) -> v1));
        for (StockRecordE stockRecordE:stockRecordEs){
            String onRoadKey=stockRecordE.getWarehouseCode()+"_"+stockRecordE.getSkuCode();
            if(factoryResMap.containsKey(onRoadKey)){
                //未出库完成的仓库在途库存
                stockRecordE.setUnCompleteOnRoadStock(factoryResMap.get(onRoadKey).getSkuQty());
            }else if(shopResMap.containsKey(onRoadKey)){
                //107U_11441
                //未出库完成的门店在途库存
                stockRecordE.setUnCompleteOnRoadStock(shopResMap.get(onRoadKey).getSkuQty());
            }
            if(purchaseInTransitResMap.containsKey(onRoadKey)){
                stockRecordE.setPurchaseOnRoadStock(purchaseInTransitResMap.get(onRoadKey).getInTransitQty());
            }
        }
    }

    @Value("${sku.merchantId}")
    private Long defaultMerchantId;
    private Long getMerchantIdByRealWarehouse(StockRecordE stockRecordE){
        Long realWarehouseId=stockRecordE.getWarehouseId();
        List<VirtualWarehouseE> virtualWarehouse=virtualWarehouseRepository.queryByRealWarehouseId(realWarehouseId);
		if(CollectionUtils.isEmpty(virtualWarehouse)){
			return defaultMerchantId;
		}
		List<Long> groupIds = virtualWarehouseGroupRelationRepository.queryGroupIdsByVmId(virtualWarehouse.get(0).getId());
		if(groupIds == null || groupIds.size() == 0) {
			return defaultMerchantId;
		}
		List<ChannelSalesE> list = channelSalesRepository.selectChannelSalesByGId(groupIds);
//		Long merchantId = (list == null || list.size() == 0) ? 0L : list.get(0).getMerchantId();
		Long merchantId = null;
		if(list != null && list.size() >0) {
			merchantId = list.get(0).getMerchantId();
		}
		if(merchantId == null || merchantId == 0L ){
			return defaultMerchantId;
		}
		return merchantId;
    }


    /**
     * 查询所有虚仓拥有的仓库编码和名称信息
     *
     * @return
     */
    @Override
    public List<StockRecord> getVitualWarehouseCodesAndNames() {
        return stockRecordConvertor.entityToDto(stockRecordRepository.getVitualWarehouseCodesAndNames());
    }

    /**
     * 查询所有渠道拥有的渠道编码和名称信息
     *
     * @return
     */
    @Override
    public List<StockRecord> getChannelCodesAndNames() {
        return stockRecordConvertor.entityToDto(stockRecordRepository.getChannelCodesAndNames());
    }

    /**
     * 通过warehouseId和skuId查询该商品的批次号
     *
     * @param skuId
     * @param warehouseId
     * @return
     */
    @Override
    public List<SkuBatchStockDTO> getBatchInfoByWarehouseIdAndSkuId(String skuId, Integer warehouseId) {
        List<SkuBatchStockDTO> skuBatchStockDTOS = stockRecordRepository.getBatchInfoByWarehouseIdAndSkuId(skuId, warehouseId);
        if (skuBatchStockDTOS == null || skuBatchStockDTOS.size() == 0) {
            return new LinkedList<>(skuBatchStockDTOS);
        }
        List<Long> skuIds = skuBatchStockDTOS.stream().map(SkuBatchStockDTO::getSkuId).distinct().collect(Collectors.toList());
        Map<Long, SkuInfoExtDTO> skuInfoExtDTOs = skuFacade.skusBySkuId(skuIds).stream().collect(Collectors.toMap(SkuInfoExtDTO::getId, item -> item));
        for (SkuBatchStockDTO skuBatchStockDTO : skuBatchStockDTOS) {
            if (skuInfoExtDTOs.containsKey(skuBatchStockDTO.getSkuId())) {
                skuBatchStockDTO.setSkuName(skuInfoExtDTOs.get(skuBatchStockDTO.getSkuId()).getName());
                skuBatchStockDTO.setSkuCode(skuInfoExtDTOs.get(skuBatchStockDTO.getSkuId()).getSkuCode());
            }
        }

        return skuBatchStockDTOS;
    }

    @Override
    public PageInfo<WmsStockQueryDTO> queryWmsWarehouseStock(WmsStockQueryDTO wmsStockQueryDTO) {
        PageInfo<WmsStockQueryDTO> pageInfo = new PageInfo<>();
        List<WmsStockQueryDTO> stockQueryDTOS = new ArrayList<>();
        //构建参数，调用WMS库存查询接口
        //获取仓库名称及仓库编码
//        RealWarehouseE realWarehouseE = realWarehouseRepository.getRealWarehouseByOutCodeAndFactoryCode(wmsStockQueryDTO.getRealWarehouseOutCode(), wmsStockQueryDTO.getFactoryCode());
        RealWarehouseE realWarehouseE = realWarehouseRepository.getRealWarehouseByCode(wmsStockQueryDTO.getRealWarehouseCode());
        if (realWarehouseE == null) {
            return pageInfo;
        }
        //通过商品id获取商品code集合
        List<String> skuCodes = null;
        if (CollectionUtils.isNotEmpty(wmsStockQueryDTO.getSkuIds())) {
            List<SkuInfoExtDTO> skuInfoExtDTOS = skuFacade.skusBySkuId(wmsStockQueryDTO.getSkuIds());
            skuCodes = RomeCollectionUtil.getValueList(skuInfoExtDTOS, "skuCode");
        }
        //查询实仓wms配置
        Integer wmsCode = realWarehouseWmsConfigRepository.queryWmsConfigById(realWarehouseE.getId());
        if (wmsCode == null) {
            log.error("暂未查询到实仓与wms的配置信息 :实仓id " + realWarehouseE.getId());
            return null;
        }
        List<StockQueryResponseItem> stockQueryItems = null;
        if (Objects.equals(wmsCode, WarehouseWmsConfigEnum.MM.getType())) {
            Page page = PageHelper.startPage(wmsStockQueryDTO.getPageIndex(), wmsStockQueryDTO.getPageSize());
            List<BatchStockDO> batchStockDOS = batchStockRepository.queryWmsBatchStockByRwId(realWarehouseE.getId(), wmsStockQueryDTO.getSkuIds());
            pageInfo.setTotal(page.getTotal());
            stockQueryItems = stockRecordConvertor.batchStockDOS2StockQueryItem(batchStockDOS);
        } else {
            StockQueryResponse response = this.queryStockFromWms(wmsStockQueryDTO, realWarehouseE, skuCodes);
            if(response == null || CollectionUtils.isEmpty(response.getItems())) {
                pageInfo.setTotal(0);
                return pageInfo;
            }else {
                pageInfo.setTotal(response.getTotalCount());
            }
            stockQueryItems = response.getItems();
        }

        skuCodes = RomeCollectionUtil.getValueList(stockQueryItems, "itemCode");
        if (CollectionUtils.isEmpty(stockQueryItems)) {
            return pageInfo;
        }
        if(skuCodes != null && skuCodes.size() > 0) {
        	List<String> skuCodeTemps = new ArrayList<String>();
        	for(String skuCode : skuCodes) {
        		if(StringUtils.isNotBlank(skuCode)) {
        			if(!skuCodeTemps.contains(skuCode)) {
        				skuCodeTemps.add(skuCode);
        			}
        		}
        	}
        	skuCodes = skuCodeTemps;
        }
        Map<String, SkuInfoExtDTO> skuInfoExtDTOs = skuFacade.skusBySkuCode(skuCodes).stream().distinct().collect(Collectors.toMap(SkuInfoExtDTO::getSkuCode, p -> p));
        stockQueryItems.forEach(detail -> {
            WmsStockQueryDTO wmsSQuery = new WmsStockQueryDTO();
            SkuInfoExtDTO skuInfoExtDTO = skuInfoExtDTOs.get(detail.getItemCode());
            if (skuInfoExtDTO != null) {
                wmsSQuery.setSkuName(skuInfoExtDTO.getName());
                wmsSQuery.setSkuTitle(skuInfoExtDTO.getDescription());
                wmsSQuery.setSkuType(skuInfoExtDTO.getCategoryName());
            }
            wmsSQuery.setWarehouseCode(realWarehouseE.getRealWarehouseCode());
            wmsSQuery.setWarehouseName(realWarehouseE.getRealWarehouseName());
            wmsSQuery.setSkuCode(detail.getItemCode());
            if (StringUtils.isBlank(detail.getUnit())) {
                if (skuInfoExtDTO != null) {
                    wmsSQuery.setSkuUnit(skuInfoExtDTO.getSpuUnitCode());
                }
            } else {
                wmsSQuery.setSkuUnit(detail.getUnit());
            }
//            wmsSQuery.setRealQty(detail.getRealQty());
            wmsSQuery.setAvailableQty(detail.getAvailableQty());

            wmsSQuery.setLockQty(detail.getLockQty());
            wmsSQuery.setQualityQty(detail.getQualityQty());
            wmsSQuery.setInventoryTransfer(detail.getOnroadQty());
            wmsSQuery.setUnqualifiedQty(detail.getUnqualifiedQty());
            wmsSQuery.setBeachCode(detail.getBatchCode());
            // 库存现有量计算=真实库存+质检+质检不合格
            BigDecimal rQty = BigDecimal.ZERO;
            if(detail.getRealQty() != null) {
            	rQty = rQty.add(detail.getRealQty());
            }
            if(detail.getQualityQty() != null) {
            	rQty = rQty.add(detail.getQualityQty());
            }
            if(detail.getUnqualifiedQty() != null) {
            	rQty = rQty.add(detail.getUnqualifiedQty());
            }
            wmsSQuery.setRealQty(rQty);
            stockQueryDTOS.add(wmsSQuery);
        });
        pageInfo.setList(stockQueryDTOS);
        return pageInfo;
    }

    private StockQueryResponse queryStockFromWms(WmsStockQueryDTO wmsStockQueryDTO, RealWarehouseE realWarehouseE, List<String> skuCodes) {
        //调用wms库存查询接口
        Integer pageIndex = wmsStockQueryDTO.getPageIndex();
        Integer pageSize = wmsStockQueryDTO.getPageSize();

        StockQueryRequest request = new StockQueryRequest();
        request.setRealWarehouseId(realWarehouseE.getId());
        request.setRealWarehouseOutCode(realWarehouseE.getRealWarehouseOutCode());
        request.setFactoryCode(realWarehouseE.getFactoryCode());
        request.setCurrentPage(pageIndex);
        request.setPageSize(pageSize);
        request.setRealWarehouseCode(realWarehouseE.getRealWarehouseCode());
        if (CollectionUtils.isNotEmpty(skuCodes)){
            request.setItemCodeList(skuCodes);
        }else {
            if(WarehouseWmsConfigEnum.WDT.name().equals(WmsRequestUtils.getRequestWmsTypeCode(request))) {
                if (CollectionUtils.isEmpty(skuCodes)){
                    List<RealWarehouseStockDTO> dtos = realWarehouseRepository.querySkuIdByWhId(realWarehouseE.getId());
                    if(dtos == null || dtos.size() == 0) {
                        return null;
                    }
                    List<String> skuCodeList = dtos.stream().map(RealWarehouseStockDTO::getSkuCode).collect(Collectors.toList());
                    request.setItemCodeList(skuCodeList);
                }
            }
        }
        request.setExt("I");//sap查询要用的，就是A和I
        return RequestWmsClient.stockQueryRequest(request);
    }



    @Override
    public List<RealWarehouse> queryWmsRealWarehouseByFactoryCode(String factoryCode) {
        //根据仓库编码查询所有的非门店实仓信息
        List<RealWarehouse> realWarehouses = realWarehouseRepository.queryWmsRealWarehouseByFactoryCode(factoryCode);
        return realWarehouses;
    }

    /**
     * 导出实仓库存
     * @return
     */
    @Override
    @AsyncExcelAutoExport(taskType = "real_warehouse_stock_export", fileNamePrefix = "#stockRecord.exportName", userId = "#stockRecord.userId")
    public void exportRealWarehouseStock(StockRecord stockRecord) throws Exception {
        stockRecord.setQueryCount(false);
        StockRecordService stockRecordService = applicationContext.getBean(StockRecordService.class);
        PageInfo<StockRecord> realWarehouseStock = stockRecordService.getSkusInfoByQueryCondition(stockRecord);
        if (realWarehouseStock == null || realWarehouseStock.getList() == null || realWarehouseStock.getList().size() == 0) {
            return ;
        }
        //根据仓分组
        Map<String, List<StockRecord>> collectMapList = realWarehouseStock.getList().stream().filter(v->StringUtils.isNotEmpty(v.getWarehouseCode())).collect(Collectors.groupingBy(StockRecord::getWarehouseCode));
        List<String> keyList=new ArrayList<>(collectMapList.keySet());
        List<List<String>> splitList = RomeCollectionUtil.splitList(keyList, 100);
        Map<String, SkuAllUnitDTO> skuUnitMap= Maps.newHashMap();
        Map<String, SkuAttributeInfoDTO > skuAttrMap=Maps.newHashMap();
        Map<String, SkuInfoExtDTO> skuInfoExtDTOMap= Maps.newHashMap();
        List<StockRecordExportTemplate> resList=new ArrayList<>();
        for (List<String> stockRecordList : splitList) {
            //每次将100个仓的数据放在一起，调订单接口进行查询
            List<StockRecord> stockRecords =Lists.newArrayList();
            for (String stockRecordKey : stockRecordList) {
                if(!collectMapList.containsKey(stockRecordKey)){
                    continue;
                }
                stockRecords.addAll(collectMapList.get(stockRecordKey));
            }
            this.addSkuUnitParams(stockRecords,skuUnitMap,skuAttrMap,skuInfoExtDTOMap);
            for(StockRecord dto : stockRecords) {
                StockRecordExportTemplate stockRecordExportTemplate=new StockRecordExportTemplate();
                BeanUtils.copyProperties(dto,stockRecordExportTemplate);
                stockRecordExportTemplate.setBarCode(stockRecordExportTemplate.getBarCode()+"\n");
                resList.add(stockRecordExportTemplate);
            }
        }
        CsvBigExportUtil.syncExportNormalCsv(resList);
    }





    /**
     * 添加商品参数
     * @param stockRecordEs
     * @param skuUnitMap
     * @param skuAttrMap
     * @param skuInfoMap
     */
    public void addSkuUnitParams(List<StockRecord> stockRecordEs,Map<String,SkuAllUnitDTO> skuUnitMap,
                                 Map<String,SkuAttributeInfoDTO> skuAttrMap,Map<String, SkuInfoExtDTO> skuInfoMap){
        List<String> skuCodes=stockRecordEs.stream().map(StockRecord::getSkuCode).distinct().collect(Collectors.toList());
        this.querySkuExtInfo(skuCodes,skuUnitMap,skuAttrMap,skuInfoMap);
        //分开查询仓库在途数据、分开查询门店在途数据
        List<ReplenishShopSkuReqDTO> factoryResList=this.queryInTransitNoLock(stockRecordEs);
        List<ReplenishShopSkuReqDTO> shopResList=this.querySkuCountByShopCodeList(stockRecordEs);
        Map<String,ReplenishShopSkuReqDTO> factoryResMap=factoryResList.stream().collect(Collectors.toMap(a->a.getFactoryCode()+"-"+a.getRealWarehouseCode()+"_"+a.getSkuCode(), Function.identity(), (v1, v2) -> v1));
        Map<String,ReplenishShopSkuReqDTO> shopResMap=shopResList.stream().collect(Collectors.toMap(a->a.getFactoryCode()+"_"+a.getSkuCode(), Function.identity(), (v1, v2) -> v1));
        //查询采购在途数量
        List<PurchaseInTransitDTO> purchaseInTransitList=queryPurchaseQtyInTransit(stockRecordEs);
        Map<String,PurchaseInTransitDTO> purchaseInTransitResMap=purchaseInTransitList.stream().collect(Collectors.toMap(a->a.getFactoryCode()+"-"+a.getWarehouseCode()+"_"+a.getSkuCode(), Function.identity(), (v1, v2) -> v1));
        for (StockRecord stockRecordE:stockRecordEs){
            try {
                //X001-C001_11441
                String onRoadKey=stockRecordE.getWarehouseCode()+"_"+stockRecordE.getSkuCode();
                if(factoryResMap.containsKey(onRoadKey)){
                    //未出库完成的仓库在途库存
                    stockRecordE.setUnCompleteOnRoadStock(factoryResMap.get(onRoadKey).getSkuQty());
                }else if(shopResMap.containsKey(onRoadKey)){
                    //107U_11441
                    //未出库完成的门店在途库存
                    stockRecordE.setUnCompleteOnRoadStock(shopResMap.get(onRoadKey).getSkuQty());
                }
                //采购在途数量
                if (purchaseInTransitResMap.containsKey(onRoadKey)){
                    stockRecordE.setPurchaseOnRoadStock(purchaseInTransitResMap.get(onRoadKey).getInTransitQty());
                }
                if (skuInfoMap.containsKey(stockRecordE.getSkuCode())) {
                    SkuInfoExtDTO skuInfo=skuInfoMap.get(stockRecordE.getSkuCode());
                    stockRecordE.setSkuCode(skuInfo.getSkuCode());
                    stockRecordE.setSkuName(skuInfo.getName());
                    stockRecordE.setSkuType(skuInfo.getCategoryName());
                    stockRecordE.setSkuTitle(skuInfo.getDescription());
                    stockRecordE.setSkuUnit(skuInfo.getSpuUnitName());
                    stockRecordE.setSkuUnitCode(skuInfo.getSpuUnitCode());
                    stockRecordE.setBarCode(skuInfo.getBarCode());
                }
                SkuAllUnitDTO skuAllUnitDTO=skuUnitMap.get(stockRecordE.getSkuCode());
                if(skuAllUnitDTO == null){
                    continue;
                }
                String boxUnitCode="KAR";
                List<SkuUnitExtDTO> boxSkuUnitExtDTOList = null;
                List<SkuUnitExtDTO> skuCodeUnits = skuAllUnitDTO.getSkuCodeUnits();
                if (skuCodeUnits != null) {
                    boxSkuUnitExtDTOList = skuCodeUnits.stream().filter((v)->boxUnitCode.equals(v.getUnitCode())).collect(Collectors.toList());
                }
                if(!CollectionUtils.isEmpty(boxSkuUnitExtDTOList)){
                    //箱单位
                    SkuUnitExtDTO boxSkuUnitExtDTO=boxSkuUnitExtDTOList.get(0);
                    if(boxSkuUnitExtDTO.getScale().compareTo(BigDecimal.ZERO)!=0){
                        BigDecimal boxUnitCount = stockRecordE.getAvailableQty().divide(boxSkuUnitExtDTO.getScale(),DECIMAL_POINT_NUM,BigDecimal.ROUND_DOWN);
                        stockRecordE.setBoxUnitName(boxSkuUnitExtDTO.getUnitName());
                        stockRecordE.setBoxUnitCount(boxUnitCount);
                        if(stockRecordE.getQualityQty() != null){
                            BigDecimal qualityQtyBox = stockRecordE.getQualityQty().divide(boxSkuUnitExtDTO.getScale(),DECIMAL_POINT_NUM,BigDecimal.ROUND_DOWN);
                            stockRecordE.setQualityQtyBox(qualityQtyBox);
                        }

                    }
                }
                SkuUnitExtDTO skuUnitExtDTO = skuFacade.getUnitByTypeAndFactory(skuAllUnitDTO, 3L, stockRecordE.getFactoryCode());
                if(skuUnitExtDTO != null){
                    //发货单位
                    if(skuUnitExtDTO.getScale().compareTo(BigDecimal.ZERO)==0){
                        continue;
                    }
                    BigDecimal saleOutUnitCount = stockRecordE.getAvailableQty().divide(skuUnitExtDTO.getScale(),DECIMAL_POINT_NUM,BigDecimal.ROUND_DOWN);
                    stockRecordE.setDeliveryUnitName(skuUnitExtDTO.getUnitName());
                    stockRecordE.setDeliveryUnitCount(saleOutUnitCount);
                }
                //匹配设置销售经营状态
                if(skuAttrMap.containsKey(stockRecordE.getSkuCode())){
                    SkuAttributeInfoDTO skuAttribute  = skuAttrMap.get(stockRecordE.getSkuCode());
                    stockRecordE.setSaleStatusName(SaleStatusVO.getDescByStatusCode(skuAttribute.getSaleStatus()));
                }
            }catch (Exception e){
                log.error("设置商品属性异常", e);
            }
        }
    }

    /**
     * 查询仓库对应物料在途
     * @param stockRecordEs
     * @return
     */
    private List<ReplenishShopSkuReqDTO> queryInTransitNoLock(List<StockRecord> stockRecordEs){
        List<StockRecord> factoryStockList=stockRecordEs.stream().filter(v->!RealWarehouseTypeVO.RW_TYPE_1.getType().equals(v.getRealWarehouseType())).distinct().collect(Collectors.toList());
        if(CollectionUtils.isEmpty(factoryStockList)){
            return Lists.newArrayList();
        }
        List<String> factorySkuCodes=factoryStockList.stream().map(StockRecord::getSkuCode).distinct().collect(Collectors.toList());
        List<String> warehouseCodes=factoryStockList.stream().map(StockRecord::getWarehouseCode).distinct().collect(Collectors.toList());
        List<RealWarehouseE> factoryCodesList=realWarehouseRepository.queryRealWarehouseByInCodes(warehouseCodes);
        if(CollectionUtils.isEmpty(factoryCodesList)){
            return Lists.newArrayList();
        }
        factoryCodesList.forEach(e->e.setRealWarehouseCode(e.getRealWarehouseOutCode()));
        List<RealWarehouse> shopCodesDtoList = realWarehouseConvertor.entityToDto(factoryCodesList);
        List<ReplenishShopSkuReqDTO> factoryResList = orderCenterFacade.queryInTransitNoLock(shopCodesDtoList,factorySkuCodes);
        if(CollectionUtils.isEmpty(factoryResList)){
            return Lists.newArrayList();
        }
        return factoryResList;
    }

    /**
     * 查询采购在途数量
     * @param stockRecords
     * @return
     */
    private List<PurchaseInTransitDTO> queryPurchaseQtyInTransit(List<StockRecord> stockRecords){
        List<PurchaseInTransitDTO> purchaseInTransitDTOS=Lists.newArrayList();
        if (CollectionUtils.isNotEmpty(stockRecords)){
            Map<String,Set<String>> paramsMap=Maps.newHashMap();
            for (StockRecord stockRecord:stockRecords){
                if (paramsMap.containsKey(stockRecord.getWarehouseCode())){
                    paramsMap.get(stockRecord.getWarehouseCode()).add(stockRecord.getSkuCode());
                }else {
                    paramsMap.put(stockRecord.getWarehouseCode(), Sets.newHashSet(stockRecord.getSkuCode()));
                }
            }
            List<RealWarehouseE> realWarehouseES=realWarehouseRepository.queryRealWarehouseByInCodes(Lists.newArrayList(paramsMap.keySet()));
            Map<String,RealWarehouseE> realWarehouseEMap=RomeCollectionUtil.listforMap(realWarehouseES,"realWarehouseCode");
            for (String warehouseCode:paramsMap.keySet()){
                if (realWarehouseEMap.containsKey(warehouseCode)){
                    RealWarehouseE realWarehouseE=realWarehouseEMap.get(warehouseCode);
                    List<String> skuCodes=Lists.newArrayList(paramsMap.get(warehouseCode));
                    List<PurchaseInTransitDTO> purchaseInTransits=purchaseCenterFacade.queryPurchaseQtyInTransit(realWarehouseE.getFactoryCode(),
                            realWarehouseE.getRealWarehouseOutCode(),skuCodes);
                    if (CollectionUtils.isNotEmpty(purchaseInTransits)){
                        purchaseInTransitDTOS.addAll(purchaseInTransits);
                    }
                }
            }
        }
        return purchaseInTransitDTOS;
    }

    /**
     * 查询门店仓对应物料在途
     * @param stockRecordEs
     * @return
     */
    private List<ReplenishShopSkuReqDTO> querySkuCountByShopCodeList(List<StockRecord> stockRecordEs){
        List<StockRecord> shopStockList=stockRecordEs.stream().filter(v->RealWarehouseTypeVO.RW_TYPE_1.getType().equals(v.getRealWarehouseType())).distinct().collect(Collectors.toList());
        if(CollectionUtils.isEmpty(shopStockList)){
            return Lists.newArrayList();
        }
        List<String> shopSkuCodes=shopStockList.stream().map(StockRecord::getSkuCode).distinct().collect(Collectors.toList());
        List<String> shopCodes=shopStockList.stream().map(StockRecord::getWarehouseCode).distinct().collect(Collectors.toList());
        List<RealWarehouseE> shopCodesList=realWarehouseRepository.queryRealWarehouseByInCodes(shopCodes);
        shopCodesList.forEach(e->e.setRealWarehouseCode(e.getRealWarehouseOutCode()));
        List<RealWarehouse> shopCodesDtoList = realWarehouseConvertor.entityToDto(shopCodesList);
        List<ReplenishShopSkuReqDTO> shopResList = orderCenterFacade.querySkuCountByShopCodeList(shopCodesDtoList,shopSkuCodes);
        if(CollectionUtils.isEmpty(shopResList)){
            return Lists.newArrayList();
        }
        return shopResList;
    }




    private void querySkuExtInfo(List<String> skuCodes,Map<String,SkuAllUnitDTO> skuUnitMap,Map<String,SkuAttributeInfoDTO> skuAttrMap
            ,Map<String, SkuInfoExtDTO> skuInfoMap){
        if (Objects.nonNull(skuInfoMap)){
            skuCodes=skuCodes.stream().filter(skuCode->!skuInfoMap.containsKey(skuCode)).collect(Collectors.toList());
            if (!org.springframework.util.CollectionUtils.isEmpty(skuCodes)){
                List<SkuInfoExtDTO> skuInfoExtDTOs = skuFacade.skuListBySkuCodes(skuCodes);
                if (!org.springframework.util.CollectionUtils.isEmpty(skuInfoExtDTOs)){
                    skuInfoExtDTOs.stream().forEach(skuInfo->{
                        skuInfoMap.put(skuInfo.getSkuCode(),skuInfo);
                    });
                }
            }
        }
        skuCodes=skuCodes.stream().filter(skuCode->!skuUnitMap.containsKey(skuCode)).collect(Collectors.toList());
        if (!org.springframework.util.CollectionUtils.isEmpty(skuCodes)){
            Map<String, SkuAllUnitDTO> itemMap=skuFacade.selectSkuWarehouseUnitBySkuCode(skuCodes);
            if (!MapUtils.isEmpty(itemMap)){
                skuUnitMap.putAll(itemMap);
            }
        }
        if (Objects.nonNull(skuAttrMap)){
            skuCodes=skuCodes.stream().filter(skuCode->!skuAttrMap.containsKey(skuCode)).collect(Collectors.toList());
            if (!org.springframework.util.CollectionUtils.isEmpty(skuCodes)){
                //获取商品的经营属性
                List<SkuAttributeInfoDTO> skuAttributeList = skuFacade.getSkuAttributeBySkuCodes(skuCodes);
                if (!org.springframework.util.CollectionUtils.isEmpty(skuAttributeList)){
                    skuAttributeList.stream().forEach(skuAttr->{
                        skuAttrMap.put(skuAttr.getSkuCode(),skuAttr);
                    });
                }
            }
        }

    }


    @Override
    public PageInfo<StockRecord> exportVirtualWarehouseStock(StockRecord stockRecord) {
        List<StockRecordE> stockRecordEs = null;
        List<VirtualWarehouseE> virtualWarehouseEs = stockRecordRepository.getVirtualWarehouseByCodeList(stockRecord.getVirtualWarehouseCodeList(),stockRecord.getRealWarehouseId(), stockRecord.getWarehouseTypeList());
        if(CollectionUtils.isEmpty(virtualWarehouseEs)) {
            throw new RomeException(ResCode.STOCK_ERROR_1002, ResCode.STOCK_ERROR_1002_DESC + "请选择一个仓库或者不存在");
        }
        //获取实体仓id集合
        List<Long> virtualWarehouseIds = virtualWarehouseEs.stream().map(VirtualWarehouseE::getId).collect(Collectors.toList());
        if (virtualWarehouseIds.size() == 0) {
            return new PageInfo<>();
        }
        Page page = PageHelper.startPage(stockRecord.getPageIndex(), stockRecord.getPageSize(),stockRecord.getQueryCount());
        //如果是按照sku编码精确查询
        if (stockRecord.getSkuIds() != null && stockRecord.getSkuIds().size() > 0) {
            //如果是按照skuIds查找商品信息
            stockRecordEs = stockRecordRepository.getVirSkusInfoByQueryConditionName(
                    stockRecordConvertor.entityToDo(stockRecordConvertor.dtoToEntity(stockRecord)),
                    virtualWarehouseIds, stockRecord.getSkuIds(),
                    stockRecord.getMoreThanZero(),stockRecord.getMoreThanZeroLock(),stockRecord.getMoreThanZeroBaoYou(),
                    stockRecord.getId());
        } else {
            //可以直接使用第一种查询方案
            stockRecordEs = stockRecordRepository.getVirSkusInfoByQueryConditionId(
                    stockRecordConvertor.entityToDo(stockRecordConvertor.dtoToEntity(stockRecord)),
                    virtualWarehouseIds, stockRecord.getMoreThanZero(),stockRecord.getMoreThanZeroLock(),stockRecord.getMoreThanZeroBaoYou(),
                    stockRecord.getId());
        }
        //将上述获得的stockRecordEs的skuId统计出来，去查找其相关信息
        if (stockRecordEs == null || stockRecordEs.size() == 0) {
            return new PageInfo<>();
        }
        List<SkuInfoExtDTO> skuInfoExtDTOs = skuFacade.skusBySkuId(stockRecordEs.stream().map(StockRecordE::getSkuId).distinct().collect(Collectors.toList()));
        Map<Long, SkuInfoExtDTO> skuInfoExtDTOMap = skuInfoExtDTOs.stream().collect(Collectors.toMap(SkuInfoExtDTO::getId, item -> item));
        Map<Long, VirtualWarehouseE> virtualWarehouseEMap = virtualWarehouseEs.stream().collect(Collectors.toMap(VirtualWarehouseE::getId, item -> item));
        List<Long> realWarehouseIdList=virtualWarehouseEs.stream().map(VirtualWarehouseE::getRealWarehouseId).collect(Collectors.toList());
        List<RealWarehouseE> realWarehouseEList=realWarehouseRepository.queryWarehouseByIds(realWarehouseIdList);
        Map<Long, RealWarehouseE> realWarehouseEMap = realWarehouseEList.stream().collect(Collectors.toMap(RealWarehouseE::getId, item -> item));
        for (StockRecordE item : stockRecordEs) {
            if (skuInfoExtDTOMap.containsKey(item.getSkuId())) {
                item.setSkuCode(skuInfoExtDTOMap.get(item.getSkuId()).getSkuCode());
            }
        }
        if (!stockRecord.getExport()){
            this.addSkuUnitParams(stockRecordEs);
        }
        List<StockRecord> stockRecords = stockRecordConvertor.entityToDto(stockRecordEs);
        for (StockRecord item : stockRecords) {
            if (virtualWarehouseEMap.containsKey(item.getWarehouseId())) {
                item.setWarehouseName(virtualWarehouseEMap.get(item.getWarehouseId()).getVirtualWarehouseName());
                item.setWarehouseCode(virtualWarehouseEMap.get(item.getWarehouseId()).getVirtualWarehouseCode());
                item.setWarehouseTypeName(VirtualWarehouseTypeVO.getDescByType(virtualWarehouseEMap.get(item.getWarehouseId()).getVirtualWarehouseType()));
                if(realWarehouseEMap.containsKey(virtualWarehouseEMap.get(item.getWarehouseId()).getRealWarehouseId())){
                    RealWarehouseE realWarehouseE = realWarehouseEMap.get(virtualWarehouseEMap.get(item.getWarehouseId()).getRealWarehouseId());
                    item.setRealWarehouseCode(realWarehouseE.getRealWarehouseCode());
                    item.setRealWarehouseName(realWarehouseE.getRealWarehouseName());
                    item.setFactoryCode(realWarehouseE.getFactoryCode());
                }
            }
            if (skuInfoExtDTOMap.containsKey(item.getSkuId())) {
                item.setSkuType(skuInfoExtDTOMap.get(item.getSkuId()).getCategoryName());
                item.setSkuTitle(skuInfoExtDTOMap.get(item.getSkuId()).getDescription());
                item.setSkuUnit(skuInfoExtDTOMap.get(item.getSkuId()).getSpuUnitName());
                item.setSkuUnitCode(skuInfoExtDTOMap.get(item.getSkuId()).getSpuUnitCode());
                item.setSkuName(skuInfoExtDTOMap.get(item.getSkuId()).getName());
                item.setSkuCode(skuInfoExtDTOMap.get(item.getSkuId()).getSkuCode());
                item.setBarCode(skuInfoExtDTOMap.get(item.getSkuId()).getBarCode());
            }
        }
        PageInfo<StockRecord> pageInfo=new PageInfo<>();
        pageInfo.setTotal(page.getTotal());
        pageInfo.setList(stockRecords);
        pageInfo.setSize(page.getPageSize());
        pageInfo.setPageNum(page.getPageNum());
        return pageInfo;
    }

    @Override
    public PageInfo<StockRecord> getMerchantSkusInfoByQueryCondition(StockRecord stockRecord) {
        List<String> realWarehouseCodes=new ArrayList<>();
        if(skuFacade.getDefaultMerchantId().equals(stockRecord.getMerchantId())){
            //管理员账号能看到所有商家仓信息
            List<RealWarehouseE> list=realWarehouseRepository.queryRealWarehouseByRWType(RealWarehouseTypeVO.RW_TYPE_21.getType());
            realWarehouseCodes=list.stream().map(RealWarehouseE::getRealWarehouseCode).distinct().collect(Collectors.toList());
        }else{
            RealWarehouse realWarehouse = stockService.queryRealWarehouseByMerchantId(stockRecord.getMerchantId());
            if(null == realWarehouse){
                throw new RomeException(ResCode.STOCK_ERROR_1003,"商家仓不存在");
            }
            realWarehouseCodes.add(realWarehouse.getRealWarehouseCode());
        }
        stockRecord.setWarehouseCodes(realWarehouseCodes);
        StockRecordService stockRecordService = applicationContext.getBean(StockRecordService.class);
        return stockRecordService.getSkusInfoByQueryCondition(stockRecord);
    }

    @Override
    public PageInfo<StockRecord> getSpacialSkusInfoByQueryCondition(StockRecord stockRecord) {
        //如果不选择仓库,只能查询量贩店的库存
        if(CollectionUtils.isEmpty(stockRecord.getWarehouseCodes())){
            List<RealWarehouse> realWarehouses = realWarehouseService.querySpecialRealWarehouseList();
            stockRecord.setWarehouseCodes(realWarehouses.stream().map(RealWarehouse::getRealWarehouseCode).distinct().collect(Collectors.toList()));
        }
        //需要将有大包装组合的品,转换成他对应的子品,然后再查询库存信息
        List<Long> barCodeSkuIds = new ArrayList<>();
        boolean res = this.getSkuInfoByBarCode(stockRecord, barCodeSkuIds);
        //把barCodeList清空,避免重复查询
        stockRecord.getBarCodeList().clear();
        if(!res){
            return new PageInfo<>();
        }
        //两个都不为空的时候 取交集
        if(CollUtil.isNotEmpty(stockRecord.getSkuIds()) && CollUtil.isNotEmpty(barCodeSkuIds)){
            List<Long> skuIds = stockRecord.getSkuIds().stream().filter(x -> barCodeSkuIds.contains(x)).collect(Collectors.toList());
            if(CollUtil.isEmpty(skuIds)){
                return new PageInfo<>();
            }
            stockRecord.setSkuIds(skuIds);
        }
        if(CollUtil.isEmpty(stockRecord.getSkuIds()) && CollUtil.isNotEmpty(barCodeSkuIds)){
            stockRecord.setSkuIds(new ArrayList<>());
            stockRecord.getSkuIds().addAll(barCodeSkuIds);
        }
        //查询子品的库存信息
        Set<Long> childSkuIds = new HashSet<>();
        //查询skuId对应的的skuCode
        if(CollectionUtils.isNotEmpty(stockRecord.getSkuIds())){
            List<SkuInfoExtDTO>  skuInfoList=skuFacade.skusBySkuId(stockRecord.getSkuIds());
            if(CollectionUtils.isNotEmpty(skuInfoList)){
                List<String> skuCodeList = skuInfoList.stream().map(SkuInfoExtDTO::getSkuCode).distinct().collect(Collectors.toList());
                List<CombineSkuInfoResultDTO> combineSkuInfoList = skuFacade.queryExtCombineInfoByCodes(skuCodeList);
                if(CollectionUtils.isNotEmpty(combineSkuInfoList)){
                    for (CombineSkuInfoResultDTO dto : combineSkuInfoList) {
                        if(CombineSkuTypeVO.LARGE_COMBINE.getCombineType().equals(dto.getCombineType())) {
                            childSkuIds.add(dto.getCombineSkuId());
                        }else{
                            childSkuIds.add(dto.getSkuId());
                        }
                    }
                }else{
                    childSkuIds.addAll(stockRecord.getSkuIds());
                }
            }
            stockRecord.setSkuIds(Lists.newArrayList(childSkuIds));
        }
        StockRecordService stockRecordService = applicationContext.getBean(StockRecordService.class);
        //量贩店查询库存
        stockRecord.setSpecial(true);
        return stockRecordService.getSkusInfoByQueryCondition(stockRecord);
    }
}
