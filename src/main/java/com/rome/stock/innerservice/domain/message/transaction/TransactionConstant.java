package com.rome.stock.innerservice.domain.message.transaction;

/**
 * @Description TransactionConstant
 * <AUTHOR>
 * @Date 2021/7/14 11:47
 * @Version 1.0
 **/
public class TransactionConstant {

	/**
	 * 监听的事务消息生产者组
	 */
	public static  final String TX_PRODUCER_GROUP = "stock_tx_produce_group";

	/**
	 * 标签，可以根据不同业务目的在同一主题下设置不同标签，消费者可以根据Tag实现对不同的不同消费逻辑
	 * 	tags从命名来看像是一个复数，但发送消息时，目的地只能指定一个topic下的一个tag，不能指定多个
	 * 	这里没有特殊需求，基本上都是根据topic区分的业务的。所以这里将tags写死
	 */
	public static  final String TX_PRODUCER_TOPIC_TAGS = "stock_tx_produce_tags";

	public static  final String TX_PRODUCER_TOPIC_LABEL = "TOPIC";

	public static  final String TX_PRODUCER_TOPIC = "stock_order_cost_topic";

}
