package com.rome.stock.innerservice.domain.entity;

import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

@Component
@Scope("prototype")
@Data
@EqualsAndHashCode(callSuper = false)
public class VirtualWarehouseFlowRecordE extends BaseE {
	/**
	 * 唯一主键
	 */
	private Long id;
	/**
	 * 分配单单号
	 */
	private String recordCode;
	/**
	 * 虚拟仓库id
	 */
	private Long virtualWarehouseId;
	/**
	 * 虚拟仓库code
	 */
	private String virtualWarehouseCode;
	/**
	 * 虚拟仓库name
	 */
	private String virtualWarehouseName;
	/**
	 * 实仓id
	 */
	private Long realWarehouseId;
	/**
	 * 实仓code
	 */
	private String realWarehouseCode;
	/**
	 * 实仓name
	 */
	private String realWarehouseName;
	/**
	 * 起始时间
	 */
	private Date startTime;
	/**
	 * 结束时间
	 */
	private Date endTime;
	/**
	 * 商品id
	 */
	private Long skuId;
	/**
	 * 商品编码
	 */
	private String skuCode;
	/**
	 * 商品名称
	 */
	private String skuName;
	/**
	 * 商品单位
	 */
	private String skuUnit;
	/**
	 * 变更数量
	 */
	private BigDecimal changeQty;
	/**
	 * 前置单据类型
	 */
	private Integer frontRecordType;
	/**
	 * 前置单据类型名称
	 */
	private String frontRecordTypeName;
	/**
	 * 库存交易类型名称
	 */
	private String transTypeName;
	/**
	 * 库存类型
	 */
	private Integer stockType;
	/**
	 * 库存类型名称
	 */
	private String stockTypeName;
	/**
	 * 商品ids
	 */
	private List<Long> skuIds;
	/**
	 * 库存交易类型
	 */
	private Integer transType;
}



