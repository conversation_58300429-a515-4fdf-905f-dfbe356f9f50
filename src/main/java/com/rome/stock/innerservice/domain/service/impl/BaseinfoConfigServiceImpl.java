package com.rome.stock.innerservice.domain.service.impl;

import com.alibaba.fastjson.JSON;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.rome.arch.core.exception.RomeException;
import com.rome.stock.innerservice.api.dto.BaseinfoConfig;
import com.rome.stock.innerservice.api.dto.ShopBatchConfigDTO;
import com.rome.stock.innerservice.api.dto.ShopBatchValidConfigDTO;
import com.rome.stock.innerservice.common.ResCode;
import com.rome.stock.innerservice.domain.convertor.BaseinfoConfigConvertor;
import com.rome.stock.innerservice.domain.entity.BaseinfoConfigE;
import com.rome.stock.innerservice.domain.repository.BaseinfoConfigRepository;
import com.rome.stock.innerservice.domain.service.BaseinfoConfigService;
import com.rome.stock.innerservice.infrastructure.dataobject.BaseinfoConfigDO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.List;
import java.util.Objects;

/**
 * @Author: zhoupeng
 * @createTime: 2022年07月15日 10:08:20
 * @version: 1.0
 * @Description:
 */
@Slf4j
@Service
public class BaseinfoConfigServiceImpl implements BaseinfoConfigService {

    @Resource
    private BaseinfoConfigConvertor baseinfoConfigConvertor;

    @Resource
    private BaseinfoConfigRepository baseinfoConfigRepository;

    /**
     * 查询基本信息配置
     * @param baseinfoConfig
     * @return
     */
    @Override
    public PageInfo<BaseinfoConfig> getBaseinfoConfig(BaseinfoConfig baseinfoConfig) {
        Page page = PageHelper.startPage(baseinfoConfig.getPageIndex(), baseinfoConfig.getPageSize());
        List<BaseinfoConfigE> baseinfoConfigList = baseinfoConfigRepository.getBaseinfoConfig(baseinfoConfig);
        if (baseinfoConfigList == null || baseinfoConfigList.size() == 0) {
            return new PageInfo();
        }
        PageInfo<BaseinfoConfig> pageList = new PageInfo(baseinfoConfigConvertor.entityToDto(baseinfoConfigList));
        pageList.setTotal(page.getTotal());
        return pageList;
    }

    @Override
    public Integer deleteBaseinfoConfig(BaseinfoConfig baseinfoConfig) {
        return baseinfoConfigRepository.deleteBaseinfoConfig(baseinfoConfig.getId(),baseinfoConfig.getModifier());
    }

    @Override
    public void updateBatchConfig(ShopBatchConfigDTO shopBatchConfigDTO) {
        if(CollectionUtils.isEmpty(shopBatchConfigDTO.getValidConfigList())){
            throw new RomeException(ResCode.STOCK_ERROR_1001,"保质期区间配置不能为空");
        }
        for (ShopBatchValidConfigDTO shopBatchValidConfigDTO : shopBatchConfigDTO.getValidConfigList()) {
            if(shopBatchValidConfigDTO.getStartValue()==null||shopBatchValidConfigDTO.getEndValue()==null){
                throw new RomeException(ResCode.STOCK_ERROR_1001,"保质期区间,起始或结束值配置不能为空");
            }
            if(shopBatchValidConfigDTO.getStartValue()>shopBatchValidConfigDTO.getEndValue()){
                throw new RomeException(ResCode.STOCK_ERROR_1001,"保质期区间,起始值不能大于结束值");
            }
            if(shopBatchValidConfigDTO.getStartValue()<0){
                throw new RomeException(ResCode.STOCK_ERROR_1001,"保质期区间,起始值不能小于0");
            }
            if(shopBatchValidConfigDTO.getEndValue()<0){
                throw new RomeException(ResCode.STOCK_ERROR_1001,"保质期区间,结束值不能小于0");
            }
            if(shopBatchValidConfigDTO.getRemovalDay()==null){
                throw new RomeException(ResCode.STOCK_ERROR_1001,"撤柜要求天数不能为空");
            }
            if(shopBatchValidConfigDTO.getRemovalDay()<0){
                throw new RomeException(ResCode.STOCK_ERROR_1001,"撤柜要求天数不能小于0");
            }
        }
        if(shopBatchConfigDTO.getSkuType() == null){
            throw new RomeException(ResCode.STOCK_ERROR_1001,"sku类型不能为空");
        }
        if(shopBatchConfigDTO.getCombineType() == null){
            throw new RomeException(ResCode.STOCK_ERROR_1001,"组合类型不能为空");
        }
        if(Objects.nonNull(shopBatchConfigDTO.getCountDiff()) && shopBatchConfigDTO.getCountDiff().compareTo(BigDecimal.ZERO)<0){
            throw new RomeException(ResCode.STOCK_ERROR_1001,"计件差值不能小于0");
        }
        if(Objects.nonNull(shopBatchConfigDTO.getWeightDiff()) && shopBatchConfigDTO.getWeightDiff().compareTo(BigDecimal.ZERO)<0){
            throw new RomeException(ResCode.STOCK_ERROR_1001,"称重差值不能小于0");
        }
        BaseinfoConfig baseinfoConfig=new BaseinfoConfig();
        baseinfoConfig.setCode("shop_batch_config");
        baseinfoConfig.setParamName("shop_batch_config");
        baseinfoConfig.setParamValue(JSON.toJSONString(shopBatchConfigDTO));
        int j= baseinfoConfigRepository.updateBatchConfig(baseinfoConfig);
        if(j==0){
            throw new RomeException(ResCode.STOCK_ERROR_1001,"更新失败");
        }
    }
}
