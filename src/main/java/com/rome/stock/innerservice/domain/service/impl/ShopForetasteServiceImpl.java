package com.rome.stock.innerservice.domain.service.impl;

import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.rome.arch.core.domain.EntityFactory;
import com.rome.arch.core.exception.RomeException;
import com.rome.stock.core.constant.StockCoreConsts;
import com.rome.stock.innerservice.api.dto.RealWarehouse;
import com.rome.stock.innerservice.api.dto.frontrecord.ShopAdjustRecordDTO;
import com.rome.stock.innerservice.api.dto.warehouserecord.OutWarehouseRecordDTO;
import com.rome.stock.innerservice.api.dto.warehouserecord.RecordDetailDTO;
import com.rome.stock.innerservice.common.AlikAssert;
import com.rome.stock.innerservice.common.KibanaLogUtils;
import com.rome.stock.innerservice.common.ResCode;
import com.rome.stock.innerservice.common.RomeCollectionUtil;
import com.rome.stock.innerservice.constant.*;
import com.rome.stock.innerservice.domain.convertor.frontrecord.ShopForetasteRecordConvertor;
import com.rome.stock.innerservice.domain.entity.RealWarehouseE;
import com.rome.stock.innerservice.domain.entity.frontrecord.ShopForetasteRecordDetailE;
import com.rome.stock.innerservice.domain.entity.frontrecord.ShopForetasteRecordE;
import com.rome.stock.innerservice.domain.entity.warehouserecord.*;
import com.rome.stock.core.domain.repository.CoreRealWarehouseStockRepository;
import com.rome.stock.innerservice.domain.repository.RealWarehouseRepository;
import com.rome.stock.innerservice.domain.repository.frontrecord.FrAdjustForetasteRepository;
import com.rome.stock.innerservice.domain.repository.warehouserecord.WarehouseRecordRepository;
import com.rome.stock.innerservice.domain.service.RealWarehouseService;
import com.rome.stock.innerservice.domain.service.ShopForetasteService;
import com.rome.stock.core.facade.RedisRollBackFacade;
import com.rome.stock.innerservice.infrastructure.dataobject.FrontWarehouseRecordRelationDO;
import com.rome.stock.core.infrastructure.dataobject.core.CoreRealStockOpDO;
import com.rome.stock.core.infrastructure.dataobject.core.CoreRealStockOpDetailDO;
import com.rome.stock.innerservice.remote.base.dto.SaleOrgDTO;
import com.rome.stock.innerservice.remote.base.dto.StoreDTO;
import com.rome.stock.innerservice.remote.base.facade.SaleOrgFacade;
import com.rome.stock.innerservice.remote.base.facade.ShopFacade;
import com.rome.stock.innerservice.remote.item.dto.SkuInfoExtDTO;
import com.rome.stock.innerservice.remote.item.facade.SkuFacade;
import com.rome.stock.innerservice.remote.sap.facade.SapFacade;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;

/**
 * 门店试吃
 */
@Slf4j
@Service
public class ShopForetasteServiceImpl implements ShopForetasteService {

	@Resource
	private ShopForetasteRecordConvertor foretasteRecordConvertor;
	@Resource
	private FrAdjustForetasteRepository adjustForetasteRepository;
	@Resource
	private EntityFactory entityFactory;
	@Resource
	private RealWarehouseService realWarehouseService;
	@Resource
	private CoreRealWarehouseStockRepository coreRealWarehouseStockRepository;
	@Resource
	private SkuFacade skuFacade;
	@Resource
	private WarehouseRecordRepository warehouseRecordRepository;
	@Resource
	private RealWarehouseRepository realWarehouseRepository;
	@Resource
	private SapFacade sapFacade;
	@Resource
	private ShopFacade shopFacade;
	@Resource
	private SaleOrgFacade saleOrgFacade;

	@Override
	@Transactional(rollbackFor = Exception.class)
	public void addShopForetasteRecord(OutWarehouseRecordDTO dto) {
		//幂等校验
		WarehouseRecordE warehouseRecordE=warehouseRecordRepository.queryWarehouseRecordByRecordCode(dto.getRecordCode());
		if(null != warehouseRecordE){
			return;
		}
		RealWarehouseE outWarehouse = realWarehouseRepository.getRealWarehouseByOutCodeAndFactoryCode(dto.getWarehouseCode(),dto.getFactoryCode());
		if(null == outWarehouse){
			throw new RomeException(ResCode.STOCK_ERROR_1002,"出库仓库不存在");
		}
		boolean isSuccess = false;
		ShopForetasteWarehouseRecordE warehouseRecord = null;
		CoreRealStockOpDO coreRealStockOpDO  = null;
		List<RecordDetailDTO> frontRecordDetails = dto.getDetailList();
		//提取skuCode
		List<String> skuCodes = RomeCollectionUtil.getValueList(frontRecordDetails, "skuCode");
		//批量根据skuCode查询skuId
		List<SkuInfoExtDTO> skuInfoExtDTOS = skuFacade.skusBySkuCode(skuCodes);
		Map<String, SkuInfoExtDTO> skuIdInfos = RomeCollectionUtil.listforMap(skuInfoExtDTOS, "skuCode");
		//接口传输的单位为单位名称，根据单位名称查询单位code
		for (RecordDetailDTO detailDTO:frontRecordDetails) {
			if(skuIdInfos.containsKey(detailDTO.getSkuCode())){
				SkuInfoExtDTO infoExtDTO = skuIdInfos.get(detailDTO.getSkuCode());
//				detailDTO.setSkuId(infoExtDTO.getId());
				detailDTO.setBasicUnitCode(infoExtDTO.getSpuUnitCode());
				detailDTO.setBasicUnit(infoExtDTO.getSpuUnitName());
			}
		}

		warehouseRecord = entityFactory.createEntity(ShopForetasteWarehouseRecordE.class);
		//根据前置单生成出库单数据
		warehouseRecord.createOutRecordByFrontRecord(dto,outWarehouse);
		warehouseRecord.setBatchStatus(WarehouseRecordBatchStatusVO.INIT.getStatus());
		warehouseRecord.setRecordStatus(WarehouseRecordStatusVO.OUT_ALLOCATION.getStatus());
		//创建门店试吃出库单
		warehouseRecord.addRecord();
		//输出kibana日志
		KibanaLogUtils.printKibanaRecordInfo(dto.getRecordCode(),dto.getChannelCode(),StockRecordInfoTypeVo.SHOP_FORETASTE.getType(),StockRecordInfoTypeVo.SHOP_FORETASTE.getDesc(),"addShopForetasteRecord",dto);
		try{
			coreRealStockOpDO = warehouseRecord.initDecreaseStockObj(outWarehouse.getId(),false);
			//减少门店实仓库存
			coreRealWarehouseStockRepository.decreaseRealQty(coreRealStockOpDO);
			//改为job补偿机制来扣减批次库存
//			//发送mq消息，扣减批次库存
//			WarehouseBatchStockE warehouseBatchStockE = entityFactory.createEntity(WarehouseBatchStockE.class);
//			try {
//				//发送批次MQ
//				warehouseBatchStockE.sendOutBatchStockMq(warehouseRecord);
//			} catch (Exception e) {
//				//不用抛异常回滚  后面有补偿机制
//				log.error("门店试吃批次库存处理异常：出库单号{}", warehouseRecord.getRecordCode(), e);
//			}
			isSuccess = true;
		}catch (RomeException e) {
			log.error(e.getMessage(),e);
			throw new RomeException(ResCode.STOCK_ERROR_9002, ResCode.STOCK_ERROR_9002_DESC);
		} catch (Exception e) {
			log.error(e.getMessage(),e);
			throw new RomeException(ResCode.STOCK_ERROR_1003, ResCode.STOCK_ERROR_1003_DESC);
		}finally {
			//业务未处理成功，回滚库存扣减操作
			if(!isSuccess){
				RedisRollBackFacade.redisRollBack(coreRealStockOpDO);
			}
		}
	}

//	/**
//	 * 门店试吃单SAP过账
//	 */
//	@Override
//	@Transactional(rollbackFor = Exception.class)
//	public void shopForetasteSapPosting(WarehouseRecordE outRecord){
//		//根据出库单id查询出库单明细
//		List<WarehouseRecordDetail> details = warehouseRecordRepository.queryDetailListByRecordId(outRecord.getId());
//		outRecord.setWarehouseRecordDetails(details);
//
//		//根据实仓id查询仓库信息
//		RealWarehouse realWarehouse = realWarehouseService.findByRealWarehouseId(outRecord.getRealWarehouseId());
//		List<WastageOrderDTO> list = new ArrayList<>();
//		for (WarehouseRecordDetail outRecordDetail : outRecord.getWarehouseRecordDetails()) {
//			WastageOrderDTO dto = new WastageOrderDTO();
//			//出库单号
//			dto.setBillNo(outRecord.getRecordCode());
//			//明细id
//			dto.setBillNum(outRecordDetail.getId());
//			//sap类型
//			dto.setType(SAPConsumeTypeVO.SAP_TYPE_CONSUME.getType());
//			//明细创建时间
//			dto.setReqDate(DateFormatTools.dateToString(outRecordDetail.getCreateTime(),"yyyyMMdd"));
//			//skuCode
//			dto.setMaterialCode(outRecordDetail.getSkuCode());
//			//工厂code
//			dto.setPlant(realWarehouse.getFactoryCode());
//			//仓库外部code
//			dto.setStorage(realWarehouse.getRealWarehouseOutCode());
//			//调整数量
//			dto.setQuantity(outRecordDetail.getActualQty());
//			//调整单位
//			dto.setUnit(outRecordDetail.getUnitCode());
//			list.add(dto);
//		}
//
//		if(!sapFacade.syncShopForetasteRecordToSap(outRecord.getRecordCode(),list)){
//			throw new RomeException(ResCode.STOCK_ERROR_9003, ResCode.STOCK_ERROR_9003_DESC + ":wmsRecordCode=" + outRecord.getRecordCode());
//		}
//	}


	/**自定义分页查询试吃单
	 *
	 * @param frontRecord
	 * @return
	 */
	@Override
//	@TargetDataSource(DynamicDataSourceEnum.READ)
	public PageInfo<ShopAdjustRecordDTO> getShopForetasteRecord(ShopAdjustRecordDTO frontRecord) {
		Page page = PageHelper.startPage(frontRecord.getPageIndex(), frontRecord.getPageSize());
		List<ShopForetasteRecordE> shopEList = adjustForetasteRepository.queryFrAdjustForetaste(frontRecord);
		PageInfo<ShopAdjustRecordDTO> shopDtoList = new PageInfo<>(foretasteRecordConvertor.entityToDto(shopEList));

		//根据仓库id查询仓库名称
		List<Long> warehouseIds = RomeCollectionUtil.getValueList(shopEList, "realWarehouseId");
		List<RealWarehouseE> warehouseES = realWarehouseRepository.queryWarehouseByIds(warehouseIds);
		Map<Long, RealWarehouseE> warehouseEMap = RomeCollectionUtil.listforMap(warehouseES, "id");

		//根据门店code查询组织归属
		List<String> shopCodes = RomeCollectionUtil.getValueList(shopEList, "shopCode");
		List<StoreDTO> storeDTOS = shopFacade.searchByCodeList(shopCodes);
		Map<String, StoreDTO> codeMap = RomeCollectionUtil.listforMap(storeDTOS, "code");
		Map<String,SaleOrgDTO> orgMap = new HashMap<>();

		shopDtoList.getList().forEach(shopAdjustRecordDTO -> {
			if(warehouseEMap.containsKey(shopAdjustRecordDTO.getRealWarehouseId())){
				RealWarehouseE warehouseE = warehouseEMap.get(shopAdjustRecordDTO.getRealWarehouseId());
				shopAdjustRecordDTO.setRealWarehouseName(warehouseE.getRealWarehouseName());
			}
			if(codeMap.containsKey(shopAdjustRecordDTO.getShopCode())){
				StoreDTO storeDTO = codeMap.get(shopAdjustRecordDTO.getShopCode());
				if(!orgMap.containsKey(storeDTO.getCompanyCode())){
					SaleOrgDTO orgCode = saleOrgFacade.getOrgByOrgCode(storeDTO.getCompanyCode());
					shopAdjustRecordDTO.setOrganizationName(orgCode.getOrgName());
					orgMap.put(orgCode.getOrgCode(),orgCode);
				}else {
					shopAdjustRecordDTO.setOrganizationName(orgMap.get(storeDTO.getCompanyCode()).getOrgName());
				}
			}
			shopAdjustRecordDTO.setRecordStatusDesc(WarehouseRecordStatusVO.getDescByType(shopAdjustRecordDTO.getRecordStatus()));
			shopAdjustRecordDTO.setReasonDesc(ShopAdjustRecordBusinessReasonVO.getDescByType(shopAdjustRecordDTO.getReason()));
			shopAdjustRecordDTO.setRecordTypeDesc(FrontRecordTypeVO.getDescByType(shopAdjustRecordDTO.getRecordType()));
		});
		shopDtoList.setTotal(page.getTotal());
		return shopDtoList;
	}


	/**
	 * 根据试吃单编号查询试吃明细
	 * @param recordId
	 * @return
	 */
	@Override
//	@TargetDataSource(DynamicDataSourceEnum.READ)
	public ShopAdjustRecordDTO getRecordByRecordId(String recordId) {
		ShopForetasteRecordE foretasteRecordE = adjustForetasteRepository.getRecordByRecordId(Long.parseLong(recordId));
		List<ShopForetasteRecordDetailE> detailES = adjustForetasteRepository.queryDetailByRecordId(foretasteRecordE.getId());

		//根据前置单id查询出库单信息
		List<Long> warehouseIds = warehouseRecordRepository.queryWarehouseIdByFrontId(foretasteRecordE.getId(), foretasteRecordE.getRecordType());
		AlikAssert.isTrue(warehouseIds != null && warehouseIds.size() > 0, ResCode.STOCK_ERROR_1017, ResCode.STOCK_ERROR_1017_DESC);
		Long warehouseId = warehouseIds.get(0);
		//根据出库单id查询出库单明细
		List<WarehouseRecordDetail> warehouseRecordDetails = warehouseRecordRepository.queryDetailListByRecordId(warehouseId);

		try {
			//第三方接口异常 ，只打印错误日志，不影响主流程
			foretasteRecordE.convertRealToBasic(detailES);
		} catch (Exception e) {
			log.error(e.getMessage(), e);
		}
		//根据商品id获取商品信息
		List<Long> skuIds = RomeCollectionUtil.getValueList(detailES, "skuId");
		List<SkuInfoExtDTO> skuInfoList = null;
		try {
			skuInfoList = skuFacade.skusBySkuId(skuIds);
		} catch (Exception e) {
			log.error(e.getMessage(), e);
		}
		Map<Long, SkuInfoExtDTO> skuInfoMap = RomeCollectionUtil.listforMap(skuInfoList, "id");
		Map<Long, WarehouseRecordDetail> warehouseRecordDetailMap = RomeCollectionUtil.listforMap(warehouseRecordDetails, "skuId");

		for (ShopForetasteRecordDetailE detailE:detailES) {
			if(skuInfoMap.containsKey(detailE.getSkuId())){
				detailE.setSkuCode(skuInfoMap.get(detailE.getSkuId()).getSkuCode());
				detailE.setSkuName(skuInfoMap.get(detailE.getSkuId()).getName());
				if(warehouseRecordDetailMap.containsKey(detailE.getSkuId()) && null != detailE.getScale()){
					//计算实际出库数量
					WarehouseRecordDetail warehouseRecordDetail = warehouseRecordDetailMap.get(detailE.getSkuId());
					detailE.setActualQty(warehouseRecordDetail.getActualQty().divide(detailE.getScale(), StockCoreConsts.DECIMAL_POINT_NUM,BigDecimal.ROUND_DOWN));
				}
			}else {
				detailE.setSkuName("");
			}
		}

		foretasteRecordE.setFrontRecordDetails(detailES);
		ShopAdjustRecordDTO shopAdjustRecordDTO = foretasteRecordConvertor.entityToDto(foretasteRecordE);
		RealWarehouse realWarehouse = realWarehouseService.findByRealWarehouseId(foretasteRecordE.getRealWarehouseId());
		shopAdjustRecordDTO.setRealWarehouseName(realWarehouse.getRealWarehouseName());
		shopAdjustRecordDTO.setRealWarehouseOutCode(realWarehouse.getRealWarehouseOutCode());

		return shopAdjustRecordDTO;
	}

	/**
	 * 保存sap过账单号
	 * @param recordId 出库单号
	 * @param sapCode sap过账单号
	 */
	@Override
	public boolean saveSapRecordCode(Long recordId, String sapCode) {
		//根据出库单号查询前置单id
		FrontWarehouseRecordRelationDO relation = warehouseRecordRepository.getFrontWarehouseRecordRelationByWrId(recordId);
		return adjustForetasteRepository.saveSapRecordCode(relation.getFrontRecordId(),sapCode);
	}

	@Override
	public void cancelBatchShopForetaste(String recordCode) {
		WarehouseRecordE recordE = warehouseRecordRepository.queryWarehouseRecordByRecordCode(recordCode);
		AlikAssert.isNotNull(recordE,ResCode.STOCK_ERROR_1002,"出入库单不存在");
		if(Objects.equals(recordE.getRecordStatus(),WarehouseRecordStatusVO.DISABLED.getStatus())){
			return;
		}
		List<WarehouseRecordDetail> warehouseRecordDetails = warehouseRecordRepository.queryDetailListByRecordId(recordE.getId());
		recordE.setWarehouseRecordDetails(warehouseRecordDetails);
		if(Objects.equals(recordE.getBusinessType(), WarehouseRecordBusinessTypeVO.OUT_WAREHOUSE_RECORD.getType())){
			//取消出库单
			//更新后置单状态为已取消
			warehouseRecordRepository.updateToCanceledFromComplete(recordE.getId());
			CoreRealStockOpDO coreRecordRealStockIncreaseDO = this.initIncreaseStockObj(recordE);
			boolean isSuccess = false;
			try {
				coreRealWarehouseStockRepository.increaseRealQty(coreRecordRealStockIncreaseDO);
				isSuccess = true;
			} catch (RomeException e) {
				throw e;
			} catch (Exception e) {
				throw new RomeException(ResCode.STOCK_ERROR_1003, ResCode.STOCK_ERROR_1003_DESC);
			} finally {
				if (!isSuccess) {
					RedisRollBackFacade.redisRollBack(coreRecordRealStockIncreaseDO);
				}
			}
		}
	}



	/**
	 * 初始化增加实体仓库库存的对象
	 */
	private CoreRealStockOpDO initIncreaseStockObj(AbstractWarehouseRecord recordE) {
		List<CoreRealStockOpDetailDO> increaseDetails = new ArrayList<>();
		for (WarehouseRecordDetail detail : recordE.getWarehouseRecordDetails()) {
			CoreRealStockOpDetailDO coreRealStockOpDetailDO = new CoreRealStockOpDetailDO();
			coreRealStockOpDetailDO.setSkuId(detail.getSkuId());
			coreRealStockOpDetailDO.setChannelCode(recordE.getChannelCode());
			coreRealStockOpDetailDO.setSkuCode(detail.getSkuCode());
			coreRealStockOpDetailDO.setRealQty(detail.getActualQty());
			coreRealStockOpDetailDO.setRealWarehouseId(recordE.getRealWarehouseId());
			increaseDetails.add(coreRealStockOpDetailDO);
		}
		CoreRealStockOpDO coreRealStockOpDO = new CoreRealStockOpDO();
		coreRealStockOpDO.setRecordCode(recordE.getRecordCode());
		coreRealStockOpDO.setTransType(recordE.getRecordType());
		coreRealStockOpDO.setDetailDos(increaseDetails);
		return coreRealStockOpDO;
	}

	/**
	 * 中台数据库查询所有门店
	 * @return
	 */
	@Override
	public List<RealWarehouse> queryShopList() {
		return realWarehouseService.queryRealWarehouseByRWType(RealWarehouseTypeVO.RW_TYPE_1.getType());
	}
}
