package com.rome.stock.innerservice.domain.entity;

import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.util.Date;

@Component
@Scope("prototype")
@Data
@EqualsAndHashCode(callSuper = false)
public class FrSaleWineLogE extends BaseE{

    /**
     * 唯一主键
     */
    private Long id;
    /**
     * 单据唯一编码(相当于主键，没有意义的唯一单号)
     */
    private String recordCode;
    /**
     * 明细行ID（sc_fr_sale_wdt_detail表id）
     */
    private Long frontDetailId;
    /**
     *主表单据编码（sc_fr_sale_wdt表record_code）
     */
    private String frontRecordCode;
    /**
     * 酒订单交易单号
     */
    private String outRecordCode;
    /**
     * 酒sku编码
     */
    private String skuCode;
    /**
     * 酒sku编码
     */
    private String bucketSkuCode;
    /**
     * 酒桶序列号（后台操作换桶,更新此字段）
     */
    private String serialNo;

    /**
     * 操作类型：0.初始化,1.更新信息，2.换桶
     */
    private Integer operateType;
    /**
     * 桶类型
     */
    private String bucketType;
    /**
     * 桶类型名称
     */
    private String bucketTypeName;
    /**
     * 酒精度数
     */
    private String vol;
    /**
     * 体积
     */
    private String volume;

    /**
     * 开始熟成时间
     */
    private Date startRipeTime;
    /**
     * 熟成要求天数
     */
    private String needRipeDays;

    /**
     * 抽样时间
     */
    private Date sampleTime;
    /**
     * 上次抽样时间
     */
    private Date lastSampleTime;

    /**
     * 酒数量（默认都是1）
     */
    private BigDecimal skuQty;
    /**
     * 酒单位
     */
    private String unit;
    /**
     * 酒单位CODE
     */
    private String unitCode;
    /**
     * 备注
     */
    private String remark;
}
