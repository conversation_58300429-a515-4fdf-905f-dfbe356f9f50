package com.rome.stock.innerservice.domain.entity.frontrecord;

import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;

/**
 * 前置预约单明细领域对象
 */
@Component
@Scope("prototype")
@Data
@EqualsAndHashCode(callSuper = false)
public class ReservationRecordDetailE extends AbstractFrontRecordDetail{


    /*领域属性*/
    /**
     * 唯一主键
     */
    private Long id;
    /**
     * 所属单据编码
     */
    private String recordCode;
    /**
     * 单据id
     */
    private Long frontRecordId;
    /**
     * 商品sku编码
     */
    private Long skuId;
    /**
     * 商品sku编码
     */
    private String skuCode;
    /**
     * 数量
     */
    private BigDecimal skuQty;
    /**
     * 单位
     */
    private String unit;

    /**
     * 前置单类型
     */
    private Integer recordType;

    /**
     * 单位code
     */
    private String unitCode;

    /**
     * 已分配数量
     */
    private BigDecimal assignedQty;

    /**
     * 未分配数量
     */
    private BigDecimal unassignedQty;

    /**
     * 拆品sku编号
     */
    private String spitSkuCode;


}    
   