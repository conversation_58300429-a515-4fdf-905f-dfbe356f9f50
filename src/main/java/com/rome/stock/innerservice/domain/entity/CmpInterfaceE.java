package com.rome.stock.innerservice.domain.entity;

import com.rome.stock.innerservice.domain.repository.CmpInterfaceRepository;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Component;

/**
 * cmp管理
 * <AUTHOR>
 */
@Data
@Slf4j
@Component
@Scope("prototype")
@EqualsAndHashCode(callSuper = false)
public class CmpInterfaceE extends BaseE {

    @Autowired
    private CmpInterfaceRepository cmpInterfaceRepository;

    /**
     * 保存cmp日志记录
     */
    public void addCmpInterfaceLog(){
        cmpInterfaceRepository.saveCmpInterfaceLog(this);
    }

    /**
     * 唯一主键
     */
    private Long id;

    /**
     * 请求服务名
     * @return
     */
    private String requestService;

    /**
     * 请求内容
     */
    private String requestContent;

    /**
     * 响应内容
     */
    private String responseContent;

    /**
     * 数据走向
     */
    private String transferContent;

    /**
     *  cmp开关：0.中台 1.中台或sap 2.sap
     */
    private String cmpSwitch ;

    /**
     *  调用类型 1：cmp调中台  2:中台调cmp
     */
    private Integer cmpType;

    /**
     * 0.初始化 1.成功 2.失败
     */
    private Integer cmpStatus;

}
