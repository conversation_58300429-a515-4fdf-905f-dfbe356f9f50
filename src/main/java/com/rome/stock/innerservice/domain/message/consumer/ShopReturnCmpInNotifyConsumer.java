package com.rome.stock.innerservice.domain.message.consumer;

import com.rome.scm.common.rocketmq.CustomConsumerListener;
import com.rome.stock.common.enums.CustomRocketMQEnum;
import com.rome.stock.innerservice.domain.service.ShopReturnService;
import lombok.extern.slf4j.Slf4j;
import org.apache.rocketmq.common.message.MessageExt;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;

/**
 * @Description: 门店退货入库推送cmp消费mq
 */
@Slf4j
@Service
public class ShopReturnCmpInNotifyConsumer implements CustomConsumerListener<MessageExt> {

    @Resource
    private ShopReturnService shopReturnService;

    @Override
    public void onMessage(MessageExt messageExt, String businessNo, String msgKey) {
        log.info("门店退货入库推送cmp消息消费，msgID: {}，消费次数: {}", messageExt.getMsgId(), messageExt.getReconsumeTimes());
        String recordCode = new String(messageExt.getBody());
        if(StringUtils.isEmpty(recordCode)) {
            log.error("门店退货入库推送cmp消息消费内容为空，msgID: {}", messageExt.getMsgId());
            return ;
        }
        //推送cmp
        shopReturnService.handleJoinReturnPushCMP(recordCode);

    }

    @Override
    public String getRocketMQCode() {
        return CustomRocketMQEnum.MQ_SHOP_RETURN_CMP_IN_NOTIFY_PUSH.getCode();
    }

}
