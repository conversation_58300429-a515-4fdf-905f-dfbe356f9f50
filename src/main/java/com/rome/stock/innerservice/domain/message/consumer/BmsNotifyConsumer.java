package com.rome.stock.innerservice.domain.message.consumer;

import com.alibaba.fastjson.JSONObject;
import com.rome.arch.core.exception.RomeException;
import com.rome.scm.common.rocketmq.CustomConsumerListener;
import com.rome.stock.common.constants.ResCode;
import com.rome.stock.common.enums.CustomRocketMQEnum;
import com.rome.stock.common.enums.warehouse.WarehouseRecordTypeVO;
import com.rome.stock.common.utils.AlikAssert;
import com.rome.stock.common.utils.DateUtil;
import com.rome.stock.innerservice.constant.WarehouseRecordBusinessTypeVO;
import com.rome.stock.innerservice.constant.WarehouseRecordStatusVO;
import com.rome.stock.innerservice.domain.entity.RealWarehouseE;
import com.rome.stock.innerservice.domain.entity.warehouserecord.WarehouseRecordE;
import com.rome.stock.innerservice.domain.message.transaction.MessageDTO;
import com.rome.stock.innerservice.domain.repository.RealWarehouseRepository;
import com.rome.stock.innerservice.domain.repository.warehouserecord.WarehouseRecordRepository;
import com.rome.stock.innerservice.domain.service.BmsRecordService;
import com.rome.stock.innerservice.domain.service.BmsService;
import com.rome.stock.innerservice.infrastructure.dataobject.RealWarehouseWmsConfigDO;
import com.rome.stock.innerservice.infrastructure.redis.RealWarehouseWmsRedis;
import com.rome.stock.innerservice.remote.bms.BmsPushRule;
import lombok.extern.slf4j.Slf4j;
import org.apache.rocketmq.common.message.MessageExt;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;
import java.util.Objects;

import static com.rome.stock.common.enums.warehouse.WarehouseRecordTypeVO.WH_CHAIN_DIRECT_IN_RECORD;

/**
 * @Description: 回传bms仓库作业及出入库数据消费mq
 */
@Slf4j
@Service
public class BmsNotifyConsumer implements CustomConsumerListener<MessageExt> {

    @Resource
    private BmsService bmsService;
    @Resource
    private BmsRecordService bmsRecordService;
    @Resource
    private WarehouseRecordRepository warehouseRecordRepository;
    @Resource
    private RealWarehouseRepository realWarehouseRepository;
    @Resource
    private RealWarehouseWmsRedis realWarehouseWmsRedis;

    @Override
    public void onMessage(MessageExt messageExt, String businessNo, String msgKey) {
        log.info("出入库回传bms消息消费，msgID: {}，消费次数: {}", messageExt.getMsgId(), messageExt.getReconsumeTimes());
        MessageDTO messageDTO= JSONObject.parseObject(messageExt.getBody(), MessageDTO.class);
        if(StringUtils.isEmpty(messageDTO.getRecordCode())) {
            log.error("出入库回传bms消息消费，msgID: {}", messageExt.getMsgId());
            return ;
        }
        WarehouseRecordE recordE=warehouseRecordRepository.queryWarehouseRecordByRecordCode(messageDTO.getRecordCode());
        AlikAssert.isNotNull(recordE, ResCode.STOCK_ERROR_1002,"出入库单据不存在:"+messageDTO.getRecordCode());
        boolean pushResult = pushBms(recordE,messageDTO.getZtRecordCode());
        specialHandler(recordE,pushResult);
    }

    @Override
    public String getRocketMQCode() {
        return CustomRocketMQEnum.MQ_BMS_NOTIFY_PUSH.getCode();
    }

    /**
     * 推送bms
     * @param recordE
     * @param entryCode
     * @return true:已推送bms;false:未推送bms;
     */
    private boolean pushBms(WarehouseRecordE recordE,String entryCode){
        //取消的不处理
        if (Objects.equals(WarehouseRecordStatusVO.DISABLED.getStatus(),recordE.getRecordStatus())){
            return false;
        }
        RealWarehouseE realWarehouseE=realWarehouseRepository.getRealWarehouseById(recordE.getRealWarehouseId());
        AlikAssert.isNotNull(realWarehouseE, ResCode.STOCK_ERROR_1002,"单据对应的实仓数据不存在:"+recordE.getRecordCode());
        recordE.setFactoryCode(realWarehouseE.getFactoryCode());
        //门店跳过不推送
        if (Objects.equals(realWarehouseE.getRwBusinessType(),1) || Objects.equals(realWarehouseE.getRwBusinessType(),2) ){
            return false;
        }
        RealWarehouseWmsConfigDO wmsConfigDO=realWarehouseWmsRedis.findWmsInformationById(realWarehouseE.getId());
        AlikAssert.isNotNull(wmsConfigDO, ResCode.STOCK_ERROR_1002,"实仓wms配置数据不存在:"+realWarehouseE.getRealWarehouseCode());
        //是否推送bms仓库作业
        boolean pushWork=false,pushInout=false;
        if (BmsPushRule.switchPushWork(recordE.getRecordType(), realWarehouseE, wmsConfigDO)){
            if (Objects.equals(recordE.getBusinessType(), WarehouseRecordBusinessTypeVO.OUT_WAREHOUSE_RECORD.getType())){
                bmsService.pushOutNotify(recordE);
            }else {
                bmsService.pushInNotify(recordE,entryCode);
            }
            pushWork=true;
        }
        //重新初始化 recordE
        recordE = warehouseRecordRepository.queryWarehouseRecordByRecordCode(recordE.getRecordCode());
        //是否推送bms出入库数据
        if (BmsPushRule.switchPushInOutRecord(recordE.getRecordType(), realWarehouseE, wmsConfigDO)){
            if (Objects.equals(recordE.getBusinessType(), WarehouseRecordBusinessTypeVO.OUT_WAREHOUSE_RECORD.getType())){
                bmsRecordService.pushOutNotify(recordE);
            }else {
                bmsRecordService.pushInNotify(recordE,entryCode);
            }
            pushInout=true;
        }
        return (pushWork || pushInout);
    }

    /**
     * 特殊单据处理
     * @param recordE
     * @param pushResult
     */
    private void specialHandler(WarehouseRecordE recordE,boolean pushResult){
        //门店直送
        if (Objects.equals(recordE.getRecordType(), WarehouseRecordTypeVO.SHOP_CHAIN_DIRECT_IN_RECORD.getType())){
            List<WarehouseRecordE> warehouseRecordList = warehouseRecordRepository.queryBySapCode(recordE.getSapOrderCode());
            WarehouseRecordE purchaseInRecord = warehouseRecordList.stream().filter(t -> WH_CHAIN_DIRECT_IN_RECORD.getType().equals(t.getRecordType())).findFirst().orElse(null);
            WarehouseRecordE replenishOutRecord = warehouseRecordList.stream().filter(t -> WarehouseRecordTypeVO.WH_CHAIN_DIRECT_OUT_RECORD.getType().equals(t.getRecordType())).findFirst().orElse(null);
            if (Objects.nonNull(purchaseInRecord)){
                pushBms(purchaseInRecord,purchaseInRecord.getRecordCode());
            }
            if (Objects.nonNull(replenishOutRecord)){
                pushBms(replenishOutRecord,null);
            }
        }else if (Objects.equals(recordE.getRecordType(), WarehouseRecordTypeVO.OUTSOURCE_PURCHASE_END_PRODUCT_IN_RECORD.getType())){
            //根据sap单号查询关联的所有出入库单据（委外仓成品出库单、大仓成品入库单）
            List<WarehouseRecordE> warehouseRecordES = warehouseRecordRepository.queryBySapCode(recordE.getSapOrderCode());
            // 筛选出对应的委外仓，成品出库单（如果存在取消，会有多条记录，需要排除掉取消的单据）
            WarehouseRecordE outWarehouseRecodeE = warehouseRecordES.stream().filter(x -> Objects.equals(x.getRecordType(), WarehouseRecordTypeVO.OUTSOURCING_OUT_RECORD.getType()) &&  !Objects.equals(x.getRecordStatus(), WarehouseRecordStatusVO.DISABLED.getStatus()) ).findFirst().orElse(null);
            if (Objects.nonNull(outWarehouseRecodeE)){
                pushBms(outWarehouseRecodeE,null);
            }
        }
        //退货预入库
        else if (Objects.equals(WarehouseRecordTypeVO.PREDICT_RETURN_DIRECT_IN_RECORD.getType(),recordE.getRecordType())) {
            if (!pushResult){ //未推送后续逻辑不考虑
                return;
            }
            if (StringUtils.isEmpty(recordE.getFactoryCode())){
                RealWarehouseE realWarehouseE=realWarehouseRepository.getRealWarehouseById(recordE.getRealWarehouseId());
                AlikAssert.isNotNull(realWarehouseE, ResCode.STOCK_ERROR_1002,"单据对应的实仓数据不存在:"+recordE.getRecordCode());
                recordE.setFactoryCode(realWarehouseE.getFactoryCode());
            }
            //非X310,退货预入库3天内重试消费
            if (!Objects.equals("X310", recordE.getFactoryCode())){
                long diff = DateUtil.diff(recordE.getCreateTime(), new Date(), 24 * 60 * 60 * 1000);
                AlikAssert.isTrue(diff>=2,ResCode.STOCK_ERROR_1000,"退货预入库单据2天内重试消费");
            }
        } else if (Objects.equals(recordE.getRecordType(), WarehouseRecordTypeVO.OUTSOURCING_IN_RECORD.getType())) {
            //根据sap单号查询关联的所有出入库单据（委外仓成品入库单、委外仓原料出库单）
            List<WarehouseRecordE> warehouseRecordES = warehouseRecordRepository.queryBySapCode(recordE.getSapOrderCode());
            // 筛选出对应的委外仓，原料出库单（如果存在取消，会有多条记录，需要排除掉取消的单据）
            WarehouseRecordE outWarehouseRecodeE = warehouseRecordES.stream().filter(x -> Objects.equals(x.getRecordType(), WarehouseRecordTypeVO.OUTSOURCE_PURCHASE_END_PRODUCT_OUT_RECORD.getType()) &&  !Objects.equals(x.getRecordStatus(), WarehouseRecordStatusVO.DISABLED.getStatus()) ).findFirst().orElse(null);
            if (Objects.nonNull(outWarehouseRecodeE)){
                pushBms(outWarehouseRecodeE,null);
            } else {
                throw new RomeException(ResCode.STOCK_ERROR_1001, "未查询到关联的原料出库单");
            }
        }

    }

}
