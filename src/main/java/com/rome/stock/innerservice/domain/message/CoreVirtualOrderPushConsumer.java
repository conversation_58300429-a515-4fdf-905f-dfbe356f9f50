package com.rome.stock.innerservice.domain.message;

import com.rome.scm.common.rocketmq.CustomConsumerListener;
import com.rome.stock.common.constants.KibanaLogConstants;
import com.rome.stock.common.constants.PageConstants;
import com.rome.stock.common.constants.WmsConfigConstants;
import com.rome.stock.common.enums.CustomRocketMQEnum;
import com.rome.stock.common.enums.warehouse.WarehouseRecordVO;
import com.rome.stock.innerservice.api.dto.WarehouseRecordDetailDTO;
import com.rome.stock.innerservice.api.dto.WarehouseRecordPageDTO;
import com.rome.stock.innerservice.api.dto.xuantian.virtual.VirtualDeliveryDTO;
import com.rome.stock.innerservice.api.dto.xuantian.virtual.VirtualDeliveryItemDTO;
import com.rome.stock.innerservice.api.dto.xuantian.virtual.VirtualDeliveryOrderDTO;
import com.rome.stock.innerservice.common.CoreKibanaLog;
import com.rome.stock.innerservice.common.StringUtils;
import com.rome.stock.innerservice.domain.entity.RwRecordPoolE;
import com.rome.stock.innerservice.domain.repository.RwRecordPoolRepository;
import com.rome.stock.innerservice.domain.service.StockService;
import com.rome.stock.innerservice.domain.service.WarehouseRecordService;
import com.rome.stock.innerservice.remote.xuantian.facade.XtFacade;
import lombok.extern.slf4j.Slf4j;
import org.apache.rocketmq.common.message.MessageExt;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

/**
 * 虚拟单据立即下发WMS消费MQ
 * <AUTHOR>
 */
@Slf4j
@Service
public class CoreVirtualOrderPushConsumer implements CustomConsumerListener<MessageExt> {


    @Resource
    private WarehouseRecordService warehouseRecordService;
    @Autowired
    private XtFacade xtFacade;
    @Autowired
    private StockService stockService;
    @Autowired
    private RwRecordPoolRepository rwRecordPoolRepository;

    @Override
    public void onMessage(MessageExt msg, String businessNo, String msgKey) {
       String content = new String(msg.getBody());
       log.info("虚拟订单推送消费消息,单号：{}" ,content);
       String recordCode = content;
       String doCode = null;
        if (content.contains("#")) {
            String[] split = recordCode.split("#");
            recordCode = split[0];
            doCode = split[1];
        }
        //后置单为空，但是doCode不为空，则需要去
        if (StringUtils.isBlank(recordCode) && StringUtils.isNotBlank(doCode)) {
            stockService.assignRecordToMerge(Arrays.asList(doCode), null);
            RwRecordPoolE pe = rwRecordPoolRepository.queryByDoCode(doCode);
            recordCode = pe.getWarehouseRecordCode();
        }
        this.handlerMessage(recordCode);
    }


    private void handlerMessage(String recordCode) {
        WarehouseRecordPageDTO recordPageDTO = new WarehouseRecordPageDTO();
        recordPageDTO.setPageIndex(1);
        recordPageDTO.setPageSize(PageConstants.ROWS_200);
        recordPageDTO.setBusinessType(WarehouseRecordVO.OUT_WAREHOUSE_RECORD.getType());
        recordPageDTO.setRecordCode(recordCode);

        //这里其实只能查到一条
        List<WarehouseRecordPageDTO> recordList = warehouseRecordService.queryNeedSyncOutWarehouseRecordList(recordPageDTO);
        if (CollectionUtils.isEmpty(recordList)) {
            return;
        }

        WarehouseRecordPageDTO record = recordList.get(0);
        try {
            int wmsCode = record.getWmsCode() == null ? WmsConfigConstants.WMS_EX : record.getWmsCode();
            if (WmsConfigConstants.WMS_VIRTUAL == wmsCode) {
                //推送到玄天对应的虚拟商品发货接口
                this.pushDataToXTWMSVIRTUAL(record);
            } else {
                log.info("推送虚拟订单job数据异常");
            }
        } catch (Exception e) {
            log.error("出库单【{}】下发到玄天系统异常", record.getRecordCode(), e);
            warehouseRecordService.updateRecordSyncWmsFailTime(Arrays.asList(record.getRecordCode()));
        }

    }

    /**
     * 构造推送到玄天商家仓接口的json数据，并上传----虚拟商品
     * @param record
     */
    private void pushDataToXTWMSVIRTUAL(WarehouseRecordPageDTO record) {
        String deliveryOrderCode = record.getRecordCode();
        // 2019.12.10 由于修改单据可能导致下发问题，先更新单据状态
        boolean rs = warehouseRecordService.updateRecordSyncStatusToSynchronized(Arrays.asList(record.getRecordCode()));
        //如果rs为false说明 job在mq之前跑了，mq就没有必要推送了
        if (rs) {
            //post数据到玄天
            Boolean flag = false;
            try {
                VirtualDeliveryDTO virtualDeliveryDTO = this.buildPushDataVirtual(record);
                flag = xtFacade.orderDelivery(virtualDeliveryDTO);
            } catch (Exception e) {
                log.error("post数据到玄天报错{}", e.getMessage(), e);
            } finally {
                // 失败则更新失败时间,回滚状态
                if (!flag) {
                    rs = warehouseRecordService.updateSyncWmsFailTimeAndStatus(deliveryOrderCode);
                    if (!rs) {
                        log.warn(CoreKibanaLog.getJobLog(KibanaLogConstants.WMS_STATUS_ROLL_BACK_LOG, "updateSyncWmsFailTimeAndStatus", "下发wms成功状态回滚", deliveryOrderCode));
                    }
                }
            }
        }
    }




    /**
     * 虚拟商品参数
     * @param record
     * @return
     */
    private VirtualDeliveryDTO buildPushDataVirtual(WarehouseRecordPageDTO record) {
        // 构造出库单信息
        VirtualDeliveryOrderDTO deliveryOrder = new VirtualDeliveryOrderDTO();
        // 送达方
        //中台交易单号
        deliveryOrder.setOrderNo(record.getOutRecordCode());
        deliveryOrder.setChannelCode(record.getChannelCode());
        deliveryOrder.setWarehouseRecordCode(record.getRecordCode());
        deliveryOrder.setSyncTransferStatus(record.getSyncTransferStatus());

        // 构造单据信息
        List<VirtualDeliveryItemDTO> orderLines = new ArrayList<>();
        List<WarehouseRecordDetailDTO> recordDetailList = record.getWarehouseRecordDetails();
        if(!CollectionUtils.isEmpty(recordDetailList)) {
            for(WarehouseRecordDetailDTO recordDetail:recordDetailList){
                VirtualDeliveryItemDTO orderLine = new VirtualDeliveryItemDTO();
                orderLine.setOrderLineNo(String.valueOf(recordDetail.getId()));
                orderLine.setUnitCode(recordDetail.getUnitCode());
                orderLine.setPlanQty(recordDetail.getPlanQty().intValue());
                orderLine.setSkuCode(recordDetail.getSkuCode());
                orderLine.setSkuId(recordDetail.getSkuId());
                orderLines.add(orderLine);
            }
        }

        // 返回构造对象
        VirtualDeliveryDTO outOrderDto = new VirtualDeliveryDTO();
        outOrderDto.setDeliveryOrder(deliveryOrder);
        outOrderDto.setOrderLines(orderLines);
        return outOrderDto;
    }


	@Override
	public String getRocketMQCode() {
		return CustomRocketMQEnum.MQ_VIRTUAL_ORDER_PUSH.getCode();
	}

}
