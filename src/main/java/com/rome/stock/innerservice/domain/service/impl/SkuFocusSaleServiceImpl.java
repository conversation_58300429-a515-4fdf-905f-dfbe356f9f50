package com.rome.stock.innerservice.domain.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.rome.arch.core.exception.RomeException;
import com.rome.stock.innerservice.api.dto.RealWarehouse;
import com.rome.stock.innerservice.api.dto.SkuFocusSale;
import com.rome.stock.innerservice.api.dto.SkuFocusSaleDTO;
import com.rome.stock.innerservice.common.AlikAssert;
import com.rome.stock.innerservice.common.ResCode;
import com.rome.stock.innerservice.constant.RealWarehouseTypeVO;
import com.rome.stock.innerservice.domain.convertor.RealWarehouseConvertor;
import com.rome.stock.innerservice.domain.convertor.SkuFocusSaleConvertor;
import com.rome.stock.innerservice.domain.entity.RealWarehouseE;
import com.rome.stock.innerservice.domain.entity.SkuFocusSaleE;
import com.rome.stock.innerservice.domain.repository.RealWarehouseRepository;
import com.rome.stock.innerservice.domain.repository.SkuFocusSaleRepository;
import com.rome.stock.innerservice.domain.service.SkuFocusSaleService;
import com.rome.stock.innerservice.facade.StockOnlineOrderFacade;
import com.rome.stock.innerservice.remote.item.dto.SkuInfoExtDTO;
import com.rome.stock.innerservice.remote.item.facade.SkuFacade;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.ListUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Slf4j
@Service
public class SkuFocusSaleServiceImpl implements SkuFocusSaleService {
    @Resource
    private SkuFocusSaleConvertor skuFocusSaleConvertor;
    @Resource
    private SkuFocusSaleRepository skuFocusSaleRepository;
    @Resource
    private SkuFacade skuFacade;
    @Resource
    private RealWarehouseRepository realWarehouseRepository;
    @Resource
    private RealWarehouseConvertor realWarehouseConvertor;

    /**
     * 查询SKU寻源仓库
     *
     * @param skuFocusSale
     * @return
     */
    @Override
    public PageInfo<SkuFocusSale> getSkuFocusSale(SkuFocusSale skuFocusSale) {
        Page page = PageHelper.startPage(skuFocusSale.getPageIndex(), skuFocusSale.getPageSize());
        List<SkuFocusSaleE> skuFocusSaleList = skuFocusSaleRepository.getSkuFocusSale(skuFocusSale);
        if (skuFocusSaleList == null || skuFocusSaleList.size() == 0) {
            return new PageInfo();
        }
        //获取实仓id，查询实仓信息
        List<Long> realWarehouseIds = skuFocusSaleList.stream().map(SkuFocusSaleE::getRealWarehouseId).distinct().collect(Collectors.toList());
        List<RealWarehouseE> realWarehouseE = realWarehouseRepository.queryWarehouseByIds(realWarehouseIds);
        Map<Long, RealWarehouseE> realWarehouseMap = realWarehouseE.stream().collect(Collectors.toMap(RealWarehouseE::getId, item -> item));
//根据skuIds，查询sku信息
        List<Long> skuIds = skuFocusSaleList.stream().map(SkuFocusSaleE::getSkuId).distinct().collect(Collectors.toList());
        List<SkuInfoExtDTO> skuInfoExtDTOs = skuFacade.skusBySkuId(skuIds);
        Map<Long, SkuInfoExtDTO> skuInfoExtDTOMap = skuInfoExtDTOs.stream().collect(Collectors.toMap(SkuInfoExtDTO::getId, item -> item));
        for (SkuFocusSaleE item : skuFocusSaleList) {
            if (realWarehouseMap.containsKey(item.getRealWarehouseId())) {
                item.setRealWarehouseName(realWarehouseMap.get(item.getRealWarehouseId()).getRealWarehouseName());
                item.setRealWarehouseCode(realWarehouseMap.get(item.getRealWarehouseId()).getRealWarehouseCode());
            }
            if (skuInfoExtDTOMap.containsKey(item.getSkuId())) {
                item.setSkuName(skuInfoExtDTOMap.get(item.getSkuId()).getName());
                item.setSkuCode(skuInfoExtDTOMap.get(item.getSkuId()).getSkuCode());
            }
        }
        PageInfo<SkuFocusSale> pageList = new PageInfo(skuFocusSaleConvertor.entityToDto(skuFocusSaleList));
        pageList.setTotal(page.getTotal());
        return pageList;
    }

    /**
     * 新增SKU寻源仓库
     *
     * @param skuFocusSale
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void addSkuFocusSale(SkuFocusSale skuFocusSale) {
        //判断是否是含有同样的商品
        int skuIdLength = skuFocusSale.getSkuIds().size();
        List<Long> skuIds = skuFocusSale.getSkuIds().stream().distinct().collect(Collectors.toList());
        int distinctSkuIdLength = skuIds.size();
        if (skuIdLength != distinctSkuIdLength) {
            throw new RomeException(ResCode.STOCK_ERROR_9004, ResCode.STOCK_ERROR_9004_DESC);
        }
        //判断是否在数据库中已存在当前记录
        List<SkuFocusSaleE> skuFocusSaleEs = skuFocusSaleRepository.selectSkuFocusSale(skuFocusSale.getRealWarehouseId(), skuFocusSale.getSkuIds());
        if (skuFocusSaleEs != null && skuFocusSaleEs.size() > 0) {
            throw new RomeException(ResCode.STOCK_ERROR_9005, ResCode.STOCK_ERROR_9005_DESC);
        }
        //添加Sku编码信息
        List<SkuInfoExtDTO> skuInfoExtDTOs = skuFacade.skusBySkuId(skuIds);
        skuFocusSaleRepository.addSkuFocusSale(skuFocusSale.getRealWarehouseId(), skuInfoExtDTOs, skuFocusSale.getCreator());
        StockOnlineOrderFacade.delRouteInfoByCoverFocusSale(skuFocusSale.getRealWarehouseId());
    }

    /**
     * 根据ids删除SKU寻源仓库
     *
     * @param skuFocusSales
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void delSkuFocusSaleByIds(List<SkuFocusSale> skuFocusSales) {
        if (skuFocusSales != null && skuFocusSales.size() > 0) {
            skuFocusSaleRepository.delSkuFocusSaleByIds(skuFocusSales.stream().map(SkuFocusSale::getId).collect(Collectors.toList()), skuFocusSales.get(0).getCreator());
            List<Long> realWarehouseIds = skuFocusSales.stream().map(SkuFocusSale::getRealWarehouseId).distinct().collect(Collectors.toList());
            for (Long item : realWarehouseIds) {
                StockOnlineOrderFacade.delRouteInfoByCoverFocusSale(item);
            }
        }
    }

    /**
     * 根据isAvailable改变状态
     *
     * @param isAvailable
     * @param userId
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void changeIsAvailable(Integer isAvailable, Long id, Long realWarehouseId, Long userId) {
        skuFocusSaleRepository.changeIsAvailable(isAvailable, id, userId);
        StockOnlineOrderFacade.delRouteInfoByCoverFocusSale(realWarehouseId);
    }

    /**
     * 查询所有电商仓
     *
     * @return
     */
    @Override
    public List<RealWarehouse> getAllECommerceWarehouse() {
        List<Integer> realWarehouseTypes = new LinkedList<>();
        realWarehouseTypes.add(RealWarehouseTypeVO.RW_TYPE_17.getType());
        realWarehouseTypes.add(RealWarehouseTypeVO.RW_TYPE_18.getType());
        realWarehouseTypes.add(RealWarehouseTypeVO.RW_TYPE_19.getType());
        realWarehouseTypes.add(RealWarehouseTypeVO.CROSS_TYPE_27.getType());
        realWarehouseTypes.add(RealWarehouseTypeVO.CROSS_TYPE_28.getType());
        List<RealWarehouseE> realWarehouseEs = skuFocusSaleRepository.getAllECommerceWarehouse(realWarehouseTypes);
        return realWarehouseConvertor.entityToDto(realWarehouseEs);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public String importExcel(List<SkuFocusSaleDTO> skuFocusSaleDTOS) {
        StringBuilder sb=new StringBuilder();
        if(CollUtil.isNotEmpty(skuFocusSaleDTOS)){
            //根据仓库编码分组
            Map<String, List<SkuFocusSaleDTO>> skuFocusSaleMap = skuFocusSaleDTOS.stream().distinct().collect(Collectors.groupingBy(SkuFocusSaleDTO::getRealWarehouseCode));
            List<String> realWarehouseCodeList = skuFocusSaleDTOS.stream().map(x -> x.getRealWarehouseCode()).distinct().collect(Collectors.toList());
            //批量查询实仓信息
            List<RealWarehouseE> realWarehouses = realWarehouseRepository.getRealWarehousesByCode(realWarehouseCodeList);
            if(CollUtil.isEmpty(realWarehouses)){
                throw new RomeException("999","未查询到实体仓库");
            }
            //删除昨日数据
            deleteSkuFocusSaleByCreateTime();
            Map<String, RealWarehouseE> realWarehouseMap = realWarehouses.stream().collect(Collectors.toMap(RealWarehouseE::getRealWarehouseCode, Function.identity(), (v1, v2) -> v2));
            skuFocusSaleMap.forEach((k,v)->{
                RealWarehouseE realWarehouseE = this.buildRealWarehouseInfo(realWarehouseMap, k, v,sb);
                if(Objects.nonNull(realWarehouseE)){
                    this.buildSkuInfo(v,sb);
                    this.deleteExistSkuInfo(v, realWarehouseE);
                    List<SkuInfoExtDTO> skuInfoExtDTOs = this.buildSkuInfoExtInfo(v);
                    if(CollUtil.isNotEmpty(skuInfoExtDTOs)){
                        skuFocusSaleRepository.addSkuFocusSale(realWarehouseE.getId(), skuInfoExtDTOs, v.get(0).getCreator());
                        StockOnlineOrderFacade.delRouteInfoByCoverFocusSale(realWarehouseE.getId());
                    }
                }
            });
        }
        return sb.toString();
    }

    /**
     * 删除之前已经存在的记录
     * @param v
     * @param realWarehouseE
     */
    private void deleteExistSkuInfo(List<SkuFocusSaleDTO> v, RealWarehouseE realWarehouseE) {
        List<Long> skuIds = v.stream().map(x -> x.getSkuId()).distinct().collect(Collectors.toList());
        //校验是否之前存在
        List<SkuFocusSaleE> skuFocusSaleEs = skuFocusSaleRepository.selectSkuFocusSale(realWarehouseE.getId(), skuIds);
        if (CollUtil.isNotEmpty(skuFocusSaleEs)) {
            Map<Long, SkuFocusSaleE> skuFocusSaleExistMap = skuFocusSaleEs.stream().collect(Collectors.toMap(SkuFocusSaleE::getSkuId, Function.identity(), (v1, v2) -> v2));
            Iterator<SkuFocusSaleDTO> iterator = v.iterator();
            while (iterator.hasNext()){
                SkuFocusSaleDTO next = iterator.next();
                SkuFocusSaleE skuFocusSaleE = skuFocusSaleExistMap.get(next.getSkuId());
                if(Objects.nonNull(skuFocusSaleE)){
                    //删除已经存在的记录
                    skuFocusSaleRepository.deleteSkuFocusBySkuInfo(next.getSkuId(),realWarehouseE.getId());
                }
            }
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deleteSkuFocusSaleByCreateTime() {
        String createTime = DateUtil.beginOfDay(DateTime.now()).toString("yyyy-MM-dd HH:mm:ss");
        List<Long> realWarehouseIds = skuFocusSaleRepository.queryRealWarehouseList(createTime);
        if(CollUtil.isNotEmpty(realWarehouseIds)){
            //删除昨日的缓存信息
            realWarehouseIds.forEach(x-> StockOnlineOrderFacade.delRouteInfoByCoverFocusSale(x));
        }
        //删除今日0点之前的数据
        skuFocusSaleRepository.deleteSkuFocusByTime(createTime);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateIsAvailable(List<SkuFocusSale> list) {
        skuFocusSaleRepository.updateIsAvailable(list);
    }

    /**
     * 组装sku信息
     * @param v
     * @return
     */
    private List<SkuInfoExtDTO> buildSkuInfoExtInfo(List<SkuFocusSaleDTO> v) {
        return v.stream().map(x -> {
                        SkuInfoExtDTO skuInfoExtDTO = new SkuInfoExtDTO();
                        skuInfoExtDTO.setSkuCode(x.getSkuCode());
                        skuInfoExtDTO.setId(x.getSkuId());
                        return skuInfoExtDTO;
                    }).collect(Collectors.toList());
    }

    /**
     * 构建sku信息
     * @param v
     * @param sb
     */
    private void buildSkuInfo(List<SkuFocusSaleDTO> v,StringBuilder sb) {
        List<String> skuCodes = v.stream().map(x -> x.getSkuCode()).distinct().collect(Collectors.toList());
        List<SkuInfoExtDTO> skuInfoExtDTOS = skuFacade.skusBySkuCode(skuCodes);
        AlikAssert.isNotNull(skuInfoExtDTOS, "999","商品编码有误");
        Map<String, SkuInfoExtDTO> skuInfoMap = skuInfoExtDTOS.stream().collect(Collectors.toMap(SkuInfoExtDTO::getSkuCode, Function.identity(), (v1, v2) -> v2));
        v.forEach(z->{
            SkuInfoExtDTO skuInfoExtDTO = skuInfoMap.get(z.getSkuCode());
            if(Objects.isNull(skuInfoExtDTO)){
                sb.append(String.format("商品编码为%1$s的商品信息不存在%2$s",z.getSkuCode()));
                log.warn("商品编码为{}的商品信息不存在",z.getSkuCode());
                return;
            }
            z.setSkuId(skuInfoExtDTO.getId());
        });
    }

    /**
     * 组装实体仓库id信息
     * @param realWarehouseMap
     * @param k
     * @param v
     * @param sb
     * @return
     */
    private RealWarehouseE buildRealWarehouseInfo(Map<String, RealWarehouseE> realWarehouseMap, String k, List<SkuFocusSaleDTO> v,StringBuilder sb) {
        RealWarehouseE realWarehouseE = realWarehouseMap.get(k);
        if(Objects.isNull(realWarehouseE)){
            log.error("仓库编码为{}的实仓不存在",k);
            sb.append(String.format("仓库编码为%1$s的仓库不存在%2$s",k,","));
            return null;
        }
        v.forEach(x->x.setRealWarehouseId(realWarehouseE.getId()));
        return realWarehouseE;
    }
}
