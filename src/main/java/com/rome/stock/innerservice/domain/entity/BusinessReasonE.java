package com.rome.stock.innerservice.domain.entity;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Component;

/**
 * @Description
 * <AUTHOR>
 * @Date 2019/5/13 15:35
 * @Version 1.0
 */
@Data
@Slf4j
@Component
@Scope("prototype")
@EqualsAndHashCode(callSuper = false)
public class BusinessReasonE extends BaseE{

    //唯一主键
    private Long id;

    //原因编号
    private String reasonCode;

    //原因名称
    private String reasonName;

    //单据类型
    private Integer recordType;
}
