package com.rome.stock.innerservice.domain.entity.warehouserecord;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.rome.stock.common.utils.DateUtil;
import com.rome.stock.innerservice.api.dto.warehouserecord.RecordDetailDTO;
import com.rome.stock.innerservice.common.AlikAssert;
import com.rome.stock.innerservice.common.ResCode;
import com.rome.stock.innerservice.common.SkuInfoTools;
import com.rome.stock.innerservice.common.StringUtils;
import com.rome.stock.innerservice.constant.WmsSyncStatusVO;
import com.rome.stock.innerservice.domain.entity.BaseE;
import com.rome.stock.innerservice.domain.entity.frontrecord.AbstractFrontRecord;
import com.rome.stock.innerservice.domain.entity.frontrecord.FrontRecordE;
import com.rome.stock.innerservice.domain.repository.warehouserecord.WarehouseRecordRepository;
import com.rome.stock.innerservice.domain.service.OrderUtilService;
import com.rome.stock.core.infrastructure.dataobject.core.CoreRealStockOpDO;
import com.rome.stock.core.infrastructure.dataobject.core.CoreRealStockOpDetailDO;
import com.rome.stock.core.infrastructure.dataobject.core.CoreRealWarehouseStockDO;
import com.rome.stock.core.infrastructure.dataobject.core.CoreVirtualWarehouseStockDO;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;

/**
 * 类WarehouseRecord的实现描述：通用出入库单
 *
 * <AUTHOR> 2019/4/17 20:52
 */
@Data
@Slf4j
@Component
@Scope("prototype")
@EqualsAndHashCode(callSuper = false)
public abstract class AbstractWarehouseRecord extends BaseE{
	@JsonIgnore
	@Autowired
	private WarehouseRecordRepository warehouseRecordRepository;
	@JsonIgnore
	@Resource
	private SkuInfoTools skuInfoTools;
	@JsonIgnore
	@Resource
	private OrderUtilService orderUtilService;
	/**
	 * 生成单号
	 */
	public void createRecodeCode(String warehouseCode){
		String code= orderUtilService.queryOrderCode(warehouseCode);
		this.setRecordCode(code);
	}

	/**
	 * 设置skuCode或者skuID
	 */
	public void initWarehouseRecodeDetail(){
		//设置商品code或id
		skuInfoTools.convertSkuCode(this.warehouseRecordDetails);
	}

	/**
	 * 设置skuCode或者skuID
	 */
	public void initWarehouseRecodeDetail(Long merchantId){
		//设置商品code或id
		skuInfoTools.convertSkuCode(this.warehouseRecordDetails ,merchantId);
	}

	/**
	 * 更新完成状态
	 */
	public void updateCompleteStatus(){
		warehouseRecordRepository.updateCompleteStatus(this.getId(),this.getRealWarehouseId(),this.getVirtualWarehouseId());
	}

	/**
	 * 前置单与出入库单一对一关系保存
	 */
	public void addFrontRecordAndWarehouseRelation(){
		warehouseRecordRepository.saveAddFrontRecordAndWarehouseRelation(this);
	}

    /**
     * 前置单与出入库单一对多关系保存
     */
    public void addFrontRecordAndWarehouseRelationForList(){
        warehouseRecordRepository.saveAddFrontRecordAndWarehouseRelationForList(this);
    }

	public List<Long> queryWarehouseIdByFrontId(Long frontRecordId ,Integer recordType) {
		return warehouseRecordRepository.queryWarehouseIdByFrontId(frontRecordId ,recordType);
	}

	public Integer queryWarehouseRecordSyncWmsStatusById(){
		return warehouseRecordRepository.getWarehouseRecordSyncWmsStatusById(getId());
	}

	/**
	 * 出库单状态更新为已出库
	 * */
	public void updateOutAllocation(){
		warehouseRecordRepository.updateRecordStatusToOutAllocation(this.getId());
	}

	public int updateOutAllocationInfo(String orderConfirmTime) {
		if(StringUtils.isNotBlank(orderConfirmTime)) {
			orderConfirmTime = orderConfirmTime.replace("　", " ");
		}
		return warehouseRecordRepository.updateRecordToOutAllocation(this.getId(), orderConfirmTime);
	}

	public Integer updateInAllocationInfo(String orderConfirmTime) {
		if(StringUtils.isNotBlank(orderConfirmTime)) {
			orderConfirmTime = orderConfirmTime.replace("　", " ");
		}
		return warehouseRecordRepository.updateRecordToInAllocation(this.getId(), orderConfirmTime);
	}

	public static void main(String[] args) {
		String a = "2019-09-17 12:31:34";
		a = a.replace("　", " ");
		System.out.println(a);
		System.out.println(DateUtil.parseDateTime(a));
	}

	/**
	 * 入库单状态更新为已入库
	 * */
	public void updateInAllocation(){
		warehouseRecordRepository.updateRecordStatusToInAllocation(this.getId());
	}

	/**
	 * 合并相同的sku信息
	 */
	public void mergeSameSkuDetails() {
		List<WarehouseRecordDetail> warehouseRecordDetails = this.getWarehouseRecordDetails();
		if (CollectionUtils.isEmpty(warehouseRecordDetails)) {
			return;
		}
		Map<String, WarehouseRecordDetail> detailMap = new HashMap<>(warehouseRecordDetails.size() * 100 / 75 + 1);
		for (WarehouseRecordDetail detailE : warehouseRecordDetails) {
			WarehouseRecordDetail warehouseRecordDetail = detailMap.get(detailE.getSkuCode());
			if (warehouseRecordDetail == null) {
				detailMap.put(detailE.getSkuCode(), detailE);
			} else {
				//相同批次的数量做合并
				warehouseRecordDetail.setPlanQty(warehouseRecordDetail.getPlanQty().add(detailE.getPlanQty()));
				warehouseRecordDetail.setActualQty(warehouseRecordDetail.getActualQty().add(detailE.getActualQty()));
			}
		}
		//增加合并之后的数据
		this.setWarehouseRecordDetails(new ArrayList<>(detailMap.values()));
	}

	/**
	 * 减少实体仓库库存的对象(按照planQty减少,特殊需求在serviceImpl中自行封装)
	 */
	public CoreRealStockOpDO initDecreaseStockObj(Long rwId, boolean checkBeforeOp){
		List<CoreRealStockOpDetailDO> decreaseDetails = new ArrayList<>();
		for (WarehouseRecordDetail detail: this.getWarehouseRecordDetails()) {
			//数量为0的不处理库存的增加和减少
			if(BigDecimal.ZERO.compareTo(detail.getPlanQty()) == 0){
				continue;
			}
			CoreRealStockOpDetailDO coreRealStockOpDetailDO = new CoreRealStockOpDetailDO();
			coreRealStockOpDetailDO.setSkuId(detail.getSkuId());
			coreRealStockOpDetailDO.setSkuCode(detail.getSkuCode());
			coreRealStockOpDetailDO.setRealQty(detail.getPlanQty());
			coreRealStockOpDetailDO.setRealWarehouseId(rwId);
			coreRealStockOpDetailDO.setCheckBeforeOp(checkBeforeOp);
			decreaseDetails.add(coreRealStockOpDetailDO);
		}
		CoreRealStockOpDO coreRealStockOpDO = new CoreRealStockOpDO();
		coreRealStockOpDO.setRecordCode(this.getRecordCode());
		coreRealStockOpDO.setTransType(this.getRecordType());
		coreRealStockOpDO.setDetailDos(decreaseDetails);
		return coreRealStockOpDO;
	}

	/**
	 * 减少实体仓库库存的对象(按照planQty减少,特殊需求在serviceImpl中自行封装)
	 */
	public CoreRealStockOpDO initDecreaseStockObj(Long rwId, boolean checkBeforeOp ,Long merchantId){
		List<CoreRealStockOpDetailDO> decreaseDetails = new ArrayList<>();
		for (WarehouseRecordDetail detail: this.getWarehouseRecordDetails()) {
			//数量为0的不处理库存的增加和减少
			if(BigDecimal.ZERO.compareTo(detail.getPlanQty()) == 0){
				continue;
			}
			CoreRealStockOpDetailDO coreRealStockOpDetailDO = new CoreRealStockOpDetailDO();
			coreRealStockOpDetailDO.setSkuId(detail.getSkuId());
			coreRealStockOpDetailDO.setSkuCode(detail.getSkuCode());
			coreRealStockOpDetailDO.setRealQty(detail.getPlanQty());
			coreRealStockOpDetailDO.setRealWarehouseId(rwId);
			coreRealStockOpDetailDO.setMerchantId(merchantId);
			coreRealStockOpDetailDO.setCheckBeforeOp(checkBeforeOp);
			decreaseDetails.add(coreRealStockOpDetailDO);
		}
		CoreRealStockOpDO coreRealStockOpDO = new CoreRealStockOpDO();
		coreRealStockOpDO.setRecordCode(this.getRecordCode());
		coreRealStockOpDO.setTransType(this.getRecordType());
		coreRealStockOpDO.setDetailDos(decreaseDetails);
		return coreRealStockOpDO;
	}

    /**
     * 根据出库单信息解锁库存并出库信息封装
     * @param warehouseRecordE
     * @return
     */
    public CoreRealStockOpDO packOutAndUnLockOpDO(WarehouseRecordE warehouseRecordE) {
        List<CoreRealStockOpDetailDO> increaseDetails = new ArrayList<>();
        for (WarehouseRecordDetail detail : warehouseRecordE.getWarehouseRecordDetails()) {
            CoreRealStockOpDetailDO detailDO = new CoreRealStockOpDetailDO();
            detailDO.setUnlockQty(detail.getPlanQty());
            detailDO.setRealQty(detail.getActualQty());
            detailDO.setSkuId(detail.getSkuId());
            detailDO.setRealWarehouseId(warehouseRecordE.getRealWarehouseId());
            detailDO.setChannelCode(warehouseRecordE.getChannelCode());
            detailDO.setCheckBeforeOp(false);
            detailDO.setSkuCode(detail.getSkuCode());
            increaseDetails.add(detailDO);
        }
        CoreRealStockOpDO coreRealStockOpDO = new CoreRealStockOpDO();
        coreRealStockOpDO.setRecordCode(warehouseRecordE.getRecordCode());
        coreRealStockOpDO.setTransType(warehouseRecordE.getRecordType());
        coreRealStockOpDO.setDetailDos(increaseDetails);
        return coreRealStockOpDO;
    }
	/**
	 * 增加实体仓库库存的对象(按照planQty增加,特殊需求在serviceImpl中自行封装)
	 */
	public CoreRealStockOpDO initIncreaseStockObj(Long rwId){
		List<CoreRealStockOpDetailDO> increaseDetails = new ArrayList<>();
		for (WarehouseRecordDetail detail: this.getWarehouseRecordDetails()) {
			//数量为0的不处理库存的增加和减少
			if(BigDecimal.ZERO.compareTo(detail.getPlanQty()) == 0){
				continue;
			}
			CoreRealStockOpDetailDO coreRealStockOpDetailDO = new CoreRealStockOpDetailDO();
			coreRealStockOpDetailDO.setSkuId(detail.getSkuId());
			coreRealStockOpDetailDO.setSkuCode(detail.getSkuCode());
			coreRealStockOpDetailDO.setRealQty(detail.getPlanQty());
			coreRealStockOpDetailDO.setRealWarehouseId(rwId);
			increaseDetails.add(coreRealStockOpDetailDO);
		}
		CoreRealStockOpDO coreRealStockOpDO = new CoreRealStockOpDO();
		coreRealStockOpDO.setRecordCode(this.getRecordCode());
		coreRealStockOpDO.setTransType(this.getRecordType());
		coreRealStockOpDO.setDetailDos(increaseDetails);
		return coreRealStockOpDO;
	}

	/**
	 * 增加实体仓库库存的对象(按照planQty增加,特殊需求在serviceImpl中自行封装)
	 */
	public CoreRealStockOpDO initIncreaseStockObj(Long rwId ,Long merchantId){
		List<CoreRealStockOpDetailDO> increaseDetails = new ArrayList<>();
		for (WarehouseRecordDetail detail: this.getWarehouseRecordDetails()) {
			//数量为0的不处理库存的增加和减少
			if(BigDecimal.ZERO.compareTo(detail.getPlanQty()) == 0){
				continue;
			}
			CoreRealStockOpDetailDO coreRealStockOpDetailDO = new CoreRealStockOpDetailDO();
			coreRealStockOpDetailDO.setSkuId(detail.getSkuId());
			coreRealStockOpDetailDO.setSkuCode(detail.getSkuCode());
			coreRealStockOpDetailDO.setRealQty(detail.getPlanQty());
			coreRealStockOpDetailDO.setRealWarehouseId(rwId);
			coreRealStockOpDetailDO.setMerchantId(merchantId);
			increaseDetails.add(coreRealStockOpDetailDO);
		}
		CoreRealStockOpDO coreRealStockOpDO = new CoreRealStockOpDO();
		coreRealStockOpDO.setRecordCode(this.getRecordCode());
		coreRealStockOpDO.setTransType(this.getRecordType());
		coreRealStockOpDO.setDetailDos(increaseDetails);
		return coreRealStockOpDO;
	}


	/**
	 * 锁定实体仓库库存对象(按照planQty锁定,特殊需求在serviceImpl中自行封装)
	 */
	public CoreRealStockOpDO initLockStockObj(Long rwId){
		List<CoreRealStockOpDetailDO> increaseDetails = new ArrayList<>();
		for (WarehouseRecordDetail detail: this.getWarehouseRecordDetails()) {
			//数量为0的不处理库存的增加和减少
			if(BigDecimal.ZERO.compareTo(detail.getPlanQty()) == 0){
				continue;
			}
			CoreRealStockOpDetailDO coreRealStockOpDetailDO = new CoreRealStockOpDetailDO();
			coreRealStockOpDetailDO.setSkuId(detail.getSkuId());
			coreRealStockOpDetailDO.setSkuCode(detail.getSkuCode());
			coreRealStockOpDetailDO.setLockQty(detail.getPlanQty());
			coreRealStockOpDetailDO.setRealWarehouseId(rwId);
			increaseDetails.add(coreRealStockOpDetailDO);
		}
		CoreRealStockOpDO coreRealStockOpDO = new CoreRealStockOpDO();
		coreRealStockOpDO.setRecordCode(this.getRecordCode());
		coreRealStockOpDO.setTransType(this.getRecordType());
		coreRealStockOpDO.setDetailDos(increaseDetails);
		return coreRealStockOpDO;
	}

	/**
	 * 初始化出库库存对象
	 */
	public CoreRealStockOpDO initOutWarehouseStockObj(Long rwId){
		List<CoreRealStockOpDetailDO> increaseDetails = new ArrayList<>();
		for (WarehouseRecordDetail detail: this.getWarehouseRecordDetails()) {
			//数量为0的不处理库存的增加和减少
			if(BigDecimal.ZERO.compareTo(detail.getPlanQty()) == 0){
				continue;
			}
			CoreRealStockOpDetailDO coreRealStockOpDetailDO = new CoreRealStockOpDetailDO();
			coreRealStockOpDetailDO.setSkuId(detail.getSkuId());
			coreRealStockOpDetailDO.setSkuCode(detail.getSkuCode());
			coreRealStockOpDetailDO.setRealQty(detail.getPlanQty());
			coreRealStockOpDetailDO.setCheckBeforeOp(false);
			coreRealStockOpDetailDO.setRealWarehouseId(rwId);
			increaseDetails.add(coreRealStockOpDetailDO);
		}
		CoreRealStockOpDO coreRealStockOpDO = new CoreRealStockOpDO();
		coreRealStockOpDO.setRecordCode(this.getRecordCode());
		coreRealStockOpDO.setTransType(this.getRecordType());
		coreRealStockOpDO.setDetailDos(increaseDetails);
		return coreRealStockOpDO;
	}



	/**
	 * 锁定实体仓库库存对象(按照planQty锁定,特殊需求在serviceImpl中自行封装)
	 */
	public CoreRealStockOpDO initLockStockObj(Long rwId,boolean checkOp){
		List<CoreRealStockOpDetailDO> increaseDetails = new ArrayList<>();
		for (WarehouseRecordDetail detail: this.getWarehouseRecordDetails()) {
			//数量为0的不处理库存的增加和减少
			if(BigDecimal.ZERO.compareTo(detail.getPlanQty()) == 0){
				continue;
			}
			CoreRealStockOpDetailDO coreRealStockOpDetailDO = new CoreRealStockOpDetailDO();
			coreRealStockOpDetailDO.setSkuId(detail.getSkuId());
			coreRealStockOpDetailDO.setSkuCode(detail.getSkuCode());
			coreRealStockOpDetailDO.setLockQty(detail.getPlanQty());
			coreRealStockOpDetailDO.setRealWarehouseId(rwId);
			coreRealStockOpDetailDO.setCheckBeforeOp(checkOp);
			increaseDetails.add(coreRealStockOpDetailDO);
		}
		CoreRealStockOpDO coreRealStockOpDO = new CoreRealStockOpDO();
		coreRealStockOpDO.setRecordCode(this.getRecordCode());
		coreRealStockOpDO.setTransType(this.getRecordType());
		coreRealStockOpDO.setDetailDos(increaseDetails);
		return coreRealStockOpDO;
	}


	/**
	 * 锁定实体仓库库存对象(按照planQty锁定,特殊需求在serviceImpl中自行封装)
	 */
	public CoreRealStockOpDO initLockStockObjByParams(WarehouseRecordE warehouseRecordE, List<RecordDetailDTO> allLockDetail){
		List<CoreRealStockOpDetailDO> increaseDetails = new ArrayList<>();
		for (RecordDetailDTO detail: allLockDetail) {
			//数量为0的不处理库存的增加和减少
			if(BigDecimal.ZERO.compareTo(detail.getBasicSkuQty()) == 0){
				continue;
			}
			CoreRealStockOpDetailDO coreRealStockOpDetailDO = new CoreRealStockOpDetailDO();
			coreRealStockOpDetailDO.setSkuId(detail.getSkuId());
			coreRealStockOpDetailDO.setSkuCode(detail.getSkuCode());
			coreRealStockOpDetailDO.setLockQty(detail.getBasicSkuQty());
			coreRealStockOpDetailDO.setRealWarehouseId(warehouseRecordE.getRealWarehouseId());
			increaseDetails.add(coreRealStockOpDetailDO);
		}
		CoreRealStockOpDO coreRealStockOpDO = new CoreRealStockOpDO();
		coreRealStockOpDO.setRecordCode(warehouseRecordE.getRecordCode());
		coreRealStockOpDO.setTransType(warehouseRecordE.getRecordType());
		coreRealStockOpDO.setDetailDos(increaseDetails);
		return coreRealStockOpDO;
	}


	/**
	 * 按照ActualQty初始化在途对象(按照planQty锁定,特殊需求在serviceImpl中自行封装)
	 */
	public CoreRealStockOpDO initOnRoadStockObj(Long rwId){
		List<CoreRealStockOpDetailDO> increaseDetails = new ArrayList<>();
		for (WarehouseRecordDetail detail: this.getWarehouseRecordDetails()) {
			//数量为0的不处理库存的增加和减少
			if (detail.getPlanQty().compareTo(BigDecimal.ZERO) > 0) {
				CoreRealStockOpDetailDO coreRealStockOpDetailDO = new CoreRealStockOpDetailDO();
				coreRealStockOpDetailDO.setSkuId(detail.getSkuId());
				coreRealStockOpDetailDO.setSkuCode(detail.getSkuCode());
				coreRealStockOpDetailDO.setOnroadQty(detail.getPlanQty());
				coreRealStockOpDetailDO.setRealWarehouseId(rwId);
				increaseDetails.add(coreRealStockOpDetailDO);
			}
		}
		CoreRealStockOpDO coreRealStockOpDO = new CoreRealStockOpDO();
		coreRealStockOpDO.setRecordCode(this.getRecordCode());
		coreRealStockOpDO.setTransType(this.getRecordType());
		coreRealStockOpDO.setDetailDos(increaseDetails);
		return coreRealStockOpDO;
	}

	/**
	 * 按照ActualQty初始化在途对象(按照planQty锁定,特殊需求在serviceImpl中自行封装)
	 */
	public CoreRealStockOpDO initOnRoadStockObjByActualQty(Long rwId){
		List<CoreRealStockOpDetailDO> detailDos = new ArrayList<>();
		for (WarehouseRecordDetail detail : this.getWarehouseRecordDetails()) {
			//实收数量为0的不处理在途的增加和减少
			if(BigDecimal.ZERO.compareTo(detail.getActualQty()) == 0){
				continue;
			}
			CoreRealStockOpDetailDO detailDO = new CoreRealStockOpDetailDO();
			detailDO.setOnroadQty(detail.getActualQty());
			detailDO.setSkuId(detail.getSkuId());
			detailDO.setSkuCode(detail.getSkuCode());
			detailDO.setRealWarehouseId(rwId);
			detailDO.setChannelCode(detail.getChannelCode());
			detailDos.add(detailDO);
		}
		CoreRealStockOpDO stockDO = new CoreRealStockOpDO();
		stockDO.setRecordCode(this.getRecordCode());
		stockDO.setTransType(this.getRecordType());
		stockDO.setDetailDos(detailDos);
		return stockDO;
	}

	/**
	 * 初始化出库库存对象(按照planQty解除锁定,按照实际库存出库,特殊需求在serviceImpl中自行封装)
	 */
	public CoreRealStockOpDO initOutWarehouseStockObj(Long rwId, boolean checkBeforeOp){
		List<CoreRealStockOpDetailDO> increaseDetails = new ArrayList<>();
		for (WarehouseRecordDetail detail: this.getWarehouseRecordDetails()) {
			CoreRealStockOpDetailDO coreRealStockOpDetailDO = new CoreRealStockOpDetailDO();
			coreRealStockOpDetailDO.setSkuId(detail.getSkuId());
			coreRealStockOpDetailDO.setSkuCode(detail.getSkuCode());
			coreRealStockOpDetailDO.setUnlockQty(detail.getPlanQty());
			coreRealStockOpDetailDO.setRealQty(detail.getActualQty());
			coreRealStockOpDetailDO.setRealWarehouseId(rwId);
			coreRealStockOpDetailDO.setCheckBeforeOp(checkBeforeOp);
			increaseDetails.add(coreRealStockOpDetailDO);
		}
		CoreRealStockOpDO coreRealStockOpDO = new CoreRealStockOpDO();
		coreRealStockOpDO.setRecordCode(this.getRecordCode());
		coreRealStockOpDO.setTransType(this.getRecordType());
		coreRealStockOpDO.setDetailDos(increaseDetails);
		return coreRealStockOpDO;
	}

	/**
	 * @Description: 初始化实仓库存查询对象 <br>
	 *
	 * <AUTHOR> 2019/9/3
	 * @param rwId
	 * @return
	 */
	public List<CoreRealWarehouseStockDO> initQueryRWStockObj(Long rwId) {
		List<CoreRealWarehouseStockDO> stockDOList = new ArrayList<>();
		for(WarehouseRecordDetail detail : this.warehouseRecordDetails) {
			CoreRealWarehouseStockDO stockDO = new CoreRealWarehouseStockDO();
			stockDO.setRealWarehouseId(rwId);
			stockDO.setSkuId(detail.getSkuId());
			stockDOList.add(stockDO);
		}
		return stockDOList;
	}

	/**
	 * @Description: 初始化虚仓库存查询对象 <br>
	 *
	 * <AUTHOR> 2019/9/3
	 * @return
	 */
	public List<CoreVirtualWarehouseStockDO> initQueryVWStockObj(Long vwId) {
		List<CoreVirtualWarehouseStockDO> stockDOList = new ArrayList<>();
		for(WarehouseRecordDetail detail : this.warehouseRecordDetails) {
			CoreVirtualWarehouseStockDO stockDO = new CoreVirtualWarehouseStockDO();
			stockDO.setVirtualWarehouseId(vwId);
			stockDO.setSkuId(detail.getSkuId());
			stockDOList.add(stockDO);
		}
		return stockDOList;
	}

	/**
	 * @Description: 锁定实体仓库库存对象(按照planQty与实仓库存取小锁定,特殊需求在serviceImpl中自行封装) <br>
	 *
	 * <AUTHOR> 2019/9/3
	 * @param rwId
	 * @param rwStockMap
	 * @return
	 */
	public CoreRealStockOpDO initMinLockStockObj(Long rwId, Map<Long,BigDecimal> rwStockMap) {
		List<CoreRealStockOpDetailDO> increaseDetails = new ArrayList<>();
		AbstractFrontRecord abstractFrontRecord = this.getAbstractFrontRecord();
		for (WarehouseRecordDetail detail: this.getWarehouseRecordDetails()) {
			//数量为0的不处理库存的增加和减少
			if(BigDecimal.ZERO.compareTo(detail.getPlanQty()) == 0){
				continue;
			}
			BigDecimal lockQty = this.findMinQty(detail.getPlanQty(), rwStockMap.get(detail.getSkuId()));
			if(BigDecimal.ZERO.compareTo(lockQty) == 0) {
				log.info("直营门店补货单【{}】锁定库存为0 跳过", abstractFrontRecord.getOutRecordCode());
				continue;
			}
			CoreRealStockOpDetailDO coreRealStockOpDetailDO = new CoreRealStockOpDetailDO();
			coreRealStockOpDetailDO.setSkuId(detail.getSkuId());
			coreRealStockOpDetailDO.setSkuCode(detail.getSkuCode());
			coreRealStockOpDetailDO.setLockQty(lockQty);
			coreRealStockOpDetailDO.setRealWarehouseId(rwId);
			increaseDetails.add(coreRealStockOpDetailDO);
		}
		AlikAssert.isNotEmpty(increaseDetails, ResCode.STOCK_ERROR_1029, ResCode.STOCK_ERROR_1029_DESC + "单号：" + abstractFrontRecord.getOutRecordCode());

		CoreRealStockOpDO coreRealStockOpDO = new CoreRealStockOpDO();
		coreRealStockOpDO.setRecordCode(this.getRecordCode());
		coreRealStockOpDO.setTransType(this.getRecordType());
		coreRealStockOpDO.setDetailDos(increaseDetails);
		return coreRealStockOpDO;
	}

	/**
	 * @Description: 预期数量与实际数量取小 <br>
	 *
	 * <AUTHOR> 2019/9/3
	 * @param planQty
	 * @param realQty
	 * @return
	 */
	private BigDecimal findMinQty(BigDecimal planQty, BigDecimal realQty) {
		realQty = realQty == null ? BigDecimal.ZERO : realQty;
		if(realQty.compareTo(BigDecimal.ZERO) <= 0) {
			return BigDecimal.ZERO;
		}

		if(planQty.compareTo(realQty) > 0) {
			return realQty;
		}
		return planQty;
	}

	/**
	 * 释放库存时，判断是否使用actual_qty
	 * @return
	 */
	public Boolean checkUnLockQty() {
		if (null != this.getTenantId() && 1L == this.getTenantId() && Objects.equals(this.getSyncWmsStatus(),WmsSyncStatusVO.NO_REQUIRED.getStatus())) {
			return true;
		} else {
			return false;
		}
	}

	/**
	 * 更新cmp状态完成
	 */
	public void updateCmpStatusComplete(){
		warehouseRecordRepository.updateCmpStatusComplete(this.getId());
	}

	/**
	 * 更新cmp状态完成
	 */
	public void updateCmpStatusFinish(){
		warehouseRecordRepository.updateCmpStatusFinish(this.getId());
	}

	/**
	 * 更新cmp7状态完成
	 */
	public void updateCmp7StatusComplete(){
		warehouseRecordRepository.updateCmp7StatusComplete(this.getId());
	}

	/**
	 * 更新cmp7状态完成（含初始状态）
	 */
	public void updateCmp7StatusFinish(){
		warehouseRecordRepository.updateCmp7StatusFinish(this.getId());
	}

	/**
	 * 所属单据编码
	 */
	private String recordCode;

	/**
	 * sap单号
	 */
	private String sapOrderCode;

	/**
	 * 直送流程用：门店入库单的外采单号
	 */
	private String purchaseSapOrderCode;

	/**
	 * 采购po单号
	 */
	private String purchaseOrderNo;

	/**
	 * 前置单sap单号
	 */
	private String frontSapOrderCode;

	/**
	 *  do单状态，0 未同步  1 已同步 10 拣货 11 打包 12 装车 13 发运 21 接单 22 配送 23 完成
	 */
	private Integer recordStatus;

	/**
	 * 业务类型：1.出库单 2.入库单
	 */
	private Integer businessType;

	/**
	 *  单据类型
	 */
	private Integer recordType;

	/**
	 *  虚拟仓库ID
	 */
	private Long virtualWarehouseId;

	/**
	 *  实仓ID
	 */
	private Long realWarehouseId;

	/**
	 * 渠道id
	 */
	private String channelCode;

	/**
	 * 渠道类型
	 */
	private Long channelType;

	/**
	 * 外部时间
	 */
	private Date outCreateTime;

	/**
	 * 出库时间
	 */
	private Date deliveryTime;

	/**
	 * 入库时间
	 */
	private Date receiverTime;

	/**
	 * 仓库单据详情
	 */
	List<WarehouseRecordDetail> warehouseRecordDetails;

	/**
	 * 前置单
	 */
	private AbstractFrontRecord abstractFrontRecord;

	private List<AbstractFrontRecord> abstractFrontRecordList = new ArrayList<>();

	/**
	 * 查询用前置单
	 */
	private FrontRecordE frontRecord;

	/**
	 * 单据同步wms状态
	 * 具体状态定义请查看
	 * @see WmsSyncStatusVO
	 * */
	private Integer syncWmsStatus;

	/**
	 *  0.无需同步 1.待同步 2.已同步
	 */
	private Integer cmpStatus;

	/**
	 * 同步wms失败时间
	 */
	private Date syncWmsFailTime;

	/**
	 * 门店批次同步状态
	 */
	private Integer batchStatus =3 ;

	private Date expectReceiveDateStart;

	private Date expectReceiveDateEnd;

	/**
	 * tms派车单号
	 */
	private String tmsRecordCode;


	/**
	 *  0.无需过账 1.待过账 2.已过账
	 */
	private Integer syncTransferStatus = 0;

	/**
	 *  传输订单中心 0-无需同步 1-待同步 2-已完成
	 */
	private Integer syncTradeStatus;

	/**
	 * 传输采购中心 0-无需同步 1-待同步 2-已完成
	 */
	private Integer syncPurchaseStatus;

	/**
	 * SAP派车状态：0-无需派车 1-待下发派车 2-已下发派车
	 */
	private Integer syncDispatchStatus;

	/**
	 * 出库或入库完成时间
	 */
	private Date outOrInTime;

	private String appId;

	/**
	 * 撤消原因，目前用来存储物流公司编码(logisticsCode)
	 */
	private String reasons;

	/**
	 * 租户ID
	 */
	private Long tenantId;
}
