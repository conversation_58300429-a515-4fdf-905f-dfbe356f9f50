package com.rome.stock.innerservice.domain.entity.frontrecord;

import java.util.Date;
import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Component;

import com.rome.stock.innerservice.common.RomeCollectionUtil;
import com.rome.stock.innerservice.constant.FrontRecordStatusVO;
import com.rome.stock.innerservice.constant.FrontRecordTypeVO;
import com.rome.stock.innerservice.domain.entity.SkuQtyUnitBaseE;
import com.rome.stock.innerservice.domain.repository.frontrecord.FrShopReverseRepository;
import com.rome.stock.innerservice.remote.item.dto.SkuInfoExtDTO;
import com.rome.stock.innerservice.remote.item.facade.SkuFacade;

import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 门店组装加工出库单
 */
@Component
@Scope("prototype")
@Data
@EqualsAndHashCode(callSuper = false)
public class ShopAssembleRecordE extends AbstractFrontRecord{
    @Autowired
    private FrShopReverseRepository frShopReverseRepository;

    @Autowired
    private SkuFacade skuFacade;

    /**
     * 创建门店加工反拆需求单
     */
    public void addTaskRecord(FrontRecordTypeVO frontRecordTypeVO) {
        this.setRecordStatus(FrontRecordStatusVO.COMPLETE.getStatus());
        //单据编号生成
        initFrontRecord(frontRecordTypeVO.getCode(), this.frontRecordDetails);
        this.setRecordType(frontRecordTypeVO.getType());
        long id = frShopReverseRepository.saveShopReverse(this);
        this.setId(id);
        this.frontRecordDetails.forEach(record -> record.setFrontRecordDetail(this) );
        for(ShopAssembleRecordDetailE detailE : this.frontRecordDetails){
            detailE.setFrontRecordId(id);
            detailE.setRecordCode(this.getRecordCode());
        }
        //设置成品skuId
        this.convertSkuCode(this.frontRecordDetails);
        //设置拆品skuId
        List<String> skuCodes = RomeCollectionUtil.getValueList(this.frontRecordDetails, "spitSkuCode");
        if(skuCodes != null && skuCodes.size() > 0 ){
            List<SkuInfoExtDTO> list = skuFacade.skusBySkuCode(skuCodes);
            for(SkuInfoExtDTO skuInfo : list){
                this.frontRecordDetails.stream().filter(sku -> sku.getSkuCode().equals(skuInfo.getSkuCode())).forEach(sku -> sku.setSpitSkuId(skuInfo.getId()));
            }
        }
        frShopReverseRepository.saveShopReverseDetail(this.frontRecordDetails);
    }

    /**
     * 门店ID
     */
    private String shopCode;

    /**
     * 门店名称
     */
    private String shopName;

    /**
     * 入向实体仓库id
     */
    private Long realWarehouseId;

    /**
     * 要求要货时间
     */
    private Date requDeliveryTime;
    /**
     * 计划交货时间
     */
    private Date planDeliveryTime;
    /**
     * 实际叫货时间
     */
    private Date realDeliveryTime;

    /**
     * 外部系统数据创建时间
     */
    private Date outCreateTime;

    /**
     * 商品数量
     */
    private List<ShopAssembleRecordDetailE> frontRecordDetails;
}
