package com.rome.stock.innerservice.domain.service.impl;

import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.rome.arch.core.domain.EntityFactory;
import com.rome.arch.core.exception.RomeException;
import com.rome.stock.common.enums.warehouse.WarehouseRecordTypeVO;
import com.rome.stock.core.constant.StockCoreConsts;
import com.rome.stock.innerservice.api.dto.RealWarehouseStockDTO;
import com.rome.stock.innerservice.api.dto.frontrecord.InventoryRecordDTO;
import com.rome.stock.innerservice.api.dto.frontrecord.ShopInventoryDetailDTO;
import com.rome.stock.innerservice.api.dto.frontrecord.ShopInventoryPageDTO;
import com.rome.stock.innerservice.api.dto.warehouserecord.InWarehouseRecordDTO;
import com.rome.stock.innerservice.api.dto.warehouserecord.OutWarehouseRecordDTO;
import com.rome.stock.innerservice.api.dto.warehouserecord.RecordDetailDTO;
import com.rome.stock.innerservice.common.*;
import com.rome.stock.innerservice.constant.FrontRecordStatusVO;
import com.rome.stock.innerservice.constant.*;
import com.rome.stock.innerservice.domain.convertor.RealWarehouseConvertor;
import com.rome.stock.innerservice.domain.convertor.frontrecord.ShopInventoryConvertor;
import com.rome.stock.innerservice.domain.entity.RealWarehouseE;
import com.rome.stock.innerservice.domain.entity.RealWarehouseStockE;
import com.rome.stock.innerservice.domain.entity.frontrecord.ShopInventoryRecordDetailE;
import com.rome.stock.innerservice.domain.entity.frontrecord.ShopInventoryRecordE;
import com.rome.stock.innerservice.domain.entity.warehouserecord.*;
import com.rome.stock.core.domain.repository.CoreRealWarehouseStockRepository;
import com.rome.stock.innerservice.domain.repository.RealWarehouseRepository;
import com.rome.stock.innerservice.domain.repository.frontrecord.FrShopInventoryRepository;
import com.rome.stock.innerservice.domain.repository.warehouserecord.ShopInventoryWarehouseRepository;
import com.rome.stock.innerservice.domain.repository.warehouserecord.WarehouseRecordRepository;
import com.rome.stock.innerservice.domain.service.ShopInventoryService;
import com.rome.stock.core.facade.RedisRollBackFacade;
import com.rome.stock.core.infrastructure.dataobject.core.CoreRealStockOpDO;
import com.rome.stock.core.infrastructure.dataobject.core.CoreRealStockOpDetailDO;
import com.rome.stock.core.infrastructure.dataobject.core.CoreRealWarehouseStockDO;
import com.rome.stock.innerservice.remote.base.dto.StoreDTO;
import com.rome.stock.innerservice.remote.base.facade.ShopFacade;
import com.rome.stock.innerservice.remote.item.dto.SkuInfoExtDTO;
import com.rome.stock.innerservice.remote.item.dto.SkuUnitExtDTO;
import com.rome.stock.innerservice.remote.item.facade.SkuFacade;
import com.rome.stock.innerservice.remote.sap.facade.SapProxyFacade;
import com.rome.stock.innerservice.remote.trade.dto.PosDaySummaryDTO;
import com.rome.stock.innerservice.remote.trade.facade.PosDaySummaryFacade;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 门店盘点
 *
 * <AUTHOR>
 */
@Slf4j
@Service
public class ShopInventoryServiceImpl implements ShopInventoryService {

    @Resource
    private ShopInventoryConvertor shopInventoryConvertor;
    @Resource
    private EntityFactory entityFactory;
    @Resource
    private FrShopInventoryRepository frShopInventoryRepository;
    @Resource
    private CoreRealWarehouseStockRepository coreRealWarehouseStockRepository;
    @Resource
    private RealWarehouseRepository realWarehouseRepository;
    @Resource
    private ShopInventoryWarehouseRepository shopInventoryWarehouseRepository;
    @Autowired
    private ShopFacade shopFacade;
    @Autowired
    private SkuFacade skuFacade;
    @Resource
    private PosDaySummaryFacade posDaySummaryFacade;
    @Resource
    private SapProxyFacade sapProxyFacade;
    @Resource
    private SkuQtyUnitTools skuQtyUnitTools;
    @Resource
    private RealWarehouseConvertor realWarehouseConvertor;
    @Autowired
    private WarehouseRecordRepository warehouseRecordRepository;

    @Override
    public List<Long> queryInitShopInventoryRecords(Integer startPage, Integer endPage) {
        Calendar cal = Calendar.getInstance();
        cal.add(Calendar.DAY_OF_MONTH, -1);
        Date date = cal.getTime();
        List<Long> ids = frShopInventoryRepository.queryInitShopInventoryRecordPage(date, startPage, endPage);
        return ids;
    }

    @Override
    public void handleShopInventoryRecords(Long id) {
        ShopInventoryRecordE frontRecord = frShopInventoryRepository.queryInitShopInventoryRecord(id);
        if (frontRecord == null) {
            return;
        }
        try {
            PosDaySummaryDTO posDaySummaryDTO = posDaySummaryFacade.posDaySummary(frontRecord.getShopCode(), frontRecord.getOutCreateTime());
            if (posDaySummaryDTO == null) {
                return;
            }
            if (frontRecord.getBusinessType() == 1) {
                //全盘
                frontRecord.allInventory();
            } else if (frontRecord.getBusinessType() == 9) {
                //账面库存
                frontRecord.accountInventory();
            }
        } catch (Exception e) {
            log.error(e.getMessage(), e);
        }

    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void addShopInventoryRecords(List<InventoryRecordDTO> frontRecords) {
        List<String> outRecordCodes = frontRecords.stream().map(InventoryRecordDTO :: getOutRecordCode).collect(Collectors.toList());
        //查询盘点单据
        List<String> finalOutRecordCodes = frShopInventoryRepository.judgeExistByOutRecordCodes(outRecordCodes);
        //判断判断单是否已存入中台
        List<InventoryRecordDTO> newFrontRecords = frontRecords.stream().
                filter(frontRecord -> !finalOutRecordCodes.contains(frontRecord.getOutRecordCode() + "_" + frontRecord.getShopCode())).collect(Collectors.toList());
        //幂等判断已有单据，返回成功
        if(newFrontRecords.size() == 0 ){
            return;
        }
        List<ShopInventoryRecordE> frontRecordList = shopInventoryConvertor.shopInventoryDtoListToShopInventoryEntityList(newFrontRecords);
        //根据门店编号查询门店仓
        List<String> shopList = frontRecordList.stream().map(ShopInventoryRecordE :: getShopCode).distinct().collect(Collectors.toList());
        List<RealWarehouseE> realWarehouseList = realWarehouseRepository.getRwListByShopCodes(shopList);
        for(ShopInventoryRecordE frontRecordE : frontRecordList){
            //根据门店查询门店仓库id
            Long warehouseId = realWarehouseList.stream().filter(realWarehouse ->
                    frontRecordE.getShopCode().equals(realWarehouse.getShopCode())).map(RealWarehouseE :: getId).findFirst().orElse(null);
            //没有实仓，设置仓库为0，并状态为异常
            if(null == warehouseId || 0 == warehouseId ){
                frontRecordE.setRealWarehouseId(0L);
                //状态为异常状态
                frontRecordE.setRecordStatus(FrontRecordStatusVO.EXCEPTION_RECORD.getStatus());
            }else{
                frontRecordE.setRealWarehouseId(warehouseId);
                if(frontRecordE.getBusinessType() == 1 || frontRecordE.getBusinessType() == 9 ){
                    //状态为完成状态
                    frontRecordE.setRecordStatus(FrontRecordStatusVO.INIT.getStatus());
                }else {
                    //状态为完成状态
                    frontRecordE.setRecordStatus(FrontRecordStatusVO.COMPLETE.getStatus());
                }
            }
            //生成盘点前置单
            frontRecordE.addFrontRecord();
            //kibana日志信息
            KibanaLogUtils.printKibanaRecordInfo(frontRecordE.getRecordCode(),frontRecordE.getChannelCode(),StockRecordInfoTypeVo.SHOP_INVENTORY.getType(),StockRecordInfoTypeVo.SHOP_INVENTORY.getDesc(),"addShopInventoryRecords",frontRecords);
        }

        List<ShopInventoryRecordE> sapFrontRecordList = shopInventoryConvertor.shopInventoryDtoListToShopInventoryEntityList(newFrontRecords);
        List<ShopInventoryRecordE> sapFilterFrontRecordList = sapFrontRecordList.stream().filter(record ->
                record.getBusinessType() == 1 || record.getBusinessType() == 9).collect(Collectors.toList());
        if(sapFilterFrontRecordList != null && sapFilterFrontRecordList.size() > 0){
            //盘点推送SAP
            sapProxyFacade.transferInventorys(sapFrontRecordList);
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void addShopInventoryOutRecord(OutWarehouseRecordDTO dto) {
        //幂等校验
        WarehouseRecordE warehouseRecordE=warehouseRecordRepository.queryWarehouseRecordByRecordCode(dto.getRecordCode());
        if(null != warehouseRecordE){
            return;
        }
        RealWarehouseE outWarehouse = realWarehouseRepository.getRealWarehouseByOutCodeAndFactoryCode(dto.getWarehouseCode(),dto.getFactoryCode());
        if(null == outWarehouse){
            throw new RomeException(ResCode.STOCK_ERROR_1002,"出库仓库不存在");
        }
        //kibana日志信息
        KibanaLogUtils.printKibanaRecordInfo(dto.getRecordCode(),dto.getChannelCode(),StockRecordInfoTypeVo.SHOP_INVENTORY.getType(),StockRecordInfoTypeVo.SHOP_INVENTORY.getDesc(),"addShopInventoryRecord",dto);
        List<RecordDetailDTO> outFrontRecordDetails = new ArrayList<>();
        List<CoreRealStockOpDetailDO> decreaseDetails = new ArrayList<>();
        List<CoreRealWarehouseStockDO> realWarehouseDOs = new ArrayList<>();
        List<RecordDetailDTO> frontRecordDetails = dto.getDetailList();
        //查询商品sku信息
        List<String> skuCodes=frontRecordDetails.stream().map(RecordDetailDTO::getSkuCode).collect(Collectors.toList());
        List<SkuInfoExtDTO> skuList = skuFacade.skusBySkuCode(skuCodes);
        Map<String, SkuInfoExtDTO> skuMap = RomeCollectionUtil.listforMap(skuList, "skuCode", null);
        for (RecordDetailDTO detailE : frontRecordDetails) {
            CoreRealWarehouseStockDO crwsDo = new CoreRealWarehouseStockDO();
            crwsDo.setRealWarehouseId(outWarehouse.getId());
            SkuInfoExtDTO skuInfoExtDTO=skuMap.get(detailE.getSkuCode());
            if(null !=skuInfoExtDTO){
                crwsDo.setSkuId(skuInfoExtDTO.getId());
                detailE.setSkuId(skuInfoExtDTO.getId());
            }
            detailE.setSkuId(detailE.getSkuId());
            realWarehouseDOs.add(crwsDo);
        }
        //查询门店实仓sku数量
        realWarehouseDOs = coreRealWarehouseStockRepository.getRWStock(realWarehouseDOs);
        //库存没有skuId,查询库存不会返回此skuId
        for (CoreRealWarehouseStockDO cwsdo : realWarehouseDOs) {
            RecordDetailDTO detailE = new RecordDetailDTO();
            RecordDetailDTO frontDetailE = frontRecordDetails.stream().filter(details -> details.getSkuId().equals(cwsdo.getSkuId()))
                    .findFirst().orElse(null);
            //盘点数与实仓库存数差值
            BigDecimal differenceValue = frontDetailE.getBasicSkuQty().add(cwsdo.getRealQty()) ;
//            if(differenceValue.compareTo(new BigDecimal(0.0)) == -1){
//                throw new RomeException(ResCode.STOCK_ERROR_1002,"盘点数与实仓库存数相加小于0");
//            }
            detailE.setSkuCode(frontDetailE.getSkuCode());
            detailE.setBasicSkuQty(frontDetailE.getBasicSkuQty());
            outFrontRecordDetails.add(detailE);
            CoreRealStockOpDetailDO coreRecordRealStockDecreaseDetailDO = new CoreRealStockOpDetailDO();
            coreRecordRealStockDecreaseDetailDO.setSkuId(cwsdo.getSkuId());
            coreRecordRealStockDecreaseDetailDO.setSkuCode(frontDetailE.getSkuCode());
            coreRecordRealStockDecreaseDetailDO.setRealQty(frontDetailE.getBasicSkuQty());
            coreRecordRealStockDecreaseDetailDO.setRealWarehouseId(outWarehouse.getId());
            coreRecordRealStockDecreaseDetailDO.setCheckBeforeOp(false);
            decreaseDetails.add(coreRecordRealStockDecreaseDetailDO);

        }
        boolean isSuccess = false;
        CoreRealStockOpDO coreRecordRealStockDecreaseDO = null;
        try {
            //实盘数量与门店实仓数量比较，有差异产生出入库单
            if(outFrontRecordDetails != null && outFrontRecordDetails.size() > 0){
//                frontRecordE.unitConvertBasicUnit(outFrontRecordDetails);
//                frontRecordE.setFrontRecordDetails(outFrontRecordDetails);
                //生成出库单
                ShopInventoryWarehouseRecordE outWarehouseRecord = entityFactory.createEntity(ShopInventoryWarehouseRecordE.class);
                outWarehouseRecord.createOutRecordByFrontRecord(dto,outWarehouse);
                //状态为已出库状态
                outWarehouseRecord.setRecordStatus(WarehouseRecordStatusVO.OUT_ALLOCATION.getStatus());
                outWarehouseRecord.setBatchStatus(WarehouseRecordBatchStatusVO.INIT.getStatus());
                outWarehouseRecord.addOutWarehouseRecord();
                //如果产生出库单减少实仓、虚仓、策略组、渠道库存数量
                coreRecordRealStockDecreaseDO = new CoreRealStockOpDO();
                coreRecordRealStockDecreaseDO.setRecordCode(outWarehouseRecord.getRecordCode());
                coreRecordRealStockDecreaseDO.setTransType(outWarehouseRecord.getRecordType());
                coreRecordRealStockDecreaseDO.setDetailDos(decreaseDetails);
                coreRealWarehouseStockRepository.decreaseRealQty(coreRecordRealStockDecreaseDO);
                //改为job补偿机制来扣减批次库存
//                //发送批次库存MQ
//                warehouseBatchStockE.sendOutBatchStockMq(outWarehouseRecord);
            }
			//kibana日志信息
			KibanaLogUtils.printKibanaRecordInfo(dto.getRecordCode(),dto.getChannelCode(), StockRecordInfoTypeVo.SHOP_INVENTORY.getType(),StockRecordInfoTypeVo.SHOP_INVENTORY.getDesc(),"addShopInventoryRecord",dto);
            isSuccess = true;
        } catch (RomeException e) {
            throw new RomeException(e.getCode(), e.getMessage());
        } finally {
            //模型成功，业务等处理失败，需要回滚
            if (!isSuccess) {
                if (coreRecordRealStockDecreaseDO != null) {
                    RedisRollBackFacade.redisRollBack(coreRecordRealStockDecreaseDO);
                }
            }
        }
    }

    @Override
    public PageInfo<ShopInventoryPageDTO> queryShopInventoryList(ShopInventoryPageDTO frontRecord) {
        Page page = PageHelper.startPage(frontRecord.getPageIndex(), frontRecord.getPageSize());
        ShopInventoryRecordE shopInventoryRecordE = shopInventoryConvertor.shopInventoryPageDtoToShopInventoryEntity(frontRecord);
        List<ShopInventoryRecordE> list = frShopInventoryRepository.queryShopInventoryList(shopInventoryRecordE);
        List<String> shopCodes = list.stream().filter(record -> StringUtils.isNotBlank(record.getShopCode())).map(ShopInventoryRecordE::getShopCode).collect(Collectors.toList());
        List<StoreDTO> storeList = shopFacade.searchByCodeList(shopCodes);
        for (StoreDTO store : storeList) {
            list.stream().filter(record -> record.getShopCode().equals(store.getCode())).forEach(record -> record.setShopName(store.getName()));
        }
        List<ShopInventoryPageDTO> pageList = shopInventoryConvertor.shopInventoryListEntityToShopInventoryPageList(list);
        PageInfo<ShopInventoryPageDTO> personPageInfo = new PageInfo<>(pageList);
        personPageInfo.setTotal(page.getTotal());
        return personPageInfo;
    }

    @Override
    public List<ShopInventoryDetailDTO> queryShopInventoryDetailList(Long frontRecordId) {
        List<ShopInventoryRecordDetailE> details = frShopInventoryRepository.queryShopInventoryDetailList(frontRecordId);
        //查询商品单位信息
        List<SkuUnitExtDTO> skuList = skuFacade.unitsByFrontDetail(details);
        //查询商品信息
        List<Long> skuIds = details.stream().map(ShopInventoryRecordDetailE::getSkuId).distinct().collect(Collectors.toList());
        List<SkuInfoExtDTO> skuInfoList = skuFacade.skusBySkuId(skuIds);
        //查询出入库单信息
        List<ShopInventoryWarehouseRecordE> warehouseRecords = shopInventoryWarehouseRepository.queryInventoryWarehouseById(frontRecordId);
        List<WarehouseRecordDetail> outDetailList = warehouseRecords.stream().filter(record ->
                WarehouseRecordTypeVO.SHOP_INVENTORY_OUT_WAREHOUSE_RECORD.getType().equals(record.getRecordType()))
                .map(ShopInventoryWarehouseRecordE::getWarehouseRecordDetails).findAny().orElse(null);
        List<WarehouseRecordDetail> intDetailList = warehouseRecords.stream().filter(record ->
                WarehouseRecordTypeVO.SHOP_INVENTORY_IN_WAREHOUSE_RECORD.getType().equals(record.getRecordType()))
                .map(ShopInventoryWarehouseRecordE::getWarehouseRecordDetails).findAny().orElse(null);
        details.forEach(detail -> {
            //设置单位信息
            SkuUnitExtDTO skuUnit = skuList.stream().filter(sku -> sku.getSkuId().equals(detail.getSkuId()) && sku.getUnitCode().equals(detail.getUnitCode()))
                    .findFirst().orElse(null);
            if (skuUnit != null) {
                detail.setUnit(skuUnit.getUnitName());
                detail.setScale(skuUnit.getScale());
            }
            //设置商品名称
            SkuInfoExtDTO skuInfo = skuInfoList.stream().filter(sku -> sku.getId().equals(detail.getSkuId())).findFirst().orElse(null);
            if (skuInfo != null) {
                detail.setSkuName(skuInfo.getName());
            }
            detail.setStockQty(detail.getSkuQty());
            detail.setDiffStockQty(new BigDecimal(0));
            if (outDetailList != null && detail.getScale() != null) {
                outDetailList.stream().filter(outDetail -> detail.getSkuId().equals(outDetail.getSkuId())).forEach(outDetail -> {
                    detail.setDiffStockQty(detail.getDiffStockQty().subtract(outDetail.getActualQty().divide(detail.getScale(),StockCoreConsts.DECIMAL_POINT_NUM, BigDecimal.ROUND_DOWN)));
                    detail.setStockQty(detail.getStockQty().add(outDetail.getActualQty().divide(detail.getScale(), StockCoreConsts.DECIMAL_POINT_NUM, BigDecimal.ROUND_DOWN)));
                });
            }
            if (intDetailList != null && detail.getScale() != null) {
                intDetailList.stream().filter(inDetail -> detail.getSkuId().equals(inDetail.getSkuId())).forEach(inDetail -> {
                    detail.setDiffStockQty(detail.getDiffStockQty().add(inDetail.getActualQty().divide(detail.getScale(),StockCoreConsts.DECIMAL_POINT_NUM, BigDecimal.ROUND_DOWN)));
                    detail.setStockQty(detail.getStockQty().subtract(inDetail.getActualQty().divide(detail.getScale(),StockCoreConsts.DECIMAL_POINT_NUM, BigDecimal.ROUND_DOWN)));
                });
            }

        });
        List<ShopInventoryDetailDTO> list = shopInventoryConvertor.shopInventoryDetailListDtoToShopInventoryDetailListEntity(details);
        return list;
    }

    @Override
    public List<RealWarehouseStockDTO> queryAllWarehouseStockById(String realWarehouseOutCode,String factoryCode) {
        RealWarehouseE realWarehouseE = realWarehouseRepository.getRealWarehouseByOutCodeAndFactoryCode(realWarehouseOutCode, factoryCode);
        AlikAssert.isNotNull(realWarehouseE,ResCode.STOCK_ERROR_1011,ResCode.STOCK_ERROR_1011_DESC);
        List<RealWarehouseStockE> realWarehouseStockES = realWarehouseRepository.queryAllWarehouseStockById(realWarehouseE.getId());
        List<RealWarehouseStockDTO> realWarehouseStockDTOS = realWarehouseConvertor.stockEntityListToDTOList(realWarehouseStockES);
        if(CollectionUtils.isNotEmpty(realWarehouseStockDTOS)){
            realWarehouseStockDTOS.parallelStream().forEach(x->{
                x.setAvailableQty(x.getRealQty().subtract(x.getLockQty()));
                x.setFactoryCode(factoryCode);
                x.setRealWarehouseOutCode(realWarehouseOutCode);
            });
        }
        return realWarehouseStockDTOS;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void addShopInventoryInRecord(InWarehouseRecordDTO dto) {
        //幂等校验
        WarehouseRecordE warehouseRecordE=warehouseRecordRepository.queryWarehouseRecordByRecordCode(dto.getRecordCode());
        if(null != warehouseRecordE){
            return;
        }
        RealWarehouseE inWarehouse = realWarehouseRepository.getRealWarehouseByOutCodeAndFactoryCode(dto.getWarehouseCode(),dto.getFactoryCode());
        if(null == inWarehouse){
            throw new RomeException(ResCode.STOCK_ERROR_1002,"出库仓库不存在");
        }
        //kibana日志信息
        KibanaLogUtils.printKibanaRecordInfo(dto.getRecordCode(),dto.getChannelCode(),StockRecordInfoTypeVo.SHOP_INVENTORY.getType(),StockRecordInfoTypeVo.SHOP_INVENTORY.getDesc(),"addShopInventoryInRecord",dto);
        List<RecordDetailDTO> inFrontRecordDetails = new ArrayList<>();
        List<CoreRealStockOpDetailDO> increaseDetails = new ArrayList<>();
        List<CoreRealWarehouseStockDO> realWarehouseDOs = new ArrayList<>();
        List<RecordDetailDTO> frontRecordDetails = dto.getDetailList();
        //查询商品sku信息
        List<String> skuCodes=frontRecordDetails.stream().map(RecordDetailDTO::getSkuCode).collect(Collectors.toList());
        List<SkuInfoExtDTO> skuList = skuFacade.skusBySkuCode(skuCodes);
        Map<String, SkuInfoExtDTO> skuMap = RomeCollectionUtil.listforMap(skuList, "skuCode", null);
        for (RecordDetailDTO detailE : frontRecordDetails) {
            CoreRealWarehouseStockDO crwsDo = new CoreRealWarehouseStockDO();
            crwsDo.setRealWarehouseId(inWarehouse.getId());
            SkuInfoExtDTO skuInfoExtDTO=skuMap.get(detailE.getSkuCode());
            if(null !=skuInfoExtDTO){
                crwsDo.setSkuId(skuInfoExtDTO.getId());
                detailE.setSkuId(skuInfoExtDTO.getId());
            }
            realWarehouseDOs.add(crwsDo);
        }
        //查询门店实仓sku数量
        realWarehouseDOs = coreRealWarehouseStockRepository.getRWStock(realWarehouseDOs);
        for (RecordDetailDTO detailE : frontRecordDetails) {
            if (realWarehouseDOs == null || (realWarehouseDOs != null && realWarehouseDOs.stream().filter(realWarehouse -> realWarehouse.getSkuId().equals(detailE.getSkuId())).count() == 0)) {
                inFrontRecordDetails.add(detailE);
                CoreRealStockOpDetailDO coreRecordRealStockIncreaseDetailDO = new CoreRealStockOpDetailDO();
                SkuInfoExtDTO skuInfoExtDTO=skuMap.get(detailE.getSkuCode());
                if(null !=skuInfoExtDTO){
                    coreRecordRealStockIncreaseDetailDO.setSkuId(skuInfoExtDTO.getId());
                }
                coreRecordRealStockIncreaseDetailDO.setSkuCode(detailE.getSkuCode());
                coreRecordRealStockIncreaseDetailDO.setRealQty(detailE.getBasicSkuQty());
                coreRecordRealStockIncreaseDetailDO.setRealWarehouseId(inWarehouse.getId());
                increaseDetails.add(coreRecordRealStockIncreaseDetailDO);
            }
        }
        //库存没有skuId,查询库存不会返回此skuId
        for (CoreRealWarehouseStockDO cwsdo : realWarehouseDOs) {
            RecordDetailDTO detailE = new RecordDetailDTO();
            RecordDetailDTO frontDetailE = frontRecordDetails.stream().filter(details -> details.getSkuId().equals(cwsdo.getSkuId()))
                    .findFirst().orElse(null);
            //盘点数与实仓库存数差值
            BigDecimal differenceValue = frontDetailE.getBasicSkuQty().add(cwsdo.getRealQty()) ;
//            if(differenceValue.compareTo(new BigDecimal(0.0)) == -1){
//                throw new RomeException(ResCode.STOCK_ERROR_1002,"盘点数与实仓库存数相加小于0");
//            }
                detailE.setSkuCode(frontDetailE.getSkuCode());
                detailE.setBasicSkuQty(frontDetailE.getBasicSkuQty());
                inFrontRecordDetails.add(detailE);
                CoreRealStockOpDetailDO coreRecordRealStockIncreaseDetailDO = new CoreRealStockOpDetailDO();
                coreRecordRealStockIncreaseDetailDO.setSkuId(cwsdo.getSkuId());
                coreRecordRealStockIncreaseDetailDO.setSkuCode(frontDetailE.getSkuCode());
                coreRecordRealStockIncreaseDetailDO.setRealQty(frontDetailE.getBasicSkuQty());
                coreRecordRealStockIncreaseDetailDO.setRealWarehouseId(inWarehouse.getId());
                increaseDetails.add(coreRecordRealStockIncreaseDetailDO);
        }
        boolean isSuccess = false;
        CoreRealStockOpDO coreRecordRealStockIncreaseDO = null;
        try {
            //实盘数量与门店实仓数量比较，有差异产生出入库单
            if(inFrontRecordDetails != null && inFrontRecordDetails.size() > 0){
                //生成入库单
                ShopInventoryWarehouseRecordE inWarehouseRecord = entityFactory.createEntity(ShopInventoryWarehouseRecordE.class);
                inWarehouseRecord.createInRecordByFrontRecord(dto,inWarehouse);
                //状态为已入库状态
                inWarehouseRecord.setRecordStatus(WarehouseRecordStatusVO.IN_ALLOCATION.getStatus());
                inWarehouseRecord.addInWarehouseRecord();
                //如果产生入库单增加实仓、虚仓、策略组、渠道库存数量
                coreRecordRealStockIncreaseDO = new CoreRealStockOpDO();
                coreRecordRealStockIncreaseDO.setRecordCode(inWarehouseRecord.getRecordCode());
                coreRecordRealStockIncreaseDO.setTransType(inWarehouseRecord.getRecordType());
                coreRecordRealStockIncreaseDO.setDetailDos(increaseDetails);
                coreRealWarehouseStockRepository.increaseRealQty(coreRecordRealStockIncreaseDO);
            }
            //kibana日志信息
            KibanaLogUtils.printKibanaRecordInfo(dto.getRecordCode(),dto.getChannelCode(), StockRecordInfoTypeVo.SHOP_INVENTORY.getType(),StockRecordInfoTypeVo.SHOP_INVENTORY.getDesc(),"addShopInventoryInRecord",dto);
            isSuccess = true;
        } catch (RomeException e) {
            throw new RomeException(e.getCode(), e.getMessage());
        } finally {
            //模型成功，业务等处理失败，需要回滚
            if (!isSuccess) {
                if (coreRecordRealStockIncreaseDO != null) {
                    RedisRollBackFacade.redisRollBack(coreRecordRealStockIncreaseDO);
                }
            }
        }
    }

    @Override
    public void cancelShopInventory(String recordCode) {
        WarehouseRecordE recordE = warehouseRecordRepository.queryWarehouseRecordByRecordCode(recordCode);
        AlikAssert.isNotNull(recordE,ResCode.STOCK_ERROR_1002,"出入库单不存在");
        if(Objects.equals(recordE.getRecordStatus(),WarehouseRecordStatusVO.DISABLED.getStatus())){
            return;
        }
        List<WarehouseRecordDetail> warehouseRecordDetails = warehouseRecordRepository.queryDetailListByRecordId(recordE.getId());
        recordE.setWarehouseRecordDetails(warehouseRecordDetails);
        if(Objects.equals(recordE.getBusinessType(), WarehouseRecordBusinessTypeVO.IN_WAREHOUSE_RECORD.getType())){
            //取消入库单
            this.cancelInWarehouseRecord(recordE);
        }
        if(Objects.equals(recordE.getBusinessType(), WarehouseRecordBusinessTypeVO.OUT_WAREHOUSE_RECORD.getType())){
            //取消出库单
            this.cancelOutWarehouseRecord(recordE);
        }
    }



    /**
     * 取消入库单
     * @param warehouseRecordE
     */
    private void cancelInWarehouseRecord(WarehouseRecordE warehouseRecordE){
        warehouseRecordRepository.updateToCanceled(warehouseRecordE.getId());
        //将批次库存状态改成初始化 表示需求扣减批次库存【定时器处理】
        warehouseRecordRepository.updateRecordBatchStatusToInitFromComplete(warehouseRecordE.getRecordCode());
        CoreRealStockOpDO coreRecordRealStockDO = this.initCoreStockObj(warehouseRecordE);
        boolean isSuccess = false;
        try {
            coreRealWarehouseStockRepository.decreaseRealQty(coreRecordRealStockDO);
            isSuccess = true;
        } catch (RomeException e) {
            throw e;
        } catch (Exception e) {
            throw new RomeException(ResCode.STOCK_ERROR_1003, ResCode.STOCK_ERROR_1003_DESC);
        } finally {
            if (!isSuccess) {
                RedisRollBackFacade.redisRollBack(coreRecordRealStockDO);
            }
        }
    }

    /**
     * 初始化增加实体仓库库存的对象
     */
    private CoreRealStockOpDO initCoreStockObj(AbstractWarehouseRecord recordE) {
        List<CoreRealStockOpDetailDO> increaseDetails = new ArrayList<>();
        for (WarehouseRecordDetail detail : recordE.getWarehouseRecordDetails()) {
            CoreRealStockOpDetailDO coreRealStockOpDetailDO = new CoreRealStockOpDetailDO();
            coreRealStockOpDetailDO.setSkuId(detail.getSkuId());
            coreRealStockOpDetailDO.setChannelCode(recordE.getChannelCode());
            coreRealStockOpDetailDO.setSkuCode(detail.getSkuCode());
            coreRealStockOpDetailDO.setCheckBeforeOp(false);
            coreRealStockOpDetailDO.setRealQty(detail.getActualQty());
            coreRealStockOpDetailDO.setRealWarehouseId(recordE.getRealWarehouseId());
            increaseDetails.add(coreRealStockOpDetailDO);
        }
        CoreRealStockOpDO coreRealStockOpDO = new CoreRealStockOpDO();
        coreRealStockOpDO.setRecordCode(recordE.getRecordCode());
        coreRealStockOpDO.setTransType(recordE.getRecordType());
        coreRealStockOpDO.setDetailDos(increaseDetails);
        return coreRealStockOpDO;
    }
    /**
     * 取消出库单
     * @param warehouseRecordE
     * @return
     */
    private void cancelOutWarehouseRecord(WarehouseRecordE warehouseRecordE){
        //更新后置单状态为已取消
        warehouseRecordRepository.updateToCanceledFromComplete(warehouseRecordE.getId());
        CoreRealStockOpDO coreRecordRealStockIncreaseDO = this.initIncreaseStockObj(warehouseRecordE);
        boolean isSuccess = false;
        try {
            coreRealWarehouseStockRepository.increaseRealQty(coreRecordRealStockIncreaseDO);
            isSuccess = true;
        } catch (RomeException e) {
            throw e;
        } catch (Exception e) {
            throw new RomeException(ResCode.STOCK_ERROR_1003, ResCode.STOCK_ERROR_1003_DESC);
        } finally {
            if (!isSuccess) {
                RedisRollBackFacade.redisRollBack(coreRecordRealStockIncreaseDO);
            }
        }
    }

    /**
     * 初始化增加实体仓库库存的对象
     */
    private CoreRealStockOpDO initIncreaseStockObj(AbstractWarehouseRecord recordE) {
        List<CoreRealStockOpDetailDO> increaseDetails = new ArrayList<>();
        for (WarehouseRecordDetail detail : recordE.getWarehouseRecordDetails()) {
            CoreRealStockOpDetailDO coreRealStockOpDetailDO = new CoreRealStockOpDetailDO();
            coreRealStockOpDetailDO.setSkuId(detail.getSkuId());
            coreRealStockOpDetailDO.setChannelCode(recordE.getChannelCode());
            coreRealStockOpDetailDO.setSkuCode(detail.getSkuCode());
            coreRealStockOpDetailDO.setRealQty(detail.getActualQty());
            coreRealStockOpDetailDO.setRealWarehouseId(recordE.getRealWarehouseId());
            increaseDetails.add(coreRealStockOpDetailDO);
        }
        CoreRealStockOpDO coreRealStockOpDO = new CoreRealStockOpDO();
        coreRealStockOpDO.setRecordCode(recordE.getRecordCode());
        coreRealStockOpDO.setTransType(recordE.getRecordType());
        coreRealStockOpDO.setDetailDos(increaseDetails);
        return coreRealStockOpDO;
    }
}
