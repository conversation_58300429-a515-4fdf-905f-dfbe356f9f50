/**
 * Filename WmsBatchCheckServiceImpl.java
 * Company 上海来伊份科技有限公司。
 * <AUTHOR>
 * @version 
 */
package com.rome.stock.innerservice.domain.service.impl;

import com.alibaba.fastjson.JSON;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.google.common.collect.Lists;
import com.rome.arch.core.exception.RomeException;
import com.rome.arch.util.SpringBeanUtil;
import com.rome.stock.common.enums.BatchStockTypeVO;
import com.rome.stock.common.enums.redis.RedisCacheKeyEnum;
import com.rome.stock.common.utils.BatchStockUtils;
import com.rome.stock.common.utils.DateUtil;
import com.rome.stock.core.constant.StockCoreConsts;
import com.rome.stock.core.infrastructure.redis.StockCacheKeyEnum;
import com.rome.stock.innerservice.api.dto.*;
import com.rome.stock.innerservice.common.EsIndexTypeConfig;
import com.rome.stock.innerservice.common.RedisUtil;
import com.rome.stock.innerservice.common.ResCode;
import com.rome.stock.innerservice.common.RomeCollectionUtil;
import com.rome.stock.innerservice.constant.RealWarehouseTypeVO;
import com.rome.stock.innerservice.domain.convertor.EsWmsBatchCheckFlowConvertor;
import com.rome.stock.innerservice.domain.entity.RealWarehouseE;
import com.rome.stock.innerservice.domain.repository.RealWarehouseRepository;
import com.rome.stock.innerservice.domain.repository.RealWarehouseWmsConfigRepository;
import com.rome.stock.innerservice.domain.repository.elasticsearch.EsWmsBatchCheckFlowRepository;
import com.rome.stock.innerservice.domain.service.BatchStockService;
import com.rome.stock.innerservice.domain.service.OrderUtilService;
import com.rome.stock.innerservice.domain.service.WmsBatchBoxCheckFlowService;
import com.rome.stock.innerservice.domain.service.WmsBatchCheckService;
import com.rome.stock.innerservice.facade.BatchStockFacade;
import com.rome.stock.innerservice.facade.StockToolFacade;
import com.rome.stock.innerservice.infrastructure.dataobject.*;
import com.rome.stock.innerservice.infrastructure.dataobject.elasticsearch.EsWmsBatchCheckFlowDO;
import com.rome.stock.innerservice.infrastructure.mapper.*;
import com.rome.stock.innerservice.remote.item.dto.SkuInfoExtDTO;
import com.rome.stock.innerservice.remote.item.facade.SkuFacade;
import com.rome.stock.innerservice.template.inactive.WmsBatchCheckFlowInactiveTemplate;
import com.rome.stock.wms.config.BaseinfoConfiguration;
import com.rome.stock.wms.constants.WarehouseWmsConfigEnum;
import com.rome.stock.wms.dto.response.StockQueryResponseItem;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.time.DateUtils;
import org.apache.commons.lang3.StringUtils;
import org.elasticsearch.action.search.SearchType;
import org.elasticsearch.index.IndexNotFoundException;
import org.elasticsearch.index.query.BoolQueryBuilder;
import org.elasticsearch.index.query.QueryBuilders;
import org.elasticsearch.index.query.RangeQueryBuilder;
import org.elasticsearch.search.sort.SortBuilders;
import org.elasticsearch.search.sort.SortOrder;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.elasticsearch.core.query.NativeSearchQuery;
import org.springframework.data.elasticsearch.core.query.NativeSearchQueryBuilder;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.concurrent.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * wms批次库存核对
 * <AUTHOR>
 * @since 2021-7-7 14:37:33
 */
@Slf4j
@Service
public class WmsBatchCheckServiceImpl implements WmsBatchCheckService {

	@Resource(name = "coreStockTask")
	private ThreadPoolTaskExecutor coreStockTask;
	
	@Autowired
	private RealWarehouseWmsConfigRepository realWarehouseWmsConfigRepository; 
	
	@Autowired
	private RealWarehouseRepository realWarehouseRepository;
	
	@Resource
	private RedisUtil redisUtil;
	
	@Resource
    private RealWarehouseStockMapper realWarehouseStockMapper;
	
	@Resource
    private BatchStockMapper batchStockMapper;
	
	@Resource
	private WmsBatchCheckFlowMapper wmsBatchCheckFlowMapper;
	
	@Resource
    private EsWmsBatchCheckFlowConvertor esWmsBatchCheckFlowConvertor;
	
	@Autowired
	private EsWmsBatchCheckFlowRepository esWmsBatchCheckFlowRepository;
	
	@Resource
    private SkuFacade skuFacade;
    @Resource
    private BatchBoxConfigMapper batchBoxConfigMapper;
    @Resource
    private BatchStockBoxDetailMapper batchStockBoxDetailMapper;
	@Resource
	private WmsBatchBoxCheckFlowMapper wmsBatchBoxCheckFlowMapper;

	/**
	 * wms批次原箱库存核对
	 */
	@Override
	public void wmsBatchBoxCheckJob(BatchStockBoxCheckDTO batchStockBoxCheckDTO) {
		log.info("开始,wms批次原箱库存核对/初始化");
		List<Long> boxRealWarehouseIdList;
		if (CollectionUtils.isNotEmpty(batchStockBoxCheckDTO.getRealWarehouseIdList())) {
			boxRealWarehouseIdList = batchStockBoxCheckDTO.getRealWarehouseIdList();
		} else {
			//查询原箱配置表
			boxRealWarehouseIdList = batchBoxConfigMapper.queryEnableConfigList();
		}
		if (CollectionUtils.isEmpty(boxRealWarehouseIdList)) {
			log.warn("批次原箱库存配置查询为空,结束");
			return;
		}
		final Map<String, SkuInfoExtDTO> skuInfoMap = new ConcurrentHashMap<>(4000);
		// 每页大小
		List<Future> futures = new ArrayList<>(100);
		Future future;
		List<RealWarehouseE> realWarehouseEs;
		realWarehouseEs = realWarehouseRepository.queryWarehouseByIds(boxRealWarehouseIdList);
		if (realWarehouseEs != null && realWarehouseEs.size() > 0) {
			for (RealWarehouseE realWarehouseE : realWarehouseEs) {
				// 过滤不需处理的
				future = coreStockTask.submit(() -> wmsBatchBoxCheckByWarehouse(realWarehouseE, skuInfoMap, batchStockBoxCheckDTO.isInitFlag(),batchStockBoxCheckDTO.getSkuIdList()));
				futures.add(future);
			}
		}
		// 等待完成
		for (Future future2 : futures) {
			try {
				future2.get();
			} catch (Exception e) {
				log.error("wms批次原箱库存核对,等队列完成,出错{}", e);
			}
		}
		futures.clear();
		futures = null;
		log.info("结束,wms批次原箱库存核对");
	}


	/**
	 * wms批次原箱库存核对-仓库
	 * @param realWarehouseE
	 * @param skuInfoMap
	 * @param initFlag:是否初始化原箱库存
	 */
	private void wmsBatchBoxCheckByWarehouse(RealWarehouseE realWarehouseE, Map<String, SkuInfoExtDTO> skuInfoMap
			, boolean initFlag,List<Long> skuIdList) {
		int count = 0;
		int countSuccess = 0;
		boolean isLock = false;
		RedisCacheKeyEnum cacheKeyEnum = RedisCacheKeyEnum.WMS_BATCH_BOX_CHECK;
		// 核对，不一致的skuCode
		Set<String> notEqualsSkuCodeSet = new HashSet<>(16);
		String wmsDesc = null;
		try {
			isLock = redisUtil.lock(cacheKeyEnum.getKeyLock() + realWarehouseE.getId(), "1", cacheKeyEnum.getExpireTimeLock());
			if (isLock == false) {
				throw new RomeException(ResCode.STOCK_ERROR_1001, "仓库原箱库存正在同步中,factoryCode=" + realWarehouseE.getFactoryCode() + ",outCode=" + realWarehouseE.getRealWarehouseOutCode());
			}
			RealWarehouseWmsConfigDTO realWarehouseWmsConfigDO = realWarehouseWmsConfigRepository.findRealWarehouseWmsConfigById(realWarehouseE.getId());
			if (realWarehouseWmsConfigDO == null) {
				log.error("wms批次原箱库存核对,出错,暂未查询到实仓与wms的配置信息,实仓id=" + realWarehouseE.getId());
			}
			Integer wmsCode=realWarehouseWmsConfigDO.getWmsCode();
			if (WarehouseWmsConfigEnum.getSupportWmsByType(wmsCode) == null) {
				throw new RomeException(ResCode.STOCK_ERROR_1003, "暂不支持此仓库批次核对仓id=" + realWarehouseE.getId());
			}
			wmsDesc = realWarehouseWmsConfigDO.getWmsName();
			int current = 1;
			// 每页大小
			final int pageSize = 1000;
			List<String> skuCodes = new ArrayList<>(pageSize);
			List<Long> skuIds = new ArrayList<>(pageSize);

			PageHelper.startPage(current, pageSize, false);
			// 查询库存
			List<RealWarehouseStockDO> list;
			if(CollectionUtils.isNotEmpty(skuIdList)){
				list = realWarehouseStockMapper.queryStockByWhIdAndSkuIds(realWarehouseE.getId(),skuIdList);
			}else{
				list= realWarehouseStockMapper.querySkuIdByWhId(realWarehouseE.getId());
			}
			List<StockQueryResponseItem> stockQueryItems;
			String type = null;
			Date now = new Date();
			List<BatchStockDO> batchStockDOs = Lists.newArrayList();
			WmsBatchBoxCheckFlowService wmsBatchBoxCheckFlowService = SpringBeanUtil.getBean(WmsBatchBoxCheckFlowService.class);
			WmsBatchCheckService wmsBatchCheckService = SpringBeanUtil.getBean(WmsBatchCheckService.class);

			while (list != null && list.size() > 0) {
				if (type == null) {
					type = "A";
				} else {
					type = "I";
				}
				count += list.size();
				skuCodes.clear();
				skuIds.clear();
				for (RealWarehouseStockDO dto : list) {
					if (dto.getSkuId() != null) {
						skuIds.add(dto.getSkuId());
						skuCodes.add(dto.getSkuCode());
					}
				}
				//根据批次号进行汇总的库存值
				batchStockDOs = batchStockMapper.queryBatchStocksBySkuIdBatchCodeSumNum(skuIds, realWarehouseE.getId());
				List<BatchStockBoxDetailDO> stockBoxDetailDOS = batchStockBoxDetailMapper.listByRwIdAndSkuList(realWarehouseE.getId(), skuIds);
				//如果是每天的核对,不是初始化,需要将原箱库存表和库存批次表里的库存进行对比,如果存在不一致,需要预警出来
				if (!initFlag) {
					Map<String, BatchStockBoxDetailDO> boxDetailDOMap = stockBoxDetailDOS.stream().collect(Collectors.toMap(item -> item.getSkuId() + "-" + item.getBatchCode(), Function.identity(), (key1, key2) -> key1));
					for (BatchStockDO batchStockDO : batchStockDOs) {
						String key = batchStockDO.getSkuId() + "-" + batchStockDO.getBatchCode();
						if (!boxDetailDOMap.containsKey(key)) {
							log.error("wms批次原箱库存核对预警,暂未查询到原箱库存,实仓:{},skuCode:{},batchCode:{}的原箱库存数据", realWarehouseE.getRealWarehouseCode(), batchStockDO.getSkuCode(), batchStockDO.getBatchCode());
							continue;
						}
						BatchStockBoxDetailDO batchStockBoxDo = boxDetailDOMap.get(key);
						if (batchStockDO.getSkuQty().compareTo(batchStockBoxDo.getBoxSkuQty().add(batchStockBoxDo.getMixSkuQty())) != 0) {
							//如果批次库存和(原箱库存+非原箱库存)不想等,需要预警
							log.error("wms批次原箱库存核对预警,批次库存和(原箱库存+非原箱库存)不一致,实仓:{},skuCode:{},batchCode:{}的原箱库存数据", realWarehouseE.getRealWarehouseCode(), batchStockDO.getSkuCode(), batchStockDO.getBatchCode());
						}
						if (batchStockDO.getLockQty().compareTo(batchStockBoxDo.getBoxLockQty().add(batchStockBoxDo.getMixLockQty())) != 0) {
							//如果批次库存和(原箱库存+非原箱库存)不想等,需要预警
							log.error("wms批次原箱库存核对预警,批次锁定库存和(原箱锁定库存+非原箱锁定库存)不一致,实仓:{},skuCode:{},batchCode:{}的原箱库存数据", realWarehouseE.getRealWarehouseCode(), batchStockDO.getSkuCode(), batchStockDO.getBatchCode());
						}
					}
				}
				// 获取wms的库存
				if (skuCodes.size() > 0) {
					// 大幅仓
					List<WmsBatchBoxCheckFlowDO> wmsBatchCheckFlows = null;
					List<BatchStockBoxDetailDO> batchStockBoxDetailDOList = new ArrayList<>(skuCodes.size());
					// 获取支持的wms
					WarehouseWmsConfigEnum wmsConfigVO = WarehouseWmsConfigEnum.getSupportWmsByType(wmsCode);
					if (wmsConfigVO != null) {
						wmsBatchCheckFlows = new ArrayList<>(skuCodes.size());
						try {
							stockQueryItems = StockToolFacade.stockQuery(realWarehouseE, type, skuInfoMap, wmsConfigVO.getType() + "", skuCodes, true, 3);
							//生成初始化原箱库存表明细
							if (initFlag) {
								initBatchBoxStock(stockQueryItems, batchStockDOs, batchStockBoxDetailDOList);
							} else {
								//生成比对明细
								mergeToWmsBatchBoxCheckFlowE(list, stockBoxDetailDOS, stockQueryItems, wmsBatchCheckFlows, wmsConfigVO, now);
							}
						} catch (Exception e) {
							log.error("wms批次原箱库存核对,出错," + wmsConfigVO.getDesc() + "仓接口异常,{}", e.getMessage(),e);
						}
					}
					if (CollectionUtils.isNotEmpty(batchStockBoxDetailDOList)) {
						//插入原箱库存表
						wmsBatchBoxCheckFlowService.batchInsertBoxStock(batchStockBoxDetailDOList);
					}
					if (wmsBatchCheckFlows != null && wmsBatchCheckFlows.size() > 0) {
						checkBoxNotEqualsSkuCode(wmsBatchCheckFlows, skuInfoMap, notEqualsSkuCodeSet);
						wmsBatchCheckService.batchUpdateOrInsertBox(wmsBatchCheckFlows, queryWmsBatchBoxCheckBySync(realWarehouseE.getId()));
						countSuccess += wmsBatchCheckFlows.size();
					}
				}
				current++;
				PageHelper.startPage(current, pageSize, false);
				if(CollectionUtils.isNotEmpty(skuIdList)){
					list = realWarehouseStockMapper.queryStockByWhIdAndSkuIds(realWarehouseE.getId(),skuIdList);
				}else{
					list= realWarehouseStockMapper.querySkuIdByWhId(realWarehouseE.getId());
				}
			}
		} catch (RomeException e) {
			log.error("wms批次原箱库存核对,出错,总数={},有效总数={}:实仓id={}{}", count, countSuccess, realWarehouseE.getId(), wmsDesc, e);
			throw e;
		} catch (Exception e) {
			log.error("wms批次原箱库存核对,出错,总数={},有效总数={}:实仓id={}{}", count, countSuccess, realWarehouseE.getId(), wmsDesc, e);
			throw new RomeException(ResCode.STOCK_ERROR_1003, e.getMessage());
		} finally {
			log.info("wms批次原箱库存核对,成功,realWarehouseId={},总数={},有效总数={}", realWarehouseE.getId(), count, countSuccess);
			if (isLock) {
				redisUtil.unLock(cacheKeyEnum.getKeyLock() + realWarehouseE.getId(), "1");
			}
			// wms批次库存核对，不一致
			if (notEqualsSkuCodeSet.size() > 0) {
				log.error("wms批次原箱库存核对,不一致,【{}】,{},不一致sku个数：{},明细为:{}", wmsDesc, realWarehouseE.getRealWarehouseCode(), notEqualsSkuCodeSet.size(), JSON.toJSONString(notEqualsSkuCodeSet));
			}
		}
	}




	
	/**
	 * wms批次库存核对
	 */
	@Override
	public void wmsBatchCheckJob() {
		log.info("开始,wms批次库存核对");
		coreStockTask.submit(new Runnable() {
			@SuppressWarnings("rawtypes")
			@Override
			public void run() {
				// 实仓类型过滤
				final Set<Integer> filterTypeSet = new HashSet<>();
				// 实仓Id过滤
				final Set<Long> filterIdSet = new HashSet<>();
				// 初始化过滤条件
				BatchStockFacade.getAllowWarehouseTypeOrIds(filterTypeSet, filterIdSet);
				// 没有要处理的wms
				if(filterTypeSet.size() == 0 && filterIdSet.size() == 0) {
					return;
				}
				final Map<String, SkuInfoExtDTO> skuInfoMap = new ConcurrentHashMap<String, SkuInfoExtDTO>(4000);
				int current = 1;
				// 每页大小 
				final int pageSize = 1000;
				List<Future> futures = new ArrayList<>(100);
				Future future;
				List<Integer> wmsCodes = WarehouseWmsConfigEnum.getSupportWmsByAllType();
				List<RealWarehouseE> realWarehouseEs;
				PageHelper.startPage(current, pageSize, false);
				List<Long> list = realWarehouseWmsConfigRepository.getRealWarehouseWmsConfigByWmsCodes(wmsCodes);
				int i;
				while(list != null && list.size() > 0) {
					realWarehouseEs = realWarehouseRepository.queryWarehouseByIds(list);
					if(realWarehouseEs != null && realWarehouseEs.size() > 0) {
						for(RealWarehouseE realWarehouseE : realWarehouseEs) {
							// 过滤不需处理的
							if(!((realWarehouseE.getRealWarehouseType() != null && filterTypeSet.contains(realWarehouseE.getRealWarehouseType()))
									|| (filterIdSet.contains(realWarehouseE.getId())))) {
								continue;
							}
							// 门店仓，过滤不需处理的
							if(RealWarehouseTypeVO.RW_TYPE_1.getType().equals(realWarehouseE.getRealWarehouseType())) {
								continue;
							}
							future = coreStockTask.submit(new Runnable() {
								@Override
								public void run() {
									wmsBatchCheckByWarehouse(realWarehouseE, skuInfoMap);
								}});
							futures.add(future);
						}
					}
					current++;
					PageHelper.startPage(current, pageSize, false);
					list = realWarehouseWmsConfigRepository.getRealWarehouseWmsConfigByWmsCodes(wmsCodes);
					for(i = 0; i < futures.size(); i++) {
						if(futures.get(i).isDone()) {
							futures.remove(i);
							i--;
						}
					}
				}
				// 等待完成
				for(Future future2 : futures) {
					try {
						future2.get();
					} catch (Exception e) {
						log.error("wms批次库存核对,等队列完成,出错{}",e);
					}
				}
				futures.clear();
				futures = null;
				log.info("结束,wms批次库存核对");
			}
		});
	}
	
	/**
	 * wms批次库存核对-仓库
	 * 是不是要批次库存核对仓，上层方法验证
	 * @param realWarehouseE
	 */
	private void wmsBatchCheckByWarehouse(RealWarehouseE realWarehouseE, Map<String, SkuInfoExtDTO> skuInfoMap) {
		int count = 0;
		int countSuccess = 0;
		boolean isLock = false;
		RedisCacheKeyEnum cacheKeyEnum = RedisCacheKeyEnum.WMS_BATCH_CHECK;
		// 核对，不一致的skuCode
		Set<String> notEqualsSkuCodeSet = new HashSet<>(16);
		String wmsDesc = null;
		try {
			isLock = redisUtil.lock(cacheKeyEnum.getKeyLock() + realWarehouseE.getId(), "1", cacheKeyEnum.getExpireTimeLock());
			if(isLock == false) {
				throw new RomeException(ResCode.STOCK_ERROR_1001, "仓库正在同步中,factoryCode=" + realWarehouseE.getFactoryCode() + ",outCode=" + realWarehouseE.getRealWarehouseOutCode()); 
			}
			Integer wmsCode = realWarehouseWmsConfigRepository.queryWmsConfigById(realWarehouseE.getId());
			if (wmsCode == null){
	            log.error("wms批次库存核对,出错,暂未查询到实仓与wms的配置信息,实仓id="+ realWarehouseE.getId());
	        }
			if(WarehouseWmsConfigEnum.getSupportWmsByType(wmsCode) == null) {
				throw new RomeException(ResCode.STOCK_ERROR_1003, "暂不支持此仓库批次核对仓id=" + realWarehouseE.getId());	
			}
			wmsDesc = WarehouseWmsConfigEnum.getSupportWmsByType(wmsCode).getDesc();
			int current = 1;
			// 每页大小 
			final int pageSize = 1000;
			List<String> skuCodes = new ArrayList<>(pageSize);
			List<Long> skuIds = new ArrayList<>(pageSize);
			PageHelper.startPage(current, pageSize, false);
			// 查询库存
			List<RealWarehouseStockDO> list = realWarehouseStockMapper.querySkuIdByWhId(realWarehouseE.getId());
			List<StockQueryResponseItem> stockQueryItems;
			String type = null;
			Date now = new Date();
			List<BatchStockDO> batchStockDOs;
			while(list != null && list.size() > 0) {
				if(type == null) {
					type = "A";
				}else {
					type = "I";
				}
				count += list.size();
				skuCodes.clear();
				skuIds.clear();
				for(RealWarehouseStockDO dto : list) {
					if(dto.getSkuId() != null) {
						skuIds.add(dto.getSkuId());
					}
				}
				// 查询批次库存
				batchStockDOs = batchStockMapper.queryBatchStocksBySkuIdBatchCodeSumNum(skuIds, realWarehouseE.getId());
				for(RealWarehouseStockDO dto : list) {
					if(dto.getSkuCode() != null) {
						skuCodes.add(dto.getSkuCode());
					}
				}
				// 获取wms的库存
				if(skuCodes.size() > 0) {
					// 大幅仓
					List<WmsBatchCheckFlowDO> wmsBatchCheckFlows = null;
					// 获取支持的wms
					WarehouseWmsConfigEnum wmsConfigVO = WarehouseWmsConfigEnum.getSupportWmsByType(wmsCode);
					if(wmsConfigVO != null) {
						wmsBatchCheckFlows = new ArrayList<>(skuCodes.size());
						try {
							stockQueryItems = StockToolFacade.stockQuery(realWarehouseE, type, skuInfoMap, wmsConfigVO.getType() + "", skuCodes, true, 1);
							mergeToWmsBatchCheckFlowE(list, batchStockDOs, stockQueryItems, wmsBatchCheckFlows, wmsConfigVO, now);
						} catch (Exception e) {
							log.error("wms批次库存核对,出错," + wmsConfigVO.getDesc() + "仓接口异常,{}", e);
						}
					}
					if(wmsBatchCheckFlows != null && wmsBatchCheckFlows.size() > 0) {
						checkNotEqualsSkuCode(wmsBatchCheckFlows, skuInfoMap, notEqualsSkuCodeSet);
						WmsBatchCheckService wmsBatchCheckService = SpringBeanUtil.getBean(WmsBatchCheckService.class);
						wmsBatchCheckService.batchUpdateOrInsert(wmsBatchCheckFlows, queryWmsBatchCheckBySync(realWarehouseE.getId()));
						countSuccess += wmsBatchCheckFlows.size();
					}
				}
				current++;
				PageHelper.startPage(current, pageSize, false);
				list = realWarehouseStockMapper.querySkuIdByWhId(realWarehouseE.getId());
			}
		} catch (RomeException e) {
			log.error("wms批次库存核对,出错,总数={},有效总数={}:实仓id={}{}",count, countSuccess, realWarehouseE.getId(), wmsDesc,e);
			throw e;
		}catch (Exception e) {
			log.error("wms批次库存核对,出错,总数={},有效总数={}:实仓id={}{}",count, countSuccess, realWarehouseE.getId(), wmsDesc, e);
			throw new RomeException(ResCode.STOCK_ERROR_1003, e.getMessage());
		} finally {
			log.info("wms批次库存核对,成功,realWarehouseId={},总数={},有效总数={}", realWarehouseE.getId(), count, countSuccess);
			if(isLock) {
				redisUtil.unLock(cacheKeyEnum.getKeyLock() + realWarehouseE.getId(), "1");
			}
			// wms批次库存核对，不一致
			if(notEqualsSkuCodeSet.size() > 0) {
				log.warn("wms批次库存核对,不一致,【{}】,{},不一致sku个数：{},明细为:{}", wmsDesc, realWarehouseE.getRealWarehouseCode(), notEqualsSkuCodeSet.size(), JSON.toJSONString(notEqualsSkuCodeSet));
			}
		}
	}
	
	/**
	 * 合并处理
	 * @param list
	 * @param stockQueryItems
	 * @param wmsBatchCheckFlowEs
	 * @param wmsConfigVO
	 * @param now
	 */
	private void mergeToWmsBatchCheckFlowE(List<RealWarehouseStockDO> list, List<BatchStockDO> batchStockDOs, List<StockQueryResponseItem> stockQueryItems, List<WmsBatchCheckFlowDO> wmsBatchCheckFlowEs, WarehouseWmsConfigEnum wmsConfigVO, Date now) {
		// 先以中台批次处理
		if(batchStockDOs != null && batchStockDOs.size() > 0) {
			StockQueryResponseItem wmsDto, wmsDto2 = null;
			for(BatchStockDO dto : batchStockDOs) {
				wmsDto = getWmsBatchStockByBatchSkuCode(stockQueryItems, dto.getSkuCode(), dto.getBatchCode());
				if(wmsDto == null) {
					if(wmsDto2 == null) {
						wmsDto2 = new StockQueryResponseItem();
					}
					wmsDto = wmsDto2;
				}
				wmsBatchCheckFlowEs.add(toWmsBatchCheckFlowE(dto, wmsDto, wmsConfigVO, now));
			}
		}
		// 以wms批次处理
		if(stockQueryItems != null && stockQueryItems.size() > 0) {
			BatchStockDO ztDto, wmsDto2 = null;
			RealWarehouseStockDO stockDTO;
			for(StockQueryResponseItem dto : stockQueryItems) {
				ztDto = getBatchStockByBatchSkuCode(batchStockDOs, dto.getItemCode(), dto.getBatchCode());
				if(ztDto == null) {
					if(wmsDto2 == null) {
						wmsDto2 = new BatchStockDO();
					}
					ztDto = wmsDto2;
					stockDTO = getStockBySkuCode(list, dto.getItemCode());
					ztDto.setRealWarehouseId(stockDTO.getRealWarehouseId());
					ztDto.setSkuId(stockDTO.getSkuId());
					ztDto.setSkuCode(stockDTO.getSkuCode());
					ztDto.setBatchCode(dto.getBatchCode());
					ztDto.setSkuQty(BigDecimal.ZERO);
					wmsBatchCheckFlowEs.add(toWmsBatchCheckFlowE(ztDto, dto, wmsConfigVO, now));
				}
			}
		}
	}





	/**
	 * 合并处理
	 * @param list
	 * @param stockQueryItems
	 * @param wmsBatchCheckFlowEs
	 * @param wmsConfigVO
	 * @param now
	 */
	private void mergeToWmsBatchBoxCheckFlowE(List<RealWarehouseStockDO> list, List<BatchStockBoxDetailDO> stockBoxDetailDOS, List<StockQueryResponseItem> stockQueryItems
			, List<WmsBatchBoxCheckFlowDO> wmsBatchCheckFlowEs, WarehouseWmsConfigEnum wmsConfigVO, Date now) {
		// 先以中台批次处理
		if(stockBoxDetailDOS != null && stockBoxDetailDOS.size() > 0) {
			StockQueryResponseItem wmsDto, wmsDto2 = null;
			for(BatchStockBoxDetailDO dto : stockBoxDetailDOS) {
				wmsDto = getWmsBatchStockByBatchSkuCode(stockQueryItems, dto.getSkuCode(), dto.getBatchCode());
				if(wmsDto == null) {
					if(wmsDto2 == null) {
						wmsDto2 = new StockQueryResponseItem();
					}
					wmsDto = wmsDto2;
					wmsDto.setBoxRealQty(BigDecimal.ZERO);
					wmsDto.setMixRealQty(BigDecimal.ZERO);
				}
				wmsBatchCheckFlowEs.add(toWmsBatchBoxCheckFlowE(dto, wmsDto, wmsConfigVO, now));
			}
		}
		// 以wms批次处理
		if(stockQueryItems != null && stockQueryItems.size() > 0) {
			BatchStockBoxDetailDO ztDto, wmsDto2 = null;
			RealWarehouseStockDO stockDTO;
			for(StockQueryResponseItem dto : stockQueryItems) {
				ztDto = getBatchStockByBatchBoxSkuCode(stockBoxDetailDOS, dto.getItemCode(), dto.getBatchCode());
				if(ztDto == null) {
					if(wmsDto2 == null) {
						wmsDto2 = new BatchStockBoxDetailDO();
					}
					ztDto = wmsDto2;
					stockDTO = getStockBySkuCode(list, dto.getItemCode());
					ztDto.setRealWarehouseId(stockDTO.getRealWarehouseId());
					ztDto.setSkuId(stockDTO.getSkuId());
					ztDto.setSkuCode(stockDTO.getSkuCode());
					ztDto.setBatchCode(dto.getBatchCode());
					ztDto.setBoxSkuQty(BigDecimal.ZERO);
					ztDto.setMixSkuQty(BigDecimal.ZERO);
					wmsBatchCheckFlowEs.add(toWmsBatchBoxCheckFlowE(ztDto, dto, wmsConfigVO, now));
				}
			}
		}
	}


	/**
	 * 初始化原箱库存
	 * @param stockQueryItems
	 * @param batchStockDOs
	 * @param batchStockBoxDetailDOList
	 */
	private void initBatchBoxStock(List<StockQueryResponseItem> stockQueryItems
			,List<BatchStockDO> batchStockDOs,List<BatchStockBoxDetailDO> batchStockBoxDetailDOList) {
		// 先以中台批次处理
		if(batchStockDOs != null && batchStockDOs.size() > 0) {
			StockQueryResponseItem wmsDto;
			for(BatchStockDO dto : batchStockDOs) {
				wmsDto = getWmsBatchStockByBatchSkuCode(stockQueryItems, dto.getSkuCode(), dto.getBatchCode());
				batchStockBoxDetailDOList.add(toBatchBoxStock(dto, wmsDto));
			}
		}
	}

	/**
	 * 获取库存，根据skuCode
	 * @param list
	 * @param skuCode
	 * @return
	 */
	private RealWarehouseStockDO getStockBySkuCode(List<RealWarehouseStockDO> list, String skuCode) {
		if(list != null && list.size() > 0) {
			for(RealWarehouseStockDO dto : list) {
				if(dto.getSkuCode().equals(skuCode)) {
					return dto;
				}
			}
		}
		return null;
	}
	
	/**
	 * 获取wms批次，根据skuCode,批次号
	 * @param list
	 * @param skuCode
	 * @param batchCode
	 * @return
	 */
	private StockQueryResponseItem getWmsBatchStockByBatchSkuCode(List<StockQueryResponseItem> list, String skuCode, String batchCode) {
		if(list != null && list.size() > 0) {
			for(StockQueryResponseItem dto : list) {
				if(skuCode.equals(dto.getItemCode()) && batchCode.equals(dto.getBatchCode())) {
					return dto;
				}
			}
		}
		return null;
	}
	
	/**
	 * 获取批次，根据skuCode,批次号
	 * @param list
	 * @param skuCode
	 * @param batchCode
	 * @return
	 */
	private BatchStockDO getBatchStockByBatchSkuCode(List<BatchStockDO> list, String skuCode, String batchCode) {
		if(list != null && list.size() > 0) {
			for(BatchStockDO dto : list) {
				if(skuCode.equals(dto.getSkuCode()) && batchCode.equals(dto.getBatchCode())) {
					return dto;
				}
			}
		}
		return null;
	}

	/**
	 * 获取批次，根据skuCode,批次号
	 * @param list
	 * @param skuCode
	 * @param batchCode
	 * @return
	 */
	private BatchStockBoxDetailDO getBatchStockByBatchBoxSkuCode(List<BatchStockBoxDetailDO> list, String skuCode, String batchCode) {
		if(list != null && list.size() > 0) {
			for(BatchStockBoxDetailDO dto : list) {
				if(skuCode.equals(dto.getSkuCode()) && batchCode.equals(dto.getBatchCode())) {
					return dto;
				}
			}
		}
		return null;
	}

	/**
	 * toEntity
	 * @param stockDTO
	 * @param item
	 * @return
	 */
	private WmsBatchCheckFlowDO toWmsBatchCheckFlowE(BatchStockDO stockDTO, StockQueryResponseItem item, WarehouseWmsConfigEnum wmsConfigVO, Date now) {
		if(item.getRealQty() == null) {
			item.setRealQty(BigDecimal.ZERO);
		}
		WmsBatchCheckFlowDO checkFlowE = new WmsBatchCheckFlowDO();
		checkFlowE.setRealWarehouseId(stockDTO.getRealWarehouseId());
		checkFlowE.setWmsCode(wmsConfigVO.getType());
		checkFlowE.setSkuId(stockDTO.getSkuId());
		checkFlowE.setSkuCode(stockDTO.getSkuCode());
		checkFlowE.setBatchCode(stockDTO.getBatchCode());
		checkFlowE.setRealQty(stockDTO.getSkuQty());
		checkFlowE.setWmsRealQty(item.getRealQty());
		checkFlowE.setSourceSystem(wmsConfigVO.getDesc());
		checkFlowE.setCreateTime(now);
		return checkFlowE;
	}


	/**
	 * toEntity
	 * @param stockDTO
	 * @param item
	 * @return
	 */
	private WmsBatchBoxCheckFlowDO toWmsBatchBoxCheckFlowE(BatchStockBoxDetailDO stockDTO, StockQueryResponseItem item, WarehouseWmsConfigEnum wmsConfigVO, Date now) {
		if(item.getRealQty() == null) {
			item.setRealQty(BigDecimal.ZERO);
		}
		WmsBatchBoxCheckFlowDO checkFlowE = new WmsBatchBoxCheckFlowDO();
		checkFlowE.setRealWarehouseId(stockDTO.getRealWarehouseId());
		checkFlowE.setWmsCode(wmsConfigVO.getType());
		checkFlowE.setSkuId(stockDTO.getSkuId());
		checkFlowE.setSkuCode(stockDTO.getSkuCode());
		checkFlowE.setBatchCode(stockDTO.getBatchCode());
		checkFlowE.setBoxRealQty(stockDTO.getBoxSkuQty());
		checkFlowE.setWmsBoxRealQty(item.getBoxRealQty());
		checkFlowE.setSourceSystem(wmsConfigVO.getDesc());
		checkFlowE.setCreateTime(now);
		checkFlowE.setMixRealQty(stockDTO.getMixSkuQty());
		checkFlowE.setWmsMixRealQty(item.getMixRealQty());
		return checkFlowE;
	}

	/**
	 * toEntity
	 * @param item
	 * @return
	 */
	private BatchStockBoxDetailDO toBatchBoxStock(BatchStockDO batchStockDO, StockQueryResponseItem item) {
		BatchStockBoxDetailDO batchBoxDo = new BatchStockBoxDetailDO();
		batchBoxDo.setRealWarehouseId(batchStockDO.getRealWarehouseId());
		batchBoxDo.setSkuId(batchStockDO.getSkuId());
		batchBoxDo.setSkuCode(batchStockDO.getSkuCode());
		batchBoxDo.setBatchCode(batchStockDO.getBatchCode());
		batchBoxDo.setBoxSkuQty(BigDecimal.ZERO);
		batchBoxDo.setMixSkuQty(BigDecimal.ZERO);
		batchBoxDo.setMixLockQty(BigDecimal.ZERO);
		batchBoxDo.setBoxLockQty(BigDecimal.ZERO);
		if (Objects.nonNull(item) && Objects.nonNull(item.getBoxRealQty())) {
			//wms存在原箱库存
			batchBoxDo.setBoxSkuQty(item.getBoxRealQty());
			batchBoxDo.setMixSkuQty(item.getMixRealQty());
		}else{
			batchBoxDo.setMixSkuQty(batchStockDO.getSkuQty());
		}
		//默认将现有批次锁定库存,赋值给非原箱锁定
		batchBoxDo.setMixLockQty(batchStockDO.getLockQty());
		if (Objects.nonNull(batchStockDO.getProductDate())) {
			batchBoxDo.setProductDate(batchStockDO.getProductDate());
		}
		return batchBoxDo;
	}


	/**
	 * 查询，同步时
	 * @param realWarehouseId
	 * @return
	 */
	@SuppressWarnings("unchecked")
	private List<WmsBatchCheckFlowDO> queryWmsBatchCheckBySync(Long realWarehouseId) {
		List<WmsBatchCheckFlowDO> list = wmsBatchCheckFlowMapper.queryWmsBatchCheckBySync(getQueryParamSync(realWarehouseId));
		if(list == null || list.size() == 0) {
			return Collections.EMPTY_LIST;
		}
		return list;
	}
	/**
	 * 查询，同步时
	 * @param realWarehouseId
	 * @return
	 */
	@SuppressWarnings("unchecked")
	private List<WmsBatchBoxCheckFlowDO> queryWmsBatchBoxCheckBySync(Long realWarehouseId) {
		List<WmsBatchBoxCheckFlowDO> list = wmsBatchBoxCheckFlowMapper.queryWmsBatchBoxCheckBySync(getQueryParamSync(realWarehouseId));
		if(list == null || list.size() == 0) {
			return Collections.EMPTY_LIST;
		}
		return list;
	}

	/**
	 * 同步时，获取请求参数
	 * @param realWarehouseId
	 * @return
	 */
	private WmsBatchCheckFlowDO getQueryParamSync(Long realWarehouseId) {
		try {
			WmsBatchCheckFlowDO flowE = new WmsBatchCheckFlowDO();
			//设置查询时间,若时间为空,默认采用当前时间
	        Date startTime;
	        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
	        String s = sdf.format(new Date());
	        startTime =  sdf.parse(s);
			Date endTime = DateUtils.addDays(startTime, 1);
			flowE.setStartTime(startTime);
			flowE.setEndTime(endTime);
			List<Long> queryWarehouseIds = new ArrayList<>(1);
			queryWarehouseIds.add(realWarehouseId);
			flowE.setRealWarehouseIds(queryWarehouseIds);
			return flowE;
		} catch (Exception e) {
			throw new RomeException(ResCode.STOCK_ERROR_1003, e.getMessage());
		}
	}
	
	@Override
	@Transactional(rollbackFor = Exception.class)
	public void batchUpdateOrInsert(List<WmsBatchCheckFlowDO> checkFlowEs, List<WmsBatchCheckFlowDO> existList) {
		List<WmsBatchCheckFlowDO> updateList = new ArrayList<WmsBatchCheckFlowDO>();
		WmsBatchCheckFlowDO temp,temp2;
		for (int i = checkFlowEs.size() - 1; i >= 0; i--) {
			temp2 = checkFlowEs.get(i);
			temp = getWmsBatchCheckFlowByBatchSkuId(existList, temp2.getSkuId(), temp2.getBatchCode()); 
			if(temp != null) {
				temp2.setId(temp.getId());
				updateList.add(temp2);
				checkFlowEs.remove(i);
			}
		}
		if(updateList.size() > 0) {
			wmsBatchCheckFlowMapper.batchUpdateBySync(updateList);
		}
        if(checkFlowEs.size() > 0) {
        	wmsBatchCheckFlowMapper.batchInsert(checkFlowEs);
		}
	}


	@Override
//	@Transactional(rollbackFor = Exception.class)
	public void batchUpdateOrInsertBox(List<WmsBatchBoxCheckFlowDO> checkFlowEs, List<WmsBatchBoxCheckFlowDO> existList) {
		List<WmsBatchBoxCheckFlowDO> updateList = new ArrayList<>();
		WmsBatchBoxCheckFlowDO temp,temp2;
		for (int i = checkFlowEs.size() - 1; i >= 0; i--) {
			temp2 = checkFlowEs.get(i);
			temp = getWmsBatchCheckBoxFlowByBatchSkuId(existList, temp2.getSkuId(), temp2.getBatchCode());
			if(temp != null) {
				temp2.setId(temp.getId());
				updateList.add(temp2);
				checkFlowEs.remove(i);
			}
		}
		if(updateList.size() > 0) {
			wmsBatchBoxCheckFlowMapper.batchUpdateBySync(updateList);
		}
		if(checkFlowEs.size() > 0) {
			wmsBatchBoxCheckFlowMapper.batchInsert(checkFlowEs);
		}
	}

	/**
	 * 获取批次，根据skuId,批次号
	 * @param list
	 * @param skuId
	 * @param batchCode
	 * @return
	 */
	private WmsBatchCheckFlowDO getWmsBatchCheckFlowByBatchSkuId(List<WmsBatchCheckFlowDO> list, Long skuId, String batchCode) {
		if(list != null && list.size() > 0) {
			for(WmsBatchCheckFlowDO dto : list) {
				if(skuId.equals(dto.getSkuId()) && batchCode.equals(dto.getBatchCode())) {
					return dto;
				}
			}
		}
		return null;
	}
	/**
	 * 获取批次，根据skuId,批次号
	 * @param list
	 * @param skuId
	 * @param batchCode
	 * @return
	 */
	private WmsBatchBoxCheckFlowDO getWmsBatchCheckBoxFlowByBatchSkuId(List<WmsBatchBoxCheckFlowDO> list, Long skuId, String batchCode) {
		if(list != null && list.size() > 0) {
			for(WmsBatchBoxCheckFlowDO dto : list) {
				if(skuId.equals(dto.getSkuId()) && batchCode.equals(dto.getBatchCode())) {
					return dto;
				}
			}
		}
		return null;
	}

	/**
	 * 根据仓库Id重新同步wms批次库存核对表,一般是手动
	 * @param paramDto
	 * @return
	 */
	@Override
	@SuppressWarnings("rawtypes")
	public void wmsBatchCheckSync(WmsBatchCheckFlowDTO paramDto) {
		List<RealWarehouseWmsConfigDO> configDOs = realWarehouseWmsConfigRepository.getRealWarehouseWmsConfigByVMId(paramDto.getRealWarehouseId());
		if(configDOs != null && configDOs.size() > 0) {
			RealWarehouseE realWarehouseE = realWarehouseRepository.getRealWarehouseById(paramDto.getRealWarehouseId());
			// 实仓类型过滤
			final Set<Integer> filterTypeSet = new HashSet<>();
			// 实仓Id过滤
			final Set<Long> filterIdSet = new HashSet<>();
			// 初始化过滤条件
			BatchStockFacade.getAllowWarehouseTypeOrIds(filterTypeSet, filterIdSet);
			// 没有要处理的wms
			if(filterTypeSet.size() == 0 && filterIdSet.size() == 0) {
				throw new RomeException(ResCode.STOCK_ERROR_1003, "没有配置仓库批次库存核对");
			}
			// 过滤不需处理的
			if(!((realWarehouseE.getRealWarehouseType() != null && filterTypeSet.contains(realWarehouseE.getRealWarehouseType()))
					|| (filterIdSet.contains(realWarehouseE.getId())))) {
				throw new RomeException(ResCode.STOCK_ERROR_1003, "仓库不在批次库存核对范围内");
			}
			// 门店仓，过滤不需处理的
			if(RealWarehouseTypeVO.RW_TYPE_1.getType().equals(realWarehouseE.getRealWarehouseType())) {
				throw new RomeException(ResCode.STOCK_ERROR_1003, "门店仓库不在批次库存初始化范围内");
			}
			
			Future future = coreStockTask.submit(new Runnable() {
				@Override
				public void run() {
					final Map<String, SkuInfoExtDTO> skuInfoMap = new ConcurrentHashMap<String, SkuInfoExtDTO>(4000);
					wmsBatchCheckByWarehouse(realWarehouseE, skuInfoMap);
				}});
			StockCacheKeyEnum cacheKeyEnum = StockCacheKeyEnum.WMS_STOCK_CHECK;
			try {
				future.get(cacheKeyEnum.getExpireTime(), TimeUnit.SECONDS);
			} catch (InterruptedException e) {
				throw new RomeException(ResCode.STOCK_ERROR_1003, e.getMessage());
			} catch (ExecutionException e) {
				throw new RomeException(ResCode.STOCK_ERROR_1003, (e.getMessage() + "").replaceAll("com.rome.arch.core.exception.RomeException:", ""));
			} catch (TimeoutException e) {
				throw new RomeException(ResCode.STOCK_ERROR_1003, "已加入同步队列中，并且正在同时中，请稍后重新查询");
			}
		}else {
			throw new RomeException(ResCode.STOCK_ERROR_1003, "仓库不在配置表");
		}
	}
	
	/**
	 * 根据条件查询wms库存核对表
	 * @param paramDto
	 * @return
	 */
	@SuppressWarnings("rawtypes")
	@Override
	public PageInfo<WmsBatchCheckFlowDTO> queryWmsBatchCheckByCondition(WmsBatchCheckFlowDTO paramDto) throws Exception{
		WmsBatchCheckFlowDO flowE = esWmsBatchCheckFlowConvertor.dtoToEntity(paramDto);

		//设置查询时间,若时间为空,默认采用当前时间
        Date startTime;
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
        if(null != flowE.getCreateTime()){
            startTime = flowE.getCreateTime();
			String s = sdf.format(startTime);
			startTime =  sdf.parse(s);
        }else {
            String s = sdf.format(new Date());
            startTime =  sdf.parse(s);
        }

		Date endTime = DateUtils.addDays(startTime, 1);
		flowE.setStartTime(startTime);
		flowE.setEndTime(endTime);

		List<Long> queryWarehouseIds = new ArrayList<>();
		if(null != paramDto.getRealWarehouseId()){
            queryWarehouseIds.add(paramDto.getRealWarehouseId());
        }else {
        	throw new RomeException(ResCode.STOCK_ERROR_1002, "仓库不能为空");
        }
		flowE.setRealWarehouseIds(queryWarehouseIds);

		//开启分页
		Page page = PageHelper.startPage(paramDto.getPageIndex(), paramDto.getPageSize());
		List<WmsBatchCheckFlowDO> wmsBatchCheckFlowES = wmsBatchCheckFlowMapper.queryWmsBatchCheckByCondition(flowE);
		List<WmsBatchCheckFlowDTO> wmsBatchCheckFlowDTOS = null;
		if(wmsBatchCheckFlowES != null && wmsBatchCheckFlowES.size() > 0) {
			wmsBatchCheckFlowDTOS = esWmsBatchCheckFlowConvertor.entityToDto(wmsBatchCheckFlowES);
		}else {
			// 没有数据时，去查历史数据
			page.setPageNum(paramDto.getPageIndex());
			page.setPageSize(paramDto.getPageSize());
			wmsBatchCheckFlowDTOS = queryWmsBatchCheckByConditionHistory(flowE, page);
		}

		//根据仓库id查询仓库信息
		List<Long> warehouseIds = null;
		if(wmsBatchCheckFlowDTOS != null) {
			warehouseIds = new ArrayList<>();
			for(WmsBatchCheckFlowDTO dto : wmsBatchCheckFlowDTOS) {
				if(dto.getRealWarehouseId() != null) {
					if(!warehouseIds.contains(dto.getRealWarehouseId())) {
						warehouseIds.add(dto.getRealWarehouseId());
					}
				}
			}
		}
		if(warehouseIds != null && warehouseIds.size() > 0) {
			List<RealWarehouseE> warehouseES = realWarehouseRepository.queryWarehouseByIds(warehouseIds);
			Map<Long, RealWarehouseE> warehouseEMap = RomeCollectionUtil.listforMap(warehouseES, "id");
			//遍历查询结果,设置仓库名称等
			for (WmsBatchCheckFlowDTO flowDTO : wmsBatchCheckFlowDTOS) {
				if(warehouseEMap.containsKey(flowDTO.getRealWarehouseId())){
					RealWarehouseE realWarehouseE = warehouseEMap.get(flowDTO.getRealWarehouseId());
					flowDTO.setFactoryCode(realWarehouseE.getFactoryCode());
					flowDTO.setRealWarehouseCode(realWarehouseE.getRealWarehouseCode());
					flowDTO.setRealWarehouseName(realWarehouseE.getRealWarehouseName());
				}
			}
		}
		
		List<String> skuCodes = wmsBatchCheckFlowDTOS.stream().filter(v -> StringUtils.isNotBlank(v.getSkuCode())).map(WmsBatchCheckFlowDTO :: getSkuCode).distinct().collect(Collectors.toList());
		//根据skuCodes获取商品信息
		List<SkuInfoExtDTO> skuInfoExtDTOList = skuFacade.skusBySkuCode(skuCodes);
		Map<String, SkuInfoExtDTO> skuInfoExtDTOMap = skuInfoExtDTOList.stream().distinct().collect(Collectors.toMap(SkuInfoExtDTO :: getSkuCode, Function.identity(), (v1, v2) -> v1));
		wmsBatchCheckFlowDTOS.forEach(item -> {
			if(skuInfoExtDTOMap.containsKey(item.getSkuCode())) {
				item.setSkuName(skuInfoExtDTOMap.get(item.getSkuCode()).getName());
				item.setUnit(skuInfoExtDTOMap.get(item.getSkuCode()).getSpuUnitName());
			}
		});
		
		PageInfo<WmsBatchCheckFlowDTO> pageList = new PageInfo<>(wmsBatchCheckFlowDTOS);
		pageList.setTotal(page.getTotal());
		if(0 == page.getTotal()) {
			return pageList;
		}
		return pageList;
	}
	
	/**
	 * 历史数据查询
	 * @param paramDTO
	 * @param page
	 * @return
	 */
	@SuppressWarnings({ "unchecked", "rawtypes" })
	private List<WmsBatchCheckFlowDTO> queryWmsBatchCheckByConditionHistory(WmsBatchCheckFlowDO paramDTO, Page page) {
		EsIndexTypeConfig.setIndexNameSuffixByYear("1", paramDTO.getStartTime());
		try {
			// 历史数据迁移时间
			Integer dbSaveDay = SpringBeanUtil.getBean(WmsBatchCheckFlowInactiveTemplate.class).getDbSaveDay();
			if(dbSaveDay == null) {
				dbSaveDay = 30;
			}
			Calendar cal = Calendar.getInstance();
			cal.set(Calendar.HOUR_OF_DAY, 0);
			cal.set(Calendar.MINUTE, 0);
			cal.set(Calendar.SECOND, 0);
			cal.set(Calendar.MILLISECOND, 0);
			cal.add(Calendar.DAY_OF_MONTH,-dbSaveDay);
			Date time = cal.getTime();
			// 历史数据迁移时间,还未迁移
			if(time.getTime() < paramDTO.getEndTime().getTime()) {
				return Collections.EMPTY_LIST;
			}
			NativeSearchQuery searchQuery = queryWmsBatchCheckByConditionHistoryParam(paramDTO, page);
			org.springframework.data.domain.Page<EsWmsBatchCheckFlowDO> resultPage = esWmsBatchCheckFlowRepository.search(searchQuery);
			if(resultPage == null || resultPage.getContent() == null || resultPage.getContent().size() == 0) {
				return Collections.EMPTY_LIST;
			}
			List<WmsBatchCheckFlowDTO> result = esWmsBatchCheckFlowConvertor.esWmsBatchCheckFlowDOEntityToDTOList(resultPage.getContent());
			page.setTotal(resultPage.getTotalElements());
			return result;
		} catch (IndexNotFoundException e) {
			log.error("查询历史数据", e);
			return Collections.EMPTY_LIST;
		} finally {
			EsIndexTypeConfig.clearIndexNameSuffix();
		}

	}

	/**
     * 获取请求参数
     * 历史数据
     */
    @SuppressWarnings("rawtypes")
	private NativeSearchQuery queryWmsBatchCheckByConditionHistoryParam(WmsBatchCheckFlowDO condition, Page page){
    	BoolQueryBuilder boolQueryBuilder = QueryBuilders.boolQuery();
        // 条件 仓库编码
        boolQueryBuilder.must(QueryBuilders.termsQuery("realWarehouseId", condition.getRealWarehouseIds()));
        // 条件 类型 0全部相同,1不相同,null为全部数据
        if (condition.getType() != null) {
        	if(condition.getType().intValue() == 0) {
        		boolQueryBuilder.must(QueryBuilders.termQuery("flag", 0));
        	}else {
        		boolQueryBuilder.mustNot(QueryBuilders.termQuery("flag", 0));
        	}
        }
        // 条件 skuId
        if(condition.getSkuIds() != null && condition.getSkuIds().size() > 0) {
        	boolQueryBuilder.must(QueryBuilders.termsQuery("skuId", condition.getSkuIds()));
        }
		//时间范围的设定
		if(condition.getStartTime() != null || condition.getEndTime() != null) {
			RangeQueryBuilder rangeQueryBuilder = QueryBuilders.rangeQuery("createTime");
			if(condition.getStartTime() != null) {
				rangeQueryBuilder.from(condition.getStartTime().getTime());
			}
			if(condition.getEndTime() != null) {
				rangeQueryBuilder.to(condition.getEndTime().getTime());
			}
			boolQueryBuilder.must(rangeQueryBuilder);
		}
		Pageable pageable = PageRequest.of(page.getPageNum() - 1, page.getPageSize() > 100000 ? 100000 : page.getPageSize());
		NativeSearchQueryBuilder builder = new NativeSearchQueryBuilder();
		builder.withQuery(boolQueryBuilder);
		builder.withPageable(pageable);
		builder.withSort(SortBuilders.fieldSort("dbId").order(SortOrder.ASC));
		builder.withSearchType(SearchType.QUERY_THEN_FETCH);
		NativeSearchQuery query = builder.build();
		query.setTrackTotalHits(true);
		return query;
    }
    
    /**
	 * 根据仓库Id,根据wms批次库存初始化批次库存工具
	 * @param paramDto
	 * @return
	 */
	@Override
	@SuppressWarnings("rawtypes")
	public void wmsBatchInitToolSync(WmsBatchCheckFlowDTO paramDto) {
		List<RealWarehouseWmsConfigDO> configDOs = realWarehouseWmsConfigRepository.getRealWarehouseWmsConfigByVMId(paramDto.getRealWarehouseId());
		if(configDOs != null && configDOs.size() > 0) {
			RealWarehouseE realWarehouseE = realWarehouseRepository.getRealWarehouseById(paramDto.getRealWarehouseId());
			// 实仓类型过滤
			final Set<Integer> filterTypeSet = new HashSet<>();
			// 实仓Id过滤
			final Set<Long> filterIdSet = new HashSet<>();
			// 先清缓存
			BaseinfoConfiguration.getInstance().clearCache("stock.batchStock");
			// 初始化过滤条件
			BatchStockFacade.getAllowWarehouseTypeOrIds(filterTypeSet, filterIdSet);
			// 没有要处理的wms
			if(filterTypeSet.size() == 0 && filterIdSet.size() == 0) {
				throw new RomeException(ResCode.STOCK_ERROR_1003, "没有配置仓库批次库存初始化");
			}
			// 过滤不需处理的
			if(!((realWarehouseE.getRealWarehouseType() != null && filterTypeSet.contains(realWarehouseE.getRealWarehouseType()))
					|| (filterIdSet.contains(realWarehouseE.getId())))) {
				throw new RomeException(ResCode.STOCK_ERROR_1003, "仓库不在批次库存初始化范围内");
			}
			// 门店仓，过滤不需处理的
			if(RealWarehouseTypeVO.RW_TYPE_1.getType().equals(realWarehouseE.getRealWarehouseType())) {
				throw new RomeException(ResCode.STOCK_ERROR_1003, "门店仓库不在批次库存初始化范围内");
			}
			
			Future future = coreStockTask.submit(new Runnable() {
				@Override
				public void run() {
					final Map<String, SkuInfoExtDTO> skuInfoMap = new ConcurrentHashMap<String, SkuInfoExtDTO>(4000);
					wmsBatchInitToolByWarehouse(realWarehouseE, paramDto.getSkuIds(), skuInfoMap);
				}});
			StockCacheKeyEnum cacheKeyEnum = StockCacheKeyEnum.WMS_STOCK_CHECK;
			try {
				future.get(cacheKeyEnum.getExpireTime(), TimeUnit.SECONDS);
			} catch (InterruptedException e) {
				throw new RomeException(ResCode.STOCK_ERROR_1003, e.getMessage());
			} catch (ExecutionException e) {
				throw new RomeException(ResCode.STOCK_ERROR_1003, (e.getMessage() + "").replaceAll("com.rome.arch.core.exception.RomeException:", ""));
			} catch (TimeoutException e) {
				throw new RomeException(ResCode.STOCK_ERROR_10001, "已加入队列中，并且正在中，请稍后重试");
			}
		}else {
			throw new RomeException(ResCode.STOCK_ERROR_1003, "仓库不在配置表");
		}
	}

	@Override
	public PageInfo<WmsBatchBoxCheckFlowDTO> queryWmsBatchBoxCheckByCondition(WmsBatchBoxCheckFlowDTO paramDto) throws Exception {
		//设置查询时间,若时间为空,默认采用当前时间
		Date startTime;
		SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
		if(null != paramDto.getCreateTime()){
			startTime = paramDto.getCreateTime();
			String s = sdf.format(startTime);
			startTime =  sdf.parse(s);
		}else {
			String s = sdf.format(new Date());
			startTime =  sdf.parse(s);
		}

		Date endTime = DateUtils.addDays(startTime, 1);
		paramDto.setStartTime(startTime);
		paramDto.setEndTime(endTime);

		List<Long> queryWarehouseIds = new ArrayList<>();
		if(null != paramDto.getRealWarehouseId()){
			queryWarehouseIds.add(paramDto.getRealWarehouseId());
		}else {
			throw new RomeException(ResCode.STOCK_ERROR_1002, "仓库不能为空");
		}
		paramDto.setRealWarehouseIds(queryWarehouseIds);

		//开启分页
		Page page = PageHelper.startPage(paramDto.getPageIndex(), paramDto.getPageSize());
		List<WmsBatchBoxCheckFlowDTO> wmsBatchBoxCheckFlowDTOS = wmsBatchBoxCheckFlowMapper.queryWmsBatchBoxCheckByCondition(paramDto);
		//根据仓库id查询仓库信息
		List<Long> warehouseIds = null;
		if(wmsBatchBoxCheckFlowDTOS != null) {
			warehouseIds = new ArrayList<>();
			for(WmsBatchBoxCheckFlowDTO dto : wmsBatchBoxCheckFlowDTOS) {
				if(dto.getRealWarehouseId() != null) {
					if(!warehouseIds.contains(dto.getRealWarehouseId())) {
						warehouseIds.add(dto.getRealWarehouseId());
					}
				}
			}
		}
		if(warehouseIds != null && warehouseIds.size() > 0) {
			List<RealWarehouseE> warehouseES = realWarehouseRepository.queryWarehouseByIds(warehouseIds);
			Map<Long, RealWarehouseE> warehouseEMap = RomeCollectionUtil.listforMap(warehouseES, "id");
			//遍历查询结果,设置仓库名称等
			for (WmsBatchBoxCheckFlowDTO flowDTO : wmsBatchBoxCheckFlowDTOS) {
				if(warehouseEMap.containsKey(flowDTO.getRealWarehouseId())){
					RealWarehouseE realWarehouseE = warehouseEMap.get(flowDTO.getRealWarehouseId());
					flowDTO.setFactoryCode(realWarehouseE.getFactoryCode());
					flowDTO.setRealWarehouseCode(realWarehouseE.getRealWarehouseCode());
					flowDTO.setRealWarehouseName(realWarehouseE.getRealWarehouseName());
				}
			}
		}
		List<String> skuCodes = wmsBatchBoxCheckFlowDTOS.stream().filter(v -> StringUtils.isNotBlank(v.getSkuCode())).map(WmsBatchBoxCheckFlowDTO :: getSkuCode).distinct().collect(Collectors.toList());
		//根据skuCodes获取商品信息
		List<SkuInfoExtDTO> skuInfoExtDTOList = skuFacade.skusBySkuCode(skuCodes);
		Map<String, SkuInfoExtDTO> skuInfoExtDTOMap = skuInfoExtDTOList.stream().distinct().collect(Collectors.toMap(SkuInfoExtDTO :: getSkuCode, Function.identity(), (v1, v2) -> v1));
		wmsBatchBoxCheckFlowDTOS.forEach(item -> {
			if(skuInfoExtDTOMap.containsKey(item.getSkuCode())) {
				item.setSkuName(skuInfoExtDTOMap.get(item.getSkuCode()).getName());
				item.setUnit(skuInfoExtDTOMap.get(item.getSkuCode()).getSpuUnitName());
			}
		});

		PageInfo<WmsBatchBoxCheckFlowDTO> pageList = new PageInfo<>(wmsBatchBoxCheckFlowDTOS);
		pageList.setTotal(page.getTotal());
		if(0 == page.getTotal()) {
			return pageList;
		}
		return pageList;
	}

	/**
	 * 根据wms批次库存初始化批次库存工具-仓库
	 * 是不是支持的批次库存仓，上层方法验证
	 * @param realWarehouseE
	 */
	private void wmsBatchInitToolByWarehouse(RealWarehouseE realWarehouseE, List<Long> skuIdList, Map<String, SkuInfoExtDTO> skuInfoMap) {
		int count = 0;
		int countSuccess = 0;
		boolean isLock = false;
		RedisCacheKeyEnum cacheKeyEnum = RedisCacheKeyEnum.WMS_BATCH_CHECK;
		try {
			isLock = redisUtil.lock(cacheKeyEnum.getKeyLock() + realWarehouseE.getId(), "1", cacheKeyEnum.getExpireTimeLock());
			if(isLock == false) {
				throw new RomeException(ResCode.STOCK_ERROR_1001, "仓库正在处理中,factoryCode=" + realWarehouseE.getFactoryCode() + ",outCode=" + realWarehouseE.getRealWarehouseOutCode()); 
			}
			Integer wmsCode = realWarehouseWmsConfigRepository.queryWmsConfigById(realWarehouseE.getId());
			if (wmsCode == null){
	            log.error("根据wms批次库存初始化批次,出错,暂未查询到实仓与wms的配置信息,实仓id="+ realWarehouseE.getId());
	        }
			WarehouseWmsConfigEnum wmsConfigVO = WarehouseWmsConfigEnum.getSupportWmsByType(wmsCode);
			if(wmsConfigVO == null) {
				throw new RomeException(ResCode.STOCK_ERROR_1003, "暂不支持此仓库批次初始化仓id=" + realWarehouseE.getId());	
			}
			int current = 1;
			// 每页大小 
			final int pageSize = 1000;
			List<String> skuCodes = new ArrayList<>(pageSize);
			List<Long> skuIds = new ArrayList<>(pageSize);
			if(skuIdList != null && skuIdList.size() == 0) {
				skuIdList = null;
			}
			PageHelper.startPage(current, pageSize, false);
			// 查询库存
			List<RealWarehouseStockDO> list = realWarehouseStockMapper.queryStockByWhIdAndSkuIds(realWarehouseE.getId(), skuIdList);
			List<StockQueryResponseItem> stockQueryItems;
			String type = null;
			List<BatchStockDO> batchStockDOs;
			String recordCode = SpringBeanUtil.getBean(OrderUtilService.class).queryOrderCode("INIT");
			while(list != null && list.size() > 0) {
				if(type == null) {
					type = "A";
				}else {
					type = "I";
				}
				count += list.size();
				skuCodes.clear();
				skuIds.clear();
				for(RealWarehouseStockDO dto : list) {
					if(dto.getSkuId() != null) {
						skuIds.add(dto.getSkuId());
					}
				}
				// 查询批次库存
				batchStockDOs = batchStockMapper.queryBatchStocksBySkuId(skuIds, realWarehouseE.getId());
				for(RealWarehouseStockDO dto : list) {
					if(dto.getSkuCode() != null) {
						skuCodes.add(dto.getSkuCode());
					}
				}
				// 获取wms的库存
				if(skuCodes.size() > 0) {
					List<BatchStockDTO> resultBatchList = new ArrayList<>(skuCodes.size());
					try {
						stockQueryItems = StockToolFacade.stockQuery(realWarehouseE, type, skuInfoMap, wmsConfigVO.getType() + "", skuCodes, true, 0);
						mergeToWmsBatchInitTool(batchStockDOs, stockQueryItems, resultBatchList, recordCode, skuInfoMap, realWarehouseE.getId());
					} catch (Exception e) {
						log.error("根据wms批次库存初始化批次,出错," + wmsConfigVO.getDesc() + "仓接口异常,{}", e);
					}
					if(resultBatchList != null && resultBatchList.size() > 0) {
						SpringBeanUtil.getBean(BatchStockService.class).refreshForBatchStock(resultBatchList);
						countSuccess += resultBatchList.size();
					}
				}
				current++;
				PageHelper.startPage(current, pageSize, false);
				list = realWarehouseStockMapper.queryStockByWhIdAndSkuIds(realWarehouseE.getId(), skuIdList);
			}
		} catch (RomeException e) {
			log.error("根据wms批次库存初始化批次,出错,总数={},有效总数={}:实仓id={}{}",count, countSuccess, realWarehouseE.getId(), e);
			throw e;
		}catch (Exception e) {
			log.error("根据wms批次库存初始化批次,出错,总数={},有效总数={}:实仓id={}{}",count, countSuccess, realWarehouseE.getId(), e);
			throw new RomeException(ResCode.STOCK_ERROR_1003, e.getMessage());
		} finally {
			log.info("根据wms批次库存初始化批次,成功,realWarehouseId={},总数={},有效总数={}", realWarehouseE.getId(), count, countSuccess);
			if(isLock) {
				redisUtil.unLock(cacheKeyEnum.getKeyLock() + realWarehouseE.getId(), "1");
			}
		}
	}
	
	/**
	 * 合并处理，根据wms批次库存初始化批次
	 * @param batchStockDOs
	 * @param stockQueryItems
	 * @param resultBatchList
	 * @param recordCode
	 * @param skuInfoMap
	 * @param realWarehouseId
	 */
	private void mergeToWmsBatchInitTool(List<BatchStockDO> batchStockDOs, List<StockQueryResponseItem> stockQueryItems, List<BatchStockDTO> resultBatchList, String recordCode, Map<String, SkuInfoExtDTO> skuInfoMap, Long realWarehouseId) {
		// 合并数据
		stockQueryItems = mergeStockQueryResultByInitTool(stockQueryItems);
		BatchStockDTO batchStockDTO;
		// 先以中台批次处理
		if(batchStockDOs != null && batchStockDOs.size() > 0) {
			StockQueryResponseItem wmsDto, wmsDto2 = null;
			for(BatchStockDO dto : batchStockDOs) {
				wmsDto = getWmsBatchStockByFingerPrintSkuCode(stockQueryItems, dto.getSkuCode(), dto.getFingerPrint());
				if(wmsDto == null) {
					if(wmsDto2 == null) {
						wmsDto2 = new StockQueryResponseItem();
						wmsDto2.setRealQty(BigDecimal.ZERO);
					}
					wmsDto = wmsDto2;
				}
				// 不相等时
				if(wmsDto.getRealQty().subtract(dto.getSkuQty()).abs().compareTo(StockCoreConsts.MIN_VALUE_ZERO) > 0) {
					batchStockDTO = new BatchStockDTO();
					batchStockDTO.setSkuId(dto.getSkuId());
					batchStockDTO.setSkuCode(dto.getSkuCode());
					batchStockDTO.setRealWarehouseId(realWarehouseId);
					batchStockDTO.setValidity(dto.getValidity());
					batchStockDTO.setBatchCode(dto.getBatchCode());
					batchStockDTO.setProductDate(dto.getProductDate());
					batchStockDTO.setEntryDate(dto.getEntryDate());
					if(wmsDto.getRealQty().compareTo(dto.getSkuQty()) > 0) {
						batchStockDTO.setSkuQty(wmsDto.getRealQty().subtract(dto.getSkuQty()));
						batchStockDTO.setStockType(BatchStockTypeVO.STOCK_INCREASE.getType());
					} else {
						batchStockDTO.setSkuQty(dto.getSkuQty().subtract(wmsDto.getRealQty()));
						batchStockDTO.setStockType(BatchStockTypeVO.STOCK_DECREASE.getType());
					}
					batchStockDTO.setRecordCode(recordCode);
					resultBatchList.add(batchStockDTO);
				}
			}
		}
		// 以wms批次处理
		if(stockQueryItems != null && stockQueryItems.size() > 0) {
			BatchStockDO ztDto;
			SkuInfoExtDTO skuInfoExtDTO;
			for(StockQueryResponseItem dto : stockQueryItems) {
				ztDto = getBatchStockByFingerPrintSkuCode(batchStockDOs, dto.getItemCode(), BatchStockUtils.calfingerPrint(dto.getProductDate(), dto.getReceiveDate()));
				if(ztDto == null && dto.getRealQty().compareTo(StockCoreConsts.MIN_VALUE_ZERO) > 0) {
					skuInfoExtDTO = skuInfoMap.get(dto.getItemCode());
					if(skuInfoExtDTO == null) {
						log.warn("根据wms批次库存初始化批次,出错,商品中心没查到物料,实仓id={},skuCode={}", realWarehouseId, dto.getItemCode());
						continue;
					}
					batchStockDTO = new BatchStockDTO();
					batchStockDTO.setSkuId(skuInfoExtDTO.getId());
					batchStockDTO.setSkuCode(skuInfoExtDTO.getSkuCode());
					batchStockDTO.setRealWarehouseId(realWarehouseId);
					batchStockDTO.setValidity(skuInfoExtDTO.getTotalShelfLife());
					batchStockDTO.setSkuQty(dto.getRealQty());
					batchStockDTO.setStockType(BatchStockTypeVO.STOCK_INCREASE.getType());
					batchStockDTO.setRecordCode(recordCode);
					batchStockDTO.setBatchCode(dto.getBatchCode());
					batchStockDTO.setProductDate(dto.getProductDate());
					batchStockDTO.setEntryDate(dto.getReceiveDate());
					resultBatchList.add(batchStockDTO);
				}
			}
		}
	}

	/**
	 * 获取wms批次，根据skuCode,批次指纹
	 * @param list
	 * @param skuCode
	 * @param fingerPrint
	 * @return
	 */
	private StockQueryResponseItem getWmsBatchStockByFingerPrintSkuCode(List<StockQueryResponseItem> list, String skuCode, String fingerPrint) {
		if(list != null && list.size() > 0) {
			for(StockQueryResponseItem dto : list) {
				if(skuCode.equals(dto.getItemCode()) && fingerPrint.equals(BatchStockUtils.calfingerPrint(dto.getProductDate(), dto.getReceiveDate()))) {
					return dto;
				}
			}
		}
		return null;
	}

	/**
	 * 获取批次，根据skuCode,批次指纹
	 * @param list
	 * @param skuCode
	 * @param fingerPrint
	 * @return
	 */
	private BatchStockDO getBatchStockByFingerPrintSkuCode(List<BatchStockDO> list, String skuCode, String fingerPrint) {
		if(list != null && list.size() > 0) {
			for(BatchStockDO dto : list) {
				if(skuCode.equals(dto.getSkuCode()) && fingerPrint.equals(dto.getFingerPrint())) {
					return dto;
				}
			}
		}
		return null;
	}

	/**
	 * 合并初始批次表
	 * 合并结果,因有多个批次,一个物料多条记录同一个批次可能
	 * @param stockQueryItems
	 * @return
	 */
	private List<StockQueryResponseItem> mergeStockQueryResultByInitTool(List<StockQueryResponseItem> stockQueryItems){
		// 入库日期为空时，当前时间填充
		Date defaultEntryDate = DateUtil.getDayBegin(new Date());
		Map<String, StockQueryResponseItem> map = new HashMap<>();
		List<StockQueryResponseItem> result = new ArrayList<>();
		StockQueryResponseItem temp;
		String str;
		for(StockQueryResponseItem item : stockQueryItems) {
			if(item.getItemCode() != null) {
				// 入库日期为空时，当前时间填充
				if(item.getReceiveDate() == null) {
					item.setReceiveDate(defaultEntryDate);
				}
				if(item.getRealQty() == null) {
					item.setRealQty(BigDecimal.ZERO);
				}
				if(item.getQualityQty() == null) {
					item.setQualityQty(BigDecimal.ZERO);
				}
				if(item.getUnqualifiedQty() == null) {
					item.setUnqualifiedQty(BigDecimal.ZERO);
				}
				if(item.getAvailableQty() == null) {
					item.setAvailableQty(BigDecimal.ZERO);
				}
				if(StringUtils.isBlank(item.getBatchCode())) {
					item.setBatchCode("");
				}
				str = item.getItemCode() + "_" + BatchStockUtils.calfingerPrint(item.getProductDate(), item.getReceiveDate());
				temp = map.get(str);
				if(temp == null) {
					temp = item;
					map.put(str, temp);
					result.add(temp);
				}else {
					temp.setRealQty(temp.getRealQty().add(item.getRealQty()));
					temp.setQualityQty(temp.getQualityQty().add(item.getQualityQty()));
					temp.setUnqualifiedQty(temp.getUnqualifiedQty().add(item.getUnqualifiedQty()));
					temp.setAvailableQty(temp.getAvailableQty().add(item.getAvailableQty()));
					temp.setLockQty(temp.getLockQty().add(item.getLockQty()));
				}
			}
		}
		return result;
	}

	/**
	 * 检查批次核对，不一致的
	 * @param wmsBatchCheckFlows
	 * @param skuInfoMap
	 * @param notEqualsSkuCodeSet
	 */
	private void checkNotEqualsSkuCode(List<WmsBatchCheckFlowDO> wmsBatchCheckFlows, Map<String, SkuInfoExtDTO> skuInfoMap, Set<String> notEqualsSkuCodeSet) {
		if(wmsBatchCheckFlows == null || skuInfoMap == null || notEqualsSkuCodeSet == null) {
			return;
		}
		// skuInfoMap 不存在，重查一下，商品中心数据。有可能wms里没有库存记录会造成skuInfoMap不存在
		List<String> skuCodes = new ArrayList<>();
		for(WmsBatchCheckFlowDO dto : wmsBatchCheckFlows) {
			if (!skuInfoMap.containsKey(dto.getSkuCode())) {
				skuCodes.add(dto.getSkuCode());
			}
		}
		if(skuCodes.size() > 0) {
			List<SkuInfoExtDTO> skuInfoExtDTOs = skuFacade.skusBySkuCode(skuCodes);
			if(skuInfoExtDTOs != null && skuInfoExtDTOs.size() > 0) {
				for(SkuInfoExtDTO dto : skuInfoExtDTOs) {
					skuInfoMap.put(dto.getSkuCode(), dto);
				}
			}
		}
		for(WmsBatchCheckFlowDO dto : wmsBatchCheckFlows) {
			//辅料等数据不需要关心辅料
			if (skuFacade.isSupplementaryMaterial(skuInfoMap.get(dto.getSkuCode()))) {
				continue;
			}
			if(!equalsFlagQty(dto.getRealQty(), dto.getWmsRealQty())) {
				notEqualsSkuCodeSet.add(dto.getSkuCode());
			}
		}

	}


	/**
	 * 检查批次核对，不一致的
	 * @param wmsBatchCheckFlows
	 * @param skuInfoMap
	 * @param notEqualsSkuCodeSet
	 */
	private void checkBoxNotEqualsSkuCode(List<WmsBatchBoxCheckFlowDO> wmsBatchCheckFlows, Map<String, SkuInfoExtDTO> skuInfoMap, Set<String> notEqualsSkuCodeSet) {
		if (wmsBatchCheckFlows == null || skuInfoMap == null || notEqualsSkuCodeSet == null) {
			return;
		}
		// skuInfoMap 不存在，重查一下，商品中心数据。有可能wms里没有库存记录会造成skuInfoMap不存在
		List<String> skuCodes = new ArrayList<>();
		for (WmsBatchBoxCheckFlowDO dto : wmsBatchCheckFlows) {
			if (!skuInfoMap.containsKey(dto.getSkuCode())) {
				skuCodes.add(dto.getSkuCode());
			}
		}
		if (skuCodes.size() > 0) {
			List<SkuInfoExtDTO> skuInfoExtDTOs = skuFacade.skusBySkuCode(skuCodes);
			if (skuInfoExtDTOs != null && skuInfoExtDTOs.size() > 0) {
				for (SkuInfoExtDTO dto : skuInfoExtDTOs) {
					skuInfoMap.put(dto.getSkuCode(), dto);
				}
			}
		}
		for (WmsBatchBoxCheckFlowDO dto : wmsBatchCheckFlows) {
			//辅料等数据不需要关心辅料
			if (skuFacade.isSupplementaryMaterial(skuInfoMap.get(dto.getSkuCode()))) {
				continue;
			}
			if (!equalsFlagQty(dto.getBoxRealQty(), dto.getWmsBoxRealQty()) || !equalsFlagQty(dto.getMixRealQty(), dto.getWmsMixRealQty())) {
				notEqualsSkuCodeSet.add(dto.getSkuCode());
			}
		}
	}


	/**
	 * 相同比较
	 * 相同为true，不相同为false
	 * @param qty
	 * @param wmsQty
	 * @return
	 */
	private boolean equalsFlagQty(BigDecimal qty, BigDecimal wmsQty) {
		if(qty == null || wmsQty == null) {
			// 全相同
			return true;
		}
		// 中台<>wms 相同
		if(qty.compareTo(wmsQty) == 0) {
			// 全相同
			return true;
		}
		// 不相同
		return false;
	}
	
}
