package com.rome.stock.innerservice.domain.service.impl;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.google.common.collect.Lists;
import com.rome.arch.core.domain.EntityFactory;
import com.rome.arch.core.exception.RomeException;
import com.rome.arch.util.SpringBeanUtil;
import com.rome.stock.common.constants.WmsConfigConstants;
import com.rome.stock.common.constants.warehouse.WarehouseRecordConstant;
import com.rome.stock.common.enums.warehouse.WarehouseRecordTypeVO;
import com.rome.stock.common.enums.warehouse.WarehouseStoreIdentiEnum;
import com.rome.stock.common.utils.DateUtil;
import com.rome.stock.core.domain.repository.CoreRealWarehouseStockRepository;
import com.rome.stock.core.infrastructure.dataobject.core.CoreRealStockOpDO;
import com.rome.stock.core.infrastructure.dataobject.core.CoreStockOpFactoryDO;
import com.rome.stock.innerservice.api.dto.*;
import com.rome.stock.innerservice.common.AlikAssert;
import com.rome.stock.innerservice.common.ResCode;
import com.rome.stock.innerservice.common.RomeCollectionUtil;
import com.rome.stock.innerservice.constant.WarehouseRecordStatusVO;
import com.rome.stock.innerservice.domain.convertor.frontrecord.FrChargeAgainstConvertor;
import com.rome.stock.innerservice.domain.convertor.warehouserecord.ReplenishWarehouseConvertor;
import com.rome.stock.innerservice.domain.entity.RealWarehouseE;
import com.rome.stock.innerservice.domain.entity.RwBatchE;
import com.rome.stock.innerservice.domain.entity.frontrecord.ChargeAgainstE;
import com.rome.stock.innerservice.domain.entity.warehouserecord.*;
import com.rome.stock.innerservice.domain.repository.RealWarehouseRepository;
import com.rome.stock.innerservice.domain.repository.RwBatchRepository;
import com.rome.stock.innerservice.domain.repository.frontrecord.FrChargeAgainstConfigRepository;
import com.rome.stock.innerservice.domain.repository.frontrecord.FrChargeAgainstDetailRepository;
import com.rome.stock.innerservice.domain.repository.frontrecord.FrChargeAgainstRepository;
import com.rome.stock.innerservice.domain.repository.frontrecord.FrontWarehouseRecordRelationRepository;
import com.rome.stock.innerservice.domain.repository.warehouserecord.WarehouseRecordRepository;
import com.rome.stock.innerservice.domain.service.*;
import com.rome.stock.innerservice.infrastructure.dataobject.*;
import com.rome.stock.innerservice.infrastructure.dataobject.frontrecord.FrChargeAgainstConfigDO;
import com.rome.stock.innerservice.infrastructure.dataobject.frontrecord.FrChargeAgainstDO;
import com.rome.stock.innerservice.infrastructure.dataobject.frontrecord.FrChargeAgainstDetailDO;
import com.rome.stock.innerservice.infrastructure.mapper.frontrecord.FrChargeAgainstConfigMapper;
import com.rome.stock.innerservice.infrastructure.mapper.frontrecord.OrderCostDetailMapper;
import com.rome.stock.innerservice.infrastructure.mapper.frontrecord.OrderCostMapper;
import com.rome.stock.innerservice.infrastructure.redis.RealWarehouseWmsRedis;
import com.rome.stock.innerservice.remote.item.dto.SkuInfoExtDTO;
import com.rome.stock.innerservice.remote.item.facade.SkuFacade;
import com.rome.stock.innerservice.remote.orderCneter.dto.FrontRecordDetailDTO;
import com.rome.stock.innerservice.remote.orderCneter.facade.OrderCenterFacade;
import com.rome.stock.innerservice.remote.venus.dto.*;
import com.rome.stock.innerservice.remote.venus.facade.VenusStockFacade;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.rome.stock.innerservice.constant.RealWarehouseTypeVO.CROSS_TYPE_28;

/**
 * FrChargeAgainstService 实现
 *
 * <AUTHOR>
 * @date 2022-01-06
 */
@Slf4j
@Service
public class ChargeAgainstServiceImpl implements ChargeAgainstService {

    @Resource
    private FrChargeAgainstRepository frChargeAgainstRepository;
    @Resource
    private FrChargeAgainstDetailRepository frChargeAgainstDetailRepository;
    @Resource
    private FrChargeAgainstConfigRepository frChargeAgainstConfigRepository;
    @Resource
    private WarehouseRecordRepository warehouseRecordRepository;
    @Resource
    private EntityFactory entityFactory;
    @Resource
    private RealWarehouseRepository realWarehouseRepository;
    @Resource
    private OrderCostMapper orderCostMapper;
    @Resource
    private OrderCostDetailMapper orderCostDetailMapper;

    @Resource
    private RwBatchRepository rwBatchRepository;
    @Resource
    private FrontWarehouseRecordRelationRepository frontWarehouseRecordRelationRepository;
    @Resource
    private SkuFacade skuFacade;
    @Resource
    private FrChargeAgainstConvertor frChargeAgainstConvertor;
    @Resource
    private VenusStockFacade venusStockFacade;
    @Resource
    private RealWarehouseWmsRedis realWarehouseWmsRedis;
    @Resource
    private WmsOutService wmsOutService;
    @Resource
    private ReplenishWarehouseConvertor replenishWarehouseConvertor;
    @Resource
    private CoreRealWarehouseStockRepository coreRealWarehouseStockRepository;
    @Resource
    private FrChargeAgainstConfigMapper frChargeAgainstConfigMapper;
    @Resource
    private ChargeAgainstService chargeAgainstService;
    @Resource
    private OrderCenterFacade orderCenterFacade;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void createChargeAgainstRecord(ChargeAgainstCreateDTO chargeAgainstCreateDTO) {
        CoreStockOpFactoryDO stockOpFactoryDO =chargeAgainstCreateDTO.getStockOpFactoryDO();
        Boolean success = false;
        try {
            this.chargeAgainst(chargeAgainstCreateDTO);
            stockOpFactoryDO.commit();
            success = true;
        } catch (RomeException e) {
            throw e;
        } catch (Exception e) {
            throw e;
        } finally {
            if (!success) {
                stockOpFactoryDO.redisRollBack();
            }
        }
    }


    private String chargeAgainst(ChargeAgainstCreateDTO chargeAgainstCreateDTO){
        WarehouseRecordE originWarehouseRecordE = warehouseRecordRepository.queryWarehouseRecordByRecordCodeAndTypeWithES(chargeAgainstCreateDTO.getOriginWarehouseRecordCode(),chargeAgainstCreateDTO.getOriginRecordType());
        AlikAssert.notNull(originWarehouseRecordE, "999", "原单据不存在");
        if (!originWarehouseRecordE.getRecordStatus().equals(WarehouseRecordStatusVO.IN_ALLOCATION.getStatus()) &&
                !originWarehouseRecordE.getRecordStatus().equals(WarehouseRecordStatusVO.OUT_ALLOCATION.getStatus())
        ) {
            throw new RomeException("999", "目前单据状态不支持冲销");
        }
        FrChargeAgainstConfigDO chargeAgainstConfigDO = this.getConfigDOByOriginRecordCode(originWarehouseRecordE.getRecordType());
        AlikAssert.notNull(chargeAgainstConfigDO, "999", "该类型单据暂不支持冲销");
        //明细校验
        this.checkDetailBeforeCreate(chargeAgainstCreateDTO,originWarehouseRecordE,chargeAgainstConfigDO);
        AbstractChargeAgainst abstractChargeAgainst = (AbstractChargeAgainst) SpringBeanUtil.getBean(chargeAgainstConfigDO.getServiceName());
        AlikAssert.notNull(abstractChargeAgainst, "999", "服务名称配置错误");
        ChargeAgainstWarehouseRecordE chargeAgainstWarehouseRecordE = entityFactory.createEntity(ChargeAgainstWarehouseRecordE.class);
        if(WarehouseRecordTypeVO.CONSUME_OUT_WAREHOUSE_RECORD.getType().equals(originWarehouseRecordE.getRecordType())
                || WarehouseRecordTypeVO.WAREHOUSE_OUT_USE_RECORD.getType().equals(originWarehouseRecordE.getRecordType())){
            //目前仓库领用和报废需要根据原单虚仓ID进行设值操作虚仓库存
            chargeAgainstWarehouseRecordE.setVirtualWarehouseId(originWarehouseRecordE.getVirtualWarehouseId());
        }
        abstractChargeAgainst.createChargeAgainstRecord(chargeAgainstCreateDTO, chargeAgainstConfigDO, chargeAgainstWarehouseRecordE);
        return chargeAgainstWarehouseRecordE.getRecordCode();
    }

    /**
     * 校验明细信息
     *
     * @param chargeAgainstCreateDTO
     */
    private void checkDetailBeforeCreate(ChargeAgainstCreateDTO chargeAgainstCreateDTO,WarehouseRecordE originWarehouseRecordE
            ,FrChargeAgainstConfigDO chargeAgainstConfigDO) {
        //如果是跨境电商一件代发,冲销只能选择跨境退货仓-28
        this.checkCrossOneRecord(chargeAgainstCreateDTO);
        //门店零售正向
        if (WarehouseRecordTypeVO.SHOP_RETAIL_WAREHOUSE_RECORD.getType().equals(chargeAgainstConfigDO.getOriginRecordType())) {
            //如果是冲销之后产生的单子，不允许继续冲销
            OrderCostDO orderCostDO = orderCostMapper.queryByZtRecordCode(originWarehouseRecordE.getRecordCode());
            AlikAssert.notNull(orderCostDO, ResCode.STOCK_ERROR_1001 , "未写入拼接表，请稍后再试");
            if (orderCostDO.getSceneType() == 30) {
                throw new RomeException(ResCode.STOCK_ERROR_1001, "此单据为冲销之后产生的单据，不允许冲销,originWarehouseRecordCode:" + originWarehouseRecordE.getRecordCode());
            }
            //如果是多组单子，包含咖啡相关的数据，也不允许冲销
            List<String> groups = venusStockFacade.queryBizIdByGroupId(originWarehouseRecordE.getRecordCode());
            if (groups.size() > 1) {
                throw new RomeException(ResCode.STOCK_ERROR_1001, "此单据为包含咖啡单，不允许冲销,originWarehouseRecordCode:" + originWarehouseRecordE.getRecordCode());
            }

            //必须是全部明细冲销，否则报错
            if (chargeAgainstCreateDTO.getChargeAgainstDetails().size() != originWarehouseRecordE.getWarehouseRecordDetails().size()) {
                throw new RomeException(ResCode.STOCK_ERROR_1001, "门店零售出库单仅支持整单冲销,originWarehouseRecordCode:" + originWarehouseRecordE.getRecordCode());
            }
        }
        //查询原单的仓库信息
        RealWarehouseE realWarehouseE = realWarehouseRepository.getRealWarehouseById(originWarehouseRecordE.getRealWarehouseId());
        if(Objects.isNull(realWarehouseE)){
            throw new RomeException(ResCode.STOCK_ERROR_1001, "仓库信息不存在,originWarehouseRecordCode:" + originWarehouseRecordE.getRecordCode());
        }
        if(WarehouseRecordTypeVO.SHOP_RECEIVE.getType().equals(chargeAgainstConfigDO.getOriginRecordType())
                        || WarehouseRecordTypeVO.SHOP_OUT_CONSUME_RECORD.getType().equals(chargeAgainstConfigDO.getOriginRecordType())
                        || WarehouseRecordTypeVO.SHOP_FORETASTE_OUT_WAREHOUSE_RECORD.getType().equals(chargeAgainstConfigDO.getOriginRecordType())){
            List<FrontRecordDetailDTO> list = orderCenterFacade.queryFrontRecordDetailByRecordCode(originWarehouseRecordE.getRecordCode());
            boolean needCheck=false;
            for (FrontRecordDetailDTO frontRecordDetailDTO : list) {
                if(CollectionUtils.isNotEmpty(frontRecordDetailDTO.getProductBomRefList())){
                    //只要有Boom只能整单冲销
                    needCheck=true;
                    break;
                }
            }
            if(needCheck){
                //仓店一体的门店领用冲销单,需要校验是否整单冲销,明细数量也要一致
                if (chargeAgainstCreateDTO.getChargeAgainstDetails().size() != originWarehouseRecordE.getWarehouseRecordDetails().size()) {
                    throw new RomeException(ResCode.STOCK_ERROR_1001, "仓店一体的仓库领用单仅支持整单冲销,originWarehouseRecordCode:" + originWarehouseRecordE.getRecordCode());
                }
                Map<String, BigDecimal> originDetailMap = originWarehouseRecordE.getWarehouseRecordDetails().stream().collect(Collectors.toMap(WarehouseRecordDetail::getSkuCode, WarehouseRecordDetail::getActualQty, (v1, v2) -> v1));
                for (ChargeAgainstCreateDetailDTO detail : chargeAgainstCreateDTO.getChargeAgainstDetails()) {
                    if (!originDetailMap.containsKey(detail.getSkuCode())) {
                        throw new RomeException(ResCode.STOCK_ERROR_1001, "skuCode:" + detail.getSkuCode() + "在原单据中不存在");
                    }
                    if (detail.getSkuQty().compareTo(originDetailMap.get(detail.getSkuCode()))!= 0) {
                        throw new RomeException(ResCode.STOCK_ERROR_1001, "skuCode:" + detail.getSkuCode() + "当前明细冲销数量不等于原单数量");
                    }
                }
            }
        }
        List<ChargeAgainstCreateDetailDTO> chargeAgainstDetails = chargeAgainstCreateDTO.getChargeAgainstDetails();
        AlikAssert.notEmpty(chargeAgainstDetails, "999", "明细不能为空");
        List<String> chargeAgainstDetailsSkuCodes =chargeAgainstDetails.stream().map(ChargeAgainstCreateDetailDTO::getSkuCode).distinct().collect(Collectors.toList());
        //查询明细信息
        if (CollectionUtils.isNotEmpty(chargeAgainstDetailsSkuCodes)) {
            List<RwBatchDo> rwBatchDos = rwBatchRepository.queryByRecordCodeAndSkuCodeList(originWarehouseRecordE.getRecordCode(),chargeAgainstDetailsSkuCodes);
            for (RwBatchDo rwBatchDo : rwBatchDos) {
                if (Objects.equals(rwBatchDo.getQualityStatus(), 0) || Objects.equals(rwBatchDo.getQualityStatus(), 2) ) {
                    throw new RomeException("999", "skuCode：" + rwBatchDo.getSkuCode() + "未质检完成或质检不合格，不支持冲销");
                }
            }
        }
        //查询已经冲销完成和冲销中的单据列表
        List<String> frontRecordCodes = frChargeAgainstRepository.listUnCancelByOriginRecordCode(chargeAgainstCreateDTO.getOriginRecordCode());
        //如果没有，直接返回
        if (CollectionUtils.isEmpty(frontRecordCodes)) {
            return;
        }
        //查询对应的冲销明细skuCode级别数据
        List<FrChargeAgainstDetailDO> frChargeAgainstDetailDOS = frChargeAgainstDetailRepository.listByRecordCodes(frontRecordCodes);
        for (FrChargeAgainstDetailDO frChargeAgainstDetailDO : frChargeAgainstDetailDOS) {
            if (chargeAgainstDetailsSkuCodes.contains(frChargeAgainstDetailDO.getSkuCode())) {
                String errorMsg = "skuCode：" + frChargeAgainstDetailDO.getSkuCode();
                throw new RomeException("999", errorMsg + " 已冲销，无法再次冲销");
            }
        }
    }



    /**
     * 如果是跨境电商一件代发,冲销只能选择跨境退货仓-28
     * @param chargeAgainstCreateDTO
     */
    private void checkCrossOneRecord(ChargeAgainstCreateDTO chargeAgainstCreateDTO){
        //如果是跨境电商一件代发
        WarehouseRecordE warehouseRecordE=warehouseRecordRepository.queryWarehouseRecordByRecordCodeAndTypeWithES(chargeAgainstCreateDTO.getOriginWarehouseRecordCode(),chargeAgainstCreateDTO.getOriginRecordType());
        if(null == warehouseRecordE){
            throw new RomeException(ResCode.STOCK_ERROR_1001,"原出入库单号对应单据不存在");
        }
        if(WarehouseRecordTypeVO.ONE_PURCHASE_RECORD.getType().equals(warehouseRecordE.getRecordType())){
            RealWarehouseWmsConfigDO realWarehouseWmsConfigDO=realWarehouseWmsRedis.findWmsInformationById(warehouseRecordE.getRealWarehouseId());
            if(null !=realWarehouseWmsConfigDO && WmsConfigConstants.WMS_CROSS==realWarehouseWmsConfigDO.getWmsCode()){
                //冲销只能选择跨境退货仓-28
                RealWarehouseE realWarehouseE=realWarehouseRepository.getRealWarehouseById(chargeAgainstCreateDTO.getRealWarehouseId());
                if(null != realWarehouseE && !CROSS_TYPE_28.getType().equals(realWarehouseE.getRealWarehouseType())){
                    throw new RomeException(ResCode.STOCK_ERROR_1001,"跨境电商一件代发单据冲销，新仓库编号不是跨境退货仓");
                }
            }
        }
    }


    /**
     * @param originRecordType
     * @return
     */
    public FrChargeAgainstConfigDO getConfigDOByOriginRecordCode(Integer originRecordType) {
        List<FrChargeAgainstConfigDO> frChargeAgainstConfigDOS = frChargeAgainstConfigRepository.listAll();
        Map<Integer, FrChargeAgainstConfigDO> configDOMap = RomeCollectionUtil.listforMap(frChargeAgainstConfigDOS, "originRecordType");
        return configDOMap.get(originRecordType);
    }


    @Override
    public List<WarehouseReceiveDTO> queryReceiveByRecordCode(WarehouseRecordDTO warehouseRecordDTO) {
        List<WarehouseReceiveDTO> resultList = new ArrayList<>();
        List<FrChargeAgainstConfigDO> list=frChargeAgainstConfigRepository.listAll();
        List<Integer> recordTypes=list.stream().distinct().map(FrChargeAgainstConfigDO::getOriginRecordType).collect(Collectors.toList());
        warehouseRecordDTO.setRecordTypes(recordTypes);

        //先查询后置单，然后根据后置单查询拼接表，防止后置单的业务单号与拼接表不一致的情况
        List<WarehouseRecordE> warehouseRecordList = warehouseRecordRepository.queryWarehouseRecordByConditionWithES(warehouseRecordDTO);
        AlikAssert.notEmpty(warehouseRecordList, "999", "未查询到对应的后置单号");

        List<String> recordCodeList = new ArrayList<>();
        List<Integer> recordTypeList = new ArrayList<>();
        Map<String, WarehouseRecordE> recordMap = new HashMap<>();
        for (WarehouseRecordE warehouseRecordE : warehouseRecordList) {
            recordCodeList.add(warehouseRecordE.getRecordCode());
            if(Objects.equals(warehouseRecordE.getRecordType(), WarehouseRecordTypeVO.DISPARITY_WH_FROM_RECORD.getType())){
                recordTypeList.add(WarehouseRecordTypeVO.DISPARITY_TRANSFER_WAREHOUSE_IN_RECOED12.getType());
            }else if(Objects.equals(warehouseRecordE.getRecordType(), WarehouseRecordTypeVO.DISPARITY_WH_TO_RECORD.getType())){
                recordTypeList.add(WarehouseRecordTypeVO.DISPARITY_SHOP_IN_RECOED11.getType());
            }else if(Objects.equals(warehouseRecordE.getRecordType(), WarehouseRecordTypeVO.DISPARITY_WH_RETURN_FROM_RECORD.getType())){
                recordTypeList.add(WarehouseRecordTypeVO.DISPARITY_SHOP_IN_RECOED21.getType());
            }else{
                recordTypeList.add(warehouseRecordE.getRecordType());
            }
            recordMap.put(warehouseRecordE.getRecordCode(), warehouseRecordE);
        }
        //通过recordType和recordCode查询拼接表获取冲销单据，带上单据类型，防止查出收货完成的单据
        List<OrderCostDO> orderCostDOList = orderCostMapper.queryByRecordCodesAndType(recordCodeList, recordTypeList);
        AlikAssert.notEmpty(orderCostDOList, "999", "未查询到对应的拼接表数据");
        for (OrderCostDO orderCostDO : orderCostDOList) {
            RealWarehouseE realWarehouseE = realWarehouseRepository.getRealWarehouseById(orderCostDO.getRealWarehouseId());
            WarehouseRecordE warehouseRecordE = recordMap.get(orderCostDO.getRecordCode());
            WarehouseReceiveDTO warehouseReceiveDTO = new WarehouseReceiveDTO();
            warehouseReceiveDTO.setRecordCode(orderCostDO.getRecordCode());
            warehouseReceiveDTO.setReceiveCode(orderCostDO.getZtRecordCode());
            warehouseReceiveDTO.setSapRecordCode(orderCostDO.getZtBusinessCode());
            warehouseReceiveDTO.setRecordTypeName(WarehouseRecordTypeVO.getByType(warehouseRecordE.getRecordType()).getDesc());
            warehouseReceiveDTO.setFactoryCode(realWarehouseE.getFactoryCode());
            warehouseReceiveDTO.setRealWarehouseCode(realWarehouseE.getRealWarehouseCode());
            warehouseReceiveDTO.setBusinessType(warehouseRecordE.getBusinessType());
            warehouseReceiveDTO.setRecordType(warehouseRecordE.getRecordType());
            resultList.add(warehouseReceiveDTO);
        }
        return resultList;
    }



    @Override
    public ChargeAgainstCreateDTO queryBatchListByReceiveCode(WarehouseReceiveDTO warehouseReceiveDTO) {
        ChargeAgainstCreateDTO chargeAgainstCreateDTO = new ChargeAgainstCreateDTO();

        WarehouseRecordE warehouseRecordE = warehouseRecordRepository.queryWarehouseRecordByRecordCodeAndTypeWithES(warehouseReceiveDTO.getRecordCode(),warehouseReceiveDTO.getRecordType());
        if (null == warehouseRecordE) {
            return chargeAgainstCreateDTO;
        }
        chargeAgainstCreateDTO.setBusinessType(warehouseRecordE.getBusinessType());
        chargeAgainstCreateDTO.setOriginBusinessCode(warehouseRecordE.getSapOrderCode());
        chargeAgainstCreateDTO.setOriginWarehouseRecordCode(warehouseRecordE.getRecordCode());
        RealWarehouseE realWarehouseE = realWarehouseRepository.getRealWarehouseById(warehouseRecordE.getRealWarehouseId());
        chargeAgainstCreateDTO.setOriginRealWarehouseCode(realWarehouseE.getRealWarehouseCode());
        chargeAgainstCreateDTO.setOriginFactoryCode(realWarehouseE.getFactoryCode());
        chargeAgainstCreateDTO.setCostCenterCode(realWarehouseE.getCostCenterCode());
        chargeAgainstCreateDTO.setOriginRealWarehouseId(realWarehouseE.getId());
        chargeAgainstCreateDTO.setRealWarehouseId(realWarehouseE.getId());
        chargeAgainstCreateDTO.setOriginRecordType(warehouseRecordE.getRecordType());
        //明细
        List<ChargeAgainstCreateDetailDTO> chargeAgainstDetails = new ArrayList<>();
        chargeAgainstCreateDTO.setChargeAgainstDetails(chargeAgainstDetails);

        //查询明细数据
        List<OrderCostDetailDO> orderCostDetailDOS = orderCostDetailMapper.queryNormalPriceTypeByZtRecordCode(warehouseReceiveDTO.getReceiveCode());



        List<RwBatchE> list = rwBatchRepository.queryBywmsRecordCode(warehouseReceiveDTO.getRecordCode(), warehouseReceiveDTO.getReceiveCode());
        if(CollectionUtils.isEmpty(list)){
            list=rwBatchRepository.queryRwInfoByRecordCode(warehouseReceiveDTO.getRecordCode());
        }
        Map<String, Integer> skuIsReverseMap = new HashMap<>();
        Map<String, List<String>> skuBatchCodeListMap = new HashMap<>();
        for (RwBatchE rwBatchE : list) {
            if(rwBatchE.getQualityStatus()==2 || rwBatchE.getQualityStatus()==0){
                //质检不合格不能冲销
                skuIsReverseMap.put(rwBatchE.getSkuCode(), 2);
            }
            List<String> batchCodeList = skuBatchCodeListMap.getOrDefault(rwBatchE.getSkuCode(), new ArrayList<>());
            batchCodeList.add(rwBatchE.getBatchCode());

        }

        //遍历拼接表明细数据
        for (OrderCostDetailDO orderCostDetailDO : orderCostDetailDOS) {
            ChargeAgainstCreateDetailDTO chargeAgainstCreateDetailDTO = new ChargeAgainstCreateDetailDTO();
            chargeAgainstCreateDetailDTO.setSkuCode(orderCostDetailDO.getSkuCode());
            chargeAgainstCreateDetailDTO.setSkuId(orderCostDetailDO.getSkuId());
            chargeAgainstCreateDetailDTO.setSkuQty(orderCostDetailDO.getActualQty());
            chargeAgainstCreateDetailDTO.setBatchCode(StringUtils.join(skuBatchCodeListMap.get(orderCostDetailDO.getSkuCode()), ","));
            chargeAgainstCreateDetailDTO.setRefDetailId(orderCostDetailDO.getRefDetailId());
            chargeAgainstCreateDetailDTO.setIsReverse(skuIsReverseMap.get(orderCostDetailDO.getSkuCode()));
            chargeAgainstCreateDetailDTO.setUnit(orderCostDetailDO.getUnit());
            chargeAgainstCreateDetailDTO.setUnitCode(orderCostDetailDO.getUnitCode());
            chargeAgainstDetails.add(chargeAgainstCreateDetailDTO);
        }

        chargeAgainstCreateDTO.setOriginRecordCode(warehouseReceiveDTO.getReceiveCode());
        //查询商品信息
        List<Long> skuIds = chargeAgainstDetails.stream().map(ChargeAgainstCreateDetailDTO::getSkuId).distinct().collect(Collectors.toList());
        List<SkuInfoExtDTO> skuInfoList = skuFacade.skusBySkuId(skuIds);
        Map<Long,SkuInfoExtDTO> skuInfoExtDTOSMap=skuInfoList.stream().collect(Collectors.toMap(SkuInfoExtDTO::getId, Function.identity(), (v1, v2) -> v1));
        //判断是否匹配冲销明细
        List<FrChargeAgainstDO> chargeAgainstDOList = frChargeAgainstRepository.getByOriginWarehouseRecordCode(warehouseReceiveDTO.getRecordCode(), warehouseReceiveDTO.getReceiveCode());
        Map<String, FrChargeAgainstDetailDO> derailMap=new HashMap<>();
        if (!CollectionUtils.isEmpty(chargeAgainstDOList)) {
            List<String> recordCodes=chargeAgainstDOList.stream().map(FrChargeAgainstDO::getRecordCode).distinct().collect(Collectors.toList());
            List<FrChargeAgainstDetailDO> derailList = frChargeAgainstDetailRepository.listByRecordCodes(recordCodes);
            derailMap = derailList.stream().collect(Collectors.toMap(k -> k.getSkuCode(), k -> k));
        }
        for(ChargeAgainstCreateDetailDTO detail:chargeAgainstDetails){
            //设置商品名称，转map
            SkuInfoExtDTO skuInfo = skuInfoExtDTOSMap.get(detail.getSkuId());
            if (skuInfo != null) {
                detail.setSkuName(skuInfo.getName());
            }
            if (detail.getIsReverse() == null) {
                detail.setIsReverse(0);
                if(derailMap.containsKey(detail.getSkuCode())){
                    //已经冲销
                    detail.setIsReverse(1);
                }
            }
        }
        return chargeAgainstCreateDTO;
    }

    @Override
    public PageInfo<ChargeAgainstDTO> queryReverseRecordByCondition(ChargeAgainstDTO chargeAgainstDTO) {
        PageHelper.startPage(chargeAgainstDTO.getPageIndex(),chargeAgainstDTO.getPageSize());
        List<ChargeAgainstDTO> list=frChargeAgainstRepository.queryReverseRecordByCondition(chargeAgainstDTO);
        List<RealWarehouseE> originRealWarehouseIds=realWarehouseRepository.queryWarehouseByIds(list.stream().map(ChargeAgainstDTO::getOriginRealWarehouseId).distinct().collect(Collectors.toList()));
        Map<Long,RealWarehouseE> originRealWarehouseMap=originRealWarehouseIds.stream().collect(Collectors.toMap(RealWarehouseE::getId, item -> item));
        List<RealWarehouseE> realWarehouseIds=realWarehouseRepository.queryWarehouseByIds(list.stream().map(ChargeAgainstDTO::getRealWarehouseId).distinct().collect(Collectors.toList()));
        Map<Long,RealWarehouseE> realWarehouseMap=realWarehouseIds.stream().collect(Collectors.toMap(RealWarehouseE::getId, item -> item));
        for (ChargeAgainstDTO againstDTO : list) {
            if(originRealWarehouseMap.containsKey(againstDTO.getOriginRealWarehouseId())){
                againstDTO.setOriginRealWarehouseCode(originRealWarehouseMap.get(againstDTO.getOriginRealWarehouseId()).getRealWarehouseCode());
                againstDTO.setOriginRealWarehouseName(originRealWarehouseMap.get(againstDTO.getOriginRealWarehouseId()).getRealWarehouseName());
                againstDTO.setCostCenterCode(originRealWarehouseMap.get(againstDTO.getOriginRealWarehouseId()).getCostCenterCode());
            }
            if(realWarehouseMap.containsKey(againstDTO.getRealWarehouseId())){
                againstDTO.setRealWarehouseCode(realWarehouseMap.get(againstDTO.getRealWarehouseId()).getRealWarehouseCode());
                againstDTO.setRealWarehouseName(realWarehouseMap.get(againstDTO.getRealWarehouseId()).getRealWarehouseName());
            }
        }
        PageInfo<ChargeAgainstDTO> result = new PageInfo<>(list);
        return result;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void confirmReverseRecord(List<String> recordCodes, Long modifier, Boolean isWms) {
        //recordCodes数量校验
        for (String recordCode : recordCodes) {
            ChargeAgainstE frChargeAgainstDO=frChargeAgainstRepository.getByRecordCode(recordCode);
            //更新前置单状态为已确认
            int i=frChargeAgainstRepository.updateToConfirmStatus(frChargeAgainstDO.getId(),modifier);
            if(i==0){
                throw new RomeException(ResCode.STOCK_ERROR_1001,"更新前置单失败,"+recordCode);
            }
            //更新后置单待下发wms
            List<Long> relation=frontWarehouseRecordRelationRepository.queryWarehouseRecordIdByRecord(recordCode);
            if(CollectionUtils.isEmpty(relation)){
                throw new RomeException(ResCode.STOCK_ERROR_1001,"查询后置单关联失败,"+recordCode);
            }
            if (!isWms) {
                //不下发wms
                int num = warehouseRecordRepository.updateRecordWmsSyncStatusSuccess(relation.get(0));
                if (num == 0) {
                    throw new RomeException(ResCode.STOCK_ERROR_1001,"更新后置单失败,"+recordCode);
                }
                return;
            }
            int j=warehouseRecordRepository.updateRecordWmsSyncStatus(relation.get(0));
            if(j==0){
                throw new RomeException(ResCode.STOCK_ERROR_1001,"更新后置单失败,"+recordCode);
            }
        }
    }


    /**
     * 调用财务中台确认接口
     * @param frChargeAgainstDO
     */
    @Override
    public void confirmReverse(ChargeAgainstE frChargeAgainstDO){
        OrderCostDO orderCostDOMain=orderCostMapper.queryByZtRecordCode(frChargeAgainstDO.getOriginRecordCode());
        if(null == orderCostDOMain){
            throw new RomeException(ResCode.STOCK_ERROR_1001,"根据originRecordCode查询OrderCostDO失败recordCode,"+frChargeAgainstDO.getRecordCode());
        }
        ReversalValidateReqDTO reversalValidateReqDTO = new ReversalValidateReqDTO();
        reversalValidateReqDTO.setZtBusinessCode(orderCostDOMain.getZtBusinessCode());
        reversalValidateReqDTO.setGroupId(frChargeAgainstDO.getOriginRecordCode());

        List<String> ztRecordCodeList;
        if(Objects.equals(String.valueOf(WarehouseRecordTypeVO.SHOP_RECEIVE.getType()),orderCostDOMain.getRecordType())){
            //如果是门店领用单,只需要查当前单据即可
            ztRecordCodeList=Lists.newArrayList(orderCostDOMain.getZtRecordCode());
        }else{
            //根据groupId查询message_log
            ztRecordCodeList=venusStockFacade.queryBizIdByGroupId(frChargeAgainstDO.getOriginRecordCode());
        }
        List<OrderCostDO> orderCostDOList=orderCostMapper.queryByZtRecordCodes(ztRecordCodeList);
        if(CollectionUtils.isEmpty(orderCostDOList)){
            throw new RomeException(ResCode.STOCK_ERROR_1001,"根据originBusinessCode查询OrderCostDO失败recordCode,"+frChargeAgainstDO.getRecordCode());
        }
        //过滤889类型单据
        orderCostDOList=orderCostDOList.stream().filter(v->!String.valueOf(889).equals(v.getRecordType())).collect(Collectors.toList());
        List<ReversalValidateOrderDTO> orderList=new ArrayList<>();
        Map<Integer, String> purchaseMap=WarehouseRecordTypeVO.getPurchaseWarehouseRecordTypeList();
        Integer originRecordType=frChargeAgainstConfigMapper.getOriginRecordTypeByFrontRecordType(frChargeAgainstDO.getRecordType());
        if(null == originRecordType){
            throw new RomeException(ResCode.STOCK_ERROR_1001,"冲销配置config不存在，recordCode："+frChargeAgainstDO.getRecordCode());
        }
        WarehouseRecordE warehouseRecordE=warehouseRecordRepository.queryWarehouseRecordByRecordCodeAndTypeWithES(frChargeAgainstDO.getOriginWarehouseRecordCode(),originRecordType);
        if(null == warehouseRecordE){
            throw new RomeException(ResCode.STOCK_ERROR_1001,"根据originRecordCode查询后置单失败recordCode,"+frChargeAgainstDO.getRecordCode());
        }

        if(Objects.equals(String.valueOf(WarehouseRecordTypeVO.SHOP_RECEIVE.getType()),orderCostDOMain.getRecordType())
                || Objects.equals(String.valueOf(WarehouseRecordTypeVO.PREDICT_RETURN_DIRECT_IN_RECORD.getType()),orderCostDOMain.getRecordType())){
            //门店领用,退货预入库的冲销单,不调用财务中台接口验证
            return;
        }
        Map<String,StockBillItemCostRespDTO> itemCostMap=new HashMap<>();
        Map<String,OrderCostDetailDO> costDetailMap=new HashMap<>();
        if(!purchaseMap.containsKey(warehouseRecordE.getRecordType())){
            //非采购查询财务中台
            List<StockBillItemCostReqDTO> billItemCostList=new ArrayList<>();
            for (OrderCostDO orderCostDO : orderCostDOList) {
                StockBillItemCostReqDTO stockBillItemCostReqDTO = new StockBillItemCostReqDTO();
                stockBillItemCostReqDTO.setCompanyCode(orderCostDO.getBusinessType()==1?orderCostDO.getFromCompanyCode():orderCostDO.getToCompanyCode());
                stockBillItemCostReqDTO.setFinancialDate(DateUtil.parse(DateUtil.formatDate(orderCostDO.getFinanceDate()),DateUtil.NORM_DATE_PATTERN));
                stockBillItemCostReqDTO.setZtRecordCode(orderCostDO.getZtRecordCode());
                billItemCostList.add(stockBillItemCostReqDTO);
            }
            List<StockBillItemCostRespDTO> itemCostList=venusStockFacade.queryBillItemCost(billItemCostList,orderCostDOMain.getZtBusinessCode());
            if(CollectionUtils.isEmpty(itemCostList)){
                throw new RomeException(ResCode.STOCK_ERROR_1001,"非采购查询财务中台queryBillItemCost查询为空,recordCode,"+frChargeAgainstDO.getRecordCode());
            }
//            itemCostMap=itemCostList.stream().distinct().collect(Collectors.toMap(k -> String.format("%s,%s",k.getZtRecordCode(),k.getSkuCode()), k -> k));
            for (StockBillItemCostRespDTO stockBillItemCostRespDTO : itemCostList) {
                if(!itemCostMap.containsKey(String.format("%s,%s",stockBillItemCostRespDTO.getZtRecordCode(),stockBillItemCostRespDTO.getSkuCode()))){
                    itemCostMap.put(String.format("%s,%s",stockBillItemCostRespDTO.getZtRecordCode(),stockBillItemCostRespDTO.getSkuCode()),stockBillItemCostRespDTO);
                }
            }
        }else{
            if(WarehouseRecordTypeVO.WH_CHAIN_DIRECT_IN_RECORD.getType().equals(warehouseRecordE.getRecordType())){
                //如果是门店直送单据，只需要查询43类型单据进行冲销
                orderCostDOList=orderCostDOList.stream().filter(v->String.valueOf(WarehouseRecordTypeVO.WH_CHAIN_DIRECT_IN_RECORD.getType()).equals(v.getRecordType())).collect(Collectors.toList());
            }
            List<String> ztRecordCodes=orderCostDOList.stream().map(OrderCostDO::getZtRecordCode).distinct().collect(Collectors.toList());
            List<OrderCostDetailDO> detailList=orderCostDetailMapper.queryByZtRecordCodes(ztRecordCodes);
//            costDetailMap=detailList.stream().collect(Collectors.toMap(k -> String.format("%s,%s",k.getZtRecordCode(),k.getSkuCode()), k -> k));
            for (OrderCostDetailDO orderCostDetailDO : detailList) {
                if(!costDetailMap.containsKey(String.format("%s,%s",orderCostDetailDO.getZtRecordCode(),orderCostDetailDO.getSkuCode()))){
                    costDetailMap.put(String.format("%s,%s",orderCostDetailDO.getZtRecordCode(),orderCostDetailDO.getSkuCode()),orderCostDetailDO);
                }
            }
        }
        //补充具体明细--分组求和的sku_qty
        List<FrChargeAgainstDetailDO> list=frChargeAgainstDetailRepository.listByRecordCodeGroupBySkuCode(frChargeAgainstDO.getRecordCode());
        String ztBusinessCode = null;
        for (OrderCostDO orderCostDO : orderCostDOList) {
            ReversalValidateOrderDTO reversalValidateOrderDTO = new ReversalValidateOrderDTO();
            BeanUtils.copyProperties(orderCostDO,reversalValidateOrderDTO);
            reversalValidateOrderDTO.setRecordSubType("R" +reversalValidateOrderDTO.getRecordSubType());
            ztBusinessCode = orderCostDO.getZtBusinessCode();
            List<ReversalValidateOrderItemDTO> orderItemList=new ArrayList<>();
            for (FrChargeAgainstDetailDO frChargeAgainstDetailDO : list) {
                ReversalValidateOrderItemDTO reversalValidateOrderItemDTO = new ReversalValidateOrderItemDTO();
                reversalValidateOrderItemDTO.setActualQty(frChargeAgainstDetailDO.getSkuQty());
                reversalValidateOrderItemDTO.setSkuCode(frChargeAgainstDetailDO.getSkuCode());
                String costDetailMapKey=String.format("%s,%s",orderCostDO.getZtRecordCode(),frChargeAgainstDetailDO.getSkuCode());
                if(purchaseMap.containsKey(warehouseRecordE.getRecordType()) && costDetailMap.containsKey(costDetailMapKey)){
                    reversalValidateOrderItemDTO.setItemCostPrice(costDetailMap.get(costDetailMapKey).getItemCostPrice());
                    reversalValidateOrderItemDTO.setSellPriceNotax(costDetailMap.get(costDetailMapKey).getSellPriceNotax());
                } else if(itemCostMap.containsKey(costDetailMapKey)){
                        reversalValidateOrderItemDTO.setItemCostPrice(itemCostMap.get(costDetailMapKey).getItemCost());
                        reversalValidateOrderItemDTO.setSellPriceNotax(itemCostMap.get(costDetailMapKey).getSellPriceNotax());
                }
                orderItemList.add(reversalValidateOrderItemDTO);
            }
            reversalValidateOrderDTO.setOrderItemList(orderItemList);
            orderList.add(reversalValidateOrderDTO);
        }
        reversalValidateReqDTO.setOrderList(orderList);
        reversalValidateReqDTO.setZtBusinessCode(ztBusinessCode);
        venusStockFacade.reversalValidate(reversalValidateReqDTO);
    }

    @Override
    public List<ReverseDownLoadDTO> queryReverseExportList(ReverseExportQueryDTO reverseExportQueryDTO) {
        if (Objects.isNull(reverseExportQueryDTO.getBusinessType())) {
            throw new RomeException(ResCode.STOCK_ERROR_1001, "冲销类型不能同时为空");
        }
        if (Objects.isNull(reverseExportQueryDTO.getType())) {
            throw new RomeException(ResCode.STOCK_ERROR_1001, "冲销维度不能同时为空");
        }
        if (StringUtils.isEmpty(reverseExportQueryDTO.getSapOrderCode()) && StringUtils.isEmpty(reverseExportQueryDTO.getOriginWarehouseRecordCode())) {
            throw new RomeException(ResCode.STOCK_ERROR_1001, "业务单号和出入库单号不能同时为空");
        }
        List<WarehouseReceiveDTO> resultList = Lists.newArrayList();
        if (Objects.equals(reverseExportQueryDTO.getType(), 1)) {
            List<String> sapOrderCodes = Lists.newArrayList(Arrays.asList(reverseExportQueryDTO.getSapOrderCode().split("\n|\r")));
            sapOrderCodes=sapOrderCodes.stream().distinct().collect(Collectors.toList());
            for (String sapOrderCode : sapOrderCodes) {
                WarehouseRecordDTO warehouseRecordDTO = new WarehouseRecordDTO();
                warehouseRecordDTO.setBusinessType(reverseExportQueryDTO.getBusinessType());
                warehouseRecordDTO.setSapOrderCode(sapOrderCode);
                List<WarehouseReceiveDTO> tempList = this.queryReceiveByRecordCode(warehouseRecordDTO);
                if (CollectionUtils.isNotEmpty(tempList)) {
                    resultList.addAll(tempList);
                }
            }
        } else {
            List<String> warehouseRecordCodes = Lists.newArrayList(Arrays.asList(reverseExportQueryDTO.getOriginWarehouseRecordCode().split("\n|\r")));
            warehouseRecordCodes=warehouseRecordCodes.stream().distinct().collect(Collectors.toList());
            for (String warehouseRecordCode : warehouseRecordCodes) {
                WarehouseRecordDTO warehouseRecordDTO = new WarehouseRecordDTO();
                warehouseRecordDTO.setBusinessType(reverseExportQueryDTO.getBusinessType());
                warehouseRecordDTO.setRecordCode(warehouseRecordCode);
                List<WarehouseReceiveDTO> tempList = this.queryReceiveByRecordCode(warehouseRecordDTO);
                if (CollectionUtils.isNotEmpty(tempList)) {
                    resultList.addAll(tempList);
                }
            }
        }
        List<ReverseDownLoadDTO> list=Lists.newArrayList();
        for (WarehouseReceiveDTO warehouseReceiveDTO : resultList) {
            ChargeAgainstCreateDTO chargeAgainstCreateDTO=this.queryBatchListByReceiveCode(warehouseReceiveDTO);
            for (ChargeAgainstCreateDetailDTO chargeAgainstDetail : chargeAgainstCreateDTO.getChargeAgainstDetails()) {
                if(Objects.equals(chargeAgainstDetail.getIsReverse(),0)){
                    //只返回可冲销明细
                    ReverseDownLoadDTO reverseDownLoadDTO = new ReverseDownLoadDTO();
                    reverseDownLoadDTO.setBusinessType(chargeAgainstCreateDTO.getBusinessType());
                    reverseDownLoadDTO.setRecordCode(chargeAgainstCreateDTO.getOriginWarehouseRecordCode());
                    reverseDownLoadDTO.setOriginRecordCode(chargeAgainstCreateDTO.getOriginRecordCode());
                    reverseDownLoadDTO.setSapRecordCode(chargeAgainstCreateDTO.getOriginBusinessCode());
                    reverseDownLoadDTO.setOriginRealWarehouseCode(chargeAgainstCreateDTO.getOriginRealWarehouseCode());
                    reverseDownLoadDTO.setRealWarehouseCode(chargeAgainstCreateDTO.getOriginRealWarehouseCode());
                    reverseDownLoadDTO.setSkuCode(chargeAgainstDetail.getSkuCode());
                    reverseDownLoadDTO.setSkuQty(chargeAgainstDetail.getSkuQty());
                    reverseDownLoadDTO.setAccQty(chargeAgainstDetail.getSkuQty());
                    reverseDownLoadDTO.setRecordType(chargeAgainstCreateDTO.getOriginRecordType());
                    reverseDownLoadDTO.setUnit(chargeAgainstDetail.getUnit());
                    reverseDownLoadDTO.setUnitCode(chargeAgainstDetail.getUnitCode());
                    reverseDownLoadDTO.setRefDetailId(chargeAgainstDetail.getRefDetailId());
                    reverseDownLoadDTO.setBatchCode(chargeAgainstDetail.getBatchCode());
                    reverseDownLoadDTO.setRwBatchId(chargeAgainstDetail.getRwBatchId());
                    reverseDownLoadDTO.setSkuId(chargeAgainstDetail.getSkuId());
                    reverseDownLoadDTO.setReasonCode("01");
                    list.add(reverseDownLoadDTO);
                }
            }
        }
        return list;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void importExcel(List<ReverseDownLoadDTO> list) {
        if(CollectionUtils.isEmpty(list)){
            throw new RomeException(ResCode.STOCK_ERROR_1001,"导入数据不能为空");
        }
        for (int i = 0; i < list.size(); i++) {
            ReverseDownLoadDTO reverseDownLoadDTO=list.get(i);
            if(StringUtils.isEmpty(reverseDownLoadDTO.getCreator())){
                throw new RomeException(ResCode.STOCK_ERROR_1001,"第"+i+"行，冲销人员不能为空");
            }
            if(Objects.isNull(reverseDownLoadDTO.getFinanceDate())){
                throw new RomeException(ResCode.STOCK_ERROR_1001,"第"+i+"行，财务日期不能为空");
            }
        }
        Map<String, List<ReverseDownLoadDTO>> reverseMap = list.stream().collect(Collectors.groupingBy(ReverseDownLoadDTO::getOriginRecordCode));
        List<String> originRealWarehouseCodeList = list.stream().map(ReverseDownLoadDTO::getOriginRealWarehouseCode).distinct().collect(Collectors.toList());
        List<RealWarehouseE> originRealWarehouseList = realWarehouseRepository.queryRealWarehouseByInCodes(originRealWarehouseCodeList);
        Map<String,Long> originRealWarehouseMap = originRealWarehouseList.stream().collect(Collectors.toMap(RealWarehouseE::getRealWarehouseCode, RealWarehouseE::getId, (v1, v2) -> v1));


        List<String> realWarehouseCodeList = list.stream().map(ReverseDownLoadDTO::getRealWarehouseCode).distinct().collect(Collectors.toList());
        List<RealWarehouseE> realWarehouseList = realWarehouseRepository.queryRealWarehouseByInCodes(realWarehouseCodeList);
        Map<String,Long> realWarehouseMap = realWarehouseList.stream().collect(Collectors.toMap(RealWarehouseE::getRealWarehouseCode, RealWarehouseE::getId, (v1, v2) -> v1));


        CoreStockOpFactoryDO stockOpFactoryDO =new CoreStockOpFactoryDO();
        Boolean success = false;
        try {
            for (Map.Entry<String, List<ReverseDownLoadDTO>> entry : reverseMap.entrySet()) {
                List<ChargeAgainstCreateDetailDTO> chargeAgainstDetails=Lists.newArrayList();
                ChargeAgainstCreateDTO chargeAgainstCreateDTO=new ChargeAgainstCreateDTO();
                chargeAgainstCreateDTO.setStockOpFactoryDO(stockOpFactoryDO);
                chargeAgainstCreateDTO.setChargeAgainstDetails(chargeAgainstDetails);
                ReverseDownLoadDTO first=entry.getValue().get(0);
                chargeAgainstCreateDTO.setOriginWarehouseRecordCode(first.getRecordCode());
                chargeAgainstCreateDTO.setOriginRecordCode(first.getOriginRecordCode());
                chargeAgainstCreateDTO.setBusinessType(first.getBusinessType());
                chargeAgainstCreateDTO.setOriginBusinessCode(first.getSapRecordCode());
                chargeAgainstCreateDTO.setOriginRealWarehouseCode(first.getOriginRealWarehouseCode());
                chargeAgainstCreateDTO.setReasonCode(first.getReasonCode());
                chargeAgainstCreateDTO.setRealWarehouseCode(first.getRealWarehouseCode());
                chargeAgainstCreateDTO.setOperator(first.getCreator());
                chargeAgainstCreateDTO.setRemark(first.getRemark());
                chargeAgainstCreateDTO.setChargeAgainstDate(DateUtil.parseDate(first.getFinanceDate()));
                chargeAgainstCreateDTO.setOriginRecordType(first.getRecordType());
                if(originRealWarehouseMap.containsKey(chargeAgainstCreateDTO.getOriginRealWarehouseCode())){
                    chargeAgainstCreateDTO.setOriginRealWarehouseId(originRealWarehouseMap.get(chargeAgainstCreateDTO.getOriginRealWarehouseCode()));
                }
                if(realWarehouseMap.containsKey(chargeAgainstCreateDTO.getOriginRealWarehouseCode())){
                    chargeAgainstCreateDTO.setRealWarehouseId(realWarehouseMap.get(chargeAgainstCreateDTO.getRealWarehouseCode()));
                }
                for (ReverseDownLoadDTO reverseDownLoadDTO : entry.getValue()) {
                    ChargeAgainstCreateDetailDTO chargeAgainstCreateDetailDTO = new ChargeAgainstCreateDetailDTO();
                    chargeAgainstCreateDetailDTO.setSkuCode(reverseDownLoadDTO.getSkuCode());
                    chargeAgainstCreateDetailDTO.setSkuQty(reverseDownLoadDTO.getSkuQty());
                    chargeAgainstCreateDetailDTO.setAccQty(reverseDownLoadDTO.getAccQty());
                    chargeAgainstCreateDetailDTO.setUnitCode(reverseDownLoadDTO.getUnitCode());
                    chargeAgainstCreateDetailDTO.setBatchCode(reverseDownLoadDTO.getBatchCode());
                    chargeAgainstCreateDetailDTO.setUnit(reverseDownLoadDTO.getUnit());
                    chargeAgainstCreateDetailDTO.setSkuId(reverseDownLoadDTO.getSkuId());
                    chargeAgainstDetails.add(chargeAgainstCreateDetailDTO);
                }
                this.chargeAgainst(chargeAgainstCreateDTO);
            }
            stockOpFactoryDO.commit();
            success = true;
        } catch (RomeException e) {
            throw e;
        } catch (Exception e) {
            throw e;
        } finally {
            if (!success) {
                stockOpFactoryDO.redisRollBack();
            }
        }
    }


    @Override
    public List<ChargeAgainstCreateDetailDTO> queryReverseRecordDetailList(String recordCode) {
        List<FrChargeAgainstDetailDO> againstList=frChargeAgainstDetailRepository.listByRecordCode(recordCode);
        List<ChargeAgainstCreateDetailDTO> resultList=frChargeAgainstConvertor.detialDoToDTO(againstList);
        //查询商品信息
        List<Long> skuIds = resultList.stream().map(ChargeAgainstCreateDetailDTO::getSkuId).distinct().collect(Collectors.toList());
        List<SkuInfoExtDTO> skuInfoList = skuFacade.skusBySkuId(skuIds);
        resultList.forEach(detail -> {
            //设置商品名称
            SkuInfoExtDTO skuInfo = skuInfoList.stream().filter(sku -> sku.getId().equals(detail.getSkuId())).findFirst().orElse(null);
            if (skuInfo != null) {
                detail.setSkuName(skuInfo.getName());
            }
        });
        return resultList;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void cancelReverseRecord(String recordCode, Long modifier) {
        ChargeAgainstE frChargeAgainstDO = frChargeAgainstRepository.getByRecordCode(recordCode);
        if (null == frChargeAgainstDO) {
            throw new RomeException(ResCode.STOCK_ERROR_1001, "单据不存在," + recordCode);
        }
        if (frChargeAgainstDO.getRecordStatus() == 2) {
            return;
        }
        if (frChargeAgainstDO.getRecordStatus() == 3 || frChargeAgainstDO.getRecordStatus() == 4) {
            throw new RomeException(ResCode.STOCK_ERROR_1001, "单据已完成或部分完成，无法取消," + recordCode);
        }
        //更新前置单状态为已确认
        int i = frChargeAgainstRepository.updateToCancel(frChargeAgainstDO.getId(), modifier);
        if (i == 0) {
            throw new RomeException(ResCode.STOCK_ERROR_1001, "更新前置单失败," + recordCode);
        }
        List<Long> relation = frontWarehouseRecordRelationRepository.queryWarehouseRecordIdByRecord(recordCode);
        if (CollectionUtils.isEmpty(relation)) {
            throw new RomeException(ResCode.STOCK_ERROR_1001, "查询后置单关联失败," + recordCode);
        }
        WarehouseRecordE warehouseRecordE = warehouseRecordRepository.getRecordWithDetailById(relation.get(0));
        //已经出入库的不能取消
        if (WarehouseRecordStatusVO.OUT_ALLOCATION.getStatus().equals(warehouseRecordE.getRecordStatus()) || WarehouseRecordStatusVO.IN_ALLOCATION.getStatus().equals(warehouseRecordE.getRecordStatus())) {
            throw new RomeException(ResCode.STOCK_ERROR_1040, "取消订单失败,当前订单已经出库或入库," + recordCode);
        }
        int j = warehouseRecordRepository.updateToCanceled(warehouseRecordE.getId());
        if (j == 0) {
            throw new RomeException(ResCode.STOCK_ERROR_1001, "更新后置单失败," + recordCode);
        }
        //出库冲销，对应的入库单取消时,无需操作库存
        if (2 == warehouseRecordE.getBusinessType()) {
            this.cancelWmsRecordCode(warehouseRecordE);
            return;
        } else {
            //解锁库存
            boolean isSuccess = false;
            CoreStockOpFactoryDO stockOpFactoryDO = new CoreStockOpFactoryDO();
            CoreRealStockOpDO coreRealStockOpDO = new CoreRealStockOpDO();
            try {
                //按照虚仓或实仓解锁
                ShopReplenishWarehouseRecordE replenishListE = replenishWarehouseConvertor.whToEntity(warehouseRecordE);
                coreRealStockOpDO = replenishListE.packUnlockStockObjForBigOrder(warehouseRecordE, coreRealStockOpDO);
                if (!CollectionUtils.isEmpty(coreRealStockOpDO.getDetailDos())) {
                    stockOpFactoryDO.reset(coreRealStockOpDO);
                    coreRealWarehouseStockRepository.unlockStock(coreRealStockOpDO);
                }
                stockOpFactoryDO.commit();
                this.cancelWmsRecordCode(warehouseRecordE);
                isSuccess = true;
            } catch (RomeException e) {
                log.error(e.getMessage(), e);
                throw new RomeException(e.getCode(), e.getMessage() + recordCode);
            } catch (Exception e) {
                log.error(e.getMessage(), e);
                throw new RomeException(ResCode.STOCK_ERROR_1001, e.getMessage() + recordCode);
            } finally {
                //模型成功，业务等处理失败，需要回滚
                if (!isSuccess) {
                    stockOpFactoryDO.redisRollBack();
                }
            }
        }
    }

    /**
     * 取消WMS
     */
    private void cancelWmsRecordCode(WarehouseRecordE warehouseRecordE){
        //取消WMS
        if (WarehouseRecordConstant.SYNC_WMS__SUCCES.equals(warehouseRecordE.getSyncWmsStatus())) {
            RealWarehouseWmsConfigDO realWarehouseWmsConfigDO = realWarehouseWmsRedis.findWmsInformationById(warehouseRecordE.getRealWarehouseId());
            boolean status;
            if (realWarehouseWmsConfigDO != null) {
                status = wmsOutService.orderCancel(warehouseRecordE.getRecordCode());
            }else{
                status = wmsOutService.cancelMMOrder(warehouseRecordE.getRecordCode(),warehouseRecordE.getBusinessType());
            }
            AlikAssert.isTrue(status, ResCode.STOCK_ERROR_1205, ResCode.STOCK_ERROR_1205_DESC);
        }
    }

    @Override
    public List<ChargeAgainstConfigDTO> queryConfigList() {
        List<FrChargeAgainstConfigDO> againstConfigDOS = frChargeAgainstConfigRepository.listAll();
        return frChargeAgainstConvertor.configDoToDTO(againstConfigDOS);
    }

    @Override
    public List<ChargeAgainstDTO> queryNeedNotifyRecordList(ChargeAgainstDTO chargeAgainstDTO) {
        PageHelper.startPage(chargeAgainstDTO.getPageIndex(),chargeAgainstDTO.getPageSize());
        List<ChargeAgainstDTO> list=frChargeAgainstRepository.queryNeedNotifyRecordList(chargeAgainstDTO);
        return list;
    }

    @Override
    public void pushNotifyData(ChargeAgainstDTO chargeAgainstDTO) {
        List<FrChargeAgainstConfigDO> frChargeAgainstConfigDOS = frChargeAgainstConfigRepository.listAll();
        Map<Object, FrChargeAgainstConfigDO> configMap = RomeCollectionUtil.listforMap(frChargeAgainstConfigDOS, "frontRecordType");
        FrChargeAgainstConfigDO chargeAgainstConfigDO = configMap.get(chargeAgainstDTO.getRecordType());
        AlikAssert.notNull(chargeAgainstConfigDO, "999", "未找到配置信息");
        AbstractChargeAgainst abstractChargeAgainst = (AbstractChargeAgainst) SpringBeanUtil.getBean(chargeAgainstConfigDO.getServiceName());
        AlikAssert.notNull(abstractChargeAgainst, "999", "服务名称配置错误");
        ChargeAgainstE chargeAgainstE = frChargeAgainstConvertor.dtoToEntity(chargeAgainstDTO);
        AlikAssert.isTrue(chargeAgainstE.getRecordStatus() == 4, "999", "未收货完成，无法推送");
        abstractChargeAgainst.pushNotice(chargeAgainstE, chargeAgainstConfigDO);
    }


    /**
     * 创建并确认冲销单--仓储店退货预入库
     *
     * @param chargeAgainstCreateDTO 入参
     * @return 布尔
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public String createAndConfirmChargeAgainstRecord(ChargeAgainstCreateDTO chargeAgainstCreateDTO) {
        String outRecodeCode = "";
        // 幂等
        List<FrChargeAgainstDO> frChargeAgainstDOList1 = frChargeAgainstRepository.getByOriginWarehouseRecordCode(chargeAgainstCreateDTO.getOriginWarehouseRecordCode(), chargeAgainstCreateDTO.getOriginRecordCode());
        if (CollectionUtils.isNotEmpty(frChargeAgainstDOList1)) {
            return frontWarehouseRecordRelationRepository.getRecordCodesByFrontRecordCode(frChargeAgainstDOList1.get(0).getRecordCode()).get(0);
        }
        //1.创建冲销单
        CoreStockOpFactoryDO stockOpFactoryDO = chargeAgainstCreateDTO.getStockOpFactoryDO();
        boolean success = false;
        try {
            outRecodeCode = this.chargeAgainst(chargeAgainstCreateDTO);
            //2.冲销单确认
            //原入库单号
            String originWarehouseRecordCode = chargeAgainstCreateDTO.getOriginWarehouseRecordCode();
            //入库单冲销为收货单号
            String originRecordCode = chargeAgainstCreateDTO.getOriginRecordCode();
            List<FrChargeAgainstDO> frChargeAgainstDOList = frChargeAgainstRepository.getByOriginWarehouseRecordCode(originWarehouseRecordCode, originRecordCode);
            //当前场景只会有一笔单
            if (CollectionUtils.isEmpty(frChargeAgainstDOList)) {
                throw new RomeException(ResCode.STOCK_ERROR_1001, "仓储店退货预入库冲销单据不存在");
            }
            ChargeAgainstE frChargeAgainstDO = frChargeAgainstConvertor.doToEntity(frChargeAgainstDOList.get(0));
            if (frChargeAgainstDO.getRecordStatus() == 1) {
                return null;
            }
            if (frChargeAgainstDO.getRecordStatus() != 0) {
                throw new RomeException(ResCode.STOCK_ERROR_1001, "仓储店退货预入库冲销单据状态不是初始状态");
            }
            //调用财务中台接口，用原单查询是否能冲销
            chargeAgainstService.confirmReverse(frChargeAgainstDO);
            chargeAgainstService.confirmReverseRecord(Collections.singletonList(frChargeAgainstDO.getRecordCode()), 1L, Boolean.FALSE);
            //wms 回调在 core 执行
            stockOpFactoryDO.commit();
            success = true;
        } catch (RomeException e) {
            throw e;
        } catch (Exception e) {
            throw e;
        } finally {
            if (!success) {
                stockOpFactoryDO.redisRollBack();
            }
        }
        return outRecodeCode;
    }
}
