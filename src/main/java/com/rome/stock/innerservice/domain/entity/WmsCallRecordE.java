package com.rome.stock.innerservice.domain.entity;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Component;

/**
 * @Description: E
 * <p>
 * @Author: chuwen<PERSON><PERSON>  2019/6/13
 */
@Data
@Slf4j
@Component
@Scope("prototype")
@EqualsAndHashCode(callSuper = false)
public class WmsCallRecordE extends BaseE {

    /**
     * 唯一主键
     */
    private Long id;
    /**
     * 仓库系统编码
     */
    private Integer wmsCode;
    /**
     * 出入库单据编号
     */
    private String recordCode;
    /**
     * 请求服务名
     */
    private String requestService;
    /**
     * 请求url
     */
    private String requestUrl;
    /**
     * 请求内容
     */
    private String requestContent;
    /**
     * 响应内容
     */
    private String responseContent;
    /**
     * 交互状态 0：失败  1:成功
     */
    private Integer status;

    /**
     * 单据类型--下发WMS预警专用字段
     */
    private Integer recordType;

}
