package com.rome.stock.innerservice.domain.entity;

import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.rome.stock.common.enums.bms.SkuTypeVo;
import com.rome.stock.common.enums.bms.WorkTypeVO;
import com.rome.stock.innerservice.api.dto.bms.BmsDTO;
import com.rome.stock.innerservice.api.dto.bms.BmsRecordDTO;
import com.rome.stock.innerservice.common.RomeCollectionUtil;
import com.rome.stock.innerservice.common.StringUtils;
import com.rome.stock.innerservice.domain.entity.warehouserecord.WarehouseRecordDetail;
import com.rome.stock.innerservice.domain.entity.warehouserecord.WarehouseRecordE;
import com.rome.stock.innerservice.domain.repository.BmsRecordRepository;
import com.rome.stock.innerservice.facade.StockBmsFacade;
import com.rome.stock.innerservice.infrastructure.dataobject.RealWarehouseWmsConfigDO;
import com.rome.stock.innerservice.remote.base.dto.KpChannelAreaInfoDTO;
import com.rome.stock.innerservice.remote.base.facade.BaseDataFacade;
import com.rome.stock.innerservice.remote.item.dto.SkuUnitExtDTO;
import com.rome.stock.innerservice.remote.item.facade.SkuFacade;
import com.rome.stock.innerservice.remote.venus.dto.SkuPriceResponseDTO;
import com.rome.stock.innerservice.remote.venus.facade.VenusStockFacade;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Objects;


/**
 * <AUTHOR>
 * @Description bms出入库
 */
@Data
@Slf4j
@Component
@Scope("prototype")
@EqualsAndHashCode(callSuper = false)
public class BmsRecordE extends BaseE {

    @Resource
    private BmsRecordRepository bmsRecordRepository;
    @Resource
    private SkuFacade skuFacade;
    @Resource
    private BaseDataFacade baseDataFacade;
    @Resource
    private VenusStockFacade venusStockFacade;

    /**
     * 来源系统
     */
    private String oriSys;
    /**
     * 流水号
     */
    private String serialNo;
    /**
     * 单据编码
     */
    private String warehouseRecordCode;
    /**
     * 订购单
     */
    private String omsOrderNo;
    /**
     * 业务单号
     */
    private String custOrderNo;
    /**
     * 客户编码
     */
    private String custCode;
    /**
     * 客户名称
     */
    private String custName;
    /**
     * 仓库编码
     */
    private String whseCode;
    /**
     * 仓库名称
     */
    private String warehouseCodeDesc;
    /**
     * 单据类型	order_type
     */
    private Integer orderType;
    /**
     * 订单渠道
     */
    private String saleChannel;
    /**
     * 多次确认标识:0-全部;1-部分;默认0
     */
    private Integer confirmFlag=0;
    /**
     * 服务产品:1-收货;2-发货;
     */
    private String confirmType;
    /**
     * 确认时间
     */
    private Date stockDate;
    /**
     * 是否电商toc单据
     */
    private Integer isToc;

    /**
     * 商品明细
     */
    private List<BmsRecordSkuDetailE> bmsSkuDetails;

    /**
     * 默认渠道
     */
    public static final String DefaultSaleChannel="LC001";

    /**
     * 添加bms出入库数据
     */
    public void addBmsRecord() {
        long id = bmsRecordRepository.insertBmsRecord(this);
        this.setId(id);
        this.getBmsSkuDetails().forEach(detailE -> detailE.setBmsRecord(this));
        if (CollectionUtils.isNotEmpty(this.getBmsSkuDetails())){
            bmsRecordRepository.insertBmsRecordSkuDetails(this.getBmsSkuDetails());
        }
    }

    /**
     * 初始化bms出入库数据
     * @param serialNo
     * @param bmsRecordDTO
     * @param realWarehouseE
     * @param wmsConfigDO
     */
    public void initBmsRecord(String serialNo, BmsRecordDTO bmsRecordDTO,
                              RealWarehouseE realWarehouseE, RealWarehouseWmsConfigDO wmsConfigDO) {
        WarehouseRecordE recordE = bmsRecordDTO.getWarehouseRecordE();
        this.setOriSys(String.valueOf(wmsConfigDO.getWmsCode()));
        this.setSerialNo(serialNo);
        this.setWarehouseRecordCode(recordE.getRecordCode());
        this.setIsToc(0);
        this.setWhseCode(realWarehouseE.getRealWarehouseCode());
        Date generateDate= Objects.nonNull(recordE.getDeliveryTime())?recordE.getDeliveryTime():recordE.getOutOrInTime();
        this.setStockDate(Objects.nonNull(generateDate)?generateDate:recordE.getCreateTime());
        //业务单号(92单号)
        this.setCustOrderNo(recordE.getSapOrderCode());
        //订单类型
        this.setOrderType(recordE.getRecordType());
        //下单方(公司，1.领用是成本中心公司，2.其他是送达方公司 3.采购-组织对应的公司)
        BmsDTO bmsDTO=bmsRecordDTO.getBmsDTO();
        if (Objects.isNull(bmsDTO)){
            bmsDTO = StockBmsFacade.findOrderCustomCodeDTO(recordE);
        }
        if (StringUtils.isNotEmpty(bmsDTO.getOrderCustCode())){
            this.setCustCode(bmsDTO.getOrderCustCode());
        }
        this.setSaleChannel(bmsDTO.getKpChannelCode());
    }


    /**
     * 初始化商品明细数据
     * @param recordE
     * @param detailList
     * @param bmsSkuDetailES
     */
    public void initBmsSkuDetail(WarehouseRecordE recordE,RealWarehouseE realWarehouseE,
                                           List<WarehouseRecordDetail> detailList,
                                           List<BmsRecordSkuDetailE> bmsSkuDetailES) {
        List<String> skuCodes= RomeCollectionUtil.getDistinctValueList(detailList,"skuCode");
        Long merchantId=Objects.nonNull(recordE.getMerchantId())?recordE.getMerchantId():skuFacade.getDefaultMerchantId();
        List<SkuUnitExtDTO> skuUnitList = skuFacade.querySkuUnits(skuCodes,merchantId);
        //商品单位map,key:skuCode+"_"+unitCode
        Map<String,SkuUnitExtDTO> unitMap = Maps.newHashMap();
        skuUnitList.forEach(item->unitMap.put(item.getSkuCode()+"_"+item.getUnitCode(),item));
        //移动平均价
        List<SkuPriceResponseDTO> skuPriceList=venusStockFacade.queryUnitPriceByStockPlant(Lists.newArrayList(realWarehouseE.getFactoryCode()),skuCodes,20);
        Map<String,SkuPriceResponseDTO> priceMap = Maps.newHashMap();
        skuPriceList.forEach(item->priceMap.put(item.getSkuCode(),item));
        for (WarehouseRecordDetail detail:detailList){
            if (detail.getActualQty().compareTo(BigDecimal.ZERO)<=0){
                continue;
            }
            String boxUnitKey= String.format("%s_KAR",detail.getSkuCode());
            boolean hasBoxUnit = unitMap.containsKey(boxUnitKey);
            BmsRecordSkuDetailE bmsSkuDetailE = new BmsRecordSkuDetailE();
            bmsSkuDetailE.setSkuCode(detail.getSkuCode());
            bmsSkuDetailE.setSkuId(detail.getSkuId());
            bmsSkuDetailE.setSkuName(detail.getSkuName());
            bmsSkuDetailE.setBasicUnitCode(detail.getUnitCode());
            bmsSkuDetailE.setBasicUnit(detail.getUnit());
            bmsSkuDetailE.setSkuQty(detail.getActualQty());
            if (hasBoxUnit){
                SkuUnitExtDTO unitExt=unitMap.get(boxUnitKey);
                bmsSkuDetailE.setBoxScale(Objects.nonNull(unitExt.getScale())?unitExt.getScale():BigDecimal.ZERO);
                bmsSkuDetailE.setBoxWeight(Objects.nonNull(unitExt.getGrossWeight())?unitExt.getGrossWeight():BigDecimal.ZERO);
                bmsSkuDetailE.setBoxVolume(Objects.nonNull(unitExt.getVolume())?unitExt.getVolume():BigDecimal.ZERO);
                //整箱数量
                BigDecimal[] divideAndRemainder=detail.getActualQty().divideAndRemainder(unitExt.getScale());
                bmsSkuDetailE.setFullBoxQty(divideAndRemainder[0]);
                //拆零件数
                bmsSkuDetailE.setSplitQty(Objects.equals(unitExt.getScale(),BigDecimal.ZERO)?detail.getActualQty():divideAndRemainder[1]);
                //箱数
                bmsSkuDetailE.setBoxQty(BigDecimal.ZERO.compareTo(bmsSkuDetailE.getSplitQty())<0?
                        bmsSkuDetailE.getFullBoxQty().add(BigDecimal.ONE):bmsSkuDetailE.getFullBoxQty());
            }
            //拆零商品毛重
            if (Objects.equals(detail.getUnitCode(),"KG")){
                bmsSkuDetailE.setBasicWeight(BigDecimal.ONE);
            }else {
                if (unitMap.containsKey(detail.getSkuCode()+"_"+detail.getUnitCode())){
                    BigDecimal basicGrossWeight=unitMap.get(detail.getSkuCode()+"_"+detail.getUnitCode()).getGrossWeight();
                    bmsSkuDetailE.setBasicWeight(Objects.nonNull(basicGrossWeight)?basicGrossWeight:BigDecimal.ZERO);
                }
            }
            BigDecimal splitWeight=bmsSkuDetailE.getBasicWeight().multiply(bmsSkuDetailE.getSplitQty());
            //毛重
            BigDecimal grossWeight=bmsSkuDetailE.getBoxWeight().multiply(bmsSkuDetailE.getFullBoxQty()).add(splitWeight);
            bmsSkuDetailE.setWeight(setScaleHalfUp(grossWeight,3));
            //拆零体积
            BigDecimal splitVolume=BigDecimal.ZERO;
            if (Objects.nonNull(bmsSkuDetailE.getBoxScale()) && bmsSkuDetailE.getBoxScale().compareTo(BigDecimal.ZERO)>0){
                //出于精度原因，先乘后除
                splitVolume=bmsSkuDetailE.getBoxVolume().multiply(bmsSkuDetailE.getSplitQty()).divide(bmsSkuDetailE.getBoxScale(), RoundingMode.HALF_UP);
            }
            BigDecimal totalVolume = bmsSkuDetailE.getBoxVolume().multiply(bmsSkuDetailE.getFullBoxQty()).add(splitVolume);
            bmsSkuDetailE.setVolume(setScaleHalfUp(totalVolume,3));
            if (priceMap.containsKey(detail.getSkuCode())){
                BigDecimal price=Objects.nonNull(priceMap.get(detail.getSkuCode()).getUnitPrice())?
                        priceMap.get(detail.getSkuCode()).getUnitPrice():BigDecimal.ZERO;
                bmsSkuDetailE.setSkuPrice(price);
            }
            bmsSkuDetailES.add(bmsSkuDetailE);
        }
    }


    private BigDecimal setScaleHalfUp(BigDecimal source, int newScale){
        if (source == null || source.scale() == newScale) {
            return source;
        }
        return source.setScale(newScale, RoundingMode.HALF_UP);
    }

    /**
     * 重新计算数据
     * @param workTypeVO
     * @param packageTotalVolume
     * @param packageTotalWeight
     * @param packageSize
     */
    public void reCalTocWork(WorkTypeVO workTypeVO, BigDecimal packageTotalVolume, BigDecimal packageTotalWeight, int packageSize) {
        //如果是仓配一体，则使用包裹上的数据
        boolean needPackage = Objects.equals(workTypeVO.getType(), WorkTypeVO.RETRIEVAL_CK2.getType());
        for (int i = 0; i < this.getBmsSkuDetails().size(); i++) {
            BmsRecordSkuDetailE bmsRecordSkuDetailE = this.getBmsSkuDetails().get(i);
            //都设置为食品
            if (i == 0) {
                //重量体积取包裹表
                bmsRecordSkuDetailE.setVolume(setScaleHalfUp(packageTotalVolume,3));
                bmsRecordSkuDetailE.setBoxVolume(setScaleHalfUp(packageTotalVolume,3));
                bmsRecordSkuDetailE.setWeight(setScaleHalfUp(packageTotalWeight,3));
                bmsRecordSkuDetailE.setBoxWeight(setScaleHalfUp(packageTotalWeight,3));
                //需要使用包裹数据
                if (needPackage) {
                    //如果是仓配一体，整箱，拆零箱数取包裹, 全部设置到第一条记录上
                    bmsRecordSkuDetailE.setFullBoxQty(new BigDecimal(packageSize));
                    bmsRecordSkuDetailE.setBoxQty(new BigDecimal(packageSize));
                    //拆零箱数为0
                    bmsRecordSkuDetailE.setSplitQty(BigDecimal.ZERO);
                }
            } else {
                //后面的清0
                bmsRecordSkuDetailE.setVolume(BigDecimal.ZERO);
                bmsRecordSkuDetailE.setBoxVolume(BigDecimal.ZERO);
                bmsRecordSkuDetailE.setWeight(BigDecimal.ZERO);
                bmsRecordSkuDetailE.setBoxWeight(BigDecimal.ZERO);
                //需要使用包裹数据
                if (needPackage) {
                    bmsRecordSkuDetailE.setFullBoxQty(BigDecimal.ZERO);
                    bmsRecordSkuDetailE.setBoxQty(BigDecimal.ZERO);
                    bmsRecordSkuDetailE.setSplitQty(BigDecimal.ZERO);
                }
            }
        }
    }
}
