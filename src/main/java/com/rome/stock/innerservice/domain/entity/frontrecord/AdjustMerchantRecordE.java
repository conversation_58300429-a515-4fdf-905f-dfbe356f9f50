package com.rome.stock.innerservice.domain.entity.frontrecord;

import java.util.List;

import javax.annotation.Resource;

import org.apache.commons.lang3.StringUtils;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Component;

import com.rome.stock.innerservice.constant.FrontRecordTypeVO;
import com.rome.stock.innerservice.domain.repository.frontrecord.FrAdjustMerchantRepository;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.extern.slf4j.Slf4j;

/**
 * 商家库存调整
 * <AUTHOR>
 */
@Component
@Scope("prototype")
@Data
@EqualsAndHashCode(callSuper = false)
@Slf4j
public class AdjustMerchantRecordE extends AbstractFrontRecord {
    /**
     * 出向实体仓库id
     */
    private Long realWarehouseId;

    /**
     * 门店仓编号
     */
    private String shopCode;
    /**
     * 非标品标识
     */
    private String unStandardFlag;
    /**
     * 库存调整备注
     */
    private String remark;

    private List<AdjustMerchantRecordDetailE> details;
    
    /**
     * 渠道编码
     */
    private String channelCode;

    @Resource
    private FrAdjustMerchantRepository frAdjustMerchantRepository;

    /**
     * 创建商家库存调整前置单
     */
    public void addFrontRecord() {
        //不考虑虚拟出库，统一处理
        this.setRecordType(this.getRecordType()==null?FrontRecordTypeVO.ADJUST_MERCHANT_RECORD.getType():this.getRecordType());
        //生成单据编号
        this.initFrontRecord(FrontRecordTypeVO.ADJUST_MERCHANT_RECORD.getCode(), this.details, this.getMerchantId());
        if (StringUtils.isBlank(this.getRemark())) {
            this.setRemark("");
        }
        //插入采购单据
        long id = frAdjustMerchantRepository.insertRecord(this);
        this.setId(id);
        //前置单详情关联主数据
        this.details.forEach(detail -> detail.setFrontRecordDetail(this));
        //插入前置单单据详情
        frAdjustMerchantRepository.insertDetails(this.details);
    }

}
