package com.rome.stock.innerservice.domain.service.impl;

import com.alibaba.fastjson.JSON;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import com.rome.arch.core.exception.RomeException;
import com.rome.arch.util.dynamic.DynamicDataSourceEnum;
import com.rome.arch.util.dynamic.TargetDataSource;
import com.rome.stock.common.utils.DateUtil;
import com.rome.stock.core.api.dto.ChannelSalesStockDTO;
import com.rome.stock.core.constant.StockCoreConsts;
import com.rome.stock.core.domain.repository.CoreChannelSalesRepository;
import com.rome.stock.core.domain.repository.CoreRealWarehouseStockRepository;
import com.rome.stock.core.infrastructure.dataobject.core.CoreRealWarehouseStockDO;
import com.rome.stock.innerservice.api.dto.*;
import com.rome.stock.innerservice.api.dto.frontrecord.*;
import com.rome.stock.innerservice.api.dto.groupbuy.CombineSkuInfoForRm;
import com.rome.stock.innerservice.api.dto.groupbuy.SkuInfoForRw;
import com.rome.stock.innerservice.api.dto.groupbuy.SkuStockForRw;
import com.rome.stock.innerservice.api.dto.groupbuy.SplitSkuInfoForRm;
import com.rome.stock.innerservice.api.dto.supplier.QuerySupplierSkuStockDTO;
import com.rome.stock.innerservice.api.dto.supplier.SupplierSkuStockDTO;
import com.rome.stock.innerservice.common.*;
import com.rome.stock.innerservice.constant.ParamValidator;
import com.rome.stock.innerservice.constant.RealWarehouseTypeVO;
import com.rome.stock.innerservice.domain.convertor.RealWarehouseConvertor;
import com.rome.stock.innerservice.domain.convertor.SkuRmStockConvertor;
import com.rome.stock.innerservice.domain.convertor.SkuStockConvertor;
import com.rome.stock.innerservice.domain.convertor.StockConvert;
import com.rome.stock.innerservice.domain.entity.ChannelSalesE;
import com.rome.stock.innerservice.domain.entity.RealWarehouseE;
import com.rome.stock.innerservice.domain.entity.VirtualWarehouseE;
import com.rome.stock.innerservice.domain.repository.*;
import com.rome.stock.innerservice.domain.service.StockQueryService;
import com.rome.stock.innerservice.infrastructure.dataobject.*;
import com.rome.stock.innerservice.infrastructure.mapper.BatchStockMapper;
import com.rome.stock.innerservice.infrastructure.mapper.EntrySaleStockMapper;
import com.rome.stock.innerservice.infrastructure.mapper.RealWarehouseStockMapper;
import com.rome.stock.innerservice.remote.base.dto.StoreDTO;
import com.rome.stock.innerservice.remote.base.facade.ShopFacade;
import com.rome.stock.innerservice.remote.item.dto.*;
import com.rome.stock.innerservice.remote.item.facade.SkuFacade;
import com.rome.stock.innerservice.remote.orderCneter.dto.ReplenishShopSkuReqDTO;
import com.rome.stock.innerservice.remote.orderCneter.facade.OrderCenterFacade;
import com.rome.stock.innerservice.remote.venus.dto.SkuPriceResponseDTO;
import com.rome.stock.innerservice.remote.venus.facade.VenusStockFacade;
import com.rome.stock.wms.config.BaseinfoConfiguration;
import com.rome.stock.wms.config.BaseinfoProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.map.HashedMap;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

import static java.math.BigDecimal.ROUND_DOWN;

/**
 * 类StockQueryServiceImpl的实现描述：通用库存查询接口
 *
 * <AUTHOR> 2021/4/23 15:42
 */
@Slf4j
@Service
public class StockQueryServiceImpl implements StockQueryService {

    @Autowired
    private SkuQtyUnitTools skuQtyUnitTools;
    @Autowired
    private SkuStockConvertor skuStockConvertor;
    @Autowired
    private SkuRmStockConvertor skuRmStockConvertor;
    @Autowired
    private SkuFacade skuFacade;
    @Autowired
    private CoreChannelSalesRepository coreChannelSalesRepository;

    @Autowired
    private VirtualWarehouseStockRepository warehouseStockRepository;

    @Autowired
    private VirtualWarehouseRepository warehouseRepository;

    @Autowired
    private StockConvert stockConvert;

    @Autowired
    private ChannelSalesRepository channelSalesRepository;

    @Autowired
    private VirtualWarehouseRepository virtualWarehouseRepository;

    @Autowired
    private VirtualWarehouseStockRepository virtualWarehouseStockRepository;

    private final ParamValidator validator = ParamValidator.INSTANCE;

    @Resource
    private ShopFacade shopFacade;

    @Resource
    private ZdeliverRelationRepository zdeliverRelationRepository;

    @Resource
    private RealWarehouseRepository realWarehouseRepository;

    @Resource
    private CoreRealWarehouseStockRepository coreRealWarehouseStockRepository;

    @Autowired
    private SkuInfoTools skuInfoTools;

    @Resource
    private VenusStockFacade venusStockFacade;
    @Resource
    private OrderCenterFacade orderCenterFacade;
    @Resource
    private RealWarehouseConvertor realWarehouseConvertor;
    @Resource
    private BatchStockRepository batchStockRepository;
    @Resource
    private RealWarehouseStockMapper realWarehouseStockMapper;
    @Resource
    private EntrySaleStockMapper entrySaleStockMapper;
    @Resource
    private BatchStockMapper batchStockMapper;
    /**
     * 批量查询渠道可售库存
     *
     * @param sis 待查询sku集合
     * @return
     */
    @Override
    public List<SkuStock> querySkuStockList(List<SkuInfo> sis) {
        List<UnitAndBaseUnitInfoDTO> unitList =  new ArrayList();
        Map<SkuIdUnitCodeDTO, UnitAndBaseUnitInfoDTO> unitMap = new HashedMap();
        List<Long> skuIds = new ArrayList<>();
        List<SkuInfo> byId = new ArrayList<>();
        //先校验数据完整性
        String method = "";
        for (SkuInfo si : sis) {
            if(si.getSkuId() == null && StringUtils.isBlank(si.getSkuCode())){
                throw new RomeException(ResCode.STOCK_ERROR_1002, ResCode.STOCK_ERROR_1002_DESC);
            }
            if(si.getSkuId() != null){
                skuIds.add(si.getSkuId());
                byId.add(si);
            }
        }
        //根据skuId 查询
        List<SkuUnitAndCombineExtDTO> extList = new ArrayList<>();
        if(CollectionUtils.isNotEmpty(skuIds)){
            method = method + "ById";
            //根据ID查询商品
            extList = skuFacade.querySkuUnitAndCombinesBySkuIdAndUnitCodeAndMerchantId(byId);
        }
        Map<Long, SkuUnitAndCombineExtDTO> extMap =  RomeCollectionUtil.listforMap(extList, "skuIdInfo", null);
        List<SkuInfo> byCode = new ArrayList<>();
        for (SkuInfo si : sis) {
            SkuUnitAndCombineExtDTO dto  = extMap.get(si.getSkuIdInfo());
            if(dto != null){
                si.setCombineType(dto.getCombineType());
                si.setCombineInfo(dto.getCombineInfo());
                si.setSkuCode(dto.getSkuCode());
                unitList.addAll(dto.getUnitInfo());
            }else if(StringUtils.isNotBlank(si.getSkuCode())){
                byCode.add(si);
            }
        }
        //根据code查询
        if(CollectionUtils.isNotEmpty(byCode)){
            method = method + "ByCode";
            List<SkuUnitAndCombineExtDTO> tempList = skuFacade.querySkuUnitAndCombinesBySkuCodeAndUnitCodeAndMerchantId(byCode);
            if(CollectionUtils.isNotEmpty(tempList)){
                Map<String, SkuUnitAndCombineExtDTO> tempMap =  RomeCollectionUtil.listforMap(tempList, "skuCodeInfo", null);
                for (SkuInfo si : sis) {
                    SkuUnitAndCombineExtDTO dto  = tempMap.get(si.getSkuCodeInfo());
                    if(dto != null){
                        si.setCombineType(dto.getCombineType());
                        si.setCombineInfo(dto.getCombineInfo());
                        si.setSkuId(dto.getSkuId());
                        si.setSkuCode(dto.getSkuCode());
                        unitList.addAll(dto.getUnitInfo());
                    }
                }
                extList.addAll(tempList);
            }
        }
        log.warn(CoreKibanaLog.getJobLog("queryStock", "queryStock" + method, "获取库存" , ""));
        AlikAssert.isNotEmpty(extList, ResCode.STOCK_ERROR_1036, ResCode.STOCK_ERROR_1036_DESC);
        //去掉重复的数据
        this.removeDuplicate(sis);
        //设置单位的map
        if(CollectionUtils.isNotEmpty(unitList)) {
            for (UnitAndBaseUnitInfoDTO dto : unitList) {
                unitMap.put(new SkuIdUnitCodeDTO(dto.getSkuId(), dto.getUnitCode()), dto);
            }
        }
        //区分单品和组合品
        List<SkuInfo> singleSkuList = new ArrayList<>();
        List<SkuInfo> combineSkuList = new ArrayList<>();
        for (SkuInfo si : sis) {
            if(si.getCombineType() == 1){
                combineSkuList.add(si);
            }else{
                singleSkuList.add(si);
            }
        }
        //组合品中可能包含需要查询的单品，处理逻辑也不同，所以分开处理
        List<SkuQtyUnitDTO> singleSquList = this.querySingleSkuByChannelCode(singleSkuList);
        List<SkuQtyUnitDTO> combineSquList = this.queryCombineSkuByChannelCode(combineSkuList);
        singleSquList.addAll(combineSquList);
        skuQtyUnitTools.convertBasicForQueryStock(singleSquList, unitMap);
        return skuStockConvertor.squToSkuStock(singleSquList);
    }

    @Override
    public List<SkuStockForRw> querySkuStockListByRwType(List<BaseSkuInfoDTO> skuList, Integer rwType) {
        List<SkuInfoForRw> sis = new ArrayList<>();
        List<UnitAndBaseUnitInfoDTO> unitList =  new ArrayList();
        Map<SkuIdUnitCodeDTO, UnitAndBaseUnitInfoDTO> unitMap = new HashedMap();
        List<SkuUnitAndCombineExtDTO> extList = new ArrayList<>();
        //获取实仓相关信息
        Map<Long, RealWarehouseE> rwMap = this.getRwMap(rwType);
        if(MapUtils.isEmpty(rwMap)){
            log.info("未查询到商品信息为{}的库存信息", JSON.toJSONString(skuList));
            return new ArrayList<>();
        }
        for (RealWarehouseE realWarehouse : rwMap.values()) {
            for (BaseSkuInfoDTO sku : skuList) {
                SkuInfoForRw skuInfo = new SkuInfoForRw();
                skuInfo.setRealWarehouseId(realWarehouse.getId());
                skuInfo.setRealWarehouseCode(realWarehouse.getRealWarehouseCode());
                skuInfo.setMerchantId(sku.getMerchantId());
                //默认商家为空就设置为10000
                if (skuInfo.getMerchantId() == null) {
                    skuInfo.setMerchantId(skuFacade.getDefaultMerchantId());
                }
                skuInfo.setSkuCode(sku.getSkuCode());
                skuInfo.setUnitCode(sku.getUnitCode());
                sis.add(skuInfo);
            }
        }
        //根据code查询
        if(CollectionUtils.isNotEmpty(sis)){
            List<SkuInfo> parmList = skuRmStockConvertor.skuRwListToSkuList(sis);
            extList = skuFacade.querySkuUnitAndCombinesBySkuCodeAndUnitCodeAndMerchantId(parmList);
            if(CollectionUtils.isNotEmpty(extList)){
                Map<String, SkuUnitAndCombineExtDTO> tempMap =  RomeCollectionUtil.listforMap(extList, "skuCodeInfo", null);
                for (SkuInfoForRw si : sis) {
                    SkuUnitAndCombineExtDTO dto  = tempMap.get(si.getSkuCodeInfo());
                    if(dto != null){
                        si.setCombineType(dto.getCombineType());
                        si.setCombineInfo(dto.getCombineInfo());
                        si.setSkuId(dto.getSkuId());
                        unitList.addAll(dto.getUnitInfo());
                    }
                }
            }
        }
        AlikAssert.isNotEmpty(extList, ResCode.STOCK_ERROR_1036, ResCode.STOCK_ERROR_1036_DESC);
        //设置单位的map
        if(CollectionUtils.isNotEmpty(unitList)) {
            for (UnitAndBaseUnitInfoDTO dto : unitList) {
                unitMap.put(new SkuIdUnitCodeDTO(dto.getSkuId(), dto.getUnitCode()), dto);
            }
        }
        //区分单品和组合品
        List<SkuInfoForRw> singleSkuList = new ArrayList<>();
        List<SkuInfoForRw> combineSkuList = new ArrayList<>();
        for (SkuInfoForRw si : sis) {
            if(si.getCombineType()==1){
                combineSkuList.add(si);
            }else{
                singleSkuList.add(si);
            }
        }
        //组合品中可能包含需要查询的单品，处理逻辑也不同，所以分开处理
        List<SkuQtyUnitDTO> singleSquList = this.querySingleSkuByRwId(singleSkuList);
        List<SkuQtyUnitDTO> combineSquList = this.queryCombineSkuByRwId(combineSkuList);;
        singleSquList.addAll(combineSquList);
        skuQtyUnitTools.convertBasicForQueryStock(singleSquList, unitMap);
        List<SkuStockForRw> list =  skuRmStockConvertor.squToSkuStock(singleSquList);
        if(CollectionUtils.isNotEmpty(list)){
            for (SkuStockForRw stock : list) {
                RealWarehouseE realWarehouse = rwMap.get(stock.getRealWarehouseId());
                if(realWarehouse != null){
                    stock.setRealWarehouseCode(realWarehouse.getRealWarehouseCode());
                    stock.setRealWarehouseId(realWarehouse.getId());
                    stock.setRealWarehouseName(realWarehouse.getRealWarehouseName());
                }
            }
        }
        return list;
    }

    /**
     * 去掉重复的数据
     * @param sis
     */
    private void removeDuplicate(List<SkuInfo> sis) {
        Map<String, SkuInfo> map = new HashMap<>();
        for (Iterator<SkuInfo> iter = sis.iterator(); iter.hasNext();) {
            SkuInfo element = iter.next();
            if (element.getCombineType() == null) {
                iter.remove();
                continue;
            }
            if(!map.containsKey(element.getSingleInfo())){
                map.put(element.getSingleInfo(), element);
            }else{
                iter.remove();
                continue;
            }
        }
    }

    /**
     * 处理单品sku查询
     *
     * @return
     */
    private List<SkuQtyUnitDTO> querySingleSkuByChannelCode(List<SkuInfo> sis) {
        if (!validator.validCollection(sis)) {
            return new ArrayList<>();
        }
        List<ChannelSalesStockDTO> queryDTOList = skuStockConvertor.paramDTOToQueryDTO(sis);
        List<ChannelSalesStockDTO> resultDTOList = coreChannelSalesRepository.getChannelSaleAvailableStock(queryDTOList);
        List<SkuQtyUnitDTO> squList = skuStockConvertor.resultDTOTOReturnDTO(resultDTOList);
        for (SkuQtyUnitDTO squ : squList) {
            Iterator<SkuInfo> iterator = sis.iterator();
            while (iterator.hasNext()) {
                SkuInfo si = iterator.next();
                if (squ.getChannelCode().equals(si.getChannelCode()) && squ.getSkuId().equals(si.getSkuId())) {
                    squ.setUnitCode(si.getUnitCode());
                    iterator.remove();
                    break;
                }
            }
        }
        return null == squList ? new ArrayList<>() : squList;
    }

    /**
     * 处理组合品sku查询
     *
     * @return
     */
    private List<SkuQtyUnitDTO>  queryCombineSkuByChannelCode(List<SkuInfo> sis) {
        if (null == sis || sis.isEmpty()) {
            return new ArrayList<>();
        }
        List<CombineSkuInfo> csInfoList = skuStockConvertor.siTosicn(sis);
        Map<Long, List<SkuCombineSimpleDTO>> sceMap = new HashMap<>();
        for (SkuInfo si : sis) {
            sceMap.put(si.getSkuId(), si.getCombineInfo());
        }
        //拆品集合
        List<SplitSkuInfo> ssInfoList = new ArrayList<>();
        Iterator<CombineSkuInfo> iterator = csInfoList.iterator();
        while (iterator.hasNext()) {
            CombineSkuInfo csInfo = iterator.next();
            List<SkuCombineSimpleDTO> dtoList = sceMap.get(csInfo.getSkuId());
            //如果是组合品，但是不存在组合关系，则直接删除查询结果
            if (!validator.validCollection(dtoList)) {
                iterator.remove();
                continue;
            }
            for (SkuCombineSimpleDTO sce : dtoList) {
                SplitSkuInfo ssInfo = new SplitSkuInfo();
                ssInfo.setSkuId(sce.getSkuId());
                ssInfo.setSkuCode(sce.getSkuCode());
                ssInfo.setCombineSkuId(sce.getCombineSkuId());
                ssInfo.setCombineSkuCode(sce.getCombineSkuCode());
                ssInfo.setChannelCode(csInfo.getChannelCode());
                ssInfo.setNum(sce.getNum());
                csInfo.getSplits().add(ssInfo);
                ssInfoList.add(ssInfo);
            }
        }
        if (!validator.validCollection(ssInfoList)) {
            return new ArrayList<>();
        }
        List<ChannelSalesStockDTO> queryDTOList = skuStockConvertor.combineSkuInfoToQueryDTO(ssInfoList);
        List<ChannelSalesStockDTO> resultDTOList = coreChannelSalesRepository.getChannelSaleAvailableStock(queryDTOList);
        //按渠道和拆品skuId，包装库存结果集
        Map<SkuIdChannelCode, ChannelSalesStockDTO> splitStockMap = new HashMap<>();
        for (ChannelSalesStockDTO cs : resultDTOList) {
            splitStockMap.put(new SkuIdChannelCode(cs.getSkuId(), cs.getChannelCode()), cs);
        }
        //包装结果集
        List<SkuQtyUnitDTO> squList = new ArrayList<>();
        for (CombineSkuInfo csInfo : csInfoList) {
            SkuQtyUnitDTO squ = new SkuQtyUnitDTO();
            squList.add(squ);
            squ.setSkuId(csInfo.getSkuId());
            squ.setSkuCode(csInfo.getSkuCode());
            squ.setUnitCode(csInfo.getUnitCode());
            squ.setChannelCode(csInfo.getChannelCode());
            BigDecimal minimumQty = new BigDecimal(Long.MAX_VALUE);
            for (SplitSkuInfo ssInfo : csInfo.getSplits()) {
                ChannelSalesStockDTO cs = splitStockMap.get(new SkuIdChannelCode(ssInfo.getCombineSkuId(), ssInfo.getChannelCode()));
                //如果库存中心不存在该拆品skuId库存，或可用库存为0，则直接设置成品可用库存为0
                if (null == cs || BigDecimal.ZERO.compareTo(cs.getAvailableQty()) == 0) {
                    squ.setBasicSkuQty(BigDecimal.ZERO);
                    break;
                }
                if (BigDecimal.ZERO.compareTo(ssInfo.getNum()) == 0) {
                    continue;
                }
                BigDecimal currentQty = cs.getAvailableQty().divide(ssInfo.getNum(), StockCoreConsts.DECIMAL_POINT_NUM, ROUND_DOWN);
                if (minimumQty.compareTo(currentQty) > 0) {
                    minimumQty = currentQty;
                    squ.setBasicSkuQty(minimumQty);
                }
            }
        }
        return squList;
    }


    /**
     * 处理单品sku查询
     *
     * @return
     */
    private List<SkuQtyUnitDTO> querySingleSkuByRwId(List<SkuInfoForRw> sis) {
        if (!validator.validCollection(sis)) {
            return new ArrayList<>();
        }
        List<CoreRealWarehouseStockDO> queryDTOList = skuRmStockConvertor.paramDTOToQueryDTO(sis);
        List<CoreRealWarehouseStockDO> resultDTOList = coreRealWarehouseStockRepository.getRWStock(queryDTOList);
        List<SkuQtyUnitDTO> squList = skuRmStockConvertor.resultDTOTOReturnDTO(resultDTOList);
        for (SkuQtyUnitDTO squ : squList) {
            Iterator<SkuInfoForRw> iterator = sis.iterator();
            while (iterator.hasNext()) {
                SkuInfoForRw si = iterator.next();
                if (squ.getRealWarehouseId().equals(si.getRealWarehouseId()) && squ.getSkuId().equals(si.getSkuId())) {
                    squ.setUnitCode(si.getUnitCode());
                    squ.setSkuCode(si.getSkuCode());
                    squ.setMerchantId(si.getMerchantId());
                    squ.setSkuId(si.getSkuId());
                    iterator.remove();
                    break;
                }
            }
        }
        return null == squList ? new ArrayList<>() : squList;
    }

    /**
     * 处理组合品sku查询
     *
     * @return
     */
    private List<SkuQtyUnitDTO>  queryCombineSkuByRwId(List<SkuInfoForRw> sis) {
        if (null == sis || sis.isEmpty()) {
            return new ArrayList<>();
        }
        List<CombineSkuInfoForRm> csInfoList = skuRmStockConvertor.siTosicn(sis);
        Map<Long, List<SkuCombineSimpleDTO>> sceMap = new HashMap<>();
        for (SkuInfoForRw si : sis) {
            sceMap.put(si.getSkuId(), si.getCombineInfo());
        }
        //拆品集合
        List<SplitSkuInfoForRm> ssInfoList = new ArrayList<>();
        Iterator<CombineSkuInfoForRm> iterator = csInfoList.iterator();
        while (iterator.hasNext()) {
            CombineSkuInfoForRm csInfo = iterator.next();
            List<SkuCombineSimpleDTO> dtoList = sceMap.get(csInfo.getSkuId());
            //如果是组合品，但是不存在组合关系，则直接删除查询结果
            if (!validator.validCollection(dtoList)) {
                iterator.remove();
                continue;
            }
            for (SkuCombineSimpleDTO sce : dtoList) {
                SplitSkuInfoForRm ssInfo = new SplitSkuInfoForRm();
                ssInfo.setSkuId(sce.getSkuId());
                ssInfo.setSkuCode(sce.getSkuCode());
                ssInfo.setCombineSkuId(sce.getCombineSkuId());
                ssInfo.setCombineSkuCode(sce.getCombineSkuCode());
                ssInfo.setRealWarehouseId(csInfo.getRealWarehouseId());
                ssInfo.setNum(sce.getNum());
                csInfo.getSplits().add(ssInfo);
                ssInfoList.add(ssInfo);
            }
        }
        if (!validator.validCollection(ssInfoList)) {
            return new ArrayList<>();
        }
        List<CoreRealWarehouseStockDO> queryDTOList = skuRmStockConvertor.combineSkuInfoToQueryDTO(ssInfoList);
        List<CoreRealWarehouseStockDO> resultDTOList = coreRealWarehouseStockRepository.getRWStock(queryDTOList);
        //按渠道和拆品skuId，包装库存结果集
        Map<SkuIdRwId, CoreRealWarehouseStockDO> splitStockMap = new HashMap<>();
        for (CoreRealWarehouseStockDO cs : resultDTOList) {
            splitStockMap.put(new SkuIdRwId(cs.getSkuId(), cs.getRealWarehouseId()), cs);
        }
        //包装结果集
        List<SkuQtyUnitDTO> squList = new ArrayList<>();
        for (CombineSkuInfoForRm csInfo : csInfoList) {
            SkuQtyUnitDTO squ = new SkuQtyUnitDTO();
            squList.add(squ);
            squ.setSkuId(csInfo.getSkuId());
            squ.setSkuCode(csInfo.getSkuCode());
            squ.setUnitCode(csInfo.getUnitCode());
            squ.setRealWarehouseId(csInfo.getRealWarehouseId());
            BigDecimal minimumQty = new BigDecimal(Long.MAX_VALUE);
            for (SplitSkuInfoForRm ssInfo : csInfo.getSplits()) {
                CoreRealWarehouseStockDO cs = splitStockMap.get(new SkuIdRwId(ssInfo.getCombineSkuId(), ssInfo.getRealWarehouseId()));
                //如果库存中心不存在该拆品skuId库存，或可用库存为0，则直接设置成品可用库存为0
                if (null == cs || BigDecimal.ZERO.compareTo(cs.getAvailableQty()) == 0) {
                    squ.setBasicSkuQty(BigDecimal.ZERO);
                    break;
                }
                if (BigDecimal.ZERO.compareTo(ssInfo.getNum()) == 0) {
                    continue;
                }
                BigDecimal currentQty = cs.getAvailableQty().divide(ssInfo.getNum(), StockCoreConsts.DECIMAL_POINT_NUM, ROUND_DOWN);
                if (minimumQty.compareTo(currentQty) > 0) {
                    minimumQty = currentQty;
                    squ.setBasicSkuQty(minimumQty);
                }
            }
        }
        return squList;
    }

    @Override
    public List<VirtualWarehouseStockDTO> listStockBySkuIdsWarehouseId(QueryVirtualWarehouseStockDTO queryVirtualWarehouseStockDTO) {
        VirtualWarehouseE virtualWarehouse = warehouseRepository.getVirtualWarehouseByCode(queryVirtualWarehouseStockDTO.getVirtualWarehouseCode());
        if(Objects.isNull(virtualWarehouse)){
            throw new RomeException(ResCode.STOCK_ERROR_1002,ResCode.STOCK_ERROR_1002_DESC+"未查询到虚仓");
        }
        queryVirtualWarehouseStockDTO.setVirtualWarehouseId(virtualWarehouse.getId());
        List<VirtualWarehouseStockDo> virtualWarehouseStockDos = warehouseStockRepository.listStockBySkuIdsWarehouseId(queryVirtualWarehouseStockDTO.getSkuIds(), queryVirtualWarehouseStockDTO.getVirtualWarehouseId());
        return stockConvert.dosToDTOS(virtualWarehouseStockDos);
    }

    /**
     * 根据渠道查询虚仓可售库存
     *
     * @param channelCode
     * @param pageNum
     * @return
     */
    @Override
    public PageInfo<VWSkuStock> queryStockByChannelCode(String channelCode, int pageNum) {
        ChannelSalesE channelSalesE = channelSalesRepository.queryByChannelCode(channelCode);
        if (null == channelSalesE) {
            return new PageInfo<>();
        }
//        AlikAssert.isNotNull(channelSalesE, ResCode.STOCK_ERROR_5028, ResCode.STOCK_ERROR_5028_DESC);
        List<VirtualWarehouseE> vwList = virtualWarehouseRepository.queryByGroupId(channelSalesE.getVirtualWarehouseGroupId());
        if (null == vwList || vwList.isEmpty()) {
            return new PageInfo<>();
        }
//        AlikAssert.notEmpty(vwList, ResCode.STOCK_ERROR_5028, ResCode.STOCK_ERROR_5028_DESC);
        List<Long> ids = new ArrayList<>();
        vwList.forEach(vw -> ids.add(vw.getId()));
        int pageSize = 100;
        Page pageObj = PageHelper.startPage(pageNum, pageSize);
        List<VWSkuStock> skuStocks = virtualWarehouseStockRepository.queryVStockByVIds(ids);
        PageInfo<VWSkuStock> pageInfo = new PageInfo<>(skuStocks);
        pageInfo.setTotal(pageObj.getTotal());
        return pageInfo;
    }

    /**
     * 分页查询渠道下SKU已售罄商品
     *
     * @param channelCode
     * @param pageNum
     * @return
     */
    @Override
    public PageInfo<VWSkuStock> queryFloridianSkuByChannelCode(String channelCode, int pageNum) {
        ChannelSalesE channelSalesE = channelSalesRepository.queryByChannelCode(channelCode);
        if (null == channelSalesE) {
            return new PageInfo<>();
        }
//        AlikAssert.isNotNull(channelSalesE, ResCode.STOCK_ERROR_5028, ResCode.STOCK_ERROR_5028_DESC);
        List<VirtualWarehouseE> vwList = virtualWarehouseRepository.queryByGroupId(channelSalesE.getVirtualWarehouseGroupId());
        if (null == vwList || vwList.isEmpty()) {
            return new PageInfo<>();
        }
//        AlikAssert.notEmpty(vwList, ResCode.STOCK_ERROR_5028, ResCode.STOCK_ERROR_5028_DESC);
        List<Long> ids = new ArrayList<>();
        vwList.forEach(vw -> ids.add(vw.getId()));
        int pageSize = 100;
        Page pageObj = PageHelper.startPage(pageNum, pageSize);
        List<VWSkuStock> skuStocks = virtualWarehouseStockRepository.queryFloridianSkuVStockByVIds(ids);
        PageInfo<VWSkuStock> pageInfo = new PageInfo<>(skuStocks);
        pageInfo.setTotal(pageObj.getTotal());
        return pageInfo;
    }


    @Override
    public PageInfo<SupplierSkuStockDTO> querySupplierStockBySkuInfo(QuerySupplierSkuStockDTO skuStockDTO) {
        List<String> shopCodes = new ArrayList<>();
        shopCodes.add(skuStockDTO.getSupplierCode());
        List<RealWarehouseE> realWarehouseEList = realWarehouseRepository.getAvailableRwListByShopCodes(shopCodes);
        AlikAssert.isNotEmpty(realWarehouseEList, ResCode.STOCK_ERROR_9055, ResCode.STOCK_ERROR_9055_DESC);
        realWarehouseEList = realWarehouseEList.stream().filter(dto -> RealWarehouseTypeVO.RW_TYPE_25.getType().equals(dto.getRealWarehouseType())).collect(Collectors.toList());
        if(realWarehouseEList.size() != 1) {
            throw new RomeException(ResCode.STOCK_ERROR_9055,ResCode.STOCK_ERROR_9055_DESC);
        }
        RealWarehouseE realWarehouse = realWarehouseEList.get(0);
        PageInfo<SupplierSkuStockDTO> page = skuFacade.skuCodesAndSkuName(skuStockDTO.getSkuCodeList() == null ? Collections.EMPTY_LIST : skuStockDTO.getSkuCodeList(), skuStockDTO.getSkuName(), skuStockDTO.getPage(), skuStockDTO.getPageSize());
        if(page.getList() == null || page.getList().size() == 0) {
            return new PageInfo<>();
        }
        List<Long> skuIds = RomeCollectionUtil.getValueList(page.getList(), "skuId");
        skuIds = skuIds.stream().distinct().collect(Collectors.toList());
        List<SkuInfoExtDTO> skuInfoExtDTOs = skuFacade.skusBySkuId(skuIds);
        Map<Long, SkuInfoExtDTO> skuInfoExtDTOMap = skuInfoExtDTOs.stream().collect(Collectors.toMap(SkuInfoExtDTO::getId, Function.identity(), (v1, v2) -> v1));
        CoreRealWarehouseStockDO stockItem;
        List<CoreRealWarehouseStockDO> coreRealWarehouseStockDOs = new ArrayList<CoreRealWarehouseStockDO>(page.getList().size());
        for(SupplierSkuStockDTO dto : page.getList()) {
            dto.setUnit(skuInfoExtDTOMap.get(dto.getSkuId()).getSpuUnitName());
            dto.setUnitCode(skuInfoExtDTOMap.get(dto.getSkuId()).getSpuUnitCode());
            dto.setName(dto.getSkuName());
            stockItem = new CoreRealWarehouseStockDO();
            stockItem.setRealWarehouseId(realWarehouse.getId());
            stockItem.setSkuId(dto.getSkuId());
            coreRealWarehouseStockDOs.add(stockItem);
        }
        List<CoreRealWarehouseStockDO> rwStock = coreRealWarehouseStockRepository.getRWStock(coreRealWarehouseStockDOs);
        if(CollectionUtils.isNotEmpty(rwStock)){
            Map<Long, CoreRealWarehouseStockDO> realStockMap = rwStock.stream().collect(Collectors.toMap(CoreRealWarehouseStockDO::getSkuId, Function.identity(), (v1, v2) -> v2));
            for(SupplierSkuStockDTO dto : page.getList()) {
                stockItem = realStockMap.get(dto.getSkuId());
                if(stockItem == null) {
                    dto.setAvailableQty(BigDecimal.ZERO);
                    dto.setRealQty(BigDecimal.ZERO);
                    dto.setLockQty(BigDecimal.ZERO);
                }else {
                    dto.setAvailableQty(stockItem.getAvailableQty());
                    dto.setRealQty(stockItem.getRealQty());
                    dto.setLockQty(stockItem.getLockQty());
                }
            }
        }else {
            for(SupplierSkuStockDTO dto : page.getList()) {
                dto.setAvailableQty(BigDecimal.ZERO);
                dto.setRealQty(BigDecimal.ZERO);
                dto.setLockQty(BigDecimal.ZERO);
            }
        }
        return page;
    }

    /**
     * 【纯属测试人员用】根据skuId和渠道，批量查询渠道可售库存不含单位
     * @param sis 待查询sku集合
     * @return
     */
    @Override
    public List<ChannelSalesStockDTO> testQuerySkuStockList(List<SkuInfo> sis) {
        List<ChannelSalesStockDTO> queryDTOList = skuStockConvertor.paramDTOToQueryDTO(sis);
        return coreChannelSalesRepository.getChannelSaleAvailableStock(queryDTOList);
    }

    @Override
    public List<ShopSkuStock> queryShipFactoryStock(QueryShipFactoryStock queryShipFactoryStock) {
        RealWarehouse realWarehouse = this.QueryShopShipWarehouse(queryShipFactoryStock);
        List<ShopSkuStock> list=new ArrayList<>();
        if(Objects.nonNull(realWarehouse)){
            List<BaseSkuInfoDTO> skuBaseList = queryShipFactoryStock.getSkuCodeList().stream().map(x -> {
                BaseSkuInfoDTO baseSkuInfoDTO = new BaseSkuInfoDTO();
                baseSkuInfoDTO.setSkuCode(x);
                return baseSkuInfoDTO;
            }).collect(Collectors.toList());
            //补全sku信息
            skuInfoTools.convertSkuCode(skuBaseList);
            //补全单位信息
            skuQtyUnitTools.queryBasicUnitNoChecked(skuBaseList);
            List<CoreRealWarehouseStockDO> coreRealStock = skuBaseList.stream().map(x -> {
                CoreRealWarehouseStockDO coreRealWarehouseStockDO = new CoreRealWarehouseStockDO();
                coreRealWarehouseStockDO.setRealWarehouseId(realWarehouse.getId());
                coreRealWarehouseStockDO.setSkuId(x.getSkuId());
                return coreRealWarehouseStockDO;
            }).collect(Collectors.toList());

            List<CoreRealWarehouseStockDO> rwStock = coreRealWarehouseStockRepository.getRWStock(coreRealStock);
            if(CollectionUtils.isNotEmpty(rwStock)){
                Map<Long, CoreRealWarehouseStockDO> realStockMap = rwStock.stream().collect(Collectors.toMap(CoreRealWarehouseStockDO::getSkuId, Function.identity(), (v1, v2) -> v2));
                skuBaseList.forEach(x->{
                    CoreRealWarehouseStockDO coreRealWarehouseStockDO = realStockMap.get(x.getSkuId());
                    ShopSkuStock skuStock=null;
                    if(Objects.nonNull(coreRealWarehouseStockDO)){
                        skuStock=new ShopSkuStock();
                        skuStock.setSkuId(x.getSkuId());
                        skuStock.setSkuCode(x.getSkuCode());
                        skuStock.setUnitCode(x.getUnitCode());
                        skuStock.setFactoryCode(realWarehouse.getFactoryCode());
                        skuStock.setRealWarehouseOutCode(realWarehouse.getRealWarehouseOutCode());
                        skuStock.setFactoryName(realWarehouse.getFactoryName());
                        skuStock.setAvailableQty(coreRealWarehouseStockDO.getAvailableQty());
                        skuStock.setRealQty(coreRealWarehouseStockDO.getRealQty());
                        skuStock.setLockQty(coreRealWarehouseStockDO.getLockQty());
                        skuStock.setOnroadQty(coreRealWarehouseStockDO.getOnroadQty());
                        skuStock.setQualityQty(coreRealWarehouseStockDO.getQualityQty());
                        skuStock.setUnqualifiedQty(coreRealWarehouseStockDO.getUnqualifiedQty());
                        skuStock.setUnitCode(x.getUnitCode());
                        list.add(skuStock);
                    }
                });
            }
        }
        return list;
    }

    @Override
    public PageInfo<StockForTempInsPage> queryStockForTempIns(QueryForTempInsDTO queryForTempIns) {
        if (null == queryForTempIns) {
            return new PageInfo<>();
        }
        List<Long> rwIdsList = new ArrayList<>();
        //判断仓库和物料不能全部为空
        AlikAssert.isFalse(CollectionUtils.isEmpty(queryForTempIns.getRealWarehouseCodes()) &&
                CollectionUtils.isEmpty(queryForTempIns.getSkuCodes()), ResCode.STOCK_ERROR_4019, ResCode.STOCK_ERROR_4019_DESC);
        if(CollectionUtils.isNotEmpty(queryForTempIns.getRealWarehouseCodes())) {
            List<RealWarehouseE> rwList = realWarehouseRepository.getRealWarehousesByCode(queryForTempIns.getRealWarehouseCodes());
            if (null == rwList || rwList.isEmpty()) {
                return new PageInfo<>();
            }
            rwList.forEach(rw -> rwIdsList.add(rw.getId()));
        }
        queryForTempIns.setRealWarehouseIds(rwIdsList);
        //查询并分页
        Page pageObj = PageHelper.startPage(queryForTempIns.getPageIndex(), queryForTempIns.getPageSize());
        List<StockForTempInsPage> skuStocks = realWarehouseRepository.queryStockForTempIns(queryForTempIns);
        //封装实仓信息
        if(CollectionUtils.isNotEmpty(skuStocks)){
            //查询实仓
            List<Long> ids = RomeCollectionUtil.getValueList(skuStocks, "realWarehouseId");
            List<RealWarehouseE> rwList  = realWarehouseRepository.getRealWarehouseByIds(ids);
            Map<Long, RealWarehouseE> rwMap = RomeCollectionUtil.listforMap(rwList, "id");
            for (StockForTempInsPage skuStock : skuStocks) {
                RealWarehouseE rw = rwMap.get(skuStock.getRealWarehouseId());
                if(rw != null){
                    skuStock.setFactoryName(rw.getRealWarehouseName());
                    skuStock.setFactoryCode(rw.getFactoryCode());
                    skuStock.setRealWarehouseOutCode(rw.getRealWarehouseOutCode());
                    skuStock.setRealWarehouseCode(rw.getRealWarehouseCode());
                    skuStock.setRealWarehouseTypeName(RealWarehouseTypeVO.getTypeVOByType(rw.getRealWarehouseType()).getDesc());
                    skuStock.setFactoryName(rw.getFactoryName());
                }
            }
        }
        PageInfo<StockForTempInsPage> pageInfo = new PageInfo<>(skuStocks);
        pageInfo.setTotal(pageObj.getTotal());
        return pageInfo;
    }

    /**[.
     * 查询门店对应的发货工厂
     * @param queryShipFactoryStock
     * @return RealWarehouse
     */
    private RealWarehouse QueryShopShipWarehouse(QueryShipFactoryStock queryShipFactoryStock) {
        StoreDTO storeDTO = shopFacade.searchByCode(queryShipFactoryStock.getShopCode());
        String deliverFactory = zdeliverRelationRepository.getDeliverFactory(storeDTO.getDeliveryFactory());
        if(storeDTO.getStoreProperties().equals("1") && StringUtils.isBlank(deliverFactory)) {
            //如果是直营的话，在配置表无数据的，就用商品返回的工厂即可
            deliverFactory = storeDTO.getDeliveryFactory();
        }
        if (StringUtils.isNotBlank(deliverFactory)) {
            List<RealWarehouse> list = realWarehouseRepository.queryRealWarehouseByFactoryCode(deliverFactory);
            List<StoreDTO> storeDTOList = shopFacade.searchByCodeList(Arrays.asList(deliverFactory));
            if(CollectionUtils.isNotEmpty(storeDTOList)){
                for (RealWarehouse warehouse : list) {
                    if (RealWarehouseTypeVO.RW_TYPE_16.getType().equals(warehouse.getRealWarehouseType())) {
                        warehouse.setFactoryName(storeDTOList.get(0).getName());
                        return warehouse;
                    }
                }
            }
        }
        return null;
    }


    /**
     * 根据仓库和商品信息查库存
     * @param queryRealStockDTO
     * @return
     */
    @Override
    public List<SkuStock> queryRealStockBySkuInfo(QueryRealStockDTO queryRealStockDTO) {
        RealWarehouseE realWarehouse = realWarehouseRepository.getRealWarehouseByOutCodeAndFactoryCode(queryRealStockDTO.getWarehouseOutCode(), queryRealStockDTO.getFactoryCode());
        if(Objects.isNull(realWarehouse)){
            throw new RomeException(ResCode.STOCK_ERROR_9055,ResCode.STOCK_ERROR_9055_DESC);
        }
        //兼容校验
        List<String> skuCodes = queryRealStockDTO.getBaseSkuInfoDTOS().stream().map(x -> x.getSkuCode()).collect(Collectors.toList());
        List<SkuInfoExtDTO> skuInfoList = skuFacade.skusBySkuCode(skuCodes);
        Map<String, SkuInfoExtDTO> skuInfoMap = skuInfoList.stream().collect(Collectors.toMap(x -> x.getSkuCode(), Function.identity(), (v1, v2) -> v2));
        Iterator<BaseSkuInfoDTO> iterator = queryRealStockDTO.getBaseSkuInfoDTOS().iterator();
        while (iterator.hasNext()){
            BaseSkuInfoDTO skuInfo = iterator.next();
            SkuInfoExtDTO skuInfoExtDTO = skuInfoMap.get(skuInfo.getSkuCode());
            if(Objects.isNull(skuInfoExtDTO)){
                iterator.remove();
            } else {
            	skuInfo.setSkuId(skuInfoExtDTO.getId());
            }
        }
//        skuInfoTools.convertSkuCode(queryRealStockDTO.getBaseSkuInfoDTOS());
        List<CoreRealWarehouseStockDO> coreRealWarehouseStockDOs = this.buildCoreRealStockDO(queryRealStockDTO, realWarehouse);
        List<CoreRealWarehouseStockDO> rwStock = coreRealWarehouseStockRepository.getRWStock(coreRealWarehouseStockDOs);
        List<SkuStock> list=new ArrayList<>();
        if(CollectionUtils.isNotEmpty(rwStock)){
            Map<Long, CoreRealWarehouseStockDO> realStockMap = rwStock.stream().collect(Collectors.toMap(CoreRealWarehouseStockDO::getSkuId, Function.identity(), (v1, v2) -> v2));
            queryRealStockDTO.getBaseSkuInfoDTOS().forEach(x->{
                CoreRealWarehouseStockDO coreRealWarehouseStockDO = realStockMap.get(x.getSkuId());
                SkuStock skuStock=null;
                if(Objects.nonNull(coreRealWarehouseStockDO)){
                    skuStock=new SkuStock();
                    skuStock.setSkuId(x.getSkuId());
                    skuStock.setSkuCode(x.getSkuCode());
                    skuStock.setUnitCode(x.getUnitCode());
                    skuStock.setAvailableQty(coreRealWarehouseStockDO.getAvailableQty());
                    skuStock.setRealQty(coreRealWarehouseStockDO.getRealQty());
                    skuStock.setLockQty(coreRealWarehouseStockDO.getLockQty());
                    skuStock.setOnroadQty(coreRealWarehouseStockDO.getOnroadQty());
                    skuStock.setQualityQty(coreRealWarehouseStockDO.getQualityQty());
                    skuStock.setUnqualifiedQty(coreRealWarehouseStockDO.getUnqualifiedQty());
                    list.add(skuStock);
                }
            });
        }
        return list;
    }

    @Override
    @TargetDataSource(DynamicDataSourceEnum.READ)
    public PageInfo<RwStock> queryFranchiseeShopRealStock(QueryRwStockDTO stockDTO) {
        AlikAssert.isNotEmpty(stockDTO.getShopCodes(), ResCode.STOCK_ERROR_1002, "门店编码不可为空");
        //查询并分页
        Page pageObj = PageHelper.startPage(stockDTO.getPageIndex(), stockDTO.getPageSize());
        List<RwStock> stocks = realWarehouseRepository.queryFranchiseeShopRealStock(stockDTO);

        if (CollectionUtils.isNotEmpty(stocks)){
            Map<String,SkuInfoExtDTO> skuInfoExtDTOMap = Maps.newHashMap();//商品信息
            Map<String,SkuCategoryExtDTO> skuCategoryExtDTOMap = Maps.newHashMap();//商品类目信息
            Map<String, SkuAttributeInfoDTO> skuAttributeMap = Maps.newHashMap();//商品属性
            Map<String, SkuUnitExtDTO> boxUnitMap = Maps.newHashMap();//商品箱单位属性
            List<String> skuCodes= RomeCollectionUtil.getDistinctValueList(stocks,"skuCode");
            List<String> shopCodeList = RomeCollectionUtil.getDistinctValueList(stocks,"shopCode");
            //获取商品扩展属性及经营属性
            if (!stockDTO.isOnlySumQtyAndAmount()){
                querySkuInfoParams(skuCodes,skuInfoExtDTOMap,skuAttributeMap,boxUnitMap,skuCategoryExtDTOMap);
            }

            List<StoreDTO> storeDTOS = shopFacade.searchByCodeList(shopCodeList);
            Map<String, StoreDTO> storeDTOMap = storeDTOS.stream()
                    .collect(Collectors.toMap(StoreDTO::getCode, Function.identity(), (v1, v2) -> v1));

            Map<String, List<String>> skuPriceRequestDTOMap = new HashedMap(stocks.size()*100/75+1);//移动平均价
            Set<Long> readWarehouseIds= Sets.newHashSet();//实仓id集合
            for (RwStock stock:stocks){
                //设置供应商信息
                StoreDTO storeDTO = storeDTOMap.get(stock.getShopCode());
                if (storeDTO != null) {
                    stock.setFranchisee(storeDTO.getFranchisee());
                    stock.setFranchiseeName(storeDTO.getFranchiseeName());
                }
                if (stockDTO.isOnlySumQtyAndAmount()){
                    continue;
                }
                String key = stock.getShopCode();
                if (skuPriceRequestDTOMap.containsKey(key)){
                    skuPriceRequestDTOMap.get(key).add(stock.getSkuCode());
                }else {
                    skuPriceRequestDTOMap.put(key,Lists.newArrayList(stock.getSkuCode()));
                }
                if (skuInfoExtDTOMap.containsKey(stock.getSkuCode())){
                    SkuInfoExtDTO extDTO=skuInfoExtDTOMap.get(stock.getSkuCode());
                    stock.setSkuName(extDTO.getName());
                    stock.setBasicUnitCode(extDTO.getSpuUnitCode());
                    stock.setBasicUnitName(extDTO.getSpuUnitName());
                }
                if (skuCategoryExtDTOMap.containsKey(stock.getSkuCode())){
                    stock.setSkuType(skuCategoryExtDTOMap.get(stock.getSkuCode()).getZtFourLevelCategoryName());
                }
                if (boxUnitMap.containsKey(stock.getSkuCode())){
                    SkuUnitExtDTO extDTO=boxUnitMap.get(stock.getSkuCode());
                    stock.setBoxScale(extDTO.getScale());
                }
                if (skuAttributeMap.containsKey(stock.getSkuCode())){
                    SkuAttributeInfoDTO skuAttribute  = skuAttributeMap.get(stock.getSkuCode());
                    stock.setSaleStatus(skuAttribute.getSaleStatus());
                }
                readWarehouseIds.add(stock.getRealWarehouseId());
            }
            //添加金额及未出库完成在途数量
            addSkuPriceAndOnRoad(stocks,Lists.newArrayList(readWarehouseIds),skuCodes,skuPriceRequestDTOMap);
        }
        PageInfo<RwStock> pageInfo = new PageInfo<>(stocks);
        pageInfo.setTotal(pageObj.getTotal());
        return pageInfo;
    }

    @Override
    public List<String> orderByFranchiseeShop(QueryRwStockDTO stockDTO) {
        AlikAssert.isNotEmpty(stockDTO.getShopCodes(), ResCode.STOCK_ERROR_1002, "门店编码不可为空");
        List shopCodes=realWarehouseRepository.orderByFranchiseeShop(stockDTO);
        return CollectionUtils.isEmpty(shopCodes)?Lists.newArrayList():shopCodes;
    }

    @Override
    @TargetDataSource(DynamicDataSourceEnum.READ)
    public PageInfo<RwStock> queryFranchiseeSumRealStock(QueryRwStockDTO stockDTO) {
        AlikAssert.isNotEmpty(stockDTO.getShopCodes(), ResCode.STOCK_ERROR_1002, "门店编码不可为空");
        //查询并分页
        Page pageObj = PageHelper.startPage(stockDTO.getPageIndex(), stockDTO.getPageSize());
        List<RwStock> stocks = realWarehouseRepository.queryFranchiseeSumRealStock(stockDTO);
        if (CollectionUtils.isNotEmpty(stocks)){
            Map<String,SkuInfoExtDTO> skuInfoExtDTOMap = Maps.newHashMap();//商品信息
            Map<String,SkuCategoryExtDTO> skuCategoryExtDTOMap = Maps.newHashMap();//商品类目信息
            Map<String, SkuAttributeInfoDTO> skuAttributeMap = Maps.newHashMap();//商品属性
            Map<String, SkuUnitExtDTO> boxUnitMap = Maps.newHashMap();//商品箱单位属性
            List<String> skuCodes= RomeCollectionUtil.getDistinctValueList(stocks,"skuCode");
            //获取商品扩展属性及经营属性
            querySkuInfoParams(skuCodes,skuInfoExtDTOMap,skuAttributeMap,boxUnitMap,skuCategoryExtDTOMap);
            Map<String,BigDecimal> sumAmountMap=Maps.newHashMap();
            Map<String,BigDecimal> sumAmountHasTaxMap=Maps.newHashMap();
            Map<String,BigDecimal> sumUnCompleteOnRoadQtyMap=Maps.newHashMap();
            querySkuPriceAndOnRoad(stockDTO.getShopCodes(),skuCodes,sumAmountMap,sumAmountHasTaxMap,sumUnCompleteOnRoadQtyMap);
            for (RwStock stock:stocks){
                if (skuInfoExtDTOMap.containsKey(stock.getSkuCode())){
                    SkuInfoExtDTO extDTO=skuInfoExtDTOMap.get(stock.getSkuCode());
                    stock.setSkuName(extDTO.getName());
                    stock.setBasicUnitCode(extDTO.getSpuUnitCode());
                    stock.setBasicUnitName(extDTO.getSpuUnitName());
                }
                if (skuCategoryExtDTOMap.containsKey(stock.getSkuCode())){
                    stock.setSkuType(skuCategoryExtDTOMap.get(stock.getSkuCode()).getZtFourLevelCategoryName());
                }
                if (boxUnitMap.containsKey(stock.getSkuCode())){
                    SkuUnitExtDTO extDTO=boxUnitMap.get(stock.getSkuCode());
                    stock.setBoxScale(extDTO.getScale());
                }
                if (skuAttributeMap.containsKey(stock.getSkuCode())){
                    SkuAttributeInfoDTO skuAttribute  = skuAttributeMap.get(stock.getSkuCode());
                    stock.setSaleStatus(skuAttribute.getSaleStatus());
                }
                //添加金额及未出库完成在途数量
                if (sumAmountMap.containsKey(stock.getSkuCode())){
                    stock.setAmount(sumAmountMap.get(stock.getSkuCode()));
                }
                if (sumAmountHasTaxMap.containsKey(stock.getSkuCode())){
                    stock.setAmountHasTax(sumAmountHasTaxMap.get(stock.getSkuCode()));
                }
                if (sumUnCompleteOnRoadQtyMap.containsKey(stock.getSkuCode())){
                    stock.setUnCompleteOnRoadStock(sumUnCompleteOnRoadQtyMap.get(stock.getSkuCode()));
                }
            }
        }
        PageInfo<RwStock> pageInfo = new PageInfo<>(stocks);
        pageInfo.setTotal(pageObj.getTotal());
        return pageInfo;
    }

    @Override
    @TargetDataSource(DynamicDataSourceEnum.READ)
    public RwStockSummary queryFranchiseeSumQtyAndAmount(QueryRwStockSummaryDTO stockDTO) {
        AlikAssert.isNotEmpty(stockDTO.getShopCodes(), ResCode.STOCK_ERROR_1002, "门店编码不可为空");
        //查询并分页
        RwStockSummary stockSummary=new RwStockSummary();
        List<RwStockQty> stocks = realWarehouseRepository.queryFranchiseeQtyStock(stockDTO);
        if (CollectionUtils.isNotEmpty(stocks)) {
            List<String> skuCodes=CollectionUtils.isNotEmpty(stockDTO.getSkuCodes())?
                    stockDTO.getSkuCodes():
                    (stocks.size()>1000?stockDTO.getSkuCodes():RomeCollectionUtil.getDistinctValueList(stocks,"skuCode"));
            List<SkuPriceResponseDTO> responseDTOS=venusStockFacade.queryUnitPriceByStockPlant(stockDTO.getShopCodes(),skuCodes,20);
            if (CollectionUtils.isNotEmpty(responseDTOS)){
                Map<String,SkuPriceResponseDTO> skuPriceMap=new HashedMap(responseDTOS.size()*100/75+1);
                responseDTOS.forEach(item->{
                    String key=item.getStockPlant()+"="+item.getSkuCode();
                    if (!skuPriceMap.containsKey(key)){
                        skuPriceMap.put(key,item);
                    }
                });
                stocks.forEach(stock->{
                    stockSummary.setSumRealQty(stockSummary.getSumRealQty().add(stock.getRealQty()));
                    String key=stock.getShopCode()+"="+stock.getSkuCode();
                    if (skuPriceMap.containsKey(key)){
                        SkuPriceResponseDTO skuPriceResponseDTO = skuPriceMap.get(key);
                        BigDecimal price=Objects.nonNull(skuPriceResponseDTO.getUnitPrice())? skuPriceResponseDTO.getUnitPrice():BigDecimal.ZERO;
                        BigDecimal amount = setNewScaleForMoney(price.multiply(stock.getRealQty()));
                        stockSummary.setSumAmount(stockSummary.getSumAmount().add(amount));
                        if (skuPriceResponseDTO.getTaxSale() != null) {
                            BigDecimal amountHaxTax =  setNewScaleForMoney(amount.multiply(skuPriceResponseDTO.getTaxSale().add(new BigDecimal("100"))).divide(new BigDecimal("100")) );
                            stockSummary.setSumAmountHasTax(stockSummary.getSumAmountHasTax().add(amountHaxTax));
                        } else {
                            stockSummary.setSumAmountHasTax(amount);
                        }
                    }
                });
            }
        }
        return stockSummary;
    }

    /**
     *
     * 仓库+SKU，返回库存大于0的当前最差批次号
     * @param queryMinBatchNoDTO
     * @return
     */
    @Override
    @TargetDataSource(DynamicDataSourceEnum.READ)
    public List<MinBatchNoResultDTO> querySkuMinBatchNo(QueryMinBatchNoDTO queryMinBatchNoDTO) {
        List<MinBatchNoResultDTO> results=Lists.newArrayList();
        RealWarehouseE realWarehouseE = realWarehouseRepository.getRealWarehouseByCode(queryMinBatchNoDTO.getRealWarehouseCode());
        if(Objects.isNull(realWarehouseE)){
            return results;
        }
        List<BatchStockDO> batchs=batchStockRepository.queryMinBatchNo(queryMinBatchNoDTO.getSkuCodes(),realWarehouseE.getId());
        if (CollectionUtils.isNotEmpty(batchs)){
            batchs.forEach(item->{
                MinBatchNoResultDTO resultDTO=new MinBatchNoResultDTO();
                resultDTO.setSkuCode(item.getSkuCode());
                resultDTO.setBatchNo(item.getBatchCode());
                results.add(resultDTO);
            });
        }
        return results;
    }

    /**
     * 获取商品扩展属性及经营属性
     * @param skuCodes
     * @param skuInfoExtDTOMap
     * @param skuAttributeMap
     */
    private void querySkuInfoParams(List<String> skuCodes, Map<String,SkuInfoExtDTO> skuInfoExtDTOMap,
                                    Map<String,SkuAttributeInfoDTO> skuAttributeMap,Map<String, SkuUnitExtDTO> boxUnitMap,
                                    Map<String,SkuCategoryExtDTO> skuCategoryExtDTOMap){
        skuCodes = RomeCollectionUtil.distinct(skuCodes);
        //获取商品扩展属性
        List<SkuInfoExtDTO> extDTOS=skuFacade.skuListBySkuCodes(skuCodes);
        for (SkuInfoExtDTO extDTO:extDTOS){
            skuInfoExtDTOMap.put(extDTO.getSkuCode(),extDTO);
        }
        //获取商品类目属性
        List<SkuCategoryExtDTO> categoryExtDTOS=skuFacade.getSkuCategoryBySkuCodes(skuCodes);
        for (SkuCategoryExtDTO categoryExtDTO:categoryExtDTOS){
            skuCategoryExtDTOMap.put(categoryExtDTO.getSkuCode(),categoryExtDTO);
        }
        //箱单位
        List<SkuUnitExtDTO> saleOutSkuUnitExtDTOLists=skuFacade.querySkuUnits(skuCodes);
        String boxUnitCode="KAR";
        for (SkuUnitExtDTO unitExtDTO:saleOutSkuUnitExtDTOLists){
            if (Objects.equals(boxUnitCode,unitExtDTO.getUnitCode())){
                boxUnitMap.put(unitExtDTO.getSkuCode(),unitExtDTO);
            }
        }
        List<SkuAttributeInfoDTO> infoDTOS=skuFacade.getSkuAttributeBySkuCodes(skuCodes);
        //获取商品的经营属性
        for (SkuAttributeInfoDTO infoDTO:infoDTOS){
            skuAttributeMap.put(infoDTO.getSkuCode(),infoDTO);
        }
    }

    /**
     * 添加移动平均价及未出库完成在途库存
     * @param stockDTOS
     * @param skuPriceMap
     */
    private void addSkuPriceAndOnRoad(List<RwStock> stockDTOS,List<Long> shopRws,
                                      List<String> skuCodes, Map<String, List<String>> skuPriceMap){
        //移动平均价Map=>key:shopCode+"="+skuCode
        Map<String, SkuPriceResponseDTO> skuPriceDTOMap = Maps.newHashMap();
        //未出库完成在途Map=>key:factoryCode+"="+skuCode
        Map<String,ReplenishShopSkuReqDTO> skuOnroadQtyMap = Maps.newHashMap();
        List<String> skuPriceShopCodes = Lists.newArrayList(skuPriceMap.keySet());
        if (CollectionUtils.isNotEmpty(skuPriceShopCodes)){
            //查询移动平均价
            List<List<String>> splitList = RomeCollectionUtil.splitList(skuPriceShopCodes, 20);
            for (List<String> shopCodes:splitList){
                Set<String> skus =Sets.newHashSet();
                for (String shopCodeKey : shopCodes) {
                    if(!skuPriceMap.containsKey(shopCodeKey)){
                        continue;
                    }
                    skus.addAll(skuPriceMap.get(shopCodeKey));
                }
                List<SkuPriceResponseDTO> responseDTOS=venusStockFacade.queryUnitPriceByStockPlant(shopCodes,new ArrayList<>(skus));
                if (CollectionUtils.isNotEmpty(responseDTOS)){
                    for (SkuPriceResponseDTO responseDTO:responseDTOS){
                        String key=responseDTO.getStockPlant()+"="+responseDTO.getSkuCode();
                        if (!skuPriceDTOMap.containsKey(key)){
                            skuPriceDTOMap.put(key,responseDTO);
                        }
                    }
                }
            }
        }
        if (CollectionUtils.isNotEmpty(shopRws) && CollectionUtils.isNotEmpty(skuCodes)){
            List<RealWarehouseE> realWarehouses=realWarehouseRepository.getRealWarehouseByIds(shopRws);
            List<RealWarehouse> shopCodesDtoList = realWarehouseConvertor.entityToDto(realWarehouses);
            //未出库完成的在途库存
            List<ReplenishShopSkuReqDTO> shopResList = orderCenterFacade.querySkuCountByShopCodeList(shopCodesDtoList,skuCodes);
            for (ReplenishShopSkuReqDTO dto:shopResList){
                String key=dto.getFactoryCode()+"="+dto.getSkuCode();
                skuOnroadQtyMap.put(key,dto);
            }
        }
        if (!skuPriceDTOMap.isEmpty() || !skuOnroadQtyMap.isEmpty()){
            stockDTOS.forEach(item->{
                String key=item.getShopCode()+"="+item.getSkuCode();
                if (skuPriceDTOMap.containsKey(key)){
                    SkuPriceResponseDTO skuPriceResponseDTO = skuPriceDTOMap.get(key);
                    BigDecimal price=Objects.nonNull(skuPriceResponseDTO.getUnitPrice())? skuPriceResponseDTO.getUnitPrice():BigDecimal.ZERO;
                    BigDecimal amount = setNewScaleForMoney(price.multiply(item.getRealQty()));
                    item.setAmount(amount);
                    if (skuPriceResponseDTO.getTaxSale() != null) {
                        //设置含税总金额
                        item.setAmountHasTax(setNewScaleForMoney(amount.multiply(skuPriceResponseDTO.getTaxSale().add(new BigDecimal("100"))).divide(new BigDecimal("100")) ));
                    }

                } else {
                    item.setAmount(BigDecimal.ZERO);
                    item.setAmountHasTax(BigDecimal.ZERO);
                }
                if (skuOnroadQtyMap.containsKey(key)){
                    item.setUnCompleteOnRoadStock(skuOnroadQtyMap.get(key).getSkuQty());
                }
            });
        }
    }

    private void querySkuPriceAndOnRoad(List<String> shopCodes,List<String> skuCodes,
                                      Map<String,BigDecimal> sumAmountMap,
                                        Map<String,BigDecimal> sumAmountHasTaxMap,
                                      Map<String,BigDecimal> sumUnCompleteOnRoadQtyMap){
        QueryRwStockDTO queryRwStockDTO=new QueryRwStockDTO();
        queryRwStockDTO.setShopCodes(shopCodes);
        queryRwStockDTO.setSkuCodes(skuCodes);
        List<RwStock> stocks=realWarehouseRepository.queryFranchiseeShopRealStock(queryRwStockDTO);
        Set<Long> shopRws=Sets.newHashSet();
        Map<String, List<String>> skuPriceRequestDTOMap = Maps.newHashMap();//移动平均价
        stocks.forEach(stock->{
            String key = stock.getShopCode();
            if (skuPriceRequestDTOMap.containsKey(key)){
                skuPriceRequestDTOMap.get(key).add(stock.getSkuCode());
            }else {
                skuPriceRequestDTOMap.put(key,Lists.newArrayList(stock.getSkuCode()));
            }
            shopRws.add(stock.getRealWarehouseId());
        });
        //获取门店商品金额及未出库完成在途库存
        addSkuPriceAndOnRoad(stocks,Lists.newArrayList(shopRws),skuCodes,skuPriceRequestDTOMap);
        stocks.forEach(stock->{
            String skuCode=stock.getSkuCode();
            BigDecimal amount = stock.getAmount() == null?BigDecimal.ZERO:stock.getAmount();
            if (sumAmountMap.containsKey(skuCode)){
                sumAmountMap.put(skuCode,sumAmountMap.get(skuCode).add(amount));
            }else {
                sumAmountMap.put(skuCode,amount);
            }
            BigDecimal amountHasTax = stock.getAmountHasTax() == null ? BigDecimal.ZERO:stock.getAmountHasTax();
            if (sumAmountHasTaxMap.containsKey(skuCode)){
               sumAmountHasTaxMap.put(skuCode,sumAmountHasTaxMap.get(skuCode).add(amountHasTax));
            }else {
                sumAmountHasTaxMap.put(skuCode,amountHasTax);
            }

            if (sumUnCompleteOnRoadQtyMap.containsKey(skuCode)){
                sumUnCompleteOnRoadQtyMap.put(skuCode,sumUnCompleteOnRoadQtyMap.get(skuCode).add(stock.getUnCompleteOnRoadStock()));
            }else {
                sumUnCompleteOnRoadQtyMap.put(skuCode,stock.getUnCompleteOnRoadStock());
            }
        });
    }

    private List<CoreRealWarehouseStockDO> buildCoreRealStockDO(QueryRealStockDTO queryRealStockDTO, RealWarehouseE realWarehouse) {
        return queryRealStockDTO.getBaseSkuInfoDTOS().stream().map(x -> {
            CoreRealWarehouseStockDO crwDo = new CoreRealWarehouseStockDO();
            crwDo.setRealWarehouseId(realWarehouse.getId());
            crwDo.setSkuId(x.getSkuId());
            return crwDo;
        }).collect(Collectors.toList());
    }


    /**
     * 根据实仓类型查询实仓并转化为map
     * @param
     * @return
     */
    private Map<Long, RealWarehouseE> getRwMap(Integer rwType) {
        List<RealWarehouseE> rwList =  realWarehouseRepository.queryRealWarehouseByRWType(rwType);
        return RomeCollectionUtil.listforMap(rwList,"id",null);
    }

    /**
     * 设置两位进度,四舍五入
     * @param sourceValue
     * @return
     */
    private BigDecimal setNewScaleForMoney(BigDecimal sourceValue) {
        if (sourceValue == null || sourceValue.scale() == 2) {
            return sourceValue;
        }
        return sourceValue.setScale(2, RoundingMode.HALF_UP);
    }

    @Data
    @AllArgsConstructor
    @EqualsAndHashCode(callSuper = false)
    private class SkuIdChannelCode {
        private Long skuId;
        private String channelCode;
    }

    @Data
    @AllArgsConstructor
    @EqualsAndHashCode(callSuper = false)
    private class SkuIdRwId {
        private Long skuId;
        private Long realWarehouseId;
    }


    @Override
    public PageInfo<String> queryWarehouseSkuCodePage(StockSkuDTO param) {
        boolean needCount = param.getPageSize() != null && !Objects.equals(Integer.MAX_VALUE,  param.getPageSize());

        if (param.getPageIndex() == null) {
            param.setPageIndex(1);
        }
        AlikAssert.isTrue(param.getRealWarehouseId() != null || param.getVirtualWarehouseId() != null, ResCode.STOCK_ERROR_1002, "实仓和虚仓不能都为空");
        Page page = PageHelper.startPage(param.getPageIndex(), param.getPageSize(), needCount);
        List<String> skuCodeList = null;
        if (param.getVirtualWarehouseId() != null) {
            skuCodeList = virtualWarehouseStockRepository.listSkuCodesByVwId(param.getVirtualWarehouseId(), param.getType());
        } else {
            skuCodeList = realWarehouseRepository.listSkuCodesByRwId(param.getRealWarehouseId(), param.getType());
        }

        PageInfo<String> pageList = new PageInfo<>(skuCodeList);
        pageList.setTotal(page.getTotal());
        return pageList;
    }

    @Override
    @TargetDataSource(DynamicDataSourceEnum.READ)
    public PageInfo<QueryForDisplayDTO> queryShopStockForDisplay(QueryDisplayDTO queryDisplayDTO) {
        RealWarehouseE realWarehouseE = realWarehouseRepository.queryByShopCode(queryDisplayDTO.getShopCode());
        AlikAssert.notNull(realWarehouseE, "-1", "未查询到对应门店,门店编号：" + queryDisplayDTO.getShopCode());
        PageHelper.startPage(queryDisplayDTO.getPageNum(), queryDisplayDTO.getPageSize());
        List<QueryForDisplayDTO> list = realWarehouseStockMapper.queryDisplayShopRealStock(realWarehouseE.getId());
        PageInfo<QueryForDisplayDTO> page = new PageInfo<>(list);
        if (CollectionUtils.isEmpty(list)) {
            return new PageInfo<>();
        }
        List<Long> skuIdList = new ArrayList<>();
        for (QueryForDisplayDTO dto : list) {
            dto.setFinalQty(BigDecimal.ZERO);
            skuIdList.add(dto.getSkuId());
        }
        Date yesterday = DateUtil.offsiteDate(new Date(), Calendar.DAY_OF_YEAR, -1);
        List<EntrySaleStockDO> entryList = entrySaleStockMapper.queryShopStockForDisplay(realWarehouseE.getId(), DateUtil.getDayBegin(yesterday), skuIdList);
        Map<String, EntrySaleStockDO> finialQtyMap = RomeCollectionUtil.listforMap(entryList, "skuCode");
        for (QueryForDisplayDTO queryForDisplayDTO : list) {
            EntrySaleStockDO entrySaleStockDO = finialQtyMap.get(queryForDisplayDTO.getSkuCode());
            if (entrySaleStockDO != null) {
                queryForDisplayDTO.setFinalQty(entrySaleStockDO.getFinalQty());
            }
        }
        return page;
    }

    @Override
    @TargetDataSource(DynamicDataSourceEnum.READ)
    public PageInfo<String> queryDistinctBatchSkuCodeByPage(StockSkuDTO dto) {
        if (Objects.isNull(dto.getPageIndex()) || Objects.isNull(dto.getPageSize())) {
           throw new RomeException(ResCode.STOCK_ERROR_1001, "分页参数不能为空");
        }
        if(dto.getPageSize()>100){
            throw new RomeException(ResCode.STOCK_ERROR_1001, "分页参数不能大于100");
        }
        if(Objects.isNull(dto.getRealWarehouseId())){
            throw new RomeException(ResCode.STOCK_ERROR_1001, "实仓ID不能为空");
        }
        this.removalFilter(dto);
        Page page = PageHelper.startPage(dto.getPageIndex(), dto.getPageSize(), true);
        List<String> skuCodeList = batchStockMapper.queryDistinctBatchSkuCodeByPage(dto);
        PageInfo<String> pageList = new PageInfo<>(skuCodeList);
        pageList.setTotal(page.getTotal());
        return pageList;
    }


    /**
     * 根据撤柜日期,来进行过滤出满足条件的物料
     * @param param
     */
    private void removalFilter(StockSkuDTO param) {
        if (StringUtils.isBlank(param.getStartDate()) || StringUtils.isBlank(param.getEndDate())){
            return ;
        }
        // 获取批次配置信息
        BaseinfoProperty property = BaseinfoConfiguration.getInstance().getObject("shop_batch_config", "shop_batch_config");
        if (null == property){
            return ;
        }
        //查询出当前仓对应的批次表的所有物料
        List<BatchRemovalDayDTO> batchStockDOList = batchStockRepository.selectDistinctSkuCodeByRwId(param.getRealWarehouseId());
        ShopBatchConfigDTO shopBatchConfig = JSON.parseObject(property.getValue(), ShopBatchConfigDTO.class);
        Set<Integer> combineTypeSet = new HashSet<>(shopBatchConfig.getCombineType());
        Set<String> skuTypeSet = new HashSet<>(shopBatchConfig.getSkuType());
        List<ShopBatchValidConfigDTO> validConfigList = shopBatchConfig.getValidConfigList();
        batchStockDOList.stream().forEach(batchStockDO -> {
            // 获取当前 SKU 的保质期
            Integer validity = batchStockDO.getValidity();
            if (null != validity){
                // 计算去除日期偏移量
                Integer removalDay = validConfigList.stream().filter(config -> config.getStartValue() < validity && validity <= config.getEndValue())
                        .map(config -> config.getRemovalDay())
                        .findFirst().orElse(null);
                batchStockDO.setRemovalDay(removalDay);
            }
        });
        // 获取 SKU 列表
        List<String> skuCodes = batchStockDOList.stream().map(BatchRemovalDayDTO::getSkuCode).collect(Collectors.toList());
        List<SkuInfoExtDTO>  skuInfoExtDTOList=  skuFacade.skuListBySkuCodes(skuCodes);
        if(CollectionUtils.isEmpty(skuInfoExtDTOList)){
            return ;
        }
        Map<String, SkuInfoExtDTO> skuInfoMap =skuInfoExtDTOList.stream().collect(Collectors.toMap(SkuInfoExtDTO::getSkuCode, Function.identity(), (x, y) -> x));
        batchStockDOList= batchStockDOList.stream().filter(batchStockDO -> {
            if(Objects.isNull(batchStockDO.getRemovalDay())){
                return false;
            }
            // 获取 SKU 详细信息
            SkuInfoExtDTO skuInfo = skuInfoMap.get(batchStockDO.getSkuCode());
            // 过滤掉 SKU 详情为空，或 SKU 类型和组合类型不符合配置的情况,只有当skuTypeSet或combineTypeSet配置了(不为空),才进行条件判断
            if (skuInfo == null
                    || (CollectionUtils.isNotEmpty(skuTypeSet) && !skuTypeSet.contains(skuInfo.getSkuType()))
                    || (CollectionUtils.isNotEmpty(combineTypeSet) && !combineTypeSet.contains(skuInfo.getCombineType()))) {
                return false;
            }
            return true;
        }).collect(Collectors.toList());
        param.setBatchList(batchStockDOList);
    }
}
