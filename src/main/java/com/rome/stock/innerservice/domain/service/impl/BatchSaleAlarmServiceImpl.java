package com.rome.stock.innerservice.domain.service.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.rome.arch.core.exception.RomeException;
import com.rome.arch.util.dynamic.DynamicDataSourceEnum;
import com.rome.arch.util.dynamic.TargetDataSource;
import com.rome.scm.common.monitor.CustomMonitorFacade;
import com.rome.scm.common.monitor.CustomMonitorTypeEnum;
import com.rome.stock.common.enums.warehouse.SaleStatusVO;
import com.rome.stock.common.utils.BatchStockUtils;
import com.rome.stock.common.utils.DateUtil;
import com.rome.stock.innerservice.api.dto.StockAlarmConfigDTO;
import com.rome.stock.innerservice.api.dto.StockAlarmConfigDetailDTO;
import com.rome.stock.innerservice.api.dto.alarm.BatchSaleAlarmDTO;
import com.rome.stock.innerservice.api.dto.alarm.BatchSaleAlarmDetailDTO;
import com.rome.stock.innerservice.common.ResCode;
import com.rome.stock.innerservice.common.RomeCollectionUtil;
import com.rome.stock.innerservice.common.StringUtils;
import com.rome.stock.innerservice.domain.convertor.alarm.BatchSaleAlarmConvertor;
import com.rome.stock.innerservice.domain.convertor.alarm.BatchSaleAlarmDetailConvertor;
import com.rome.stock.innerservice.domain.convertor.alarm.StockAlarmConfigConvertor;
import com.rome.stock.innerservice.domain.entity.RealWarehouseE;
import com.rome.stock.innerservice.domain.repository.BatchStockRepository;
import com.rome.stock.innerservice.domain.repository.RealWarehouseRepository;
import com.rome.stock.innerservice.domain.repository.RwBatchRepository;
import com.rome.stock.innerservice.domain.repository.alarm.BatchSaleAlarmDetailRepository;
import com.rome.stock.innerservice.domain.repository.alarm.BatchSaleAlarmRepository;
import com.rome.stock.innerservice.domain.repository.alarm.StockAlarmConfigRepository;
import com.rome.stock.innerservice.domain.repository.alarm.StockAlarmHeadRepository;
import com.rome.stock.innerservice.domain.service.BatchSaleAlarmService;
import com.rome.stock.innerservice.infrastructure.dataobject.BatchStockDO;
import com.rome.stock.innerservice.infrastructure.dataobject.RwBatchDo;
import com.rome.stock.innerservice.infrastructure.dataobject.alarm.BatchSaleAlarmDO;
import com.rome.stock.innerservice.infrastructure.dataobject.alarm.BatchSaleAlarmDetailDO;
import com.rome.stock.innerservice.infrastructure.dataobject.alarm.StockAlarmConfigDO;
import com.rome.stock.innerservice.infrastructure.dataobject.alarm.StockAlarmHeadDO;
import com.rome.stock.innerservice.remote.cpfr.dto.DemandQueryDTO;
import com.rome.stock.innerservice.remote.cpfr.facade.DayChannelDemandFacade;
import com.rome.stock.innerservice.remote.item.dto.SkuInfoExtDTO;
import com.rome.stock.innerservice.remote.item.dto.SkuSalePriceDTO;
import com.rome.stock.innerservice.remote.item.dto.SkuUnitExtDTO;
import com.rome.stock.innerservice.remote.item.facade.SkuFacade;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.context.ApplicationContext;
import org.springframework.dao.DuplicateKeyException;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * BatchSaleAlarmService 实现
 *
 * <AUTHOR>
 * @date 2024-04-11
 */
@Slf4j
@Service
public class BatchSaleAlarmServiceImpl implements BatchSaleAlarmService {

    @Resource
    private BatchSaleAlarmRepository batchSaleAlarmRepository;


    @Resource
    private StockAlarmConfigRepository stockAlarmConfigRepository;

    @Resource
    private StockAlarmConfigConvertor stockAlarmConfigConvertor;

    @Resource
    private BatchSaleAlarmConvertor batchSaleAlarmConvertor;
    @Resource
    private BatchSaleAlarmDetailRepository batchSaleAlarmDetailRepository;

    @Resource
    private BatchStockRepository batchStockRepository;

    @Resource
    private RwBatchRepository rwBatchRepository;

    @Resource
    private RealWarehouseRepository realWarehouseRepository;

    @Resource
    private ApplicationContext applicationContext;
    @Resource
    private DayChannelDemandFacade dayChannelDemandFacade;

    @Resource
    private SkuFacade skuFacade;

    private final static String IDEMPOTENT_ERROR_1 = "Duplicate entry";
    private final static String IDEMPOTENT_ERROR_2 = "sc_stock_alarm_config";

    @Resource
    private StockAlarmHeadRepository stockAlarmHeadRepository;

    private static final String BASE_CONTENT = " **{realWarehouseCode}** {realWarehouseName}：累计预计滞销库存数量：**{sumAlarmBoxQty} 箱** ，金额：**￥ {sumAlarmPrice}**，最早滞销天数：**{minAlarmDayCount}天** ；已超{configPercent}效期库存数量：**{sumOverAlarmBoxQty} 箱**，金额：**￥ {sumOverAlarmPrice}**；共计滞销商品数：**{skuCount}**\n";

    @Resource
    private BatchSaleAlarmDetailConvertor batchSaleAlarmDetailConvertor;

    @Override
    public PageInfo<BatchSaleAlarmDTO> queryBatchSaleAlarmListByCondition(BatchSaleAlarmDTO paramDto) {
        Page page = PageHelper.startPage(paramDto.getPageIndex(), paramDto.getPageSize());
        List<BatchSaleAlarmDTO> list = batchSaleAlarmRepository.queryBatchSaleAlarmListByCondition(paramDto);
        if (CollectionUtils.isEmpty(list)) {
            PageInfo<BatchSaleAlarmDTO> pageInfo = new PageInfo<>(Collections.EMPTY_LIST);
            pageInfo.setTotal(0);
            return pageInfo;
        }
        //取明细表,未来滞销天数：多个批次取最小滞销天数
        List<Long> alarmIdList = list.stream().map(BatchSaleAlarmDTO::getId).distinct().collect(Collectors.toList());
        List<BatchSaleAlarmDetailDO> detailList = batchSaleAlarmDetailRepository.listMinAlarmDayCountByAlarmIdList(alarmIdList);
        Map<Long, Integer> detailListMap = detailList.stream().collect(Collectors.toMap(BatchSaleAlarmDetailDO::getAlarmId, BatchSaleAlarmDetailDO::getAlarmDayCount, (v1, v2) -> v1));
        List<String> skuCodeList = list.stream().map(BatchSaleAlarmDTO::getSkuCode).distinct().collect(Collectors.toList());
        List<SkuInfoExtDTO> skuInfoExtDTOS = skuFacade.skuListBySkuCodes(skuCodeList);

        Map<String, SkuInfoExtDTO> skuInfoExtDTOMap = skuInfoExtDTOS.stream().collect(Collectors.toMap(SkuInfoExtDTO::getSkuCode, Function.identity(), (v1, v2) -> v1));
        for (BatchSaleAlarmDTO batchSaleAlarmDTO : list) {
            if (detailListMap.containsKey(batchSaleAlarmDTO.getId())) {
                batchSaleAlarmDTO.setAlarmDayCount(detailListMap.get(batchSaleAlarmDTO.getId()));
            }
            if (Objects.nonNull(batchSaleAlarmDTO.getSalePrice()) && Objects.nonNull(batchSaleAlarmDTO.getAlarmSaleQty())) {
                batchSaleAlarmDTO.setAlarmPrice(batchSaleAlarmDTO.getSalePrice().multiply(batchSaleAlarmDTO.getAlarmSaleQty()).setScale(2, RoundingMode.HALF_UP));
            }
            //商品销售状态
            if (skuInfoExtDTOMap.containsKey(batchSaleAlarmDTO.getSkuCode())) {
                batchSaleAlarmDTO.setSaleStatus(SaleStatusVO.getDescByStatusCode(skuInfoExtDTOMap.get(batchSaleAlarmDTO.getSkuCode()).getSaleStatus()));
            }
        }
        PageInfo<BatchSaleAlarmDTO> pageList = new PageInfo<>(list);
        pageList.setTotal(page.getTotal());
        return pageList;
    }

    @Override
    public PageInfo<StockAlarmConfigDTO> queryStockAlarmConfigList(StockAlarmConfigDTO paramDto) {
        Page page = PageHelper.startPage(paramDto.getPageIndex(), paramDto.getPageSize());
        List<StockAlarmConfigDTO> list = stockAlarmConfigRepository.queryStockAlarmConfigList(paramDto);
        if (CollectionUtils.isEmpty(list)) {
            PageInfo<StockAlarmConfigDTO> pageInfo = new PageInfo<>(Collections.EMPTY_LIST);
            pageInfo.setTotal(0);
            return pageInfo;
        }
        PageInfo<StockAlarmConfigDTO> pageList = new PageInfo<>(list);
        pageList.setTotal(page.getTotal());
        return pageList;
    }

    @Override
    public List<StockAlarmConfigDetailDTO> listConfigDetailByAlarmId(Long alarmId) {
        List<StockAlarmConfigDetailDTO> list = stockAlarmConfigRepository.listConfigDetailByAlarmId(alarmId);
        return list;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void saveOrUpdateConfigAndDetailList(StockAlarmConfigDTO dto) {
        Integer alarmType = dto.getAlarmType();
        if (dto.getIsAll() == null) {
            dto.setIsAll(false);
        }

        if (alarmType == null || alarmType < 1 || alarmType > 2) {
            throw new RomeException(ResCode.STOCK_ERROR_1001, "预警类型必须是 1-仓库滞销预警 或 2-门店滞销预警");
        }

        StockAlarmConfigDO stockAlarmConfigDO = stockAlarmConfigConvertor.dtoToDO(dto);
        if (alarmType == 1 && CollectionUtils.isEmpty(dto.getItemDTOList())) {
            throw new RomeException(ResCode.STOCK_ERROR_1001, "配置明细不能为空");
        }
        if (alarmType == 2 && !dto.getIsAll() && CollectionUtils.isEmpty(dto.getItemDTOList())) {
            throw new RomeException(ResCode.STOCK_ERROR_1001, "配置明细不能为空");
        }
        if (Objects.isNull(dto.getAlarmSourceType())) {
            throw new RomeException(ResCode.STOCK_ERROR_1001, "预警值不能为空");
        }
        if (Objects.isNull(dto.getCompareType())) {
            throw new RomeException(ResCode.STOCK_ERROR_1001, "比较关系不能为空");
        }
        if (Objects.isNull(dto.getCompareValueType())) {
            throw new RomeException(ResCode.STOCK_ERROR_1001, "预警比较值不能为空");
        }
        if (Objects.isNull(dto.getMolecule())) {
            throw new RomeException(ResCode.STOCK_ERROR_1001, "预警上限分子不能为空");
        }
        if (Objects.isNull(dto.getDenominator())) {
            throw new RomeException(ResCode.STOCK_ERROR_1001, "预警上限分母不能为空");
        }
        if (Objects.isNull(dto.getLowerLimitQty())) {
            throw new RomeException(ResCode.STOCK_ERROR_1001, "预警下限不能为空");
        }
        if (dto.getMolecule() < 0) {
            throw new RomeException(ResCode.STOCK_ERROR_1001, "预警上限分子不能小于0");
        }
        if (dto.getDenominator() <= 0) {
            throw new RomeException(ResCode.STOCK_ERROR_1001, "预警上限分母不能小于等于0");
        }
        if (alarmType == 1) {
            List<Long> realWarehouseIds = dto.getItemDTOList().stream().map(StockAlarmConfigDetailDTO::getRealWarehouseId).distinct().collect(Collectors.toList());
            if (realWarehouseIds.size() != dto.getItemDTOList().size()) {
                throw new RomeException(ResCode.STOCK_ERROR_1001, "预警范围,仓库不能重复");
            }
        } else if (alarmType == 2) {
            List<String> realWarehouseCodes = dto.getItemDTOList().stream().map(StockAlarmConfigDetailDTO::getRealWarehouseCode).distinct().collect(Collectors.toList());
            if (realWarehouseCodes.size() != dto.getItemDTOList().size()) {
                throw new RomeException(ResCode.STOCK_ERROR_1001, "预警范围,门店不能重复");
            }
        }
        if (Objects.isNull(dto.getId())) {
            //新增
            try {
                int i = stockAlarmConfigRepository.insertStockAlarmConfig(stockAlarmConfigDO);
                if (i == 0) {
                    throw new RomeException(ResCode.STOCK_ERROR_1001, "插入失败");
                }
            } catch (DuplicateKeyException e) {
                if (e.getMessage() != null && (e.getMessage().indexOf(IDEMPOTENT_ERROR_1) > 0 && e.getMessage().indexOf(IDEMPOTENT_ERROR_2) > 0)) {
                    throw new RomeException(ResCode.STOCK_ERROR_1001, "预警类型已存在");
                } else {
                    throw e;
                }
            } catch (Exception e) {
                throw e;
            }
            for (StockAlarmConfigDetailDTO stockAlarmConfigDetailDTO : dto.getItemDTOList()) {
                if (alarmType == 1 && Objects.isNull(stockAlarmConfigDetailDTO.getRealWarehouseId())) {
                    throw new RomeException(ResCode.STOCK_ERROR_1001, ",仓库不能为空");
                } else if (alarmType == 2 && Objects.isNull(stockAlarmConfigDetailDTO.getRealWarehouseCode())) {
                    throw new RomeException(ResCode.STOCK_ERROR_1001, ",门店不能为空");
                }
                //插入明细
                stockAlarmConfigDetailDTO.setAlarmId(stockAlarmConfigDO.getId());
                stockAlarmConfigRepository.insertStockAlarmConfigDetail(stockAlarmConfigDetailDTO);
            }
        } else {
            //更新
            try {
                stockAlarmConfigRepository.updateByParams(stockAlarmConfigDO);
            } catch (DuplicateKeyException e) {
                if (e.getMessage() != null && (e.getMessage().indexOf(IDEMPOTENT_ERROR_1) > 0 && e.getMessage().indexOf(IDEMPOTENT_ERROR_2) > 0)) {
                    throw new RomeException(ResCode.STOCK_ERROR_1001, "预警类型已存在");
                } else {
                    throw e;
                }
            } catch (Exception e) {
                throw e;
            }

            //更新明细,先删除再插入
            stockAlarmConfigRepository.deleteByAlarmId(stockAlarmConfigDO.getId());

            //不是全部门店或仓库
            if (!stockAlarmConfigDO.getIsAll()) {
                //重写toString方法使用distinct去重
                List<StockAlarmConfigDetailDTO> itemDTOList = dto.getItemDTOList().stream().distinct().collect(Collectors.toList());
                for (StockAlarmConfigDetailDTO stockAlarmConfigDetailDTO : itemDTOList) {
                    if (alarmType == 1 && Objects.isNull(stockAlarmConfigDetailDTO.getRealWarehouseId())) {
                        throw new RomeException(ResCode.STOCK_ERROR_1001, ",仓库不能为空");
                    } else if (alarmType == 2 && Objects.isNull(stockAlarmConfigDetailDTO.getRealWarehouseCode())) {
                        throw new RomeException(ResCode.STOCK_ERROR_1001, ",门店不能为空");
                    }
                    //插入明细
                    stockAlarmConfigDetailDTO.setAlarmId(stockAlarmConfigDO.getId());
                    stockAlarmConfigRepository.insertStockAlarmConfigDetail(stockAlarmConfigDetailDTO);
                }
            }

        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void changeStatus(StockAlarmConfigDTO dto) {
        stockAlarmConfigRepository.changeStatusById(dto.getId(), dto.getModifier());
    }


    @Override
    @TargetDataSource(DynamicDataSourceEnum.READ)
    public void calAlarmStock(StockAlarmConfigDTO stockAlarmConfigDTO) {
        StockAlarmConfigDetailDTO stockAlarmConfigItemDTO = stockAlarmConfigDTO.getItemDTOList().get(0);

        Long realWarehouseId = stockAlarmConfigItemDTO.getRealWarehouseId();
        RealWarehouseE realWarehouseE = realWarehouseRepository.getRealWarehouseById(realWarehouseId);
        // 以 X001-C001 W为例子，线上sc_batch_stock 只有1889个物料， 无需考虑分批处理，一次处理全部数据
        // 查询对应的批次库存和质检库存，做相同物料，相同批次合并, 根据物料进行分组，根据生产日期升序排列，空批次拍最前面
        Map<String, List<BatchSaleAlarmDetailDTO>> batchStock = this.getBatchStockAndCal(stockAlarmConfigItemDTO.getRealWarehouseId(), stockAlarmConfigDTO);
        //查询仓库下的所有物料，当前天数之后，每天的计划数量
        // 返回 Map<String, List<BigDecimal>> ; key =  skuCode  ;  value = list  list.get(day) 为 day天累计的需求数量
        Map<String, List<BigDecimal>> skuAlarmValueQtyMap = this.getAlarmValueQtyMap(stockAlarmConfigDTO, realWarehouseE);

        //查询商品属性
        List<String> skuCodeList = new ArrayList<>(batchStock.keySet());
        List<SkuInfoExtDTO> skuInfoExtDTOS = skuFacade.skusBySkuCode(skuCodeList);
        Map<String, SkuInfoExtDTO> skuInfoExtDTOMap = RomeCollectionUtil.listforMap(skuInfoExtDTOS, "skuCode");

        //查询商品零售价
        Map<String, SkuSalePriceDTO> skuSalePriceMap = skuFacade.salePricesBySkuCodesAndChannelCodesAndSaleUnitCodesWithRedis(skuCodeList, skuInfoExtDTOMap);

        //分组设置属性，并批量保存
        List<BatchSaleAlarmDO> dataList = new ArrayList<>();
        List<BatchSaleAlarmDetailDO> dataDetailList = new ArrayList<>();
        for (Map.Entry<String, List<BatchSaleAlarmDetailDTO>> entry : batchStock.entrySet()) {
            SkuInfoExtDTO skuInfoExtDTO = skuInfoExtDTOMap.get(entry.getKey());
            if (skuInfoExtDTO == null) {
                log.error("滞销预警：" + realWarehouseE.getRealWarehouseCode() + "未查询到商品信息 skuCode：" + entry.getKey());
                continue;
            }
            List<BatchSaleAlarmDetailDO> detailList = this.calAlarmDetail(entry.getKey(), entry.getValue(), skuAlarmValueQtyMap, stockAlarmConfigDTO, skuInfoExtDTO);
            if (CollectionUtils.isEmpty(detailList)) {
                continue;
            }
            SkuSalePriceDTO skuSalePriceDTO = skuSalePriceMap.get(entry.getKey());
            BatchSaleAlarmDO batchSaleAlarmDO = this.calAlarmData(detailList, realWarehouseE, stockAlarmConfigDTO, skuInfoExtDTO, skuSalePriceDTO);
            dataList.add(batchSaleAlarmDO);
            dataDetailList.addAll(detailList);
        }
        if (CollectionUtils.isEmpty(dataList)) {
            return;
        }
        //批量保存
        BatchSaleAlarmService batchSaleAlarmService = applicationContext.getBean(BatchSaleAlarmService.class);
        batchSaleAlarmService.saveData(dataList, dataDetailList);
    }

    /**
     * 批量保存数据
     *
     * @param dataList
     * @param dataDetailList
     */
    @Override
    @TargetDataSource(DynamicDataSourceEnum.WRITE)
    public void saveData(List<BatchSaleAlarmDO> dataList, List<BatchSaleAlarmDetailDO> dataDetailList) {
        List<List<BatchSaleAlarmDO>> lists = RomeCollectionUtil.splitList(dataList, 500);
        for (List<BatchSaleAlarmDO> list : lists) {
            batchSaleAlarmRepository.batchInsert(list);
            for (BatchSaleAlarmDO batchSaleAlarmDO : list) {
                for (BatchSaleAlarmDetailDO batchSaleAlarmDetailDO : batchSaleAlarmDO.getItemDoList()) {
                    batchSaleAlarmDetailDO.setAlarmId(batchSaleAlarmDO.getId());
                }
            }
        }
        List<List<BatchSaleAlarmDetailDO>> itemList = RomeCollectionUtil.splitList(dataDetailList, 500);
        for (List<BatchSaleAlarmDetailDO> batchSaleAlarmDetailDOS : itemList) {
            batchSaleAlarmDetailRepository.batchInsert(batchSaleAlarmDetailDOS);
        }
    }

    /**
     * 计算主记录数据
     *
     * @param detailList
     * @param skuSalePriceDTO
     */
    private BatchSaleAlarmDO calAlarmData(List<BatchSaleAlarmDetailDO> detailList, RealWarehouseE realWarehouseE, StockAlarmConfigDTO stockAlarmConfigDTO, SkuInfoExtDTO skuInfoExtDTO, SkuSalePriceDTO skuSalePriceDTO) {
        BatchSaleAlarmDO batchSaleAlarmDO = new BatchSaleAlarmDO();
        BatchSaleAlarmDetailDO batchSaleAlarmDetailDO = detailList.get(0);
        batchSaleAlarmDO.setHeadId(batchSaleAlarmDetailDO.getHeadId());
        batchSaleAlarmDO.setStockDate(batchSaleAlarmDetailDO.getStockDate());
        batchSaleAlarmDO.setRealWarehouseId(realWarehouseE.getId());
        batchSaleAlarmDO.setSkuId(batchSaleAlarmDetailDO.getSkuId());
        batchSaleAlarmDO.setSkuCode(batchSaleAlarmDetailDO.getSkuCode());

        if (skuSalePriceDTO == null || skuSalePriceDTO.getSalePrice() == null) {
            batchSaleAlarmDO.setSalePrice(BigDecimal.ZERO);
        } else {
            batchSaleAlarmDO.setSalePrice(skuSalePriceDTO.getSalePrice());
        }

        batchSaleAlarmDO.setUnitCode(skuInfoExtDTO.getSpuUnitCode());
        batchSaleAlarmDO.setUnitName(skuInfoExtDTO.getSpuUnitName());
        batchSaleAlarmDO.setSkuName(skuInfoExtDTO.getName());

        BigDecimal alarmSaleQty = BigDecimal.ZERO;
        BigDecimal totalQty = BigDecimal.ZERO;
        BigDecimal overConfigQty = BigDecimal.ZERO;
        Integer alarmStatus = 0;
        for (BatchSaleAlarmDetailDO saleAlarmDetailDO : detailList) {
            totalQty = totalQty.add(saleAlarmDetailDO.getStockQty());
            if (saleAlarmDetailDO.getAlarmStatus() == 1) {
                alarmStatus = 1;
            }
            // 记录超1/2批次库存量，不含=的量
            if (saleAlarmDetailDO.getAlarmDayCount() < 0) {
                overConfigQty = overConfigQty.add(saleAlarmDetailDO.getStockQty());
            } else {
                alarmSaleQty = alarmSaleQty.add(saleAlarmDetailDO.getAlarmQty());
            }
        }
        batchSaleAlarmDO.setAlarmSaleQty(alarmSaleQty);
        batchSaleAlarmDO.setTotalQty(totalQty);
        batchSaleAlarmDO.setOverConfigQty(overConfigQty);
        batchSaleAlarmDO.setConfigPercent(stockAlarmConfigDTO.getMolecule() + "/" + stockAlarmConfigDTO.getDenominator());
        batchSaleAlarmDO.setAlarmStatus(alarmStatus);

        batchSaleAlarmDO.setAlarmType(stockAlarmConfigDTO.getAlarmType());
        batchSaleAlarmDO.setItemDoList(detailList);
        return batchSaleAlarmDO;
    }

    /**
     * 计算数据
     *
     * @param skuCode
     * @param value               批次库存
     * @param skuAlarmValueQtyMap 需求计划
     * @param stockAlarmConfigDTO 配置
     * @param skuInfoExtDTO       商品数据
     * @return
     */
    private List<BatchSaleAlarmDetailDO> calAlarmDetail(String skuCode, List<BatchSaleAlarmDetailDTO> value, Map<String, List<BigDecimal>> skuAlarmValueQtyMap, StockAlarmConfigDTO stockAlarmConfigDTO, SkuInfoExtDTO skuInfoExtDTO) {
        List<BatchSaleAlarmDetailDO> result = new ArrayList<>();
        if (skuFacade.isSupplementaryMaterial(skuInfoExtDTO)) {
            return result;
        }
        if (skuInfoExtDTO.getTotalShelfLife() == null || skuInfoExtDTO.getTotalShelfLife() == 0) {
            log.error("滞销预警：" + "realWarehouseE.getRealWarehouseCode()" + "商品的保质期为0, skuCode：" + skuCode);
            return result;
        }
        //获取库存日期 stockDate 为当天时间
        Date stockDate = DateUtil.parseDate(stockAlarmConfigDTO.getStockDateStr());
        Integer totalShelfLife = skuInfoExtDTO.getTotalShelfLife();
        Integer configDayCount = stockAlarmConfigDTO.getAlarmDayCount();
        BatchSaleAlarmDetailDO item = null;
        BatchSaleAlarmDetailDO preDTO = null;
        int sortNum = 1;
        List<BigDecimal> alarmValueQtyList = skuAlarmValueQtyMap.get(skuCode);
        for (BatchSaleAlarmDetailDTO batchSaleAlarmDetailDTO : value) {
            batchSaleAlarmDetailDTO.setValidity(totalShelfLife);
            if (batchSaleAlarmDetailDTO.getProductDate() == null) {
                batchSaleAlarmDetailDTO.setAlarmDayCount(-9999999);
            } else {
                // 计算到达指定效期，需要的天数 无需小数，并且向上取整
                BigDecimal dayCount = new BigDecimal(totalShelfLife).multiply(new BigDecimal(stockAlarmConfigDTO.getMolecule())).divide(new BigDecimal(stockAlarmConfigDTO.getDenominator()), 0, RoundingMode.UP);
                // 生产日期 + (到达指定效期，需要的天数) = 到达指定效期所需要的日期
                // 到达指定效期所需要的日期 - 当前时间  = 当前时间到达指定效期所需要的天数
                // 生产日期 + (到达指定效期，需要的天数) - 当前时间  = 当前时间到达指定效期所需要的天数
                long diff = DateUtil.diff(stockDate, batchSaleAlarmDetailDTO.getProductDate(), 1000 * 3600 * 24);
                int alarmDayCount = (int) (diff + dayCount.longValue());
                batchSaleAlarmDetailDTO.setAlarmDayCount(alarmDayCount);
            }
            Integer alarmDayCount = batchSaleAlarmDetailDTO.getAlarmDayCount();
            if (alarmDayCount > configDayCount) {
                //大于需求计划天数
                continue;
            }
            item = batchSaleAlarmConvertor.detailDto2Do(batchSaleAlarmDetailDTO);
            item.setSortNum(sortNum++);
            //累计需求数据
            BigDecimal currentSumSaleQty;
            if (alarmDayCount < 0 || CollectionUtils.isEmpty(alarmValueQtyList)) {
                currentSumSaleQty = BigDecimal.ZERO;
            } else if (alarmDayCount >= alarmValueQtyList.size()) {
                currentSumSaleQty = alarmValueQtyList.get(alarmValueQtyList.size() - 1);
            } else {
                currentSumSaleQty = alarmValueQtyList.get(alarmDayCount);
            }
            item.setCurrentSumSaleQty(currentSumSaleQty);

            if (preDTO != null) {
                item.setSaleQty(currentSumSaleQty.subtract(preDTO.getCurrentSumSaleQty()));
                item.setCurrentSumStockQty(item.getStockQty().add(preDTO.getCurrentSumStockQty()));
            } else {
                item.setSaleQty(currentSumSaleQty);
                item.setCurrentSumStockQty(item.getStockQty());
            }
            //累计的预警滞销量 = 累计的库数量 - 累计的需求计划
            item.setCurrentSumAlarmQty(item.getCurrentSumStockQty().subtract(currentSumSaleQty));
            BigDecimal alarmQty = item.getStockQty().compareTo(item.getCurrentSumAlarmQty()) < 0 ? item.getStockQty() : item.getCurrentSumAlarmQty();

            //和 0 取小
            alarmQty = alarmQty.compareTo(BigDecimal.ZERO) < 0 ? BigDecimal.ZERO : alarmQty;
            item.setAlarmQty(alarmQty);
            //如果预警天数大于0 ，预警数量大于配置数量，则需要发送消息
            if (item.getAlarmDayCount() > 0 && item.getCurrentSumAlarmQty().compareTo(item.getConfigAlarmQty()) >= 0) {
                item.setAlarmStatus(1);
            }
            preDTO = item;
            result.add(item);
        }
        return result;
    }

    /**
     * 返回参数是  key = skuCode-day  value = 累计的需求计划数量
     *
     * @param ruleDTO
     * @param realWarehouseE
     * @return
     */
    private Map<String, List<BigDecimal>> getAlarmValueQtyMap(StockAlarmConfigDTO ruleDTO, RealWarehouseE realWarehouseE) {
        Map<String, List<BigDecimal>> resultMap = new HashMap<>();
        Integer dayCount = ruleDTO.getAlarmDayCount();
        //需要取前一天的需求计划，当天的不一定跑出来了
        Date yesterday = DateUtil.offsiteDate(DateUtil.parseDate(ruleDTO.getStockDateStr()), Calendar.DAY_OF_YEAR, -1);
        String yesterdayStr = DateUtil.formatDate(yesterday);
        // 根据规则配置，查询需求计划,封装获取 近90天的计划数据
        if (Objects.equals(ruleDTO.getAlarmSourceType(), 1)) {
            DemandQueryDTO demandQueryDTO = new DemandQueryDTO();
            demandQueryDTO.setEndDate(yesterdayStr);
            demandQueryDTO.setStartDate(yesterdayStr);
            demandQueryDTO.setCreateTime(yesterdayStr);
            demandQueryDTO.setRealWarehouseOutCode(realWarehouseE.getRealWarehouseCode());
            resultMap = dayChannelDemandFacade.queryDayChannelDemand(demandQueryDTO, dayCount);
        } else {
            throw new RomeException("-1", "未知的预警值" + ruleDTO.getAlarmSourceType());
        }
        return resultMap;
    }


    /**
     * 根据sku分组，并且批次库存按照生产日期升序排列
     *
     * @param realWarehouseId
     * @return
     */
    private Map<String, List<BatchSaleAlarmDetailDTO>> getBatchStockAndCal(Long realWarehouseId, StockAlarmConfigDTO stockAlarmConfigDTO) {
        Map<String, List<BatchSaleAlarmDetailDTO>> result = new HashMap<>();
        Map<String, BatchSaleAlarmDetailDTO> margerMap = new HashMap<>();
        List<BatchStockDO> batchStockDOList = batchStockRepository.listBatchStockByRwId(realWarehouseId);
        List<RwBatchDo> rwBatchDos = rwBatchRepository.queryWaitQualityByRwId(realWarehouseId);
        if (CollectionUtils.isNotEmpty(rwBatchDos)) {
            List<BatchStockDO> qualityBatchStockDOList = this.covertToBatchStockDO(rwBatchDos);
            batchStockDOList.addAll(qualityBatchStockDOList);
        }
        //转换对象并做批次合并
        for (BatchStockDO batchStockDO : batchStockDOList) {
            List<BatchSaleAlarmDetailDTO> itemList = result.getOrDefault(batchStockDO.getSkuCode(), new ArrayList<>());
            String uniqKey = null;
            if (batchStockDO.getProductDate() == null) {
                uniqKey = batchStockDO.getSkuCode() + "-";
            } else {
                uniqKey = batchStockDO.getSkuCode() + "-" + DateUtil.formatDate(batchStockDO.getProductDate());
            }
            if (margerMap.containsKey(uniqKey)) {
                //如果已经存在，则需要累加库存数量
                BatchSaleAlarmDetailDTO batchSaleAlarmDetailDTO = margerMap.get(uniqKey);
                BigDecimal stockQty = batchStockDO.getSkuQty().add(batchSaleAlarmDetailDTO.getStockQty());
                batchSaleAlarmDetailDTO.setStockQty(stockQty);
            } else {
                BatchSaleAlarmDetailDTO batchSaleAlarmDetailDTO = new BatchSaleAlarmDetailDTO();
                batchSaleAlarmDetailDTO.setSkuId(batchStockDO.getSkuId());
                batchSaleAlarmDetailDTO.setSkuCode(batchStockDO.getSkuCode());
                batchSaleAlarmDetailDTO.setProductDate(batchStockDO.getProductDate());
                batchSaleAlarmDetailDTO.setStockQty(batchStockDO.getSkuQty());
                batchSaleAlarmDetailDTO.setHeadId(stockAlarmConfigDTO.getHeadId());
                batchSaleAlarmDetailDTO.setStockDate(DateUtil.parseDate(stockAlarmConfigDTO.getStockDateStr()));
                batchSaleAlarmDetailDTO.setConfigAlarmQty(stockAlarmConfigDTO.getLowerLimitQty());
                batchSaleAlarmDetailDTO.setAlarmStatus(0);
                batchSaleAlarmDetailDTO.setBatchCode(batchStockDO.getBatchCode());
                itemList.add(batchSaleAlarmDetailDTO);
                margerMap.put(uniqKey, batchSaleAlarmDetailDTO);
            }
            result.put(batchStockDO.getSkuCode(), itemList);
        }
        //需要做好排序 按照生产日期升序排列，空批次排在最前面
        for (Map.Entry<String, List<BatchSaleAlarmDetailDTO>> entry : result.entrySet()) {
            List<BatchSaleAlarmDetailDTO> itemList = entry.getValue();
            itemList.sort((o1, o2) -> {
                if (o1.getProductDate() == o2.getProductDate()) {
                    return 0;
                }
                if (o1.getProductDate() == null) {
                    return -1;
                }
                if (o2.getProductDate() == null) {
                    return 1;
                }
                return o1.getProductDate().compareTo(o2.getProductDate());
            });
        }
        return result;
    }

    private List<BatchStockDO> covertToBatchStockDO(List<RwBatchDo> rwBatchDos) {
        List<BatchStockDO> result = new ArrayList<>();
        for (RwBatchDo rwBatchDo : rwBatchDos) {
            BatchStockDO batchStockDO = new BatchStockDO();
            batchStockDO.setSkuId(rwBatchDo.getSkuId());
            batchStockDO.setSkuCode(rwBatchDo.getSkuCode());
            batchStockDO.setSkuQty(rwBatchDo.getActualQty());
            batchStockDO.setBatchCode(rwBatchDo.getBatchCode());
            batchStockDO.setRealWarehouseId(rwBatchDo.getRealWarehouseId());
            batchStockDO.setValidity(rwBatchDo.getValidity());
            batchStockDO.setProductDate(rwBatchDo.getProductDate());
            batchStockDO.setEntryDate(rwBatchDo.getCreateTime());
            result.add(batchStockDO);
        }
        return result;
    }

    @Override
    public List<StockAlarmConfigDTO> queryAllConfigList(StockAlarmConfigDTO stockAlarmConfigDTO) {
        List<StockAlarmConfigDTO> result = stockAlarmConfigRepository.queryStockAlarmConfigList(stockAlarmConfigDTO);
        if (CollectionUtils.isEmpty(result)) {
            return result;
        }
        List<Long> idList = RomeCollectionUtil.getValueList(result, "id");
        List<StockAlarmConfigDetailDTO> detailDTOList = stockAlarmConfigRepository.listConfigDetailByAlarmIdList(idList);
        Map<Object, List<StockAlarmConfigDetailDTO>> alarmIdListMap = RomeCollectionUtil.listforListMap(detailDTOList, "alarmId");
        for (StockAlarmConfigDTO alarmConfigDTO : result) {
            alarmConfigDTO.setItemDTOList(alarmIdListMap.get(alarmConfigDTO.getId()));
        }
        return result;
    }

    @Override
    public void alarmStockNotice() {
        //查询出执行失败的数据，发送飞书预警
        this.callExecuteNotice();
        List<BatchSaleAlarmDTO> batchSaleAlarmDTOList = batchSaleAlarmRepository.listNeedAlarmData();
        if (CollectionUtils.isEmpty(batchSaleAlarmDTOList)) {
            return;
        }
        Set<Long> realWarehouseIds = new HashSet<>();
        List<Long> alarmIds = new ArrayList<>();
        Map<Long, List<BatchSaleAlarmDTO>> headIdMapList = new HashMap<>();
        Set<String> skuSet = new HashSet<>();
        for (BatchSaleAlarmDTO batchSaleAlarmDTO : batchSaleAlarmDTOList) {
            realWarehouseIds.add(batchSaleAlarmDTO.getRealWarehouseId());
            List<BatchSaleAlarmDTO> itemList = headIdMapList.getOrDefault(batchSaleAlarmDTO.getHeadId(), new ArrayList<>());
            itemList.add(batchSaleAlarmDTO);
            headIdMapList.put(batchSaleAlarmDTO.getHeadId(), itemList);
            alarmIds.add(batchSaleAlarmDTO.getId());
            skuSet.add(batchSaleAlarmDTO.getSkuCode());
        }
        Map<String, SkuUnitExtDTO> skuUnitExtDTOMap = skuFacade.querySkuBoxUnits(new ArrayList<>(skuSet));

        List<RealWarehouseE> realWarehouseES = realWarehouseRepository.queryWarehouseByIds(new ArrayList<>(realWarehouseIds));
        Map<Long, RealWarehouseE> realWarehouseEMap = RomeCollectionUtil.listforMap(realWarehouseES, "id");
        ArrayList<Long> headIds = new ArrayList<>(headIdMapList.keySet());
        List<StockAlarmHeadDO> headDOList = stockAlarmHeadRepository.listByIds(headIds);
        Map<Long, StockAlarmHeadDO> stockAlarmHeadDOMap = RomeCollectionUtil.listforMap(headDOList, "id");
        List<BatchSaleAlarmDetailDO> detailList = batchSaleAlarmDetailRepository.listMinAlarmDayCountByHeadIdList(headIds);
        Map<Long, BatchSaleAlarmDetailDO> alarmDetailMap = RomeCollectionUtil.listforMap(detailList, "headId");


        for (Map.Entry<Long, List<BatchSaleAlarmDTO>> entry : headIdMapList.entrySet()) {
            StockAlarmHeadDO stockAlarmHeadDO = stockAlarmHeadDOMap.get(entry.getKey());
            if (stockAlarmHeadDO == null) {
                continue;
            }
            StockAlarmConfigDO stockAlarmConfigDO = JSON.parseObject(stockAlarmHeadDO.getConfigJson(), StockAlarmConfigDO.class);
            if (stockAlarmConfigDO == null) {
                continue;
            }

            JSONObject json = new JSONObject();

            BatchSaleAlarmDetailDO batchSaleAlarmDetailDO = alarmDetailMap.get(entry.getKey());
            if (batchSaleAlarmDetailDO != null) {
                json.put("minAlarmDayCount", batchSaleAlarmDetailDO.getAlarmDayCount());
            } else {
                json.put("minAlarmDayCount", "0");
            }
            RealWarehouseE realWarehouseE = realWarehouseEMap.get(stockAlarmHeadDO.getRealWarehouseId());
            if (realWarehouseE == null) {
                continue;
            }
            json.put("realWarehouseCode", realWarehouseE.getRealWarehouseCode());
            json.put("realWarehouseName", realWarehouseE.getRealWarehouseName());
            List<String> accountCodeList = new ArrayList<>();
            if (StringUtils.isNotBlank(stockAlarmConfigDO.getAccountCodes()) && StringUtils.isNotBlank(stockAlarmConfigDO.getAccountCodes().trim())) {
                accountCodeList.addAll(Arrays.asList(stockAlarmConfigDO.getAccountCodes().trim().split(",")));

            }
            if (StringUtils.isNotBlank(stockAlarmConfigDO.getItemDTOList().get(0).getAccountCodes()) && StringUtils.isNotBlank(stockAlarmConfigDO.getItemDTOList().get(0).getAccountCodes().trim())) {
                accountCodeList.addAll(Arrays.asList(stockAlarmConfigDO.getItemDTOList().get(0).getAccountCodes().trim().split(",")));
            }
            String atString = this.getAtString(accountCodeList);
            //  "sumAlarmBoxQty", "sumAlarmPrice", "minAlarmDayCount", "sumOverAlarmBoxQty", "sumOverAlarmPrice",
            json.put("skuCount", entry.getValue().size());
            BigDecimal sumAlarmBoxQty = BigDecimal.ZERO;
            BigDecimal sumAlarmPrice = BigDecimal.ZERO;
            ;
            BigDecimal sumOverAlarmBoxQty = BigDecimal.ZERO;
            BigDecimal sumOverAlarmPrice = BigDecimal.ZERO;

            for (BatchSaleAlarmDTO batchSaleAlarmDTO : entry.getValue()) {
                json.put("configPercent", batchSaleAlarmDTO.getConfigPercent());
                String skuCode = batchSaleAlarmDTO.getSkuCode();

                SkuUnitExtDTO boxUnitExtDTO = skuUnitExtDTOMap.get(skuCode);
                if (boxUnitExtDTO != null && boxUnitExtDTO.getScale() != null) {
                    sumAlarmBoxQty = sumAlarmBoxQty.add(batchSaleAlarmDTO.getAlarmSaleQty().divide(boxUnitExtDTO.getScale(), 2, RoundingMode.HALF_UP));
                    sumOverAlarmBoxQty = sumOverAlarmBoxQty.add(batchSaleAlarmDTO.getOverConfigQty().divide(boxUnitExtDTO.getScale(), 2, RoundingMode.HALF_UP));
                }

                sumAlarmPrice = sumAlarmPrice.add(batchSaleAlarmDTO.getAlarmSaleQty().multiply(batchSaleAlarmDTO.getSalePrice()).setScale(2, RoundingMode.HALF_UP));
                sumOverAlarmPrice = sumOverAlarmPrice.add(batchSaleAlarmDTO.getOverConfigQty().multiply(batchSaleAlarmDTO.getSalePrice()).setScale(2, RoundingMode.HALF_UP));
            }

            json.put("sumAlarmBoxQty", sumAlarmBoxQty);
            json.put("sumAlarmPrice", sumAlarmPrice);
            json.put("sumOverAlarmBoxQty", sumOverAlarmBoxQty);
            json.put("sumOverAlarmPrice", sumOverAlarmPrice);
            String content = this.replaceDataForContent(BASE_CONTENT, json);
            boolean sendSuccess = false;
            if (Objects.equals(stockAlarmConfigDO.getAlarmWay(), 1)) {
                sendSuccess = CustomMonitorFacade.callMonitor(atString + content, "仓库滞销预警", CustomMonitorTypeEnum.TYPE_EXCEPTION, "仓库滞销预警", "stock-inner-service");
            } else {
                //其他的预警方式
            }
            if (sendSuccess) {
                batchSaleAlarmRepository.updateAlarmStatusSuccess(entry.getKey());
            }
        }
    }

    private void callExecuteNotice() {
        StockAlarmConfigDTO stockAlarmConfigDTO = new StockAlarmConfigDTO();
        stockAlarmConfigDTO.setAlarmType(1);
        stockAlarmConfigDTO.setStatus(1);
        List<StockAlarmConfigDTO> stockAlarmConfigDTOS = this.queryAllConfigList(stockAlarmConfigDTO);
        Integer totalCount = 0;
        for (StockAlarmConfigDTO alarmConfigDTO : stockAlarmConfigDTOS) {
            if (CollectionUtils.isNotEmpty(alarmConfigDTO.getItemDTOList())) {
                totalCount += alarmConfigDTO.getItemDTOList().size();
            }
        }

        List<StockAlarmHeadDO> headDOList = stockAlarmHeadRepository.listByStockDate(DateUtil.formatDate(new Date()));
        StringBuilder sb = new StringBuilder();
        if (CollectionUtils.isEmpty(headDOList)) {
            //没有执行记录，发送飞书预警
            sb.append("库存滞销预警数据计算未执行，无执行记录，需要执行" + totalCount + "条");
        } else {
            Integer successCount = 0;
            Integer failedCount = 0;
            for (StockAlarmHeadDO stockAlarmHeadDO : headDOList) {
                if (stockAlarmHeadDO.getStatus() == 1) {
                    successCount += 1;
                } else {
                    failedCount += 1;
                }
            }
            sb.append("库存滞销预警数据计算，成功：")
                    .append(successCount).append("条，")
                    .append("失败").append(failedCount).append("条.")
                    .append("需要执行 ").append(totalCount).append("条.");
            ;
        }
        CustomMonitorFacade.callMonitor(sb.toString(), "仓库滞销预警", CustomMonitorTypeEnum.TYPE_EXCEPTION, "库存内部预警", "stock-inner-service");
    }

    private String getAtString(List<String> accountCodeList) {
        if (CollectionUtils.isEmpty(accountCodeList)) {
            return "";
        }
        String atString = "<at id = \"{accountCode}\"></at>";
        StringBuilder stringBuilder = new StringBuilder();
        for (String accountCode : accountCodeList) {
            stringBuilder.append(atString.replace("{accountCode}", accountCode)).append(" ");
        }
        return stringBuilder.toString();
    }

    /**
     * 替换字段中的内容
     *
     * @param content
     * @param jsonObject
     * @return
     */
    private String replaceDataForContent(String content, JSONObject jsonObject) {
        for (String key : jsonObject.keySet()) {
            String value = jsonObject.getString(key);
            content = content.replace("{" + key + "}", value == null ? "" : value);
        }
        return content;
    }

    @Override
    public void deleteExtData(String dateStr) {
        List<Long> idList = stockAlarmHeadRepository.queryExtData(dateStr);
        if (CollectionUtils.isEmpty(idList)) {
            return;
        }
        for (Long headId : idList) {
            batchSaleAlarmRepository.deleteByHeadId(headId);
            batchSaleAlarmDetailRepository.deleteByHeadId(headId);
            stockAlarmHeadRepository.updateStatus(headId, 3, "超过31天，删除数据");
        }
    }

    @Override
    public List<BatchSaleAlarmDetailDTO> listByAlarmIdList(List<Long> alarmIdList) {
        List<BatchSaleAlarmDetailDO> resList = batchSaleAlarmDetailRepository.listByAlarmIdList(alarmIdList);
        List<String> skuCodeList = resList.stream().map(BatchSaleAlarmDetailDO::getSkuCode).distinct().collect(Collectors.toList());
        List<SkuInfoExtDTO> skuInfoExtDTOS = skuFacade.skuListBySkuCodes(skuCodeList);
        Map<String, SkuInfoExtDTO> skuInfoExtDTOMap = skuInfoExtDTOS.stream().collect(Collectors.toMap(SkuInfoExtDTO::getSkuCode, Function.identity(), (v1, v2) -> v1));
        List<BatchSaleAlarmDetailDTO> resultList = batchSaleAlarmDetailConvertor.doListToDtoList(resList);
        for (BatchSaleAlarmDetailDTO batchSaleAlarmDetailDTO : resultList) {
            //商品销售状态
            if (skuInfoExtDTOMap.containsKey(batchSaleAlarmDetailDTO.getSkuCode())) {
                batchSaleAlarmDetailDTO.setSaleStatus(SaleStatusVO.getDescByStatusCode(skuInfoExtDTOMap.get(batchSaleAlarmDetailDTO.getSkuCode()).getSaleStatus()));
                batchSaleAlarmDetailDTO.setSkuName(skuInfoExtDTOMap.get(batchSaleAlarmDetailDTO.getSkuCode()).getName());
            }
        }
        return resultList;
    }
}
