package com.rome.stock.innerservice.domain.entity;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

import javax.annotation.Resource;

import org.apache.commons.collections4.CollectionUtils;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Component;

import com.rome.stock.innerservice.common.AlikAssert;
import com.rome.stock.innerservice.common.ResCode;
import com.rome.stock.innerservice.common.RomeCollectionUtil;
import com.rome.stock.innerservice.domain.entity.warehouserecord.WarehouseRecordDetail;
import com.rome.stock.innerservice.domain.entity.warehouserecord.WarehouseRecordE;
import com.rome.stock.core.domain.repository.CoreRealWarehouseStockRepository;
import com.rome.stock.innerservice.domain.repository.warehouserecord.ShopReplenishWarehouseRepository;
import com.rome.stock.core.infrastructure.dataobject.core.CoreRealStockOpDO;
import com.rome.stock.core.infrastructure.dataobject.core.CoreRealStockOpDetailDO;
import com.rome.stock.innerservice.remote.item.dto.SkuUnitExtDTO;
import com.rome.stock.innerservice.remote.item.facade.SkuFacade;

import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <AUTHOR> generate>
 * @version  2019-07-31 22:42:33
 */
@Data
@Component
@Scope("prototype")
@EqualsAndHashCode(callSuper = false)
public class TurnoverBoxE extends BaseE implements Serializable{

	@Resource
	private ShopReplenishWarehouseRepository shopReplenishWarehouseRepository;
	@Resource
	private SkuFacade skuFacade;
	@Resource
	private CoreRealWarehouseStockRepository coreRealWarehouseStockRepository;

	
	//columns START
	/**
	 * 唯一主键
	 */
	private Long id;
	/**
	 * 出入库单据编号
	 */
	private String recordCode;
	/**
	 * 物料号
	 */
	private String skuCode;
	/**
	 * 包材型号
	 */
	private String boxType;
	/**
	 * 包材数量
	 */
	private Integer boxQty;
	/**
	 * 单位
	 */
	private String unitCode;

	/**
	 * 增加出入库单明细并减少周转箱库存
	 * @param turnoverBoxList
	 * @return
	 */
	public CoreRealStockOpDO decreaseTurnoverBox(List<TurnoverBoxE> turnoverBoxList, WarehouseRecordE warehouseRecordE){
		CoreRealStockOpDO coreRecordRealStockDecreaseDO = null;
		if(CollectionUtils.isNotEmpty(turnoverBoxList)){
			//获取商品及单位信息
			List<String>  skuCodeList = RomeCollectionUtil.getValueList(turnoverBoxList, "skuCode");
			List<SkuUnitExtDTO> skuUnitList = skuFacade.querySkuUnits(skuCodeList);
			if(CollectionUtils.isNotEmpty(skuUnitList)) {
				Map<String, List<SkuUnitExtDTO>> unitListMap = RomeCollectionUtil.listforListMap(skuUnitList, "skuCode");
				List<WarehouseRecordDetail> warehouseRecordDetails = new ArrayList<>();
				for (TurnoverBoxE turnoverBoxE : turnoverBoxList) {
					SkuUnitExtDTO skuInfo = null;
					List<SkuUnitExtDTO> unitList = unitListMap.get(turnoverBoxE.getSkuCode());
					if(CollectionUtils.isNotEmpty(unitList)) {
						for (SkuUnitExtDTO unitExtDTO : unitList) {
							if (turnoverBoxE.getUnitCode().equals(unitExtDTO.getUnitCode())) {
								skuInfo = unitExtDTO;
							}
						}
					}
					AlikAssert.isNotNull(skuInfo, ResCode.STOCK_ERROR_1001, "周转箱"+ turnoverBoxE.getSkuCode() + "单位"+ turnoverBoxE.getUnitCode() + ResCode.STOCK_ERROR_2010_DESC);
					WarehouseRecordDetail detail = new WarehouseRecordDetail();
					detail.setRecordCode(warehouseRecordE.getRecordCode());
					detail.setWarehouseRecordId(warehouseRecordE.getId());
					detail.setPlanQty(new BigDecimal(turnoverBoxE.getBoxQty()));
					detail.setActualQty(new BigDecimal(turnoverBoxE.getBoxQty()));
					detail.setSkuCode(turnoverBoxE.getSkuCode());
					detail.setUnitCode(turnoverBoxE.getUnitCode());
					detail.setUnit(skuInfo.getUnitCode());
					detail.setSkuId(skuInfo.getSkuId());
					detail.setDeliveryLineNo("999999");
					detail.setSapPoNo("999999");
					detail.setLineNo("9999");
					warehouseRecordDetails.add(detail);
				}
				shopReplenishWarehouseRepository.saveWarehouseRecordDetails(warehouseRecordDetails);
				//执行库存扣减操作
				warehouseRecordE.setWarehouseRecordDetails(warehouseRecordDetails);
				coreRecordRealStockDecreaseDO = this.initCoreStockObj(warehouseRecordE);
				coreRealWarehouseStockRepository.decreaseRealQty(coreRecordRealStockDecreaseDO);

			}
		}
		return coreRecordRealStockDecreaseDO;

	}

	/**
	 * 初始化增加实体仓库库存的对象
	 */
	private CoreRealStockOpDO initCoreStockObj(WarehouseRecordE recordE) {
		List<CoreRealStockOpDetailDO> increaseDetails = new ArrayList<>();
		for (WarehouseRecordDetail detail : recordE.getWarehouseRecordDetails()) {
			CoreRealStockOpDetailDO coreRealStockOpDetailDO = new CoreRealStockOpDetailDO();
			coreRealStockOpDetailDO.setSkuId(detail.getSkuId());
			coreRealStockOpDetailDO.setChannelCode(recordE.getChannelCode());
			coreRealStockOpDetailDO.setSkuCode(detail.getSkuCode());
			coreRealStockOpDetailDO.setCheckBeforeOp(false);
			coreRealStockOpDetailDO.setRealQty(detail.getActualQty());
			coreRealStockOpDetailDO.setRealWarehouseId(recordE.getRealWarehouseId());
			increaseDetails.add(coreRealStockOpDetailDO);
		}
		CoreRealStockOpDO coreRealStockOpDO = new CoreRealStockOpDO();
		coreRealStockOpDO.setRecordCode(recordE.getRecordCode());
		coreRealStockOpDO.setTransType(recordE.getRecordType());
		coreRealStockOpDO.setDetailDos(increaseDetails);
		return coreRealStockOpDO;
	}
}

	
