package com.rome.stock.innerservice.domain.entity.frontrecord;

import java.math.BigDecimal;
import java.util.Date;

import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Component;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 批次调整明细
 */
@Component
@Scope("prototype")
@Data
@EqualsAndHashCode(callSuper = false)
public class BatchAdjustRecordDetailE extends AbstractFrontRecordDetail {

    /**
     * 批次数
     */
    private BigDecimal skuQty;

    /**
     * 批次备注
     */
    private String remark;

    /**
     *批次类型(1,损 2，益)
     */
    private Integer adjustType;

    /**
     * 批次编号
     */
    private String batchCode;

    /**
     * 生产日期(YYYY-MM-DD)
     */
    private Date productDate;

    @ApiModelProperty(value = "有效期,天数")
    private Integer validity;
}
