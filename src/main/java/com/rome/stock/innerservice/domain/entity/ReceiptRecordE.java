package com.rome.stock.innerservice.domain.entity;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Component;

/**
 * @Description: 收货单
 * <p>
 * @Author: chuwen<PERSON><PERSON>  2019/5/31
 */
@Data
@Slf4j
@Component
@Scope("prototype")
@EqualsAndHashCode(callSuper = false)
public class ReceiptRecordE extends BaseE {

    /**
     * 唯一主键
     */
    private Long id;

    /**
     * po前置单据编号
     */
    private String frontRecordCode;

    /**
     * sap采购单编号
     */
    private String outRecordCode;

    /**
     * 入库单据编号
     */
    private String warehouseRecordCode;

    /**
     * wms单据编号
     */
    private String wmsRecordCode;

    /**
     * 质检同步状态 0:待同步 1:已同步
     */
    private String qualityStatus;

    /**
     * 是否推送订单中心(0.无需同步，1.待推送，2.已推送)
     */
    private Integer syncOrderStatus;
    
    /**
     * 操作员编码（工号）
     */
    private String operatorCode;
    
    /**
     * 操作员名称
     */
    private String operatorName;

}
