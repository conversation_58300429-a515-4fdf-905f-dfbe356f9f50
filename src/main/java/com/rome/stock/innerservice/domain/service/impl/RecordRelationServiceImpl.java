/**
 * Filename RecordRelationServiceImpl.java
 * Company 上海来伊份科技有限公司。
 *
 * <AUTHOR>
 * @version
 */
package com.rome.stock.innerservice.domain.service.impl;

import com.rome.arch.core.exception.RomeException;
import com.rome.arch.util.dynamic.DynamicDataSourceContextHolder;
import com.rome.arch.util.dynamic.DynamicDataSourceEnum;
import com.rome.stock.common.enums.RecordRelationSourceTypeEnum;
import com.rome.stock.innerservice.common.ResCode;
import com.rome.stock.innerservice.domain.service.RecordRelationService;
import com.rome.stock.innerservice.handler.recordrelation.RecordRelationHandlerExecutor;
import com.rome.stock.innerservice.handler.recordrelation.dto.RecordRelationHandlerDTO;
import com.rome.stock.innerservice.handler.recordrelation.dto.RecordRelationHandlerExecutorDTO;
import com.rome.stock.innerservice.infrastructure.dataobject.BaseDo;
import com.rome.stock.innerservice.infrastructure.dataobject.RwStockChangeFlowDO;
import com.rome.stock.innerservice.infrastructure.dataobject.WarehouseRecordDo;
import com.rome.stock.innerservice.infrastructure.mapper.RwStockChangeFlowMapper;
import com.rome.stock.innerservice.infrastructure.mapper.WarehouseRecordMapper;
import com.rome.stock.wms.config.BaseinfoConfiguration;
import com.rome.stock.wms.config.BaseinfoProperty;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Set;

/**
 * 单据关联关系
 * <AUTHOR>
 * @since 2024/4/23 15:45
 */
@Slf4j
@Service
public class RecordRelationServiceImpl implements RecordRelationService {

    @Resource
    private WarehouseRecordMapper warehouseRecordMapper;
    @Resource
    private RwStockChangeFlowMapper rwStockChangeFlowMapper;
    @Resource
    private RecordRelationHandlerExecutor recordRelationHandlerExecutor;

    /**
     * 单据关联关系处理，定时任务
     * @param runStopTime 运行结束的指定毫秒数
     * @return
     */
    @Override
    public int recordRelationTask(long runStopTime) {
        List<BaseinfoProperty> list = BaseinfoConfiguration.getInstance().getListByNoCache("recordrelation.id");
        // 如果没有配置直接返回
        if(CollectionUtils.isEmpty(list)) {
            return 0;
        }
        int num = 0;
        try {
            //查找当前最大ID
            Long maxChangeFlowId =rwStockChangeFlowMapper.selectMaxId();
            for(BaseinfoProperty property : list) {
                if(System.currentTimeMillis() > runStopTime) {
                    throw new RomeException(ResCode.STOCK_ERROR_1003, "单据关联关系处理超过指定的时间,立即结束");
                }
                num=recordRelationByConfig(runStopTime, property,maxChangeFlowId)+num;
            }
        } catch (Exception e) {
            log.error("本次单据关联关系处理异常", e);
        }
        return num;
    }

    /**
     * 单据关联关系处理，根据单据编码列表
     *
     * @param sourceType
     * @param recordCodeList
     * @return
     */
    @Override
    public int recordRelationByRecordCodeList(Integer sourceType, List<String> recordCodeList) {
        if(sourceType == null) {
            throw new RomeException(ResCode.STOCK_ERROR_1002, ResCode.STOCK_ERROR_1002_DESC + "类型为空");
        }
        if(recordCodeList == null || recordCodeList.isEmpty()) {
            throw new RomeException(ResCode.STOCK_ERROR_1002, ResCode.STOCK_ERROR_1002_DESC + "单据列表为空");
        }
        // 来源类型-后置单sc_warehouse_record
        if(RecordRelationSourceTypeEnum.TYPE_WH_RECORD.getType().equals(sourceType)) {
            List<WarehouseRecordDo> list = warehouseRecordMapper.getRecordTypeByRecordCodes(recordCodeList);
            // 处理出错，直接报错不发mq补偿
            recordRelationByWarehouseRecord(list, false);
        }
        // 来源类型-实仓流水
        else if (RecordRelationSourceTypeEnum.TYPE_REAL_FLOW.getType().equals(sourceType)) {
            List<RwStockChangeFlowDO> list = rwStockChangeFlowMapper.queryByRecordCodeList(recordCodeList);
            // 处理出错，直接报错不发mq补偿
            recordRelationByRealFlow(list, false);
        } else {
            throw new RomeException(ResCode.STOCK_ERROR_1002, ResCode.STOCK_ERROR_1002_DESC + "不存在此来源类型操作");
        }
        return recordCodeList.size();
    }

    /**
     * 关联关系处理，根据配置
     * @param runStopTime
     * @param property
     * @return
     */
    private int recordRelationByConfig(long runStopTime, BaseinfoProperty property,Long maxChangeFlowId) {
        int num=0;
        try {
            Integer sourceType = null;
            // 不配默认为后置单
            if(property.getKey().indexOf("_") > 0) {
                sourceType = Integer.valueOf(property.getKey().substring(property.getKey().indexOf("_") + 1));
            } else {
                sourceType = RecordRelationSourceTypeEnum.TYPE_WH_RECORD.getType();
            }
            // 来源类型-后置单sc_warehouse_record
            if(RecordRelationSourceTypeEnum.TYPE_WH_RECORD.getType().equals(sourceType)) {
                num = recordRelationByConfigWhRecord(runStopTime, property);
            }
            // 来源类型-实仓流水
            else if (RecordRelationSourceTypeEnum.TYPE_REAL_FLOW.getType().equals(sourceType)) {
                num = recordRelationByConfigRealFlow(runStopTime, property,maxChangeFlowId);
            } else {
                throw new RomeException(ResCode.STOCK_ERROR_1003, "本次单据关联关系处理,单个类型,来源类型不支持,描述为" + property.getDesc());
            }
        } catch (Throwable e) {
            log.error("重要本次单据关联关系处理，根据配置处理，单据类型：{}，配置描述：{}，异常", property.getKey(), property.getDesc(), e);
        }
        return num;
    }

    /**
     * 关联关系处理，具体处理，后置单表类型
     * @param runStopTime
     * @param propertyConfig
     * @return
     */
    private int recordRelationByConfigWhRecord(long runStopTime, BaseinfoProperty propertyConfig) {
        int num = 0;
        try {
            List<WarehouseRecordDo> list = null;
            boolean runFlag = true;
            int resut;
            int indexOf = propertyConfig.getKey().indexOf("_");
            Integer recordType = indexOf>0 ? Integer.valueOf(propertyConfig.getKey().substring(0,indexOf)):Integer.valueOf(propertyConfig.getKey());
            final int pageSize = 200;
            int newNum;
            do {
                if(System.currentTimeMillis() > runStopTime) {
                    throw new RomeException(ResCode.STOCK_ERROR_1003, "单据关联关系处理，根据配置处理，后置单表,超过指定的时间,立即结束");
                }
                BaseinfoProperty property = BaseinfoConfiguration.getInstance().getObjectByNoCache(propertyConfig.getRootKey(), propertyConfig.getKey());
                DynamicDataSourceContextHolder.setDataSource(DynamicDataSourceEnum.READ.name());
                list = warehouseRecordMapper.queryWarehouseRecordByGtIdRecordType(Long.parseLong(property.getValue()), recordType, pageSize);
                DynamicDataSourceContextHolder.clearDataSource();
                // 检查数据，并去除不满足条件的数据
                validateDataRemove(list);
                if(list != null && list.size() > 0) {
                    newNum = list.size();
                    // 关联关系处理，根据后置单处理
                    recordRelationByWarehouseRecord(list, true);
                    property.setValue("" + list.get(list.size() - 1).getId());
                    resut = BaseinfoConfiguration.getInstance().updateOrInsert(property);
                    if(resut != 1) {
                        throw new RomeException(ResCode.STOCK_ERROR_1003, "单据关联关系处理，根据配置处理，后置单表,更新最大id配置失败,立即结束");
                    }
                    if(pageSize != newNum) {
                        runFlag = false;
                    }
                    num += newNum;
                    list.clear();
                    list = null;
                } else {
                    log.warn("本次单据关联关系处理，根据配置处理，后置单表，单据类型：{}，配置描述：{}，结束==========", propertyConfig.getKey(), propertyConfig.getDesc());
                    runFlag = false;
                }
            } while(runFlag);
            list = null;
        }  catch (Exception e) {
            log.error("本次单据关联关系处理，根据配置处理，后置单表，单据类型：{}，配置描述：{}，异常", propertyConfig.getKey(), propertyConfig.getDesc(), e);
        } finally {
            DynamicDataSourceContextHolder.clearDataSource();
        }
        return num;
    }

    /**
     * 关联关系处理，具体处理，流水类型
     * @param runStopTime
     * @param propertyConfig
     * @return
     */
    private int recordRelationByConfigRealFlow(long runStopTime, BaseinfoProperty propertyConfig,Long maxChangeFlowId) {
        int num = 0;
        try {
            List<RwStockChangeFlowDO> list = null;
            boolean runFlag = true;
            int resut;
            int indexOf = propertyConfig.getKey().indexOf("_");
            Integer recordType = indexOf>0 ? Integer.valueOf(propertyConfig.getKey().substring(0,indexOf)):Integer.valueOf(propertyConfig.getKey());
            final int pageSize = 200;
            int newNum;
            do {
                if(System.currentTimeMillis() > runStopTime) {
                    throw new RomeException(ResCode.STOCK_ERROR_1003, "单据关联关系处理，根据配置处理，流水表,超过指定的时间,立即结束");
                }
                BaseinfoProperty property = BaseinfoConfiguration.getInstance().getObjectByNoCache(propertyConfig.getRootKey(), propertyConfig.getKey());
                DynamicDataSourceContextHolder.setDataSource(DynamicDataSourceEnum.READ.name());
                list = rwStockChangeFlowMapper.queryFlowRecordRelationByGtIdRecordType(Long.parseLong(property.getValue()), recordType, pageSize);
                DynamicDataSourceContextHolder.clearDataSource();
                // 检查数据，并去除不满足条件的数据
                validateDataRemove(list);
                if(list != null && list.size() > 0) {
                    newNum = list.size();
                    // 关联关系处理，根据流水处理
                    recordRelationByRealFlow(list, true);
                    property.setValue("" + list.get(list.size() - 1).getId());
                    resut = BaseinfoConfiguration.getInstance().updateOrInsert(property);
                    if(resut != 1) {
                        throw new RomeException(ResCode.STOCK_ERROR_1003, "单据关联关系处理，根据配置处理，流水表,更新最大id配置失败,立即结束");
                    }
                    if(pageSize != newNum) {
                        runFlag = false;
                    }
                    num += newNum;
                    list.clear();
                    list = null;
                } else {
                    //如果查不到数据,使用最大的流水ID来更新进去
                    property.setValue(String.valueOf(maxChangeFlowId));
                    BaseinfoConfiguration.getInstance().updateOrInsert(property);
                    log.warn("本次单据关联关系处理，根据配置处理，流水表，单据类型：{}，配置描述：{}，结束==========", propertyConfig.getKey(), propertyConfig.getDesc());
                    runFlag = false;
                }
            } while(runFlag);
            list = null;
        }  catch (Exception e) {
            log.error("本次单据关联关系处理，根据配置处理，流水表，单据类型：{}，配置描述：{}，异常", propertyConfig.getKey(), propertyConfig.getDesc(), e);
        } finally {
            DynamicDataSourceContextHolder.clearDataSource();
        }
        return num;
    }

    /**
     * 关联关系处理，根据后置单处理
     * @param list
     * @param errorSendMq 是否失败出错发送Mq
     */
    private void recordRelationByWarehouseRecord(List<WarehouseRecordDo> list, boolean errorSendMq) {
        if(CollectionUtils.isEmpty(list)) {
            return;
        }
        RecordRelationHandlerExecutorDTO param = new RecordRelationHandlerExecutorDTO();
        List<RecordRelationHandlerDTO> details = new ArrayList<>(list.size());
        param.setErrorSendMq(errorSendMq);
        param.setDetails(details);
        RecordRelationHandlerDTO detail;
        for(WarehouseRecordDo dto : list) {
            detail = new RecordRelationHandlerDTO();
            detail.setRecordCode(dto.getRecordCode());
            detail.setRecordType(dto.getRecordType());
            detail.setSourceType(RecordRelationSourceTypeEnum.TYPE_WH_RECORD.getType());
            details.add(detail);
        }
        recordRelationHandlerExecutor.executor(param);
    }

    /**
     * 关联关系处理，根据流水处理
     * @param list
     * @param errorSendMq 是否失败出错发送Mq
     */
    private void recordRelationByRealFlow(List<RwStockChangeFlowDO> list, boolean errorSendMq) {
        if(CollectionUtils.isEmpty(list)) {
            return;
        }
        RecordRelationHandlerExecutorDTO param = new RecordRelationHandlerExecutorDTO();
        List<RecordRelationHandlerDTO> details = new ArrayList<>();
        param.setErrorSendMq(errorSendMq);
        param.setDetails(details);
        RecordRelationHandlerDTO detail;
        // 根据单据编码去一下重
        Set<String> set = new HashSet<>();
        for(RwStockChangeFlowDO dto : list) {
            if(set.contains(dto.getRecordCode())) {
                continue;
            }
            set.add(dto.getRecordCode());
            detail = new RecordRelationHandlerDTO();
            detail.setRecordCode(dto.getRecordCode());
            detail.setRecordType(dto.getTransType());
            detail.setSourceType(RecordRelationSourceTypeEnum.TYPE_REAL_FLOW.getType());
            details.add(detail);
        }
        recordRelationHandlerExecutor.executor(param);
    }

    /**
     * 检查数据，并去除不合法的数据
     * 主要检测有，不得早于2分钟
     * @param list 数据 id是按升序
     */
    private void validateDataRemove(List<? extends BaseDo> list) {
        if(CollectionUtils.isEmpty(list)) {
            return;
        }
        long curTime = System.currentTimeMillis();
        // 结束标识
        boolean flag = true;
        // 不得早于时间 2分钟
		long maxTime = curTime - 120000;
        BaseDo dto;
        for(int i = 0; i < list.size(); i++) {
            if(flag == false) {
                list.remove(i);
                i--;
                continue;
            }
            dto = list.get(i);
			if(dto.getCreateTime().getTime() > maxTime) {
				flag = false;
                list.remove(i);
                i--;
			}
        }
    }


}
