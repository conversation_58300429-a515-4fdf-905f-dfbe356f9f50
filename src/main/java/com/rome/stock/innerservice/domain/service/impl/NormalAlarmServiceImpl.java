package com.rome.stock.innerservice.domain.service.impl;

import com.rome.scm.common.monitor.CustomMonitorFacade;
import com.rome.scm.common.monitor.CustomMonitorTypeEnum;
import com.rome.stock.innerservice.api.dto.RealWarehouse;
import com.rome.stock.innerservice.api.dto.alarm.ShopCloseAlarmDTO;
import com.rome.stock.innerservice.common.RomeCollectionUtil;
import com.rome.stock.innerservice.common.StringUtils;
import com.rome.stock.innerservice.constant.RealWarehouseTypeVO;
import com.rome.stock.innerservice.domain.entity.RealWarehouseE;
import com.rome.stock.innerservice.domain.repository.RealWarehouseRepository;
import com.rome.stock.innerservice.domain.repository.warehouserecord.WarehouseRecordRepository;
import com.rome.stock.innerservice.domain.service.NormalAlarmService;
import com.rome.stock.innerservice.infrastructure.mapper.RealWarehouseStockMapper;
import com.rome.stock.innerservice.remote.base.dto.StoreMdmAttributeExtDTO;
import com.rome.stock.innerservice.remote.base.facade.BaseDataFacade;
import com.rome.stock.innerservice.remote.base.facade.ShopFacade;
import com.rome.stock.wms.config.BaseinfoConfiguration;
import com.rome.stock.wms.config.BaseinfoProperty;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;

/**
 * @Description NormalAlarmServiceImpl
 * <AUTHOR>
 * @Date 2024/7/2
 **/
@Service
@Slf4j
public class NormalAlarmServiceImpl implements NormalAlarmService {

    @Resource
    private RealWarehouseRepository realWarehouseRepository;

    @Resource
    private ShopFacade shopFacade;

    @Resource
    private WarehouseRecordRepository warehouseRecordRepository;

    @Resource
    private RealWarehouseStockMapper realWarehouseStockMapper;

    @Override
    public void shopCloseAlarm() {
        //无需查询出全部数据
        List<RealWarehouse> realWarehouseES = realWarehouseRepository.queryByTypeForFrontend(RealWarehouseTypeVO.RW_TYPE_1.getType());
        if (CollectionUtils.isEmpty(realWarehouseES)) {
            return;
        }

        Map<String, RealWarehouse> shopMap = new HashMap<>();
        Map<Long, RealWarehouse> shopIdMap = new HashMap<>();
        for (RealWarehouse realWarehouseE : realWarehouseES) {
            shopMap.put(realWarehouseE.getFactoryCode(), realWarehouseE);
            shopIdMap.put(realWarehouseE.getId(), realWarehouseE);
        }

        List<String> shopCodeList = new ArrayList<>(shopMap.keySet());
        List<StoreMdmAttributeExtDTO> storeMdmAttributeExtDTOS = shopFacade.selectMdmStoreInfoByCodeList(shopCodeList);
        List<Long> hasClosedRwIdList = new ArrayList<>();
        for (StoreMdmAttributeExtDTO storeDTO : storeMdmAttributeExtDTOS) {
            RealWarehouse realWarehouseE = shopMap.get(storeDTO.getStoreCode());
            if (Objects.equals(storeDTO.getDesc(),"拆店") && realWarehouseE != null ) {
                hasClosedRwIdList.add(realWarehouseE.getId());
            }
        }
        if (CollectionUtils.isEmpty(hasClosedRwIdList)) {
            return;
        }
        String valueDayCount = BaseinfoConfiguration.getInstance().get("shopCloseAlarmRootKey ", "dayCount");
        Integer dayCount = 10;
        if (StringUtils.isNotBlank(valueDayCount)) {
            dayCount = Integer.valueOf(valueDayCount);
        }
        List<ShopCloseAlarmDTO> alarmDTOS = new ArrayList<>();
        //首先查询是否存在在途
        List<List<Long>> lists = RomeCollectionUtil.splitList(hasClosedRwIdList, 100);
        for (List<Long> itemRwList : lists) {
            List<ShopCloseAlarmDTO> recordList =  warehouseRecordRepository.queryGroupInitCountByRwIdList(itemRwList);
            List<ShopCloseAlarmDTO> stockList = realWarehouseStockMapper.queryGroupCountByRwIdList(itemRwList);
            if (CollectionUtils.isEmpty(recordList) && CollectionUtils.isEmpty(stockList)) {
                continue;
            }
            Map<Long, ShopCloseAlarmDTO> recordCountMap = RomeCollectionUtil.listforMap(recordList, "realWarehouseId");
            Map<Long, ShopCloseAlarmDTO> stockMap = RomeCollectionUtil.listforMap(stockList, "realWarehouseId");
            for (Long rwId : itemRwList) {
                if (!recordCountMap.containsKey(rwId) && !stockMap.containsKey(rwId)) {
                    continue;
                }
                RealWarehouse realWarehouseE = shopIdMap.get(rwId);
                ShopCloseAlarmDTO stockDTO = stockMap.get(rwId);
                ShopCloseAlarmDTO recordCountDTO = recordCountMap.get(rwId);
                if (stockDTO != null) {
                    //达到预警天数，则预警
                    if (recordCountDTO != null) {
                        stockDTO.setSumRecordCount(recordCountDTO.getSumRecordCount());
                    } else {
                        stockDTO.setSumRecordCount(0);
                    }
                    stockDTO.setShopCode(realWarehouseE.getFactoryCode());
                    if (dayCount <= stockDTO.getDayCount() && stockDTO.getDayCount() <=730 ) {
                        alarmDTOS.add(stockDTO);
                    }
                } else if (recordCountDTO != null) {
                    //库存数据不存在或者已经清0，但是存在在途，也需要预警
                    recordCountDTO.setShopCode(realWarehouseE.getFactoryCode());
                    alarmDTOS.add(recordCountDTO);
                }
            }
        }
        if (CollectionUtils.isEmpty(alarmDTOS)) {
            return;
        }
        //每10条发送一次消息
        List<List<ShopCloseAlarmDTO>> alarmListList = RomeCollectionUtil.splitList(alarmDTOS, 10);
        StringBuffer stringBuffer = new StringBuffer();
        for (List<ShopCloseAlarmDTO> alarmList : alarmListList) {
            for (ShopCloseAlarmDTO closeAlarmDTO : alarmList) {
                stringBuffer.append(closeAlarmDTO.getShopCode()).append("门店");
                if (closeAlarmDTO.getDayCount() != null) {
                    stringBuffer.append("拆店超过").append(closeAlarmDTO.getDayCount())
                            .append("天，库存未清零，未清零物料为").append(closeAlarmDTO.getSkuCount())
                            .append("个，库存合计").append(closeAlarmDTO.getSumStockQty()).append("(基本单位)，未清在途单据").append(closeAlarmDTO.getSumRecordCount())
                            .append("单，请及时处理。\n");
                } else {
                    //库存记录不存在，但是存在在途单据
                    stringBuffer.append("已拆店，库存已清零；存在未清在途单据").append(closeAlarmDTO.getSumRecordCount()).append("单，请及时处理。\n");
                }
            }
            CustomMonitorFacade.callMonitor(stringBuffer.toString(), "已拆店门店，库存存在结余", CustomMonitorTypeEnum.TYPE_EXCEPTION, "闭店门店库存未清零预警", "stock-inner-service");
            stringBuffer.setLength(0);
        }
    }
}
