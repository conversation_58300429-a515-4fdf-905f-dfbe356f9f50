package com.rome.stock.innerservice.domain.entity.warehouserecord;

import com.rome.arch.core.domain.EntityFactory;
import com.rome.stock.common.enums.warehouse.WarehouseRecordTypeVO;
import com.rome.stock.innerservice.constant.WarehouseRecordBatchStatusVO;
import com.rome.stock.innerservice.constant.WarehouseRecordBusinessTypeVO;
import com.rome.stock.innerservice.constant.WmsSyncStatusVO;
import com.rome.stock.innerservice.domain.entity.frontrecord.OnlineRetailE;
import com.rome.stock.innerservice.domain.entity.frontrecord.OnlineRetailRecordDetailE;
import com.rome.stock.innerservice.domain.repository.warehouserecord.SaleWarehouseRepository;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * 电商零售出入库单
 *
 * <AUTHOR> 2019/4/17 20:52
 */
@Data
@Slf4j
@Component
@Scope("prototype")
@EqualsAndHashCode(callSuper = false)
public class VirtualSkuWarehouseRecordE extends AbstractWarehouseRecord{
    @Resource
    private EntityFactory entityFactory;
    @Autowired
    private SaleWarehouseRepository saleWarehouseRepository;

    /**
     * 创建仓库单据
     */
    public void addWarehouseRecord() {
        if(StringUtils.isBlank(this.getMobile())) {
            this.setMobile("");
        }
        if(StringUtils.isBlank(this.getUserCode())) {
            this.setUserCode("");
        }
        long id=saleWarehouseRepository.saveWarehouseRecord(this);
        this.setId(id);
        this.warehouseRecordDetails.forEach(record -> record.setWarehouseRecordDetail(this));
        saleWarehouseRepository.saveWarehouseRecordDetails(this.warehouseRecordDetails);
        //存储前置单与仓库单关系
        this.addFrontRecordAndWarehouseRelation();
    }

    /**
     * 根据前置单生成出库单据
     */
    public void createOutRecordByFrontRecord(OnlineRetailE onlineRetailE){
        createRecodeCode(WarehouseRecordTypeVO.SHOP_RETAIL_WAREHOUSE_RECORD.getCode());
        this.setAbstractFrontRecord(onlineRetailE);
        this.setRealWarehouseId(onlineRetailE.getRealWarehouseId());
        this.setBusinessType(WarehouseRecordBusinessTypeVO.OUT_WAREHOUSE_RECORD.getType());
        this.setRecordType(WarehouseRecordTypeVO.ONLINE_RETAILERS_OUT_RECORD.getType());
        this.setChannelCode(onlineRetailE.getChannelCode());
        this.setMerchantId(onlineRetailE.getMerchantId());
        this.setOutCreateTime(onlineRetailE.getOutCreateTime());
        this.setUserCode(onlineRetailE.getUserCode()==null?"":onlineRetailE.getUserCode());
        this.setMobile(onlineRetailE.getMobile()==null?"":onlineRetailE.getMobile());
        List<OnlineRetailRecordDetailE> frontRecordDetails=onlineRetailE.getFrontRecordDetails();
        this.setWarehouseRecordDetails(new ArrayList<>(frontRecordDetails.size()));
        this.setSyncWmsStatus(WmsSyncStatusVO.NO_REQUIRED.getStatus());
        this.setBatchStatus(WarehouseRecordBatchStatusVO.NO_HANDLING.getStatus());
        if(frontRecordDetails!=null){
            for(OnlineRetailRecordDetailE detailE:frontRecordDetails){
                WarehouseRecordDetail warehouseRecordDetail=entityFactory.createEntity(WarehouseRecordDetail.class);
                warehouseRecordDetail.createRecordDetailByFrontRecord(detailE);
                warehouseRecordDetail.setActualQty(detailE.getBasicSkuQty());
                this.warehouseRecordDetails.add(warehouseRecordDetail);
            }
            //设置skuCode和skuID
            this.initWarehouseRecodeDetail();
        }
    }

    /**
     * 用户code
     */
    private String userCode;

    /**
     * 用户手机号
     */
    private String mobile;

    /**
     * 外部系统数据创建时间:下单时间
     */
    private Date outCreateTime;

}
