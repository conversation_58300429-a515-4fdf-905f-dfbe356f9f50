package com.rome.stock.innerservice.domain.service.impl;

import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.Random;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import com.alibaba.fastjson.JSONObject;
import com.rome.stock.innerservice.common.Snowflake;
import com.rome.stock.innerservice.domain.service.OrderUtilService;
import com.rome.stock.leaf.LeafClient;
import com.rome.stock.leaf.exception.LeafServerException;
import lombok.extern.slf4j.Slf4j;

/**
 * OrderUtilServiceImpl类的实现描述：订单工具类
 *
 * <AUTHOR> 2019/7/7 16:02
 */
@Slf4j
@Service("orderUtilService")
public class OrderUtilServiceImpl implements OrderUtilService {

    @Value("${genkey.seed}")
    private  int seed;

    //门店调拨的单号最长16位，数字15位
    private final static int SOCODELEN = 15;

    
    /**
     * 区间块的值(每次自增), 通过spring注入
     */
    @Value("${genkey.sectionNumNew}")
    private Long sectionNumNew ;
    
    /**
     * 单号生成用leaf方式
     */
    @Value("${genkey.leafType:false}")
    private boolean leafType ;

    @Override
    public String queryOrderCode(String prefix) {
    	// 如果是 leaf方式生成
    	if(leafType) {
    		try {
    			String orderNo = prefix.concat(LeafClient.getOrderNOByDefault(prefix, "other"));
        		JSONObject jsonObject=new JSONObject();
        		jsonObject.put("logType", "rome-stock-leaforderno"); // 日志类型
        		jsonObject.put("orderNo", orderNo); // 单号
        		log.warn(jsonObject.toJSONString());
        		return orderNo;
			} catch (LeafServerException e) {
				JSONObject jsonObject=new JSONObject();
        		jsonObject.put("logType", "rome-stock-leafordernoerror"); // 日志类型
        		jsonObject.put("msg", e.getMessage()); // 错误信息
        		log.warn(jsonObject.toJSONString());
				throw e;
			}
    		
    	}
        if ("S".equals(prefix)) {
            //门店调拨前置单的单号用老的算法【由于门店调拨前置单的单号不能太长】
            SimpleDateFormat sdf = new SimpleDateFormat("yyMMddHHmmss");
            String soCode = sdf.format(new Date());
            int idLen = StringUtils.length(soCode);
            int left = SOCODELEN - 6;
            if (idLen < left) {
                for (int i = 0; i < left - idLen; i++) {
                    soCode = "0".concat(soCode);
                }
            }
            soCode = prefix + soCode;
            //增加100-1000内的随机数
            Random rd=new Random();
            int ram = rd.nextInt(900) + 100;
            soCode = soCode  + String.valueOf(ram);
            return soCode;
        } else {
            return prefix + Snowflake.getInstanceSnowflake().nextId();
        }
    }
    
}
