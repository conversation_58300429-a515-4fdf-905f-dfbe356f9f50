package com.rome.stock.innerservice.domain.entity;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

import com.alibaba.fastjson.JSON;
import com.rome.stock.wms.config.BaseinfoConfiguration;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import org.springframework.util.DigestUtils;

import com.rome.arch.core.domain.EntityFactory;
import com.rome.stock.innerservice.common.AlikAssert;
import com.rome.stock.innerservice.common.ResCode;
import com.rome.stock.core.constant.StockCoreConsts;
import com.rome.stock.innerservice.domain.entity.frontrecord.AbstractFrontRecord;
import com.rome.stock.innerservice.domain.entity.frontrecord.OnlineRetailE;
import com.rome.stock.innerservice.domain.entity.frontrecord.OnlineRetailRecordDetailE;
import com.rome.stock.innerservice.domain.entity.frontrecord.WDTOnlineRetailE;
import com.rome.stock.innerservice.domain.entity.frontrecord.WDTOnlineRetailRecordDetailE;
import com.rome.stock.innerservice.domain.repository.AddressRepository;
import com.rome.stock.innerservice.domain.repository.RwRecordPoolRepository;
import com.rome.stock.innerservice.domain.repository.frontrecord.FrSaleRepository;
import com.rome.stock.innerservice.domain.repository.frontrecord.FrWDTSaleRepository;
import com.rome.stock.core.infrastructure.dataobject.core.CoreVirtualStockOpDO;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.extern.slf4j.Slf4j;

import static java.math.BigDecimal.ROUND_DOWN;

/**
 * 类RwRecordPool的实现描述：单据池领域
 *
 * <AUTHOR> 2019/4/17 21:48
 */
@Data
@Slf4j
@Component
@Scope("prototype")
@EqualsAndHashCode(callSuper = false)
public class RwRecordPoolE extends AbstractFrontRecord {
    @Autowired
    private RwRecordPoolRepository rwRecordPoolRepository;
    @Autowired
    private FrSaleRepository frSaleRepository;
    @Autowired
    private FrWDTSaleRepository frWDTSaleRepository;
    @Autowired
    private AddressRepository addressRepository;
    @Autowired
    private EntityFactory entityFactory;

    /**
     * 为了统一处理，根据pool表找前置单
     * @return
     */
    public OnlineRetailE queryFrontRecord(){
        OnlineRetailE onlineRetailE = null ;
        //APP拆单后-统一查WDT表
        WDTOnlineRetailE wdtOnlineRetailE = frWDTSaleRepository.queryByRecordCode(this.getFrontRecordCode());
        if (null != wdtOnlineRetailE) {
            onlineRetailE = wdtOnlineRetailE.convertToNormalOnlineRetailE();
            onlineRetailE.setOriginOrderCode(wdtOnlineRetailE.getOriginOrderCode());
        }else{
            //查不到的查sale表，表示为老单据
            onlineRetailE = frSaleRepository.queryOnlineRetailByRecordCode(this.getFrontRecordCode());
            onlineRetailE.setOriginOrderCode(onlineRetailE.getOutRecordCode());
        }
        return onlineRetailE;
    }

    /**
     * 计算MD5指纹信息
     * @param addressE
     * @return
     */
    public String calcFingerprint(AddressE addressE ) {
        StringBuilder sb = new StringBuilder();
        List<String> shopCodeArr = JSON.parseArray(BaseinfoConfiguration.getInstance().get("calcFingerprint", "shopCodeArr"), String.class);
        String shopCode = this.channelCode.split("_")[0];
        if (null != addressE) {
            String name;
            String mobile;
            String address;
            if (!CollectionUtils.isEmpty(shopCodeArr) && shopCodeArr.contains(shopCode)) {
                name = extractIndex(addressE.getName());
                mobile = extractIndex(addressE.getMobile());
                address = extractIndex(addressE.getAddress());
            } else {
                name = addressE.getName();
                mobile = addressE.getMobile();
                address = addressE.getAddress();
            }
            sb.append(null == name ? "" : name);
            sb.append(null == mobile ? "" : mobile);
            sb.append(null == address ? "" : address);
            //把oaid放入加密信息中
            sb.append(null == addressE.getRemark() ? "" : addressE.getRemark());
        }
        sb.append(null == channelCode ? "" : channelCode);
        sb.append(null == realWarehouseId ? "" : realWarehouseId);
        sb.append(null == logisticsCode ? "" : logisticsCode);
        //用户编号
        sb.append(null == userCode ? "" : userCode);
        return DigestUtils.md5DigestAsHex(sb.toString().getBytes());
    }

    /**
     * 获取加密信息中的索引信息(抖音与拼多多)
     * https://open.pinduoduo.com/application/faq?id=D18C2DF89E718FA4
     * https://op.jinritemai.com/docs/guide-docs/118/594
     * @param encryptedData
     * @return
     */
    public String extractIndex(String encryptedData) {
        if (encryptedData == null || encryptedData.length() < 4) {
            //长度不符合，直接返回原数据
            return encryptedData;
        }
        char sepInData = encryptedData.charAt(0);
        if (encryptedData.charAt(encryptedData.length() - 2) != sepInData) {
            //格式不符合，也直接返回原数据
            return encryptedData;
        }
        String[] parts = StringUtils.split(encryptedData, sepInData);
        if (sepInData == '$' || sepInData == '#') {
            return parts[0];
        } else if (parts.length <= 1) {
            //兼容异常格式
            return encryptedData;
        } else {
            return parts[1];
        }
    }



    /**
     * 设置MD5指纹信息
     */
    public void bindMergeFingerprint(AddressE addressE) {
        this.mergeFingerprint = calcFingerprint(addressE);
    }

    public int updateMergeFingerprint() {
        return rwRecordPoolRepository.updateMergeFingerprint(this.id, this.mergeFingerprint);
    }

    /**
     * 包装Do池sku明细
     */
    public void  warpRwRecordPoolDetail(List<CoreVirtualStockOpDO> cvsList, Map<Long, OnlineRetailRecordDetailE> skuIdUnitCodeMap) {
        this.setRwRecordPoolDetails(new ArrayList<>());
        CoreVirtualStockOpDO master = cvsList.get(0);
        RwRecordPoolDetailE poolDetailE;
        for (CoreVirtualStockOpDO cvs : cvsList) {
            OnlineRetailRecordDetailE frontDetailE = skuIdUnitCodeMap.get(cvs.getSkuId());
            AlikAssert.isNotNull(frontDetailE, ResCode.STOCK_ERROR_5024, ResCode.STOCK_ERROR_5024_DESC);
            poolDetailE = entityFactory.createEntity(RwRecordPoolDetailE.class);
            poolDetailE.setSkuId(cvs.getSkuId());
            poolDetailE.setSkuCode(cvs.getSkuCode());
            //基本单位转为销售单位
            poolDetailE.setSkuQty(cvs.getLockQty().divide(frontDetailE.getScale(), StockCoreConsts.DECIMAL_POINT_NUM, ROUND_DOWN));
            poolDetailE.setUnit(frontDetailE.getUnit());
            poolDetailE.setUnitCode(frontDetailE.getUnitCode());
            poolDetailE.setBasicSkuQty(cvs.getLockQty());
            poolDetailE.setBasicUnit(frontDetailE.getBasicUnit());
            poolDetailE.setBasicUnitCode(frontDetailE.getBasicUnitCode());
            poolDetailE.setRealWarehouseId(master.getRealWarehouseId());
            poolDetailE.setVirtualWarehouseId(master.getVirtualWarehouseId());
            poolDetailE.setRwRecordPoolDetail(this);
            this.getRwRecordPoolDetails().add(poolDetailE);
        }
    }
    /**
     * 包装Do池sku明细
     */
    public void warpRwRecordPoolDetail(List<WDTOnlineRetailRecordDetailE> detailES) {
        this.setRwRecordPoolDetails(new ArrayList<>());
        RwRecordPoolDetailE poolDetailE;
        for(WDTOnlineRetailRecordDetailE detailE :detailES){
            poolDetailE = entityFactory.createEntity(RwRecordPoolDetailE.class);
            poolDetailE.setLineNo(detailE.getLineNo());
            poolDetailE.setSkuId(detailE.getSkuId());
            poolDetailE.setSkuCode(detailE.getSkuCode());
            //基本单位转为销售单位
            poolDetailE.setSkuQty(detailE.getSkuQty());
            poolDetailE.setUnit(detailE.getUnit());
            poolDetailE.setUnitCode(detailE.getUnitCode());
            poolDetailE.setBasicSkuQty(detailE.getBasicSkuQty());
            poolDetailE.setBasicUnit(detailE.getBasicUnit());
            poolDetailE.setBasicUnitCode(detailE.getBasicUnitCode());
            poolDetailE.setRealWarehouseId(this.getRealWarehouseId());
            poolDetailE.setVirtualWarehouseId(this.getVirtualWarehouseId());
            poolDetailE.setRwRecordPoolDetail(this);
            this.getRwRecordPoolDetails().add(poolDetailE);
        }
    }

    /**
     * 更新DO单池状态为待合单 修改物流信息和仓库信息
     * @return
     */
    public int updateRwInfoAndLogisticInfo() {
        AddressE addressE = addressRepository.queryByRecordCode(this.getDoCode());
        this.setLogisticsCode(logisticsCode);
        //计算指纹信息
        this.bindMergeFingerprint(addressE);
        //更新仓库物流及指纹信息
        int i = rwRecordPoolRepository.updateRwInfoAndLogisticInfo(this);
        //修改明细维度的仓库明细
        for (RwRecordPoolDetailE detail : this.getRwRecordPoolDetails()) {
            detail.setRealWarehouseId(this.getRealWarehouseId());
            detail.setVirtualWarehouseId(this.getVirtualWarehouseId());
        }
        //根据前置单ID批量修改明细的实仓和虚仓
        rwRecordPoolRepository.updateDetailRwInfo(this.getId(), this.getRealWarehouseId(), this.getVirtualWarehouseId());
        return i;
    }

    /**
     * 主键
     */
    private Long id;

    private String doCode;
    /**
     * 前置单据编号
     */
    private String frontRecordCode;
    /**
     * 仓库（wms合单后）的单据编号
     */
    private String warehouseRecordCode;
    /**
     * 前置单据id
     */
    private Long frontRecordId;
    /**
     * 仓库（wms合单后）的单据id
     */
    private Long warehouseRecordId;
    /**
     * 渠道编码
     */
    private String channelCode;
    /**
     * 实仓ID
     */
    private Long realWarehouseId;
    /**
     * 虚仓ID
     */
    private Long virtualWarehouseId;
    /**
     *  单据类型
     */
    private Integer recordType;
    /**
     * 单据状态
     */
    private Integer recordStatus;
    /**
     * 单据池详情
     */
    private List<RwRecordPoolDetailE> rwRecordPoolDetails;
    /**
     * 用于合并的MD5指纹信息
     */
    private String mergeFingerprint;
    /**
     * 是否需要合单
     */
    private Integer needCombine;

    /**
     * 物流公司编码
     */
    private String logisticsCode;

    /**
     * 用户编号
     * @return
     */
    private String userCode;

    public boolean getNeedCombineFlag() {
        return (1 == needCombine);
    }

    private Integer syncStatus;
}
