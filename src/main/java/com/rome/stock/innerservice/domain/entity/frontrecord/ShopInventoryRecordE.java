package com.rome.stock.innerservice.domain.entity.frontrecord;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Component;

import com.rome.arch.core.domain.EntityFactory;
import com.rome.arch.core.exception.RomeException;
import com.rome.stock.innerservice.common.SkuInfoTools;
import com.rome.stock.innerservice.common.SkuQtyUnitTools;
import com.rome.stock.innerservice.constant.FrontRecordTypeVO;
import com.rome.stock.innerservice.constant.WarehouseRecordStatusVO;
import com.rome.stock.innerservice.domain.entity.RealWarehouseStockE;
import com.rome.stock.innerservice.domain.entity.warehouserecord.ShopInventoryWarehouseRecordE;
import com.rome.stock.core.domain.repository.CoreRealWarehouseStockRepository;
import com.rome.stock.innerservice.domain.repository.RealWarehouseRepository;
import com.rome.stock.innerservice.domain.repository.frontrecord.FrShopInventoryRepository;
import com.rome.stock.innerservice.domain.service.OrderUtilService;
import com.rome.stock.core.facade.RedisRollBackFacade;
import com.rome.stock.core.infrastructure.dataobject.core.CoreRealStockOpDO;
import com.rome.stock.core.infrastructure.dataobject.core.CoreRealStockOpDetailDO;

import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

/**
 * 门店盘点单
 *
 * <AUTHOR>
 */
@Component
@Scope("prototype")
@Data
@EqualsAndHashCode(callSuper = false)
@Slf4j
public class ShopInventoryRecordE extends AbstractFrontRecord {
    @Resource
    private FrShopInventoryRepository frShopInventoryRepository;

    @Resource
    private SkuQtyUnitTools skuQtyUnitTools;
    @Resource
    private SkuInfoTools skuInfoTools;
    @Autowired
    private RealWarehouseRepository realWarehouseRepository;
    @Autowired
    private EntityFactory entityFactory;
    @Resource
    private CoreRealWarehouseStockRepository coreRealWarehouseStockRepository;
    @Resource
    private OrderUtilService orderUtilService;

    /**
     * 全盘
     */
    @Transactional(propagation = Propagation.REQUIRES_NEW,rollbackFor = Exception.class)
    public void allInventory() throws Exception {
        List<ShopInventoryRecordDetailE> outFrontRecordDetails = new ArrayList<>();
        List<ShopInventoryRecordDetailE> inFrontRecordDetails = new ArrayList<>();
        List<CoreRealStockOpDetailDO> increaseDetails = new ArrayList<>();
        List<CoreRealStockOpDetailDO> decreaseDetails = new ArrayList<>();
        List<RealWarehouseStockE> stockList = realWarehouseRepository.queryAllWarehouseStockById(this.realWarehouseId);
        List<Long> skuIds = this.frontRecordDetails.stream().map(ShopInventoryRecordDetailE::getSkuId).collect(Collectors.toList());
        //库存数量不在全盘中置为0
        stockList.stream().filter(stock -> !skuIds.contains(stock.getSkuId())).forEach(stock -> {
            CoreRealStockOpDetailDO coreRealStockOpDetailDO = new CoreRealStockOpDetailDO();
            ShopInventoryRecordDetailE detailE = entityFactory.createEntity(ShopInventoryRecordDetailE.class);
            detailE.setSkuCode(stock.getSkuCode() == null ? "" : stock.getSkuCode());
            detailE.setSkuId(stock.getSkuId());
            detailE.setSkuQty(stock.getRealQty().abs());
            coreRealStockOpDetailDO.setSkuCode(stock.getSkuCode() == null ? "" : stock.getSkuCode());
            coreRealStockOpDetailDO.setSkuId(stock.getSkuId());
            coreRealStockOpDetailDO.setRealQty(stock.getRealQty().abs());
            coreRealStockOpDetailDO.setRealWarehouseId(stock.getRealWarehouseId());
            if (stock.getRealQty().compareTo(new BigDecimal(0.0)) == 1) {
                outFrontRecordDetails.add(detailE);
                decreaseDetails.add(coreRealStockOpDetailDO);
                stock.setRealQty(new BigDecimal(0.0));
            }
            if (stock.getRealQty().compareTo(new BigDecimal(0.0)) == -1) {
                inFrontRecordDetails.add(detailE);
                increaseDetails.add(coreRealStockOpDetailDO);
                stock.setRealQty(new BigDecimal(0.0));
            }
        });

        //计算库存差异
        gainDifferStockQty(this.getBusinessType(), this.getRealWarehouseId(), stockList, outFrontRecordDetails, inFrontRecordDetails, increaseDetails, decreaseDetails);
        //生成单据并扣减库存
        this.createWarehouseRecord(outFrontRecordDetails, inFrontRecordDetails, increaseDetails, decreaseDetails);
    }

    /**
     * 账面库存盘点
     */
    @Transactional(propagation = Propagation.REQUIRES_NEW,rollbackFor = Exception.class)
    public void accountInventory() throws Exception {
        List<ShopInventoryRecordDetailE> outFrontRecordDetails = new ArrayList<>();
        List<ShopInventoryRecordDetailE> inFrontRecordDetails = new ArrayList<>();
        List<CoreRealStockOpDetailDO> increaseDetails = new ArrayList<>();
        List<CoreRealStockOpDetailDO> decreaseDetails = new ArrayList<>();
        this.frontRecordDetails.forEach(detail -> detail.setSkuQty(detail.getAccQty()));
        //查询基础单位，单位转换
        skuQtyUnitTools.queryBasicUnit(this.frontRecordDetails);
        List<Long> skuIds = this.frontRecordDetails.stream().map(ShopInventoryRecordDetailE :: getSkuId).collect(Collectors.toList());
        List<RealWarehouseStockE> stockList = realWarehouseRepository.queryWarehouseStockById(this.realWarehouseId,skuIds);
        //计算库存差异
        this.gainDifferStockQty(this.getBusinessType(), this.getRealWarehouseId(), stockList, outFrontRecordDetails, inFrontRecordDetails, increaseDetails, decreaseDetails);
        //生成单据并扣减库存
        this.createWarehouseRecord(outFrontRecordDetails, inFrontRecordDetails, increaseDetails, decreaseDetails);
    }

    /**
     * 盘点库存差异
     */
    private void gainDifferStockQty(Integer type, Long realWarehouseId, List<RealWarehouseStockE> stockList, List<ShopInventoryRecordDetailE> outFrontRecordDetails, List<ShopInventoryRecordDetailE> inFrontRecordDetails,
                                    List<CoreRealStockOpDetailDO> increaseDetails, List<CoreRealStockOpDetailDO> decreaseDetails){
        for (ShopInventoryRecordDetailE shopInventoryRecordDetailE : this.frontRecordDetails) {
            BigDecimal differenceValue;
            RealWarehouseStockE stockE = stockList.stream().filter(stock -> stock.getSkuId().equals(shopInventoryRecordDetailE.getSkuId())).findFirst().orElse(null);
            if (stockE == null) {
                //门店仓没有库存，盘新库存
                differenceValue = type == 9 ? shopInventoryRecordDetailE.getAccQty() : shopInventoryRecordDetailE.getSkuQty();
            }else if(type == 9){
                //账面库存盘点，账面库存计算盘差
                differenceValue = shopInventoryRecordDetailE.getAccQty().subtract(stockE.getRealQty());
            }else{
                //全盘盘点，实盘数量计算盘差
                differenceValue = shopInventoryRecordDetailE.getSkuQty().subtract(stockE.getRealQty());
            }
            CoreRealStockOpDetailDO coreRealStockOpDetailDO = new CoreRealStockOpDetailDO();
            ShopInventoryRecordDetailE detailE = entityFactory.createEntity(ShopInventoryRecordDetailE.class);
            //库存大于0，增加库存
            if (differenceValue.compareTo(new BigDecimal(0.0)) == 1) {
                detailE.setSkuId(shopInventoryRecordDetailE.getSkuId());
                detailE.setSkuCode(shopInventoryRecordDetailE.getSkuCode() == null ? "" : shopInventoryRecordDetailE.getSkuCode());
                detailE.setSkuQty(differenceValue);
                inFrontRecordDetails.add(detailE);
                coreRealStockOpDetailDO.setSkuId(shopInventoryRecordDetailE.getSkuId());
                coreRealStockOpDetailDO.setSkuCode(shopInventoryRecordDetailE.getSkuCode());
                coreRealStockOpDetailDO.setRealQty(differenceValue);
                coreRealStockOpDetailDO.setRealWarehouseId(realWarehouseId);
                increaseDetails.add(coreRealStockOpDetailDO);
            }
            //库存小于0，减少库存
            if (differenceValue.compareTo(new BigDecimal(0.0)) == -1) {
                detailE.setSkuId(shopInventoryRecordDetailE.getSkuId());
                detailE.setSkuCode(shopInventoryRecordDetailE.getSkuCode() == null ? "" : shopInventoryRecordDetailE.getSkuCode());
                detailE.setSkuQty(differenceValue.abs());
                outFrontRecordDetails.add(detailE);
                coreRealStockOpDetailDO.setSkuId(shopInventoryRecordDetailE.getSkuId());
                coreRealStockOpDetailDO.setSkuCode(shopInventoryRecordDetailE.getSkuCode());
                coreRealStockOpDetailDO.setRealQty(differenceValue.abs());
                coreRealStockOpDetailDO.setRealWarehouseId(realWarehouseId);
                coreRealStockOpDetailDO.setCheckBeforeOp(false);
                decreaseDetails.add(coreRealStockOpDetailDO);
            }
        }
        if(outFrontRecordDetails.size() > 0){
            //查询基础单位
            skuQtyUnitTools.queryBasicUnitNoChecked(outFrontRecordDetails);
        }

        if(inFrontRecordDetails.size() > 0){
            //查询基础单位
            skuQtyUnitTools.queryBasicUnitNoChecked(inFrontRecordDetails);
        }
    }

    /**
     * 生成出入库单并扣减库存
     */
    private void createWarehouseRecord(List<ShopInventoryRecordDetailE> outFrontRecordDetails, List<ShopInventoryRecordDetailE> inFrontRecordDetails,
                                       List<CoreRealStockOpDetailDO> increaseDetails, List<CoreRealStockOpDetailDO> decreaseDetails) throws Exception {
        boolean isSuccess = false;
        CoreRealStockOpDO coreRecordRealStockIncreaseDO = null;
        CoreRealStockOpDO coreRecordRealStockDecreaseDO = null;
        try {
            //实盘数量与门店实仓数量比较，有差异产生出入库单
            if (outFrontRecordDetails != null && outFrontRecordDetails.size() > 0) {
                //基础单位赋值
                this.unitConvertBasicUnit(outFrontRecordDetails);
                this.setFrontRecordDetails(outFrontRecordDetails);
                //生成出库单
                ShopInventoryWarehouseRecordE outWarehouseRecord = entityFactory.createEntity(ShopInventoryWarehouseRecordE.class);
                //TODO
//                outWarehouseRecord.createOutRecordByFrontRecord(this);
                //状态为已出库状态
                outWarehouseRecord.setRecordStatus(WarehouseRecordStatusVO.OUT_ALLOCATION.getStatus());
                outWarehouseRecord.addOutWarehouseRecord();
                //如果产生出库单减少实仓、虚仓、策略组、渠道库存数量
                coreRecordRealStockDecreaseDO = new CoreRealStockOpDO();
                coreRecordRealStockDecreaseDO.setRecordCode(outWarehouseRecord.getRecordCode());
                coreRecordRealStockDecreaseDO.setTransType(outWarehouseRecord.getRecordType());
                coreRecordRealStockDecreaseDO.setDetailDos(decreaseDetails);
                coreRealWarehouseStockRepository.decreaseRealQty(coreRecordRealStockDecreaseDO);
                //改为job补偿机制来扣减批次库存
//                //发送批次库存MQ
//                warehouseBatchStockE.sendOutBatchStockMq(outWarehouseRecord);
            }
            if (inFrontRecordDetails != null && inFrontRecordDetails.size() > 0) {
                //基础单位赋值
                this.unitConvertBasicUnit(inFrontRecordDetails);
                this.setFrontRecordDetails(inFrontRecordDetails);
                //生成入库单
                ShopInventoryWarehouseRecordE inWarehouseRecord = entityFactory.createEntity(ShopInventoryWarehouseRecordE.class);
                //TODO
//                inWarehouseRecord.createInRecordByFrontRecord(this);
                //状态为已入库状态
                inWarehouseRecord.setRecordStatus(WarehouseRecordStatusVO.IN_ALLOCATION.getStatus());
                inWarehouseRecord.addInWarehouseRecord();
                //如果产生入库单增加实仓、虚仓、策略组、渠道库存数量
                coreRecordRealStockIncreaseDO = new CoreRealStockOpDO();
                coreRecordRealStockIncreaseDO.setRecordCode(inWarehouseRecord.getRecordCode());
                coreRecordRealStockIncreaseDO.setTransType(inWarehouseRecord.getRecordType());
                coreRecordRealStockIncreaseDO.setDetailDos(increaseDetails);
                coreRealWarehouseStockRepository.increaseRealQty(coreRecordRealStockIncreaseDO);
            }
            this.updateCompleteStatus();
            isSuccess = true;
        } catch (RomeException e) {
            log.error("门店盘点RomeException错误：",e.getMessage());
            throw new RomeException(e.getCode(), e.getMessage());
        } catch (Exception e) {
            log.error("门店盘点Exception错误：",e);
            throw new Exception();
        } finally {
            //模型成功，业务等处理失败，需要回滚
            if (isSuccess == false) {
                if (coreRecordRealStockIncreaseDO != null) {
                    RedisRollBackFacade.redisRollBack(coreRecordRealStockIncreaseDO);
                }
                if (coreRecordRealStockDecreaseDO != null) {
                    RedisRollBackFacade.redisRollBack(coreRecordRealStockDecreaseDO);
                }
            }
        }
    }

    /**
     * 基础单位赋值
     * @param details
     */
    public void unitConvertBasicUnit(List<ShopInventoryRecordDetailE> details){
        details.forEach(detail -> {
            detail.setBasicSkuQty(detail.getSkuQty());
            detail.setBasicUnit(detail.getUnit());
            detail.setBasicUnitCode(detail.getUnitCode());
        });
    }

    /**
     * 生成单号、明细单位换算
     */
    @Override
    public void initFrontRecord(String frontCode, List<? extends AbstractFrontRecordDetail> frontRecordDetails) {
        //获取单据编号
        String code = orderUtilService.queryOrderCode(frontCode);
        this.setRecordCode(code);
        //设置商品code或id
        skuInfoTools.convertSkuCodeFilter(this.frontRecordDetails);
        //查询基础单位
        skuQtyUnitTools.queryBasicUnitNoChecked(this.frontRecordDetails);
    }

    /**
     * 创建盘点单
     */
    public void addFrontRecord() {
        //设置单据类型
        this.setRecordType(FrontRecordTypeVO.SHOP_INVENTORY_RECORD.getType());
        //生成单据编号
        this.initFrontRecord(FrontRecordTypeVO.SHOP_INVENTORY_RECORD.getCode(), this.frontRecordDetails);
        if (StringUtils.isBlank(this.getRemark())) {
            this.setRemark("");
        }
        if (StringUtils.isBlank(this.getRecordStatusReason())) {
            this.setRecordStatusReason("");
        }
        if (StringUtils.isBlank(this.getShopName())) {
            this.setRecordStatusReason("");
        }
        //插入盘点单据
        long id = frShopInventoryRepository.saveShopInventoryRecord(this);
        this.setId(id);
        //盘点详情关联主数据
        this.frontRecordDetails.forEach(detail -> detail.setFrontRecordDetail(this));
        //插入盘点单据详情
        frShopInventoryRepository.saveShopInventoryRecordDetails(this.frontRecordDetails);

    }

    /**
     * 更新完成状态
     */
    public void updateCompleteStatus() {
        frShopInventoryRepository.updateCompleteStatus(this.getId());
    }

    /**
     * 渠道code
     */
    private String channelCode;

    /**
     * 渠道类型
     */
    private Long channelType;

    /**
     * 出向实体仓库id
     */
    private Long realWarehouseId;

    /**
     * 抽盘类型：1-抽盘，2-全盘
     */
    private Integer businessType;


    /**
     * 盘点备注
     */
    private String remark;

    /**
     * 门店编号
     */
    private String shopCode;

    /**
     * 门店名称
     */
    private String shopName;

    /**
     * 开始日期
     */
    private Date startDate;

    /**
     * 结束日期
     */
    private Date endDate;

    /**
     * 外部单号
     */
    private String outRecordCode;

    /**
     * 商品数量
     */
    private List<ShopInventoryRecordDetailE> frontRecordDetails;

}
