package com.rome.stock.innerservice.domain.entity;

import com.rome.stock.innerservice.domain.convertor.RwBatchConvertor;
import com.rome.stock.innerservice.domain.repository.RwBatchRepository;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.Date;

@Data
@Slf4j
@Component
@Scope("prototype")
@EqualsAndHashCode(callSuper = false)
public class RwBatchE extends SkuQtyUnitBaseE {

    @Resource
    private RwBatchRepository lotStockRepository;

    @Resource
    private RwBatchConvertor lotStockConvertor;


    /**
     * 主键
     * */
    private Long id;

    /**
     * skuID
     * */
    private Long skuId;

    /**
     * sku编码
     * */
    private String skuCode;


    /**
     * 出库单编号
     * */
    private String recordCode;

    /**
     * 批次编号
     * */
    private String batchCode;

    /**
     * 业务类型：1:出库单 2:入库单
     */
    private Integer businessType;

    /**
     * 生产日期
     * */
    private Date productDate;
    /**
     * 入库日期
     * */
    private Date entryDate;

    /**
     * 生产日期
     * */
    private Date expireDate;

    /**
     * 生产批次
     * */
    private String produceCode;

    /**
     * 库存类型
     * */
    private Integer inventoryType;

    /**
     * 商品数量
     * */
    private BigDecimal actualQty;

    /**
     * wms收货单编号
     */
    private String wmsRecordCode;

    /**
     * 质检状态 -1:无需质检 0:待质检 1:质检合格 2:质检不合格
     */
    private Integer qualityStatus;

    /**
     * 质检批号
     */
    private String qualityCode;

    /**
     * 实仓ID
     */
    private Long realWarehouseId;

    /**
     * sap仓时是po行号，其他是中台行号
     */
    private  String lineNo;

    /**
     * 质检专用字段：sap行号
     */
    private String sapLineNo;

    /**
     * 过账状态
     */
    private Integer syncTransferStatus;

    /**
     * 回调次数
     */
    private Integer callbackNum;

    /**
     * wms推送状态 -1 未推送 0--待推送 1--已推送
     */
    private Integer syncStatus;

    /**
     * 是否已操作 0--未操作 1--已操作
     */
    private Integer isOperate;

    private String uniqueKey;

    public String getUniqueKey(){
        return this.getWmsRecordCode()+"_"+this.getBatchCode()+"_"+this.getSkuCode()+"_"+this.lineNo;
    }

    /**
     * 推送质检中心状态 0--无需推送 1--待推送 2--推送成功
     */
    private Integer syncPurchaseStatus;

    /**
     * 质检回调时间
     */
    private Date qualityTime;

    /**
     * 有效期,天数
     */
    private Integer validity;

}
