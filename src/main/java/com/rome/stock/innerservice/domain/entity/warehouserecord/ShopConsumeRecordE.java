package com.rome.stock.innerservice.domain.entity.warehouserecord;

import com.rome.arch.core.domain.EntityFactory;
import com.rome.stock.common.constants.warehouse.WarehouseRecordConstant;
import com.rome.stock.common.enums.warehouse.WarehouseRecordTypeVO;
import com.rome.stock.innerservice.constant.*;
import com.rome.stock.innerservice.domain.entity.frontrecord.ShopConsumeAdjustRecordDetailE;
import com.rome.stock.innerservice.domain.entity.frontrecord.ShopConsumeAdjustRecordE;
import com.rome.stock.innerservice.domain.repository.warehouserecord.ShopConsumeRecordRepository;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * @Description 仓库损耗单
 * <AUTHOR>
 * @Date 2019/5/12 17:07
 * @Version 1.0
 */
@Data
@Slf4j
@Component
@Scope("prototype")
@EqualsAndHashCode(callSuper = false)
public class ShopConsumeRecordE extends AbstractWarehouseRecord  {

    @Resource
    private EntityFactory entityFactory;

    @Resource
    ShopConsumeRecordRepository shopConsumeRecordRepository;

    /**
     * 添加出库单
     */
    public void addRecord(){
        long id= shopConsumeRecordRepository.saveWarehouseConsumeRecord(this);
        this.setId(id);
        this.warehouseRecordDetails.forEach(record -> record.setWarehouseRecordDetail(this));
        shopConsumeRecordRepository.saveWarehouseConsumeRecordDetails(this.warehouseRecordDetails);
        this.addFrontRecordAndWarehouseRelation();
    }

    /**
     * 创建门店出库单
     * @param frontRecordE
     */
    public void createRWOutRecord(ShopConsumeAdjustRecordE frontRecordE){
        this.setRealWarehouseId(frontRecordE.getRealWarehouseId());
        //设置交易类型 1出库2入库
        this.setBusinessType(WarehouseRecordBusinessTypeVO.OUT_WAREHOUSE_RECORD.getType());

        this.createRecodeCode(WarehouseRecordTypeVO.SHOP_OUT_CONSUME_RECORD.getCode());
        this.setRecordType(WarehouseRecordTypeVO.SHOP_OUT_CONSUME_RECORD.getType());

        //设置出库单的创建时间
        this.setOutCreateTime(new Date());
        this.setRecordStatus(WarehouseRecordStatusVO.OUT_ALLOCATION.getStatus());
        this.setSyncWmsStatus(WmsSyncStatusVO.NO_REQUIRED.getStatus());
        this.setSyncTransferStatus(WarehouseRecordConstant.TRANSFER_SUCCES);
        this.setBatchStatus(WarehouseRecordBatchStatusVO.INIT.getStatus());
        this.setDeliveryTime(frontRecordE.getOutCreateTime());
        this.setAbstractFrontRecord(frontRecordE);
        List<ShopConsumeAdjustRecordDetailE> frontRecordDetails = frontRecordE.getFrontRecordDetails();
        this.setWarehouseRecordDetails(new ArrayList<>(frontRecordDetails.size()));
        if(null != frontRecordDetails){
            for(ShopConsumeAdjustRecordDetailE detailE : frontRecordDetails){
                WarehouseRecordDetail warehouseRecordDetail = entityFactory.createEntity(WarehouseRecordDetail.class);
                warehouseRecordDetail.createRecordDetailByFrontRecord(detailE);
                warehouseRecordDetail.setActualQty(detailE.getBasicSkuQty());
                this.warehouseRecordDetails.add(warehouseRecordDetail);
            }
            //设置skuCode和skuID
            this.initWarehouseRecodeDetail();
        }
    }


    //更新出库单
    public void updateRecord(WarehouseConsumeRecordE outRecord) {
        shopConsumeRecordRepository.updateWarehouseConsumeRecord(outRecord);
    }
}
