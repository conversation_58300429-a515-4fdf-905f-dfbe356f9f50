package com.rome.stock.innerservice.domain.service.impl;

import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import com.rome.arch.util.dynamic.DynamicDataSourceEnum;
import com.rome.arch.util.dynamic.TargetDataSource;
import com.rome.stock.common.enums.warehouse.WarehouseRecordTypeVO;
import com.rome.stock.innerservice.api.dto.LogManagerDTO;
import com.rome.stock.innerservice.api.dto.LogManagerParamDTO;
import com.rome.stock.innerservice.common.AlikAssert;
import com.rome.stock.innerservice.common.ResCode;
import com.rome.stock.innerservice.common.RomeCollectionUtil;
import com.rome.stock.innerservice.common.StringUtils;
import com.rome.stock.innerservice.constant.LogBusinessTypeVo;
import com.rome.stock.innerservice.constant.WmsSyncStatusVO;
import com.rome.stock.innerservice.context.distributedlock.log.FrontRecordHandler;
import com.rome.stock.innerservice.domain.convertor.FrontWarehouseRecordRelationConvertor;
import com.rome.stock.innerservice.domain.entity.RwRecordPoolE;
import com.rome.stock.innerservice.domain.entity.frontrecord.InventoryAdjustRecordE;
import com.rome.stock.innerservice.domain.entity.frontrecord.WDTOnlineRetailE;
import com.rome.stock.innerservice.domain.entity.warehouserecord.WarehouseRecordE;
import com.rome.stock.innerservice.domain.repository.LogEnumRepository;
import com.rome.stock.innerservice.domain.repository.LogManagerRepository;
import com.rome.stock.innerservice.domain.repository.RwRecordPoolRepository;
import com.rome.stock.innerservice.domain.repository.frontrecord.FrInventoryAdjustRepository;
import com.rome.stock.innerservice.domain.repository.frontrecord.FrWDTSaleRepository;
import com.rome.stock.innerservice.domain.repository.frontrecord.FrontWarehouseRecordRelationRepository;
import com.rome.stock.innerservice.domain.repository.warehouserecord.WarehouseRecordRepository;
import com.rome.stock.innerservice.domain.service.LogManagerService;
import com.rome.stock.innerservice.infrastructure.dataobject.FrontWarehouseRecordRelationDO;
import com.rome.stock.innerservice.infrastructure.dataobject.LogEnumDO;
import com.rome.stock.innerservice.infrastructure.dataobject.WmsCallRecordDO;
import com.rome.stock.innerservice.remote.sap.dto.SyncWhInfoDTO;
import com.rome.stock.wms.constants.WarehouseWmsConfigEnum;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;

@Slf4j
@Service(value = "logManagerServiceImpl")
public class LogManagerServiceImpl implements LogManagerService {

    @Resource
    private LogManagerRepository logManagerRepository;
    @Resource
    private LogEnumRepository logEnumRepository;
    @Resource
    private WarehouseRecordRepository warehouseRecordRepository;
    @Resource
    private FrontWarehouseRecordRelationRepository frontWarehouseRecordRelationRepository;
    @Resource
    private FrontWarehouseRecordRelationConvertor frontWarehouseRecordRelationConvertor;
    @Resource
    private RwRecordPoolRepository rwRecordPoolRepository;
    @Resource
    private FrWDTSaleRepository frWDTSaleRepository;
    @Resource
    private FrontRecordHandler frontRecordHandler;
    @Resource
    private FrInventoryAdjustRepository  frInventoryAdjustRepository;

    @Override
    @TargetDataSource(DynamicDataSourceEnum.READ)
    public PageInfo<LogManagerDTO> queryLogs(LogManagerParamDTO paramDTO) {
        Page page= PageHelper.startPage(paramDTO.getPageIndex(),paramDTO.getPageSize());
        List<LogManagerDTO> pageList= Lists.newArrayList();
        Boolean checkOk=StringUtils.isNotEmpty(paramDTO.getBusinessType())&&!Objects.isNull(paramDTO.getStartTime());
        AlikAssert.isTrue(checkOk,ResCode.STOCK_ERROR_1002,"业务类型及创建时间范围不可为空");
        boolean supportType = LogBusinessTypeVo.getTypeMap().containsKey(paramDTO.getBusinessType());
        AlikAssert.isTrue(supportType,ResCode.STOCK_ERROR_1002,"暂不支持此业务类型");
        if (Objects.equals(paramDTO.getBusinessType(), LogBusinessTypeVo.WMS_DELIVERY.getType())
                || Objects.equals(paramDTO.getBusinessType(), LogBusinessTypeVo.WMS_CALLBACK.getType())){
            if (Objects.equals(paramDTO.getBusinessType(), LogBusinessTypeVo.WMS_DELIVERY.getType())){
                paramDTO.setRequestServices(Arrays.asList(LogBusinessTypeVo.WMS_DELIVERY.getRequestServices()));
                paramDTO.setSyncWmsStatus(WmsSyncStatusVO.UNSYNCHRONIZED.getStatus());
            }else if (Objects.equals(paramDTO.getBusinessType(), LogBusinessTypeVo.WMS_CALLBACK.getType())){
                paramDTO.setRequestServices(Arrays.asList(LogBusinessTypeVo.WMS_CALLBACK.getRequestServices()));
                paramDTO.setSyncWmsStatus(WmsSyncStatusVO.SYNCHRONIZED.getStatus());
            }
            pageList=logManagerRepository.queryWmsCallByCondition(paramDTO);
            if (CollectionUtils.isNotEmpty(pageList)){
                pageList.stream().forEach(dto->{
                    String recordCode= dto.getRecordCode();
                    String requestService=dto.getRequestService();
                    WmsCallRecordDO wmsCallRecordDO=logManagerRepository.queryWmsCallByRecordAndService(recordCode,requestService);
                    if (Objects.nonNull(wmsCallRecordDO)){
                        dto.setRequestContent(wmsCallRecordDO.getRequestContent());
                        dto.setResponseContent(wmsCallRecordDO.getResponseContent());
                        dto.setCreateTime(wmsCallRecordDO.getCreateTime());
                        dto.setWmsCode(wmsCallRecordDO.getWmsCode());
                    }
                });
            }
        } else if (Objects.equals(paramDTO.getBusinessType(), LogBusinessTypeVo.WMS_RECEIVE_MANYTIMES.getType())){
            paramDTO.setRequestServices(Arrays.asList(LogBusinessTypeVo.WMS_RECEIVE_MANYTIMES.getRequestServices()));
            pageList=logManagerRepository.queryWmsReceiveManyTimes(paramDTO);
            if (CollectionUtils.isNotEmpty(pageList)){
                pageList.stream().forEach(dto->{
                    String recordCode= dto.getRecordCode();
                    String wmsRecordCode=dto.getWmsRecordCode();
                    String requestService=dto.getRequestService();
                    if (StringUtils.isNotEmpty(recordCode)&&StringUtils.isNotEmpty(wmsRecordCode)){
                        WmsCallRecordDO recordDO=logManagerRepository.queryWmsCall(requestService,recordCode,wmsRecordCode);
                        if (Objects.nonNull(recordDO)){
                            dto.setRequestService(recordDO.getRequestService());
                            dto.setResponseContent(recordDO.getResponseContent());
                            dto.setRequestContent(recordDO.getRequestContent());
                            dto.setCreateTime(recordDO.getCreateTime());
                            dto.setWmsCode(recordDO.getWmsCode());
                        }
                    }
                });
            }
        }else if (Objects.equals(paramDTO.getBusinessType(), LogBusinessTypeVo.INVENTORY_ADJUST.getType())){
            paramDTO.setRequestServices(Arrays.asList(LogBusinessTypeVo.INVENTORY_ADJUST.getRequestServices()));
            pageList=logManagerRepository.querySingleWmsCallByCondition(paramDTO);
            if (CollectionUtils.isNotEmpty(pageList)){
                pageList.stream().forEach(dto->{
                    String recordCode= dto.getRecordCode();
                    String requestService=dto.getRequestService();
                    WmsCallRecordDO wmsCallRecordDO=logManagerRepository.queryWmsCallByRecordAndService(recordCode,requestService);
                    if (Objects.nonNull(wmsCallRecordDO)){
                        dto.setRequestContent(wmsCallRecordDO.getRequestContent());
                        dto.setResponseContent(wmsCallRecordDO.getResponseContent());
                        dto.setCreateTime(wmsCallRecordDO.getCreateTime());
                        dto.setWmsCode(wmsCallRecordDO.getWmsCode());
                    }
                });
            }
        } else if (Objects.equals(paramDTO.getBusinessType(), LogBusinessTypeVo.SHOP_ALLOCATION.getType())){
            paramDTO.setRequestServices(Arrays.asList(LogBusinessTypeVo.SHOP_ALLOCATION.getRequestServices()));
            paramDTO.setRecordTypes(Arrays.asList(new Integer[]{
                    WarehouseRecordTypeVO.SHOP_ALLOCATION_OUT_WAREHOUSE_RECORD.getType(),
                    WarehouseRecordTypeVO.SHOP_ALLOCATION_IN_WAREHOUSE_RECORD.getType()}
            ));
            List<LogManagerDTO> shopLogs = logManagerRepository.queryShopAllocationLogByCondition(paramDTO);
            if (CollectionUtils.isNotEmpty(shopLogs)){
                pageList.addAll(shopLogs);
            }
            if (CollectionUtils.isNotEmpty(pageList)){
                pageList.stream().forEach(dto->{
                    String recordCode= dto.getRecordCode();
                    LogManagerDTO logDto=logManagerRepository.queryShopAllocationLogByRecordAndType(recordCode,dto.getType());;
                    if (Objects.nonNull(logDto)){
                        dto.setRequestContent(logDto.getRequestContent());
                        dto.setResponseContent(logDto.getResponseContent());
                        dto.setCreateTime(logDto.getCreateTime());
                    }
                });
            }
        }else {
            if (Objects.equals(paramDTO.getBusinessType(), LogBusinessTypeVo.CANCEL_ORDER.getType())) {
                paramDTO.setRequestServices(Arrays.asList(LogBusinessTypeVo.CANCEL_ORDER.getRequestServices()));
                pageList = logManagerRepository.querySapInterfaceLogByCancelOrder(paramDTO);
                pageList.stream()
                        .filter(dto->StringUtils.isEmpty(dto.getOutRecordCode()))
                        .forEach(dto->dto.setOutRecordCode(dto.getRecordCode()));
            }else if (Objects.equals(paramDTO.getBusinessType(), LogBusinessTypeVo.CANCEL_CHILD_DO.getType())) {
                paramDTO.setRequestServices(Arrays.asList(LogBusinessTypeVo.CANCEL_CHILD_DO.getRequestServices()));
                pageList = logManagerRepository.querySapInterfaceLogByCancelChildDo(paramDTO);
                pageList.stream().forEach(dto->{
                    RwRecordPoolE poolE=rwRecordPoolRepository.queryWithDetailsByDoCode(dto.getRecordCode());
                    if (Objects.nonNull(poolE)){
                        dto.setOutInOrderCode(poolE.getWarehouseRecordCode());
                        WDTOnlineRetailE onlineRetailE = frWDTSaleRepository.queryByRecordCode(poolE.getFrontRecordCode());
                        if (Objects.nonNull(onlineRetailE)){
                            dto.setOutRecordCode(onlineRetailE.getOutRecordCode());
                        }
                        WarehouseRecordE warehouseRecordE = warehouseRecordRepository.queryWarehouseRecordByRecordCode(poolE.getFrontRecordCode());
                        if (Objects.nonNull(warehouseRecordE)){
                            dto.setSapOrderCode(warehouseRecordE.getSapOrderCode());
                        }
                    }
                });
            } else {
                if (Objects.equals(paramDTO.getBusinessType(), LogBusinessTypeVo.PUSH_CMP.getType())){
                    paramDTO.setRequestServices(Arrays.asList(LogBusinessTypeVo.PUSH_CMP.getRequestServices()));
                }else if (Objects.equals(paramDTO.getBusinessType(), LogBusinessTypeVo.SHOP_RECEIVE.getType())){
                    paramDTO.setRequestServices(Arrays.asList(LogBusinessTypeVo.SHOP_RECEIVE.getRequestServices()));
                    paramDTO.setRecordTypes(Arrays.asList(new Integer[]{
                            WarehouseRecordTypeVO.DS_REPLENISH_IN_SHOP_RECORD.getType(),
                            WarehouseRecordTypeVO.LS_REPLENISH_IN_SHOP_RECORD.getType(),
                            WarehouseRecordTypeVO.SHOP_COLD_CHAIN_IN_RECORD.getType(),
                            WarehouseRecordTypeVO.LS_COLD_OVER_STOCK_IN_RECORD.getType(),
                            WarehouseRecordTypeVO.SHOP_CHAIN_DIRECT_IN_RECORD.getType(),
                            WarehouseRecordTypeVO.AB_PRESALE_DISTRIBUTION_IN_RECORD.getType(),
                            WarehouseRecordTypeVO.DIRECT_SALE_NEWPRODUCT_IN_RECORD.getType(),
                            WarehouseRecordTypeVO.SHOP_NEWSTORE_PRECROSS_IN_RECORD.getType(),
                            WarehouseRecordTypeVO.DS_SHOP_COLD_IN_RECORD.getType(),
                            WarehouseRecordTypeVO.DS_REPLENISH_IN_SHOP_PLAN_RECORD.getType(),
                            WarehouseRecordTypeVO.LS_REPLENISH_IN_SHOP_PLAN_RECORD.getType(),
                    }
                    ));
                }
                List<LogManagerDTO> sapLogs=logManagerRepository.querySapInterfaceLogByCondition(paramDTO);
                if (CollectionUtils.isNotEmpty(sapLogs)){
                    pageList.addAll(sapLogs);
                }
            }
            if (CollectionUtils.isNotEmpty(pageList)){
                pageList.stream().forEach(dto->{
                    String recordCode= dto.getRecordCode();
                    String requestService=dto.getRequestService();
                    LogManagerDTO logDto=logManagerRepository.querySapLogByRecordAndService(recordCode,requestService);
                    if (Objects.nonNull(logDto)){
                        dto.setRequestContent(logDto.getRequestContent());
                        dto.setResponseContent(logDto.getResponseContent());
                        dto.setCreateTime(logDto.getCreateTime());
                    }
                });
            }
        }
        if (CollectionUtils.isEmpty(pageList)){
            return new PageInfo<>();
        }
        pageList.stream().forEach(dto->dto.setStatus(0));
        PageInfo<LogManagerDTO> pageInfo = new PageInfo<>(pageList);
        pageInfo.setTotal(page.getTotal());
        return pageInfo;
    }

    /**
     * 匹配日志其他相关信息数据
     * @param dtos
     */
    @TargetDataSource(DynamicDataSourceEnum.READ)
    public void queryForLogEnum(List<LogManagerDTO> dtos){
        if (CollectionUtils.isNotEmpty(dtos)){
            List<LogManagerDTO> shopLogs=Lists.newArrayList();
            Set<String> requestServices = Sets.newHashSet();
            dtos.forEach(dto->{
                if (StringUtils.isEmpty(dto.getRequestService()) && Objects.nonNull(dto.getType())){
                    shopLogs.add(dto);
                }
                if (StringUtils.isNotEmpty(dto.getRequestService())){
                    requestServices.add(dto.getRequestService());
                }
            });
            if (CollectionUtils.isNotEmpty(shopLogs)){
                shopLogs.forEach(log->{
                    log.setRequestSystem(Objects.equals(1,log.getType())?"SAP":"CMP");
                    log.setRequestName(String.format("门店调拨%s",Objects.equals(1,log.getType())?"SAP过账":"下发CMP"));
                    log.setRequestService(String.format(Objects.equals(1,log.getType())?"TransferOrder":"shopAllocationPushCMP"));
                    log.setRequestType(1);
                });
            }
            if (CollectionUtils.isEmpty(requestServices)){
                return;
            }
            List<LogEnumDO> enumDOS=logEnumRepository.queryByRequestServices(new ArrayList<>(requestServices));
            if (CollectionUtils.isNotEmpty(enumDOS)){
                Map<String,LogEnumDO> map=RomeCollectionUtil.listforMap(enumDOS,"requestService");
                dtos.stream().forEach(logDto->{
                    LogEnumDO enumDO=map.get(logDto.getRequestService());
                    if (Objects.nonNull(enumDO)){
                        logDto.setRequestName(enumDO.getRequestName());
                        logDto.setRequestTable(enumDO.getRequestTable());
                        logDto.setRequestType(enumDO.getRequestType());
                        logDto.setRequestSystem(enumDO.getRequestSystem());
                        logDto.setRequestWay(enumDO.getRequestWay());
                        logDto.setTableName(enumDO.getTableName());
                        logDto.setTableField(enumDO.getTableField());
                        if (Objects.equals(enumDO.getRequestService(), "inRecordNotify")) {
                            if (!isShopInType(logDto.getRecordCode())) {
                                logDto.setTableField("sc_receipt_record表sync_order_status");
                            }
                        }
                        //通过wmsCode区分下发系统
                        if (Objects.equals(enumDO.getRequestService(), "entryOrderCreate")) {
                            if (Objects.nonNull(logDto.getWmsCode())) {
                                String wmsDesc = WarehouseWmsConfigEnum.getDescByType(logDto.getWmsCode());
                                if (StringUtils.isNotEmpty(wmsDesc)) {
                                    logDto.setRequestName("【" + wmsDesc + "】入库单创建");
                                    logDto.setRequestSystem(wmsDesc);
                                }
                            }
                        }
                        if (Objects.equals(enumDO.getRequestService(), "inventoryAdjust")) {
                            if (Objects.nonNull(logDto.getWmsCode())) {
                                String wmsDesc = WarehouseWmsConfigEnum.getDescByType(logDto.getWmsCode());
                                if (StringUtils.isNotEmpty(wmsDesc)) {
                                    logDto.setRequestName("【" + wmsDesc + "】库存调整");
                                    logDto.setRequestSystem(wmsDesc);
                                }
                            }
                        }
                        if (Objects.equals(enumDO.getRequestService(), "batchOutOrderCreate")||
                                Objects.equals(enumDO.getRequestService(), "outOrderCreate")) {
                            if (Objects.nonNull(logDto.getWmsCode())) {
                                String wmsDesc = WarehouseWmsConfigEnum.getDescByType(logDto.getWmsCode());
                                if (StringUtils.isNotEmpty(wmsDesc)) {
                                    String desc=Objects.equals(enumDO.getRequestService(), "batchOutOrderCreate")?"批量":"";
                                    logDto.setRequestName("【" + wmsDesc + "】"+desc+"出库单创建");
                                    logDto.setRequestSystem(wmsDesc);
                                }
                            }
                        }
                        logDto.setTableFieldBeforeValue(enumDO.getTableFieldBeforeValue());
                        logDto.setTableFieldAfterValue(enumDO.getTableFieldAfterValue());
                    }
                });
            }
        }
    }

    /**
     * 查询后置单信息，及前置单外部单据号信息
     * @param dtos
     */
    @TargetDataSource(DynamicDataSourceEnum.READ)
    public void queryForOrderInfo(List<LogManagerDTO> dtos){
        if (CollectionUtils.isNotEmpty(dtos)){
            dtos.stream().forEach(dto->{
                String recordCode=dto.getRecordCode();
                if (StringUtils.isEmpty(dto.getOutInOrderCode())){
                    if (Objects.equals(dto.getRequestService(), "inventoryAdjust")) {
                        if (Objects.equals(dto.getStatus(),1)){
                            InventoryAdjustRecordE inventoryAdjustRecordE=frInventoryAdjustRepository
                                    .queryFrontRecordByOutCode(recordCode);
                            if (Objects.nonNull(inventoryAdjustRecordE)){
                                List<String> recordCodes=frontWarehouseRecordRelationRepository
                                        .getRecordCodeByFrontRecordCode(inventoryAdjustRecordE.getRecordCode());
                                if (CollectionUtils.isNotEmpty(recordCodes)){
                                    dto.setOutInOrderCode(recordCodes.get(0));
                                }
                            }
                        }
                    }else {
                        WarehouseRecordE warehouseRecordE=warehouseRecordRepository.queryWarehouseRecordByRecordCode(recordCode);
                        if (Objects.nonNull(warehouseRecordE)){
                            if (StringUtils.isEmpty(dto.getOutInOrderCode())){
                                dto.setSapOrderCode(warehouseRecordE.getSapOrderCode());
                                dto.setOutInOrderCode(warehouseRecordE.getRecordCode());
                            }
                        }
                    }
                }
                if (StringUtils.isEmpty(dto.getOutRecordCode())){
                    if (Objects.equals(dto.getRequestService(), "inventoryAdjust")) {
                        dto.setOutRecordCode(dto.getRecordCode());
                    }else {
                        List<FrontWarehouseRecordRelationDO> list=
                                frontWarehouseRecordRelationConvertor.entityListToDoList(frontWarehouseRecordRelationRepository.getFrontWarehouseRecordsRelationByWrCode(recordCode));
                        if (CollectionUtils.isNotEmpty(list)){
                            FrontWarehouseRecordRelationDO frontWarehouseRecordRelationDO=list.get(0);
                            SyncWhInfoDTO infoDTO=frontRecordHandler.getFrontRecordInfo(frontWarehouseRecordRelationDO);
                            if (Objects.nonNull(infoDTO)&&StringUtils.isNotEmpty(infoDTO.getOutRecordCode())){
                                dto.setOutRecordCode(infoDTO.getOutRecordCode());
                            }
                        }
                    }
                }
            });
        }
    }

    /**
     * 是否门店类型
     * @param recordCode
     * @return
     */
    private boolean isShopInType(String recordCode){
        WarehouseRecordE warehouseRecordE=warehouseRecordRepository.queryWarehouseRecordByRecordCode(recordCode);
        if (Objects.nonNull(warehouseRecordE)){
            Integer recordType=warehouseRecordE.getRecordType();
            if (Objects.equals(recordType,11)||Objects.equals(recordType,15) ||Objects.equals(recordType,WarehouseRecordTypeVO.LS_COLD_OVER_STOCK_IN_RECORD.getType())
                    ||Objects.equals(recordType,42)||Objects.equals(recordType,45)
                    ||Objects.equals(recordType,23)||Objects.equals(recordType,27)
                    ||Objects.equals(recordType,115)||Objects.equals(recordType,117)
                    ||Objects.equals(recordType,120)||Objects.equals(recordType,146)
                    ||Objects.equals(recordType,147)||Objects.equals(recordType,148)
                    ||Objects.equals(recordType,149)||Objects.equals(recordType,150)
                    ||Objects.equals(recordType,152)
                    ||Objects.equals(recordType,WarehouseRecordTypeVO.DS_REPLENISH_IN_SHOP_PLAN_RECORD.getType())
                    ||Objects.equals(recordType,WarehouseRecordTypeVO.LS_REPLENISH_IN_SHOP_PLAN_RECORD.getType())){
                return true;
            }
        }
        return false;
    }
}
