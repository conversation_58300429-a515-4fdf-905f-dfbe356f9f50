package com.rome.stock.innerservice.domain.entity.warehouserecord;

import com.rome.arch.core.domain.EntityFactory;
import com.rome.stock.common.enums.warehouse.WarehouseRecordTypeVO;
import com.rome.stock.common.utils.AlikAssert;
import com.rome.stock.innerservice.api.dto.warehouserecord.InWarehouseRecordDTO;
import com.rome.stock.innerservice.api.dto.warehouserecord.RecordDetailDTO;
import com.rome.stock.innerservice.common.ResCode;
import com.rome.stock.innerservice.common.SkuQtyUnitTools;
import com.rome.stock.innerservice.constant.*;
import com.rome.stock.innerservice.domain.repository.warehouserecord.ReservationReturnWarehouseRepository;
import com.rome.stock.innerservice.domain.repository.warehouserecord.WarehouseRecordRepository;
import com.rome.stock.core.infrastructure.dataobject.core.CoreRealStockOpDO;
import com.rome.stock.core.infrastructure.dataobject.core.CoreRealStockOpDetailDO;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;

/**
 * 退货单
 */
@Data
@Slf4j
@Component
@Scope("prototype")
@EqualsAndHashCode(callSuper = false)
public class ReservationReturnWarehouseRecordE extends AbstractWarehouseRecord {

    @Resource
    private EntityFactory entityFactory;

    @Resource
    private SkuQtyUnitTools skuQtyUnitTools;

    @Resource
    private ReservationReturnWarehouseRepository reservationReturnWarehouseRepository;

    @Resource
    private WarehouseRecordRepository warehouseRecordRepository;

    /**
     * 创建单据
     */
    public void createRWOutRecordForRes(){
        long id = reservationReturnWarehouseRepository.saveReturnWarehouseRecord(this);
        this.setId(id);
        this.warehouseRecordDetails.forEach(wrRecord -> {
            wrRecord.setWarehouseRecordDetail(this);
            wrRecord.setActualQty(BigDecimal.ZERO);
        });
        reservationReturnWarehouseRepository.saveWarehouseRecordDetails(this.warehouseRecordDetails);
    }


    /**
     * 构建前置单出库单
     * @param frontRecord
     */
    public void createOutRecordByFontRecord(InWarehouseRecordDTO frontRecord,Long realWarehouseId){
        this.setRecordCode(frontRecord.getRecordCode());
        this.setRealWarehouseId(realWarehouseId);
        this.setBusinessType(WarehouseRecordBusinessTypeVO.IN_WAREHOUSE_RECORD.getType());
        this.setRecordType(frontRecord.getRecordType());
        this.setRecordStatus(WarehouseRecordStatusVO.INIT.getStatus());
        this.setSapOrderCode(frontRecord.getSapOrderCode());

        List<RecordDetailDTO> frontRecordDetails=frontRecord.getDetailList();
        this.setWarehouseRecordDetails(new ArrayList<>(frontRecordDetails.size()));
        this.setSyncWmsStatus(WmsSyncStatusVO.UNSYNCHRONIZED.getStatus());
        this.setBatchStatus(WarehouseRecordBatchStatusVO.INIT.getStatus());
        List<Long> frontIds = new ArrayList<>();
        if(frontRecordDetails!=null){
            for(RecordDetailDTO detailE:frontRecordDetails){
                WarehouseRecordDetail warehouseRecordDetail=entityFactory.createEntity(WarehouseRecordDetail.class);
                warehouseRecordDetail.setActualQty(BigDecimal.ZERO);
                warehouseRecordDetail.setRealWarehouseId(realWarehouseId);
                warehouseRecordDetail.setPlanQty(detailE.getBasicSkuQty());
                warehouseRecordDetail.setUnit(detailE.getBasicUnit());
                warehouseRecordDetail.setUnitCode(detailE.getBasicUnitCode());
                warehouseRecordDetail.setSkuCode(detailE.getSkuCode());
                warehouseRecordDetail.setRecordCode(frontRecord.getRecordCode());
                warehouseRecordDetail.setLineNo(detailE.getLineNo());
                warehouseRecordDetail.setDeliveryLineNo(detailE.getDeliveryLineNo());
                warehouseRecordDetail.setDeliveryData(detailE.getDeliveryData());
                this.warehouseRecordDetails.add(warehouseRecordDetail);
            }
            //设置skuCode和skuID
            this.initWarehouseRecodeDetail();
        }
    }


    /**
     * 封装门店在途库存DO对象
     */
    public CoreRealStockOpDO packOnRoadStockOpDo(ReservationReturnWarehouseRecordE warehouseRecordE){
        CoreRealStockOpDO stockDO = new CoreRealStockOpDO();
        List<CoreRealStockOpDetailDO> detailDos = new ArrayList<>();
        stockDO.setRecordCode(warehouseRecordE.getRecordCode());
        stockDO.setTransType(warehouseRecordE.getRecordType());
        stockDO.setDetailDos(detailDos);
        for (WarehouseRecordDetail detail : warehouseRecordE.getWarehouseRecordDetails()) {
            //实收数量为0的不处理在途的增加和减少
            if(BigDecimal.ZERO.compareTo(detail.getPlanQty()) == 0){
                continue;
            }
            CoreRealStockOpDetailDO detailDO = new CoreRealStockOpDetailDO();
            detailDO.setOnroadQty(detail.getPlanQty());
            detailDO.setSkuId(detail.getSkuId());
            detailDO.setSkuCode(detail.getSkuCode());
            detailDO.setRealWarehouseId(warehouseRecordE.getRealWarehouseId());
            detailDO.setChannelCode(detail.getChannelCode());
            detailDos.add(detailDO);
        }
        return stockDO;
    }

    /**
     * 收货入库
     */
    public void inWarehouse() {
        //1.累加实收数量(基本单位数量)
        for (WarehouseRecordDetail detail : warehouseRecordDetails) {
            AlikAssert.notNull(detail.getBasicSkuQty(), ResCode.STOCK_ERROR_1002, "商品【"+ detail.getSkuCode() +"】单位【"+ detail.getUnitCode() +"】异常");
            boolean flag = warehouseRecordRepository.increaseActualQtyById(detail.getBasicSkuQty(), detail.getId(), detail.getSkuCode(), detail.getRecordCode());
            AlikAssert.isTrue(flag, ResCode.STOCK_ERROR_1002, ResCode.STOCK_ERROR_1002_DESC+"：主键或单号错误");
        }
        //更新单据状态和待推送销售中心状态
       int i = warehouseRecordRepository.reservationReturninWarehouse(this.getId(), this.getModifier());
        AlikAssert.isTrue(i>0, ResCode.STOCK_ERROR_1016, ResCode.STOCK_ERROR_1016_DESC);

    }

    /**
     * 封装增加库存方法
     * @return
     */
    public CoreRealStockOpDO initAddStockObj() {
        List<CoreRealStockOpDetailDO> increaseDetails = new ArrayList<>();
        for (WarehouseRecordDetail detail: this.getWarehouseRecordDetails()) {
            //数量为0的不处理库存的增加和减少
            if(BigDecimal.ZERO.compareTo(detail.getActualQty()) == 0){
                continue;
            }
            CoreRealStockOpDetailDO coreRealStockOpDetailDO = new CoreRealStockOpDetailDO();
            coreRealStockOpDetailDO.setSkuId(detail.getSkuId());
            coreRealStockOpDetailDO.setSkuCode(detail.getSkuCode());
            coreRealStockOpDetailDO.setRealQty(detail.getActualQty());
            coreRealStockOpDetailDO.setRealWarehouseId(this.getRealWarehouseId());
            increaseDetails.add(coreRealStockOpDetailDO);
        }
        CoreRealStockOpDO coreRealStockOpDO = new CoreRealStockOpDO();
        coreRealStockOpDO.setRecordCode(this.getRecordCode());
        coreRealStockOpDO.setTransType(this.getRecordType());
        coreRealStockOpDO.setDetailDos(increaseDetails);
        return coreRealStockOpDO;
    }
}    
