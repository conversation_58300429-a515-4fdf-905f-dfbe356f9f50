package com.rome.stock.innerservice.domain.entity.frontrecord;

import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Component;

@Component
@Scope("prototype")
@Data
@EqualsAndHashCode(callSuper = false)
public class OutsourcingOutDetailE extends AbstractFrontRecordDetail {

	/**
	 * 行号
	 */
	private String lineNo;
}
