/**
 * Filename SapInterfaceLogConsumer.java
 * Company 上海来伊份科技有限公司。
 *
 * <AUTHOR>
 * @version
 */
package com.rome.stock.innerservice.domain.message;

import com.alibaba.fastjson.JSONObject;
import com.rome.stock.common.enums.CustomRocketMQEnum;
import com.rome.scm.common.rocketmq.CustomConsumerListener;
import com.rome.stock.innerservice.domain.repository.SapInterfaceLogRepository;
import com.rome.stock.innerservice.infrastructure.dataobject.SapInterfaceLogDO;
import org.apache.rocketmq.common.message.MessageExt;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import javax.annotation.Resource;

/**
 * sap日志保存
 * <AUTHOR>
 * @since 2023/7/13 13:57
 */
@Service
public class SapInterfaceLogConsumer implements CustomConsumerListener<MessageExt> {

    @Resource
    private SapInterfaceLogRepository sapInterfaceLogRepository;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void onMessage(MessageExt message, String businessNo, String msgKey) {
        String content = new String(message.getBody());
        SapInterfaceLogDO sapInterfaceLogDO= JSONObject.parseObject(content, SapInterfaceLogDO.class);
        sapInterfaceLogRepository.insert(sapInterfaceLogDO);
    }

    @Override
    public String getRocketMQCode() {
        return CustomRocketMQEnum.MQ_SAP_LOG_SAVE_PUSH.getCode();
    }
}
