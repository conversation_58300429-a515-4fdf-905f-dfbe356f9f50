package com.rome.stock.innerservice.domain.entity;

import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Component;

import java.util.Date;

/**
 * @Author: zhoupeng
 * @createTime: 2022年07月21日 21:16:17
 * @version: 1.0
 * @Description:
 */
@Component
@Scope("prototype")
@Data
@EqualsAndHashCode(callSuper = false)
public class ChannelWdtVwRelationE {

    /**
     * 唯一主键
     */
    private Long id;

    /**
     * 渠道code
     */
    private String  channelCode;


    /**
     * 渠道名称
     */
    private  String   channelName;



    /**
     *旺店通虚仓编号
     */
    private  String  wdtVwCode;

    /**
     * 比率（百分比），1-100区间可选数字
     */
    private  Integer syncRate;


    /**
     * 备注
     */
    private  String  remark;


    /**
     * 虚拟仓库组名称
     */
    private  String  name;

    /**
     * 虚拟仓库组编码
     */
    private  String    virtualWarehouseGroupCode;

    /**
     * 模板名称
     */
    private  String   template;


    /**
     * 模板描述
     */
    private  String    templateDesc;


    /**
     * 创建人
     */
    private Long creator;

    /**
     * 更新人
     */
    private Long modifier;


    /**
     * 创建时间
     */
    private Date createTime;


    /**
     * 更新时间
     */
    private Date updateTime;




}
