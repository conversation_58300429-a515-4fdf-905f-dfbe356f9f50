package com.rome.stock.innerservice.domain.entity;

import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Component;

@Component
@Scope("prototype")
@Data
@EqualsAndHashCode(callSuper = false)
public class DistributiveRelationshipE extends BaseE {
    /**
     * 唯一主键
     */
    private Long id;
    /**
     * 分配关系类型
     */
    private Integer relationType;
    /**
     * 实仓仓库Id
     */
    private Long realWarehouseId;
    /**
     * 实仓仓库编码
     */
    private String realWarehouseCode;
    /**
     * 实仓仓库名称
     */
    private String realWarehouseName;
    /**
     * 分配比例
     */
    private Integer syncRate;
    /**
     * 渠道策略组比例
     */
    private Integer showRate;
    /**
     * 虚拟仓库Id
     */
    private String virtualWarehouseId;
    /**
     * 虚仓仓库编码
     */
    private String virtualWarehouseCode;
    /**
     * 虚拟仓库名称
     */
    private String virtualWarehouseName;
    /**
     * 策略组Id
     */
    private Long virtualWarehouseGroupId;
    /**
     * 策略组编码
     */
    private String virtualWarehouseGroupCode;
    /**
     * 策略组名称
     */
    private String virtualWarehouseGroupName;
    /**
     * 渠道Id
     */
    private Long channelId;
    /**
     * 渠道编码
     */
    private String channelCode;
    /**
     * 渠道名称
     */
    private String channelName;
}



