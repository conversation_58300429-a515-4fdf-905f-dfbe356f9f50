package com.rome.stock.innerservice.domain.entity.frontrecord;

import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Component;

@Component
@Scope("prototype")
@Data
@EqualsAndHashCode(callSuper = false)
public class WDTOnlineRetailRecordDetailE extends AbstractFrontRecordDetail{
    /**
     * 拆单状态： 0待分拆到do单 1 已分拆到do单
     */
    private Integer splitStatus;

    private String lineNo ;

    /**
     * 1赠品 2非赠品
     */
    private Integer giftType;

    /**
     * 所属主品的商品编码,为null则表示非组合品
     */
    private String parentSkuCode;

    private String serialNo;

    /**
     * 交易单号
     */
    private String outRecordCode;

    /**
     * 前置单状态
     */
    private Integer recordStatus;

    /**
     * 换桶标识
     */
    private Integer hasChangeBucket;

}
