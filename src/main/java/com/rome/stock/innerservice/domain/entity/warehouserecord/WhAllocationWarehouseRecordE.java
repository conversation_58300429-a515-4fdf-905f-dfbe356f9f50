package com.rome.stock.innerservice.domain.entity.warehouserecord;

import com.rome.arch.core.domain.EntityFactory;
import com.rome.stock.common.constants.warehouse.WarehouseRecordConstant;
import com.rome.stock.common.enums.warehouse.WarehouseRecordTypeVO;
import com.rome.stock.innerservice.api.dto.warehouserecord.InWarehouseRecordDTO;
import com.rome.stock.innerservice.api.dto.warehouserecord.OutWarehouseRecordDTO;
import com.rome.stock.innerservice.api.dto.warehouserecord.RecordDetailDTO;
import com.rome.stock.innerservice.common.AlikAssert;
import com.rome.stock.innerservice.common.ResCode;
import com.rome.stock.innerservice.common.SkuQtyUnitTools;
import com.rome.stock.innerservice.constant.WarehouseRecordBusinessTypeVO;
import com.rome.stock.innerservice.constant.WarehouseRecordStatusVO;
import com.rome.stock.innerservice.constant.WmsSyncStatusVO;
import com.rome.stock.innerservice.domain.convertor.WarehouseRecordDetailConvertor;
import com.rome.stock.innerservice.domain.convertor.frontrecord.ShopAllocationConvertor;
import com.rome.stock.innerservice.domain.convertor.frontrecord.WhAllocationConvertor;
import com.rome.stock.innerservice.domain.entity.RealWarehouseE;
import com.rome.stock.innerservice.domain.repository.RealWarehouseRepository;
import com.rome.stock.innerservice.domain.repository.warehouserecord.WhAllocationWarehouseRepository;
import com.rome.stock.core.infrastructure.dataobject.core.*;
import com.rome.stock.innerservice.infrastructure.redis.RealWarehouseWmsRedis;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

/**
 * 类WhAllocationWarehouseRecord的实现描述：仓库调拨出入库单
 *
 * <AUTHOR> 2019/5/14 10:22
 */
@Data
@Slf4j
@Component
@Scope("prototype")
@EqualsAndHashCode(callSuper = false)
public class WhAllocationWarehouseRecordE extends AbstractWarehouseRecord {

    @Resource
    private EntityFactory entityFactory;

    @Resource
    private WhAllocationWarehouseRepository whAllocationWarehouseRepository;
    @Resource
    private SkuQtyUnitTools skuQtyUnitTools;
    @Resource
    private RealWarehouseRepository realWarehouseRepository;
    @Resource
    private WhAllocationConvertor whAllocationConvertor;
    @Autowired
    private RealWarehouseWmsRedis realWarehouseWmsRedis;
    @Autowired
    WarehouseRecordDetailConvertor warehouseRecordDetailConvertor;
    @Autowired
    ShopAllocationConvertor shopAllocationConvertor;


    /**
     * 大仓出库单
     */
    public void whOutRecord(OutWarehouseRecordDTO dto){
        this.setRecordType(WarehouseRecordTypeVO.WH_ALLOCATION_OUT_WAREHOUSE_RECORD.getType());
        this.setRecordCode(dto.getRecordCode());
        //5.锁定大仓库存
        RealWarehouseE outWarehouse = realWarehouseRepository.getRealWarehouseByOutCodeAndFactoryCode(dto.getWarehouseCode(),dto.getFactoryCode());
        this.setRealWarehouseId(outWarehouse.getId());
        this.setBusinessType(WarehouseRecordBusinessTypeVO.OUT_WAREHOUSE_RECORD.getType());
        this.setRecordStatus(WarehouseRecordStatusVO.INIT.getStatus());
        this.setCmpStatus(WarehouseRecordConstant.INIT_CMP);
        this.setSapOrderCode(dto.getSapOrderCode());
        this.setSyncTradeStatus(WarehouseRecordConstant.NEED_SYNC_TRADE);
        //插入单据
        List<RecordDetailDTO> frontRecordDetails = dto.getDetailList();
        this.setWarehouseRecordDetails(new ArrayList<>(frontRecordDetails.size()));
        for(RecordDetailDTO detailDTO : frontRecordDetails){
            //数量为0的不写入明细
            if(BigDecimal.ZERO.compareTo(detailDTO.getBasicSkuQty()) == 0){
                continue;
            }
            WarehouseRecordDetail warehouseRecordDetail = entityFactory.createEntity(WarehouseRecordDetail.class);
            //根据前置单生成单据明细
            this.createRecordDetailByFrontRecord(warehouseRecordDetail,detailDTO);
            warehouseRecordDetail.setPlanQty(detailDTO.getBasicSkuQty());
            warehouseRecordDetail.setActualQty(BigDecimal.ZERO);
            warehouseRecordDetail.setDeliveryLineNo(detailDTO.getDeliveryLineNo());
            warehouseRecordDetail.setLineNo(detailDTO.getLineNo());
            this.warehouseRecordDetails.add(warehouseRecordDetail);
        }
        AlikAssert.isTrue(this.warehouseRecordDetails.size() > 0, ResCode.STOCK_ERROR_1029, "当前调拨的单所有物料库存不足");
        //设置skuCode和skuID
        this.initWarehouseRecodeDetail();
        long id = whAllocationWarehouseRepository.saveReturnWarehouseRecord(this);
        this.setId(id);
        this.warehouseRecordDetails.forEach(wrRecord -> wrRecord.setWarehouseRecordDetail(this));
        whAllocationWarehouseRepository.saveWarehouseRecordDetails(this.warehouseRecordDetails);
    }

    /**
     * 大仓出库单(中台调非中台)
     */
    public void whOutRecordNotZT(OutWarehouseRecordDTO dto){
        this.setRecordType(dto.getRecordType());
        this.setRecordCode(dto.getRecordCode());
        RealWarehouseE outWarehouse = realWarehouseRepository.getRealWarehouseByOutCodeAndFactoryCode(dto.getWarehouseCode(),dto.getFactoryCode());
        this.setRealWarehouseId(outWarehouse.getId());
        this.setBusinessType(WarehouseRecordBusinessTypeVO.OUT_WAREHOUSE_RECORD.getType());
        this.setRecordStatus(WarehouseRecordStatusVO.OUT_ALLOCATION.getStatus());
        this.setSyncWmsStatus(WmsSyncStatusVO.NO_REQUIRED.getStatus());
        this.setCmpStatus(WarehouseRecordConstant.INIT_CMP);
        this.setSapOrderCode(dto.getSapOrderCode());
        this.setSyncTransferStatus(WarehouseRecordConstant.NEED_TRANSFER);
        this.setSyncTradeStatus(WarehouseRecordConstant.INIT_SYNC_TRADE);
        //插入单据
        List<RecordDetailDTO> frontRecordDetails = dto.getDetailList();
        this.setWarehouseRecordDetails(new ArrayList<>(frontRecordDetails.size()));
        for(RecordDetailDTO detailDTO : frontRecordDetails){
            //数量为0的不写入明细
            if(BigDecimal.ZERO.compareTo(detailDTO.getBasicSkuQty()) == 0){
                continue;
            }
            WarehouseRecordDetail warehouseRecordDetail = entityFactory.createEntity(WarehouseRecordDetail.class);
            //根据前置单生成单据明细
            this.createRecordDetailByFrontRecord(warehouseRecordDetail,detailDTO);
            warehouseRecordDetail.setPlanQty(detailDTO.getBasicSkuQty());
            warehouseRecordDetail.setActualQty(detailDTO.getBasicSkuQty());
            warehouseRecordDetail.setDeliveryLineNo(detailDTO.getDeliveryLineNo());
            warehouseRecordDetail.setLineNo(detailDTO.getLineNo());
            this.warehouseRecordDetails.add(warehouseRecordDetail);
        }
        AlikAssert.isTrue(this.warehouseRecordDetails.size() > 0, ResCode.STOCK_ERROR_1029, "当前调拨的单所有物料库存不足");
        //设置skuCode和skuID
        this.initWarehouseRecodeDetail();
        long id = whAllocationWarehouseRepository.saveReturnWarehouseRecord(this);
        this.setId(id);
        this.warehouseRecordDetails.forEach(wrRecord -> wrRecord.setWarehouseRecordDetail(this));
        whAllocationWarehouseRepository.saveWarehouseRecordDetails(this.warehouseRecordDetails);
    }

    /**
     * 根据前置单生成单据明细
     */
    public void createRecordDetailByFrontRecord(WarehouseRecordDetail warehouseRecordDetail,RecordDetailDTO detailDTO){
        warehouseRecordDetail.setSkuCode(detailDTO.getSkuCode());
        //此处设置计划数量和实际数量一致，wms回调再更新实际数量，无需wms回调的业务，直接设置成一致即可
        warehouseRecordDetail.setPlanQty(detailDTO.getBasicSkuQty());
        warehouseRecordDetail.setUnit(detailDTO.getBasicUnit());
        warehouseRecordDetail.setUnitCode(detailDTO.getBasicUnitCode());
    }

    /**
     * 根据出库单生成大仓入库单
     */
    public void whInRecordByOutRecord(InWarehouseRecordDTO inWarehouseRecordDTO){
        this.setRecordCode(inWarehouseRecordDTO.getRecordCode());
        this.setRecordType(WarehouseRecordTypeVO.WH_ALLOCATION_IN_WAREHOUSE_RECORD.getType());
        RealWarehouseE inWarehouse = realWarehouseRepository.getRealWarehouseByOutCodeAndFactoryCode(inWarehouseRecordDTO.getWarehouseCode(),inWarehouseRecordDTO.getFactoryCode());
        this.setRealWarehouseId(inWarehouse.getId());
        this.setBusinessType(WarehouseRecordBusinessTypeVO.IN_WAREHOUSE_RECORD.getType());
        this.setRecordStatus(WarehouseRecordStatusVO.INIT.getStatus());
        //兼容订单中心假派车问题
        if(Objects.nonNull(inWarehouseRecordDTO.getSyncWmsStatus()) && Objects.equals(inWarehouseRecordDTO.getSyncWmsStatus(),WmsSyncStatusVO.UNSYNCHRONIZED.getStatus())){
            this.setSyncWmsStatus(WmsSyncStatusVO.UNSYNCHRONIZED.getStatus());
        }
        //sap_order_code使用中台后置单号
        this.setSapOrderCode(inWarehouseRecordDTO.getSapOrderCode());
        this.setSyncTradeStatus(WarehouseRecordConstant.INIT_SYNC_TRADE);
        //插入单据
        List<RecordDetailDTO> frontRecordDetails =inWarehouseRecordDTO.getDetailList();
        this.setWarehouseRecordDetails(new ArrayList<>(frontRecordDetails.size()));
        for(RecordDetailDTO detailDTO : frontRecordDetails){
            //数量为0的不写入明细
            if(BigDecimal.ZERO.compareTo(detailDTO.getBasicSkuQty()) == 0){
                continue;
            }
            WarehouseRecordDetail warehouseRecordDetail = entityFactory.createEntity(WarehouseRecordDetail.class);
            //根据前置单生成单据明细
            this.createRecordDetailByFrontRecord(warehouseRecordDetail,detailDTO);
            warehouseRecordDetail.setActualQty(BigDecimal.ZERO);
            warehouseRecordDetail.setSapPoNo(inWarehouseRecordDTO.getSapOrderCode());
            warehouseRecordDetail.setLineNo(detailDTO.getLineNo());
            warehouseRecordDetail.setDeliveryData(inWarehouseRecordDTO.getDeliveryDate());
            warehouseRecordDetail.setDeliveryLineNo(detailDTO.getDeliveryLineNo());
            warehouseRecordDetail.setSapPoNo(detailDTO.getSapPoNo());
            warehouseRecordDetail.setRealWarehouseId(inWarehouse.getId());
            this.warehouseRecordDetails.add(warehouseRecordDetail);
        }
        //设置skuCode和skuID
        this.initWarehouseRecodeDetail();
        long id = whAllocationWarehouseRepository.saveReturnWarehouseRecord(this);
        this.setId(id);
        this.warehouseRecordDetails.forEach(wrRecord -> wrRecord.setWarehouseRecordDetail(this));
        whAllocationWarehouseRepository.saveWarehouseRecordDetails(this.warehouseRecordDetails);
    }


    /**
     * 根据出库单生成大仓入库单(非中台调中台)
     */
    public void whInRecordByOutRecordZT(InWarehouseRecordDTO inWarehouseRecordDTO){
        this.setRecordCode(inWarehouseRecordDTO.getRecordCode());
        this.setRecordType(WarehouseRecordTypeVO.WH_ALLOCATION_IN_WAREHOUSE_RECORD.getType());
        RealWarehouseE inWarehouse = realWarehouseRepository.getRealWarehouseByOutCodeAndFactoryCode(inWarehouseRecordDTO.getWarehouseCode(),inWarehouseRecordDTO.getFactoryCode());
        this.setRealWarehouseId(inWarehouse.getId());
        this.setBusinessType(WarehouseRecordBusinessTypeVO.IN_WAREHOUSE_RECORD.getType());
        this.setRecordStatus(WarehouseRecordStatusVO.IN_ALLOCATION.getStatus());
        this.setSapOrderCode(inWarehouseRecordDTO.getSapOrderCode());
        this.setSyncTradeStatus(WarehouseRecordConstant.INIT_SYNC_TRADE);
        this.setSyncWmsStatus(WmsSyncStatusVO.NO_REQUIRED.getStatus());
        //插入单据
        List<RecordDetailDTO> frontRecordDetails =inWarehouseRecordDTO.getDetailList();
        this.setWarehouseRecordDetails(new ArrayList<>(frontRecordDetails.size()));
        for(RecordDetailDTO detailDTO : frontRecordDetails){
            //数量为0的不写入明细
            if(BigDecimal.ZERO.compareTo(detailDTO.getBasicSkuQty()) == 0){
                continue;
            }
            WarehouseRecordDetail warehouseRecordDetail = entityFactory.createEntity(WarehouseRecordDetail.class);
            //根据前置单生成单据明细
            this.createRecordDetailByFrontRecord(warehouseRecordDetail,detailDTO);
            //直接收货
            warehouseRecordDetail.setActualQty(detailDTO.getBasicSkuQty());
            warehouseRecordDetail.setSapPoNo(inWarehouseRecordDTO.getSapOrderCode());
            warehouseRecordDetail.setLineNo(detailDTO.getLineNo());
            warehouseRecordDetail.setDeliveryData(inWarehouseRecordDTO.getDeliveryDate());
            warehouseRecordDetail.setDeliveryLineNo(detailDTO.getDeliveryLineNo());
            warehouseRecordDetail.setSapPoNo(detailDTO.getSapPoNo());
            warehouseRecordDetail.setRealWarehouseId(inWarehouse.getId());
            this.warehouseRecordDetails.add(warehouseRecordDetail);
        }
        //设置skuCode和skuID
        this.initWarehouseRecodeDetail();
        long id = whAllocationWarehouseRepository.saveReturnWarehouseRecord(this);
        this.setId(id);
        this.warehouseRecordDetails.forEach(wrRecord -> wrRecord.setWarehouseRecordDetail(this));
        whAllocationWarehouseRepository.saveWarehouseRecordDetails(this.warehouseRecordDetails);
    }

    /**
     * 解锁库存,由下往上
     */
    public CoreRealStockOpDO packUnlockStockObjForDown(){
        CoreRealStockOpDO stockDO = new CoreRealStockOpDO();
        List<CoreRealStockOpDetailDO> detailDos = new ArrayList<>();
        stockDO.setRecordCode(this.getRecordCode());
        stockDO.setTransType(this.getRecordType());
        stockDO.setDetailDos(detailDos);
        List<CoreVirtualStockOpDO> virtualStockByCalculateDOs=new ArrayList<>();
        for (WarehouseRecordDetail detail : this.getWarehouseRecordDetails()) {
            CoreRealStockOpDetailDO detailDO = new CoreRealStockOpDetailDO();
            //部分锁定时，未完全锁定
            if (this.checkUnLockQty()) {
                detailDO.setUnlockQty(detail.getActualQty());
            } else {
                detailDO.setUnlockQty(detail.getPlanQty());
            }
            //数量为0的不处理库存的增加和减少
            if(BigDecimal.ZERO.compareTo(detailDO.getUnlockQty()) == 0){
                continue;
            }
            detailDO.setSkuId(detail.getSkuId());
            detailDO.setSkuCode(detail.getSkuCode());
            detailDO.setRealWarehouseId(this.getRealWarehouseId());
            detailDO.setChannelCode(detail.getChannelCode());
            detailDos.add(detailDO);
            if(null!=this.getVirtualWarehouseId()){
                CoreVirtualStockOpDO coreVirtualStockOpDO = new CoreVirtualStockOpDO();
                coreVirtualStockOpDO.setRecordCode(this.getRecordCode());
                coreVirtualStockOpDO.setTransType(this.getRecordType());
                coreVirtualStockOpDO.setSkuCode(detail.getSkuCode());
                coreVirtualStockOpDO.setSkuId(detail.getSkuId());
                coreVirtualStockOpDO.setUnlockQty(detailDO.getUnlockQty());
                coreVirtualStockOpDO.setVirtualWarehouseId(this.getVirtualWarehouseId());
                virtualStockByCalculateDOs.add(coreVirtualStockOpDO);
            }
        }
        if(!CollectionUtils.isEmpty(virtualStockByCalculateDOs)){
            stockDO.setVirtualStockByCalculateDOs(virtualStockByCalculateDOs);
        }
        return stockDO;
    }

    /**
     * 封装取消减少在途库存
     */
    public CoreRealStockOpDO packUnlockOnRoadStock(){
        List<CoreRealStockOpDetailDO> detailDos = new ArrayList<>();
        for (WarehouseRecordDetail detail : this.getWarehouseRecordDetails()) {
            //实收数量为0的不处理在途的增加和减少
            if(BigDecimal.ZERO.compareTo(detail.getPlanQty()) == 0){
                continue;
            }
            CoreRealStockOpDetailDO detailDO = new CoreRealStockOpDetailDO();
            detailDO.setOnroadQty(detail.getPlanQty());
            detailDO.setSkuId(detail.getSkuId());
            detailDO.setSkuCode(detail.getSkuCode());
            detailDO.setRealWarehouseId(this.getRealWarehouseId());
            detailDO.setChannelCode(detail.getChannelCode());
            detailDos.add(detailDO);
        }
        CoreRealStockOpDO stockDO = new CoreRealStockOpDO();
        stockDO.setRecordCode(this.getRecordCode());
        stockDO.setTransType(this.getRecordType());
        stockDO.setDetailDos(detailDos);
        return stockDO;
    }

    /**
     * SAP派车状态：0-无需派车 1-待下发派车 2-已下发派车
     */
    private Integer syncDispatchStatus;


    /**
     * 指定虚仓锁库存构建参数对象
     * @param outWarehouseRecordDTO 出库对象
     * @param vmId 虚仓id
     * @param rwId 实仓id
     * @return  构建锁库存对象
     */
    public CoreRealStockOpDO initLockStockWithVm(WhAllocationWarehouseRecordE outWarehouseRecordDTO,Long vmId,Long rwId) {
        List<CoreVirtualStockOpDO>  cvsList = new ArrayList<>();
        List<CoreRealStockOpDetailDO> increaseDetails = new ArrayList<>();
        for (WarehouseRecordDetail detail : outWarehouseRecordDTO.getWarehouseRecordDetails()) {
            if(BigDecimal.ZERO.compareTo(detail.getPlanQty()) == 0){
                continue;
            }
            CoreRealStockOpDetailDO detailDO = new CoreRealStockOpDetailDO();
            detailDO.setLockQty(detail.getPlanQty());
            detailDO.setSkuId(detail.getSkuId());
            detailDO.setRealWarehouseId(rwId);
            detailDO.setSkuCode(detail.getSkuCode());
            increaseDetails.add(detailDO);

            CoreVirtualStockOpDO coreVirtualStockDO = new CoreVirtualStockOpDO();
            coreVirtualStockDO.setLockQty(detail.getPlanQty());
            coreVirtualStockDO.setVirtualWarehouseId(vmId);
            coreVirtualStockDO.setRealWarehouseId(rwId);
            coreVirtualStockDO.setRecordCode(outWarehouseRecordDTO.getRecordCode());
            coreVirtualStockDO.setTransType(outWarehouseRecordDTO.getRecordType());
            coreVirtualStockDO.setSkuId(detail.getSkuId());
            coreVirtualStockDO.setSkuCode(detail.getSkuCode());
            cvsList.add(coreVirtualStockDO);

        }
        CoreRealStockOpDO coreRealStockOpDO = new CoreRealStockOpDO();
        coreRealStockOpDO.setRecordCode(outWarehouseRecordDTO.getRecordCode());
        coreRealStockOpDO.setTransType(outWarehouseRecordDTO.getRecordType());
        coreRealStockOpDO.setVirtualStockByCalculateDOs(cvsList);
        coreRealStockOpDO.setDetailDos(increaseDetails);
        coreRealStockOpDO.setCalculateVirtualStockFlag(false);
        return coreRealStockOpDO;
    }

    /**
     * 根据虚仓和渠道去锁定库存
     * @param vmId
     * @param channelCode
     * @return
     */
    public CoreChannelOrderDO initLockStockObjWithVm(Long vmId,String channelCode) {
        List<CoreVirtualStockOpDO>  cvsList = new ArrayList<>();
        List<CoreOrderDetailDO> increaseDetails = new ArrayList<>();
        for (WarehouseRecordDetail detail : this.getWarehouseRecordDetails()) {
            CoreOrderDetailDO detailDO = new CoreOrderDetailDO();
            detailDO.setLockQty(detail.getPlanQty());
            detailDO.setRealQty(detail.getActualQty());
            detailDO.setSkuId(detail.getSkuId());
            detailDO.setRealWarehouseId(this.getRealWarehouseId());
            detailDO.setChannelCode(channelCode);
            detailDO.setCheckBeforeOp(false);
            detailDO.setSkuCode(detail.getSkuCode());
            CoreVirtualStockOpDO coreVirtualStockDO = new CoreVirtualStockOpDO();
            coreVirtualStockDO.setLockQty(detail.getPlanQty());
            coreVirtualStockDO.setVirtualWarehouseId(vmId);
            coreVirtualStockDO.setRealWarehouseId(this.getRealWarehouseId());
            coreVirtualStockDO.setRecordCode(this.getRecordCode());
            coreVirtualStockDO.setTransType(this.getRecordType());
            coreVirtualStockDO.setChannelCode(channelCode);
            coreVirtualStockDO.setMerchantId(detail.getMerchantId());
            coreVirtualStockDO.setSkuId(detail.getSkuId());
            coreVirtualStockDO.setSkuCode(detail.getSkuCode());
            cvsList.add(coreVirtualStockDO);
            increaseDetails.add(detailDO);
        }
        CoreChannelOrderDO coreChannelOrderDO = new CoreChannelOrderDO();
        coreChannelOrderDO.setRecordCode(this.getRecordCode());
        coreChannelOrderDO.setTransType(this.getRecordType());
        coreChannelOrderDO.setOrderDetailDOs(increaseDetails);
        coreChannelOrderDO.setVirtualStockOpDetailDOs(cvsList);
        coreChannelOrderDO.setChannelCode(channelCode);
        return coreChannelOrderDO;
    }
}
