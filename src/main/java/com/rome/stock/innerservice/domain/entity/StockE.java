package com.rome.stock.innerservice.domain.entity;

import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Component;

import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 类StockValue的实现描述：库存模型
 *
 * <AUTHOR> 2019/4/17 16:28
 */
@Component
@Scope("prototype")
@Data
@EqualsAndHashCode(callSuper = false)
public class StockE {

    /**
     * 仓库ID
     */
    private Long relationId;
    /**
     * 商品sku编码
     */
    private Long skuId;

    /**
     * 不可用库存
     */
    private Long unUseQty;

    /**
     * 可用库存
     */
    private Long availableQty;



}
