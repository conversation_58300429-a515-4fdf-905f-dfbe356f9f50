package com.rome.stock.innerservice.domain.service.impl;

import java.math.BigDecimal;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.Function;
import java.util.stream.Collectors;

import javax.annotation.Resource;

import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.alibaba.fastjson.JSON;
import com.rome.arch.core.domain.EntityFactory;
import com.rome.arch.core.exception.RomeException;
import com.rome.stock.common.constants.WmsConfigConstants;
import com.rome.stock.common.constants.warehouse.WarehouseRecordConstant;
import com.rome.stock.common.enums.warehouse.WarehouseRecordTypeVO;
import com.rome.stock.innerservice.api.dto.groupbuy.ReservationDTO;
import com.rome.stock.innerservice.api.dto.warehouserecord.CancelRecordDTO;
import com.rome.stock.innerservice.api.dto.warehouserecord.OutWarehouseRecordDTO;
import com.rome.stock.innerservice.common.AlikAssert;
import com.rome.stock.innerservice.common.ResCode;
import com.rome.stock.innerservice.constant.FrontRecordStatusVO;
import com.rome.stock.innerservice.constant.FrontRecordTypeVO;
import com.rome.stock.innerservice.constant.ReservationAssignVo;
import com.rome.stock.innerservice.constant.ReservationBussTypeVo;
import com.rome.stock.innerservice.constant.WarehouseRecordBusinessTypeVO;
import com.rome.stock.innerservice.constant.WarehouseRecordStatusVO;
import com.rome.stock.innerservice.constant.WmsSyncStatusVO;
import com.rome.stock.innerservice.domain.convertor.frontrecord.FrReservationConvertor;
import com.rome.stock.innerservice.domain.entity.RealWarehouseE;
import com.rome.stock.innerservice.domain.entity.VirtualWarehouseE;
import com.rome.stock.innerservice.domain.entity.frontrecord.ReservationRecordE;
import com.rome.stock.innerservice.domain.entity.warehouserecord.ReservationWarehouseRecordE;
import com.rome.stock.innerservice.domain.entity.warehouserecord.WarehouseRecordE;
import com.rome.stock.core.domain.repository.CoreChannelSalesRepository;
import com.rome.stock.core.domain.repository.CoreRealWarehouseStockRepository;
import com.rome.stock.innerservice.domain.repository.RealWarehouseRepository;
import com.rome.stock.innerservice.domain.repository.frontrecord.FrReservationRepository;
import com.rome.stock.innerservice.domain.repository.warehouserecord.WarehouseRecordRepository;
import com.rome.stock.innerservice.domain.service.ReservationService;
import com.rome.stock.innerservice.domain.service.VirtualWarehouseService;
import com.rome.stock.innerservice.domain.service.WmsOutService;
import com.rome.stock.core.facade.RedisRollBackFacade;
import com.rome.stock.innerservice.infrastructure.dataobject.RealWarehouseWmsConfigDO;
import com.rome.stock.core.infrastructure.dataobject.core.CoreChannelOrderDO;
import com.rome.stock.core.infrastructure.dataobject.core.CoreRealStockOpDO;
import com.rome.stock.core.infrastructure.dataobject.core.CoreVirtualStockOpDO;
import com.rome.stock.innerservice.infrastructure.redis.RealWarehouseWmsRedis;

import cn.hutool.core.collection.CollectionUtil;
import lombok.extern.slf4j.Slf4j;

@Service
@Slf4j
public class ReservationServiceImpl implements ReservationService {

    @Resource
    private EntityFactory entityFactory;

    @Resource
    private VirtualWarehouseService virtualWarehouseService;

    @Resource
    private FrReservationRepository reservationRepository;

    @Resource
    private CoreChannelSalesRepository coreChannelSalesRepository;

    @Resource
    private FrReservationConvertor frReservationConvertor;

    @Resource
    private RealWarehouseRepository realWarehouseRepository;

    @Resource
    private CoreRealWarehouseStockRepository coreRealWarehouseStockRepository;

    @Resource
    private WarehouseRecordRepository warehouseRecordRepository;

    @Resource
    private RealWarehouseWmsRedis realWarehouseWmsRedis;

    @Resource
    private WmsOutService wmsOutService;

    
    @Override
    @Transactional(rollbackFor = Exception.class)
    public ReservationDTO addReservation(ReservationDTO reservationDTO) {
        log.info("开始创建预约单参数{}",JSON.toJSONString(reservationDTO));
        //参数校验
        this.validateParam(reservationDTO);
        //根据渠道查询实仓和虚仓信息
        this.getVirtualWareHouseInfo(reservationDTO);
        //构建领域对象
        ReservationRecordE reservationRecordE =frReservationConvertor.dtoToEntity(reservationDTO);
        //执行新增任务
        reservationRecordE.addReservation();
        //入库后置单
        ReservationWarehouseRecordE warehouseRecordE = this.createOrBuildWarehouseRecord(reservationRecordE,ReservationBussTypeVo.CREATE.getType());
        //锁定库存
        CoreChannelOrderDO coreChannelOrderDO= this.maxAbleLockStock(warehouseRecordE);
        boolean res=false;
        try{
            //构建差异明细信息
            reservationRecordE = this.buildDifferenceDTO(reservationRecordE, coreChannelOrderDO);
            //执行更新业务
            reservationRecordE.updateReservation();
            res=true;
        }catch (Exception e){
            log.error("创建预约单异常:",e);
            throw e;
        }finally {
            if(!res){
                RedisRollBackFacade.redisRollBack(coreChannelOrderDO);
            }
        }

        //构建返回参数信息
        return frReservationConvertor.entityToDTO(reservationRecordE);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public ReservationDTO lockReservation(String outRecordCode) {
        log.info("开始锁定预约单,outRecordCode={}",outRecordCode);
        ReservationRecordE reservationRecordE = entityFactory.createEntity(ReservationRecordE.class);
        reservationRecordE.setOutRecordCode(outRecordCode);
        reservationRecordE = reservationRecordE.queryReservationByCode();
        if(Objects.isNull(reservationRecordE)){
            log.error("预约单不存在:{}",outRecordCode);
            return new ReservationDTO();
        }
        if(Objects.nonNull(reservationRecordE) && Objects.equals(reservationRecordE.getIsAssigned(),ReservationAssignVo.UN_ASSIGN.getType())){
            return frReservationConvertor.entityToDTO(reservationRecordE);
        }
        if(Objects.nonNull(reservationRecordE) && !Objects.equals(reservationRecordE.getIsAssigned(),ReservationAssignVo.ASSIGN.getType()) ){
            throw new RomeException(ResCode.STOCK_ERROR_1001,"单据状态不是未完全锁定!");
        }
        //构建后置单
        ReservationWarehouseRecordE warehouseRecordE = createOrBuildWarehouseRecord(reservationRecordE,ReservationBussTypeVo.LOCK.getType());
        warehouseRecordE.setRecordCode(reservationRecordE.getRecordCode());
        //锁定库存
        CoreChannelOrderDO coreChannelOrderDO= maxAbleLockStock(warehouseRecordE);
        boolean res=false;
        try{
            //构建差异明细信息
            reservationRecordE = buildDifferenceDTO(reservationRecordE, coreChannelOrderDO);
            //执行更新业务
            reservationRecordE.updateReservation();
            res=true;
        }catch (Exception e){
           log.error("锁定预约单异常:",e);
           throw  e;
        }finally {
            if(!res){
                RedisRollBackFacade.redisRollBack(coreChannelOrderDO);
            }
        }
        //构建返回参数信息
        return frReservationConvertor.entityToDTO(reservationRecordE);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void cancelReservation(String outRecordCode) {
        //根据外部单号查询前置单
        ReservationRecordE reservationRecordE = entityFactory.createEntity(ReservationRecordE.class);
        reservationRecordE.setOutRecordCode(outRecordCode);
        reservationRecordE = reservationRecordE.queryReservationByCode();
        if(Objects.isNull(reservationRecordE)){
            throw new RomeException(ResCode.STOCK_ERROR_1017, "预约单不存在:outRecordCode="+outRecordCode);
        }
        //已取消的不能再次取消
        if(Objects.equals(reservationRecordE.getRecordStatus(), FrontRecordStatusVO.DISABLED.getStatus())){
            log.warn("预约单已被取消:{}",outRecordCode);
            return;
        }
        //只能取消部分匹配和全部匹配的数据
        List<Integer> status = Arrays.asList(FrontRecordStatusVO.SO_PREDICT_RETURN_PART_MATCH.getStatus(), FrontRecordStatusVO.SO_PREDICT_RETURN_FULL_MATCH.getStatus());
        if(!status.contains(reservationRecordE.getRecordStatus())){
            throw new RomeException(ResCode.STOCK_ERROR_9064,ResCode.STOCK_ERROR_9064_DESC+"outRecordCode="+outRecordCode);
        }
        reservationRecordE.setRecordStatus(FrontRecordStatusVO.DISABLED.getStatus());
        //更新前置单状态为取消
        reservationRecordE.updateReservationStatus();
        ReservationWarehouseRecordE resOutRecordE = buildCancelResOutRecordE(reservationRecordE);
        resOutRecordE.setFrontRecordId(reservationRecordE.getId());
        resOutRecordE.setFrontRecordType(FrontRecordTypeVO.GROUP_PURCHASE_RESERVATION_RECORD.getType());
        List<ReservationWarehouseRecordE> reservationWarehouseRecordES = resOutRecordE.queryReservationEByFrontCode();
        AlikAssert.isNotEmpty(reservationWarehouseRecordES,ResCode.STOCK_ERROR_1002_DESC,"后置单不存在");
        resOutRecordE.setRecordCode(reservationWarehouseRecordES.get(0).getRecordCode());
        //更新后置单状态为取消
        resOutRecordE.updateCancelStatus();
        //释放库存
        this.unLockStock(reservationRecordE, resOutRecordE);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public String addReservationDo(OutWarehouseRecordDTO outWarehouseRecordDTO) {
        log.info("开始创建do出库单:{}",JSON.toJSONString(outWarehouseRecordDTO));
        //幂等性判断
        WarehouseRecordE recordE = warehouseRecordRepository.queryWarehouseRecordByRecordCode(outWarehouseRecordDTO.getRecordCode());
        if(Objects.nonNull(recordE)){
            return recordE.getRecordCode();
        }
        //根据传入的warehouseCode和FactoryCode 查实仓信息
        RealWarehouseE realWarehouse = realWarehouseRepository.getRealWarehouseByOutCodeAndFactoryCode(outWarehouseRecordDTO.getWarehouseCode(), outWarehouseRecordDTO.getFactoryCode());
        AlikAssert.isNotNull(realWarehouse,ResCode.STOCK_ERROR_1002_DESC,"仓库外部编码="+outWarehouseRecordDTO.getWarehouseCode()+"的实仓不存在");
        ReservationWarehouseRecordE outWarehouseRecord=entityFactory.createEntity(ReservationWarehouseRecordE.class);
        outWarehouseRecord.createDoRecord(outWarehouseRecordDTO,realWarehouse.getId());
        //创建后置单
        outWarehouseRecord.createRWOutRecordForRes();
        //更新捋单系统状态
        outWarehouseRecord.updateSyncTradeToBeStatus();
        //更新前置单状态
        outWarehouseRecord.setRecordStatus(FrontRecordStatusVO.WAITTING_DELIVERY.getStatus());
        reservationRepository.updateReservationByOutCode(outWarehouseRecordDTO.getOutRecordCode(),FrontRecordStatusVO.WAITTING_DELIVERY.getStatus());
        //锁定库存
        this.lock(outWarehouseRecord,realWarehouse.getId());
        return outWarehouseRecord.getRecordCode();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void cancelGroupBuyDo(CancelRecordDTO cancelRecordDTO) {
        log.info("开始取消团购do单:{}",cancelRecordDTO.getRecordCode());
        ReservationWarehouseRecordE resOutRecordE = entityFactory.createEntity(ReservationWarehouseRecordE.class);
        resOutRecordE.setRecordType(WarehouseRecordTypeVO.RESERVATION_DO_RECORD.getType());
        resOutRecordE.setRecordCode(cancelRecordDTO.getRecordCode());
        //根据do单号查询后置单
        resOutRecordE = resOutRecordE.getReservationWarehouseEByRecordCode();
        if(Objects.isNull(resOutRecordE)){
            throw new RomeException(ResCode.STOCK_ERROR_1017, "单据不存在:-100");
        }
        //已取消的不能再次取消
        if(Objects.equals(resOutRecordE.getRecordStatus(), FrontRecordStatusVO.DISABLED.getStatus())){
            return;
        }
        resOutRecordE.setRecordStatus(WarehouseRecordStatusVO.DISABLED.getStatus());
        //更新后置单状态为取消状态
        resOutRecordE.updateWarehouseDoStatus();
        //根据后置单查询前置单
        Long fontRecordId = resOutRecordE.getFrontIdList();
        if(Objects.nonNull(fontRecordId)){
            ReservationRecordE reservationRecordE = entityFactory.createEntity(ReservationRecordE.class);
            reservationRecordE.setId(fontRecordId);
            reservationRecordE=reservationRecordE.queryReservationById();
            reservationRecordE.setRecordStatus(FrontRecordStatusVO.DISABLED.getStatus());
            //更新前置单状态为取消
            reservationRecordE.updateReservationStatus();
        }
        //如果是强制取消就要取消wms
        if (cancelRecordDTO.getIsForceCancel()  && WarehouseRecordConstant.SYNC_WMS__SUCCES.equals(resOutRecordE.getSyncWmsStatus())) {
            //强制取消需要取消WMS
            RealWarehouseWmsConfigDO realWarehouseWmsConfigDO = realWarehouseWmsRedis.findWmsInformationById(resOutRecordE.getRealWarehouseId());
            if (realWarehouseWmsConfigDO != null) {//大福旺店通邮政,科捷仓库需要取消仓库的单子
                if (realWarehouseWmsConfigDO.getWmsCode() == WmsConfigConstants.ZTO_YC
                        || realWarehouseWmsConfigDO.getWmsCode() == WmsConfigConstants.WMS_DF
                        || realWarehouseWmsConfigDO.getWmsCode() == WmsConfigConstants.WMS_ODY
                        || realWarehouseWmsConfigDO.getWmsCode() == WmsConfigConstants.WMS_KJ
                        || realWarehouseWmsConfigDO.getWmsCode() == WmsConfigConstants.WMS_EMS
                        || realWarehouseWmsConfigDO.getWmsCode() == WmsConfigConstants.WMS_WDT
                        || realWarehouseWmsConfigDO.getWmsCode() == WmsConfigConstants.WMS_MM
                        || realWarehouseWmsConfigDO.getWmsCode() == WmsConfigConstants.WMS_ODYC) {
                    boolean cancleStatus = wmsOutService.orderCancel(resOutRecordE.getRecordCode());
                    AlikAssert.isTrue(cancleStatus, ResCode.STOCK_ERROR_1205, ResCode.STOCK_ERROR_1205_DESC);
                }
            }
        }
        //释放锁定的库存
        this.unLockStock(resOutRecordE);
    }

    @Override
    public Integer checkDoStatus(String recordCode) {
        WarehouseRecordE warehouseRecordE = warehouseRecordRepository.queryWarehouseRecordByRecordCode(recordCode);
        if(Objects.nonNull(warehouseRecordE) && WarehouseRecordStatusVO.OUT_ALLOCATION.getStatus().equals(warehouseRecordE.getRecordStatus())){
            return 0;
        }
        return 1;
    }


    /**
     * 锁定库存
     * @param resOutRecordE
     * @param rwId
     */
    private void lock(ReservationWarehouseRecordE resOutRecordE,Long rwId){
        CoreRealStockOpDO coreChannelOrderDO=null;
        boolean isSuccess=false;
        try{
            coreChannelOrderDO= resOutRecordE.initLockStockObj(rwId);
            log.info("开始锁定库存:{}",JSON.toJSONString(coreChannelOrderDO));
            CoreRealStockOpDO coreRealStockOpDO = coreRealWarehouseStockRepository.lockStock(coreChannelOrderDO);
            log.info("锁定库存完成:{}",JSON.toJSONString(coreRealStockOpDO));
            isSuccess=true;
        }catch (RomeException ex){
            log.error(ex.getMessage(), ex);
            throw new RomeException(ex.getCode(), ex.getMessage());
        }catch (Exception ex){
            log.error(ex.getMessage(), ex);
            throw new RomeException(ResCode.STOCK_ERROR_9062, ResCode.STOCK_ERROR_9062_DESC + ex.getMessage());
        }finally {
            if(!isSuccess){
                RedisRollBackFacade.redisRollBack(coreChannelOrderDO);
            }
        }
    }


    /**
     * 构建取消预约单后置单对象
     * @param reservationRecordE
     * @return
     */
    private ReservationWarehouseRecordE buildCancelResOutRecordE(ReservationRecordE reservationRecordE) {
        ReservationWarehouseRecordE resOutRecordE = entityFactory.createEntity(ReservationWarehouseRecordE.class);
        resOutRecordE.createOutRecordByFontRecord(reservationRecordE,ReservationBussTypeVo.CANCEL.getType());
        resOutRecordE.setFrontRecordId(reservationRecordE.getId());
        resOutRecordE.setRecordType(WarehouseRecordTypeVO.GROUP_PURCHASE_OUT_RECORD.getType());
        resOutRecordE.setFrontRecordType(FrontRecordTypeVO.GROUP_PURCHASE_RESERVATION_RECORD.getType());
        resOutRecordE.setBusinessType(WarehouseRecordBusinessTypeVO.OUT_WAREHOUSE_RECORD.getType());
        resOutRecordE.setRecordStatus(WarehouseRecordStatusVO.DISABLED.getStatus());
        return resOutRecordE;
    }


    /**
     * 释放do单锁定库存
     * @param resOutRecordE
     */
    private void unLockStock(ReservationWarehouseRecordE resOutRecordE){
        CoreRealStockOpDO coreChannelOrderDO=null;
        boolean isSuccess=false;
        try{
            coreChannelOrderDO = resOutRecordE.initUnlockStock();
            log.info("开始释放库存：{}",JSON.toJSONString(coreChannelOrderDO));
            if(CollectionUtil.isNotEmpty(coreChannelOrderDO.getDetailDos())){
                CoreRealStockOpDO coreChannelRes = coreRealWarehouseStockRepository.unlockStock(coreChannelOrderDO);
                log.info("释放库存返回：{}",JSON.toJSONString(coreChannelRes));
            }
            isSuccess=true;
        }catch (RomeException e){
            log.error(e.getMessage(), e);
            throw new RomeException(e.getCode(), e.getMessage());
        }catch (Exception e){
            log.error(e.getMessage(), e);
            throw new RomeException(ResCode.STOCK_ERROR_9062, ResCode.STOCK_ERROR_9062_DESC+ e.getMessage());
        }finally {
            if(!isSuccess){
                RedisRollBackFacade.redisRollBack(coreChannelOrderDO);
            }
        }

    }

    /**
     * 释放库存
     * @param reservationRecordE
     * @param resOutRecordE
     */
    private void unLockStock(ReservationRecordE reservationRecordE, ReservationWarehouseRecordE resOutRecordE) {
        CoreChannelOrderDO coreChannelOrderDO=null;
        boolean isSuccess=false;
        try{
            coreChannelOrderDO= resOutRecordE.initResUnlockStockWithVm(reservationRecordE);
            log.info("开始释放库存：{}",JSON.toJSONString(coreChannelOrderDO));
            if(CollectionUtil.isNotEmpty(coreChannelOrderDO.getOrderDetailDOs()) &&CollectionUtil.isNotEmpty(coreChannelOrderDO.getVirtualStockOpDetailDOs())){
                CoreChannelOrderDO coreChannelRes = coreChannelSalesRepository.unlockStock(coreChannelOrderDO);
                log.info("释放库存返回：{}",JSON.toJSONString(coreChannelRes));
            }
            isSuccess=true;
        }catch (RomeException ex){
            log.error(ex.getMessage(), ex);
            throw new RomeException(ex.getCode(), ex.getMessage());
        }catch (Exception ex){
            log.error(ex.getMessage(), ex);
            throw new RomeException(ResCode.STOCK_ERROR_9062, ResCode.STOCK_ERROR_9062_DESC+ ex.getMessage());
        }finally {
            if(!isSuccess){
                RedisRollBackFacade.redisRollBack(coreChannelOrderDO);
            }
        }
    }


    /**
     * 根据锁定信息比较差异信息
     * @param reservationRecordE
     * @param coreChannelOrderDO
     * @return
     */
    public ReservationRecordE buildDifferenceDTO(ReservationRecordE reservationRecordE,CoreChannelOrderDO coreChannelOrderDO){
        if(Objects.nonNull(reservationRecordE) && Objects.nonNull(coreChannelOrderDO)){
            Map<Long, CoreVirtualStockOpDO> coreChannelMap = coreChannelOrderDO.getVirtualStockOpDetailDOs().stream().collect(Collectors.toMap(x -> x.getSkuId(), Function.identity(),(v1,v2)->v2));
            reservationRecordE.getReservationDetails().stream().collect(Collectors.toMap(x -> x.getSkuId(), Function.identity(),(v1,v2)->v2))
                .forEach((k,v)-> {
                    CoreVirtualStockOpDO coreOrderDetailDO = coreChannelMap.get(k);
                    if (Objects.nonNull(coreOrderDetailDO)) {
                        //实际数量大于锁定数量 说明库存不足
                        if (v.getSkuQty().compareTo(coreOrderDetailDO.getLockQty().add(v.getAssignedQty())) > 0) {
                            //未锁定数量=总数量-已锁定数量
                            v.setUnassignedQty(v.getSkuQty().subtract((coreOrderDetailDO.getLockQty().add(v.getAssignedQty()))));
                        }else{
                            v.setUnassignedQty(BigDecimal.ZERO);
                        }
                        v.setAssignedQty(coreOrderDetailDO.getLockQty().add(v.getAssignedQty()));
                    }else{
                        if(v.getAssignedQty().compareTo(v.getSkuQty())==0){
                            v.setUnassignedQty(BigDecimal.ZERO);
                        }else if(v.getAssignedQty().compareTo(v.getSkuQty())<0 && v.getAssignedQty().compareTo(BigDecimal.ZERO)>0){
                            v.setUnassignedQty(v.getUnassignedQty());
                        } else{
                            v.setUnassignedQty(v.getSkuQty());
                    }
                    }
                });
            boolean isAssigned = reservationRecordE.getReservationDetails().stream().anyMatch(x -> x.getUnassignedQty().compareTo(BigDecimal.ZERO) > 0);
            if(isAssigned){
                //设置可分配状态 部分锁定
                reservationRecordE.setIsAssigned(ReservationAssignVo.ASSIGN.getType());
                reservationRecordE.setRecordStatus(FrontRecordStatusVO.SO_PREDICT_RETURN_PART_MATCH.getStatus());
            }else{
                //不可分配 全部锁定
                reservationRecordE.setIsAssigned(ReservationAssignVo.UN_ASSIGN.getType());
                reservationRecordE.setRecordStatus(FrontRecordStatusVO.SO_PREDICT_RETURN_FULL_MATCH.getStatus());
            }
        }else{
            //全部未锁定情况下 更新未分配的数量
            reservationRecordE.getReservationDetails().forEach(x->{
                if(x.getAssignedQty().compareTo(x.getSkuQty())==0){
                    x.setUnassignedQty(BigDecimal.ZERO);
                }else if(x.getAssignedQty().compareTo(x.getSkuQty())<0 && x.getAssignedQty().compareTo(BigDecimal.ZERO)>0){
                    x.setUnassignedQty(x.getUnassignedQty());
                } else{
                    x.setUnassignedQty(x.getSkuQty());
        }
            });
        }
        return reservationRecordE;
    }



    /**
     * 创建大仓出库单
     * @param reservationRecordE
     */
    private ReservationWarehouseRecordE createOrBuildWarehouseRecord(ReservationRecordE reservationRecordE,Integer type){
        ReservationWarehouseRecordE resOutRecordE = entityFactory.createEntity(ReservationWarehouseRecordE.class);
        resOutRecordE.createOutRecordByFontRecord(reservationRecordE,type);
        resOutRecordE.setRecordType(FrontRecordTypeVO.GROUP_PURCHASE_RESERVATION_RECORD.getType());
        resOutRecordE.setBusinessType(WarehouseRecordBusinessTypeVO.OUT_WAREHOUSE_RECORD.getType());
        resOutRecordE.setRecordStatus(WarehouseRecordStatusVO.INIT.getStatus());
        if(Objects.equals(type,ReservationBussTypeVo.CREATE.getType())){
            resOutRecordE.createRWOutRecordForRes();
        }
        return resOutRecordE;
    }




    /**
     * 锁定库存
     * @param reservationWarehouseRecordE
     * @return
     */
    private CoreChannelOrderDO maxAbleLockStock(ReservationWarehouseRecordE reservationWarehouseRecordE){
        CoreChannelOrderDO coreChannelOrderDO=null;
        try{
            coreChannelOrderDO=reservationWarehouseRecordE.buildCoreStockChannelOrderDo();
            if(Objects.nonNull(coreChannelOrderDO) && CollectionUtils.isNotEmpty(coreChannelOrderDO.getVirtualStockOpDetailDOs())){
            coreChannelOrderDO=coreChannelSalesRepository.maxableLockStock(coreChannelOrderDO);
            }
            return coreChannelOrderDO;
        }catch (RomeException e){
            log.error(e.getMessage(), e);
            throw new RomeException(e.getCode(), e.getMessage());
        }catch (Exception e){
            log.error(e.getMessage(), e);
            throw new RomeException(ResCode.STOCK_ERROR_9050, ResCode.STOCK_ERROR_9050_DESC);
        }
    }

    /**
     * 根据渠道查询虚仓和实仓信息
     * @param reservationDTO
     */
    private void getVirtualWareHouseInfo(ReservationDTO reservationDTO) {
        List<VirtualWarehouseE> vwListByChannelCode = virtualWarehouseService.getVwListByChannelCode(reservationDTO.getChannelCode());
        if(CollectionUtils.isEmpty(vwListByChannelCode)){
            throw new RomeException(ResCode.STOCK_ERROR_9054,ResCode.STOCK_ERROR_9054_DESC);
        }
        //默认取一个虚仓
        reservationDTO.setVirtualWarehouseId(vwListByChannelCode.get(0).getId());
        reservationDTO.setVirtualWarehouseCode(vwListByChannelCode.get(0).getVirtualWarehouseCode());
        reservationDTO.setVirtualFactoryCode(vwListByChannelCode.get(0).getFactoryCode());
        //根据实仓id查询实仓信息
        RealWarehouseE realWarehouseE = realWarehouseRepository.getRealWarehouseById(vwListByChannelCode.get(0).getRealWarehouseId());
        if(Objects.isNull(realWarehouseE)){
            throw new RomeException(ResCode.STOCK_ERROR_9055,ResCode.STOCK_ERROR_9055_DESC);
        }
        reservationDTO.setRealWarehouseId(realWarehouseE.getId());
        reservationDTO.setRealWarehouseCode(realWarehouseE.getRealWarehouseOutCode());
        reservationDTO.setRealFactoryCode(realWarehouseE.getFactoryCode());
    }


    /**
     * 参数校验
     * @param reservationDTO
     */
    private void validateParam(ReservationDTO reservationDTO){
        if(Objects.isNull(reservationDTO)){
            throw new RomeException(ResCode.STOCK_ERROR_9056,ResCode.STOCK_ERROR_9053_DESC);
        }
        ReservationRecordE reservationRecordE = reservationRepository.queryFrReservationByOutRecordCode(reservationDTO.getOutRecordCode());
        //幂等校验
        if(Objects.nonNull(reservationRecordE)){
            throw new RomeException(ResCode.STOCK_ERROR_9052,ResCode.STOCK_ERROR_9052_DESC);
        }
    }


}
