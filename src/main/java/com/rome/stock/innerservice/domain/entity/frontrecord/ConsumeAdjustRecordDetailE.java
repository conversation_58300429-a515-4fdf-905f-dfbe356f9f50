package com.rome.stock.innerservice.domain.entity.frontrecord;

import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Component;

/**
 * @Description
 * <AUTHOR>
 * @Date 2019/5/11 10:27
 * @Version 1.0
 */
@Component
@Scope("prototype")
@Data
@EqualsAndHashCode(callSuper = false)
public class ConsumeAdjustRecordDetailE extends AbstractFrontRecordDetail{

    /**
     *  调整单单号
     */
    private Long frontRecordId;

    /**
     * 商品skuId
     */
    private Long skuId;

    /**
     * 商品skuCode
     */
    private String skuCode;

    /**
     * 调整单位名称
     */
    private String unit;

    /**
     * 调整单位编号
     */
    private String unitCode;

    /**
     * 批次备注
     */
    private String remark;

}
