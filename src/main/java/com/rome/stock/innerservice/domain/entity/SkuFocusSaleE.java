package com.rome.stock.innerservice.domain.entity;

import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Component;

import java.util.List;

@Component
@Scope("prototype")
@Data
@EqualsAndHashCode(callSuper = false)
public class SkuFocusSaleE extends BaseE {
	/**
	 * 单据子表id
	 */
	private Long id;
	/**
	 * 实仓id
	 */
	private Long realWarehouseId;
	/**
	 * 实仓名称
	 */
	private String realWarehouseName;
	/**
	 * 实仓编码
	 */
	private String realWarehouseCode;
	/**
	 * SkuId
	 */
	private Long skuId;
	/**
	 * SkuIds
	 */
	private List<Long> skuIds;
	/**
	 * sku编码
	 */
	private String skuCode;
	/**
	 * sku名称
	 */
	private String skuName;
}



