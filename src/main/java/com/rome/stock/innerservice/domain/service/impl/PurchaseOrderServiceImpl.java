package com.rome.stock.innerservice.domain.service.impl;

import cn.hutool.core.collection.CollUtil;
import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.rome.arch.core.clientobject.Response;
import com.rome.arch.core.domain.EntityFactory;
import com.rome.arch.core.exception.RomeException;
import com.rome.arch.util.SpringBeanUtil;
import com.rome.scm.common.rocketmq.CustomRocketMQProducerClient;
import com.rome.stock.common.constants.CommonConstants;
import com.rome.stock.common.constants.front.PurchaseOrderConsts;
import com.rome.stock.common.enums.CustomRocketMQEnum;
import com.rome.stock.common.enums.warehouse.SaleStatusVO;
import com.rome.stock.common.enums.warehouse.WarehouseRecordTypeVO;
import com.rome.stock.common.utils.DateUtil;
import com.rome.stock.core.domain.repository.CoreRealWarehouseStockRepository;
import com.rome.stock.core.facade.RedisRollBackFacade;
import com.rome.stock.core.infrastructure.dataobject.core.CoreRealStockOpDO;
import com.rome.stock.core.infrastructure.dataobject.core.CoreRealStockOpDetailDO;
import com.rome.stock.core.infrastructure.dataobject.core.CoreVirtualStockOpDO;
import com.rome.stock.innerservice.api.dto.*;
import com.rome.stock.innerservice.api.dto.frontrecord.*;
import com.rome.stock.innerservice.common.*;
import com.rome.stock.innerservice.constant.*;
import com.rome.stock.innerservice.context.distributedlock.LockStockContext;
import com.rome.stock.innerservice.domain.convertor.RwBatchConvertor;
import com.rome.stock.innerservice.domain.convertor.WarehouseRecordConvertor;
import com.rome.stock.innerservice.domain.convertor.frontrecord.FrPurchaseOrderConvertor;
import com.rome.stock.innerservice.domain.entity.ReceiptRecordE;
import com.rome.stock.innerservice.domain.entity.RwBatchE;
import com.rome.stock.innerservice.domain.entity.VirtualWarehouseE;
import com.rome.stock.innerservice.domain.entity.frontrecord.PurchaseOrderDetailE;
import com.rome.stock.innerservice.domain.entity.frontrecord.PurchaseOrderE;
import com.rome.stock.innerservice.domain.entity.warehouserecord.PurchaseWarehouseRecordE;
import com.rome.stock.innerservice.domain.entity.warehouserecord.WarehouseRecordDetail;
import com.rome.stock.innerservice.domain.entity.warehouserecord.WarehouseRecordE;
import com.rome.stock.innerservice.domain.repository.ReceiptRecordRepository;
import com.rome.stock.innerservice.domain.repository.RwBatchRepository;
import com.rome.stock.innerservice.domain.repository.SapInterfaceLogRepository;
import com.rome.stock.innerservice.domain.repository.VirtualWarehouseRepository;
import com.rome.stock.innerservice.domain.repository.frontrecord.FrOutsourcingRepository;
import com.rome.stock.innerservice.domain.repository.frontrecord.FrPurchaseOrderRepository;
import com.rome.stock.innerservice.domain.repository.frontrecord.FrontWarehouseRecordRelationRepository;
import com.rome.stock.innerservice.domain.repository.warehouserecord.WarehouseRecordRepository;
import com.rome.stock.innerservice.domain.service.*;
import com.rome.stock.innerservice.infrastructure.dataobject.*;
import com.rome.stock.innerservice.infrastructure.mapper.RealWarehouseMapper;
import com.rome.stock.innerservice.infrastructure.mapper.SkuRealVirtualStockSyncRelationMapper;
import com.rome.stock.innerservice.remote.item.dto.SkuInfoExtDTO;
import com.rome.stock.innerservice.remote.item.facade.SkuFacade;
import com.rome.stock.innerservice.remote.mpd.facade.MpdFacade;
import com.rome.stock.innerservice.remote.orderCneter.dto.DetailDTO;
import com.rome.stock.innerservice.remote.orderCneter.dto.NotifyOrderDTO;
import com.rome.stock.innerservice.remote.orderCneter.dto.ReceiveDTO;
import com.rome.stock.innerservice.remote.orderCneter.dto.ReceiveDetailDTO;
import com.rome.stock.innerservice.remote.orderCneter.facade.OrderCenterFacade;
import com.rome.stock.innerservice.remote.purchase.dto.*;
import com.rome.stock.innerservice.remote.purchase.facade.PurchaseCenterFacade;
import com.rome.stock.innerservice.remote.qualityCenter.dto.QualityNotifyDTO;
import com.rome.stock.innerservice.remote.qualityCenter.facade.QualityCenterFacade;
import com.rome.stock.innerservice.remote.sap.dto.sapDto930.PwOutDTO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.RandomUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.DigestUtils;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

@Slf4j
@Service
public class PurchaseOrderServiceImpl extends AbstractPurchase implements PurchaseOrderService {
    @Resource
    private FrPurchaseOrderRepository frPurchaseOrderRepository;
    @Resource
    private FrPurchaseOrderConvertor frPurchaseOrderConvertor;
    @Resource
    private VirtualWarehouseService virtualWarehouseService;
    @Resource
    private EntityFactory entityFactory;

    @Resource
    private WmsOutService wmsOutService;

    @Resource
    private WarehouseRecordRepository warehouseRecordRepository;

    @Resource
    private CoreRealWarehouseStockRepository coreRealWarehouseStockRepository;

    @Resource
    private RealWarehouseService realWarehouseService;

    @Resource
    private RecordRealVirtualStockSyncRelationService recordRealVirtualStockSyncRelationService;

    @Resource
    private SkuFacade skuFacade;


    @Resource
    private RwBatchRepository rwBatchRepository;

    @Resource
    private ReceiptRecordRepository receiptRecordRepository;


    @Resource
    private SkuRealVirtualStockSyncRelationMapper skuRealVirtualStockSyncRelationMapper;

    @Resource
    private FrontWarehouseRecordRelationRepository frontWarehouseRecordRelationRepository;
    @Resource
    private PurchaseCenterFacade purchaseCenterFacade;

    @Resource
    private WarehouseRecordConvertor warehouseRecordConvertor;
    @Resource
    private FrOutsourcingRepository frOutsourcingRepository;
    @Resource
    private QualityCenterFacade qualityCenterFacade;
    @Resource
    private PurchaseOrderOldService purchaseOrderOldService;
    @Resource
    private SapInterfaceLogRepository sapInterfaceLogRepository;
    @Resource
    private LockStockContext lockStockContext;
    @Resource
    private VirtualWarehouseRepository virtualWarehouseRepository;
    @Resource
    private MpdFacade mpdFacade;
    @Resource
    private OrderCenterFacade orderCenterFacade;
    @Autowired
    private RealWarehouseMapper realWarehouseMapper;

    /**
     * 创建外采单
     *
     * @param purchaseOrderDTO
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void addPurchaseNoticeRecord(PurchaseOrderDTO purchaseOrderDTO) {
        log.info("创建大仓采购入库单:{}", JSON.toJSONString(purchaseOrderDTO));
        //1.进行参数校验
        String errorMsg = this.validAddDataInfo(purchaseOrderDTO);
        AlikAssert.isFalse(StringUtils.isNotBlank(errorMsg), ResCode.STOCK_ERROR_1002, ResCode.STOCK_ERROR_1002_DESC + errorMsg);
        //2.幂等性判断
        if (frPurchaseOrderRepository.judgeExistByOutRecordCode(purchaseOrderDTO.getOutRecordCode())) {
            log.warn("单据【" + purchaseOrderDTO.getOutRecordCode() + "】已存在");
            //单据已存在
            return;
        }
        AbstractPurchase purchaseService = getPurchaseBean(purchaseOrderDTO.getPurchaseRecordType());
        AlikAssert.isNotNull(purchaseService, ResCode.STOCK_ERROR_1003, ResCode.STOCK_ERROR_1003_DESC + "未找到该类型单据对应的业务单元");
        PurchaseWarehouseRecordE warehouseRecord = entityFactory.createEntity(PurchaseWarehouseRecordE.class);
        purchaseService.createPurchaseIn(purchaseOrderDTO, warehouseRecord);
        KibanaLogUtils.printKibanaRecordInfo(purchaseOrderDTO.getOutRecordCode(), "", StockRecordInfoTypeVo.PURCHASE_ORDER.getType(), StockRecordInfoTypeVo.PURCHASE_ORDER.getDesc(), "addPurchaseNoticeRecord", purchaseOrderDTO);
    }


    /**
     * 采购单DO单基本信息校验
     *
     * @param dto
     * @return 校验结果
     */
    private String validAddDataInfo(PurchaseOrderDTO dto) {
        if (Objects.isNull(PurchaseTypeVO.getPurchaseVoByRecordType(dto.getPurchaseRecordType()))) {
            log.error("大仓采购类型有误：{}", JSON.toJSONString(dto));
            return "采购类型有误";
        }
        //直送的必须要有内采sap单号
        if (PurchaseTypeVO.DIRECT_PURCHASE_IN_RECORD.getType().equals(dto.getPurchaseRecordType())) {
            if (StringUtils.isBlank(dto.getSapPoNo())) {
                log.error("直送类型需要传sap单号：{}", JSON.toJSONString(dto));
                return "直送类型需传sap单号";
            }
            if (StringUtils.isBlank(dto.getShopCode())) {
                log.error("直送类型需要传shopCode：{}", JSON.toJSONString(dto));
                return "直送类型需传shopCode";
            }
        }
        List<String> businessTypeList = PurchaseOrderConsts.PURCHASE_BUSINESS_TYPE_MAP.get(dto.getPurchaseRecordType());
        if (CollUtil.isNotEmpty(businessTypeList)) {
            boolean res = businessTypeList.stream().anyMatch(x -> x.equals(dto.getBusinessType().toUpperCase()));
            if (!res) {
                log.error("业务类型不匹配{}", JSON.toJSONString(dto));
                return "业务类型不匹配";
            }
        }
        //对过账到库存业务类型做校验
        for (PurchaseOrderDetailDTO purchaseOrderDetailDTO : dto.getPurchaseOrderDetails()) {
            if (Objects.equals(purchaseOrderDetailDTO.getIsNeedQuality(), 0) && Objects.equals(purchaseOrderDetailDTO.getIsPostQcInventory(), 1)) {
                log.error("无需质检的物料不能增加质检库存");
                return "无需质检的物料不能增加质检库存";
            }
        }
        return "";
    }

    /**
     * 根据类型获取对应的业务对象
     *
     * @param purchaseType
     * @return
     */
    private AbstractPurchase getPurchaseBean(Integer purchaseType) {
        try {
            return (AbstractPurchase) SpringBeanUtil.getBean(PurchaseOrderConsts.PURCHASE_MAP.get(purchaseType));
        } catch (Exception e) {
            log.error("未找到该类型对应的业务处理单元:{}", e);
            throw new RomeException(ResCode.STOCK_ERROR_1002, ResCode.STOCK_ERROR_1002_DESC + "未找到该类型单据对应的业务单元");
        }
    }


    /**
     * 根据收货单信息同步质检结果给wms
     *
     * @param wmsRecordCode          收货单号
     * @param wmsRecordCodeBatchList 收货批次明细
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void synchronizationCheckResultToWmsByReceiveRecord(String wmsRecordCode, List<RwBatchDTO> wmsRecordCodeBatchList,boolean isHand) {
        log.info("同步质检结果给wms,wmsRecordCode={}", wmsRecordCode);
        ReceiptRecordE receiptRecordE = receiptRecordRepository.selectReceiptRecordBywmsRecordCode(wmsRecordCode);
        PurchaseOrderE purchaseOrderE = frPurchaseOrderRepository.queryByCode(receiptRecordE.getFrontRecordCode());
        AlikAssert.isTrue((purchaseOrderE != null && purchaseOrderE.getId() != null), ResCode.STOCK_ERROR_1014, ResCode.STOCK_ERROR_1014_DESC + ":recordCode=" + receiptRecordE.getFrontRecordCode());
        //设置采购单号
        receiptRecordE.setOutRecordCode(purchaseOrderE.getOutRecordCode());
        WarehouseRecordE warehouseRecordE = warehouseRecordRepository.queryWarehouseRecordByRecordCode(receiptRecordE.getWarehouseRecordCode());
        AlikAssert.isTrue((warehouseRecordE != null && warehouseRecordE.getId() != null), ResCode.STOCK_ERROR_1017, ResCode.STOCK_ERROR_1017_DESC);
        if (!(warehouseRecordE.getAppId() != null && "1".equals(warehouseRecordE.getAppId()))) {
            //走老逻辑
            purchaseOrderOldService.synchronizationCheckResultToWmsByReceiveRecord(wmsRecordCode, wmsRecordCodeBatchList);
            return;
        }
        Long realWarehouseId = warehouseRecordE.getRealWarehouseId();
        RealWarehouse realWarehouse = realWarehouseService.findByRealWarehouseId(realWarehouseId);
        List<CoreRealStockOpDO> rollBackList = new ArrayList<>();
        boolean isSuccess = false;
        try {
            List<RwBatchE> rwBatchES = rwBatchConvertor.dtosToEntities(wmsRecordCodeBatchList);
            //批量更新状态为已同步给wms
            int res = rwBatchRepository.updateToHasSynchronized(RomeCollectionUtil.getValueList(wmsRecordCodeBatchList, "id"));
            AlikAssert.isTrue(res > 0, ResCode.STOCK_ERROR_1002, "更新质检结果下发给WMS状态失败");
            log.info("开始通知wms质检结果wmsCode={},批次信息={}", wmsRecordCode, JSON.toJSONString(wmsRecordCodeBatchList));
            //质检完成加完真实后，通知订单中心缺货回调
            List<String> skuCodes = wmsRecordCodeBatchList.stream().filter(v -> v.getQualityStatus() == 1).map(RwBatchDTO::getSkuCode).distinct().collect(Collectors.toList());
            //如果采购入库包含周转箱，需要创建周转箱仓库领用单
            this.createBoxReceiveRecord(warehouseRecordE,wmsRecordCodeBatchList,purchaseOrderE.getOutRecordCode());
            List<WarehouseRecordDetail> realStockDetailList =this.operateQualityInspectionStock(rwBatchES, warehouseRecordE.getRecordCode(), wmsRecordCode, warehouseRecordE, rollBackList);
            //平铺的批次结果信息聚合成wms需要的树形结构并调用wms接口
            if(!isHand){
                //非手动处理，正常流程，需要通知WMS
                boolean syncToWMSResult = wmsOutService.inspectionAdvice(realWarehouseId, realWarehouse.getRealWarehouseOutCode(), realWarehouse.getFactoryCode(), receiptRecordE.getOutRecordCode(), receiptRecordE.getWmsRecordCode(), wmsRecordCodeBatchList);
                AlikAssert.isTrue(syncToWMSResult, ResCode.STOCK_ERROR_1039, ResCode.STOCK_ERROR_1039_DESC);
            }
            try {
                this.sendMessageToOrderCenter(warehouseRecordE, skuCodes,realStockDetailList,wmsRecordCode);
            } catch (RomeException e) {
                log.error("质检回调库存中心发生异常:{}", e);
            } catch (Exception e) {
                log.error("质检回调库存中心发生异常:{}", e);
            }
            isSuccess = true;
        } catch (RomeException e) {
            log.error(e.getMessage() + "wmccode=" + wmsRecordCode, e);
            throw e;
        } catch (Exception e) {
            log.error(e.getMessage() + "wmccode=" + wmsRecordCode, e);
            throw new RomeException(ResCode.STOCK_ERROR_1003, e.getMessage());
        } finally {
            if (!isSuccess) {
                for (CoreRealStockOpDO coreRealStockOpDO : rollBackList) {
                    RedisRollBackFacade.redisRollBack(coreRealStockOpDO);
                }
            }
        }
    }




    /**
     * 如果采购入库包含周转箱，需要创建周转箱仓库领用单
     * @param warehouseRecordE
     * @param pwRecordCode
     */
    private void createBoxReceiveRecord(WarehouseRecordE warehouseRecordE,List<RwBatchDTO> wmsRecordCodeBatchList,String pwRecordCode){
        List<RwBatchDTO> rwBatchDTOList = wmsRecordCodeBatchList.stream().filter(v -> v.getQualityStatus() == 1).distinct().collect(Collectors.toList());
        List<String> z016SkuCodes = skuFacade.searchZ016Codes();
        List<RwBatchDTO> boxRecordDetails=rwBatchDTOList.stream().filter(v->z016SkuCodes.contains(v.getSkuCode())).collect(Collectors.toList());
        if(CollectionUtils.isEmpty(boxRecordDetails)){
            return;
        }
        //相同Sku多批次分组
        Map<String,List<RwBatchDTO>> batchListMap=boxRecordDetails.stream().collect(Collectors.groupingBy(RwBatchDTO::getSkuCode));
        List<WarehouseRecordDetail> warehouseRecordDetailList=warehouseRecordRepository.queryDetailListByRecordCode(warehouseRecordE.getRecordCode());
        Map<String,WarehouseRecordDetail> warehouseRecordDetailMap=warehouseRecordDetailList.stream().collect(Collectors.toMap(WarehouseRecordDetail::getSkuCode, Function.identity(), (v1, v2) -> v1));
        //查询采购中心接口，返回领用成本中心code
        PwOutDTO pwOutDTO = purchaseCenterFacade.queryByOutRecordCode(pwRecordCode);
        if (!Objects.isNull(pwOutDTO)) {
            RealWarehouse realWarehouse=realWarehouseService.findByRealWarehouseId(warehouseRecordE.getRealWarehouseId());
            if(null == realWarehouse){
                throw new RomeException(ResCode.STOCK_ERROR_1001,"入库仓不存在");
            }
            ReceiveDTO receiveDTO = new ReceiveDTO();
            receiveDTO.setFactoryCode(realWarehouse.getFactoryCode());
            receiveDTO.setRealWarehouseCode(realWarehouse.getRealWarehouseOutCode());
            receiveDTO.setOutRecordCode(pwRecordCode);
            receiveDTO.setCostCenterCode(pwOutDTO.getCostCenterCode());
            //周转箱领用
            receiveDTO.setReceiveBusinessType(5);
            receiveDTO.setTransWay(1);
            //材料领用出库--Y13
            receiveDTO.setReasonCode("1006");
            receiveDTO.setPushWms(false);
            List<ReceiveDetailDTO> receiveDetailDTOList = Lists.newArrayList();
            for (Map.Entry<String, List<RwBatchDTO>> entry : batchListMap.entrySet()) {
                ReceiveDetailDTO receiveDetailDTO = new ReceiveDetailDTO();
                receiveDetailDTO.setSkuCode(entry.getKey());
                receiveDetailDTO.setSkuQty(entry.getValue().stream().map(x -> x.getActualQty()).reduce(BigDecimal.ZERO, BigDecimal::add));
                receiveDetailDTO.setUnitCode(warehouseRecordDetailMap.get(entry.getKey()).getUnitCode());
                receiveDetailDTOList.add(receiveDetailDTO);
            }
            receiveDTO.setReceiveDetailDTOList(receiveDetailDTOList);
            String outRecordCode=DigestUtils.md5DigestAsHex(JSON.toJSONString(receiveDetailDTOList).getBytes()).substring(0,5);
            receiveDTO.setOutRecordCode(pwRecordCode+"_"+outRecordCode);
            //时间放在最后
            receiveDTO.setReceiveDate(new Date());
            CustomRocketMQProducerClient.send(JSON.toJSONString(receiveDTO), CustomRocketMQEnum.MQ_STOCK_PURCHASE_IN_WAREHOUSE_RECEIVE_PUSH.getCode(), receiveDTO.getOutRecordCode(), UUID.randomUUID().toString().replaceAll("\\-", ""));
        }
    }



    /**
     * 根据质检结果移动质检库存
     *
     * @param wmsRecordCodeBatchList
     * @param rollBackList
     */
    private void moveQualityStock(List<RwBatchDTO> wmsRecordCodeBatchList, WarehouseRecordE warehouseRecordE, List<CoreRealStockOpDO> rollBackList
            , String wmsRecordCode) {
        Long realWarehouseId = warehouseRecordE.getRealWarehouseId();
        String recordCode = warehouseRecordE.getRecordCode();
        Integer recordType = warehouseRecordE.getRecordType();
        List<WarehouseRecordDetail> failSkuDetails = getWarehouseRecordDetailsByInspectResult(wmsRecordCodeBatchList, false);
        if (failSkuDetails.size() > 0) {
            CoreRealStockOpDO coreRealStockOpDOFail = initStockObj(failSkuDetails, realWarehouseId, recordCode, recordType);
            coreRealWarehouseStockRepository.moveQualityToUnqualifiedStock(coreRealStockOpDOFail);
            rollBackList.add(coreRealStockOpDOFail);
        }
        //调用质检成功的接口增加可用，减质检
        List<WarehouseRecordDetail> passSkuDetails = getWarehouseRecordDetailsByInspectResult(wmsRecordCodeBatchList, true);
        //通知采购中心质检回传数量，合格/不合格都要通知
        List<RwBatchE> realStockList = rwBatchConvertor.dtosToEntities(wmsRecordCodeBatchList.stream().filter(v -> v.getQualityStatus() == 1).collect(Collectors.toList()));
        List<RwBatchE> unqualifiedStockList = rwBatchConvertor.dtosToEntities(wmsRecordCodeBatchList.stream().filter(v -> v.getQualityStatus() == 2).collect(Collectors.toList()));
        Map<String, List<SkuLabelDTO>> skuLabelListMap = this.initSkuLabelStock(warehouseRecordE, wmsRecordCode, realStockList, unqualifiedStockList);
        if (passSkuDetails.size() > 0) {
            CoreRealStockOpDO coreRealStockOpDO = initStockObjForPre(passSkuDetails, realWarehouseId
                    , recordCode, recordType, skuLabelListMap);
            coreRealWarehouseStockRepository.moveQualityToRealStock(coreRealStockOpDO);
            rollBackList.add(coreRealStockOpDO);
        }
        //批量更新状态为已同步给wms
        rwBatchRepository.updateToHasSynchronized(RomeCollectionUtil.getValueList(wmsRecordCodeBatchList, "id"));
    }


    /**
     * 根据收货单信息同步质检结果给wms
     *
     * @param wmsRecordCode          收货单号
     * @param wmsRecordCodeBatchList 收货批次明细
     */
    @Resource
    private RwBatchConvertor rwBatchConvertor;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void handleCheckResultByForce(String wmsRecordCode, List<RwBatchWithoutWmsDTO> list) {
        List<RwBatchDTO> wmsRecordCodeBatchList = rwBatchConvertor.entitiesToDtos(rwBatchRepository.selectBatchInfoByIdS(wmsRecordCode, RomeCollectionUtil.getValueList(list, "id")));
        AlikAssert.isTrue(list.size() == wmsRecordCodeBatchList.size(), ResCode.STOCK_ERROR_1002, ResCode.STOCK_ERROR_1002_DESC + ":id输入有误，请检查");
        ReceiptRecordE receiptRecordE = receiptRecordRepository.selectReceiptRecordBywmsRecordCode(wmsRecordCode);
        PurchaseOrderE purchaseOrderE = frPurchaseOrderRepository.queryByCode(receiptRecordE.getFrontRecordCode());
        AlikAssert.isTrue((purchaseOrderE != null && purchaseOrderE.getId() != null), ResCode.STOCK_ERROR_1014, ResCode.STOCK_ERROR_1014_DESC);
        //设置采购单号
        receiptRecordE.setOutRecordCode(purchaseOrderE.getOutRecordCode());

        WarehouseRecordE warehouseRecordE = warehouseRecordRepository.queryWarehouseRecordByRecordCode(receiptRecordE.getWarehouseRecordCode());
        AlikAssert.isTrue((warehouseRecordE != null && warehouseRecordE.getId() != null), ResCode.STOCK_ERROR_1017, ResCode.STOCK_ERROR_1017_DESC);

        if (!(warehouseRecordE.getAppId() != null && "1".equals(warehouseRecordE.getAppId()))) {
            //走老逻辑
            throw new RomeException(ResCode.STOCK_ERROR_1003, "这是老流程，应该走老逻辑接口处理");
        }
        List<CoreRealStockOpDO> rollBackList = new ArrayList<>();
        boolean isSuccess = false;
        try {
            //调用质检失败的即可增加质检失败数，减质检
            this.moveQualityStock(wmsRecordCodeBatchList, warehouseRecordE, rollBackList, wmsRecordCode);
            isSuccess = true;
        } catch (RomeException e) {
            log.error(e.getMessage() + "wmscode=" + wmsRecordCode, e);
            throw e;
        } catch (Exception e) {
            log.error(e.getMessage() + "wmscode=" + wmsRecordCode, e);
            throw new RomeException(ResCode.STOCK_ERROR_1003, e.getMessage());
        } finally {
            if (!isSuccess) {
                for (CoreRealStockOpDO coreRealStockOpDO : rollBackList) {
                    RedisRollBackFacade.redisRollBack(coreRealStockOpDO);
                }
            }
        }
    }

    /**
     * 对质检库存进行操作
     *
     * @param batchList
     * @param recordCode
     * @param wmsRecordCode
     */
    private List<WarehouseRecordDetail> operateQualityInspectionStock(List<RwBatchE> rwBatchList, String recordCode, String wmsRecordCode, WarehouseRecordE warehouseRecordE, List<CoreRealStockOpDO> rollBackList) {
        Long realWarehouseId = warehouseRecordE.getRealWarehouseId();
        Integer recordType = warehouseRecordE.getRecordType();
        rwBatchList = rwBatchList.stream().filter(x -> Objects.equals(x.getIsOperate(), 0)).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(rwBatchList)) {
            return Lists.newArrayList();
        }
        log.info("开始操作质检库存:{}", JSON.toJSONString(rwBatchList));
        List<RwBatchE> realStockList = new ArrayList<>();
        List<RwBatchE> unqualifiedStockList = new ArrayList<>();
        //根据库存扣减类型进行分组
        rwBatchList.forEach(x -> {
            if (Objects.equals(x.getQualityStatus(), 1)) {
                realStockList.add(x);
            }
            if (Objects.equals(x.getQualityStatus(), 2)) {
                unqualifiedStockList.add(x);
            }
        });
        List<WarehouseRecordDetail> realStockDetailList = buildStockInfo(realStockList);
        List<WarehouseRecordDetail> unqualifiedStockDetailList = buildStockInfo(unqualifiedStockList);
        Map<String, List<SkuLabelDTO>> skuLabelListMap = new HashMap<>();
        //通知采购中心质检回传数量，合格/不合格都要通知
        //临时性检验不调采购中心
        if (!Objects.equals(warehouseRecordE.getRecordType(), WarehouseRecordTypeVO.TEMP_QUALITY_IN_RECORD.getType())) {
            skuLabelListMap = this.initSkuLabelStock(warehouseRecordE, wmsRecordCode, realStockList, unqualifiedStockList);
        }
        try {
            if (realStockDetailList.size() > 0) {
                CoreRealStockOpDO coreRealStockOpDOPass = initStockObjForPre(realStockDetailList, realWarehouseId
                        , recordCode, recordType, skuLabelListMap);
                coreRealWarehouseStockRepository.moveQualityToRealStock(coreRealStockOpDOPass);
                rollBackList.add(coreRealStockOpDOPass);
            }
            if (unqualifiedStockDetailList.size() > 0) {
                CoreRealStockOpDO coreRealStockOpDOFail = initStockObj(unqualifiedStockDetailList, realWarehouseId, recordCode, recordType);
                coreRealWarehouseStockRepository.moveQualityToUnqualifiedStock(coreRealStockOpDOFail);
                rollBackList.add(coreRealStockOpDOFail);
            }
            if (CollectionUtils.isNotEmpty(rwBatchList)) {
                //更新库存状态为已操作,更新推送采购中心为已推送sync_purchase_status=2
                int res = rwBatchRepository.updateOperateByWmsCode(rwBatchConvertor.entitiesToDos(rwBatchList), wmsRecordCode);
                AlikAssert.isTrue(res > 0, ResCode.STOCK_ERROR_1002, "更新操作状态失败");
            }
            return realStockDetailList;
        } catch (RomeException e) {
            log.error("质检回调库存中心发生异常:", e);
            throw e;
        } catch (Exception e) {
            log.error("质检回调库存中心发生异常:", e);
            throw new RomeException(ResCode.STOCK_ERROR_1003, e.getMessage());
        }
    }


    /**
     * 包装通知采购中心质检数量参数，合格/不合格都要通知
     *
     * @param warehouseRecordE
     * @param wmsRecordCode
     * @param realStockList
     * @param unqualifiedStockList
     * @return
     */
    private Map<String, List<SkuLabelDTO>> initSkuLabelStock(WarehouseRecordE warehouseRecordE, String wmsRecordCode
            , List<RwBatchE> realStockList, List<RwBatchE> unqualifiedStockList) {
        List<WarehouseRecordDetail> detailList = warehouseRecordRepository.queryDetailListByRecordCode(warehouseRecordE.getRecordCode());
        //查询前置单
        List<FrontWarehouseRecordRelationDO> frontWarehouseRecordRelationDOs = warehouseRecordRepository.getFrontWarehouseRecordsRelationByWrId(warehouseRecordE.getId());
        AlikAssert.isTrue(frontWarehouseRecordRelationDOs != null && frontWarehouseRecordRelationDOs.size() > 0, ResCode.STOCK_ERROR_1018, ResCode.STOCK_ERROR_1018_DESC);
        FrontWarehouseRecordRelationDO frontWarehouseRecordRelationDO = frontWarehouseRecordRelationDOs.get(0);
        PurchaseOrderE purchaseOrderE = frPurchaseOrderRepository.queryById(frontWarehouseRecordRelationDO.getFrontRecordId());
        AlikAssert.isNotNull(purchaseOrderE, ResCode.STOCK_ERROR_1014, ResCode.STOCK_ERROR_1014_DESC);
        Map<String, WarehouseRecordDetail> detailMap = detailList.stream().collect(Collectors.toMap(x -> x.getSkuCode(), Function.identity(), (v1, v2) -> v2));
        StockQualityNotifyDTO stockQualityNotifyDTO = new StockQualityNotifyDTO();
        stockQualityNotifyDTO.setPurchaseEntryNo(purchaseOrderE.getOutRecordCode());
        stockQualityNotifyDTO.setReceiptNo(wmsRecordCode);
        List<SkuQualityDTO> detail = new ArrayList<>();
        Map<String, BigDecimal> qualifiedMap = new HashMap<>();
        //质检合格
        for (RwBatchE e : realStockList) {
            SkuQualityDTO skuQualityDTO = new SkuQualityDTO();
            skuQualityDTO.setIsSuccess(1);
            skuQualityDTO.setSkuCode(e.getSkuCode());
            skuQualityDTO.setInspectionBatchNo(e.getBatchCode());
            skuQualityDTO.setInspectionDate(e.getProductDate());
            skuQualityDTO.setInspectionQty(e.getActualQty());
            if (detailMap.containsKey(e.getSkuCode())) {
                skuQualityDTO.setLineNo(detailMap.get(e.getSkuCode()).getLineNo());
            }
            String key = e.getSkuCode() + "_" + e.getBatchCode();
            if (qualifiedMap.containsKey(key)) {
                qualifiedMap.put(key, qualifiedMap.get(key).add(e.getActualQty()));
            } else {
                qualifiedMap.put(key, e.getActualQty());
            }
            detail.add(skuQualityDTO);
        }
        //质检不合格
        unqualifiedStockList.forEach(e -> {
            SkuQualityDTO skuQualityDTO = new SkuQualityDTO();
            skuQualityDTO.setIsSuccess(0);
            skuQualityDTO.setSkuCode(e.getSkuCode());
            skuQualityDTO.setInspectionBatchNo(e.getBatchCode());
            skuQualityDTO.setInspectionDate(e.getProductDate());
            skuQualityDTO.setInspectionQty(e.getActualQty());
            if (detailMap.containsKey(e.getSkuCode())) {
                skuQualityDTO.setLineNo(detailMap.get(e.getSkuCode()).getLineNo());
            }
            detail.add(skuQualityDTO);
        });
        stockQualityNotifyDTO.setDetail(detail);
        Response<StockQualityResultDTO> response = purchaseCenterFacade.notifyQuality(stockQualityNotifyDTO);
        if (!Objects.equals(response.getCode(), "0")) {
            throw new RomeException(ResCode.STOCK_ERROR_1003, "通知采购中心质检数量异常,msg:" + response.getMsg() + "data:" + JSON.toJSONString(response.getData()));
        }
        //查询实仓对应的虚仓是否存在
        List<VirtualWarehouseE> list = virtualWarehouseRepository.queryByRealWarehouseId(warehouseRecordE.getRealWarehouseId());
        if (response.getData() == null || CollectionUtils.isEmpty(response.getData().getDetail())) {
//            if(!CollectionUtils.isEmpty(list) && qualifiedMap.size()>0){
//                //存在质检合格的数据，并且虚仓也有，不过采购中心返回空，就要抛异常
//                throw new RomeException(ResCode.STOCK_ERROR_1003,"通知采购中心质检数量异常,msg:"+response.getMsg()+"data:"+JSON.toJSONString(response.getData()));
//            }
            return new HashMap<>();
        }
        //过滤出质检合格转真实库存的虚仓返回值
        List<SkuQualityResultDTO> skuQualityResultDetail = response.getData().getDetail();
        List<SkuLabelDTO> skuLabelDetailList = new ArrayList<>();
        Map<String, BigDecimal> skuCodeVirtualMap = new HashMap<>();
        skuQualityResultDetail.forEach(e -> {
            if (!CollectionUtils.isEmpty(e.getLabelDetail())) {
                skuLabelDetailList.addAll(e.getLabelDetail());
                //组装返回值映射Map
                String key = e.getSkuCode() + "_" + e.getInspectionBatchNo();
                BigDecimal total = BigDecimal.ZERO;
                for (SkuLabelDTO skuLabelDTO : e.getLabelDetail()) {
                    total = total.add(skuLabelDTO.getQty());
                }
                skuCodeVirtualMap.put(key, total);
            }
        });
        List<Long> virtualWarehouseIds = list.stream().map(VirtualWarehouseE::getId).distinct().collect(Collectors.toList());
        skuLabelDetailList.forEach(e -> {
            if (StringUtils.isEmpty(e.getSkuCode())) {
                purchaseCenterFacade.sendMessage(JSON.toJSONString(stockQualityNotifyDTO),"采购中心返回skuCode不能为空","/api/v1/pw/stock/notifyQuality");
                throw new RomeException(ResCode.STOCK_ERROR_1003, "采购中心返回skuCode不能为空");
            }
            if (null == e.getQty() || e.getQty().compareTo(BigDecimal.ZERO) <= 0) {
                purchaseCenterFacade.sendMessage(JSON.toJSONString(stockQualityNotifyDTO),"采购中心返回Qty数量异常,skuCode:" + e.getSkuCode() + ",Qty:" + e.getQty(),"/api/v1/pw/stock/notifyQuality");
                throw new RomeException(ResCode.STOCK_ERROR_1003, "采购中心返回Qty数量异常,skuCode:" + e.getSkuCode() + ",Qty:" + e.getQty());
            }
            if (!virtualWarehouseIds.contains(e.getVirtualWarehouseId())) {
                purchaseCenterFacade.sendMessage(JSON.toJSONString(stockQualityNotifyDTO),"采购中心返回虚仓ID无法与实仓对应,skuCode:" + e.getSkuCode() + ",virtualWarehouseId:" + e.getVirtualWarehouseId(),"/api/v1/pw/stock/notifyQuality");
                throw new RomeException(ResCode.STOCK_ERROR_1003, "采购中心返回虚仓ID无法与实仓对应,skuCode:" + e.getSkuCode() + ",virtualWarehouseId:" + e.getVirtualWarehouseId());
            }
        });
        //校验实仓库存操作数量和返回虚仓操作数量是否一致，采购中心只有质检合格的才会返回虚仓分配数
        for (Map.Entry<String, BigDecimal> entry : qualifiedMap.entrySet()) {
            if (!skuCodeVirtualMap.containsKey(entry.getKey())) {
                purchaseCenterFacade.sendMessage(JSON.toJSONString(stockQualityNotifyDTO),"采购中心未返回对应明细，skuCode:" + entry.getKey(),"/api/v1/pw/stock/notifyQuality");
                throw new RomeException(ResCode.STOCK_ERROR_1003, "采购中心未返回对应明细，skuCode:" + entry.getKey());
            }
            if (skuCodeVirtualMap.get(entry.getKey()).compareTo(entry.getValue()) != 0) {
                purchaseCenterFacade.sendMessage(JSON.toJSONString(stockQualityNotifyDTO), "采购中心返回对应明细收货数量不一致，skuCode:" + entry.getKey(),"/api/v1/pw/stock/notifyQuality");
                throw new RomeException(ResCode.STOCK_ERROR_1003, "采购中心返回对应明细收货数量不一致，skuCode:" + entry.getKey());
            }
        }
        //根据skuCode分组
        Map<String, List<SkuLabelDTO>> skuLabelListMap = skuLabelDetailList.stream().collect(Collectors.groupingBy(SkuLabelDTO::getSkuCode));
        return skuLabelListMap;
    }


    /**
     * 包装通知采购中心质检数量参数，合格/不合格都要通知
     *
     * @param warehouseRecordE
     * @param wmsRecordCode
     * @return
     */
    private void intNotifyQuality(WarehouseRecordE warehouseRecordE, String wmsRecordCode, List<RwBatchE> batchList) {
        List<WarehouseRecordDetail> detailList = warehouseRecordRepository.queryDetailListByRecordCode(warehouseRecordE.getRecordCode());
        Map<String, WarehouseRecordDetail> detailMap = detailList.stream().collect(Collectors.toMap(x -> x.getSkuCode(), Function.identity(), (v1, v2) -> v2));
        StockQualityNotifyDTO stockQualityNotifyDTO = new StockQualityNotifyDTO();
        stockQualityNotifyDTO.setPurchaseEntryNo(warehouseRecordE.getSapOrderCode());
        stockQualityNotifyDTO.setReceiptNo(wmsRecordCode);
        List<SkuQualityDTO> detail = new ArrayList<>();
        //质检合格
        batchList.forEach(e -> {
            SkuQualityDTO skuQualityDTO = new SkuQualityDTO();
            if (Objects.equals(e.getQualityStatus(), 1)) {
                skuQualityDTO.setIsSuccess(1);
            } else {
                skuQualityDTO.setIsSuccess(0);
            }
            skuQualityDTO.setSkuCode(e.getSkuCode());
            skuQualityDTO.setInspectionBatchNo(e.getBatchCode());
            skuQualityDTO.setInspectionDate(e.getProductDate());
            skuQualityDTO.setInspectionQty(e.getActualQty());
            if (detailMap.containsKey(e.getSkuCode())) {
                skuQualityDTO.setLineNo(detailMap.get(e.getSkuCode()).getLineNo());
            }
            detail.add(skuQualityDTO);
        });
        stockQualityNotifyDTO.setDetail(detail);
        Response<StockQualityResultDTO> response = purchaseCenterFacade.notifyQuality(stockQualityNotifyDTO);
        if (!Objects.equals(response.getCode(), "0")) {
            throw new RomeException(ResCode.STOCK_ERROR_1003, "通知采购中心质检数量异常,msg:" + response.getMsg() + "data:" + JSON.toJSONString(response.getData()));
        }
    }


    private void sendMessageToOrderCenter(WarehouseRecordE warehouseRecordE, List<String> skuCodes,List<WarehouseRecordDetail> realStockDetailList,String wmsRecordCode) {
        List<FrontWarehouseRecordRelationDO> frontWarehouseRecordRelationDOs = warehouseRecordRepository.getFrontWarehouseRecordsRelationByWrId(warehouseRecordE.getId());
        AlikAssert.isTrue(frontWarehouseRecordRelationDOs != null && frontWarehouseRecordRelationDOs.size() > 0, ResCode.STOCK_ERROR_1018, ResCode.STOCK_ERROR_1018_DESC);
        FrontWarehouseRecordRelationDO frontWarehouseRecordRelationDO = frontWarehouseRecordRelationDOs.get(0);
        PurchaseOrderE purchaseOrderE = frPurchaseOrderRepository.queryById(frontWarehouseRecordRelationDO.getFrontRecordId());
        this.notifyToOrderCenter(warehouseRecordE, purchaseOrderE.getOutRecordCode(), "purchaseInNotifyPushProducer2", skuCodes,realStockDetailList,wmsRecordCode);
    }


    /**
     * 构建待操作的库存对象
     *
     * @return
     */
    private List<WarehouseRecordDetail> buildStockInfo(List<RwBatchE> rwBatchEList) {
        List<WarehouseRecordDetail> details = new ArrayList<>();
        if (CollUtil.isNotEmpty(rwBatchEList)) {
            Map<String, List<RwBatchE>> rwBatchMap = rwBatchEList.stream().collect(Collectors.groupingBy(RwBatchE::getSkuCode));
            rwBatchMap.forEach((k, v) -> {
                WarehouseRecordDetail detail = new WarehouseRecordDetail();
                detail.setSkuCode(k);
                detail.setSkuId(v.get(0).getSkuId());
                BigDecimal actualQty = v.stream().map(x -> x.getActualQty()).reduce(BigDecimal.ZERO, BigDecimal::add);
                detail.setActualQty(actualQty);
                details.add(detail);
            });
        }
        return details;
    }


    /**
     * 根据质检结果转换入库单明细[需要根据skuId聚合计算]
     *
     * @param rwBatchEList  质检结果
     * @param inspectResult true：过滤得到质检成功的 false：过滤得到质检失败的
     * @return 一个采购单下的多个收货单聚合的sku明细
     */
    private List<WarehouseRecordDetail> getWarehouseRecordDetailsByInspectResult(List<RwBatchDTO> rwBatchEList, boolean inspectResult) {
        List<WarehouseRecordDetail> details = new ArrayList<>();
        //按skuId 归类
        Map<Long, List<RwBatchDTO>> skuBatchInfoMap = RomeCollectionUtil.listforListMap(rwBatchEList, "skuId");
        //表示质检结果为不合格
        int desc = 2;
        if (inspectResult) {
            //表示质检结果为合格
            desc = 1;
        }
        for (Map.Entry<Long, List<RwBatchDTO>> mapEntry : skuBatchInfoMap.entrySet()) {
            WarehouseRecordDetail detail = new WarehouseRecordDetail();
            detail.setSkuId(mapEntry.getKey());
            BigDecimal actualQty = BigDecimal.ZERO;
            for (RwBatchDTO batch : mapEntry.getValue()) {
                if (desc == batch.getQualityStatus()) {
                    detail.setSkuCode(batch.getSkuCode());
                    actualQty = actualQty.add(batch.getActualQty());
                }
            }
            if (actualQty.compareTo(BigDecimal.ZERO) > 0) {
                detail.setActualQty(actualQty);
                details.add(detail);
            }
        }
        return details;
    }



    @Override
    public void notifyToOrderCenter(WarehouseRecordE warehouseRecordE,String purchaseOrderOutCode,String requestService
            ,List<String> skuCodes,List<WarehouseRecordDetail> realStockDetailList,String wmsRecordCode){
        if(CollectionUtils.isEmpty(skuCodes)){
            log.info("采购入库回调不推送mq到订单中心，物料为空:采购入库单号：=>{}",warehouseRecordE.getRecordCode());
            return;
        }
        //查询采购中心采购单是否为加盟计划,如果返回的有值,就一定是计划补货出库单
        List<PwStoreRelationDTO> pwStoreRelationList = purchaseCenterFacade.queryDOCodeByPwCode(Lists.newArrayList(purchaseOrderOutCode));
        if(CollectionUtils.isNotEmpty(pwStoreRelationList) && CollectionUtils.isNotEmpty(pwStoreRelationList.get(0).getSourceOrderNos())){
            //过滤出计划补货出库单
            NotifyOrderDTO notifyOrderDTO = new NotifyOrderDTO();
            List<DetailDTO> detailList = Lists.newArrayList();
            notifyOrderDTO.setWmsOrderCode(wmsRecordCode+"_"+ UUID.randomUUID().toString().replace("-", "").substring(0, 8));
            RealWarehouseDO realWarehouseDO = realWarehouseMapper.queryById(warehouseRecordE.getRealWarehouseId());
            if(Objects.isNull(realWarehouseDO)){
                throw new RomeException(ResCode.STOCK_ERROR_1002, "仓库不存在");
            }
            notifyOrderDTO.setFactoryCode(realWarehouseDO.getFactoryCode());
            notifyOrderDTO.setRealWarehouseCode(realWarehouseDO.getRealWarehouseOutCode());
            notifyOrderDTO.setRecordCodeList(pwStoreRelationList.get(0).getSourceOrderNos());
            notifyOrderDTO.setDetailList(detailList);

            //查询后置单明细
            List<WarehouseRecordDetail> warehouseRecordDetailList = warehouseRecordRepository.queryDetailListByRecordCode(warehouseRecordE.getRecordCode());
            Map<String, WarehouseRecordDetail> skuCodeMap = warehouseRecordDetailList.stream().collect(Collectors.toMap(WarehouseRecordDetail::getSkuCode, Function.identity(),(x,y)->y));
            //发新的消息调用订单中心
            for (WarehouseRecordDetail detail : realStockDetailList) {
                if(!skuCodeMap.containsKey(detail.getSkuCode())){
                    continue;
                }
                DetailDTO detailDTO = new DetailDTO();
                detailDTO.setSkuCode(detail.getSkuCode());
                detailDTO.setSkuQty(detail.getActualQty());
                detailDTO.setUnitCode(skuCodeMap.get(detail.getSkuCode()).getUnitCode());
                detailDTO.setUnit(skuCodeMap.get(detail.getSkuCode()).getUnit());
                detailList.add(detailDTO);
            }
            MqProducerClientTools.sendByTransactionAfterCallback(JSON.toJSONString(notifyOrderDTO),
                    CustomRocketMQEnum.MQ_FUTURE_PLAN_INNER_PUSH.getCode(),
                    notifyOrderDTO.getWmsOrderCode(),null, ()->{
                        orderCenterFacade.purchaseInNotify(notifyOrderDTO);
                        return true;
                    });
        }else{
            //调用原来的缺货回调
            this.notifyToOrderCenterOld(warehouseRecordE,purchaseOrderOutCode,requestService,skuCodes);
        }
    }


    private void notifyToOrderCenterOld(WarehouseRecordE warehouseRecordE,String purchaseOrderOutCode,String requestService,List<String> skuCodes){
        LockStockDTO lockStockDTO = new LockStockDTO();
        String message="200";
        try {
            log.info("采购入库回调开始推送mq到订单中心:采购单号：=>{}",JSON.toJSONString(lockStockDTO));
            RealWarehouse realWarehouse = realWarehouseService.findByRealWarehouseId(warehouseRecordE.getRealWarehouseId());
            lockStockDTO.setFactoryCode(realWarehouse.getFactoryCode());
            lockStockDTO.setRealWarehouseCode(realWarehouse.getRealWarehouseOutCode());
            PwOutDTO pwOutDTO = purchaseCenterFacade.queryByOutRecordCode(purchaseOrderOutCode);
            if (null != pwOutDTO) {
                log.info("已查询到采购中心采购单:采购单号：=>{},来源单号=>{}",purchaseOrderOutCode,pwOutDTO.getSourceOrderNo());
                lockStockDTO.setRecordCode(pwOutDTO.getSourceOrderNo());
            }else{
                log.info("未查询到采购中心采购单:采购单号：=>{}",purchaseOrderOutCode);
            }
            lockStockDTO.setSkuCodes(skuCodes);
            log.info("采购入库回调开始推送mq到订单中心:采购入库单号：=>{},参数:=>{}",warehouseRecordE.getRecordCode(),JSON.toJSONString(lockStockDTO));
            //延迟推送MQ
            CustomRocketMQProducerClient.send(JSON.toJSONString(lockStockDTO), CustomRocketMQEnum.MQ_PURCHASE_IN_NOTIFY_PUSH.getCode(), warehouseRecordE.getRecordCode(), null);
        } catch (Exception e) {
            log.error("生产消息异常: ", e);
            message=e.getMessage();
        }finally {
            sapInterfaceLogRepository.saveCallBackInterFaceLog(1, warehouseRecordE.getRecordCode(), requestService,
                    JSON.toJSONString(lockStockDTO), message, true);
        }
    }

    @Override
    public List<PurchaseReceiptDTO> queryRwBatchInfoByPWCode(String purchaseRecordCode) {
        log.info("采购中心开始查询收货信息:{}", purchaseRecordCode);
        String recordCode = warehouseRecordRepository.queryRecordCodeBySapRecordCode(purchaseRecordCode);
        if (StringUtils.isBlank(recordCode)) {
            //委外新流程,sap单号为采购预约单号
            PurchaseOrderE purchaseOrderE = frPurchaseOrderRepository.queryByOutRecordCode(purchaseRecordCode);
            //如果是委外成品采购单入库单
            if (purchaseOrderE != null && Objects.equals(purchaseOrderE.getRecordType(), FrontRecordTypeVO.OUTSOURCE_PURCHASE_END_PRODUCT_RECORD.getType())) {
                //sap 单号为采购预约单号
                List<String> recordCodeList = frontWarehouseRecordRelationRepository.getRecordCodeByFrontRecordCode(purchaseOrderE.getRecordCode());
                if (CollectionUtils.isNotEmpty(recordCodeList)) {
                    recordCode = recordCodeList.get(0);
                }
            }
        }
        List<PurchaseReceiptDTO> purchaseReceiptList = new ArrayList<>();
        if (StringUtils.isNotBlank(recordCode)) {
            List<RwBatchDTO> rwBatchDTOS = rwBatchRepository.queryRwBatchByRecordCode(recordCode);
            if (CollectionUtils.isNotEmpty(rwBatchDTOS)) {
                RealWarehouse realWarehouse = realWarehouseService.findByRealWarehouseId(rwBatchDTOS.get(0).getRealWarehouseId());
                AlikAssert.isNotNull(realWarehouse, ResCode.STOCK_ERROR_1003, ResCode.STOCK_ERROR_1003_DESC + "实仓不存在:realWarehouseId" + rwBatchDTOS.get(0).getRealWarehouseId());
                purchaseReceiptList = rwBatchDTOS.stream().map(x -> {
                    PurchaseReceiptDTO purchaseReceiptDTO = new PurchaseReceiptDTO();
                    purchaseReceiptDTO.setSkuCode(x.getSkuCode());
                    String produceDateStr = "";
                    Date productDate = x.getProductDate();
                    if (Objects.isNull(productDate)) {
                        produceDateStr = x.getBatchCode();
                    } else {
                        produceDateStr = DateUtil.format(productDate, "yyyyMMdd");
                    }
                    purchaseReceiptDTO.setProductDate(produceDateStr);
                    purchaseReceiptDTO.setWmsRecordCode(x.getWmsRecordCode());
                    purchaseReceiptDTO.setFactoryName(realWarehouse.getRealWarehouseName());
                    purchaseReceiptDTO.setReceiveDate(DateUtil.format(x.getCreateTime(), "yyyyMMdd"));
                    purchaseReceiptDTO.setActualQty(x.getActualQty());
                    purchaseReceiptDTO.setWarehouseRecordCode(x.getRecordCode());
                    purchaseReceiptDTO.setBathCode(x.getBatchCode());
                    return purchaseReceiptDTO;
                }).collect(Collectors.toList());
                return purchaseReceiptList;
            }
        }
        log.info("查询收货批次信息返回:{}", JSON.toJSONString(purchaseReceiptList));
        return purchaseReceiptList;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void receiptCompletedNotify(String recordCode) {
        log.info("通知收货完成:{}", recordCode);
        WarehouseRecordE warehouseRecordE = warehouseRecordRepository.queryWarehouseRecordByRecordCode(recordCode);
        AlikAssert.isNotNull(warehouseRecordE, ResCode.STOCK_ERROR_1002, ResCode.STOCK_ERROR_1002_DESC + "出入库单不存在 recordCode=" + recordCode);
        if (Objects.isNull(warehouseRecordE.getAppId()) || Objects.equals(warehouseRecordE.getAppId(), "0")) {
            log.warn("老单据不做处理{}", recordCode);
            return;
        }
        if (!Objects.equals(warehouseRecordE.getRecordStatus(), WarehouseRecordStatusVO.IN_ALLOCATION.getStatus())) {
            throw new RomeException(ResCode.STOCK_ERROR_1002, "该单据未入库 不能进行确认收货完成操作 recordCode=" + recordCode);
        }
        if (Objects.nonNull(warehouseRecordE.getSyncFulfillmentStatus()) && Objects.equals(warehouseRecordE.getSyncFulfillmentStatus().intValue(), 30)) {
            log.warn("该单据已经确认收货完成{}", recordCode);
            return;
        }
        //限制非外采单据报错
        List<Integer> purchaseType = Arrays.asList(WarehouseRecordTypeVO.PURCHASE_IN_WAREHOUSE_RECORD.getType(), WarehouseRecordTypeVO.OUTSOURCE_PURCHASE_END_PRODUCT_IN_RECORD.getType());
        if (!purchaseType.contains(warehouseRecordE.getRecordType())) {
            throw new RomeException(ResCode.STOCK_ERROR_1002, "非外采类型单据不能进行此操作! recordCode=" + recordCode);
        }
        List<WarehouseRecordDetail> warehouseRecordDetailList = warehouseRecordRepository.queryDetailListByRecordCode(recordCode);
        AlikAssert.isNotEmpty(warehouseRecordDetailList, ResCode.STOCK_ERROR_1002, ResCode.STOCK_ERROR_1002_DESC + " 出库单明细为空");
        warehouseRecordE.setWarehouseRecordDetails(warehouseRecordDetailList);
        String frontRecordCode = frontWarehouseRecordRelationRepository.getFrontRecordCodeByRecordCode(recordCode);
        AlikAssert.isNotBlank(frontRecordCode, ResCode.STOCK_ERROR_1002, ResCode.STOCK_ERROR_1002_DESC + " 大仓采购前置单和后置单关联关系不存在");
        PurchaseOrderE purchaseOrderE = frPurchaseOrderRepository.queryByCode(frontRecordCode);
        List<PurchaseOrderDetailE> purchaseOrderDetail = frPurchaseOrderRepository.queryDetailsByFrontIds(Arrays.asList(purchaseOrderE.getId()));
        AlikAssert.isNotEmpty(purchaseOrderDetail, ResCode.STOCK_ERROR_1017, ResCode.STOCK_ERROR_1017_DESC);
        Map<String, WarehouseRecordDetail> detailMap = warehouseRecordE.getWarehouseRecordDetails().stream().collect(Collectors.toMap(x -> x.getSkuCode(), Function.identity(), (v1, v2) -> v2));
        //更新已收货完成状态标识
        warehouseRecordRepository.updateReceiptCompleted(recordCode);
        //更新通知待质检
        int res = warehouseRecordRepository.updateSyncPurchaseStatusByRecordCodes(Arrays.asList(recordCode));
        AlikAssert.isTrue(res > 0, ResCode.STOCK_ERROR_1002, "更新通知待质检状态失败:recordCode=" + recordCode);
//        this.notifyPurchaseRecFinish(purchaseOrderE,purchaseOrderDetail,detailMap);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updatePurchaseInfo(Long modifier, String recordCode, String warehouseRecordCode) {
        receiptCompletedNotify(warehouseRecordCode);
        int result = frPurchaseOrderRepository.updatePurchaseInfo(modifier, recordCode);
        AlikAssert.isTrue(result > 0, ResCode.STOCK_ERROR_1002, "更新采购前置单信息失败" + recordCode);
    }

    @Override
    public List<OutRecordCodeResultDTO> queryOutRecordCode(OutRecordCodeParamDTO appointRecordCode) {
        return frPurchaseOrderRepository.queryOutRecordCode(appointRecordCode.getAppointRecordCode());
    }

    @Override
    public void skuStatusChangeNotice(SkuStatusChangeSendMessageDTO messageDTO) {
        if (!(Objects.equals(SaleStatusVO.OFFICIAL.getSaleStatusCode(), messageDTO.getPreStatus()) && Objects.equals(SaleStatusVO.NEW.getSaleStatusCode(), messageDTO.getStatus()))
                && !(Objects.equals(SaleStatusVO.OLDUP.getSaleStatusCode(), messageDTO.getPreStatus()) && Objects.equals(SaleStatusVO.NORMAL.getSaleStatusCode(), messageDTO.getStatus()))) {
            log.info("品类通知库存中心商品状态变更不匹配,无需处理，messageDTO：{}", JSON.toJSONString(messageDTO));
            return;
        }
        //正式→新品  老品新上→正常
        if (StringUtils.isEmpty(messageDTO.getSkuCode())) {
            log.error("品类通知库存中心商品状态变更,商品编码不能为空，messageDTO：{}", JSON.toJSONString(messageDTO));
            throw new RomeException(ResCode.STOCK_ERROR_1001, "商品编码不能为空");
        }
        Date pushDate;
        BigDecimal pushQty;
        if (CollectionUtils.isNotEmpty(messageDTO.getPurchaseEntryNoList())) {
            //指定单据查采购单,取入库时间早的排序
            //查询是否存在委外单据
            List<String> appointRecordCodeList =frOutsourcingRepository.queryAppointRecordCodeByOutRecordCodeList(messageDTO.getPurchaseEntryNoList());
            if(CollectionUtils.isNotEmpty(appointRecordCodeList)){
                messageDTO.getPurchaseEntryNoList().addAll(appointRecordCodeList);
            }
            List<RwBatchDo> rwBatchEList = rwBatchRepository.queryByPWRecordCodeAndSkuCode(messageDTO.getPurchaseEntryNoList(), messageDTO.getSkuCode());
            if (CollectionUtils.isEmpty(rwBatchEList)) {
                log.error("品类通知库存中心商品状态变更,指定单据查采购单结果为空,不处理，messageDTO：{}", JSON.toJSONString(messageDTO));
                return;
            }
            //入库时间早的，默认取第一个
            RwBatchDo rwBatchDo = rwBatchEList.get(0);
            pushDate = rwBatchDo.getQualityTime();
            if(StringUtils.isNotEmpty(rwBatchDo.getQualityCode())){
                pushQty = rwBatchEList.stream().filter(v -> Objects.equals(rwBatchDo.getQualityCode(), v.getQualityCode())).map(RwBatchDo::getActualQty).reduce(BigDecimal::add).orElse(BigDecimal.ZERO);
            }else{
                pushQty = rwBatchEList.stream().filter(v -> Objects.equals(rwBatchDo.getWmsRecordCode(), v.getWmsRecordCode())).map(RwBatchDo::getActualQty).reduce(BigDecimal::add).orElse(BigDecimal.ZERO);
            }
        } else if (Objects.nonNull(messageDTO.getIsManualChange()) && messageDTO.getIsManualChange()) {
            //手动变更单据
            WarehouseRecordDetailDo warehouseRecordDetailDo = warehouseRecordRepository.queryPurchaseWarehouseRecordBySkuCode(messageDTO.getSkuCode());
            if (Objects.isNull(warehouseRecordDetailDo)) {
                log.error("品类通知库存中心商品状态,手动变更单据查询为空,不处理，messageDTO：{}", JSON.toJSONString(messageDTO));
                return;
            }
            pushDate = warehouseRecordDetailDo.getOutOrInTime();
            pushQty = warehouseRecordDetailDo.getActualQty();
        } else {
            //包装单
            WarehouseRecordDetailDo warehouseRecordDetailDo = warehouseRecordRepository.queryPackageWarehouseRecordBySkuCode(messageDTO.getSkuCode());
            if (Objects.isNull(warehouseRecordDetailDo)) {
                log.error("品类通知库存中心商品状态,包装单查询为空,不处理，messageDTO：{}", JSON.toJSONString(messageDTO));
                return;
            }
            pushDate = warehouseRecordDetailDo.getOutOrInTime();
            pushQty = warehouseRecordDetailDo.getActualQty();
        }
        //大货验收信息同步mpd
        MpdAcceptanceDTO mpdAcceptanceDTO = new MpdAcceptanceDTO(messageDTO.getSkuCode(), DateUtil.formatDateTime(pushDate), String.valueOf(pushQty));
        try {
            mpdFacade.acceptance(mpdAcceptanceDTO);
        } catch (Exception e) {
            log.error("大货验收信息同步mpd异常:{},messageDTO：{}",e.getMessage(),JSON.toJSONString(messageDTO), e);
            throw e;
        }
    }

    /**
     * 第一次收货通知采购中心
     *
     * @param purchaseEntryNo
     * @param warehouseRecordE
     */
    public void notifyPurchaseReceiptBegin(String purchaseEntryNo, WarehouseRecordE warehouseRecordE) {
        if (Objects.isNull(warehouseRecordE.getTenantId()) || !Objects.equals(warehouseRecordE.getTenantId().intValue(), 10)) {
            Response response = purchaseCenterFacade.notifyPurchaseReceiptBegin(purchaseEntryNo);
            AlikAssert.isTrue(Objects.equals(response.getCode(), "0"), ResCode.STOCK_ERROR_1003, "通知采购中心开始收货异常");
            //更新第一次收货通知采购中心标识
            warehouseRecordRepository.updateTenantIdByRecordCode(warehouseRecordE.getRecordCode());
        }
    }


    /**
     * 针对成品特殊处理流程
     *
     * @param warehouseRecordE
     */
    private WarehouseRecordE handlerEndProduct(WarehouseRecordE warehouseRecordE, FrontWarehouseRecordRelationDO relationDO, String wmsRecordCode) {
        log.info("成品入库回调:{}", JSON.toJSONString(warehouseRecordE));
        RealWarehouse realWarehouse = realWarehouseService.findByRealWarehouseId(warehouseRecordE.getRealWarehouseId());
        AlikAssert.isNotNull(realWarehouse, ResCode.STOCK_ERROR_1004, "实仓不存在:realWarehouseId=" + warehouseRecordE.getRealWarehouseId());
        warehouseRecordE.setFactoryCode(realWarehouse.getFactoryCode());
        //更新明细数量为本次实际收货数量
        List<RwBatchE> rwBatchES = rwBatchRepository.queryBywmsRecordCode(warehouseRecordE.getRecordCode(), wmsRecordCode);
        AlikAssert.isNotEmpty(rwBatchES, ResCode.STOCK_ERROR_1002, "收货明细不存在:wmsRecordCode=" + wmsRecordCode);
        Map<String, RwBatchE> rwBatchMap = rwBatchES.stream().collect(Collectors.toMap(RwBatchE::getSkuCode, Function.identity(), (v1, v2) -> v2));
        warehouseRecordE.getWarehouseRecordDetails().forEach(x -> {
            RwBatchE rwBatchE = rwBatchMap.get(x.getSkuCode());
            if (Objects.nonNull(rwBatchE)) {
                x.setActualQty(rwBatchE.getActualQty());
            }
        });
        WarehouseRecordE outWarehouseRecodeE = null;
        //查询成品对应的原料出库单信息
        List<Long> warehouseRecordList = frontWarehouseRecordRelationRepository.queryWarehouseRecordIdByRecord(relationDO.getFrontRecordCode());
        if (CollectionUtils.isNotEmpty(warehouseRecordList)) {
            //筛选出成品入库对应的出库单信息
            List<Long> outRecordList = warehouseRecordList.stream().filter(x -> !Objects.equals(warehouseRecordE.getId(), x)).collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(outRecordList)) {
                outWarehouseRecodeE = warehouseRecordRepository.getRecordWithDetailById(outRecordList.get(0));
            }
        }
        AlikAssert.isNotNull(outWarehouseRecodeE, ResCode.STOCK_ERROR_1017, "委外原料单" + warehouseRecordE.getSapOrderCode() + ResCode.STOCK_ERROR_1017_DESC);
        RealWarehouse outRowRealWarehouse = realWarehouseService.findByRealWarehouseId(outWarehouseRecodeE.getRealWarehouseId());
        AlikAssert.isNotNull(outRowRealWarehouse, ResCode.STOCK_ERROR_1023, ResCode.STOCK_ERROR_1023_DESC);
        List<WarehouseRecordDetail> rowWarehouseDetailList = super.buildOutWarehouseRecordDetailList(warehouseRecordE, PurchaseOrderConsts.ENTRY_CALLBACK_RECORD, outRowRealWarehouse.getFactoryCode());
        //更新实际出库数量
        if (CollectionUtils.isNotEmpty(rowWarehouseDetailList)) {
            //转化成基本单位
            Map<String, WarehouseRecordDetail> realDetailMap = rowWarehouseDetailList.stream().collect(Collectors.toMap(x -> x.getSkuCode(), Function.identity(), (v1, v2) -> v2));
            outWarehouseRecodeE.getWarehouseRecordDetails().forEach(x -> {
                WarehouseRecordDetail realDetail = realDetailMap.get(x.getSkuCode());
                if (Objects.nonNull(realDetail) && Objects.nonNull(realDetail.getActualQty())) {
                    x.setActualQty(realDetail.getActualQty());
                } else {
                    throw new RomeException("1001", "bom信息发生变化：" + x.getSkuCode() + " 在新bom找不到");
                }
            });
        }
        //更新数据库明细实际出库
        Integer result = warehouseRecordRepository.updateActualQtyBatch(outWarehouseRecodeE.getWarehouseRecordDetails());
        AlikAssert.isTrue(result.compareTo(0) > 0, ResCode.STOCK_ERROR_1002, ResCode.STOCK_ERROR_1002_DESC + "更新出库单实收数量失败");
        //更新为已出库
        warehouseRecordRepository.updateRecordToOutAllocation(outWarehouseRecodeE.getId(), DateUtil.now());
        return outWarehouseRecodeE;
    }


    /**
     * 构建扣减库存对象
     *
     * @param outWarehouseRecodeE
     * @return
     */
    private CoreRealStockOpDO initDecreaseStockObj(WarehouseRecordE outWarehouseRecodeE) {
        List<CoreRealStockOpDetailDO> decreaseDetails = new ArrayList<>();
        for (WarehouseRecordDetail detail : outWarehouseRecodeE.getWarehouseRecordDetails()) {
            //数量为0的不处理库存的增加和减少
            if (BigDecimal.ZERO.compareTo(detail.getPlanQty()) == 0) {
                continue;
            }
            CoreRealStockOpDetailDO coreRealStockOpDetailDO = new CoreRealStockOpDetailDO();
            coreRealStockOpDetailDO.setSkuId(detail.getSkuId());
            coreRealStockOpDetailDO.setSkuCode(detail.getSkuCode());
            coreRealStockOpDetailDO.setRealQty(detail.getActualQty());
            coreRealStockOpDetailDO.setRealWarehouseId(outWarehouseRecodeE.getRealWarehouseId());
            coreRealStockOpDetailDO.setCheckBeforeOp(true);
            decreaseDetails.add(coreRealStockOpDetailDO);
        }
        CoreRealStockOpDO coreRealStockOpDO = new CoreRealStockOpDO();
        coreRealStockOpDO.setRecordCode(outWarehouseRecodeE.getRecordCode());
        coreRealStockOpDO.setTransType(outWarehouseRecodeE.getRecordType());
        coreRealStockOpDO.setDetailDos(decreaseDetails);
        return coreRealStockOpDO;
    }


    /**
     * 将批次信息转换为按skuid维度的出入库明细
     *
     * @param rwBatchES
     * @return
     */
    private List<WarehouseRecordDetail> batchInfoToWarehouseRecordDetails(List<RwBatchE> rwBatchES) {
        List<WarehouseRecordDetail> result = new ArrayList<>();
        Map<Long, List<RwBatchE>> skuBatchInfoMap = RomeCollectionUtil.listforListMap(rwBatchES, "skuId");
        for (Map.Entry<Long, List<RwBatchE>> mapEntry : skuBatchInfoMap.entrySet()) {
            WarehouseRecordDetail detail = new WarehouseRecordDetail();
            detail.setSkuId(mapEntry.getKey());
            BigDecimal actualQty = BigDecimal.ZERO;
            for (RwBatchE batchE : mapEntry.getValue()) {
                detail.setSkuCode(batchE.getSkuCode());
                actualQty = actualQty.add(batchE.getActualQty());
            }
            detail.setActualQty(actualQty);
            result.add(detail);
        }
        return result;
    }

    /**
     * 增加质检库存/质检库存移动到不合格的DO,无需关心单据级别的sku分配关系
     *
     * @param details
     * @param rwId
     * @param recordCode
     * @param recordType
     * @return
     */
    private CoreRealStockOpDO initStockObj(List<WarehouseRecordDetail> details, Long rwId, String recordCode, Integer recordType) {
        List<CoreRealStockOpDetailDO> detailDos = new ArrayList<>();
        for (WarehouseRecordDetail detail : details) {
            if (detail.getActualQty().compareTo(BigDecimal.ZERO) > 0) {
                CoreRealStockOpDetailDO coreRealStockOpDetailDO = new CoreRealStockOpDetailDO();
                coreRealStockOpDetailDO.setSkuId(detail.getSkuId());
                coreRealStockOpDetailDO.setSkuCode(detail.getSkuCode());
                coreRealStockOpDetailDO.setQualityQty(detail.getActualQty());
                coreRealStockOpDetailDO.setRealWarehouseId(rwId);
                detailDos.add(coreRealStockOpDetailDO);
            }
        }
        CoreRealStockOpDO coreRealStockOpDO = new CoreRealStockOpDO();
        coreRealStockOpDO.setRecordCode(recordCode);
        coreRealStockOpDO.setTransType(recordType);
        coreRealStockOpDO.setDetailDos(detailDos);
        return coreRealStockOpDO;
    }


    /**
     * 无需质检，直接增加可用库存的DO
     *
     * @param details
     * @param rwId
     * @param recordCode
     * @param recordType
     * @param type       1--无需质检 加可用库存 2--需要质检 加质检库存
     * @return
     */
    private CoreRealStockOpDO initStockObjForPre(List<WarehouseRecordDetail> details, Long rwId
            , String recordCode, Integer recordType, Map<String, List<SkuLabelDTO>> skuLabelMap) {
        List<CoreVirtualStockOpDO> cvsList = Lists.newArrayList();
        CoreRealStockOpDO coreRealStockOpDO = new CoreRealStockOpDO();
        List<CoreRealStockOpDetailDO> defaultIncreaseDetails = new ArrayList<>();
        for (WarehouseRecordDetail detail : details) {
            //数量为0的不处理库存的增加和减少
            if (BigDecimal.ZERO.compareTo(detail.getActualQty()) == 0) {
                continue;
            }
            CoreRealStockOpDetailDO coreRealStockOpDetailDO = new CoreRealStockOpDetailDO();
            coreRealStockOpDetailDO.setSkuId(detail.getSkuId());
            coreRealStockOpDetailDO.setSkuCode(detail.getSkuCode());
            //质检转可用
            coreRealStockOpDetailDO.setQualityQty(detail.getActualQty());
            //当前实仓对应的多虚仓库存操作
            if (skuLabelMap.containsKey(detail.getSkuCode())) {
                for (SkuLabelDTO e : skuLabelMap.get(detail.getSkuCode())) {
                    lockStockContext.initVirtualObj(coreRealStockOpDetailDO, e.getVirtualWarehouseId()
                            , rwId, recordCode, recordType, cvsList, coreRealStockOpDO, e.getQty());
                }
            }
            coreRealStockOpDetailDO.setRealWarehouseId(rwId);
            //该sku没有配置比例或绝对数，走默认的
            defaultIncreaseDetails.add(coreRealStockOpDetailDO);
        }
        coreRealStockOpDO.setRecordCode(recordCode);
        coreRealStockOpDO.setTransType(recordType);
        coreRealStockOpDO.setDetailDos(defaultIncreaseDetails);
        if (!org.springframework.util.CollectionUtils.isEmpty(cvsList)) {
            coreRealStockOpDO.setVirtualStockByCalculateDOs(cvsList);
        }
        return coreRealStockOpDO;
    }


    /**
     * 根据skulist 来过滤得到skuId在目标id集合里面的details列表
     *
     * @param detailList
     * @param skuIds
     * @param directSelect 正选或反选  true正选
     * @return
     */
    private List<WarehouseRecordDetail> filterDetailBySkuIds(List<WarehouseRecordDetail> detailList, List<Long> skuIds, boolean directSelect) {
        List<WarehouseRecordDetail> details = new ArrayList<>();
        Set<Long> skuIdSet = new HashSet<>(skuIds);
        for (WarehouseRecordDetail detail : detailList) {
            if (directSelect && skuIdSet.contains(detail.getSkuId())) {
                //正选包含
                details.add(detail);
            } else if (!directSelect && !skuIdSet.contains(detail.getSkuId())) {
                //反选 不包含
                details.add(detail);
            }
        }
        return details;
    }

    /**
     * 相同sku物料多行合并处理，设置比例或绝对数的时候是sku维度并不是行号维度
     *
     * @param list
     */
    private List<PurchaseOrderDetailPageDTO> mergeBySku(List<PurchaseOrderDetailPageDTO> list) {
        List<PurchaseOrderDetailPageDTO> res = new ArrayList<>();
        Map<Long, List<PurchaseOrderDetailPageDTO>> tempMap = RomeCollectionUtil.listforListMap(list, "skuId");
        for (Map.Entry<Long, List<PurchaseOrderDetailPageDTO>> entry : tempMap.entrySet()) {
            PurchaseOrderDetailPageDTO dto = entry.getValue().get(0);
            for (int i = 1; i < entry.getValue().size(); i++) {
                dto.setSkuQty(dto.getSkuQty().add(entry.getValue().get(i).getSkuQty()));
            }
            res.add(dto);
        }
        return res;
    }

    /**
     * @param recordId
     * @param needQuerySystemSku 是否需要查询系统sku级别的配置
     * @return
     */
    private PurchaseOrderAllocConfigPageDTO queryAllocConfigInfo(Long recordId, boolean needQuerySystemSku) {
        PurchaseOrderAllocConfigPageDTO result = new PurchaseOrderAllocConfigPageDTO();
        PurchaseOrderE purchaseOrderE = frPurchaseOrderRepository.queryById(recordId);
        if (purchaseOrderE != null) {
            //根据单据id查询该单据下sku的明细
            List<PurchaseOrderDetailE> purchaseOrderDetailEList = frPurchaseOrderRepository.queryDetailsByRecordId(recordId);
            List<PurchaseOrderDetailPageDTO> purchaseOrderDetailDTOList = frPurchaseOrderConvertor.purchaseDetailEntityListToDtoList(purchaseOrderDetailEList);

            //相同sku物料多行合并处理，设置比例的时候是sku维度并不是行号维度
            purchaseOrderDetailDTOList = this.mergeBySku(purchaseOrderDetailDTOList);

            List<VirtualWarehouse> virtualWarehouseList = virtualWarehouseService.queryByRealWarehouseId(purchaseOrderE.getRealWarehouseId());

            //根据单据编码查询该单据配置的sku级别的实仓虚仓配比关系
            Map<String, RecordRealVirtualStockSyncRelationDO> configRelationMap = recordRealVirtualStockSyncRelationService.queryRelationMapByRecordCode(purchaseOrderE.getRecordCode());
            RealWarehouse realWarehouse = realWarehouseService.findByRealWarehouseId(purchaseOrderE.getRealWarehouseId());

            List<SkuInfoExtDTO> skuInfoList = null;
            try {
                //第三方接口异常 ，只打印错误日志，不影响主流程
                skuInfoList = skuFacade.skusBySkuId(RomeCollectionUtil.getValueList(purchaseOrderDetailDTOList, "skuId"));
            } catch (Exception e) {
                log.error(e.getMessage(), e);
            }
            Map<Long, SkuInfoExtDTO> skuInfoMap = RomeCollectionUtil.listforMap(skuInfoList, "id");
            //设置sku级别的实仓虚仓分配比例
            for (PurchaseOrderDetailPageDTO dto : purchaseOrderDetailDTOList) {
                //查询该sku系统级别的配置关系
                Map<Long, SkuRealVirtualStockSyncRelationDO> skuConfigMap = new HashMap<>();
                if (needQuerySystemSku) {
                    List<SkuRealVirtualStockSyncRelationDO> skuRelationDOs = skuRealVirtualStockSyncRelationMapper.querySyncRateBySkuIdWId(dto.getSkuId(), purchaseOrderE.getRealWarehouseId(), null);
                    skuConfigMap = RomeCollectionUtil.listforMap(skuRelationDOs, "virtualWarehouseId");
                }
                //这里必须用拷贝的_virtualWarehouseList对象来使用，因为各个sku下面的实仓配比关系有可能是不一样
                List<VirtualWarehouse> virtualWarehouses = copyVirtualWarehouse(virtualWarehouseList);
                for (VirtualWarehouse warehouse : virtualWarehouses) {
                    String key = dto.getSkuId() + "_" + warehouse.getId();
                    if (configRelationMap.containsKey(key)) {
                        //该sku默认了单据级别的实仓虚仓配比关系，只要配了该sku的分配关系，那么该sku必须指定所属单据所对应的的实仓对应的所有虚仓
                        warehouse.setConfigSyncRate(configRelationMap.get(key).getSyncRate());
                        warehouse.setAllotType(configRelationMap.get(key).getAllotType());
                    }
                    //系统sku级别比例覆盖默认仓到仓的比例
                    if (skuConfigMap.containsKey(warehouse.getId())) {
                        warehouse.setSyncRate(skuConfigMap.get(warehouse.getId()).getSyncRate());
                    }
//					else {
//						//只要配了该sku的分配关系，那么该sku必须指定所属单据所对应的的实仓对应的所有虚仓,所以只要有一个不符合就直接可以跳出循环。
//						break;
//					}
                }
                if (skuInfoMap.containsKey(dto.getSkuId())) {
                    dto.setSkuName(skuInfoMap.get(dto.getSkuId()).getName());
                } else {
                    dto.setSkuName("");
                }

                //默认的实仓虚仓分配关系，每个sku都使用该默认的比例
                dto.setVmSyncRate(virtualWarehouses);
            }
            result.setRecordStatus(purchaseOrderE.getRecordStatus());
            result.setRecordId(purchaseOrderE.getId());
            result.setRecordCode(purchaseOrderE.getRecordCode());
            result.setRealWarehouseId(purchaseOrderE.getRealWarehouseId());
            result.setRealWarehouseCode(realWarehouse.getRealWarehouseCode());
            result.setRealWarehouseName(realWarehouse.getRealWarehouseName());
            result.setSkuDetails(purchaseOrderDetailDTOList);
        }
        return result;
    }

    private List<VirtualWarehouse> copyVirtualWarehouse(List<VirtualWarehouse> origin) {
        List<VirtualWarehouse> desc = new ArrayList<>();
        try {
            for (VirtualWarehouse virtualWarehouse : origin) {
                VirtualWarehouse item = new VirtualWarehouse();
                BeanUtils.copyProperties(virtualWarehouse, item);
                desc.add(item);
            }
        } catch (Exception e) {
            log.error(e.getMessage(), e);
        }
        return desc;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void receivePurchaseReturn(PurchaseReturnDTO purchaseReturnDTO) {
        log.info("大仓采购退货单入参:{}", JSON.toJSONString(purchaseReturnDTO));
        if (!Objects.equals(purchaseReturnDTO.getPurchaseRecordType(), PurchaseTypeVO.RETURN_ONE_IN_RECORD.getType()) && !Objects.equals(purchaseReturnDTO.getPurchaseRecordType(), PurchaseTypeVO.RETURN_PURCHASE_IN_RECORD.getType())) {
            throw new RomeException(ResCode.STOCK_ERROR_1002, ResCode.STOCK_ERROR_1002_DESC + "大仓采购退货单类型错误!");
        }
        List<String> businessTypeList = PurchaseOrderConsts.PURCHASE_BUSINESS_TYPE_MAP.get(purchaseReturnDTO.getPurchaseRecordType());
        if (CollUtil.isNotEmpty(businessTypeList)) {
            if (!businessTypeList.contains(purchaseReturnDTO.getBusinessType().toUpperCase())) {
                throw new RomeException(ResCode.STOCK_ERROR_1002, ResCode.STOCK_ERROR_1002_DESC + "大仓采购退货业务类型错误!");
            }
        }
        //幂等性判断
        if (frPurchaseOrderRepository.judgeExistByOutRecordCode(purchaseReturnDTO.getOutRecordCode())) {
            return;
        }
        RealWarehouse realWarehouse = realWarehouseService.findByRealWarehouseOutCodeAndFactoryCode(purchaseReturnDTO.getRealWarehouseCode(), purchaseReturnDTO.getFactoryCode());
        AlikAssert.isNotNull(realWarehouse, ResCode.STOCK_ERROR_1011, ResCode.STOCK_ERROR_1011_DESC);
        PurchaseOrderE purchaseOrderE = frPurchaseOrderConvertor.purchaseDtoToEntity(purchaseReturnDTO);
        purchaseOrderE.setRealWarehouseId(realWarehouse.getId());
        // 创建采购退货供应商前置单及明细
        purchaseOrderE.addReturnFrontRecord();
        PurchaseWarehouseRecordE warehouseRecord = entityFactory.createEntity(PurchaseWarehouseRecordE.class);
        warehouseRecord.setRecordStatus(WarehouseRecordStatusVO.INIT.getStatus());
        //根据前置单生成出库单
        warehouseRecord.createOutRecordByFrontRecord(purchaseOrderE);
        //一件代发退供单无需确认
        if (Objects.equals(purchaseReturnDTO.getPurchaseRecordType(), PurchaseTypeVO.RETURN_ONE_IN_RECORD.getType())) {
            warehouseRecord.setSyncWmsStatus(WmsSyncStatusVO.UNSYNCHRONIZED.getStatus());
        }
        // 创建采购退货供应商出库单及明细
        warehouseRecord.addWarehouseRecord();
        //锁定实仓库存
        CoreRealStockOpDO coreRealStockOpDO = null;
        boolean isSuccess = false;
        try {
            coreRealStockOpDO = warehouseRecord.initLockStockObj(warehouseRecord.getRealWarehouseId());
            coreRealWarehouseStockRepository.lockStock(coreRealStockOpDO);
            //kibana日志输出
            KibanaLogUtils.printKibanaRecordInfo(purchaseOrderE.getOutRecordCode(), "", StockRecordInfoTypeVo.PURCHASE_RETURN.getType(), StockRecordInfoTypeVo.PURCHASE_RETURN.getDesc(), "receivePurchaseReturn", purchaseReturnDTO);
            isSuccess = true;
        } catch (RomeException e) {
            throw new RomeException(e.getCode(), e.getMessage());
        } catch (Exception e) {
            throw new RomeException(ResCode.STOCK_ERROR_1003, ResCode.STOCK_ERROR_1003_DESC);
        } finally {
            if (!isSuccess) {
                RedisRollBackFacade.redisRollBack(coreRealStockOpDO);
            }
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void cancelPurchaseReturnRecord(String sapRecordCode) {
        PurchaseOrderE purchaseOrderE = frPurchaseOrderRepository.queryByOutRecordCode(sapRecordCode);
        AlikAssert.isNotNull(purchaseOrderE, ResCode.STOCK_ERROR_1014, ResCode.STOCK_ERROR_1014_DESC);
        AlikAssert.isTrue(FrontRecordTypeVO.WAREHOUSE_RETURN_GOODS_RECORD.getType().equals(purchaseOrderE.getRecordType()), ResCode.STOCK_ERROR_1002, ResCode.STOCK_ERROR_1002_DESC + ":该接口只支持取消退厂单");

        List<Long> warehouseIds = warehouseRecordRepository.queryWarehouseIdByFrontId(purchaseOrderE.getId(), purchaseOrderE.getRecordType());
        AlikAssert.isTrue(warehouseIds != null && warehouseIds.size() > 0, ResCode.STOCK_ERROR_1017, ResCode.STOCK_ERROR_1017_DESC);
        Long warehouseId = warehouseIds.get(0);
        WarehouseRecordE warehouseRecordE = warehouseRecordRepository.getRecordWithDetailByIdWithDeleted(warehouseId);
        if (Objects.equals(WarehouseRecordStatusVO.DISABLED.getStatus(), warehouseRecordE.getRecordStatus())) {
            log.warn("该单据已取消:{}", warehouseRecordE.getRecordCode());
            return;
        }
        if (Objects.equals(WarehouseRecordStatusVO.OUT_ALLOCATION.getStatus(), warehouseRecordE.getRecordStatus())) {
            throw new RomeException(ResCode.STOCK_ERROR_1002, "该单据已出库不能取消:recordCode=" + sapRecordCode);
        }
        //判断是否下发wms
        if (WmsSyncStatusVO.SYNCHRONIZED.getStatus().equals(warehouseRecordE.getSyncWmsStatus())) {
            boolean wmsCancelResult = wmsOutService.orderCancel(warehouseRecordE.getRecordCode());
            AlikAssert.isTrue(wmsCancelResult, ResCode.STOCK_ERROR_1205, ResCode.STOCK_ERROR_1205_DESC);
        }
        int executeResult = frPurchaseOrderRepository.updateToCancle(purchaseOrderE.getId());
        AlikAssert.isTrue(executeResult > 0, ResCode.STOCK_ERROR_1001, ResCode.STOCK_ERROR_1001_DESC + ":取消采购单失败 更新前置单状态失败" + sapRecordCode);
        executeResult = warehouseRecordRepository.updateToCanceledPurchaseReturn(warehouseRecordE.getId());
        AlikAssert.isTrue(executeResult > 0, ResCode.STOCK_ERROR_1001, ResCode.STOCK_ERROR_1001_DESC + ":取消采购单失败 更新后置单状态失败" + sapRecordCode);
        //锁定库存还原
        // 大仓出库
        PurchaseWarehouseRecordE outRecordE = entityFactory.createEntity(PurchaseWarehouseRecordE.class);
        CoreRealStockOpDO unlockOpDO = null;
        boolean isSuccess = false;
        try {
            unlockOpDO = outRecordE.packOutAndUnLockOpDO(warehouseRecordE);
            coreRealWarehouseStockRepository.unlockStock(unlockOpDO);
            isSuccess = true;
        } catch (RomeException e) {
            throw e;
        } catch (Exception e) {
            throw new RomeException(ResCode.STOCK_ERROR_1003, ResCode.STOCK_ERROR_1003_DESC);
        } finally {
            if (!isSuccess) {
                RedisRollBackFacade.redisRollBack(unlockOpDO);
            }
        }
    }

    /**
     * 通知确认出库
     */
    @Override
    public void warehouseOutNotify(FrontWarehouseRecordRelationDO frontWarehouseRecordRelationDO) {
        //根据前置单ID，查询前置单领域实体
        PurchaseOrderE frontRecordE = frPurchaseOrderRepository.queryById(frontWarehouseRecordRelationDO.getFrontRecordId());
        AlikAssert.isNotNull(frontRecordE, ResCode.STOCK_ERROR_1014, ResCode.STOCK_ERROR_1014_DESC);
        WarehouseRecordE warehouseRecordE = warehouseRecordRepository.getRecordWithDetailById(frontWarehouseRecordRelationDO.getWarehouseRecordId());
        AlikAssert.isNotNull(warehouseRecordE, ResCode.STOCK_ERROR_1017, ResCode.STOCK_ERROR_1017_DESC);
        // 更新前置单状态为已出库
        frontRecordE.updateToOutAllocation();
        PurchaseWarehouseRecordE outRecordE = entityFactory.createEntity(PurchaseWarehouseRecordE.class);
        // 更新出库单为待过账
        int execResult = warehouseRecordRepository.updateToWaitTransfer(warehouseRecordE.getId());
        AlikAssert.isTrue(execResult > 0, ResCode.STOCK_ERROR_1066, ResCode.STOCK_ERROR_1066_DESC);
        //更新待推送采购中心
        int res = warehouseRecordRepository.updateSyncPurchaseToBeStatus(warehouseRecordE.getId());
        AlikAssert.isTrue(res > 0, ResCode.STOCK_ERROR_1066, "更新推送采购中心状态失败");
        // 大仓出库
        CoreRealStockOpDO outAndUnlockOpDO = null;
        boolean isSuccess = false;
        try {
            outAndUnlockOpDO = outRecordE.packOutAndUnLockOpDO(warehouseRecordE);
            log.info("outAndUnlockOpDO:{}", JSON.toJSON(outAndUnlockOpDO));
            coreRealWarehouseStockRepository.outAndUnlockStock(outAndUnlockOpDO);
            isSuccess = true;
        } catch (RomeException e) {
            throw e;
        } catch (Exception e) {
            throw new RomeException(ResCode.STOCK_ERROR_1003, ResCode.STOCK_ERROR_1003_DESC);
        } finally {
            if (!isSuccess) {
                RedisRollBackFacade.redisRollBack(outAndUnlockOpDO);
            }
        }
    }

    /**
     * 查询收货信息
     *
     * @param recordCode
     * @return
     * @Param wmsRecordCode
     */
    @Override
    public List<ReceiptRecordWithDetailDTO> queryReceiptByRecordCode(String recordCode, String wmsRecordCode) {
        //查询主表信息
        List<ReceiptRecordWithDetailDTO> receiptRecord = receiptRecordRepository.selectReceiptRecordByRecord(recordCode, wmsRecordCode);
        //查询明细信息
        List<RwBatchDTO> rwBatchList = rwBatchRepository.queryRwBatchByRecordCode(recordCode);
        //查询后置单明细
        List<WarehouseRecordDetail> warehouseRecordDetailList = warehouseRecordRepository.queryDetailListByRecordCode(recordCode);
        if (CollUtil.isNotEmpty(warehouseRecordDetailList)) {
            Map<Long, WarehouseRecordDetail> warehouseRecordDetailMap = warehouseRecordDetailList.stream()
                    .collect(Collectors.toMap(x -> x.getId(), Function.identity(), (v1, v2) -> v2));
            if (CollUtil.isNotEmpty(rwBatchList)) {
                rwBatchList.forEach(x -> {
                    WarehouseRecordDetail detail = warehouseRecordDetailMap.get(Long.valueOf(x.getLineNo()));
                    if (Objects.nonNull(detail)) {
                        x.setLineNo(detail.getLineNo());
                        x.setUnitCode(detail.getUnitCode());
                        x.setDeliveryLineNo(detail.getDeliveryLineNo());
                    }
                });
            }
        }
        if (CollUtil.isNotEmpty(rwBatchList)) {
            Map<String, List<RwBatchDTO>> rwBatchMap = rwBatchList.stream().filter(x -> Objects.nonNull(x.getWmsRecordCode())).collect(Collectors.groupingBy(x -> x.getWmsRecordCode()));
            if (CollUtil.isNotEmpty(receiptRecord)) {
                receiptRecord.forEach(x -> {
                    List<RwBatchDTO> rwBatchDTOS = rwBatchMap.get(x.getWmsRecordCode());
                    x.setDetails(rwBatchDTOS);
                });
            }
        }
        return receiptRecord;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void cancelPurchaseOrder(String outRecordCode, Integer recordType) {
        log.info("开始取消大仓采购：outRecordCode={},recordType={}", outRecordCode, recordType);
        if (Objects.equals(recordType, PurchaseTypeVO.RETURN_PURCHASE_IN_RECORD.getType())) {
            this.cancelPurchaseReturnRecord(outRecordCode);
        } else {
            AbstractPurchase purchaseService = getPurchaseBean(recordType);
            AlikAssert.isNotNull(purchaseService, ResCode.STOCK_ERROR_1002, ResCode.STOCK_ERROR_1002_DESC + "参数类型错误");
            purchaseService.cancelRecord(outRecordCode);
        }
    }

    @Override
    public List<RwBatchDTO> queryRwBatchInfoByOutRecordCode(String outRecordCode) {
        PurchaseOrderE purchaseOrderE = frPurchaseOrderRepository.queryByOutRecordCode(outRecordCode);
        AlikAssert.isNotNull(purchaseOrderE, ResCode.STOCK_ERROR_1002, ResCode.STOCK_ERROR_1002_DESC + " 采购单不存在");
        List<String> recordCodeList = frontWarehouseRecordRelationRepository.getRecordCodesByFrontRecordCode(purchaseOrderE.getRecordCode());
        AlikAssert.isNotEmpty(recordCodeList, ResCode.STOCK_ERROR_1002, ResCode.STOCK_ERROR_1002_DESC + "后置单不存在");
        List<RwBatchDTO> list = new ArrayList<>();
        recordCodeList.forEach(x -> {
            //查询明细信息
            List<RwBatchDTO> rwBatchList = rwBatchRepository.queryRwBatchByRecordCode(x);
            list.addAll(rwBatchList);
        });
        return list;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Response purchaseEntryNotify(String recordCode) {
        Response response = null;
        try {
            BuildPurchaseInfoDTO buildPurchaseInfoDTO = super.validateAndBuildPurchaseInfo(recordCode);
            if (Objects.nonNull(buildPurchaseInfoDTO) && Objects.nonNull(buildPurchaseInfoDTO.getWarehouseRecordE()) && !Objects.equals(buildPurchaseInfoDTO.getWarehouseRecordE().getRecordType(), WarehouseRecordTypeVO.SHOP_CHAIN_DIRECT_IN_RECORD.getType())) {
                List<RwBatchDTO> rwBatchList = rwBatchRepository.queryRwBatchByRecordCode(recordCode);
                AlikAssert.isNotEmpty(rwBatchList, ResCode.STOCK_ERROR_1003, "收货信息不存在! recordCode=" + recordCode);
                StockQualityNotifyDTO stockNotifyDTO = initNotifyQuality(buildPurchaseInfoDTO, rwBatchList);
                response = purchaseCenterFacade.notifyQuality(stockNotifyDTO);
                if (!Objects.equals(response.getCode(), "0")) {
                    throw new RomeException(ResCode.STOCK_ERROR_1003, "通知采购中心质检数量异常,msg:" + response.getMsg() + "data:" + JSON.toJSONString(response.getData()));
                }
            } else {
                //针对直送业务-通知采购中心收货完成
                StockReceiveNotifyDTO stockReceiveNotifyDTO = new StockReceiveNotifyDTO();
                stockReceiveNotifyDTO.setPurchaseEntryNo(buildPurchaseInfoDTO.getPurchaseOrderE().getOutRecordCode());
                stockReceiveNotifyDTO.setReceiptNo(buildPurchaseInfoDTO.getWarehouseRecordE().getRecordCode());
                List<SkuReceiveDTO> detail = new ArrayList<>();
                buildPurchaseInfoDTO.getWarehouseRecordE().getWarehouseRecordDetails().forEach(e -> {
                    SkuReceiveDTO skuReceiveDTO = new SkuReceiveDTO();
                    skuReceiveDTO.setSkuCode(e.getSkuCode());
                    skuReceiveDTO.setIsQuality(0);
                    skuReceiveDTO.setReceiveQty(e.getActualQty());
                    skuReceiveDTO.setLineNo(e.getLineNo());
                    detail.add(skuReceiveDTO);
                });
                stockReceiveNotifyDTO.setDetail(detail);
                stockReceiveNotifyDTO.setReceiptDate(buildPurchaseInfoDTO.getWarehouseRecordE().getOutOrInTime());
                response = purchaseCenterFacade.notifyReceive(stockReceiveNotifyDTO);
                if (!Objects.equals(response.getCode(), "0")) {
                    throw new RomeException(ResCode.STOCK_ERROR_1003, "通知采购中心收货数量异常,msg:" + response.getMsg() + "data:" + JSON.toJSONString(response.getData()));
                }
            }
            //更新通知采购中心为已推送
            int result = warehouseRecordRepository.updateSyncPurchaseSuccess(recordCode);
            AlikAssert.isTrue(result > 0, ResCode.STOCK_ERROR_1002, "更新推送采购中心状态失败+recordCode=" + recordCode);
        } catch (Exception e) {
            log.error("通知采购中心入库发生异常:", e);
            throw e;
        }
        return response;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    @Deprecated
    public Response purchaseQualityNotify(RwBatchDTO rwBatchDTO) {
        String recordCode = rwBatchDTO.getRecordCode();
        Response response;
        try {
            BuildPurchaseInfoDTO buildPurchaseInfoDTO = super.validateAndBuildPurchaseInfo(recordCode);
            StockQualityNotifyDTO stockQualityNotifyDTO = this.initNotifyQuality(buildPurchaseInfoDTO, Arrays.asList(rwBatchDTO));
            response = purchaseCenterFacade.notifyQuality(stockQualityNotifyDTO);
            if (!Objects.equals(response.getCode(), "0")) {
                throw new RomeException(ResCode.STOCK_ERROR_1003, "通知采购中心质检数量异常,msg:" + response.getMsg() + "data:" + JSON.toJSONString(response.getData()));
            }
            //更新rwBatch表通成功
            int count = rwBatchRepository.updateRwPurchaseStatusById(rwBatchDTO.getId());
            AlikAssert.isTrue(count > 0, ResCode.STOCK_ERROR_1003, "更新rwBatch表通知采购中心成功失败:recordCode=" + recordCode);
        } catch (Exception e) {
            log.error("通知采购中心质检批发生异常:", e);
            throw e;
        }
        return response;
    }

    private StockQualityNotifyDTO initNotifyQuality(BuildPurchaseInfoDTO buildPurchaseInfoDTO, List<RwBatchDTO> rwBatchDTOList) {
        StockQualityNotifyDTO stockNotifyDTO = new StockQualityNotifyDTO();
        if (CollectionUtils.isEmpty(rwBatchDTOList)) {
            throw new RomeException(ResCode.STOCK_ERROR_1003, "批次信息为空");
        }
        Map<String, WarehouseRecordDetail> detailMap = buildPurchaseInfoDTO.getWarehouseRecordE().getWarehouseRecordDetails().stream().collect(Collectors.toMap(x -> x.getSkuCode(), Function.identity(), (v1, v2) -> v2));
        rwBatchDTOList.forEach(e -> {
            SkuQualityDTO skuQualityDTO = new SkuQualityDTO();
            skuQualityDTO.setSkuCode(e.getSkuCode());
            if (e.getQualityStatus().intValue() == 1) {
                skuQualityDTO.setIsSuccess(1);
            } else {
                skuQualityDTO.setIsSuccess(0);
            }
            skuQualityDTO.setInspectionQty(e.getActualQty());
            skuQualityDTO.setInspectionDate(new Date());
            skuQualityDTO.setInspectionBatchNo(e.getBatchCode());
            if (detailMap.containsKey(e.getSkuCode())) {
                skuQualityDTO.setLineNo(detailMap.get(e.getSkuCode()).getLineNo());
            }
            stockNotifyDTO.setDetail(Arrays.asList(skuQualityDTO));
        });
        stockNotifyDTO.setPurchaseEntryNo(buildPurchaseInfoDTO.getPurchaseOrderE().getOutRecordCode());
        stockNotifyDTO.setReceiptNo(rwBatchDTOList.get(0).getWmsRecordCode());
        return stockNotifyDTO;
    }


    @Override
    @Transactional(rollbackFor = Exception.class)
    public void qualityFinished(String recordCode, String pwOrderNo) {
        //更新已推送
        int res = warehouseRecordRepository.updateSyncPurchaseSuccess(recordCode);
        AlikAssert.isTrue(res > 0, ResCode.STOCK_ERROR_1003, "更新推送质检完成到采购中心失败:recordCode=" + recordCode);
        WarehouseRecordE warehouseRecordE = warehouseRecordRepository.queryWarehouseRecordByRecordCode(recordCode);
        if (Objects.equals(warehouseRecordE.getRecordType(), WarehouseRecordTypeVO.OUTSOURCE_PURCHASE_END_PRODUCT_IN_RECORD.getType())) {
            //委外的需要特殊处理
            String frontRecordCode = frontWarehouseRecordRelationRepository.getFrontRecordCodeByRecordCode(recordCode);
            PurchaseOrderE purchaseOrderE = frPurchaseOrderRepository.queryByCode(frontRecordCode);
            AlikAssert.notNull(purchaseOrderE, "999", "未找到采购前置单据recordCode = " + recordCode);
            //设置采购入库单号
            pwOrderNo = purchaseOrderE.getOutRecordCode();
        }
        FinishReqDTO finishReqDTO = new FinishReqDTO();
        finishReqDTO.setPwOrderNo(pwOrderNo);
        purchaseCenterFacade.qualityFinished(finishReqDTO);
    }


    @Override
    @Transactional(rollbackFor = Exception.class)
    public Response purchaseReturnEntryNotify(String recordCode) {
        String requestContent = "";
        String responseContent = "";
        Response response;
        BuildPurchaseInfoDTO buildPurchaseInfoDTO = null;
        boolean status = false;
        try {
            buildPurchaseInfoDTO = super.validateAndBuildPurchaseInfo(recordCode);
            CompleteDeliveryDTO completeDeliveryDTO = this.buildCompleteDeliveryDTO(buildPurchaseInfoDTO.getWarehouseRecordE().getWarehouseRecordDetails(), buildPurchaseInfoDTO.getPurchaseOrderE());
            requestContent = JSON.toJSONString(completeDeliveryDTO);
            response = purchaseCenterFacade.purchaseReturnEntryNotify(completeDeliveryDTO);
            responseContent = JSON.toJSONString(response);
            if (Objects.equals(response.getCode(), CommonConstants.CODE_SUCCESS)) {
                status = true;
                //更新推送采购中心退供信息状态
                int result = warehouseRecordRepository.updateSyncPurchaseSuccess(recordCode);
                AlikAssert.isTrue(result > 0, ResCode.STOCK_ERROR_1002, "更新退供订单已推送采购中心状态失败 recordCode=" + recordCode);
            }
        } catch (Exception e) {
            log.error("通知采购中心退供出库发生异常：{}", e);
            requestContent = requestContent == null ? recordCode : requestContent;
            responseContent = "通知采购中心退供出库发生异常:" + e.getMessage();
            response = ResponseMsg.EXCEPTION.buildMsg();
            throw e;
        } finally {
            sapInterfaceLogRepository.saveCallBackInterFaceLog(1, buildPurchaseInfoDTO.getPurchaseOrderE().getOutRecordCode(), "completeDelivery", requestContent, responseContent, status);
        }
        return response;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Response qualityCenterNotify(String wmsRecordCode, String recordCode) {
        Response response;
        WarehouseRecordE warehouseRecordE = warehouseRecordRepository.queryWarehouseRecordByRecordCode(recordCode);
        AlikAssert.isNotNull(warehouseRecordE, ResCode.STOCK_ERROR_1002, ResCode.STOCK_ERROR_1002_DESC + " recordCode=" + recordCode);
        //查询后置单明细
        List<WarehouseRecordDetail> warehouseRecordDetailList = warehouseRecordRepository.queryDetailListByRecordCode(recordCode);
        AlikAssert.isNotEmpty(warehouseRecordDetailList, ResCode.STOCK_ERROR_1002, ResCode.STOCK_ERROR_1002_DESC + " 出库单明细为空 recordCode=" + recordCode);
        warehouseRecordE.setWarehouseRecordDetails(warehouseRecordDetailList);
        List<RwBatchE> rwBatchES = rwBatchRepository.queryBywmsRecordCode(recordCode, wmsRecordCode);
        AlikAssert.isNotEmpty(rwBatchES, ResCode.STOCK_ERROR_1003, "收货行不存在 recordCode=" + recordCode + " wmsRecordCode=" + wmsRecordCode);
        List<FrontWarehouseRecordRelationDO> frontWarehouseRecordRelationDOs = warehouseRecordRepository.getFrontWarehouseRecordsRelationByWrId(warehouseRecordE.getId());
        AlikAssert.isNotEmpty(frontWarehouseRecordRelationDOs, ResCode.STOCK_ERROR_1002, "前置单关联关系不存在:warehouseId=" + warehouseRecordE.getId());
        PurchaseOrderE purchaseOrderE = frPurchaseOrderRepository.queryByCode(frontWarehouseRecordRelationDOs.get(0).getFrontRecordCode());
        AlikAssert.isNotNull(purchaseOrderE, ResCode.STOCK_ERROR_1002, ResCode.STOCK_ERROR_1002_DESC + "大仓采购前置单不存在 recordCode=" + frontWarehouseRecordRelationDOs.get(0).getFrontRecordCode());
        List<PurchaseOrderDetailE> purchaseOrderDetail = frPurchaseOrderRepository.queryDetailsByFrontIds(Arrays.asList(purchaseOrderE.getId()));
        AlikAssert.isNotEmpty(purchaseOrderDetail, ResCode.STOCK_ERROR_1003, "大仓采购前置单明细不存在:id=" + purchaseOrderE.getId());
        RealWarehouse realWarehouse = realWarehouseService.findByRealWarehouseId(warehouseRecordE.getRealWarehouseId());
        AlikAssert.isNotNull(realWarehouse, ResCode.STOCK_ERROR_1002, "实仓不存在:" + warehouseRecordE.getRealWarehouseId());
        List<QualityNotifyDTO> list;
        if (Objects.nonNull(warehouseRecordE.getAppId()) && Objects.equals(warehouseRecordE.getAppId(), "1") && !Objects.equals(warehouseRecordE.getRecordType(), WarehouseRecordTypeVO.TEMP_QUALITY_IN_RECORD.getType())) {
            //930新流程
            list = buildQualityNotifyList(wmsRecordCode, recordCode, warehouseRecordDetailList, rwBatchES, purchaseOrderE, purchaseOrderDetail, realWarehouse);
        } else {
            //730老流程
            list = buildQualityNotifyListOld(wmsRecordCode, recordCode, warehouseRecordDetailList, rwBatchES, purchaseOrderE, purchaseOrderDetail, realWarehouse);
        }
        if (CollectionUtils.isNotEmpty(list)) {
            response = qualityCenterFacade.notifyQualityCenter(list, recordCode);
            if (Objects.equals(response.getCode(), CommonConstants.CODE_SUCCESS)) {
                //更新推送质检中心已推送
                int result = receiptRecordRepository.updateStatusToHasSynchronized(wmsRecordCode);
                AlikAssert.isTrue(result > 0, ResCode.STOCK_ERROR_1002, "更新推送质检质检状态失败:wmsRecordCode=" + wmsRecordCode);
            }
        } else {
            //没有待质检的 质检更新为无需质检
            receiptRecordRepository.updateQualityStatus(wmsRecordCode);
            response = Response.builderSuccess("");
        }
        return response;
    }

    /**
     * 构建生成质检批信息 (兼容730)
     *
     * @param recordCode
     * @param warehouseRecordDetailList
     * @param rwBatchES
     * @param purchaseOrderE
     * @param purchaseOrderDetail
     * @param realWarehouse
     * @return
     */
    private List<QualityNotifyDTO> buildQualityNotifyListOld(String wmsRecordCode, String recordCode, List<WarehouseRecordDetail> warehouseRecordDetailList, List<RwBatchE> rwBatchES, PurchaseOrderE purchaseOrderE, List<PurchaseOrderDetailE> purchaseOrderDetail, RealWarehouse realWarehouse) {
        List<QualityNotifyDTO> list = new ArrayList<>();
        // Map<String, RwBatchE> rwBatchMap = rwBatchES.stream().collect(Collectors.toMap(RwBatchE::getLineNo, Function.identity(), (v1, v2) -> v2));
        Map<String, PurchaseOrderDetailE> purchaseDetailMap = purchaseOrderDetail.stream().collect(Collectors.toMap(PurchaseOrderDetailE::getSkuCode, Function.identity(), (v1, v2) -> v2));
        Map<String, SkuInfoExtDTO> skuMap = new HashMap();
        if (Objects.equals(purchaseOrderE.getRecordType(), FrontRecordTypeVO.TEMPORARY_QUALITY.getType())) {
            List<String> skuCodes = warehouseRecordDetailList.stream().map(x -> x.getSkuCode()).collect(Collectors.toList());
            List<SkuInfoExtDTO> skuInfoExtDTOS = skuFacade.skusBySkuCode(skuCodes);
            if (CollectionUtils.isNotEmpty(skuInfoExtDTOS)) {
                skuMap = skuInfoExtDTOS.stream().collect(Collectors.toMap(x -> x.getSkuCode(), Function.identity(), (v1, v2) -> v2));
            }
        }
        Map<Long, WarehouseRecordDetail> skumap = RomeCollectionUtil.listforMap(warehouseRecordDetailList, "id");
        for (RwBatchE rwBatch : rwBatchES) {
            PurchaseOrderDetailE detail = purchaseDetailMap.get(rwBatch.getSkuCode());
            // RwBatchE rwBatch = rwBatchMap.get(String.valueOf(x.getId()));
            WarehouseRecordDetail x = skumap.get(Long.parseLong(rwBatch.getLineNo()));
            if (Objects.nonNull(detail) && Objects.nonNull(x) && Objects.equals(rwBatch.getQualityStatus(), 0)) {
                QualityNotifyDTO qualityNotifyDTO = new QualityNotifyDTO();
                qualityNotifyDTO.setPurchaseRecordCode(purchaseOrderE.getOutRecordCode());
                qualityNotifyDTO.setPurchaseLineNo(x.getLineNo());
                if (Objects.nonNull(rwBatch.getProductDate())) {
                    qualityNotifyDTO.setProduceDate(DateUtil.format(rwBatch.getProductDate(), "yyyy-MM-dd"));
                } else {
                    qualityNotifyDTO.setProduceDate(null);
                }
                qualityNotifyDTO.setReceiptDate(DateUtil.format(rwBatch.getCreateTime(), "yyyy-MM-dd"));
                qualityNotifyDTO.setBatchCode(rwBatch.getBatchCode());
                qualityNotifyDTO.setInspectionQty(rwBatch.getActualQty());
                qualityNotifyDTO.setSkuCode(x.getSkuCode());
                qualityNotifyDTO.setUnitCode(x.getUnitCode());
                qualityNotifyDTO.setReceivedQty(rwBatch.getActualQty());
                qualityNotifyDTO.setEntryRecordCode(recordCode);
                qualityNotifyDTO.setEntryLineNo(String.valueOf(x.getId()));
                qualityNotifyDTO.setAccountRecordCode(wmsRecordCode);
                qualityNotifyDTO.setRealWarehouseCode(realWarehouse.getRealWarehouseOutCode());
                qualityNotifyDTO.setRealWarehouseName(realWarehouse.getRealWarehouseName());
                qualityNotifyDTO.setRealWarehouseType(realWarehouse.getRealWarehouseType());
                qualityNotifyDTO.setFactoryCode(realWarehouse.getFactoryCode());
                if (Objects.equals(purchaseOrderE.getRecordType(), FrontRecordTypeVO.TEMPORARY_QUALITY.getType())) {
                    qualityNotifyDTO.setSourceType(2);
                    SkuInfoExtDTO skuInfoExtDTO = skuMap.get(x.getSkuCode());
                    AlikAssert.isTrue(Objects.nonNull(skuInfoExtDTO) && CollectionUtils.isNotEmpty(skuInfoExtDTO.getSkuSupplierDTOList()) && StringUtils.isNotEmpty(skuInfoExtDTO.getSkuSupplierDTOList().get(0).getSupplierCode()), ResCode.STOCK_ERROR_1002, "调用商品接口查询供应商编码不存在 recordCode=" + recordCode + " skuCode=" + x.getSkuCode());
                    String supplierCode = this.findFirstNotZeroStr(skuInfoExtDTO.getSkuSupplierDTOList().get(0).getSupplierCode());
                    AlikAssert.isNotBlank(supplierCode, ResCode.STOCK_ERROR_1002, "供应商编码有误:" + skuInfoExtDTO.getSkuSupplierDTOList().get(0).getSupplierCode());
                    qualityNotifyDTO.setSupplierCode(supplierCode);
                } else {
                    qualityNotifyDTO.setSourceType(1);
                    String supplierCode = this.findFirstNotZeroStr(purchaseOrderE.getSupplierCode());
                    qualityNotifyDTO.setSupplierCode(supplierCode);
                }
                list.add(qualityNotifyDTO);
            }
        }
        return list;
    }


    /**
     * 构建生成质检批信息
     *
     * @param wmsRecordCode
     * @param recordCode
     * @param warehouseRecordDetailList
     * @param rwBatchES
     * @param purchaseOrderE
     * @param purchaseOrderDetail
     * @param realWarehouse
     * @return
     */
    private List<QualityNotifyDTO> buildQualityNotifyList(String wmsRecordCode, String recordCode, List<WarehouseRecordDetail> warehouseRecordDetailList, List<RwBatchE> rwBatchES, PurchaseOrderE purchaseOrderE, List<PurchaseOrderDetailE> purchaseOrderDetail, RealWarehouse realWarehouse) {
        List<QualityNotifyDTO> list = new ArrayList<>();
        // Map<String, RwBatchE> rwBatchMap = rwBatchES.stream().collect(Collectors.toMap(RwBatchE::getLineNo, Function.identity(), (v1, v2) -> v2));
        Map<String, PurchaseOrderDetailE> purchaseDetailMap = purchaseOrderDetail.stream().collect(Collectors.toMap(PurchaseOrderDetailE::getSkuCode, Function.identity(), (v1, v2) -> v2));
        Map<String, SkuInfoExtDTO> skuMap = new HashMap();
        if (Objects.equals(purchaseOrderE.getRecordType(), FrontRecordTypeVO.TEMPORARY_QUALITY.getType())) {
            List<String> skuCodes = warehouseRecordDetailList.stream().map(x -> x.getSkuCode()).collect(Collectors.toList());
            List<SkuInfoExtDTO> skuInfoExtDTOS = skuFacade.skusBySkuCode(skuCodes);
            if (CollectionUtils.isNotEmpty(skuInfoExtDTOS)) {
                skuMap = skuInfoExtDTOS.stream().collect(Collectors.toMap(x -> x.getSkuCode(), Function.identity(), (v1, v2) -> v2));
            }
        }
        //todo pw对应多个po单，使用逗号拼接
        PwOutDTO pwOutDTO = purchaseCenterFacade.queryByOutRecordCode(purchaseOrderE.getOutRecordCode());
        AlikAssert.isNotNull(pwOutDTO, ResCode.STOCK_ERROR_1002, "调用采购中心未查询到采购订单信息" + purchaseOrderE.getOutRecordCode());
        Map<Long, WarehouseRecordDetail> skumap = RomeCollectionUtil.listforMap(warehouseRecordDetailList, "id");
        for (RwBatchE rwBatch : rwBatchES) {
            PurchaseOrderDetailE detail = purchaseDetailMap.get(rwBatch.getSkuCode());
            // RwBatchE rwBatch = rwBatchMap.get(String.valueOf(x.getId()));
            WarehouseRecordDetail x = skumap.get(Long.parseLong(rwBatch.getLineNo()));
            if (Objects.nonNull(detail) && Objects.nonNull(x) && Objects.equals(rwBatch.getQualityStatus(), 0)) {
                QualityNotifyDTO qualityNotifyDTO = new QualityNotifyDTO();
                qualityNotifyDTO.setPurchaseRecordCode(pwOutDTO.getPurchaseOrderNos());
                qualityNotifyDTO.setPurchaseLineNo(x.getLineNo());
                if (Objects.nonNull(rwBatch.getProductDate())) {
                    qualityNotifyDTO.setProduceDate(DateUtil.format(rwBatch.getProductDate(), "yyyy-MM-dd"));
                } else {
                    qualityNotifyDTO.setProduceDate(null);
                }
                qualityNotifyDTO.setReceiptDate(DateUtil.format(rwBatch.getCreateTime(), "yyyy-MM-dd"));
                qualityNotifyDTO.setBatchCode(rwBatch.getBatchCode());
                qualityNotifyDTO.setInspectionQty(rwBatch.getActualQty());
                qualityNotifyDTO.setRealWarehouseCode(realWarehouse.getRealWarehouseOutCode());
                qualityNotifyDTO.setRealWarehouseName(realWarehouse.getRealWarehouseName());
                qualityNotifyDTO.setRealWarehouseType(realWarehouse.getRealWarehouseType());
                qualityNotifyDTO.setSkuCode(x.getSkuCode());
                qualityNotifyDTO.setUnitCode(x.getUnitCode());
                qualityNotifyDTO.setReceivedQty(rwBatch.getActualQty());
                qualityNotifyDTO.setEntryRecordCode(purchaseOrderE.getOutRecordCode());
                qualityNotifyDTO.setEntryLineNo(String.valueOf(x.getId()));
                qualityNotifyDTO.setAccountRecordCode(wmsRecordCode);
                qualityNotifyDTO.setFactoryCode(realWarehouse.getFactoryCode());
                if (Objects.equals(purchaseOrderE.getRecordType(), FrontRecordTypeVO.TEMPORARY_QUALITY.getType())) {
                    qualityNotifyDTO.setSourceType(2);
                    SkuInfoExtDTO skuInfoExtDTO = skuMap.get(x.getSkuCode());
                    AlikAssert.isTrue(Objects.nonNull(skuInfoExtDTO) && CollectionUtils.isNotEmpty(skuInfoExtDTO.getSkuSupplierDTOList()) && StringUtils.isNotEmpty(skuInfoExtDTO.getSkuSupplierDTOList().get(0).getSupplierCode()), ResCode.STOCK_ERROR_1002, "调用商品接口查询供应商编码不存在 recordCode=" + recordCode + " skuCode=" + x.getSkuCode());
                    String supplierCode = this.findFirstNotZeroStr(skuInfoExtDTO.getSkuSupplierDTOList().get(0).getSupplierCode());
                    AlikAssert.isNotBlank(supplierCode, ResCode.STOCK_ERROR_1002, "供应商编码有误:" + skuInfoExtDTO.getSkuSupplierDTOList().get(0).getSupplierCode());
                    qualityNotifyDTO.setSupplierCode(supplierCode);
                    qualityNotifyDTO.setSupplierName(skuInfoExtDTO.getSkuSupplierDTOList().get(0).getSupplierName());
                } else {
                    qualityNotifyDTO.setSourceType(1);
                    String supplierCode = this.findFirstNotZeroStr(purchaseOrderE.getSupplierCode());
                    qualityNotifyDTO.setSupplierCode(supplierCode);
                    qualityNotifyDTO.setSupplierName(purchaseOrderE.getSupplierName());
                }
                list.add(qualityNotifyDTO);
            }
        }
        return list;
    }

    /**
     * 对供应商编码进行处理
     *
     * @param str
     * @return
     */
    private String findFirstNotZeroStr(String str) {
        if (StringUtils.isNotBlank(str)) {
            int position = -1;
            char[] charArray = str.toCharArray();
            for (int i = 0; i < charArray.length; i++) {
                if (!Objects.equals(charArray[i], '0')) {
                    position = i;
                    break;
                }
            }
            if (position != -1) {
                return str.substring(position);
            }
        }
        return "";
    }


    @Override
    public List<WarehouseRecordPageDTO> selectWaitNotifyToPurchase(Integer page, Integer maxResult) {
        List<WarehouseRecordE> warehouseRecordEList = warehouseRecordRepository.selectWaitNotifyToPurchase(page, maxResult, Arrays.asList(WarehouseRecordTypeVO.PURCHASE_OUT_WAREHOUSE_RECORD.getType()));
        return warehouseRecordConvertor.entityToWarehouseDto(warehouseRecordEList);
    }

    @Override
    public List<WarehouseRecordPageDTO> selectSupplierDirectDeliveryToPurchase(Integer page, Integer maxResult) {
        List<WarehouseRecordE> warehouseRecordEList = warehouseRecordRepository.selectWaitNotifyToPurchase(page, maxResult, Arrays.asList(WarehouseRecordTypeVO.SHOP_CHAIN_DIRECT_IN_RECORD.getType()));
        return warehouseRecordConvertor.entityToWarehouseDto(warehouseRecordEList);
    }

    /**
     * 构建退供信息对象
     *
     * @param warehouseRecordDetails
     * @param purchaseOrderE
     * @return
     */
    private CompleteDeliveryDTO buildCompleteDeliveryDTO(List<WarehouseRecordDetail> warehouseRecordDetails, PurchaseOrderE purchaseOrderE) {
        CompleteDeliveryDTO completeDeliveryDTO = new CompleteDeliveryDTO();
        completeDeliveryDTO.setRefundNo(purchaseOrderE.getOutRecordCode());
        List<CompleteDeliveryDetailDTO> list = warehouseRecordDetails.stream().map(x -> {
            CompleteDeliveryDetailDTO completeDeliveryDetailDTO = new CompleteDeliveryDetailDTO();
            completeDeliveryDetailDTO.setLineNo(x.getLineNo());
            completeDeliveryDetailDTO.setRefundQty(x.getActualQty());
            return completeDeliveryDetailDTO;
        }).collect(Collectors.toList());
        completeDeliveryDTO.setDetail(list);
        return completeDeliveryDTO;
    }

    /**
     * 针对直送业务
     *
     * @param warehouseRecordDetailList
     * @param purchaseOrderE
     * @return
     */
    private StockNotifyDTO buildStockNotifyDTO(List<WarehouseRecordDetail> warehouseRecordDetailList, PurchaseOrderE purchaseOrderE) {
        StockNotifyDTO stockNotifyDTO = new StockNotifyDTO();
        List<PurchaseReceiveDetailDTO> list = warehouseRecordDetailList.stream().map(detail -> {
            PurchaseReceiveDetailDTO purchaseReceiveDetailDTO = new PurchaseReceiveDetailDTO();
            purchaseReceiveDetailDTO.setLineNo(detail.getLineNo());
            purchaseReceiveDetailDTO.setSkuCode(detail.getSkuCode());
            purchaseReceiveDetailDTO.setReceivedQty(detail.getActualQty());
            return purchaseReceiveDetailDTO;
        }).collect(Collectors.toList());
        stockNotifyDTO.setDetail(list);
        stockNotifyDTO.setPurchaseEntryNo(purchaseOrderE.getOutRecordCode());
        stockNotifyDTO.setWarehousingDate(cn.hutool.core.date.DateUtil.format(new Date(), DateUtil.NORM_DATETIME_PATTERN));
        return stockNotifyDTO;
    }

}
