package com.rome.stock.innerservice.domain.entity.warehouserecord;

import com.rome.arch.core.domain.EntityFactory;
import com.rome.stock.innerservice.common.AlikAssert;
import com.rome.stock.innerservice.common.ResCode;
import com.rome.stock.innerservice.common.SkuQtyUnitTools;
import com.rome.stock.innerservice.constant.WarehouseRecordBusinessTypeVO;
import com.rome.stock.innerservice.constant.WarehouseRecordStatusVO;
import com.rome.stock.innerservice.constant.WmsSyncStatusVO;
import com.rome.stock.innerservice.domain.entity.frontrecord.ChargeAgainstDetailE;
import com.rome.stock.innerservice.domain.entity.frontrecord.ChargeAgainstE;
import com.rome.stock.innerservice.domain.repository.warehouserecord.ChargeAgainstWarehouseRepository;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Data
@Slf4j
@Component
@Scope("prototype")
@EqualsAndHashCode(callSuper = false)
public class ChargeAgainstWarehouseRecordE extends AbstractWarehouseRecord {
    @Resource
    private EntityFactory entityFactory;
    @Resource
    private SkuQtyUnitTools skuQtyUnitTools;
    @Resource
    private ChargeAgainstWarehouseRepository chargeAgainstWarehouseRepository;

    /**
     * 创建仓库单据
     */
    public void addWarehouseRecord() {
        long id = chargeAgainstWarehouseRepository.saveWarehouseRecord(this);
        this.setId(id);
        this.warehouseRecordDetails.forEach(record -> record.setWarehouseRecordDetail(this));
        chargeAgainstWarehouseRepository.saveWarehouseRecordDetails(this.warehouseRecordDetails);
        //存储前置单与仓库单关系
        this.addFrontRecordAndWarehouseRelation();
    }

    /**
     * 根据前置单生成出入库单据
     */
    public void createInRecordByFrontRecord(ChargeAgainstE frontRecord) {
        createRecodeCode("CAIR");
        this.setBusinessType(WarehouseRecordBusinessTypeVO.IN_WAREHOUSE_RECORD.getType());
        this.setSyncWmsStatus(WmsSyncStatusVO.NO_REQUIRED.getStatus());
        this.setRecordStatus(WarehouseRecordStatusVO.INIT.getStatus());
        this.setAbstractFrontRecord(frontRecord);
        this.setRealWarehouseId(frontRecord.getRealWarehouseId());
        this.setMerchantId(frontRecord.getMerchantId());
        this.setSapOrderCode(frontRecord.getBusinessCode());
        this.setOutCreateTime(frontRecord.getCreateTime());
        this.setAppId("1");
        List<ChargeAgainstDetailE> frontRecordDetails = frontRecord.getChargeAgainstDetails();
        this.setWarehouseRecordDetails(new ArrayList<>(frontRecordDetails.size()));
        Map<String, WarehouseRecordDetail> detailMap = new HashMap<>(frontRecordDetails.size());
        for (ChargeAgainstDetailE detailE : frontRecordDetails) {
            WarehouseRecordDetail warehouseRecordDetail = detailMap.get(detailE.getSkuCode());
            if (warehouseRecordDetail == null) {
                warehouseRecordDetail = entityFactory.createEntity(WarehouseRecordDetail.class);
                warehouseRecordDetail.createRecordDetailByFrontRecord(detailE);
                detailMap.put(detailE.getSkuCode(), warehouseRecordDetail);
            } else {
                //相同批次的数量做合并
                warehouseRecordDetail.setPlanQty(warehouseRecordDetail.getPlanQty().add(detailE.getBasicSkuQty()));
            }
            warehouseRecordDetail.setActualQty(BigDecimal.ZERO);
        }
        //增加合并之后的数据
        this.warehouseRecordDetails.addAll(detailMap.values());
        //设置skuCode和skuID
        this.initWarehouseRecodeDetail();
    }

    /**
     * 根据前置单生成退货出库单
     */
    public void createOutRecordByFrontRecord(ChargeAgainstE frontRecord) {
        createRecodeCode("CAOR");
        this.setBusinessType(WarehouseRecordBusinessTypeVO.OUT_WAREHOUSE_RECORD.getType());
        this.setSyncWmsStatus(WmsSyncStatusVO.NO_REQUIRED.getStatus());
        this.setAbstractFrontRecord(frontRecord);
        this.setRealWarehouseId(frontRecord.getRealWarehouseId());
        this.setMerchantId(frontRecord.getMerchantId());
        this.setSapOrderCode(frontRecord.getBusinessCode());
        this.setOutCreateTime(frontRecord.getCreateTime());
        this.setRecordStatus(WarehouseRecordStatusVO.INIT.getStatus());
        this.setAppId("1");
        List<ChargeAgainstDetailE> frontRecordDetails = frontRecord.getChargeAgainstDetails();
        this.setWarehouseRecordDetails(new ArrayList<>(frontRecordDetails.size()));
        WarehouseRecordDetail warehouseRecordDetail = null;
        Map<String, WarehouseRecordDetail> detailMap = new HashMap<>(frontRecordDetails.size());
        //前置单一个sku会存在多个批次，需要合并
        for (ChargeAgainstDetailE detailE : frontRecordDetails) {
            warehouseRecordDetail = detailMap.get(detailE.getSkuCode());
            if (warehouseRecordDetail == null) {
                warehouseRecordDetail = entityFactory.createEntity(WarehouseRecordDetail.class);
                warehouseRecordDetail.createRecordDetailByFrontRecord(detailE);
                detailMap.put(detailE.getSkuCode(), warehouseRecordDetail);
            } else {
                //相同批次的数量做合并
                warehouseRecordDetail.setPlanQty(warehouseRecordDetail.getPlanQty().add(detailE.getBasicSkuQty()));
            }
//            AlikAssert.isNotNull(warehouseRecordDetail.getPlanQty(), ResCode.STOCK_ERROR_1003, "调用商品中心未查询到基本单位信息:sku_code=" + detailE.getSkuCode() + " unitCode=" + detailE.getUnitCode());
            warehouseRecordDetail.setActualQty(BigDecimal.ZERO);
        }
        //增加合并之后的数据
        this.warehouseRecordDetails.addAll(detailMap.values());

        //设置skuCode和skuID
        this.initWarehouseRecodeDetail();
    }


}
