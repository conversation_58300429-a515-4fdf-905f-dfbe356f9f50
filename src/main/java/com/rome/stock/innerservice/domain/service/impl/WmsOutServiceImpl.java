package com.rome.stock.innerservice.domain.service.impl;

import cn.hutool.core.collection.CollUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.rome.arch.core.clientobject.Response;
import com.rome.arch.core.exception.RomeException;
import com.rome.scm.common.monitor.CustomMonitorFacade;
import com.rome.scm.common.monitor.CustomMonitorTypeEnum;
import com.rome.stock.common.constants.CommonConstants;
import com.rome.stock.common.enums.warehouse.WarehouseStoreIdentiEnum;
import com.rome.stock.innerservice.api.dto.RwBatchDTO;
import com.rome.stock.innerservice.api.dto.warehouserecord.MmCancelResultDTO;
import com.rome.stock.innerservice.api.dto.wms.UpdateTmsOrderDTO;
import com.rome.stock.innerservice.api.dto.wms.UpdateTmsOrderItem;
import com.rome.stock.innerservice.common.AlikAssert;
import com.rome.stock.innerservice.common.ResCode;
import com.rome.stock.innerservice.domain.entity.RealWarehouseE;
import com.rome.stock.innerservice.domain.entity.frontrecord.PurchaseOrderE;
import com.rome.stock.innerservice.domain.entity.warehouserecord.WarehouseRecordDetail;
import com.rome.stock.innerservice.domain.entity.warehouserecord.WarehouseRecordE;
import com.rome.stock.innerservice.domain.repository.RealWarehouseRepository;
import com.rome.stock.innerservice.domain.repository.RealWarehouseWmsConfigRepository;
import com.rome.stock.innerservice.domain.repository.warehouserecord.WarehouseRecordRepository;
import com.rome.stock.innerservice.domain.service.WmsOutService;
import com.rome.stock.innerservice.remote.item.facade.SkuFacade;
import com.rome.stock.innerservice.remote.mm.facade.MmOrderIssuesFacade;
import com.rome.stock.innerservice.remote.wms.facade.WmsDataBuildFacade;
import com.rome.stock.wms.constants.WarehouseWmsConfigEnum;
import com.rome.stock.wms.dto.request.EntryOrderUpdateRequest;
import com.rome.stock.wms.dto.request.OrderCancelRequest;
import com.rome.stock.wms.dto.request.QualityReportRequest;
import com.rome.stock.wms.dto.request.TmsOrderUpdateRequest;
import com.rome.stock.wms.dto.response.EntryOrderUpdateResponse;
import com.rome.stock.wms.dto.response.OrderCancelResponse;
import com.rome.stock.wms.dto.response.QualityReportResponse;
import com.rome.stock.wms.dto.response.TmsOrderUpdateResponse;
import com.rome.stock.wms.request.RequestWmsClient;
import com.rome.stock.wms.util.WmsRequestUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.*;

@Slf4j
@Service
public class WmsOutServiceImpl implements WmsOutService {

    @Resource
    private WarehouseRecordRepository warehouseRecordRepository;

    @Resource
    private RealWarehouseWmsConfigRepository realWarehouseWmsConfigRepository;

    @Resource
    private RealWarehouseRepository realWarehouseRepository;
    @Resource
    private SkuFacade skuFacade;

    @Autowired
    private MmOrderIssuesFacade mmOrderIssuesFacade;
    
    @Override
    @Transactional(propagation = Propagation.NOT_SUPPORTED)
    public boolean inspectionAdvice(Long realWarehouseId, String realWarehouseOutCode, String factoryCode,
			String outRecordCode, String wmsRecordCode, List<RwBatchDTO> rwBatchEList) {
        //根据实仓id查询对应的wms信息
        Integer wmsCode = realWarehouseWmsConfigRepository.queryWmsConfigById(realWarehouseId);
        if (wmsCode == null){
            log.error("暂未查询到实仓与wms的配置信息 :实仓id "+ realWarehouseId);
            CustomMonitorFacade.callMonitor(factoryCode + "-" + factoryCode + "未查询到实仓与wms的配置信息", "质检结果通知WMS失败", CustomMonitorTypeEnum.TYPE_EXCEPTION
                    , "下发大福WMS预警", "stock-inner-service");
           throw new RomeException(ResCode.STOCK_ERROR_6008,ResCode.STOCK_ERROR_6008_DESC);
        }
        List<String> z016SkuCodes = skuFacade.searchZ016Codes();
        if (WarehouseWmsConfigEnum.DF.getType().equals(wmsCode)
                || WarehouseWmsConfigEnum.ODY.getType().equals(wmsCode)
                || WarehouseWmsConfigEnum.ODYC.getType().equals(wmsCode)
                || WarehouseWmsConfigEnum.ZTO.getType().equals(wmsCode)){
            //需要质检加真实库存，不合格sku不需要推送到wms
            Iterator<RwBatchDTO> iterator = rwBatchEList.iterator();
            String recordCode = "";
            while (iterator.hasNext()){
                RwBatchDTO rwBatchDTO = iterator.next();
                recordCode = rwBatchDTO.getRecordCode();
                //周转箱质检结果无需下发wms
                if(z016SkuCodes.contains(rwBatchDTO.getSkuCode())
                        || (Objects.equals(rwBatchDTO.getQualityStatus(),2) && Objects.equals(rwBatchDTO.getIsOperate(),1))){
                    iterator.remove();
                }
            }
            if(CollectionUtils.isEmpty(rwBatchEList)){
                return true;
            }
            QualityReportRequest request = WmsDataBuildFacade.buildQualityReportRequestData(realWarehouseId, realWarehouseOutCode, factoryCode, outRecordCode, wmsRecordCode, rwBatchEList);
            QualityReportResponse response = RequestWmsClient.qualityReportRequest(request);
            if(CommonConstants.CODE_SUCCESS.equals(response.getCode())) {
                return true;
            }else {
                //目前只有大幅下发质检结果
                StringBuilder stringBuilder = new StringBuilder();
                stringBuilder.append("**单据编号：").append(recordCode).append("收货单号：").append(wmsRecordCode).append("；质检结果下发WMS异常！\n")
                        .append("失败原因：").append(JSON.toJSONString(response.getResponseContentJson())).append("**\n");
                CustomMonitorFacade.callMonitor(stringBuilder.toString(), "质检结果通知WMS失败", CustomMonitorTypeEnum.TYPE_EXCEPTION
                        , "下发大福WMS预警", "stock-inner-service");
                return false;
            }
        }
        if (WarehouseWmsConfigEnum.SAP.getType().equals(wmsCode) || WarehouseWmsConfigEnum.MM.getType().equals(wmsCode)){
            //调用SAP质检接口
            return true;
        }
        return false;
    }
    
    @Override
    @Transactional(propagation = Propagation.NOT_SUPPORTED)
    public boolean updateEntryOrder(Long realWarehouseId,
			String realWarehouseOutCode, String factoryCode, String entryOrderCode,
			PurchaseOrderE purchaseOrderE, List<WarehouseRecordDetail> detailList) {
        //根据实仓id查询对应的wms信息
        Integer wmsCode = realWarehouseWmsConfigRepository.queryWmsConfigById(realWarehouseId);
        if (wmsCode == null){
            throw new RomeException(ResCode.STOCK_ERROR_6008,ResCode.STOCK_ERROR_6008_DESC);
        }
        if (WarehouseWmsConfigEnum.SAP.getType().equals(wmsCode)){
            return true;
        }else {
        	EntryOrderUpdateRequest request = WmsDataBuildFacade.buildEntryOrderUpdateRequestData(realWarehouseId, realWarehouseOutCode, factoryCode, entryOrderCode, purchaseOrderE, detailList);
    		EntryOrderUpdateResponse response = RequestWmsClient.entryOrderUpdateRequest(request);
    		if(CommonConstants.CODE_SUCCESS.equals(response.getCode())) {
    			return true;
    		}else {
    			return false;
    		}
        }
//        return false;
    }

    @Override
    @Transactional(propagation = Propagation.NOT_SUPPORTED)
    public boolean orderCancel(String orderCode) {
        AlikAssert.isNotBlank(orderCode, ResCode.STOCK_ERROR_1002, ResCode.STOCK_ERROR_1002_DESC + ":取消单号不能为空");
        WarehouseRecordE warehouseRecordE = warehouseRecordRepository.queryWarehouseRecordByRecordCode(orderCode);
        AlikAssert.isNotNull(warehouseRecordE, ResCode.STOCK_ERROR_1002, ResCode.STOCK_ERROR_1002_DESC + ":单据不存在" + orderCode);
        // 取消单据
        RealWarehouseE realWarehouseE = realWarehouseRepository.getRealWarehouseById(warehouseRecordE.getRealWarehouseId());
        OrderCancelRequest request = new OrderCancelRequest();
		request.setRecordCode(orderCode);
		request.setRecordType(warehouseRecordE.getRecordType());
		request.setBusinessType(warehouseRecordE.getBusinessType());
		request.setRealWarehouseId(warehouseRecordE.getRealWarehouseId());
		request.setFactoryCode(realWarehouseE.getFactoryCode());
		request.setRealWarehouseOutCode(realWarehouseE.getRealWarehouseOutCode());
        request.setTransWay(warehouseRecordE.getTransWay());
        request.setRealWarehouseCode(realWarehouseE.getRealWarehouseCode());
//		request.setSplitFlag("");
        //只要是仓店一体,就需要调用WMS,目前是旺店通
        if(Objects.equals(realWarehouseE.getWarehouseStoreIdenti(), WarehouseStoreIdentiEnum.WAREHOUSESTORE.getCode())){
            OrderCancelResponse response = RequestWmsClient.orderCancelRequest(request);
            if(CommonConstants.CODE_SUCCESS.equals(response.getCode())) {
                return true;
            }else {
                return false;
            }
        }
        if(WarehouseWmsConfigEnum.MM.name().equals(WmsRequestUtils.getRequestWmsTypeCode(request))) {
        	return this.cancelMMOrder(orderCode,warehouseRecordE.getBusinessType());
        }else if(WarehouseWmsConfigEnum.XT_SJ.name().equals(WmsRequestUtils.getRequestWmsTypeCode(request))) {
            //如果是协同商家仓，直接返回成功
            return true;
        }else {
        	OrderCancelResponse response = RequestWmsClient.orderCancelRequest(request);
    		if(CommonConstants.CODE_SUCCESS.equals(response.getCode())) {
    			return true;
    		}else {
    			return false;
    		}
        }
    }


    /**
     * 取消MM出库单据
     * @param deliveryOrderCode
     * @return
     */
    @Override
    @Transactional(propagation = Propagation.NOT_SUPPORTED)
    public boolean cancelMMOrder(String deliveryOrderCode,Integer orderType){
        Response response = mmOrderIssuesFacade.cancelDeliveryOrder(Arrays.asList(deliveryOrderCode),orderType);
        if(Objects.nonNull(response) && Objects.nonNull(response.getData())){
            List<MmCancelResultDTO> mmCancelResultDTOS = JSONArray.parseArray(JSON.toJSONString(response.getData()), MmCancelResultDTO.class);
            if(CollUtil.isNotEmpty(mmCancelResultDTOS)){
               return mmCancelResultDTOS.stream().anyMatch(x->Objects.equals(x.getStatus(),0));
            }
        }
        return false;
    }


    /**
     * @Description: 大福修改派车单号 <br>
     * <AUTHOR> 2019/10/15
     * @param updateTmsOrderDTO
     * @return
     */
    @Override
    @Transactional(propagation = Propagation.NOT_SUPPORTED)
    public List<UpdateTmsOrderItem> updateTmsOrder(Integer wmsType ,UpdateTmsOrderDTO updateTmsOrderDTO) {
        AlikAssert.isNotNull(updateTmsOrderDTO, ResCode.STOCK_ERROR_1002, "修改派车单信息不能为空");
        AlikAssert.isNotEmpty(updateTmsOrderDTO.getRecordOrders(), ResCode.STOCK_ERROR_1002, "中台单单据集合不能为空");
        AlikAssert.isNotNull(updateTmsOrderDTO.getBusinessType(), ResCode.STOCK_ERROR_1002, "");
        
        TmsOrderUpdateRequest request = WmsDataBuildFacade.buildTmsOrderUpdateRequestData(wmsType, updateTmsOrderDTO);
		TmsOrderUpdateResponse response = RequestWmsClient.tmsOrderUpdateRequest(request);
		if(!CommonConstants.CODE_SUCCESS.equals(response.getCode())) {
			 String expressCode = updateTmsOrderDTO.getRecordOrders().get(0).getExpressCode();
            log.error(response.getWmsConfig().getDesc() + "修改派车单【{}】返回结果失败", expressCode);
            AlikAssert.isTrue(false, ResCode.STOCK_ERROR_1001, "修改派车单号失败" + expressCode);
        }
        if (CollectionUtils.isEmpty(response.getItems())){
            AlikAssert.isTrue(false, ResCode.STOCK_ERROR_1001, "修改派车单号失败"+ updateTmsOrderDTO.getRecordOrders().get(0).getExpressCode());
        }
        List<UpdateTmsOrderItem> result = new ArrayList<UpdateTmsOrderItem>(response.getItems().size());
        UpdateTmsOrderItem dto;
        for(TmsOrderUpdateResponse.Item item : response.getItems()) {
        	dto = new UpdateTmsOrderItem();
        	dto.setDeliveryOrderCode(item.getDeliveryOrderCode());
        	dto.setUpStatus(item.getUpStatus());
        	result.add(dto);
        }
        return result;
    }

}

