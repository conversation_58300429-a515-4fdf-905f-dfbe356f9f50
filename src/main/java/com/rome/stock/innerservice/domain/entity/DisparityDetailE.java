package com.rome.stock.innerservice.domain.entity;

import com.rome.stock.innerservice.domain.entity.frontrecord.AbstractFrontRecordDetail;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;

@Component
@Scope("prototype")
@Data
@EqualsAndHashCode(callSuper = false)
public class DisparityDetailE extends AbstractFrontRecordDetail {

    /**
     * 差异单编码
     */
    private Long disparityId;
    /**
     * 入库数量
     */
    private BigDecimal inSkuQty;
    /**
     * 出库数量
     */
    private BigDecimal outSkuQty;

    /**
     * 责任方
     */
    private String responsible;
    /**
     * 责任方类型
     */
    private Integer responsibleType;
    /**
     * 责任原因
     */
    private Integer reasons;

    /**
     * 入库仓库数据源
     */
    private Long handlerInRealWarehouseId;

    /**
     * 备注：承运商信息，物流责任用
     */
    private String remark;

    /**
     * 成本中心，物流责任用
     */
    private String costCenter;

    /**
     * 行号
     * */
    private String lineNo;

    /**
     * 交货单行号
     * */
    private String deliveryLineNo;

    private String sapPoNo;

    /**
     * 前置单据关联主键
     */
    private Long frontRecordId;
    /**
     * 前置单据编码
     */
    private String frontRecordCode;

    private Integer recordStatus;

    /**
     * 真实单位名称
     */
    private String realUnit;
    /**
     * 真实单位code
     */
    private String realUnitCode;
}
