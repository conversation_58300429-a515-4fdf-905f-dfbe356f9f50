package com.rome.stock.innerservice.domain.service.impl;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import javax.annotation.Resource;

import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.github.pagehelper.util.StringUtil;
import com.rome.stock.common.utils.DateUtil;
import com.rome.stock.innerservice.api.dto.cmkanban.FirstKanBanDTO;
import com.rome.stock.innerservice.api.dto.cmkanban.KanBanAllDTO;
import com.rome.stock.innerservice.api.dto.cmkanban.KbChannelDTO;
import com.rome.stock.innerservice.api.dto.cmkanban.KbOrderTypeDTO;
import com.rome.stock.innerservice.api.dto.cmkanban.KbQueryParm;
import com.rome.stock.innerservice.api.dto.cmkanban.KbWarehouseDTO;
import com.rome.stock.innerservice.common.RedisUtil;
import com.rome.stock.innerservice.common.RomeCollectionUtil;
import com.rome.stock.innerservice.domain.entity.ChannelSalesE;
import com.rome.stock.innerservice.domain.entity.RealWarehouseE;
import com.rome.stock.innerservice.domain.repository.ChannelSalesRepository;
import com.rome.stock.innerservice.domain.repository.RealWarehouseRepository;
import com.rome.stock.innerservice.domain.service.CmKanBanService;
import com.rome.stock.innerservice.infrastructure.mapper.BusinessReasonMapper;

import lombok.extern.slf4j.Slf4j;

/**
 * 类CmKanBanServiceImpl的实现描述：看板实现类
 *
 * <AUTHOR> 2020/7/11 20:42
 */
@Slf4j
@Service
public class CmKanBanServiceImpl implements CmKanBanService {

    @Resource
    private RedisUtil redisUtil;

    @Resource
    private BusinessReasonMapper businessReasonMapper;
    @Autowired
    private RealWarehouseRepository realWarehouseRepository;
    @Autowired
    private ChannelSalesRepository channelSalesRepository;

    /**
     * 看板的key
     */
    private final static String CM_KAN_BAN_LOG_KEY_FIRST = "cm_kanBan_log_first";

    /**
     * 看板的key
     */
    private final static String CM_KAN_BAN_LOG_KEY_SECOND = "cm_kanBan_log_second";

    @Override
    public void saveLog(Integer type, String mylog) {
        //放入的redis, 先放3个月
        if(type == 1){
            redisUtil.set(CM_KAN_BAN_LOG_KEY_FIRST, mylog, 90 * 24 * 3600);
        }else {
            redisUtil.set(CM_KAN_BAN_LOG_KEY_SECOND, mylog, 90 * 24 * 3600);
        }
    }

    @Override
    public List<FirstKanBanDTO> getKanBanInfoForFirst(KbQueryParm kbQueryParm) {
        List<FirstKanBanDTO> kanBanList = new ArrayList<>();
        Object myLog = redisUtil.get(CM_KAN_BAN_LOG_KEY_FIRST);
        if(myLog == null){
            //myLog = "SELECT sw.channel_code, COUNT( DISTINCT sw.out_record_code) 'dealOrderNum', COUNT( DISTINCT rp.warehouse_record_code ) 'dealDoNum' FROM sc_fr_sale_wdt sw LEFT JOIN sc_rw_record_pool rp ON rp.front_record_code = sw.record_code WHERE rp.is_deleted = 0 AND rp.is_available = 1 AND sw.is_deleted = 0 AND sw.is_available =1 #{queryCm} GROUP BY sw.channel_code union all SELECT sw.channel_code, COUNT( DISTINCT sw.out_record_code ) 'dealOrderNum', COUNT( DISTINCT rp.warehouse_record_code ) 'dealDoNum' FROM sc_fr_sale sw FORCE INDEX(`idx_create_time`) LEFT JOIN sc_rw_record_pool rp ON rp.front_record_code = sw.record_code and sw.trans_type in(1,3) AND sw.record_type = 99 WHERE rp.is_deleted = 0 AND rp.is_available = 1 AND sw.is_deleted = 0 AND sw.is_available =1 and sw.merchant_id = 10000 #{queryApp} GROUP BY sw.channel_code";
            myLog = "select 1;";
        }
        myLog = this.setQueryForCm(kbQueryParm, myLog.toString(), false);
        myLog = this.setQueryForApp(kbQueryParm, myLog.toString(), false);
        //log.info("云商第一块看板log:---" + myLog);
        List<FirstKanBanDTO> list = businessReasonMapper.getKanBanInfoForFirst(myLog.toString());
        List<String> channelCodes = RomeCollectionUtil.getValueList(list, "channelCode");
        List<ChannelSalesE> channelSalesList = channelSalesRepository.selectChannelSalesByChannelCodes(channelCodes);
        Map<String, ChannelSalesE> channelMap = RomeCollectionUtil.listforMap(channelSalesList, "channelCode");
        for (FirstKanBanDTO firstKanBanDTO : list) {
            ChannelSalesE channelSale = channelMap.get(firstKanBanDTO.getChannelCode());
            if(channelSale != null) {
                firstKanBanDTO.setChannelName(channelSale.getChannelName());
                String [] arr = channelSale.getChannelName().split("_");
                if(arr.length == 2){
                    firstKanBanDTO.setChannelNickName(arr[1]);
                }else{
                    firstKanBanDTO.setChannelNickName(channelSale.getChannelName());
                }

            }
        }
        return list;
    }

    @Override
    public List<KbWarehouseDTO> getKanBanInfoForSecond(KbQueryParm kbQueryParm) {
        List<KbWarehouseDTO> kanBanList = new ArrayList<>();
        Object myLog = redisUtil.get(CM_KAN_BAN_LOG_KEY_SECOND);
        if(myLog == null){
            //myLog = "SELECT CASE WHEN rp.real_warehouse_id IS NULL THEN '-1' ELSE rp.real_warehouse_id END AS 'realWarehouseId', sw.channel_code AS 'channelCode', is_pre_sale AS orderType, count( wr.sync_wms_status = 2 OR NULL) 'syncOrderNum', count( wr.sync_wms_status = 1 OR NULL ) 'unSyncOrderNum', count( wr.record_status = 11 OR NULL ) 'deliveryOrderNum', count( sw.record_status = 2 OR NULL ) 'cancleOrderNum', count(( DATEDIFF(NOW( ) , sw.pay_time) > 1 AND wr.record_status IN ( 0 ) and sync_wms_status =2 ) OR NULL ) 'tfdeliveryOrderNum', count( ( DATEDIFF(NOW( ) ,sw.pay_time) > 2 AND wr.record_status IN ( 0 ) and sync_wms_status =2 ) OR NULL ) 'feDeliveryOrderNum' FROM sc_fr_sale_wdt sw LEFT JOIN sc_rw_record_pool rp ON rp.front_record_code = sw.record_code LEFT JOIN sc_warehouse_record wr ON wr.id = rp.warehouse_record_id AND wr.record_type = 100 WHERE rp.is_deleted = 0 AND rp.is_available = 1 AND sw.is_deleted = 0 AND sw.is_available = 1 #{queryCm} GROUP BY realWarehouseId, channelCode, orderType union all SELECT CASE WHEN rp.real_warehouse_id IS NULL THEN '-1' ELSE rp.real_warehouse_id END AS 'realWarehouseId', sw.channel_code AS 'channelCode', 0 AS orderType, count( wr.sync_wms_status = 2 OR NULL ) 'syncOrderNum', count( wr.sync_wms_status = 1 OR NULL ) 'unSyncOrderNum', count( wr.record_status = 11 OR NULL ) 'deliveryOrderNum', count( sw.record_status = 2 OR NULL ) 'cancleOrderNum', count( ( DATEDIFF(NOW( ) , sw.pay_time) > 1 AND wr.record_status IN ( 0 ) and sync_wms_status =2 ) OR NULL ) 'tfdeliveryOrderNum', count( ( DATEDIFF(NOW( ) ,sw.pay_time) > 2 AND wr.record_status IN ( 0 ) and sync_wms_status =2 ) OR NULL ) 'feDeliveryOrderNum' FROM sc_fr_sale sw FORCE INDEX(`idx_create_time`) LEFT JOIN sc_rw_record_pool rp ON rp.front_record_code = sw.record_code and sw.trans_type in(1,3) AND sw.record_type = 99 LEFT JOIN sc_warehouse_record wr ON wr.id = rp.warehouse_record_id AND wr.record_type = 99 WHERE rp.is_deleted = 0 AND sw.merchant_id = 10000 AND rp.is_available = 1 AND sw.is_deleted = 0 AND sw.is_available = 1 #{queryApp} GROUP BY realWarehouseId, channelCode, orderType";
            myLog = "SELECT 1";
        }
        myLog = this.setQueryForCm(kbQueryParm, myLog.toString(), true);
        myLog = this.setQueryForApp(kbQueryParm, myLog.toString(), true);
        //log.info("云商第二块看板log:---" + myLog);
        List<KanBanAllDTO> list = businessReasonMapper.getAllKanBanInfo(myLog.toString());
        if(CollectionUtils.isNotEmpty(list)){
          List<Long> rwIds = RomeCollectionUtil.getValueList(list, "realWarehouseId");
          List<RealWarehouseE> rwList = realWarehouseRepository.getRealWarehouseByIds(rwIds);
          Map<Long, RealWarehouseE> rwMap = RomeCollectionUtil.listforMap(rwList, "id");
          Map<Long, KbWarehouseDTO> kbWarehouseMap = new HashMap<>();
          List<String> channelCodes = RomeCollectionUtil.getValueList(list, "channelCode");
          List<ChannelSalesE> channelSalesList = channelSalesRepository.selectChannelSalesByChannelCodes(channelCodes);
          Map<String, ChannelSalesE> channelMap = RomeCollectionUtil.listforMap(channelSalesList, "channelCode");
          Map<Long, List<KanBanAllDTO>> kbGroupRwMap= RomeCollectionUtil.listforListMap(list, "realWarehouseId");
          Map<String, List<KbOrderTypeDTO>> kbOrderTypeMap = new HashMap<>();
          Map<String, KbOrderTypeDTO> kbRwCountMap = new HashMap<>();
            for(Long realWarehouseId : kbGroupRwMap.keySet()){
                KbWarehouseDTO kbWarehouse = new KbWarehouseDTO();
                RealWarehouseE realWarehouse = rwMap.get(realWarehouseId);
                if(realWarehouse != null){
                    kbWarehouse.setRealWarehouseId(realWarehouse.getId());
                    kbWarehouse.setWarehouseName(realWarehouse.getRealWarehouseName());
                    kbWarehouse.setWarehouseCode(realWarehouse.getRealWarehouseCode());
                }else {
                    continue;
                }
                List<KanBanAllDTO> channelDataList = kbGroupRwMap.get(realWarehouseId);
                List<KbChannelDTO> kbChannelList = new ArrayList<>();
                for (KanBanAllDTO kanBanAllDTO : channelDataList) {
                    ChannelSalesE channelSale = channelMap.get(kanBanAllDTO.getChannelCode());
                    if(channelSale != null) {
                        KbChannelDTO   kbChannel = new KbChannelDTO();
                        kbChannel.setChannelCode(channelSale.getChannelCode());
                        kbChannel.setChannelName(channelSale.getChannelName());
                        String [] arr = channelSale.getChannelName().split("_");
                        if(arr.length == 2){
                            kbChannel.setChannelNickName(arr[1]);
                        }else{
                            kbChannel.setChannelNickName(channelSale.getChannelName());
                        }
                        List<KbOrderTypeDTO> orderTypeList = kbOrderTypeMap.get(realWarehouseId + "_" + kbChannel.getChannelCode()+ "_" + kanBanAllDTO.getOrderType());
                        if(orderTypeList == null){
                            orderTypeList = new ArrayList<>();
                        }
                        KbOrderTypeDTO orderType  = kbRwCountMap.get(realWarehouseId + "_" + kbChannel.getChannelCode() + "_" + kanBanAllDTO.getOrderType());
                        if(orderType == null){
                            orderType = new KbOrderTypeDTO();
                            orderType.setSyncOrderNum(0);
                            orderType.setUnSyncOrderNum(0);
                            orderType.setDeliveryOrderNum(0);
                            orderType.setCancleOrderNum(0);
                            orderType.setTfdeliveryOrderNum(0);
                            orderType.setFeDeliveryOrderNum(0);
                        }
                        if(kanBanAllDTO.getOrderType() == 0){
                            orderType.setOrderType(kanBanAllDTO.getOrderType());
                            orderType.setOrderTypeName("普通订单");
                        }else{
                            orderType.setOrderType(kanBanAllDTO.getOrderType());
                            orderType.setOrderTypeName("预售订单");
                        }

                        orderType.setSyncOrderNum(orderType.getSyncOrderNum() + kanBanAllDTO.getSyncOrderNum());
                        orderType.setUnSyncOrderNum(orderType.getUnSyncOrderNum() + kanBanAllDTO.getUnSyncOrderNum());
                        orderType.setDeliveryOrderNum(orderType.getDeliveryOrderNum() + kanBanAllDTO.getDeliveryOrderNum());
                        orderType.setCancleOrderNum(orderType.getCancleOrderNum() + kanBanAllDTO.getCancleOrderNum());
                        orderType.setTfdeliveryOrderNum(orderType.getTfdeliveryOrderNum() + kanBanAllDTO.getTfdeliveryOrderNum());
                        orderType.setFeDeliveryOrderNum(orderType.getFeDeliveryOrderNum() + kanBanAllDTO.getFeDeliveryOrderNum());

                        orderTypeList.add(orderType);
                        kbOrderTypeMap.put(realWarehouseId + "_" + kbChannel.getChannelCode()+ "_" + kanBanAllDTO.getOrderType(), orderTypeList);
                        kbRwCountMap.put(realWarehouseId + "_" + kbChannel.getChannelCode() + "_" + kanBanAllDTO.getOrderType(), orderType);
                        kbChannel.setOrderTypeList(orderTypeList);
                        kbChannelList.add(kbChannel);
                    }
                }
                kbWarehouse.setKbChannelList(kbChannelList);
                kanBanList.add(kbWarehouse);
            }
        }
        return kanBanList;
    }

    /**
     * 设置云商组装查询
     * @return
     */
    private String setQueryForCm(KbQueryParm kbQueryParm, String myLog, boolean hasRw){
        String queryCm = "";
        if(kbQueryParm.getOrderType() != null){
            queryCm = queryCm + " and sw.is_pre_sale = " + kbQueryParm.getOrderType();
        }
        if(StringUtil.isNotEmpty(kbQueryParm.getChannelCodes())){
            queryCm = queryCm + " and sw.channel_code in( " + this.spilt(kbQueryParm.getChannelCodes()) +")";
        }
        if(kbQueryParm.getRealWarehouseId() != null && hasRw){
            queryCm = queryCm + " and rp.real_warehouse_id  = " + kbQueryParm.getRealWarehouseId();
        }
        if(kbQueryParm.getStartTime() != null){
            String startTime = DateUtil.formatDateTime(kbQueryParm.getStartTime());
            queryCm = queryCm + " and sw.create_time >= '" + startTime + "'";
        }
        if(kbQueryParm.getEndTime() != null){
           String endTime = DateUtil.formatDateTime(kbQueryParm.getEndTime());
            queryCm = queryCm + " and sw.create_time <= '" +  endTime + "'";
        }
        myLog = myLog.replace("#{queryCm}", queryCm);
        return myLog;
    }

    /**
     * 设置app组装查询
     * @return
     */
    private String setQueryForApp(KbQueryParm kbQueryParm, String myLog, boolean hasRw){
        String queryApp = "";
        if(StringUtil.isNotEmpty(kbQueryParm.getChannelCodes())){
            queryApp = queryApp + " and sw.channel_code in( " + this.spilt(kbQueryParm.getChannelCodes())+")";
        }
        if(kbQueryParm.getRealWarehouseId() != null && hasRw){
            queryApp = queryApp + " and rp.real_warehouse_id  = " + kbQueryParm.getRealWarehouseId();
        }
        if(kbQueryParm.getStartTime() != null){
            String startTime = DateUtil.formatDateTime(kbQueryParm.getStartTime());
            queryApp = queryApp + " and sw.create_time >= '" + startTime + "'";
        }
        if(kbQueryParm.getEndTime() != null){
            String endTime = DateUtil.formatDateTime(kbQueryParm.getEndTime());
            queryApp = queryApp + " and sw.create_time <= '" +  endTime + "'";
        }
        myLog = myLog.replace("#{queryApp}", queryApp);
        return myLog;
    }

    /**
     * 分隔并加上引号
     * @param str
     * @return
     */
    public String spilt(String str) {
        StringBuffer sb = new StringBuffer();
        String[] temp = str.split(",");
        for (int i = 0; i < temp.length; i++) {
            if (!"".equals(temp[i]) && temp[i] != null) {
                sb.append("'" + temp[i] + "',");
            }
        }
        String result = sb.toString();
        String tp = result.substring(result.length() - 1, result.length());
        if (",".equals(tp)) {
            return result.substring(0, result.length() - 1);
        }
        else {
            return result;
        }
    }
}
