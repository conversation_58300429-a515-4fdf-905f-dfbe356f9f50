package com.rome.stock.innerservice.domain.entity.warehouserecord;

import com.rome.arch.core.domain.EntityFactory;
import com.rome.stock.common.constants.front.PurchaseOrderConsts;
import com.rome.stock.common.constants.warehouse.WarehouseRecordConstant;
import com.rome.stock.common.enums.warehouse.WarehouseRecordTypeVO;
import com.rome.stock.innerservice.common.AlikAssert;
import com.rome.stock.innerservice.common.ResCode;
import com.rome.stock.innerservice.common.SkuQtyUnitTools;
import com.rome.stock.innerservice.constant.FrontRecordTypeVO;
import com.rome.stock.innerservice.constant.WarehouseRecordBusinessTypeVO;
import com.rome.stock.innerservice.constant.WmsSyncStatusVO;
import com.rome.stock.innerservice.domain.entity.frontrecord.PurchaseOrderDetailE;
import com.rome.stock.innerservice.domain.entity.frontrecord.PurchaseOrderE;
import com.rome.stock.innerservice.domain.repository.warehouserecord.PurchaseOrderWarehouseRepository;
import com.rome.stock.core.infrastructure.dataobject.core.CoreRealStockOpDO;
import com.rome.stock.core.infrastructure.dataobject.core.CoreRealStockOpDetailDO;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

@Data
@Slf4j
@Component
@Scope("prototype")
@EqualsAndHashCode(callSuper = false)
public class PurchaseWarehouseRecordE extends AbstractWarehouseRecord {
	@Resource
	private EntityFactory entityFactory;
	@Resource
	private PurchaseOrderWarehouseRepository purchaseOrderWarehouseRepository;
	@Resource
	private SkuQtyUnitTools skuQtyUnitTools;

	/**
	 * 创建仓库单据
	 */
	public void addWarehouseRecord() {
		long id = purchaseOrderWarehouseRepository.saveWarehouseRecord(this);
		this.setId(id);
		this.warehouseRecordDetails.forEach(record -> record.setWarehouseRecordDetail(this));
		purchaseOrderWarehouseRepository.saveWarehouseRecordDetails(this.warehouseRecordDetails);
		//存储前置单与仓库单关系
		this.addFrontRecordAndWarehouseRelation();
	}

	/**
	 * 更新仓库单据
	 */
	public void updateWarehouseRecord(List<PurchaseOrderDetailE> purchaseOrderDetailEList) {
		//不允许更新主表,只能更新状态
		//6.18 确定入库单和前置单状态修改的时候不更新
//		purchaseOrderWarehouseRepository.updateToInitStatus(this.getId());
		if (this.warehouseRecordDetails != null && this.warehouseRecordDetails.size() > 0) {
			this.warehouseRecordDetails.forEach(record -> record.setWarehouseRecordDetail(this));
			purchaseOrderWarehouseRepository.updateDetails(this.warehouseRecordDetails);
		}
		if(!CollectionUtils.isEmpty(purchaseOrderDetailEList)){
			//新增入库单详情
			List<WarehouseRecordDetail> warehouseRecordDetails =new ArrayList<WarehouseRecordDetail>();
			for (PurchaseOrderDetailE detailE : purchaseOrderDetailEList) {
				WarehouseRecordDetail warehouseRecordDetail = entityFactory.createEntity(WarehouseRecordDetail.class);
				warehouseRecordDetail.createRecordDetailByFrontRecord(detailE);
				warehouseRecordDetail.setLineNo(detailE.getLineNo());
				warehouseRecordDetail.setDeliveryData(detailE.getDeliveryData());
				//采购入库或委外入库：实际入库数量初始化为0
				warehouseRecordDetail.setActualQty(BigDecimal.ZERO);
	//			//根据行状态设置入库单delete状态
				if (PurchaseOrderConsts.ROW_STATUS_DELETED.equals(detailE.getStatus())) {
					warehouseRecordDetail.setIsDeleted(Byte.valueOf("1"));
				} else {
					warehouseRecordDetail.setIsDeleted(Byte.valueOf("0"));
				}
				warehouseRecordDetail.setWarehouseRecordDetail(this);
				warehouseRecordDetails.add(warehouseRecordDetail);
			}
			purchaseOrderWarehouseRepository.saveWarehouseRecordDetails(warehouseRecordDetails);
		}
	}

	/**
	 * 根据前置单生成出入库单据
	 */
	public void createInRecordByFrontRecord(PurchaseOrderE frontRecord) {
		this.setSapOrderCode(frontRecord.getOutRecordCode());
		this.setAbstractFrontRecord(frontRecord);
		this.setRealWarehouseId(frontRecord.getRealWarehouseId());
		this.setMerchantId(frontRecord.getMerchantId());
		this.setOutCreateTime(frontRecord.getOutCreateTime());
		List<PurchaseOrderDetailE> frontRecordDetails = frontRecord.getPurchaseOrderDetails();
		this.setWarehouseRecordDetails(new ArrayList<>(frontRecordDetails.size()));
		for (PurchaseOrderDetailE detailE : frontRecordDetails) {
			WarehouseRecordDetail warehouseRecordDetail = entityFactory.createEntity(WarehouseRecordDetail.class);
			warehouseRecordDetail.createRecordDetailByFrontRecord(detailE);
			warehouseRecordDetail.setLineNo(detailE.getLineNo());
			warehouseRecordDetail.setDeliveryData(detailE.getDeliveryData());
			if(Objects.equals(frontRecord.getRecordType(), FrontRecordTypeVO.TEMPORARY_QUALITY.getType()) || Objects.equals(frontRecord.getRecordType(), FrontRecordTypeVO.OUTSOURCE_PURCHASE_RAW_MATERIAL_RECORD.getType())){
				//针对临时性检验入库时就写入实际数量
				warehouseRecordDetail.setActualQty(detailE.getBasicSkuQty());
			}else{
				warehouseRecordDetail.setActualQty(BigDecimal.ZERO);
			}
		   this.warehouseRecordDetails.add(warehouseRecordDetail);
		}
		//设置skuCode和skuID
		this.initWarehouseRecodeDetail();
	}

	/**
	 * 根据前置单生成出入库单据
	 */
	public void createInRecordByFrontRecordOld(PurchaseOrderE frontRecord) {
		if (PurchaseOrderConsts.FOOD_PURCHASE_TYPE.equals(frontRecord.getPurchaseRecordType())
				|| PurchaseOrderConsts.FOOD_NOT_PURCHASE_TYPE.equals(frontRecord.getPurchaseRecordType())) {
			//大仓采购入库【包括食品或非食品】
			this.setRecordCode(frontRecord.getOutRecordCode());
			this.setBusinessType(WarehouseRecordBusinessTypeVO.IN_WAREHOUSE_RECORD.getType());
			//需要wms拉取，设置状态为未同步
			this.setSyncWmsStatus(WmsSyncStatusVO.UNSYNCHRONIZED.getStatus());
			this.setRecordType(WarehouseRecordTypeVO.PURCHASE_IN_WAREHOUSE_RECORD.getType());
		} else if (PurchaseOrderConsts.OUTSOURCE_PROCESS_TYPE.equals(frontRecord.getPurchaseRecordType())) {
			//委外入库
			this.setRecordCode(frontRecord.getOutRecordCode());
			this.setBusinessType(WarehouseRecordBusinessTypeVO.IN_WAREHOUSE_RECORD.getType());
			//需要wms拉取，设置状态为未同步
			this.setSyncWmsStatus(WmsSyncStatusVO.UNSYNCHRONIZED.getStatus());
			this.setRecordType(WarehouseRecordTypeVO.OUTSOURCE_IN_RECORD.getType());
		} else if (PurchaseOrderConsts.CHAIN_DIRECT_PURCHASE_TYPE.equals(frontRecord.getPurchaseRecordType())) {
			//直送
			this.setRecordCode(frontRecord.getOutRecordCode());
			this.setBusinessType(WarehouseRecordBusinessTypeVO.IN_WAREHOUSE_RECORD.getType());
			//不需要wms拉取，设置状态为无需同步
			this.setSyncWmsStatus(WmsSyncStatusVO.NO_REQUIRED.getStatus());
			this.setRecordType(WarehouseRecordTypeVO.WH_CHAIN_DIRECT_IN_RECORD.getType());
		} else if (PurchaseOrderConsts.CLOD_CHAIN_PURCHASE_TYPE.equals(frontRecord.getPurchaseRecordType())) {
			//冷链
			this.setRecordCode(frontRecord.getOutRecordCode());
			this.setBusinessType(WarehouseRecordBusinessTypeVO.IN_WAREHOUSE_RECORD.getType());
			//需要wms拉取，设置状态为未同步
			this.setSyncWmsStatus(WmsSyncStatusVO.UNSYNCHRONIZED.getStatus());
			this.setRecordType(WarehouseRecordTypeVO.WH_COLD_CHAIN_IN_RECORD.getType());
		}
		this.setSapOrderCode(frontRecord.getOutRecordCode());
		this.setAbstractFrontRecord(frontRecord);
		this.setRealWarehouseId(frontRecord.getRealWarehouseId());
//		this.setChannelCode(frontRecord.getChannelCode());
//		this.setChannelType(frontRecord.getChannelType());
		this.setMerchantId(frontRecord.getMerchantId());
		this.setOutCreateTime(frontRecord.getOutCreateTime());

		List<PurchaseOrderDetailE> frontRecordDetails = frontRecord.getPurchaseOrderDetails();
		this.setWarehouseRecordDetails(new ArrayList<>(frontRecordDetails.size()));
//		this.setExpectReceiveDateEnd(frontRecord.getExpectReceiveDateEnd());
//		this.setExpectReceiveDateStart(frontRecord.getExpectReceiveDateStart());
		for (PurchaseOrderDetailE detailE : frontRecordDetails) {
			WarehouseRecordDetail warehouseRecordDetail = entityFactory.createEntity(WarehouseRecordDetail.class);
			warehouseRecordDetail.createRecordDetailByFrontRecord(detailE);
			warehouseRecordDetail.setLineNo(detailE.getLineNo());
			warehouseRecordDetail.setDeliveryData(detailE.getDeliveryData());
            warehouseRecordDetail.setSapPoNo(frontRecord.getOutRecordCode());
			//采购入库或委外入库：实际入库数量初始化为0
			warehouseRecordDetail.setActualQty(BigDecimal.ZERO);
//			//根据行状态设置入库单delete状态
			if (PurchaseOrderConsts.ROW_STATUS_DELETED.equals(detailE.getStatus())) {
				warehouseRecordDetail.setIsDeleted(Byte.valueOf("1"));
			} else {
				warehouseRecordDetail.setIsDeleted(Byte.valueOf("0"));
			}
			this.warehouseRecordDetails.add(warehouseRecordDetail);
		}
		//设置skuCode和skuID
		this.initWarehouseRecodeDetail();
	}

	/**
	 * 根据前置单生成退货出库单
	 */
	public void createOutRecordByFrontRecord(PurchaseOrderE frontRecord) {
		createRecodeCode(WarehouseRecordTypeVO.PURCHASE_OUT_WAREHOUSE_RECORD.getCode());
		this.setBusinessType(WarehouseRecordBusinessTypeVO.OUT_WAREHOUSE_RECORD.getType());
		this.setSyncWmsStatus(WmsSyncStatusVO.NO_REQUIRED.getStatus());
		this.setRecordType(WarehouseRecordTypeVO.PURCHASE_OUT_WAREHOUSE_RECORD.getType());
		this.setSyncPurchaseStatus(WarehouseRecordConstant.INIT_SYNC_PURCHASE);

		this.setAbstractFrontRecord(frontRecord);
		this.setRealWarehouseId(frontRecord.getRealWarehouseId());
		this.setMerchantId(frontRecord.getMerchantId());
		this.setSapOrderCode(frontRecord.getOutRecordCode());
		this.setOutCreateTime(frontRecord.getOutCreateTime());
		this.setAppId("1");
		List<PurchaseOrderDetailE> frontRecordDetails = frontRecord.getPurchaseOrderDetails();
		this.setWarehouseRecordDetails(new ArrayList<>(frontRecordDetails.size()));
		WarehouseRecordDetail warehouseRecordDetail = null;
		for (PurchaseOrderDetailE detailE : frontRecordDetails) {
			warehouseRecordDetail = entityFactory.createEntity(WarehouseRecordDetail.class);
			warehouseRecordDetail.createRecordDetailByFrontRecord(detailE);
			AlikAssert.isNotNull(warehouseRecordDetail.getPlanQty(), ResCode.STOCK_ERROR_1003,"调用商品中心未查询到基本单位信息:sku_code="+detailE.getSkuCode()+" unitCode="+detailE.getUnitCode());
			warehouseRecordDetail.setLineNo(detailE.getLineNo());
			warehouseRecordDetail.setActualQty(BigDecimal.ZERO);
			warehouseRecordDetail.setDeliveryData(detailE.getDeliveryData());
			this.warehouseRecordDetails.add(warehouseRecordDetail);
		}
		//设置skuCode和skuID
		this.initWarehouseRecodeDetail();
	}
    /**
     * 根据前置单生成退货出库单
     */
    public void createOutRecordByFrontRecordOld(PurchaseOrderE frontRecord) {
        createRecodeCode(WarehouseRecordTypeVO.PURCHASE_OUT_WAREHOUSE_RECORD.getCode());
        this.setBusinessType(WarehouseRecordBusinessTypeVO.OUT_WAREHOUSE_RECORD.getType());
        this.setSyncWmsStatus(WmsSyncStatusVO.UNSYNCHRONIZED.getStatus());
        this.setRecordType(WarehouseRecordTypeVO.PURCHASE_OUT_WAREHOUSE_RECORD.getType());
        this.setAbstractFrontRecord(frontRecord);
        this.setRealWarehouseId(frontRecord.getRealWarehouseId());
//		this.setChannelCode(frontRecord.getChannelCode());
//		this.setChannelType(frontRecord.getChannelType());
        this.setMerchantId(frontRecord.getMerchantId());
        this.setSapOrderCode(frontRecord.getOutRecordCode());
        this.setOutCreateTime(frontRecord.getOutCreateTime());
        List<PurchaseOrderDetailE> frontRecordDetails = frontRecord.getPurchaseOrderDetails();
        this.setWarehouseRecordDetails(new ArrayList<>(frontRecordDetails.size()));
        WarehouseRecordDetail warehouseRecordDetail = null;
        for (PurchaseOrderDetailE detailE : frontRecordDetails) {
            warehouseRecordDetail = entityFactory.createEntity(WarehouseRecordDetail.class);
            warehouseRecordDetail.createRecordDetailByFrontRecord(detailE);
            warehouseRecordDetail.setSapPoNo(frontRecord.getOutRecordCode());
            warehouseRecordDetail.setLineNo(detailE.getLineNo());
            warehouseRecordDetail.setActualQty(BigDecimal.ZERO);
            if (PurchaseOrderConsts.ROW_STATUS_DELETED.equals(detailE.getStatus())) {
                warehouseRecordDetail.setIsDeleted(Byte.valueOf("1"));
            } else {
                warehouseRecordDetail.setIsDeleted(Byte.valueOf("0"));
            }
            this.warehouseRecordDetails.add(warehouseRecordDetail);
        }
        //设置skuCode和skuID
        this.initWarehouseRecodeDetail();
    }

	/**
	 * 根据前置单生成出入库单据详细
	 */
	public void createInRecordDetailsByFrontRecordDetails(List<PurchaseOrderDetailE> frontRecordDetails) {
		this.setWarehouseRecordDetails(new ArrayList<>(frontRecordDetails.size()));
		for (PurchaseOrderDetailE detailE : frontRecordDetails) {
			WarehouseRecordDetail warehouseRecordDetail = entityFactory.createEntity(WarehouseRecordDetail.class);
			warehouseRecordDetail.createRecordDetailByFrontRecord(detailE);
			warehouseRecordDetail.setLineNo(detailE.getLineNo());
			warehouseRecordDetail.setDeliveryData(detailE.getDeliveryData());
			//根据行状态设置入库单delete状态
			if (PurchaseOrderConsts.ROW_STATUS_DELETED.equals(detailE.getStatus())) {
				warehouseRecordDetail.setIsDeleted(Byte.valueOf("1"));
			} else {
				warehouseRecordDetail.setIsDeleted(Byte.valueOf("0"));
			}
			this.warehouseRecordDetails.add(warehouseRecordDetail);
		}

	}

	/**
	 * 包装采购退货供应商出库DO对象
	 */
	@Override
	public CoreRealStockOpDO packOutAndUnLockOpDO(WarehouseRecordE warehouseRecordE) {
		CoreRealStockOpDO stockDO = new CoreRealStockOpDO();
		List<CoreRealStockOpDetailDO> detailDos = new ArrayList<>();
		stockDO.setRecordCode(warehouseRecordE.getRecordCode());
		stockDO.setTransType(warehouseRecordE.getRecordType());
		stockDO.setDetailDos(detailDos);
		CoreRealStockOpDetailDO detailDO;
		for (WarehouseRecordDetail detail : warehouseRecordE.getWarehouseRecordDetails()) {
			//数量为0的不处理库存的增加和减少
			if(BigDecimal.ZERO.compareTo(detail.getPlanQty()) == 0){
				continue;
			}
			detailDO = new CoreRealStockOpDetailDO();
			detailDO.setUnlockQty(detail.getPlanQty());
			detailDO.setRealQty(detail.getActualQty());
			detailDO.setSkuId(detail.getSkuId());
			detailDO.setSkuCode(detail.getSkuCode());
			detailDO.setRealWarehouseId(warehouseRecordE.getRealWarehouseId());
			detailDO.setChannelCode(warehouseRecordE.getChannelCode());
			detailDO.setCheckBeforeOp(false);
			detailDos.add(detailDO);
		}
		return stockDO;
	}

}
