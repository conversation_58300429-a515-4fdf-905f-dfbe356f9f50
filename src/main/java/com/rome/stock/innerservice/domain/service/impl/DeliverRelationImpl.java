package com.rome.stock.innerservice.domain.service.impl;

import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.rome.arch.core.exception.RomeException;
import com.rome.stock.innerservice.api.dto.DeliverRelation;
import com.rome.stock.innerservice.common.ResCode;
import com.rome.stock.innerservice.domain.convertor.DeliverRelationConvertor;
import com.rome.stock.innerservice.domain.entity.DeliverRelationE;
import com.rome.stock.innerservice.domain.repository.DeliverRelationRepository;
import com.rome.stock.innerservice.domain.repository.ZdeliverRelationRepository;
import com.rome.stock.innerservice.domain.service.DeliverRelationService;
import com.rome.stock.innerservice.remote.base.dto.StoreDTO;
import com.rome.stock.innerservice.remote.base.facade.ShopFacade;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Service
public class DeliverRelationImpl implements DeliverRelationService {
    @Resource
    private DeliverRelationConvertor deliverRelationConvertor;
    @Resource
    private DeliverRelationRepository deliverRelationRepository;
    @Resource
    private ShopFacade shopFacade;
    @Resource
    private ZdeliverRelationRepository zdeliverRelationRepository;


    /**
     * 查询发货仓库配置
     *
     * @param deliverRelation
     * @return
     */
    @Override
    public PageInfo<DeliverRelation> getDeliverRelationPage(DeliverRelation deliverRelation) {
        Page page = PageHelper.startPage(deliverRelation.getPageIndex(), deliverRelation.getPageSize());
        List<DeliverRelationE> deliverRelationEList = deliverRelationRepository.getDeliverRelation(deliverRelationConvertor.dtoToEntity(deliverRelation));
        if (deliverRelationEList == null || deliverRelationEList.size() == 0) {
            return new PageInfo();
        }
        List<String> factoryCodes = deliverRelationEList.stream().map(DeliverRelationE::getFactoryCode).distinct().collect(Collectors.toList());
        List<String> deliverFactoryCodes = deliverRelationEList.stream().map(DeliverRelationE::getDeliverFactoryCode).distinct().collect(Collectors.toList());
        factoryCodes.addAll(deliverFactoryCodes);
        List<StoreDTO> storeDTOS = shopFacade.searchByCodeList(factoryCodes.stream().distinct().collect(Collectors.toList()));
        Map<String, StoreDTO> stringStoreDTOMap = storeDTOS.stream().distinct().collect(Collectors.toMap(StoreDTO::getCode, item -> item));
        for (DeliverRelationE item : deliverRelationEList) {
            if (stringStoreDTOMap.containsKey(item.getFactoryCode())) {
                item.setFactoryName(stringStoreDTOMap.get(item.getFactoryCode()).getName());
            }
            if (stringStoreDTOMap.containsKey(item.getDeliverFactoryCode())) {
                item.setDeliverFactoryName(stringStoreDTOMap.get(item.getDeliverFactoryCode()).getName());
            }
        }
        PageInfo<DeliverRelation> pageList = new PageInfo(deliverRelationConvertor.entityToDto(deliverRelationEList));
        pageList.setTotal(page.getTotal());
        return pageList;
    }
    /**
     * 新增发货仓库配置
     *
     * @param deliverRelation
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void addDeliverRelation(List<DeliverRelation> deliverRelation) {
        //检查是否是同一个工厂
        //检查是否为重复的记录
        Byte zero = 0;
        Byte one = 1;
        for (DeliverRelation item : deliverRelation) {
            if (item.getFactoryCode() == null || item.getDeliverFactoryCode() == null) {
                throw new RomeException(ResCode.STOCK_ERROR_1002, ResCode.STOCK_ERROR_1002_DESC);
            }
            if (item.getFactoryCode().equals(item.getDeliverFactoryCode())) {
                throw new RomeException(ResCode.STOCK_ERROR_9006, ResCode.STOCK_ERROR_9006_DESC);
            }
            DeliverRelationE deliverRelationE = deliverRelationRepository.getDeliverRelationByDeliverFactoryCode(item.getDeliverFactoryCode(),item.getFactoryCode());
            //此发货工厂数据存在且未被删除
            if (deliverRelationE != null && deliverRelationE.getIsDeleted().equals(zero)) {
                throw new RomeException(ResCode.STOCK_ERROR_9007, ResCode.STOCK_ERROR_9007_DESC);
            }
            //此发货工厂数据存在且被删除
            if (deliverRelationE != null && deliverRelationE.getIsDeleted().equals(one)) {
//更新数据库
                deliverRelationRepository.updateDeliverRelation(deliverRelationConvertor.dtoToEntity(item));
            } else {
//不满足上述条件的当新增数据处理
                deliverRelationRepository.addDeliverRelation(deliverRelationConvertor.dtoToEntity(item));
            }
        }
    }

    /**
     * 根据ids删除发货仓库配置
     *
     * @param ids
     * @param userId
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void delDeliverRelationByIds(List<Long> ids, Long userId) {
        if (ids != null && ids.size() > 0) {
            deliverRelationRepository.delDeliverRelationByIds(ids, userId);
        }
    }

    /**
     * 根据z工厂查询发货工厂
     * @param zFactory
     * @return
     */
    @Override
    public String getDeliverFactory(String zFactory){
        return zdeliverRelationRepository.getDeliverFactory(zFactory);
    }

    @Override
    public List<DeliverRelationE> getDeliverRelation() {
        return deliverRelationRepository.getDeliverRelation(new  DeliverRelationE());
    }

}
