package com.rome.stock.innerservice.domain.batch.impl;

import com.rome.stock.common.enums.warehouse.WarehouseRecordTypeVO;
import com.rome.stock.innerservice.domain.batch.RwBatchProcessRecordType;
import com.rome.stock.innerservice.domain.batch.RwBatchStockRelationProcessor;
import com.rome.stock.innerservice.domain.batch.dto.RwRelationQueryDTO;
import com.rome.stock.innerservice.remote.orderCneter.facade.OrderCenterFacade;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;

/**
 * @Description 电商
 * <AUTHOR>
@Service
@RwBatchProcessRecordType(recordVOTypes={
        WarehouseRecordTypeVO.ONLINE_RETAILERS_OUT_RECORD,//电商零售出库单(99)
        WarehouseRecordTypeVO.WDT_ONLINE_RETAILERS_OUT_RECORD,//旺店通出库单(100)
        WarehouseRecordTypeVO.CROSS_RETAILERS_OUT_RECORD,//跨境电商出库单(101)
        WarehouseRecordTypeVO.CLOUD_SHOP_WAREHOUSE_RECORD,//云店单据(161)
        WarehouseRecordTypeVO.WINE_SALE_WAREHOUSE_RECORD,//沪威酒订单(165)

})
public class RwBatchOnlineRetailRelationProcessor implements RwBatchStockRelationProcessor {

    @Resource
    private OrderCenterFacade orderCenterFacade;

    @Override
    public Map<String, List<String>> getRelationRecordList(RwRelationQueryDTO rwRelationQueryDTO) {
        List<String> recordCodeList = rwRelationQueryDTO.getRecordCodeList();
        Map<String,List<String>> soCodeToRecordCodesMap=orderCenterFacade.queryOutCodeBySoCodes(recordCodeList);
        //key:前置单,value:后置单列表
        Map<String, List<String>> result = new HashMap<>();
        if (Objects.isNull(soCodeToRecordCodesMap) || soCodeToRecordCodesMap.isEmpty()) {
            return result;
        }
        for (String soCode:recordCodeList){
            List<String> recordCodes=soCodeToRecordCodesMap.getOrDefault(soCode,new ArrayList<>());
            result.put(soCode,recordCodes);
        }
        return result;
    }
}
