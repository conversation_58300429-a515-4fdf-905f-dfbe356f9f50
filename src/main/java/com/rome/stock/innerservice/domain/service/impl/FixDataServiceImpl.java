package com.rome.stock.innerservice.domain.service.impl;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.google.common.collect.Lists;
import com.rome.arch.core.exception.RomeException;
import com.rome.arch.util.SpringBeanUtil;
import com.rome.stock.common.constants.PageConstants;
import com.rome.stock.common.utils.DateUtil;
import com.rome.stock.innerservice.common.ResCode;
import com.rome.stock.innerservice.common.RomeCollectionUtil;
import com.rome.stock.innerservice.common.SkuQtyUnitTools;
import com.rome.stock.innerservice.common.StringUtils;
import com.rome.stock.innerservice.domain.convertor.frontrecord.WhAllocationConvertor;
import com.rome.stock.innerservice.domain.entity.RealWarehouseE;
import com.rome.stock.innerservice.domain.entity.frontrecord.WhAllocationRecordDetailE;
import com.rome.stock.innerservice.domain.entity.warehouserecord.WarehouseRecordDetail;
import com.rome.stock.innerservice.domain.entity.warehouserecord.WarehouseRecordE;
import com.rome.stock.innerservice.domain.message.AddShopConsumer;
import com.rome.stock.core.domain.repository.CoreRealWarehouseStockRepository;
import com.rome.stock.innerservice.domain.repository.RealWarehouseRepository;
import com.rome.stock.innerservice.domain.repository.frontrecord.FrWhAllocationRepository;
import com.rome.stock.innerservice.domain.repository.warehouserecord.WarehouseRecordRepository;
import com.rome.stock.innerservice.domain.service.FixDataService;
import com.rome.stock.core.facade.RedisRollBackFacade;
import com.rome.stock.innerservice.infrastructure.dataobject.FrontWarehouseRecordRelationDO;
import com.rome.stock.core.infrastructure.dataobject.core.CoreRealStockOpDO;
import com.rome.stock.core.infrastructure.dataobject.core.CoreRealStockOpDetailDO;
import com.rome.stock.innerservice.infrastructure.dataobject.OrderCostDO;
import com.rome.stock.innerservice.infrastructure.dataobject.frontrecord.FrWhAllocationDetailDO;
import com.rome.stock.innerservice.infrastructure.mapper.FixDataMapper;
import com.rome.stock.innerservice.infrastructure.mapper.frontrecord.OrderCostMapper;
import com.rome.stock.innerservice.remote.base.dto.RegionDivideInfosDTO;
import com.rome.stock.innerservice.remote.base.dto.StoreBaseInfoDTO;
import com.rome.stock.innerservice.remote.base.dto.StoreDTO;
import com.rome.stock.innerservice.remote.base.dto.StoreMdmAttributeExtDTO;
import com.rome.stock.innerservice.remote.base.facade.ShopFacade;
import com.rome.stock.innerservice.remote.stockCore.facade.StockCoreFacade;
import com.rome.stock.innerservice.remote.venus.facade.VenusStockFacade;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 类FixDataServiceImpl的实现描述：数据修复服务
 *
 * <AUTHOR> 2019/10/11 19:13
 */
@Slf4j
@Service
public class FixDataServiceImpl implements FixDataService {

    @Resource
    private FixDataMapper fixDataMapper;

    @Resource
    private WarehouseRecordRepository warehouseRecordRepository;

    @Resource
    private FrWhAllocationRepository frWhAllocationRepository;

    @Resource
    private SkuQtyUnitTools skuQtyUnitTools;

    @Resource
    private WhAllocationConvertor whAllocationConvertor;

    @Resource
    private CoreRealWarehouseStockRepository coreRealWarehouseStockRepository;

    @Resource
    private ShopFacade shopFacade;

    @Resource
    private AddShopConsumer shopConsumer;

    @Resource
    private RealWarehouseRepository realWarehouseRepository;

    @Resource
    private StockCoreFacade stockCoreFacade;
    @Resource
    private OrderCostMapper orderCostMapper;
    @Resource
    private VenusStockFacade venusStockFacade;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void fixShopinventoryData() {
        int i = 0;
       while (true){
           Page page = PageHelper.startPage(i, 100);
           List<Long> ids = fixDataMapper.getFixDataList();
           if(CollectionUtils.isNotEmpty(ids)){
               fixDataMapper.fixShopinventoryData(ids);
               i++;
           }else{
               break;
           }
       }
    }

    @Override
    public void fixWhallotData(String recordCode) {
        //先根据code查询出库单及明细
        WarehouseRecordE warehouseRecordE = warehouseRecordRepository.getRecordWithDetailByCode(recordCode, null);
        List<WarehouseRecordDetail> wareDetailList = warehouseRecordE.getWarehouseRecordDetails();
        //获取前置单
        FrontWarehouseRecordRelationDO recordRelationDO = warehouseRecordRepository.getFrontWarehouseRecordRelationByWrId(warehouseRecordE.getId());
        List<WhAllocationRecordDetailE>  frontRecordDetails =   frWhAllocationRepository.queryDetailByOrderId(recordRelationDO.getFrontRecordId());
        Map<Long, WarehouseRecordDetail> wareDetailMap = RomeCollectionUtil.listforMap(warehouseRecordE.getWarehouseRecordDetails(), "skuId", null);
        List<WhAllocationRecordDetailE> updateDetail =  new ArrayList<>();
        for (WhAllocationRecordDetailE frontDetail : frontRecordDetails) {
            WarehouseRecordDetail whDetail = wareDetailMap.get(frontDetail.getSkuId());
            if(whDetail != null){
                BigDecimal actualQty = whDetail.getActualQty();
                frontDetail.setInQty(actualQty);
                updateDetail.add(frontDetail);
            }
        }
        frontRecordDetails.forEach(wrRecord -> wrRecord.setBasicSkuQty(wrRecord.getInQty()));
        skuQtyUnitTools.convertBasicToReal(frontRecordDetails);
        for(WhAllocationRecordDetailE detailE : frontRecordDetails){
            detailE.setInQty(detailE.getSkuQty());
        }
        List<FrWhAllocationDetailDO> listDo = whAllocationConvertor.detailListEntityToDo(updateDetail);
        if(CollectionUtils.isNotEmpty(listDo)){
            fixDataMapper.updateDetailInQtyList(listDo);
        }
    }

    @Override
    public void fixJoinReceiptData(String recordCode) {
        boolean isSuccess = false;
        CoreRealStockOpDO coreRealStockOpDO1 = null;
        CoreRealStockOpDO coreRealStockOpDO2 = null;
        WarehouseRecordE warehouseRecordE  = warehouseRecordRepository.getRecordWithDetailByCode(recordCode, null);
        if(warehouseRecordE != null){
            try {
                coreRealStockOpDO1 = this.initDecreaseStockObj(warehouseRecordE);
                coreRealWarehouseStockRepository.decreaseRealQty(coreRealStockOpDO1);
                coreRealStockOpDO2 = warehouseRecordE.initIncreaseStockObj(warehouseRecordE.getRealWarehouseId());
                coreRealWarehouseStockRepository.increaseRealQty(coreRealStockOpDO2);
                isSuccess = true;
            } catch (RomeException e) {
                log.error(e.getMessage(), e);
                throw new RomeException(e.getCode(), e.getMessage());
            } catch (Exception e) {
                log.error(e.getMessage(), e);
                throw new RomeException(ResCode.STOCK_ERROR_1003, ResCode.STOCK_ERROR_1003_DESC);
            } finally {
                if (!isSuccess) {
                    RedisRollBackFacade.redisRollBack(coreRealStockOpDO1);
                }
                if (!isSuccess) {
                    RedisRollBackFacade.redisRollBack(coreRealStockOpDO2);
                }
            }
        }

    }

    @Override
    public List<String> selectAllNormalStoreContainNoAndPreOpen() {
        StoreDTO query =new StoreDTO();
        query.setType("A");
        List<String> shopCodes= Lists.newArrayList();
        List<StoreDTO> storeDTOList =null ;
        int page = 1;
        do {
            storeDTOList=Lists.newArrayList();
            PageInfo<StoreDTO> res= shopFacade.searchStoreByName(page,PageConstants.ROWS_500,query);
            if (null == res || org.springframework.util.CollectionUtils.isEmpty(res.getList())) {
                break;
            }
            storeDTOList=res.getList();
            //新开店补偿查询所有非闭店的门店
            List<String> codes=storeDTOList.stream().filter(v-> !Objects.equals("拆店",v.getDesc())).map(StoreDTO::getCode).collect(Collectors.toList());
            shopCodes.addAll(codes);
            if(res.isIsLastPage()){
                //添加完最后一页数据时，直接结束
                break;
            }
            page++;
        } while (!org.springframework.util.CollectionUtils.isEmpty(storeDTOList) && storeDTOList.size() == PageConstants.ROWS_500);
        return shopCodes;
    }

    @SuppressWarnings("unchecked")
	@Override
    public String openNewShop(List<String> shopCodeList) {
    	// 批量查询实仓表
    	List<RealWarehouseE> list = realWarehouseRepository.getRwListByShopCodes(shopCodeList);
    	// key>>>shopCode val>>>RealWarehouse
    	Map<String, RealWarehouseE> mapRealWarehouse;
    	if(CollectionUtils.isNotEmpty(list)){
    		mapRealWarehouse = list.stream().collect(Collectors.toMap(RealWarehouseE::getShopCode, Function.identity(), (v1, v2) -> v1));
    	} else {
    		mapRealWarehouse = Collections.EMPTY_MAP;
    	}
    	// 批量查询门店数据
    	List<StoreDTO> storeList = shopFacade.searchByCodeList(shopCodeList);
    	// key>>>shopCode val>>>StoreDTO
    	Map<String, StoreDTO> mapStore;
    	// 行政区域 key>>>code val>>>RegionDivideInfosDTO
    	Map<String, RegionDivideInfosDTO> mapRegion = Collections.EMPTY_MAP;
    	if(CollectionUtils.isNotEmpty(storeList)){
    		mapStore = storeList.stream().collect(Collectors.toMap(StoreDTO::getCode, Function.identity(), (v1, v2) -> v1));
    		List<String> provinceCodeList = storeList.stream().filter(dto -> StringUtils.isNotBlank(dto.getProvinceCode())).map(StoreDTO::getProvinceCode).distinct().collect(Collectors.toList());
    		// 根据省查询行政区域
    		if(CollectionUtils.isNotEmpty(provinceCodeList)) {
    			List<RegionDivideInfosDTO> dtoList = shopFacade.queryRegionByProvinceCodes(provinceCodeList);
    			if(CollectionUtils.isNotEmpty(dtoList)){
    				mapRegion = dtoList.stream().collect(Collectors.toMap(RegionDivideInfosDTO::getCode, Function.identity(), (v1, v2) -> v1));
    			}
    		}
    	} else {
    		mapStore = Collections.EMPTY_MAP;
    	}
    	// 财务中台相关数据
        List<StoreMdmAttributeExtDTO> storeMdmDTOList = shopFacade.selectMdmStoreInfoByCodeList(shopCodeList);
        // key>>>shopCode val>>>StoreDTO
    	Map<String, StoreMdmAttributeExtDTO> mapStoreMdm;
    	if(CollectionUtils.isNotEmpty(storeList)){
    		mapStoreMdm = storeMdmDTOList.stream().collect(Collectors.toMap(StoreMdmAttributeExtDTO::getStoreCode, Function.identity(), (v1, v2) -> v1));
    	} else {
    		mapStoreMdm = Collections.EMPTY_MAP;
    	}
    	FixDataService fixDataService = SpringBeanUtil.getBean(FixDataService.class);
    	// 处理
    	RealWarehouseE realWarehouseE;
    	StoreDTO storeDTO;
    	StoreMdmAttributeExtDTO storeMdmDTO;
    	RegionDivideInfosDTO regionDTO;
    	// 错误
    	JSONArray errorArray = new JSONArray();
    	for(String shopCode : shopCodeList) {
    		try {
    			realWarehouseE = mapRealWarehouse.get(shopCode);
    			storeDTO = mapStore.get(shopCode);
    			storeMdmDTO = mapStoreMdm.get(shopCode);
    			regionDTO = null;
    			if(storeDTO != null && StringUtils.isNotBlank(storeDTO.getProvinceCode())) {
    				regionDTO = mapRegion.get(storeDTO.getProvinceCode());
    			}
    			boolean flag = fixDataService.innerOpenNewShop(realWarehouseE, storeDTO, storeMdmDTO, regionDTO);
                // true 新增加的门店增加门店时效
                if(flag) {
                    stockCoreFacade.addRealWarehouseServiceLab(storeDTO.getCode());
                }
			} catch (RomeException e) {
				log.error("补偿开店,出错,code={}",shopCode, e);
				JSONObject errorObject = new JSONObject();
				errorObject.put("shopCode", shopCode);
				errorObject.put("errorMsg", e.getMessage());
				errorArray.add(errorObject);
			} catch (Exception e) {
				log.error("补偿开店,出错,code={}",shopCode, e);
				JSONObject errorObject = new JSONObject();
				errorObject.put("shopCode", shopCode);
				errorObject.put("errorMsg", e.getMessage());
				errorArray.add(errorObject);
			}
    	}
    	if(errorArray.size() > 0) {
    		return errorArray.toJSONString();
    	}
    	return "";
    }

    /**
     * 内部实现，增加或更新门店数据
     * @param realWarehouseE 实仓数据
     * @param storeDTO 门店基础数据
     * @param storeMdmDTO 门店mdm数据
     * @param regionDTO 行政区
     * @return true-表示新增加的门店，false-其他
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
	public boolean innerOpenNewShop(RealWarehouseE realWarehouseE, StoreDTO storeDTO, StoreMdmAttributeExtDTO storeMdmDTO,
			RegionDivideInfosDTO regionDTO) {
		if(realWarehouseE != null) {
			// 老的字
			String oldToString = realWarehouseE.toString();
			realWarehouseE.setRealWarehouseProvince(storeDTO.getProvince());
			realWarehouseE.setRealWarehouseProvinceCode(storeDTO.getProvinceCode());
			realWarehouseE.setRealWarehouseCity(storeDTO.getCity());
			realWarehouseE.setRealWarehouseCityCode(storeDTO.getCityCode());
			realWarehouseE.setRealWarehouseCounty(storeDTO.getCounty());
			realWarehouseE.setRealWarehouseCountyCode(storeDTO.getDistrictCode());
			// 设置行政区域
			// 根据省查询行政区域
			if (regionDTO != null) {
				realWarehouseE.setRegionId(regionDTO.getAreaId());
				realWarehouseE.setRegionName(regionDTO.getRegionName());
			}
			// 财务中台相关数据
			if (storeMdmDTO != null) {
				realWarehouseE.setFactoryName(storeDTO.getName());// 工厂名称
				realWarehouseE.setCostCenterCode(storeMdmDTO.getCostCenterCode());// 成本中心编码
				realWarehouseE.setCostCenterName(storeMdmDTO.getCostCenterName());// 成本中心名称
				realWarehouseE.setCompanyCode(storeMdmDTO.getCompanyCode());// 公司编码
				realWarehouseE.setCompanyName(storeMdmDTO.getCompanyName());// 公司名称
				// 最新判断虚拟门店的接口：/api/v1/storeMdmAttribute/selectMdmStoreInfoByCodes
				// 字段：storeExistenceForm 门店存在形式[0-线上门店,1-线下有执照门店,2-线下无执照门店（展会）
				// storeExistenceForm=0为虚拟门店,并且StoreProperties以这个接口为准
				if ("0".equals(storeMdmDTO.getStoreExistenceForm())) {
                    // 特殊逻辑，加盟线上店设置仓库类型为2， 贾妮已确认
                    if (!"3".equals(storeMdmDTO.getStoreProperties())){
                        storeDTO.setStoreProperties("5");
                    }
				} else {
					storeDTO.setStoreProperties(storeMdmDTO.getStoreProperties());
				}
			}
			if (storeDTO.getStoreProperties() != null) {
				realWarehouseE.setRwBusinessType(shopConsumer.getRwBusinessTypeByStore(storeDTO));// 仓库业务类型: 1: 直营门店 2: 加盟门店 3: 仓库 4.其他仓库
			}
			String newToString = realWarehouseE.toString();
			if (!newToString.equals(oldToString)) {
				boolean j = realWarehouseRepository.updateRealWarehouseByWhere(realWarehouseE);
				if (!j) {
					throw new RomeException(ResCode.STOCK_ERROR_1003, "更新失败，仓库code：" + realWarehouseE.getRealWarehouseCode());
				}
			}
			return false;
		}
		if(null != storeDTO) {
			StringBuffer sb = new StringBuffer("");
			com.rome.stock.innerservice.api.dto.message.StoreDTO store = new com.rome.stock.innerservice.api.dto.message.StoreDTO();
			BeanUtils.copyProperties(storeDTO, store);
			boolean rs = shopConsumer.addShop(sb, store);
			if (rs) {
				throw new RomeException(ResCode.STOCK_ERROR_1003, sb.toString());
			}
            // 新增加的
			return true;
		}
		throw new RomeException(ResCode.STOCK_ERROR_1003, "门店不存在");
	}


    /**
     * 省市区或行政区域不存在
     * @param realWarehouseE
     * @return
     */
    private boolean checkParams(RealWarehouseE realWarehouseE){
        if(StringUtils.isEmpty(realWarehouseE.getRealWarehouseProvince()) || StringUtils.isEmpty(realWarehouseE.getRealWarehouseProvinceCode())
                ||StringUtils.isEmpty(realWarehouseE.getRealWarehouseCity()) || StringUtils.isEmpty(realWarehouseE.getRealWarehouseCityCode())
                ||StringUtils.isEmpty(realWarehouseE.getRealWarehouseCounty()) || StringUtils.isEmpty(realWarehouseE.getRealWarehouseCountyCode())
                ){
//            ||StringUtils.isEmpty(realWarehouseE.getRegionName()) || null==realWarehouseE.getRegionId()
            return true;
        }
        return false;
    }





    /**
     * 减少实体仓库库存的对象(按照planQty减少,特殊需求在serviceImpl中自行封装)
     */
    private CoreRealStockOpDO initDecreaseStockObj(WarehouseRecordE warehouseRecordE){
        List<CoreRealStockOpDetailDO> decreaseDetails = new ArrayList<>();
        for (WarehouseRecordDetail detail: warehouseRecordE.getWarehouseRecordDetails()) {
            //数量为0的不处理库存的增加和减少
            if(BigDecimal.ZERO.compareTo(detail.getActualQty()) == 0){
                continue;
            }
            CoreRealStockOpDetailDO coreRealStockOpDetailDO = new CoreRealStockOpDetailDO();
            coreRealStockOpDetailDO.setSkuId(detail.getSkuId());
            coreRealStockOpDetailDO.setSkuCode(detail.getSkuCode());
            coreRealStockOpDetailDO.setRealQty(detail.getActualQty());
            coreRealStockOpDetailDO.setRealWarehouseId(warehouseRecordE.getRealWarehouseId());
            coreRealStockOpDetailDO.setMerchantId(10000L);
            coreRealStockOpDetailDO.setCheckBeforeOp(false);
            decreaseDetails.add(coreRealStockOpDetailDO);
        }
        CoreRealStockOpDO coreRealStockOpDO = new CoreRealStockOpDO();
        coreRealStockOpDO.setRecordCode(warehouseRecordE.getRecordCode());
        coreRealStockOpDO.setTransType(warehouseRecordE.getRecordType());
        coreRealStockOpDO.setDetailDos(decreaseDetails);
        return coreRealStockOpDO;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public String updateCostFinanceDate(String ztRecordCodeStr, String ztBusinessCodeStr, String financDate, Integer businessType) {

        if (DateUtil.parse(financDate, DateUtil.NORM_DATE_PATTERN) == null) {
            return "财务日期格式不正确";
        }
        List<String> ztRecordCodeList = new ArrayList<>();
        if (StringUtils.isNoneBlank(ztRecordCodeStr)) {
            ztRecordCodeList = Arrays.asList(ztRecordCodeStr.trim().split(","));
        } else if (StringUtils.isNoneBlank(ztBusinessCodeStr)) {
            List<String> ztBusinessCodeList = Arrays.asList(ztBusinessCodeStr.trim().split(","));
            ztRecordCodeList= orderCostMapper.queryZtRecordCodeByZtBusinessCodes(ztBusinessCodeList);
        } else {
            return "唯一单号和业务单号必须填一个";
        }
        if (businessType != null) {
            List<OrderCostDO> orderCostDOList = orderCostMapper.queryByZtRecordCodes(ztRecordCodeList);
            ztRecordCodeList = orderCostDOList.stream().filter(item -> Objects.equals(item.getBusinessType(), businessType)).map(OrderCostDO::getZtRecordCode).collect(Collectors.toList());
        }

        if (CollectionUtils.isEmpty(ztRecordCodeList)) {
            return "没有符合条件的单据";
        }
        //刷对应的一组单子，入股存在虚单的话
        // 调财务中台修改财务日期,这里是组单号列表,返回修改的唯一单号列表
        ztRecordCodeList = venusStockFacade.updateStockMessageLogFinanceDate(ztRecordCodeList, financDate);
        if (CollectionUtils.isEmpty(ztRecordCodeList)) {
            return "财务中台返回,没有符合条件的单据";
        }
        // 修改拼接表的财务日期
        orderCostMapper.updateFinanceDateByZtRecordCodeList(ztRecordCodeList, financDate);
        return null;
    }
}
