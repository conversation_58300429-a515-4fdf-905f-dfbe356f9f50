package com.rome.stock.innerservice.domain.batch.impl;

import com.rome.stock.common.enums.warehouse.WarehouseRecordTypeVO;
import com.rome.stock.innerservice.api.dto.ChargeAgainstDTO;
import com.rome.stock.innerservice.domain.batch.RwBatchProcessRecordType;
import com.rome.stock.innerservice.domain.batch.RwBatchStockRelationProcessor;
import com.rome.stock.innerservice.domain.batch.dto.RwRelationQueryDTO;
import com.rome.stock.innerservice.infrastructure.mapper.frontrecord.FrChargeAgainstMapper;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;

/**
 * @Description 采购退货冲销
 * <AUTHOR>
 * @Date 2023/11/22
 * @Version 1.0
 */
@Service
@RwBatchProcessRecordType(recordVOTypes = {
        WarehouseRecordTypeVO.REVERSE_RETURN_PURCHASE_RECORD,//大仓采购退库单冲销(3017)
        WarehouseRecordTypeVO.REVERSE_PURCHASE_RECORD,//大仓采购入库单冲销(3016)
        WarehouseRecordTypeVO.REVERSE_RECEIVE_RECORD,//领用出库单冲销(3049)
        WarehouseRecordTypeVO.REVERSE_DIRECT_SENDING_RECORD,//直送越库入库单冲销(3043)
        WarehouseRecordTypeVO.REVERSE_FREE_EAT_RECORD,//门店试吃出库单冲销(3009)
        WarehouseRecordTypeVO.REVERSE_WAREHOUSE_SCRAP_RECORD,//仓库报废出库单冲销(3028)
        WarehouseRecordTypeVO.SHOP_OUT_CONSUME_REVERSE_RECORD//门店报废出库单冲销(3048)
})
public class RwBatchChargeAgainstRelationProcessor implements RwBatchStockRelationProcessor {

    @Resource
    private FrChargeAgainstMapper frChargeAgainstMapper;

    @Override
    public Map<String, List<String>> getRelationRecordList(RwRelationQueryDTO rwRelationQueryDTO) {
        List<String> recordCodeList = rwRelationQueryDTO.getRecordCodeList();
        List<ChargeAgainstDTO> list = frChargeAgainstMapper.getOriginWarehouseRecordCodeByWrCodeList(recordCodeList);
        Map<String, List<String>> result = new HashMap<>();
        if (CollectionUtils.isEmpty(list)) {
            return result;
        }
        for (ChargeAgainstDTO chargeAgainstDTO : list) {
            String warehouseRecordCode = chargeAgainstDTO.getWarehouseRecordCode();
            List<String> itemList = result.getOrDefault(warehouseRecordCode, new ArrayList<>());
            itemList.add(chargeAgainstDTO.getOriginWarehouseRecordCode());
            result.put(warehouseRecordCode, itemList);
        }
        if (Objects.equals(WarehouseRecordTypeVO.REVERSE_RECEIVE_RECORD.getType(),rwRelationQueryDTO.getRecordType())||
                Objects.equals(WarehouseRecordTypeVO.REVERSE_FREE_EAT_RECORD.getType(),rwRelationQueryDTO.getRecordType())||
                Objects.equals(WarehouseRecordTypeVO.REVERSE_WAREHOUSE_SCRAP_RECORD.getType(),rwRelationQueryDTO.getRecordType())||
                Objects.equals(WarehouseRecordTypeVO.SHOP_OUT_CONSUME_REVERSE_RECORD.getType(),rwRelationQueryDTO.getRecordType())){
            //查询关联单流水数据
            rwRelationQueryDTO.setNeedQueryFlow(1);
        }
        return result;
    }

}
