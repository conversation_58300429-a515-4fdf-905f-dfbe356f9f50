package com.rome.stock.innerservice.domain.message.consumer;

import com.alibaba.fastjson.JSONObject;
import com.rome.scm.common.rocketmq.CustomConsumerListener;
import com.rome.stock.common.enums.CustomRocketMQEnum;
import com.rome.stock.innerservice.api.dto.RwBatchDTO;
import com.rome.stock.innerservice.api.dto.frontrecord.QualityResultCallBackDTO;
import com.rome.stock.innerservice.domain.convertor.RwBatchConvertor;
import com.rome.stock.innerservice.domain.entity.RwBatchE;
import com.rome.stock.innerservice.domain.repository.RwBatchRepository;
import com.rome.stock.innerservice.domain.service.PurchaseOrderService;
import lombok.extern.slf4j.Slf4j;
import org.apache.rocketmq.common.message.MessageExt;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * @Description: 质检回调自动通知WMS结果MQ
 */
@Slf4j
@Service
public class QualityNotifyAutoWmsConsumer implements CustomConsumerListener<MessageExt> {

    @Resource
    private PurchaseOrderService purchaseOrderService;
    @Resource
    private RwBatchRepository rwBatchRepository;
    @Resource
    private RwBatchConvertor rwBatchConvertor;

    @Override
    public void onMessage(MessageExt messageExt, String businessNo, String msgKey) {
        log.info("质检回调自动通知WMS结果MQ消费，msgID: {}，消费次数: {}", messageExt.getMsgId(), messageExt.getReconsumeTimes());
        String json = new String(messageExt.getBody());
        if(StringUtils.isEmpty(json)) {
            log.error("质检回调自动通知WMS结果MQ消费内容为空，msgID: {}", messageExt.getMsgId());
            return ;
        }
        QualityResultCallBackDTO dto = JSONObject.parseObject(json, QualityResultCallBackDTO.class);
        List<RwBatchE> wmsBatchList = rwBatchRepository.queryBywmsRecordCode(dto.getRecordCode(), dto.getWmsRecordCode());
        //只需要处理待同步的数据
        wmsBatchList=wmsBatchList.stream().filter(v-> Objects.equals(v.getSyncStatus(),0)).collect(Collectors.toList());
        if(CollectionUtils.isEmpty(wmsBatchList)){
            return;
        }
        List<RwBatchDTO> wmsRecordCodeBatchList = rwBatchConvertor.entitiesToDtos(wmsBatchList);
        purchaseOrderService.synchronizationCheckResultToWmsByReceiveRecord(dto.getWmsRecordCode(), wmsRecordCodeBatchList,false);
    }

	@Override
	public String getRocketMQCode() {
		return CustomRocketMQEnum.MQ_QUALITY_NOTIFY_AUTO_WMS.getCode();
	}

}
