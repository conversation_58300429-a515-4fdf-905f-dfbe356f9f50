package com.rome.stock.innerservice.domain.entity.warehouserecord;

import com.rome.arch.core.domain.EntityFactory;
import com.rome.stock.innerservice.api.dto.warehouserecord.InWarehouseRecordDTO;
import com.rome.stock.innerservice.api.dto.warehouserecord.OutWarehouseRecordDTO;
import com.rome.stock.innerservice.api.dto.warehouserecord.RecordDetailDTO;
import com.rome.stock.innerservice.constant.WarehouseRecordBatchStatusVO;
import com.rome.stock.innerservice.constant.WarehouseRecordBusinessTypeVO;
import com.rome.stock.innerservice.constant.WmsSyncStatusVO;
import com.rome.stock.innerservice.domain.repository.warehouserecord.SaleWarehouseRepository;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * 门店销售出入库单
 *
 * <AUTHOR> 2019/4/17 20:52
 */
@Data
@Slf4j
@Component
@Scope("prototype")
@EqualsAndHashCode(callSuper = false)
public class ShopRetailWarehouseRecordE extends AbstractWarehouseRecord{
    @Resource
    private EntityFactory entityFactory;
    @Autowired
    private SaleWarehouseRepository saleWarehouseRepository;

    /**
     * 创建仓库单据
     */
    public void addWarehouseRecord() {
        if(StringUtils.isBlank(this.getMobile())) {
            this.setMobile("");
        }
        if(StringUtils.isBlank(this.getUserCode())) {
            this.setUserCode("");
        }
        long id=saleWarehouseRepository.saveWarehouseRecord(this);
        this.setId(id);
        this.warehouseRecordDetails.forEach(record -> record.setWarehouseRecordDetail(this));
        saleWarehouseRepository.saveWarehouseRecordDetails(this.warehouseRecordDetails);
        //存储前置单与仓库单关系
        //this.addFrontRecordAndWarehouseRelation();
    }

    /**
     * 根据前置单生成出库单据
     */
    public void createOutRecordByFrontRecord(OutWarehouseRecordDTO frontRecord, Long realWarehouseId){
        this.setRecordCode(frontRecord.getRecordCode());
        this.setRealWarehouseId(realWarehouseId);
        this.setBusinessType(WarehouseRecordBusinessTypeVO.OUT_WAREHOUSE_RECORD.getType());
        this.setRecordType(frontRecord.getRecordType());
        this.setChannelCode(frontRecord.getChannelCode());
        List<RecordDetailDTO> frontRecordDetails=frontRecord.getDetailList();
        this.setWarehouseRecordDetails(new ArrayList<>(frontRecordDetails.size()));
        this.setSyncWmsStatus(WmsSyncStatusVO.NO_REQUIRED.getStatus());
        this.setBatchStatus(WarehouseRecordBatchStatusVO.INIT.getStatus());
        this.setSapOrderCode(frontRecord.getSapOrderCode());
        if(frontRecordDetails!=null){
            for(RecordDetailDTO detail:frontRecordDetails){
                WarehouseRecordDetail warehouseRecordDetail=entityFactory.createEntity(WarehouseRecordDetail.class);
                warehouseRecordDetail.setUnitCode(detail.getBasicUnitCode());
                warehouseRecordDetail.setUnit(detail.getBasicUnit());
                warehouseRecordDetail.setSkuCode(detail.getSkuCode());
                warehouseRecordDetail.setActualQty(detail.getBasicSkuQty());
                warehouseRecordDetail.setPlanQty(detail.getBasicSkuQty());
                warehouseRecordDetail.setRecordCode(frontRecord.getRecordCode());
                warehouseRecordDetail.setSapPoNo(detail.getSapPoNo());
                warehouseRecordDetail.setLineNo(detail.getLineNo());
                warehouseRecordDetail.setDeliveryLineNo(detail.getDeliveryLineNo());
                this.warehouseRecordDetails.add(warehouseRecordDetail);
            }
            //设置skuCode和skuID
            log.info(frontRecord.getRecordCode()+":设置skuCode和skuID："+System.currentTimeMillis());
            this.initWarehouseRecodeDetail();
        }
    }



    /**
     * 根据前置单生成入库单据
     */
    public void createInRecordByFrontRecord(InWarehouseRecordDTO frontRecord, Long realWarehouseId){
        this.setRecordCode(frontRecord.getRecordCode());
        this.setRealWarehouseId(realWarehouseId);
        this.setBusinessType(WarehouseRecordBusinessTypeVO.IN_WAREHOUSE_RECORD.getType());
        this.setRecordType(frontRecord.getRecordType());
        this.setChannelCode(frontRecord.getChannelCode());
        List<RecordDetailDTO> frontRecordDetails=frontRecord.getDetailList();
        this.setWarehouseRecordDetails(new ArrayList<>(frontRecordDetails.size()));
        this.setSyncWmsStatus(WmsSyncStatusVO.NO_REQUIRED.getStatus());
        this.setBatchStatus(WarehouseRecordBatchStatusVO.INIT.getStatus());
        this.setSapOrderCode(frontRecord.getSapOrderCode());
        if(frontRecordDetails!=null){
            for(RecordDetailDTO detail:frontRecordDetails){
                WarehouseRecordDetail warehouseRecordDetail=entityFactory.createEntity(WarehouseRecordDetail.class);
                warehouseRecordDetail.setUnitCode(detail.getBasicUnitCode());
                warehouseRecordDetail.setUnit(detail.getBasicUnit());
                warehouseRecordDetail.setSkuCode(detail.getSkuCode());
                warehouseRecordDetail.setActualQty(detail.getBasicSkuQty());
                warehouseRecordDetail.setPlanQty(detail.getBasicSkuQty());
                warehouseRecordDetail.setRecordCode(frontRecord.getRecordCode());
                warehouseRecordDetail.setSapPoNo(detail.getSapPoNo());
                warehouseRecordDetail.setLineNo(detail.getLineNo());
                warehouseRecordDetail.setDeliveryLineNo(detail.getDeliveryLineNo());
                this.warehouseRecordDetails.add(warehouseRecordDetail);
            }
            //设置skuCode和skuID
            log.info(frontRecord.getRecordCode()+":设置skuCode和skuID："+System.currentTimeMillis());
            this.initWarehouseRecodeDetail();
        }
    }

    /**
     * 用户code
     */
    private String userCode;

    /**
     * 用户手机号
     */
    private String mobile;

    /**
     * 外部系统数据创建时间:下单时间
     */
    private Date outCreateTime;

}
