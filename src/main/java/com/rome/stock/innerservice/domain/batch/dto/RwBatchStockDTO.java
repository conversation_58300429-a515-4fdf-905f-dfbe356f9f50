package com.rome.stock.innerservice.domain.batch.dto;

import com.rome.stock.innerservice.infrastructure.dataobject.RwBatchDo;
import lombok.Data;

import java.util.List;
import java.util.Set;

/**
 * @Description
 * <AUTHOR>
 * @Date 2023/11/23
 * @Version 1.0
 */
@Data
public class RwBatchStockDTO {




    /**
     * 单据编号
     */
    private String recordCode;


    /**
     * 单据编号
     */
    private Integer recordType;


    /**
     * 批次数据
     */
    private List<RwBatchDo> batchDoList;

    /**
     * 组成batchDoList 的单据 recordCode 集合 (后置单号) , 如果是电商，则是前置单集合
     */
    private Set<String> comRecordCodeSet;
}
