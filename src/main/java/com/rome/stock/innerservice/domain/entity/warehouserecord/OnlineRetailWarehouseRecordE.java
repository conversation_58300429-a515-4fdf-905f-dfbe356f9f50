package com.rome.stock.innerservice.domain.entity.warehouserecord;

import com.rome.arch.core.domain.EntityFactory;
import com.rome.stock.common.enums.warehouse.WarehouseRecordTypeVO;
import com.rome.stock.innerservice.api.dto.RealWarehouseWmsConfigDTO;
import com.rome.stock.innerservice.common.AlikAssert;
import com.rome.stock.innerservice.common.ResCode;
import com.rome.stock.innerservice.constant.*;
import com.rome.stock.innerservice.domain.convertor.warehouserecord.OnlineRetailWarehouseConvertor;
import com.rome.stock.innerservice.domain.entity.RwRecordPoolDetailE;
import com.rome.stock.innerservice.domain.entity.RwRecordPoolE;
import com.rome.stock.innerservice.domain.entity.frontrecord.OnlineRetailE;
import com.rome.stock.core.domain.repository.CoreRealWarehouseStockRepository;
import com.rome.stock.innerservice.domain.repository.RealWarehouseWmsConfigRepository;
import com.rome.stock.innerservice.domain.repository.RwRecordPoolRepository;
import com.rome.stock.innerservice.domain.repository.warehouserecord.OnlineRetailWarehouseRepository;
import com.rome.stock.innerservice.domain.repository.warehouserecord.WarehouseRecordRepository;
import com.rome.stock.innerservice.infrastructure.dataobject.FrontWarehouseRecordRelationDO;
import com.rome.stock.innerservice.remote.orderTrack.facade.OrderTrackFacade;
import com.rome.stock.wms.constants.WarehouseWmsConfigEnum;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;

/**
 * 电商出入库
 */
@Data
@Slf4j
@Component
@Scope("prototype")
@EqualsAndHashCode(callSuper = false)
public class OnlineRetailWarehouseRecordE extends AbstractWarehouseRecord{

    @Autowired
    private CoreRealWarehouseStockRepository coreRealWarehouseStockRepository;
    @Autowired
    private OnlineRetailWarehouseConvertor onlineRetailWarehouseConvertor;
    @Autowired
    private OnlineRetailWarehouseRepository onlineRetailWarehouseRepository;
    @Autowired
    private RwRecordPoolRepository rwRecordPoolRepository;
    @Autowired
    private OrderTrackFacade orderTrackFacade;
    @Autowired
    private WarehouseRecordRepository warehouseRecordRepository;
    @Resource
    private EntityFactory entityFactory;
    @Resource
    private RealWarehouseWmsConfigRepository realWarehouseWmsConfigRepository;


    /**
     * 根据前置单及Do池数据，构建出库单及明细信息(单个SO单)
     *  以出库单级别进行回滚
     * @param frontRecord
     */
    @Transactional(rollbackFor = Exception.class)
    public void addRecordAndMerge(OnlineRetailE frontRecord) {
        //添加出库单
        addWarehouseRecord(frontRecord);
        //前置单与出库单绑定关系
        this.setAbstractFrontRecord(frontRecord);
        this.addFrontRecordAndWarehouseRelation();
        //添加出库单明细
        addWarehouseRecordDetails(frontRecord.getRwRecordPoolEList());

        try {
            orderTrackFacade.save(frontRecord.getOutRecordCode() , "订单已处理待推送至仓库" ,"");
        } catch (Exception e) {
            log.error(e.getMessage() ,e );
        }
    }

    /**
     * 根据前置单及Do池数据，构建出库单及明细信息(多个SO单)
     *  以出库单级别进行回滚
     * @param frontRecords
     */
    @Transactional(rollbackFor = Exception.class)
    public void addRecordAndMerge(List<OnlineRetailE> frontRecords) {
        //添加出库单
        addWarehouseRecord(frontRecords.get(0));
        List<RwRecordPoolE> poolRecordList = new ArrayList<>();
        List<String> orderList = new ArrayList<>();
        //添加出库单明细
        for (OnlineRetailE onlineRetailE : frontRecords) {
            poolRecordList.addAll(onlineRetailE.getRwRecordPoolEList());
            //前置单与出库单绑定关系
            this.setAbstractFrontRecord(onlineRetailE);
            this.addFrontRecordAndWarehouseRelation();
            orderList.add(onlineRetailE.getOutRecordCode());
        }
        addWarehouseRecordDetails(poolRecordList);
        try {
            orderTrackFacade.save(orderList , "订单已处理待推送至仓库" ,"");
        } catch (Exception e) {
            log.error(e.getMessage() ,e );
        }

    }

    /**
     * 添加出库单
     * @param onlineRetailE
     */
    private void addWarehouseRecord(OnlineRetailE onlineRetailE) {
        RwRecordPoolE poolMaster =  onlineRetailE.getRwRecordPoolEList().get(0);
        this.setSyncWmsStatus(WmsSyncStatusVO.UNSYNCHRONIZED.getStatus());
        if (onlineRetailE.getRecordType().equals(FrontRecordTypeVO.WDT_ONLINE_SALE_RECORD.getType())) {
            createRecodeCode(WarehouseRecordTypeVO.WDT_ONLINE_RETAILERS_OUT_RECORD.getCode());
            this.setBusinessType(WarehouseRecordTypeVO.WDT_ONLINE_RETAILERS_OUT_RECORD.getBusinessType());
            this.setRecordType(WarehouseRecordTypeVO.WDT_ONLINE_RETAILERS_OUT_RECORD.getType());
        }else if (onlineRetailE.getRecordType().equals(FrontRecordTypeVO.CROSS_SALE_RECORD.getType())) {
            createRecodeCode(WarehouseRecordTypeVO.CROSS_RETAILERS_OUT_RECORD.getCode());
            this.setBusinessType(WarehouseRecordTypeVO.CROSS_RETAILERS_OUT_RECORD.getBusinessType());
            this.setRecordType(WarehouseRecordTypeVO.CROSS_RETAILERS_OUT_RECORD.getType());
        }else if (onlineRetailE.getRecordType().equals(FrontRecordTypeVO.WINE_SALE_RECORD.getType())) {
            //沪威酒订单
            createRecodeCode(WarehouseRecordTypeVO.WINE_SALE_WAREHOUSE_RECORD.getCode());
            this.setBusinessType(WarehouseRecordTypeVO.WINE_SALE_WAREHOUSE_RECORD.getBusinessType());
            this.setRecordType(WarehouseRecordTypeVO.WINE_SALE_WAREHOUSE_RECORD.getType());
        }else if (onlineRetailE.getRecordType().equals(FrontRecordTypeVO.WINE_BUCKET_SALE_RECORD.getType())) {
            //沪威酒-桶订单
            createRecodeCode(WarehouseRecordTypeVO.WINE_BUCKET_SALE_WAREHOUSE_RECORD.getCode());
            this.setBusinessType(WarehouseRecordTypeVO.WINE_BUCKET_SALE_WAREHOUSE_RECORD.getBusinessType());
            this.setRecordType(WarehouseRecordTypeVO.WINE_BUCKET_SALE_WAREHOUSE_RECORD.getType());
            this.setSyncWmsStatus(WmsSyncStatusVO.SYNCHRONIZED.getStatus());
        }  else {
            createRecodeCode(WarehouseRecordTypeVO.ONLINE_RETAILERS_OUT_RECORD.getCode());
            this.setBusinessType(WarehouseRecordTypeVO.ONLINE_RETAILERS_OUT_RECORD.getBusinessType());
            this.setRecordType(WarehouseRecordTypeVO.ONLINE_RETAILERS_OUT_RECORD.getType());
        }
        this.setRecordStatus(WarehouseRecordStatusVO.INIT.getStatus());
        this.setUserCode(onlineRetailE.getUserCode());
        this.setMerchantId(onlineRetailE.getMerchantId());
        this.setRealWarehouseId(poolMaster.getRealWarehouseId());
        this.setVirtualWarehouseId(poolMaster.getVirtualWarehouseId());
        this.setChannelCode(onlineRetailE.getChannelCode());
        this.setOutCreateTime(onlineRetailE.getOutCreateTime());
        RealWarehouseWmsConfigDTO realWarehouseWmsConfigDTO = realWarehouseWmsConfigRepository.findRealWarehouseWmsConfigById(poolMaster.getRealWarehouseId());
        if (null != realWarehouseWmsConfigDTO && null != realWarehouseWmsConfigDTO.getWmsCode() && WarehouseWmsConfigEnum.XT_SJ.getType().equals(realWarehouseWmsConfigDTO.getWmsCode())) {
            //协同商家仓直接下发WMS已完成
            this.setSyncWmsStatus(WmsSyncStatusVO.SYNCHRONIZED.getStatus());
        }
        this.setPayTime(onlineRetailE.getPayTime());
        //根据前置单判断，订单是否为自营外卖
        this.setSelfTakeout(OnlineStockTransTypeVO.TRANS_TYPE_7.equals(OnlineStockTransTypeVO.getByTransType(onlineRetailE.getTransType())) ? 1 : 0);
        this.setSyncFulfillmentStatus(1);
        long id = onlineRetailWarehouseRepository.saveWarehouseRecord(this);
        this.setId(id);
    }

    /**
     * 合单，并添加出库单明细
     * @param poolRecordList
     */
    private void addWarehouseRecordDetails(List<RwRecordPoolE> poolRecordList) {
        Map<Long, WarehouseRecordDetail> skuQtyMap = new HashMap<>();
        for (RwRecordPoolE poolRecord : poolRecordList) {
            // 回写Do池状态
            poolRecord.setWarehouseRecordCode(this.getRecordCode());
            int executeResult = rwRecordPoolRepository.updateToMerged(poolRecord.getId(), this.getId(), this.getRecordCode(),poolRecord.getVersionNo());
            AlikAssert.isTrue(executeResult > 0, ResCode.STOCK_ERROR_5019, ResCode.STOCK_ERROR_5019_DESC);
            List<RwRecordPoolDetailE> poolDetailEList = poolRecord.getRwRecordPoolDetails();
            for (RwRecordPoolDetailE poolDetailE : poolDetailEList) {
                WarehouseRecordDetail recordDetail = skuQtyMap.get(poolDetailE.getSkuId());
                if (null == recordDetail) {
                    recordDetail = new WarehouseRecordDetail();
                    // 出库单实际数量初始化默认为0
                    recordDetail.setActualQty(BigDecimal.ZERO);
                    recordDetail.setPlanQty(poolDetailE.getBasicSkuQty());
                    recordDetail.setSkuId(poolDetailE.getSkuId());
                    recordDetail.setSkuCode(poolDetailE.getSkuCode());
                    recordDetail.setUnit(poolDetailE.getBasicUnit());
                    recordDetail.setUnitCode(poolDetailE.getBasicUnitCode());
                    recordDetail.setRecordCode(this.getRecordCode());
                    recordDetail.setChannelCode(this.getChannelCode());
                    skuQtyMap.put(poolDetailE.getSkuId(), recordDetail);
                } else {
//                    recordDetail.setActualQty(recordDetail.getActualQty() + poolDetailE.getBasicSkuQty());
                    recordDetail.setPlanQty(recordDetail.getPlanQty().add(poolDetailE.getBasicSkuQty()));
                }
                recordDetail.setUserCode(this.getUserCode());
                recordDetail.setWarehouseRecordId(this.getId());
            }
        }
        this.warehouseRecordDetails = new ArrayList<>(skuQtyMap.values());
        //设置skuCode和skuID
        this.initWarehouseRecodeDetail(this.getMerchantId());
        onlineRetailWarehouseRepository.saveWarehouseRecordDetails(this.warehouseRecordDetails);
    }

    /**
     * 更新出库单状态为已出库
     */
    public int updateToOutAllocation(WarehouseRecordE warehouseRecordE) {
        return onlineRetailWarehouseRepository.updateToOutAllocation(warehouseRecordE.getId());
    }

    /**
     * 取消电商出库单
     * @param recordId
     * @param fromPage true 是从网页通出库查询或电商出库查询页面点击的重新推送或改仓进来的，false表示为旺店通取消so失败回滚过来的
     * @return
     */
    public void cancleWarehouseRecord(Long  recordId , boolean fromPage){
        //修改出库单为已取消
        int i ;
        if (fromPage) {
            i = onlineRetailWarehouseRepository.updateToCanceledFromBack(recordId);
        } else {
            i = onlineRetailWarehouseRepository.updateToCanceledById(recordId);
        }
        AlikAssert.isTrue(i > 0, ResCode.STOCK_ERROR_1028, ResCode.STOCK_ERROR_1028_DESC);
        //删除关联关系
        warehouseRecordRepository.deleteRelationByWrId(recordId);
    }

    /**
     * 用户编码
     */
    private String userCode;


    /**
     * 根据原始单据创建新的电商出库单
     * @param
     * @return
     */
    public void createNewByOld(List<FrontWarehouseRecordRelationDO> relationList , WarehouseRecordE recordE, Long realWarehouseId
            , Long virtualWarehouseId, String logisticsCode, boolean needForceSyncWms){
        //生成订单
        if (WarehouseRecordTypeVO.ONLINE_RETAILERS_OUT_RECORD.getType().equals(recordE.getRecordType())) {
            createRecodeCode(WarehouseRecordTypeVO.ONLINE_RETAILERS_OUT_RECORD.getCode());
        } else {
            createRecodeCode(WarehouseRecordTypeVO.WDT_ONLINE_RETAILERS_OUT_RECORD.getCode());
        }
        recordE.setRecordCode(this.getRecordCode());
        recordE.setVirtualWarehouseId(virtualWarehouseId);
        recordE.setRealWarehouseId(realWarehouseId);
        // 是否需要强制同步wms
        if(needForceSyncWms) {
        	recordE.setSyncWmsStatus(WmsSyncStatusVO.WAITFORCESYNCH.getStatus());
        } else {
        	recordE.setSyncWmsStatus(WmsSyncStatusVO.UNSYNCHRONIZED.getStatus());
        }
        RealWarehouseWmsConfigDTO realWarehouseWmsConfigDTO = realWarehouseWmsConfigRepository.findRealWarehouseWmsConfigById(recordE.getRealWarehouseId());
        if (null != realWarehouseWmsConfigDTO && null != realWarehouseWmsConfigDTO.getWmsCode() && WarehouseWmsConfigEnum.XT_SJ.getType().equals(realWarehouseWmsConfigDTO.getWmsCode())) {
            //协同商家仓直接下发WMS已完成
            this.setSyncWmsStatus(WmsSyncStatusVO.SYNCHRONIZED.getStatus());
        }
        long id = onlineRetailWarehouseRepository.saveWarehouseRecord(recordE);
        this.setId(id);
        //创建明细
        for (WarehouseRecordDetail detail : recordE.getWarehouseRecordDetails()) {
            detail.setRecordCode(recordE.getRecordCode());
            detail.setWarehouseRecordId(id);
        }
        onlineRetailWarehouseRepository.saveWarehouseRecordDetails(recordE.getWarehouseRecordDetails());
        //创建关联关系表
        if(CollectionUtils.isNotEmpty(relationList)){
            for (FrontWarehouseRecordRelationDO relationDO : relationList) {
                relationDO.setWarehouseRecordId(id);
                relationDO.setRecordCode(recordE.getRecordCode());
                warehouseRecordRepository.insertRelation(relationDO);
            }
        }
        //修改池子的实仓 虚仓 以及关联的后置单号
        RwRecordPoolE poolE = entityFactory.createEntity(RwRecordPoolE.class);
        poolE.setModifier(recordE.getCreator());
        poolE.setRealWarehouseId(realWarehouseId);
        poolE.setVirtualWarehouseId(virtualWarehouseId);
        poolE.setWarehouseRecordId(id);
        poolE.setWarehouseRecordCode(recordE.getRecordCode());
        poolE.setLogisticsCode(logisticsCode);
        rwRecordPoolRepository.updateNewRecordInfo(poolE, recordE.getId());
        //根据前置单ID批量修改明细的实仓和虚仓
        List<RwRecordPoolE> poolList = rwRecordPoolRepository.queryKeysByWarehouseId(id);
        for(RwRecordPoolE pool : poolList) {
            rwRecordPoolRepository.updateDetailRwInfo(pool.getId(), realWarehouseId, virtualWarehouseId);
        }
    }

    /**
     * 手机号码
     */
    private String mobile;

    /**
     * 是否是自营外卖
     */
    private Integer selfTakeout;

    /**
     * 同步合单状态给捋单系统
     */
    private Integer syncFulfillmentStatus;

    /**
     * 前置单
     */
    private OnlineRetailE onlineRetailE;

    private List<RwRecordPoolE> rwRecordPoolEList;

    /**
     * 支付时间
     */
    private Date payTime;
}
