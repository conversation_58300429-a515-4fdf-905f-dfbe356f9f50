package com.rome.stock.innerservice.domain.entity.warehouserecord;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

import javax.annotation.Resource;

import com.rome.stock.core.infrastructure.dataobject.core.CoreRealStockOpDO;
import com.rome.stock.core.infrastructure.dataobject.core.CoreRealStockOpDetailDO;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Component;

import com.rome.arch.core.domain.EntityFactory;
import com.rome.stock.common.constants.warehouse.WarehouseRecordConstant;
import com.rome.stock.innerservice.api.dto.warehouserecord.OutWarehouseRecordDTO;
import com.rome.stock.innerservice.api.dto.warehouserecord.RecordDetailDTO;
import com.rome.stock.innerservice.constant.WarehouseRecordBusinessTypeVO;
import com.rome.stock.innerservice.constant.WarehouseRecordStatusVO;
import com.rome.stock.innerservice.constant.WmsSyncStatusVO;
import com.rome.stock.innerservice.domain.entity.RealWarehouseE;
import com.rome.stock.innerservice.domain.repository.warehouserecord.WarehouseConsumeRecordRepository;
import com.rome.stock.core.infrastructure.dataobject.core.CoreRealStockOpDO;
import com.rome.stock.core.infrastructure.dataobject.core.CoreRealStockOpDetailDO;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.extern.slf4j.Slf4j;

/**
 * @Description 仓库损耗单
 * <AUTHOR>
 * @Date 2019/5/12 17:07
 * @Version 1.0
 */
@Data
@Slf4j
@Component
@Scope("prototype")
@EqualsAndHashCode(callSuper = false)
public class WarehouseConsumeRecordE extends AbstractWarehouseRecord  {

    @Resource
    private EntityFactory entityFactory;

    @Resource
    WarehouseConsumeRecordRepository warehouseConsumeRecordRepository;

    /**
     * 添加出库单
     */
    public void addRecord(){
        long id= warehouseConsumeRecordRepository.saveWarehouseConsumeRecord(this);
        this.setId(id);
        this.warehouseRecordDetails.forEach(record -> record.setWarehouseRecordDetail(this));
        warehouseConsumeRecordRepository.saveWarehouseConsumeRecordDetails(this.warehouseRecordDetails);
//        this.addFrontRecordAndWarehouseRelation();
    }


    /**
     * 创建仓库出库单
     * @param outWarehouseRecordDTO
     * @param realWarehouseE
     */
    public void createRWOutRecord(OutWarehouseRecordDTO outWarehouseRecordDTO, RealWarehouseE realWarehouseE){
        this.setRealWarehouseId(realWarehouseE.getId());
        this.setBusinessType(WarehouseRecordBusinessTypeVO.OUT_WAREHOUSE_RECORD.getType());
        this.setRecordCode(outWarehouseRecordDTO.getRecordCode());
        this.setRecordType(outWarehouseRecordDTO.getRecordType());
        //设置出库单初始化状态
        this.setRecordStatus(WarehouseRecordStatusVO.OUT_ALLOCATION.getStatus());
        //设置出库单的创建时间
        this.setOutCreateTime(new Date());
        this.setSyncWmsStatus(WmsSyncStatusVO.NO_REQUIRED.getStatus());
        this.setSyncTransferStatus(WarehouseRecordConstant.INIT_TRANSFER);
        this.setSyncTradeStatus(WarehouseRecordConstant.NEED_SYNC_TRADE);
        this.setSapOrderCode(outWarehouseRecordDTO.getSapOrderCode());
        List<RecordDetailDTO> frontRecordDetails = outWarehouseRecordDTO.getDetailList();
        this.setWarehouseRecordDetails(new ArrayList<>(frontRecordDetails.size()));
        for(RecordDetailDTO detailE : frontRecordDetails){
            WarehouseRecordDetail warehouseRecordDetail = entityFactory.createEntity(WarehouseRecordDetail.class);
            warehouseRecordDetail.setSkuId(detailE.getSkuId());
            warehouseRecordDetail.setSkuCode(detailE.getSkuCode());
            warehouseRecordDetail.setPlanQty(detailE.getBasicSkuQty());
            warehouseRecordDetail.setUnit(detailE.getBasicUnit());
            warehouseRecordDetail.setUnitCode(detailE.getBasicUnitCode());
            warehouseRecordDetail.setActualQty(BigDecimal.ZERO);
            warehouseRecordDetail.setLineNo(detailE.getLineNo());
            warehouseRecordDetail.setDeliveryLineNo(detailE.getDeliveryLineNo());
            warehouseRecordDetail.setSapPoNo(outWarehouseRecordDTO.getSapOrderCode());
            this.warehouseRecordDetails.add(warehouseRecordDetail);
        }
        //设置skuCode和skuID
        this.initWarehouseRecodeDetail();
    }
   
    /**
     * @Description: 创建领用后置单 <br>
     * @param realWarehouseE
     * realWarehouseE
     * @return 
     */
    public void createReceiveRWOutRecord(OutWarehouseRecordDTO outWarehouseRecordDTO,RealWarehouseE realWarehouseE){
        this.setRealWarehouseId(realWarehouseE.getId());
        this.setBusinessType(WarehouseRecordBusinessTypeVO.OUT_WAREHOUSE_RECORD.getType());
        this.setRecordCode(outWarehouseRecordDTO.getRecordCode());
        this.setRecordType(outWarehouseRecordDTO.getRecordType());
        this.setRecordStatus(WarehouseRecordStatusVO.OUT_ALLOCATION.getStatus());
        this.setOutCreateTime(new Date());
        this.setSyncTransferStatus(WarehouseRecordConstant.TRANSFER_SUCCES);
        this.setSyncWmsStatus(WmsSyncStatusVO.NO_REQUIRED.getStatus());
        this.setSyncTradeStatus(WarehouseRecordConstant.NEED_SYNC_TRADE);
        this.setSapOrderCode(outWarehouseRecordDTO.getSapOrderCode());
        List<RecordDetailDTO> frontRecordDetails = outWarehouseRecordDTO.getDetailList();
        this.setWarehouseRecordDetails(new ArrayList<>(frontRecordDetails.size()));
        if(null != frontRecordDetails){
            for(RecordDetailDTO detailE : frontRecordDetails){
                WarehouseRecordDetail warehouseRecordDetail = entityFactory.createEntity(WarehouseRecordDetail.class);
                warehouseRecordDetail.setSkuId(detailE.getSkuId());
                warehouseRecordDetail.setSkuCode(detailE.getSkuCode());
                warehouseRecordDetail.setPlanQty(detailE.getBasicSkuQty());
                warehouseRecordDetail.setUnit(detailE.getBasicUnit());
                warehouseRecordDetail.setUnitCode(detailE.getBasicUnitCode());
                warehouseRecordDetail.setActualQty(detailE.getBasicSkuQty());
                warehouseRecordDetail.setLineNo(detailE.getLineNo());
                warehouseRecordDetail.setDeliveryLineNo(detailE.getDeliveryLineNo());
                warehouseRecordDetail.setSapPoNo(outWarehouseRecordDTO.getSapOrderCode());
                this.warehouseRecordDetails.add(warehouseRecordDetail);
            }
            //设置skuCode和skuID
            this.initWarehouseRecodeDetail();
        }
    }

    //更新出库单
    public void updateRecord(WarehouseConsumeRecordE outRecord) {
        warehouseConsumeRecordRepository.updateWarehouseConsumeRecord(outRecord);
    }


    /**
     * 解锁库存,由下往上
     */
    public CoreRealStockOpDO packUnlockStockObjForDown(){
        CoreRealStockOpDO stockDO = new CoreRealStockOpDO();
        List<CoreRealStockOpDetailDO> detailDos = new ArrayList<>();
        stockDO.setRecordCode(this.getRecordCode());
        stockDO.setTransType(this.getRecordType());
        stockDO.setDetailDos(detailDos);
        for (WarehouseRecordDetail detail : this.getWarehouseRecordDetails()) {
            //数量为0的不处理库存的增加和减少
            if(BigDecimal.ZERO.compareTo(detail.getPlanQty()) == 0){
                continue;
}
            CoreRealStockOpDetailDO detailDO = new CoreRealStockOpDetailDO();
            detailDO.setUnlockQty(detail.getPlanQty());
            detailDO.setSkuId(detail.getSkuId());
            detailDO.setSkuCode(detail.getSkuCode());
            detailDO.setRealWarehouseId(this.getRealWarehouseId());
            detailDO.setChannelCode(detail.getChannelCode());
            detailDos.add(detailDO);
        }
        return stockDO;
    }

}
