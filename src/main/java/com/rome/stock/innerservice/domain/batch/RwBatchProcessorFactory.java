package com.rome.stock.innerservice.domain.batch;

import com.rome.arch.core.exception.RomeException;
import com.rome.stock.common.enums.warehouse.WarehouseRecordTypeVO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeansException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationContextAware;
import org.springframework.core.annotation.AnnotationUtils;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.Map;

/**
 * @Description RwBatchProcessorFactory
 * <AUTHOR>
 * @Date 2023/4/7 14:39
 * @Version 1.0
 **/
@Slf4j
@Component
@SuppressWarnings("all")
public class RwBatchProcessorFactory implements ApplicationContextAware {

	private static final Map<Integer, RwBatchStockRelationProcessor> defProcessorMap = new HashMap<>();

	public RwBatchStockRelationProcessor findStockProcessor(Integer recordType) {
		return defProcessorMap.get(recordType);
	}


	@Override
	public void setApplicationContext(ApplicationContext applicationContext) throws BeansException {
		applicationContext.getBeansWithAnnotation(RwBatchProcessRecordType.class).forEach((name, stockProcessor) -> {
			if (stockProcessor instanceof RwBatchStockRelationProcessor) {
				RwBatchProcessRecordType processorBean = AnnotationUtils.findAnnotation(stockProcessor.getClass(), RwBatchProcessRecordType.class);
				WarehouseRecordTypeVO[] warehouseRecordTypeVOS = processorBean.recordVOTypes();
				for (WarehouseRecordTypeVO warehouseRecordTypeVO : warehouseRecordTypeVOS) {
					if (defProcessorMap.containsKey(warehouseRecordTypeVO.getType())) {
						throw new RomeException("1001", "初始化处理器异常,发现多个handler接受处理类型=" + warehouseRecordTypeVO.getType() + "的单据");
					}
					defProcessorMap.put(warehouseRecordTypeVO.getType(), (RwBatchStockRelationProcessor) stockProcessor);
				}
			} else {
				throw new RomeException("1001", name + "没有实现接口RwBatchStockProcessorService");
			}
		});
	}

}
