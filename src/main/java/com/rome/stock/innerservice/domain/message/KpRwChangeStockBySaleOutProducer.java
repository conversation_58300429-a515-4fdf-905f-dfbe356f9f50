/**
 * Filename KpRwChangeStockBySaleOutProducer.java
 * <AUTHOR>
 * @version
 */
package com.rome.stock.innerservice.domain.message;

import javax.annotation.Resource;
import javax.validation.constraints.NotNull;
import org.apache.rocketmq.client.producer.SendResult;
import org.apache.rocketmq.client.producer.SendStatus;
import org.apache.rocketmq.spring.core.RocketMQTemplate;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.messaging.Message;
import org.springframework.messaging.support.MessageBuilder;
import org.springframework.stereotype.Service;

import com.rome.stock.common.enums.CustomRocketMQEnum;
import com.rome.scm.common.rocketmq.CustomRocketMQProducerClient;

import lombok.extern.slf4j.Slf4j;

/**
 * 鲲鹏实仓库存变化推到外卖
 * <AUTHOR>
 */
@Slf4j
@Service
public class KpRwChangeStockBySaleOutProducer {

//    @Resource
//    private RocketMQTemplate rocketMQTemplate;
//
//    @NotNull
//    @Value("${rocketmq.saleOut.topic}")
//    private String topic;
//
//    @NotNull
//    @Value("${rocketmq.saleOut.tag}")
//    private String tag;
//
//    /**
//     * mq消息配置
//     */
//    private MQConfigEnum mqConfigEnum = MQConfigEnum.SW_CHANGE_STOCK_QUEUE;

    /**
     * 发送消息
     * @return
     */
    public boolean sendMQ(String msg) {
        try {
        	boolean result = CustomRocketMQProducerClient.send(msg, CustomRocketMQEnum.MQ_KP_CHANGE_STOCK_SALE_OUT_PUSH.getCode(), null, null);
        	if(result){
                return true;
            }else {
            	log.error("鲲鹏实仓库存变化推到外卖,发送消息,失败,结果:{}",result);
            }
//            log.info("鲲鹏实仓库存变化推到外卖推送mq,发送消息,参数:{}", msg);
//            String destination = topic + ":" + tag;
//            Message<String> message = MessageBuilder.withPayload(msg)
//                    .build();
//            SendResult sendResult = rocketMQTemplate.syncSend(destination, message, mqConfigEnum.getTimeout());
//            destination = null;
//            // 发送结果
//            if(sendResult.getSendStatus() == SendStatus.SEND_OK){
//                return true;
//            }else {
//                log.error("鲲鹏实仓库存变化推到外卖,发送消息,失败,结果:{}",sendResult);
//            }
        } catch (Exception e) {
            log.error("鲲鹏实仓库存变化推到外卖,发送消息,出错:msg={}", msg, e);
        }
        return false;
    }

}
