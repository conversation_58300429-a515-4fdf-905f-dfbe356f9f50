package com.rome.stock.innerservice.domain.entity;

import com.rome.stock.innerservice.domain.repository.RealWarehouseWmsConfigRepository;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * 实仓wms配置
 */

@Data
@Slf4j
@Component
@Scope("prototype")
@EqualsAndHashCode(callSuper = false)
public class RealWarehouseWmsConfigE extends BaseE{

    @Resource
    private RealWarehouseWmsConfigRepository realWarehouseWmsConfigRepository;

    public Long addRealWarehouseWmsConfig(RealWarehouseWmsConfigE realWarehouseWmsConfigE) {
        Long row = realWarehouseWmsConfigRepository.addRealWarehouseWmsConfig(realWarehouseWmsConfigE);
        return row;
    }

    /**
     * 唯一id
     */
    private Long id;

    /**
     * 实体仓库id
     */
    private Long realWarehouseId;

    /**
     * wms编码
     */
    private Integer wmsCode;

    /**
     * wms名称
     */
    private String wmsName;

    /**
     * 一件代发类型：0  普通， 1 一件代发-TOC、 2 一件代发-商家、3 一件代发-跨境
     */
    private Integer purchaseType;

    /**
     * 调用接口  0 默认 无、 1  采购单自动创建、 2 入库单创建
     */
    private Integer reqType;



    private Integer purchaseOrderType;


    /**
     * 逆向调用接口:0.默认无、1.采购单自动创建、2.入库单创建、3.退货创建
     */
    private Integer returnReqType;

    /**
     * 退供类型：1：一件代发-跨境，2：一件代发-商家
     */
    private Integer refundType;

    private String supplierCode;

    private String supplierName;

    /**
     * 运输供应商编码
     */
    private String logisticsCode;

    /**
     * 运输供应商名称
     */
    private String logisticsName;


    /**
     * 批次开启状态
     */
    private Integer batchStatus;
}
