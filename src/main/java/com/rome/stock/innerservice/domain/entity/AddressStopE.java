package com.rome.stock.innerservice.domain.entity;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Component;

@Data
@Slf4j
@Component
@Scope("prototype")
@EqualsAndHashCode(callSuper = false)
public class AddressStopE extends  BaseE {

    /**
     * 主键
     */
    private Long id;

    /**
     * 实体仓库id
     */
    private Long realWarehouseId;

    /**
     * 停发类型：1 全省，2 全市，3 全区，4 全镇/街道
     */
    private Integer stopType;

    /**
     * 国家
     */
    private String country;

    /**
     * 省份
     */
    private String province;

    /**
     * 城市
     */
    private String city;

    /**
     * 区/县城市
     */
    private String county;

    /**
     * 街道/镇
     */
    private String area;

    /**
     * 国家code
     */
    private String countryCode;

    /**
     * 省份code
     */
    private String provinceCode;

    /**
     * 城市code
     */
    private String cityCode;

    /**
     * 区/县城市code
     */
    private String countyCode;

    /**
     * 街道/镇code
     */
    private String areaCode;

    /**
     * 备注
     */
    private String remark;
}
