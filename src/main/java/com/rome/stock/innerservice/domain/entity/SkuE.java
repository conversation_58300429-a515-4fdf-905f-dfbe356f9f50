package com.rome.stock.innerservice.domain.entity;

import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Component;

import com.rome.arch.core.domain.BaseEntity;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.extern.slf4j.Slf4j;

/**
 * 类SkuE的实现描述：sku信息
 *
 * <AUTHOR> 2019/4/18 10:24
 */
@Data
@Slf4j
@Component
@Scope("prototype")
@EqualsAndHashCode(callSuper = false)
public class SkuE extends BaseEntity {

    /**
     *  skuId
     */
    private Long skuId;

    /**
     *  渠道编码
     */
    private Long channelId;

    /**
     * 商家Id
     */
    private Long merchantId;
}
