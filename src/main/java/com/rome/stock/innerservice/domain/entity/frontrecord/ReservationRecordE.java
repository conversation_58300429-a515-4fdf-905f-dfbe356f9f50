package com.rome.stock.innerservice.domain.entity.frontrecord;

import com.rome.stock.innerservice.common.AlikAssert;
import com.rome.stock.innerservice.common.ResCode;
import com.rome.stock.innerservice.common.SkuInfoTools;
import com.rome.stock.innerservice.common.SkuQtyUnitTools;
import com.rome.stock.innerservice.constant.FrontRecordStatusVO;
import com.rome.stock.innerservice.constant.FrontRecordTypeVO;
import com.rome.stock.innerservice.constant.ReservationAssignVo;
import com.rome.stock.innerservice.domain.convertor.AddressConvertor;
import com.rome.stock.innerservice.domain.convertor.frontrecord.FrReservationConvertor;
import com.rome.stock.innerservice.domain.convertor.frontrecord.FrReservationDetailConvertor;
import com.rome.stock.innerservice.domain.entity.AddressE;
import com.rome.stock.innerservice.domain.repository.frontrecord.FrReservationRepository;
import com.rome.stock.innerservice.infrastructure.dataobject.frontrecord.FrReservationDO;
import com.rome.stock.innerservice.infrastructure.mapper.WarehouseRecordDetailMapper;
import com.rome.stock.innerservice.infrastructure.mapper.WarehouseRecordMapper;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Component;

import java.util.Date;
import java.util.List;
import java.util.Objects;

/**
 * 前置预约单领域对象
 */
@Component
@Scope("prototype")
@Data
@Slf4j
@EqualsAndHashCode(callSuper = false)
public class ReservationRecordE extends AbstractFrontRecord {

    @Autowired
    private FrReservationRepository reservationRepository;

    @Autowired
    private SkuQtyUnitTools skuQtyUnitTools;

    @Autowired
    private SkuInfoTools skuInfoTools;

    @Autowired
    private WarehouseRecordMapper warehouseRecordMapper;

    @Autowired
    private WarehouseRecordDetailMapper warehouseRecordDetailMapper;

    @Autowired
    private FrReservationConvertor reservationConvertor;

    @Autowired
    private FrReservationDetailConvertor frReservationDetailConvertor;

    @Autowired
    private AddressConvertor addressConvertor;

    /**
     * 创建预约单
     */
    public void addReservation(){
        this.setRecordStatus(FrontRecordStatusVO.INIT.getStatus());
        this.setIsAssigned(ReservationAssignVo.ASSIGN.getType());
        this.setRecordType(FrontRecordTypeVO.GROUP_PURCHASE_RESERVATION_RECORD.getType());
        //单据编号生成
        initFrontRecord(FrontRecordTypeVO.GROUP_PURCHASE_RESERVATION_RECORD.getCode(),this.reservationDetails);
        //保存预约单
        long id = reservationRepository.addReservation(this);
        AlikAssert.isTrue(id > 0, ResCode.STOCK_ERROR_9050,ResCode.STOCK_ERROR_9050_DESC);
        this.setId(id);
        //保存明细
        ReservationRecordE reservationRecordE = reservationRepository.addReservationDetailBatch(this);
        this.setReservationDetails(reservationRecordE.getReservationDetails());
        //保存地址信息表
        saveAddress();
    }

    /**
     * 构建sku相关信息
     */
    public void buildSkuBaseInfo(){
        skuInfoTools.convertSkuCode(this.reservationDetails);
        //单位转换
        skuQtyUnitTools.convertRealToBasic(this.reservationDetails);
        //批次单位转换
        for(AbstractFrontRecordDetail detail : this.reservationDetails){
            if(detail.getBatchStocks() != null && detail.getBatchStocks().size() > 0){
                detail.getBatchStocks().forEach( batchStock -> batchStock.changeBatchStockUnit(detail));
            }
        }
    }

    /**
     * 保存地址信息
     */
    private void saveAddress() {
        AddressE addressE=addressConvertor.dtoToEntity(this);
        addressE.setUserType((byte) 0);
        addressE.setAddressType((byte) 0);
        addressE.setRecordCode(this.getRecordCode());
        addressE.addAddress();
    }

    /**
     * 更新预约单信息
     */
    public void updateReservation(){
        reservationRepository.updateReservation(this);
        reservationRepository.updateReservationDetailBatch(this);
    }

    /**
     * 更新预约单状态
     */
    public void updateReservationStatus(){
        reservationRepository.updateReservation(this);
    }

    /***
     * 查询当前单据是否存在
     * @return
     */
    public ReservationRecordE queryReservationByOutCode(){
       return reservationRepository.queryFrReservationByOutRecordCode(this.getOutRecordCode());
    }

    /***
     * 根据外部单号查询预约单信息及明细
     * @return
     */
    public ReservationRecordE queryWithDetailByOutRecordCode(){
        return reservationRepository.queryWithDetailByOutRecordCode(this.getOutRecordCode());
    }


    /**
     * 根据外部单号查询预约单信息
     * @return
     */
    public ReservationRecordE queryReservationByCode(){
        ReservationRecordE reservationRecordE = reservationRepository.queryFrReservationByOutRecordCode(this.getOutRecordCode());
        if(Objects.nonNull(reservationRecordE)){
            List<ReservationRecordDetailE> reservationRecordDetailES = reservationRepository.queryReservationDetailsByCode(reservationRecordE.getRecordCode());
            reservationRecordE.setReservationDetails(reservationRecordDetailES);
            return reservationRecordE;
        }
        return null;
    }




    /**
     * 根据ID查询预约单及明细
     * @return
     */
    public ReservationRecordE queryReservationById(){
       return reservationRepository.queryWithDetailByOutRecordId(this.getId());
    }

    /**
     * 转换调拨单,修改状态并记录调拨单号
     */
    public void tranferWhallot(){
        FrReservationDO frReservationDO = reservationConvertor.entityToDo(this);
        int i = reservationRepository.updateWhallotInfo(frReservationDO);
    }



   /*领域相关属性*/

    /**
     * 渠道编码
     */
    private String channelCode;

    /**
     * 单据编号
     */
    private String recordCode;

    /**
     * 单据类型
     */
    private Integer recordType;

    /**
     * 单据状态   19--部分锁定 20--全部锁定  23--已转调拨单 24--待发货(已创建DO) 6--已发货
     */
    private Integer recordStatus;

    /**
     * 是否可分配 1--可分配 2--不可分配
     */
    private Integer isAssigned;

    /**
     * 实仓ID
     */
    private Long realWarehouseId;

    /**
     * 实仓编码
     */
    private String realWarehouseCode;

    /**
     * 实仓工厂编码
     */
    private String realFactoryCode;

    /**
     * 虚仓ID
     */
    private Long virtualWarehouseId;

    /**
     * 虚仓编码
     */
    private String virtualWarehouseCode;

    /**
     * 虚仓工厂编码
     */
    private String virtualFactoryCode;

    /**
     * 外部单号
     */
    private String outRecordCode;

    /**
     * 业务类型：1-DIY　２-普通订单 3--卡订单
     */
    private Integer businessType;

    /**
     * 手机号
     */
    private String mobile;

    /**
     * 外部订单下单时间
     */
    private Date outCreateTime;

    /**
     * 用户code
     */
    private String userCode;

    /**
     * 备注
     */
    private String remark;


    /**
     * 省
     */
    private String province;


    /**
     * 省编码
     */
    private String provinceCode;

    /**
     * 市
     */
    private String city;

    /**
     * 市编码
     */
    private String cityCode;

    /**
     * 区县
     */
    private String county;

    /**
     * 区县编码
     */
    private String countyCode;

    /**
     * 详细地址
     */
    private String address;

    /**
     *收货人姓名
     */
    private String name;

    /**
     * 预约明细集合
     */
    private List<ReservationRecordDetailE> reservationDetails;

    /**
     * 预计收货时间
     */
    private Date expectReceiveDateStart;

    /**
     * 调拨单号
     */
    private String whallotCode;
}    
