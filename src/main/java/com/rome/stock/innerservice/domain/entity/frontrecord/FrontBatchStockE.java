package com.rome.stock.innerservice.domain.entity.frontrecord;

import com.rome.stock.core.constant.StockCoreConsts;
import com.rome.stock.innerservice.domain.entity.SkuQtyUnitBaseE;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;

import static java.math.BigDecimal.ROUND_DOWN;

/**
 * 前置单批次
 * <AUTHOR>
@Component
@Scope("prototype")
@Data
@EqualsAndHashCode(callSuper = false)
public class FrontBatchStockE extends SkuQtyUnitBaseE {

    /**
     * 批次单位换算
     * @param detail
     */
    public void changeBatchStockUnit(AbstractFrontRecordDetail detail){
        BigDecimal basicSkuQty = this.getSkuQty().multiply(detail.getScale()).setScale(StockCoreConsts.DECIMAL_POINT_NUM, ROUND_DOWN);
        this.setBasicSkuQty(basicSkuQty);
        this.setBasicUnit(detail.getBasicUnit());
        this.setBasicUnitCode(detail.getBasicUnitCode());
        this.setSkuCode(detail.getSkuCode());
    }

    /**
     * 批次
     */
    private String batchCode;
}
