package com.rome.stock.innerservice.domain.entity;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Component;

@Data
@Slf4j
@Component
@Scope("prototype")
@EqualsAndHashCode(callSuper = false)
public class ChannelSalesE extends BaseE{
	/**
	 * 唯一主键
	 */
	private Long id;
	/**
	 * 渠道id
	 */
	private String channelCode;
	/**
	 * 渠道名字
	 */
	private String channelName;
	/**
	 * 虚拟仓库组id   所属虚拟仓库组
	 */
	private Long virtualWarehouseGroupId;

	/**
	 * 虚仓code
	 */
	private String virtualWarehouseGroupCode;

	/**
	 * 暂时不用，显示比率（百分比），大于0区间可选数字
	 */
	private Integer showRate;
	/**
	 * 商家id
	 */
	private Long merchantId;
	/**
	 * 接单类型，0-库存不足拒绝，1-不可拒绝
	 */
	private Integer receiptType;
}
