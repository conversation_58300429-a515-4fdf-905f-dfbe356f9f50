package com.rome.stock.innerservice.domain.entity.warehouserecord;

import com.rome.arch.core.domain.EntityFactory;
import com.rome.stock.common.constants.warehouse.WarehouseRecordConstant;
import com.rome.stock.common.enums.warehouse.WarehouseRecordTypeVO;
import com.rome.stock.innerservice.api.dto.warehouserecord.InWarehouseRecordDTO;
import com.rome.stock.innerservice.api.dto.warehouserecord.OutWarehouseRecordDTO;
import com.rome.stock.innerservice.api.dto.warehouserecord.RecordDetailDTO;
import com.rome.stock.innerservice.constant.WarehouseRecordBatchStatusVO;
import com.rome.stock.innerservice.constant.WarehouseRecordBusinessTypeVO;
import com.rome.stock.innerservice.constant.WarehouseRecordStatusVO;
import com.rome.stock.innerservice.constant.WmsSyncStatusVO;
import com.rome.stock.innerservice.domain.entity.RealWarehouseE;
import com.rome.stock.innerservice.domain.repository.RealWarehouseRepository;
import com.rome.stock.innerservice.domain.repository.warehouserecord.PackageProcessWarehouseRepository;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;

/**
 * 包装加工
 *
 */
@Data
@Slf4j
@Component
@Scope("prototype")
@EqualsAndHashCode(callSuper = false)
public class PackageProcessWarehouseRecordE extends AbstractWarehouseRecord{

    @Resource
    private PackageProcessWarehouseRepository packageProcessWarehouseRepository;
    @Resource
    private EntityFactory entityFactory;
    private RealWarehouseRepository realWarehouseRepository;
    /**
     * 保存入库单
     */
    public Long addInWarehouseRecord() {
        //保存入库单
        long id = packageProcessWarehouseRepository.saveInWarehouseRecord(this);
        this.setId(id);
        return id;
    }

    /**
     * 保存出库单
     */
    public void addOutWarehouseRecord() {
        //保存出库单
        long id = packageProcessWarehouseRepository.saveOutWarehouseRecord(this);
        this.setId(id);
        this.addWarehouseRecordDetail();
    }

    /**
     * 保存盘点单明细
     */
    public void addWarehouseRecordDetail(){
        this.warehouseRecordDetails.forEach(record -> record.setWarehouseRecordDetail(this));
        packageProcessWarehouseRepository.saveWarehouseRecordDetails(this.warehouseRecordDetails);
    }

    /**
     * 根据前置单生成出库单
     */
    public void createOutRecordByFrontRecord(OutWarehouseRecordDTO dto,RealWarehouseE outWarehouse){
        this.setRecordCode(dto.getRecordCode());
        this.setRealWarehouseId(outWarehouse.getId());
        this.setBusinessType(WarehouseRecordBusinessTypeVO.OUT_WAREHOUSE_RECORD.getType());
        this.setRecordType(dto.getRecordType());
        this.setSapOrderCode(dto.getSapOrderCode());
        this.setRecordStatus(WarehouseRecordStatusVO.OUT_ALLOCATION.getStatus());
        //默认无需同步wms
        this.setSyncWmsStatus(WmsSyncStatusVO.NO_REQUIRED.getStatus());
        //默认无需派车
        this.setSyncDispatchStatus(WarehouseRecordConstant.INIT_DISPATCH);
        //默认无需过账
        this.setSyncTransferStatus(WarehouseRecordConstant.INIT_TRANSFER);
        //包装需求完成单反拆出库--需要过账
        if(WarehouseRecordTypeVO.PACKAGE_FINISH_UNPACK_OUT_RECORD.getType().equals(dto.getRecordType())){
            this.setSyncTransferStatus(WarehouseRecordConstant.NEED_TRANSFER);
        }
        //默认无需同步交易中心，需要同步交易中心的 调用方自己处理
        this.setSyncTradeStatus(WarehouseRecordConstant.INIT_SYNC_TRADE);
        this.setBatchStatus(WarehouseRecordBatchStatusVO.INIT.getStatus());
        List<RecordDetailDTO> frontRecordDetails=dto.getDetailList();
        this.setWarehouseRecordDetails(new ArrayList<>(frontRecordDetails.size()));
        if(frontRecordDetails!=null){
            for(RecordDetailDTO detailE:frontRecordDetails){
                WarehouseRecordDetail warehouseRecordDetail=entityFactory.createEntity(WarehouseRecordDetail.class);
                warehouseRecordDetail.setSkuCode(detailE.getSkuCode());
                warehouseRecordDetail.setPlanQty(detailE.getBasicSkuQty());
                //无需同步实际数量与计划数量一致
                warehouseRecordDetail.setActualQty(detailE.getBasicSkuQty());
                warehouseRecordDetail.setUnit(detailE.getBasicUnit());
                warehouseRecordDetail.setUnitCode(detailE.getBasicUnitCode());
                warehouseRecordDetail.setRecordCode(dto.getRecordCode());
                warehouseRecordDetail.setRealWarehouseId(outWarehouse.getId());
                this.warehouseRecordDetails.add(warehouseRecordDetail);
            }
            //设置skuCode和skuID
            this.initWarehouseRecodeDetail();
        }
    }


    /**
     * 根据出库单生成大仓入库单
     */
    public void createInRecordByOutRecord(InWarehouseRecordDTO inWarehouseRecordDTO,RealWarehouseE inWarehouse){
        this.setRecordCode(inWarehouseRecordDTO.getRecordCode());
        this.setRecordType(inWarehouseRecordDTO.getRecordType());
        this.setRealWarehouseId(inWarehouse.getId());
        this.setBusinessType(WarehouseRecordBusinessTypeVO.IN_WAREHOUSE_RECORD.getType());
        this.setRecordStatus(WarehouseRecordStatusVO.IN_ALLOCATION.getStatus());
        //sap_order_code使用中台后置单号
        this.setSapOrderCode(inWarehouseRecordDTO.getSapOrderCode());
        this.setSyncTradeStatus(WarehouseRecordConstant.INIT_SYNC_TRADE);
        this.setSyncWmsStatus(WmsSyncStatusVO.NO_REQUIRED.getStatus());
        //默认无需过账
        this.setSyncTransferStatus(WarehouseRecordConstant.INIT_TRANSFER);
        //包装需求完成单组装入库--需要过账
        if(WarehouseRecordTypeVO.PACKAGE_FINISH_PACK_IN_RECORD.getType().equals(inWarehouseRecordDTO.getRecordType())){
            this.setSyncTransferStatus(WarehouseRecordConstant.NEED_TRANSFER);
        }
        //插入单据
        List<RecordDetailDTO> frontRecordDetails =inWarehouseRecordDTO.getDetailList();
        this.setWarehouseRecordDetails(new ArrayList<>(frontRecordDetails.size()));
        for(RecordDetailDTO detailDTO : frontRecordDetails){
            //数量为0的不写入明细
            if(BigDecimal.ZERO.compareTo(detailDTO.getBasicSkuQty()) == 0){
                continue;
            }
            WarehouseRecordDetail warehouseRecordDetail = entityFactory.createEntity(WarehouseRecordDetail.class);
            //根据前置单生成单据明细
            warehouseRecordDetail.setSkuCode(detailDTO.getSkuCode());
            //此处设置计划数量和实际数量一致，wms回调再更新实际数量，无需wms回调的业务，直接设置成一致即可
            warehouseRecordDetail.setUnit(detailDTO.getBasicUnit());
            warehouseRecordDetail.setUnitCode(detailDTO.getBasicUnitCode());
            warehouseRecordDetail.setPlanQty(detailDTO.getBasicSkuQty());
            warehouseRecordDetail.setActualQty(detailDTO.getBasicSkuQty());
            warehouseRecordDetail.setSapPoNo(inWarehouseRecordDTO.getSapOrderCode());
            warehouseRecordDetail.setLineNo(detailDTO.getLineNo());
            warehouseRecordDetail.setDeliveryLineNo(detailDTO.getDeliveryLineNo());
            warehouseRecordDetail.setRealWarehouseId(inWarehouse.getId());
            this.warehouseRecordDetails.add(warehouseRecordDetail);
        }
        //设置skuCode和skuID
        this.initWarehouseRecodeDetail();
        long id = this.addInWarehouseRecord();
        this.setId(id);
        this.warehouseRecordDetails.forEach(wrRecord -> wrRecord.setWarehouseRecordDetail(this));
        packageProcessWarehouseRepository.saveWarehouseRecordDetails(this.warehouseRecordDetails);
    }
}
