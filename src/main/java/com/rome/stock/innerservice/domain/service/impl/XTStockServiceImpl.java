package com.rome.stock.innerservice.domain.service.impl;

import com.rome.arch.core.domain.EntityFactory;
import com.rome.arch.core.exception.RomeException;
import com.rome.stock.innerservice.api.dto.*;
import com.rome.stock.innerservice.common.AlikAssert;
import com.rome.stock.innerservice.common.ResCode;
import com.rome.stock.innerservice.common.RomeCollectionUtil;
import com.rome.stock.innerservice.common.Snowflake;
import com.rome.stock.innerservice.constant.*;
import com.rome.stock.innerservice.domain.convertor.RealWarehouseConvertor;
import com.rome.stock.innerservice.domain.convertor.frontrecord.AdjustMerchantConvertor;
import com.rome.stock.innerservice.domain.entity.ChannelSalesE;
import com.rome.stock.innerservice.domain.entity.RealWarehouseE;
import com.rome.stock.innerservice.domain.entity.SkuQtyUnitBaseE;
import com.rome.stock.innerservice.domain.entity.VirtualWarehouseE;
import com.rome.stock.innerservice.domain.entity.frontrecord.AdjustMerchantRecordDetailE;
import com.rome.stock.innerservice.domain.entity.frontrecord.AdjustMerchantRecordE;
import com.rome.stock.innerservice.domain.entity.warehouserecord.AdjustMerchantWarehouseRecordE;
import com.rome.stock.innerservice.domain.entity.warehouserecord.WarehouseRecordDetail;
import com.rome.stock.core.domain.repository.CoreChannelSalesRepository;
import com.rome.stock.core.domain.repository.CoreRealWarehouseStockRepository;
import com.rome.stock.innerservice.domain.repository.RealWarehouseRepository;
import com.rome.stock.innerservice.domain.repository.VirtualWarehouseGroupRelationRepository;
import com.rome.stock.innerservice.domain.repository.VirtualWarehouseRepository;
import com.rome.stock.innerservice.domain.repository.frontrecord.FrAdjustMerchantRepository;
import com.rome.stock.innerservice.domain.service.*;
import com.rome.stock.core.facade.RedisRollBackFacade;
import com.rome.stock.core.infrastructure.dataobject.core.CoreChannelSalesDO;
import com.rome.stock.core.infrastructure.dataobject.core.CoreRealStockOpDO;
import com.rome.stock.core.infrastructure.dataobject.core.CoreRealStockOpDetailDO;
import com.rome.stock.core.infrastructure.dataobject.core.CoreRealWarehouseStockDO;
import com.rome.stock.core.infrastructure.dataobject.core.CoreStockOpFactoryDO;
import com.rome.stock.core.infrastructure.dataobject.core.CoreVirtualStockOpDO;
import com.rome.stock.innerservice.infrastructure.dataobject.VirtualWarehouseGroupRelationDO;
import com.rome.stock.innerservice.infrastructure.mapper.RealWarehouseMapper;
import com.rome.stock.core.infrastructure.redis.ChannelSalesRedis;
import com.rome.stock.innerservice.remote.base.dto.ChannelDTO;
import com.rome.stock.innerservice.remote.base.facade.ChannelFacade;
import com.rome.stock.innerservice.remote.item.dto.SkuUnitExtDTO;
import com.rome.stock.innerservice.remote.item.dto.SkuUnitParamExtDTO;
import com.rome.stock.innerservice.remote.item.facade.SkuFacade;
import com.rome.stock.wms.constants.WarehouseWmsConfigEnum;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 玄天接口对接相关服务实现
 */
@Slf4j
@Service
public class XTStockServiceImpl implements XTStockService {

    @Autowired
    private RealWarehouseConvertor realWarehouseConvertor;
    @Autowired
    private RealWarehouseService realWarehouseService;
    @Autowired
    private VirtualWarehouseGroupService virtualWarehouseGroupService;
    @Autowired
    private ChannelSalesService channelSalesService;
    @Autowired
    private VirtualWarehouseService virtualWarehouseService;
    @Autowired
    private VirtualWarehouseRepository virtualWarehouseRepository;
    @Autowired
    private RealWarehouseRepository realWarehouseRepository;
    @Autowired
    private RealWarehouseWmsConfigService realWarehouseWmsConfigService;
    @Resource
    private FrAdjustMerchantRepository frAdjustMerchantRepository;
    @Resource
    private AdjustMerchantConvertor adjustMerchantConvertor;
    @Resource
    private CoreRealWarehouseStockRepository coreRealWarehouseStockRepository;
    @Autowired
    private ChannelFacade channelFacade;

    @Resource
    private EntityFactory entityFactory;

    @Resource
    private CoreChannelSalesRepository coreChannelSalesRepository;

    @Autowired
    private SkuFacade skuFacade;

    private final ParamValidator validator = ParamValidator.INSTANCE;
    
    /**
     * channelSalesRedis.getChannelSales
     * 临时一用,7.30上线后，coreChannelSalesRepository，吐出
     */
    @Autowired
    private ChannelSalesRedis channelSalesRedis;
    
    
    @Autowired
    private VirtualWarehouseGroupRelationRepository virtualWarehouseGroupRelationRepository;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public RealWarehouse createWarehouse(WarehouseDTO warehouseDTO) {
        if (warehouseDTO.getWarehouseType() == 1 || warehouseDTO.getWarehouseType() == 2) {
        	warehouseDTO.setRwBusinessType(3);// 仓库业务类型: 1: 直营门店 2: 加盟门店 3: 仓库 4. 其他仓库
            if (StringUtils.isBlank(warehouseDTO.getRealWarehouseAddress())
                    || StringUtils.isBlank(warehouseDTO.getRealWarehouseContactName())
                    || StringUtils.isBlank(warehouseDTO.getRealWarehouseMobile())) {
                throw new RomeException(ResCode.STOCK_ERROR_1002, "联系地址、联系人、联系手机均不能为空");
            }
        }
        //创建实仓，外部仓库编码和工厂编码暂定一致，随机生成
        String warehouseCode = StringUtils.isBlank(warehouseDTO.getFactoryCode())
                ? getTStr("WC") : warehouseDTO.getFactoryCode();
        String warehouseOutCode = StringUtils.isBlank(warehouseDTO.getWarehouseOutCode()) ? warehouseCode : warehouseDTO.getWarehouseOutCode();
        //商家仓，冗余商家编号到仓库
        if (warehouseDTO.getWarehouseType().equals(1)) {
            warehouseCode = String.valueOf(warehouseDTO.getMerchantId()) + "-" +  String.valueOf(warehouseDTO.getMerchantId());
            warehouseOutCode = String.valueOf(warehouseDTO.getMerchantId());
            warehouseDTO.setRealWarehouseRank(RealWarehouseRankVO.RW_RANK_99.getType());
        }
        RealWarehouseAddDTO realWarehouseAddDTO = realWarehouseConvertor.paramDTOToAddDTO(warehouseDTO);
        realWarehouseAddDTO.setRealWarehouseCode(warehouseCode);
        realWarehouseAddDTO.setFactoryCode(warehouseCode);
        realWarehouseAddDTO.setRealWarehouseOutCode(warehouseOutCode);
        realWarehouseAddDTO.setRealWarehouseContactName(warehouseDTO.getRealWarehouseContactName());
        realWarehouseAddDTO.setRealWarehouseAddress(warehouseDTO.getRealWarehouseAddress());
        //处理省市区及行政区域
        if(StringUtils.isNotBlank(warehouseDTO.getProvinceCode())){
            realWarehouseAddDTO.setRealWarehouseProvinceCode(warehouseDTO.getProvinceCode());
        }
        if(StringUtils.isNotBlank(warehouseDTO.getRealWarehouseProvince())){
            realWarehouseAddDTO.setRealWarehouseProvince(warehouseDTO.getRealWarehouseProvince());
        }
        if(StringUtils.isNotBlank(warehouseDTO.getCityCode())){
            realWarehouseAddDTO.setRealWarehouseCityCode(warehouseDTO.getCityCode());
        }
        if(StringUtils.isNotBlank(warehouseDTO.getRealWarehouseCity())){
            realWarehouseAddDTO.setRealWarehouseCity(warehouseDTO.getRealWarehouseCity());
        }
        if(StringUtils.isNotBlank(warehouseDTO.getCountryCode())){
            realWarehouseAddDTO.setRealWarehouseCountryCode(warehouseDTO.getCountryCode());
        }
        if(StringUtils.isNotBlank(warehouseDTO.getRealWarehouseCounty())){
            realWarehouseAddDTO.setRealWarehouseCounty(warehouseDTO.getRealWarehouseCounty());
        }
        if(StringUtils.isNotBlank(warehouseDTO.getRegionName())){
            realWarehouseAddDTO.setRegionName(warehouseDTO.getRegionName());
        }
        if(null != warehouseDTO.getRegionId()){
            realWarehouseAddDTO.setRegionId(warehouseDTO.getRegionId());
        }
        switch (warehouseDTO.getWarehouseType()) {
            case 1:
                realWarehouseAddDTO.setRealWarehouseType(RealWarehouseTypeVO.RW_TYPE_21.getType());
                break;
            case 2:
                realWarehouseAddDTO.setRealWarehouseType(RealWarehouseTypeVO.RW_TYPE_15.getType());
                break;
            case 3:
                realWarehouseAddDTO.setRealWarehouseType(RealWarehouseTypeVO.RW_TYPE_1.getType());
                realWarehouseAddDTO.setShopCode(warehouseDTO.getShopCode());
                break;
            default:
                break;
        }
        //创建渠道
        List<String> channelCodeList = warehouseDTO.getChannelDTOList()
                .stream().map(ChannelDTO::getChannelCode).collect(Collectors.toList());

        List<ChannelDTO> channelDTOList = channelFacade.batchQueryByChannelcodes(channelCodeList);
        AlikAssert.isNotEmpty(channelDTOList, ResCode.STOCK_ERROR_5001, ResCode.STOCK_ERROR_5001_DESC);
        Map<String, ChannelDTO> channelMap = RomeCollectionUtil.listforMap(channelDTOList, "channelCode", null);
        //虚拟商品库存
        if(warehouseDTO.getWarehouseType().equals(2)){
            //一个渠道只对应一个渠道销售表
            if(channelDTOList.size()>1){
                throw new RomeException(ResCode.STOCK_ERROR_9028, ResCode.STOCK_ERROR_9028_DESC);
            }
            ChannelSales channelSale=new ChannelSales();
            channelSale.setChannelCode(channelDTOList.get(0).getChannelCode());
            List<ChannelSales> channelSalesEList = channelSalesService.getChannelSalesByChannelCode(channelSale);
            for(ChannelSales channelSales:channelSalesEList){
                List<VirtualWarehouseE> virtualWarehouseEs=virtualWarehouseService.getVwListByChannelCode(channelSales.getChannelCode());
                for(VirtualWarehouseE virtualWarehouseE:virtualWarehouseEs){
                    RealWarehouse realWarehouse=realWarehouseService.findByRealWarehouseId(virtualWarehouseE.getRealWarehouseId());
                    if(realWarehouse!=null && realWarehouse.getRealWarehouseType().equals(RealWarehouseTypeVO.RW_TYPE_15.getType())){
                        throw new RomeException(ResCode.STOCK_ERROR_9029, ResCode.STOCK_ERROR_9029_DESC);
                    }
                }
            }
            //存在虚拟仓库但不存在该渠道的仓库时，虚拟商品仓库和新渠道的对应关系
            RealWarehouse realWarehouse= realWarehouseService.queryVirtualSkuRealWarehouse();
            if(realWarehouse!=null && CollectionUtils.isEmpty(channelSalesEList)){
                List<VirtualWarehouseE> virtualWarehouseEList=virtualWarehouseRepository.queryByRealWarehouseId(realWarehouse.getId());
                Long groupId = virtualWarehouseGroupRelationRepository.queryGroupIdsByVmId(virtualWarehouseEList.get(0).getId()).get(0);
                VirtualWarehouseGroup virtualWarehouseGroup=virtualWarehouseGroupService.getVirtualWarehouseGroupById(groupId);
                for (ChannelDTO dto : warehouseDTO.getChannelDTOList()) {
                    ChannelDTO baseChannelDTO = channelMap.get(dto.getChannelCode());
                    AlikAssert.isNotNull(baseChannelDTO, ResCode.STOCK_ERROR_5001, ResCode.STOCK_ERROR_5001_DESC);
                    ChannelSales channelSales = new ChannelSales();
                    channelSales.setMerchantId(warehouseDTO.getMerchantId());
                    channelSales.setChannelCode(baseChannelDTO.getChannelCode());
                    channelSales.setChannelName(baseChannelDTO.getChannelName());
                    channelSales.setVirtualWarehouseGroupId(virtualWarehouseGroup.getId());
                    channelSales.setShowRate(100);
                    channelSalesService.addChannelSales(channelSales);
                }
                return realWarehouse;
            }else if(!CollectionUtils.isEmpty(channelSalesEList)){
                //渠道存在对应库存,但是不存在虚拟商品仓
                for(ChannelSales channelSales:channelSalesEList){
                    Long vwGroupId =channelSales.getVirtualWarehouseGroupId();
                    Long realWarehouseId = realWarehouseService.addRealWarehouse(realWarehouseAddDTO);
                    AlikAssert.isNotNull(realWarehouseId, ResCode.STOCK_ERROR_1004, ResCode.STOCK_ERROR_1004_DESC);
                    //创建虚仓
                    VirtualWarehouse virtualWarehouse = new VirtualWarehouse();
                    virtualWarehouse.setRealWarehouseId(realWarehouseId);
                    virtualWarehouse.setSyncRate(100);
                    virtualWarehouse.setVirtualWarehouseName(warehouseDTO.getWarehouseName());
                    virtualWarehouse.setVirtualWarehouseCode(warehouseCode);
                    virtualWarehouseService.saveVirtualWarehouse(virtualWarehouse);
                    // 创建虚拟仓库与组关系
                    VirtualWarehouseGroupRelationDO relationDO = new VirtualWarehouseGroupRelationDO();
                    relationDO.setVirtualWarehouseId(virtualWarehouse.getId());
                    relationDO.setVirtualWarehouseGroupId(vwGroupId);
                    virtualWarehouseGroupRelationRepository.batchInsert(Collections.singletonList(relationDO));
                    //虚拟物品仓库-添加实仓和wms对应关系数据
                    RealWarehouseWmsConfigDTO realWarehouseWmsConfigDTO = new RealWarehouseWmsConfigDTO();
                    realWarehouseWmsConfigDTO.setRealWarehouseId(realWarehouseId);
                    realWarehouseWmsConfigDTO.setRealWarehouseCode(warehouseCode);
                    realWarehouseWmsConfigDTO.setWmsCode(WarehouseWmsConfigEnum.VR.getType());
                    realWarehouseWmsConfigService.addRealWarehouseWmsConfig(realWarehouseWmsConfigDTO);
                    realWarehouse = realWarehouseService.findByRealWarehouseId(realWarehouseId);
                    return realWarehouse;
                }
            }

        }
        Long realWarehouseId = realWarehouseService.addRealWarehouse(realWarehouseAddDTO);
        AlikAssert.isNotNull(realWarehouseId, ResCode.STOCK_ERROR_1004, ResCode.STOCK_ERROR_1004_DESC);
        //创建策略组
        VirtualWarehouseGroup virtualWarehouseGroup = new VirtualWarehouseGroup();
        virtualWarehouseGroup.setMerchantId(warehouseDTO.getMerchantId());
        virtualWarehouseGroup.setVirtualWarehouseGroupCode(warehouseCode);
        virtualWarehouseGroup.setName(warehouseDTO.getWarehouseName());
        Long vwGroupId = virtualWarehouseGroupService.addVirtualWarehouseGroup(virtualWarehouseGroup);
        AlikAssert.isNotNull(vwGroupId, ResCode.STOCK_ERROR_2004, ResCode.STOCK_ERROR_2004_DESC);

        if (warehouseDTO.getWarehouseType().equals(1)) {
            List<ChannelSalesE> channelSalesEList = channelSalesService.queryByMerchantId(warehouseDTO.getMerchantId());
            if (null != channelSalesEList && !channelSalesEList.isEmpty()) {
                throw new RomeException(ResCode.STOCK_ERROR_1076, ResCode.STOCK_ERROR_1076_DESC);
            }
        }
        for (ChannelDTO dto : warehouseDTO.getChannelDTOList()) {
            ChannelDTO baseChannelDTO = channelMap.get(dto.getChannelCode());
            AlikAssert.isNotNull(baseChannelDTO, ResCode.STOCK_ERROR_5001, ResCode.STOCK_ERROR_5001_DESC);
            ChannelSales channelSales = new ChannelSales();
            channelSales.setMerchantId(warehouseDTO.getMerchantId());
            channelSales.setChannelCode(baseChannelDTO.getChannelCode());
            channelSales.setChannelName(baseChannelDTO.getChannelName());
            channelSales.setVirtualWarehouseGroupId(vwGroupId);
            channelSales.setShowRate(100);
            channelSalesService.addChannelSales(channelSales);
        }
        //创建虚仓
        VirtualWarehouse virtualWarehouse = new VirtualWarehouse();
        virtualWarehouse.setRealWarehouseId(realWarehouseId);
        virtualWarehouse.setSyncRate(100);
        virtualWarehouse.setVirtualWarehouseName(warehouseDTO.getWarehouseName());
        virtualWarehouse.setVirtualWarehouseCode(warehouseCode);
        virtualWarehouseService.saveVirtualWarehouse(virtualWarehouse);
        // 创建虚拟仓库与组关系
        VirtualWarehouseGroupRelationDO relationDO = new VirtualWarehouseGroupRelationDO();
        relationDO.setVirtualWarehouseId(virtualWarehouse.getId());
        relationDO.setVirtualWarehouseGroupId(vwGroupId);
        virtualWarehouseGroupRelationRepository.batchInsert(Collections.singletonList(relationDO));
        //商家仓 - 添加实仓和wms对应关系数据
        if (warehouseDTO.getWarehouseType().equals(1)) {
            RealWarehouseWmsConfigDTO realWarehouseWmsConfigDTO = new RealWarehouseWmsConfigDTO();
            realWarehouseWmsConfigDTO.setRealWarehouseId(realWarehouseId);
            realWarehouseWmsConfigDTO.setRealWarehouseCode(warehouseCode);
            realWarehouseWmsConfigDTO.setWmsCode(WarehouseWmsConfigEnum.SJ.getType());
            realWarehouseWmsConfigService.addRealWarehouseWmsConfig(realWarehouseWmsConfigDTO);
        }else if(warehouseDTO.getWarehouseType().equals(2)){
            //虚拟物品仓库-添加实仓和wms对应关系数据
            RealWarehouseWmsConfigDTO realWarehouseWmsConfigDTO = new RealWarehouseWmsConfigDTO();
            realWarehouseWmsConfigDTO.setRealWarehouseId(realWarehouseId);
            realWarehouseWmsConfigDTO.setRealWarehouseCode(warehouseCode);
            realWarehouseWmsConfigDTO.setWmsCode(WarehouseWmsConfigEnum.VR.getType());
            realWarehouseWmsConfigService.addRealWarehouseWmsConfig(realWarehouseWmsConfigDTO);
        }
        return realWarehouseService.findByRealWarehouseId(realWarehouseId);
    }


    /**
     * 查询商家仓库信息
     *
     * @param merchantId
     * @param channelCode
     * @return
     */
    @Override
    public List<RealWarehouse> queryWarehouse(Long merchantId, String channelCode) {
        List<ChannelSalesE> channelSalesEList = channelSalesService.queryByMerchantIdAndChannelCode(merchantId, channelCode);
        if (null == channelSalesEList || channelSalesEList.isEmpty()) {
            return null;
        }
        Long vwGroupId = channelSalesEList.get(0).getVirtualWarehouseGroupId();
        List<VirtualWarehouseE> virtualWarehouseEList = virtualWarehouseRepository.queryByGroupId(vwGroupId);
        if (null == virtualWarehouseEList || virtualWarehouseEList.isEmpty()) {
            return null;
        }
        List<Long> rwIdList = virtualWarehouseEList.stream()
                .map(VirtualWarehouseE::getRealWarehouseId)
                .collect(Collectors.toList());
        if (null == rwIdList || rwIdList.isEmpty()) {
            return null;
        }
        List<RealWarehouseE> realWarehouseEList = realWarehouseRepository.queryWarehouseByIds(rwIdList);
        return realWarehouseConvertor.entityToDto(realWarehouseEList);
    }

    private String getTStr(String str) {
        return str + Snowflake.getInstanceSnowflake().nextId();
    }


    /**
     * 调整商家仓库库存
     *
     * @param adjustSkuStockDTO
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public RealWarehouse adjustSkuStock(AdjustSkuStockDTO adjustSkuStockDTO) {
        Long merchantId = skuFacade.getDefaultMerchantId();
        if (!(adjustSkuStockDTO.getVirtualSkuFlag() != null && adjustSkuStockDTO.getVirtualSkuFlag() == 1)) {
            if(null == adjustSkuStockDTO.getMerchantId()){
                throw new RomeException(ResCode.STOCK_ERROR_9032,ResCode.STOCK_ERROR_9032_DESC);
            }
            merchantId = adjustSkuStockDTO.getMerchantId();
        }

        if (frAdjustMerchantRepository.judgeExistByOutRecordCode(adjustSkuStockDTO.getOutRecordCode())) {
            //幂等判断
            throw new RomeException(ResCode.STOCK_ERROR_1024, ResCode.STOCK_ERROR_1024_DESC);
        }
        adjustSkuStockDTO.setOutRecordCode(adjustSkuStockDTO.getWarehouseCode());
        RealWarehouse warehouse = realWarehouseService.findByRealWarehouseOutCodeAndFactoryCode(adjustSkuStockDTO.getWarehouseCode(), adjustSkuStockDTO.getFactoryCode());
        AlikAssert.isNotNull(warehouse, ResCode.STOCK_ERROR_1011, ResCode.STOCK_ERROR_1011_DESC);
        if(adjustSkuStockDTO.getVirtualSkuFlag()!=null && adjustSkuStockDTO.getVirtualSkuFlag()!=0 && !warehouse.getRealWarehouseType().equals(RealWarehouseTypeVO.RW_TYPE_15.getType())){
            throw new RomeException(ResCode.STOCK_ERROR_9019, ResCode.STOCK_ERROR_9019_DESC);
        }
        AdjustMerchantRecordE adjustMerchantRecordE = adjustMerchantConvertor.dtoToEntity(adjustSkuStockDTO);
        List <Long> skuIds = new ArrayList<>();
        adjustMerchantRecordE.setRecordStatus(FrontRecordStatusVO.COMPLETE.getStatus());
        adjustMerchantRecordE.setRealWarehouseId(warehouse.getId());
        adjustMerchantRecordE.addFrontRecord();
        List<AdjustMerchantRecordDetailE> outFrontRecordDetails = new ArrayList<>();
        List<AdjustMerchantRecordDetailE> inFrontRecordDetails = new ArrayList<>();
        CoreRealStockOpDO increaseOpDO = null;
        CoreRealStockOpDO decreaseOpDO = null;
        for (AdjustMerchantRecordDetailE detailE : adjustMerchantRecordE.getDetails()) {
            if (detailE.getBasicSkuQty().compareTo(BigDecimal.ZERO) > 0) {
                inFrontRecordDetails.add(detailE);
            } else if (detailE.getBasicSkuQty().compareTo(BigDecimal.ZERO) < 0) {
                detailE.setBasicSkuQty(detailE.getBasicSkuQty().abs());
                outFrontRecordDetails.add(detailE);
                skuIds.add(detailE.getSkuId());
            }
        }
        boolean isSuccess = false;
        try {
            if (outFrontRecordDetails.size() > 0) {
                AdjustMerchantWarehouseRecordE warehouseRecordE = entityFactory.createEntity(AdjustMerchantWarehouseRecordE.class);
                adjustMerchantRecordE.setDetails(outFrontRecordDetails);
                warehouseRecordE.createRecordByFrontRecord(adjustMerchantRecordE, WarehouseRecordBusinessTypeVO.OUT_WAREHOUSE_RECORD);
                warehouseRecordE.setRecordStatus(WarehouseRecordStatusVO.OUT_ALLOCATION.getStatus());
                warehouseRecordE.addWarehouseRecord();
                decreaseOpDO = warehouseRecordE.initDecreaseStockObj(warehouseRecordE.getRealWarehouseId(), true , merchantId);
                coreRealWarehouseStockRepository.decreaseRealQty(decreaseOpDO);

                //校验可用库存是否为负
                this.verifyStock(skuIds ,warehouse.getId());
            }
            if (inFrontRecordDetails.size() > 0) {
                AdjustMerchantWarehouseRecordE warehouseRecordE = entityFactory.createEntity(AdjustMerchantWarehouseRecordE.class);
                adjustMerchantRecordE.setDetails(inFrontRecordDetails);
                warehouseRecordE.createRecordByFrontRecord(adjustMerchantRecordE, WarehouseRecordBusinessTypeVO.IN_WAREHOUSE_RECORD);
                warehouseRecordE.setRecordStatus(WarehouseRecordStatusVO.IN_ALLOCATION.getStatus());
                warehouseRecordE.addWarehouseRecord();
                increaseOpDO = warehouseRecordE.initIncreaseStockObj(warehouseRecordE.getRealWarehouseId(),merchantId);
                coreRealWarehouseStockRepository.increaseRealQty(increaseOpDO);
            }
            isSuccess = true;
        } catch (RomeException e) {
            log.error(e.getMessage(), e);
            throw e;
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            throw new RomeException(ResCode.STOCK_ERROR_1003, ResCode.STOCK_ERROR_1003_DESC + e.getMessage());
        } finally {
            //模型成功，业务等处理失败，需要回滚
            if (!isSuccess) {
                RedisRollBackFacade.redisRollBack(increaseOpDO);
                RedisRollBackFacade.redisRollBack(decreaseOpDO);
            }
        }
        return warehouse;
    }

    /**
     * 批量校验是否有负库存
     *
     * @param skuIds
     * @param rwId
     * @return 没有负库存 返回  true 有负库存返回false
     */
    private void verifyStock(List<Long> skuIds, Long rwId) {
        List<CoreRealWarehouseStockDO> queryParam = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(skuIds)) {
            for (Long skuId : skuIds) {
                CoreRealWarehouseStockDO crwDo = new CoreRealWarehouseStockDO();
                crwDo.setSkuId(skuId);
                crwDo.setRealWarehouseId(rwId);
                queryParam.add(crwDo);
            }
            List<CoreRealWarehouseStockDO> stockList = coreRealWarehouseStockRepository.getRWStock(queryParam);
            for (CoreRealWarehouseStockDO warehouseStockDO : stockList) {
                if (warehouseStockDO.getAvailableQty().compareTo(BigDecimal.ZERO) < 0) {
                    AlikAssert.isTrue(false , ResCode.STOCK_ERROR_1002,ResCode.STOCK_ERROR_1002_DESC + ":可用库存不能调整为负数:skuId=" + warehouseStockDO.getSkuId());
                }
            }
        }
    }
    
    /**
     * 调整商家仓库库存,即虚拟仓库
     * 根据渠道
     *
     * @param adjustSkuStockDTO
     * <AUTHOR>
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public RealWarehouse adjustVirtualSkuStockByChannelCode(AdjustSkuStockDTO adjustSkuStockDTO) {
        Long merchantId = skuFacade.getDefaultMerchantId();
        if(null == adjustSkuStockDTO.getMerchantId()){
            throw new RomeException(ResCode.STOCK_ERROR_9032,ResCode.STOCK_ERROR_9032_DESC);
        }
        merchantId = adjustSkuStockDTO.getMerchantId();

        if (frAdjustMerchantRepository.judgeExistByOutRecordCode(adjustSkuStockDTO.getOutRecordCode())) {
            //幂等判断
            throw new RomeException(ResCode.STOCK_ERROR_1024, ResCode.STOCK_ERROR_1024_DESC);
        }
       
        //虚仓列表
        List<VirtualWarehouseE> virtualWarehouseEList = queryVirtualWarehouse(adjustSkuStockDTO.getChannelCode());
        AlikAssert.isNotEmpty(virtualWarehouseEList, ResCode.STOCK_ERROR_1011, ResCode.STOCK_ERROR_1011_DESC);
        List<Long> rwIdList = virtualWarehouseEList.stream()
                .map(VirtualWarehouseE::getRealWarehouseId)
                .collect(Collectors.toList());
        List<RealWarehouseE> realWarehouseEList = realWarehouseRepository.queryWarehouseByIds(rwIdList);
        AlikAssert.isNotEmpty(realWarehouseEList, ResCode.STOCK_ERROR_1011, ResCode.STOCK_ERROR_1011_DESC);
        realWarehouseEList = realWarehouseEList.stream().filter(dto -> RealWarehouseTypeVO.RW_TYPE_15.getType().equals(dto.getRealWarehouseType())).collect(Collectors.toList());
        if(realWarehouseEList.size() != 1) {
        	throw new RomeException(ResCode.STOCK_ERROR_9019, String.format("%s,%s【%d】", ResCode.STOCK_ERROR_9019_DESC, "或者多个无法分配库存", realWarehouseEList.size()));
        }
        RealWarehouseE warehouse = realWarehouseEList.get(0);
        VirtualWarehouseE virtualWarehouse = null;
        for(VirtualWarehouseE dto : virtualWarehouseEList) {
        	if(warehouse.getId().equals(dto.getRealWarehouseId())) {
        		virtualWarehouse = dto;
        		break;
        	}
        }
        AdjustMerchantRecordE adjustMerchantRecordE = adjustMerchantConvertor.dtoToEntity(adjustSkuStockDTO);
        List <Long> skuIds = new ArrayList<>();
        adjustMerchantRecordE.setRecordStatus(FrontRecordStatusVO.COMPLETE.getStatus());
        adjustMerchantRecordE.setRealWarehouseId(warehouse.getId());
        adjustMerchantRecordE.addFrontRecord();
        List<AdjustMerchantRecordDetailE> outFrontRecordDetails = new ArrayList<>();
        List<AdjustMerchantRecordDetailE> inFrontRecordDetails = new ArrayList<>();
        for (AdjustMerchantRecordDetailE detailE : adjustMerchantRecordE.getDetails()) {
            if (detailE.getBasicSkuQty().compareTo(BigDecimal.ZERO) > 0) {
                inFrontRecordDetails.add(detailE);
            } else if (detailE.getBasicSkuQty().compareTo(BigDecimal.ZERO) < 0) {
                detailE.setBasicSkuQty(detailE.getBasicSkuQty().abs());
                outFrontRecordDetails.add(detailE);
                skuIds.add(detailE.getSkuId());
            }
        }
        CoreStockOpFactoryDO stockOpFactoryDO = new CoreStockOpFactoryDO();
        boolean isSuccess = false;
        try {
            if (outFrontRecordDetails.size() > 0) {
                AdjustMerchantWarehouseRecordE warehouseRecordE = entityFactory.createEntity(AdjustMerchantWarehouseRecordE.class);
                adjustMerchantRecordE.setDetails(outFrontRecordDetails);
                warehouseRecordE.createRecordByFrontRecord(adjustMerchantRecordE, WarehouseRecordBusinessTypeVO.OUT_WAREHOUSE_RECORD);
                warehouseRecordE.setRecordStatus(WarehouseRecordStatusVO.OUT_ALLOCATION.getStatus());
                warehouseRecordE.setVirtualWarehouseId(virtualWarehouse.getId());
                warehouseRecordE.addWarehouseRecord();
                CoreRealStockOpDO decreaseOpDO = warehouseRecordE.initDecreaseStockObj(warehouseRecordE.getRealWarehouseId(), true , merchantId);
                setCoreVirtualStock(virtualWarehouse.getId(), decreaseOpDO, adjustSkuStockDTO.getChannelCode());
                stockOpFactoryDO.reset(decreaseOpDO);
                coreRealWarehouseStockRepository.decreaseRealQty(decreaseOpDO);

                //校验可用库存是否为负
                this.verifyStock(skuIds ,warehouse.getId());
            }
            if (inFrontRecordDetails.size() > 0) {
                AdjustMerchantWarehouseRecordE warehouseRecordE = entityFactory.createEntity(AdjustMerchantWarehouseRecordE.class);
                adjustMerchantRecordE.setDetails(inFrontRecordDetails);
                warehouseRecordE.createRecordByFrontRecord(adjustMerchantRecordE, WarehouseRecordBusinessTypeVO.IN_WAREHOUSE_RECORD);
                warehouseRecordE.setRecordStatus(WarehouseRecordStatusVO.IN_ALLOCATION.getStatus());
                warehouseRecordE.setVirtualWarehouseId(virtualWarehouse.getId());
                warehouseRecordE.addWarehouseRecord();
                CoreRealStockOpDO increaseOpDO = warehouseRecordE.initIncreaseStockObj(warehouseRecordE.getRealWarehouseId(),merchantId);
                setCoreVirtualStock(virtualWarehouse.getId(), increaseOpDO, adjustSkuStockDTO.getChannelCode());
                stockOpFactoryDO.reset(increaseOpDO);
                coreRealWarehouseStockRepository.increaseRealQty(increaseOpDO);
            }
            // 提交模型数据，必须紧跟isSuccess=true;语句
            stockOpFactoryDO.commit();
            isSuccess = true;
        } catch (RomeException e) {
            log.error(e.getMessage(), e);
            throw e;
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            throw new RomeException(ResCode.STOCK_ERROR_1003, ResCode.STOCK_ERROR_1003_DESC + e.getMessage());
        } finally {
            //模型成功，业务等处理失败，需要回滚
            if (!isSuccess) {
            	stockOpFactoryDO.redisRollBack();
            }
        }
        return realWarehouseConvertor.entityToDto(warehouse);
    }
    
    /**
     * 调整协同供应商仓库库存,即供应商仓库
     * 根据shopCode
     *
     * @param adjustSkuStockDTO
     * <AUTHOR>
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public RealWarehouse adjustSupplierSkuStockByShopCode(AdjustSkuStockDTO adjustSkuStockDTO) {
        Long merchantId = skuFacade.getDefaultMerchantId();
        if(null != adjustSkuStockDTO.getMerchantId()){
        	merchantId = adjustSkuStockDTO.getMerchantId();
        }else {
        	adjustSkuStockDTO.setMerchantId(merchantId);
        }
        if (frAdjustMerchantRepository.judgeExistByOutRecordCode(adjustSkuStockDTO.getOutRecordCode())) {
            //幂等判断
            throw new RomeException(ResCode.STOCK_ERROR_1024, ResCode.STOCK_ERROR_1024_DESC);
        }
        if(CollectionUtils.isEmpty(adjustSkuStockDTO.getDetails())) {
        	throw new RomeException(ResCode.STOCK_ERROR_1002, "明细不能为空");
        }
        for(SkuDetailDTO dto : adjustSkuStockDTO.getDetails()) {
        	if(StringUtils.isBlank(dto.getSkuCode()) && dto.getSkuId() == null) {
        		throw new RomeException(ResCode.STOCK_ERROR_1002, "商品编码为空");
        	}
            if(dto.getSkuQty() == null || dto.getSkuQty().compareTo(BigDecimal.ZERO) < 0) {
            	throw new RomeException(ResCode.STOCK_ERROR_1002, "调整数量不能小于0");
        	}
        }
        List<String> shopCodes = new ArrayList<>();
        shopCodes.add(adjustSkuStockDTO.getShopCode());
        List<RealWarehouseE> realWarehouseEList = realWarehouseRepository.getAvailableRwListByShopCodes(shopCodes);
        AlikAssert.isNotEmpty(realWarehouseEList, ResCode.STOCK_ERROR_1011, ResCode.STOCK_ERROR_1011_DESC);
        realWarehouseEList = realWarehouseEList.stream().filter(dto -> RealWarehouseTypeVO.RW_TYPE_25.getType().equals(dto.getRealWarehouseType())).collect(Collectors.toList());
        if(realWarehouseEList.size() != 1) {
        	throw new RomeException(ResCode.STOCK_ERROR_9019, String.format("%s,%s【%d】", "仓库类型不是正确", "或者多个无法分配库存", realWarehouseEList.size()));
        }
        RealWarehouseE warehouse = realWarehouseEList.get(0);
        AdjustMerchantRecordE adjustMerchantRecordE = adjustMerchantConvertor.dtoToEntity(adjustSkuStockDTO);
        List <Long> skuIds = new ArrayList<>();
        adjustMerchantRecordE.setRecordStatus(FrontRecordStatusVO.COMPLETE.getStatus());
        adjustMerchantRecordE.setRealWarehouseId(warehouse.getId());
        adjustMerchantRecordE.addFrontRecord();
        List<AdjustMerchantRecordDetailE> outFrontRecordDetails = new ArrayList<>();
        List<AdjustMerchantRecordDetailE> inFrontRecordDetails = new ArrayList<>();
        
        List<CoreRealWarehouseStockDO> realWarehouseStockDOList = new ArrayList<>();
        for(AdjustMerchantRecordDetailE detailE : adjustMerchantRecordE.getDetails()) {
            CoreRealWarehouseStockDO stockDO = new CoreRealWarehouseStockDO();
            stockDO.setSkuId(detailE.getSkuId());
            stockDO.setRealWarehouseId(warehouse.getId());
            realWarehouseStockDOList.add(stockDO);
        }
        List<CoreRealWarehouseStockDO> stockDOList = coreRealWarehouseStockRepository.getRWStock(realWarehouseStockDOList);
        Map<Long, BigDecimal> stockDOMap = stockDOList.stream().collect(Collectors.toMap(CoreRealWarehouseStockDO::getSkuId, CoreRealWarehouseStockDO::getRealQty));
        BigDecimal realQty;
        BigDecimal diffQty;
        for (AdjustMerchantRecordDetailE detailE : adjustMerchantRecordE.getDetails()) {
        	realQty = stockDOMap.get(detailE.getSkuId()) == null ? BigDecimal.ZERO : stockDOMap.get(detailE.getSkuId());
            diffQty = detailE.getSkuQty().subtract(realQty);
            if(BigDecimal.ZERO.compareTo(diffQty) < 0) {
            	// 盘盈
            	detailE.setBasicSkuQty(diffQty);
            	inFrontRecordDetails.add(detailE);
            }else if(BigDecimal.ZERO.compareTo(diffQty) > 0) {
            	// 盘亏
            	detailE.setBasicSkuQty(diffQty.abs());
                outFrontRecordDetails.add(detailE);
                skuIds.add(detailE.getSkuId());
            }
        }
        CoreStockOpFactoryDO stockOpFactoryDO = new CoreStockOpFactoryDO();
        boolean isSuccess = false;
        try {
            if (outFrontRecordDetails.size() > 0) {
                AdjustMerchantWarehouseRecordE warehouseRecordE = entityFactory.createEntity(AdjustMerchantWarehouseRecordE.class);
                adjustMerchantRecordE.setDetails(outFrontRecordDetails);
                warehouseRecordE.createRecordByFrontRecord(adjustMerchantRecordE, WarehouseRecordBusinessTypeVO.OUT_WAREHOUSE_RECORD);
                warehouseRecordE.setRecordStatus(WarehouseRecordStatusVO.OUT_ALLOCATION.getStatus());
                warehouseRecordE.setVirtualWarehouseId(null);
                warehouseRecordE.addWarehouseRecord();
                CoreRealStockOpDO decreaseOpDO = warehouseRecordE.initDecreaseStockObj(warehouseRecordE.getRealWarehouseId(), true , merchantId);
                stockOpFactoryDO.reset(decreaseOpDO);
                coreRealWarehouseStockRepository.decreaseRealQty(decreaseOpDO);

                //校验可用库存是否为负
//                this.verifyStock(skuIds ,warehouse.getId());
            }
            if (inFrontRecordDetails.size() > 0) {
                AdjustMerchantWarehouseRecordE warehouseRecordE = entityFactory.createEntity(AdjustMerchantWarehouseRecordE.class);
                adjustMerchantRecordE.setDetails(inFrontRecordDetails);
                warehouseRecordE.createRecordByFrontRecord(adjustMerchantRecordE, WarehouseRecordBusinessTypeVO.IN_WAREHOUSE_RECORD);
                warehouseRecordE.setRecordStatus(WarehouseRecordStatusVO.IN_ALLOCATION.getStatus());
                warehouseRecordE.setVirtualWarehouseId(null);
                warehouseRecordE.addWarehouseRecord();
                CoreRealStockOpDO increaseOpDO = warehouseRecordE.initIncreaseStockObj(warehouseRecordE.getRealWarehouseId(),merchantId);
                stockOpFactoryDO.reset(increaseOpDO);
                coreRealWarehouseStockRepository.increaseRealQty(increaseOpDO);
            }
            // 提交模型数据，必须紧跟isSuccess=true;语句
            stockOpFactoryDO.commit();
            isSuccess = true;
        } catch (RomeException e) {
            log.error(e.getMessage(), e);
            throw e;
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            throw new RomeException(ResCode.STOCK_ERROR_1003, ResCode.STOCK_ERROR_1003_DESC + e.getMessage());
        } finally {
            //模型成功，业务等处理失败，需要回滚
            if (!isSuccess) {
            	stockOpFactoryDO.redisRollBack();
            }
        }
        return realWarehouseConvertor.entityToDto(warehouse);
    }
    
    /**
     * 查询虚拟仓库信息
     *
     * @param channelCode
     * @return
     */
    private List<VirtualWarehouseE> queryVirtualWarehouse(String channelCode) {
        CoreChannelSalesDO channelSales = channelSalesRedis.getChannelSales(channelCode);
        if (null == channelSales || channelSales.getGroupId() == null) {
            return null;
        }
        Long vwGroupId = channelSales.getGroupId();
        List<VirtualWarehouseE> virtualWarehouseEList = virtualWarehouseRepository.queryByGroupId(vwGroupId);
        if (null == virtualWarehouseEList || virtualWarehouseEList.isEmpty()) {
            return null;
        }
        return virtualWarehouseEList;
    }
    
    /**
     * 组装虚仓库存数据
     * @param vwId
     * @param realStockOpDO
     * @param channelCode
     */
    private void setCoreVirtualStock(Long vwId, CoreRealStockOpDO realStockOpDO, String channelCode) {
    	List<CoreVirtualStockOpDO> virtualStockByCalculateDOs = new ArrayList<>(realStockOpDO.getDetailDos().size());
    	for(CoreRealStockOpDetailDO dto : realStockOpDO.getDetailDos()) {
    		CoreVirtualStockOpDO coreVirtualStockOpDO = new CoreVirtualStockOpDO();
            coreVirtualStockOpDO.setSkuId(dto.getSkuId());
            coreVirtualStockOpDO.setVirtualWarehouseId(vwId);
            coreVirtualStockOpDO.setSkuCode(dto.getSkuCode());
            coreVirtualStockOpDO.setChannelCode(channelCode);
            coreVirtualStockOpDO.setMerchantId(dto.getMerchantId());
            coreVirtualStockOpDO.setRealWarehouseId(dto.getRealWarehouseId());
            coreVirtualStockOpDO.setRecordCode(realStockOpDO.getRecordCode());
            coreVirtualStockOpDO.setTransType(realStockOpDO.getTransType());
            coreVirtualStockOpDO.setRealQty(dto.getRealQty());
            dto.setChannelCode(channelCode);
            virtualStockByCalculateDOs.add(coreVirtualStockOpDO);
    	}
    	realStockOpDO.setCalculateVirtualStockFlag(false);
    	realStockOpDO.setVirtualStockByCalculateDOs(virtualStockByCalculateDOs);
    }
}
