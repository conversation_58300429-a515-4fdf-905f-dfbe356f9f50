package com.rome.stock.innerservice.domain.batch.impl;

import com.google.common.collect.Lists;
import com.rome.stock.common.enums.warehouse.WarehouseRecordTypeVO;
import com.rome.stock.innerservice.domain.batch.RwBatchProcessRecordType;
import com.rome.stock.innerservice.domain.batch.RwBatchStockRelationProcessor;
import com.rome.stock.innerservice.domain.batch.dto.RwRelationQueryDTO;
import com.rome.stock.innerservice.remote.orderCneter.facade.OrderCenterFacade;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * @Description 调拨及差异
 * <AUTHOR>
@Service
@RwBatchProcessRecordType(recordVOTypes={
        WarehouseRecordTypeVO.WH_ALLOCATION_IN_WAREHOUSE_RECORD,//仓库调拨入库单

        WarehouseRecordTypeVO.DISPARITY_WH_FROM_RECORD,//差异管理-增加出库仓库存[调拨.出库仓责任]
        WarehouseRecordTypeVO.DISPARITY_WH_TO_RECORD,//差异管理-增加入库仓库存[调拨.入库仓责任]
        WarehouseRecordTypeVO.DISPARITY_WH_RETURN_FROM_RECORD,//差异管理-增加出库仓库存[逆向调拨.出库仓责任]
        WarehouseRecordTypeVO.DISPARITY_WH_RETURN_TO_RECORD,//差异管理-增加入库仓库存[逆向调拨.入库仓责任]

})
public class RwBatchWhAllocationProcessor implements RwBatchStockRelationProcessor {

    @Resource
    private OrderCenterFacade orderCenterFacade;

    @Override
    public Map<String, List<String>> getRelationRecordList(RwRelationQueryDTO rwRelationQueryDTO) {
        List<String> recordCodeList = rwRelationQueryDTO.getRecordCodeList();
        Map<String, String> outWhRecordMap = orderCenterFacade.queryOutWhRecordByInWhRecords(recordCodeList);
        Map<String, List<String>> resultMap = new HashMap<>();
        for(Map.Entry<String, String> entry :outWhRecordMap.entrySet()){
            if(resultMap.containsKey(entry.getKey())){
                resultMap.get(entry.getKey()).add(entry.getValue());
            }else{
                resultMap.put(entry.getKey(), Lists.newArrayList(entry.getValue()));
            }
        }
        return resultMap;
    }
}
