package com.rome.stock.innerservice.domain.entity;

import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Component;

/**
 * @Author: zhoupeng
 * @createTime: 2022年07月15日 09:47:20
 * @version: 1.0
 * @Description:
 */
@Component
@Scope("prototype")
@Data
@EqualsAndHashCode(callSuper = false)
public class BaseinfoConfigE  extends BaseE {

    /**
     * 唯一主键
     */
    private Long id;

    /**
     * 业务code
     */
    private  String  code;

    /**
     * 参数名称
     */
    private  String  paramName;

    /**
     * 参数值
     */
    private  String   paramValue;

    /**
     * 参数描述
     */
    private  String   paramDesc;

    /**
     * 额外字段
     */
    private  String  ext;

}
