/**
 * Filename WmsStockCheckServiceImpl.java
 * Company 上海来伊份科技有限公司。
 * <AUTHOR>
 * @version 
 */
package com.rome.stock.innerservice.domain.service.impl;

import com.alibaba.fastjson.JSON;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.rome.arch.core.domain.EntityFactory;
import com.rome.arch.core.exception.RomeException;
import com.rome.arch.util.SpringBeanUtil;
import com.rome.stock.common.enums.warehouse.CombineSkuTypeVO;
import com.rome.stock.common.enums.warehouse.WarehouseStoreIdentiEnum;
import com.rome.stock.core.constant.StockCoreConsts;
import com.rome.stock.core.infrastructure.redis.StockCacheKeyEnum;
import com.rome.stock.innerservice.api.dto.RealWarehouse;
import com.rome.stock.innerservice.api.dto.WmsStockCheckFlowDTO;
import com.rome.stock.innerservice.common.EsIndexTypeConfig;
import com.rome.stock.innerservice.common.RedisUtil;
import com.rome.stock.innerservice.common.ResCode;
import com.rome.stock.innerservice.common.RomeCollectionUtil;
import com.rome.stock.innerservice.constant.RealWarehouseTypeVO;
import com.rome.stock.innerservice.domain.convertor.EsWmsStockCheckFlowConvertor;
import com.rome.stock.innerservice.domain.convertor.WmsStockCheckFlowConvertor;
import com.rome.stock.innerservice.domain.entity.RealWarehouseE;
import com.rome.stock.innerservice.domain.entity.WmsStockCheckFlowE;
import com.rome.stock.innerservice.domain.repository.RealWarehouseRepository;
import com.rome.stock.innerservice.domain.repository.RealWarehouseWmsConfigRepository;
import com.rome.stock.innerservice.domain.repository.WmsStockCheckFlowRepository;
import com.rome.stock.innerservice.domain.repository.elasticsearch.EsWmsStockCheckFlowRepository;
import com.rome.stock.innerservice.domain.service.RealWarehouseService;
import com.rome.stock.innerservice.domain.service.WmsStockCheckService;
import com.rome.stock.innerservice.facade.StockToolFacade;
import com.rome.stock.innerservice.infrastructure.dataobject.RealWarehouseStockDO;
import com.rome.stock.innerservice.infrastructure.dataobject.RealWarehouseWmsConfigDO;
import com.rome.stock.innerservice.infrastructure.dataobject.elasticsearch.EsWmsStockCheckFlowDO;
import com.rome.stock.innerservice.infrastructure.mapper.RealWarehouseStockMapper;
import com.rome.stock.innerservice.infrastructure.redis.RealWarehouseWmsRedis;
import com.rome.stock.innerservice.remote.item.dto.CombineSkuInfoResultDTO;
import com.rome.stock.innerservice.remote.item.dto.SkuInfoExtDTO;
import com.rome.stock.innerservice.remote.item.facade.SkuFacade;
import com.rome.stock.innerservice.remote.venus.facade.VenusStockFacade;
import com.rome.stock.innerservice.template.inactive.WmsStockCheckFlowInactiveTemplate;
import com.rome.stock.wms.constants.WarehouseWmsConfigEnum;
import com.rome.stock.wms.dto.response.StockQueryResponseItem;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.time.DateUtils;
import org.apache.commons.lang3.StringUtils;
import org.elasticsearch.action.search.SearchType;
import org.elasticsearch.index.IndexNotFoundException;
import org.elasticsearch.index.query.BoolQueryBuilder;
import org.elasticsearch.index.query.QueryBuilders;
import org.elasticsearch.index.query.RangeQueryBuilder;
import org.elasticsearch.search.sort.SortBuilders;
import org.elasticsearch.search.sort.SortOrder;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.elasticsearch.core.query.NativeSearchQuery;
import org.springframework.data.elasticsearch.core.query.NativeSearchQueryBuilder;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.concurrent.*;
import java.util.function.Function;
import java.util.stream.Collectors;

import static java.math.BigDecimal.ROUND_DOWN;

/**
 * wms库存核对
 * <AUTHOR>
 * @since 2019年7月7日 下午8:50:57
 */
@Slf4j
@Service
public class WmsStockCheckServiceImpl implements WmsStockCheckService {

	@Resource
	private WmsStockCheckFlowRepository wmsStockCheckFlowRepository;
	
	@Autowired
    private EntityFactory entityFactory;
	
	@Autowired
	private RealWarehouseWmsConfigRepository realWarehouseWmsConfigRepository; 
	
	@Autowired
	private RealWarehouseRepository realWarehouseRepository;
	
	@Resource
    private RealWarehouseStockMapper realWarehouseStockMapper;
	
	@Resource(name = "coreStockTask")
	private ThreadPoolTaskExecutor coreStockTask;
	
	@Resource
    private SkuFacade skuFacade;

	@Resource
	private WmsStockCheckFlowConvertor wmsStockCheckFlowConvertor;

	@Resource
	private RealWarehouseService realWarehouseService;

	@Resource
	private RealWarehouseWmsRedis realWarehouseWmsRedis;
	
	@Resource
	private RedisUtil redisUtil;

	@Resource
	private EsWmsStockCheckFlowRepository esWmsStockCheckFlowRepository;

	@Resource
    private EsWmsStockCheckFlowConvertor esWmsStockCheckFlowConvertor;

	@Autowired
    private VenusStockFacade venusStockFacade;

	/**
	 * wms库存核对-现在是大福和SAP系统仓
	 */
	@Override
	public void wmsStockCheckJob() {
		log.info("开始,wms库存核对");
		coreStockTask.submit(new Runnable() {
			@SuppressWarnings("rawtypes")
			@Override
			public void run() {
				final Map<String, SkuInfoExtDTO> skuInfoMap = new ConcurrentHashMap<String, SkuInfoExtDTO>(4000);
				int current = 1;
				// 每页大小 
				final int pageSize = 1000;
				List<Future> futures = new ArrayList<>(100);
				Future future;
				List<Integer> wmsCodes = WarehouseWmsConfigEnum.getSupportWmsByAllType();
				wmsCodes.add(WarehouseWmsConfigEnum.MM.getType()); // 如果是 中台虚拟仓库MM，库存核对时，只核对中台与sap库存
				List<RealWarehouseE> realWarehouseEs;
				PageHelper.startPage(current, pageSize, false);
				List<Long> list = realWarehouseWmsConfigRepository.getRealWarehouseWmsConfigByWmsCodes(wmsCodes);
				int i;
				while(list != null && list.size() > 0) {
					realWarehouseEs = realWarehouseRepository.queryWarehouseByIds(list);
					if(realWarehouseEs != null && realWarehouseEs.size() > 0) {
						for(RealWarehouseE realWarehouseE : realWarehouseEs) {
							future = coreStockTask.submit(new Runnable() {
								@Override
								public void run() {
									wmsStockCheckByWarehouse(realWarehouseE, skuInfoMap);
								}});
							futures.add(future);
						}
					}
					current++;
					PageHelper.startPage(current, pageSize, false);
					list = realWarehouseWmsConfigRepository.getRealWarehouseWmsConfigByWmsCodes(wmsCodes);
					for(i = 0; i < futures.size(); i++) {
						if(futures.get(i).isDone()) {
							futures.remove(i);
							i--;
						}
					}
				}
				// 等待完成
				for(Future future2 : futures) {
					try {
						future2.get();
					} catch (Exception e) {
						log.error("wms库存核对,等队列完成,出错{}",e);
					}
				}
				futures.clear();
				futures = null;
				log.info("结束,wms库存核对");
			}
		});
	}
	
	/**
	 * wms库存核对-仓库
	 * @param realWarehouseE
	 */
	private void wmsStockCheckByWarehouse(RealWarehouseE realWarehouseE, Map<String, SkuInfoExtDTO> skuInfoMap) {
		int count = 0;
		int countSuccess = 0;
		boolean isLock = false;
		StockCacheKeyEnum cacheKeyEnum = StockCacheKeyEnum.WMS_STOCK_CHECK;
		// 核对，不一致的skuCode
		Set<String> notEqualsSkuCodeSet = new HashSet<>(16);
		String wmsDesc = null;
		try {
			isLock = redisUtil.lock(cacheKeyEnum.getKeyLock() + realWarehouseE.getId(), "1", cacheKeyEnum.getExpireTimeLock());
			if(isLock == false) {
				throw new RomeException(ResCode.STOCK_ERROR_1001, "仓库正在同步中,factoryCode=" + realWarehouseE.getFactoryCode() + ",outCode=" + realWarehouseE.getRealWarehouseOutCode()); 
			}
			Integer wmsCode = realWarehouseWmsConfigRepository.queryWmsConfigById(realWarehouseE.getId());
			if (wmsCode == null){
	            log.error("wms库存核对,出错,暂未查询到实仓与wms的配置信息,实仓id="+ realWarehouseE.getId());
	        }
			// 如果是 中台虚拟仓库MM，库存核对时，只核对中台与sap库存
			WarehouseWmsConfigEnum wmsConfigVO = WarehouseWmsConfigEnum.getSupportWmsByType(wmsCode);
			if(wmsConfigVO == null && WarehouseWmsConfigEnum.MM.getType().equals(wmsCode)) {
				wmsConfigVO = WarehouseWmsConfigEnum.MM;
			}
			if(wmsConfigVO == null) {
				throw new RomeException(ResCode.STOCK_ERROR_1003, "暂不支持此仓库核对仓id=" + realWarehouseE.getId());	
			}
			wmsDesc = wmsConfigVO.getDesc();
			int current = 1;
			// 每页大小 
			final int pageSize = 1000;
			List<String> skuCodes = new ArrayList<>(1000);
			PageHelper.startPage(current, pageSize, false);
			// 查询库存
			List<RealWarehouseStockDO> list = realWarehouseStockMapper.querySkuIdByWhId(realWarehouseE.getId());
			List<StockQueryResponseItem> stockQueryItems;
			String type = null;
			Calendar cal = Calendar.getInstance();
			cal.set(Calendar.HOUR_OF_DAY, 6);
			cal.set(Calendar.MINUTE, 0);
			cal.set(Calendar.SECOND, 0);
			cal.set(Calendar.MILLISECOND, 0);
			Date now = cal.getTime();
			while(list != null && list.size() > 0) {
				if(type == null) {
					type = "A";
				}else {
					type = "I";
				}
				count += list.size();
				skuCodes.clear();
				for(RealWarehouseStockDO dto : list) {
					if(dto.getSkuCode() != null) {
						skuCodes.add(dto.getSkuCode());
					}
				}
				// 获取wms的库存
				if(skuCodes.size() > 0) {
					// 大幅仓
					List<WmsStockCheckFlowE> wmsStockCheckFlowEs = null;
//					Date now = new Date();
					// 获取支持的wms
					if(wmsConfigVO != null) {
						if(wmsConfigVO != WarehouseWmsConfigEnum.SAP) {
							wmsStockCheckFlowEs = new ArrayList<>(skuCodes.size() * 2);
						}else {
							wmsStockCheckFlowEs = new ArrayList<>(skuCodes.size());
						}
						// mm仓是中台自己的
						if(wmsConfigVO != WarehouseWmsConfigEnum.MM) {
							try {
								stockQueryItems = StockToolFacade.stockQuery(realWarehouseE, type, skuInfoMap, wmsConfigVO.getType() + "", skuCodes, true, 2);
								mergeToWmsStockCheckFlowE(list, stockQueryItems, wmsStockCheckFlowEs, wmsConfigVO, (byte)0, now);
							} catch (Exception e) {
								log.error("wms库存核对,出错," + wmsConfigVO.getDesc() + "仓接口异常,{}", e);
							}
						}
						
						// 如果不是sap仓需要核对sap库存情况
						if(wmsConfigVO != WarehouseWmsConfigEnum.SAP) {
							try {
//								stockQueryItems = StockToolFacade.stockQuery(realWarehouseE.getRealWarehouseOutCode(), realWarehouseE.getFactoryCode(), type, skuInfoMap, WarehouseWmsConfigEnum.SAP.getType() + "", skuCodes, true, false);
								stockQueryItems = venusStockFacade.stockQueryByWmsCheck(realWarehouseE.getRealWarehouseOutCode(), realWarehouseE.getFactoryCode(), skuCodes);
								mergeToWmsStockCheckFlowE(list, stockQueryItems, wmsStockCheckFlowEs, WarehouseWmsConfigEnum.SAP, (byte)1, now);
								if(wmsConfigVO == WarehouseWmsConfigEnum.MM) {
									// mm仓 wms字段就是中台的
									mergeToWmsStockCheckFlowEByMM(list, wmsStockCheckFlowEs, wmsConfigVO, (byte)0, now);
								}
							} catch (Exception e) {
								log.error("wms库存核对,出错,SAP仓接口异常,{}", e);
							}
						}
						
					}
					if(wmsStockCheckFlowEs != null && wmsStockCheckFlowEs.size() > 0) {
						checkNotEqualsSkuCode(wmsStockCheckFlowEs, skuInfoMap, notEqualsSkuCodeSet);
						WmsStockCheckService wmsStockCheckService = SpringBeanUtil.getBean(WmsStockCheckService.class);
//						wmsStockCheckService.batchInsert(wmsStockCheckFlowEs);
						wmsStockCheckService.batchUpdateOrInsert(wmsStockCheckFlowEs, queryWmsStockCheckBySync(realWarehouseE.getId()));
						countSuccess += wmsStockCheckFlowEs.size();
					}
				}
				current++;
				PageHelper.startPage(current, pageSize, false);
				list = realWarehouseStockMapper.querySkuIdByWhId(realWarehouseE.getId());
			}
		} catch (RomeException e) {
			log.error("wms库存核对,出错,总数={},有效总数={}:实仓id={}{}",count, countSuccess, realWarehouseE.getId(), wmsDesc, e);
			throw e;
		}catch (Exception e) {
			log.error("wms库存核对,出错,总数={},有效总数={}:实仓id={}{}",count, countSuccess, realWarehouseE.getId(), wmsDesc, e);
			throw new RomeException(ResCode.STOCK_ERROR_1003, e.getMessage());
		} finally {
			log.info("wms库存核对,成功,realWarehouseId={},总数={},有效总数={}", realWarehouseE.getId(), count, countSuccess);
			if(isLock) {
				redisUtil.unLock(cacheKeyEnum.getKeyLock() + realWarehouseE.getId(), "1");
			}
			// wms批次库存核对，不一致
			if(notEqualsSkuCodeSet.size() > 0) {
				log.warn("wms库存核对,不一致,【{}】,{},不一致sku个数：{},明细为:{}", wmsDesc, realWarehouseE.getRealWarehouseCode(), notEqualsSkuCodeSet.size(), JSON.toJSONString(notEqualsSkuCodeSet));
			}
		}
	}
	
	/**
	 * 合并处理
	 * @param list
	 * @param stockQueryItems
	 * @param wmsStockCheckFlowEs
	 * @param wmsConfigVO
	 * @param extWms
	 * @param now
	 */
	private void mergeToWmsStockCheckFlowE(List<RealWarehouseStockDO> list, List<StockQueryResponseItem> stockQueryItems, List<WmsStockCheckFlowE> wmsStockCheckFlowEs, WarehouseWmsConfigEnum wmsConfigVO, Byte extWms, Date now) {
		Map<String, StockQueryResponseItem> map = RomeCollectionUtil.listforMap(stockQueryItems, "itemCode");
		StockQueryResponseItem temp,temp2 = null;
		for(RealWarehouseStockDO dto : list) {
			temp = map.get(dto.getSkuCode());
			if(temp == null) {
				if(temp2 == null) {
					temp2 = new StockQueryResponseItem();
				}
				temp = temp2;
			}
			wmsStockCheckFlowEs.add(toWmsStockCheckFlowE(dto, temp, wmsConfigVO, extWms, now));
		}
	}
	
	/**
	 * 合并处理-MM
	 * @param list
	 * @param wmsStockCheckFlowEs
	 * @param wmsConfigVO
	 * @param extWms
	 * @param now
	 */
	private void mergeToWmsStockCheckFlowEByMM(List<RealWarehouseStockDO> list, List<WmsStockCheckFlowE> wmsStockCheckFlowEs, WarehouseWmsConfigEnum wmsConfigVO, Byte extWms, Date now) {
		for(RealWarehouseStockDO dto : list) {
			wmsStockCheckFlowEs.add(toWmsStockCheckFlowEByMM(dto, wmsConfigVO, extWms, now));
		}
	}
	
	/**
	 * 查询，同步时
	 * @param realWarehouseId
	 * @return
	 */
	private Map<String, WmsStockCheckFlowE> queryWmsStockCheckBySync(Long realWarehouseId) {
		List<WmsStockCheckFlowE> list = wmsStockCheckFlowRepository.queryWmsStockCheckBySync(getQueryParamSync(realWarehouseId));
		if(list == null || list.size() == 0) {
			return Collections.EMPTY_MAP;
		}
		Map<String, WmsStockCheckFlowE> map = new HashMap<String, WmsStockCheckFlowE>(list.size() * 100 /75);
		for(WmsStockCheckFlowE dto : list) {
			map.put(dto.getSkuId() + "_" + dto.getWmsCode(), dto);
		}
		return map;
	}
	
	/**
	 * 同步时，获取请求参数
	 * @param realWarehouseId
	 * @return
	 */
	private WmsStockCheckFlowE getQueryParamSync(Long realWarehouseId) {
		try {
			WmsStockCheckFlowE flowE = new WmsStockCheckFlowE();
			//设置查询时间,若时间为空,默认采用当前时间
	        Date startTime;
	        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
	        String s = sdf.format(new Date());
	        startTime =  sdf.parse(s);
			Date endTime = DateUtils.addDays(startTime, 1);
			flowE.setStartTime(startTime);
			flowE.setEndTime(endTime);
			List<Long> queryWarehouseIds = new ArrayList<>(1);
			queryWarehouseIds.add(realWarehouseId);
			flowE.setRealWarehouseIds(queryWarehouseIds);
			return flowE;
		} catch (Exception e) {
			throw new RomeException(ResCode.STOCK_ERROR_1003, e.getMessage());
		}
	}
	
	@Override
	@Transactional(rollbackFor = Exception.class)
	public void batchInsert(List<WmsStockCheckFlowE> checkFlowEs) {
		wmsStockCheckFlowRepository.batchInsert(checkFlowEs);
	}
	
	@Override
	@Transactional(rollbackFor = Exception.class)
	public void batchUpdateOrInsert(List<WmsStockCheckFlowE> checkFlowEs, Map<String, WmsStockCheckFlowE> existMap) {
		List<WmsStockCheckFlowE> updateList = new ArrayList<WmsStockCheckFlowE>();
		WmsStockCheckFlowE temp,temp2;
		for (int i = checkFlowEs.size() - 1; i >= 0; i--) {
			temp2 = checkFlowEs.get(i);
			temp = existMap.get(temp2.getSkuId() + "_" + temp2.getWmsCode());
			if(temp != null) {
				temp2.setId(temp.getId());
				updateList.add(temp2);
				checkFlowEs.remove(i);
			}
		}
		if(updateList.size() > 0) {
			wmsStockCheckFlowRepository.batchUpdateBySync(updateList);
		}
        if(checkFlowEs.size() > 0) {
        	wmsStockCheckFlowRepository.batchInsert(checkFlowEs);
		}
	}
	
	/**
	 * toEntity
	 * @param stockDTO
	 * @param item
	 * @return
	 */
	private WmsStockCheckFlowE toWmsStockCheckFlowE(RealWarehouseStockDO stockDTO, StockQueryResponseItem item, WarehouseWmsConfigEnum wmsConfigVO, Byte extWms, Date now) {
		if(item.getRealQty() == null) {
			item.setRealQty(BigDecimal.ZERO);
		}
		if(item.getLockQty() == null) {
			item.setLockQty(BigDecimal.ZERO);
		}
		if(item.getQualityQty() == null) {
			item.setQualityQty(BigDecimal.ZERO);
		}
		if(item.getUnqualifiedQty() == null) {
			item.setUnqualifiedQty(BigDecimal.ZERO);
		}
		WmsStockCheckFlowE checkFlowE = entityFactory.createEntity(WmsStockCheckFlowE.class);
		checkFlowE.setRealWarehouseId(stockDTO.getRealWarehouseId());
		checkFlowE.setWmsCode(wmsConfigVO.getType());
		checkFlowE.setSkuId(stockDTO.getSkuId());
		checkFlowE.setSkuCode(stockDTO.getSkuCode());
		checkFlowE.setRealQty(stockDTO.getRealQty());
		checkFlowE.setLockQty(stockDTO.getLockQty());
		checkFlowE.setQualityQty(stockDTO.getQualityQty());
		checkFlowE.setUnqualifiedQty(stockDTO.getUnqualifiedQty());
		checkFlowE.setWmsRealQty(item.getRealQty());
		checkFlowE.setWmsLockQty(item.getLockQty());
		checkFlowE.setWmsQualityQty(item.getQualityQty());
		checkFlowE.setWmsUnqualifiedQty(item.getUnqualifiedQty());
		checkFlowE.setType(1);
		checkFlowE.setSourceSystem(wmsConfigVO.getDesc());
		checkFlowE.setUnitCode(item.getUnit());
		checkFlowE.setExtWms(extWms);
		checkFlowE.setCreateTime(now);
		addQualityUnqualifiedQtyQtyToSap(checkFlowE, wmsConfigVO);// sap只有总库存630特殊处理
		return checkFlowE;
	}
	
	/**
	 * sap只有总库存（sap真实库存=真实库存+质检库存+不合格库存） 
	 * 对比时，中台的待质检和不合格的，加到sap待质检和不合格的库存上去，
	 * 然后sap真实库存减去中台的待质检和不合格的库存，其他查询条件不变
	 * 原因为630的变化，待质检库存，直接过账为合格库存
	 * @param checkFlowE
	 * @param wmsConfigVO
	 */
	private void addQualityUnqualifiedQtyQtyToSap(WmsStockCheckFlowE checkFlowE, WarehouseWmsConfigEnum wmsConfigVO) {
		if(wmsConfigVO == WarehouseWmsConfigEnum.SAP) {
			checkFlowE.setWmsQualityQty(checkFlowE.getWmsQualityQty().add(checkFlowE.getQualityQty()));
			checkFlowE.setWmsUnqualifiedQty(checkFlowE.getWmsUnqualifiedQty().add(checkFlowE.getUnqualifiedQty()));
			checkFlowE.setWmsRealQty(checkFlowE.getWmsRealQty().subtract(checkFlowE.getQualityQty()).subtract(checkFlowE.getUnqualifiedQty()));
//			checkFlowE.setSourceSystem(wmsConfigVO.getDesc() + "-只有总库存");
			checkFlowE.setSourceSystem("财务中台-只有总库存");
		}
	}
	
	/**
	 * toEntity MM仓的
	 * @param stockDTO
	 * @param item
	 * @return
	 */
	private WmsStockCheckFlowE toWmsStockCheckFlowEByMM(RealWarehouseStockDO stockDTO, WarehouseWmsConfigEnum wmsConfigVO, Byte extWms, Date now) {
		WmsStockCheckFlowE checkFlowE = entityFactory.createEntity(WmsStockCheckFlowE.class);
		checkFlowE.setRealWarehouseId(stockDTO.getRealWarehouseId());
		checkFlowE.setWmsCode(wmsConfigVO.getType());
		checkFlowE.setSkuId(stockDTO.getSkuId());
		checkFlowE.setSkuCode(stockDTO.getSkuCode());
		checkFlowE.setRealQty(stockDTO.getRealQty());
		checkFlowE.setLockQty(stockDTO.getLockQty());
		checkFlowE.setQualityQty(stockDTO.getQualityQty());
		checkFlowE.setUnqualifiedQty(stockDTO.getUnqualifiedQty());
		checkFlowE.setWmsRealQty(stockDTO.getRealQty());
		checkFlowE.setWmsLockQty(stockDTO.getLockQty());
		checkFlowE.setWmsQualityQty(stockDTO.getQualityQty());
		checkFlowE.setWmsUnqualifiedQty(stockDTO.getUnqualifiedQty());
		checkFlowE.setType(1);
		checkFlowE.setSourceSystem(wmsConfigVO.getDesc());
//		checkFlowE.setUnitCode(item.getUnit());
		checkFlowE.setExtWms(extWms);
		checkFlowE.setCreateTime(now);
		return checkFlowE;
	}

	/**
	 * 检查核对，不一致的
	 * @param wmsBatchCheckFlows
	 * @param skuInfoMap
	 * @param notEqualsSkuCodeSet
	 */
	private void checkNotEqualsSkuCode(List<WmsStockCheckFlowE> wmsBatchCheckFlows, Map<String, SkuInfoExtDTO> skuInfoMap, Set<String> notEqualsSkuCodeSet) {
		if(wmsBatchCheckFlows == null || skuInfoMap == null || notEqualsSkuCodeSet == null) {
			return;
		}
		// mm仓不核对，ext为1额外仓，即财务中台，不核对
		// skuInfoMap 不存在，重查一下，商品中心数据。有可能wms里没有库存记录会造成skuInfoMap不存在
		List<String> skuCodes = new ArrayList<>();
		Byte extWms = (byte)0;
		for(WmsStockCheckFlowE dto : wmsBatchCheckFlows) {
			if(!extWms.equals(dto.getExtWms())) {
				continue;
			}
			if(WarehouseWmsConfigEnum.MM.getType().equals(dto.getWmsCode())) {
				continue;
			}
			if (!skuInfoMap.containsKey(dto.getSkuCode())) {
				skuCodes.add(dto.getSkuCode());
			}
		}
		if(skuCodes.size() > 0) {
			List<SkuInfoExtDTO> skuInfoExtDTOs = skuFacade.skusBySkuCode(skuCodes);
			if(skuInfoExtDTOs != null && skuInfoExtDTOs.size() > 0) {
				for(SkuInfoExtDTO dto : skuInfoExtDTOs) {
					skuInfoMap.put(dto.getSkuCode(), dto);
				}
			}
		}
		SkuInfoExtDTO skuInfo;
		for(WmsStockCheckFlowE dto : wmsBatchCheckFlows) {
			if(!extWms.equals(dto.getExtWms())) {
				continue;
			}
			if(WarehouseWmsConfigEnum.MM.getType().equals(dto.getWmsCode())) {
				continue;
			}
			skuInfo = skuInfoMap.get(dto.getSkuCode());
			if(skuInfo != null && "Z016".equals(skuInfo.getSpuType())) {
				continue;
			}
			if(!equalsFlagQty(dto.getRealQty(), dto.getWmsRealQty()) || !equalsFlagQty(dto.getQualityQty(), dto.getWmsQualityQty()) || !equalsFlagQty(dto.getUnqualifiedQty(), dto.getWmsUnqualifiedQty())) {
				notEqualsSkuCodeSet.add(dto.getSkuCode());
			}
		}

	}

	/**
	 * 相同比较
	 * 相同为true，不相同为false
	 * @param qty
	 * @param wmsQty
	 * @return
	 */
	private boolean equalsFlagQty(BigDecimal qty, BigDecimal wmsQty) {
		if(qty == null || wmsQty == null) {
			// 全相同
			return true;
		}
		// 中台<>wms 相同
		if(qty.compareTo(wmsQty) == 0) {
			// 全相同
			return true;
		}
		// 不相同
		return false;
	}

	/**
	 * 根据条件查询wms库存核对表
	 * @param paramDto
	 * @return
	 */
	@Override
	public PageInfo<WmsStockCheckFlowDTO> queryWmsStockCheckByCondition(WmsStockCheckFlowDTO paramDto) throws Exception{
		WmsStockCheckFlowE flowE = wmsStockCheckFlowConvertor.dtoToEntity(paramDto);

		//设置查询时间,若时间为空,默认采用当前时间
        Date startTime;
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
        if(null != flowE.getCreateTime()){
            startTime = flowE.getCreateTime();
			String s = sdf.format(startTime);
			startTime =  sdf.parse(s);
        }else {
            String s = sdf.format(new Date());
            startTime =  sdf.parse(s);
        }

		Date endTime = DateUtils.addDays(startTime, 1);
		flowE.setStartTime(startTime);
		flowE.setEndTime(endTime);

		List<Long> queryWarehouseIds = new ArrayList<>();
		if(null != paramDto.getFactoryCode() && null != paramDto.getRealWarehouseId()){
            queryWarehouseIds.add(paramDto.getRealWarehouseId());
        }else if(null != paramDto.getFactoryCode() && null == paramDto.getRealWarehouseId()){
		    //根据工厂code查询仓库
            List<RealWarehouse> realWarehouses = queryWmsRealWarehouseByFactoryCode(paramDto.getFactoryCode());
            queryWarehouseIds = RomeCollectionUtil.getValueList(realWarehouses, "id");
        }
		flowE.setRealWarehouseIds(queryWarehouseIds);

		//开启分页
		Page page = PageHelper.startPage(paramDto.getPageIndex(), paramDto.getPageSize());
		List<WmsStockCheckFlowE> wmsStockCheckFlowES = wmsStockCheckFlowRepository.queryWmsStockCheckByCondition(flowE);
		List<WmsStockCheckFlowDTO> wmsStockCheckFlowDTOS = null;
		if(wmsStockCheckFlowES != null && wmsStockCheckFlowES.size() > 0) {
			wmsStockCheckFlowDTOS = wmsStockCheckFlowConvertor.entityToDto(wmsStockCheckFlowES);
		}else {
			// 没有数据时，去查历史数据
			page.setPageNum(paramDto.getPageIndex());
			page.setPageSize(paramDto.getPageSize());
			wmsStockCheckFlowDTOS = queryWmsStockCheckByConditionHistory(flowE, page);
		}

		//根据仓库id查询仓库信息
		List<Long> warehouseIds = null;
		if(wmsStockCheckFlowDTOS != null) {
			warehouseIds = new ArrayList<>();
			for(WmsStockCheckFlowDTO dto : wmsStockCheckFlowDTOS) {
				if(dto.getRealWarehouseId() != null) {
					if(!warehouseIds.contains(dto.getRealWarehouseId())) {
						warehouseIds.add(dto.getRealWarehouseId());
					}
				}
			}
		}
		//是否仓店一体的查询差异,目前参数虽然是集合,但是页面限制了只能查一个仓
		boolean specialFlag = false;
		if(warehouseIds != null && warehouseIds.size() > 0) {
			List<RealWarehouseE> warehouseES = realWarehouseRepository.queryWarehouseByIds(warehouseIds);
			Map<Long, RealWarehouseE> warehouseEMap = RomeCollectionUtil.listforMap(warehouseES, "id");
			//遍历查询结果,设置仓库名称等
			for (WmsStockCheckFlowDTO flowDTO:wmsStockCheckFlowDTOS) {
				if(warehouseEMap.containsKey(flowDTO.getRealWarehouseId())){
					RealWarehouseE realWarehouseE = warehouseEMap.get(flowDTO.getRealWarehouseId());
					if(Objects.equals(WarehouseStoreIdentiEnum.WAREHOUSESTORE.getCode(), realWarehouseE.getWarehouseStoreIdenti())){
						specialFlag = true;
					}
					flowDTO.setFactoryCode(realWarehouseE.getFactoryCode());
					flowDTO.setRealWarehouseCode(realWarehouseE.getRealWarehouseCode());
					flowDTO.setRealWarehouseName(realWarehouseE.getRealWarehouseName());
					flowDTO.setAvailableQty(flowDTO.getRealQty().subtract(flowDTO.getLockQty()));
				}
			}
		}
		List<String> skuCodes = wmsStockCheckFlowDTOS.stream().filter(v -> StringUtils.isNotBlank(v.getSkuCode())).map(WmsStockCheckFlowDTO :: getSkuCode).distinct().collect(Collectors.toList());
		if (specialFlag) {
			List<CombineSkuInfoResultDTO> combineSkuInfoList = skuFacade.queryExtCombineInfoByCodes(skuCodes);
			Map<String, CombineSkuInfoResultDTO> combineParentMap = combineSkuInfoList.stream().filter(v -> CombineSkuTypeVO.LARGE_COMBINE.getCombineType().equals(v.getCombineType())).collect(Collectors.toMap(CombineSkuInfoResultDTO::getSkuCode, Function.identity(), (v1, v2) -> v2));
			//7字码库存
			List<WmsStockCheckFlowDTO> sevenStockCheckFlowList =wmsStockCheckFlowDTOS.stream().filter(dto -> combineParentMap.containsKey(dto.getSkuCode())).collect(Collectors.toList());
			//非7字码库存
			List<WmsStockCheckFlowDTO> otherFlowList =wmsStockCheckFlowDTOS.stream().filter(dto -> !combineParentMap.containsKey(dto.getSkuCode())).collect(Collectors.toList());
			//如果是量贩店查询库存,需要将2字码库存转换为7字码,并将2字码剔除
			List<CombineSkuInfoResultDTO> combineSkuInfoResultDTOList = skuFacade.queryCombineInfoSkuBySkuCodes(skuCodes);
			if (CollectionUtils.isNotEmpty(combineSkuInfoResultDTOList)) {
				Map<String, CombineSkuInfoResultDTO> combinMap = combineSkuInfoResultDTOList.stream().filter(v -> CombineSkuTypeVO.LARGE_COMBINE.getCombineType().equals(v.getCombineType())).collect(Collectors.toMap(CombineSkuInfoResultDTO::getCombineSkuCode, Function.identity(), (v1, v2) -> v2));
				for (WmsStockCheckFlowDTO wmsFlowDTO : otherFlowList) {
					if (!combinMap.containsKey(wmsFlowDTO.getSkuCode())) {
						continue;
					}
					CombineSkuInfoResultDTO dto = combinMap.get(wmsFlowDTO.getSkuCode());
					wmsFlowDTO.setSkuCode(dto.getSkuCode());
					wmsFlowDTO.setSkuId(dto.getSkuId());
					wmsFlowDTO.setSkuName(dto.getSkuName());
					wmsFlowDTO.setRealQty(wmsFlowDTO.getRealQty().divide(dto.getNum(), StockCoreConsts.DECIMAL_POINT_NUM, ROUND_DOWN));
					wmsFlowDTO.setAvailableQty(wmsFlowDTO.getAvailableQty().divide(dto.getNum(),StockCoreConsts.DECIMAL_POINT_NUM, ROUND_DOWN));
					wmsFlowDTO.setLockQty(wmsFlowDTO.getLockQty().divide(dto.getNum(),StockCoreConsts.DECIMAL_POINT_NUM, ROUND_DOWN));
					wmsFlowDTO.setUnqualifiedQty(wmsFlowDTO.getUnqualifiedQty().divide(dto.getNum(),StockCoreConsts.DECIMAL_POINT_NUM, ROUND_DOWN));
					wmsFlowDTO.setQualityQty(wmsFlowDTO.getQualityQty().divide(dto.getNum(),StockCoreConsts.DECIMAL_POINT_NUM, ROUND_DOWN));
					wmsFlowDTO.setWmsRealQty(wmsFlowDTO.getWmsRealQty().divide(dto.getNum(),StockCoreConsts.DECIMAL_POINT_NUM, ROUND_DOWN));
					wmsFlowDTO.setWmsUnqualifiedQty(wmsFlowDTO.getWmsUnqualifiedQty().divide(dto.getNum(),StockCoreConsts.DECIMAL_POINT_NUM, ROUND_DOWN));
					wmsFlowDTO.setWmsQualityQty(wmsFlowDTO.getWmsQualityQty().divide(dto.getNum(),StockCoreConsts.DECIMAL_POINT_NUM, ROUND_DOWN));
					wmsFlowDTO.setExtWmsRealQty(wmsFlowDTO.getExtWmsRealQty().divide(dto.getNum(),StockCoreConsts.DECIMAL_POINT_NUM, ROUND_DOWN));
					wmsFlowDTO.setExtWmsUnqualifiedQty(wmsFlowDTO.getExtWmsUnqualifiedQty().divide(dto.getNum(),StockCoreConsts.DECIMAL_POINT_NUM, ROUND_DOWN));
					wmsFlowDTO.setExtWmsQualityQty(wmsFlowDTO.getExtWmsQualityQty().divide(dto.getNum(),StockCoreConsts.DECIMAL_POINT_NUM, ROUND_DOWN));
				}
				//将sevenStockCheckFlowList和otherFlowList的各种库存按照skuCode进行合并
				List<WmsStockCheckFlowDTO> mergeList = new ArrayList<>();
				Map<String, WmsStockCheckFlowDTO> mergeMap = new HashMap<>();
				// 先处理7字码库存
				for (WmsStockCheckFlowDTO dto : sevenStockCheckFlowList) {
					mergeMap.put(dto.getSkuCode(), dto);
				}
				// 处理非7字码库存,合并相同skuCode的库存
				for (WmsStockCheckFlowDTO dto : otherFlowList) {
					if (mergeMap.containsKey(dto.getSkuCode())) {
						WmsStockCheckFlowDTO existDto = mergeMap.get(dto.getSkuCode());
						// 合并各种库存数量
						existDto.setRealQty(existDto.getRealQty().add(dto.getRealQty()));
						existDto.setAvailableQty(existDto.getAvailableQty().add(dto.getAvailableQty()));
						existDto.setLockQty(existDto.getLockQty().add(dto.getLockQty()));
						existDto.setUnqualifiedQty(existDto.getUnqualifiedQty().add(dto.getUnqualifiedQty()));
						existDto.setQualityQty(existDto.getQualityQty().add(dto.getQualityQty()));
						existDto.setWmsRealQty(existDto.getWmsRealQty().add(dto.getWmsRealQty()));
						existDto.setWmsUnqualifiedQty(existDto.getWmsUnqualifiedQty().add(dto.getWmsUnqualifiedQty()));
						existDto.setWmsQualityQty(existDto.getWmsQualityQty().add(dto.getWmsQualityQty()));
						existDto.setExtWmsRealQty(existDto.getExtWmsRealQty().add(dto.getExtWmsRealQty()));
						existDto.setExtWmsUnqualifiedQty(existDto.getExtWmsUnqualifiedQty().add(dto.getExtWmsUnqualifiedQty()));
						existDto.setExtWmsQualityQty(existDto.getExtWmsQualityQty().add(dto.getExtWmsQualityQty()));
					} else {
						mergeMap.put(dto.getSkuCode(), dto);
					}
				}
				// 将合并后的结果放入mergeList
				mergeList.addAll(mergeMap.values());
				wmsStockCheckFlowDTOS = mergeList;
			}
		}

		//根据skuCodes获取商品信息
		List<SkuInfoExtDTO> skuInfoExtDTOList = skuFacade.skusBySkuCode(skuCodes);
		Map<String, SkuInfoExtDTO> skuInfoExtDTOMap = skuInfoExtDTOList.stream().distinct().collect(Collectors.toMap(SkuInfoExtDTO :: getSkuCode, Function.identity(), (v1, v2) -> v1));
		wmsStockCheckFlowDTOS.forEach(item -> {
			if(skuInfoExtDTOMap.containsKey(item.getSkuCode())) {
				item.setSkuName(skuInfoExtDTOMap.get(item.getSkuCode()).getName());
				item.setUnit(skuInfoExtDTOMap.get(item.getSkuCode()).getSpuUnitName());
			}
		});
		
		PageInfo<WmsStockCheckFlowDTO> pageList = new PageInfo<>(wmsStockCheckFlowDTOS);
		pageList.setTotal(page.getTotal());
		if(0 == page.getTotal()) {
			return pageList;
		}
		return pageList;
	}

	/**
	 * 根据工厂code查询仓库信息
	 * @param factoryCode
	 * @return
	 */
	@Override
	public List<RealWarehouse> queryWmsRealWarehouseByFactoryCode(String factoryCode) {
		Map<Integer, String> realWarehouseTypeList = RealWarehouseTypeVO.getRealWarehouseTypeList();
		realWarehouseTypeList.remove(RealWarehouseTypeVO.RW_TYPE_1.getType());
		Set<Integer> typeSet = realWarehouseTypeList.keySet();
		List<Integer> types = new ArrayList<>(typeSet);

		//根据类型和工厂查询所有仓库
		List<RealWarehouse> warehouses = realWarehouseService.queryRealWarehouseByFactoryCodeAndRWType(factoryCode, types);
		Map<Long, RealWarehouse> warehousesMap = RomeCollectionUtil.listforMap(warehouses, "id");
		warehouses.clear();

		//查wms仓库配置表里的仓库
		List<RealWarehouseWmsConfigDO> doList = realWarehouseWmsRedis.findWmsInformationByIds(new ArrayList<>(warehousesMap.keySet()));
		for (RealWarehouseWmsConfigDO configDO : doList) {
			if(warehousesMap.containsKey(configDO.getRealWarehouseId())){// if(!WarehouseWmsConfigVO.SAP.getType().equals(configDO.getWmsCode()) && warehousesMap.containsKey(configDO.getRealWarehouseId())){
				warehouses.add(warehousesMap.get(configDO.getRealWarehouseId()));
			}
		}
		return warehouses;
	}
	
	/**
	 * 根据仓库Id重新同步wms库存核对表,一般是手动
	 * @param paramDto
	 * @return
	 */
	@Override
	@SuppressWarnings("rawtypes")
	public void wmsStockCheckSync(WmsStockCheckFlowDTO paramDto) {
		List<RealWarehouseWmsConfigDO> configDOs = realWarehouseWmsConfigRepository.getRealWarehouseWmsConfigByVMId(paramDto.getRealWarehouseId());
		if(configDOs != null && configDOs.size() > 0) {
			RealWarehouseE realWarehouseE = realWarehouseRepository.getRealWarehouseById(paramDto.getRealWarehouseId());
			Future future = coreStockTask.submit(new Runnable() {
				@Override
				public void run() {
					final Map<String, SkuInfoExtDTO> skuInfoMap = new ConcurrentHashMap<String, SkuInfoExtDTO>(4000);
					wmsStockCheckByWarehouse(realWarehouseE, skuInfoMap);
				}});
			StockCacheKeyEnum cacheKeyEnum = StockCacheKeyEnum.WMS_STOCK_CHECK;
			try {
				future.get(cacheKeyEnum.getExpireTime(), TimeUnit.SECONDS);
			} catch (InterruptedException e) {
				throw new RomeException(ResCode.STOCK_ERROR_1003, e.getMessage());
			} catch (ExecutionException e) {
				throw new RomeException(ResCode.STOCK_ERROR_1003, (e.getMessage() + "").replaceAll("com.rome.arch.core.exception.RomeException:", ""));
			} catch (TimeoutException e) {
				throw new RomeException(ResCode.STOCK_ERROR_1003, "已加入同步队列中，并且正在同时中，请稍后重新查询");
			}
		}else {
			throw new RomeException(ResCode.STOCK_ERROR_1003, "仓库不在配置表");
		}
		
	}

	/**
	 * 历史数据查询
	 * @param paramDTO
	 * @param page
	 * @return
	 */
	private List<WmsStockCheckFlowDTO> queryWmsStockCheckByConditionHistory(WmsStockCheckFlowE paramDTO, Page page) {
		EsIndexTypeConfig.setIndexNameSuffixByDate("1", paramDTO.getStartTime());
		try {
			// 历史数据迁移时间
			Integer dbSaveDay = SpringBeanUtil.getBean(WmsStockCheckFlowInactiveTemplate.class).getDbSaveDay();
			if(dbSaveDay == null) {
				dbSaveDay = 30;
			}
			Calendar cal = Calendar.getInstance();
			cal.set(Calendar.HOUR_OF_DAY, 0);
			cal.set(Calendar.MINUTE, 0);
			cal.set(Calendar.SECOND, 0);
			cal.set(Calendar.MILLISECOND, 0);
			cal.add(Calendar.DAY_OF_MONTH,-dbSaveDay);
			Date time = cal.getTime();
			// 历史数据迁移时间,还未迁移
			if(time.getTime() < paramDTO.getEndTime().getTime()) {
				return Collections.EMPTY_LIST;
			}
			NativeSearchQuery searchQuery = queryWmsStockCheckByConditionHistoryParam(paramDTO, page);
			org.springframework.data.domain.Page<EsWmsStockCheckFlowDO> resultPage = esWmsStockCheckFlowRepository.search(searchQuery);
			if(resultPage == null || resultPage.getContent() == null || resultPage.getContent().size() == 0) {
				return Collections.EMPTY_LIST;
			}
			List<WmsStockCheckFlowDTO> result = esWmsStockCheckFlowConvertor.esWmsStockCheckFlowDOEntityToDTOList(resultPage.getContent());
			page.setTotal(resultPage.getTotalElements());
			return result;
		} catch (IndexNotFoundException e) {
			log.error("查询历史数据", e);
			return Collections.EMPTY_LIST;
		} finally {
			EsIndexTypeConfig.clearIndexNameSuffix();
		}

	}

	/**
     * 获取请求参数
     * 历史数据
     */
    private NativeSearchQuery queryWmsStockCheckByConditionHistoryParam(WmsStockCheckFlowE condition, Page page){
    	BoolQueryBuilder boolQueryBuilder = QueryBuilders.boolQuery();
        // 条件 仓库编码
        boolQueryBuilder.must(QueryBuilders.termsQuery("realWarehouseId", condition.getRealWarehouseIds()));
        // 条件 类型 0全部相同,1不相同,null为全部数据
        if (condition.getType() != null) {
        	if(condition.getType().intValue() == 0) {
        		boolQueryBuilder.must(QueryBuilders.termQuery("flag", 0));
        	}else {
        		boolQueryBuilder.mustNot(QueryBuilders.termQuery("flag", 0));
        	}
        }
        // 条件 类型 差异查询
        if(condition.getDiffTypeWms() != null || condition.getDiffTypeExtWms() != null) {
        	Set<Integer> listTypeWms = Collections.EMPTY_SET;
        	Set<Integer> listTypeExtWms = Collections.EMPTY_SET;
        	Set<Integer> notInListTypeWms = Collections.EMPTY_SET;
        	Set<Integer> notInListTypeExtWms = Collections.EMPTY_SET;
        	// 中台与wms差异
        	if(condition.getDiffTypeWms() != null) {
        		// 无
        		if(condition.getDiffTypeWms().intValue() == 0) {
        			listTypeWms = getEqualsFlagList(1);
        		} else {
        			notInListTypeWms = getEqualsFlagList(1);
        		}
        	}
        	// 中台与额外sap系统差异
        	if(condition.getDiffTypeExtWms() != null) {
        		// 无
        		if(condition.getDiffTypeExtWms().intValue() == 0) {
        			listTypeExtWms = getEqualsFlagList(2);
        		} else {
        			notInListTypeExtWms = getEqualsFlagList(2);
        		}
        	}
        	// 取交集
        	if(listTypeWms.size() > 0 && listTypeExtWms.size() > 0) {
        		listTypeWms.retainAll(listTypeExtWms);
        	}else {
        		if(listTypeExtWms.size() > 0) {
        			listTypeWms = listTypeExtWms;
        		}
        	}
        	if(listTypeWms.size() > 0) {
        		boolQueryBuilder.must(QueryBuilders.termsQuery("flag", listTypeWms));
        	}
        	// 取并集
        	if(notInListTypeWms.size() > 0 && notInListTypeExtWms.size() > 0) {
        		notInListTypeWms.addAll(notInListTypeExtWms);
        	}else {
        		if(notInListTypeExtWms.size() > 0) {
        			notInListTypeWms = notInListTypeExtWms;
        		}
        	}
        	if(notInListTypeWms.size() > 0) {
        		boolQueryBuilder.mustNot(QueryBuilders.termsQuery("flag", notInListTypeWms));
        	}
        }
        // 条件 skuId
        if(condition.getSkuIds() != null && condition.getSkuIds().size() > 0) {
        	boolQueryBuilder.must(QueryBuilders.termsQuery("skuId", condition.getSkuIds()));
        }
		//时间范围的设定
		if(condition.getStartTime() != null || condition.getEndTime() != null) {
			RangeQueryBuilder rangeQueryBuilder = QueryBuilders.rangeQuery("createTime");
			if(condition.getStartTime() != null) {
				rangeQueryBuilder.from(condition.getStartTime().getTime());
			}
			if(condition.getEndTime() != null) {
				rangeQueryBuilder.to(condition.getEndTime().getTime());
			}
			boolQueryBuilder.must(rangeQueryBuilder);
		}
		Pageable pageable = PageRequest.of(page.getPageNum() - 1, page.getPageSize() > 100000 ? 100000 : page.getPageSize());
		NativeSearchQueryBuilder builder = new NativeSearchQueryBuilder();
		builder.withQuery(boolQueryBuilder);
		builder.withPageable(pageable);
		builder.withSort(SortBuilders.fieldSort("dbId").order(SortOrder.ASC));
		builder.withSearchType(SearchType.QUERY_THEN_FETCH);
		NativeSearchQuery query = builder.build();
		query.setTrackTotalHits(true);
		return query;
    }
    
    /**
     * 获取中台和wms(或sap)相同flag值
     * @param e 种子
     * @return
     */
    private Set<Integer> getEqualsFlagList(int e){
    	Set<Integer> list = new HashSet<>(11);
    	list.add(0); // 全相同
    	list.add(e + (e << 3) + (e << 6)); // 中台和wms(或sap)相同
    	list.add(e + 0 + (e << 6)); // 中台和wms(或sap)相同
    	list.add(e + (e << 3) + 0); // 中台和wms(或sap)相同
    	list.add(e + 0 + 0); // 中台和wms(或sap)相同
    	list.add(0 + 0 + (e << 6)); // 中台和wms(或sap)相同
    	list.add(0 + (e << 3) + (e << 6)); // 中台和wms(或sap)相同
    	list.add(0 + (e << 3) + 0); // 中台和wms(或sap)相同
    	return list;
    }
}
