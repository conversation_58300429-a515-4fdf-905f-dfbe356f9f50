package com.rome.stock.innerservice.domain.entity.warehouserecord;

import com.rome.arch.core.domain.EntityFactory;
import com.rome.stock.common.constants.warehouse.WarehouseRecordConstant;
import com.rome.stock.common.enums.warehouse.WarehouseRecordTypeVO;
import com.rome.stock.innerservice.api.dto.frontrecord.ShopAllocationDetailDTO;
import com.rome.stock.innerservice.api.dto.frontrecord.ShopAllocationRecordDTO;
import com.rome.stock.innerservice.api.dto.warehouserecord.InWarehouseRecordDTO;
import com.rome.stock.innerservice.api.dto.warehouserecord.OutWarehouseRecordDTO;
import com.rome.stock.innerservice.api.dto.warehouserecord.RecordDetailDTO;
import com.rome.stock.innerservice.constant.WarehouseRecordBatchStatusVO;
import com.rome.stock.innerservice.constant.WarehouseRecordBusinessTypeVO;

import com.rome.stock.innerservice.constant.WmsSyncStatusVO;
import com.rome.stock.innerservice.domain.repository.warehouserecord.ShopAllocationWarehouseRepository;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;

/**
 * 门店调拨出入库单
 *
 * <AUTHOR> 2019/4/17 20:52
 */
@Data
@Slf4j
@Component
@Scope("prototype")
@EqualsAndHashCode(callSuper = false)
public class ShopAllocationWarehouseRecordE extends AbstractWarehouseRecord{
    @Resource
    private ShopAllocationWarehouseRepository shopAllocationWarehouseRepository;

    @Resource
    private EntityFactory entityFactory;

    /**
     * 保存出库单
     */
    public void addOutWarehouseRecord() {
        long id = shopAllocationWarehouseRepository.saveOutWarehouseRecord(this);
        this.setId(id);
        this.addWarehouseRecordDetail();
    }

    /**
     * 保存入库单
     */
    public void addInWarehouseRecord() {
        long id = shopAllocationWarehouseRepository.saveInWarehouseRecord(this);
        this.setId(id);
        this.addWarehouseRecordDetail();
    }

    /**
     * 保存单据明细
     */
    public void addWarehouseRecordDetail() {
        this.warehouseRecordDetails.forEach(record -> record.setWarehouseRecordDetail(this));
        shopAllocationWarehouseRepository.saveWarehouseRecordDetails(this.warehouseRecordDetails);
    }

    /**
     * 根据前置单生成出库单
     */
    public void createOutRecordByFrontRecord(OutWarehouseRecordDTO frontRecord,Long realWarehouseId){
        this.setRecordCode(frontRecord.getRecordCode());
        this.setRealWarehouseId(realWarehouseId);
        this.setBusinessType(WarehouseRecordBusinessTypeVO.OUT_WAREHOUSE_RECORD.getType());
        this.setRecordType(frontRecord.getRecordType());
        this.setCmpStatus(WarehouseRecordConstant.INIT_CMP);
        this.setSyncWmsStatus(WmsSyncStatusVO.NO_REQUIRED.getStatus());
        this.setBatchStatus(WarehouseRecordBatchStatusVO.INIT.getStatus());
        List<RecordDetailDTO> frontRecordDetails=frontRecord.getDetailList();
        this.setWarehouseRecordDetails(new ArrayList<>(frontRecordDetails.size()));
        if(frontRecordDetails!=null){
            for(RecordDetailDTO detail:frontRecordDetails){
                WarehouseRecordDetail warehouseRecordDetail=entityFactory.createEntity(WarehouseRecordDetail.class);
                warehouseRecordDetail.setRealWarehouseId(this.getRealWarehouseId());
                warehouseRecordDetail.setUnitCode(detail.getBasicUnitCode());
                warehouseRecordDetail.setUnit(detail.getBasicUnit());
                warehouseRecordDetail.setSkuCode(detail.getSkuCode());
                warehouseRecordDetail.setActualQty(detail.getBasicSkuQty());
                warehouseRecordDetail.setPlanQty(detail.getBasicSkuQty());
                warehouseRecordDetail.setRecordCode(frontRecord.getRecordCode());
                this.warehouseRecordDetails.add(warehouseRecordDetail);
            }
            //设置skuCode和skuID
            this.initWarehouseRecodeDetail();
        }

    }

    /**
     * 根据前置单生成入库单
     */
    public void createInRecordByFrontRecord(InWarehouseRecordDTO frontRecord,Long realWarehouseId){
        this.setRecordCode(frontRecord.getRecordCode());
        this.setRealWarehouseId(realWarehouseId);
        this.setBusinessType(WarehouseRecordBusinessTypeVO.IN_WAREHOUSE_RECORD.getType());
        this.setRecordType(frontRecord.getRecordType());
        this.setSyncWmsStatus(WmsSyncStatusVO.NO_REQUIRED.getStatus());
        this.setBatchStatus(WarehouseRecordBatchStatusVO.INIT.getStatus());
        this.setCmpStatus(WarehouseRecordConstant.INIT_CMP);
        this.setSyncTransferStatus(WarehouseRecordConstant.NEED_TRANSFER);
        List<RecordDetailDTO> frontRecordDetails=frontRecord.getDetailList();
        this.setWarehouseRecordDetails(new ArrayList<>(frontRecordDetails.size()));
        if(frontRecordDetails!=null){
            for(RecordDetailDTO detail:frontRecordDetails){
                WarehouseRecordDetail warehouseRecordDetail = entityFactory.createEntity(WarehouseRecordDetail.class);
                warehouseRecordDetail.setRealWarehouseId(this.getRealWarehouseId());
                warehouseRecordDetail.setUnitCode(detail.getBasicUnitCode());
                warehouseRecordDetail.setUnit(detail.getBasicUnit());
                warehouseRecordDetail.setSkuCode(detail.getSkuCode());
                warehouseRecordDetail.setActualQty(detail.getBasicSkuQty());
                warehouseRecordDetail.setPlanQty(detail.getBasicSkuQty());
                warehouseRecordDetail.setRecordCode(frontRecord.getRecordCode());
                this.warehouseRecordDetails.add(warehouseRecordDetail);
            }
            //设置skuCode和skuID
            this.initWarehouseRecodeDetail();
        }
    }





}
