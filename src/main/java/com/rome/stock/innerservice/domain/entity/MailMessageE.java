package com.rome.stock.innerservice.domain.entity;

import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Component;

import java.util.Date;

/**
 * @Description
 * <AUTHOR>
 * @Date 2019/8/4 14:31
 * @Version 1.0
 */
@Component
@Scope("prototype")
@Data
@EqualsAndHashCode(callSuper = false)
public class MailMessageE extends BaseE{

    /**
     * 唯一标识
     */
    private Long id;

    /**
     * 发件人邮箱地址
     */
    private String sendUserMail;

    /**
     * 收件人邮箱
     */
    private String receiveUserMail;

    /**
     * 邮件标题
     */
    private String mailTitle;

    /**
     * 邮件内容
     */
    private String mailContent;

    /**
     * 发送状态
     */
    private Integer sendStatus;

    /**
     * 发送次数
     */
    private Integer sendTimes;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新时间
     */
    private Date updateTime;
}
