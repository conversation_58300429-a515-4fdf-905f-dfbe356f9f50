package com.rome.stock.innerservice.domain.service.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.rome.arch.core.clientobject.Response;
import com.rome.arch.core.domain.EntityFactory;
import com.rome.arch.core.exception.RomeException;
import com.rome.stock.common.constants.UpdateWmsRecordConstants;
import com.rome.stock.common.constants.WmsConfigConstants;
import com.rome.stock.core.domain.repository.CoreRealWarehouseStockRepository;
import com.rome.stock.innerservice.api.dto.frontrecord.DispatchNoticeDTO;
import com.rome.stock.innerservice.api.dto.frontrecord.RealWarehouseAllocationDispatchDTO;
import com.rome.stock.innerservice.api.dto.warehouserecord.BatchResultDTO;
import com.rome.stock.innerservice.api.dto.warehouserecord.MmCancelResultDTO;
import com.rome.stock.innerservice.api.dto.wms.MMExpressCodeDTO;
import com.rome.stock.innerservice.api.dto.wms.RecordOrder;
import com.rome.stock.innerservice.api.dto.wms.UpdateTmsOrderDTO;
import com.rome.stock.innerservice.api.dto.wms.UpdateTmsOrderItem;
import com.rome.stock.innerservice.common.*;
import com.rome.stock.innerservice.constant.DispatchNoticeStatusVO;
import com.rome.stock.innerservice.constant.ParamValidator;
import com.rome.stock.innerservice.constant.WarehouseRecordStatusVO;
import com.rome.stock.innerservice.domain.convertor.DispatchNoticeConvertor;
import com.rome.stock.innerservice.domain.convertor.WarehouseRecordConvertor;
import com.rome.stock.innerservice.domain.entity.ChangeLogE;
import com.rome.stock.innerservice.domain.entity.warehouserecord.WarehouseRecordDetail;
import com.rome.stock.innerservice.domain.entity.warehouserecord.WarehouseRecordE;
import com.rome.stock.innerservice.domain.repository.*;
import com.rome.stock.innerservice.domain.repository.frontrecord.FrWhAllocationRepository;
import com.rome.stock.innerservice.domain.repository.warehouserecord.WarehouseRecordRepository;
import com.rome.stock.innerservice.domain.service.*;
import com.rome.stock.innerservice.infrastructure.dataobject.DispatchNoticeDO;
import com.rome.stock.innerservice.infrastructure.dataobject.FrontWarehouseRecordRelationDO;
import com.rome.stock.innerservice.infrastructure.dataobject.RealWarehouseWmsConfigDO;
import com.rome.stock.innerservice.infrastructure.dataobject.WarehouseRecordDo;
import com.rome.stock.innerservice.infrastructure.mapper.FrontWarehouseRecordRelationMapper;
import com.rome.stock.innerservice.infrastructure.mapper.RealWarehouseWmsConfigMapper;
import com.rome.stock.innerservice.remote.mm.facade.MmOrderIssuesFacade;
import com.rome.stock.innerservice.remote.sap.dto.DeliveryLineResDTO;
import com.rome.stock.innerservice.remote.sap.facade.SapFacade;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.time.DateUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * DispatchNoticeServiceImpl类的实现描述： 派车
 *
 * <AUTHOR> 2019/6/18 9:51
 */
@Slf4j
@Service
public class DispatchNoticeServiceImpl implements DispatchNoticeService {

    @Autowired
    private EntityFactory entityFactory;
    @Autowired
    private DispatchNoticeRepository dispatchNoticeRepository;
    @Autowired
    private DispatchNoticeConvertor dispatchNoticeConvertor;
    @Autowired
    private ShopReplenishService shopReplenishService;
    @Resource
    private WarehouseRecordRepository warehouseRecordRepository;
    @Resource
    private ShopReturnService shopReturnService;
    @Resource
    private WhAllocationService whAllocationService;
    @Resource
    private ColdChainOrderService coldChainOrderService;
    @Resource
    private RealWarehouseRepository realWarehouseRepository;
    @Resource
    private SapFacade sapFacade;
    @Resource
    private FrWhAllocationRepository frWhAllocationRepository;
    @Resource
    private SkuQtyUnitTools skuQtyUnitTools;
    @Resource
    private CoreRealWarehouseStockRepository coreRealWarehouseStockRepository;
    @Resource
    private SkuInfoTools skuInfoTools;
    @Resource
    private FrontWarehouseRecordRelationMapper frontWarehouseRecordRelationMapper;
    @Resource
    private SapInterfaceLogRepository sapInterfaceLogRepository;
    @Resource
    private ChangeLogRepository changeLogRepository;
    @Resource
    private RealWarehouseWmsConfigRepository realWarehouseWmsConfigRepository;
    @Resource
    private WmsOutService wmsOutService;

    @Resource
    private WarehouseRecordConvertor warehouseRecordConvertor;

    private final static Integer NEED_DISPATCH = 1;

    private final static Integer NOT_NEED_DISPATCH = 2;

    @Resource
    private RealWarehouseWmsConfigMapper realWarehouseWmsConfigMapper;

    @Resource
    private MmOrderIssuesFacade mmOrderIssuesFacade;

    private ParamValidator validator = ParamValidator.INSTANCE;

    @Override
    public DispatchNoticeDO getByFrontId(Long frontRecordId, Integer type) {
        List<DispatchNoticeDO> list = dispatchNoticeRepository.getByFrontId(frontRecordId, type);
        if(CollectionUtils.isNotEmpty(list)){
            //退货 调拨  冷链只会产生一张派车单
            AlikAssert.isTrue(list.size() == 1 ,ResCode.STOCK_ERROR_1054,ResCode.STOCK_ERROR_1054_DESC);
            return list.get(0);
        }
        return null;
    }

    /**
     * 派车通知单，应该是公用接口，自有业务逻辑封装剥离出去
     * @param dispatchNoticeDTO
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void handleDispatchNotice(DispatchNoticeDTO dispatchNoticeDTO) {
        //valid校验
        this.validInfo(dispatchNoticeDTO);
        //派车
        if(NEED_DISPATCH.equals(dispatchNoticeDTO.getDispatchType())){
            AlikAssert.isNotBlank(dispatchNoticeDTO.getThirdRecordCode(), ResCode.STOCK_ERROR_1069 ,ResCode.STOCK_ERROR_1069_DESC);
        }
        //查询出入库领域实体
        WarehouseRecordE warehouseRecordE = warehouseRecordRepository.queryWarehouseRecordByRecordCode(dispatchNoticeDTO.getRecordCode());
        if(null == warehouseRecordE){
            throw new RomeException(ResCode.STOCK_ERROR_1017,ResCode.STOCK_ERROR_1017_DESC);
        }
        if(WarehouseRecordStatusVO.DISABLED.getStatus().equals(warehouseRecordE.getRecordStatus())){
            throw new RomeException(ResCode.STOCK_ERROR_1017,"已取消的单据不支持此操作");
        }
        List<WarehouseRecordDetail> warehouseRecordDetails = warehouseRecordRepository.queryDetailListByRecordCode(dispatchNoticeDTO.getRecordCode());
        //对行号和交货单号进行校验
        if(CollectionUtils.isNotEmpty(warehouseRecordDetails)){
            warehouseRecordDetails.forEach(x->{
                if(StringUtils.isBlank(x.getLineNo()) || StringUtils.isBlank(x.getDeliveryLineNo())){
                    throw new RomeException(ResCode.STOCK_ERROR_1002,ResCode.STOCK_ERROR_1002_DESC+"行号不能为空,recordCode="+x.getRecordCode());
                }
            });
        }
        //幂等校验,一个交货单只能派车一次
        if(dispatchNoticeRepository.getByRecordCode(dispatchNoticeDTO.getRecordCode()) != null){
            return;
        }
        //新增派车单
        DispatchNoticeDO dispatchNoticeDO = dispatchNoticeConvertor.dtoToDo(dispatchNoticeDTO);
        dispatchNoticeDO.setRecordCode(warehouseRecordE.getRecordCode());
        dispatchNoticeDO.setDispatchStatus(DispatchNoticeStatusVO.DISPATCHING.getStatus());
        dispatchNoticeDO.setFrontRecordId(0L);
        dispatchNoticeDO.setFrontRecordCode("");
        dispatchNoticeDO.setFrontRecordType(0);
        //sap_order_code使用后置单号
        if(StringUtils.isEmpty(warehouseRecordE.getSapOrderCode())){
            dispatchNoticeDO.setSapRecordCode(warehouseRecordE.getRecordCode());
        }else{
            dispatchNoticeDO.setSapRecordCode(warehouseRecordE.getSapOrderCode());
        }
        //部分锁库，可能未完全锁定，强制发货
        this.deleteZeroDetailAndUpdatePlanQtyByActualQty(warehouseRecordE);

        boolean executeResult = dispatchNoticeRepository.insertDispatchNotice(dispatchNoticeDO);
        AlikAssert.isTrue(executeResult, ResCode.STOCK_ERROR_4001, ResCode.STOCK_ERROR_4001_DESC);
        WarehouseRecordDo warehouseRecordDo= warehouseRecordConvertor.entityToDo(warehouseRecordE);
        warehouseRecordDo.setSapOrderCode(dispatchNoticeDO.getSapRecordCode());
        warehouseRecordDo.setTmsRecordCode(dispatchNoticeDO.getThirdRecordCode());
        int j=warehouseRecordRepository.updateSyncStatusAndSapCode(warehouseRecordDo);
        if(j==0){
            throw  new RomeException(ResCode.STOCK_ERROR_1001 , "更新TmsCode和sapOrderCode失败");
        }
        //未出库的派车时修改实际数量为0
        if(!WarehouseRecordStatusVO.OUT_ALLOCATION.getStatus().equals(warehouseRecordE.getRecordStatus())){
            //批量修改明细actural_qty =0
            int k=warehouseRecordRepository.batchUpdateDetailActualQtyByRecordCode(warehouseRecordE.getRecordCode());
            if(k==0){
                throw  new RomeException(ResCode.STOCK_ERROR_1001 , "更新detailActualQty失败");
            }
        }
    }

    /**如果是部分锁库
     * 1.通过actual_qty设置plan_qty
     * 2.删除部分锁库，plan_qty为零的明细
     * @param warehouseRecordE
     */
    private void deleteZeroDetailAndUpdatePlanQtyByActualQty(WarehouseRecordE warehouseRecordE){
        if(warehouseRecordE.checkUnLockQty()){
            warehouseRecordRepository.updatePlanQtyByActualQty(warehouseRecordE.getRecordCode());
            warehouseRecordRepository.deleteZeroDetail(warehouseRecordE.getRecordCode());
        }
    }




    /**
     * 根据邮件发送状态和单据类型查询仓库调拨派车单
     * @param mailStatus
     * @param frontRecordType
     * @return
     */
    @Override
    public List<RealWarehouseAllocationDispatchDTO> selectDispatchRecordByMailStatus(Integer mailStatus, Integer frontRecordType){
        //查询派车单
        Date endTime = new Date();
        Date startTime = DateUtils.addDays(endTime,-7);
        List<DispatchNoticeDTO> noticeDTOS = dispatchNoticeRepository.selectDispatchRecordByMailStatus(mailStatus,frontRecordType,startTime,endTime);
        if(null == noticeDTOS || 0 == noticeDTOS.size()){
            return null;
        }
        List<RealWarehouseAllocationDispatchDTO> realWarehouseAllocationDispatchDTOS = dispatchNoticeConvertor.dispatchNoticeDtoToRealWarehouseAllocationDispatchDto(noticeDTOS);

        //根据派车单中的前置单id批量查询调拨单
        List<Long> frontRecordIds = RomeCollectionUtil.getValueList(realWarehouseAllocationDispatchDTOS, "frontRecordId");
//        List<WhAllocationRecordE> whAllocationRecordES = frWhAllocationRepository.queryByIds(frontRecordIds);
//        //抽取调拨单的入仓和出仓
//        List<Long> inWarehouseIds = RomeCollectionUtil.getValueList(whAllocationRecordES, "inWarehouseId");
//        List<Long> outWarehouseIds = RomeCollectionUtil.getValueList(whAllocationRecordES, "outWarehouseId");
//        inWarehouseIds.addAll(outWarehouseIds);
//
//        Set<Long> realWarehouseIds = new HashSet<>(inWarehouseIds);
//        inWarehouseIds.clear();
//        inWarehouseIds.addAll(realWarehouseIds);
//        //根据仓库id查询仓库信息
//        List<RealWarehouseE> warehouseES = realWarehouseRepository.queryWarehouseByIds(inWarehouseIds);
//
//        Map<Long, RealWarehouseE> warehouseEMap = RomeCollectionUtil.listforMap(warehouseES, "id");
//        Map<Long, WhAllocationRecordE> whAllocationRecordEMap = RomeCollectionUtil.listforMap(whAllocationRecordES, "id");
//
//        realWarehouseAllocationDispatchDTOS.forEach(dto ->{
//            if(whAllocationRecordEMap.containsKey(dto.getFrontRecordId())){
//                WhAllocationRecordE recordE = whAllocationRecordEMap.get(dto.getFrontRecordId());
//                dto.setBusinessType(recordE.getBusinessType());
//
//                RealWarehouseE outRealWarehouse = warehouseEMap.get(recordE.getOutWarehouseId());
//                dto.setOutWarehouseId(outRealWarehouse.getId());
//                dto.setOutFactoryCode(outRealWarehouse.getFactoryCode());
//                dto.setOutWarehouseCode(outRealWarehouse.getRealWarehouseOutCode());
//
//                RealWarehouseE inRealWarehouse = warehouseEMap.get(recordE.getInWarehouseId());
//                dto.setInWarehouseId(inRealWarehouse.getId());
//                dto.setInFactoryCode(inRealWarehouse.getFactoryCode());
//                dto.setInWarehouseCode(inRealWarehouse.getRealWarehouseOutCode());
//            }
//        });

        return realWarehouseAllocationDispatchDTOS;

    }

    @Override
    public List<DispatchNoticeDTO> getDispatchInfoByTmsCode(List<String> tmsCodeList) {
        return dispatchNoticeRepository.getDispatchInfoByTmsCode(tmsCodeList);
    }

    /**
     * 信息校验
     * @param dto
     */
    private void validInfo(DispatchNoticeDTO dto){
        AlikAssert.isNotNull(dto.getRecordCode(), ResCode.STOCK_ERROR_1002_DESC, "后置单号" + ResCode.STOCK_ERROR_1002_DESC);
        AlikAssert.isNotNull(dto.getDispatchType(), ResCode.STOCK_ERROR_1002_DESC, "派车类型" + ResCode.STOCK_ERROR_1002_DESC);
        AlikAssert.isNotNull(dto.getSourceSystem(), ResCode.STOCK_ERROR_1002_DESC, "来源系统" + ResCode.STOCK_ERROR_1002_DESC);

    }


    /**
     * 信息校验
     * @param resList
     * @param relationDO
     * @param details
     * @return
     */
    private String validAndSetLineNo(List<DeliveryLineResDTO> resList, FrontWarehouseRecordRelationDO relationDO, List<WarehouseRecordDetail> details){
        String sapPoCode = "";
        Map<Long, DeliveryLineResDTO> deliveryOrderMap = new HashMap<>();
        AlikAssert.isNotEmpty(resList, ResCode.STOCK_ERROR_1150, ResCode.STOCK_ERROR_1150_DESC);
        Map<Long, WarehouseRecordDetail> detailMap = RomeCollectionUtil.listforMap(details, "id", null);
        Map<String, WarehouseRecordDetail> poLineMap = RomeCollectionUtil.listforMap(details, "lineNo", null);
       //字段完整性校验
        for (DeliveryLineResDTO deliveryOrderResDTO : resList) {
            AlikAssert.isNotBlank(deliveryOrderResDTO.getBillno(), ResCode.STOCK_ERROR_1151, ResCode.STOCK_ERROR_1151_DESC);
            AlikAssert.isNotBlank(deliveryOrderResDTO.getBillnum(), ResCode.STOCK_ERROR_1152, ResCode.STOCK_ERROR_1152_DESC);
            AlikAssert.isNotNull(deliveryOrderResDTO.getZthh(), ResCode.STOCK_ERROR_1154, ResCode.STOCK_ERROR_1154_DESC);
            AlikAssert.isNotBlank(deliveryOrderResDTO.getVbeln(), ResCode.STOCK_ERROR_1155, ResCode.STOCK_ERROR_1155_DESC);
            AlikAssert.isNotBlank(deliveryOrderResDTO.getPosnr(), ResCode.STOCK_ERROR_1156, ResCode.STOCK_ERROR_1156_DESC);
            AlikAssert.isNotBlank(deliveryOrderResDTO.getQuantity(), ResCode.STOCK_ERROR_1157, ResCode.STOCK_ERROR_1157_DESC);
            AlikAssert.isNotBlank(deliveryOrderResDTO.getUnit(), ResCode.STOCK_ERROR_1157, ResCode.STOCK_ERROR_1158_DESC);
            //校验中台行号是否匹配
            AlikAssert.isTrue(detailMap.containsKey(deliveryOrderResDTO.getZthh()), ResCode.STOCK_ERROR_1002, ResCode.STOCK_ERROR_1002_DESC + ": 行号" + deliveryOrderResDTO.getZthh() + "与交货无关");
            //校验sap采购单行号是否匹配
            AlikAssert.isTrue(poLineMap.containsKey(deliveryOrderResDTO.getBillnum()), ResCode.STOCK_ERROR_1002, ResCode.STOCK_ERROR_1002_DESC + ": 行号" + deliveryOrderResDTO.getBillnum() + "与交货无关");
            sapPoCode = deliveryOrderResDTO.getVbeln();
            deliveryOrderMap.put(deliveryOrderResDTO.getZthh(), deliveryOrderResDTO);
        }
        //设置交货单行号
        for (WarehouseRecordDetail detail : details) {
            DeliveryLineResDTO deliveryOrderResDTO = deliveryOrderMap.get(detail.getId());
            detail.setDeliveryLineNo(deliveryOrderResDTO.getPosnr());
        }
        return sapPoCode;
    }


    /**
     * @Description: 批量更新派车单号 <br>
     *
     * <AUTHOR> 2019/10/16
     * @param recordCodeList
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public List<BatchResultDTO> updateRealWarehouseBatch(List<String> recordCodeList, String tmsCode) {
        List<BatchResultDTO> resultList =  new ArrayList<>();
        AlikAssert.isTrue(recordCodeList.size() <= 100 , ResCode.STOCK_ERROR_1001, "每次最多只能修改100条");
        List<WarehouseRecordE> warehouseRecordES = warehouseRecordRepository.getRecordTypeByRecordCodes(recordCodeList);
        List<WarehouseRecordE> warehouseRecordEList = warehouseRecordES.stream().filter(r -> r.getSyncWmsStatus() != 0).collect(Collectors.toList());
        AlikAssert.isNotEmpty(warehouseRecordEList, ResCode.STOCK_ERROR_1001, "单据派车状态异常"+tmsCode);
        AlikAssert.isTrue(recordCodeList.size() == warehouseRecordEList.size(), ResCode.STOCK_ERROR_1001, "部分单据派车状态异常"+tmsCode);
        // 仓库按系统分组
        List<Long> realWarehouseIds = warehouseRecordES.stream().map(WarehouseRecordE::getRealWarehouseId).distinct().collect(Collectors.toList());
        List<RealWarehouseWmsConfigDO> configDOList = realWarehouseWmsConfigRepository.findWmsConfigByWarehouseIds(realWarehouseIds);
        if(CollectionUtils.isEmpty(configDOList)){
            log.error("如下单据仓库非中台管理: " + StringUtils.join(recordCodeList, ","));
            for (String recordCode : recordCodeList) {
                BatchResultDTO result = new BatchResultDTO();
                result.setRecordCode(recordCode);
                result.setStatus(true);
                resultList.add(result);
            }
            return resultList;
        }
        List<Long> dfRealWarehouseIds = configDOList.stream().filter(r -> WmsConfigConstants.WMS_DF == r.getWmsCode().intValue()).map(RealWarehouseWmsConfigDO::getRealWarehouseId).collect(Collectors.toList());
        List<Long> odyRealWarehouseIds = configDOList.stream().filter(r -> WmsConfigConstants.WMS_ODY == r.getWmsCode().intValue()).map(RealWarehouseWmsConfigDO::getRealWarehouseId).collect(Collectors.toList());
        List<Long>  mmRealWarehouseIds = configDOList.stream().filter(r -> WmsConfigConstants.WMS_MM == r.getWmsCode().intValue()).map(RealWarehouseWmsConfigDO::getRealWarehouseId).collect(Collectors.toList());

        List<Long> otherRealWarehouseIds = configDOList.stream().filter(r -> {
            if(WmsConfigConstants.WMS_SAP == r.getWmsCode().intValue()
                    || WmsConfigConstants.WMS_ODY == r.getWmsCode().intValue()
                    || WmsConfigConstants.WMS_DF == r.getWmsCode().intValue()
                    || WmsConfigConstants.WMS_MM == r.getWmsCode().intValue()) {
                return false;
            }
            return true;
        }).map(RealWarehouseWmsConfigDO::getRealWarehouseId).collect(Collectors.toList());
        List<WarehouseRecordE> dfWarehouseRecordEs = new ArrayList<>();
        List<WarehouseRecordE> odyWarehouseRecordEs = new ArrayList<>();
        List<WarehouseRecordE> mmWarehouseRecordEs=new ArrayList<>();
        List<WarehouseRecordE> otherWarehouseRecordEs = new ArrayList<>();
        for(WarehouseRecordE recordE : warehouseRecordEList) {
            if(dfRealWarehouseIds.contains(recordE.getRealWarehouseId())) {
                dfWarehouseRecordEs.add(recordE);
            } else if(odyRealWarehouseIds.contains(recordE.getRealWarehouseId())) {
                odyWarehouseRecordEs.add(recordE);
            }else if(mmRealWarehouseIds.contains(recordE.getRealWarehouseId())){
                mmWarehouseRecordEs.add(recordE);
            } else if(otherRealWarehouseIds.contains(recordE.getRealWarehouseId())) {
                otherWarehouseRecordEs.add(recordE);
            }
        }
        // 按照同步wms分组
        List<String> unSyncCodes = dfWarehouseRecordEs.stream().filter(r -> r.getSyncWmsStatus().intValue() == 1).map(WarehouseRecordE::getRecordCode).collect(Collectors.toList());
        List<String> syncCodes = dfWarehouseRecordEs.stream().filter(r -> r.getSyncWmsStatus().intValue() == 2).map(WarehouseRecordE::getRecordCode).collect(Collectors.toList());

        List<String> odyUnSyncCodes = odyWarehouseRecordEs.stream().filter(r -> r.getSyncWmsStatus().intValue() == 1).map(WarehouseRecordE::getRecordCode).collect(Collectors.toList());
        List<String> odySyncCodes = odyWarehouseRecordEs.stream().filter(r -> r.getSyncWmsStatus().intValue() == 2).map(WarehouseRecordE::getRecordCode).collect(Collectors.toList());

        List<String>  mmUnSyncCodes = mmWarehouseRecordEs.stream().filter(r -> r.getSyncWmsStatus().intValue() == 1).map(WarehouseRecordE::getRecordCode).collect(Collectors.toList());
        List<String> mmSyncCodes = mmWarehouseRecordEs.stream().filter(r -> r.getSyncWmsStatus().intValue() == 2).map(WarehouseRecordE::getRecordCode).collect(Collectors.toList());

        List<String> otherWarehouseCodes = otherWarehouseRecordEs.stream().map(WarehouseRecordE::getRecordCode).collect(Collectors.toList());
        syncCodes.addAll(odySyncCodes);
        syncCodes.addAll(mmSyncCodes);

        unSyncCodes.addAll(odyUnSyncCodes);
        unSyncCodes.addAll(mmUnSyncCodes);
        String suCode = "";
        if(unSyncCodes.size() == warehouseRecordEList.size()) {
            // 全部未同步
            resultList.addAll(this.updateTmsCode(unSyncCodes, tmsCode, 0));
            suCode = unSyncCodes.stream().collect(Collectors.joining(","));
        } else if(syncCodes.size() == warehouseRecordEList.size()) {
            // 全部已同步
            //做成map,方便以后的wms的扩充
            Map<Integer , List<String>> codeMap = new HashMap<>();
            //移除MM相关的
            syncCodes.removeAll(mmSyncCodes);
            syncCodes.removeAll(odySyncCodes);
            codeMap.put(WmsConfigConstants.WMS_DF , syncCodes);
            codeMap.put(WmsConfigConstants.WMS_ODY , odySyncCodes);
            List<UpdateTmsOrderItem> mmUpdateTmsOrderItems=new ArrayList<>();
            //针对MM的更新
            if(CollectionUtils.isNotEmpty(mmSyncCodes)){
                mmUpdateTmsOrderItems = getMmUpdateTmsOrderItems(mmSyncCodes, tmsCode);
            }
            //针对DF和ODY
            List<UpdateTmsOrderItem> items = this.getUpdateTmsOrderItems(codeMap, tmsCode);
            items.addAll(mmUpdateTmsOrderItems);
            List<UpdateTmsOrderItem> successItems = items.stream().filter(r -> "success".equals(r.getUpStatus())).collect(Collectors.toList());
            List<UpdateTmsOrderItem> failItems = items.stream().filter(r -> "fail".equals(r.getUpStatus())).collect(Collectors.toList());
            List<String> suCodes = successItems.stream().map(UpdateTmsOrderItem::getDeliveryOrderCode).collect(Collectors.toList());
            List<String> errCodes = failItems.stream().map(UpdateTmsOrderItem::getDeliveryOrderCode).collect(Collectors.toList());
            resultList.addAll(this.updateTmsCode(suCodes, tmsCode, 1));
            for (String recordCode : errCodes) {
                BatchResultDTO result = new BatchResultDTO(recordCode, false);
                resultList.add(result);
            }
        } else if(CollectionUtils.isNotEmpty(unSyncCodes) && CollectionUtils.isNotEmpty(syncCodes) ){
            // 部分已同步
            resultList.addAll(this.updateTmsCode(unSyncCodes, tmsCode, 0));
            suCode = suCode + "," + unSyncCodes.stream().collect(Collectors.joining(","));
            //做成map,方便以后的wms的扩充
            Map<Integer , List<String>> codeMap = new HashMap<>();
            syncCodes.removeAll(mmSyncCodes);
            syncCodes.removeAll(odySyncCodes);
            codeMap.put(WmsConfigConstants.WMS_DF , syncCodes);
            codeMap.put(WmsConfigConstants.WMS_ODY , odySyncCodes);
            List<UpdateTmsOrderItem> items = this.getUpdateTmsOrderItems(codeMap, tmsCode);
            List<UpdateTmsOrderItem> mmUpdateTmsOrderItems=new ArrayList<>();
            //针对MM的更新派车单
            if(CollectionUtils.isNotEmpty(mmSyncCodes)){
                mmUpdateTmsOrderItems = getMmUpdateTmsOrderItems(mmSyncCodes, tmsCode);
            }
            items.addAll(mmUpdateTmsOrderItems);
            List<UpdateTmsOrderItem> successItems = items.stream().filter(r -> "success".equals(r.getUpStatus())).collect(Collectors.toList());
            List<UpdateTmsOrderItem> failItems = items.stream().filter(r -> "fail".equals(r.getUpStatus())).collect(Collectors.toList());
            List<String> suCodes = successItems.stream().map(UpdateTmsOrderItem::getDeliveryOrderCode).collect(Collectors.toList());
            List<String> errCodes = failItems.stream().map(UpdateTmsOrderItem::getDeliveryOrderCode).collect(Collectors.toList());
            resultList.addAll(this.updateTmsCode(suCodes, tmsCode, 1));
            for (String recordCode : errCodes) {
                BatchResultDTO result = new BatchResultDTO(recordCode, false);
                resultList.add(result);
            }
        }else{
            Map<String, Object> map = new HashMap<>();
            map.put("recordIdList", otherWarehouseCodes);
            map.put("tmsCode", tmsCode);
            log.info(ResCode.STOCK_ERROR_1001 + "当前所有订单无需更新派车单号" + JSONObject.toJSONString(map));
            for (String recordCode : otherWarehouseCodes) {
                BatchResultDTO result = new BatchResultDTO(recordCode, false);
                resultList.add(result);
        }
            return resultList;
        }
//        // 保存日志
//        ChangeLogE changeLogE = new ChangeLogE();
//        changeLogE.setBusinessName("派车单号修改");
//        changeLogE.setServiceName(tmsCode);
//        changeLogE.setBeforeContent(this.transferContent(warehouseRecordES));
//        Map<String, Object> map = new HashMap<>();
//        map.put("recordIdList", recordCodeList);
//        map.put("tmsCode", tmsCode);
//        changeLogE.setAfterContent(JSONObject.toJSONString(map));
//        changeLogRepository.saveChangeLog(changeLogE);
        return resultList;
    }

    /**
     * 更新MM派车单号
     * @param recordList
     * @param tmsCode
     * @return
     */
    private List<UpdateTmsOrderItem> getMmUpdateTmsOrderItems(List<String> recordList, String tmsCode){
        MMExpressCodeDTO mmExpressCodeDTO=null;
        List<MMExpressCodeDTO> mmExpressCodeDTOList=new ArrayList<>();
        List<UpdateTmsOrderItem> temp = new ArrayList<>();
        if(CollectionUtils.isNotEmpty(recordList)){
               for(String recordCode:recordList){
                   mmExpressCodeDTO=new MMExpressCodeDTO();
                   mmExpressCodeDTO.setDeliveryOrderCode(recordCode);
                   mmExpressCodeDTO.setSendCarCode(tmsCode);
                   mmExpressCodeDTOList.add(mmExpressCodeDTO);
               }
            Response response = mmOrderIssuesFacade.updateExpressCode(mmExpressCodeDTOList);
            if (validator.validResponse(response)) {
                    if(Objects.nonNull(response.getData())){
                        UpdateTmsOrderItem updateTmsOrderItem=null;
                        List<MmCancelResultDTO> resultCancelList = JSONArray.parseArray(JSON.toJSONString(response.getData()), MmCancelResultDTO.class);
                        for(MmCancelResultDTO mmCancelResultDTO:resultCancelList){
                            updateTmsOrderItem=new UpdateTmsOrderItem();
                            updateTmsOrderItem.setDeliveryOrderCode(mmCancelResultDTO.getDeliveryOrderCode());
                            updateTmsOrderItem.setUpStatus(Objects.equals(mmCancelResultDTO.getStatus(),0)?"success":"fail");
                            temp.add(updateTmsOrderItem);
                    }
                }
             }else{
                    UpdateTmsOrderItem item=null;
                     for (String code : recordList) {
                        item= new UpdateTmsOrderItem();
                        item.setDeliveryOrderCode(code);
                        item.setUpStatus("fail");
                        temp.add(item);
                    }
                }
            }
        return temp;
    }
    

    /**
     * @Description: 获取wms返回同步结果 <br>
     *
     * <AUTHOR> 2019/10/23
     * @param codeMap
     * @return
     */
    private List<UpdateTmsOrderItem> getUpdateTmsOrderItems(Map<Integer ,List<String> > codeMap, String tmsCode) {
        List<UpdateTmsOrderItem> res = new ArrayList<>();
        codeMap.forEach((k, codes) -> {
            UpdateTmsOrderDTO updateTmsOrderDTO = new UpdateTmsOrderDTO();
            List<RecordOrder> recordOrders = new ArrayList<>();
                if(CollectionUtils.isNotEmpty(codes)){
            for (String code : codes) {
                RecordOrder recordOrder = new RecordOrder();
                recordOrder.setDeliveryOrderCode(code);
                recordOrder.setExpressCode(tmsCode);
                recordOrders.add(recordOrder);
            }
            updateTmsOrderDTO.setRecordOrders(recordOrders);
            updateTmsOrderDTO.setBusinessType(UpdateWmsRecordConstants.UP_TMS_ORDER);

            List<UpdateTmsOrderItem> temp;
            try {
                temp = wmsOutService.updateTmsOrder(k, updateTmsOrderDTO);
            } catch (Exception e) {
                //抛异常后相当于全部失败
                temp = new ArrayList<>();
                for (String code : codes) {
                    UpdateTmsOrderItem item = new UpdateTmsOrderItem();
                    item.setDeliveryOrderCode(code);
                    item.setUpStatus("fail");
                    temp.add(item);
                }
                log.error(e.getMessage(), e);
            }
            res.addAll(temp);
                }
        });
        return res;
    }

    /**
     * 简化报文
     * @return
     */
    private String transferContent(List<WarehouseRecordE> warehouseRecordES){
        JSONObject par = new JSONObject();
        List<JSONObject> list = new ArrayList<>();
        for (WarehouseRecordE warehouseRecordE : warehouseRecordES) {
            JSONObject record = new JSONObject();
            record.put("id", warehouseRecordE.getId());
            record.put("tmsCode", warehouseRecordE.getTmsRecordCode());
            list.add(record);
        }
        par.put("warehouseRecord", list);
        return par.toJSONString();
    }

    /**
     * 修改出入库单派车单号
     * @param recordCode
     * @param syncStatus 0: 未下发 1: 已下发
     * @return
     */
    private List<BatchResultDTO> updateTmsCode(List<String> recordCodeList, String tmsCode, Integer syncStatus){
        List<BatchResultDTO> resultList = new ArrayList<>();
        for (String recordCode : recordCodeList) {
            BatchResultDTO result = new BatchResultDTO(recordCode, false);
            int upRs = warehouseRecordRepository.updateUnSyncWmsRecord(recordCode, tmsCode);
            if(upRs > 0){
                result.setStatus(true);
            }
            resultList.add(result);
        }
        return resultList;
    }
}
