package com.rome.stock.innerservice.domain.entity.frontrecord;

import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;

/**
 * 损益调整明细
 */
@Component
@Scope("prototype")
@Data
@EqualsAndHashCode(callSuper = false)
public class InventoryAdjustRecordDetailE extends AbstractFrontRecordDetail {

    /**
     * 损益数
     */
    private BigDecimal skuQty;

    /**
     * 批次备注
     */
    private String remark;

    /**
     *损益类型(1,损 2，益)
     */
    private Integer adjustType;

    /**
     * 库存类型(1,合格 2，不合格)
     */
    private Integer inventoryType;
}
