package com.rome.stock.innerservice.domain.entity.warehouserecord;

import java.math.BigDecimal;
import java.util.*;

import javax.annotation.Resource;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import com.rome.stock.common.constants.warehouse.WarehouseRecordConstant;
import com.rome.stock.common.enums.warehouse.WarehouseRecordTypeVO;
import com.rome.stock.innerservice.api.dto.warehouserecord.OutWarehouseRecordDTO;
import com.rome.stock.innerservice.api.dto.warehouserecord.RecordDetailDTO;
import com.rome.stock.innerservice.common.AlikAssert;
import com.rome.stock.innerservice.common.ResCode;
import com.rome.stock.innerservice.constant.*;
import com.rome.stock.innerservice.domain.repository.VirtualWarehouseStockRepository;
import com.rome.stock.innerservice.infrastructure.dataobject.VirtualWarehouseStockDo;
import com.rome.stock.core.infrastructure.dataobject.core.*;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Component;

import com.alibaba.fastjson.JSON;
import com.rome.arch.core.domain.EntityFactory;
import com.rome.stock.innerservice.common.SkuQtyUnitTools;
import com.rome.stock.innerservice.domain.entity.frontrecord.ReservationRecordDetailE;
import com.rome.stock.innerservice.domain.entity.frontrecord.ReservationRecordE;
import com.rome.stock.innerservice.domain.repository.warehouserecord.ReservationWarehouseRepository;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.extern.slf4j.Slf4j;

/**
 * 预约单
 */
@Data
@Slf4j
@Component
@Scope("prototype")
@EqualsAndHashCode(callSuper = false)
public class ReservationWarehouseRecordE extends AbstractWarehouseRecord {

    @Resource
    private EntityFactory entityFactory;

    @Resource
    private SkuQtyUnitTools skuQtyUnitTools;

    @Resource
    private ReservationWarehouseRepository reservationWarehouseRepository;

    @Resource
    private VirtualWarehouseStockRepository virtualWarehouseStockRepository;

    /**
     * 创建预约单出库单据
     */
    public void createRWOutRecordForRes(){
        this.setMobile(null);
        long id = reservationWarehouseRepository.saveReservationWarehouseRecord(this);
        this.setId(id);
        this.warehouseRecordDetails.forEach(wrRecord ->{
            wrRecord.setWarehouseRecordDetail(this);
            wrRecord.setActualQty(BigDecimal.ZERO);
        });
        reservationWarehouseRepository.saveWarehouseRecordDetails(this.warehouseRecordDetails);
        //只有创建预约单时需要创建关联关系 其他则不用
        if(Objects.equals(WarehouseRecordTypeVO.GROUP_PURCHASE_OUT_RECORD.getType(),this.getRecordType())){
            addFrontRecordAndWarehouseRelationForList();
        }
    }





    /**
     * 更新后置单未取消状态
     */
    public void updateCancelStatus(){
        reservationWarehouseRepository.updateOutWarehouseStatus(this.frontRecordId,this.getFrontRecordType());
    }

    /**
     * 更新捋单系统状态
     */
    public void updateFulfillmentStatus(){
        reservationWarehouseRepository.updateFulfillmentStatus(this.getId());
    }

    /**
     * 更新状态为待推送销售中心
     */
    public void updateSyncTradeToBeStatus(){
        reservationWarehouseRepository.updateSyncTradeStatus(this.getId());
    }

    /**
     * 根据前置单查询后置单
     * @return
     */
    public List<ReservationWarehouseRecordE> queryReservationEByFrontCode(){
        return reservationWarehouseRepository.queryWhRecordByFrCode(this.frontRecordId, this.getFrontRecordType());
    }

    /**
     * 更新Do单状态
     */
    public void updateWarehouseDoStatus(){
        reservationWarehouseRepository.updateOutWhStatus(this.getRecordStatus(),this.getId());
    }

    /**
     * 根据单据编号和单据类型查询后置单
     * @return
     */
    public ReservationWarehouseRecordE getReservationWarehouseEByRecordCode(){
        return reservationWarehouseRepository.getWarehouseRecordEByWrCode(this.getRecordCode(),this.getRecordType());
    }

    /***
     * 根据后置单id查询前置
     * @return
     */
    public Long getFrontIdList(){
        List<Long> frontRecordIds = reservationWarehouseRepository.getFrontRecordIds(Arrays.asList(this.getId()));
        if(CollUtil.isEmpty(frontRecordIds)){
            return null;
        }
        return frontRecordIds.get(0);
    }


    /**
     * 构建do出库单信息
     * @param outWarehouseRecordDTO
     */
    public void createDoRecord(OutWarehouseRecordDTO outWarehouseRecordDTO,Long realWarehouseId){
        this.setRealWarehouseId(realWarehouseId);
        this.setRecordType(outWarehouseRecordDTO.getRecordType());
        this.setBusinessType(WarehouseRecordBusinessTypeVO.OUT_WAREHOUSE_RECORD.getType());
        this.setSyncWmsStatus(WmsSyncStatusVO.UNSYNCHRONIZED.getStatus());
        this.setSyncTradeStatus(WarehouseRecordConstant.NEED_SYNC_TRADE);
        this.setRecordStatus(WarehouseRecordStatusVO.INIT.getStatus());
        this.setRecordCode(outWarehouseRecordDTO.getRecordCode());
        this.setBatchStatus(WarehouseRecordBatchStatusVO.INIT.getStatus());
        List<RecordDetailDTO> frontRecordDetails=outWarehouseRecordDTO.getDetailList();
        this.setWarehouseRecordDetails(new ArrayList<>(frontRecordDetails.size()));
        if(frontRecordDetails!=null){
            for(RecordDetailDTO detail:frontRecordDetails){
                WarehouseRecordDetail warehouseRecordDetail=entityFactory.createEntity(WarehouseRecordDetail.class);
                warehouseRecordDetail.setRealWarehouseId(this.getRealWarehouseId());
                warehouseRecordDetail.setUnitCode(detail.getBasicUnitCode());
                warehouseRecordDetail.setUnit(detail.getBasicUnit());
                warehouseRecordDetail.setSkuCode(detail.getSkuCode());
                warehouseRecordDetail.setActualQty(detail.getBasicSkuQty());
                warehouseRecordDetail.setPlanQty(detail.getBasicSkuQty());
                warehouseRecordDetail.setRecordCode(outWarehouseRecordDTO.getRecordCode());
                warehouseRecordDetail.setDeliveryLineNo(detail.getDeliveryLineNo());
                warehouseRecordDetail.setLineNo(detail.getLineNo());
                this.warehouseRecordDetails.add(warehouseRecordDetail);
            }
            //设置skuCode和skuID
            this.initWarehouseRecodeDetail();
        }

    }

    /**
     * 构建前置单出库单
     * @param frontRecord
     */
    public void createOutRecordByFontRecord(ReservationRecordE frontRecord,Integer type){
        if(Objects.equals(type,ReservationBussTypeVo.CREATE_DO.getType())){
            createRecodeCode(WarehouseRecordTypeVO.RESERVATION_DO_RECORD.getCode());
        }else if(Objects.equals(type,ReservationBussTypeVo.CREATE.getType())){
            createRecodeCode(WarehouseRecordTypeVO.GROUP_PURCHASE_OUT_RECORD.getCode());
        }
        this.setAbstractFrontRecord(frontRecord);
        this.setRealWarehouseId(frontRecord.getRealWarehouseId());
        this.setVirtualWarehouseId(frontRecord.getVirtualWarehouseId());
        this.setChannelCode(frontRecord.getChannelCode());
        this.setMerchantId(frontRecord.getMerchantId());
        this.setOutCreateTime(frontRecord.getOutCreateTime());
        this.setUserCode(frontRecord.getUserCode()==null?"":frontRecord.getUserCode());
        this.setMobile(frontRecord.getMobile()==null?"":frontRecord.getMobile());
        List<ReservationRecordDetailE> frontRecordDetails=frontRecord.getReservationDetails();
        this.setWarehouseRecordDetails(new ArrayList<>(frontRecordDetails.size()));
        this.setSyncWmsStatus(WmsSyncStatusVO.NO_REQUIRED.getStatus());
        this.setBatchStatus(WarehouseRecordBatchStatusVO.INIT.getStatus());
        List<Long> frontIds = new ArrayList<>();
        if(frontRecordDetails!=null){
        for(ReservationRecordDetailE detailE : frontRecordDetails){
            WarehouseRecordDetail warehouseRecordDetail = entityFactory.createEntity(WarehouseRecordDetail.class);
            warehouseRecordDetail.createRecordDetailByFrontRecord(detailE);
            warehouseRecordDetail.setActualQty(detailE.getBasicSkuQty()==null?BigDecimal.ZERO:detailE.getBasicSkuQty());
            warehouseRecordDetail.setRealWarehouseId(frontRecord.getRealWarehouseId());
            warehouseRecordDetail.setVirtualWarehouseId(frontRecord.getVirtualWarehouseId());
            if(Objects.equals(ReservationBussTypeVo.CREATE.getType(),type) || Objects.equals(ReservationBussTypeVo.CREATE_DO.getType(),type)){
                warehouseRecordDetail.setPlanQty(detailE.getSkuQty());
            }else if(Objects.equals(ReservationBussTypeVo.LOCK.getType(),type)){
                //待锁定库存为未分配的数量
                warehouseRecordDetail.setPlanQty(detailE.getUnassignedQty());
            }else{
                //取消库存为已锁定的库存
                warehouseRecordDetail.setPlanQty(detailE.getAssignedQty());
            }
            this.warehouseRecordDetails.add(warehouseRecordDetail);
            if(!frontIds.contains(detailE.getFrontRecordId())){
                frontIds.add(detailE.getFrontRecordId());
                frontRecord.setId(detailE.getFrontRecordId());
                frontRecord.setRecordCode(detailE.getRecordCode());
                frontRecord.setRecordType(detailE.getRecordType());
                this.getAbstractFrontRecordList().add(frontRecord);
            }
        }
        //设置skuCode和skuID
        this.initWarehouseRecodeDetail();
       }
    }


    /**
     * 封装从上往下锁实仓DO对象
     * @return  当虚仓物料不存在时 返回null
     */
    public CoreChannelOrderDO buildCoreStockChannelOrderDo() {
        CoreChannelOrderDO coreChannelOrderDO =new CoreChannelOrderDO();
        coreChannelOrderDO.setChannelCode(this.getChannelCode());
        coreChannelOrderDO.setRecordCode(this.getRecordCode());
        coreChannelOrderDO.setTransType(this.getRecordType());
        coreChannelOrderDO.setMerchantId(this.getMerchantId());
            //虚仓信息
            List<CoreVirtualStockOpDO> coreOrderDetailDOList=new ArrayList<>();
            for (WarehouseRecordDetail sku : this.getWarehouseRecordDetails()) {
            if(sku.getPlanQty().compareTo(BigDecimal.ZERO)>0){
                    CoreVirtualStockOpDO vmDetailDO = new CoreVirtualStockOpDO();
                    vmDetailDO.setSkuId(sku.getSkuId());
                    vmDetailDO.setSkuCode(sku.getSkuCode());
                    vmDetailDO.setLockQty(sku.getPlanQty());
                    vmDetailDO.setVirtualWarehouseId(sku.getVirtualWarehouseId());
                    vmDetailDO.setRealWarehouseId(sku.getRealWarehouseId());
                    vmDetailDO.setRecordCode(this.getRecordCode());
                    coreOrderDetailDOList.add(vmDetailDO);
                }
            }
            coreChannelOrderDO.setVirtualStockOpDetailDOs(coreOrderDetailDOList);

            log.info("构建锁定最大库存入参:{}", JSON.toJSONString(coreChannelOrderDO));
            return coreChannelOrderDO;
        }

    /**
     * 释放锁定库存
     */
    public CoreRealStockOpDO initUnlockStock() {
        List<CoreRealStockOpDetailDO> increaseDetails = new ArrayList<>();
        List<CoreVirtualStockOpDO> virtualStockByCalculateDOs=new ArrayList<>();
        for (WarehouseRecordDetail detail : this.getWarehouseRecordDetails()) {
            //过滤小于等于0的数据
            CoreRealStockOpDetailDO coreRealStockOpDetailDO = new CoreRealStockOpDetailDO();
            //部分锁定时，未完全锁定
            if (this.checkUnLockQty()) {
                coreRealStockOpDetailDO.setUnlockQty(detail.getActualQty());
            } else {
                coreRealStockOpDetailDO.setUnlockQty(detail.getPlanQty());
            }
            //数量为0的不处理库存的增加和减少
            if (BigDecimal.ZERO.compareTo(coreRealStockOpDetailDO.getUnlockQty()) == 0) {
                continue;
            }
            coreRealStockOpDetailDO.setRealQty(detail.getPlanQty());
            coreRealStockOpDetailDO.setSkuId(detail.getSkuId());
            coreRealStockOpDetailDO.setRealWarehouseId(this.getRealWarehouseId());
            coreRealStockOpDetailDO.setCheckBeforeOp(true);
            coreRealStockOpDetailDO.setSkuCode(detail.getSkuCode());
            increaseDetails.add(coreRealStockOpDetailDO);
            if(null!=this.getVirtualWarehouseId()){
                CoreVirtualStockOpDO coreVirtualStockOpDO = new CoreVirtualStockOpDO();
                coreVirtualStockOpDO.setRecordCode(this.getRecordCode());
                coreVirtualStockOpDO.setTransType(this.getRecordType());
                coreVirtualStockOpDO.setSkuCode(detail.getSkuCode());
                coreVirtualStockOpDO.setSkuId(detail.getSkuId());
                coreVirtualStockOpDO.setUnlockQty(coreRealStockOpDetailDO.getUnlockQty());
                coreVirtualStockOpDO.setVirtualWarehouseId(this.getVirtualWarehouseId());
                virtualStockByCalculateDOs.add(coreVirtualStockOpDO);
            }
        }
        CoreRealStockOpDO coreRealStockOpDO = new CoreRealStockOpDO();
        coreRealStockOpDO.setRecordCode(this.getRecordCode());
        coreRealStockOpDO.setTransType(this.getRecordType());
        coreRealStockOpDO.setDetailDos(increaseDetails);
        coreRealStockOpDO.setVirtualStockByCalculateDOs(virtualStockByCalculateDOs);
        return coreRealStockOpDO;
    }

    /**
     * 根据预约单释放锁定库存 包含未全部锁定情况
     * @param reservationRecordE
     * @return
     */
    public CoreChannelOrderDO initResUnlockStockWithVm(ReservationRecordE reservationRecordE) {
        List<CoreVirtualStockOpDO> cvsList = new ArrayList<>();
        List<CoreOrderDetailDO> increaseDetails = new ArrayList<>();
        for (ReservationRecordDetailE detail : reservationRecordE.getReservationDetails()) {
            //过滤小于等于0的数据
            if(detail.getAssignedQty().compareTo(BigDecimal.ZERO)>0){
                CoreOrderDetailDO detailDO = new CoreOrderDetailDO();
                detailDO.setUnlockQty(detail.getAssignedQty());
                detailDO.setRealQty(detail.getAssignedQty());
                detailDO.setSkuId(detail.getSkuId());
                detailDO.setRealWarehouseId(reservationRecordE.getRealWarehouseId());
                detailDO.setChannelCode(reservationRecordE.getChannelCode());
                detailDO.setCheckBeforeOp(false);
                detailDO.setSkuCode(detail.getSkuCode());
                CoreVirtualStockOpDO coreVirtualStockDO = new CoreVirtualStockOpDO();
                coreVirtualStockDO.setUnlockQty(detail.getAssignedQty());
                coreVirtualStockDO.setVirtualWarehouseId(reservationRecordE.getVirtualWarehouseId());
                coreVirtualStockDO.setRealWarehouseId(reservationRecordE.getRealWarehouseId());
                coreVirtualStockDO.setRecordCode(this.getRecordCode());
                coreVirtualStockDO.setTransType(reservationRecordE.getRecordType());
                coreVirtualStockDO.setChannelCode(reservationRecordE.getChannelCode());
                coreVirtualStockDO.setMerchantId(detail.getMerchantId());
                coreVirtualStockDO.setSkuId(detail.getSkuId());
                coreVirtualStockDO.setSkuCode(detail.getSkuCode());
                cvsList.add(coreVirtualStockDO);
                increaseDetails.add(detailDO);
            }
        }
        CoreChannelOrderDO coreChannelOrderDO = new CoreChannelOrderDO();
        coreChannelOrderDO.setRecordCode(this.getRecordCode());
        coreChannelOrderDO.setChannelCode(this.getChannelCode());
        coreChannelOrderDO.setTransType(this.getRecordType());
        coreChannelOrderDO.setOrderDetailDOs(increaseDetails);
        coreChannelOrderDO.setVirtualStockOpDetailDOs(cvsList);
        return coreChannelOrderDO;
    }


    /**
     * 锁定库存对象构建
     * @param rwId
     * @return
     */
    @Override
    public CoreRealStockOpDO initLockStockObj(Long rwId){
        List<CoreRealStockOpDetailDO> increaseDetails = new ArrayList<>();
        for (WarehouseRecordDetail detail: this.getWarehouseRecordDetails()) {
            //数量为0的不处理库存的增加和减少
            if(BigDecimal.ZERO.compareTo(detail.getPlanQty()) == 0){
                continue;
            }
            CoreRealStockOpDetailDO coreRealStockOpDetailDO = new CoreRealStockOpDetailDO();
            coreRealStockOpDetailDO.setSkuId(detail.getSkuId());
            coreRealStockOpDetailDO.setSkuCode(detail.getSkuCode());
            coreRealStockOpDetailDO.setLockQty(detail.getPlanQty());
            coreRealStockOpDetailDO.setRealWarehouseId(rwId);
            increaseDetails.add(coreRealStockOpDetailDO);
        }
        CoreRealStockOpDO coreRealStockOpDO = new CoreRealStockOpDO();
        coreRealStockOpDO.setRecordCode(this.getRecordCode());
        coreRealStockOpDO.setTransType(this.getRecordType());
        coreRealStockOpDO.setDetailDos(increaseDetails);
        return coreRealStockOpDO;
    }



    /**
     * 外部系统数据创建时间:下单时间
     */
    private Date outCreateTime;

    /**
     * 前置单ID
     */
    private Long frontRecordId;

    /**
     * 前置单类型
     */
    private Integer frontRecordType;

    /**
     * 用户手机号
     */
    private String mobile;

    /**
     * 用户号
     */
    private String userCode;

    /**
     * 同步捋单系统状态  0-无需同步 1-待通知履单 2-已通知履单  10-待通知销售中心 20 已通知销售中心
     */
    private Integer syncFulfillmentStatus;
}    
