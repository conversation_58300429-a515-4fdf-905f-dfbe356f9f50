package com.rome.stock.innerservice.domain.entity.frontrecord;

import com.rome.stock.innerservice.constant.FrontRecordTypeVO;
import com.rome.stock.innerservice.domain.repository.frontrecord.FrAdjustForetasteRepository;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;

/**
 * 门店试吃调整单
 */
@Component
@Scope("prototype")
@Data
@EqualsAndHashCode(callSuper = false)
public class ShopForetasteRecordE extends AbstractFrontRecord{

    @Resource
    private FrAdjustForetasteRepository frAdjustForetasteRepository;

    /**
     * 创建前置单据
     */
    public void addRecord() {
        this.setRecordType(FrontRecordTypeVO.SHOP_FORETASTE_RECORD.getType());
        initFrontRecord(FrontRecordTypeVO.SHOP_FORETASTE_RECORD.getCode(), this.frontRecordDetails);
        long id=frAdjustForetasteRepository.saveFrAdjustForetaste(this);
        this.setId(id);
        this.frontRecordDetails.forEach(record -> record.setFrontRecordDetail(this) );
        frAdjustForetasteRepository.saveFrAdjustForetasteDetail(this.frontRecordDetails);
    }

    /**
     * 渠道id
     */
    private String channelCode;

    /**
     * 出向实体仓库id
     */
    private Long outRealWarehouseId;

    /**
     * 实仓id
     */
    private Long realWarehouseId;

    private Long creator;

    private String shopCode;

    private Integer reason;

    private String sapRecordCode;
    /**
     * 商品数量
     */
    private List<ShopForetasteRecordDetailE> frontRecordDetails;
}
