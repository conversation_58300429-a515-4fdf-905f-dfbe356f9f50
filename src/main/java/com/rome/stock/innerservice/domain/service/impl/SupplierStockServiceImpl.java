package com.rome.stock.innerservice.domain.service.impl;

import com.alibaba.fastjson.JSON;
import com.rome.arch.core.exception.RomeException;
import com.rome.arch.util.dynamic.DynamicDataSourceEnum;
import com.rome.arch.util.dynamic.TargetDataSource;
import com.rome.stock.common.utils.DateUtil;
import com.rome.stock.innerservice.api.dto.supplier.SupplierStockGroupDTO;
import com.rome.stock.innerservice.common.AlikAssert;
import com.rome.stock.innerservice.common.BomConvertUtil;
import com.rome.stock.innerservice.common.RomeCollectionUtil;
import com.rome.stock.innerservice.common.StringUtils;
import com.rome.stock.innerservice.domain.entity.RealWarehouseE;
import com.rome.stock.innerservice.domain.repository.RealWarehouseRepository;
import com.rome.stock.innerservice.domain.repository.supplier.RealWarehouseStockDaySnapshotRepository;
import com.rome.stock.innerservice.domain.repository.supplier.SupplierStockRepository;
import com.rome.stock.innerservice.domain.service.SupplierStockService;
import com.rome.stock.innerservice.infrastructure.dataobject.supplier.RealWarehouseStockDaySnapshotDO;
import com.rome.stock.innerservice.infrastructure.dataobject.supplier.SupplierStockDO;
import com.rome.stock.innerservice.infrastructure.mapper.RealWarehouseStockMapper;
import com.rome.stock.innerservice.remote.base.dto.StoreDTO;
import com.rome.stock.innerservice.remote.base.facade.ShopFacade;
import com.rome.stock.innerservice.remote.item.dto.*;
import com.rome.stock.innerservice.remote.item.facade.SkuFacade;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.springframework.context.ApplicationContext;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

/**
 * SupplierStockService 实现
 *
 * <AUTHOR>
 * @date 2022-05-09
 */
@Slf4j
@Service
public class SupplierStockServiceImpl implements SupplierStockService {

    @Resource
    private SupplierStockRepository supplierStockRepository;

    @Resource
    private RealWarehouseStockDaySnapshotRepository realWarehouseStockDaySnapshotRepository;

    @Resource
    private RealWarehouseRepository realWarehouseRepository;

    @Resource
    private RealWarehouseStockMapper realWarehouseStockMapper;
    @Resource
    private BomConvertUtil bomConvertUtil;
    @Resource
    private SkuFacade skuFacade;
    @Resource
    private ShopFacade shopFacade;
    @Resource
    private ApplicationContext applicationContext;

    @Override
    //@Transactional(rollbackFor = Exception.class)
    public void calSupplierDailyStock(String stockDate) {
        //做幂等先查询是否存在数据，如果存在直接返回成功
        Integer flag = supplierStockRepository.queryIfExist(stockDate);
        if (Objects.equals(1, flag)) {
            return;
        }
        Date stockDateTime = DateUtil.parseDate(stockDate);
        //查询快照，如果快照不存在直接返回失败
        List<SupplierStockGroupDTO> groupDTOS = realWarehouseStockDaySnapshotRepository.listDistinctGroupDTO(stockDateTime);
        AlikAssert.notEmpty(groupDTOS, "999", "快照信息不存在");
        //先查询出所有sku的boom信息，（总用也就4w不到的物料数）
        List<String> skuCodes = realWarehouseStockDaySnapshotRepository.listDistinctSkuCode(stockDateTime);;
        Map<String, CombineSkuResultDTO> skuBomSkuMap = bomConvertUtil.convertBomSkuBySkuCodes(skuCodes);
        //再查询出所有sku的单位信息 （总用也就4w不到的物料数）
        Map<String, SkuInfoExtDTO> skuInfoExtDTOMap = new HashMap<>();
        this.setSkuInfo(skuInfoExtDTOMap, skuCodes);

        //查询sku供应商信息
        Map<String, SkuWarehouseParamDTO> skuFactorySupplierMap = skuFacade.getSkuWareHouseBySkuCodes(skuCodes);

        //根据公司分组
        for (SupplierStockGroupDTO groupDTO : groupDTOS) {
            Integer stockType = groupDTO.getStockType();
            String companyCode = groupDTO.getCompanyCode();
            String deliveryFactory = groupDTO.getDeliveryFactory();
            List<RealWarehouseStockDaySnapshotDO> snapshotDOList = realWarehouseStockDaySnapshotRepository.listByGroupDTO(groupDTO);
           //根据发货工厂分组计算库存数据（最终唯一维度为：公司编码 - 库存类型 - 发货工厂 - skuCode ）
            Map<String, SupplierStockDO> skuCodeSupplierStockDOMap = new HashMap<>();
            List<SupplierStockDO> supplierStockDOS = new ArrayList<>();
            for (RealWarehouseStockDaySnapshotDO realWarehouseStockDaySnapshotDO : snapshotDOList) {
                SupplierStockDO supplierStockDO = new SupplierStockDO();
                supplierStockDO.setCompanyCode(companyCode);
                supplierStockDO.setStockType(stockType);
                supplierStockDO.setDeliveryFactory(realWarehouseStockDaySnapshotDO.getDeliveryFactory());
                supplierStockDO.setSkuId(realWarehouseStockDaySnapshotDO.getSkuId());
                supplierStockDO.setSkuCode(realWarehouseStockDaySnapshotDO.getSkuCode());
                supplierStockDO.setRealQty(realWarehouseStockDaySnapshotDO.getRealQty());
                supplierStockDO.setOnroadQty(realWarehouseStockDaySnapshotDO.getOnroadQty());
                supplierStockDO.setCombineRealQty(BigDecimal.ZERO);
                supplierStockDO.setCombineOnroadQty(BigDecimal.ZERO);
                supplierStockDO.setStockDate(stockDateTime);
                supplierStockDOS.add(supplierStockDO);
                skuCodeSupplierStockDOMap.put(realWarehouseStockDaySnapshotDO.getSkuCode(), supplierStockDO);
            }
            for (SupplierStockDO supplierStockDO : supplierStockDOS) {
                String path = supplierStockDO.getSkuCode();
                //重新赋值
                supplierStockDO.setCalBomRealQty(supplierStockDO.getRealQty());
                supplierStockDO.setCalBomOnroadQty(supplierStockDO.getOnroadQty());
                this.calCombineQty(supplierStockDO, skuCodeSupplierStockDOMap, skuBomSkuMap, 1, path);
            }
            //skuCodeSupplierStockDOMap 可能会有新增的数据
            supplierStockDOS = new ArrayList<>(skuCodeSupplierStockDOMap.values());
            if (CollectionUtils.isEmpty(supplierStockDOS)) {
                continue;
            }
            //判断是否有新的没有查询过单位信息的sku
            List<String> notExistSkuCodes = supplierStockDOS.stream().filter(item -> !skuInfoExtDTOMap.containsKey(item.getSkuCode())).map(SupplierStockDO::getSkuCode).distinct().collect(Collectors.toList());
            this.setSkuInfo(skuInfoExtDTOMap, notExistSkuCodes);
            List<String> notExistSupplierSkuCodes = supplierStockDOS.stream().filter(item -> !skuFactorySupplierMap.containsKey(item.getSkuCode())).map(SupplierStockDO::getSkuCode).distinct().collect(Collectors.toList());
            this.setSkuSupplierInfo(skuFactorySupplierMap, notExistSupplierSkuCodes);

            //设置商品信息
            for (SupplierStockDO supplierStockDO : supplierStockDOS) {
                SkuInfoExtDTO skuInfoExtDTO = skuInfoExtDTOMap.get(supplierStockDO.getSkuCode());
                if (skuInfoExtDTO == null) {
                    supplierStockDO.setUnitName("");
                    supplierStockDO.setUnitCode("");
                    supplierStockDO.setSkuName("");
                } else {
                    supplierStockDO.setUnitName(skuInfoExtDTO.getSpuUnitName());
                    supplierStockDO.setUnitCode(skuInfoExtDTO.getSpuUnitCode());
                    supplierStockDO.setSkuName(skuInfoExtDTO.getName());
                }

                //设置供应商信息
                SkuWarehouseParamDTO skuWarehouseParamDTO = skuFactorySupplierMap.get(supplierStockDO.getSkuCode());
                if (skuWarehouseParamDTO != null ) {
                    //先默认取最外层的
                    supplierStockDO.setSupplierCode(skuWarehouseParamDTO.getSupplierCode());
                    supplierStockDO.setSupplierName(skuWarehouseParamDTO.getSupplierName());
                    //如果存在多供应商，则将原来的覆盖掉
                    if (MapUtils.isNotEmpty(skuWarehouseParamDTO.getSkuExtMap())) {
                        SkuWarehouseExtDTO skuWarehouseExtDTO = skuWarehouseParamDTO.getSkuExtMap().get(deliveryFactory);
                        if (skuWarehouseExtDTO != null && StringUtils.isNotBlank(skuWarehouseExtDTO.getSupplierAffiliatedTo())) {
                            supplierStockDO.setSupplierCode(skuWarehouseExtDTO.getSupplierAffiliatedTo());
                            supplierStockDO.setSupplierName(skuWarehouseExtDTO.getSupplierName());
                        }
                    }
                }

                if (StringUtils.isBlank(supplierStockDO.getSupplierCode())) {
                    supplierStockDO.setSupplierCode("-");
                }
                if (StringUtils.isBlank(supplierStockDO.getSupplierName())) {
                    supplierStockDO.setSupplierName("");
                }
            }
            supplierStockDOS.removeIf(item-> BigDecimal.ZERO.compareTo(item.getRealQty()) == 0
                    && BigDecimal.ZERO.compareTo(item.getOnroadQty()) == 0
                    && BigDecimal.ZERO.compareTo(item.getCombineOnroadQty()) == 0
                    && BigDecimal.ZERO.compareTo(item.getCombineRealQty()) == 0);
            if (CollectionUtils.isNotEmpty(supplierStockDOS)) {
                List<List<SupplierStockDO>> lists = RomeCollectionUtil.splitList(supplierStockDOS, 1000);
                for (List<SupplierStockDO> list : lists) {
                    supplierStockRepository.batchInsert(list);
                }
            }
        }
        supplierStockRepository.updateAvailable(stockDateTime);
    }

    /**
     * 查询单位信息
     *
     * @param skuInfoExtDTOMap
     * @param skuCodes
     */
    private void setSkuInfo(Map<String, SkuInfoExtDTO> skuInfoExtDTOMap, List<String> skuCodes) {
        if (CollectionUtils.isEmpty(skuCodes)) {
            return;
        }
        for (String skuCode : skuCodes) {
            skuInfoExtDTOMap.put(skuCode, null);
        }
        List<SkuInfoExtDTO> skuInfoExtDTOS = skuFacade.skusBySkuCode(skuCodes);
        Map<String, SkuInfoExtDTO> skuMap = RomeCollectionUtil.listforMap(skuInfoExtDTOS, "skuCode");
        skuInfoExtDTOMap.putAll(skuMap);
    }

    private void setSkuSupplierInfo( Map<String, SkuWarehouseParamDTO> skuFactorySupplierMap, List<String> skuCodes) {
        if (CollectionUtils.isEmpty(skuCodes)) {
            return;
        }
        Map<String, SkuWarehouseParamDTO> skuMap = skuFacade.getSkuWareHouseBySkuCodes(skuCodes);
        skuFactorySupplierMap.putAll(skuMap);
    }


    /**
     * 计算组合数据
     *
     * @param supplierStockDO
     * @param skuCodeSupplierStockDOMap
     * @param skuBomSkuMap
     * @param level
     */
    private void calCombineQty(SupplierStockDO supplierStockDO, Map<String, SupplierStockDO> skuCodeSupplierStockDOMap, Map<String, CombineSkuResultDTO> skuBomSkuMap, int level, String path) {
        if (level > 5) {
            throw new RomeException("999", "超过最大组合深度：" + path);
        }
        CombineSkuResultDTO bomSkuBatchDTO = skuBomSkuMap.get(supplierStockDO.getSkuCode());
        if (bomSkuBatchDTO != null && CollectionUtils.isNotEmpty(bomSkuBatchDTO.getCombineSonSkuResultDTOS())) {
            supplierStockDO.setIsCombineLeaf(0);
            supplierStockDO.setBomJson(JSON.toJSONString(bomSkuBatchDTO));
            for (CombineSonSkuResultDTO combineSonSkuResultDTO : bomSkuBatchDTO.getCombineSonSkuResultDTOS()) {
                if (supplierStockDO.getSkuCode().equals(combineSonSkuResultDTO.getCombineSkuCode())) {
                    //如果子品就是自己，不再计算
                    continue;
                }
                SupplierStockDO childSupplierStockDO = skuCodeSupplierStockDOMap.get(combineSonSkuResultDTO.getCombineSkuCode());
                if (childSupplierStockDO == null) {
                    childSupplierStockDO = new SupplierStockDO();
                    childSupplierStockDO.setCompanyCode(supplierStockDO.getCompanyCode());
                    childSupplierStockDO.setSkuId(combineSonSkuResultDTO.getCombineSkuId());
                    childSupplierStockDO.setStockType(supplierStockDO.getStockType());
                    childSupplierStockDO.setDeliveryFactory(supplierStockDO.getDeliveryFactory());
                    childSupplierStockDO.setSkuCode(combineSonSkuResultDTO.getCombineSkuCode());
                    childSupplierStockDO.setRealQty(BigDecimal.ZERO);
                    childSupplierStockDO.setOnroadQty(BigDecimal.ZERO);
                    childSupplierStockDO.setCombineRealQty(BigDecimal.ZERO);
                    childSupplierStockDO.setCombineOnroadQty(BigDecimal.ZERO);
                    childSupplierStockDO.setStockDate(supplierStockDO.getStockDate());
                    skuCodeSupplierStockDOMap.put(combineSonSkuResultDTO.getCombineSkuCode(), childSupplierStockDO);
                }

                childSupplierStockDO.setCalBomOnroadQty(supplierStockDO.getCalBomOnroadQty().multiply(combineSonSkuResultDTO.getBasicSkuQty()));
                childSupplierStockDO.setCalBomRealQty(supplierStockDO.getCalBomRealQty().multiply(combineSonSkuResultDTO.getBasicSkuQty()));

                childSupplierStockDO.setCombineOnroadQty(childSupplierStockDO.getCombineOnroadQty().add(childSupplierStockDO.getCalBomOnroadQty()));
                childSupplierStockDO.setCombineRealQty(childSupplierStockDO.getCombineRealQty().add(childSupplierStockDO.getCalBomRealQty()));
                List<String> bomPathList = childSupplierStockDO.getBomPathList();
                if (bomPathList == null) {
                    bomPathList = new ArrayList<>();
                    childSupplierStockDO.setBomPathList(bomPathList);
                }
                String bomPath = path + "#" + combineSonSkuResultDTO.getCombineSkuCode();
                bomPathList.add(bomPath);
                childSupplierStockDO.setBomPath(JSON.toJSONString(bomPathList));
                this.calCombineQty(childSupplierStockDO, skuCodeSupplierStockDOMap, skuBomSkuMap, level + 1, bomPath);
            }
        } else if (skuBomSkuMap.containsKey(supplierStockDO.getSkuCode())) {
            // 已经查询过bom但是 明细为空
            supplierStockDO.setIsCombineLeaf(1);
        } else {
            //不存在， 也没有查询过，需要再查一遍，
            Map<String, CombineSkuResultDTO> itemBomMap = bomConvertUtil.convertBomSkuBySkuCodes(Arrays.asList(supplierStockDO.getSkuCode()));
            //存在bom，将bom放入map中
            skuBomSkuMap.putAll(itemBomMap);
            //不算次数，继续走一遍
            this.calCombineQty(supplierStockDO, skuCodeSupplierStockDOMap, skuBomSkuMap, level, path);
        }

    }

    @Override
    public void saveStockSnapshotForSupplierDailyStock(String stockDate) {
        log.info("开始保存供应商日结库存快照,参数:{},开始时间:{}", stockDate,DateUtil.now());
        Date stockDateTime = DateUtil.parseDate(stockDate);
        List<SupplierStockGroupDTO>  companyCodes = realWarehouseStockDaySnapshotRepository.listDistinctGroupDTO(stockDateTime);
        if (CollectionUtils.isNotEmpty(companyCodes)) {
            return;
        }
        List<RealWarehouseE> realWarehouseList = realWarehouseRepository.getRealWarehouseAllList();

        List<String> shopCodes = realWarehouseList.stream().filter(item -> (item.getRwBusinessType() != null && (item.getRwBusinessType() == 1 || item.getRwBusinessType() == 2))).map(RealWarehouseE::getShopCode).collect(Collectors.toList());

        List<StoreDTO> storeDTOS = shopFacade.searchByCodeList(shopCodes);
        Map<String, StoreDTO> storeDTOMap = RomeCollectionUtil.listforMap(storeDTOS, "code");

        Map<String, Map<String, List<Long>>> storeCompanyFactoryMapList = new HashMap<>();
        Map<String, Map<String, List<Long>>> whCompanyFactoryMapList = new HashMap<>();

        for (RealWarehouseE realWarehouseE : realWarehouseList) {
            if (realWarehouseE.getRwBusinessType() != null && (realWarehouseE.getRwBusinessType() == 1 || realWarehouseE.getRwBusinessType() == 2)) {
                StoreDTO storeDTO = storeDTOMap.get(realWarehouseE.getShopCode());
                if (storeDTO == null) {
                    log.error("未查询到主数据门店信息shopCode：" + realWarehouseE.getShopCode());
                    continue;
                }
                Map<String, List<Long>> factoryMapList = storeCompanyFactoryMapList.computeIfAbsent(realWarehouseE.getCompanyCode(), k -> new HashMap<>());
                List<Long> realWarehouseES = factoryMapList.computeIfAbsent(storeDTO.getActualWarehouse(), k -> new ArrayList<>());
                realWarehouseES.add(realWarehouseE.getId());
            } else {
                Map<String, List<Long>> factoryMapList = whCompanyFactoryMapList.computeIfAbsent(realWarehouseE.getCompanyCode(), k -> new HashMap<>());
                List<Long> realWarehouseES = factoryMapList.computeIfAbsent(realWarehouseE.getFactoryCode(), k -> new ArrayList<>());
                realWarehouseES.add(realWarehouseE.getId());
            }
        }

        //用主库保存,线上数据，一天也就5w~6w
        SupplierStockService supplierStockService = applicationContext.getBean(SupplierStockService.class);
        //查询备库，封装数据
        List<RealWarehouseStockDaySnapshotDO> list = new ArrayList<>();
        list.addAll(supplierStockService.praseDaySnapshotList(whCompanyFactoryMapList, 1, stockDateTime));
        list.addAll(supplierStockService.praseDaySnapshotList(storeCompanyFactoryMapList, 2, stockDateTime));
        //切换主库，插入数据
        //过滤掉发货仓为空的数据
        list=list.stream().filter(v->Objects.nonNull(v.getDeliveryFactory())).collect(Collectors.toList());
        supplierStockService.saveDaySnapshot(list);
        log.info("开始保存供应商日结库存快照,结束时间:{}",DateUtil.now());
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void saveDaySnapshot(List<RealWarehouseStockDaySnapshotDO> saveList) {
        if (CollectionUtils.isEmpty(saveList)) {
            return;
        }
        List<List<RealWarehouseStockDaySnapshotDO>> lists = RomeCollectionUtil.splitList(saveList, 1000);
        for (List<RealWarehouseStockDaySnapshotDO> list : lists) {
            realWarehouseStockDaySnapshotRepository.batchInsert(list);
        }
    }


    @Override
    @TargetDataSource(DynamicDataSourceEnum.READ)
    public List<RealWarehouseStockDaySnapshotDO> praseDaySnapshotList(Map<String, Map<String, List<Long>>> storeCompanyFactoryMapList, Integer stockType, Date stockDateTime) {
        List<RealWarehouseStockDaySnapshotDO> result = new ArrayList<>();
        for (Map.Entry<String, Map<String, List<Long>>> entry : storeCompanyFactoryMapList.entrySet()) {
            String companyCode = entry.getKey();
            if (StringUtils.isBlank(companyCode)) {
                continue;
            }
            Map<String, List<Long>> factoryMap = entry.getValue();
            for (Map.Entry<String, List<Long>> factoryEntry : factoryMap.entrySet()) {
                String factoryCode = factoryEntry.getKey();
                List<Long> realWarehouseIds = factoryEntry.getValue();
                // 按每50个仓库ID分批查询
                List<RealWarehouseStockDaySnapshotDO> itemList = this.querySupplierDailyStockInBatches(realWarehouseIds, stockDateTime, companyCode, factoryCode);
                //删除掉在途和实际库存为0的数据
                itemList.removeIf(item -> item.getOnroadQty().compareTo(BigDecimal.ZERO) == 0 && item.getRealQty().compareTo(BigDecimal.ZERO) == 0);
                if (CollectionUtils.isEmpty(itemList)) {
                    continue;
                }
                itemList.forEach(item -> item.setStockType(stockType));
                result.addAll(itemList);
            }
        }
        return result;
    }

    /**
     * 分批查询供应商日结库存数据
     * @param realWarehouseIds 仓库ID列表
     * @param stockDateTime 库存日期
     * @param companyCode 公司编码
     * @param factoryCode 工厂编码
     * @return 合并后的库存数据
     */
    private List<RealWarehouseStockDaySnapshotDO> querySupplierDailyStockInBatches(List<Long> realWarehouseIds, Date stockDateTime, String companyCode, String factoryCode) {
        if (CollectionUtils.isEmpty(realWarehouseIds)) {
            return new ArrayList<>();
        }
        // 按每50个ID分批
        List<List<Long>> batches = RomeCollectionUtil.splitList(realWarehouseIds, 50);
        Map<String, RealWarehouseStockDaySnapshotDO> mergedResult = new HashMap<>();
        for (List<Long> batchList : batches) {
            List<RealWarehouseStockDaySnapshotDO> batchResult = realWarehouseStockMapper.sumSupplierDailyStock(batchList, stockDateTime, companyCode, factoryCode);
            // 合并批次结果，按sku_code汇总
            for (RealWarehouseStockDaySnapshotDO item : batchResult) {
                String skuCode = item.getSkuCode();
                if (mergedResult.containsKey(skuCode)) {
                    // 如果已存在相同SKU，则累加数量
                    RealWarehouseStockDaySnapshotDO existing = mergedResult.get(skuCode);
                    existing.setRealQty(existing.getRealQty().add(item.getRealQty()));
                    existing.setOnroadQty(existing.getOnroadQty().add(item.getOnroadQty()));
                } else {
                    // 如果不存在，则直接添加
                    mergedResult.put(skuCode, item);
                }
            }
        }
        return new ArrayList<>(mergedResult.values());
    }

    @Override
    public void rollBackCal(String stockDate) {
        supplierStockRepository.deletedByStockDate(stockDate);
    }

    @Override
    public void deleteSnapshot(String stockDate) {
        boolean executeFlag = true;
        do {
            int executeNum = realWarehouseStockDaySnapshotRepository.deleteSnapshot(stockDate,1000);
            //每次删除1000条，当删除条数小于1000时，表示已经删除完成，退出循环
            if (executeNum < 1000){
                executeFlag=false;
            }
        }while (executeFlag);
    }
}
