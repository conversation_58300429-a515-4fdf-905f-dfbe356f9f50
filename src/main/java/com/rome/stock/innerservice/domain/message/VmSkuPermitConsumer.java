/**
 * Filename VmSkuPermitConsumer.java
 * Company 上海来伊份科技有限公司。
 * <AUTHOR>
 * @version 
 */
package com.rome.stock.innerservice.domain.message;

import javax.annotation.Resource;
import org.apache.rocketmq.client.consumer.DefaultMQPushConsumer;
import org.apache.rocketmq.common.consumer.ConsumeFromWhere;
import org.apache.rocketmq.common.message.MessageExt;
import org.apache.rocketmq.spring.annotation.RocketMQMessageListener;
import org.apache.rocketmq.spring.core.RocketMQListener;
import org.apache.rocketmq.spring.core.RocketMQPushConsumerLifecycleListener;
import org.springframework.stereotype.Service;
import com.rome.stock.innerservice.api.dto.message.SkuPublishPurchaseDTO;
import com.rome.stock.innerservice.common.CoreKibanaLog;
import com.rome.stock.innerservice.domain.service.VmSkuPermitService;
import lombok.extern.slf4j.Slf4j;

/**
 * 虚仓进货权 MQ消费
 * 增加改变数据
 * <AUTHOR>
 * @since 2020年4月21日 下午4:56:19
 */
@Slf4j
@Service
//@RocketMQMessageListener(topic = "${rocketmq.consumer.vmskupermit.topic:item-core}", selectorExpression = "publish_and_purchase_update_by_sap", consumerGroup = "${rocketmq.consumer.vmskupermit.group:core-vmskupermit-group}")
public class VmSkuPermitConsumer implements RocketMQListener<MessageExt>, RocketMQPushConsumerLifecycleListener {

	@Resource
	private VmSkuPermitService vmSkuPermitService;
	
	@Override
    public void onMessage(MessageExt msg) {
		return;
	}
	
	@Override
	public void prepareStart(DefaultMQPushConsumer consumer) {
        consumer.setConsumeFromWhere(ConsumeFromWhere.CONSUME_FROM_FIRST_OFFSET);
	}
}
