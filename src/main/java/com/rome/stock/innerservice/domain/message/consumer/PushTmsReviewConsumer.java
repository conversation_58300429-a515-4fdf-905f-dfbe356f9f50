package com.rome.stock.innerservice.domain.message.consumer;

import com.rome.scm.common.rocketmq.CustomConsumerListener;
import com.rome.stock.common.enums.CustomRocketMQEnum;
import com.rome.stock.innerservice.common.StringUtils;
import com.rome.stock.innerservice.domain.service.ShopReplenishService;
import lombok.extern.slf4j.Slf4j;
import org.apache.rocketmq.common.message.MessageExt;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;

/**
 * @Description: 拼团-推送TMS复核实际消息
 */
@Slf4j
@Service
public class PushTmsReviewConsumer implements CustomConsumerListener<MessageExt> {

    @Resource
    private ShopReplenishService shopReplenishService;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void onMessage(MessageExt messageExt, String businessNo, String msgKey) {
        String recordCode = new String(messageExt.getBody());
        log.info("拼团-推送TMS复核实际消息消息消费，msgID: {}，recordCode: {}", messageExt.getMsgId(), recordCode);
        if(StringUtils.isEmpty(recordCode)) {
            log.error("拼团-推送TMS复核实际消息消费内容为空，msgID: {}", messageExt.getMsgId());
            return ;
        }
        //拼团-推送TMS复核实际消息
        shopReplenishService.pushTmsReview(recordCode);
    }

    @Override
    public String getRocketMQCode() {
        return CustomRocketMQEnum.MQ_PUSH_TMS_REVIEW.getCode();
    }

}
