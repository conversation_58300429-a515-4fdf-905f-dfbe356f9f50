package com.rome.stock.innerservice.domain.entity;

import com.rome.stock.innerservice.domain.entity.frontrecord.AbstractFrontRecordDetail;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Component;

@Component
@Scope("prototype")
@Data
@EqualsAndHashCode(callSuper = false)
public class RwRecordPoolDetailE extends AbstractFrontRecordDetail {

    /**
     * 详情放入单据池关联数据
     */
    public void setRwRecordPoolDetail(RwRecordPoolE rwRecordPoolE){
        this.setRecordPoolId(rwRecordPoolE.getId());
        this.setDoCode(rwRecordPoolE.getDoCode());
    }

    private String doCode;
    /**
     * Do池主键引用
     */
    private Long recordPoolId;
    /**
     * 实仓ID
     */
    private Long realWarehouseId;
    /**
     * 虚仓ID
     */
    private Long virtualWarehouseId;

    private String lineNo;

}
