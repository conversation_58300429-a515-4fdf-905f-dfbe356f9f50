package com.rome.stock.innerservice.domain.entity;

import com.rome.stock.innerservice.domain.convertor.VirtualWarehouseStockConvertor;
import com.rome.stock.innerservice.domain.entity.warehouserecord.AbstractWarehouseRecord;
import com.rome.stock.innerservice.domain.repository.VirtualWarehouseRepository;
import com.rome.stock.innerservice.domain.repository.VirtualWarehouseStockRepository;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Component;

import java.util.List;

@Component
@Scope("prototype")
@Data
@EqualsAndHashCode(callSuper = false)
public class VirtualWarehouseE extends BaseE {

    @Autowired
    private VirtualWarehouseRepository virtualWarehouseRepository;

    @Autowired
    private VirtualWarehouseStockRepository virtualWarehouseStockRepository;

    @Autowired
    private VirtualWarehouseStockConvertor virtualWarehouseStockConvertor;


    /**
     * 增加虚仓库存
     *
     * @param warehouseRecord
     * @param type            1:增加在途  2: 增加账面库存
     */
    public void addVirtualWareStock(AbstractWarehouseRecord warehouseRecord, int type) {
        //依赖单据明细驱动库存变化
    }


    /**
     * 减少虚仓库存
     *
     * @param warehouseRecord
     * @param type            1:减少在途 增加账面库存 2: 减少账面库存
     */
    public void reduceVirtualWareStock(AbstractWarehouseRecord warehouseRecord, int type) {
        //依赖单据明细驱动库存变化
    }


    //检查仓库的状态
    public void validVirtualWare() throws Exception {
        //
    }

    //查询虚仓库存
//    public List<StockE> getVirtualWarehouseStock(List<SkuE> skuList, List<Long> vwIdList) {
//        //1、参数校验
//        if (CollectionUtils.isEmpty(skuList)) {
//            throw new RomeException("999", "参数列表不能为空");
//        }
//        if (CollectionUtils.isEmpty(skuList)) {
//            throw new RomeException("999", "参数列表不能为空");
//        }
//        for (SkuE sku : skuList) {
//            Optional.ofNullable(sku.getSkuId()).orElseThrow(() -> new RomeException("999", "skuId不能为空"));
//            Optional.ofNullable(sku.getMerchantId()).orElseThrow(() -> new RomeException("999", "merchantId不能为空"));
//        }
//        //2、查询虚仓的库存
//        List<StockE> stockList = virtualWarehouseStockRepository.getVwStockBacth(skuList, vwIdList);
//        return stockList;
//    }

    /**
     * 虚拟仓库编码
     */
    private String virtualWarehouseCode;
    /**
     * 虚拟仓库名称
     */
    private String virtualWarehouseName;
    /**
     * 实体仓库id
     */
    private Long realWarehouseId;
    /**
     * 仓库类型：1-独享虚仓，2-共享虚仓
     */
    private Integer virtualWarehouseType;
    /**
     * 仓库状态：0-初始，1-启用，2-停用
     */
    private Integer virtualWarehouseStatus;
    /**
     * 虚拟仓库库存操作优先级 1为最高
     */
    private Integer virtualWarehousePriority;
    /**
     * 仓库是否允许负库存：0-否，1-是
     */
    private Integer virtualWarehouseIsNegative;
    /**
     * 覆盖区域是使用实仓覆盖区域，0-否，1-是
     */
    private Integer useRealWarehouseArea;
    /**
     * 备注信息
     */
    private String virtualWarehouseRemark;
    /**
     * 同步比率（百分比），1-100区间可选数字
     */
    private Integer syncRate;
    /**
     * 工厂编码
     */
    private String factoryCode;

    /**
     * 仓库外部编码
     */
    private String realWarehouseOutCode;
    /**
     * 实体仓库ids
     */
    private List<Long> realWarehouseIds;
    /**
     * 实体仓库code
     */
    private String realWarehouseCode;
    /**
     * 实体仓库name
     */
    private String realWarehouseName;
    /**
     * 工厂name
     */
    private String factoryName;
    /**
     * 虚拟仓库类型列表
     */
    private List<Integer> virtualWarehouseTypeList;
}
