package com.rome.stock.innerservice.domain.message.consumer;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.rome.scm.common.rocketmq.CustomConsumerListener;
import com.rome.stock.common.enums.CustomRocketMQEnum;
import com.rome.stock.common.enums.warehouse.SaleStatusVO;
import com.rome.stock.innerservice.api.dto.SkuStatusChangeSendMessageDTO;
import com.rome.stock.innerservice.domain.service.PurchaseOrderService;
import lombok.extern.slf4j.Slf4j;
import org.apache.rocketmq.common.message.MessageExt;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 * @Description: 品类通知库存中心商品状态变更(仅消费)
 */
@Slf4j
@Service
public class SkuStatusChangeNoticeConsumer implements CustomConsumerListener<MessageExt> {


    @Resource
    private PurchaseOrderService purchaseOrderService;


    @Override
    public void onMessage(MessageExt messageExt, String businessNo, String msgKey) {
        String json = new String(messageExt.getBody());
        log.info("品类通知库存中心商品状态变更，msgID: {}，消费次数: {},消费内容:{}", messageExt.getMsgId(), messageExt.getReconsumeTimes(),json);
        List<SkuStatusChangeSendMessageDTO> messageList= JSONObject.parseArray(json, SkuStatusChangeSendMessageDTO.class);
        for (SkuStatusChangeSendMessageDTO messageDTO : messageList) {
            purchaseOrderService.skuStatusChangeNotice(messageDTO);
        }
    }

	@Override
	public String getRocketMQCode() {
		return CustomRocketMQEnum.MQ_SKU_STATUS_CHANGE_NOTICE.getCode();
	}

}
