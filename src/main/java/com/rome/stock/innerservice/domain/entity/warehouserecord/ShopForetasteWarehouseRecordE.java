package com.rome.stock.innerservice.domain.entity.warehouserecord;

import com.rome.arch.core.domain.EntityFactory;
import com.rome.stock.common.constants.warehouse.WarehouseRecordConstant;
import com.rome.stock.common.enums.warehouse.WarehouseRecordTypeVO;
import com.rome.stock.innerservice.api.dto.warehouserecord.OutWarehouseRecordDTO;
import com.rome.stock.innerservice.api.dto.warehouserecord.RecordDetailDTO;
import com.rome.stock.innerservice.constant.WarehouseRecordBatchStatusVO;
import com.rome.stock.innerservice.constant.WarehouseRecordBusinessTypeVO;

import com.rome.stock.innerservice.constant.WmsSyncStatusVO;
import com.rome.stock.innerservice.domain.entity.RealWarehouseE;
import com.rome.stock.innerservice.domain.entity.frontrecord.ShopForetasteRecordDetailE;
import com.rome.stock.innerservice.domain.entity.frontrecord.ShopForetasteRecordE;
import com.rome.stock.innerservice.domain.repository.warehouserecord.ShopForetasteWarehouseRepository;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;

/**
 * 门店试吃调整单
 *
 * <AUTHOR> 2019/4/17 20:52
 */
@Data
@Slf4j
@Component
@Scope("prototype")
@EqualsAndHashCode(callSuper = false)
public class ShopForetasteWarehouseRecordE extends AbstractWarehouseRecord{

    @Resource
    private EntityFactory entityFactory;

    @Resource
    private ShopForetasteWarehouseRepository shopForetasteWarehouseRepository;

    /**
     * 创建仓库单据
     */
    public void addRecord() {
        long id= shopForetasteWarehouseRepository.saveShopForetasteWarehouseRecord(this);
        this.setId(id);
        this.warehouseRecordDetails.forEach(record -> record.setWarehouseRecordDetail(this));
        shopForetasteWarehouseRepository.saveWarehouseRecordDetails(this.warehouseRecordDetails);
//        this.addFrontRecordAndWarehouseRelation();
    }

    /**
     * 根据前置单生成出库单据
     */
    public void createOutRecordByFrontRecord(OutWarehouseRecordDTO dto, RealWarehouseE realWarehouseE){
        createRecodeCode(WarehouseRecordTypeVO.SHOP_FORETASTE_OUT_WAREHOUSE_RECORD.getCode());
//        this.setAbstractFrontRecord(frontRecord);
        this.setRecordCode(dto.getRecordCode());
        this.setRealWarehouseId(realWarehouseE.getId());
        this.setBusinessType(WarehouseRecordBusinessTypeVO.OUT_WAREHOUSE_RECORD.getType());
        this.setRecordType(WarehouseRecordTypeVO.SHOP_FORETASTE_OUT_WAREHOUSE_RECORD.getType());
        this.setSyncWmsStatus(WmsSyncStatusVO.NO_REQUIRED.getStatus());
        this.setBatchStatus(WarehouseRecordBatchStatusVO.INIT.getStatus());
        this.setSyncTransferStatus(WarehouseRecordConstant.NEED_TRANSFER);
//        this.setDeliveryTime(frontRecord.getOutCreateTime());
        List<RecordDetailDTO> shopForetasteRecordDetailEList = dto.getDetailList();
        this.setWarehouseRecordDetails(new ArrayList<>(shopForetasteRecordDetailEList.size()));
        for(RecordDetailDTO shopForetasteRecordDetailE:shopForetasteRecordDetailEList){
            WarehouseRecordDetail warehouseRecordDetail=entityFactory.createEntity(WarehouseRecordDetail.class);
//            warehouseRecordDetail.createRecordDetailByFrontRecord(shopForetasteRecordDetailE);
            warehouseRecordDetail.setSkuCode(shopForetasteRecordDetailE.getSkuCode());
            //此处设置计划数量和实际数量一致，wms回调再更新实际数量，无需wms回调的业务，直接设置成一致即可
            warehouseRecordDetail.setPlanQty(shopForetasteRecordDetailE.getBasicSkuQty());
            warehouseRecordDetail.setUnit(shopForetasteRecordDetailE.getBasicUnit());
            warehouseRecordDetail.setUnitCode(shopForetasteRecordDetailE.getBasicUnitCode());
            warehouseRecordDetail.setActualQty(shopForetasteRecordDetailE.getBasicSkuQty());
            warehouseRecordDetail.setDeliveryLineNo(shopForetasteRecordDetailE.getDeliveryLineNo());
            warehouseRecordDetail.setLineNo(shopForetasteRecordDetailE.getLineNo());
            this.warehouseRecordDetails.add(warehouseRecordDetail);
        }
        //设置skuCode和skuID
        this.initWarehouseRecodeDetail();
    }


}
