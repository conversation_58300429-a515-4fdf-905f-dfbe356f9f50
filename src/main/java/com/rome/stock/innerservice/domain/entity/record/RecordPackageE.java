package com.rome.stock.innerservice.domain.entity.record;

import com.rome.stock.innerservice.domain.entity.BaseE;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Component;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

/**
 * @Description: RecordPackage
 * <p>
 * @Author: chuwenchao  2019/9/5
 */
@Data
@Slf4j
@Component
@Scope("prototype")
@EqualsAndHashCode(callSuper = false)
public class RecordPackageE extends BaseE implements Serializable {

    /**
     * 唯一主键
     */
    private Long id;
    /**
     * 出入库单据编号
     */
    private String recordCode;
    /**
     * 仓库系统编码 1:大福  2:旺店通 3:SAP-WM 4:欧电云 5:邮政 6:玄天
     */
    private Integer wmsCode;
    /**
     * 物流公司编码
     */
    private String logisticsCode;
    /**
     * 物流公司名称
     */
    private String logisticsName;
    /**
     * 运单号
     */
    private String expressCode;
    /**
     * 包裹编号
     */
    private String packageCode;
    /**
     * 包裹重量
     */
    private BigDecimal weight;

    /**
     * 长（单位cm）
     */
    private BigDecimal length;
    /**
     * 宽（单位cm）
     */
    private BigDecimal width;
    /**
     * 高(单位cm)
     */
    private BigDecimal height;
    /**
     * 体积（单位L）
     */
    private BigDecimal volume;

    /**
     * 同步tms状态 0:待同步 1:同步成功
     */
    private Integer syncTmsStatus;

    /**
     * 包裹明细
     */
    private List<RecordPackageDetailE> details;

}
