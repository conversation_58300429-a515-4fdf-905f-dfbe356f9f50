package com.rome.stock.innerservice.domain.entity.frontrecord;

import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Component;

import java.util.Date;

@Component
@Scope("prototype")
@Data
@EqualsAndHashCode(callSuper = false)
public class SalesReturnRecordE extends AbstractFrontRecord {

    private Long channelId;

    private int channelType;

    private Long merchantId;

    private Long inRealWarehouseId;

    private Long outRealWarehouseId;

    private String reason;

    private String mobile;

    private Date outCreateTime;

    private String userCode;

    private String shopCode;

    private Date createTime;

    private Date updateTime;

    private Long creator;

    private Long modifier;
}
