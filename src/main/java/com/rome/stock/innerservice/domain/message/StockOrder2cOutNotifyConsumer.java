package com.rome.stock.innerservice.domain.message;

import com.rome.scm.common.rocketmq.CustomConsumerListener;
import com.rome.stock.common.enums.CustomRocketMQEnum;
import com.rome.stock.innerservice.domain.service.StockService;
import lombok.extern.slf4j.Slf4j;
import org.apache.rocketmq.common.message.MessageExt;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;

/**
 * @Description: 电商发货回传订单中心消费mq
 */
@Slf4j
@Service
public class StockOrder2cOutNotifyConsumer implements CustomConsumerListener<MessageExt> {

    @Resource
    private StockService stockService;

    @Override
    public void onMessage(MessageExt messageExt, String businessNo, String msgKey) {
        log.info("电商发货回传订单中心消息消费，msgID: {}，消费次数: {}", messageExt.getMsgId(), messageExt.getReconsumeTimes());
        String recordCode = new String(messageExt.getBody());
        if(StringUtils.isEmpty(recordCode)) {
            log.error("电商发货回传订单中心消息消费内容为空，msgID: {}", messageExt.getMsgId());
            return ;
        }
        //电商toc单据发货完成通知订单中心
        stockService.pushOutNotify(recordCode);
    }

	@Override
	public String getRocketMQCode() {
		return CustomRocketMQEnum.MQ_STOCK_ORDER2C_OUT_NOTIFY_PUSH.getCode();
	}

}
