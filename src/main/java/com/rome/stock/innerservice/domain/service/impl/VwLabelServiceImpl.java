/**
 * Filename VwLabelServiceImpl.java
 * Company 上海来伊份科技有限公司。
 * <AUTHOR>
 * @version 
 */
package com.rome.stock.innerservice.domain.service.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.rome.arch.core.exception.RomeException;
import com.rome.arch.util.dynamic.DynamicDataSourceEnum;
import com.rome.arch.util.dynamic.TargetDataSource;
import com.rome.stock.common.enums.warehouse.VirtualWarehouseTypeVO;
import com.rome.stock.innerservice.api.dto.*;
import com.rome.stock.innerservice.common.ResCode;
import com.rome.stock.innerservice.common.RomeCollectionUtil;
import com.rome.stock.innerservice.domain.convertor.EsSapInterfaceLogConvertor;
import com.rome.stock.innerservice.domain.service.VwLabelService;
import com.rome.stock.innerservice.infrastructure.dataobject.KpChannelVwTypeRelationDO;
import com.rome.stock.innerservice.infrastructure.dataobject.SapInterfaceLogDO;
import com.rome.stock.innerservice.infrastructure.mapper.KpChannelVwTypeRelationMapper;
import com.rome.stock.innerservice.infrastructure.mapper.SapInterfaceLogMapper;
import com.rome.stock.innerservice.remote.base.dto.KpChannelDTO;
import com.rome.stock.innerservice.remote.base.facade.BaseDataFacade;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.*;

/**
 * 虚仓标签接口
 * <AUTHOR>
 * @since 2022-7-6 10:50:42
 */
@Slf4j
@Service
public class VwLabelServiceImpl implements VwLabelService {

	@Resource
	private KpChannelVwTypeRelationMapper kpChannelVwTypeRelationMapper;
	@Resource
	private SapInterfaceLogMapper sapInterfaceLogMapper;
	@Resource
	private BaseDataFacade baseDataFacade;
	@Resource
    private EsSapInterfaceLogConvertor esSapInterfaceLogConvertor;
	
	/**
	 * 根据条件查询
	 * @param paramDto
	 * @return
	 */
	@SuppressWarnings({ "unchecked", "rawtypes" })
	@Override
	public PageInfo<KpChannelVwTypeRelationDTO> queryCondition(KpChannelVwTypeRelationDTO paramDto) {
		Page page = PageHelper.startPage(paramDto.getPageIndex(), paramDto.getPageSize());
		List<KpChannelVwTypeRelationDTO> list = kpChannelVwTypeRelationMapper.queryCondition(paramDto);
		if(list == null || list.size() == 0) {
			PageInfo<KpChannelVwTypeRelationDTO> pageInfo = new PageInfo<>(Collections.EMPTY_LIST);
			pageInfo.setTotal(0);
			return pageInfo;
		}
		for(KpChannelVwTypeRelationDTO dto :list) {
			dto.setNoShareTypes(StringUtils.isNotEmpty(dto.getNoShareType())? Arrays.asList(dto.getNoShareType().split(",")) :Collections.emptyList());
			dto.setVirtualWarehouseTypeName(VirtualWarehouseTypeVO.getDescByType(dto.getVirtualWarehouseType()));
		}
		PageInfo<KpChannelVwTypeRelationDTO> pageList = new PageInfo<>(list);
		pageList.setTotal(page.getTotal());
		return pageList;
	}

	@Override
	@Transactional(rollbackFor = Exception.class)
	public void saveKpChannelVwTypeRelation(KpChannelVwTypeRelationDTO paramDto) {
	    // 验证
		if(paramDto.getKpChannelCodeList() == null || paramDto.getKpChannelCodeList().size() == 0) {
			throw new RomeException(ResCode.STOCK_ERROR_1001,"鲲鹏渠道必填");
		}
//		if(VirtualWarehouseTypeVO.getTypeVOByType(paramDto.getVirtualWarehouseType()) == null) {
//			throw new RomeException(ResCode.STOCK_ERROR_1001,"虚仓标签不能为空或者不存在");
//		}
		if (CollectionUtils.isNotEmpty(paramDto.getNoShareTypes())){
			paramDto.getNoShareTypes().forEach(type->{
				if(Objects.isNull(VirtualWarehouseTypeVO.getTypeVOByType(Integer.valueOf(type)))) {
					throw new RomeException(ResCode.STOCK_ERROR_1001,"不可共享标签库存:"+type+"不存在");
				}
				if (Objects.equals(paramDto.getTag(),1) && Objects.equals(paramDto.getVirtualWarehouseType(),Integer.valueOf(type))){
					throw new RomeException(ResCode.STOCK_ERROR_1001,"是【特殊标签】时,【虚仓标签】与【不可共享标签库存】:"+type+"不可相同");
				}
			});
			paramDto.setNoShareType(String.join(",",paramDto.getNoShareTypes()));
		}
		for(String kpChannelCode : paramDto.getKpChannelCodeList()) {
			if(StringUtils.isBlank(kpChannelCode)) {
				throw new RomeException(ResCode.STOCK_ERROR_1001,"鲲鹏渠道不能为空");
			}
		}
		List<KpChannelDTO> kpChannelDTOs = baseDataFacade.queryKpChannelByChannelCodes(paramDto.getKpChannelCodeList());
		Map<String, KpChannelDTO> kpMap = RomeCollectionUtil.listforMap(kpChannelDTOs, "channel");
		for(String kpChannelCode : paramDto.getKpChannelCodeList()) {
			if(!kpMap.containsKey(kpChannelCode) && !Objects.equals("-", kpChannelCode)) {
				throw new RomeException(ResCode.STOCK_ERROR_1001,"鲲鹏渠道基础数据无法查到");
			}
		}
		// 查询是否存在，包含逻辑删除
		List<KpChannelVwTypeRelationDO> existList = kpChannelVwTypeRelationMapper.queryByKpChannelCodeListAll(paramDto.getKpChannelCodeList());
		Map<String, KpChannelVwTypeRelationDO> existMap = RomeCollectionUtil.listforMap(existList, "kpChannelCode");
		List<SapInterfaceLogDO> logList = new ArrayList<>(paramDto.getKpChannelCodeList().size());
		List<KpChannelVwTypeRelationDO> addList = new ArrayList<>();
		List<KpChannelVwTypeRelationDO> updateList = new ArrayList<>();
		JSONObject jsonObject = new JSONObject();
		String requestContent;
		String responseContent;
		String method;
		for(String kpChannelCode : paramDto.getKpChannelCodeList()) {
			KpChannelVwTypeRelationDO relationDO = existMap.get(kpChannelCode);
			KpChannelDTO kpChannelDTO = kpMap.get(kpChannelCode);
			String kpChannelName = null;
			if (kpChannelDTO != null) {
				kpChannelName =kpChannelDTO.getChannelName();
			}else if (Objects.equals(kpChannelCode, "-")) {
				kpChannelName = "默认渠道配置";
			}
			// db中，不存在时
			if(relationDO == null) {
				relationDO = new KpChannelVwTypeRelationDO();
				relationDO.setKpChannelCode(kpChannelCode);
				relationDO.setKpChannelName(kpChannelName);
				relationDO.setVirtualWarehouseType(paramDto.getVirtualWarehouseType());
				relationDO.setTag(Objects.isNull(paramDto.getTag())?0: paramDto.getTag());
				relationDO.setNoShareType(paramDto.getNoShareType());
				relationDO.setBatchRemainExpireDay(paramDto.getBatchRemainExpireDay());
				relationDO.setValidityConfigJson(paramDto.getValidityConfigJson());
				relationDO.setRemark(paramDto.getRemark());
				relationDO.setCreator(paramDto.getCreator());
				relationDO.setModifier(paramDto.getCreator());
				addList.add(relationDO);
				method = "新增";
				// 变更前数据
				requestContent = getLogContent(null, jsonObject);
				// 变更后数据
				responseContent = getLogContent(relationDO, jsonObject);
			} else {
				// 变更前数据
				requestContent = getLogContent(relationDO, jsonObject);
				relationDO.setKpChannelName(kpChannelName);
				relationDO.setVirtualWarehouseType(paramDto.getVirtualWarehouseType());
				relationDO.setTag(Objects.isNull(paramDto.getTag())?0: paramDto.getTag());
				relationDO.setNoShareType(paramDto.getNoShareType());
				relationDO.setBatchRemainExpireDay(paramDto.getBatchRemainExpireDay());
				relationDO.setValidityConfigJson(paramDto.getValidityConfigJson());
				relationDO.setNoShareType(paramDto.getNoShareType());
				relationDO.setNoShareType(paramDto.getNoShareType());
				relationDO.setRemark(paramDto.getRemark());
				relationDO.setCreator(paramDto.getCreator());
				relationDO.setModifier(paramDto.getCreator());
				relationDO.setIsAvailable((byte)1);
				relationDO.setIsDeleted((byte)0);
				// 变更后数据
				responseContent = getLogContent(relationDO, jsonObject);
				updateList.add(relationDO);
				method = "编辑";
			}
			logList.add(getSaveLog(kpChannelCode, method, paramDto.getCreator(), requestContent, responseContent));
		}
		// 保存
		if(addList.size() > 0) {
			kpChannelVwTypeRelationMapper.batchInsert(addList);
		}
		// 更新
		if(updateList.size() > 0) {
			kpChannelVwTypeRelationMapper.batchUpdateBySync(updateList);
		}
		// 保存日志
		if(logList.size() > 0) {
			sapInterfaceLogMapper.batchInsert(logList);
		}
	}
	
	/**
	 * 查询鲲鹏渠道与虚仓类型关系编辑日志
	 * @param kpChannelCode
	 * @return
	 */
	@Override
	public List<SapInterfaceLogDTO> getOperatorLog(String kpChannelCode) {
		Page page = PageHelper.startPage(1, 1000, false);
		return esSapInterfaceLogConvertor.sapInterfaceLogToList(sapInterfaceLogMapper.queryByRecordCode(kpChannelCode));
	}
	
	/**
	 * 根据id批量删除关系
	 * @param ids
	 * @return
	 */
	@Override
	public boolean deleteRelation(List<Long> ids, Long modifier) {
		List<KpChannelVwTypeRelationDO> existList = kpChannelVwTypeRelationMapper.queryByIdList(ids);
		if(existList != null && existList.size() > 0) {
			String requestContent;
			String responseContent;
			String method = "编辑";
			List<SapInterfaceLogDO> logList = new ArrayList<>(existList.size());
			List<KpChannelVwTypeRelationDO> updateList = new ArrayList<>(existList.size());
			JSONObject jsonObject = new JSONObject();
			for(KpChannelVwTypeRelationDO dto : existList) {
				// 变更前数据
				requestContent = getLogContent(dto, jsonObject);
				dto.setModifier(modifier);
				dto.setIsAvailable((byte)0);
				dto.setIsDeleted((byte)1);
				// 变更后数据
				responseContent = getLogContent(dto, jsonObject);
				updateList.add(dto);
				logList.add(getSaveLog(dto.getKpChannelCode(), method, modifier, requestContent, responseContent));
			}
			// 更新
			if(updateList.size() > 0) {
				kpChannelVwTypeRelationMapper.batchUpdateBySync(updateList);
			}
			// 保存日志
			if(logList.size() > 0) {
				sapInterfaceLogMapper.batchInsert(logList);
			}
		}
		return true;
	}

	@Override
	public KpChannelVwTypeRelationDTO queryByKpChannelCode(String kpChannelCode) {
		return kpChannelVwTypeRelationMapper.queryByKpChannelCode(kpChannelCode);
	}

	@Override
	@TargetDataSource(DynamicDataSourceEnum.READ)
	public List<RealWarehouseKpChannelDTO> selectKpChannelByRealWarehouses(List<String> realWarehouseCodes) {
		return kpChannelVwTypeRelationMapper.queryRwKpChannelByRealWarehouseCodes(realWarehouseCodes);
	}

	/**
	 * 日志
	 * @param kpChannelCode
	 * @param method
	 * @param creator
	 * @param requestContent
	 * @param responseContent
	 * @return
	 */
	private SapInterfaceLogDO getSaveLog(String kpChannelCode, String method, Long creator, String requestContent, String responseContent) {
		SapInterfaceLogDO logDO = new SapInterfaceLogDO();
        logDO.setInteracteSystem(1);
        logDO.setRequestUrl("local");
        logDO.setRecordCode(kpChannelCode);
        logDO.setRequestService(method);
        logDO.setRequestContent(requestContent);// 之前
        logDO.setResponseContent(responseContent);  // 之后
        logDO.setType(1);
        logDO.setStatus(1);
        logDO.setCreator(creator);
        return logDO;
	}
	
	/**
	 * 获取日志内容
	 * @param dto
	 * @return
	 */
	private String getLogContent(KpChannelVwTypeRelationDO dto, JSONObject jsonObject) {
		jsonObject.clear();
		if(dto != null) {
			jsonObject.put("虚仓标签", VirtualWarehouseTypeVO.getDescByType(dto.getVirtualWarehouseType()) + "(" + dto.getVirtualWarehouseType() + ")");
			jsonObject.put("特殊标签",dto.getTag());
			jsonObject.put("不可共享标签库存",dto.getNoShareType());
			jsonObject.put("备注", dto.getRemark());
			jsonObject.put("剩余不可发货天数", dto.getBatchRemainExpireDay());
			jsonObject.put("剩余不可发货天数-区分保质期", dto.getValidityConfigJson());
			if(dto.getIsDeleted() != null && dto.getIsDeleted().byteValue() == (byte)1) {
				jsonObject.put("状态", "删除");
			} else {
				jsonObject.put("状态", "正常");
			}
		}
		return jsonObject.toJSONString();
	}

}
