package com.rome.stock.innerservice.domain.service.impl;

import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.google.common.collect.Lists;
import com.rome.arch.core.exception.RomeException;
import com.rome.stock.common.utils.DateUtil;
import com.rome.stock.innerservice.api.dto.BatchBoxConfigDTO;
import com.rome.stock.innerservice.api.dto.BatchStockBoxCheckDTO;
import com.rome.stock.innerservice.common.AlikAssert;
import com.rome.stock.innerservice.common.ResCode;
import com.rome.stock.innerservice.common.StringUtils;
import com.rome.stock.innerservice.domain.convertor.BatchBoxConfigConvertor;
import com.rome.stock.innerservice.domain.entity.RealWarehouseE;
import com.rome.stock.innerservice.domain.repository.RealWarehouseRepository;
import com.rome.stock.innerservice.domain.service.BatchBoxConfigService;
import com.rome.stock.innerservice.domain.service.WmsBatchCheckService;
import com.rome.stock.innerservice.facade.BatchStockFacade;
import com.rome.stock.innerservice.infrastructure.dataobject.BatchBoxConfigDo;
import com.rome.stock.innerservice.infrastructure.mapper.BatchBoxConfigMapper;
import com.rome.stock.innerservice.infrastructure.mapper.WmsBatchCheckFlowMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.dao.DuplicateKeyException;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.List;
import java.util.Objects;


@Slf4j
@Service
public class BatchBoxConfigServiceImpl implements BatchBoxConfigService {

    @Resource
    private BatchBoxConfigMapper batchBoxConfigMapper;
    @Resource
    private BatchBoxConfigConvertor batchBoxConfigConvertor;
    @Resource
    private RealWarehouseRepository realWarehouseRepository;
    @Resource
    private WmsBatchCheckService wmsBatchCheckService;
    @Resource
    private WmsBatchCheckFlowMapper wmsBatchCheckFlowMapper;

    private final static String IDEMPOTENT_ERROR_1 = "Duplicate entry";
    private final static String IDEMPOTENT_ERROR_2 = "sc_batch_box_config";


    @Override
    public PageInfo<BatchBoxConfigDTO> getInfoByQueryCondition(BatchBoxConfigDTO dto) {
        Page page = PageHelper.startPage(dto.getPageIndex(), dto.getPageSize());
        List<BatchBoxConfigDo> list = batchBoxConfigMapper.queryByBatchConfigList(dto);
        List<BatchBoxConfigDTO> resultList = batchBoxConfigConvertor.doListToDtoList(list);
        PageInfo<BatchBoxConfigDTO> pageList = new PageInfo(resultList);
        pageList.setTotal(page.getTotal());
        return pageList;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void saveOrUpdate(BatchBoxConfigDTO configDTO) {
        BatchBoxConfigDo batchBoxConfigDo = batchBoxConfigConvertor.dtoToDo(configDTO);
        if (Objects.isNull(batchBoxConfigDo.getId())) {
            AlikAssert.isNotNull(batchBoxConfigDo.getRealWarehouseId(), ResCode.STOCK_ERROR_1003, "仓库编码不可为空");
            RealWarehouseE realWarehouseE = realWarehouseRepository.getRealWarehouseById(configDTO.getRealWarehouseId());
            AlikAssert.notNull(realWarehouseE, ResCode.STOCK_ERROR_1003, "仓库信息不存在");
            //是否开启批次管理校验
            boolean flag = BatchStockFacade.isSupportRealWarehouse(realWarehouseE.getId(), realWarehouseE.getRealWarehouseType());
            AlikAssert.isTrue(flag, ResCode.STOCK_ERROR_1003, "仓库:" + realWarehouseE.getRealWarehouseCode() + " 不支持批次管理");
            try {
                int j = batchBoxConfigMapper.save(batchBoxConfigDo);
                if (j == 0) {
                    throw new RomeException(ResCode.STOCK_ERROR_1003, "更新失败");
                }
            }catch (DuplicateKeyException e) {
                if (e.getMessage() != null && (e.getMessage().indexOf(IDEMPOTENT_ERROR_1) > 0 && e.getMessage().indexOf(IDEMPOTENT_ERROR_2) > 0)) {
                    throw new RomeException(ResCode.STOCK_ERROR_1001, "当前仓库原箱配置已存在");
                } else {
                    throw e;
                }
            } catch (Exception e) {
                throw e;
            }
        } else {
            BatchBoxConfigDo batchBoxConfig = batchBoxConfigMapper.selectById(batchBoxConfigDo.getId());
            AlikAssert.isNotNull(batchBoxConfig, ResCode.STOCK_ERROR_1003, "原箱批次库存配置不存在");
            int j = batchBoxConfigMapper.updateByBatchBoxConfigDo(batchBoxConfigDo);
            if (j == 0) {
                throw new RomeException(ResCode.STOCK_ERROR_1003, "更新失败");
            }
        }
    }

    @Override
    public void enableBox(BatchBoxConfigDTO configDTO) {
        BatchBoxConfigDo batchBoxConfig = batchBoxConfigMapper.selectById(configDTO.getId());
        AlikAssert.isNotNull(batchBoxConfig, ResCode.STOCK_ERROR_1003, "原箱批次库存配置不存在");
        if(Objects.equals(batchBoxConfig.getBoxStatus(),1)){
            return;
        }
        if(!configDTO.isForce()){
            String maxDate = wmsBatchCheckFlowMapper.queryMaxCreateTime(batchBoxConfig.getRealWarehouseId());
            //非强制开启的时候,需要校验当天批次是否存在和WMS不一致的情况
            String startTime= maxDate+" 00:00:00";
            String endTime= maxDate+" 23:59:59";
            int k =wmsBatchCheckFlowMapper.queryWmsBatchCheckSameCount(startTime,endTime,batchBoxConfig.getRealWarehouseId());
            if(k>0){
                throw new RomeException(ResCode.STOCK_ERROR_1003, "当前仓库今天存在批次核对数据不一致");
            }
        }
        //手动调用开启原箱库存核对的接口
        BatchStockBoxCheckDTO batchStockBoxCheckDTO=new BatchStockBoxCheckDTO();
        batchStockBoxCheckDTO.setInitFlag(true);
        batchStockBoxCheckDTO.setRealWarehouseIdList(Lists.newArrayList(batchBoxConfig.getRealWarehouseId()));
        wmsBatchCheckService.wmsBatchBoxCheckJob(batchStockBoxCheckDTO);
        //最后更新状态
        batchBoxConfig.setBoxStatus(1);
        batchBoxConfig.setModifier(configDTO.getModifier());
        int j = batchBoxConfigMapper.updateByBatchBoxConfigDo(batchBoxConfig);
        if (j == 0) {
            throw new RomeException(ResCode.STOCK_ERROR_1003, "更新失败");
        }

    }

    @Override
    public BatchBoxConfigDTO queryBatchBoxConfig(String realWarehouseCode) {
        if(StringUtils.isEmpty(realWarehouseCode)){
            throw new RomeException(ResCode.STOCK_ERROR_1001,"实仓编码不能为空");
        }
        BatchBoxConfigDo batchBoxConfigDo=batchBoxConfigMapper.queryBatchBoxConfig(realWarehouseCode);
        return batchBoxConfigConvertor.doToDto(batchBoxConfigDo);
    }

}
