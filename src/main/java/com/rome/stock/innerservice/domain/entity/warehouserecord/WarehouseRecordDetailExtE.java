package com.rome.stock.innerservice.domain.entity.warehouserecord;

import com.rome.stock.innerservice.domain.entity.BaseE;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Component;


@Data
@Slf4j
@Component
@Scope("prototype")
@EqualsAndHashCode(callSuper = false)
public class WarehouseRecordDetailExtE extends BaseE {

    /**
     * 唯一主键
     */
    private Long id;

    /**
     * 单据编码
     */
    private String recordCode;

    /**
     * 类型
     */
    private int type;

    /**
     * 物料编号
     */
    private String skuCode;


    /**
     * 收货原因
     */
    private String reason;

    /**
     * 扩展字段1
     */
    private String ext1;

    /**
     * 扩展字段2
     */
    private String ext2;

    /**
     * 扩展字段3
     */
    private String ext3;

    /**
     * 数据描述
     */
    private String desc;
}
