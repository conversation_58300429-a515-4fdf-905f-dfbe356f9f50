package com.rome.stock.innerservice.domain.entity.frontrecord;


import com.rome.arch.core.exception.RomeException;
import com.rome.stock.common.constants.front.PurchaseOrderConsts;
import com.rome.stock.innerservice.api.dto.frontrecord.PurchaseOrderDetailDTO;
import com.rome.stock.innerservice.common.*;
import com.rome.stock.innerservice.constant.FrontRecordStatusVO;
import com.rome.stock.innerservice.constant.FrontRecordTypeVO;
import com.rome.stock.innerservice.constant.PurchaseTypeVO;
import com.rome.stock.innerservice.domain.convertor.frontrecord.FrPurchaseOrderConvertor;
import com.rome.stock.innerservice.domain.repository.frontrecord.FrPurchaseOrderRepository;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;
import java.util.Objects;

@Component
@Scope("prototype")
@Data
@EqualsAndHashCode(callSuper = false)
public class PurchaseOrderE extends AbstractFrontRecord {


	@Resource
	private FrPurchaseOrderRepository frPurchaseOrderRepository;
	@Resource
	private FrPurchaseOrderConvertor frPurchaseOrderConvertor;
	@Resource
	private SkuInfoTools skuInfoTools;
	@Resource
	private SkuQtyUnitTools skuQtyUnitTools;

	/**
	 * 创建采购入库前置单
	 */
	public void addFrontRecordOld() {
		if (PurchaseOrderConsts.OUTSOURCE_PROCESS_TYPE.equals(this.getPurchaseRecordType())) {
			//PurchaseRecordType = 3 表示委外入库，委外虚拟入库不考虑，跟委外入库统一处理
			this.setRecordType(FrontRecordTypeVO.OUTSOURCE_IN_RECORD.getType());
			//生成单据编号
			this.initFrontRecord(FrontRecordTypeVO.OUTSOURCE_IN_RECORD.getCode(), this.purchaseOrderDetails);
		} else if (PurchaseOrderConsts.FOOD_PURCHASE_TYPE .equals( this.getPurchaseRecordType())
				|| PurchaseOrderConsts.FOOD_NOT_PURCHASE_TYPE .equals( this.getPurchaseRecordType())) {
			//PurchaseRecordType = 1 表示大仓采购食品 2表示大仓采购非食品
			this.setRecordType(FrontRecordTypeVO.WAREHOUSE_PURCHASE_RECORD.getType());
			//生成单据编号
			this.initFrontRecord(FrontRecordTypeVO.WAREHOUSE_PURCHASE_RECORD.getCode(), this.purchaseOrderDetails);
		} else if (PurchaseOrderConsts.CLOD_CHAIN_PURCHASE_TYPE.equals(this.getPurchaseRecordType())) {
			// 冷链入库
			this.setRecordType(FrontRecordTypeVO.COLD_CHAIN_SURPASS_WAREHOUSE_RECORD.getType());
			//生成单据编号
			this.initFrontRecord(FrontRecordTypeVO.COLD_CHAIN_SURPASS_WAREHOUSE_RECORD.getCode(), this.purchaseOrderDetails);
		} else if (PurchaseOrderConsts.CHAIN_DIRECT_PURCHASE_TYPE.equals(this.getPurchaseRecordType())) {
			// 直送入库
			this.setRecordType(FrontRecordTypeVO.SUPPLIER_DIRECT_DELIVERY_RECORD.getType());
			//生成单据编号
			this.initFrontRecord(FrontRecordTypeVO.SUPPLIER_DIRECT_DELIVERY_RECORD.getCode(), this.purchaseOrderDetails);
		}
		if (StringUtils.isBlank(this.getRemark())) {
			this.setRemark("");
		}
		//插入采购单据
		long id = frPurchaseOrderRepository.savePurchaseOrderRecord(this);
		this.setId(id);
		//采购单详情关联主数据
		this.purchaseOrderDetails.forEach(detail ->{
			detail.setFrontRecordDetail(this);
			if (PurchaseOrderConsts.ROW_STATUS_DELETED.equals(detail.getStatus())) {
				detail.setIsDeleted(Byte.valueOf("1"));
			} else {
				detail.setIsDeleted(Byte.valueOf("0"));
			}
		});
		//插入采购入库单据详情
		frPurchaseOrderRepository.savePurchaseOrderRecordDetails(this.purchaseOrderDetails);
	}

	/**
	 * 创建采购入库前置单
	 */
	public void addFrontRecord() {
		FrontRecordTypeVO frontRecordTypeVo = FrontRecordTypeVO.getFrontVOByType(PurchaseTypeVO.getPurchaseVoByRecordType(this.getPurchaseRecordType()).getRealFrontType());
		if(Objects.nonNull(frontRecordTypeVo)){
			this.setRecordType(frontRecordTypeVo.getType());
			this.initFrontRecord(frontRecordTypeVo.getCode(), this.purchaseOrderDetails);
		}
		if (StringUtils.isBlank(this.getRemark())) {
			this.setRemark("");
		}
		//插入采购单据
		long id = frPurchaseOrderRepository.savePurchaseOrderRecord(this);
		this.setId(id);
		//采购单详情关联主数据
		this.purchaseOrderDetails.forEach(detail ->{
			detail.setFrontRecordDetail(this);
		});
		//插入采购入库单据详情
		frPurchaseOrderRepository.savePurchaseOrderRecordDetails(this.purchaseOrderDetails);
	}

	/**
	 * 创建采购退货前置单
	 */
	public void addReturnFrontRecord() {
		this.setRecordType(FrontRecordTypeVO.WAREHOUSE_RETURN_GOODS_RECORD.getType());
		this.setRecordStatus(FrontRecordStatusVO.INIT.getStatus());
		//生成单据编号
		this.initFrontRecord(FrontRecordTypeVO.WAREHOUSE_RETURN_GOODS_RECORD.getCode(), this.purchaseOrderDetails);
		if (StringUtils.isBlank(this.getRemark())) {
			this.setRemark("");
		}
		//插入采购单据
		long id = frPurchaseOrderRepository.savePurchaseOrderRecord(this);
		this.setId(id);
		//采购单详情关联主数据
		this.purchaseOrderDetails.forEach(detail -> {
			detail.setFrontRecordDetail(this);
			if (PurchaseOrderConsts.ROW_STATUS_DELETED.equals(detail.getStatus())) {
				detail.setIsDeleted(Byte.valueOf("1"));
			} else {
				detail.setIsDeleted(Byte.valueOf("0"));
			}
		});
		//插入采购入库单据详情
		frPurchaseOrderRepository.savePurchaseOrderRecordDetails(this.purchaseOrderDetails);
	}
    /**
     * 创建采购退货前置单
     */
    public void addReturnFrontRecordOld() {
        this.setRecordType(FrontRecordTypeVO.WAREHOUSE_RETURN_GOODS_RECORD.getType());
        this.setRecordStatus(FrontRecordStatusVO.INIT.getStatus());
        //生成单据编号
        this.initFrontRecord(FrontRecordTypeVO.WAREHOUSE_RETURN_GOODS_RECORD.getCode(), this.purchaseOrderDetails);
        if (StringUtils.isBlank(this.getRemark())) {
            this.setRemark("");
        }
        //插入采购单据
        long id = frPurchaseOrderRepository.savePurchaseOrderRecord(this);
        this.setId(id);
        //采购单详情关联主数据
        this.purchaseOrderDetails.forEach(detail -> {
            detail.setFrontRecordDetail(this);
            if (PurchaseOrderConsts.ROW_STATUS_DELETED.equals(detail.getStatus())) {
                detail.setIsDeleted(Byte.valueOf("1"));
            } else {
                detail.setIsDeleted(Byte.valueOf("0"));
            }
        });
        //插入采购入库单据详情
        frPurchaseOrderRepository.savePurchaseOrderRecordDetails(this.purchaseOrderDetails);
    }
	/**
	 * 更新采购入库前置单
	 * 支持添加新的详情
	 */
	public void updateFrontRecord(List<PurchaseOrderDetailE> purchaseOrderDetailEList) {
		//主表不允许更新信息，只允许更新状态
		//	//6.18 确定入库单和前置单状态修改的时候不更新
//		frPurchaseOrderRepository.updateToInitStatus(this.getId());
		if (this.purchaseOrderDetails != null && this.purchaseOrderDetails.size() > 0) {
			List<PurchaseOrderDetailE> oldDetails = frPurchaseOrderRepository.queryDetailsByRecordId(this.getId());
			Map<String, Integer> oldRowStatusMap = RomeCollectionUtil.listforMap(oldDetails, "lineNo", "status");

			//采购单详情关联主数据
			this.purchaseOrderDetails.forEach(detail -> {
				detail.setFrontRecordDetail(this);
				if (PurchaseOrderConsts.ROW_STATUS_DELETED.equals(detail.getStatus())) {
					if (oldRowStatusMap.containsKey(detail.getLineNo()) && PurchaseOrderConsts.ROW_STATUS_RECEIVED.equals(oldRowStatusMap.get(detail.getLineNo()))) {
						//已收货的明细不允许删除
						throw new RomeException(ResCode.STOCK_ERROR_1002, ResCode.STOCK_ERROR_1002_DESC + ":已收货的明细不允许删除:" + detail.getSkuCode());
					}
					detail.setIsDeleted(Byte.valueOf("1"));
				} else {
					detail.setIsDeleted(Byte.valueOf("0"));
				}
			});
			//修改采购入库单据详情
			frPurchaseOrderRepository.updateDetails(this.purchaseOrderDetails);
			//新增采购入单据详情
			if(!CollectionUtils.isEmpty(purchaseOrderDetailEList)){
				purchaseOrderDetailEList.forEach(detail -> {detail.setFrontRecordDetail(this);});
				skuInfoTools.convertSkuCode(purchaseOrderDetailEList);
				//单位换算
				skuQtyUnitTools.convertRealToBasic(purchaseOrderDetailEList);
				frPurchaseOrderRepository.savePurchaseOrderRecordDetails(purchaseOrderDetailEList);
			}
		}
	}


	/**
	 * 初始状态更新为已完成
	 *
	 */
	public void updateInitToCompleteStatus() {
		frPurchaseOrderRepository.updateInitToCompleteStatus(this.getId());
	}

	/**
	 * 初始状态更新为已入库
	 *
	 */
	public void updateInitToInAllocation() {
		frPurchaseOrderRepository.updateInitToInAllocation(this.getId());
	}
	/**
	 * 入库状态更新为已完成
	 *
	 */
	public void updateInAllocationToCompleteStatus() {
		frPurchaseOrderRepository.updateInAllocationToCompleteStatus(this.getId());
	}


	/**
	 * 批量更新对应sku为需要质检行为已收货完成
	 *
	 * @return
	 */
	public void updateRowStatusToComplete() {
		frPurchaseOrderRepository.updateRowStatusToComplete(this.getId());
	}
	/**
	 * 更新采购退货供应商前置单据状态为已出库
	 */
	public void updateToOutAllocation() {
		int executeResult = frPurchaseOrderRepository.updateToOutAllocation(this.getId());
		AlikAssert.isTrue(executeResult == 1, ResCode.STOCK_ERROR_1013, ResCode.STOCK_ERROR_1013_DESC);
	}


	/**
	 * 出向实体仓库id
	 */
	private Long realWarehouseId;

	/**
	 * 工产代码
	 */
	private String factoryCode;
	/**
	 * 工产名称
	 */
	private String factoryName;
	/**
	 * 供应商编码代码
	 */
	private String supplierCode;
	/**
	 * 供应商名称
	 */
	private String supplierName;
	/**
	 * 备注
	 */
	private String remark;


	/**
	 * 直送外采流程必须有关联的内采单号
	 */
	private String sapPoNo;

	/**
	 * 供应商联系人
	 */
	private String supplierContact;
	/**
	 * "采购单类型：1、普通采购 2、紧急入库单 3、委外原料入库单  4、委外成品入库单 5、 越库采购入库单 6、直送入库单 7、折让入库单 8、更正入库单
	 */
	private Integer purchaseRecordType;

	/**
	 * 业务类型
	 */
	private String businessType;

	/**
	 * 门店编码
	 */
	private String shopCode;

	/**
	 * 明细集合
	 */
	private List<PurchaseOrderDetailE> purchaseOrderDetails;
}
