package com.rome.stock.innerservice.domain.entity;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Component;

@Data
@Slf4j
@Component
@Scope("prototype")
@EqualsAndHashCode(callSuper = false)
public class BatchStrategyConfigE extends  BaseE {

    /**
     * 出库单仓库id
     */
    private Long outRealWarehouseId;

    /**
     * 入库仓仓库id
     */
    private Long inRealWarehouseId;

    /**
     * 单据类型
     */
    private Integer recordType;

    /**
     * 出库策略
     */
    private Integer outStrategy;

}
