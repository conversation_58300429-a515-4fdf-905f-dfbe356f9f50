package com.rome.stock.innerservice.domain.entity.frontrecord;

import com.rome.arch.core.domain.EntityFactory;
import com.rome.arch.core.exception.RomeException;
import com.rome.stock.common.constants.WmsConfigConstants;
import com.rome.stock.common.enums.warehouse.WarehouseRecordTypeVO;
import com.rome.stock.innerservice.api.dto.frontrecord.CrossStockDTO;
import com.rome.stock.innerservice.api.dto.frontrecord.StockOrderDTO;
import com.rome.stock.innerservice.common.ResCode;
import com.rome.stock.innerservice.common.RomeCollectionUtil;
import com.rome.stock.innerservice.constant.FrontRecordStatusVO;
import com.rome.stock.innerservice.constant.FrontRecordTypeVO;
import com.rome.stock.innerservice.constant.OnlineStockTransTypeVO;
import com.rome.stock.innerservice.constant.RwRecordPoolStatusVO;
import com.rome.stock.innerservice.domain.convertor.AddressConvertor;
import com.rome.stock.innerservice.domain.entity.AddressE;
import com.rome.stock.innerservice.domain.entity.RwRecordPoolDetailE;
import com.rome.stock.innerservice.domain.entity.RwRecordPoolE;
import com.rome.stock.innerservice.domain.repository.RealWarehouseRepository;
import com.rome.stock.innerservice.domain.repository.RwRecordPoolRepository;
import com.rome.stock.innerservice.domain.repository.frontrecord.FrSaleRepository;
import com.rome.stock.innerservice.infrastructure.dataobject.RealWarehouseWmsConfigDO;
import com.rome.stock.core.infrastructure.dataobject.core.CoreRealStockOpDO;
import com.rome.stock.core.infrastructure.dataobject.core.CoreRealStockOpDetailDO;
import com.rome.stock.core.infrastructure.dataobject.core.CoreVirtualStockOpDO;
import com.rome.stock.innerservice.infrastructure.mapper.RealWarehouseWmsConfigMapper;
import com.rome.stock.innerservice.remote.item.facade.SkuFacade;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;

/**
 * 电商销售单
 */
@Component
@Scope("prototype")
@Data
@Slf4j
@EqualsAndHashCode(callSuper = false)
public class OnlineRetailE extends AbstractFrontRecord {

    @Autowired
    private FrSaleRepository frSaleRepository;
    @Autowired
    private EntityFactory entityFactory;
    @Autowired
    private RwRecordPoolRepository rwRecordPoolRepository;

    @Autowired
    private AddressConvertor addressConvertor;

    @Resource
    private RealWarehouseWmsConfigMapper realWarehouseWmsConfigMapper;
    private String originOrderCode;

    /**
     * 添加前置单
     */
    public void addFrontRecord(StockOrderDTO stockOrderDTO) {
        long start = System.currentTimeMillis();
        initFrontRecord(FrontRecordTypeVO.ONLINE_SALE_RECORD.getCode(), this.frontRecordDetails, stockOrderDTO.getMerchantId());
        log.info("虚拟物品下单锁定库存查询商品中心耗时：单号{},耗时{}",stockOrderDTO.getOrderCode(),(System.currentTimeMillis() - start));

//        boolean isWDTransType = (OnlineStockTransTypeVO.TRANS_TYPE_5.getTransType().equals(stockOrderDTO.getTransType()));
//        if (isWDTransType) {
//            //WDT旺店通为已支付订单
//            this.setRecordStatus(FrontRecordStatusVO.SO_PAID.getStatus());
//        } else {
//            //APP为未支付订单
//            this.setRecordStatus(FrontRecordStatusVO.SO_UNPAID.getStatus());
//        }
        this.setRecordStatus(FrontRecordStatusVO.SO_UNPAID.getStatus());
        this.setRecordType(FrontRecordTypeVO.ONLINE_SALE_RECORD.getType());
        //插入前置单数据
        long id = frSaleRepository.saveOnlineSaleRecord(this);
        this.setId(id);
        //电商零售详情关联数据
        this.frontRecordDetails.forEach(record -> record.setFrontRecordDetail(this));
        //保存电商零售前置单明细
        frSaleRepository.saveOnlineSaleRecordDetails(this.frontRecordDetails);
    }

    /**
     * 锁定实体仓库库存对象(按照planQty锁定,特殊需求在serviceImpl中自行封装)
     */
    public CoreRealStockOpDO initLockStockObj(List<CoreVirtualStockOpDO> cvsList,Long virtualWarehouseId){
        List<CoreRealStockOpDetailDO> increaseDetails = new ArrayList<>();
        for (OnlineRetailRecordDetailE detail: this.getFrontRecordDetails()) {
            //数量为0的不处理库存的增加和减少
            if(BigDecimal.ZERO.compareTo(detail.getSkuQty()) == 0){
                continue;
            }
            CoreRealStockOpDetailDO coreRealStockOpDetailDO = new CoreRealStockOpDetailDO();
            coreRealStockOpDetailDO.setSkuId(detail.getSkuId());
            coreRealStockOpDetailDO.setSkuCode(detail.getSkuCode());
            coreRealStockOpDetailDO.setLockQty(detail.getSkuQty());
            coreRealStockOpDetailDO.setRealWarehouseId(this.getRealWarehouseId());
            increaseDetails.add(coreRealStockOpDetailDO);
            //构建虚仓do
            CoreVirtualStockOpDO coreVirtualStockOpDO=new CoreVirtualStockOpDO();
            coreVirtualStockOpDO.setVirtualWarehouseId(virtualWarehouseId);
            coreVirtualStockOpDO.setSkuId(detail.getSkuId());
            coreVirtualStockOpDO.setSkuCode(detail.getSkuCode());
            coreVirtualStockOpDO.setLockQty(detail.getSkuQty());
            coreVirtualStockOpDO.setRecordCode(this.getRecordCode());
            coreVirtualStockOpDO.setTransType(this.getRecordType());
            coreVirtualStockOpDO.setRealWarehouseId(this.getRealWarehouseId());
            coreVirtualStockOpDO.setChannelCode(this.getChannelCode());
            cvsList.add(coreVirtualStockOpDO);
        }
        CoreRealStockOpDO coreRealStockOpDO = new CoreRealStockOpDO();
        coreRealStockOpDO.setRecordCode(this.getRecordCode());
        coreRealStockOpDO.setTransType(this.getRecordType());
        coreRealStockOpDO.setDetailDos(increaseDetails);
        return coreRealStockOpDO;
    }



    /**
     * 封装从上往下出库和释放多锁的库存
     */
    public CoreRealStockOpDO packageUnlockStockObjOrder(List<RwRecordPoolDetailE> poolDetails) {
        List<CoreRealStockOpDetailDO> detailDos = new ArrayList<>();
//        List<CoreVirtualStockOpDO> virtualStockByCalculateDOs=new ArrayList<>();
        CoreRealStockOpDO coreRealStockOpDO=new CoreRealStockOpDO();
        coreRealStockOpDO.setRecordCode(this.getRecordCode());
        coreRealStockOpDO.setTransType(this.getRecordType());
        for (RwRecordPoolDetailE detailE : poolDetails) {
            CoreRealStockOpDetailDO detailDO = new CoreRealStockOpDetailDO();
            detailDO.setUnlockQty(detailE.getBasicSkuQty());
            if(BigDecimal.ZERO.compareTo(detailDO.getUnlockQty()) == 0){
                continue;
            }
            detailDO.setSkuId(detailE.getSkuId());
            detailDO.setSkuCode(detailE.getSkuCode());
            detailDO.setRealWarehouseId(detailE.getRealWarehouseId());
            detailDos.add(detailDO);
            //虚仓库存
//            if(null != detailE.getVirtualWarehouseId()){
//                CoreVirtualStockOpDO coreVirtualStockOpDO = new CoreVirtualStockOpDO();
//                coreVirtualStockOpDO.setRecordCode(onlineRetailE.getRecordCode());
//                coreVirtualStockOpDO.setTransType(onlineRetailE.getRecordType());
//                coreVirtualStockOpDO.setSkuCode(detailE.getSkuCode());
//                coreVirtualStockOpDO.setSkuId(detailE.getSkuId());
//                coreVirtualStockOpDO.setUnlockQty(detailDO.getUnlockQty());
//                coreVirtualStockOpDO.setVirtualWarehouseId(detailE.getVirtualWarehouseId());
//                virtualStockByCalculateDOs.add(coreVirtualStockOpDO);
//            }
        }
//        if(!CollectionUtils.isEmpty(virtualStockByCalculateDOs)){
//            coreRealStockOpDO.setVirtualStockByCalculateDOs(virtualStockByCalculateDOs);
//        }
        coreRealStockOpDO.setDetailDos(detailDos);
        return coreRealStockOpDO;
    }



    /**
     * 封装从下往上出库和释放多锁的库存
     */
    public CoreRealStockOpDO packOutAndUnlockStockObjForDown(List<RwRecordPoolDetailE> poolDetails){
        List<CoreRealStockOpDetailDO> increaseDetails = new ArrayList<>();
//        List<CoreVirtualStockOpDO>  cvsList = new ArrayList<>();
        for (RwRecordPoolDetailE detail: poolDetails) {
            //数量为0的不处理库存的增加和减少
            if(BigDecimal.ZERO.compareTo(detail.getBasicSkuQty()) == 0){
                continue;
            }
            CoreRealStockOpDetailDO coreRealStockOpDetailDO = new CoreRealStockOpDetailDO();
            coreRealStockOpDetailDO.setSkuId(detail.getSkuId());
            coreRealStockOpDetailDO.setSkuCode(detail.getSkuCode());
            coreRealStockOpDetailDO.setUnlockQty(detail.getBasicSkuQty());
            coreRealStockOpDetailDO.setRealQty(detail.getBasicSkuQty());
            coreRealStockOpDetailDO.setRealWarehouseId(detail.getRealWarehouseId());
            coreRealStockOpDetailDO.setCheckBeforeOp(true);
            increaseDetails.add(coreRealStockOpDetailDO);
//            if(null != detail.getVirtualWarehouseId()){
//                CoreVirtualStockOpDO coreVirtualStockOpDO = new CoreVirtualStockOpDO();
//                coreVirtualStockOpDO.setRecordCode(this.getRecordCode());
//                coreVirtualStockOpDO.setTransType(this.getRecordType());
//                coreVirtualStockOpDO.setSkuCode(detail.getSkuCode());
//                coreVirtualStockOpDO.setSkuId(detail.getSkuId());
//                coreVirtualStockOpDO.setUnlockQty(detail.getBasicSkuQty());
//                coreVirtualStockOpDO.setRealQty(detail.getBasicSkuQty());
//                coreVirtualStockOpDO.setVirtualWarehouseId(detail.getVirtualWarehouseId());
//                cvsList.add(coreVirtualStockOpDO);
//            }
        }
        CoreRealStockOpDO coreRealStockOpDO = new CoreRealStockOpDO();
        coreRealStockOpDO.setRecordCode(this.getRecordCode());
        coreRealStockOpDO.setTransType(this.getRecordType());
        coreRealStockOpDO.setDetailDos(increaseDetails);
//        if(!CollectionUtils.isEmpty(cvsList)){
//            coreRealStockOpDO.setVirtualStockByCalculateDOs(cvsList);
//        }
        return coreRealStockOpDO;
    }
    /**
     * 添加前置单
     */
    public void addCrossFrontRecord(CrossStockDTO crossStockDTO) {
        initFrontRecord(FrontRecordTypeVO.CROSS_SALE_RECORD.getCode(), this.frontRecordDetails, crossStockDTO.getMerchantId());
        this.setRecordStatus(FrontRecordStatusVO.SO_UNPAID.getStatus());
        this.setRecordType(FrontRecordTypeVO.CROSS_SALE_RECORD.getType());
        //插入前置单数据
        long id = frSaleRepository.saveOnlineSaleRecord(this);
        this.setId(id);
        //电商零售详情关联数据
        this.frontRecordDetails.forEach(record -> record.setFrontRecordDetail(this));
        //保存电商零售前置单明细
        frSaleRepository.saveOnlineSaleRecordDetails(this.frontRecordDetails);
    }

    @Resource
    private RealWarehouseRepository realWarehouseRepository;
    /**
     * 拆单
     */
    @Autowired
    private SkuFacade skuFacade;
    public List<RwRecordPoolE> splitOnlineOrder( List<CoreVirtualStockOpDO> cvsList, StockOrderDTO stockOrderDTO) {
        Map<Long, List<CoreVirtualStockOpDO>> rwIdMap = RomeCollectionUtil.listforListMap(cvsList, "realWarehouseId", null);
        //同一个skuId的基础单位相同，可以选取一个放入缓存中给后面使用
        Map<Long, OnlineRetailRecordDetailE> skuIdUnitCodeMap = new HashMap<>();
        for(OnlineRetailRecordDetailE detailE : this.getFrontRecordDetails()) {
            skuIdUnitCodeMap.putIfAbsent(detailE.getSkuId(), detailE);
        }
        OnlineStockTransTypeVO transTypeVO = OnlineStockTransTypeVO.getByTransType(stockOrderDTO.getTransType());
        int needCombine = skuFacade.getDefaultMerchantId().equals(stockOrderDTO.getMerchantId()) ? 1 : 0;
        boolean isVirtual = false ;
        if (OnlineStockTransTypeVO.TRANS_TYPE_7.equals(transTypeVO)) {
            //自营外卖，无需合单,目前外卖的不会走到这里来
            needCombine = 0;
        } else {
            List<RealWarehouseWmsConfigDO> configDOS = realWarehouseWmsConfigMapper.findRealWarehouseWmsConfigByIds(new ArrayList<>(rwIdMap.keySet()));
            if (configDOS == null || configDOS.size() < rwIdMap.keySet().size()) {
                throw new RomeException(ResCode.STOCK_ERROR_1002, "寻源仓库不在仓库配置表");
            }
            int wmsCode = configDOS.get(0).getWmsCode();
//            for (RealWarehouseWmsConfigDO configDO : configDOS) {
//                if (wmsCode != configDO.getWmsCode()) {
//                    throw new RomeException(ResCode.STOCK_ERROR_1002, "寻源仓库类型不一致");
//                }
//            }
            if (WmsConfigConstants.WMS_VIRTUAL == wmsCode) {
                //虚拟订单无需合单
                needCombine = 0;
                isVirtual = true;
            } else if (WmsConfigConstants.WMS_MERCHANT == wmsCode) {
                needCombine = 0;
            }
        }
//        boolean isWDTransType = (OnlineStockTransTypeVO.TRANS_TYPE_5.equals(transTypeVO));
        List<RwRecordPoolE> result = new ArrayList<>();
        for(List<CoreVirtualStockOpDO> mapValue : rwIdMap.values()) {
            CoreVirtualStockOpDO master = mapValue.get(0);
            RwRecordPoolE poolE = entityFactory.createEntity(RwRecordPoolE.class);
            //生成do单号和recordType
            poolE.setRecordType(WarehouseRecordTypeVO.POOL_DO_RECORD.getType());
            poolE.setDoCode(getOrderUtilService().queryOrderCode(WarehouseRecordTypeVO.POOL_DO_RECORD.getCode()));
            poolE.setFrontRecordId(this.getId());
            poolE.setFrontRecordCode(this.getRecordCode());
            poolE.setChannelCode(this.getChannelCode());
            poolE.setRealWarehouseId(master.getRealWarehouseId());
            poolE.setVirtualWarehouseId(master.getVirtualWarehouseId());
            poolE.setMerchantId(this.getMerchantId());
            //待合单
            poolE.setRecordStatus(RwRecordPoolStatusVO.INIT.getStatus());
//            //MD5指纹应该放在后面，保证已经设置过poolE的其他值
//            poolE.bindMergeFingerprint(addressE);
            poolE.setNeedCombine(needCombine);
            long id = rwRecordPoolRepository.addRecordPool(poolE);
            poolE.setId(id);
            poolE.warpRwRecordPoolDetail(mapValue, skuIdUnitCodeMap);
            rwRecordPoolRepository.addAllRecordPoolSku(poolE.getRwRecordPoolDetails());
            result.add(poolE);

            if(!isVirtual){
                //地址信息在do上各存一份，后面有可能修改do的地址信息
                AddressE addr = addressConvertor.dtoToEntity(stockOrderDTO);
                addr.setUserType((byte) 0);
                addr.setAddressType((byte) 0);
                addr.setRecordCode(poolE.getDoCode());
                addr.addAddress();
            }
        }
        return result;
    }


    /**
     * 更新前置单状态为已支付
     */
    public int updateToPaid(Date payTime) {
        return frSaleRepository.updateToPaid(this.getId(),payTime);
    }

    /**
     * 更新前置单状态为已取消
     */
    public int updateToCanceled() {
        return frSaleRepository.updateToCanceled(this.getId());
    }

    /**
     * 更新前置单状态为已出库
     */
    public int updateToOutAllocation() {
        return frSaleRepository.updateToOutAllocation(this.getId());
    }

    //渠道code
    private String channelCode;
    //门店编号
    private String shopCode;
    //出向实体仓库id
    private Long realWarehouseId;
    //用户手机号
    private String mobile;
    //用户code
    private String userCode;
    //交易类型
    private Integer transType;
    //商家ID
    private Long merchantId;
    //期望收货日期_开始
    private Date expectReceiveDateStart;
    //期望收货日期_截止
    private Date expectReceiveDateEnd;
    //sku数量及明细
    private List<OnlineRetailRecordDetailE> frontRecordDetails;
    //Do池数据
    private List<RwRecordPoolE> rwRecordPoolEList;
    /**
     * 支付时间
     */
    private Date payTime;
}
