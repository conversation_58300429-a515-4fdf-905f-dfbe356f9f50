package com.rome.stock.innerservice.domain.entity.frontrecord;

import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;

/**
 * 门店盘点详情
 * <AUTHOR>
 */
@Component
@Scope("prototype")
@Data
@EqualsAndHashCode(callSuper = false)
public class ShopInventoryRecordDetailE extends AbstractFrontRecordDetail{

    /**
     * 账面数量
     */
    private BigDecimal accQty;

    /**
     * 差异数量
     */
    private BigDecimal diffQty;

    /**
     * 库存账面数量
     */
    private BigDecimal stockQty;

    /**
     * 库存差异数量
     */
    private BigDecimal diffStockQty;
}
