/**
 * Filename KpRwChangeStockByBigdataProducer.java
 * Company 上海来伊份科技有限公司。
 * <AUTHOR>
 * @version 
 */
package com.rome.stock.innerservice.domain.message;

import javax.annotation.Resource;
import javax.validation.constraints.NotNull;
import org.apache.rocketmq.client.producer.SendResult;
import org.apache.rocketmq.client.producer.SendStatus;
import org.apache.rocketmq.spring.core.RocketMQTemplate;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.messaging.Message;
import org.springframework.messaging.support.MessageBuilder;
import org.springframework.stereotype.Service;

import com.rome.stock.common.enums.CustomRocketMQEnum;
import com.rome.scm.common.rocketmq.CustomRocketMQProducerClient;

import lombok.extern.slf4j.Slf4j;

/**
 * 鲲鹏实仓库存变化推到大数据
 * <AUTHOR>
 * @since 2020-9-11 15:28:03
 */
@Slf4j
@Service
public class KpRwChangeStockByBigdataProducer {

//	@Resource
//    private RocketMQTemplate rocketMQTemplate;
//
//	@NotNull
//    @Value("${rocketmq.topic.stock.kprwchangestockbdata:stock-kp_c_stock}")
//    private String topic;
//	
//	/**
//	 * mq消息配置
//	 */
//    private MQConfigEnum mqConfigEnum = MQConfigEnum.KP_CHANGE_STOCK_QUEUE;
    
    /**
     * 发送消息
     * @return
     */
    public boolean sendMQ(String msg) {
        try {
        	boolean result = CustomRocketMQProducerClient.send(msg, CustomRocketMQEnum.MQ_KP_CHANGE_STOCK_BIGDATA_PUSH.getCode(), null, null);
        	if(result){
                return true;
            }else {
            	log.error("鲲鹏实仓库存变化推到大数据,发送消息,失败,结果:{}",result);
            }
//        	log.info("鲲鹏实仓库存变化推到大数据推送mq,发送消息,参数:{}", msg);
//        	String destination = topic + ":" + mqConfigEnum.getTag();
//        	Message<String> message = MessageBuilder.withPayload(msg)
//        			.build();
//        	SendResult sendResult = rocketMQTemplate.syncSend(destination, message, mqConfigEnum.getTimeout());
//        	destination = null;
//        	// 发送结果
//            if(sendResult.getSendStatus() == SendStatus.SEND_OK){
//                return true;
//            }else {
//            	log.error("鲲鹏实仓库存变化推到大数据,发送消息,失败,结果:{}",sendResult);
//            }
        } catch (Exception e) {
            log.error("鲲鹏实仓库存变化推到大数据,发送消息,出错:msg={}", msg, e);
        }
        return false;
    }
	
}
