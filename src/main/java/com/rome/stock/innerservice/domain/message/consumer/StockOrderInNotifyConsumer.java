package com.rome.stock.innerservice.domain.message.consumer;

import com.alibaba.fastjson.JSONObject;
import com.rome.arch.core.exception.RomeException;
import com.rome.scm.common.rocketmq.CustomConsumerListener;
import com.rome.stock.common.constants.ResCode;
import com.rome.stock.common.enums.CustomRocketMQEnum;
import com.rome.stock.common.enums.warehouse.PushTypeVO;
import com.rome.stock.innerservice.api.dto.ReceiptRecordDTO;
import com.rome.stock.innerservice.domain.service.WhAllocationService;
import lombok.extern.slf4j.Slf4j;
import org.apache.rocketmq.common.message.MessageExt;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.util.Objects;

/**
 * @Description: 收货回传订单中心消费mq
 */
@Slf4j
@Service
public class StockOrderInNotifyConsumer implements CustomConsumerListener<MessageExt> {

    @Resource
    private WhAllocationService whAllocationService;


    @Override
    public void onMessage(MessageExt messageExt, String businessNo, String msgKey) {
        log.info("收货回传订单中心消息消费，msgID: {}，消费次数: {}", messageExt.getMsgId(), messageExt.getReconsumeTimes());
        String json = new String(messageExt.getBody());
        ReceiptRecordDTO receiptRecordDTO= JSONObject.parseObject(json, ReceiptRecordDTO.class);
        if(StringUtils.isEmpty(receiptRecordDTO.getWarehouseRecordCode())) {
            log.error("收货回传订单中心消息消费内容为空，msgID: {}", messageExt.getMsgId());
            return ;
        }
        if (Objects.equals(receiptRecordDTO.getPushType(), PushTypeVO.RW.getType())){
            whAllocationService.pushInNotifyForRw(receiptRecordDTO);
        }else if (Objects.equals(receiptRecordDTO.getPushType(), PushTypeVO.SHOP.getType())){
            whAllocationService.pushInNotifyForShop(receiptRecordDTO);
        }else {
            throw new RomeException(ResCode.STOCK_ERROR_1002,"推送类型pushType为"+ receiptRecordDTO.getPushType()+",不存在");
        }
    }

	@Override
	public String getRocketMQCode() {
		return CustomRocketMQEnum.MQ_STOCK_ORDER_IN_NOTIFY_PUSH.getCode();
	}

}
