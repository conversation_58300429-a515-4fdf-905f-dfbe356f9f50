package com.rome.stock.innerservice.domain.message.consumer;

import com.alibaba.fastjson.JSONObject;
import com.rome.scm.common.rocketmq.CustomConsumerListener;
import com.rome.stock.common.constants.warehouse.WarehouseRecordConstant;
import com.rome.stock.common.enums.CustomRocketMQEnum;
import com.rome.stock.common.enums.warehouse.WarehouseRecordTypeVO;
import com.rome.stock.innerservice.api.dto.WarehouseRecordPageDTO;
import com.rome.stock.innerservice.api.dto.warehouserecord.InCmpMQDTO;
import com.rome.stock.innerservice.domain.service.ShopReplenishService;
import lombok.extern.slf4j.Slf4j;
import org.apache.rocketmq.common.message.MessageExt;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.util.Objects;

/**
 * @Description: 门店补货入库回传CMP消费mq
 */
@Slf4j
@Service
public class ShopReplenishCmpInNotifyConsumer implements CustomConsumerListener<MessageExt> {

    @Resource
    private ShopReplenishService shopReplenishService;

    @Override
    public void onMessage(MessageExt messageExt, String businessNo, String msgKey) {
        log.info("门店补货入库回传cmp消息消费，msgID: {}，消费次数: {}", messageExt.getMsgId(), messageExt.getReconsumeTimes());
        String json = new String(messageExt.getBody());
        InCmpMQDTO inCmpMQDTO= JSONObject.parseObject(json, InCmpMQDTO.class);
        if(StringUtils.isEmpty(inCmpMQDTO.getRecordCode())) {
            log.error("门店补货入库回传cmp消息消费内容为空，msgID: {}", messageExt.getMsgId());
            return ;
        }
        //门店补货入库单
        WarehouseRecordPageDTO recordDto = inCmpMQDTO.initWarehouseRecordPageDTO();
        if (Objects.equals(WarehouseRecordConstant.NEED_CMP7,inCmpMQDTO.getCmpStatus())){
            if(isFranchisee(inCmpMQDTO.getRecordType())){
                shopReplenishService.pushShopReplenishResultCmp6(recordDto);
            }else {
                shopReplenishService.pushShopReplenishResultCmp5(recordDto);
            }
        }else if (Objects.equals(WarehouseRecordConstant.NEED_CMP,inCmpMQDTO.getCmpStatus())){
            if(isFranchisee(inCmpMQDTO.getRecordType())){
                shopReplenishService.handleJoinReplenishPushCMP(recordDto);
            }else {
                shopReplenishService.handleDirectReplenishPushCMP(recordDto);
            }
        }

    }

	@Override
	public String getRocketMQCode() {
		return CustomRocketMQEnum.MQ_SHOP_REPLENISH_CMP_IN_NOTIFY_PUSH.getCode();
	}


    /**
     * 是否加盟门店
     * @param recordType
     * @return
     */
    private boolean isFranchisee(Integer recordType){
        return WarehouseRecordTypeVO.LS_REPLENISH_IN_SHOP_RECORD.getType().equals(recordType)||
                WarehouseRecordTypeVO.SHOP_INVENTORY_IN_RECORD.getType().equals(recordType)
                || WarehouseRecordTypeVO.AB_PRESALE_DISTRIBUTION_IN_RECORD.getType().equals(recordType)
                || WarehouseRecordTypeVO.LS_COLD_OVER_STOCK_IN_RECORD.getType().equals(recordType)
                || WarehouseRecordTypeVO.LS_REPLENISH_IN_SHOP_PLAN_RECORD.getType().equals(recordType);
    }

}
