package com.rome.stock.innerservice.domain.service.impl;

import com.rome.arch.core.exception.RomeException;
import com.rome.stock.innerservice.api.dto.ShortageSummaryDTO;
import com.rome.stock.innerservice.common.ResCode;
import com.rome.stock.innerservice.common.RomeCollectionUtil;
import com.rome.stock.innerservice.constant.FrontRecordTypeVO;
import com.rome.stock.innerservice.constant.RecordStatusVO;
import com.rome.stock.core.domain.repository.CoreVirtualWarehouseStockRepository;
import com.rome.stock.innerservice.constant.VirtualWarehouseMoveRecordTypeVO;
import com.rome.stock.innerservice.domain.repository.VirtualWarehouseMoveRecordRepository;
import com.rome.stock.innerservice.domain.repository.WarehouseMoveConfigRepository;
import com.rome.stock.innerservice.domain.service.OrderUtilService;
import com.rome.stock.innerservice.domain.service.VwStockMoveService;
import com.rome.stock.core.facade.RedisRollBackFacade;
import com.rome.stock.innerservice.infrastructure.dataobject.VirtualWarehouseMoveRecordDO;
import com.rome.stock.innerservice.infrastructure.dataobject.VirtualWarehouseSkuDO;
import com.rome.stock.innerservice.infrastructure.dataobject.WarehouseMoveConfigDO;
import com.rome.stock.core.infrastructure.dataobject.core.CoreRealStockOpDO;
import com.rome.stock.core.infrastructure.dataobject.core.CoreVirtualStockOpDO;
import com.rome.stock.core.infrastructure.dataobject.core.CoreVirtualWarehouseStockDO;
import com.rome.stock.core.infrastructure.redis.VirtualWarehouseStockRedis;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

/**
 * @Doc:虚仓自动转移
 * @Author: lchy
 * @Date: 2020/6/11
 * @Version 1.0
 */
@Slf4j
@Service
public class VwStockMoveServiceImpl implements VwStockMoveService {

    @Resource
    private WarehouseMoveConfigRepository warehouseMoveConfigRepository;
    @Resource
    private VirtualWarehouseStockRedis virtualWarehouseStockRedis;
    @Resource
    private VirtualWarehouseMoveRecordRepository virtualWarehouseMoveRecordRepository;
    @Resource
    private OrderUtilService orderUtilService;
    @Resource
    private CoreVirtualWarehouseStockRepository coreVirtualWarehouseStockRepository;

    /**
     * 处理虚仓转移
     *
     * @param skuInfos 缺货明细
     * @param rwId     实仓id
     * @param vwId     缺货虚仓id
     * @param typeVO   区分来自页面还是job
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void executeVmStockMove(List<ShortageSummaryDTO> skuInfos, Long rwId, Long vwId , FrontRecordTypeVO typeVO) {
        log.info("处理虚仓转移开始，实仓id=[{}]，虚仓id=[{}]", rwId, vwId);
        if (CollectionUtils.isEmpty(skuInfos)) {
            throw new RomeException(ResCode.STOCK_ERROR_1001, "处理虚仓转移结束，实仓id=[" + rwId + "]，虚仓id=[" + vwId + "]，无缺货明细");
        }
        WarehouseMoveConfigDO param = new WarehouseMoveConfigDO();
        param.setRealWarehouseId(rwId);
        param.setIsMoved(1);
        param.setStatus(1);
        param.setWarehouseType(1);
        List<WarehouseMoveConfigDO> realConfigs = warehouseMoveConfigRepository.queryWarehouseConfigByCondition(param);
        if (CollectionUtils.isEmpty(realConfigs)) {
            throw new RomeException(ResCode.STOCK_ERROR_1001, "实仓id=[" + rwId +"]，虚仓id=[" + vwId + "]，无需处理，请检查实仓配置");

        }
        param.setWarehouseType(2);
        param.setSecurityLevel(1);
        param.setIsMoved(null);
        List<WarehouseMoveConfigDO> virtualConfigs = warehouseMoveConfigRepository.queryWarehouseConfigByCondition(param);
        Map<Long, WarehouseMoveConfigDO> virtualConfigMap = RomeCollectionUtil.listforMap(virtualConfigs, "virtualWarehouseId");
        if (!virtualConfigMap.containsKey(vwId)) {
            throw new RomeException(ResCode.STOCK_ERROR_1001, "实仓id=[" + rwId +"]，虚仓id=[" + vwId + "]，无需处理，请检查虚仓配置");

        }
        List<Long> vwIdList = virtualConfigs.stream().filter(item -> item.getIsMoved() == 1 && !item.getVirtualWarehouseId().equals(rwId)).map(WarehouseMoveConfigDO::getVirtualWarehouseId).collect(Collectors.toList());
        if (vwIdList.size() == 0) {
            throw new RomeException(ResCode.STOCK_ERROR_1001, "实仓id=[" + rwId +"]，虚仓id=[" + vwId + "]，无可转出虚仓，请检查虚仓配置");

        }
        List<Long> skuIds = RomeCollectionUtil.getValueList(skuInfos, "skuId");

        List<WarehouseMoveConfigDO> skuConfigs = warehouseMoveConfigRepository.querySkuConfigs(rwId, vwIdList, skuIds);
        Map<Long, List<WarehouseMoveConfigDO>> skuConfigMap = RomeCollectionUtil.listforListMap(skuConfigs, "skuId");
        Map<Long , Map<Long ,BigDecimal>> skuVwSecurityQtyMap = new HashMap<>();
        skuConfigMap.forEach((k,v)->{
            skuVwSecurityQtyMap.put(k , RomeCollectionUtil.listforMap(v,"virtualWarehouseId","securityQty"));
        });

        List<CoreVirtualWarehouseStockDO> stockList ;
        List<CoreVirtualStockOpDO> virtualStockDOs = new ArrayList<>();
        //key为虚仓id，value为该虚仓对应sku的预留安全库存
        Map<Long , BigDecimal> vwSecurityQtyMap ;
        //key为skuId + '_' + 虚仓id，value为该sku对应虚仓预计出库数
        Map<String , BigDecimal> planMoveQtyMap = new HashMap<>() ;
        for (ShortageSummaryDTO sku : skuInfos) {
            stockList = new ArrayList<>();
            vwSecurityQtyMap = new HashMap<>();
            for (Long vId : vwIdList) {
                CoreVirtualWarehouseStockDO tempStock = virtualWarehouseStockRedis.getVirtualWarehouseStock(sku.getSkuId(), vId);
                if (tempStock == null) {
                    continue;
                }
                BigDecimal securityQty = virtualConfigMap.get(vId).getSecurityQty();
                //设置了sku级的安全库存。则用sku级的覆盖
                if (skuVwSecurityQtyMap.containsKey(sku.getSkuId()) && skuVwSecurityQtyMap.get(sku.getSkuId()).containsKey(vId)) {
                    securityQty = skuVwSecurityQtyMap.get(sku.getSkuId()).get(vId);
                }
                if (securityQty == null) {
                    securityQty = BigDecimal.ZERO;
                }
                tempStock.setAvailableQty(tempStock.getAvailableQty().subtract(securityQty));
                stockList.add(tempStock);
                vwSecurityQtyMap.put(vId , securityQty);
            }
            //按可用库存排序,越大的排在越前
            stockList = stockList.stream().sorted(Comparator.comparing(CoreVirtualWarehouseStockDO::getAvailableQty).reversed()).collect(Collectors.toList());
            BigDecimal shortageQty = sku.getShortageQty();
            for (CoreVirtualWarehouseStockDO stockDO : stockList) {
                if (stockDO.getAvailableQty().compareTo(BigDecimal.ZERO) > 0) {
                    Long fromVId = stockDO.getVirtualWarehouseId();
                    String key = sku.getSkuId() + "_" + fromVId;
                    if (stockDO.getAvailableQty().compareTo(shortageQty) > 0) {
                        virtualStockDOs.add(wrapCoreVirtualStockOpDO(sku.getSkuId(), sku.getSkuCode(), fromVId, vwId, shortageQty, vwSecurityQtyMap.get(fromVId)));
                        planMoveQtyMap.put(key , shortageQty);
                        break;
                    } else {
                        virtualStockDOs.add(wrapCoreVirtualStockOpDO(sku.getSkuId(), sku.getSkuCode(), fromVId, vwId, stockDO.getAvailableQty(), vwSecurityQtyMap.get(fromVId)));
                        shortageQty = shortageQty.subtract(stockDO.getAvailableQty());
                        planMoveQtyMap.put(key , stockDO.getAvailableQty());
                    }
                }
            }
        }
        if (virtualStockDOs.size() == 0) {
            throw new RomeException(ResCode.STOCK_ERROR_1001, "实仓id=[" + rwId +"]，虚仓id=[" + vwId + "]，所有缺货物料在其他虚仓也都无多余库存可转出");
        }
        VirtualWarehouseMoveRecordDO recordDO = createRecordBasicInfo(typeVO, rwId, vwId);
        CoreRealStockOpDO stockDO = new CoreRealStockOpDO();
        stockDO.setRecordCode(recordDO.getRecordCode());
        //虚仓流水使用后置单统一类型,50
        stockDO.setTransType(VirtualWarehouseMoveRecordTypeVO.VIRTUAL_WAREHOUSE_MOVE_RECORD.getType());
        stockDO.setVirtualStockByCalculateDOs(virtualStockDOs);
        boolean isSuccess = false;
        try {
            coreVirtualWarehouseStockRepository.minAvailMoveRealQtyToOtherVWarehouse(stockDO);
            saveRecord(recordDO, virtualStockDOs, planMoveQtyMap);
            isSuccess = true;
        } catch (Exception e) {
            log.error("处理虚仓转移异常结束，实仓id=[{}]，虚仓id=[{}]，错误信息{}", rwId, vwId, e.getMessage(), e);
            throw new RomeException(ResCode.STOCK_ERROR_1001, "处理虚仓转移异常结束，实仓id=[" + rwId +"]，虚仓id=[" + vwId + "]，错误信息:" + e.getMessage());

        } finally {
            if (!isSuccess) {
                RedisRollBackFacade.redisRollBack(stockDO);
            }
        }
        log.info("处理虚仓转移正常结束，实仓id=[{}]，虚仓id=[{}]", rwId, vwId);
    }


    private VirtualWarehouseMoveRecordDO createRecordBasicInfo(FrontRecordTypeVO typeVO, Long rwId, Long vwId) {
        VirtualWarehouseMoveRecordDO virDO = new VirtualWarehouseMoveRecordDO();
        virDO.setRecordStatus(RecordStatusVO.FINISHED.getStatus());
        virDO.setRecordCode(orderUtilService.queryOrderCode(typeVO.getCode()));
        virDO.setRecordType(typeVO.getType());
        virDO.setRealWarehouseId(rwId);
        virDO.setInVirtualWarehouseId(vwId);
        return virDO;
    }

    private void saveRecord(VirtualWarehouseMoveRecordDO recordDO, List<CoreVirtualStockOpDO> virtualStockDOs , Map<String , BigDecimal> planMoveQtyMap) {
        virtualWarehouseMoveRecordRepository.insertVirtualWarehouseMoveRecord(recordDO);
        List<VirtualWarehouseSkuDO> virtualWarehouseSkus = new ArrayList<>();
        for (CoreVirtualStockOpDO stockOpDO : virtualStockDOs) {
            VirtualWarehouseSkuDO virtualWarehouseSkuDO = new VirtualWarehouseSkuDO();
            virtualWarehouseSkuDO.setSkuCode(stockOpDO.getSkuCode());
            virtualWarehouseSkuDO.setSkuId(stockOpDO.getSkuId());
            virtualWarehouseSkuDO.setInVirtualWarehouseId(stockOpDO.getToVirtualWarehouseId());
            virtualWarehouseSkuDO.setOutVirtualWarehouseId(stockOpDO.getVirtualWarehouseId());
            virtualWarehouseSkuDO.setSkuQty(planMoveQtyMap.get(stockOpDO.getSkuId() + "_" + stockOpDO.getVirtualWarehouseId()));
            virtualWarehouseSkuDO.setActualQty(stockOpDO.getRealQty());
            //为子表设置主键
            virtualWarehouseSkuDO.setMoveRecordId(recordDO.getId());
            virtualWarehouseSkuDO.setRecordCode(recordDO.getRecordCode());
            virtualWarehouseSkus.add(virtualWarehouseSkuDO);
        }
        //插入子表数据
        virtualWarehouseMoveRecordRepository.insertVirtualWarehouseMoveRecordDetail(virtualWarehouseSkus);
    }


    private CoreVirtualStockOpDO wrapCoreVirtualStockOpDO(Long skuId, String skuCode, Long fromVId, Long toVId, BigDecimal realQty, BigDecimal securityQty) {
        CoreVirtualStockOpDO detailDO = new CoreVirtualStockOpDO();
        detailDO.setSkuId(skuId);
        detailDO.setSkuCode(skuCode);
        //出仓id
        detailDO.setVirtualWarehouseId(fromVId);
        //入仓id
        detailDO.setToVirtualWarehouseId(toVId);
        detailDO.setLockQty(securityQty);
        detailDO.setRealQty(realQty);
        return detailDO;
    }
}
