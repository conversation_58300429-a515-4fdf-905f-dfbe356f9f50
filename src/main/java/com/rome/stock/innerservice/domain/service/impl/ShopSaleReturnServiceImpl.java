package com.rome.stock.innerservice.domain.service.impl;

import com.rome.arch.core.domain.EntityFactory;
import com.rome.arch.core.exception.RomeException;
import com.rome.stock.common.utils.DateUtil;
import com.rome.stock.innerservice.api.dto.RealWarehouse;
import com.rome.stock.innerservice.api.dto.warehouserecord.InWarehouseRecordDTO;
import com.rome.stock.innerservice.common.AlikAssert;
import com.rome.stock.innerservice.common.ResCode;
import com.rome.stock.innerservice.common.StringUtils;
import com.rome.stock.innerservice.constant.FrontRecordStatusVO;
import com.rome.stock.innerservice.constant.WarehouseRecordStatusVO;
import com.rome.stock.innerservice.domain.convertor.frontrecord.FrSalesReturnConvertor;
import com.rome.stock.innerservice.domain.entity.warehouserecord.*;
import com.rome.stock.core.domain.repository.CoreRealWarehouseStockRepository;
import com.rome.stock.innerservice.domain.repository.frontrecord.FrSalesReturnRepository;
import com.rome.stock.innerservice.domain.repository.warehouserecord.WarehouseRecordRepository;
import com.rome.stock.innerservice.domain.service.RealWarehouseService;
import com.rome.stock.innerservice.domain.service.ShopSaleReturnService;
import com.rome.stock.core.facade.RedisRollBackFacade;
import com.rome.stock.core.infrastructure.dataobject.core.CoreRealStockOpDO;
import com.rome.stock.core.infrastructure.dataobject.core.CoreRealStockOpDetailDO;
import com.rome.stock.innerservice.facade.StockCostFacade;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Objects;

/**
 * 门店退货
 */
@Service
public class ShopSaleReturnServiceImpl implements ShopSaleReturnService {

	@Resource
	private RealWarehouseService realWarehouseService;

	@Resource
	private CoreRealWarehouseStockRepository coreRealWarehouseStockRepository;

	@Resource
	private WarehouseRecordRepository warehouseRecordRepository;

	@Resource
	private EntityFactory entityFactory;


	/**
	 * 取消门店销售退货单
	 * @param outRecordCode
	 * @return
	 */
	@Override
	@Transactional(rollbackFor = Exception.class)
	public  void cancelShopSaleReturnRecord(String outRecordCode){
		WarehouseRecordE warehouseRecordE = warehouseRecordRepository.queryWarehouseRecordByRecordCode(outRecordCode);
		if(warehouseRecordE == null || FrontRecordStatusVO.DISABLED.getStatus().equals(warehouseRecordE.getRecordStatus())){
			return;
		}
		List<WarehouseRecordDetail> warehouseRecordDetails = warehouseRecordRepository.queryDetailListByRecordId(warehouseRecordE.getId());
		warehouseRecordE.setWarehouseRecordDetails(warehouseRecordDetails);
		warehouseRecordRepository.updateToCanceled(warehouseRecordE.getId());
		//将批次库存状态改成初始化 表示需求扣减批次库存【定时器处理】
		warehouseRecordRepository.updateRecordBatchStatusToInitFromComplete(warehouseRecordE.getRecordCode());
		CoreRealStockOpDO coreRecordRealStockDO = this.initCoreStockObj(warehouseRecordE);
		boolean isSuccess = false;
		try {
			coreRealWarehouseStockRepository.decreaseRealQty(coreRecordRealStockDO);
			isSuccess = true;
		} catch (RomeException e) {
			throw e;
		} catch (Exception e) {
			throw new RomeException(ResCode.STOCK_ERROR_1003, ResCode.STOCK_ERROR_1003_DESC);
		} finally {
			if (!isSuccess) {
				RedisRollBackFacade.redisRollBack(coreRecordRealStockDO);
			}
		}
	}


	@Override
	@Transactional(rollbackFor = Exception.class)
	public void addShopSaleReturnRecord(InWarehouseRecordDTO inWarehouseRecordDTO) {
		//幂等性判断
		WarehouseRecordE warehouseRecordE = warehouseRecordRepository.queryWarehouseRecordByRecordCode(inWarehouseRecordDTO.getRecordCode());
		if(Objects.nonNull(warehouseRecordE)){
			return;
		}
		ShopSaleReturnWarehouseRecordE warehouseRecord = entityFactory.createEntity(ShopSaleReturnWarehouseRecordE.class);
		RealWarehouse realWarehouse = realWarehouseService.findByRealWarehouseOutCodeAndFactoryCode(inWarehouseRecordDTO.getWarehouseCode(),inWarehouseRecordDTO.getFactoryCode());
		AlikAssert.isNotNull(realWarehouse, "999", "当前仓库不存在");
		//根据前置单生成入库单数据
		warehouseRecord.createInRecordByFrontRecord(inWarehouseRecordDTO,realWarehouse.getId());
		//退货单为完成状态
		warehouseRecord.setRecordStatus(WarehouseRecordStatusVO.IN_ALLOCATION.getStatus());

		Date currentDate = new Date();
		warehouseRecord.setOutOrInTime(currentDate);
		if (StringUtils.isNotBlank(inWarehouseRecordDTO.getOutCreateTime())) {
			warehouseRecord.setDeliveryTime(DateUtil.parseDateTime(inWarehouseRecordDTO.getOutCreateTime()));
		}
		if (warehouseRecord.getDeliveryTime() == null) {
			warehouseRecord.setDeliveryTime(currentDate);
		}
		//合并相同的sku明细信息
		warehouseRecord.mergeSameSkuDetails();
		//创建门店销售入库单
		warehouseRecord.addWarehouseRecord();
		CoreRealStockOpDO coreRecordRealStockIncreaseDO = this.initCoreStockObj(warehouseRecord);
		boolean isSuccess = false;
		try {
			coreRealWarehouseStockRepository.increaseRealQty(coreRecordRealStockIncreaseDO);
			//入库单推送到库存成本中心
			StockCostFacade.sendOutOrderToCostMsg(warehouseRecord);
			isSuccess = true;
		} catch (RomeException e) {
			throw e;
		} catch (Exception e) {
			throw new RomeException(ResCode.STOCK_ERROR_1003, ResCode.STOCK_ERROR_1003_DESC);
		} finally {
			if (!isSuccess) {
				RedisRollBackFacade.redisRollBack(coreRecordRealStockIncreaseDO);
			}
		}
	}


	/**
	 * 初始化增加实体仓库库存的对象
	 */
	private CoreRealStockOpDO initCoreStockObj(AbstractWarehouseRecord recordE) {
		List<CoreRealStockOpDetailDO> increaseDetails = new ArrayList<>();
		for (WarehouseRecordDetail detail : recordE.getWarehouseRecordDetails()) {
			CoreRealStockOpDetailDO coreRealStockOpDetailDO = new CoreRealStockOpDetailDO();
			coreRealStockOpDetailDO.setSkuId(detail.getSkuId());
			coreRealStockOpDetailDO.setChannelCode(recordE.getChannelCode());
			coreRealStockOpDetailDO.setSkuCode(detail.getSkuCode());
			coreRealStockOpDetailDO.setCheckBeforeOp(false);
			coreRealStockOpDetailDO.setRealQty(detail.getActualQty());
			coreRealStockOpDetailDO.setRealWarehouseId(recordE.getRealWarehouseId());
			increaseDetails.add(coreRealStockOpDetailDO);
		}
		CoreRealStockOpDO coreRealStockOpDO = new CoreRealStockOpDO();
		coreRealStockOpDO.setRecordCode(recordE.getRecordCode());
		coreRealStockOpDO.setTransType(recordE.getRecordType());
		coreRealStockOpDO.setDetailDos(increaseDetails);
		return coreRealStockOpDO;
	}

}
