package com.rome.stock.innerservice.domain.entity;

import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Component;

import java.util.Date;
import java.util.List;

@Component
@Scope("prototype")
@Data
@EqualsAndHashCode(callSuper = false)
public class VirtualWarehouseMoveRecordE extends BaseE {
	/**
	 * 唯一主键
	 */
	private Long id;
	/**
	 * 编号
	 */
	private String recordCode;
	/**
	 * 单据类型
	 */
	private Integer recordType;
	/**
	 * 单据状态
	 */
	private Integer recordStatus;
	/**
	 * 虚拟仓库id入
	 */
	private Long inVirtualWarehouseId;
	/**
	 * 虚拟仓库id出
	 */
	private Long outVirtualWarehouseId;
	/**
	 * 实仓id
	 */
	private Long realWarehouseId;
	/**
	 * 实仓code
	 */
	private String realWarehouseCode;
	/**
	 * 实仓name
	 */
	private String realWarehouseName;
	/**
	 * 起始时间
	 */
	private Date startTime;
	/**
	 * 结束时间
	 */
	private Date endTime;
	/**
	 * 虚拟仓库名称（入）
	 */
	private String inVirtualWarehouseName;
	/**
	 * 虚拟仓库名称（出）
	 */
	private String outVirtualWarehouseName;
	/**
	 * 创建人名称
	 */
	private String creatorName;

	/**
	 * 创建人
	 */
	private Long creator;

	private String remark;
	/**
	 * 转移商品细节
	 */
	private List<VirtualWarehouseSkuE> virtualWarehouseSkus;
}



