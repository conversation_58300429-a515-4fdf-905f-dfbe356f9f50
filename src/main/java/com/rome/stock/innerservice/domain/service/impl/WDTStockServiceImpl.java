package com.rome.stock.innerservice.domain.service.impl;

import cn.hutool.core.collection.CollUtil;
import com.alibaba.fastjson.JSON;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.rome.arch.core.domain.EntityFactory;
import com.rome.arch.core.exception.RomeException;
import com.rome.arch.util.dynamic.DynamicDataSourceEnum;
import com.rome.arch.util.dynamic.TargetDataSource;
import com.rome.stock.common.enums.warehouse.WarehouseRecordTypeVO;
import com.rome.stock.core.constant.StockCoreConsts;
import com.rome.stock.core.constant.WarehouseRouteLockModeEnum;
import com.rome.stock.core.domain.repository.CoreChannelSalesRepository;
import com.rome.stock.core.domain.repository.CoreVirtualWarehouseStockRepository;
import com.rome.stock.core.facade.RedisRollBackFacade;
import com.rome.stock.core.infrastructure.dataobject.core.*;
import com.rome.stock.innerservice.api.dto.*;
import com.rome.stock.innerservice.api.dto.frontrecord.*;
import com.rome.stock.innerservice.common.AlikAssert;
import com.rome.stock.innerservice.common.ResCode;
import com.rome.stock.innerservice.common.RomeCollectionUtil;
import com.rome.stock.innerservice.common.TmsTools;
import com.rome.stock.innerservice.constant.*;
import com.rome.stock.innerservice.domain.convertor.AddressConvertor;
import com.rome.stock.innerservice.domain.convertor.SkuStockConvertor;
import com.rome.stock.innerservice.domain.convertor.frontrecord.FrWDTSaleConvertor;
import com.rome.stock.innerservice.domain.entity.AddressE;
import com.rome.stock.innerservice.domain.entity.RwRecordPoolDetailE;
import com.rome.stock.innerservice.domain.entity.RwRecordPoolE;
import com.rome.stock.innerservice.domain.entity.VirtualWarehouseE;
import com.rome.stock.innerservice.domain.entity.frontrecord.WDTOnlineRetailE;
import com.rome.stock.innerservice.domain.entity.frontrecord.WDTOnlineRetailRecordDetailE;
import com.rome.stock.innerservice.domain.entity.warehouserecord.WarehouseRecordE;
import com.rome.stock.innerservice.domain.repository.AddressRepository;
import com.rome.stock.innerservice.domain.repository.RwRecordPoolRepository;
import com.rome.stock.innerservice.domain.repository.frontrecord.FrWDTSaleRepository;
import com.rome.stock.innerservice.domain.repository.warehouserecord.WarehouseRecordRepository;
import com.rome.stock.innerservice.domain.service.*;
import com.rome.stock.innerservice.facade.StockOnlineOrderFacade;
import com.rome.stock.innerservice.infrastructure.dataobject.frontrecord.WDTPageInfoDO;
import com.rome.stock.innerservice.infrastructure.redis.WarehouseRouteRedis;
import com.rome.stock.innerservice.remote.base.dto.ChannelDTO;
import com.rome.stock.innerservice.remote.base.facade.ChannelFacade;
import com.rome.stock.innerservice.remote.item.dto.SkuInfoExtDTO;
import com.rome.stock.innerservice.remote.item.facade.SkuFacade;
import com.rome.stock.innerservice.remote.orderTrack.facade.OrderTrackFacade;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

import static java.math.BigDecimal.ROUND_DOWN;

/**
 * @Doc:旺店通接口实现
 * @Author: lchy
 * @Date: 2020/4/22
 * @Version 1.0
 */
@Service
@Slf4j
public class WDTStockServiceImpl extends AbstractStockService implements WDTStockService {

    @Resource
    private FrWDTSaleRepository frWDTSaleRepository;
    @Resource
    private RealWarehouseService realWarehouseService;
    @Resource
    private SkuStockConvertor skuStockConvertor;
    @Resource
    private AddressConvertor addressConvertor;
    @Resource
    private RwRecordPoolRepository rwRecordPoolRepository;
    @Resource
    private WarehouseRecordRepository warehouseRecordRepository;
    @Resource
    private CoreChannelSalesRepository coreChannelSalesRepository;

    @Resource
    private AddressRepository addressRepository;
    @Resource
    private WDTCancelDoRollbackService wdtCancelDoRollbackService;
    @Resource
    private EntityFactory entityFactory;

    @Resource
    private FrWDTSaleConvertor frWDTSaleConvertor;
    @Resource
    private ChannelFacade channelFacade;
    @Resource
    private SkuFacade skuFacade;

    @Resource
    private CoreVirtualWarehouseStockRepository coreVirtualWarehouseStockRepository;

    @Resource
    private TmsTools tmsTools;
    @Resource
    private VirtualWarehouseService virtualWarehouseService;
    @Resource
    private OrderTrackFacade orderTrackFacade;
    /**
     * 定义最大查询条件
     */
    private static final int MAX_COUNT=1000;

    /**
     * 电商下单锁库存
     *
     * @param stockOrderDTO
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public RealWarehouse lockStockByRecord(StockOrderDTO stockOrderDTO) {
        // 必要参数校验
        ValidResult validResult = validOnlineAddOrderParam(stockOrderDTO);
        if (null != validResult) {
            throw new RomeException(validResult.getResCode(), validResult.getResDesc());
        }
        WDTOnlineRetailE recordE = frWDTSaleRepository.queryByOutRecordCode(stockOrderDTO.getOrderCode());
        if (recordE != null) {
            //单据已存在，直接返回
            return realWarehouseService.findByRealWarehouseId(recordE.getRealWarehouseId());
        }
        // 入电商销售前置单表
        WDTOnlineRetailE onlineRetailE = skuStockConvertor.dtoToWDTEntity(stockOrderDTO);
        onlineRetailE.initFrontRecord(stockOrderDTO);
        // 入sc_address收货地址信息表
        AddressE addressE = addressConvertor.dtoToEntity(stockOrderDTO);
        addressE.setUserType((byte) 0);
        addressE.setAddressType((byte) 0);
        addressE.setRecordCode(onlineRetailE.getRecordCode());
        if(StringUtils.isNoneBlank(stockOrderDTO.getOaid())){
            addressE.setRemark(stockOrderDTO.getOaid());
        }
        addressE.addAddress();
        // 寻源 + 锁库存
        CoreChannelOrderDO cco = null;
        boolean isSuccess = false;
        try {
            cco = new CoreChannelOrderDO();
            warpRouteAndLockStockDO(onlineRetailE, addressE ,cco);
            cco.setRouteLockModeAuto(true);
            //不指定仓库下单，需要寻源
            List<CoreVirtualStockOpDO> cvsList = StockOnlineOrderFacade.routeAndLockStock(cco);
            Map<Long, List<CoreVirtualStockOpDO>> rwIdMap = RomeCollectionUtil.listforListMap(cvsList, "realWarehouseId", null);

            //旺店通寻源只会寻源到一个实仓id，否则就是看你没有配置路由模板
            AlikAssert.notEmpty(cvsList, ResCode.STOCK_ERROR_5024, ResCode.STOCK_ERROR_5024_DESC);
            if (rwIdMap.size() > 1 || cco.getRouteLockMode() == null) {
                throw new RomeException(ResCode.STOCK_ERROR_1003, ResCode.STOCK_ERROR_1003_DESC + "请检查配置路由模板配置:" + stockOrderDTO.getChannelCode());
            }
            CoreVirtualStockOpDO res = cvsList.get(0);
            if (cco.getRouteLockMode().equals(WarehouseRouteLockModeEnum.ROUTE_LOCK_MODE_GROUP)) {
                //锁成负库存,渠道库存充足
                onlineRetailE.setSplitType(WDTRecordConst.WDT_SPLIT_EXCEPT);
            } else if (cco.getRouteLockMode().equals(WarehouseRouteLockModeEnum.ROUTE_LOCK_MODE_NEGATIVE)) {
                //超卖订单，渠道库存不足
                onlineRetailE.setSplitType(WDTRecordConst.WDT_SPLIT_OVERSELL);
            } else {
                //正常订单
                onlineRetailE.setSplitType(WDTRecordConst.WDT_SPLIT_NORMAL);
            }
            onlineRetailE.setAllotStatus(WDTRecordConst.WDT_ALLOT_INIT);
            onlineRetailE.setRealWarehouseId(res.getRealWarehouseId());
            onlineRetailE.setVirtualWarehouseId(res.getVirtualWarehouseId());
            onlineRetailE.addFrontRecord();
            isSuccess = true;
            return realWarehouseService.findByRealWarehouseId(onlineRetailE.getRealWarehouseId());
        } catch (RomeException e) {
            log.error(e.getMessage(), e);
            throw e;
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            throw new RomeException(ResCode.STOCK_ERROR_5024, ResCode.STOCK_ERROR_5024_DESC);
        } finally {
            if (!isSuccess && null != cco) {
                RedisRollBackFacade.redisRollBack(cco);
            }
        }
    }
    /**
     * 包装寻源+锁库存接口参数
     */
    private  void warpRouteAndLockStockDO(WDTOnlineRetailE frontRecord, AddressE addressE , CoreChannelOrderDO coreChannelOrderDO) {

        //库存交易类型
        coreChannelOrderDO.setRecordCode(frontRecord.getRecordCode());
        coreChannelOrderDO.setTransType(frontRecord.getRecordType());
        coreChannelOrderDO.setMerchantId(frontRecord.getMerchantId());
        coreChannelOrderDO.setChannelCode(frontRecord.getChannelCode());
        if(addressE != null){
        	// 分别为省、市、区/县、街道或镇Code
        	coreChannelOrderDO.setProvinceCode(addressE.getProvinceCode());
            coreChannelOrderDO.setCityCode(addressE.getCityCode());
            coreChannelOrderDO.setCountyCode(addressE.getCountyCode());
            coreChannelOrderDO.setAreaCode(addressE.getAreaCode());
        }
        List<CoreOrderDetailDO> details = new ArrayList<>();
        coreChannelOrderDO.setOrderDetailDOs(details);
        CoreOrderDetailDO detailDO;
        for (WDTOnlineRetailRecordDetailE detailE : frontRecord.getFrontRecordDetails()) {
            if (detailE.getBasicSkuQty().compareTo(BigDecimal.ZERO) > 0) {
                detailDO = new CoreOrderDetailDO();
                detailDO.setLockQty(detailE.getBasicSkuQty());
                detailDO.setSkuId(detailE.getSkuId());
                detailDO.setSkuCode(detailE.getSkuCode());
                details.add(detailDO);
            }
        }
    }
    /**
     * 校验结果包装器
     */
    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    private class ValidResult {
        private String resCode;
        private String resDesc;
    }
    /**
     * 校验电商下单锁库存接口参数
     *
     * @return 校验结果
     */
    private ValidResult validOnlineAddOrderParam(StockOrderDTO stockOrderDTO) {
        if (null == stockOrderDTO) {
            return new ValidResult(ResCode.STOCK_ERROR_1002, ResCode.STOCK_ERROR_1002_DESC);
        }
        if (StringUtils.isBlank(stockOrderDTO.getAddress())) {
            return new ValidResult(ResCode.STOCK_ERROR_5012, ResCode.STOCK_ERROR_5012_DESC);
        }
        if (StringUtils.isBlank(stockOrderDTO.getOriginOrderCode())) {
            return new ValidResult(ResCode.STOCK_ERROR_1002, "原始订单号不能为空");
        }
        if (stockOrderDTO.getPayTime() == null) {
            return new ValidResult(ResCode.STOCK_ERROR_1002, "支付时间不能为空");
        }
        if (StringUtils.isBlank(stockOrderDTO.getMobile())) {
            return new ValidResult(ResCode.STOCK_ERROR_5005, ResCode.STOCK_ERROR_5005_DESC);
        }
        if (StringUtils.isBlank(stockOrderDTO.getProvince())) {
            return new ValidResult(ResCode.STOCK_ERROR_5006, ResCode.STOCK_ERROR_5006_DESC);
        }
        if (StringUtils.isBlank(stockOrderDTO.getProvinceCode())) {
            return new ValidResult(ResCode.STOCK_ERROR_5007, ResCode.STOCK_ERROR_5007_DESC);
        }
        if (StringUtils.isBlank(stockOrderDTO.getCity())) {
            return new ValidResult(ResCode.STOCK_ERROR_5008, ResCode.STOCK_ERROR_5008_DESC);
        }
        if (StringUtils.isBlank(stockOrderDTO.getCityCode())) {
            return new ValidResult(ResCode.STOCK_ERROR_5009, ResCode.STOCK_ERROR_5009_DESC);
        }
        if (StringUtils.isBlank(stockOrderDTO.getName())) {
            return new ValidResult(ResCode.STOCK_ERROR_5013, ResCode.STOCK_ERROR_5013_DESC);
        }
        if (StringUtils.isBlank(stockOrderDTO.getChannelCode())) {
            return new ValidResult(ResCode.STOCK_ERROR_5001, ResCode.STOCK_ERROR_5001_DESC);
        }
        if (StringUtils.isBlank(stockOrderDTO.getOrderCode())) {
            return new ValidResult(ResCode.STOCK_ERROR_5003, ResCode.STOCK_ERROR_5003_DESC);
        }
        if (null == stockOrderDTO.getMerchantId()) {
            return new ValidResult(ResCode.STOCK_ERROR_5004, ResCode.STOCK_ERROR_5004_DESC);
        }
        if (!OnlineStockTransTypeVO.TRANS_TYPE_5.getTransType().equals(stockOrderDTO.getTransType())) {
            return new ValidResult(ResCode.STOCK_ERROR_5030, ResCode.STOCK_ERROR_5030_DESC);
        }
        if(Objects.isNull(stockOrderDTO.getIsPreSale())){
            return new ValidResult(ResCode.STOCK_ERROR_5030, ResCode.STOCK_ERROR_5030_DESC+" isPreSale不能为空");
        }
        if (null == stockOrderDTO.getFrontRecordDetails() || stockOrderDTO.getFrontRecordDetails().isEmpty()) {
            return new ValidResult(ResCode.STOCK_ERROR_5014, ResCode.STOCK_ERROR_5014_DESC);
        }
        for (OrderDetailDTO orderDetailDTO : stockOrderDTO.getFrontRecordDetails()) {
            if (StringUtils.isBlank(orderDetailDTO.getLineNo())) {
                return new ValidResult(ResCode.STOCK_ERROR_5027, ResCode.STOCK_ERROR_5027_DESC);
            }
            Integer giftType = orderDetailDTO.getGiftType();
            if(giftType == null || giftType < 1 || giftType > 2){
                return new ValidResult(ResCode.STOCK_ERROR_5027, ResCode.STOCK_ERROR_5027_DESC);
            }
        }
        return null;
    }

    /**
     * 保存物流公司编码,SO维度
     * @param paramDTO
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void saveLogisticInfo(List<WDTStockLogisticInfoParamDTO> paramDTO) {
        if (paramDTO == null || paramDTO.size() == 0) {
            throw new RomeException(ResCode.STOCK_ERROR_1002, "推送物流公司信息入参不能为空");
        }
        for (WDTStockLogisticInfoParamDTO dto : paramDTO) {
            //1.物流公司编码校验
            if (StringUtils.isBlank(dto.getLogisticsCode()) || StringUtils.isBlank(dto.getSoCode())) {
                throw new RomeException(ResCode.STOCK_ERROR_1002, "订单编号或物流公司编码不能为空");
            }
            WDTOnlineRetailE retailE = frWDTSaleRepository.queryByOutRecordCode(dto.getSoCode());
            AlikAssert.isNotNull(retailE, ResCode.STOCK_ERROR_1002, "soCode不存在:soCode=" + dto.getSoCode());
            if (FrontRecordStatusVO.DISABLED.getStatus().equals(retailE.getRecordStatus())) {
                throw new RomeException(ResCode.STOCK_ERROR_1002, "soCode已取消:soCode=" + dto.getSoCode());
            }
            if (retailE.getLogisticsCode() != null) {
                //说明已经推送过了，或者用户已经修改过仓库了或重新计算过仓库了无需再推送
                continue;
            }
            retailE.saveLogisticsCode(dto.getLogisticsCode());
            if (WDTRecordConst.WDT_SPLIT_NORMAL.equals(retailE.getSplitType())) {
                //对于正常订单，需要拆单生成一个do单，该do单可以合单
                RwRecordPoolE pool = retailE.createDo(true , null);
                retailE.updateToHasSplitForOrder();
                retailE.updateToHasSplitForDetailsByRecordId();
                int executeResult = retailE.updateVersion(retailE.getVersionNo());
                AlikAssert.isTrue(executeResult > 0, ResCode.STOCK_ERROR_1016,   "更新订单时，同时发生了取消操作:soCode=" + dto.getSoCode());
                //保存订单轨迹
                try {
                    orderTrackFacade.save( retailE.getOutRecordCode(), "拆单成功" ,pool.getDoCode());
                } catch (Exception e) {
                    log.error(e.getMessage() ,e );
                }
            }
        }
    }


    /**
     * 修改子DO单信息[取消行明细，修改地址]
     * @param paramDTO
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateChildDo(UpdateDoOrderInfoDTO paramDTO){
        log.info("=====更新子do单：入参={}", JSON.toJSONString(paramDTO));
        //1.第一步校验子do是否存在 且 不是取消状态，否则抛异常
        String doCode = paramDTO.getDoCode();
        RwRecordPoolE pool = rwRecordPoolRepository.queryWithDetailsByDoCode(doCode);
        AlikAssert.isNotNull(pool, ResCode.STOCK_ERROR_1003, "修改子do单单号不存在：" + doCode);
        if (FrontRecordStatusVO.DISABLED.getStatus().equals(pool.getRecordStatus())) {
            throw new RomeException(ResCode.STOCK_ERROR_5018, ResCode.STOCK_ERROR_5018_DESC + ":doCode=" + doCode);
        }
        //2、第二步校验前置单是否存在，不存在直接抛异常
        //查询前置SO单数据
        WDTOnlineRetailE onlineRetailE = frWDTSaleRepository.queryByRecordCode(pool.getFrontRecordCode());
        AlikAssert.isNotNull(onlineRetailE, ResCode.STOCK_ERROR_1014, ResCode.STOCK_ERROR_1014_DESC + ":doCode=" + doCode);

        int executeResult;
        //如果该do单已合单
        WarehouseRecordE parentDo = null;
        if (RwRecordPoolStatusVO.MERGED.getStatus().equals(pool.getRecordStatus())) {
            //3、第三步查询父DO,并更新父do为取消状态
            parentDo = warehouseRecordRepository.queryWarehouseRecordById(pool.getWarehouseRecordId());
            AlikAssert.isTrue(parentDo!=null && !WarehouseRecordStatusVO.OUT_ALLOCATION.getStatus().equals(parentDo.getRecordStatus()),
                    ResCode.STOCK_ERROR_1017, ResCode.STOCK_ERROR_1017_DESC + "或已出库:doCode=" + doCode);
            //取消后置单操作移到后面执行

            //4、第4步查询父单下所有的子单,全部置为待合单状态
            List<RwRecordPoolE> rwRecordPoolEList = rwRecordPoolRepository.queryKeysByWarehouseId(parentDo.getId());
            for (RwRecordPoolE rwRecordPoolE : rwRecordPoolEList) {
                //从已合单状态改为待合单
                rwRecordPoolRepository.updateToPreMergeById(rwRecordPoolE.getId());
            }
        }
        // 5、第五步，修改地址信息并更新指纹信息
        if (paramDTO.getType() == 1 || paramDTO.getType() == 3) {
            //== 1 仅修改地址  == 3 明细和地址都更新
            AddressE addressE = addressRepository.queryByRecordCode(doCode);
            AlikAssert.isNotNull(addressE, ResCode.STOCK_ERROR_1031, ResCode.STOCK_ERROR_1031_DESC);
            AlikAssert.isTrue(validAddressInfo(paramDTO), ResCode.STOCK_ERROR_1002, "修改地址重要信息都为空：" + doCode);
            addressE.setMobile(paramDTO.getMobile());
            addressE.setName(paramDTO.getName());
            addressE.setAddress(paramDTO.getAddress());
            addressE.setPostcode(paramDTO.getPostcode());
            addressE.setRemark(paramDTO.getRemark());
            addressE.setEmail(paramDTO.getEmail());

            //修改省市区
            addressE.setProvince(paramDTO.getProvince());
            addressE.setProvinceCode(paramDTO.getProvinceCode());
            addressE.setCity(paramDTO.getCity());
            addressE.setCityCode(paramDTO.getCityCode());
            //county
            addressE.setCounty(paramDTO.getCounty());
            addressE.setCountyCode(paramDTO.getCountyCode());
            addressRepository.updateAddressForChildDo(addressE);
            //重新计算指纹信息并更新指纹信息
            pool.setUserCode(onlineRetailE.getUserCode());
            pool.bindMergeFingerprint(addressE);
            pool.updateMergeFingerprint();

            //so地址也要更新，后续拆单的do都用新地址
            addressE.setRecordCode(onlineRetailE.getRecordCode());
            addressRepository.updateAddressForChildDo(addressE);
        }
        List<RwRecordPoolDetailE> unlockDetails = new ArrayList<>();
        List<RwRecordPoolDetailE> poolDetails = pool.getRwRecordPoolDetails();
        // 6、第六步 更新明细
        if (paramDTO.getType() == 2 || paramDTO.getType() == 3) {
            //== 2 仅修改明细  == 3 明细和地址都更新，明细不能为空
            List<OrderDetailDTO> detailDTOS = paramDTO.getFrontRecordDetails();
            AlikAssert.isNotEmpty(detailDTOS, ResCode.STOCK_ERROR_1003, "修改子do单明细 单入参明细不存在");
            Set<String> skuLineNoList = new HashSet<>();
            Map<String, RwRecordPoolDetailE> poolDetailsMap = RomeCollectionUtil.listforMap(poolDetails, "lineNo");
            for (OrderDetailDTO dto : detailDTOS) {
                String lineNo = dto.getLineNo();
                if (dto.getLineNo() == null || skuLineNoList.contains(dto.getLineNo())) {
                    throw new RomeException(ResCode.STOCK_ERROR_1002, ResCode.STOCK_ERROR_1002_DESC + ": 不允许有相同行出现多行或行号为空");
                }
                skuLineNoList.add(dto.getLineNo());
                if (!poolDetailsMap.containsKey(dto.getLineNo())) {
                    throw new RomeException(ResCode.STOCK_ERROR_1003, "修改子do单明细,明细在do单中不存在doCode=" + doCode + ",lineNo=" + lineNo);
                }
                RwRecordPoolDetailE detailE = poolDetailsMap.get(lineNo);
                if (dto.getSkuQty().compareTo(BigDecimal.ZERO) < 0) {
                    //入参不能小于0，可以等于0
                    throw new RomeException(ResCode.STOCK_ERROR_1003, "修改子do单明细,不能小于0：" + doCode + ",lineNo=" + lineNo);
                }
                if (detailE.getSkuQty().compareTo(dto.getSkuQty()) <= 0) {
                    //数量只能改小，不能改大，也不能相等， 相等不应该传
                    throw new RomeException(ResCode.STOCK_ERROR_1003, "修改子do单明细,明细只能改小(也不能相等， 相等不应该传)：" + doCode + ",lineNo=" + lineNo);
                }
                //根据基本单位与销售单位的数量反算计算比例
                BigDecimal scale = detailE.getBasicSkuQty().divide(detailE.getSkuQty(), StockCoreConsts.DECIMAL_POINT_NUM,ROUND_DOWN);
                //销售单位转换为基本单位
                BigDecimal actQty = dto.getSkuQty().multiply(scale);
                //更新明细数量【销售单位和基本单位的数量都要更新】
                rwRecordPoolRepository.updateQty(detailE.getId(), dto.getSkuQty(), actQty);
                AlikAssert.isTrue(this.validPoolHasEffectDetails(doCode), ResCode.STOCK_ERROR_1003, "请调用取消接口而非修改接口:" + doCode);
                //计算需要释放的量
                BigDecimal needUnlockQty = detailE.getBasicSkuQty().subtract(actQty);
                detailE.setBasicSkuQty(needUnlockQty);
                unlockDetails.add(detailE);
            }
        }

        // 7、第七步释放锁定库存
        CoreChannelOrderDO cco = null;
        boolean isSuccess = false;
        try {
            if (unlockDetails.size() > 0) {
                cco = new CoreChannelOrderDO();
                warpUnLockOutStockDo(onlineRetailE, unlockDetails ,cco);
                coreChannelSalesRepository.unlockStock(cco);
            }
            executeResult = rwRecordPoolRepository.updateVersionNo(pool.getId(), pool.getVersionNo());
            AlikAssert.isTrue(executeResult > 0, ResCode.STOCK_ERROR_1016, "修改do时，该do单已被其他程序修改:doCode=" + doCode);

            //8、第八步，如果已推送wms，则通知wms撤销父do
            if (parentDo != null) {
                if (WmsSyncStatusVO.SYNCHRONIZED.getStatus().equals(parentDo.getSyncWmsStatus())) {
                    executeResult = warehouseRecordRepository.updateToCanceledFromHasSync(parentDo.getId());
                    AlikAssert.isTrue(executeResult > 0, ResCode.STOCK_ERROR_1016, ResCode.STOCK_ERROR_1016_DESC + ",请稍后再试:doCode=" + doCode);
                    boolean syncStatus = this.notifyWmsToCancelDO(parentDo);
                    AlikAssert.isTrue(syncStatus, ResCode.STOCK_ERROR_1003, "wms不允许修改do单" + ":doCode=" + doCode);
                } else {
                    executeResult = warehouseRecordRepository.updateToCanceledFromUnSync(parentDo.getId());
                    AlikAssert.isTrue(executeResult > 0, ResCode.STOCK_ERROR_1016, ResCode.STOCK_ERROR_1016_DESC + ",请稍后再试:doCode=" + doCode);
                }
            }
            isSuccess = true;
        } catch (RomeException e) {
            log.error(e.getMessage() + "doCode=" + doCode, e);
            throw e;
        } catch (Exception e) {
            log.error(e.getMessage() + "doCode=" + doCode, e);
            throw new RomeException(ResCode.STOCK_ERROR_5025, ResCode.STOCK_ERROR_5025_DESC);
        } finally {
            if (!isSuccess) {
                RedisRollBackFacade.redisRollBack(cco);
            }
        }
        log.info("=====修改子do单成功结束：doCode={}", doCode);
    }

    /**
     * 修改so地址,拆单之前才允许修改
     * @param
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateAddressInfoBySoCode(StockOrderRecordInfoDTO stockOrderRecordDTO) {
        //查询前置SO单数据
        WDTOnlineRetailE onlineRetailE = frWDTSaleRepository.queryByOutRecordCode(stockOrderRecordDTO.getOrderCode());
        AlikAssert.isNotNull(onlineRetailE, ResCode.STOCK_ERROR_1014, ResCode.STOCK_ERROR_1014_DESC);
        if (FrontRecordStatusVO.DISABLED.getStatus().equals(onlineRetailE.getRecordStatus())) {
            throw new RomeException(ResCode.STOCK_ERROR_1001, ResCode.STOCK_ERROR_1001_DESC + "该订单已取消无法修改地址:soCode=" + stockOrderRecordDTO.getOrderCode());
        }
        List<RwRecordPoolE> pools = rwRecordPoolRepository.queryNotCanceledByFRId(onlineRetailE.getRecordCode());
        //已拆过单的不允许修改so地址
        if (pools.size() > 0) {
            throw  new RomeException(ResCode.STOCK_ERROR_1001,"已拆过单不允许修改地址,请用do单修改地址接口");
        }
        AddressE addressE = addressRepository.queryByRecordCode(onlineRetailE.getRecordCode());
        AlikAssert.isNotNull(addressE, ResCode.STOCK_ERROR_1031, ResCode.STOCK_ERROR_1031_DESC);
        addressE.setMobile(stockOrderRecordDTO.getMobile());
        addressE.setName(stockOrderRecordDTO.getName());
        addressE.setAddress(stockOrderRecordDTO.getAddress());
        addressE.setPostcode(stockOrderRecordDTO.getPostcode());
        addressE.setRemark(stockOrderRecordDTO.getRemark());
        addressE.setEmail(stockOrderRecordDTO.getEmail());
        //修改省市区
        addressE.setProvince(stockOrderRecordDTO.getProvince());
        addressE.setProvinceCode(stockOrderRecordDTO.getProvinceCode());
        addressE.setCity(stockOrderRecordDTO.getCity());
        addressE.setCityCode(stockOrderRecordDTO.getCityCode());
        //county
        addressE.setCounty(stockOrderRecordDTO.getCounty());
        addressE.setCountyCode(stockOrderRecordDTO.getCountyCode());
        addressRepository.updateAddressForChildDo(addressE);
        //9、状态校验，验证是否有并发修改
        int executeResult = onlineRetailE.updateVersion(onlineRetailE.getVersionNo());
        AlikAssert.isTrue(executeResult > 0, ResCode.STOCK_ERROR_1016,   "修改so时，该so单已被其他程序修改:soCode=" + stockOrderRecordDTO.getOrderCode());
    }

    /**
     * 根据子do单号取消do单
     * @param doCode
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void cancelChildDo(String doCode){
        log.info("=====取消旺店通子do单：doCode={}", doCode);
        //1.第一步校验子do是否存在 且 不是取消状态，否则抛异常
        RwRecordPoolE pool = rwRecordPoolRepository.queryWithDetailsByDoCode(doCode);
        AlikAssert.isNotNull(pool, ResCode.STOCK_ERROR_1003, "取消子do单单号不存在：" + doCode);
        if (FrontRecordStatusVO.DISABLED.getStatus().equals(pool.getRecordStatus())) {
            throw new RomeException(ResCode.STOCK_ERROR_5017, ResCode.STOCK_ERROR_5017_DESC + ":doCode=" + doCode);
        }
        //2、第二步校验前置单是否存在，不存在直接抛异常
        //查询前置SO单数据
        WDTOnlineRetailE onlineRetailE = frWDTSaleRepository.queryByRecordCode(pool.getFrontRecordCode());
        AlikAssert.isNotNull(onlineRetailE, ResCode.STOCK_ERROR_1014, ResCode.STOCK_ERROR_1014_DESC + ":doCode=" + doCode);

        //3、第三步 将该do单改为取消状态
        int executeResult = rwRecordPoolRepository.updateToCanceledById(pool.getId());
        AlikAssert.isTrue(executeResult > 0, ResCode.STOCK_ERROR_1030, ResCode.STOCK_ERROR_1030_DESC + ":doCode=" + doCode);


        //4、第四步根据前置单ID查询对应的Do池数据，如果该前置单下的所有子单都取消了，则将前置单置为取消
        List<RwRecordPoolE> rwRecordPoolEList = rwRecordPoolRepository.queryNotCanceledByFRIdWithDetails(onlineRetailE.getRecordCode());
        if (rwRecordPoolEList.size() < 1) {
            //小于1表示该前置单下其他子单都是取消状态,
            if (onlineRetailE.getAllotStatus().equals(WDTRecordConst.WDT_ALLOT_OVER)) {
                //并且该订单已经拆单结束了
                onlineRetailE.updateToCanceled();
            }
        }
        //如果该do单已合单
        WarehouseRecordE parentDo = null ;
        if (RwRecordPoolStatusVO.MERGED.getStatus().equals(pool.getRecordStatus())) {
            //5、第五步查询父DO,并更新父do为取消状态
            parentDo = warehouseRecordRepository.queryWarehouseRecordById(pool.getWarehouseRecordId());
            AlikAssert.isTrue(parentDo!=null && !WarehouseRecordStatusVO.OUT_ALLOCATION.getStatus().equals(parentDo.getRecordStatus()),
                    ResCode.STOCK_ERROR_1017, ResCode.STOCK_ERROR_1017_DESC + "或已出库:doCode=" + doCode);


            //6、第六步查询父单下所有的子单,除了该do单外全部置为待合单状态
            rwRecordPoolEList = rwRecordPoolRepository.queryKeysByWarehouseId(parentDo.getId());
            for (RwRecordPoolE rwRecordPoolE : rwRecordPoolEList) {
                if (!doCode.equals(rwRecordPoolE.getDoCode())) {
                    //从已合单状态改为待合单
                    rwRecordPoolRepository.updateToPreMergeById(rwRecordPoolE.getId());
                }
            }
        }

        // 7、第七步释放锁定库存
        List<RwRecordPoolDetailE> poolDetails = pool.getRwRecordPoolDetails();
        CoreChannelOrderDO cco = null;
        boolean isSuccess = false;
        try {
            cco = new CoreChannelOrderDO();
            warpUnLockOutStockDo(onlineRetailE, poolDetails ,cco);
            coreChannelSalesRepository.unlockStock(cco);
            executeResult = rwRecordPoolRepository.updateVersionNo(pool.getId(), pool.getVersionNo());
            AlikAssert.isTrue(executeResult > 0, ResCode.STOCK_ERROR_1016, "取消do时，该do单已被其他程序修改:doCode=" + doCode);
            //8、第八步，如果已推送wms，则通知wms撤销父do
            if (parentDo != null) {
                if (WmsSyncStatusVO.SYNCHRONIZED.getStatus().equals(parentDo.getSyncWmsStatus())) {
                    executeResult = warehouseRecordRepository.updateToCanceledFromHasSync(parentDo.getId());
                    AlikAssert.isTrue(executeResult > 0, ResCode.STOCK_ERROR_1016, ResCode.STOCK_ERROR_1016_DESC + ",请稍后再试:doCode=" + doCode);
                    boolean syncStatus = this.notifyWmsToCancelDO(parentDo);
                    AlikAssert.isTrue(syncStatus, ResCode.STOCK_ERROR_1003, "wms不允许取消do单" + ":doCode=" + doCode);
                } else {
                    executeResult = warehouseRecordRepository.updateToCanceledFromUnSync(parentDo.getId());
                    AlikAssert.isTrue(executeResult > 0, ResCode.STOCK_ERROR_1016, ResCode.STOCK_ERROR_1016_DESC + ",请稍后再试:doCode=" + doCode);
                }
            }
            isSuccess = true;
        } catch (RomeException e) {
            log.error(e.getMessage() + "doCode=" + doCode, e);
            throw e;
        } catch (Exception e) {
            log.error(e.getMessage() + "doCode=" + doCode, e);
            throw new RomeException(ResCode.STOCK_ERROR_5025, ResCode.STOCK_ERROR_5025_DESC);
        } finally {
            if (!isSuccess) {
                RedisRollBackFacade.redisRollBack(cco);
            }
        }
        log.info("=====取消子do单成功结束：doCode={}", doCode);
    }
    /**
     * 包装取消订单/出库的接口参数
     */
    private void warpUnLockOutStockDo(WDTOnlineRetailE frontRecordE, List<RwRecordPoolDetailE> poolDetails ,CoreChannelOrderDO cco ) {
        cco.setRecordCode(frontRecordE.getRecordCode());
        cco.setTransType(frontRecordE.getRecordType());
        cco.setMerchantId(frontRecordE.getMerchantId());
        cco.setChannelCode(frontRecordE.getChannelCode());
        //虚仓信息
        cco.setVirtualStockOpDetailDOs(new ArrayList<>());
        CoreVirtualStockOpDO vwDo;
        for (RwRecordPoolDetailE detailE : poolDetails) {
            if(detailE.getBasicSkuQty().compareTo(BigDecimal.ZERO)> 0) {
                //虚仓
                vwDo = new CoreVirtualStockOpDO();
                vwDo.setSkuId(detailE.getSkuId());
                vwDo.setSkuCode(detailE.getSkuCode());
                vwDo.setUnlockQty(detailE.getBasicSkuQty());
                vwDo.setVirtualWarehouseId(detailE.getVirtualWarehouseId());
                vwDo.setRealWarehouseId(detailE.getRealWarehouseId());
                cco.getVirtualStockOpDetailDOs().add(vwDo);
            }
        }
    }
    private void warpUnLockOutStockDoForChangeInfo(WDTOnlineRetailE frontRecordE, List<RwRecordPoolDetailE> poolDetails ,CoreChannelOrderDO cco,Long rId,Long vId ) {
        cco.setRecordCode(frontRecordE.getRecordCode());
        cco.setTransType(frontRecordE.getRecordType());
        cco.setMerchantId(frontRecordE.getMerchantId());
        cco.setChannelCode(frontRecordE.getChannelCode());
        //虚仓信息
        cco.setVirtualStockOpDetailDOs(new ArrayList<>());
        CoreVirtualStockOpDO vwDo;
        for (RwRecordPoolDetailE detailE : poolDetails) {
            if(detailE.getBasicSkuQty().compareTo(BigDecimal.ZERO)> 0) {
                //虚仓
                vwDo = new CoreVirtualStockOpDO();
                vwDo.setSkuId(detailE.getSkuId());
                vwDo.setSkuCode(detailE.getSkuCode());
                vwDo.setUnlockQty(detailE.getBasicSkuQty());
                vwDo.setVirtualWarehouseId(vId);
                vwDo.setRealWarehouseId(rId);
                cco.getVirtualStockOpDetailDOs().add(vwDo);
            }
        }
    }

    /**
     * 根据订单号取消订单
     * @param soCode
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void cancelOrder(String  soCode){
        //查询前置SO单数据
        WDTOnlineRetailE onlineRetailE = frWDTSaleRepository.queryByOutRecordCode(soCode);
        AlikAssert.isNotNull(onlineRetailE, ResCode.STOCK_ERROR_1014, ResCode.STOCK_ERROR_1014_DESC);
        if (FrontRecordStatusVO.DISABLED.getStatus().equals(onlineRetailE.getRecordStatus())) {
            throw new RomeException(ResCode.STOCK_ERROR_5017, ResCode.STOCK_ERROR_5017_DESC + ":soCode=" + soCode);
        }
        int executeResult ;
        //1、取消前置单
        onlineRetailE.updateToCanceled();
        //2、查关联的pool表
        List<RwRecordPoolE> rwRecordPoolEList = rwRecordPoolRepository.queryNotCanceledByFRIdWithDetails(onlineRetailE.getRecordCode());
        //需要释放库存的明细列表
        List<RwRecordPoolDetailE> unClockPoolDetails = new ArrayList<>();
        //需要通知wms取消的出库单列表
        List<WarehouseRecordE> cancelDoList = new ArrayList<>();

        for (RwRecordPoolE pool : rwRecordPoolEList) {
            if (RwRecordPoolStatusVO.MERGED.getStatus().equals(pool.getRecordStatus())) {
                //3、第3步查询父DO
                WarehouseRecordE parentDo = warehouseRecordRepository.queryWarehouseRecordById(pool.getWarehouseRecordId());
                AlikAssert.isTrue(parentDo != null && !WarehouseRecordStatusVO.OUT_ALLOCATION.getStatus().equals(parentDo.getRecordStatus()),
                        ResCode.STOCK_ERROR_1017, ResCode.STOCK_ERROR_1017_DESC + "或已出库:soCode=" + soCode);
                cancelDoList.add(parentDo);
                //4、第4步查询父单下所有的子单,除了该do单外全部置为待合单状态【只有该so是正常单时tempPoolList才会 > 1】
                //只有正常单才有必要查询其他do单，因为异常单不会合单
                if (onlineRetailE.getSplitType().equals(WDTRecordConst.WDT_SPLIT_NORMAL)) {
                    List<RwRecordPoolE> tempPoolList = rwRecordPoolRepository.queryKeysByWarehouseId(parentDo.getId());
                    for (RwRecordPoolE rwRecordPoolE : tempPoolList) {
                        if (!pool.getDoCode().equals(rwRecordPoolE.getDoCode())) {
                            //从已合单状态改为待合单
                            rwRecordPoolRepository.updateToPreMergeById(rwRecordPoolE.getId());
                        }
                    }
                }
            }
            // 该子do单本身需要取消掉
            executeResult = rwRecordPoolRepository.updateToCanceledById(pool.getId());
            AlikAssert.isTrue(executeResult > 0, ResCode.STOCK_ERROR_1030, "取消so时，该so单已被其他程序修改:soCode=" + soCode+ ",doCode = " + pool.getDoCode());
            //5、暂存需要释放库存的明细
            unClockPoolDetails.addAll(pool.getRwRecordPoolDetails());
        }
        // 6、查找该so是否还有未合单的明细，未合单的明细也需要释放库存
        if (onlineRetailE.getAllotStatus().equals(WDTRecordConst.WDT_ALLOT_INIT)) {
            List<WDTOnlineRetailRecordDetailE> notSplitDetails = onlineRetailE.selectDetailByIdForSplit();
            if (notSplitDetails.size() > 0) {
                //单位转换一下，得到基本单位
                onlineRetailE.getSkuQtyUnitTools().convertRealToBasic(notSplitDetails, onlineRetailE.getMerchantId());
                RwRecordPoolE tempPoolE = entityFactory.createEntity(RwRecordPoolE.class);
                tempPoolE.setVirtualWarehouseId(onlineRetailE.getVirtualWarehouseId());
                tempPoolE.setRealWarehouseId(onlineRetailE.getRealWarehouseId());
                tempPoolE.warpRwRecordPoolDetail(notSplitDetails);
                unClockPoolDetails.addAll(tempPoolE.getRwRecordPoolDetails());
            }
        }
        boolean isSuccess = false;

        CoreStockOpFactoryDO stockOpFactoryDO = new CoreStockOpFactoryDO();
        CoreChannelOrderDO cco;
        //取消成功的do单，回滚用
        List<WarehouseRecordE> cancelSuccessDoList = new ArrayList<>();
        try {
            // 7、释放库存
            cco = stockOpFactoryDO.createCoreChannelOrderDO();
            warpUnLockOutStockDo(onlineRetailE , unClockPoolDetails , cco);
            coreChannelSalesRepository.unlockStock(cco);
            //8、通知wms取消，异常单只有一个  正常单可能有多条
            for (WarehouseRecordE parentDo : cancelDoList) {
                if (WmsSyncStatusVO.SYNCHRONIZED.getStatus().equals(parentDo.getSyncWmsStatus())) {
                    executeResult = warehouseRecordRepository.updateToCanceledFromHasSync(parentDo.getId());
                    AlikAssert.isTrue(executeResult > 0, ResCode.STOCK_ERROR_1016, "取消so时,取消出库单失败,请稍后再试:soCode=" + soCode + ",doCode = " + parentDo.getRecordCode());
                    boolean syncStatus = this.notifyWmsToCancelDO(parentDo);
                    AlikAssert.isTrue(syncStatus, ResCode.STOCK_ERROR_1016, "取消so时,取消出库单失败(wms不允许):soCode=" + soCode + ",doCode = " + parentDo.getRecordCode());
                    cancelSuccessDoList.add(parentDo);
                } else if(WmsSyncStatusVO.UNSYNCHRONIZED.getStatus().equals(parentDo.getSyncWmsStatus())) {
                    executeResult = warehouseRecordRepository.updateToCanceledFromUnSync(parentDo.getId());
                    AlikAssert.isTrue(executeResult > 0, ResCode.STOCK_ERROR_1016, "取消so时,取消出库单失败,请稍后再试:soCode=" + soCode + ",doCode = " + parentDo.getRecordCode());
                } else {
                    //从回撤状态取消订单
                    executeResult = warehouseRecordRepository.updateToCanceledFromBack(parentDo.getId());
                    AlikAssert.isTrue(executeResult > 0, ResCode.STOCK_ERROR_1016, "取消so时,取消出库单失败,请稍后再试:soCode=" + soCode + ",doCode = " + parentDo.getRecordCode());
                }
            }
            //9、状态校验，验证是否有并发修改
            executeResult = onlineRetailE.updateVersion(onlineRetailE.getVersionNo());
            AlikAssert.isTrue(executeResult > 0, ResCode.STOCK_ERROR_1016,   "取消so时，该so单已被其他程序修改:soCode=" + soCode);
            //so为异常单时，rwRecordPoolEList的size有可能大于1，正常单==1
            for (RwRecordPoolE pool : rwRecordPoolEList) {
                executeResult = rwRecordPoolRepository.updateVersionNo(pool.getId(), pool.getVersionNo());
                AlikAssert.isTrue(executeResult > 0, ResCode.STOCK_ERROR_1016, "取消so时，该so单已被其他程序修改:soCode=" + soCode+ ",doCode = " + pool.getDoCode());
            }
            stockOpFactoryDO.commit();
            isSuccess = true ;
        } catch (Exception e) {
            log.error(e.getMessage(),e);
            throw e;
        } finally {
            if (!isSuccess) {
                //回滚操作
                stockOpFactoryDO.redisRollBack();
                //撤销成功的出库单重新处理
                wdtCancelDoRollbackService.cancelDoRollback(cancelSuccessDoList);
            }
        }
    }

    /**
     * 根据条件查询，导出excel
     *
     * @param wdtPageParamDTO
     * @return
     */
    @Override
    @TargetDataSource(DynamicDataSourceEnum.READ)
    public List<WDTStockSplitExportDetailTemplate> exportWDTSaleForSplitPage(WDTPageParamDTO wdtPageParamDTO) {
        //条件查询最大条数
        wdtPageParamDTO = this.handlerParam(wdtPageParamDTO, MAX_COUNT);
        if(Objects.isNull(wdtPageParamDTO)){
            return null;
        }
        Page page = PageHelper.startPage(wdtPageParamDTO.getPageIndex(), wdtPageParamDTO.getPageSize());
        List<WDTPageInfoDO> infoDOS = frWDTSaleRepository.queryWDTSaleListByCondition(wdtPageParamDTO);
        List<WDTStockSplitExportDetailTemplate> result = frWDTSaleConvertor.doListToTemplateDto(infoDOS);
        List<WDTStockSplitExportDetailTemplate> resultList=new ArrayList<>();
        //填充实仓信息
        List<Long> rwIds = RomeCollectionUtil.getValueList(result,"realWarehouseId");
        rwIds = rwIds.stream().distinct().collect(Collectors.toList());
        List<RealWarehouse> realWarehouseList = realWarehouseService.findByRealWarehouseIds(rwIds);
        Map<Long, RealWarehouse> realWarehouseMap = RomeCollectionUtil.listforMap(realWarehouseList, "id");

        //填充渠道信息
        Map<String, ChannelDTO> channelMap = new HashMap<>();
        Map<Long , List<WDTOnlineRetailRecordDetailE>> detailIdMap = new HashMap<>();
        if (result.size() > 0) {
            List<String> channelCodeList = RomeCollectionUtil.getValueList(result, "channelCode");
            channelCodeList = channelCodeList.stream().distinct().collect(Collectors.toList());
            // 远程调用接口查询渠道名称
            List<ChannelDTO> channelDTOList = channelFacade.batchQueryByChannelcodes(channelCodeList);
            AlikAssert.isNotEmpty(channelDTOList, ResCode.STOCK_ERROR_5001, ResCode.STOCK_ERROR_5001_DESC);
            channelMap = RomeCollectionUtil.listforMap(channelDTOList, "channelCode");
            List<Long> ids = RomeCollectionUtil.getValueList(result, "id");
            List<WDTOnlineRetailRecordDetailE> detailES = frWDTSaleRepository.selectFrSaleRecordDetailByIds(ids);
            detailIdMap = RomeCollectionUtil.listforListMap(detailES,"frontRecordId");
        }
        //拆单标识
        List<String> frontRecordCodes=result.stream().map(WDTStockSplitExportDetailTemplate::getRecordCode).collect(Collectors.toList());
        List<String> poolList = rwRecordPoolRepository.queryFrontRecordCodesByFrontCodes(frontRecordCodes);
        //拆单数
        List<WDTPageInfoDTO> splitCountList = rwRecordPoolRepository.querySplitCount(frontRecordCodes);
        Map<String,Integer> splitCountMap=splitCountList.stream().collect(Collectors.toMap(WDTPageInfoDTO::getRecordCode, WDTPageInfoDTO::getSplitCount));

        for (WDTStockSplitExportDetailTemplate dto : result) {
            List<String> skuCodeList = new ArrayList<>();
            //实际拆单数量
            if(splitCountMap.containsKey(dto.getRecordCode())){
                dto.setSplitCount(splitCountMap.get(dto.getRecordCode()));
            }
            BigDecimal totalQty = BigDecimal.ZERO;
            for (WDTOnlineRetailRecordDetailE detailE : detailIdMap.get(dto.getId())) {
                if (!skuCodeList.contains(detailE.getSkuCode())) {
                    skuCodeList.add(detailE.getSkuCode());
                }
                totalQty = totalQty.add(detailE.getSkuQty());
            }
            dto.setDetailSize(skuCodeList.size());
            dto.setTotalQty(totalQty);
            dto.setAddressSimply(dto.getProvince() + "/" + dto.getCity() + "/" + dto.getCounty());
            dto.setRealWarehouseCode(realWarehouseMap.get(dto.getRealWarehouseId()).getRealWarehouseCode());
            dto.setRealWarehouseName(realWarehouseMap.get(dto.getRealWarehouseId()).getRealWarehouseName());
            dto.setRealWarehouseAddress(realWarehouseMap.get(dto.getRealWarehouseId()).getRealWarehouseAddress());
            if (channelMap.containsKey(dto.getChannelCode())) {
                dto.setChannelName(channelMap.get(dto.getChannelCode()).getChannelName());
            }
            //拆单标识
            if(dto.getRecordStatus()==2){
                //拆单页面添加已拆单标识，取消的单子展示白色
                dto.setSplitColor(0);
            }else if(dto.getSplitType()!=0 && dto.getAllotStatus()==0 && !poolList.contains(dto.getRecordCode())){
                //(无)  splitType！=0 且 allotStatus=0 且 pool无记录 （前置单code查pool表）
                dto.setSplitColor(0);
            }else if(dto.getSplitType()!=0 && dto.getAllotStatus()==0 && poolList.contains(dto.getRecordCode())){
                //(浅色)splitType！=0且 allotStatus=0 且 pool有记录
                dto.setSplitColor(1);
            }else if(dto.getSplitType()==0 || dto.getAllotStatus()==1 ){
                //(深) splitType==0 || allotStatus=1
                dto.setSplitColor(2);
            }
            if(Objects.equals(dto.getRecordStatus(),18)){
                dto.setRecordStatusName("已支付");
            }else if(Objects.equals(dto.getRecordStatus(),10)){
                dto.setRecordStatusName("已出库");
            }else if(Objects.equals(dto.getRecordStatus(),2)){
                dto.setRecordStatusName("已取消");
            }else if(Objects.equals(dto.getRecordStatus(),17)){
                dto.setRecordStatusName("待支付");
            }else if(Objects.equals(dto.getRecordStatus(),12)){
                dto.setRecordStatusName("已部分出库");
            }
            if(Objects.equals(dto.getSplitType(),0)){
                dto.setSplitTypeName("普通订单");
            }else if(Objects.equals(dto.getSplitType(),1)){
                dto.setSplitTypeName("异常订单");
            }else if(Objects.equals(dto.getSplitType(),2)){
                dto.setSplitTypeName("超卖异常订单");
            }
            //填充详情信息
            WDTPageInfoDTO wdtPageInfoDTO=new WDTPageInfoDTO();
            //RecordCode  id   MerchantId
            wdtPageInfoDTO.setRecordCode(dto.getRecordCode());
            wdtPageInfoDTO.setId(dto.getId());
            wdtPageInfoDTO.setMerchantId(dto.getMerchantId());
            WDTPageInfoDTO temp=this.querySplitDetail(wdtPageInfoDTO);
            if(null !=temp && CollectionUtils.isNotEmpty(temp.getDetailDTOList())){
                for(WDTSaleDetailDTO wDTSaleDetailDTO: temp.getDetailDTOList()){
                    WDTStockSplitExportDetailTemplate end=new WDTStockSplitExportDetailTemplate();
                    BeanUtils.copyProperties(dto,end);
                    end.setSkuCode(wDTSaleDetailDTO.getSkuCode());
                    end.setSkuName(wDTSaleDetailDTO.getSkuName());
                    end.setLineNo(wDTSaleDetailDTO.getLineNo());
                    end.setDoCode(wDTSaleDetailDTO.getDoCode());
                    end.setAllotQty(wDTSaleDetailDTO.getAllotQty());
                    end.setUnit(wDTSaleDetailDTO.getUnit());
                    end.setDoRecordStatus(wDTSaleDetailDTO.getDoRecordStatus());
                    end.setFulfillmentSyncStatus(wDTSaleDetailDTO.getFulfillmentSyncStatus());
                    end.setWmsSyncStatus(wDTSaleDetailDTO.getWmsSyncStatus());
                    end.setWareHouseRecordCode(wDTSaleDetailDTO.getWareHouseRecordCode());
                    end.setMergeTime(wDTSaleDetailDTO.getMergeTime());
                    end.setSplitStatus(wDTSaleDetailDTO.getSplitStatus());
                    end.setGiftType(wDTSaleDetailDTO.getGiftType());
                    end.setWareHouseRecordStatus(wDTSaleDetailDTO.getWareHouseRecordStatus());
                    end.setIsPreSale(dto.getIsPreSale());
                    end.setSkuQty(wDTSaleDetailDTO.getSkuQty());
                    end.setParentSkuCode(wDTSaleDetailDTO.getParentSkuCode());
                    resultList.add(end);
                }
            }
        }
        return resultList;
    }

    @Override
    @TargetDataSource(DynamicDataSourceEnum.READ)
    public PageInfo queryWDTSaleForSplitPage(WDTPageParamDTO wdtPageParamDTO) {
        //条件查询最大条数
        wdtPageParamDTO = this.handlerParam(wdtPageParamDTO, MAX_COUNT);
        if(Objects.isNull(wdtPageParamDTO)){
            return new PageInfo<>();
        }
        Page page = PageHelper.startPage(wdtPageParamDTO.getPageIndex(), wdtPageParamDTO.getPageSize());
        List<WDTPageInfoDO> infoDOS = frWDTSaleRepository.queryWDTSaleListByCondition(wdtPageParamDTO);
        List<WDTPageInfoDTO> result = frWDTSaleConvertor.doListToPageDto(infoDOS);
        if (wdtPageParamDTO.getIsForMaxSplit()) {
            PageInfo<WDTPageInfoDTO> personPageInfo = new PageInfo<>(result);
            personPageInfo.setTotal(page.getTotal());
            return personPageInfo;
        }
        //填充实仓信息
        List<Long> rwIds = RomeCollectionUtil.getValueList(result,"realWarehouseId");
        rwIds = rwIds.stream().distinct().collect(Collectors.toList());
        List<RealWarehouse> realWarehouseList = realWarehouseService.findByRealWarehouseIds(rwIds);
        Map<Long, RealWarehouse> realWarehouseMap = RomeCollectionUtil.listforMap(realWarehouseList, "id");
        //填充渠道信息
        Map<String, ChannelDTO> channelMap = new HashMap<>();
        Map<Long , List<WDTOnlineRetailRecordDetailE>> detailIdMap = new HashMap<>();
        if (result.size() > 0) {
            List<String> channelCodeList = RomeCollectionUtil.getValueList(result, "channelCode");
            channelCodeList = channelCodeList.stream().distinct().collect(Collectors.toList());
            // 远程调用接口查询渠道名称
            List<ChannelDTO> channelDTOList = channelFacade.batchQueryByChannelcodes(channelCodeList);
            AlikAssert.isNotEmpty(channelDTOList, ResCode.STOCK_ERROR_5001, ResCode.STOCK_ERROR_5001_DESC);
            channelMap = RomeCollectionUtil.listforMap(channelDTOList, "channelCode");
            List<Long> ids = RomeCollectionUtil.getValueList(result, "id");
            List<WDTOnlineRetailRecordDetailE> detailES = frWDTSaleRepository.selectFrSaleRecordDetailByIds(ids);
            detailIdMap = RomeCollectionUtil.listforListMap(detailES,"frontRecordId");
        }
        //拆单标识
        List<String> frontRecordCodes=result.stream().map(WDTPageInfoDTO::getRecordCode).collect(Collectors.toList());
        List<String> poolList = rwRecordPoolRepository.queryFrontRecordCodesByFrontCodes(frontRecordCodes);
        //拆单数
        List<WDTPageInfoDTO> splitCountList = rwRecordPoolRepository.querySplitCount(frontRecordCodes);
        Map<String,Integer> splitCountMap=splitCountList.stream().collect(Collectors.toMap(WDTPageInfoDTO::getRecordCode, WDTPageInfoDTO::getSplitCount));

        for (WDTPageInfoDTO dto : result) {
            //实际拆单数量
            if(splitCountMap.containsKey(dto.getRecordCode())){
                dto.setSplitCount(splitCountMap.get(dto.getRecordCode()));
            }
            List<String> skuCodeList = new ArrayList<>();
            BigDecimal totalQty = BigDecimal.ZERO;
            for (WDTOnlineRetailRecordDetailE detailE : detailIdMap.get(dto.getId())) {
                if (!skuCodeList.contains(detailE.getSkuCode())) {
                    skuCodeList.add(detailE.getSkuCode());
                }
                totalQty = totalQty.add(detailE.getSkuQty());
            }
            dto.setDetailSize(skuCodeList.size());
            dto.setTotalQty(totalQty);
            dto.setAddressSimply(dto.getProvince() + "/" + dto.getCity() + "/" + dto.getCounty());
            dto.setRealWarehouseCode(realWarehouseMap.get(dto.getRealWarehouseId()).getRealWarehouseCode());
            dto.setRealWarehouseName(realWarehouseMap.get(dto.getRealWarehouseId()).getRealWarehouseName());
            dto.setRealWarehouseAddress(realWarehouseMap.get(dto.getRealWarehouseId()).getRealWarehouseAddress());
            if (channelMap.containsKey(dto.getChannelCode())) {
                dto.setChannelName(channelMap.get(dto.getChannelCode()).getChannelName());
            }
            //拆单标识
            if(dto.getRecordStatus()==2){
                //拆单页面添加已拆单标识，取消的单子展示白色
                dto.setSplitColor(0);
            }else if (dto.getSplitType()==3){
                dto.setSplitColor(3);
            }else if(dto.getSplitType()!=0 && dto.getAllotStatus()==0 && !poolList.contains(dto.getRecordCode())){
                //(无)  splitType！=0 且 allotStatus=0 且 pool无记录 （前置单code查pool表）
                dto.setSplitColor(0);
            }else if(dto.getSplitType()!=0 && dto.getAllotStatus()==0 && poolList.contains(dto.getRecordCode())){
                //(浅色)splitType！=0且 allotStatus=0 且 pool有记录
                dto.setSplitColor(1);
            }else if(dto.getSplitType()==0 || dto.getAllotStatus()==1 ){
                //(深) splitType==0 || allotStatus=1
                dto.setSplitColor(2);
            }
        }
        PageInfo<WDTPageInfoDTO> personPageInfo = new PageInfo<>(result);
        personPageInfo.setTotal(page.getTotal());
        return personPageInfo;
    }

    /**
     * 处理查询参数
     * @param wdtPageParamDTO
     * @param maxValue
     * @return
     */
    private WDTPageParamDTO handlerParam(WDTPageParamDTO wdtPageParamDTO,int maxValue){
        if(StringUtils.isNotBlank(wdtPageParamDTO.getChannelCode())){
            wdtPageParamDTO.setChannelCodeList(Arrays.asList(wdtPageParamDTO.getChannelCode().split(",")));
        }
        if(StringUtils.isNotBlank(wdtPageParamDTO.getProvinceCode())){
            wdtPageParamDTO.setProvinceCodes(Arrays.asList(wdtPageParamDTO.getProvinceCode().split(",")));
        }
        if (StringUtils.isNotBlank(wdtPageParamDTO.getSkuCodeStr())) {
            List<String> skuCodes = this.spiltFiled(wdtPageParamDTO.getSkuCodeStr());
            AlikAssert.isTrue(skuCodes.size() <= maxValue, ResCode.STOCK_ERROR_1002, ResCode.STOCK_ERROR_1002_DESC + ":商品编码不能超过200");
            wdtPageParamDTO.setSkuCodes(skuCodes);
        }
        if (StringUtils.isNotBlank(wdtPageParamDTO.getWarehouseCodeStr())) {
            List<String> warehouseCodeList = this.spiltFiled(wdtPageParamDTO.getWarehouseCodeStr());
            AlikAssert.isTrue(warehouseCodeList.size() <= maxValue, ResCode.STOCK_ERROR_1002, ResCode.STOCK_ERROR_1002_DESC + ":出库单号不能超过200");
            List<RwRecordPoolE> poolEList = rwRecordPoolRepository.queryKeysByWarehouseCodes(warehouseCodeList);
            if(CollUtil.isEmpty(poolEList)){
                return null;
            }
            List<Long> frontRecordIds =poolEList.stream().map(x->x.getFrontRecordId()).distinct().collect(Collectors.toList());
            wdtPageParamDTO.setFrontRecordIds(frontRecordIds);
        }
        if(StringUtils.isNotBlank(wdtPageParamDTO.getOriginOrderCode())){
            List<String> originCodes=this.spiltFiled(wdtPageParamDTO.getOriginOrderCode());
            AlikAssert.isTrue(originCodes.size() <= maxValue, ResCode.STOCK_ERROR_1002, ResCode.STOCK_ERROR_1002_DESC + "：原始单号不能超过200");
            wdtPageParamDTO.setOriginOrderCodeList(originCodes);
        }
        if(StringUtils.isNotBlank(wdtPageParamDTO.getOutRecordCode())){
            List<String> outCodes=this.spiltFiled(wdtPageParamDTO.getOutRecordCode());
            AlikAssert.isTrue(outCodes.size() <= maxValue, ResCode.STOCK_ERROR_1002, ResCode.STOCK_ERROR_1002_DESC + ":销售订单号不能超过200");
            wdtPageParamDTO.setOutRecordCodeList(outCodes);
        }
        //外部单号，原单号、前置单号，时间为空， 抛出异常
        if (CollectionUtils.isEmpty(wdtPageParamDTO.getOutRecordCodeList())
            && CollectionUtils.isEmpty(wdtPageParamDTO.getOriginOrderCodeList())
                && CollectionUtils.isEmpty(wdtPageParamDTO.getFrontRecordIds())
                && (wdtPageParamDTO.getEndTime() == null || wdtPageParamDTO.getStartTime() == null)
        ) {
            throw new RomeException("999", "必填查询条件（原始单号、销售订单号、出库单号、支付时间）不能全为空");
        }


        return wdtPageParamDTO;
    }


    /**
     *分割拼接的字段
     * @param values
     * @return
     */
    private List<String> spiltFiled(String values){
          return Arrays.asList(values.replaceAll("\\n|\\r", ",").split(","))
                  .stream().distinct().collect(Collectors.toList());
    }




    @Override
    public  WDTPageInfoDTO  querySplitDetail(WDTPageInfoDTO wdtPageInfoDTO){
        List<RwRecordPoolE> pools = rwRecordPoolRepository.queryByFrontRecordIdWithDetails(wdtPageInfoDTO.getRecordCode());
        List<RwRecordPoolDetailE> poolDetailES = new ArrayList<>();
        for(RwRecordPoolE pool : pools) {
            poolDetailES.addAll(pool.getRwRecordPoolDetails());
        }
        Map<String, RwRecordPoolE> poolMap = RomeCollectionUtil.listforMap(pools, "doCode");
        List<WDTSaleDetailDTO> dtos = frWDTSaleConvertor.entityDetailsToDtos(frWDTSaleRepository.querySaleRecordDetailsById(wdtPageInfoDTO.getId()));
        List<Long> skuIds = RomeCollectionUtil.getValueList(dtos,"skuId");
        skuIds = skuIds.stream().distinct().collect(Collectors.toList());
        List<SkuInfoExtDTO> skuInfoList = null;
        try {
            //第三方接口异常 ，只打印错误日志，不影响主流程
            skuInfoList = skuFacade.skusBySkuId(skuIds , wdtPageInfoDTO.getMerchantId());
        } catch (Exception e) {
            log.error(e.getMessage(), e);
        }
        Map<Long, SkuInfoExtDTO> skuInfoMap = RomeCollectionUtil.listforMap(skuInfoList, "id");
        Map<String , RwRecordPoolDetailE> detailEMap = RomeCollectionUtil.listforMap(poolDetailES ,"lineNo");
        wdtPageInfoDTO.setDetailDTOList(dtos);
        WarehouseRecordE warehouseRecordE ;
        Map<String, WarehouseRecordE> warehouseRecordEMap = new HashMap<>();
        for (WDTSaleDetailDTO dto : dtos) {
            if (skuInfoMap.containsKey(dto.getSkuId())) {
                dto.setSkuName(skuInfoMap.get(dto.getSkuId()).getName());
            }
            dto.setDoRecordStatus(-1);
            if (detailEMap.containsKey(dto.getLineNo())) {
                dto.setDoCode(detailEMap.get(dto.getLineNo()).getDoCode());
                dto.setAllotQty(detailEMap.get(dto.getLineNo()).getSkuQty());
                if (poolMap.containsKey(dto.getDoCode())) {
                    RealWarehouse realWarehouse = realWarehouseService.findByRealWarehouseId(poolMap.get(dto.getDoCode()).getRealWarehouseId());
                    dto.setRealWarehouseId(realWarehouse.getId());
                    dto.setRealWarehouseCode(realWarehouse.getRealWarehouseCode());
                    dto.setRealWarehouseName(realWarehouse.getRealWarehouseName());
                    dto.setDoRecordStatus(poolMap.get(dto.getDoCode()).getRecordStatus());
                    if (warehouseRecordEMap.containsKey(dto.getDoCode())) {
                        warehouseRecordE = warehouseRecordEMap.get(dto.getDoCode());
                    } else if (poolMap.get(dto.getDoCode()).getWarehouseRecordId() != null) {
                        warehouseRecordE = warehouseRecordRepository.queryWarehouseRecordById(poolMap.get(dto.getDoCode()).getWarehouseRecordId());
                        warehouseRecordEMap.put(dto.getDoCode(), warehouseRecordE);
                    } else {
                        warehouseRecordE = null;
                    }
                    if (warehouseRecordE != null) {
                        //把后置单的状态写到明细上
                        dto.setWareHouseRecordCode(warehouseRecordE.getRecordCode());
                        dto.setWareHouseRecordStatus(warehouseRecordE.getRecordStatus());
                        dto.setFulfillmentSyncStatus(warehouseRecordE.getSyncFulfillmentStatus());
                        dto.setWmsSyncStatus(warehouseRecordE.getSyncWmsStatus());
                        dto.setMergeTime(warehouseRecordE.getCreateTime());
                    }
                }
            } else {
                dto.setAllotQty(BigDecimal.ZERO);
            }
        }
        return wdtPageInfoDTO;

    }



    @Override
    @Transactional(rollbackFor = Exception.class)
    public  boolean splitOrder(WDTPageInfoDTO wdtPageInfoDTO){
        //基本的校验
        WDTOnlineRetailE retailE = frWDTSaleRepository.queryByRecordCode(wdtPageInfoDTO.getRecordCode());
        AlikAssert.isNotNull(retailE, ResCode.STOCK_ERROR_1014, ResCode.STOCK_ERROR_1014_DESC);
        AlikAssert.isFalse(FrontRecordStatusVO.DISABLED.getStatus().equals(retailE.getRecordStatus()), ResCode.STOCK_ERROR_1002, "单据已取消，无法拆单");
        AlikAssert.isFalse(retailE.getSplitType().equals(WDTRecordConst.WDT_SPLIT_NORMAL), ResCode.STOCK_ERROR_1001, "正常单无需拆单");
        AlikAssert.isFalse(retailE.getAllotStatus().equals(WDTRecordConst.WDT_ALLOT_OVER), ResCode.STOCK_ERROR_1001, "拆单结束无需再拆单");
        AlikAssert.isNotBlank(retailE.getLogisticsCode(), ResCode.STOCK_ERROR_1001, "未推送物流公司编码不允许拆单");
        //为了尽量拆单成功，不允许同时操作
//        boolean isLock = redisUtil.lock(WDTRecordConst.WDT_ONLINE_ORDER, WDTRecordConst.CLIENT_ID, WDTRecordConst.WDT_ONLINE_ORDER_TIME);
//        if (!isLock) {
//            return false;
//        }
        List<WDTOnlineRetailRecordDetailE> waitSplitDetails = frWDTSaleRepository.selectDetailByIdForSplit(wdtPageInfoDTO.getId());
        //父品skuCode为空的 用唯一的行号代替，方便后续按parentCode分组处理，不在一组的就认为不是系列品，在一组的就是组合品
        waitSplitDetails.forEach(item -> {
              //0707改动，允许组合品拆单
          //  if (item.getParentSkuCode() == null) {
                item.setParentSkuCode(item.getLineNo());
           // }
        });
        //查询虚仓库存
        Long vwId = retailE.getVirtualWarehouseId();
        //系列品都有库存才算有库存，所以系列品需要一起处理
        Map<String, List<WDTOnlineRetailRecordDetailE>> listMap = RomeCollectionUtil.listforListMap(waitSplitDetails, "parentSkuCode");
        //可以拆单的明细集合
        List<WDTOnlineRetailRecordDetailE> couldSplitList = new ArrayList<>();
        listMap.forEach((key, value) -> {
            boolean sufficient = true;
            List<CoreVirtualWarehouseStockDO> coreVirtualWarehouseStockDOs = new ArrayList<>();
            CoreVirtualWarehouseStockDO coreVirtualWarehouseStockDO;
            for (WDTOnlineRetailRecordDetailE item : value) {
                coreVirtualWarehouseStockDO = new CoreVirtualWarehouseStockDO();
                coreVirtualWarehouseStockDO.setVirtualWarehouseId(vwId);
                coreVirtualWarehouseStockDO.setSkuId(item.getSkuId());
                coreVirtualWarehouseStockDOs.add(coreVirtualWarehouseStockDO);
            }
            coreVirtualWarehouseStockDOs = coreVirtualWarehouseStockRepository.getVWStock(coreVirtualWarehouseStockDOs);
            Map<Long, CoreVirtualWarehouseStockDO> stockMap = RomeCollectionUtil.listforMap(coreVirtualWarehouseStockDOs, "skuId");

            for (WDTOnlineRetailRecordDetailE item : value) {
                CoreVirtualWarehouseStockDO vStock = stockMap.get(item.getSkuId());
                if (vStock == null || vStock.getAvailableQty().compareTo(BigDecimal.ZERO) < 0) {
                    //组合品中有一个子品库存不足就不能拆单
                    sufficient = false;
                    break;
                }
            }
            if (sufficient) {
                couldSplitList.addAll(value);
            }
        });
        //所有行库存均不足，返回拆单失败
        if (couldSplitList.size() == 0) {
//            redisUtil.unLock(WDTRecordConst.WDT_ONLINE_ORDER, WDTRecordConst.CLIENT_ID);
            throw new RomeException(ResCode.STOCK_ERROR_1001 , "所有行库存均不足");
        }
        //创建do
        retailE.setFrontRecordDetails(couldSplitList);
        RwRecordPoolE pool = retailE.createDo(false, wdtPageInfoDTO.getUserId());

        //修改明细的allotStatus状态为已分配
        List<Long> detailIds = RomeCollectionUtil.getValueList(couldSplitList, "id");
        retailE.updateToHasSplitForDetailsByDetailIds(detailIds);
        //本次将未拆单行全部生成了do，则更新单据allotStatus状态为已分配
        if (couldSplitList.size() == waitSplitDetails.size()) {
            retailE.updateToHasSplitForOrder();
        }

        //释放库存并重新锁
        CoreStockOpFactoryDO stockOpFactoryDO = new CoreStockOpFactoryDO();
        CoreChannelOrderDO cco;
        boolean isSuccess = false;
        try {
            // 释放库存
            cco = stockOpFactoryDO.createCoreChannelOrderDO();
            warpUnLockOutStockDo(retailE, pool.getRwRecordPoolDetails(), cco);
            coreChannelSalesRepository.unlockStock(cco);
            //重新锁库存
            cco = stockOpFactoryDO.createCoreChannelOrderDO();
            //包装指定虚仓和实仓锁库存的入参
            warpRouteAndLockStockDO(retailE, null, cco);
            cco.setVirtualStockOpDetailDOs(warpAssignHouseRoute(retailE));
            //只要虚仓库存足就允许锁成功
            coreChannelSalesRepository.lockStockVirtual(cco);
            //并发校验，有可能正在取消订单
            int executeResult = retailE.updateVersion(retailE.getVersionNo());
            AlikAssert.isTrue(executeResult > 0, ResCode.STOCK_ERROR_1003, "拆单失败，其他程序正在修改次订单,soCode =" + retailE.getRecordCode());
            stockOpFactoryDO.commit();
            //记录操作日志
            WDTLogDTO wdtLogDTO = new WDTLogDTO();
            wdtLogDTO.setOriginOrderCode(retailE.getOriginOrderCode());
            wdtLogDTO.setRecordCode(retailE.getRecordCode());
            wdtLogDTO.setOutRecordCode(retailE.getOutRecordCode());
            wdtLogDTO.setType(WDTRecordConst.OPERATE_SPLIT_ORDER);
            wdtLogDTO.addChangedKeyValue(null , pool.getDoCode());
            wdtLogDTO.setCreator(wdtPageInfoDTO.getUserId());
            frWDTSaleRepository.saveLog(wdtLogDTO);
            //保存订单轨迹
            try {
                orderTrackFacade.save( retailE.getOutRecordCode(), "拆单成功" ,pool.getDoCode());
            } catch (Exception e) {
                log.error(e.getMessage() ,e );
            }
            isSuccess = true;
            return true;
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            throw e;
        } finally {
            if (!isSuccess) {
                stockOpFactoryDO.redisRollBack();
            }
//            redisUtil.unLock(WDTRecordConst.WDT_ONLINE_ORDER, WDTRecordConst.CLIENT_ID);
        }

    }


    @Resource
    private WarehouseRouteRedis warehouseRouteRedis;
    @Override
    @Transactional(rollbackFor = Exception.class)
    public  boolean splitOrderFoxMax(WDTPageInfoDTO wdtPageInfoDTO){
        //基本的校验
        WDTOnlineRetailE retailE = frWDTSaleRepository.queryByRecordCode(wdtPageInfoDTO.getRecordCode());
        AlikAssert.isNotNull(retailE, ResCode.STOCK_ERROR_1014, ResCode.STOCK_ERROR_1014_DESC);
        AlikAssert.isFalse(FrontRecordStatusVO.DISABLED.getStatus().equals(retailE.getRecordStatus()), ResCode.STOCK_ERROR_1002, "单据已取消，无法拆单");
        AlikAssert.isFalse(retailE.getSplitType().equals(WDTRecordConst.WDT_SPLIT_NORMAL), ResCode.STOCK_ERROR_1001, "正常单无需拆单");
        AlikAssert.isFalse(retailE.getAllotStatus().equals(WDTRecordConst.WDT_ALLOT_OVER), ResCode.STOCK_ERROR_1001, "拆单结束无需再拆单");
        AlikAssert.isNotBlank(retailE.getLogisticsCode(), ResCode.STOCK_ERROR_1001, "未推送物流公司编码不允许拆单");

        List<WDTOnlineRetailRecordDetailE> waitSplitDetails = frWDTSaleRepository.selectDetailByIdForSplit(wdtPageInfoDTO.getId());
        Map <Long ,List<WDTOnlineRetailRecordDetailE>> couldSplitList = new HashMap<>();
        Map <Long ,Integer> virPriority = new HashMap<>();
        Map<Long , RouteWarehouseDTO> realPriority = new HashMap<>();
        //查询所有的虚仓
        List<VirtualWarehouseE>  virtualWarehouses = virtualWarehouseService.getVwListByChannelCode(wdtPageInfoDTO.getChannelCode());
        // 过滤掉停发仓
        AddressE addr = addressRepository.queryByRecordCode(retailE.getRecordCode());
        if(virtualWarehouses != null && virtualWarehouses.size() > 0) {
        	VirtualWarehouseE dto;
        	RouteWarehouseDTO routeWarehouseDTO;
        	for(int i = 0; i < virtualWarehouses.size(); i++) {
        		dto = virtualWarehouses.get(i);
        		routeWarehouseDTO = warehouseRouteRedis.getWarehouseRouteInfoByCoverFocusSale(dto.getRealWarehouseId(), addr.getProvinceCode(), addr.getCityCode(), addr.getCountyCode(), addr.getAreaCode(), null);
        		if(routeWarehouseDTO != null && routeWarehouseDTO.isRwStop()) {
        			virtualWarehouses.remove(i);
    				i--;
        		}
        	}
        	if(virtualWarehouses.size() == 0) {
        		throw new RomeException(ResCode.STOCK_ERROR_1001 , "此单发货地址所有仓库全部停发");
        	}
        }
        Map<Long ,VirtualWarehouseE > virtualWarehouseEMap = RomeCollectionUtil.listforMap(virtualWarehouses,"id");
        //查询仓库优先级
        RouteChannelSalesDTO routeChannelSalesDTO = warehouseRouteRedis.getWarehouseRouteInfoByPriorityRouteTemple(wdtPageInfoDTO.getChannelCode());
        if (routeChannelSalesDTO != null && routeChannelSalesDTO.getRouteWarehouses() != null) {
            realPriority = RomeCollectionUtil.listforMap(routeChannelSalesDTO.getRouteWarehouses(), "realWarehouseId");
        }
        //当前锁的虚仓
        Long vwId = retailE.getVirtualWarehouseId();
        Long rwId = retailE.getRealWarehouseId();

        //批量库存查询
        List<CoreVirtualWarehouseStockDO> coreVirtualWarehouseStockDOs = new ArrayList<>();
        CoreVirtualWarehouseStockDO coreVirtualWarehouseStockDO;
        for(VirtualWarehouseE ve : virtualWarehouses) {
            for (WDTOnlineRetailRecordDetailE item : waitSplitDetails) {
                coreVirtualWarehouseStockDO = new CoreVirtualWarehouseStockDO();
                coreVirtualWarehouseStockDO.setVirtualWarehouseId(ve.getId());
                coreVirtualWarehouseStockDO.setSkuId(item.getSkuId());
                coreVirtualWarehouseStockDOs.add(coreVirtualWarehouseStockDO);
            }
        }
        coreVirtualWarehouseStockDOs = coreVirtualWarehouseStockRepository.getVWStock(coreVirtualWarehouseStockDOs);
        Map<Long, List<CoreVirtualWarehouseStockDO>> stockvMap = RomeCollectionUtil.listforListMap(coreVirtualWarehouseStockDOs, "virtualWarehouseId");
       //批量查询库结束

        for(VirtualWarehouseE ve : virtualWarehouses) {
            //可以拆单的明细集合
            List<WDTOnlineRetailRecordDetailE> couldSplitTemp = new ArrayList<>();
            List<CoreVirtualWarehouseStockDO> coreVirtualWarehouseStocks = stockvMap.get(ve.getId());
            Map<Long, CoreVirtualWarehouseStockDO> stockMap = RomeCollectionUtil.listforMap(coreVirtualWarehouseStocks, "skuId");
            for (WDTOnlineRetailRecordDetailE item : waitSplitDetails) {
                CoreVirtualWarehouseStockDO vStock = stockMap.get(item.getSkuId());
                if (vStock != null && vwId.equals(ve.getId())) {
                    vStock.setAvailableQty(vStock.getAvailableQty().add(item.getSkuQty()));
                }
                if (vStock != null && vStock.getAvailableQty().compareTo(item.getSkuQty()) >= 0) {
                    couldSplitTemp.add(item);
                }
            }
            Integer defaultPriority = 1;
            if (realPriority.containsKey(ve.getRealWarehouseId())) {
                defaultPriority = realPriority.get(ve.getRealWarehouseId()).getPriority();
            }
            virPriority.put(ve.getId(), (couldSplitTemp.size() * 1000 - defaultPriority));
            couldSplitList.put(ve.getId(), couldSplitTemp);
        }
        //求优先级最高的虚仓
        Long lastVid = 0L;
        Integer tempPriority = -1000;
        for (Map.Entry<Long, Integer> map : virPriority.entrySet()) {
            if (map.getValue() > tempPriority) {
                lastVid = map.getKey();
                tempPriority = map.getValue();
            }
        }
        List<WDTOnlineRetailRecordDetailE> couldSplit = couldSplitList.get(lastVid);
        //所有行库存均不足，返回拆单失败
        if (couldSplit.size() == 0) {
            throw new RomeException(ResCode.STOCK_ERROR_1001 , "所有行库存均不足");
        }
        //方便记录操作日志
        RealWarehouse oRealWarehouse = realWarehouseService.findByRealWarehouseId(retailE.getRealWarehouseId());
        RealWarehouse nRealWarehouse = oRealWarehouse;
        String doCode =  retailE.getOrderUtilService().queryOrderCode(WarehouseRecordTypeVO.POOL_DO_RECORD.getCode());
        if (!vwId.equals(lastVid)) {
            //如果最终求出来的虚仓id不等于之前锁的仓id，则需要查询物流公司，并且修改retailE的实仓id，用于创建do
            retailE.setRealWarehouseId(virtualWarehouseEMap.get(lastVid).getRealWarehouseId());
            retailE.setVirtualWarehouseId(lastVid);
            nRealWarehouse = realWarehouseService.findByRealWarehouseId(virtualWarehouseEMap.get(lastVid).getRealWarehouseId());
            TmsLogisticInfoDTO tmsLogisticInfoDTO = tmsTools.queryLogisticByAddress(addr,doCode, retailE.getChannelCode(), nRealWarehouse.getRealWarehouseCode());
            if(null == tmsLogisticInfoDTO || StringUtils.isEmpty(tmsLogisticInfoDTO.getLogisticsCode())){
                throw new RomeException(ResCode.STOCK_ERROR_1001,"查询TMS获取仓库默认物流公司失败");
            }
            retailE.setLogisticsCode(tmsLogisticInfoDTO.getLogisticsCode());
        }
        //创建do
        retailE.setFrontRecordDetails(couldSplit);
        RwRecordPoolE pool = retailE.createDo(false, wdtPageInfoDTO.getUserId(),doCode);

        //修改明细的allotStatus状态为已分配
        List<Long> detailIds = RomeCollectionUtil.getValueList(couldSplit, "id");
        retailE.updateToHasSplitForDetailsByDetailIds(detailIds);
        //本次将未拆单行全部生成了do，则更新单据allotStatus状态为已分配
        if (couldSplit.size() == waitSplitDetails.size()) {
            retailE.updateToHasSplitForOrder();
        }

        //释放库存并重新锁
        CoreStockOpFactoryDO stockOpFactoryDO = new CoreStockOpFactoryDO();
        CoreChannelOrderDO cco;
        boolean isSuccess = false;
        try {
            // 释放库存
            cco = stockOpFactoryDO.createCoreChannelOrderDO();
            warpUnLockOutStockDoForChangeInfo(retailE, pool.getRwRecordPoolDetails(), cco, rwId, vwId);
            coreChannelSalesRepository.unlockStock(cco);
            //重新锁库存
            cco = stockOpFactoryDO.createCoreChannelOrderDO();
            //包装指定虚仓和实仓锁库存的入参
            warpRouteAndLockStockDO(retailE, null, cco);
            cco.setVirtualStockOpDetailDOs(warpAssignHouseRoute(retailE));
            //只要虚仓库存足就允许锁成功
            coreChannelSalesRepository.lockStockVirtual(cco);
            //并发校验，有可能正在取消订单
            int executeResult = retailE.updateVersion(retailE.getVersionNo());
            AlikAssert.isTrue(executeResult > 0, ResCode.STOCK_ERROR_1003, "拆单失败，其他程序正在修改次订单,soCode =" + retailE.getRecordCode());
            stockOpFactoryDO.commit();
            //记录操作日志
            WDTLogDTO wdtLogDTO = new WDTLogDTO();
            wdtLogDTO.setOriginOrderCode(retailE.getOriginOrderCode());
            wdtLogDTO.setRecordCode(retailE.getRecordCode());
            wdtLogDTO.setOutRecordCode(retailE.getOutRecordCode());
            wdtLogDTO.setType(WDTRecordConst.OPERATE_SPLIT_MAX_ORDER);
            wdtLogDTO.addChangedKeyValue(oRealWarehouse.getRealWarehouseCode() , nRealWarehouse.getRealWarehouseCode());
            wdtLogDTO.addChangedKeyValue(null , pool.getDoCode());
            wdtLogDTO.setCreator(wdtPageInfoDTO.getUserId());
            frWDTSaleRepository.saveLog(wdtLogDTO);
            //保存订单轨迹
            try {
                orderTrackFacade.save( retailE.getOutRecordCode(), "拆单成功" ,pool.getDoCode());
            } catch (Exception e) {
                log.error(e.getMessage() ,e );
            }
            isSuccess = true;
            return true;
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            throw e;
        } finally {
            if (!isSuccess) {
                stockOpFactoryDO.redisRollBack();
            }
//            redisUtil.unLock(WDTRecordConst.WDT_ONLINE_ORDER, WDTRecordConst.CLIENT_ID);
        }

    }

    private List<CoreVirtualStockOpDO> warpAssignHouseRoute(WDTOnlineRetailE onlineRetailE) {
        List<CoreVirtualStockOpDO> cvsList = new ArrayList<>();
        for (WDTOnlineRetailRecordDetailE detailE : onlineRetailE.getFrontRecordDetails()) {
            if (detailE.getBasicSkuQty().compareTo(BigDecimal.ZERO) > 0) {
                CoreVirtualStockOpDO coreStockDO = new CoreVirtualStockOpDO();
                coreStockDO.setLockQty(detailE.getBasicSkuQty());
                coreStockDO.setVirtualWarehouseId(onlineRetailE.getVirtualWarehouseId());
                coreStockDO.setRealWarehouseId(onlineRetailE.getRealWarehouseId());
                coreStockDO.setRecordCode(onlineRetailE.getRecordCode());
                coreStockDO.setTransType(onlineRetailE.getRecordType());
                coreStockDO.setChannelCode(onlineRetailE.getChannelCode());
                coreStockDO.setMerchantId(onlineRetailE.getMerchantId());
                coreStockDO.setSkuId(detailE.getSkuId());
                coreStockDO.setSkuCode(detailE.getSkuCode());
                cvsList.add(coreStockDO);
            }
        }
        return cvsList;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean recalculateHouse(WDTPageInfoDTO wdtPageInfoDTO) {
        //基本的校验
        WDTOnlineRetailE retailE = frWDTSaleRepository.queryByRecordCode(wdtPageInfoDTO.getRecordCode());
        AlikAssert.isNotNull(retailE, ResCode.STOCK_ERROR_1014, ResCode.STOCK_ERROR_1014_DESC);
        AlikAssert.isFalse(FrontRecordStatusVO.DISABLED.getStatus().equals(retailE.getRecordStatus()), ResCode.STOCK_ERROR_1002, "单据已取消，无法重新计算仓库");
        AlikAssert.isFalse(retailE.getSplitType().equals(WDTRecordConst.WDT_SPLIT_NORMAL), ResCode.STOCK_ERROR_1001, "正常单无需重新计算仓库");
        AlikAssert.isFalse(retailE.getAllotStatus().equals(WDTRecordConst.WDT_ALLOT_OVER), ResCode.STOCK_ERROR_1001, "拆单结束无法重新计算仓库");
//        //为了尽量成功，不允许同时操作
//        boolean isLock = redisUtil.lock(WDTRecordConst.WDT_ONLINE_ORDER, WDTRecordConst.CLIENT_ID, WDTRecordConst.WDT_ONLINE_ORDER_TIME);
//        if (!isLock) {
//            return false;
//        }
        List<RwRecordPoolE> pools = rwRecordPoolRepository.queryByFrontRecordCode(wdtPageInfoDTO.getRecordCode());
        //已拆过单的不允许重新计算仓库
        if (pools.size() > 0) {
//            redisUtil.unLock(WDTRecordConst.WDT_ONLINE_ORDER, WDTRecordConst.CLIENT_ID);
            throw  new RomeException(ResCode.STOCK_ERROR_1001,"已拆过单不允许重新计算仓库");
        }
        List<WDTOnlineRetailRecordDetailE> details = frWDTSaleRepository.querySaleRecordDetailsByCode(wdtPageInfoDTO.getRecordCode());
        //单位转换
        retailE.getSkuQtyUnitTools().convertRealToBasic(details, retailE.getMerchantId());
        retailE.setFrontRecordDetails(details);
        AddressE addressE = addressRepository.queryByRecordCode(retailE.getRecordCode());

        CoreStockOpFactoryDO stockOpFactoryDO = new CoreStockOpFactoryDO();
        CoreChannelOrderDO cco;
        boolean isSuccess = false;
        try {
            //先释放库存
            cco = stockOpFactoryDO.createCoreChannelOrderDO();
            RwRecordPoolE tempPoolE = entityFactory.createEntity(RwRecordPoolE.class);
            tempPoolE.setVirtualWarehouseId(retailE.getVirtualWarehouseId());
            tempPoolE.setRealWarehouseId(retailE.getRealWarehouseId());
            tempPoolE.warpRwRecordPoolDetail(details);
            warpUnLockOutStockDo(retailE, tempPoolE.getRwRecordPoolDetails(), cco);
            coreChannelSalesRepository.unlockStock(cco);

            //重新寻源
            cco = stockOpFactoryDO.createCoreChannelOrderDO();
            warpRouteAndLockStockDO(retailE, addressE, cco);
            cco.setRouteLockModeAuto(false);
            cco.setRouteLockMode(WarehouseRouteLockModeEnum.ROUTE_LOCK_MODE_VIRTUAL);
            //不指定仓库下单，需要寻源
            List<CoreVirtualStockOpDO> cvsList = StockOnlineOrderFacade.routeAndLockStock(cco);
            //旺店通寻源只会寻源到一个实仓id，否则就是看你没有配置路由模板
            AlikAssert.notEmpty(cvsList, ResCode.STOCK_ERROR_5024, "重新计算仓库寻源失败");
            Map<Long, List<CoreVirtualStockOpDO>> rwIdMap = RomeCollectionUtil.listforListMap(cvsList, "realWarehouseId", null);
            if (rwIdMap.size() > 1 || cco.getRouteLockMode() == null) {
                throw new RomeException(ResCode.STOCK_ERROR_1003, ResCode.STOCK_ERROR_1003_DESC + "请检查配置路由模板配置");
            }
            CoreVirtualStockOpDO res = cvsList.get(0);
            if (cco.getRouteLockMode().getMode()>WarehouseRouteLockModeEnum.ROUTE_LOCK_MODE_VIRTUAL.getMode()) {
                throw new RomeException(ResCode.STOCK_ERROR_1003, "重新计算仓库无仓库满足");
            }
            // 寻源仓库已停发
            if(cco.isRwStop() && wdtPageInfoDTO.getForceReCal() == false) {
            	throw new RomeException(ResCode.STOCK_ERROR_1003, "重新计算仓库已停发无仓库满足");
            }
            RealWarehouse oldRw = realWarehouseService.findByRealWarehouseId(retailE.getRealWarehouseId());

            retailE.setRealWarehouseCode(oldRw.getRealWarehouseCode());
            retailE.setVirtualWarehouseId(res.getVirtualWarehouseId());
            if (!oldRw.getId().equals(res.getRealWarehouseId())  ||  retailE.getLogisticsCode() == null) {
                retailE.setVirtualWarehouseId(res.getVirtualWarehouseId());
                retailE.setRealWarehouseId(res.getRealWarehouseId());
                //重新设置物流公司编码，根据实仓id查询tms信息
                RealWarehouse realhouse = realWarehouseService.findByRealWarehouseId(res.getRealWarehouseId());
                List<TmsLogisticInfoDTO> logisticInfoDTOList = tmsTools.queryLogisticByOutHouseCode(realhouse.getRealWarehouseCode());
                if (logisticInfoDTOList == null || logisticInfoDTOList.size() < 1) {
                    throw new RomeException(ResCode.STOCK_ERROR_1003, ResCode.STOCK_ERROR_1003_DESC + ":查询tms系统异常" + wdtPageInfoDTO.getOriginOrderCode());
                }
                retailE.setLogisticsCode(logisticInfoDTOList.get(0).getLogisticsCode());
                retailE.setRealWarehouseCode(realhouse.getRealWarehouseCode());
            }
            retailE.updateOrderForRecalHouse();
            //生成do
            RwRecordPoolE pool = retailE.createDo(true, wdtPageInfoDTO.getUserId());
            //修改订单类型为正常单,并保存新的物流公司编码以及寻源信息
            retailE.updateToHasSplitForDetailsByRecordId();

            //并发校验，有可能正在取消订单
            int executeResult = retailE.updateVersion(retailE.getVersionNo());
            AlikAssert.isTrue(executeResult > 0, ResCode.STOCK_ERROR_1003, "重新计算仓库失败，其他程序正在修改次订单,soCode =" + retailE.getRecordCode());


            //记录操作日志
            WDTLogDTO wdtLogDTO = new WDTLogDTO();
            wdtLogDTO.setOriginOrderCode(retailE.getOriginOrderCode());
            wdtLogDTO.setRecordCode(retailE.getRecordCode());
            wdtLogDTO.setOutRecordCode(retailE.getOutRecordCode());
            if(wdtPageInfoDTO.getForceReCal()) {
            	wdtLogDTO.setType(WDTRecordConst.OPERATE_FORCE_RECALCULATE_HOUSE);
            } else {
            	wdtLogDTO.setType(WDTRecordConst.OPERATE_RECALCULATE_HOUSE);
            }
            wdtLogDTO.addChangedKeyValue(oldRw.getRealWarehouseCode() + "", retailE.getRealWarehouseCode() + "");
            wdtLogDTO.addChangedKeyValue(wdtPageInfoDTO.getLogisticsCode() == null ? "未推送" : wdtPageInfoDTO.getLogisticsCode(), retailE.getLogisticsCode());
            wdtLogDTO.addChangedKeyValue(null, pool.getDoCode());
            wdtLogDTO.setCreator(wdtPageInfoDTO.getUserId());
            frWDTSaleRepository.saveLog(wdtLogDTO);
            stockOpFactoryDO.commit();
            isSuccess = true;
            //保存订单轨迹
            try {
                String ext = "原仓库和物流编码为" + wdtLogDTO.getBeforeValue().replace(",\"null\"", "") + "重新计算的新仓库、物流编码以及生成的do为" + wdtLogDTO.getAfterValue();
                orderTrackFacade.save(retailE.getOutRecordCode(), "重新计算仓库成功", ext);
            } catch (Exception e) {
                log.error(e.getMessage(), e);
            }
            return true;
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            throw e;
        } finally {
            if (!isSuccess) {
                stockOpFactoryDO.redisRollBack();
            }
//            redisUtil.unLock(WDTRecordConst.WDT_ONLINE_ORDER, WDTRecordConst.CLIENT_ID);
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void changeHouseAndLogistic(WDTPageInfoDTO wdtPageInfoDTO) {
        //为了尽量成功，不允许同时操作
//        boolean isLock = redisUtil.lock(WDTRecordConst.WDT_ONLINE_ORDER, WDTRecordConst.CLIENT_ID, WDTRecordConst.WDT_ONLINE_ORDER_TIME);
//        if (!isLock) {
//           throw  new RomeException(ResCode.STOCK_ERROR_1001,ResCode.STOCK_ERROR_1001_DESC + ":并发异常");
//        }

        List<RwRecordPoolDetailE> unClockPoolDetails = new ArrayList<>();
        WDTChangeInfoDTO wdtChangeInfo = new WDTChangeInfoDTO();
        //1.参数校验
        WDTOnlineRetailE frontRecord = null;
        RwRecordPoolE pool = null;
        try {
            frontRecord = this.validChangeHouseAndLogistic(wdtPageInfoDTO, wdtChangeInfo);
        } catch (Exception e) {
//            redisUtil.unLock(WDTRecordConst.WDT_ONLINE_ORDER, WDTRecordConst.CLIENT_ID);
            throw  e;
        }
        if(wdtChangeInfo.getDealType() == -1){
            //什么都没改 直接return
//            redisUtil.unLock(WDTRecordConst.WDT_ONLINE_ORDER, WDTRecordConst.CLIENT_ID);
            return;
        }
        if (frontRecord.getSplitType().equals(WDTRecordConst.WDT_SPLIT_NORMAL)) {
            //正常订单，正常订单只能查询到一个子do单
            List<RwRecordPoolE> pools = rwRecordPoolRepository.queryNotCanceledByFRIdWithDetails(frontRecord.getRecordCode());
            if (RwRecordPoolStatusVO.MERGED.getStatus().equals(pools.get(0).getRecordStatus())) {
//                redisUtil.unLock(WDTRecordConst.WDT_ONLINE_ORDER, WDTRecordConst.CLIENT_ID);
                throw new RomeException(ResCode.STOCK_ERROR_1001, ResCode.STOCK_ERROR_1001_DESC + ":已合单不允许此操作");
            }
            pool = pools.get(0);
            pool.setRealWarehouseId(frontRecord.getRealWarehouseId());
            pool.setVirtualWarehouseId(frontRecord.getVirtualWarehouseId());
            if(StringUtils.isNotBlank(frontRecord.getLogisticsCode())){
                pool.setLogisticsCode(frontRecord.getLogisticsCode());
            }
            pool.setUserCode(frontRecord.getUserCode());
            int i = pool.updateRwInfoAndLogisticInfo();
            if (i == 0) {
                throw new RomeException(ResCode.STOCK_ERROR_1030, ResCode.STOCK_ERROR_1030_DESC);
            }
            unClockPoolDetails.addAll(pool.getRwRecordPoolDetails());
        } else if (frontRecord.getAllotStatus().equals(WDTRecordConst.WDT_ALLOT_OVER)) {
            //拆单结束的异常订单不允许此操作
//            redisUtil.unLock(WDTRecordConst.WDT_ONLINE_ORDER, WDTRecordConst.CLIENT_ID);
            throw new RomeException(ResCode.STOCK_ERROR_1001, ResCode.STOCK_ERROR_1001_DESC + ":拆单结束的异常单不允许此操作");
        }
        //2.修改前置单默认仓库和物流信息
        int i = frontRecord.updateOrderForChangeHouse();
        if(i == 0){
//            redisUtil.unLock(WDTRecordConst.WDT_ONLINE_ORDER, WDTRecordConst.CLIENT_ID);
            throw new RomeException(ResCode.STOCK_ERROR_5031, ResCode.STOCK_ERROR_5031_DESC + ":拆单结束的订单不允许此操作");
        }

        //改仓才需要处理库存
        if (!WDTRecordConst.OPERATE_EDIT_LOGISTICS.equals(wdtChangeInfo.getDealType())) {
            //6.库存处理 查找该so是否还有未合单的明细，未合单的明细也需要释放库存
            if (frontRecord.getAllotStatus().equals(WDTRecordConst.WDT_ALLOT_INIT)) {
                List<WDTOnlineRetailRecordDetailE> notSplitDetails = frontRecord.selectDetailByIdForSplit();
                if (notSplitDetails.size() > 0) {
                    //单位转换一下，得到基本单位
                    frontRecord.getSkuQtyUnitTools().convertRealToBasic(notSplitDetails, frontRecord.getMerchantId());
                    RwRecordPoolE tempPoolE = entityFactory.createEntity(RwRecordPoolE.class);
                    tempPoolE.setVirtualWarehouseId(frontRecord.getVirtualWarehouseId());
                    tempPoolE.setRealWarehouseId(frontRecord.getRealWarehouseId());
                    tempPoolE.warpRwRecordPoolDetail(notSplitDetails);
                    unClockPoolDetails.addAll(tempPoolE.getRwRecordPoolDetails());
                }
            }

            boolean isSuccess = false;
            CoreStockOpFactoryDO stockOpFactoryDO = new CoreStockOpFactoryDO();
            CoreChannelOrderDO cco;
            int executeResult = 0;
            try {
                // 7、释放库存
                cco = stockOpFactoryDO.createCoreChannelOrderDO();
                this.warpUnLockOutStockDoForChangeInfo(frontRecord, unClockPoolDetails, cco, wdtChangeInfo.getOldRealWarehouseId(), wdtChangeInfo.getOldVirtualWarehouseId());
                coreChannelSalesRepository.unlockStock(cco);
                //8.锁定库存
                cco = stockOpFactoryDO.createCoreChannelOrderDO();
                this.warpLockStockDoForChangeInfo(unClockPoolDetails, frontRecord, cco);
                coreChannelSalesRepository.allowAvailNegativeLockStockAutoAdd(cco);
                //9.并发校验，有可能正在取消订单
                executeResult = frontRecord.updateVersion(frontRecord.getVersionNo());
                AlikAssert.isTrue(executeResult > 0, ResCode.STOCK_ERROR_1003, "更新仓库或者物流失败，其他程序正在修改此订单");
                if (pool != null) {
                    executeResult = rwRecordPoolRepository.updateVersionNo(pool.getId(), pool.getVersionNo());
                    AlikAssert.isTrue(executeResult > 0, ResCode.STOCK_ERROR_1016, "更新仓库或者物流失败，其他程序正在修改此订单");
                }
                stockOpFactoryDO.commit();
                isSuccess = true;
            } catch (Exception e) {
                log.error(e.getMessage(), e);
                throw e;
            } finally {
                if (!isSuccess) {
                    //回滚操作
                    stockOpFactoryDO.redisRollBack();
                }
//                redisUtil.unLock(WDTRecordConst.WDT_ONLINE_ORDER, WDTRecordConst.CLIENT_ID);
            }
        } else {
//            redisUtil.unLock(WDTRecordConst.WDT_ONLINE_ORDER, WDTRecordConst.CLIENT_ID);
        }
        //10.写入操作日志(不影响主流程)
        this.saveChangeInfoLog(frontRecord , wdtChangeInfo);
    }

    /**
     * 参数校验并返回前置单信息
     * @param wdtPageInfoDTO
     */
    private WDTOnlineRetailE validChangeHouseAndLogistic(WDTPageInfoDTO wdtPageInfoDTO, WDTChangeInfoDTO wdtChangeInfo) {
        Integer dealType = -1;
        Long realWarehouseId = wdtPageInfoDTO.getRealWarehouseId();
        String recordCode = wdtPageInfoDTO.getRecordCode();
        String logisticsCode = wdtPageInfoDTO.getLogisticsCode();
        AlikAssert.isNotNull(realWarehouseId, ResCode.STOCK_ERROR_1002, ResCode.STOCK_ERROR_1002_DESC);
        AlikAssert.isNotBlank(recordCode, ResCode.STOCK_ERROR_1002, ResCode.STOCK_ERROR_1002_DESC);
        //2.先根据单据取出前置单信息
        WDTOnlineRetailE frontRecord = frWDTSaleRepository.queryByRecordCode(wdtPageInfoDTO.getRecordCode());
        //基本的校验
        AlikAssert.isNotNull(frontRecord, ResCode.STOCK_ERROR_1014, ResCode.STOCK_ERROR_1014_DESC);
        AlikAssert.isFalse(FrontRecordStatusVO.DISABLED.getStatus().equals(frontRecord.getRecordStatus()), ResCode.STOCK_ERROR_1002, "单据已取消，无法修改仓库或物流");
        AlikAssert.isNotBlank(frontRecord.getLogisticsCode(), ResCode.STOCK_ERROR_1001, "未推送物流公司编码不允许修改仓库或物流");
        //3.查询修改的仓库是否存在
        wdtChangeInfo.setOldLogisticsCode(frontRecord.getLogisticsCode());
        RealWarehouse oldRw = realWarehouseService.findByRealWarehouseId(frontRecord.getRealWarehouseId());
        RealWarehouse changeRw = realWarehouseService.findByRealWarehouseId(wdtPageInfoDTO.getRealWarehouseId());
        AlikAssert.isNotNull(changeRw, ResCode.STOCK_ERROR_4002, ResCode.STOCK_ERROR_4002_DESC);
        //4.判断当前仓库是否属于该渠道
        Long vmId = virtualWarehouseService.getVwIdByRwAndChannel(realWarehouseId, frontRecord.getChannelCode());
        AlikAssert.isNotNull(vmId, ResCode.STOCK_ERROR_2018, ResCode.STOCK_ERROR_2018_DESC);
        //5.判断配送公司和仓库是否匹配
        if(StringUtils.isNotBlank(logisticsCode)) {
            List<TmsLogisticInfoDTO> logisticInfoDTOList = tmsTools.queryLogisticByOutHouseCode(changeRw.getRealWarehouseCode());
            if (CollectionUtils.isEmpty(logisticInfoDTOList)) {
                throw new RomeException(ResCode.STOCK_ERROR_1003, ResCode.STOCK_ERROR_1003_DESC + ":查询tms系统异常" + wdtPageInfoDTO.getOriginOrderCode());
            }
            List<String> logisticsCodeList = RomeCollectionUtil.getValueList(logisticInfoDTOList, "logisticsCode");
            AlikAssert.isTrue(logisticsCodeList.contains(wdtPageInfoDTO.getLogisticsCode()),
                    ResCode.STOCK_ERROR_5021, ResCode.STOCK_ERROR_5021_DESC);
            frontRecord.setLogisticsCode(logisticsCode);
        }
        //判断是否改仓了
        if(!changeRw.getId().equals(frontRecord.getRealWarehouseId()) || !vmId.equals(frontRecord.getVirtualWarehouseId())){
            if(StringUtils.isBlank(logisticsCode)){
                //只改仓库
                dealType = WDTRecordConst.OPERATE_EDIT_HOUSE;
            }else{
                //都进行了更改
                dealType = WDTRecordConst.OPERATE_EDIT_HOUSE_AND_LOGISTICS;
            }
        }else{
            if(StringUtils.isNotBlank(logisticsCode)){
                //只改物流
                dealType = WDTRecordConst.OPERATE_EDIT_LOGISTICS;
            }else{
                //都没改
                dealType = -1;
            }
        }
        wdtChangeInfo.setOldRealWarehouseId(frontRecord.getRealWarehouseId());
        wdtChangeInfo.setOldVirtualWarehouseId(frontRecord.getVirtualWarehouseId());
        wdtChangeInfo.setOldRwCode(oldRw.getRealWarehouseCode());
        frontRecord.setRealWarehouseId(changeRw.getId());
        frontRecord.setVirtualWarehouseId(vmId);
        wdtChangeInfo.setDealType(dealType);
        wdtChangeInfo.setNewLogisticsCode(logisticsCode);
        wdtChangeInfo.setNewRwCode(changeRw.getRealWarehouseCode());
        wdtChangeInfo.setUserId(wdtPageInfoDTO.getUserId());
        return frontRecord;
    }

    /**
     * 封装库存锁定信息
     * @param poolDetails
     * @param frontRecord
     */
    private void warpLockStockDoForChangeInfo(List<RwRecordPoolDetailE> poolDetails, WDTOnlineRetailE frontRecord, CoreChannelOrderDO coreChannelOrderDO) {
        //库存交易类型
        coreChannelOrderDO.setRecordCode(frontRecord.getRecordCode());
        coreChannelOrderDO.setTransType(frontRecord.getRecordType());
        coreChannelOrderDO.setMerchantId(frontRecord.getMerchantId());
        coreChannelOrderDO.setChannelCode(frontRecord.getChannelCode());
        List<CoreOrderDetailDO> details = new ArrayList<>();
        coreChannelOrderDO.setOrderDetailDOs(details);
        CoreOrderDetailDO detailDO;
        List<CoreVirtualStockOpDO> cvsList = new ArrayList<>();
        for (RwRecordPoolDetailE detailE : poolDetails) {
            if (detailE.getBasicSkuQty().compareTo(BigDecimal.ZERO) > 0) {
                detailDO = new CoreOrderDetailDO();
                detailDO.setLockQty(detailE.getBasicSkuQty());
                detailDO.setSkuId(detailE.getSkuId());
                detailDO.setSkuCode(detailE.getSkuCode());
                details.add(detailDO);
                CoreVirtualStockOpDO coreStockDO = new CoreVirtualStockOpDO();
                coreStockDO.setLockQty(detailE.getBasicSkuQty());
                coreStockDO.setVirtualWarehouseId(frontRecord.getVirtualWarehouseId());
                coreStockDO.setRealWarehouseId(frontRecord.getRealWarehouseId());
                coreStockDO.setRecordCode(frontRecord.getRecordCode());
                coreStockDO.setTransType(frontRecord.getRecordType());
                coreStockDO.setChannelCode(frontRecord.getChannelCode());
                coreStockDO.setMerchantId(frontRecord.getMerchantId());
                coreStockDO.setSkuId(detailE.getSkuId());
                coreStockDO.setSkuCode(detailE.getSkuCode());
                cvsList.add(coreStockDO);
            }
        }
        coreChannelOrderDO.setVirtualStockOpDetailDOs(cvsList);
    }

    /**
     * 记录修改仓库和物流的操作日志
     * @param frontRecord
     */
    private void saveChangeInfoLog(WDTOnlineRetailE frontRecord, WDTChangeInfoDTO wdtChangeInfo){
        //记录操作日志
        WDTLogDTO wdtLogDTO = new WDTLogDTO();
        wdtLogDTO.setOriginOrderCode(frontRecord.getOriginOrderCode());
        wdtLogDTO.setRecordCode(frontRecord.getRecordCode());
        wdtLogDTO.setOutRecordCode(frontRecord.getOutRecordCode());
        wdtLogDTO.setType(wdtChangeInfo.getDealType());
        String ext = "";
        String eventName = "";

        if(WDTRecordConst.OPERATE_EDIT_HOUSE.equals(wdtChangeInfo.getDealType())){
            wdtLogDTO.addChangedKeyValue(wdtChangeInfo.getOldRwCode() , wdtChangeInfo.getNewRwCode() );
            ext =  "原仓" + wdtLogDTO.getBeforeValue() + "修改为新仓" + wdtLogDTO.getAfterValue();
            eventName = "修改仓库" ;
        }
        else if(WDTRecordConst.OPERATE_EDIT_LOGISTICS.equals(wdtChangeInfo.getDealType())){
            wdtLogDTO.addChangedKeyValue(wdtChangeInfo.getOldLogisticsCode(), wdtChangeInfo.getNewLogisticsCode());
            ext = "原物流编码为" + wdtLogDTO.getBeforeValue() + "修改为新物流编码为" + wdtLogDTO.getAfterValue();
            eventName = "修改物流" ;
        }
        else if(WDTRecordConst.OPERATE_EDIT_HOUSE_AND_LOGISTICS.equals(wdtChangeInfo.getDealType())){
            wdtLogDTO.addChangedKeyValue(wdtChangeInfo.getOldRwCode() , wdtChangeInfo.getNewRwCode() );
            wdtLogDTO.addChangedKeyValue(wdtChangeInfo.getOldLogisticsCode(), wdtChangeInfo.getNewLogisticsCode());
            ext = "原仓库和物流编码为" + wdtLogDTO.getBeforeValue() + "修改为新仓库和物流编码为" + wdtLogDTO.getAfterValue();
            eventName = "修改仓库和物流" ;
        }
        wdtLogDTO.setCreator(wdtChangeInfo.getUserId());
        frWDTSaleRepository.saveLog(wdtLogDTO);
        //保存订单轨迹
        try {
            orderTrackFacade.save(frontRecord.getOutRecordCode(), eventName, ext);
        } catch (Exception e) {
            log.error(e.getMessage(), e);

        }
    }

    @Override
    public List<TmsLogisticInfoDTO> getLogisticListByRwId(Long realWarehouseId) {
        RealWarehouse realWarehouse  = realWarehouseService.findByRealWarehouseId(realWarehouseId);
        AlikAssert.isNotNull(realWarehouse, ResCode.STOCK_ERROR_4002, ResCode.STOCK_ERROR_4002_DESC);
        return tmsTools.queryLogisticByOutHouseCode(realWarehouse.getRealWarehouseCode());
    }


    @Override
    public PageInfo queryOperateLog(WDTLogPageParamDTO logPageParamDTO){
        Page page = PageHelper.startPage(logPageParamDTO.getPageIndex(), logPageParamDTO.getPageSize());
        List<WDTLogDTO> res =  frWDTSaleRepository.queryOperateLog(logPageParamDTO);
        PageInfo<WDTLogPageDTO> pageInfo = new PageInfo<>(frWDTSaleConvertor.dtolistToPageDtoList(res));
        pageInfo.setTotal(page.getTotal());
        return pageInfo;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateSoOrderInfo(UpdateSoOrderDTO paramDTO){
        log.info("=====更新SO单：入参={}", JSON.toJSONString(paramDTO));
        //查询前置SO单数据
        WDTOnlineRetailE onlineRetailE = frWDTSaleRepository.queryByOutRecordCode(paramDTO.getSoCode());
        AlikAssert.isNotNull(onlineRetailE, ResCode.STOCK_ERROR_1014, ResCode.STOCK_ERROR_1014_DESC);
        if (FrontRecordStatusVO.DISABLED.getStatus().equals(onlineRetailE.getRecordStatus())) {
            throw new RomeException(ResCode.STOCK_ERROR_5018, ResCode.STOCK_ERROR_5018_DESC + ":soCode=" + paramDTO.getSoCode());
        }
        if (WDTRecordConst.WDT_SPLIT_NORMAL.equals(onlineRetailE.getSplitType()) && StringUtils.isNotBlank(onlineRetailE.getLogisticsCode())) {
            throw new RomeException(ResCode.STOCK_ERROR_1001, ResCode.STOCK_ERROR_1001_DESC + "，正常订单不允许该操作:soCode=" + paramDTO.getSoCode());
        }
        List<WDTOnlineRetailRecordDetailE> details = frWDTSaleRepository.querySaleRecordDetailsById(onlineRetailE.getId());
        Map<String , WDTOnlineRetailRecordDetailE> lineNoMap = RomeCollectionUtil.listforMap(details,"lineNo");
        List<Long> ids = new ArrayList<>();
        List<WDTOnlineRetailRecordDetailE> tempList = new ArrayList<>();
        for (UpdateOrderDetailDTO dto : paramDTO.getFrontRecordDetails()) {
            if (!lineNoMap.containsKey(dto.getLineNo())) {
                //取消的行不存在
                throw new RomeException(ResCode.STOCK_ERROR_1002, ResCode.STOCK_ERROR_1002_DESC + ":取消的行不存在." + dto.getLineNo());
            }
            WDTOnlineRetailRecordDetailE temp = lineNoMap.get(dto.getLineNo());
            if (temp.getSplitStatus().equals(WDTRecordConst.WDT_ALLOT_OVER)) {
                //取消的行已经拆单了
                throw new RomeException(ResCode.STOCK_ERROR_1002, ResCode.STOCK_ERROR_1002_DESC + ":取消的行已经拆单了." + dto.getLineNo());
            }
            if (temp.getSkuQty().compareTo(BigDecimal.ZERO) == 0) {
                //取消的行已经取消了，重复取消
                continue;
            }
            ids.add(temp.getId());
            tempList.add(temp);
        }
        if (ids.size() > 0) {
            int executeResult = frWDTSaleRepository.updateDetailToCancel(ids);
            AlikAssert.isTrue(executeResult == ids.size(), ResCode.STOCK_ERROR_1002, "取消so明细失败:soCode=" + paramDTO.getSoCode());
            AlikAssert.isTrue(validHasEffectDetail(onlineRetailE.getId()) , ResCode.STOCK_ERROR_1003, "请调用取消接口而非修改接口:" + paramDTO.getSoCode());
            //如果取消未拆单的行后，改订单就再没有待拆行，则将订单改为已拆单结束，无需拆单
            if (onlineRetailE.selectDetailByIdForSplit().size() == 0) {
                onlineRetailE.updateToHasSplitForOrder();
            }
            executeResult = onlineRetailE.updateVersion(onlineRetailE.getVersionNo());
            AlikAssert.isTrue(executeResult > 0, ResCode.STOCK_ERROR_1002, "更新so时，该so单已被其他程序修改:soCode=" + paramDTO.getSoCode());

            //单位转换一下，得到基本单位
            onlineRetailE.getSkuQtyUnitTools().convertRealToBasic(tempList, onlineRetailE.getMerchantId());
            RwRecordPoolE tempPoolE = entityFactory.createEntity(RwRecordPoolE.class);
            tempPoolE.setRealWarehouseId(onlineRetailE.getRealWarehouseId());
            tempPoolE.setVirtualWarehouseId(onlineRetailE.getVirtualWarehouseId());
            tempPoolE.warpRwRecordPoolDetail(tempList);
            CoreStockOpFactoryDO stockOpFactoryDO = new CoreStockOpFactoryDO();
            CoreChannelOrderDO cco;
            boolean isSuccess = false;
            try {
                // 释放库存
                cco = stockOpFactoryDO.createCoreChannelOrderDO();
                warpUnLockOutStockDo(onlineRetailE, tempPoolE.getRwRecordPoolDetails(), cco);
                coreChannelSalesRepository.unlockStock(cco);
                stockOpFactoryDO.commit();
                isSuccess = true;
            } catch (Exception e) {
                log.error(e.getMessage(), e);
                throw e;
            } finally {
                if (!isSuccess) {
                    //回滚操作
                    stockOpFactoryDO.redisRollBack();
                }
            }
        }

    }

    private boolean validHasEffectDetail(Long id){
        List<WDTOnlineRetailRecordDetailE> details = frWDTSaleRepository.querySaleRecordDetailsById(id);
        for (WDTOnlineRetailRecordDetailE detailE : details) {
            if (detailE.getSkuQty().compareTo(BigDecimal.ZERO) > 0) {
                return true;
            }
        }
        return false;
    }

    @Override
    public List<WDTPageInfoDTO> queryNeedReCalRwList(Long minId, Integer limit) {
        return frWDTSaleRepository.queryNeedReCalRwList(minId, limit);
    }
}
