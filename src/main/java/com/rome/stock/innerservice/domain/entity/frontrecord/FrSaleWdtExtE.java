package com.rome.stock.innerservice.domain.entity.frontrecord;

import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Component;

import com.rome.stock.innerservice.infrastructure.dataobject.frontrecord.FrSaleWdtExtDO;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.extern.slf4j.Slf4j;

/**
 * 类FrSaleWdtExtE的实现描述：旺店通销售前置单附加信息表
 *
 * <AUTHOR> 2022/5/24 10:55
 */
@Component
@Scope("prototype")
@Data
@Slf4j
@EqualsAndHashCode(callSuper = false)
public class FrSaleWdtExtE extends FrSaleWdtExtDO {

}
