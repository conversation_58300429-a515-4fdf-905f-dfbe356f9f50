package com.rome.stock.innerservice.domain.service.impl;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

import javax.annotation.Resource;

import com.rome.stock.innerservice.api.dto.frontrecord.PurchaseOrderOldDTO;
import com.rome.stock.innerservice.constant.*;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.rome.arch.core.domain.EntityFactory;
import com.rome.arch.core.exception.RomeException;
import com.rome.stock.common.constants.warehouse.WarehouseRecordConstant;
import com.rome.stock.common.enums.warehouse.WarehouseRecordTypeVO;
import com.rome.stock.innerservice.api.dto.RealWarehouse;
import com.rome.stock.innerservice.api.dto.warehouserecord.InWarehouseRecordDTO;
import com.rome.stock.innerservice.api.dto.warehouserecord.OutWarehouseRecordDTO;
import com.rome.stock.innerservice.common.AlikAssert;
import com.rome.stock.innerservice.common.ResCode;
import com.rome.stock.innerservice.common.RomeCollectionUtil;
import com.rome.stock.innerservice.common.VmAllocation.AllocationCalQtyParam;
import com.rome.stock.innerservice.common.VmAllocation.AllocationCalQtyRes;
import com.rome.stock.innerservice.common.VmAllocation.AllocationTool;
import com.rome.stock.innerservice.common.VmAllocation.VwAllocationQty;
import com.rome.stock.innerservice.domain.convertor.frontrecord.FrPurchaseOrderConvertor;
import com.rome.stock.innerservice.domain.convertor.frontrecord.ShopReplenishRecordConvertor;
import com.rome.stock.innerservice.domain.convertor.warehouserecord.ColdChainWarehouseConvertor;
import com.rome.stock.innerservice.domain.entity.frontrecord.PurchaseOrderE;
import com.rome.stock.innerservice.domain.entity.warehouserecord.PurchaseWarehouseRecordE;
import com.rome.stock.innerservice.domain.entity.warehouserecord.ShopReplenishWarehouseRecordE;
import com.rome.stock.innerservice.domain.entity.warehouserecord.WarehouseRecordDetail;
import com.rome.stock.innerservice.domain.entity.warehouserecord.WarehouseRecordE;
import com.rome.stock.core.domain.repository.CoreRealWarehouseStockRepository;
import com.rome.stock.innerservice.domain.repository.frontrecord.FrPurchaseOrderRepository;
import com.rome.stock.innerservice.domain.repository.warehouserecord.WarehouseRecordRepository;
import com.rome.stock.innerservice.domain.service.ColdChainOrderService;
import com.rome.stock.innerservice.domain.service.RealWarehouseService;
import com.rome.stock.innerservice.domain.service.RecordRealVirtualStockSyncRelationService;
import com.rome.stock.core.facade.RedisRollBackFacade;
import com.rome.stock.innerservice.facade.VmSkuPermitFacade;
import com.rome.stock.innerservice.infrastructure.dataobject.FrontWarehouseRecordRelationDO;
import com.rome.stock.core.infrastructure.dataobject.core.CoreRealStockOpDO;
import com.rome.stock.core.infrastructure.dataobject.core.CoreRealStockOpDetailDO;
import com.rome.stock.core.infrastructure.dataobject.core.CoreVirtualStockOpDO;

import lombok.extern.slf4j.Slf4j;

/**
 * 类ColdChainOrderServiceImpl的实现描述：冷链订单
 *
 * <AUTHOR> 2019/5/16 15:15
 */
@Slf4j
@Service
public class ColdChainOrderServiceImpl implements ColdChainOrderService {

    @Autowired
    private RealWarehouseService realWarehouseService;
    @Autowired
    private FrPurchaseOrderConvertor frPurchaseOrderConvertor;
    @Autowired
    private FrPurchaseOrderRepository frPurchaseOrderRepository;
    @Autowired
    private EntityFactory entityFactory;
    @Autowired
    private WarehouseRecordRepository warehouseRecordRepository;
    @Autowired
    private RecordRealVirtualStockSyncRelationService recordRealVirtualStockSyncRelationService;
    @Autowired
    private CoreRealWarehouseStockRepository coreRealWarehouseStockRepository;
    @Resource
    private ShopReplenishRecordConvertor shopReplenishConvertor;
    @Resource
    private AllocationTool allocationTool;
    @Resource
    private ColdChainWarehouseConvertor coldChainWarehouseConvertor;


    @Override
    @Transactional(rollbackFor = Exception.class)
    public void addColdChainPurchaseRecord(PurchaseOrderOldDTO purchaseOrderDTO) {

        //查询门店对应的工厂的冷链仓库
        RealWarehouse realWarehouse = realWarehouseService.findByRealWarehouseOutCodeAndFactoryCode(purchaseOrderDTO.getRealWarehouseCode(), purchaseOrderDTO.getFactoryCode());
        AlikAssert.isNotNull(realWarehouse, "999", "当前仓库不存在");
        //仓库必须是冷链仓库
        if(!RealWarehouseTypeVO.RW_TYPE_6.getType().equals(realWarehouse.getRealWarehouseType())){
           throw new RomeException(ResCode.STOCK_ERROR_1061, ResCode.STOCK_ERROR_1061_DESC);
        }
        PurchaseOrderE purchaseOrderE = frPurchaseOrderConvertor.purchaseDtoToEntity(purchaseOrderDTO);
        purchaseOrderE.setRealWarehouseId(realWarehouse.getId());
        purchaseOrderE.setRecordStatus(FrontRecordStatusVO.INIT.getStatus());
        //创建采购通知单
        purchaseOrderE.addFrontRecordOld();
        PurchaseWarehouseRecordE warehouseRecord = entityFactory.createEntity(PurchaseWarehouseRecordE.class);

        //根据前置单生成入库单数据
        warehouseRecord.createInRecordByFrontRecordOld(purchaseOrderE);
        warehouseRecord.setRecordStatus(WarehouseRecordStatusVO.INIT.getStatus());
        //创建入库单
        warehouseRecord.addWarehouseRecord();
    }


    @Override
    @Transactional(rollbackFor = Exception.class)
    public void addColdChainOutRecord(OutWarehouseRecordDTO dto) {
        //查询门店对应的冷链仓库
        RealWarehouse outWarehouse = realWarehouseService.findByRealWarehouseOutCodeAndFactoryCode(dto.getWarehouseCode(), dto.getFactoryCode());
        AlikAssert.isNotNull(outWarehouse, ResCode.STOCK_ERROR_1049, ResCode.STOCK_ERROR_1049_DESC);
        //5.生成冷链越库出库单
        ShopReplenishWarehouseRecordE outRecordE = entityFactory.createEntity(ShopReplenishWarehouseRecordE.class);
        outRecordE.setSyncDispatchStatus(WarehouseRecordConstant.NEED_DISPATCH);
        outRecordE.setRecordCode(dto.getRecordCode());
        outRecordE.setSapOrderCode(dto.getSapOrderCode());
        outRecordE.addCodeChainOutOrder(dto, outWarehouse.getId());
        outRecordE.setAppId(dto.getAppId());
        outRecordE.setSyncTradeStatus(WarehouseRecordConstant.NEED_SYNC_TRADE);
        outRecordE.addWarehouseRecord();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean warehouseOutNotify(Long wrId) {
        //查询出库单
        WarehouseRecordE outRecordE = warehouseRecordRepository.getRecordWithDetailById(wrId);
        AlikAssert.isNotNull(outRecordE, ResCode.STOCK_ERROR_1017, ResCode.STOCK_ERROR_1017_DESC);


        int i = warehouseRecordRepository.updateToWaitTransfer(wrId);
        AlikAssert.isTrue(i > 0, ResCode.STOCK_ERROR_1066, ResCode.STOCK_ERROR_1066_DESC);

        boolean isSuccess = false;
        CoreRealStockOpDO coreRealStockOpDO = null;
        try {
            //出仓库存减少
            coreRealStockOpDO = this.initOutWarehouseStockObj(outRecordE, outRecordE.getWarehouseRecordDetails());
            coreRealWarehouseStockRepository.decreaseRealQty(coreRealStockOpDO);
            isSuccess = true;
        }catch (RomeException e){
            throw new RomeException(e.getCode(),e.getMessage());
        }catch (Exception e){
            throw new RomeException(ResCode.STOCK_ERROR_1003,ResCode.STOCK_ERROR_1003_DESC);
        }finally {
            if(!isSuccess) {
                RedisRollBackFacade.redisRollBack(coreRealStockOpDO);
            }
        }
        return true;
    }

    @Override
    public RealWarehouse addColdChainShopInRecord(InWarehouseRecordDTO dto) {
        //1.根据门店编码查询门店仓实仓信息
        RealWarehouse inRealWarehouse = realWarehouseService.findByRealWarehouseOutCodeAndFactoryCode(dto.getWarehouseCode(), dto.getFactoryCode());
        AlikAssert.isNotNull(inRealWarehouse, ResCode.STOCK_ERROR_1022, ResCode.STOCK_ERROR_1022_DESC);
        //2.直接创建门店入库单
        ShopReplenishWarehouseRecordE inRecordE = entityFactory.createEntity(ShopReplenishWarehouseRecordE.class);
        inRecordE.setRecordType(WarehouseRecordTypeVO.SHOP_COLD_CHAIN_IN_RECORD.getType());
        inRecordE.setRecordCode(dto.getRecordCode());
        inRecordE.setCmpStatus(WarehouseRecordConstant.NEED_CMP);
        //表示待推送cmp7的标识
        inRecordE.setSyncDispatchStatus(WarehouseRecordConstant.NEED_CMP7);
        inRecordE.setSapOrderCode(dto.getSapOrderCode());
        inRecordE.createInRecord(dto, inRealWarehouse.getId());
        inRecordE.setSyncDispatchStatus(WarehouseRecordConstant.INIT_DISPATCH);
        inRecordE.setSyncWmsStatus(WarehouseRecordConstant.NEED_SYNC_WMS);
        inRecordE.setSyncTradeStatus(WarehouseRecordConstant.NEED_SYNC_TRADE);
        //3 保存入库单据及明细
        inRecordE.addWarehouseRecord();
        //6.增加在途库存
        CoreRealStockOpDO coreRealStockOpDO = null;
        boolean isSuccess = false;
        try {
            coreRealStockOpDO = inRecordE.initOnRoadStockObj(inRealWarehouse.getId());
            coreRealWarehouseStockRepository.increaseOnroadStock(coreRealStockOpDO);
            isSuccess = true;
        } catch (RomeException e) {
            log.error(e.getMessage(), e);
            throw new RomeException(e.getCode(), e.getMessage());
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            throw new RomeException(ResCode.STOCK_ERROR_1003, ResCode.STOCK_ERROR_1003_DESC);
        } finally {
            if (!isSuccess) {
                RedisRollBackFacade.redisRollBack(coreRealStockOpDO);
            }
        }
        return inRealWarehouse;
    }

    /**
     * 无需质检，直接增加可用库存的DO，需关心单据级别的sku分配关系
     * @param details
     * @param rwId
     * @param recordCode
     * @param recordType
     * @param resultMap
     * @return
     */
    private List<CoreRealStockOpDO> initStockObjForPre(List<WarehouseRecordDetail> details, Long rwId, String recordCode,
                                                       Integer recordType, Map<String, AllocationCalQtyRes> resultMap ){
        List<CoreRealStockOpDO> result = new ArrayList<>();
        List<CoreRealStockOpDetailDO> increaseDetails = new ArrayList<>();
        List<CoreRealStockOpDetailDO> defaultIncreaseDetails = new ArrayList<>();


        List<CoreVirtualStockOpDO> virtualStockByCalculateDOs = new ArrayList<>();

        for (WarehouseRecordDetail detail: details) {
            //数量为0的不处理库存的增加和减少
            if(BigDecimal.ZERO.compareTo(detail.getActualQty()) == 0){
                continue;
            }

            if (resultMap.containsKey(detail.getLineNo())) {
                AllocationCalQtyRes calQtyRes = resultMap.get(detail.getLineNo());
                List<VwAllocationQty> list = calQtyRes.getVwAllocationQtyList();
                CoreRealStockOpDetailDO coreRealStockOpDetailDO = new CoreRealStockOpDetailDO();
                coreRealStockOpDetailDO.setSkuId(detail.getSkuId());
                coreRealStockOpDetailDO.setSkuCode(detail.getSkuCode());
                coreRealStockOpDetailDO.setRealQty(detail.getActualQty());
                coreRealStockOpDetailDO.setRealWarehouseId(rwId);

                if (CollectionUtils.isNotEmpty(list)) {
                    //该sku有配置比例或绝对数，均转换成绝对数即可，因为数值已经计算好了

                    for (VwAllocationQty vwAllocationQty : list) {
                        CoreVirtualStockOpDO coreVirtualStockOpDO = new CoreVirtualStockOpDO();
                        coreVirtualStockOpDO.setRealQty(vwAllocationQty.getSkuQty());
                        this.setCoreVirtualStock(rwId, recordCode, recordType, virtualStockByCalculateDOs,
                                detail, vwAllocationQty.getVirtualWarehouseId(), coreVirtualStockOpDO);
                    }
                    increaseDetails.add(coreRealStockOpDetailDO);
                } else {
                    //该sku没有配置比例或绝对数，走默认的
                    defaultIncreaseDetails.add(coreRealStockOpDetailDO);
                }
            } else {
                throw new RomeException(ResCode.STOCK_ERROR_1002, ResCode.STOCK_ERROR_1002_DESC );
            }

        }
        if (defaultIncreaseDetails.size() > 0) {
            CoreRealStockOpDO coreRealStockOpDO = new CoreRealStockOpDO();
            coreRealStockOpDO.setRecordCode(recordCode);
            coreRealStockOpDO.setTransType(recordType);
            coreRealStockOpDO.setDetailDos(defaultIncreaseDetails);
            result.add(coreRealStockOpDO);
        }
        if (increaseDetails.size() > 0) {
            CoreRealStockOpDO coreRealStockOpDO = new CoreRealStockOpDO();
            coreRealStockOpDO.setRecordCode(recordCode);
            coreRealStockOpDO.setTransType(recordType);
            coreRealStockOpDO.setDetailDos(increaseDetails);
            coreRealStockOpDO.setVirtualStockByCalculateDOs(virtualStockByCalculateDOs);
            coreRealStockOpDO.setCalculateVirtualStockFlag(false);
            result.add(coreRealStockOpDO);
        }
        return result;
    }

    /**
     * 组装虚仓库存数据
     * @param rwId
     * @param recordCode
     * @param recordType
     * @param virtualStockByCalculateDOs
     * @param detail
     * @param vId
     * @param coreVirtualStockOpDO
     */
	private void setCoreVirtualStock(Long rwId, String recordCode, Integer recordType,
			List<CoreVirtualStockOpDO> virtualStockByCalculateDOs, WarehouseRecordDetail detail,
			Long vId , CoreVirtualStockOpDO coreVirtualStockOpDO) {
		coreVirtualStockOpDO.setSkuId(detail.getSkuId());
		coreVirtualStockOpDO.setVirtualWarehouseId(vId);
		coreVirtualStockOpDO.setSkuCode(detail.getSkuCode());
		coreVirtualStockOpDO.setChannelCode(detail.getChannelCode());
		coreVirtualStockOpDO.setMerchantId(detail.getMerchantId());
		coreVirtualStockOpDO.setRealWarehouseId(rwId);
		coreVirtualStockOpDO.setRecordCode(recordCode);
		coreVirtualStockOpDO.setTransType(recordType);
		if(coreVirtualStockOpDO.getRealQty().compareTo(BigDecimal.ZERO) == 1) {
			virtualStockByCalculateDOs.add(coreVirtualStockOpDO);
		}
	}

    /**
     * 初始化出库库存对象
     */
    private CoreRealStockOpDO initOutWarehouseStockObj(WarehouseRecordE outRecord, List<WarehouseRecordDetail> detailList){
        List<CoreRealStockOpDetailDO> increaseDetails = new ArrayList<>();
        for (WarehouseRecordDetail detail: detailList) {
            if(detail.getActualQty().compareTo(BigDecimal.ZERO) > 0) {
                CoreRealStockOpDetailDO coreRealStockOpDetailDO = new CoreRealStockOpDetailDO();
                coreRealStockOpDetailDO.setSkuId(detail.getSkuId());
                coreRealStockOpDetailDO.setSkuCode(detail.getSkuCode());
                coreRealStockOpDetailDO.setUnlockQty(detail.getPlanQty());
                coreRealStockOpDetailDO.setRealQty(detail.getActualQty());
                coreRealStockOpDetailDO.setRealWarehouseId(outRecord.getRealWarehouseId());
                coreRealStockOpDetailDO.setCheckBeforeOp(true);
                increaseDetails.add(coreRealStockOpDetailDO);
            }
        }
        CoreRealStockOpDO coreRealStockOpDO = new CoreRealStockOpDO();
        coreRealStockOpDO.setRecordCode(outRecord.getRecordCode());
        coreRealStockOpDO.setTransType(outRecord.getRecordType());
        coreRealStockOpDO.setDetailDos(increaseDetails);
        return coreRealStockOpDO;
    }

    /**
     * 增加实体仓库库存的对象
     */
    private CoreRealStockOpDO initStockObj(ShopReplenishWarehouseRecordE recordE){
        List<CoreRealStockOpDetailDO> increaseDetails = new ArrayList<>();
        for (WarehouseRecordDetail detail: recordE.getWarehouseRecordDetails()) {
            CoreRealStockOpDetailDO coreRealStockOpDetailDO = new CoreRealStockOpDetailDO();
            coreRealStockOpDetailDO.setSkuId(detail.getSkuId());
            coreRealStockOpDetailDO.setSkuCode(detail.getSkuCode());
            coreRealStockOpDetailDO.setRealQty(detail.getActualQty());
            coreRealStockOpDetailDO.setRealWarehouseId(recordE.getRealWarehouseId());
            increaseDetails.add(coreRealStockOpDetailDO);
        }
        CoreRealStockOpDO coreRealStockOpDO = new CoreRealStockOpDO();
        coreRealStockOpDO.setRecordCode(recordE.getRecordCode());
        coreRealStockOpDO.setTransType(recordE.getRecordType());
        coreRealStockOpDO.setDetailDos(increaseDetails);
        return coreRealStockOpDO;
    }
}
