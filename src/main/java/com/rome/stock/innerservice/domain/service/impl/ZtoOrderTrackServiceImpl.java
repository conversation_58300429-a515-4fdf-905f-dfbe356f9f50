package com.rome.stock.innerservice.domain.service.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.rome.arch.core.exception.RomeException;
import com.rome.stock.innerservice.api.dto.wms.ZtoOrderTrackQueryParam;
import com.rome.stock.innerservice.api.dto.wms.ZtoOrderTrackQueryResult;
import com.rome.stock.innerservice.common.ResCode;
import com.rome.stock.innerservice.domain.service.ZtoOrderTrackService;
import com.rome.stock.wms.constants.CommonConstants;
import com.rome.stock.wms.constants.WarehouseWmsConfigEnum;
import com.rome.stock.wms.dto.request.OriginalRequest;
import com.rome.stock.wms.dto.response.OriginalResponse;
import com.rome.stock.wms.dto.response.TmsOrderUpdateResponse;
import com.rome.stock.wms.request.RequestWmsClient;
import com.rome.stock.wms.util.WmsRequestUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;

@Slf4j
@Service
public class ZtoOrderTrackServiceImpl implements ZtoOrderTrackService {

	private static final String ZTO_ORDERTRACK_QUERY = "cwst.logistics.get";

	@Override
	public List<ZtoOrderTrackQueryResult> query(ZtoOrderTrackQueryParam param) {
		OriginalRequest request = new OriginalRequest();
		request.setWmsTypeCode(WarehouseWmsConfigEnum.ZTO.name());
		request.setRequestContent(JSON.toJSONString(param));
		request.setRequestService(ZTO_ORDERTRACK_QUERY);
		OriginalResponse response = RequestWmsClient.originalRequest(request);
		log.info("查询中通云仓物流轨迹，入参：{},结果：{}", JSON.toJSONString(param), response.toString());
		if (CommonConstants.CODE_SUCCESS.equals(response.getCode())) {
			JSONArray jsonArray = response.getResponseContentJson().getJSONArray("result");
			if (jsonArray == null) {
				throw new RomeException(ResCode.STOCK_ERROR_1002, "查询中通云仓异常：" + param.getLogisticsCode());
			}
			return jsonArray.toJavaList(ZtoOrderTrackQueryResult.class);
		}
		throw new RomeException(ResCode.STOCK_ERROR_1002, "查询中通云仓异常：" + param.getLogisticsCode());
	}
}
