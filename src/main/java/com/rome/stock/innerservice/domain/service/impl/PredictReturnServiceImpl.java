package com.rome.stock.innerservice.domain.service.impl;

import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.rome.arch.core.clientobject.Response;
import com.rome.arch.core.exception.RomeException;
import com.rome.stock.innerservice.api.dto.RealWarehouse;
import com.rome.stock.innerservice.api.dto.frontrecord.PredictReturnDTO;
import com.rome.stock.innerservice.api.dto.frontrecord.PredictReturnDetailDTO;
import com.rome.stock.innerservice.common.*;
import com.rome.stock.innerservice.constant.JobHandelStatusEnum;
import com.rome.stock.innerservice.domain.entity.FrontWarehouseRecordRelationE;
import com.rome.stock.innerservice.domain.repository.warehouserecord.PredictReturnRepository;
import com.rome.stock.innerservice.domain.service.PredictReturnService;
import com.rome.stock.innerservice.domain.service.RealWarehouseService;
import com.rome.stock.innerservice.remote.item.dto.SkuInfoExtDTO;
import com.rome.stock.innerservice.remote.stockCore.facade.StockCoreFacade;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * @Description: 退货预入库Impl
 * <p>
 * @Author: chuwenchao  2019/11/22
 */
@Slf4j
@Service
public class PredictReturnServiceImpl implements PredictReturnService {

    @Resource
    private PredictReturnRepository predictReturnRepository;
    @Resource
    private RealWarehouseService realWarehouseService;
    @Resource
    private SkuInfoTools skuInfoTools;


    /**
     * @Description: 通过DTO查询退货预入库列表 <br>
     *
     * <AUTHOR> 2019/11/22
     * @param predictReturnDTO
     * @return
     */
    @Override
    public PageInfo<PredictReturnDTO> queryPageListByDTO(PredictReturnDTO predictReturnDTO) {
        if(StringUtils.isNotBlank(predictReturnDTO.getWarehouseCodes())) {
            String[] inRecordCodes = predictReturnDTO.getWarehouseCodes().split("\n");
            List<RealWarehouse> realWarehouses = realWarehouseService.findListByRealWarehouseCode(Arrays.asList(inRecordCodes));
            if(CollectionUtils.isEmpty(realWarehouses)){
                PageInfo<PredictReturnDTO> pageList = new PageInfo<>(new ArrayList<>());
                pageList.setTotal(0);
                return pageList;
            }
            List<Long> realWarehouseIds = RomeCollectionUtil.getValueList(realWarehouses, "id");
            predictReturnDTO.setRealWarehouseIds(realWarehouseIds);
        }
        //根据入库单号查询
        if(!StringUtils.isEmpty(predictReturnDTO.getRecordCodes())){
            String[] inRecordCodes = predictReturnDTO.getRecordCodes().split("\n");
            predictReturnDTO.setRecordCodeList(Arrays.asList(inRecordCodes));
        }

        //根据快递单号单号查询
        if(!StringUtils.isEmpty(predictReturnDTO.getExpressCodes())){
            String[] expressCodes = predictReturnDTO.getExpressCodes().split("\n");
            predictReturnDTO.setExpressCodeList(Arrays.asList(expressCodes));
        }




        Page page = PageHelper.startPage(predictReturnDTO.getPageIndex(), predictReturnDTO.getPageSize());
        List<PredictReturnDTO> predictReturnDTOS = predictReturnRepository.queryListByDTO(predictReturnDTO);
        if(CollectionUtils.isNotEmpty(predictReturnDTOS)) {
            List<Long> warehouseIds = predictReturnDTOS.stream().map(PredictReturnDTO::getRealWarehouseId).distinct().collect(Collectors.toList());
            List<RealWarehouse> realWarehouses = realWarehouseService.findByRealWarehouseIds(warehouseIds);
            Map<Long, RealWarehouse> realWarehouseMap = realWarehouses.stream().collect(Collectors.toMap(RealWarehouse::getId, Function.identity(), (v1, v2) -> v1));
            RealWarehouse temp = null;
            for(PredictReturnDTO dto : predictReturnDTOS) {
                temp = realWarehouseMap.get(dto.getRealWarehouseId());
                if(temp != null) {
                    dto.setRealWarehouseName(temp.getRealWarehouseName());
                    dto.setWarehouseCode(temp.getRealWarehouseCode());
                }
            }
        }

        PageInfo<PredictReturnDTO> pageList = new PageInfo<>(predictReturnDTOS);
        pageList.setTotal(page.getTotal());
        return pageList;
    }

    /**
     * @Description: 通过前置单号查询退货预入库明细列表 <br>
     *
     * <AUTHOR> 2019/11/22
     * @param recordCode
     * @return
     */
    @Override
    public List<PredictReturnDetailDTO> queryDetailListByCode(String recordCode) {
        List<PredictReturnDetailDTO> detailDTOS = predictReturnRepository.queryDetailListByCode(recordCode);
        if(CollectionUtils.isNotEmpty(detailDTOS)) {
            List<Long> skuIds = detailDTOS.stream().map(PredictReturnDetailDTO::getSkuId).distinct().collect(Collectors.toList());
            List<SkuInfoExtDTO> skuInfoExtDTOS = skuInfoTools.querySkuListBySkuIds(skuIds);
            Map<String, String> skuInfoMap = skuInfoExtDTOS.stream().collect(Collectors.toMap(SkuInfoExtDTO::getSkuCode, SkuInfoExtDTO::getName));
            for(PredictReturnDetailDTO detailDTO : detailDTOS) {
                detailDTO.setSkuName(skuInfoMap.get(detailDTO.getSkuCode()));
            }
        }
        return detailDTOS;
    }

    /**
     * POP商城逆向退货查询待处理数据
     * @param handleNum num
     * @return 数据
     */
    @Override
    public List<PredictReturnDTO> queryNeedHandlePredictReturnData(Integer handleNum) {
        List<PredictReturnDTO> predictReturnDTOS = predictReturnRepository.queryNeedHandlePredictReturnData(handleNum);
        if (CollectionUtils.isEmpty(predictReturnDTOS)) {
            return new ArrayList<>();
        }
        return predictReturnDTOS;
    }


    /**
     * 处理失败，更新失败时间
     *
     */
    @Override
    public Integer updateNeedJobHandleFail(String recordCode) {
        return predictReturnRepository.updateNeedJobHandleFail(recordCode);
    }

}
