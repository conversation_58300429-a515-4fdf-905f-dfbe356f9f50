/**
 * Filename CoreStockConsumer.java
 * Company 上海来伊份科技有限公司。
 *
 * <AUTHOR>
 * @version
 */
package com.rome.stock.innerservice.domain.message;

import com.alibaba.fastjson.JSON;
import com.rome.stock.common.enums.CustomRocketMQEnum;
import com.rome.scm.common.rocketmq.CustomConsumerListener;
import com.rome.stock.innerservice.remote.orderTrack.dto.TrackInfo;
import com.rome.stock.innerservice.remote.orderTrack.facade.OrderTrackFacade;
import lombok.extern.slf4j.Slf4j;
import org.apache.rocketmq.common.message.MessageExt;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * 订单物流轨迹接收消息
 * <AUTHOR>
 * @since 2019年6月5日 下午4:09:46
 */
@Slf4j
@Service
//@RocketMQMessageListener(topic = "${rocketmq.topic.stock.virtual:stock-virtual}", selectorExpression = "stockVirtual", consumerGroup = "stock-virtual-group")
public class OrderTrackPushConsumer implements CustomConsumerListener<MessageExt> {


    @Resource
    private OrderTrackFacade orderTrackFacade;

    @Override
    public void onMessage(MessageExt msg, String businessNo, String msgKey) {
        log.info("保存订单轨迹接收到消息,单号：{}" ,businessNo);
        TrackInfo trackInfo = JSON.parseObject(msg.getBody(), TrackInfo.class);
        orderTrackFacade.saveRemoteInfo(trackInfo);
    }

	@Override
	public String getRocketMQCode() {
		return CustomRocketMQEnum.MQ_ORDER_TRACK_PUSH.getCode();
	}

}
