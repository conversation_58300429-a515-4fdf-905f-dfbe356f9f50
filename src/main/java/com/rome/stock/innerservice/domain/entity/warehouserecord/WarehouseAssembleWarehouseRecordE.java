package com.rome.stock.innerservice.domain.entity.warehouserecord;

import com.rome.arch.core.domain.EntityFactory;
import com.rome.stock.common.enums.warehouse.WarehouseRecordTypeVO;
import com.rome.stock.innerservice.constant.WarehouseRecordBusinessTypeVO;

import com.rome.stock.innerservice.constant.WmsSyncStatusVO;
import com.rome.stock.innerservice.domain.entity.frontrecord.WarehouseAssembleRecordDetailE;
import com.rome.stock.innerservice.domain.entity.frontrecord.WarehouseAssembleRecordE;
import com.rome.stock.innerservice.domain.repository.warehouserecord.WarehouseAssembleWarehouseRepository;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.extern.slf4j.Slf4j;

import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;

/**
 * @Description 仓库加工出入库单
 * <AUTHOR>
 * @Date 2019/5/12 17:07
 * @Version 1.0
 */
@Data
@Slf4j
@Component
@Scope("prototype")
@EqualsAndHashCode(callSuper = false)
public class WarehouseAssembleWarehouseRecordE extends AbstractWarehouseRecord {
    @Resource
    private EntityFactory entityFactory;
    @Resource
    private WarehouseAssembleWarehouseRepository warehouseAssembleWarehouseRepository;

    /**
     * 创建仓库单据
     */
    public void addWarehouseRecord() {
        Long id = warehouseAssembleWarehouseRepository.saveWarehouseRecord(this);
        this.setId(id);
        this.warehouseRecordDetails.forEach(record -> record.setWarehouseRecordDetail(this));
        warehouseAssembleWarehouseRepository.saveWarehouseRecordDetails(this.warehouseRecordDetails);
        this.addFrontRecordAndWarehouseRelation();
    }

    /**
     * 根据前置单生成组装加工入库单
     */
    public void createInAssembleRecordByFrontRecord(WarehouseAssembleRecordE frontRecord){
        this.createRecodeCode(WarehouseRecordTypeVO.WAREHOUSE_ASSEMBLE_IN_RECORD.getCode());
        this.setBusinessType(WarehouseRecordBusinessTypeVO.IN_WAREHOUSE_RECORD.getType());
        this.setRecordType(WarehouseRecordTypeVO.WAREHOUSE_ASSEMBLE_IN_RECORD.getType());
        this.createRecordByFrontRecord(frontRecord);
    }

    /**
     * 根据前置单生成组装加工出库单
     */
    public void createOutAssembleRecordByFrontRecord(WarehouseAssembleRecordE frontRecord){
        this.createRecodeCode(WarehouseRecordTypeVO.WAREHOUSE_ASSEMBLE_OUT_RECORD.getCode());
        this.setBusinessType(WarehouseRecordBusinessTypeVO.OUT_WAREHOUSE_RECORD.getType());
        this.setRecordType(WarehouseRecordTypeVO.WAREHOUSE_ASSEMBLE_OUT_RECORD.getType());
        this.createRecordByFrontRecord(frontRecord);
    }

    /**
     * 根据前置单生成反拆加工入库单
     */
    public void createInSpitRecordByFrontRecord(WarehouseAssembleRecordE frontRecord){
        this.createRecodeCode(WarehouseRecordTypeVO.WAREHOUSE_SPIT_IN_RECORD.getCode());
        this.setBusinessType(WarehouseRecordBusinessTypeVO.IN_WAREHOUSE_RECORD.getType());
        this.setRecordType(WarehouseRecordTypeVO.WAREHOUSE_SPIT_IN_RECORD.getType());
        this.createRecordByFrontRecord(frontRecord);
    }

    /**
     * 根据前置单生成反拆加工出库单
     */
    public void createOutSpitRecordByFrontRecord(WarehouseAssembleRecordE frontRecord){
        this.createRecodeCode(WarehouseRecordTypeVO.WAREHOUSE_SPIT_OUT_RECORD.getCode());
        this.setBusinessType(WarehouseRecordBusinessTypeVO.OUT_WAREHOUSE_RECORD.getType());
        this.setRecordType(WarehouseRecordTypeVO.WAREHOUSE_SPIT_OUT_RECORD.getType());
        this.createRecordByFrontRecord(frontRecord);
    }

    /**
     * 根据前置单设置出入库数据
     * @param frontRecord
     */
    private void createRecordByFrontRecord(WarehouseAssembleRecordE frontRecord){
        this.setAbstractFrontRecord(frontRecord);
        this.setRealWarehouseId(frontRecord.getRealWarehouseId());
        this.setMerchantId(frontRecord.getMerchantId());
        this.setOutCreateTime(frontRecord.getOutCreateTime());
        this.setSyncWmsStatus(WmsSyncStatusVO.NO_REQUIRED.getStatus());
        List<WarehouseAssembleRecordDetailE> frontRecordDetails=frontRecord.getFrontRecordDetails();
        this.setWarehouseRecordDetails(new ArrayList<>(frontRecordDetails.size()));
        if(frontRecordDetails!=null){
            for(WarehouseAssembleRecordDetailE detailE:frontRecordDetails){
                WarehouseRecordDetail warehouseRecordDetail=entityFactory.createEntity(WarehouseRecordDetail.class);
                warehouseRecordDetail.createRecordDetailByFrontRecord(detailE);
                //盘点入库直接设置实际出库库数量
                warehouseRecordDetail.setActualQty(detailE.getBasicSkuQty());
                this.warehouseRecordDetails.add(warehouseRecordDetail);
            }
            //设置skuCode和skuID
            this.initWarehouseRecodeDetail();
        }
    }
}
