package com.rome.stock.innerservice.domain.service.impl;

import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.rome.arch.core.domain.EntityFactory;
import com.rome.arch.core.exception.RomeException;
import com.rome.stock.innerservice.api.dto.RealWarehouse;
import com.rome.stock.innerservice.api.dto.RealWarehouseStockDTO;
import com.rome.stock.innerservice.api.dto.allocation.WhSkuUnitDTO;
import com.rome.stock.innerservice.api.dto.frontrecord.ShopConsumeAdjustRecordDTO;
import com.rome.stock.innerservice.api.dto.frontrecord.ShopConsumeAdjustRecordDetailDTO;
import com.rome.stock.innerservice.common.*;
import com.rome.stock.innerservice.constant.FrontRecordStatusVO;
import com.rome.stock.core.constant.StockCoreConsts;
import com.rome.stock.innerservice.constant.StockRecordInfoTypeVo;
import com.rome.stock.innerservice.domain.convertor.frontrecord.ShopConsumeConvertor;
import com.rome.stock.innerservice.domain.convertor.frontrecord.WhAllocationConvertor;
import com.rome.stock.innerservice.domain.entity.frontrecord.ShopConsumeAdjustRecordDetailE;
import com.rome.stock.innerservice.domain.entity.frontrecord.ShopConsumeAdjustRecordE;
import com.rome.stock.innerservice.domain.entity.warehouserecord.WarehouseRecordDetail;
import com.rome.stock.core.domain.repository.CoreRealWarehouseStockRepository;
import com.rome.stock.innerservice.domain.repository.RealWarehouseRepository;
import com.rome.stock.innerservice.domain.repository.frontrecord.FrShopConsumeDetailRepository;
import com.rome.stock.innerservice.domain.repository.frontrecord.FrShopConsumeRepository;
import com.rome.stock.innerservice.domain.repository.warehouserecord.WarehouseRecordRepository;
import com.rome.stock.innerservice.domain.service.RealWarehouseConsumeService;
import com.rome.stock.innerservice.domain.service.RealWarehouseService;
import com.rome.stock.innerservice.domain.service.ShopConsumeOrderService;
import com.rome.stock.innerservice.domain.service.ShopConsumeService;
import com.rome.stock.core.infrastructure.dataobject.core.CoreRealWarehouseStockDO;
import com.rome.stock.innerservice.remote.base.dto.SaleOrgDTO;
import com.rome.stock.innerservice.remote.base.dto.StoreDTO;
import com.rome.stock.innerservice.remote.base.facade.SaleOrgFacade;
import com.rome.stock.innerservice.remote.base.facade.ShopFacade;
import com.rome.stock.innerservice.remote.item.dto.SkuInfoExtDTO;
import com.rome.stock.innerservice.remote.item.dto.SkuUnitExtDTO;
import com.rome.stock.innerservice.remote.item.facade.SkuFacade;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;

/**
 * @Description 门店报废调整单业务实现类
 * <AUTHOR>
 * @Date 2019/5/10 19:57
 * @Version 1.0
 */
@Slf4j
@Service
public class ShopConsumeServiceImpl implements ShopConsumeService {

    @Resource
    private EntityFactory entityFactory;
    @Resource
    private ShopConsumeConvertor shopConsumeConvertor;
    @Resource
    private FrShopConsumeRepository frShopConsumeRepository;
    @Resource
    private FrShopConsumeDetailRepository frShopConsumeDetailRepository;
    @Resource
    private CoreRealWarehouseStockRepository coreRealWarehouseStockRepository;
    @Resource
    private RealWarehouseService realWarehouseService;
    @Resource
    private RealWarehouseRepository realWarehouseRepository;
    @Resource
    private WarehouseRecordRepository warehouseRecordRepository;
    @Resource
    private SkuFacade skuFacade;
    @Resource
    private SkuQtyUnitTools skuQtyUnitTools;
    @Resource
    private WhAllocationConvertor whAllocationConvertor;
    @Resource
    private SkuInfoTools skuInfoTools;
    @Resource
    private ShopFacade shopFacade;
    @Resource
    private SaleOrgFacade saleOrgFacade;
    @Resource
    private RealWarehouseConsumeService realWarehouseConsumeService;
    @Resource
    private ShopConsumeOrderService shopConsumeOrderService;

    /**
     * 创建仓库门店报废单
     * @param shopConsumeAdjustRecordDTO
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void createShopConsumeAdjustRecord(ShopConsumeAdjustRecordDTO shopConsumeAdjustRecordDTO) {

        try{
            //1.保存损耗调整单
            //2.设置单子状态为已新建
            shopConsumeAdjustRecordDTO.setRecordStatus(FrontRecordStatusVO.INIT.getStatus());
            ShopConsumeAdjustRecordE shopConsumeAdjustRecordE = shopConsumeConvertor.dtoTOEntity(shopConsumeAdjustRecordDTO);
            shopConsumeAdjustRecordE.setFrontRecordDetails(shopConsumeConvertor.detailDtoTOEntity(shopConsumeAdjustRecordDTO.getDetailDTOS()));
            //3.保存前置单
            shopConsumeAdjustRecordE.addFrontRecord();
            //输入kibana日志信息
            KibanaLogUtils.printKibanaRecordInfo(shopConsumeAdjustRecordE.getRecordCode(),"", StockRecordInfoTypeVo.SHOP_CONSUME_CREATE.getType(),StockRecordInfoTypeVo.SHOP_CONSUME_CREATE.getDesc(),"createShopConsumeAdjustRecord",shopConsumeAdjustRecordDTO);
        } catch (RomeException e){
            throw new RomeException(e.getCode(),e.getMessage());
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            throw e;
        }
    }

    /**
     * 根据条件查询门店报废单
     * @param shopConsumeAdjustRecordDTO
     * @return
     */
    @Override
    public PageInfo<ShopConsumeAdjustRecordDTO> selectShopConsumeAdjustRecordByCondition(ShopConsumeAdjustRecordDTO shopConsumeAdjustRecordDTO) {
        List<ShopConsumeAdjustRecordDTO> shopConsumeAdjustRecordDTOS;
        ShopConsumeAdjustRecordE shopConsumeAdjustRecordE = shopConsumeConvertor.dtoTOEntity(shopConsumeAdjustRecordDTO);

        //设置分页
        Page page;
        PageInfo<ShopConsumeAdjustRecordDTO> pageList;
        List<RealWarehouse> warehouses = null;

        //根据查询条件查询损耗单
        if(null != shopConsumeAdjustRecordDTO.getFactoryCode() && null != shopConsumeAdjustRecordDTO.getRealWarehouseCode()){
            //若查询条件中工厂code和仓库code不为空,查询仓库的唯一id,并设置进查询条件中
            RealWarehouse warehouse = realWarehouseService.findByRwCodeAndFactoryCode(shopConsumeAdjustRecordDTO.getRealWarehouseCode(), shopConsumeAdjustRecordDTO.getFactoryCode());
            warehouses = new ArrayList<>();
            warehouses.add(warehouse);
        }else if(null != shopConsumeAdjustRecordDTO.getFactoryCode() && null == shopConsumeAdjustRecordDTO.getRealWarehouseCode()){
            //若只有工厂，查询工厂下所有的仓库id
            warehouses = realWarehouseConsumeService.queryRealWarehouseByFactoryCode(shopConsumeAdjustRecordDTO.getFactoryCode(),shopConsumeAdjustRecordDTO.getRecordType());
            if(0 == warehouses.size()){
                warehouses.add(new RealWarehouse());
            }
        }

        page = PageHelper.startPage(shopConsumeAdjustRecordDTO.getPageIndex(), shopConsumeAdjustRecordDTO.getPageSize());
        shopConsumeAdjustRecordDTOS = shopConsumeConvertor.entityTODto(frShopConsumeRepository.selectConsumeAdjustRecordByCondition(shopConsumeAdjustRecordE,warehouses));

        pageList = new PageInfo<>(shopConsumeAdjustRecordDTOS);
        pageList.setTotal(page.getTotal());

        if(0 == pageList.getTotal()) {
            return pageList;
        }

        //根据仓库id查询仓库信息
        List<ShopConsumeAdjustRecordDTO> dtoList = pageList.getList();
        for (ShopConsumeAdjustRecordDTO recordDTO : dtoList) {
            RealWarehouse realWarehouse = realWarehouseService.findByRealWarehouseId(recordDTO.getRealWarehouseId());
            recordDTO.setFactoryCode(realWarehouse.getFactoryCode());
            recordDTO.setRealWarehouseCode(realWarehouse.getRealWarehouseCode());
            recordDTO.setRealWarehouseName(realWarehouse.getRealWarehouseName());
        }
        return pageList;
    }

    /**
     * 根据调整单id查询明细
     * @param id
     * @return
     */
    @Override
    public ShopConsumeAdjustRecordDTO selectShopConsumeRecordDetail(Long id) {
        //1.根据id查询调整单信息
        ShopConsumeAdjustRecordE shopConsumeAdjustRecordE = frShopConsumeRepository.selectConsumeAdjustRecordById(id);
        ShopConsumeAdjustRecordDTO shopConsumeAdjustRecordDTO = shopConsumeConvertor.entityTODto(shopConsumeAdjustRecordE);
        //2.根据调整单id查询明细
        List<ShopConsumeAdjustRecordDetailE> detailEList = frShopConsumeDetailRepository.selectAdjustConsumeRecordDetail(shopConsumeAdjustRecordE.getId());
        shopConsumeAdjustRecordE.convertRealToBasic(detailEList);
        List<ShopConsumeAdjustRecordDetailDTO> detailDtoList = shopConsumeConvertor.detailEntityTODto(detailEList);

        //根据skuId获取库存数量
        List<Long> skuIds = RomeCollectionUtil.getValueList(detailDtoList, "skuId");
        List<RealWarehouseStockDTO>  stockList = realWarehouseRepository.queryStockByWhIdAndSkuIds(shopConsumeAdjustRecordE.getRealWarehouseId(),skuIds);
        Map<Object, RealWarehouseStockDTO> stockDTOMap = RomeCollectionUtil.listforMap(stockList, "skuId");

        //调用商品接口，查询sku信息
        List<SkuInfoExtDTO> skuInfoExtDTOS = skuFacade.skusBySkuId(skuIds);
        Map<Long, SkuInfoExtDTO> skuInfoExtDTOMap = RomeCollectionUtil.listforMap(skuInfoExtDTOS, "id");
        //根据skuId调用第三方单位接口，查询sku单位
        Map<Long,List<WhSkuUnitDTO>> unitsMap = new HashMap<>(skuIds.size());
        for (Long skuId :skuIds) {
            if(skuInfoExtDTOMap.containsKey(skuId)){
                SkuInfoExtDTO skuInfo = skuInfoExtDTOMap.get(skuId);
                List<SkuUnitExtDTO> skuUnitDTOS = skuFacade.skuUnitBySkuCode(skuInfo.getSkuCode());
                List<WhSkuUnitDTO> whUnitList = whAllocationConvertor.skuUnitCovertor(skuUnitDTOS);
                this.dealUnitList(whUnitList);
//                this.noRepeatUnit(whUnitList);
                unitsMap.put(skuId,whUnitList);
            }
        }

        //若单子状态为已出库，查询实际出库数量
        Map<Long, WarehouseRecordDetail> warehouseRecordDetailMap = null;
        if(shopConsumeAdjustRecordE.getRecordStatus().equals(FrontRecordStatusVO.OUT_ALLOCATION.getStatus())){
            //根据前置单id查询出库单信息
            List<Long> warehouseIds = warehouseRecordRepository.queryWarehouseIdByFrontId(shopConsumeAdjustRecordE.getId(), shopConsumeAdjustRecordE.getRecordType());
            AlikAssert.isTrue(warehouseIds != null && warehouseIds.size() > 0, ResCode.STOCK_ERROR_1017, ResCode.STOCK_ERROR_1017_DESC);
            Long warehouseId = warehouseIds.get(0);
            //根据出库单id查询出库单明细
            List<WarehouseRecordDetail> warehouseRecordDetails = warehouseRecordRepository.queryDetailListByRecordId(warehouseId);
            warehouseRecordDetailMap = RomeCollectionUtil.listforMap(warehouseRecordDetails, "skuId");
        }

        for (ShopConsumeAdjustRecordDetailDTO detailDTO:detailDtoList) {
            if(skuInfoExtDTOMap.containsKey(detailDTO.getSkuId())){
                SkuInfoExtDTO infoExtDTO = skuInfoExtDTOMap.get(detailDTO.getSkuId());
                detailDTO.setSkuCode(infoExtDTO.getSkuCode());
                detailDTO.setSkuName(infoExtDTO.getName());
            }
            if(null != warehouseRecordDetailMap && warehouseRecordDetailMap.containsKey(detailDTO.getSkuId()) && null != detailDTO.getScale()){
                //计算实际出库数量
                WarehouseRecordDetail warehouseRecordDetail = warehouseRecordDetailMap.get(detailDTO.getSkuId());
                detailDTO.setActualQty(warehouseRecordDetail.getActualQty().divide(detailDTO.getScale(),StockCoreConsts.DECIMAL_POINT_NUM,BigDecimal.ROUND_DOWN));
            }else{
                detailDTO.setActualQty(new BigDecimal(0));
            }
            detailDTO.setBasicQty(detailDTO.getScale().multiply(detailDTO.getSkuQty()));
            detailDTO.setSkuUnitList(unitsMap.get(detailDTO.getSkuId()));
            RealWarehouseStockDTO stockDTO = stockDTOMap.get(detailDTO.getSkuId());
            detailDTO.setRemainStockQty(stockDTO.getRealQty().subtract(stockDTO.getLockQty().setScale(3,BigDecimal.ROUND_HALF_DOWN)));
        }
        //3.根据仓库id查询仓库信息
        RealWarehouse realWarehouse = realWarehouseService.findByRealWarehouseId(shopConsumeAdjustRecordDTO.getRealWarehouseId());
        shopConsumeAdjustRecordDTO.setFactoryCode(realWarehouse.getFactoryCode());
        shopConsumeAdjustRecordDTO.setRealWarehouseCode(realWarehouse.getRealWarehouseCode());
        shopConsumeAdjustRecordDTO.setRealWarehouseName(realWarehouse.getRealWarehouseName());
        //4.根据工厂code查询名称
        String factoryName = shopFacade.searchShopName(realWarehouse.getFactoryCode());
        shopConsumeAdjustRecordDTO.setFactoryName(factoryName);

        shopConsumeAdjustRecordDTO.setDetailDTOS(detailDtoList);
        return shopConsumeAdjustRecordDTO;
    }

    /**
     * 修改门店报废单
     * @param shopConsumeAdjustRecordDTO
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void modifyShopConsumeRecord(ShopConsumeAdjustRecordDTO shopConsumeAdjustRecordDTO) {
        try{
            //1.修改损耗调整单
            frShopConsumeRepository.updateAdjustConsumeRecord(shopConsumeConvertor.dtoTOEntity(shopConsumeAdjustRecordDTO));

            List<ShopConsumeAdjustRecordDetailDTO> detailDTOS = shopConsumeAdjustRecordDTO.getDetailDTOS();
            if(null != detailDTOS){
                //2.修改损耗调整单明细
                //删除原有的明细
                frShopConsumeDetailRepository.deleteAdjustConsumeRecordDetail(shopConsumeAdjustRecordDTO.getId());
                //重新插入
                List<ShopConsumeAdjustRecordDetailE> adjustRecordDetailES = shopConsumeConvertor.detailDtoTOEntity(detailDTOS);
                skuInfoTools.convertSkuCode(adjustRecordDetailES);
                skuQtyUnitTools.convertRealToBasic(adjustRecordDetailES);
                for (ShopConsumeAdjustRecordDetailE detailE:adjustRecordDetailES) {
                    detailE.setFrontRecordId(shopConsumeAdjustRecordDTO.getId());
                }
                frShopConsumeDetailRepository.saveConsumeAdjustRecord(adjustRecordDetailES);
            }
        } catch (RomeException e){
            throw new RomeException(e.getCode(),e.getMessage());
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            throw e;
        }
    }

    /**
     * 门店报废单确认(状态修改)
     * @param ids
     */
    @Override
    @Async("coreStockTask")
    public void confirmShopConsumeRecord(List<Long> ids,Long modifier) {
        frShopConsumeRepository.confirmShopConsumeRecords(ids,modifier);
        for (Long id : ids) {
            shopConsumeOrderService.confirmSingleShopConsumeRecord(id, modifier);
        }
    }

    /**
     * 门店报废单取消
     * @param id
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void cancelShopConsumeRecord(Long id,Long modifier) {
        ShopConsumeAdjustRecordDTO recordDTO = shopConsumeConvertor.entityTODto(frShopConsumeRepository.selectConsumeAdjustRecordById(id));

        //若单子不存在，抛出异常
        if(null == recordDTO){
            throw new RomeException(ResCode.STOCK_ERROR_1014, ResCode.STOCK_ERROR_1014_DESC);
        }

        if(!FrontRecordStatusVO.INIT.getStatus().equals( recordDTO.getRecordStatus()) && !FrontRecordStatusVO.ENABLED_FAIL.getStatus().equals( recordDTO.getRecordStatus())){
            throw new RomeException(ResCode.STOCK_ERROR_1028, ResCode.STOCK_ERROR_1028_DESC);
        }
        //设置单子状态为已取消
        recordDTO.setRecordStatus(FrontRecordStatusVO.DISABLED.getStatus());
        recordDTO.setModifier(modifier);
        frShopConsumeRepository.updateAdjustConsumeRecordStatus(shopConsumeConvertor.dtoTOEntity(recordDTO));
    }

    /**
     * 根据id批量查询单子
     * @param ids
     * @return
     */
    @Override
    public List<ShopConsumeAdjustRecordDTO> selectShopConsumeRecordByIds(List<Long> ids) {
        List<ShopConsumeAdjustRecordE> recordES = frShopConsumeRepository.selectConsumeAdjustRecordByIds(ids);
        return shopConsumeConvertor.entityTODto(recordES);
    }






    /**
     * 查询所有门店仓库
     * @return
     */
    @Override
    public List<StoreDTO> queryShopList() {
        return shopFacade.searchByType("A");
    }

    /**
     * 根据组织code查询组织信息
     * @param orgCode
     * @return
     */
    @Override
    public SaleOrgDTO getOrgByOrgCode(String orgCode){
        return saleOrgFacade.getOrgByOrgCode(orgCode);
    }

    /**
     * 根据工厂名称查询模糊查询门店
     * @param query
     * @return
     */
    @Override
    public PageInfo<StoreDTO> queryShopFactory(StoreDTO query) {
        return shopFacade.searchStoreByName(query.getPageIndex(),query.getPageSize(),query);
    }

    /**
     * 批量查询总库存
     *
     * @param skuIds
     * @param rwId
     * @return
     */
    private Map<Long, CoreRealWarehouseStockDO> getTotalStock(List<Long> skuIds, Long rwId) {
        Map<Long, CoreRealWarehouseStockDO> skuStockMap = new HashMap<>();
        List<CoreRealWarehouseStockDO> queryParam = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(skuIds)) {
            for (Long skuId : skuIds) {
                CoreRealWarehouseStockDO crwDo = new CoreRealWarehouseStockDO();
                crwDo.setSkuId(skuId);
                crwDo.setRealWarehouseId(rwId);
                queryParam.add(crwDo);
            }
            List<CoreRealWarehouseStockDO> stockList = coreRealWarehouseStockRepository.getRWStock(queryParam);
            skuStockMap = RomeCollectionUtil.listforMap(stockList, "skuId");
        }
        return skuStockMap;
    }

    /**
     * 处理前端展示的单位
     */
    private void dealUnitList(List<WhSkuUnitDTO> whUnitList){
        Iterator<WhSkuUnitDTO> it= whUnitList.iterator();
        WhSkuUnitDTO dto1 = null;
        while (it.hasNext()){
            WhSkuUnitDTO dto =  it.next();
            if(5 == dto.getType()){
                dto1 = dto;
                break;
            }
        }
        whUnitList.clear();
        whUnitList.add(dto1);
    }

    /**
     * 去除重复单位
     * @param whUnitList
     */
    private void noRepeatUnit(List<WhSkuUnitDTO> whUnitList){
        Map<String, WhSkuUnitDTO> unitCodes = RomeCollectionUtil.listforMap(whUnitList, "unitCode");
        whUnitList = new ArrayList<>();
        for (String unitCode : unitCodes.keySet()) {
            whUnitList.add(unitCodes.get(unitCode));
        }
    }
}
