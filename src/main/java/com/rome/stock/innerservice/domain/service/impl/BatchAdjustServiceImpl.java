package com.rome.stock.innerservice.domain.service.impl;

import com.rome.arch.core.domain.EntityFactory;
import com.rome.stock.common.enums.BatchStockTypeVO;
import com.rome.stock.common.enums.warehouse.BatchAdjustVO;
import com.rome.stock.common.enums.warehouse.WarehouseRecordTypeVO;
import com.rome.stock.innerservice.api.dto.BoxBatchStockOpDTO;
import com.rome.stock.innerservice.api.dto.RealWarehouse;
import com.rome.stock.innerservice.api.dto.frontrecord.WmsBatchAdjustDTO;
import com.rome.stock.innerservice.common.AlikAssert;
import com.rome.stock.innerservice.common.ResCode;
import com.rome.stock.innerservice.common.RomeCollectionUtil;
import com.rome.stock.innerservice.common.SkuQtyUnitTools;
import com.rome.stock.innerservice.constant.FrontRecordDetailStatusVO;
import com.rome.stock.innerservice.domain.convertor.frontrecord.FrBatchAdjustConvertor;
import com.rome.stock.innerservice.domain.entity.frontrecord.BatchAdjustRecordDetailE;
import com.rome.stock.innerservice.domain.entity.frontrecord.BatchAdjustRecordE;
import com.rome.stock.innerservice.domain.entity.frontrecord.WarehouseInventoryRecordDetailE;
import com.rome.stock.innerservice.domain.entity.frontrecord.WarehouseInventoryRecordE;
import com.rome.stock.innerservice.domain.entity.warehouserecord.BatchAdjustWarehouseRecordE;
import com.rome.stock.innerservice.domain.repository.BatchStockBoxDetailRepository;
import com.rome.stock.innerservice.domain.repository.BatchStockRepository;
import com.rome.stock.innerservice.domain.repository.BatchStockToolRepository;
import com.rome.stock.innerservice.domain.repository.frontrecord.FrBatchAdjustRepository;
import com.rome.stock.innerservice.domain.repository.frontrecord.FrWarehouseInventoryTempRepository;
import com.rome.stock.innerservice.domain.repository.frontrecord.FrWhInventoryBatchTempRepository;
import com.rome.stock.innerservice.domain.service.BatchAdjustService;
import com.rome.stock.innerservice.domain.service.BatchStockBoxDetailService;
import com.rome.stock.innerservice.domain.service.BatchStockService;
import com.rome.stock.innerservice.domain.service.RealWarehouseService;
import com.rome.stock.innerservice.facade.BatchStockFacade;
import com.rome.stock.innerservice.infrastructure.dataobject.BatchStockBoxDetailDO;
import com.rome.stock.innerservice.infrastructure.dataobject.BatchStockDO;
import com.rome.stock.innerservice.infrastructure.dataobject.BatchStockDataOpDO;
import com.rome.stock.innerservice.infrastructure.dataobject.BatchStockOpDO;
import com.rome.stock.innerservice.infrastructure.dataobject.frontrecord.FrWarehouseInventoryStartDO;
import com.rome.stock.innerservice.infrastructure.dataobject.frontrecord.FrWarehouseInventoryTempDO;
import com.rome.stock.innerservice.infrastructure.dataobject.frontrecord.FrWhInventoryBatchTempDO;
import com.rome.stock.innerservice.infrastructure.mapper.BatchBoxConfigMapper;
import com.rome.stock.innerservice.remote.item.dto.SkuInfoExtDTO;
import com.rome.stock.innerservice.remote.item.facade.SkuFacade;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 类BatchAdjustServiceimpl的实现描述：批次调整实现
 *
 * <AUTHOR> 2022/7/12 15:02
 */
@Slf4j
@Service
public class BatchAdjustServiceImpl implements BatchAdjustService {

    @Resource
    private EntityFactory entityFactory;

    @Resource
    private RealWarehouseService realWarehouseService;

    @Resource
    private FrBatchAdjustConvertor frBatchAdjustConvertor;

    @Resource
    private FrBatchAdjustRepository frBatchAdjustRepository;

    @Resource
    private FrWarehouseInventoryTempRepository frWarehouseInventoryTempRepository;

    @Resource
    private FrWhInventoryBatchTempRepository frWhInventoryBatchTempRepository;

    @Resource
    private BatchStockRepository batchStockRepository;

    @Resource
    private SkuFacade skuFacade;

    @Resource
    private SkuQtyUnitTools skuQtyUnitTools;

    @Autowired
    private BatchStockBoxDetailRepository batchStockBoxDetailRepository;

    @Resource
    private BatchStockToolRepository batchStockToolRepository;

    @Resource
    private BatchBoxConfigMapper batchBoxConfigMapper;

    /**
     * 保存批次调整单(仓库盘点专用)
     * @param startDO
     * @param warehouseInventoryRecordE 业务类型：1-抽盘，2-全盘
     * @return 返回true表示批次实时处理，否则false不是实时处理，异步处理的
     */
    @Override
    public boolean saveInvetoryBatchAdjust(FrWarehouseInventoryStartDO startDO, WarehouseInventoryRecordE warehouseInventoryRecordE) {
        Integer businessType = warehouseInventoryRecordE.getBusinessType();
        List<WarehouseInventoryRecordDetailE> warehouseInventoryRecordDetailEList = warehouseInventoryRecordE.getFrontRecordDetails();
        List<Long> detailIdList = new ArrayList<>();
        List<Long> skuIdList = new ArrayList<>();
        List<BatchStockDO> ztStockBatch = new ArrayList<>();
        List<BatchStockBoxDetailDO>  boxBatchStockList = new ArrayList<>();
        //查询仓库信息
        RealWarehouse realWarehouse = realWarehouseService.findByRealWarehouseId(startDO.getRealWarehouseId());
        AlikAssert.isNotNull(realWarehouse, ResCode.STOCK_ERROR_4002, ResCode.STOCK_ERROR_4002_DESC);
        if(!BatchStockFacade.isSupportRealWarehouse(realWarehouse.getId(), realWarehouse.getRealWarehouseType())){
            return false;
        }
        //查询有效的明细信息
        List<FrWarehouseInventoryTempDO> inventoryTempList = frWarehouseInventoryTempRepository.queryAvailableDetailByFrontId(startDO.getId());
        if(CollectionUtils.isNotEmpty(inventoryTempList)) {
            for (FrWarehouseInventoryTempDO tempDO : inventoryTempList) {
                detailIdList.add(tempDO.getId());
                skuIdList.add(tempDO.getSkuId());
            }
            List<FrWhInventoryBatchTempDO> batchTempList = frWhInventoryBatchTempRepository.queryBatchByDetailIds(detailIdList);
            // 根据闯云和贾妮产品讨论结果，若盘点批次临时表，为空时不实时处理批次
            if(CollectionUtils.isEmpty(batchTempList)) {
                return false;
            }
            List<Long> rwIds = batchBoxConfigMapper.queryEnableConfigList();
            boolean openBox = rwIds.contains(startDO.getRealWarehouseId());
            //盘点单批次信息不为空
            if (businessType == 2) {
                //全盘(查询所有批次)
                ztStockBatch = batchStockRepository.queryBatchStockByRwId(startDO.getRealWarehouseId(), new ArrayList<>());
                if (openBox) {
                    boxBatchStockList = batchStockBoxDetailRepository.listByRwIdAndSkuList(startDO.getRealWarehouseId(), new ArrayList<>());
                }
            } else {
                //抽盘(需要指定sku查询批次)
                ztStockBatch = batchStockRepository.queryBatchStockByRwId(startDO.getRealWarehouseId(), skuIdList);
                if (openBox) {
                    boxBatchStockList = batchStockBoxDetailRepository.listByRwIdAndSkuList(startDO.getRealWarehouseId(), skuIdList);
                }
            }
            List<WmsBatchAdjustDTO> rwBatchList = this.compareAndDealBatch(ztStockBatch, batchTempList,warehouseInventoryRecordDetailEList);

            List<BoxBatchStockOpDTO> boxRwBatchList = null;
            if (openBox) {
                boxRwBatchList = this.compareAndDealBoxBatch(boxBatchStockList, batchTempList,warehouseInventoryRecordE);
            }
            //保存批次调整单
            if (CollectionUtils.isNotEmpty(rwBatchList) || CollectionUtils.isNotEmpty(boxRwBatchList)) {
                for (WmsBatchAdjustDTO wmsBatchAdjustDTO : rwBatchList) {
                    wmsBatchAdjustDTO.setFactoryCode(realWarehouse.getFactoryCode());
                    wmsBatchAdjustDTO.setOutWarehouseCode(realWarehouse.getRealWarehouseOutCode());
                    wmsBatchAdjustDTO.setOutRecordCode(startDO.getRecordCode());
                }
                this.saveBatchAdjust(rwBatchList,boxRwBatchList, BatchAdjustVO.BATCH_WAREHOUSE_INVETORY.getBusinessType());
            }else{
                log.error("盘点单[" + startDO.getRecordCode() + "]"+ResCode.STOCK_ERROR_1001_DESC + "中台和WMS批次一致, 无需处理批次信息");
            }

            // 实时处理批次了
            return true;
        }else{
            log.error("盘点单[" + startDO.getRecordCode() + "]"+ResCode.STOCK_ERROR_1001_DESC + "有效盘点单明细为空, 无需处理批次信息");
        }
        return false;
    }

    private List<BoxBatchStockOpDTO> compareAndDealBoxBatch(List<BatchStockBoxDetailDO> boxBatchStockList, List<FrWhInventoryBatchTempDO> batchTempList,WarehouseInventoryRecordE warehouseInventoryRecordE) {
        List<WarehouseInventoryRecordDetailE> warehouseInventoryRecordDetailEList = warehouseInventoryRecordE.getFrontRecordDetails();
        List<BoxBatchStockOpDTO> rwBatchList = new ArrayList<>();
        Map<String, FrWhInventoryBatchTempDO> wmsBatchMap = new HashMap<>(20);
        Map<String, BatchStockBoxDetailDO> ztBatchMap = new HashMap<>(20);
        for (FrWhInventoryBatchTempDO wmsBatchDo : batchTempList) {
            wmsBatchMap.put(wmsBatchDo.getSkuCode() + "_" +  wmsBatchDo.getBatchCode(), wmsBatchDo);
        }
        for (BatchStockBoxDetailDO batchStockDO : boxBatchStockList) {
            ztBatchMap.put(batchStockDO.getSkuCode() + "_" +  batchStockDO.getBatchCode(), batchStockDO);
        }
        Map<String, WarehouseInventoryRecordDetailE> skuCodeMap = RomeCollectionUtil.listforMap(warehouseInventoryRecordE.getFrontRecordDetails(), "skuCode");
        //对比中台批次和wms(按照批次号对比)
        for (String wmsBatchKey : wmsBatchMap.keySet()) {
            FrWhInventoryBatchTempDO wmsBatchDo = wmsBatchMap.get(wmsBatchKey);
            this.initWmsBatchDo(wmsBatchDo);
            BatchStockBoxDetailDO ztbatchStockDO = ztBatchMap.get(wmsBatchKey);
            WarehouseInventoryRecordDetailE warehouseInventoryRecordDetailE = skuCodeMap.get(wmsBatchDo.getSkuCode());
            // 如果存在:  对比数字
            BigDecimal ztBatckBoxQty = ztbatchStockDO != null? ztbatchStockDO.getBoxSkuQty():BigDecimal.ZERO;
            BigDecimal ztBatckMixQty =  ztbatchStockDO != null? ztbatchStockDO.getMixSkuQty():BigDecimal.ZERO;
            BigDecimal wmsBatchBoxQty = wmsBatchDo.getBoxQty();
            BigDecimal wmsBatchMixQty = wmsBatchDo.getMixQty();
            
            //分别计算原箱和非原箱库存
            this.calWmsRwBatch(warehouseInventoryRecordE, warehouseInventoryRecordDetailE,wmsBatchDo.getBatchCode(), wmsBatchDo.getProductDate(), wmsBatchBoxQty, ztBatckBoxQty, rwBatchList, 1);
            this.calWmsRwBatch(warehouseInventoryRecordE, warehouseInventoryRecordDetailE,wmsBatchDo.getBatchCode(), wmsBatchDo.getProductDate(), wmsBatchMixQty, ztBatckMixQty, rwBatchList, 0);
        }
        List<String> inventoryRecordDetailSkuCodes=warehouseInventoryRecordDetailEList.stream().map(WarehouseInventoryRecordDetailE::getSkuCode).collect(Collectors.toList());

        //找出库存中心存在  wms不存在的  做批次出库
        for (String ztBatchKey : ztBatchMap.keySet()) {
            FrWhInventoryBatchTempDO wmsbatchStockDO = wmsBatchMap.get(ztBatchKey);
            BatchStockBoxDetailDO ztBatchStockDo = ztBatchMap.get(ztBatchKey);
            if(!inventoryRecordDetailSkuCodes.contains(ztBatchStockDo.getSkuCode())){
                continue;
            }
            WarehouseInventoryRecordDetailE warehouseInventoryRecordDetailE = skuCodeMap.get(ztBatchStockDo.getSkuCode());
            if(wmsbatchStockDO == null) {
                // 如果存在:  对比数字
                BigDecimal ztBatchBoxQty = ztBatchStockDo.getBoxSkuQty();
                BigDecimal ztBatchMixQty = ztBatchStockDo.getMixSkuQty();
                BigDecimal wmsBatchBoxQty = BigDecimal.ZERO;
                BigDecimal wmsBatchMixQty = BigDecimal.ZERO;
                //分别计算原箱和非原箱库存
                this.calWmsRwBatch(warehouseInventoryRecordE, warehouseInventoryRecordDetailE,ztBatchStockDo.getBatchCode(), ztBatchStockDo.getProductDate(),  wmsBatchBoxQty, ztBatchBoxQty, rwBatchList, 1);
                this.calWmsRwBatch(warehouseInventoryRecordE, warehouseInventoryRecordDetailE, ztBatchStockDo.getBatchCode(), ztBatchStockDo.getProductDate(),  wmsBatchMixQty, ztBatchMixQty,  rwBatchList, 0);
            }
        }
        return rwBatchList;
    }

    private void initWmsBatchDo(FrWhInventoryBatchTempDO wmsBatchDo) {
        if (wmsBatchDo.getBoxQty() == null) {
            wmsBatchDo.setBoxQty(BigDecimal.ZERO);
        }
        if (wmsBatchDo.getMixQty() == null) {
            wmsBatchDo.setMixQty(BigDecimal.ZERO);
        }
        if (wmsBatchDo.getMixQty().compareTo(BigDecimal.ZERO) == 0 && wmsBatchDo.getBoxQty().compareTo(BigDecimal.ZERO) == 0) {
            wmsBatchDo.setMixQty(wmsBatchDo.getSkuQty());
        }
    }

    private void calWmsRwBatch( WarehouseInventoryRecordE warehouseInventoryRecordE, WarehouseInventoryRecordDetailE warehouseInventoryRecordDetailE, String batchCode, Date productDate, BigDecimal wmsBatchQty, BigDecimal ztBatckQty, List<BoxBatchStockOpDTO> rwBatchList, Integer boxType) {
        if(wmsBatchQty.compareTo(ztBatckQty) > 0){
            //wms批次数大于中台的,做益
            BoxBatchStockOpDTO boxBatchStockOpDTO = new BoxBatchStockOpDTO();
            boxBatchStockOpDTO.setSkuCode(warehouseInventoryRecordDetailE.getSkuCode());
            boxBatchStockOpDTO.setSkuId(warehouseInventoryRecordDetailE.getSkuId());
            boxBatchStockOpDTO.setRecordCode(warehouseInventoryRecordE.getRecordCode());
            boxBatchStockOpDTO.setRecordType(FrontRecordDetailStatusVO.WAREHOUSE_ADJUST_INCREASE.getStatus());
            boxBatchStockOpDTO.setRealWarehouseId(warehouseInventoryRecordE.getRealWarehouseId());
            boxBatchStockOpDTO.setSkuQty(wmsBatchQty.subtract(ztBatckQty).abs());
            boxBatchStockOpDTO.setBatchCode(batchCode);
            boxBatchStockOpDTO.setProductDate(productDate);
            boxBatchStockOpDTO.setBoxType(boxType);
            rwBatchList.add(boxBatchStockOpDTO);
        } else if(wmsBatchQty.compareTo(ztBatckQty) < 0){
            //wms批次数小于中台的,做损
            BoxBatchStockOpDTO boxBatchStockOpDTO = new BoxBatchStockOpDTO();
            boxBatchStockOpDTO.setSkuCode(warehouseInventoryRecordDetailE.getSkuCode());
            boxBatchStockOpDTO.setSkuId(warehouseInventoryRecordDetailE.getSkuId());
            boxBatchStockOpDTO.setRecordCode(warehouseInventoryRecordE.getRecordCode());
            boxBatchStockOpDTO.setRecordType(FrontRecordDetailStatusVO.WAREHOUSE_ADJUST_DISCREASE.getStatus());
            boxBatchStockOpDTO.setRealWarehouseId(warehouseInventoryRecordE.getRealWarehouseId());
            boxBatchStockOpDTO.setSkuQty(ztBatckQty.subtract(wmsBatchQty).abs());
            boxBatchStockOpDTO.setBatchCode(batchCode);
            boxBatchStockOpDTO.setProductDate(productDate);
            boxBatchStockOpDTO.setBoxType(boxType);
            rwBatchList.add(boxBatchStockOpDTO);
        }
    }


    @Override
    @Transactional(rollbackFor = Exception.class)
    public void saveBatchAdjust(List<WmsBatchAdjustDTO> rwBatchList, List<BoxBatchStockOpDTO> boxRwBatchList, Integer businessType) {
        if(CollectionUtils.isNotEmpty(rwBatchList)) {
            WmsBatchAdjustDTO headInfo  = rwBatchList.get(0);
            //幂等判断
            //查询前置单是否存在
            BatchAdjustRecordE batchAdjustRecordE = frBatchAdjustRepository.queryFrontRecordByOutCode(headInfo.getOutRecordCode());
            if (batchAdjustRecordE != null){
                return;
            }
            //合并重复批次
            Map<String, WmsBatchAdjustDTO> batchMap = new HashMap<>();
            if(CollectionUtils.isNotEmpty(rwBatchList)){
                for (WmsBatchAdjustDTO batchTempDO : rwBatchList) {
                    String key = batchTempDO.getSkuCode() +"_" + batchTempDO.getBatchCode();
                    WmsBatchAdjustDTO tempDo = batchMap.get(key);
                    if(tempDo != null){
                        tempDo.setSkuQty(tempDo.getSkuQty().add(batchTempDO.getSkuQty()));
                        batchMap.put(key, tempDo);
                    }else{
                        batchMap.put(key, batchTempDO);
                    }
                }
            }
            rwBatchList = new ArrayList<>(batchMap.values());
            RealWarehouse realWarehouse = realWarehouseService.findByRealWarehouseOutCodeAndFactoryCode(headInfo.getOutWarehouseCode(), headInfo.getFactoryCode());
            AlikAssert.isNotNull(realWarehouse, ResCode.STOCK_ERROR_9055,ResCode.STOCK_ERROR_9055_DESC);
            //创建前置单
            BatchAdjustRecordE adjustRecordE = entityFactory.createEntity(BatchAdjustRecordE.class);
            adjustRecordE.setBusinessType(businessType);
            adjustRecordE.setRealWarehouseId(realWarehouse.getId());
            adjustRecordE.setOutRecordCode(headInfo.getOutRecordCode());
            List<BatchAdjustRecordDetailE> frontDetails = frBatchAdjustConvertor.dtoToEntity(rwBatchList);
            this.setSkuId(frontDetails);
            adjustRecordE.setFrontRecordDetails(frontDetails);
            adjustRecordE.addFrontRecord();
            //处理前置单明细
            List<BatchAdjustRecordDetailE> outFrontDetail = new ArrayList<>();
            List<BatchAdjustRecordDetailE> inFrontDetail = new ArrayList<>();
            for (BatchAdjustRecordDetailE frontDetail : frontDetails) {
                if (BigDecimal.ZERO.compareTo(frontDetail.getSkuQty()) < 0) {
                    if (FrontRecordDetailStatusVO.WAREHOUSE_ADJUST_DISCREASE.getStatus().equals(frontDetail.getAdjustType())) {
                        outFrontDetail.add(frontDetail);
                    } else {
                        inFrontDetail.add(frontDetail);
                    }
                }
            }
            //根据前置单和明细生成出入库单
            if(CollectionUtils.isNotEmpty(outFrontDetail)){
                BatchAdjustWarehouseRecordE outWarehouseRecord = entityFactory.createEntity(BatchAdjustWarehouseRecordE.class);
                outWarehouseRecord.createOutRecordByFrontRecord(adjustRecordE, outFrontDetail);
                outWarehouseRecord.addWarehouseRecord();
                //保存批次信息
                outWarehouseRecord.saveRwBatch(outFrontDetail);
                //操作批次
                List<BatchStockOpDO> batchStockList = outWarehouseRecord.converToBatchStockInfo(outFrontDetail, realWarehouse.getId(), BatchStockTypeVO.STOCK_DECREASE.getType(), realWarehouse.getRealWarehouseType());
                BatchStockDataOpDO stockDO = new BatchStockDataOpDO();
                stockDO.setDetailDos(batchStockList);
                batchStockToolRepository.batchStockRealtime(stockDO);
            }
            if(CollectionUtils.isNotEmpty(inFrontDetail)){
                BatchAdjustWarehouseRecordE inWarehouseRecord = entityFactory.createEntity(BatchAdjustWarehouseRecordE.class);
                inWarehouseRecord.createInRecordByFrontRecord(adjustRecordE, inFrontDetail);
                inWarehouseRecord.addWarehouseRecord();
                //保存批次信息
                inWarehouseRecord.saveRwBatch(inFrontDetail);
                //操作批次
                List<BatchStockOpDO> batchStockList = inWarehouseRecord.converToBatchStockInfo(inFrontDetail, realWarehouse.getId(), BatchStockTypeVO.STOCK_INCREASE.getType(), realWarehouse.getRealWarehouseType());
                BatchStockDataOpDO stockDO = new BatchStockDataOpDO();
                stockDO.setDetailDos(batchStockList);
                batchStockToolRepository.batchStockRealtime(stockDO);
            }
        }
        //处理盘点单
        if (CollectionUtils.isNotEmpty(boxRwBatchList)) {
            List<BoxBatchStockOpDTO> increaseList = new ArrayList<>();
            List<BoxBatchStockOpDTO> decreaseList = new ArrayList<>();
            for (BoxBatchStockOpDTO wmsBatchAdjustDTO : boxRwBatchList) {
                if (Objects.equals(wmsBatchAdjustDTO.getRecordType(), FrontRecordDetailStatusVO.WAREHOUSE_ADJUST_INCREASE.getStatus())) {
                    wmsBatchAdjustDTO.setRecordType(WarehouseRecordTypeVO.BATCH_PROFIT_ADJUST_IN_RECORD.getType());
                    wmsBatchAdjustDTO.setStockType(BatchStockTypeVO.STOCK_INCREASE.getType());
                    increaseList.add(wmsBatchAdjustDTO);
                } else {
                    wmsBatchAdjustDTO.setRecordType(WarehouseRecordTypeVO.BATCH_PROFIT_ADJUST_OUT_RECORD.getType());
                    wmsBatchAdjustDTO.setStockType(BatchStockTypeVO.STOCK_DECREASE.getType());
                    decreaseList.add(wmsBatchAdjustDTO);
                }
            }
            batchStockBoxDetailRepository.baseOperate(increaseList,decreaseList,null,null);
        }

    }

    /**
     * 对比中台和wms批次差异并创建rwbatch
     * @param ztStockBatch
     * @param batchTempList
     * @return
     */
    private List<WmsBatchAdjustDTO> compareAndDealBatch(List<BatchStockDO> ztStockBatch, List<FrWhInventoryBatchTempDO> batchTempList
            ,List<WarehouseInventoryRecordDetailE> warehouseInventoryRecordDetailEList) {
        List<WmsBatchAdjustDTO> rwBatchList = new ArrayList<>();
        Map<String, FrWhInventoryBatchTempDO> wmsBatchMap = new HashMap<>(20);
        Map<String, BatchStockDO> ztBatchMap = new HashMap<>(20);
        for (FrWhInventoryBatchTempDO wmsBatchDo : batchTempList) {
            wmsBatchMap.put(wmsBatchDo.getSkuCode() + "_" +  wmsBatchDo.getBatchCode(), wmsBatchDo);
        }
        for (BatchStockDO batchStockDO : ztStockBatch) {
            ztBatchMap.put(batchStockDO.getSkuCode() + "_" +  batchStockDO.getBatchCode(), batchStockDO);
        }
        //对比中台批次和wms(按照批次号对比)
        for (String wmsBatchKey : wmsBatchMap.keySet()) {
            FrWhInventoryBatchTempDO wmsBatchDo = wmsBatchMap.get(wmsBatchKey);
            BatchStockDO ztbatchStockDO = ztBatchMap.get(wmsBatchKey);
            if(ztbatchStockDO != null){
                // 如果存在:  对比数字
                BigDecimal ztBatckQty = ztbatchStockDO.getSkuQty();
                BigDecimal wmsBatchQty = wmsBatchDo.getSkuQty();
                if(wmsBatchQty.compareTo(ztBatckQty) == 1){
                    //wms批次数大于中台的,做益
                    WmsBatchAdjustDTO wmsBatchAdjustDTO = new WmsBatchAdjustDTO();
                    wmsBatchAdjustDTO.setSkuCode(wmsBatchDo.getSkuCode());
                    wmsBatchAdjustDTO.setSkuQty(wmsBatchQty.subtract(ztBatckQty).abs());
                    wmsBatchAdjustDTO.setBatchCode(wmsBatchDo.getBatchCode());
                    wmsBatchAdjustDTO.setProductDate(wmsBatchDo.getProductDate());
                    wmsBatchAdjustDTO.setAdjustType(FrontRecordDetailStatusVO.WAREHOUSE_ADJUST_INCREASE.getStatus());
                    rwBatchList.add(wmsBatchAdjustDTO);
                } else if(wmsBatchQty.compareTo(ztBatckQty) == -1){
                    //wms批次数小于中台的,做损
                    WmsBatchAdjustDTO wmsBatchAdjustDTO = new WmsBatchAdjustDTO();
                    wmsBatchAdjustDTO.setSkuCode(wmsBatchDo.getSkuCode());
                    wmsBatchAdjustDTO.setSkuQty(ztBatckQty.subtract(wmsBatchQty).abs());
                    wmsBatchAdjustDTO.setBatchCode(wmsBatchDo.getBatchCode());
                    wmsBatchAdjustDTO.setProductDate(wmsBatchDo.getProductDate());
                    wmsBatchAdjustDTO.setAdjustType(FrontRecordDetailStatusVO.WAREHOUSE_ADJUST_DISCREASE.getStatus());
                    rwBatchList.add(wmsBatchAdjustDTO);
                }
            }else{
                if(wmsBatchDo != null) {
                    //如果不存在: 做批次入库
                    WmsBatchAdjustDTO wmsBatchAdjustDTO = new WmsBatchAdjustDTO();
                    wmsBatchAdjustDTO.setSkuCode(wmsBatchDo.getSkuCode());
                    wmsBatchAdjustDTO.setSkuQty(wmsBatchDo.getSkuQty().abs());
                    wmsBatchAdjustDTO.setBatchCode(wmsBatchDo.getBatchCode());
                    wmsBatchAdjustDTO.setProductDate(wmsBatchDo.getProductDate());
                    wmsBatchAdjustDTO.setAdjustType(FrontRecordDetailStatusVO.WAREHOUSE_ADJUST_INCREASE.getStatus());
                    rwBatchList.add(wmsBatchAdjustDTO);
                }
            }
        }
        List<String> inventoryRecordDetailSkuCodes=warehouseInventoryRecordDetailEList.stream().map(WarehouseInventoryRecordDetailE::getSkuCode).collect(Collectors.toList());
        //找出库存中心存在  wms不存在的  做批次出库
        for (String ztBatchKey : ztBatchMap.keySet()) {
            FrWhInventoryBatchTempDO wmsbatchStockDO = wmsBatchMap.get(ztBatchKey);
            BatchStockDO ztBatchStockDo = ztBatchMap.get(ztBatchKey);
            if(!inventoryRecordDetailSkuCodes.contains(ztBatchStockDo.getSkuCode())){
                continue;
            }
            if(wmsbatchStockDO == null){
                //数量为0的跳过
                WmsBatchAdjustDTO wmsBatchAdjustDTO = new WmsBatchAdjustDTO();
                if(BigDecimal.ZERO.compareTo(ztBatchStockDo.getSkuQty()) == 0){
                    continue;
                }else if(ztBatchStockDo.getSkuQty().compareTo(BigDecimal.ZERO) == 1){
                    //大于0
                    wmsBatchAdjustDTO.setAdjustType(FrontRecordDetailStatusVO.WAREHOUSE_ADJUST_DISCREASE.getStatus());
                }else{
                    //小于0
                    wmsBatchAdjustDTO.setAdjustType(FrontRecordDetailStatusVO.WAREHOUSE_ADJUST_INCREASE.getStatus());
                }
                wmsBatchAdjustDTO.setSkuCode(ztBatchStockDo.getSkuCode());
                wmsBatchAdjustDTO.setSkuQty(ztBatchStockDo.getSkuQty().abs());
                wmsBatchAdjustDTO.setBatchCode(ztBatchStockDo.getBatchCode());
                wmsBatchAdjustDTO.setProductDate(ztBatchStockDo.getProductDate());
                rwBatchList.add(wmsBatchAdjustDTO);
            }
        }
        return rwBatchList;
    }


    /**
     * 设置基础单位
     * @param frontDetails
     */
    private void setSkuId(List<BatchAdjustRecordDetailE> frontDetails){
        List<String> skuCodeList = RomeCollectionUtil.getValueList(frontDetails, "skuCode");
        List<SkuInfoExtDTO> skuInfoList = skuFacade.skusBySkuCode(skuCodeList);
        Map<String, SkuInfoExtDTO> skuMap = RomeCollectionUtil.listforMap(skuInfoList, "skuCode");
        for (BatchAdjustRecordDetailE frontDetail : frontDetails) {
            SkuInfoExtDTO skuInfo = skuMap.get(frontDetail.getSkuCode());
            if(skuInfo != null){
                frontDetail.setSkuId(skuInfo.getId());
                frontDetail.setValidity(skuInfo.getTotalShelfLife());
                frontDetail.setUnit(skuInfo.getSpuUnitName());
                frontDetail.setUnitCode(skuInfo.getSpuUnitCode());
            }
        }
    }
}
