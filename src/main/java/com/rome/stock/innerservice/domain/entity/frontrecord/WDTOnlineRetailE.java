package com.rome.stock.innerservice.domain.entity.frontrecord;

import com.rome.arch.core.domain.EntityFactory;
import com.rome.stock.common.constants.WmsConfigConstants;
import com.rome.stock.common.enums.warehouse.WarehouseRecordTypeVO;
import com.rome.stock.innerservice.api.dto.frontrecord.StockOrderDTO;
import com.rome.stock.innerservice.common.ResCode;
import com.rome.stock.innerservice.common.RomeCollectionUtil;
import com.rome.stock.innerservice.constant.*;
import com.rome.stock.innerservice.domain.convertor.AddressConvertor;
import com.rome.stock.innerservice.domain.convertor.frontrecord.FrWDTSaleConvertor;
import com.rome.stock.innerservice.domain.entity.AddressE;
import com.rome.stock.innerservice.domain.entity.RwRecordPoolE;
import com.rome.stock.innerservice.domain.repository.AddressRepository;
import com.rome.stock.innerservice.domain.repository.RealWarehouseRepository;
import com.rome.stock.innerservice.domain.repository.RwRecordPoolRepository;
import com.rome.stock.innerservice.domain.repository.frontrecord.FrSaleRepository;
import com.rome.stock.innerservice.domain.repository.frontrecord.FrWDTSaleRepository;
import com.rome.stock.innerservice.domain.service.impl.FulfillmentJobServiceImpl;
import com.rome.stock.innerservice.infrastructure.dataobject.RealWarehouseWmsConfigDO;
import com.rome.stock.core.infrastructure.dataobject.core.CoreVirtualStockOpDO;
import com.rome.stock.innerservice.infrastructure.mapper.RealWarehouseWmsConfigMapper;
import com.rome.stock.innerservice.remote.item.facade.SkuFacade;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.*;

/**
 * 电商销售单
 */
@Component
@Scope("prototype")
@Data
@Slf4j
@EqualsAndHashCode(callSuper = false)
public class WDTOnlineRetailE extends AbstractFrontRecord {

    @Autowired
    private FrWDTSaleRepository frWDTSaleRepository;
    @Autowired
    private EntityFactory entityFactory;
    @Autowired
    private RwRecordPoolRepository rwRecordPoolRepository;
    @Autowired
    private AddressRepository addressRepository;

    @Autowired
    private AddressConvertor addressConvertor;
    @Resource
    private FrWDTSaleConvertor frWDTSaleConvertor;
    @Resource
    private FulfillmentJobServiceImpl fulfillmentJobService;

    @Resource
    private RealWarehouseWmsConfigMapper realWarehouseWmsConfigMapper;

    public OnlineRetailE convertToNormalOnlineRetailE(){
        return frWDTSaleConvertor.convertToNormalOnlineRetailE(this);
    }

    /**
     * 初始化前置单
     */
    public void initFrontRecord(StockOrderDTO stockOrderDTO) {
        initFrontRecord(FrontRecordTypeVO.WDT_ONLINE_SALE_RECORD.getCode(), this.frontRecordDetails, stockOrderDTO.getMerchantId());
        this.setRecordStatus(FrontRecordStatusVO.SO_PAID.getStatus());
        this.setRecordType(FrontRecordTypeVO.WDT_ONLINE_SALE_RECORD.getType());

    }

    public List<WDTOnlineRetailRecordDetailE> selectDetailByIdForSplit(){
        return frWDTSaleRepository.selectDetailByIdForSplit(getId());
    }

    /**
     * 添加前置单
     */
    public void addFrontRecord() {
        //插入前置单数据
        long id = frWDTSaleRepository.saveSaleRecord(this);
        this.setId(id);
        //电商零售详情关联数据
        this.frontRecordDetails.forEach(detail -> {
            detail.setFrontRecordDetail(this);
            detail.setSplitStatus(WDTRecordConst.WDT_ALLOT_INIT);
        });
        //保存电商零售前置单明细
        frWDTSaleRepository.saveFrSaleRecordDetails(this.frontRecordDetails);
    }

    /**
     * 根据id更新明细状态为已拆单完成
     */
    public void updateToHasSplitForDetailsByRecordId(){
        frWDTSaleRepository.updateToHasSplitForDetailsByRecordId(getId());
    }

    /**
     * 根据明细id集合更新明细状态为已拆单完成
     */
    public void updateToHasSplitForDetailsByDetailIds(List<Long> ids){
        frWDTSaleRepository.updateToHasSplitForDetailsByDetailIds(ids);
    }

    /**
     * 更新单据拆单状态为已完成
     */
    public void updateToHasSplitForOrder(){
        frWDTSaleRepository.updateToHasSplitForOrder(getId());
    }
    public RwRecordPoolE createDo(boolean needCombine ,Long userId ,String doCode){
        RwRecordPoolE poolE = entityFactory.createEntity(RwRecordPoolE.class);
        poolE.setRecordType(WarehouseRecordTypeVO.POOL_DO_RECORD.getType());
        poolE.setDoCode(doCode == null ? getOrderUtilService().queryOrderCode(WarehouseRecordTypeVO.POOL_DO_RECORD.getCode()): doCode);
        poolE.setFrontRecordId(this.getId());
        poolE.setFrontRecordCode(this.getRecordCode());
        poolE.setChannelCode(this.getChannelCode());
        poolE.setRealWarehouseId(this.getRealWarehouseId());
        poolE.setVirtualWarehouseId(this.getVirtualWarehouseId());
        poolE.setMerchantId(this.getMerchantId());
        if (userId != null) {
            poolE.setCreator(userId);
            poolE.setModifier(userId);
        }
        //待合单
        poolE.setRecordStatus(RwRecordPoolStatusVO.PRE_MERGE.getStatus());
        //用户信息，用于计算指纹
        poolE.setUserCode(this.getUserCode());
        //计算指纹信息
        //地址信息保存在do上,从so获取，后面有可能修改do的地址信息
        AddressE addr = addressRepository.queryByRecordCode(this.getRecordCode());
        addr.setRecordCode(poolE.getDoCode());
        addr.addAddress();
        poolE.setNeedCombine(needCombine ? 1 : 0);
        poolE.setLogisticsCode(this.getLogisticsCode());
        //需要推送将do单推送交易
        poolE.setSyncStatus(1);
        poolE.bindMergeFingerprint(addr);
        long id = rwRecordPoolRepository.addRecordPool(poolE);
        poolE.setId(id);
        if(this.frontRecordDetails == null){
            this.frontRecordDetails = frWDTSaleRepository.querySaleRecordDetailsById(this.getId());
        }
        this.getSkuQtyUnitTools().convertRealToBasic(frontRecordDetails,merchantId);
        poolE.warpRwRecordPoolDetail(this.frontRecordDetails);
        rwRecordPoolRepository.addAllRecordPoolSku(poolE.getRwRecordPoolDetails());
        return poolE;
    }
    public RwRecordPoolE createDo(boolean needCombine ,Long userId){
        return  createDo(needCombine,userId, null);
    }

    public int  saveLogisticsCode( String logisticsCode){
        this.setLogisticsCode(logisticsCode);
        return frWDTSaleRepository.saveLogisticsCode(getId() , logisticsCode);
    }

    /**
     * 修改单据信息,重新寻源用
     * @return
     */
    public int  updateOrderForRecalHouse( ){
        return frWDTSaleRepository.updateOrderForRecalHouse(this);
    }

    /**
     * 修改单据信息,改仓和物流公司用
     * @return
     */
    public int  updateOrderForChangeHouse( ){
        return frWDTSaleRepository.updateOrderForChangeHouse(this);
    }


    @Resource
    private RealWarehouseRepository realWarehouseRepository;



    /**
     * 更新前置单状态为已取消
     */
    public int updateToCanceled() {
        return frWDTSaleRepository.updateToCanceled(this.getId());
    }

    public int updateVersion(Integer version) {
        return frWDTSaleRepository.updateVersion(getId() ,version);
    }

    /**
     * 更新前置单状态为已出库
     */
    public int updateToOutAllocation() {
        return frWDTSaleRepository.updateToOutAllocation(this.getId());
    }

    //渠道code
    private String channelCode;
    //门店编号
    private String shopCode;
    //出向实体仓库id
    private Long realWarehouseId;
    //出向实体仓库id
    private String realWarehouseCode;
    //用户手机号
    private String mobile;
    //用户code
    private String userCode;
    //交易类型
    private Integer transType;
    //商家ID
    private Long merchantId;

    //sku数量及明细
    private List<WDTOnlineRetailRecordDetailE> frontRecordDetails;
    //Do池数据
    private List<RwRecordPoolE> rwRecordPoolEList;
    /**
     * 支付时间
     */
    private Date payTime;

    /**
     * 拆单状态：0 正常单 1异常单 需要拆单 2超卖订单
     */
    private Integer splitType;
    /**
     * 分配状态：0 还需分配  1无需再分配（正常单或拆单结束）
     */
    private Integer allotStatus;

    /**
     * 虚拟仓库id
     */
    private Long virtualWarehouseId;
    /**
     * 物流公司编码
     */
    private String logisticsCode;
    /**
     * 原始订单号
     */
    private  String originOrderCode;

    /**
     * 是否预售
     */
    private Integer isPreSale;

    private String oaid;
}
