package com.rome.stock.innerservice.domain.entity.warehouserecord;

import com.rome.arch.core.domain.EntityFactory;
import com.rome.stock.common.enums.warehouse.WarehouseRecordTypeVO;
import com.rome.stock.innerservice.common.SkuQtyUnitTools;
import com.rome.stock.innerservice.constant.WarehouseRecordBusinessTypeVO;

import com.rome.stock.innerservice.constant.WmsSyncStatusVO;
import com.rome.stock.innerservice.domain.entity.frontrecord.InventoryAdjustRecordDetailE;
import com.rome.stock.innerservice.domain.entity.frontrecord.InventoryAdjustRecordE;
import com.rome.stock.innerservice.domain.repository.warehouserecord.InventoryAdjustWarehouseRecordRepository;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * 损益调整出入库单
 */
@Data
@Slf4j
@Component
@Scope("prototype")
@EqualsAndHashCode(callSuper = false)
public class InventoryAdjustWarehouseRecordE extends AbstractWarehouseRecord{

    @Autowired
    private EntityFactory entityFactory;

    @Autowired
    private InventoryAdjustWarehouseRecordRepository inventoryAdjustWarehouseRecordRepository;
    @Resource
    private SkuQtyUnitTools skuQtyUnitTools;

    /**
     * 创建仓库单据
     */
    public void addWarehouseRecord() {
        long id = inventoryAdjustWarehouseRecordRepository.saveWarehouseRecord(this);
        this.setId(id);
        this.warehouseRecordDetails.forEach(record -> record.setWarehouseRecordDetail(this));
        inventoryAdjustWarehouseRecordRepository.saveWarehouseRecordDetails(this.warehouseRecordDetails);
        this.addFrontRecordAndWarehouseRelation();
    }
    /**
     * 根据前置单生成入库单
     * @param inventoryAdjustRecordE
     */
    public void createInRecordByFrontRecord(InventoryAdjustRecordE inventoryAdjustRecordE) {
        createRecodeCode(WarehouseRecordTypeVO.INVENTORY_PROFIT_ADJUST_IN_RECORD.getCode());
        this.setAbstractFrontRecord(inventoryAdjustRecordE);
        this.setRealWarehouseId(inventoryAdjustRecordE.getRealWarehouseId());
        this.setBusinessType(WarehouseRecordBusinessTypeVO.IN_WAREHOUSE_RECORD.getType());
        this.setRecordType(WarehouseRecordTypeVO.INVENTORY_PROFIT_ADJUST_IN_RECORD.getType());
        this.setMerchantId(inventoryAdjustRecordE.getMerchantId());
        if(null == inventoryAdjustRecordE.getOutCreateTime()){
            this.setOutCreateTime(new Date());
        }else {
            this.setOutCreateTime(inventoryAdjustRecordE.getOutCreateTime());
        }
        this.setSyncWmsStatus(WmsSyncStatusVO.NO_REQUIRED.getStatus());
        List<InventoryAdjustRecordDetailE> frontRecordDetails = inventoryAdjustRecordE.getFrontRecordDetails();
        inventoryAdjustRecordE.getFrontRecordDetails();
        this.setWarehouseRecordDetails(new ArrayList<>(frontRecordDetails.size()));
        //单位换算处理
        frontRecordDetails.forEach(wrRecord -> wrRecord.setBasicSkuQty(wrRecord.getSkuQty()));
        skuQtyUnitTools.convertRealToBasic(frontRecordDetails);
        if(frontRecordDetails!=null){
            for(InventoryAdjustRecordDetailE detailE:frontRecordDetails){
                WarehouseRecordDetail warehouseRecordDetail=entityFactory.createEntity(WarehouseRecordDetail.class);
                warehouseRecordDetail.createRecordDetailByFrontRecord(detailE);
                //库存调整，计划入库数量与实际入库数量是一致的
                warehouseRecordDetail.setActualQty(detailE.getBasicSkuQty());
                warehouseRecordDetail.setPlanQty(detailE.getBasicSkuQty());
                this.warehouseRecordDetails.add(warehouseRecordDetail);
            }
            //设置skuCode和skuID
            this.initWarehouseRecodeDetail();
        }
    }

    /**
     * 根据前置单生成出库单
     * @param inventoryAdjustRecordE
     */
    public void createOutRecordByFrontRecord(InventoryAdjustRecordE inventoryAdjustRecordE) {
        createRecodeCode(WarehouseRecordTypeVO.INVENTORY_PROFIT_ADJUST_OUT_RECORD.getCode());
        this.setAbstractFrontRecord(inventoryAdjustRecordE);
        this.setRealWarehouseId(inventoryAdjustRecordE.getRealWarehouseId());
        this.setBusinessType(WarehouseRecordBusinessTypeVO.OUT_WAREHOUSE_RECORD.getType());
        this.setRecordType(WarehouseRecordTypeVO.INVENTORY_PROFIT_ADJUST_OUT_RECORD.getType());
        this.setMerchantId(inventoryAdjustRecordE.getMerchantId());
        if(null == inventoryAdjustRecordE.getOutCreateTime()){
            this.setOutCreateTime(new Date());
        }else {
            this.setOutCreateTime(inventoryAdjustRecordE.getOutCreateTime());
        }
        this.setSyncWmsStatus(WmsSyncStatusVO.NO_REQUIRED.getStatus());
        List<InventoryAdjustRecordDetailE> frontRecordDetails = inventoryAdjustRecordE.getFrontRecordDetails();
        inventoryAdjustRecordE.getFrontRecordDetails();
        this.setWarehouseRecordDetails(new ArrayList<>(frontRecordDetails.size()));
        //单位换算处理
        frontRecordDetails.forEach(wrRecord -> wrRecord.setBasicSkuQty(wrRecord.getSkuQty()));
        skuQtyUnitTools.convertRealToBasic(frontRecordDetails);
        if(frontRecordDetails!=null){
            for(InventoryAdjustRecordDetailE detailE:frontRecordDetails){
                WarehouseRecordDetail warehouseRecordDetail=entityFactory.createEntity(WarehouseRecordDetail.class);
                warehouseRecordDetail.createRecordDetailByFrontRecord(detailE);
                //库存调整，计划入库数量与实际入库数量是一致的
                warehouseRecordDetail.setActualQty(detailE.getBasicSkuQty());
                warehouseRecordDetail.setPlanQty(detailE.getBasicSkuQty());
                this.warehouseRecordDetails.add(warehouseRecordDetail);
            }
            //设置skuCode和skuID
            this.initWarehouseRecodeDetail();
        }
    }

    /**
     * @Description: 创建包材出库单 <br>
     *
     * <AUTHOR> 2019/11/15
     * @param inventoryAdjustRecordE
     * @return 
     */
    public void createPMORRecordByFrontRecord(InventoryAdjustRecordE inventoryAdjustRecordE) {
        createRecodeCode(WarehouseRecordTypeVO.PACKAGE_MATERIAL_OUT_RECORD.getCode());
        this.setAbstractFrontRecord(inventoryAdjustRecordE);
        this.setRealWarehouseId(inventoryAdjustRecordE.getRealWarehouseId());
        this.setBusinessType(WarehouseRecordBusinessTypeVO.OUT_WAREHOUSE_RECORD.getType());
        this.setRecordType(WarehouseRecordTypeVO.PACKAGE_MATERIAL_OUT_RECORD.getType());
        this.setMerchantId(inventoryAdjustRecordE.getMerchantId());
        if(null == inventoryAdjustRecordE.getOutCreateTime()){
            this.setOutCreateTime(new Date());
        }else {
            this.setOutCreateTime(inventoryAdjustRecordE.getOutCreateTime());
        }
        this.setSyncWmsStatus(WmsSyncStatusVO.NO_REQUIRED.getStatus());
        List<InventoryAdjustRecordDetailE> frontRecordDetails = inventoryAdjustRecordE.getFrontRecordDetails();
        inventoryAdjustRecordE.getFrontRecordDetails();
        this.setWarehouseRecordDetails(new ArrayList<>(frontRecordDetails.size()));
        //单位换算处理
        frontRecordDetails.forEach(wrRecord -> wrRecord.setBasicSkuQty(wrRecord.getSkuQty()));
        skuQtyUnitTools.convertRealToBasic(frontRecordDetails);
        if(frontRecordDetails!=null){
            for(InventoryAdjustRecordDetailE detailE:frontRecordDetails){
                WarehouseRecordDetail warehouseRecordDetail=entityFactory.createEntity(WarehouseRecordDetail.class);
                warehouseRecordDetail.createRecordDetailByFrontRecord(detailE);
                //库存调整，计划入库数量与实际入库数量是一致的
                warehouseRecordDetail.setActualQty(detailE.getBasicSkuQty());
                warehouseRecordDetail.setPlanQty(detailE.getBasicSkuQty());
                this.warehouseRecordDetails.add(warehouseRecordDetail);
            }
            //设置skuCode和skuID
            this.initWarehouseRecodeDetail();
        }
    }

    /**
     * @Description: 创建包材入库单 <br>
     *
     * <AUTHOR> 2019/11/19
     * @param inventoryAdjustRecordE
     * @return 
     */
    public void createPMIRRecordByFrontRecord(InventoryAdjustRecordE inventoryAdjustRecordE) {
        createRecodeCode(WarehouseRecordTypeVO.PACKAGE_MATERIAL_IN_RECORD.getCode());
        this.setAbstractFrontRecord(inventoryAdjustRecordE);
        this.setRealWarehouseId(inventoryAdjustRecordE.getRealWarehouseId());
        this.setBusinessType(WarehouseRecordBusinessTypeVO.IN_WAREHOUSE_RECORD.getType());
        this.setRecordType(WarehouseRecordTypeVO.PACKAGE_MATERIAL_IN_RECORD.getType());
        this.setMerchantId(inventoryAdjustRecordE.getMerchantId());
        if(null == inventoryAdjustRecordE.getOutCreateTime()){
            this.setOutCreateTime(new Date());
        }else {
            this.setOutCreateTime(inventoryAdjustRecordE.getOutCreateTime());
        }
        this.setSyncWmsStatus(WmsSyncStatusVO.NO_REQUIRED.getStatus());
        List<InventoryAdjustRecordDetailE> frontRecordDetails = inventoryAdjustRecordE.getFrontRecordDetails();
        inventoryAdjustRecordE.getFrontRecordDetails();
        this.setWarehouseRecordDetails(new ArrayList<>(frontRecordDetails.size()));
        //单位换算处理
        frontRecordDetails.forEach(wrRecord -> wrRecord.setBasicSkuQty(wrRecord.getSkuQty()));
        skuQtyUnitTools.convertRealToBasic(frontRecordDetails);
        if(frontRecordDetails!=null){
            for(InventoryAdjustRecordDetailE detailE:frontRecordDetails){
                WarehouseRecordDetail warehouseRecordDetail=entityFactory.createEntity(WarehouseRecordDetail.class);
                warehouseRecordDetail.createRecordDetailByFrontRecord(detailE);
                //库存调整，计划入库数量与实际入库数量是一致的
                warehouseRecordDetail.setActualQty(detailE.getBasicSkuQty());
                warehouseRecordDetail.setPlanQty(detailE.getBasicSkuQty());
                this.warehouseRecordDetails.add(warehouseRecordDetail);
            }
            //设置skuCode和skuID
            this.initWarehouseRecodeDetail();
        }
    }

    /**
     * @Description: 构造门店退货仓库出入库单数据 <br>
     *
     * <AUTHOR> 2020/3/25
     * @param inventoryAdjustRecordE
     * @return 
     */
    public void createSRWRecordByFrontRecord(InventoryAdjustRecordE inventoryAdjustRecordE, WarehouseRecordTypeVO recordTypeVO) {
        createRecodeCode(recordTypeVO.getCode());
        this.setAbstractFrontRecord(inventoryAdjustRecordE);
        this.setRealWarehouseId(inventoryAdjustRecordE.getRealWarehouseId());
        this.setBusinessType(recordTypeVO.getBusinessType());
        this.setRecordType(recordTypeVO.getType());
        this.setMerchantId(inventoryAdjustRecordE.getMerchantId());
        if(null == inventoryAdjustRecordE.getOutCreateTime()){
            this.setOutCreateTime(new Date());
        }else {
            this.setOutCreateTime(inventoryAdjustRecordE.getOutCreateTime());
        }
        this.setSyncWmsStatus(WmsSyncStatusVO.NO_REQUIRED.getStatus());
        List<InventoryAdjustRecordDetailE> frontRecordDetails = inventoryAdjustRecordE.getFrontRecordDetails();
        inventoryAdjustRecordE.getFrontRecordDetails();
        this.setWarehouseRecordDetails(new ArrayList<>(frontRecordDetails.size()));
        //单位换算处理
        frontRecordDetails.forEach(wrRecord -> wrRecord.setBasicSkuQty(wrRecord.getSkuQty()));
        skuQtyUnitTools.convertRealToBasic(frontRecordDetails);
        if(frontRecordDetails!=null){
            for(InventoryAdjustRecordDetailE detailE:frontRecordDetails){
                WarehouseRecordDetail warehouseRecordDetail=entityFactory.createEntity(WarehouseRecordDetail.class);
                warehouseRecordDetail.createRecordDetailByFrontRecord(detailE);
                //库存调整，计划入库数量与实际入库数量是一致的
                warehouseRecordDetail.setActualQty(detailE.getBasicSkuQty());
                warehouseRecordDetail.setPlanQty(detailE.getBasicSkuQty());
                this.warehouseRecordDetails.add(warehouseRecordDetail);
            }
            //设置skuCode和skuID
            this.initWarehouseRecodeDetail();
        }
    }

    /**
     * @Description: 构造门店退货门店出入库单数据 <br>
     *
     * <AUTHOR> 2020/3/25
     * @param shopWId
     * @return 
     */
    public void createSRSRecordByFrontRecord(Long shopWId, WarehouseRecordTypeVO recordTypeVO) {
        createRecodeCode(recordTypeVO.getCode());
        this.setRealWarehouseId(shopWId);
        this.setBusinessType(recordTypeVO.getBusinessType());
        this.setRecordType(recordTypeVO.getType());
    }

}
