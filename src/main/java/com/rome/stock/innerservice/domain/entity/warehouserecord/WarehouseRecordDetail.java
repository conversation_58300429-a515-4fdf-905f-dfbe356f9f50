package com.rome.stock.innerservice.domain.entity.warehouserecord;

import com.rome.arch.core.domain.EntityFactory;
import com.rome.stock.innerservice.api.dto.BatchStockDTO;
import com.rome.stock.innerservice.common.StringUtils;
import com.rome.stock.innerservice.domain.entity.SkuQtyUnitBaseE;
import com.rome.stock.innerservice.domain.entity.frontrecord.AbstractFrontRecordDetail;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * 类WarehouseRecordDetail的实现描述：出入库单详情
 *
 * <AUTHOR> 2019/4/17 21:27
 */
@Data
@Slf4j
@Component
@Scope("prototype")
@EqualsAndHashCode(callSuper = false)
public class WarehouseRecordDetail extends SkuQtyUnitBaseE {
    @Resource
    private EntityFactory entityFactory;

    /**
     * 详情放入仓库单关联数据
     */
    public void setWarehouseRecordDetail(AbstractWarehouseRecord abstractWarehouseRecord){
        this.setWarehouseRecordId(abstractWarehouseRecord.getId());
        this.setRecordCode(abstractWarehouseRecord.getRecordCode());
    }

    /**
     * 根据前置单生成单据明细
     */
    public void createRecordDetailByFrontRecord(AbstractFrontRecordDetail frontRecordDetail){
        this.setSkuId(frontRecordDetail.getSkuId());
        this.setSkuCode(frontRecordDetail.getSkuCode());
        //此处设置计划数量和实际数量一致，wms回调再更新实际数量，无需wms回调的业务，直接设置成一致即可
        this.setPlanQty(frontRecordDetail.getBasicSkuQty());
        this.setUnit(frontRecordDetail.getBasicUnit());
        this.setUnitCode(frontRecordDetail.getBasicUnitCode());
    }

    /**
     * 根据前置单生成单据明细
     */
    public void createRecordDetailByReturnFrontRecord(AbstractFrontRecordDetail frontRecordDetail){
        this.setSkuId(frontRecordDetail.getSkuId());
        this.setSkuCode(frontRecordDetail.getSkuCode());
        //此处设置计划数量和实际数量一致，wms回调再更新实际数量，无需wms回调的业务，直接设置成一致即可
        this.setPlanQty(frontRecordDetail.getSkuQty());
        this.setActualQty(frontRecordDetail.getSkuQty());
        if(StringUtils.isNotBlank(frontRecordDetail.getUnit())) {
            this.setUnit(frontRecordDetail.getUnit());
        } else {
            this.setUnit("");
        }
        if(StringUtils.isNotBlank(frontRecordDetail.getUnitCode())) {
            this.setUnitCode(frontRecordDetail.getUnitCode());
        } else {
            this.setUnitCode("");
        }
    }

    /**
     * 根据前置单生成单据明细(实际出库数就是计划数)
     */
    public void createRecordDetailByActualFrontRecord(AbstractFrontRecordDetail frontRecordDetail){
        this.setSkuId(frontRecordDetail.getSkuId());
        this.setSkuCode(frontRecordDetail.getSkuCode());
        this.setPlanQty(frontRecordDetail.getBasicSkuQty());
        this.setActualQty(frontRecordDetail.getBasicSkuQty());
        this.setUnit(frontRecordDetail.getBasicUnit());
        this.setUnitCode(frontRecordDetail.getBasicUnitCode());
    }


    /**
     * 根据出入库单明细生成单据明细
     */
    public void createRecordDetail(WarehouseRecordDetail warehouseRecordDetail){
        this.setSkuId(warehouseRecordDetail.getSkuId());
        this.setSkuCode(warehouseRecordDetail.getSkuCode());
        this.setPlanQty(warehouseRecordDetail.getPlanQty());
        this.setActualQty(warehouseRecordDetail.getActualQty());
        this.setUnit(warehouseRecordDetail.getUnit());
        this.setUnitCode(warehouseRecordDetail.getUnitCode());
    }

    /**
     * 所属单据编码
     */
    private String recordCode;

    /**
     * 所属单据id
     */
    private Long warehouseRecordId;

    /**
     * 商品名称
     */
    private String skuName;


    private String skuPicPath;

    private String skuCname;

    private String skuEname;

    /**
     *  商品数量
     */
    private BigDecimal planQty;

    /**
     * 实际收货数量 包括待质检的
     */
    private BigDecimal actualQty;

    public BigDecimal getActualQty(){
        return this.actualQty;
    }

    public void setActualQty(BigDecimal actualQty){
        this.actualQty=actualQty;
    }

    /**
     * sap采购单号
     */
    private String sapPoNo;

    /**
     * 行号
     * */
    private String lineNo;

    /**
     * 中台行号
     */
    private String ztLineNo;

    /**
     * 交货单行号
     * */
    private String deliveryLineNo;

    /**
     * 虚拟仓库ID
     */
    private Long virtualWarehouseId;

    /**
     * 实仓ID
     */
    private Long realWarehouseId;

    /**
     * 渠道编码
     */
    private String channelCode;

    /**
     * 用户编号
     */
    private String userCode;

    /**
     * 期望交货时间
     */
    private Date deliveryData;

    /**
     * 商品条码
     */
    private String barCode;

    /**
     * 是否免费sku行 0:不免费  1:免费
     */
    private Integer isFree;

    /**
     * 采购单位
     */
    private String purchaseUnit;

    /**
     * 退货原因
     */
    private String reason;

    /**
     * 设置超收比例
     */
    private Integer exceedRaio;

    /**
     * 成品和组合品转换关系(外采专用字段)
     */
    private Integer num;

    /**
     * 基础单位转换总数
     */
    private BigDecimal totalBaseSkuQty;

    /**
     * 是否需要质检，外采用
     */
    private Integer needInspect;

    private String reasonCode;
    /**
     * 打包码
     */
    private String packageCode;

    /**
     * 批次信息
     */
    private List<BatchStockDTO> batchStocks;

    /**
     * 最大效期 分子,要求批次有效期需传,否则为null
     */
    private Integer molecule;

    /**
     * 最大效期 分母,要求批次有效期需传,否则为null
     */
    private Integer denominator;


    /**
     * 最小效期 分子,要求批次有效期需传,否则为null
     */
    private Integer lowerMolecule;

    /**
     * 最小效期 分母,要求批次有效期需传,否则为null
     */
    private Integer lowerDenominator;

    /**
     * 冗余天数
     */
    private Integer transDay;


    @ApiModelProperty(value = "大包装组合-7字码物料ID")
    private Long largeSkuId;

    @ApiModelProperty(value = "大包装组合-7字码物料skuCode")
    private String largeSkuCode;

    @ApiModelProperty(value = "大包装组合-7字码物料名称")
    private String largeSkuName;

    @ApiModelProperty(value = "大包装组合-7字码物料单位")
    private String largeSkuUnit;

    @ApiModelProperty(value = "大包装组合-7字码物料单位Code")
    private String largeSkuUnitCode;

    @ApiModelProperty(value = "大包装组合-7字码对应2字码的转换比")
    private BigDecimal largeSkuScale;
}
