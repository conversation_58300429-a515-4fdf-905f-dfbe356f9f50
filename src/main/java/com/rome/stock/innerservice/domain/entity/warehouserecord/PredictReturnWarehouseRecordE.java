package com.rome.stock.innerservice.domain.entity.warehouserecord;

import com.rome.arch.core.domain.EntityFactory;
import com.rome.stock.common.enums.warehouse.WarehouseRecordTypeVO;
import com.rome.stock.innerservice.common.SkuQtyUnitTools;
import com.rome.stock.innerservice.constant.WarehouseRecordBusinessTypeVO;
import com.rome.stock.innerservice.domain.entity.frontrecord.PredictReturnRecordDetailE;
import com.rome.stock.innerservice.domain.entity.frontrecord.PredictReturnRecordE;
import com.rome.stock.innerservice.domain.repository.warehouserecord.PredictReturnRepository;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;

/**
 * 退货预入库入库单
 */
@Data
@Slf4j
@Component
@Scope("prototype")
@EqualsAndHashCode(callSuper = false)
public class PredictReturnWarehouseRecordE extends AbstractWarehouseRecord {

    @Resource
    private EntityFactory entityFactory;

    @Autowired
    private PredictReturnRepository predictReturnRepository;

    @Resource
    private SkuQtyUnitTools skuQtyUnitTools;
    /**
     * 创建仓库单据
     */
    public void addWarehouseRecord() {
        long id = predictReturnRepository.saveWarehouseRecord(this);
        this.setId(id);
        this.warehouseRecordDetails.forEach(record -> record.setWarehouseRecordDetail(this));
        predictReturnRepository.saveWarehouseRecordDetails(this.warehouseRecordDetails);
        //保存前置单表与入库单表关系
        this.setRecordType(WarehouseRecordTypeVO.PREDICT_RETURN_DIRECT_IN_RECORD.getType());
        this.addFrontRecordAndWarehouseRelation();
    }

    /**
     * 根据前置单生成入库单
     */
    public void createInRecordByFrontRecord(PredictReturnRecordE frontRecord){
        createRecodeCode(WarehouseRecordTypeVO.PREDICT_RETURN_DIRECT_IN_RECORD.getCode());
        this.setAbstractFrontRecord(frontRecord);
        this.setRealWarehouseId(frontRecord.getRealWarehouseId());
        this.setBusinessType(WarehouseRecordBusinessTypeVO.IN_WAREHOUSE_RECORD.getType());
        this.setRecordType(WarehouseRecordTypeVO.PREDICT_RETURN_DIRECT_IN_RECORD.getType());
        this.setMerchantId(frontRecord.getMerchantId());
        this.setOutCreateTime(frontRecord.getOutCreateTime());
        List<PredictReturnRecordDetailE> frontRecordDetails = frontRecord.getFrontRecordDetails();
        this.setWarehouseRecordDetails(new ArrayList<>(frontRecordDetails.size()));
        if(frontRecordDetails != null){
            for(PredictReturnRecordDetailE detailE : frontRecordDetails) {
                WarehouseRecordDetail warehouseRecordDetail = entityFactory.createEntity(WarehouseRecordDetail.class);
                warehouseRecordDetail.createRecordDetailByReturnFrontRecord(detailE);
                this.warehouseRecordDetails.add(warehouseRecordDetail);
            }
        }
    }
}
