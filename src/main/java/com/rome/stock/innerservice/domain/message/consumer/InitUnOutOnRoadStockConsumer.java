package com.rome.stock.innerservice.domain.message.consumer;

import com.rome.scm.common.rocketmq.CustomConsumerListener;
import com.rome.stock.common.enums.CustomRocketMQEnum;
import com.rome.stock.innerservice.domain.service.ShopReplenishService;
import lombok.extern.slf4j.Slf4j;
import org.apache.rocketmq.common.message.MessageExt;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;

/**
 * @Description: 未出库在途初始化入库单0库存消息MQ
 */
@Slf4j
@Service
public class InitUnOutOnRoadStockConsumer implements CustomConsumerListener<MessageExt> {

    @Resource
    private ShopReplenishService shopReplenishService;

    @Override
    public void onMessage(MessageExt messageExt, String businessNo, String msgKey) {
        log.info("未出库在途初始化入库单0库存消息MQ消息消费，msgID: {}，消费次数: {}", messageExt.getMsgId(), messageExt.getReconsumeTimes());
        String recordCode = new String(messageExt.getBody());
        if(StringUtils.isEmpty(recordCode)) {
            log.error("未出库在途初始化入库单0库存消息MQ消息消费内容为空，msgID: {}", messageExt.getMsgId());
            return ;
        }
        shopReplenishService.InitUnOutOnRoadStock(recordCode);
    }

	@Override
	public String getRocketMQCode() {
		return CustomRocketMQEnum.MQ_INIT_UN_OUT_ON_ROAD_STOCK.getCode();
	}



}
