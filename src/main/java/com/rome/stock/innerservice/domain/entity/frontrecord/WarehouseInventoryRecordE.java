package com.rome.stock.innerservice.domain.entity.frontrecord;

import com.rome.stock.innerservice.constant.FrontRecordTypeVO;
import com.rome.stock.innerservice.domain.repository.frontrecord.FrWarehouseInventoryRepository;
import com.rome.stock.innerservice.domain.service.OrderUtilService;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;

/**
 * 仓库盘点单
 */
@Component
@Scope("prototype")
@Data
@EqualsAndHashCode(callSuper = false)
public class WarehouseInventoryRecordE extends AbstractFrontRecord{

    @Autowired
    private FrWarehouseInventoryRepository frWarehouseInventoryRepository;
    @Resource
    private OrderUtilService orderUtilService;

    /**
     * 创建盘点单
     */
    public void addFrontRecord(){
        //生成单据编号
        this.initFrontRecord(FrontRecordTypeVO.WAREHOUSE_INVENTORY_RECORD.getCode(), this.frontRecordDetails);
        if(StringUtils.isBlank(this.getRemark())) {
            this.setRemark("");
        }
        if(StringUtils.isBlank(this.getRecordStatusReason())) {
            this.setRecordStatusReason("");
        }
        //插入盘点单据
        long id=frWarehouseInventoryRepository.saveWarehouseInventoryRecord(this);
        this.setId(id);
        //盘点详情关联主数据
        this.frontRecordDetails.forEach(detail->detail.setFrontRecordDetail(this));
        //插入盘点单据详情
        frWarehouseInventoryRepository.saveShopInventoryRecordDetails(this.frontRecordDetails);
    }

    /**
     * 创建C盘点单
     */
    public void addCFrontRecord(){
        // 生成单据编号
        String code = orderUtilService.queryOrderCode(FrontRecordTypeVO.WAREHOUSE_INVENTORY_RECORD.getCode());
        this.setRecordCode(code);
        // 插入盘点单据
        long id = frWarehouseInventoryRepository.saveWarehouseInventoryRecord(this);
        this.setId(id);
        // 盘点详情关联主数据
        this.frontRecordDetails.forEach(detail->detail.setFrontRecordDetail(this));
        // 插入盘点单据详情
        frWarehouseInventoryRepository.saveShopInventoryRecordDetails(this.frontRecordDetails);
    }

    /**
     * 更新完成状态
     */
    public int updateCompleteStatus(){
        return frWarehouseInventoryRepository.updateCompleteStatus(this.getId());
    }

    public int updateCostStatusSuccess() {
        return frWarehouseInventoryRepository.updateCostStatusSuccess(this.getId());
    }

    public int updateCostStatusFailed() {
        return frWarehouseInventoryRepository.updateCostStatusFailed(this.getId());
    }

    /**
     * 出向实体仓库id
     */
    private Long realWarehouseId;

    /**
     * 抽盘类型：1-抽盘，2-全盘
     */
    private Integer businessType;

    /**
     * 工厂编号
     */
    private String factoryCode;

    /**
     *仓库编码
     */
    private String realWarehouseCode;

    /**
     * 盘点备注
     */
    private String remark;

    /**
     * 开始日期
     */
    private Date startTime;

    /**
     * 结束日期
     */
    private Date endTime;

    /**
     * 外部系统单据编号
     */
    private String outRecordCode;
    /**
     * 操作人工号
     */
    private String modifierCode;
    /**
     * 是否有差异，0.否，1.是
     */
    private Integer isDiff;
    /**
     * 商品数量
     */
    private List<WarehouseInventoryRecordDetailE> frontRecordDetails;


}
