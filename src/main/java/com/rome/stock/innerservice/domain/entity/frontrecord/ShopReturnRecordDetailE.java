package com.rome.stock.innerservice.domain.entity.frontrecord;

import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;

/**
 * 类ShopReturnRecordDetailE的实现描述：门店退货明细
 *
 * <AUTHOR> 2019/5/8 10:52
 */
@Component
@Scope("prototype")
@Data
@EqualsAndHashCode(callSuper = false)
public class ShopReturnRecordDetailE extends AbstractFrontRecordDetail {

    /**
     * 实际收货数量
     */
    private BigDecimal realSkuQty;

    /**
     * 退货原因
     */
    private String reason;

    /**
     * sap采购单行号
     */
    private String lineNo;
}
