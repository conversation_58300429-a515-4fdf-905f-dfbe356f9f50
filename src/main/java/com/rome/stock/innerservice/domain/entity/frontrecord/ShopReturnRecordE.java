package com.rome.stock.innerservice.domain.entity.frontrecord;

import com.rome.stock.common.constants.front.ShopReturnConsts;
import com.rome.stock.innerservice.common.AlikAssert;
import com.rome.stock.innerservice.constant.FrontRecordStatusVO;
import com.rome.stock.innerservice.constant.FrontRecordTypeVO;
import com.rome.stock.innerservice.domain.repository.frontrecord.FrShopReturnRepository;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * 类ShopReturnRecordE的实现描述：门店退货单
 *
 * <AUTHOR> 2019/5/8 10:52
 */
@Component
@Scope("prototype")
@Data
@EqualsAndHashCode(callSuper = false)
public class ShopReturnRecordE extends AbstractFrontRecord {

    @Resource
    private FrShopReturnRepository frShopReturnRepository;

    /**
     * 生成直营或加盟门店退货单
     */
    public void addReturnRecord(FrontRecordTypeVO orderType) {
        this.setRecordType(orderType.getType());
        initFrontRecord(orderType.getCode(), this.frontRecordDetails);
        this.setRecordStatus(FrontRecordStatusVO.INIT.getStatus());
        this.setIsDiffIn(ShopReturnConsts.NOT_DIFF_IN);
        //单据编号生成
        Long id = frShopReturnRepository.addShopReplenishRecord(this);
        AlikAssert.isTrue(id > 0, "999", "创建退货单失败");
        this.setId(id);
        this.frontRecordDetails.forEach(record -> record.setFrontRecordDetail(this) );
        for(ShopReturnRecordDetailE detailE : this.frontRecordDetails){
            detailE.setFrontRecordId(id);
            detailE.setRecordCode(this.getRecordCode());
            detailE.setRealSkuQty(BigDecimal.ZERO);
        }
        frShopReturnRepository.saveShopReturnDetail(this.frontRecordDetails);
    }

    /**
     * 门店编号
     */
    private String shopCode;

    /**
     * 门店Name
     */
    private String shopName;
    /**
     * 门店类型
     */
    private Integer shopType;

    /**
     * 期望时间
     */
    private Date  expectDate;

    /**
     * 是否差异入库：0-没有差异，1-有差异
     */
    private Integer isDiffIn;
    /**
     * 入向实体仓库id
     */
    private Long inRealWarehouseId;
    /**
     * 出向实体仓库id
     */
    private Long outRealWarehouseId;
    /**
     * 外部系统单据编号:退货编码
     */
    private String outRecordCode;

    /**
     * 外部系统数据创建时间:退货时间
     */
    private Date outCreateTime;
    /**
     * 货被提走的时间
     */
    private Date pickedTime;

    /**
     * 是否需要派车(0:待分配 1: 派车 , 2. 自提)
     */
    private Integer isNeedDispatch;

    /**
     * sap单号
     */
    private String sapReverseNo;

    /**
     * 推送交易中心的状态 0-未推送,1-待推送,2-推送完成
     */
    private Integer transStatus;

    /**
     * 开始时间
     */
    private Date startTime;
    /**
     * 结束时间
     */
    private Date endTime;
    /**
     * 商品数量
     */
    private List<ShopReturnRecordDetailE> frontRecordDetails;
}
