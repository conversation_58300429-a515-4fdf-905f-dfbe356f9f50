package com.rome.stock.innerservice.domain.service.impl;

import com.alibaba.fastjson.JSON;
import com.rome.arch.core.exception.RomeException;
import com.rome.stock.common.constants.ResCode;
import com.rome.stock.common.utils.DateUtil;
import com.rome.stock.innerservice.api.dto.StockAlarmConfigDTO;
import com.rome.stock.innerservice.common.AlikAssert;
import com.rome.stock.innerservice.common.StringUtils;
import com.rome.stock.innerservice.domain.repository.alarm.BatchSaleAlarmDetailRepository;
import com.rome.stock.innerservice.domain.repository.alarm.BatchSaleAlarmRepository;
import com.rome.stock.innerservice.infrastructure.dataobject.alarm.BatchSaleAlarmDetailDO;
import com.rome.stock.innerservice.infrastructure.dataobject.alarm.StockAlarmHeadDO;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Service;
import lombok.extern.slf4j.Slf4j;
import org.springframework.transaction.annotation.Transactional;
import com.rome.stock.innerservice.domain.repository.alarm.StockAlarmHeadRepository;
import com.rome.stock.innerservice.domain.service.StockAlarmHeadService;
import javax.annotation.Resource;

import java.util.*;

/**
 * StockAlarmHeadService 实现
 * 
 * <AUTHOR>
 * @date 2024-04-11
 */
@Slf4j
@Service
public class StockAlarmHeadServiceImpl implements StockAlarmHeadService {

    @Resource
    private StockAlarmHeadRepository stockAlarmHeadRepository;

    @Resource
    private BatchSaleAlarmRepository batchSaleAlarmRepository;

    @Resource
    private BatchSaleAlarmDetailRepository batchSaleAlarmDetailRepository;
    @Override
    @Transactional(rollbackFor = Exception.class)
    public StockAlarmHeadDO startAlarmHead(StockAlarmConfigDTO stockAlarmConfigDTO) {
        if (CollectionUtils.isEmpty(stockAlarmConfigDTO.getItemDTOList()) && stockAlarmConfigDTO.getItemDTOList().size() != 1) {
            throw new RomeException(ResCode.STOCK_ERROR_1002, ResCode.STOCK_ERROR_1002_DESC);
        }
        Long realWarehouseId = stockAlarmConfigDTO.getItemDTOList().get(0).getRealWarehouseId();
        StockAlarmHeadDO stockAlarmHeadDO = stockAlarmHeadRepository.queryByRwIdAndStockDate(stockAlarmConfigDTO.getStockDateStr(), realWarehouseId);
        //存在，并且不是执行成功，则直接返回
        if (stockAlarmHeadDO == null) {
            Date currentDate = new Date();
            stockAlarmHeadDO = new StockAlarmHeadDO();
            stockAlarmHeadDO.setStockDate(DateUtil.parseDate(stockAlarmConfigDTO.getStockDateStr()));
            stockAlarmHeadDO.setAlarmType(stockAlarmConfigDTO.getAlarmType());
            stockAlarmHeadDO.setStatus(0);
            stockAlarmHeadDO.setRealWarehouseId(realWarehouseId);
            stockAlarmHeadDO.setConfigJson(JSON.toJSONString(stockAlarmConfigDTO));
            stockAlarmHeadDO.setMsg(null);
            stockAlarmHeadDO.setCreateTime(currentDate);
            stockAlarmHeadDO.setUpdateTime(currentDate);
            stockAlarmHeadRepository.insert(stockAlarmHeadDO);
            stockAlarmHeadDO.setNeedExecute(true);
        } else if (Objects.equals(stockAlarmHeadDO.getStatus(),  2)) {
            //执行失败， 需要删除掉，重新执行
            // 删除掉明细，跟新为执行中
            batchSaleAlarmRepository.deleteByHeadId(stockAlarmHeadDO.getId());
            batchSaleAlarmDetailRepository.deleteByHeadId(stockAlarmHeadDO.getId());
            stockAlarmHeadRepository.updateStatus(stockAlarmHeadDO.getId(), 0, null);
            stockAlarmHeadDO.setNeedExecute(true);
        } else  {
            //执行成功或者执行中，则无需执行
            stockAlarmHeadDO.setNeedExecute(false);
        }
        return stockAlarmHeadDO;
    }

    @Override
    public void saveAlarmHeadResult(Long headId, boolean isCalSuccess, String errorMsg) {
        if (headId == null) {
            return;
        }
        if (StringUtils.isNotBlank(errorMsg) && errorMsg.length() > 512) {
            errorMsg = errorMsg.substring(0, 512);
        }
        stockAlarmHeadRepository.updateStatus(headId, isCalSuccess? 1:2 , errorMsg);
    }
}
