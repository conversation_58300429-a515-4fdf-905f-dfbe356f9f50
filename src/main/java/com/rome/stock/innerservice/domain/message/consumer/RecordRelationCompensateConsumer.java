/**
 * Filename RecordRelationCompensateConsumer.java
 * Company 上海来伊份科技有限公司。
 *
 * <AUTHOR>
 * @version
 */
package com.rome.stock.innerservice.domain.message.consumer;

import com.alibaba.fastjson.JSONObject;
import com.rome.arch.core.exception.RomeException;
import com.rome.scm.common.exception.NoWarnMqMaxTimesException;
import com.rome.scm.common.rocketmq.CustomConsumerListener;
import com.rome.stock.common.enums.CustomRocketMQEnum;
import com.rome.stock.innerservice.common.ResCode;
import com.rome.stock.innerservice.common.StringUtils;
import com.rome.stock.innerservice.handler.recordrelation.RecordRelationHandlerExecutor;
import com.rome.stock.innerservice.handler.recordrelation.dto.RecordRelationHandlerDTO;
import com.rome.stock.innerservice.handler.recordrelation.dto.RecordRelationHandlerExecutorDTO;
import com.rome.stock.wms.config.BaseinfoConfiguration;
import lombok.extern.slf4j.Slf4j;
import org.apache.rocketmq.common.message.MessageExt;
import org.springframework.stereotype.Service;
import javax.annotation.Resource;
import java.util.ArrayList;

/**
 * 单据关联关系收集错误补偿推送MQ
 * <AUTHOR>
 * @since 2024/4/24 15:23
 */
@Slf4j
@Service
public class RecordRelationCompensateConsumer implements CustomConsumerListener<MessageExt> {

    @Resource
    private RecordRelationHandlerExecutor recordRelationHandlerExecutor;

    /**
     * 业务处理方法，单据关联关系收集错误补偿推送MQ
     *
     * @param message
     * @param businessNo 业务号，例如单据号、门店号等等，不同的消息，可以重复，在kibana可以查看，可选，不是必填
     * @param msgKey     消息key，每个消息key唯一，RocketMQ控制台方便查看，可选，不填时为uuid
     */
    @Override
    public void onMessage(MessageExt message, String businessNo, String msgKey) {
        String content = new String(message.getBody());
        RecordRelationHandlerDTO dto = JSONObject.parseObject(content, RecordRelationHandlerDTO.class);
        // 必填参数校验，recordCode、recordType、sourceType
        if(StringUtils.isBlank(dto.getRecordCode())) {
            throw new RomeException(ResCode.STOCK_ERROR_1002, "单据关联关系收集错误补偿，recordCode不能为空");
        }
        if(dto.getRecordType() == null) {
            throw new RomeException(ResCode.STOCK_ERROR_1002, "单据关联关系收集错误补偿，recordType不能为空");
        }
        if(dto.getSourceType() == null) {
            throw new RomeException(ResCode.STOCK_ERROR_1002, "单据关联关系收集错误补偿，sourceType不能为空");
        }
        RecordRelationHandlerExecutorDTO param = new RecordRelationHandlerExecutorDTO();
        // 是否失败出错发送Mq，这里不能为true一定为false
        param.setErrorSendMq(false);
        param.setDetails(new ArrayList<>());
        param.getDetails().add(dto);
        recordRelationHandlerExecutor.executor(param);
        // handler处理数据完整性，不完整时抛出异常
        if(!dto.isDataComplete()) {
            // 数据不完整结束条件，达到最大重试次数结束
            String maxRetryTimesStr = BaseinfoConfiguration.getInstance().get("recordrelation.config","maxretrytimes");
            int maxRetryTimes = 132;
            if(StringUtils.isNotBlank(maxRetryTimesStr)) {
                maxRetryTimes = Integer.parseInt(maxRetryTimesStr);
            }
            if(message.getReconsumeTimes() >= maxRetryTimes) {
                return;
            }
            // mq自身消费时，达到最大次数不发预警信息
            throw new NoWarnMqMaxTimesException("单据关联关系收集错误补偿，handler处理数据不完整");
        }
    }

    /**
     * 用来获取配置需要用到，消费和发送MQ时需要有对应code,
     * @return 不能为null
     */
    @Override
    public String getRocketMQCode() {
        return CustomRocketMQEnum.MQ_RECORD_RELATION_COMPENSATE_PUSH.getCode();
    }
}
