package com.rome.stock.innerservice.domain.service.impl;

import com.alibaba.fastjson.JSON;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.rome.arch.core.clientobject.Response;
import com.rome.arch.core.domain.EntityFactory;
import com.rome.arch.core.exception.RomeException;
import com.rome.arch.util.SpringBeanUtil;
import com.rome.stock.common.constants.WmsConfigConstants;
import com.rome.stock.common.enums.warehouse.WarehouseRecordExtTypeEnum;
import com.rome.stock.common.enums.warehouse.WarehouseRecordTypeVO;
import com.rome.stock.common.utils.DateUtil;
import com.rome.stock.core.constant.StockCoreConsts;
import com.rome.stock.core.constant.WarehouseRouteLockModeEnum;
import com.rome.stock.core.domain.repository.CoreChannelSalesRepository;
import com.rome.stock.core.domain.repository.CoreRealWarehouseStockRepository;
import com.rome.stock.core.facade.RedisRollBackFacade;
import com.rome.stock.core.infrastructure.dataobject.core.*;
import com.rome.stock.innerservice.api.dto.*;
import com.rome.stock.innerservice.api.dto.frontrecord.*;
import com.rome.stock.innerservice.api.dto.warehouserecord.RecordPackageDTO;
import com.rome.stock.innerservice.common.AlikAssert;
import com.rome.stock.innerservice.common.ResCode;
import com.rome.stock.innerservice.common.RomeCollectionUtil;
import com.rome.stock.innerservice.constant.*;
import com.rome.stock.innerservice.domain.convertor.*;
import com.rome.stock.innerservice.domain.convertor.record.RecordPackageConvertor;
import com.rome.stock.innerservice.domain.convertor.warehouserecord.PredictReturnRecordConvertor;
import com.rome.stock.innerservice.domain.entity.*;
import com.rome.stock.innerservice.domain.entity.frontrecord.*;
import com.rome.stock.innerservice.domain.entity.record.RecordPackageE;
import com.rome.stock.innerservice.domain.entity.warehouserecord.OnlineRetailWarehouseRecordE;
import com.rome.stock.innerservice.domain.entity.warehouserecord.WarehouseRecordDetail;
import com.rome.stock.innerservice.domain.entity.warehouserecord.WarehouseRecordE;
import com.rome.stock.innerservice.domain.repository.*;
import com.rome.stock.innerservice.domain.repository.frontrecord.FrSaleRepository;
import com.rome.stock.innerservice.domain.repository.frontrecord.FrWDTSaleRepository;
import com.rome.stock.innerservice.domain.repository.warehouserecord.PredictReturnRepository;
import com.rome.stock.innerservice.domain.repository.warehouserecord.RecordPackageRepository;
import com.rome.stock.innerservice.domain.repository.warehouserecord.WarehouseRecordRepository;
import com.rome.stock.innerservice.domain.service.AbstractStockService;
import com.rome.stock.innerservice.domain.service.FulfillmentJobService;
import com.rome.stock.innerservice.domain.service.ShopRetailService;
import com.rome.stock.innerservice.domain.service.StockService;
import com.rome.stock.innerservice.facade.StockOnlineOrderFacade;
import com.rome.stock.innerservice.infrastructure.dataobject.*;
import com.rome.stock.innerservice.infrastructure.dataobject.frontrecord.FrSaleExtDO;
import com.rome.stock.innerservice.infrastructure.dataobject.frontrecord.FrSaleSupplierDO;
import com.rome.stock.innerservice.infrastructure.dataobject.frontrecord.PredictReturnMatchResultDO;
import com.rome.stock.innerservice.infrastructure.dataobject.record.RecordPackageDO;
import com.rome.stock.innerservice.infrastructure.mapper.RealWarehouseWmsConfigMapper;
import com.rome.stock.innerservice.infrastructure.mapper.frontrecord.FrSaleExtMapper;
import com.rome.stock.innerservice.infrastructure.mapper.frontrecord.FrSaleSupplierMapper;
import com.rome.stock.innerservice.remote.base.TransactionRemoteService;
import com.rome.stock.innerservice.remote.item.dto.SkuCombineExtDTO;
import com.rome.stock.innerservice.remote.item.dto.SkuInfoExtDTO;
import com.rome.stock.innerservice.remote.item.dto.SkuSupplierDTO;
import com.rome.stock.innerservice.remote.item.facade.SkuFacade;
import com.rome.stock.innerservice.remote.orderCneter.dto.*;
import com.rome.stock.innerservice.remote.orderCneter.facade.OrderCenterFacade;
import com.rome.stock.innerservice.remote.orderTrack.facade.OrderTrackFacade;
import com.rome.stock.innerservice.remote.trade.dto.CloudShopPoDTO;
import com.rome.stock.innerservice.remote.trade.dto.CloudShopPoLineDTO;
import com.rome.stock.innerservice.remote.trade.dto.DoCodeMergeDTO;
import com.rome.stock.wms.config.BaseinfoConfiguration;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StopWatch;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.rome.stock.common.utils.DateUtil.NORM_DATETIME_PATTERN;
import static java.math.BigDecimal.ROUND_DOWN;
import static java.util.stream.Collectors.toList;

/**
 * 电商相关服务实现
 */
@Slf4j
@Service
public class StockServiceImpl extends AbstractStockService implements StockService {

    @Autowired
    private AddressRepository addressRepository;
    @Autowired
    private AddressConvertor addressConvertor;
    @Autowired
    private CoreChannelSalesRepository coreChannelSalesRepository;
    @Autowired
    private SkuStockConvertor skuStockConvertor;
    @Autowired
    private RwRecordPoolRepository rwRecordPoolRepository;
    @Autowired
    private FrSaleRepository frSaleRepository;
    @Autowired
    private FrWDTSaleRepository frWDTSaleRepository;
    @Autowired
    private WarehouseRecordRepository warehouseRecordRepository;
    @Autowired
    private RwRecordPoolConvertor rwRecordPoolConvertor;
    @Autowired
    private EntityFactory entityFactory;
    @Autowired
    private PredictReturnRepository predictReturnRepository;
    @Autowired
    private PredictReturnRecordConvertor predictReturnRecordConvertor;
    @Autowired
    private SkuFacade skuFacade;
    @Autowired
    private RealWarehouseRepository realWarehouseRepository;
    @Resource
    private WarehouseRecordConvertor warehouseRecordConvertor;
    @Autowired
    private ChannelSalesRepository channelSalesRepository;
    @Autowired
    private VirtualWarehouseRepository virtualWarehouseRepository;
    @Autowired
    private VirtualWarehouseStockRepository virtualWarehouseStockRepository;

    @Autowired
    private RecordPackageRepository recordPackageRepository;

    @Resource
    private RealWarehouseConvertor realWarehouseConvertor;

    @Resource
    private RecordPackageConvertor recordPackageConvertor;

    @Autowired
    private OrderTrackFacade orderTrackFacade;

    @Resource
    private CoreRealWarehouseStockRepository coreRealWarehouseStockRepository;

    @Resource
    private FulfillmentJobService fulfillmentJobService;

    private final ParamValidator validator = ParamValidator.INSTANCE;
    @Resource
    private RealWarehouseWmsConfigMapper realWarehouseWmsConfigMapper;
    @Resource
    private FrSaleExtMapper frSaleExtMapper;
    @Resource
    private FrSaleSupplierMapper frSaleSupplierMapper;
    @Resource
    private SapInterfaceLogRepository sapInterfaceLogRepository;
    @Resource
    private TransactionRemoteService transactionRemoteService;
    @Resource
    private OrderCenterFacade orderCenterFacade;

    private final Integer PAGESIZE = 3000;
    /**
     * 根据SO单号查询发货单信息
     *
     * @param orderCode SO单号
     */
    @Override
    public List<RwRecordPoolDTO> queryDoInfo(String orderCode) {
        // 根据SO单号，查询前置单据ID
        OnlineRetailE onlineRetailE = frSaleRepository.queryOnlineRetailByOutRecordCode(orderCode);
        AlikAssert.isNotNull(onlineRetailE, ResCode.STOCK_ERROR_1014, ResCode.STOCK_ERROR_1014_DESC);
        /**
         *  根据前置单ID，查询池子中的数据，如果状态为0(初始),99(待合单),2(已取消),
         *  直接以该状态返回，否则如果是100(已合单)，则查询出入库表中对应状态位返回
         */
        List<RwRecordPoolE> rwRecordPoolEList = rwRecordPoolRepository.queryByFrontRecordIdWithDetails(onlineRetailE.getRecordCode());
        if (null != rwRecordPoolEList && !rwRecordPoolEList.isEmpty()) {
            Set<Long> warehouseIdSet = new HashSet<>();
            for (RwRecordPoolE rwRecordPoolE : rwRecordPoolEList) {
                if (RwRecordPoolStatusVO.MERGED.getStatus().equals(rwRecordPoolE.getRecordStatus())) {
                    warehouseIdSet.add(rwRecordPoolE.getWarehouseRecordId());
                }
            }
            //如果存在rwRecordPoolDo状态为100(已合单)的数据，则查询出入库表中对应的状态位
            if (!warehouseIdSet.isEmpty()) {
                List<WarehouseRecordE> warehouseRecordEList = warehouseRecordRepository.queryWarehouseRecordByIds(warehouseIdSet);
                Map<Long, WarehouseRecordE> map = RomeCollectionUtil.listforMap(warehouseRecordEList, "id", null);
                for (RwRecordPoolE rwRecordPoolE : rwRecordPoolEList) {
                    if (RwRecordPoolStatusVO.MERGED.getStatus().equals(rwRecordPoolE.getRecordStatus())) {
                        WarehouseRecordE wre = map.get(rwRecordPoolE.getWarehouseRecordId());
                        if (null != wre) {
                            rwRecordPoolE.setRecordStatus(wre.getRecordStatus());
                        }
                    }
                }
            }
        }
        return rwRecordPoolConvertor.entityListToDTOList(rwRecordPoolEList);
    }

    /**
     * 订单支付成功通知，推送do单
     *
     * @param orderCode SO单号
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public List<RwRecordPoolResultDTO> toCombineDo(String orderCode, Date payTime) {
        // 查询前置SO单数据
        OnlineRetailE onlineRetailE = frSaleRepository.queryOnlineRetailByOutRecordCode(orderCode);
        AlikAssert.isNotNull(onlineRetailE, ResCode.STOCK_ERROR_1014, ResCode.STOCK_ERROR_1014_DESC);
        // 更新前置SO单状态为已支付，
        if (payTime == null) {
            payTime = new Date();
        }
        if (onlineRetailE.getRecordStatus().equals(FrontRecordStatusVO.DISABLED.getStatus())){
            throw  new RomeException(ResCode.STOCK_ERROR_1014 ,ResCode.STOCK_ERROR_1014_DESC + "：单据已取消");
        }
        if (onlineRetailE.getRecordStatus().equals(FrontRecordStatusVO.SO_UNPAID.getStatus())){
            //只有待支付的订单 才执行修改状态的操作，否则只做查询
            onlineRetailE.updateToPaid(payTime);
        }
        List<RwRecordPoolE> splitResult = rwRecordPoolRepository.queryNotCanceledByFRIdWithDetails(onlineRetailE.getRecordCode());
        if (CollectionUtils.isEmpty(splitResult)) {
            throw new RomeException(ResCode.STOCK_ERROR_1003,ResCode.STOCK_ERROR_1003_DESC+"，没有查到对应的do单：订单号：" + orderCode);
        }
        List<Long> realWarehouseIds = RomeCollectionUtil.getValueList(splitResult, "realWarehouseId");
        List<RealWarehouseE> realWarehouseEList = realWarehouseRepository.queryWarehouseByIds(realWarehouseIds);
        Map<Long, RealWarehouseE> realWarehouseMap = RomeCollectionUtil.listforMap(realWarehouseEList, "id");
        List<RwRecordPoolResultDTO> res = rwRecordPoolConvertor.entityToResultDTOList(splitResult);
        for (RwRecordPoolResultDTO dto : res) {
            Long realhouseId = dto.getRealWarehouseId();
            if (realWarehouseMap.containsKey(realhouseId)) {
                dto.setOutRealWarehouseCode(realWarehouseMap.get(realhouseId).getRealWarehouseOutCode());
                dto.setFactoryCode(realWarehouseMap.get(realhouseId).getFactoryCode());
                dto.setRealWarehouseCode(realWarehouseMap.get(realhouseId).getRealWarehouseCode());
            }
        }
        return res;
    }

    /**
     * 保存物流公司编码
     * @param paramDTO
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void saveLogisticInfo(List<OnLineStockLogisticInfoParamDTO> paramDTO) {
        for (OnLineStockLogisticInfoParamDTO dto : paramDTO) {
            //1.物流公司编码校验
            if (StringUtils.isBlank(dto.getLogisticsCode())) {
                throw new RomeException(ResCode.STOCK_ERROR_1003,"物流公司编码不能为空");
            }
            RwRecordPoolE poolE = rwRecordPoolRepository.queryByDoCode(dto.getDoCode());
            AlikAssert.isNotNull(poolE, ResCode.STOCK_ERROR_1030, ResCode.STOCK_ERROR_1030_DESC + "doCode不存在:doCode=" + dto.getDoCode());
            AddressE addressE = addressRepository.queryByRecordCode(dto.getDoCode());
            AlikAssert.isNotNull(addressE, ResCode.STOCK_ERROR_1030, ResCode.STOCK_ERROR_1030_DESC + "doCode地址信息不存在:doCode=" + dto.getDoCode());
            poolE.setLogisticsCode(dto.getLogisticsCode());
            //添加用户编号
            ShopTradeRecordDTO shopRetailRecordE=frSaleRepository.getFrSaleRecordByRecordCode(poolE.getFrontRecordCode());
            poolE.setUserCode(shopRetailRecordE.getUserCode());
            //计算指纹信息
            poolE.bindMergeFingerprint(addressE);
            rwRecordPoolRepository.updateToPreMergeAndLogisticInfo(poolE);
            //跨境电商立即合单
            if(FrontRecordTypeVO.CROSS_SALE_RECORD.getType().equals(shopRetailRecordE.getRecordType())){
                try{
                    assignRecordToMerge(Arrays.asList(poolE.getDoCode()),null);
                }catch (Exception e){
                    log.error("跨境电商立即合单失败，doCode:{},",poolE.getDoCode(),e);
                }
            }
        }
    }

    /**
     * 根据子do查询包裹单号
     * @param doCode
     * @return
     */
    @Override
    public List<RecordPackageDTO> queryPackageInfoByDoCode(String doCode) {
        RwRecordPoolE rwRecordPoolE = rwRecordPoolRepository.queryByDoCode(doCode);
        if (rwRecordPoolE == null || null == rwRecordPoolE.getWarehouseRecordCode() ) {
            //合单之前 ，没有出库单，直接返回空
            return new ArrayList<>();
        }
        List<RecordPackageE> res = recordPackageRepository.getRecordPackageWithDetailByRecordCode(rwRecordPoolE.getWarehouseRecordCode());
        return recordPackageConvertor.convertEntityListToDTOList(res);
    }

    /**
     * 根据子do批量查询包裹单号
     * @param doCodeList
     * @return
     */
    @Override
    public Map<String , List<RecordPackageDTO>> queryPackageInfoByDoCodeList(List<String> doCodeList) {
        List<RwRecordPoolE> poolList = rwRecordPoolRepository.queryByDoCodes(doCodeList);
        Map<String, String> doWrCodeMap = new HashMap<>(doCodeList.size());
        List<String> codeList = new ArrayList<>();
        Map<String , List<RecordPackageDTO>> resultMap = new HashMap<>(doCodeList.size());
        AlikAssert.isNotEmpty(poolList, ResCode.STOCK_ERROR_1014, "子do单不存在：" + doCodeList);
        for (RwRecordPoolE poolE : poolList) {
            if(StringUtils.isNotBlank(poolE.getWarehouseRecordCode())){
                doWrCodeMap.put(poolE.getWarehouseRecordCode(), poolE.getDoCode());
                codeList.add(poolE.getWarehouseRecordCode());
            }
        }
        //都没合单就直接返回空的
        if(CollectionUtils.isEmpty(codeList)){
            return resultMap;
        }
        //查询包裹信息列表
        List<RecordPackageDO> tempList = recordPackageRepository.getRecordPackageByRecordCodeList(codeList);
        List<RecordPackageDTO> recordPackageList = recordPackageConvertor.convertDOList2DTOList(tempList);
        Map<String, List<RecordPackageDTO>> recordPackageMap = RomeCollectionUtil.listforListMap(recordPackageList,"recordCode");
        for (String recordCode : recordPackageMap.keySet()) {
            String doCode = doWrCodeMap.get(recordCode);
            resultMap.put(doCode, recordPackageMap.get(recordCode));
        }
        return resultMap;
    }

    /**
     * 通过包裹号查询包裹信息
     *
     * @param packageCode
     * @return
     */
    @Override
    public RecordPackageDTO queryPackageInfoByPackageCode(String packageCode) {
        RecordPackageE recordPackageE = recordPackageRepository.getRecordPackageWithDetailByPackageCode(packageCode);
        return recordPackageConvertor.convertE2DTO(recordPackageE);
    }

    @Override
    public RealWarehouseStockDTO queryWarehouseStockByMerchantId(String skuCode, Long merchantId) {
        RealWarehouseStockDO realWarehouseStockDO= realWarehouseRepository.queryRealWarehouseStockByMerchantId(skuCode,merchantId);
        RealWarehouseStockDTO realWarehouseStockDTO=realWarehouseConvertor.stockDoToDTO(realWarehouseStockDO);
        return realWarehouseStockDTO;
    }

    @Override
    public RealWarehouse queryRealWarehouseByMerchantId(Long merchantId) {
        RealWarehouse realWarehouse=realWarehouseRepository.queryRealWarehousesByMerchantId(merchantId);
        return realWarehouse;
    }




    /**
     * 根据子do单批量查询父子关系
     * @param doCodes
     * @return
     */
    @Override
    public List<DoCodeMergeDTO> queryMergeInfoByChildDoCode(List<String> doCodes) {
        List<DoCodeMergeDTO> result = new ArrayList<>();
        for (String doCode : doCodes) {
            RwRecordPoolE pool = rwRecordPoolRepository.queryByDoCode(doCode);
            if (pool == null || FrontRecordStatusVO.DISABLED.getStatus().equals(pool.getRecordStatus())) {
                continue;
            }
            if (null == pool.getWarehouseRecordCode()) {
                //合单之前 没有出库单
                continue;
            }
            WarehouseRecordE warehouseRecordE = warehouseRecordRepository.queryWarehouseRecordByRecordCode(pool.getWarehouseRecordCode());
            if (warehouseRecordE == null || WarehouseRecordStatusVO.DISABLED.getStatus().equals(warehouseRecordE.getRecordStatus())) {
                continue;
            }
            List<RwRecordPoolE> pools = rwRecordPoolRepository.queryKeysByWarehouseId(warehouseRecordE.getId());
            DoCodeMergeDTO dto = new DoCodeMergeDTO();
            dto.setDoCode(warehouseRecordE.getRecordCode());
            dto.setChildDoCode(RomeCollectionUtil.getValueList(pools, "doCode"));
            result.add(dto);
        }
        return result;
    }

    /**
     * 根据父doCode查询子do列表
     * @param parentCode
     * @return
     */
    @Override
    public List<String> queryChildDoCodeByParentDo(String parentCode){
        WarehouseRecordE warehouseRecordE = warehouseRecordRepository.queryWarehouseRecordByRecordCode(parentCode);
        if (warehouseRecordE == null) {
            return new ArrayList<>();
        }
        List<RwRecordPoolE> pools = rwRecordPoolRepository.queryKeysByWarehouseId(warehouseRecordE.getId());
        return RomeCollectionUtil.getValueList(pools,"doCode");
    }

    /**
     * 电商下单锁定库存
     *
     * @param stockOrderDTO
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void lockStockByRecord(StockOrderDTO stockOrderDTO) {
        long start = System.currentTimeMillis();
        // 必要参数校验
        ValidResult validResult = validOnlineAddOrderParam(stockOrderDTO);
        if (null != validResult) {
            throw new RomeException(validResult.getResCode(), validResult.getResDesc());
        }
        if (frSaleRepository.judgeExistByOutRecordCode(stockOrderDTO.getOrderCode())) {
            //单据已存在，直接返回
            return;
        }
        log.info("虚拟物品下单锁定库存参数校验耗时：单号{},耗时{}",stockOrderDTO.getOrderCode(),(System.currentTimeMillis() - start));
        start = System.currentTimeMillis();
        boolean isVirtual = OnlineStockTransTypeVO.TRANS_TYPE_4.getTransType().equals(stockOrderDTO.getTransType()) ||
                OnlineStockTransTypeVO.TRANS_TYPE_12.getTransType().equals(stockOrderDTO.getTransType());
        // 入电商销售前置单表
        OnlineRetailE onlineRetailE = skuStockConvertor.dtoToEntity(stockOrderDTO);
        onlineRetailE.addFrontRecord(stockOrderDTO);
        log.info("虚拟物品下单锁定库存写前置单耗时：单号{},耗时{}",stockOrderDTO.getOrderCode(),(System.currentTimeMillis() - start));
        // 入sc_address收货地址信息表
        AddressE addressE = null ;
        if (!isVirtual) {
            //非虚拟订单
            addressE = addressConvertor.dtoToEntity(stockOrderDTO);
            addressE.setUserType((byte) 0);
            addressE.setAddressType((byte) 0);
            addressE.setRecordCode(onlineRetailE.getRecordCode());
            addressE.addAddress();
        }
        // 寻源 + 锁库存
        CoreChannelOrderDO cco = null;
        boolean isSuccess = false;
        try {
            start = System.currentTimeMillis();
            cco = warpRouteAndLockStockDO(onlineRetailE, addressE);
            log.info("虚拟物品下单锁定库存参数包装耗时：单号{},耗时{}",stockOrderDTO.getOrderCode(),(System.currentTimeMillis() - start));
            List<CoreVirtualStockOpDO> cvsList = null;
            start = System.currentTimeMillis();
            if (stockOrderDTO.getRealWarehouseId() != null) {
                //指定仓库下单
                onlineRetailE.setRealWarehouseId(stockOrderDTO.getRealWarehouseId());
                //包装指定虚仓和实仓锁库存的入参
                cvsList = warpAssignHouseRoute(onlineRetailE);
                cco.setVirtualStockOpDetailDOs(cvsList);
                coreChannelSalesRepository.lockStock(cco);
            } else {
                //不指定仓库下单，需要寻源
                cco.setRouteLockMode(WarehouseRouteLockModeEnum.ROUTE_LOCK_MODE_VIRTUAL);
                cvsList = StockOnlineOrderFacade.routeAndLockStock(cco);
            }
            log.info("虚拟物品下单锁定库存寻源耗时：单号{},耗时{}",stockOrderDTO.getOrderCode(),(System.currentTimeMillis() - start));
            AlikAssert.notEmpty(cvsList, ResCode.STOCK_ERROR_5024, ResCode.STOCK_ERROR_5024_DESC);
            // 根据寻源结果拆单
            start = System.currentTimeMillis();
            this.checkIsCross(cvsList.get(0).getRealWarehouseId());
            onlineRetailE.splitOnlineOrder( cvsList, stockOrderDTO);
            log.info("虚拟物品下单锁定库存拆单耗时：单号{},耗时{}",stockOrderDTO.getOrderCode(),(System.currentTimeMillis() - start));
            isSuccess = true;
        } catch (RomeException e) {
            log.error(e.getMessage(), e);
            throw e;
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            throw new RomeException(ResCode.STOCK_ERROR_5024, ResCode.STOCK_ERROR_5024_DESC);
        } finally {
            if (!isSuccess && null != cco) {
                RedisRollBackFacade.redisRollBack(cco);
            }
        }
    }

    /**
     * 检查普通下单接口是否有跨境电商商品下单
     * @param realWarehouseId
     */
    private void checkIsCross(Long realWarehouseId){
        RealWarehouseWmsConfigDO realWarehouseWmsConfigDO = realWarehouseWmsConfigMapper.findRealWarehouseWmsConfigById(realWarehouseId);
        if (null != realWarehouseWmsConfigDO) {
            if (WmsConfigConstants.WMS_CROSS == realWarehouseWmsConfigDO.getWmsCode()) {
                throw new RomeException(ResCode.STOCK_ERROR_5024, "该接口不支持跨境电商下单,请检查商品是否属于跨境电商渠道");
            }
        }
    }





    /**
     * 跨境电商下单锁定库存
     *
     * @param crossStockDTO
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void lockStockByCross(CrossStockDTO crossStockDTO) {
        StopWatch stopWatch=new StopWatch("跨境电商锁库,orderCode:"+crossStockDTO.getOrderCode());
        // 必要参数校验
        ValidResult validResult = this.validCrossOrderParam(crossStockDTO);
        if (null != validResult) {
            throw new RomeException(validResult.getResCode(), validResult.getResDesc());
        }
        if (frSaleRepository.judgeExistByOutRecordCode(crossStockDTO.getOrderCode())) {
            return;
        }
        RealWarehouseE outWarehouse = realWarehouseRepository.getRealWarehouseByCode(crossStockDTO.getRealWarehouseCode());
        if(null == outWarehouse){
            throw new RomeException(ResCode.STOCK_ERROR_1002,"跨境电商仓库不存在");
        }
        //查询实仓对应的虚仓
        List<VirtualWarehouseE> virtualWarehouseList = virtualWarehouseRepository.queryByRealWarehouseId(outWarehouse.getId());
        Long virtualWarehouseId=null;
        if(!CollectionUtils.isEmpty(virtualWarehouseList)){
            virtualWarehouseId=virtualWarehouseList.get(0).getId();
            if(virtualWarehouseList.size()>1){
                throw new RomeException(ResCode.STOCK_ERROR_1002,"实仓对应虚仓不唯一");
            }
        }
        stopWatch.start("写前置单耗时");
        // 入电商销售前置单表
        OnlineRetailE onlineRetailE = skuStockConvertor.dtoToEntity(crossStockDTO);
        onlineRetailE.addCrossFrontRecord(crossStockDTO);
        stopWatch.stop();
        // 入sc_address收货地址信息表
        AddressE addressE  = addressConvertor.dtoToEntity(crossStockDTO);
        addressE.setUserType((byte) 0);
        addressE.setAddressType((byte) 0);
        addressE.setRecordCode(onlineRetailE.getRecordCode());
        addressE.addAddress();
        //锁库存
        CoreRealStockOpDO coreRealStockOpDO = new CoreRealStockOpDO();
        boolean isSuccess = false;
        try {
            List<CoreVirtualStockOpDO> cvsList=new ArrayList<>();
            onlineRetailE.setRealWarehouseId(outWarehouse.getId());
            coreRealStockOpDO = onlineRetailE.initLockStockObj(cvsList,virtualWarehouseId);
            //指定仓库下单
            coreRealWarehouseStockRepository.lockStock(coreRealStockOpDO);
            // 根据锁库结果拆单
            stopWatch.start("根据锁库结果拆单耗时");
            onlineRetailE.splitOnlineOrder(cvsList,crossStockDTO);
            stopWatch.stop();
            log.info(stopWatch.toString());
             isSuccess = true;
        } catch (RomeException e) {
            log.error(e.getMessage(), e);
            throw e;
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            throw e;
        } finally {
            if (!isSuccess) {
                RedisRollBackFacade.redisRollBack(coreRealStockOpDO);
            }
        }
    }


    /**
     * 虚拟商品下单锁定库存
     *
     * @param virtualSkuStockOrderDTO
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void lockVirtualSkuStock(VirtualSkuStockOrderDTO virtualSkuStockOrderDTO) {
        StockOrderDTO stockOrderDTO=new StockOrderDTO();
        stockOrderDTO.setFrontRecordDetails(virtualSkuStockOrderDTO.getFrontRecordDetails());
        stockOrderDTO.setChannelCode(virtualSkuStockOrderDTO.getChannelCode());
        stockOrderDTO.setOrderCode(virtualSkuStockOrderDTO.getOrderCode());
        stockOrderDTO.setMerchantId(virtualSkuStockOrderDTO.getMerchantId());
        stockOrderDTO.setOutCreateTime(virtualSkuStockOrderDTO.getOutCreateTime());
        stockOrderDTO.setUserCode(virtualSkuStockOrderDTO.getUserCode());
        stockOrderDTO.setTransType(virtualSkuStockOrderDTO.getTransType());
        this.lockStockByRecord(stockOrderDTO);
    }


    /**
     * 虚拟物品支付成功后扣库存
     *
     * @param orderCode
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public List<RwRecordPoolResultDTO> virtualSkuStockAfterPay(String orderCode) {
        //单据不存在，直接返回
        OnlineRetailE onlineRetailE = frSaleRepository.queryOnlineRetailByOutRecordCode(orderCode);
        AlikAssert.isNotNull(onlineRetailE, ResCode.STOCK_ERROR_1014, ResCode.STOCK_ERROR_1014_DESC);

        boolean hasHandled = false;
        if (onlineRetailE.getRecordStatus().equals(FrontRecordStatusVO.SO_PAID.getStatus())
                || onlineRetailE.getRecordStatus().equals(FrontRecordStatusVO.OUT_ALLOCATION.getStatus())) {
            //已经支付过了或已出库，就不再合单，只查询相当于
            hasHandled = true;
        }

        List<RwRecordPoolResultDTO> rwRecordPoolResultDTOList = this.toCombineDo(orderCode,null);
        //计算指纹信息
        if (!hasHandled) {
            List<RwRecordPoolE> poolEList = rwRecordPoolRepository.queryNotCanceledByFRIdWithDetails(onlineRetailE.getRecordCode());
            RwRecordPoolE poolE = poolEList.get(0);
            poolE.bindMergeFingerprint(null);
            //更新订单池状态为待合单
            rwRecordPoolRepository.updateToPreMergeAndLogisticInfo(poolE);
            //根据前置SO单id状态为已支付

            //虚拟商品订单支付成功后直接合单
            onlineRetailE.setRwRecordPoolEList(new ArrayList<>());
            onlineRetailE.getRwRecordPoolEList().add(poolE);
            //添加出库单及明细，并合单
            addRecordAndMerge(onlineRetailE);

            try {
                //推送mq失败就等着定时器跑
//                virtualOrderPushProducer.sendMQ(poolE.getWarehouseRecordCode());
            } catch (Exception e) {
                log.error("推送虚拟订单mq失败:" + e.getMessage(), e);
            }
        }
        return rwRecordPoolResultDTOList;
    }

    @Value("${sku.merchantId}")
    private Long defaultMerchantId;
    /**
     * 电商下单锁定库存、支付成功、推物流（临时使用，上线完成后删除）
     *
     * @param stockOrderLogisticDTO
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void lockStockAndPaySuccessAndSaveLogisticByRecord(StockOrderLogisticDTO stockOrderLogisticDTO) {
        StockOrderDTO stockOrderDTO=stockOrderLogisticDTO.getStockOrderDTO();
        if (! validator.validPositiveInt(stockOrderDTO.getTransType())) {
            throw new RomeException(ResCode.STOCK_ERROR_1002,ResCode.STOCK_ERROR_1002_DESC);
        }
        //非欧电云下单报错
        if(stockOrderDTO.getTransType()!=1){
            throw new RomeException(ResCode.STOCK_ERROR_1002,ResCode.STOCK_ERROR_1002_DESC+"：当前接口仅支持欧电云App下单");
        }
        //非默认商家
        if(!stockOrderDTO.getMerchantId().equals(defaultMerchantId)){
            throw new RomeException(ResCode.STOCK_ERROR_1002,ResCode.STOCK_ERROR_1002_DESC+"：商家编号不是默认商家编号");
        }
        //下单锁库存
        this.lockStockByRecord(stockOrderDTO);
        //订单支付成功通知
        List<RwRecordPoolResultDTO> res=this.toCombineDo(stockOrderDTO.getOrderCode(),null);
        String doCode=res.get(0).getDoCode();
        stockOrderLogisticDTO.getOnLineStockLogisticInfoParam().forEach(temp->temp.setDoCode(doCode));
        //保存物流公司编码
        this.saveLogisticInfo(stockOrderLogisticDTO.getOnLineStockLogisticInfoParam());
    }

    /**
     * 根据包裹号修改运单号
     * @param updateBillCodeDTOs
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public List<UpdateBillCodeDTO> updateBillCodeByPackageCode(List<UpdateBillCodeDTO> updateBillCodeDTOs) {
        List<UpdateBillCodeDTO> updateBillCodeDTOTemp=new ArrayList<>();
        List<UpdateBillCodeDTO> resList=new ArrayList<>();
        for(UpdateBillCodeDTO updateBillCodeDTO :updateBillCodeDTOs){
            String packageCode=updateBillCodeDTO.getPackageCode();
            RecordPackageE recordPackageE=recordPackageRepository.getRecordPackageWithDetailByPackageCode(packageCode);
            if(null == recordPackageE){
                throw new RomeException(ResCode.STOCK_ERROR_9033, ResCode.STOCK_ERROR_9033_DESC+",packageCode:"+packageCode);
            }
            //包裹信息未同步tms
            if(recordPackageE.getSyncTmsStatus()!=1){
                throw new RomeException(ResCode.STOCK_ERROR_9034, ResCode.STOCK_ERROR_9034_DESC+",packageCode:"+packageCode);
            }
            WarehouseRecordE warehouseRecordE=warehouseRecordRepository.queryWarehouseRecordByRecordCode(recordPackageE.getRecordCode());
            List<FrontWarehouseRecordRelationDO> recordRelationDOs=warehouseRecordRepository.getFrontWarehouseRecordsRelationByWrId(warehouseRecordE.getId());
            String frontRecordCode=recordRelationDOs.get(0).getFrontRecordCode();
            ShopTradeRecordDTO shopRetailRecordE=frSaleRepository.getFrSaleRecordByRecordCode(frontRecordCode);
            //判断是否为商家订单C
            if(!OnlineStockTransTypeVO.ALLOW_MERCHANT_TYPE.contains(shopRetailRecordE.getTransType()) || shopRetailRecordE.getMerchantId().equals(defaultMerchantId) ){
                throw new RomeException(ResCode.STOCK_ERROR_9035, ResCode.STOCK_ERROR_9035_DESC+",packageCode:"+packageCode);
            }
            int j=recordPackageRepository.updateBillCodeByPackageCode(updateBillCodeDTO);
            if(j<=0){
                throw new RomeException(ResCode.STOCK_ERROR_9036, ResCode.STOCK_ERROR_9036_DESC+",packageCode:"+packageCode);
            }
            updateBillCodeDTOTemp.add(updateBillCodeDTO);
        }
        for(UpdateBillCodeDTO updateBillCodeDTO :updateBillCodeDTOTemp){
            String packageCode=updateBillCodeDTO.getPackageCode();
            boolean flag=fulfillmentJobService.updateBillCodeByPackageCode(updateBillCodeDTO);
            if(flag){
                resList.add(updateBillCodeDTO);
            }else{
                log.error(ResCode.STOCK_ERROR_9036_DESC+",packageCode={}",packageCode);
//                throw new RomeException(ResCode.STOCK_ERROR_9036, ResCode.STOCK_ERROR_9036_DESC+",packageCode:"+packageCode);
            }
        }
        return resList;
    }

    private List<CoreVirtualStockOpDO> warpAssignHouseRoute(OnlineRetailE onlineRetailE){
        List<CoreVirtualStockOpDO>  cvsList = new ArrayList<>();
        //根据指定的实仓跟渠道code查询虚仓信息
        ChannelSalesE channelSalesE = channelSalesRepository.queryByChannelCode(onlineRetailE.getChannelCode());
        AlikAssert.isNotNull(channelSalesE,ResCode.STOCK_ERROR_5028,ResCode.STOCK_ERROR_5028_DESC);
        Long vmId = null;
        List<VirtualWarehouseE> vwList = virtualWarehouseRepository.queryByGroupIdAndRwId(channelSalesE.getVirtualWarehouseGroupId(), onlineRetailE.getRealWarehouseId());
        if(vwList != null && vwList.size()== 1) {
        	vmId = vwList.get(0).getId();
        }
        AlikAssert.isNotNull(vmId, ResCode.STOCK_ERROR_1041, ResCode.STOCK_ERROR_1041_DESC +
                "rwId=" + onlineRetailE.getRealWarehouseId() + ",groupId=" + channelSalesE.getVirtualWarehouseGroupId());
        for(OnlineRetailRecordDetailE detailE :onlineRetailE.getFrontRecordDetails()){
            CoreVirtualStockOpDO coreStockDO = new CoreVirtualStockOpDO();
            coreStockDO.setLockQty(detailE.getBasicSkuQty());
            coreStockDO.setVirtualWarehouseId(vmId);
            coreStockDO.setRealWarehouseId(onlineRetailE.getRealWarehouseId());
            coreStockDO.setRecordCode(onlineRetailE.getRecordCode());
            coreStockDO.setTransType(onlineRetailE.getRecordType());
            coreStockDO.setChannelCode(onlineRetailE.getChannelCode());
            coreStockDO.setMerchantId(onlineRetailE.getMerchantId());
            coreStockDO.setSkuId(detailE.getSkuId());
            coreStockDO.setSkuCode(detailE.getSkuCode());
            cvsList.add(coreStockDO);
        }
        return cvsList;
    }


    /**
     * 取消订单，解锁库存
     * 目前暂定为出库单中sync_wms_status状态 1-未同步可以取消，2-已同步的不允许取消
     *
     * @param orderCode SO单号
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void unLockStockByRecord(String orderCode) {
        //查询前置SO单数据
        OnlineRetailE onlineRetailE = frSaleRepository.queryOnlineRetailByOutRecordCode(orderCode);
        AlikAssert.isNotNull(onlineRetailE, ResCode.STOCK_ERROR_1014, ResCode.STOCK_ERROR_1014_DESC);
        if (FrontRecordStatusVO.DISABLED.getStatus().equals(onlineRetailE.getRecordStatus())) {
            throw new RomeException(ResCode.STOCK_ERROR_5017, ResCode.STOCK_ERROR_5017_DESC);
        }
        //是否可以解锁并取消的标记
        boolean unLockAbleFlag = true;
        //根据前置单ID查询对应的Do池数据
        List<RwRecordPoolE> rwRecordPoolEList = rwRecordPoolRepository.queryNotCanceledByFRIdWithDetails(onlineRetailE.getRecordCode());
        //已合单出库单ID集合
        Set<Long> mergedWarehouseIdSet = new HashSet<>();
        if (null != rwRecordPoolEList && !rwRecordPoolEList.isEmpty()) {
            for (RwRecordPoolE rwRecordPoolE : rwRecordPoolEList) {
                if (RwRecordPoolStatusVO.MERGED.getStatus().equals(rwRecordPoolE.getRecordStatus())) {
                    mergedWarehouseIdSet.add(rwRecordPoolE.getWarehouseRecordId());
                }
            }
            if (!mergedWarehouseIdSet.isEmpty()) {
                //查询所有出库单数据
                List<WarehouseRecordE> warehouseRecordEList = warehouseRecordRepository.queryWarehouseRecordByIds(mergedWarehouseIdSet);
                for (WarehouseRecordE warehouseRecordE : warehouseRecordEList) {
                    // 如果有一条出库单数据状态为 2-已同步，则不允许取消订单解锁库存
                    if (WmsSyncStatusVO.SYNCHRONIZED.getStatus().equals(warehouseRecordE.getSyncWmsStatus())) {
                        unLockAbleFlag = false;
                        break;
                    }
                }
            }
        }
        if (unLockAbleFlag) {
            // 将前置单状态改为已取消
            int executeResult = onlineRetailE.updateToCanceled();
            AlikAssert.isTrue(executeResult > 0, ResCode.STOCK_ERROR_1013, ResCode.STOCK_ERROR_1013_DESC);
            // 将Do池订单状态改为已取消
            if (null != rwRecordPoolEList && !rwRecordPoolEList.isEmpty()) {
                executeResult = rwRecordPoolRepository.updateToCanceled(onlineRetailE.getRecordCode());
                AlikAssert.isTrue(executeResult > 0, ResCode.STOCK_ERROR_1030, ResCode.STOCK_ERROR_1030_DESC);

            }
            // 将出库单状态改为已取消
            if (null != mergedWarehouseIdSet && !mergedWarehouseIdSet.isEmpty()) {
                for (Long id : mergedWarehouseIdSet) {
                    // 更新出库单状态为已取消，使用id in (x,x,x) 的方式，可能不会命中索引，产生表锁，所以使用循环更新的方式
                    executeResult = warehouseRecordRepository.updateToCanceledFromUnSync(id);
                    AlikAssert.isTrue(executeResult > 0, ResCode.STOCK_ERROR_1016, ResCode.STOCK_ERROR_1016_DESC + ":取消订单失败，请待会再试");
                    // 可能存在已经合单的其他SO单，将已合单DO池数据状态更新为待合单，也可能不存在，所以不能根据修改条数来判断是否失败
                    rwRecordPoolRepository.updateToPreMergeByWarehouseRecordId(id);
                }
            }
            // 释放锁定库存
            List<RwRecordPoolDetailE> poolDetails = new ArrayList<>();
            rwRecordPoolEList.forEach(poolRecord -> poolDetails.addAll(poolRecord.getRwRecordPoolDetails()));
            CoreChannelOrderDO cco = null;
            boolean isSuccess = false;
            try {
                cco = warpUnLockOutStockDo(onlineRetailE, poolDetails);
                coreChannelSalesRepository.unlockStock(cco);
                for (RwRecordPoolE rwRecordPoolE : rwRecordPoolEList) {
                    executeResult = rwRecordPoolRepository.updateVersionNo(rwRecordPoolE.getId(), rwRecordPoolE.getVersionNo());
                    AlikAssert.isTrue(executeResult > 0, ResCode.STOCK_ERROR_1016, "取消订单时，该do单已被其他程序修改:doCode=" + rwRecordPoolE.getDoCode());
                }
                isSuccess = true;
            } catch (RomeException e) {
                throw e;
            } catch (Exception e) {
                throw new RomeException(ResCode.STOCK_ERROR_5025, ResCode.STOCK_ERROR_5025_DESC);
            } finally {
                if (!isSuccess && null != cco) {
                    RedisRollBackFacade.redisRollBack(cco);
                }
            }
        } else {
            throw  new RomeException(ResCode.STOCK_ERROR_1003, "已推送wms,不允许取消订单" + ":orderCode=" + orderCode);
        }
    }

    /**
     * 取消跨境电商订单，解锁库存
     * 目前暂定为出库单中sync_wms_status状态 1-未同步可以取消，2-已同步的不允许取消
     *
     * @param orderCode SO单号
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void unLockStockByCross(String orderCode) {
        OnlineRetailE onlineRetailE = frSaleRepository.queryOnlineRetailByOutRecordCode(orderCode);
        AlikAssert.isNotNull(onlineRetailE, ResCode.STOCK_ERROR_1014, ResCode.STOCK_ERROR_1014_DESC);
        if (FrontRecordStatusVO.DISABLED.getStatus().equals(onlineRetailE.getRecordStatus())) {
            throw new RomeException(ResCode.STOCK_ERROR_5017, ResCode.STOCK_ERROR_5017_DESC);
        }
        //根据前置单ID查询对应的Do池数据
        List<RwRecordPoolE> rwRecordPoolEList = rwRecordPoolRepository.queryNotCanceledByFRIdWithDetails(onlineRetailE.getRecordCode());
        //已合单出库单ID集合
        Set<Long> mergedWarehouseIdSet = new HashSet<>();
        if (null != rwRecordPoolEList && !rwRecordPoolEList.isEmpty()) {
            for (RwRecordPoolE rwRecordPoolE : rwRecordPoolEList) {
                if (RwRecordPoolStatusVO.MERGED.getStatus().equals(rwRecordPoolE.getRecordStatus())) {
                    mergedWarehouseIdSet.add(rwRecordPoolE.getWarehouseRecordId());
                }
            }
            if (!mergedWarehouseIdSet.isEmpty()) {
                //查询所有出库单数据
                List<WarehouseRecordE> warehouseRecordEList = warehouseRecordRepository.queryWarehouseRecordByIds(mergedWarehouseIdSet);
                for (WarehouseRecordE warehouseRecordE : warehouseRecordEList) {
                    // 如果有一条出库单数据状态为 2-已同步，则不允许取消订单解锁库存
                    if (WmsSyncStatusVO.SYNCHRONIZED.getStatus().equals(warehouseRecordE.getSyncWmsStatus())) {
                        throw new RomeException( ResCode.STOCK_ERROR_1030, "该接口不支持支付后取消，recordCode="+warehouseRecordE.getRecordCode());
                    }
                }
            }
        }
        // 将前置单状态改为已取消
        int executeResult = onlineRetailE.updateToCanceled();
        AlikAssert.isTrue(executeResult > 0, ResCode.STOCK_ERROR_1013, ResCode.STOCK_ERROR_1013_DESC);
        // 将Do池订单状态改为已取消
        if (null != rwRecordPoolEList && !rwRecordPoolEList.isEmpty()) {
            executeResult = rwRecordPoolRepository.updateToCanceled(onlineRetailE.getRecordCode());
            AlikAssert.isTrue(executeResult > 0, ResCode.STOCK_ERROR_1030, ResCode.STOCK_ERROR_1030_DESC);
        }
        // 将出库单状态改为已取消
        if (null != mergedWarehouseIdSet && !mergedWarehouseIdSet.isEmpty()) {
            for (Long id : mergedWarehouseIdSet) {
                // 更新出库单状态为已取消，使用id in (x,x,x) 的方式，可能不会命中索引，产生表锁，所以使用循环更新的方式
                executeResult = warehouseRecordRepository.updateToCanceledFromUnSync(id);
                AlikAssert.isTrue(executeResult > 0, ResCode.STOCK_ERROR_1016, ResCode.STOCK_ERROR_1016_DESC + ":取消订单失败，请待会再试");
                // 可能存在已经合单的其他SO单，将已合单DO池数据状态更新为待合单，也可能不存在，所以不能根据修改条数来判断是否失败
                rwRecordPoolRepository.updateToPreMergeByWarehouseRecordId(id);
            }
        }
        // 释放锁定库存
        List<RwRecordPoolDetailE> poolDetails = new ArrayList<>();
        rwRecordPoolEList.forEach(poolRecord -> poolDetails.addAll(poolRecord.getRwRecordPoolDetails()));
        CoreRealStockOpDO coreRealStockOpDO = new CoreRealStockOpDO();
        boolean isSuccess = false;
        try {
            //按照虚仓或实仓解锁
            coreRealStockOpDO=onlineRetailE.packageUnlockStockObjOrder(poolDetails);
            if (!CollectionUtils.isEmpty(coreRealStockOpDO.getDetailDos())) {
                log.info("单据被释放：coreChannelOrderDO:{}", coreRealStockOpDO);
                coreRealWarehouseStockRepository.unlockStock(coreRealStockOpDO);
            }else{
                log.error(ResCode.STOCK_ERROR_1003,"解锁明细为空,单号：=>{}",onlineRetailE.getRecordCode());
            }
            for (RwRecordPoolE rwRecordPoolE : rwRecordPoolEList) {
                executeResult = rwRecordPoolRepository.updateVersionNo(rwRecordPoolE.getId(), rwRecordPoolE.getVersionNo());
                AlikAssert.isTrue(executeResult > 0, ResCode.STOCK_ERROR_1016, "取消订单时，该do单已被其他程序修改:doCode=" + rwRecordPoolE.getDoCode());
            }
            isSuccess = true;
        } catch (RomeException e) {
            throw e;
        } catch (Exception e) {
            throw e;
        } finally {
            if (!isSuccess) {
                RedisRollBackFacade.redisRollBack(coreRealStockOpDO);
            }
        }
    }

    /**
     * 标签类型(1.门店自提，2.仓库次日达，3.供应商外卖，4.供应商送门店)
     */
    @Override
    //不加事务，交易那边不提供回滚接口
    public void mergeCloudShopRecord(CloudShopDTO cloudShopDTO) {
        if (null == cloudShopDTO.getStartTime() || null == cloudShopDTO.getEndTime()) {
            throw new RomeException(ResCode.STOCK_ERROR_1017, "时间不能为空");
        }
        if (null == cloudShopDTO.getLabType()) {
            throw new RomeException(ResCode.STOCK_ERROR_1017, "标签类型不能为空");
        }
        List<FrSaleExtDO> frSaleExtDOList = new ArrayList<>();
        //一次性查询待集单的单据，查询待集单并且交易单号为空的单据
        Integer labType = cloudShopDTO.getLabType();
        if (labType == 3 || labType == 4) {
            frSaleExtDOList = frSaleExtMapper.queryNeedMergeRecordByOut(cloudShopDTO);
        } else {
            //只查询已支付-未核销的单据进行集单
            frSaleExtDOList = frSaleExtMapper.queryNeedMergeRecord(cloudShopDTO);
        }
        if (CollectionUtils.isEmpty(frSaleExtDOList)) {
            throw new RomeException(ResCode.STOCK_ERROR_1017, "待集单单据为空");
        }
        Map<String, List<FrSaleExtDO>> frSaleShopMap = frSaleExtDOList.stream().collect(Collectors.groupingBy(x -> x.getShopCode()));
        for (Map.Entry<String, List<FrSaleExtDO>> shopEntry : frSaleShopMap.entrySet()) {
            //门店编号
            String shopCode = shopEntry.getKey();
            if (labType == 3 || labType == 4) {
                //供应商直送不需要根据仓库分组,但是需要根据供应商分组
                List<String> recordCodes = shopEntry.getValue().stream().map(FrSaleExtDO::getRecordCode).distinct().collect(toList());
                //超过3000进行分拆
                List<List<String>> splitList = this.splitList(recordCodes);
                List<RwRecordPoolDetailDo> allDetails = new ArrayList<>();
                for (List<String> tempList : splitList) {
                    //通过前置单查询关联表ID集合
                    List<Long> poolDetailList = rwRecordPoolRepository.queryRecordPoolIdsByFrontCodes(tempList);
                    List<RwRecordPoolDetailDo> tempDetailList = rwRecordPoolRepository.selectPoolDetailByRecordPoolIds(poolDetailList);
                    List<String> skuCodes = tempDetailList.stream().map(RwRecordPoolDetailDo::getSkuCode).distinct().collect(toList());
                    //查询商品对应供应商
                    List<SkuSupplierDTO> skuSupplierList = skuFacade.skuSupplierList(skuCodes);
                    if (CollectionUtils.isEmpty(skuSupplierList)) {
                        throw new RomeException(ResCode.STOCK_ERROR_1017, "查询商品对应供应商集合为空，skuCode：" + org.apache.commons.lang.StringUtils.join(skuCodes.toArray(), ","));
                    }
                    Map<Long, SkuSupplierDTO> skuSupplierMap = skuSupplierList.stream().collect(Collectors.toMap(SkuSupplierDTO::getSkuId, Function.identity(), (v1, v2) -> v1));
                    for (RwRecordPoolDetailDo frSaleDetailDO : tempDetailList) {
                        if (!skuSupplierMap.containsKey(frSaleDetailDO.getSkuId())) {
                            throw new RomeException(ResCode.STOCK_ERROR_1017, "查询商品对应供应商为空,skuId:" + frSaleDetailDO.getSkuId());
                        }
                        frSaleDetailDO.setSupplierCode(skuSupplierMap.get(frSaleDetailDO.getSkuId()).getSupplierCode());
                        frSaleDetailDO.setSupplierName(skuSupplierMap.get(frSaleDetailDO.getSkuId()).getSupplierName());
                    }
                    if (CollectionUtils.isNotEmpty(tempDetailList)) {
                        allDetails.addAll(tempDetailList);
                    }
                }
                Map<String, List<RwRecordPoolDetailDo>> supplierMap = allDetails.stream().collect(Collectors.groupingBy(x -> x.getSupplierCode()));
                for (Map.Entry<String, List<RwRecordPoolDetailDo>> supplierEntry : supplierMap.entrySet()) {
                    //供应商
                    String supplierCode = supplierEntry.getKey();
                    List<RwRecordPoolDetailDo> frSaleDetailDOList = supplierEntry.getValue();
                    //对于重复行进行合并
                    Map<String, RwRecordPoolDetailDo> detailMap = new HashMap<>();
                    for (RwRecordPoolDetailDo detail : frSaleDetailDOList) {
                        RwRecordPoolDetailDo dto = detailMap.get(detail.getSkuCode());
                        if (dto != null) {
                            BigDecimal quantity = dto.getSkuQty();
                            quantity = quantity.add(detail.getSkuQty());
                            dto.setSkuQty(quantity);
                        } else {
                            detailMap.put(detail.getSkuCode(), detail);
                        }
                    }
                    List<RwRecordPoolDetailDo> details = new ArrayList<>(detailMap.values());
                    List<Long> rwPoolIds = frSaleDetailDOList.stream().map(RwRecordPoolDetailDo::getRecordPoolId).distinct().collect(toList());
                    List<String> frontRecordCodeList = rwRecordPoolRepository.queryRwPoolByRwIds(rwPoolIds);
                    String orderNoOut = shopCode + "-" + supplierCode + "-" + DateUtil.today();
                    this.packAndPushData(shopCode, orderNoOut, null, details, frontRecordCodeList, 12);
                }
            } else {
                Map<Long, List<FrSaleExtDO>> frSaleWarehouseMap = shopEntry.getValue().stream().collect(Collectors.groupingBy(x -> x.getRealWarehouseId()));
                for (Map.Entry<Long, List<FrSaleExtDO>> channelEntry : frSaleWarehouseMap.entrySet()) {
                    List<FrSaleExtDO> frSaleExtList = channelEntry.getValue();
                    //补货仓库
                    Long realWarehouseId = channelEntry.getKey();
                    List<String> recordCodes = frSaleExtList.stream().map(FrSaleExtDO::getRecordCode).distinct().collect(toList());
                    //超过3000进行分拆
                    List<RwRecordPoolDetailDo> details = this.getAllStatisticInfoList(recordCodes);
                    //组装补货单据给交易
                    RealWarehouseE realWarehouse = realWarehouseRepository.getRealWarehouseById(realWarehouseId);
                    if (null == realWarehouse) {
                        throw new RomeException(ResCode.STOCK_ERROR_1002_DESC, "仓库ID不存在,ID:" + realWarehouseId);
                    }
                    //每天唯一单号
                    String orderNoOut = shopCode + "-" + realWarehouse.getRealWarehouseCode() + "-" + DateUtil.today();
                    //推送数据到交易
                    this.packAndPushData(shopCode, orderNoOut, realWarehouse, details, recordCodes, 10);
                }
            }
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void collectSupplierRecord(CloudShopDTO cloudShopDTO) {
        if (null == cloudShopDTO.getStartTime() || null == cloudShopDTO.getEndTime()) {
            throw new RomeException(ResCode.STOCK_ERROR_1017, "时间不能为空");
        }
        if (null == cloudShopDTO.getLabType()) {
            throw new RomeException(ResCode.STOCK_ERROR_1017, "标签类型不能为空");
        }
        //查询时间范围内的供应商到家的单子
        List<FrSaleSupplierDO> frSaleSupplierDO=frSaleExtMapper.queryNeedCollectSupplierRecord(cloudShopDTO);
        if(CollectionUtils.isEmpty(frSaleSupplierDO)){
            throw new RomeException(ResCode.STOCK_ERROR_1017, "查询时间范围内的供应商到家数据为空");
        }
        String supplierDate=DateUtil.formatDate(cloudShopDTO.getEndTime());
        frSaleSupplierDO.forEach(v->v.setSupplierDate(supplierDate));
        //批量插入供应商汇总单据
        int j=frSaleSupplierMapper.batchInsert(frSaleSupplierDO);
        if(frSaleSupplierDO.size()!=j){
            throw new RomeException(ResCode.STOCK_ERROR_1002_DESC, "批量插入失败" );
        }
    }

    @Override
    public PageInfo<FrSaleSupplierDTO> queryCollectSupplierRecordByCondition(FrSaleSupplierDTO frSaleSupplierDTO) {
        PageHelper.startPage(frSaleSupplierDTO.getPageIndex(),frSaleSupplierDTO.getPageSize());
        List<FrSaleSupplierDTO> list=frSaleSupplierMapper.queryCollectSupplierRecordByCondition(frSaleSupplierDTO);
        PageInfo<FrSaleSupplierDTO> result = new PageInfo<>(list);
        return result;
    }


    @Override
    @Transactional(rollbackFor = Exception.class)
    public void pushOutNotify(String recordCode) {
        WarehouseRecordE warehouseRecordE=warehouseRecordRepository.getRecordWithDetailByCode(recordCode,null);
        if(Objects.isNull(warehouseRecordE)){
            throw new RomeException(ResCode.STOCK_ERROR_1002,"出库单据不存在");
        }
        OutNotifyDTO outNotifyDTO=new OutNotifyDTO();
        outNotifyDTO.setRecordCode(warehouseRecordE.getRecordCode());
        outNotifyDTO.setOperateTime(Objects.isNull(warehouseRecordE.getOutOrInTime())?DateUtil.format(warehouseRecordE.getCreateTime(),NORM_DATETIME_PATTERN):DateUtil.format(warehouseRecordE.getOutOrInTime(),NORM_DATETIME_PATTERN));
        outNotifyDTO.setRecordType(warehouseRecordE.getRecordType());
        List<OutNotifyDetailDTO> detailList=new ArrayList<>();
        //明细
        for(WarehouseRecordDetail detail : warehouseRecordE.getWarehouseRecordDetails()){
            OutNotifyDetailDTO notifyDetailDTO=new OutNotifyDetailDTO();
            notifyDetailDTO.setSkuCode(detail.getSkuCode());
            notifyDetailDTO.setLineNo(detail.getLineNo());
            notifyDetailDTO.setUnitCode(detail.getUnitCode());
            notifyDetailDTO.setUnit(detail.getUnit());
            notifyDetailDTO.setActualQty(detail.getActualQty());
            detailList.add(notifyDetailDTO);
        }
        outNotifyDTO.setOutNotifyDetailList(detailList);
        //出库单
        orderCenterFacade.outNotify(outNotifyDTO);
        int j=warehouseRecordRepository.updateSyncOrderStatusSuccess(warehouseRecordE.getRecordCode());
        if(j<=0){
            throw new RomeException(ResCode.STOCK_ERROR_1002,"更新sync_trade_status状态失败");
        }
    }

    /**
     * 获取所有明细
     * @return
     */
    private List<RwRecordPoolDetailDo> getAllStatisticInfoList(List<String> recordCodes) {
        List<RwRecordPoolDetailDo> allDetails = new ArrayList<>();
        //大于3000进行分拆
        List<List<String>> splitList = this.splitList(recordCodes);
        for (List<String> tempList : splitList) {
            List<Long> poolDetailList=rwRecordPoolRepository.queryRecordPoolIdsByFrontCodes(tempList);
            List<RwRecordPoolDetailDo> tempDetailList = rwRecordPoolRepository.selectPoolDetailByRecordPoolIds(poolDetailList);
            if(CollectionUtils.isNotEmpty(tempDetailList)){
                allDetails.addAll(tempDetailList);
            }
        }
        //对于重复行进行合并
        Map<String, RwRecordPoolDetailDo> resultMap = new HashMap<>();
        for (RwRecordPoolDetailDo detail : allDetails) {
            RwRecordPoolDetailDo dto = resultMap.get(detail.getSkuCode());
            if(dto != null){
                BigDecimal quantity = dto.getSkuQty();
                quantity = quantity.add(detail.getSkuQty());
                dto.setSkuQty(quantity);
            }else {
                resultMap.put(detail.getSkuCode(), detail);
            }
        }
        List<RwRecordPoolDetailDo>  singleList =  new ArrayList<>(resultMap.values());
        return singleList;
    }

    /**
     * 对list切片
     * @param list
     * @param <T>
     * @return
     */
    public <T> List<List<T>> splitList(List<T> list) {
        List<List<T>> listArray = new ArrayList<>();
        if (! validator.validCollection(list)) {return listArray;}
        if (list.size() <= PAGESIZE) {
            listArray.add(list);
            return listArray;
        }
        List<T> subList = new ArrayList<>();
        listArray.add(subList);
        for (int i=0; i<list.size(); i++) {
            subList.add(list.get(i));
            if (subList.size() == PAGESIZE) {
                if (i != list.size()-1) {
                    subList = new ArrayList<>();
                    listArray.add(subList);
                }
            }
        }
        return listArray;
    }


    /**
     * 推送数据到交易
     */
    private void packAndPushData(String shopCode, String orderNoOut,RealWarehouseE realWarehouseE, List<RwRecordPoolDetailDo> details
            ,List<String> recordCodes,Integer tradeType) {
        CloudShopPoDTO cloudShopPoDTO = new CloudShopPoDTO();
        cloudShopPoDTO.setOrderNoOut(orderNoOut);
        cloudShopPoDTO.setShopCode(shopCode);
        cloudShopPoDTO.setTradeType(tradeType);
        cloudShopPoDTO.setSource(10);
        cloudShopPoDTO.setExpectDate(new Date());
        if (null == realWarehouseE) {
            //非指定仓叫货
            cloudShopPoDTO.setRequireType(1);
        } else {
            //指定仓推送到交易叫货
            cloudShopPoDTO.setFactoryCode(realWarehouseE.getFactoryCode());
            cloudShopPoDTO.setWarehouseCode(realWarehouseE.getRealWarehouseOutCode());
            cloudShopPoDTO.setRequireType(2);
        }
        cloudShopPoDTO.setReplenishReason(10);
        cloudShopPoDTO.setCreator(-1L);
        cloudShopPoDTO.setComment("云店叫货");
        List<CloudShopPoLineDTO> poLineList = new ArrayList<>();
        for (RwRecordPoolDetailDo frSaleDetailDO : details) {
            CloudShopPoLineDTO cloudShopPoLineDTO = new CloudShopPoLineDTO();
            cloudShopPoLineDTO.setSaleUnitCode(frSaleDetailDO.getUnitCode());
            cloudShopPoLineDTO.setSkuCode(frSaleDetailDO.getSkuCode());
            cloudShopPoLineDTO.setSkuQuantity(frSaleDetailDO.getSkuQty());
            cloudShopPoLineDTO.setSupplierCode(frSaleDetailDO.getSupplierCode());
            cloudShopPoLineDTO.setSupplierName(frSaleDetailDO.getSupplierName());
            poLineList.add(cloudShopPoLineDTO);
        }
        cloudShopPoDTO.setPoLineList(poLineList);
        String message = "";
        boolean isSucc = false;
        String json = JSON.toJSONString(cloudShopPoDTO);
        log.info("集单推送数据到交易请求参数："+json);
        try {
            //推送数据到交易
            Response<String> response = transactionRemoteService.create(cloudShopPoDTO);
            if (response!= null && "0".equals(response.getCode()) && "success".equals(response.getMsg().toString())) {
                //回写交易单号
                int j = frSaleExtMapper.updateTradeNoByFrontRecordCodes(recordCodes, response.getData());
                if (j == 0) {
                    throw new RomeException(ResCode.STOCK_ERROR_1002_DESC, "回写交易单号失败，recordCodes:" + recordCodes);
                }
                isSucc = true;
                message = JSON.toJSONString(response);
            }else{
                throw new RomeException(response.getCode(),response.getMsg());
            }
        } catch (RomeException e) {
            message = e.getMessage();
            throw e;
        } catch (Exception e) {
            message = e.getMessage();
            throw e;
        } finally {
            sapInterfaceLogRepository.saveCallBackInterFaceLog(2, cloudShopPoDTO.getOrderNoOut(), "mergeCloudShopToTrade",
                    json, message, isSucc);
        }
    }


    /**
     * 修改发货单信息(详细地址、备注、手机号码，用户姓名等)
     *
     * @param paramDTO
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateOrderInfo(StockOrderRecordDTO paramDTO) {
        //查询前置SO单数据
        OnlineRetailE onlineRetailE = frSaleRepository.queryOnlineRetailByOutRecordCode(paramDTO.getOrderCode());
        AlikAssert.isNotNull(onlineRetailE, ResCode.STOCK_ERROR_1014, ResCode.STOCK_ERROR_1014_DESC);
        if (FrontRecordStatusVO.DISABLED.getStatus().equals(onlineRetailE.getRecordStatus())) {
            throw new RomeException(ResCode.STOCK_ERROR_5018, ResCode.STOCK_ERROR_5018_DESC);
        }
        //是否可以解锁并取消的标记
        boolean editableFlag = true;
        //根据前置单ID查询对应的Do池数据
        List<RwRecordPoolE> rwRecordPoolEList = rwRecordPoolRepository.queryNotCanceledByFRId(onlineRetailE.getRecordCode());
        //查询已有订单地址基本信息
        AddressE addressE = addressRepository.queryByRecordCode(onlineRetailE.getRecordCode());
        AlikAssert.isNotNull(addressE, ResCode.STOCK_ERROR_1031, ResCode.STOCK_ERROR_1031_DESC);
        addressE.setMobile(paramDTO.getMobile());
        addressE.setName(paramDTO.getName());
        addressE.setAddress(paramDTO.getAddress());
        addressE.setPostcode(paramDTO.getPostcode());
        addressE.setRemark(paramDTO.getRemark());
        addressE.setEmail(paramDTO.getEmail());
        //省市区
        addressE.setProvince(paramDTO.getProvince());
        addressE.setProvinceCode(paramDTO.getProvinceCode());
        addressE.setCity(paramDTO.getCity());
        addressE.setCityCode(paramDTO.getCityCode());
        addressE.setCounty(paramDTO.getArea());
        addressE.setCountyCode(paramDTO.getAreaCode());
        //已合单出库单ID集合
        Set<Long> mergedWarehouseIdSet = new HashSet<>();
        if (null != rwRecordPoolEList && !rwRecordPoolEList.isEmpty()) {
            for (RwRecordPoolE rwRecordPoolE : rwRecordPoolEList) {
                //更新对象MD5指纹信息
                rwRecordPoolE.bindMergeFingerprint(addressE);
                if (RwRecordPoolStatusVO.MERGED.getStatus().equals(rwRecordPoolE.getRecordStatus())) {
                    mergedWarehouseIdSet.add(rwRecordPoolE.getWarehouseRecordId());
                }
            }
            if (!mergedWarehouseIdSet.isEmpty()) {
                //查询所有出库单数据
                List<WarehouseRecordE> warehouseRecordEList = warehouseRecordRepository.queryWarehouseRecordByIds(mergedWarehouseIdSet);
                for (WarehouseRecordE warehouseRecordE : warehouseRecordEList) {
                    // 如果有一条出库单数据状态为 2-已同步，则不允许取消订单解锁库存
                    if (WmsSyncStatusVO.SYNCHRONIZED.getStatus().equals(warehouseRecordE.getSyncWmsStatus())) {
                        editableFlag = false;
                        break;
                    }
                }
            }
        }
        if (editableFlag) {
            // 将出库单状态改为已取消
            if (null != mergedWarehouseIdSet && !mergedWarehouseIdSet.isEmpty()) {
                for (Long id : mergedWarehouseIdSet) {
                    // 更新出库单状态为已取消，使用id in (x,x,x) 的方式，可能不会命中索引，产生表锁，所以使用循环更新的方式
                    int executeResult = warehouseRecordRepository.updateToCanceledFromUnSync(id);
                    AlikAssert.isTrue(executeResult > 0, ResCode.STOCK_ERROR_1016, ResCode.STOCK_ERROR_1016_DESC + ":修改订单失败，请稍后再试");
                    // 将已合单DO池数据状态更新为待合单，一定存在，所以必须根据修改条数来判断是否失败
                    executeResult = rwRecordPoolRepository.updateToPreMergeByWarehouseRecordId(id);
                    AlikAssert.isTrue(executeResult > 0, ResCode.STOCK_ERROR_1030, ResCode.STOCK_ERROR_1030_DESC);
                }
            }
            // 更新DO池数据MD5指纹信息
            for (RwRecordPoolE rwRecordPoolE : rwRecordPoolEList) {
                rwRecordPoolE.updateMergeFingerprint();
                int executeResult = rwRecordPoolRepository.updateVersionNo(rwRecordPoolE.getId(), rwRecordPoolE.getVersionNo());
                AlikAssert.isTrue(executeResult > 0, ResCode.STOCK_ERROR_1016, "修改订单时，该do单已被其他程序修改:doCode=" + rwRecordPoolE.getDoCode());
            }
            // 更新发货单基本信息,需要更新so以及所有do的地址信息
            addressRepository.updateAddressByRecordCode(addressE,rwRecordPoolEList);
        } else {
            throw  new RomeException(ResCode.STOCK_ERROR_1003, "已推送wms,不允许修改订单" + ":orderCode=" + paramDTO.getOrderCode());
        }
    }

    /**
     * 根据子do单号取消do单
     *
     * @param doCode 子do单编码
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void cancelChildDO(String doCode) {
        log.info("=====取消子do单：doCode={}", doCode);
        //1.第一步校验子do是否存在 且 不是取消状态，否则抛异常
        RwRecordPoolE pool = rwRecordPoolRepository.queryWithDetailsByDoCode(doCode);
        AlikAssert.isNotNull(pool, ResCode.STOCK_ERROR_1003, "取消子do单单号不存在：" + doCode);
        if (FrontRecordStatusVO.DISABLED.getStatus().equals(pool.getRecordStatus())) {
            throw new RomeException(ResCode.STOCK_ERROR_5017, ResCode.STOCK_ERROR_5017_DESC + ":doCode=" + doCode);
        }
        //2、第二步校验前置单是否存在，不存在直接抛异常
        //查询前置SO单数据
        OnlineRetailE onlineRetailE = frSaleRepository.queryOnlineRetailByRecordCode(pool.getFrontRecordCode());
        AlikAssert.isNotNull(onlineRetailE, ResCode.STOCK_ERROR_1014, ResCode.STOCK_ERROR_1014_DESC + ":doCode=" + doCode);

        //3、第三步 将该do单改为取消状态
        int executeResult = rwRecordPoolRepository.updateToCanceledById(pool.getId());
        AlikAssert.isTrue(executeResult > 0, ResCode.STOCK_ERROR_1030, ResCode.STOCK_ERROR_1030_DESC + ":doCode=" + doCode);


        //4、第四步根据前置单ID查询对应的Do池数据，如果该前置单下的所有子单都取消了，则将前置单置为取消
        List<RwRecordPoolE> rwRecordPoolEList = rwRecordPoolRepository.queryNotCanceledByFRIdWithDetails(onlineRetailE.getRecordCode());
        if (rwRecordPoolEList.size() < 1) {
            //小于1表示该前置单下其他子单都是取消状态，则可以将前置单置为取消
            onlineRetailE.updateToCanceled();
        }
        //如果该do单已合单
        WarehouseRecordE parentDo = null ;
        if (RwRecordPoolStatusVO.MERGED.getStatus().equals(pool.getRecordStatus())) {
            //5、第五步查询父DO,并更新父do为取消状态
            parentDo = warehouseRecordRepository.queryWarehouseRecordById(pool.getWarehouseRecordId());
            AlikAssert.isTrue(parentDo!=null && !WarehouseRecordStatusVO.OUT_ALLOCATION.getStatus().equals(parentDo.getRecordStatus()),
                    ResCode.STOCK_ERROR_1017, ResCode.STOCK_ERROR_1017_DESC + "或已出库:doCode=" + doCode);
            //取消后置单操作移到后面执行

            //6、第六步查询父单下所有的子单,除了该do单外全部置为待合单状态
            rwRecordPoolEList = rwRecordPoolRepository.queryKeysByWarehouseId(parentDo.getId());
            for (RwRecordPoolE rwRecordPoolE : rwRecordPoolEList) {
                if (!doCode.equals(rwRecordPoolE.getDoCode())) {
                    //从已合单状态改为待合单
                    rwRecordPoolRepository.updateToPreMergeById(rwRecordPoolE.getId());
                }
            }
        }
        // 7、第七步释放锁定库存
        List<RwRecordPoolDetailE> poolDetails = pool.getRwRecordPoolDetails();
        CoreChannelOrderDO cco = null;
        CoreRealStockOpDO coreRealStockOpDO=null;
        boolean isSuccess = false;
        try {
            if(FrontRecordTypeVO.CROSS_SALE_RECORD.getType().equals(onlineRetailE.getRecordType())){
                //跨境电商按照虚仓或实仓解锁
                coreRealStockOpDO=onlineRetailE.packageUnlockStockObjOrder(poolDetails);
                if (!CollectionUtils.isEmpty(coreRealStockOpDO.getDetailDos())) {
                    coreRealWarehouseStockRepository.unlockStock(coreRealStockOpDO);
                }else{
                    log.error(ResCode.STOCK_ERROR_1003,"解锁明细为空,单号：=>{}",onlineRetailE.getRecordCode());
                }
            }else{
                cco = warpUnLockOutStockDo(onlineRetailE, poolDetails);
                coreChannelSalesRepository.unlockStock(cco);
            }
            executeResult = rwRecordPoolRepository.updateVersionNo(pool.getId(), pool.getVersionNo());
            AlikAssert.isTrue(executeResult > 0, ResCode.STOCK_ERROR_1016, "取消do时，该do单已被其他程序修改:doCode=" + doCode);
            //8、第八步，如果已推送wms，则通知wms撤销父do
            if (parentDo != null) {
                if (WmsSyncStatusVO.SYNCHRONIZED.getStatus().equals(parentDo.getSyncWmsStatus())) {
                    executeResult = warehouseRecordRepository.updateToCanceledFromHasSync(parentDo.getId());
                    AlikAssert.isTrue(executeResult > 0, ResCode.STOCK_ERROR_1016, ResCode.STOCK_ERROR_1016_DESC + ",请稍后再试:doCode=" + doCode);
                    boolean syncStatus = this.notifyWmsToCancelDO(parentDo);
                    AlikAssert.isTrue(syncStatus, ResCode.STOCK_ERROR_1003, "wms不允许取消do单" + ":doCode=" + doCode);
                } else {
                    executeResult = warehouseRecordRepository.updateToCanceledFromUnSync(parentDo.getId());
                    AlikAssert.isTrue(executeResult > 0, ResCode.STOCK_ERROR_1016, ResCode.STOCK_ERROR_1016_DESC + ",请稍后再试:doCode=" + doCode);
                }
            }
            isSuccess = true;
        } catch (RomeException e) {
            log.error(e.getMessage() + "doCode=" + doCode, e);
            throw e;
        } catch (Exception e) {
            log.error(e.getMessage() + "doCode=" + doCode, e);
            throw new RomeException(ResCode.STOCK_ERROR_5025, e.getMessage());
        } finally {
            if (!isSuccess) {
                RedisRollBackFacade.redisRollBack(cco);
                RedisRollBackFacade.redisRollBack(coreRealStockOpDO);
            }
        }
        log.info("=====取消子do单成功结束：doCode={}", doCode);
    }

    /**
     * 修改子DO单信息
     * @param paramDTO
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateChildDoInfo(UpdateDoOrderInfoDTO paramDTO){
        log.info("=====更新子do单：入参={}", JSON.toJSONString(paramDTO));
        //1.第一步校验子do是否存在 且 不是取消状态，否则抛异常
        String doCode = paramDTO.getDoCode();
        RwRecordPoolE pool = rwRecordPoolRepository.queryWithDetailsByDoCode(doCode);
        AlikAssert.isNotNull(pool, ResCode.STOCK_ERROR_1003, "修改子do单单号不存在：" + doCode);
        if (FrontRecordStatusVO.DISABLED.getStatus().equals(pool.getRecordStatus())) {
            throw new RomeException(ResCode.STOCK_ERROR_5018, ResCode.STOCK_ERROR_5018_DESC + ":doCode=" + doCode);
        }
        //2、第二步校验前置单是否存在，不存在直接抛异常
        //查询前置SO单数据
        OnlineRetailE onlineRetailE = frSaleRepository.queryOnlineRetailByRecordCode(pool.getFrontRecordCode());
        AlikAssert.isNotNull(onlineRetailE, ResCode.STOCK_ERROR_1014, ResCode.STOCK_ERROR_1014_DESC + ":doCode=" + doCode);

        int executeResult;
        //如果该do单已合单
        WarehouseRecordE parentDo = null;
        if (RwRecordPoolStatusVO.MERGED.getStatus().equals(pool.getRecordStatus())) {
            //3、第三步查询父DO,并更新父do为取消状态
            parentDo = warehouseRecordRepository.queryWarehouseRecordById(pool.getWarehouseRecordId());
            AlikAssert.isTrue(parentDo!=null && !WarehouseRecordStatusVO.OUT_ALLOCATION.getStatus().equals(parentDo.getRecordStatus()),
                    ResCode.STOCK_ERROR_1017, ResCode.STOCK_ERROR_1017_DESC + "或已出库:doCode=" + doCode);
            //取消后置单操作移到后面执行

            //4、第4步查询父单下所有的子单,全部置为待合单状态
            List<RwRecordPoolE> rwRecordPoolEList = rwRecordPoolRepository.queryKeysByWarehouseId(parentDo.getId());
            for (RwRecordPoolE rwRecordPoolE : rwRecordPoolEList) {
                //从已合单状态改为待合单
                rwRecordPoolRepository.updateToPreMergeById(rwRecordPoolE.getId());
            }
        }
        // 5、第五步，修改地址信息并更新指纹信息
        if (paramDTO.getType() == 1 || paramDTO.getType() == 3) {
            //== 1 仅修改地址  == 3 明细和地址都更新
            AddressE addressE = addressRepository.queryByRecordCode(doCode);
            AlikAssert.isNotNull(addressE, ResCode.STOCK_ERROR_1031, ResCode.STOCK_ERROR_1031_DESC);
            AlikAssert.isTrue(validAddressInfo(paramDTO), ResCode.STOCK_ERROR_1003, "修改地址重要信息都为空：" + doCode);
            addressE.setMobile(paramDTO.getMobile());
            addressE.setName(paramDTO.getName());
            addressE.setAddress(paramDTO.getAddress());
            addressE.setPostcode(paramDTO.getPostcode());
            addressE.setRemark(paramDTO.getRemark());
            addressE.setEmail(paramDTO.getEmail());

            //修改省市区
            addressE.setProvince(paramDTO.getProvince());
            addressE.setProvinceCode(paramDTO.getProvinceCode());
            addressE.setCity(paramDTO.getCity());
            addressE.setCityCode(paramDTO.getCityCode());
            addressE.setCounty(paramDTO.getCounty());//county
            addressE.setCountyCode(paramDTO.getCountyCode());
            addressRepository.updateAddressForChildDo(addressE);
            //重新计算指纹信息并更新指纹信息
            pool.setUserCode(onlineRetailE.getUserCode());
            pool.bindMergeFingerprint(addressE);
            pool.updateMergeFingerprint();
        }
        List<RwRecordPoolDetailE> unlockDetails = new ArrayList<>();
        List<RwRecordPoolDetailE> poolDetails = pool.getRwRecordPoolDetails();
        // 6、第六步 更新明细
        if (paramDTO.getType() == 2 || paramDTO.getType() == 3) {
            //== 2 仅修改明细  == 3 明细和地址都更新，明细不能为空
            List<OrderDetailDTO> detailDTOS = paramDTO.getFrontRecordDetails();
            AlikAssert.isNotEmpty(detailDTOS, ResCode.STOCK_ERROR_1003, "修改子do单明细 单入参明细不存在");
            Set<String> skuCodeList = new HashSet<>();
            Map<String, RwRecordPoolDetailE> poolDetailsMap = RomeCollectionUtil.listforMap(poolDetails, "skuCode");
            for (OrderDetailDTO dto : detailDTOS) {
                String skuCode = dto.getSkuCode();
                if (skuCodeList.contains(skuCode)) {
                    throw new RomeException(ResCode.STOCK_ERROR_1002, ResCode.STOCK_ERROR_1002_DESC + ": 不允许有相同物料出现多行");
                }
                skuCodeList.add(skuCode);
                if (!poolDetailsMap.containsKey(skuCode)) {
                    throw new RomeException(ResCode.STOCK_ERROR_1003, "修改子do单明细,明细在do单中不存在doCode=" + doCode + ",skuCode=" + skuCode);
                }
                RwRecordPoolDetailE detailE = poolDetailsMap.get(skuCode);
                if (dto.getSkuQty().compareTo(BigDecimal.ZERO) < 0) {
                    //入参不能小于0，可以等于0
                    throw new RomeException(ResCode.STOCK_ERROR_1003, "修改子do单明细,不能小于0：" + doCode + ",skuCode=" + skuCode);
                }
                if (detailE.getSkuQty().compareTo(dto.getSkuQty()) <= 0) {
                    //数量只能改小，不能改大，也不能相等， 相等不应该传
                    throw new RomeException(ResCode.STOCK_ERROR_1003, "修改子do单明细,明细只能改小(也不能相等， 相等不应该传)：" + doCode + ",skuCode=" + skuCode);
                }
                //根据基本单位与销售单位的数量反算计算比例
                BigDecimal scale = detailE.getBasicSkuQty().divide(detailE.getSkuQty(), StockCoreConsts.DECIMAL_POINT_NUM,ROUND_DOWN);
                //销售单位转换为基本单位
                BigDecimal actQty = dto.getSkuQty().multiply(scale);
                //更新明细数量【销售单位和基本单位的数量都要更新】
                rwRecordPoolRepository.updateQty(detailE.getId(), dto.getSkuQty(), actQty);
                AlikAssert.isTrue(this.validPoolHasEffectDetails(doCode), ResCode.STOCK_ERROR_1003, "请调用取消接口而非修改接口:" + doCode);
                //计算需要释放的量
                BigDecimal needUnlockQty = detailE.getBasicSkuQty().subtract(actQty);
                detailE.setBasicSkuQty(needUnlockQty);
                unlockDetails.add(detailE);

            }

        }

        // 7、第七步释放锁定库存
        CoreChannelOrderDO cco = null;
        boolean isSuccess = false;
        try {
            if (unlockDetails.size() > 0) {
                cco = warpUnLockOutStockDo(onlineRetailE, unlockDetails);
                coreChannelSalesRepository.unlockStock(cco);
            }
            executeResult = rwRecordPoolRepository.updateVersionNo(pool.getId(), pool.getVersionNo());
            AlikAssert.isTrue(executeResult > 0, ResCode.STOCK_ERROR_1016, "修改do时，该do单已被其他程序修改:doCode=" + doCode);

            //8、第八步，如果已推送wms，则通知wms撤销父do
            if (parentDo != null) {
                if (WmsSyncStatusVO.SYNCHRONIZED.getStatus().equals(parentDo.getSyncWmsStatus())) {
                    executeResult = warehouseRecordRepository.updateToCanceledFromHasSync(parentDo.getId());
                    AlikAssert.isTrue(executeResult > 0, ResCode.STOCK_ERROR_1016, ResCode.STOCK_ERROR_1016_DESC + ",请稍后再试:doCode=" + doCode);
                    boolean syncStatus = this.notifyWmsToCancelDO(parentDo);
                    AlikAssert.isTrue(syncStatus, ResCode.STOCK_ERROR_1003, "wms不允许修改do单" + ":doCode=" + doCode);
                } else {
                    executeResult = warehouseRecordRepository.updateToCanceledFromUnSync(parentDo.getId());
                    AlikAssert.isTrue(executeResult > 0, ResCode.STOCK_ERROR_1016, ResCode.STOCK_ERROR_1016_DESC + ",请稍后再试:doCode=" + doCode);
                }
            }
            isSuccess = true;
        } catch (RomeException e) {
            log.error(e.getMessage() + "doCode=" + doCode, e);
            throw e;
        } catch (Exception e) {
            log.error(e.getMessage() + "doCode=" + doCode, e);
            throw new RomeException(ResCode.STOCK_ERROR_5025, ResCode.STOCK_ERROR_5025_DESC);
        } finally {
            if (!isSuccess) {
                RedisRollBackFacade.redisRollBack(cco);
            }
        }
        log.info("=====修改子do单成功结束：doCode={}", doCode);
    }






    /**
     * 下单前查询满足下单条件的所有仓库列表
     *
     * @param stockOrderDTO
     * @return
     */
    @Override
    public List<RealWarehouse> querySatisfyRealhouse(StockOrderDTO stockOrderDTO) {
        long time = System.currentTimeMillis();
        log.info("查询满足条件的仓库开始：订单号={},渠道={}" , stockOrderDTO.getOrderCode(), stockOrderDTO.getChannelCode());
        ChannelSalesE channelSalesE = channelSalesRepository.queryByChannelCode(stockOrderDTO.getChannelCode());
        AlikAssert.isNotNull(channelSalesE, ResCode.STOCK_ERROR_5028, "下单前查询满足下单条件的仓库：" + ResCode.STOCK_ERROR_5028_DESC + ":" + stockOrderDTO.getChannelCode());
        List<VirtualWarehouseE> vwList = virtualWarehouseRepository.queryByGroupId(channelSalesE.getVirtualWarehouseGroupId());
        AlikAssert.isTrue(vwList != null && vwList.size() > 0, ResCode.STOCK_ERROR_5029, "下单前查询满足下单条件的仓库：" + ResCode.STOCK_ERROR_5029_DESC + ":" + stockOrderDTO.getChannelCode());
        OnlineRetailE onlineRetailE = skuStockConvertor.dtoToEntity(stockOrderDTO);
        //单位转换与skuid与skucode转换
        onlineRetailE.convertRealToBasic(onlineRetailE.getFrontRecordDetails(),onlineRetailE.getMerchantId());
        onlineRetailE.convertSkuCode(onlineRetailE.getFrontRecordDetails(),onlineRetailE.getMerchantId());

        List<String> skuCodes = RomeCollectionUtil.getValueList(stockOrderDTO.getFrontRecordDetails(), "skuCode");

        //将组合品转为子品进行查询
        List<OrderDetailDTO> newDetails = new ArrayList<>();
        List<SkuInfoExtDTO> sieList =  skuFacade.skusBySkuCode(skuCodes,stockOrderDTO.getMerchantId());
        if (!validator.validCollection(sieList)) {
            throw new RomeException(ResCode.STOCK_ERROR_1002,"商品接口查询异常");
        }
        Map<String, SkuInfoExtDTO> sceMap = RomeCollectionUtil.listforMap(sieList, "skuCode", null);
        List<Long> combineSkuIds = new ArrayList<>();
        for (OnlineRetailRecordDetailE dto : onlineRetailE.getFrontRecordDetails()) {
            SkuInfoExtDTO sie = sceMap.get(dto.getSkuCode());
            if (null != sie) {
                if (SkuCombineTypeVO.P_COMPOSE.getType().equals(sie.getCombineType())) {
                    combineSkuIds.add(dto.getSkuId());
                }
            }
        }
        Map<Long, List<SkuCombineExtDTO>> combineSkuMap = new HashMap<>();
        if (combineSkuIds.size() > 0) {
            combineSkuMap = skuFacade.skuCombinesByCombineSkuId(combineSkuIds, stockOrderDTO.getMerchantId());
        }
        for (OnlineRetailRecordDetailE detailE : onlineRetailE.getFrontRecordDetails()) {
            if (combineSkuIds.contains(detailE.getSkuId())) {
                List<SkuCombineExtDTO> dtoList = combineSkuMap.get(detailE.getSkuId());
                //如果是组合品，但是不存在组合关系，则直接删除查询结果
                if (!validator.validCollection(dtoList)) {
                    throw new RomeException(ResCode.STOCK_ERROR_1002, "是组合品，但是不存在组合关系：skuCode = " + detailE.getSkuCode() + ",merchantId=" + stockOrderDTO.getMerchantId());
                }
                for (SkuCombineExtDTO sce : dtoList) {
                    OrderDetailDTO d = new OrderDetailDTO();
                    d.setSkuCode(sce.getCombineSkuCode());
                    d.setSkuId(sce.getCombineSkuId());
                    d.setSkuQty(detailE.getBasicSkuQty());
                    newDetails.add(d);
                }
            } else {
                OrderDetailDTO d = new OrderDetailDTO();
                d.setSkuCode(detailE.getSkuCode());
                d.setSkuId(detailE.getSkuId());
                d.setSkuQty(detailE.getBasicSkuQty());
                newDetails.add(d);
            }
        }
        skuCodes = RomeCollectionUtil.getValueList(newDetails, "skuCode");
        List<Long> realIdS = new ArrayList<>();
        for (VirtualWarehouseE vw : vwList) {
            List<VirtualWarehouseStockDo> virtualWarehouseStockDos = virtualWarehouseStockRepository.listStockBySkuCodesWarehouseId(skuCodes, vw.getId());
            Map<String, VirtualWarehouseStockDo> skuMap = RomeCollectionUtil.listforMap(virtualWarehouseStockDos, "skuCode");
            boolean satisfy = true;
            for (OrderDetailDTO dto : newDetails) {
                String skuCode = dto.getSkuCode();
                if (skuMap.containsKey(skuCode)) {
                    if (skuMap.get(skuCode).getAvailableQty().compareTo(dto.getSkuQty()) < 0) {
                        satisfy = false;
                        break;
                    }
                } else {
                    satisfy = false;
                    break;
                }
            }
            if (satisfy) {
                realIdS.add(vw.getRealWarehouseId());
            }
        }
        if (realIdS.size() < 1) {
            log.info("查询无满足条件的仓库：订单号={},渠道={}", stockOrderDTO.getOrderCode(), stockOrderDTO.getChannelCode());
            return new ArrayList<>();
        }
        List<RealWarehouseE> warehouseES = realWarehouseRepository.queryWarehouseByIds(realIdS);
        log.info("查询满足条件的仓库：订单号={},渠道={},结果={},耗时={}", stockOrderDTO.getOrderCode(), stockOrderDTO.getChannelCode(),
                JSON.toJSONString(realIdS), (System.currentTimeMillis() - time));
        return realWarehouseConvertor.entityToDto(warehouseES);
    }

    /**
     * 指定do单进行合单,只处理7天之前的处于待合单状态的单子
     *
     * @param doCodes
     */
    @Override
    public void assignRecordToMerge(List<String> doCodes,Integer type) {
        List<RwRecordPoolE> results = rwRecordPoolRepository.queryByDoCodesForMerge(doCodes,type);
        AlikAssert.isTrue(results.size() == doCodes.size(), ResCode.STOCK_ERROR_1002, ResCode.STOCK_ERROR_1002_DESC + ":有do不存在或状态或创建时间不对");
        Map<String, RwPoolCacher> cacheMap = new ConcurrentHashMap<>();
        processPoolData(cacheMap, results);
        handleMergeOrder(cacheMap.values());
    }

    private void handleMergeOrder(Collection<RwPoolCacher> cachers){
        for (RwPoolCacher cacher : cachers) {
            if (cacher.isSingle()) {
                RwRecordPoolE recordPoolE = cacher.getEntity();
                OnlineRetailE onlineRetailE = recordPoolE.queryFrontRecord();
                if (null != onlineRetailE) {
                    onlineRetailE.setRwRecordPoolEList(new ArrayList<>());
                    onlineRetailE.getRwRecordPoolEList().add(recordPoolE);
                    //添加出库单及明细，并合单
                    addRecordAndMerge(onlineRetailE);
                }
            } else {
                List<RwRecordPoolE> rwRecordPoolEList = cacher.getEntityList();
                Map<Long, OnlineRetailE> onlineRetailEMap = new ConcurrentHashMap<>();
                for (RwRecordPoolE recordPoolE : rwRecordPoolEList) {
                    OnlineRetailE onlineRetailE = onlineRetailEMap.get(recordPoolE.getFrontRecordId());
                    if (null == onlineRetailE) {
                        onlineRetailE = recordPoolE.queryFrontRecord();
                        if (null == onlineRetailE) {
                            continue;
                        }
                        onlineRetailEMap.put(onlineRetailE.getId(), onlineRetailE);
                    }
                    if (null == onlineRetailE.getRwRecordPoolEList()) {
                        onlineRetailE.setRwRecordPoolEList(new ArrayList<>());
                    }
                    onlineRetailE.getRwRecordPoolEList().add(recordPoolE);
                }
                //添加出库单及明细，并合单
                addRecordAndMerge(new ArrayList<>(onlineRetailEMap.values()));
            }
        }
    }

    /**
     * 合并订单
     */
    @Override
    public void mergeOrders(Integer needCombine) {
        //一次性分页方式抓取所有待合单数据
        Collection<RwPoolCacher> cachers = fetchPreMergedRecords(needCombine);
        handleMergeOrder(cachers);
        // start 计算电商停发,根据单据列表,异步方法
        List<String> recordCodeList = new ArrayList<>();
        try {
        	for (RwPoolCacher cacher : cachers) {
        		if (cacher.isSingle()) {
        			RwRecordPoolE recordPoolE = cacher.getEntity();
                    if(recordPoolE!=null && recordPoolE.getWarehouseRecordCode()!=null) {
                    	if(!recordCodeList.contains(recordPoolE.getWarehouseRecordCode())) {
                    		recordCodeList.add(recordPoolE.getWarehouseRecordCode());
                    	}
                    }
        		} else {
        			List<RwRecordPoolE> rwRecordPoolEList = cacher.getEntityList();
        			if(rwRecordPoolEList != null) {
        				for (RwRecordPoolE recordPoolE : rwRecordPoolEList) {
        					if(recordPoolE.getWarehouseRecordCode()!=null && !recordCodeList.contains(recordPoolE.getWarehouseRecordCode())) {
                        		recordCodeList.add(recordPoolE.getWarehouseRecordCode());
                        	}
            			}
        			}
        		}
        	}
        	if(recordCodeList.size() > 0) {
        		SpringBeanUtil.getBean(ShopRetailService.class).calculateStopDeliveryByRecordCodeListAsyn(recordCodeList, null);
        	}        	
		} catch (Exception e) {
			log.error("计算电商停发,根据单据列表,异步方法,处理数据出错，recordCode={},{}", recordCodeList, e);
		}
        recordCodeList = null;
        // end 计算电商停发,根据单据列表,异步方法
    }

    /**
     * 添加出库单及明细，并合单
     *
     * @param frontRecord
     */
    private void addRecordAndMerge(OnlineRetailE frontRecord) {
        try {
            OnlineRetailWarehouseRecordE outRecordE = entityFactory.createEntity(OnlineRetailWarehouseRecordE.class);
            outRecordE.addRecordAndMerge(frontRecord);
        } catch (Exception e) {
            log.error("merge orders error! ", e);
        }
    }

    /**
     * 添加出库单及明细，并合单
     *
     * @param frontRecords
     */
    private void addRecordAndMerge(List<OnlineRetailE> frontRecords) {
        try {
            OnlineRetailWarehouseRecordE outRecordE = entityFactory.createEntity(OnlineRetailWarehouseRecordE.class);
            outRecordE.addRecordAndMerge(frontRecords);
        } catch (Exception e) {
            log.error("merge orders error! ", e);
        }
    }

    /**
     * 分页拉取待合单数据
     *
     * @return 待合单数据
     */
    private Collection<RwPoolCacher> fetchPreMergedRecords(Integer needCombine) {
        int page;
        int currentPage = 1;
        int maxResult = 200;
        Map<String, RwPoolCacher> cacheMap = new ConcurrentHashMap<>();
        List<RwRecordPoolE> tempResult;
        do {
            page = (currentPage - 1) * maxResult;
            tempResult = rwRecordPoolRepository.queryAllPreMergedWithDetails(page, maxResult,needCombine);
            processPoolData(cacheMap, tempResult);
//            tempResult.clear();
            currentPage++;
        } while (tempResult.size() == maxResult);
        return cacheMap.values();
    }

    /**
     * 包装处理Do池数据
     */
    private void processPoolData(Map<String, RwPoolCacher> cacheMap, List<RwRecordPoolE> tempResult) {
        RwPoolCacher poolCache;
        for (RwRecordPoolE entity : tempResult) {
            if (! entity.getNeedCombineFlag()) {
                //如果不支持合单，直接根据uuid作为键，包装返回
                cacheMap.put(UUID.randomUUID().toString(), new RwPoolCacher(entity));
            } else {
                poolCache = cacheMap.get(entity.getMergeFingerprint());
                if (null == poolCache) {
                    cacheMap.put(entity.getMergeFingerprint(), new RwPoolCacher(entity));
                } else {
                    poolCache.put(entity);
                }
            }
        }
    }

    /**
     * 根据退货预入库单号，查询退货预入库单信息
     * @param recordCode   退货预入库单号
     */
    @Override
    public PredictReturnDTO queryPredictReturnInfoByCode(String recordCode) {
        PredictReturnRecordE predictReturnRecordE = predictReturnRepository.queryPredictReturnInfoByCode(recordCode);
        return predictReturnRecordConvertor.entityToDTO(predictReturnRecordE);
    }

    /**
     * 根据退货预入库单号，查询退货预入库单信息
     * 明细扣除已匹配数量
     * @param recordCode   退货预入库单号
     */
    @Override
    public PredictReturnDTO queryPredictReturnNotMatchInfoByCode(String recordCode) {
        PredictReturnRecordE predictReturnRecordE = predictReturnRepository.queryPredictReturnInfoByCode(recordCode);
        if (null != predictReturnRecordE) {
            List<PredictReturnMatchResultDO> matchResults = predictReturnRepository.queryMatchResultByRecordCode(recordCode);
            Map<String, List<PredictReturnMatchResultDO>> matchMap = RomeCollectionUtil.listforListMap(matchResults, "skuCode");
            for (PredictReturnRecordDetailE detailDO : predictReturnRecordE.getFrontRecordDetails()) {
                if (matchMap.containsKey(detailDO.getSkuCode())) {
                    for (PredictReturnMatchResultDO matchResultDO : matchMap.get(detailDO.getSkuCode())) {
                        detailDO.setSkuQty(detailDO.getSkuQty().subtract(matchResultDO.getSkuQty()));
                    }
                }
            }
        }
        return predictReturnRecordConvertor.entityToDTO(predictReturnRecordE);
    }

    /**
     * 电商出库WMS确认出库回调
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public  Boolean warehouseOutNotify(Long warehouseRecordId ,boolean isVirtual,List<AbstractCoreBaseRollBack> rollBackDos) {
        //查询出库单
        WarehouseRecordE warehouseRecordE = warehouseRecordRepository.getRecordWithDetailById(warehouseRecordId);
        AlikAssert.isNotNull(warehouseRecordE, ResCode.STOCK_ERROR_1017, ResCode.STOCK_ERROR_1017_DESC);
        // 解锁库存，扣减库存
        List<RwRecordPoolE> pools = rwRecordPoolRepository.queryKeysByWarehouseId(warehouseRecordId);
        AlikAssert.isTrue(pools.size() > 0, ResCode.STOCK_ERROR_1003, ResCode.STOCK_ERROR_1003_DESC + ":拆单数据为空,后置单code=" + warehouseRecordE.getRecordCode());

//        List<CoreChannelOrderDO> rollBackDos = new ArrayList<>();
        boolean isSuccess = false;
        try {
            List<String> orderCodeList = new ArrayList<>();
            for (RwRecordPoolE pool : pools) {
                List<Long> pid = new ArrayList<>();
                pid.add(pool.getId());
                List<RwRecordPoolDetailE> poolDetails = rwRecordPoolRepository.queryByRwPoolIds(pid);
                //前置端对应的某一个后置单出库回调后，就将前置单置为已出库状态，后面对应的出库单回调就不再更新状态了
                OnlineRetailE frontRecordE ;
                if (warehouseRecordE.getRecordType().equals(WarehouseRecordTypeVO.ONLINE_RETAILERS_OUT_RECORD.getType())
                ||warehouseRecordE.getRecordType().equals(WarehouseRecordTypeVO.CROSS_RETAILERS_OUT_RECORD.getType())) {
                    frontRecordE = frSaleRepository.queryOnlineRetailByRecordCode(pool.getFrontRecordCode());
                    AlikAssert.isNotNull(frontRecordE, ResCode.STOCK_ERROR_1014, ResCode.STOCK_ERROR_1014_DESC);
                    orderCodeList.add(frontRecordE.getOutRecordCode());
                    // 更新前置单状态为已出库
                    frontRecordE.updateToOutAllocation();
                } else {
                    //旺店通
                    WDTOnlineRetailE wdtOnlineRetailE = frWDTSaleRepository.queryByRecordCode(pool.getFrontRecordCode());
                    AlikAssert.isNotNull(wdtOnlineRetailE, ResCode.STOCK_ERROR_1014, ResCode.STOCK_ERROR_1014_DESC);
                    orderCodeList.add(wdtOnlineRetailE.getOutRecordCode());
                    // 更新前置单状态为已出库
                    wdtOnlineRetailE.updateToOutAllocation();
                    frontRecordE = wdtOnlineRetailE.convertToNormalOnlineRetailE();
                }
                //跨境电商出库
                if(FrontRecordTypeVO.CROSS_SALE_RECORD.getType().equals(frontRecordE.getRecordType())){
                    CoreRealStockOpDO coreRealStockOpDO = frontRecordE.packOutAndUnlockStockObjForDown(poolDetails);
                    coreRealWarehouseStockRepository.outAndUnlockStock(coreRealStockOpDO);
                    rollBackDos.add(coreRealStockOpDO);
                }else{
                    CoreChannelOrderDO cco = warpUnLockOutStockDo(frontRecordE, poolDetails);
                    rollBackDos.add(cco);
                    coreChannelSalesRepository.outStock(cco);
                }
            }
            /*对于虚拟订单尝试同步推送出库信息给捋单*/
            if (isVirtual) {
                try {
                    WarehouseRecordPageDTO pageDTO = warehouseRecordConvertor.warehouseEntityToPageDto(warehouseRecordE);
                    fulfillmentJobService.syncDeliveryFulfillment(pageDTO);
                } catch (Exception e) {
                    //同步推送捋单抛异常不回滚，后续有jop补偿机制
                    log.error("虚拟订单同步推送交货信息给捋单失败[" + warehouseRecordE.getRecordCode() + "]:" + e.getMessage(), e);
                }
            }
            try {
                orderTrackFacade.save(orderCodeList , "出库回调，订单发货(发券)完成" ,"");
            } catch (Exception e) {
                log.error(e.getMessage() ,e );
            }
            isSuccess = true;
        } catch (RomeException e) {
            throw e;
        } catch (Exception e) {
            throw e;
        } finally {
//            if (!isSuccess) {
//                for (CoreChannelOrderDO dto : rollBackDos) {
//                    RedisRollBackFacade.redisRollBack(dto);
//                }
//            }
        }
        return isSuccess;
    }

    /**
     * 根据运单号和物流公司编码，查询退货预入库单信息
     *
     * @param expressCode   运单号
     * @param logisticsCode 物流公司编码
     */
    @Override
    public PredictReturnDTO queryPredictReturnInfo(String expressCode, String logisticsCode) {
        PredictReturnRecordE predictReturnRecordE = predictReturnRepository.queryPredictReturnInfo(expressCode, logisticsCode);
        return predictReturnRecordConvertor.entityToDTO(predictReturnRecordE);
    }

    /**
     * 退货预入库单完全匹配回调通知
     *
     * @param expressCode   运单号
     * @param logisticsCode 物流公司编码
     */
    @Override
    public void matchFullNotify(String expressCode, String logisticsCode) {
        int executeResult = predictReturnRepository.updateToFullMatch(expressCode, logisticsCode);
        AlikAssert.isTrue(executeResult > 0, ResCode.STOCK_ERROR_5022, ResCode.STOCK_ERROR_5022_DESC);
    }

    /**
     * 退货预入库单部分匹配回调通知
     *
     * @param expressCode   运单号
     * @param logisticsCode 物流公司编码
     */
    @Override
    public void matchPartNotify(String expressCode, String logisticsCode) {
        int executeResult = predictReturnRepository.updateToPartMatch(expressCode, logisticsCode);
        AlikAssert.isTrue(executeResult > 0, ResCode.STOCK_ERROR_5022, ResCode.STOCK_ERROR_5022_DESC);
    }

    /**
     * 退货预入库回调通知
     * @param paramDTO
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void matchNotify(List<PredictReturnParamDTO> paramDTO) {
        List<PredictReturnParamDTO> needHandleList = new ArrayList<>();
        Map<String , List<PredictReturnParamDTO>> recordMap = RomeCollectionUtil.listforListMap(paramDTO , "predictRecordCode");
        for(Map.Entry<String, List<PredictReturnParamDTO>> entry : recordMap.entrySet()){
            String recordCode = entry.getKey();
            PredictReturnRecordE returnRecordE = predictReturnRepository.queryPredictReturnInfoByCode(recordCode);
            AlikAssert.isNotNull(returnRecordE,ResCode.STOCK_ERROR_1002,ResCode.STOCK_ERROR_1002_DESC + ",退货预入库单不存在：" + recordCode);
            Map<String, PredictReturnRecordDetailE> detailMap = RomeCollectionUtil.listforMap(returnRecordE.getFrontRecordDetails(), "skuCode");
            List<PredictReturnMatchResultDO> matchResults = predictReturnRepository.queryMatchResultByRecordCode(recordCode);
            Map<String, List<PredictReturnMatchResultDO>> matchMap = RomeCollectionUtil.listforListMap(matchResults,"skuCode");
            List<String> keyList = new ArrayList<>();
            for (PredictReturnParamDTO dto : entry.getValue()) {
                String skuCode = dto.getSkuCode();
                String key = skuCode + "_" + dto.getReverseOrderNo();
                if (keyList.contains(key)) {
                    throw new RomeException(ResCode.STOCK_ERROR_1002, ResCode.STOCK_ERROR_1002_DESC + ":有重复行");
                } else {
                    keyList.add(key);
                }
                //单据存在校验
                if (!detailMap.containsKey(skuCode)) {
                    throw new RomeException(ResCode.STOCK_ERROR_1002, ResCode.STOCK_ERROR_1002_DESC + ",物料不存在：" + recordCode + "_" + skuCode);
                }

                if(!(returnRecordE.getExpressCode().equals(dto.getExpressCode()) && returnRecordE.getLogisticsCode().equals(dto.getLogisticsCode()))){
                    throw new RomeException(ResCode.STOCK_ERROR_1002, ResCode.STOCK_ERROR_1002_DESC + ",物流公司编码或运单号错误:" + recordCode);
                }

                //幂等校验
                if (matchMap.containsKey(skuCode)) {
                    List<PredictReturnMatchResultDO> matchResultList = matchMap.get(skuCode);
                    List<String> reverseOrderNoList = RomeCollectionUtil.getValueList(matchResultList, "reverseOrderNo");
                    if (reverseOrderNoList.contains(dto.getReverseOrderNo())) {
                        continue;
                    }
                }

                //匹配数量检验
                PredictReturnRecordDetailE detailE = detailMap.get(dto.getSkuCode());
                if (detailE.getMatchQty().add(dto.getSkuQty()).compareTo(detailE.getSkuQty()) > 0) {
                    throw new RomeException(ResCode.STOCK_ERROR_1002, ResCode.STOCK_ERROR_1002_DESC + ",匹配数大于退货预入库数：" + recordCode + "_" + skuCode);
                }
                detailE.setMatchQty(detailE.getMatchQty().add(dto.getSkuQty()));

                //并发校验
                int res = predictReturnRepository.updateHasMatchQty(detailE);
                AlikAssert.isTrue(res > 0, ResCode.STOCK_ERROR_1200, ResCode.STOCK_ERROR_1200_DESC + ",请重试");
                detailE.setVersionNo(detailE.getVersionNo() + 1);

                dto.setUnit(detailE.getUnit());
                dto.setUnitCode(detailE.getUnitCode());
                dto.setSkuId(detailE.getSkuId());
                dto.setFrontRecordId(detailE.getFrontRecordId());
                needHandleList.add(dto);
            }
        }
        if (needHandleList.size() > 0) {
            predictReturnRepository.saveMatchResult(needHandleList);
        }

    }


    /**
     * 校验电商下单锁库存接口参数
     *
     * @return 校验结果
     */
    private ValidResult validOnlineAddOrderParam(StockOrderDTO stockOrderDTO) {
        if (null == stockOrderDTO) {
            return new ValidResult(ResCode.STOCK_ERROR_1002, ResCode.STOCK_ERROR_1002_DESC);
        }
//        if (!OnlineStockTransTypeVO.typeExist(stockOrderDTO.getTransType())) {
//            return new ValidResult(ResCode.STOCK_ERROR_5030, ResCode.STOCK_ERROR_5030_DESC);
//        }
        if (!(OnlineStockTransTypeVO.TRANS_TYPE_4.getTransType().equals(stockOrderDTO.getTransType()) ||
                OnlineStockTransTypeVO.TRANS_TYPE_12.getTransType().equals(stockOrderDTO.getTransType()))) {
            //虚拟商品不校验地址相关参数
            if (StringUtils.isBlank(stockOrderDTO.getAddress())) {
                return new ValidResult(ResCode.STOCK_ERROR_5012, ResCode.STOCK_ERROR_5012_DESC);
            }
            if (StringUtils.isBlank(stockOrderDTO.getMobile())) {
                return new ValidResult(ResCode.STOCK_ERROR_5005, ResCode.STOCK_ERROR_5005_DESC);
            }
            if (StringUtils.isBlank(stockOrderDTO.getProvince())) {
                return new ValidResult(ResCode.STOCK_ERROR_5006, ResCode.STOCK_ERROR_5006_DESC);
            }
            if (StringUtils.isBlank(stockOrderDTO.getProvinceCode())) {
                return new ValidResult(ResCode.STOCK_ERROR_5007, ResCode.STOCK_ERROR_5007_DESC);
            }
            if (StringUtils.isBlank(stockOrderDTO.getCity())) {
                return new ValidResult(ResCode.STOCK_ERROR_5008, ResCode.STOCK_ERROR_5008_DESC);
            }
            if (StringUtils.isBlank(stockOrderDTO.getCityCode())) {
                return new ValidResult(ResCode.STOCK_ERROR_5009, ResCode.STOCK_ERROR_5009_DESC);
            }
            if (StringUtils.isBlank(stockOrderDTO.getName())) {
                return new ValidResult(ResCode.STOCK_ERROR_5013, ResCode.STOCK_ERROR_5013_DESC);
            }
        }
        if (OnlineStockTransTypeVO.TRANS_TYPE_7.getTransType().equals(stockOrderDTO.getTransType())) {
            if (stockOrderDTO.getExpectReceiveDateStart() == null) {
                return new ValidResult(ResCode.STOCK_ERROR_5030, ResCode.STOCK_ERROR_5030_DESC + "：自营外卖期望发货时间必传");
            }
        }
        if (StringUtils.isBlank(stockOrderDTO.getChannelCode())) {
            return new ValidResult(ResCode.STOCK_ERROR_5001, ResCode.STOCK_ERROR_5001_DESC);
        }
        if (StringUtils.isBlank(stockOrderDTO.getOrderCode())) {
            return new ValidResult(ResCode.STOCK_ERROR_5003, ResCode.STOCK_ERROR_5003_DESC);
        }
        if (null == stockOrderDTO.getMerchantId()) {
            return new ValidResult(ResCode.STOCK_ERROR_5004, ResCode.STOCK_ERROR_5004_DESC);
        }
        /*if (StringUtils.isBlank(stockOrderDTO.getCounty())) {
            return new ValidResult(ResCode.STOCK_ERROR_5010, ResCode.STOCK_ERROR_5010_DESC);
        }
        if (StringUtils.isBlank(stockOrderDTO.getCountyCode())) {
            return new ValidResult(ResCode.STOCK_ERROR_5011, ResCode.STOCK_ERROR_5011_DESC);
        }*/
        if (null == stockOrderDTO.getFrontRecordDetails() || stockOrderDTO.getFrontRecordDetails().isEmpty()) {
            return new ValidResult(ResCode.STOCK_ERROR_5014, ResCode.STOCK_ERROR_5014_DESC);
        }
        for (OrderDetailDTO orderDetailDTO : stockOrderDTO.getFrontRecordDetails()) {
            if (!validator.validPositiveLong(orderDetailDTO.getSkuId()) ||
                    !validator.validStr(orderDetailDTO.getSkuCode()) ||
                    !validator.validPositiveBigDecimal(orderDetailDTO.getSkuQty()) ||
                    !validator.validStr(orderDetailDTO.getUnitCode())) {
                return new ValidResult(ResCode.STOCK_ERROR_5027, ResCode.STOCK_ERROR_5027_DESC);
            }
        }
        return null;
    }

    /**
     * 校验电商下单锁库存接口参数
     *
     * @return 校验结果
     */
    private ValidResult validCrossOrderParam(CrossStockDTO crossStockDTO) {
        crossStockDTO.setMerchantId(defaultMerchantId);
        if (StringUtils.isBlank(crossStockDTO.getRealWarehouseCode())) {
            return new ValidResult(ResCode.STOCK_ERROR_5012,"仓库编号不能为空");
        }
        if (StringUtils.isBlank(crossStockDTO.getAddress())) {
            return new ValidResult(ResCode.STOCK_ERROR_5012, ResCode.STOCK_ERROR_5012_DESC);
        }
        if (StringUtils.isBlank(crossStockDTO.getMobile())) {
            return new ValidResult(ResCode.STOCK_ERROR_5005, ResCode.STOCK_ERROR_5005_DESC);
        }
        if (StringUtils.isBlank(crossStockDTO.getProvince())) {
            return new ValidResult(ResCode.STOCK_ERROR_5006, ResCode.STOCK_ERROR_5006_DESC);
        }
        if (StringUtils.isBlank(crossStockDTO.getProvinceCode())) {
            return new ValidResult(ResCode.STOCK_ERROR_5007, ResCode.STOCK_ERROR_5007_DESC);
        }
        if (StringUtils.isBlank(crossStockDTO.getCity())) {
            return new ValidResult(ResCode.STOCK_ERROR_5008, ResCode.STOCK_ERROR_5008_DESC);
        }
        if (StringUtils.isBlank(crossStockDTO.getCityCode())) {
            return new ValidResult(ResCode.STOCK_ERROR_5009, ResCode.STOCK_ERROR_5009_DESC);
        }
        if (StringUtils.isBlank(crossStockDTO.getName())) {
            return new ValidResult(ResCode.STOCK_ERROR_5013, ResCode.STOCK_ERROR_5013_DESC);
        }
        if (StringUtils.isBlank(crossStockDTO.getChannelCode())) {
            return new ValidResult(ResCode.STOCK_ERROR_5001, ResCode.STOCK_ERROR_5001_DESC);
        }
        if (StringUtils.isBlank(crossStockDTO.getOrderCode())) {
            return new ValidResult(ResCode.STOCK_ERROR_5003, ResCode.STOCK_ERROR_5003_DESC);
        }
        if (null == crossStockDTO.getMerchantId()) {
            return new ValidResult(ResCode.STOCK_ERROR_5004, ResCode.STOCK_ERROR_5004_DESC);
        }
        if (null == crossStockDTO.getFrontRecordDetails() || crossStockDTO.getFrontRecordDetails().isEmpty()) {
            return new ValidResult(ResCode.STOCK_ERROR_5014, ResCode.STOCK_ERROR_5014_DESC);
        }
        for (OrderDetailDTO orderDetailDTO : crossStockDTO.getFrontRecordDetails()) {
            if (!validator.validPositiveLong(orderDetailDTO.getSkuId()) ||
                    !validator.validStr(orderDetailDTO.getSkuCode()) ||
                    !validator.validPositiveBigDecimal(orderDetailDTO.getSkuQty()) ||
                    !validator.validStr(orderDetailDTO.getUnitCode())) {
                return new ValidResult(ResCode.STOCK_ERROR_5027, ResCode.STOCK_ERROR_5027_DESC+"skuCode:"+orderDetailDTO.getSkuCode());
            }
        }
        return null;
    }

    /**
     * 包装寻源+锁库存接口参数
     */
    private CoreChannelOrderDO warpRouteAndLockStockDO(OnlineRetailE frontRecord, AddressE addressE) {
        CoreChannelOrderDO coreChannelOrderDO = new CoreChannelOrderDO();
        //库存交易类型
        coreChannelOrderDO.setRecordCode(frontRecord.getRecordCode());
        coreChannelOrderDO.setTransType(frontRecord.getRecordType());
        coreChannelOrderDO.setMerchantId(frontRecord.getMerchantId());
        coreChannelOrderDO.setChannelCode(frontRecord.getChannelCode());
        if(addressE != null){
            coreChannelOrderDO.setCityCode(addressE.getCityCode());
        }
        List<CoreOrderDetailDO> details = new ArrayList<>();
        coreChannelOrderDO.setOrderDetailDOs(details);
        CoreOrderDetailDO detailDO;
        for (OnlineRetailRecordDetailE detailE : frontRecord.getFrontRecordDetails()) {
            detailDO = new CoreOrderDetailDO();
            detailDO.setLockQty(detailE.getBasicSkuQty());
            detailDO.setSkuId(detailE.getSkuId());
            detailDO.setSkuCode(detailE.getSkuCode());
            details.add(detailDO);
        }
        return coreChannelOrderDO;
    }



    /**
     * 包装取消订单/出库的接口参数
     */
    private CoreChannelOrderDO warpUnLockOutStockDo(OnlineRetailE frontRecordE, List<RwRecordPoolDetailE> poolDetails) {
        CoreChannelOrderDO cco = new CoreChannelOrderDO();
        cco.setRecordCode(frontRecordE.getRecordCode());
        cco.setTransType(frontRecordE.getRecordType());
        cco.setMerchantId(frontRecordE.getMerchantId());
        cco.setChannelCode(frontRecordE.getChannelCode());
        //虚仓信息
        cco.setVirtualStockOpDetailDOs(new ArrayList<>());
        CoreVirtualStockOpDO vwDo;
        for (RwRecordPoolDetailE detailE : poolDetails) {
            if(detailE.getBasicSkuQty().compareTo(BigDecimal.ZERO)> 0) {
                //虚仓
                vwDo = new CoreVirtualStockOpDO();
                vwDo.setSkuId(detailE.getSkuId());
                vwDo.setSkuCode(detailE.getSkuCode());
                vwDo.setUnlockQty(detailE.getBasicSkuQty());
                vwDo.setVirtualWarehouseId(detailE.getVirtualWarehouseId());
                vwDo.setRealWarehouseId(detailE.getRealWarehouseId());
                cco.getVirtualStockOpDetailDOs().add(vwDo);
            }
        }
        return cco;
    }

    /**
     * 校验结果包装器
     */
    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    private class ValidResult {
        private String resCode;
        private String resDesc;
    }

    /**
     * Do池本地缓存优化器(非线程安全)
     */
    private class RwPoolCacher {
        private volatile int qty = 0;
        private RwRecordPoolE rwRecordPoolE;
        private List<RwRecordPoolE> entityList;

        public RwPoolCacher(RwRecordPoolE rwRecordPoolE) {
            put(rwRecordPoolE);
        }

        public void put(RwRecordPoolE entity) {
            if (null == entity) {
                return;
            }
            if (0 == this.qty) {
                this.rwRecordPoolE = entity;
            } else {
                if (null == this.entityList) {
                    this.entityList = new ArrayList<>();
                    this.entityList.add(this.rwRecordPoolE);
                }
                this.entityList.add(entity);
                this.rwRecordPoolE = null;
            }
            this.qty++;
        }

        public boolean isSingle() {
            return this.qty == 1;
        }

        public RwRecordPoolE getEntity() {
            return this.rwRecordPoolE;
        }

        public List<RwRecordPoolE> getEntityList() {
            return this.entityList;
        }
    }

    /**
     * 根据单据池DO单号查询单据池信息
     */
    @Override
    public List<RwRecordPoolDTO> queryPoolRecordByPoolRecordCode(List<String> poolRecordCodeList) {
        List<RwRecordPoolE> recordList = rwRecordPoolRepository.queryByDoCodes(poolRecordCodeList);
        if(recordList == null || recordList.isEmpty()) {
            return new ArrayList<>();
        }
        List<Long> ids = recordList.stream().map(RwRecordPoolE::getId).distinct().collect(toList());
        List<RwRecordPoolDetailE> detailList = rwRecordPoolRepository.queryByRwPoolIds(ids);
        Map<Long, List<RwRecordPoolDetailE>> detailMap = detailList.stream().collect(Collectors.groupingBy(RwRecordPoolDetailE::getRecordPoolId));

        List<Long> rws = recordList.stream().map(RwRecordPoolE::getRealWarehouseId).distinct().collect(toList());
        List<RealWarehouseE> realWarehouseEList = realWarehouseRepository.getRealWarehouseByIds(rws);
        Map<Long, List<RealWarehouseE>> realWarehouseMap = realWarehouseEList.stream().collect(Collectors.groupingBy(RealWarehouseE::getId));

        List<RwRecordPoolDTO> resultList = new ArrayList<>();
        for(RwRecordPoolE recordPool: recordList) {
            List<RwRecordPoolDetailE> currDetailList = detailMap.get(recordPool.getId());
            recordPool.setRwRecordPoolDetails(currDetailList);
            List<RwRecordPoolDTO> rwRecordPoolDoList = rwRecordPoolConvertor.entityListToDTOList(Arrays.asList(recordPool));
            RwRecordPoolDTO rwRecordPoolDo = rwRecordPoolDoList.get(0);
            resultList.add(rwRecordPoolDo);

            List<RealWarehouseE> currRwList = realWarehouseMap.get(recordPool.getRealWarehouseId());
            if(currRwList != null) {
                RealWarehouseE rw = currRwList.get(0);
                RealWarehouse realWarehouse = realWarehouseConvertor.entityToDto(rw);
                rwRecordPoolDo.setRealWarehouse(realWarehouse);
            }
        }
        return resultList;
    }
        }
