package com.rome.stock.innerservice.domain.entity;

import com.rome.stock.innerservice.domain.entity.warehouserecord.AbstractWarehouseRecord;
import com.rome.stock.innerservice.domain.repository.VirtualWarehouseGroupRepository;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Component;

import java.util.List;

@Component
@Scope("prototype")
@Data
@EqualsAndHashCode(callSuper = false)
public class VirtualWarehouseGroupE extends BaseE {
	@Autowired
	private VirtualWarehouseGroupRepository virtualWarehouseGroupRepository;

	private VirtualWarehouseGroupE() {

	}

	/**
	 * 名称
	 */
	private String name;
	/**
	 * 虚拟仓库组编码
	 */
	private String virtualWarehouseGroupCode;
	/**
	 * 模板名称，一般是指寻源规则和合单规则等等，内容如为模板类名
	 */
	private String template;
	/**
	 * 模板描述
	 */
	private String templateDesc;
	/**
	 * 备注信息
	 */
	private String remark;
	/**
	 * 虚仓组id(作条件查询用)
	 */
	private List<Long> groupIds;

	/**
	 * 虚拟仓库id
	 */
	private  Long  virtualWarehouseId;

	/**
	 * 增加虚仓组库存
	 * @param warehouseRecord
	 * @param
	 *
	 */
	public void addVirtualWareStock(AbstractWarehouseRecord warehouseRecord) {
		//修改redis库存数据
	}


	/**
	 * 减少虚仓组库存
	 * @param warehouseRecord
	 * @param 1:减少在途 增加账面库存 2: 减少账面库存
	 *
	 */
	public void reduceVirtualWareStock(AbstractWarehouseRecord warehouseRecord) {
		//修改redis库存数据
	}

	//获取策略组与渠道的关联关系
	public List<?> getChannelSyncRelation(){
		return null;
	}


	//获取策略组与虚仓的关联关系
	public List<?> getVwGroupSyncRelation(){
		return null;
	}


	//查询库存
	public List<StockE> getVwGroupStock(){
		//查询库存
		//先查询redis库存,如果被穿透就查询虚仓组关系库存并设置redis
		return null;
	}
}



