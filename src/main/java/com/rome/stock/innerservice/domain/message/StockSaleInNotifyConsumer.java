package com.rome.stock.innerservice.domain.message;

import com.rome.stock.common.constants.KibanaLogConstants;
import com.rome.stock.common.enums.CustomRocketMQEnum;
import com.rome.scm.common.rocketmq.CustomConsumerListener;
import com.rome.stock.innerservice.api.dto.WarehouseRecordPageDTO;
import com.rome.stock.innerservice.common.CoreKibanaLog;
import com.rome.stock.innerservice.domain.convertor.WarehouseRecordConvertor;
import com.rome.stock.innerservice.domain.entity.warehouserecord.WarehouseRecordE;
import com.rome.stock.innerservice.domain.repository.SapInterfaceLogRepository;
import com.rome.stock.innerservice.domain.repository.warehouserecord.WarehouseRecordRepository;
import com.rome.stock.innerservice.domain.service.FulfillmentJobService;
import lombok.extern.slf4j.Slf4j;
import org.apache.rocketmq.common.message.MessageExt;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;

/**
 * @Description: 电商发货回传交易中心消费mq
 */
@Slf4j
@Service
public class StockSaleInNotifyConsumer implements CustomConsumerListener<MessageExt> {

    @Resource
    private SapInterfaceLogRepository sapInterfaceLogRepository;
    @Resource
    private WarehouseRecordRepository warehouseRecordRepository;
    @Resource
    private FulfillmentJobService fulfillmentJobService;
    @Resource
    private WarehouseRecordConvertor warehouseRecordConvertor;


    @Override
    public void onMessage(MessageExt messageExt, String businessNo, String msgKey) {
        log.info("电商发货回传中心消息消费，msgID: {}，消费次数: {}", messageExt.getMsgId(), messageExt.getReconsumeTimes());
        String recordCode = new String(messageExt.getBody());
        log.warn(CoreKibanaLog.getJobLog(KibanaLogConstants.CONSUMER_MQ, "stockSaleInNotifyConsumer", messageExt.getMsgId(), recordCode));
        if(StringUtils.isEmpty(recordCode)) {
            log.error("电商发货回传中心消息消费内容为空，msgID: {}", messageExt.getMsgId());
            return ;
        }
        String message = "";
        boolean isSucc = false;
        try {
            //电商及跨境单据调用交易发货接口
            WarehouseRecordE warehouseRecordE=warehouseRecordRepository.queryWarehouseRecordByRecordCode(recordCode);
            WarehouseRecordPageDTO pageDTO = warehouseRecordConvertor.warehouseEntityToPageDto(warehouseRecordE);
            fulfillmentJobService.syncDeliveryFulfillment(pageDTO);
            isSucc = true;
            message = "200";
        }catch (Exception e){
            //消息只消费一次，job去补偿
            log.error(e.getMessage(), e);
            message=e.getMessage();
        }finally {
            sapInterfaceLogRepository.saveCallBackInterFaceLog(1, recordCode, "stockSaleInNotifyConsumer",
                    recordCode, message, isSucc);
        }
    }

	@Override
	public String getRocketMQCode() {
		return CustomRocketMQEnum.MQ_STOCK_SALE_IN_NOTIFY_PUSH.getCode();
	}

}
