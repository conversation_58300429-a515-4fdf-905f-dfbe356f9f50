/**
 * Filename EntrySaleStockServiceImpl.java
 * Company 上海来伊份科技有限公司。
 * <AUTHOR>
 * @version 
 */
package com.rome.stock.innerservice.domain.service.impl;

import java.math.BigDecimal;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Calendar;
import java.util.Collection;
import java.util.Collections;
import java.util.Comparator;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.concurrent.Future;
import java.util.function.Function;
import java.util.stream.Collectors;

import javax.annotation.Resource;

import org.apache.commons.collections.CollectionUtils;
import org.elasticsearch.action.search.SearchType;
import org.elasticsearch.index.IndexNotFoundException;
import org.elasticsearch.index.query.BoolQueryBuilder;
import org.elasticsearch.index.query.QueryBuilders;
import org.elasticsearch.index.query.RangeQueryBuilder;
import org.elasticsearch.search.sort.SortBuilders;
import org.elasticsearch.search.sort.SortOrder;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.elasticsearch.core.ElasticsearchRestTemplate;
import org.springframework.data.elasticsearch.core.query.NativeSearchQuery;
import org.springframework.data.elasticsearch.core.query.NativeSearchQueryBuilder;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.rome.arch.core.exception.RomeException;
import com.rome.arch.util.SpringBeanUtil;
import com.rome.stock.common.utils.DateUtil;
import com.rome.stock.common.utils.ListUtil;
import com.rome.stock.innerservice.api.dto.EntrySaleStockDTO;
import com.rome.stock.innerservice.common.DateFormatTools;
import com.rome.stock.innerservice.common.EsIndexTypeConfig;
import com.rome.stock.innerservice.common.KibanaLogUtils;
import com.rome.stock.innerservice.common.ResCode;
import com.rome.stock.innerservice.common.StringUtils;
import com.rome.stock.innerservice.constant.EntrySaleAdjustDetailStatusVO;
import com.rome.stock.innerservice.constant.RealWarehouseStatusVO;
import com.rome.stock.innerservice.constant.WarehouseRecordBusinessTypeVO;
import com.rome.stock.core.constant.StockCoreConsts;
import com.rome.stock.core.constant.StockTransactionConsts;
import com.rome.stock.innerservice.domain.convertor.EsEntrySaleStockConvertor;
import com.rome.stock.innerservice.domain.repository.elasticsearch.EsEntrySaleStockRepository;
import com.rome.stock.innerservice.domain.service.EntrySaleStockService;
import com.rome.stock.innerservice.infrastructure.dataobject.EntrySaleAdjustDetailDO;
import com.rome.stock.innerservice.infrastructure.dataobject.EntrySaleStockDO;
import com.rome.stock.innerservice.infrastructure.dataobject.RealWarehouseDO;
import com.rome.stock.innerservice.infrastructure.dataobject.RealWarehouseStockDO;
import com.rome.stock.innerservice.infrastructure.dataobject.RwStockChangeFlowDO;
import com.rome.stock.innerservice.infrastructure.dataobject.elasticsearch.EsEntrySaleStockDO;
import com.rome.stock.innerservice.infrastructure.mapper.EntrySaleAdjustDetailMapper;
import com.rome.stock.innerservice.infrastructure.mapper.EntrySaleStockMapper;
import com.rome.stock.innerservice.infrastructure.mapper.RealWarehouseMapper;
import com.rome.stock.innerservice.infrastructure.mapper.RealWarehouseStockMapper;
import com.rome.stock.innerservice.infrastructure.mapper.RwStockChangeFlowMapper;
import com.rome.stock.innerservice.remote.item.dto.SkuInfoExtDTO;
import com.rome.stock.innerservice.remote.item.facade.SkuFacade;
import com.rome.stock.innerservice.template.inactive.EntrySaleStockInactiveTemplate;
import com.rome.stock.innerservice.template.inactive.RwStockChangeFlowInactiveTemplate;
import com.rome.stock.wms.config.BaseinfoConfiguration;
import com.rome.stock.wms.config.BaseinfoProperty;

import lombok.extern.slf4j.Slf4j;

/**
 * 库存进销存
 * <AUTHOR>
 * @since 2021-8-12 15:42:05
 */
@Service
@Slf4j
public class EntrySaleStockServiceImpl implements EntrySaleStockService {

	@Resource
	private RwStockChangeFlowMapper rwStockChangeFlowMapper;
	
	@Resource
    private RealWarehouseStockMapper realWarehouseStockMapper;
	
	@Resource
    private EntrySaleStockMapper entrySaleStockMapper;
	
	@Resource
    private RealWarehouseMapper realWarehouseMapper;
	
	@Resource(name = "coreStockTask")
	private ThreadPoolTaskExecutor coreStockTask;
	
	@Resource
    private SkuFacade skuFacade;
	
	@Autowired
	private EsEntrySaleStockRepository esEntrySaleStockRepository;
	
	@Resource
    private EsEntrySaleStockConvertor esEntrySaleStockConvertor;
	
	@Resource
    private EntrySaleAdjustDetailMapper entrySaleAdjustDetailMapper;
	
	@Autowired
	private ElasticsearchRestTemplate esTemplate;
	
	/**
	 * 生成进销存数据，生成当前时间的前一天的进销存数据，定时任务
	 */
	@Override
	public void createEntrySaleStockJob() {
		log.info("开始,异步,生成进销存数据所有");
		coreStockTask.submit(new Runnable() {
			@Override
			public void run() {
				createEntrySaleStockByAll();
			}
			});
		log.info("成功,加入线程池任务异步,生成进销存数据所有");
	}
	
	/**
	 * 生成进销存数据，生成当前时间的前一天的进销存数据
	 */
	@SuppressWarnings("rawtypes")
	@Override
	public void createEntrySaleStockByAll() {
		log.info("开始,进销存数据生成所有");
		int current = 1;
		// 每页大小 
		final int pageSize = 1000;
		List<Future> futures = new ArrayList<>();
		Future future;
		// 实仓类型列表
		List<Integer> rWTypeList = new ArrayList<>();
		// 实仓Id列表
		List<Long> rWIdList = new ArrayList<>();
		getAllowWarehouseTypeOrIds(rWTypeList, rWIdList);
		// 实仓类型列表处理
		if(rWTypeList.size() > 0) {
			// 实仓执行对账
			PageHelper.startPage(current, pageSize, false);
			List<RealWarehouseDO> list = realWarehouseMapper.getRealWarehouseFactory(rWTypeList);
			int i;
			while(list != null && list.size()>0) {
				for(RealWarehouseDO dto : list) {
					if(RealWarehouseStatusVO.DISABLED.getStatus().equals(dto.getRealWarehouseStatus())) {
						continue;
					}
					if(rWIdList.size() > 0) {
						if(rWIdList.contains(dto.getId())) {
							rWIdList.remove(dto.getId());
						}
					}
					future = coreStockTask.submit(new Runnable() {
						@Override
						public void run() {
							// 进销存数据生成
							createEntrySaleStockByWarehouse(dto);
						}});
					futures.add(future);
				}
				// 等待完成
				for(Future future2 : futures) {
					try {
						future2.get();
					} catch (Exception e) {
						log.error("进销存数据生成,等队列完成,出错{}",e);
					}
				}
				current++;
				PageHelper.startPage(current, pageSize, false);
				list = realWarehouseMapper.getRealWarehouseFactory(rWTypeList);
				for(i = 0; i < futures.size(); i++) {
					if(futures.get(i).isDone()) {
						futures.remove(i);
						i--;
					}
				}
			}
		}
		// 实仓Id列表处理
		if(rWIdList.size() > 0) {
			List<RealWarehouseDO> list = realWarehouseMapper.queryByIds(rWIdList);
			if(list != null && list.size()>0) {
				for(RealWarehouseDO dto : list) {
					if(RealWarehouseStatusVO.DISABLED.getStatus().equals(dto.getRealWarehouseStatus())) {
						continue;
					}
					future = coreStockTask.submit(new Runnable() {
						@Override
						public void run() {
							// 进销存数据生成
							createEntrySaleStockByWarehouse(dto);
						}});
					futures.add(future);
				}
			}
		}
		
		// 等待完成
		for(Future future2 : futures) {
			try {
				future2.get();
			} catch (Exception e) {
				log.error("进销存数据生成,等队列完成,出错{}",e);
			}
		}
		futures.clear();
		futures = null;
		log.info("结束,进销存数据生成所有");
	}
	
	/**
	 * 根据仓库Id重新生成进销存数据表,一般是手动
	 * @param paramDto
	 * @return
	 */
	@Override
	public void createEntrySaleStockSync(EntrySaleStockDTO paramDto) {
		if(paramDto.getRealWarehouseId() != null) {
			RealWarehouseDO realWarehouseDO = realWarehouseMapper.queryById(paramDto.getRealWarehouseId());
			if(realWarehouseDO == null) {
				throw new RomeException(ResCode.STOCK_ERROR_1002, "仓库不存在");
			}
			if(RealWarehouseStatusVO.DISABLED.getStatus().equals(realWarehouseDO.getRealWarehouseStatus())) {
				throw new RomeException(ResCode.STOCK_ERROR_1002, "仓库已停用");
			}
			// 实仓类型过滤
			final Set<Integer> filterTypeSet = new HashSet<>();
			// 实仓Id过滤
			final Set<Long> filterIdSet = new HashSet<>();
			// 初始化过滤条件
			getAllowWarehouseTypeOrIds(filterTypeSet, filterIdSet);
			// 没有要处理的wms
			if(filterTypeSet.size() == 0 && filterIdSet.size() == 0) {
				throw new RomeException(ResCode.STOCK_ERROR_1003, "没有配置仓库进销存数据生成");
			}
			// 过滤不需处理的
			if(!((realWarehouseDO.getRealWarehouseType() != null && filterTypeSet.contains(realWarehouseDO.getRealWarehouseType()))
					|| (filterIdSet.contains(realWarehouseDO.getId())))) {
				throw new RomeException(ResCode.STOCK_ERROR_1003, "仓库不在进销存数据生成范围内");
			}
			createEntrySaleStockByWarehouse(realWarehouseDO);
		} else {
			throw new RomeException(ResCode.STOCK_ERROR_1002, "参数有误，仓库realWarehouseId为空");
		}
	}
	
	/**
	 *根据仓库Id重新生成历史进销存数据表,一般是手动,根据仓和指定时间，重新生成历史进销存数据，若date+1天的没有进销存数据无法生成，
	 * 以date+1天的进销存数据的期初库存作为期末库存
	 * @param realWarehouseId
	 * @param date
	 */
	@Override
	public void createEntrySaleStockSyncHistory(Long realWarehouseId, Date date) {
		if(realWarehouseId != null) {
			RealWarehouseDO realWarehouseDO = realWarehouseMapper.queryById(realWarehouseId);
			if(realWarehouseDO == null) {
				throw new RomeException(ResCode.STOCK_ERROR_1002, "仓库不存在");
			}
			if(RealWarehouseStatusVO.DISABLED.getStatus().equals(realWarehouseDO.getRealWarehouseStatus())) {
				throw new RomeException(ResCode.STOCK_ERROR_1002, "仓库已停用");
			}
			// 实仓类型过滤
			final Set<Integer> filterTypeSet = new HashSet<>();
			// 实仓Id过滤
			final Set<Long> filterIdSet = new HashSet<>();
			// 初始化过滤条件
			getAllowWarehouseTypeOrIds(filterTypeSet, filterIdSet);
			// 没有要处理的wms
			if(filterTypeSet.size() == 0 && filterIdSet.size() == 0) {
				throw new RomeException(ResCode.STOCK_ERROR_1003, "没有配置仓库进销存数据生成");
			}
			// 过滤不需处理的
			if(!((realWarehouseDO.getRealWarehouseType() != null && filterTypeSet.contains(realWarehouseDO.getRealWarehouseType()))
					|| (filterIdSet.contains(realWarehouseDO.getId())))) {
				throw new RomeException(ResCode.STOCK_ERROR_1003, "仓库不在进销存数据生成范围内");
			}
			createEntrySaleStockHistoryByWarehouse(realWarehouseDO, date);
		}else {
			throw new RomeException(ResCode.STOCK_ERROR_1002, "参数有误，仓库realWarehouseId为空");
		}
	}
	
	/**
     * 获取允许的进销存处理仓库类型或仓Id列表
     * 进销存处理
     * @param rWTypeList 实仓类型列表
     * @param rWIdList 实仓Id列表
     */
    public static void getAllowWarehouseTypeOrIds(Collection<Integer> rWTypeList, Collection<Long> rWIdList) {
    	try {
			// 数据内容格式  {"realWarehouseIds":[1,2,3,4,5],"realWarehouseTypes":[1,2]}
			BaseinfoProperty propertyfilter = BaseinfoConfiguration.getInstance().getObject("stock.entrySaleStock", "flow.filter");
			if(propertyfilter != null && StringUtils.isNotBlank(propertyfilter.getValue())) {
				JSONObject jsonObject = JSON.parseObject(propertyfilter.getValue());
				// 仓库类型列表
				JSONArray jsonArray= jsonObject.getJSONArray("realWarehouseTypes");
				if(jsonArray != null && jsonArray.size() > 0) {
					List<Integer> tempList = new ArrayList<>(jsonArray.size());
					for(int i = 0; i < jsonArray.size(); i++) {
						tempList.add(jsonArray.getInteger(i));
					}
					rWTypeList.addAll(tempList);
				}
				// 仓库Id列表
				jsonArray= jsonObject.getJSONArray("realWarehouseIds");
				if(jsonArray != null && jsonArray.size() > 0) {
					List<Long> tempList = new ArrayList<>(jsonArray.size());
					for(int i = 0; i < jsonArray.size(); i++) {
						tempList.add(jsonArray.getLong(i));
					}
					rWIdList.addAll(tempList);
				}
			}
		} catch (Exception e) {
			log.error("获取允许的进销存处理仓库类型或仓Id列表，取值，出错", e);
		}
    }
	
    /**
     * 根据仓生成，当前时间的前一天的进销存数据
     * @param realWarehouseDO
     */
	private void createEntrySaleStockByWarehouse(RealWarehouseDO realWarehouseDO) {
		int count = 0;
		try {
			// 昨天时间
			Date yesterTime = DateUtil.yesterday();
			// 开始时间
			final Date originalStartTime = DateUtil.getDayBegin(yesterTime);
			// 结束时间
			final Date originalEndTime = DateUtil.getDayEnd(yesterTime);
			// 计算，出入库库存量
			List<EntrySaleStockDO> list = processInOutQty(realWarehouseDO.getId(), originalStartTime, originalEndTime);
			// 汇总数据及保存
			count = mergeDataSummaryAndSave(list, realWarehouseDO.getId(), originalEndTime);
		} catch (RomeException e) {
			log.error("进销存数据生成,出错,总数={},实仓id={}{}",count, realWarehouseDO.getId(), e);
			throw e;
		}catch (Exception e) {
			log.error("进销存数据生成,出错,总数={},实仓id={}{}",count, realWarehouseDO.getId(), e);
			throw new RomeException(ResCode.STOCK_ERROR_1003, e.getMessage());
		} finally {
			log.info("进销存数据生成,成功,realWarehouseId={},总数={}", realWarehouseDO.getId(), count);
		}
	}
	
	/**
     * 根据仓和指定时间，重新生成历史进销存数据，若date+1天的没有进销存数据无法生成，
     * 以date+1天的进销存数据的期初库存作为期末库存
     * @param realWarehouseDO
     */
	private void createEntrySaleStockHistoryByWarehouse(RealWarehouseDO realWarehouseDO, Date date) {
		int count = 0;
		try {
			// 历史数据迁移时间
			Integer dbSaveDay = SpringBeanUtil.getBean(RwStockChangeFlowInactiveTemplate.class).getDbSaveDay();
			if(dbSaveDay == null) {
				dbSaveDay = 30;
			}	
			Calendar cal = Calendar.getInstance();
			cal.set(Calendar.HOUR_OF_DAY, 0);
			cal.set(Calendar.MINUTE, 0);
			cal.set(Calendar.SECOND, 0);
			cal.set(Calendar.MILLISECOND, 0);
			cal.add(Calendar.DAY_OF_MONTH, -dbSaveDay);
			cal.add(Calendar.MILLISECOND, -1);
			
			Date time = cal.getTime();
			// 历史数据迁移时间,已迁移
			if(time.getTime() >= date.getTime()) {
				throw new RomeException(ResCode.STOCK_ERROR_1002, "参数有误，要生成的进销存数据日期已迁移");
			}
			// 开始时间
			final Date originalStartTime = DateUtil.getDayBegin(date);
			// 结束时间
			final Date originalEndTime = DateUtil.getDayEnd(date);
			if(originalEndTime.getTime() < realWarehouseDO.getCreateTime().getTime()) {
				throw new RomeException(ResCode.STOCK_ERROR_1002, "参数有误，要生成的进销存数据日期不能小于仓库创建日期");
			}
			Date nextDate = DateUtil.offsiteDate(originalEndTime, Calendar.DAY_OF_YEAR, 1);
			List<EntrySaleStockDO> entrySaleStockDOs = entrySaleStockMapper.queryEntrySaleStockBySync(realWarehouseDO.getId(), nextDate);
			if(entrySaleStockDOs == null || entrySaleStockDOs.size() == 0) {
				throw new RomeException(ResCode.STOCK_ERROR_1002, "参数有误，要生成的进销存数据日期+1天没有数据无法生成历史数据");
			}
			// 计算，出入库库存量
			List<EntrySaleStockDO> list = processInOutQty(realWarehouseDO.getId(), originalStartTime, originalEndTime);
			// 汇总数据及保存
			count = mergeDataSummaryAndSaveByHistory(list, realWarehouseDO.getId(), originalEndTime, entrySaleStockDOs);
		} catch (RomeException e) {
			log.error("历史进销存数据生成,出错,总数={},实仓id={}{}",count, realWarehouseDO.getId(), e);
			throw e;
		}catch (Exception e) {
			log.error("历史进销存数据生成,出错,总数={},实仓id={}{}",count, realWarehouseDO.getId(), e);
			throw new RomeException(ResCode.STOCK_ERROR_1003, e.getMessage());
		} finally {
			log.info("历史进销存数据生成,成功,realWarehouseId={},总数={}", realWarehouseDO.getId(), count);
		}
	}
	
	/**
	 * 计算进销存，出入库库存量
	 * @param realWarehouseId 实仓Id
	 * @param originalStartTime
	 * @param originalEndTime
	 * @return
	 */
	private List<EntrySaleStockDO> processInOutQty(Long realWarehouseId, final Date originalStartTime, final Date originalEndTime) {
		int currentPage = 1;
		// 每页大小 
		final int pageSize = 1000;
		// 当前开始时间
		Date startTime = new Date(originalStartTime.getTime());
		Long minId = null;
		// 单号,缓存大小
		final int cacheSize = 2000;
		int sumIndex = 0;
		// 单号,缓存
		List<String> cacheList = new ArrayList<>(cacheSize);
		for(int i = 0; i < cacheSize; i++) {
			cacheList.add(null);
		}			
		List<EntrySaleStockDO> list = new ArrayList<>();
		List<RwStockChangeFlowDO> flowList = null;
		List<String> recordCodeList;
		// 出入库抵销量的单据编码
		List<String> recordCodeListByOffset = new ArrayList<>();
		// key>>skuId  val抵销量
		Map<Long, BigDecimal> skuIdOffsetQtyMap = new HashMap<>(16);
		boolean runFlag = true;
		// 相同时间查询，开始和结束时间相同的，根据id来
		boolean isSameTime = false;
		do {
			// 查询 start  分页达到5页后，会返回从第1页开始，且开始时间，为上次查询的最大时间+1s
			// 返回第1页时，会特殊处理，会查询等于上次查询的最大时间，还没有查询到的数据
			if(isSameTime) {
				flowList = rwStockChangeFlowMapper.queryFlowEntrySaleStockByRwId(realWarehouseId, startTime, startTime, minId, (currentPage-1) * pageSize, pageSize);
				currentPage++;
				if(flowList == null || flowList.size() == 0) {
					isSameTime = false;
					startTime.setTime(startTime.getTime() + 1000);
					currentPage = 1;
				}
			} 
			if(isSameTime == false) {
				flowList = rwStockChangeFlowMapper.queryFlowEntrySaleStockByRwId(realWarehouseId, startTime, originalEndTime, null, (currentPage-1) * pageSize, pageSize);
				currentPage++;
				if(currentPage > 5 && flowList != null && flowList.size() > 0) {
					currentPage = 1;
					isSameTime = true;
					minId = flowList.get(flowList.size()-1).getId();
					startTime = flowList.get(flowList.size()-1).getCreateTime();
				}
			}
			// 查询 end
			if(flowList != null && flowList.size() > 0) {
				// 流水数据合并处理出入库量等
				recordCodeList = mergeFlowToEntrySaleStock(list, flowList);
				// 抵消量处理 start
				// 验证抵消量，并计算出抵消量，并设置进recordCodeListByOffsetMap
				validateOffsetRecordCode(skuIdOffsetQtyMap, recordCodeListByOffset, recordCodeList, realWarehouseId, originalStartTime, originalEndTime, cacheList);
				for(String recordCode : recordCodeList) {
					if(!cacheList.contains(recordCode)) {
						// 设入缓存中
						cacheList.set(getCacheIndex(cacheSize, sumIndex++), recordCode);
					}
				}
				// 抵消量处理 end
				flowList.clear();
				flowList = null;
			} else {
				runFlag = false;
			}
		} while(runFlag);
		// 减掉抵消量
		for(EntrySaleStockDO dto : list) {
			if(skuIdOffsetQtyMap.containsKey(dto.getSkuId())) {
				dto.setInQty(dto.getInQty().subtract(skuIdOffsetQtyMap.get(dto.getSkuId())));
				dto.setOutQty(dto.getOutQty().subtract(skuIdOffsetQtyMap.get(dto.getSkuId())));
			}
		}
		return list;
	}
	
	/**
	 * 汇总数据及保存
	 * @param list
	 * @param realWarehouseId
	 * @param endTime
	 * @return
	 */
	private int mergeDataSummaryAndSave(List<EntrySaleStockDO> list, Long realWarehouseId, final Date endTime) {
		List<RwStockChangeFlowDO> flowSumList = rwStockChangeFlowMapper.queryFlowEntrySaleStockSumByStartTime(realWarehouseId, new Date(endTime.getTime() + 1000));
		List<RealWarehouseStockDO> realWarehouseStockDOs = realWarehouseStockMapper.querySkuIdByWhId(realWarehouseId);
		if(realWarehouseStockDOs == null || realWarehouseStockDOs.isEmpty()) {
			return 0;
		}
		Date entrySaleDate = new Date(endTime.getTime());
		EntrySaleStockDO entrySaleStockDO;
		List<EntrySaleStockDO> result = new ArrayList<>(realWarehouseStockDOs.size());
		for(RealWarehouseStockDO dto : realWarehouseStockDOs) {
			entrySaleStockDO = getEntrySaleStockDoByStock(list, dto);
			if(entrySaleStockDO == null) {
				entrySaleStockDO = new EntrySaleStockDO();
				entrySaleStockDO.setRealWarehouseId(dto.getRealWarehouseId());
				entrySaleStockDO.setSkuId(dto.getSkuId());
				entrySaleStockDO.setSkuCode(dto.getSkuCode());
				entrySaleStockDO.setInQty(BigDecimal.ZERO);
				entrySaleStockDO.setOutQty(BigDecimal.ZERO);
			}
			// 期末库存计算 start
			entrySaleStockDO.setFinalQty(dto.getRealQty().add(dto.getQualityQty()).add(dto.getUnqualifiedQty()));
			// 计算期末库存,库存数据减去时间差产生加减量，等于期末库存
			calculateFinalQtyDiffByFlow(flowSumList, entrySaleStockDO);
			// 期末库存计算 end
			// 期初库存计算 start
			// 期初库存量=期末+出量-入量
			entrySaleStockDO.setInitialQty(entrySaleStockDO.getFinalQty().add(entrySaleStockDO.getOutQty()).subtract(entrySaleStockDO.getInQty()));
			// 期初库存计算 end			
			entrySaleStockDO.setEntrySaleDate(entrySaleDate);
			result.add(entrySaleStockDO);
		}
		// 打印监控数据,生成进销存数据和前一天相比有差异监控,一般是生成进销存期初和前一天的期末相比
		printKibanaCreateEntrySaleStockDiff(result, entrySaleDate, realWarehouseId);
		int count = result.size();
		// save保存数据
		EntrySaleStockService entrySaleStockService = SpringBeanUtil.getBean(EntrySaleStockService.class);
		entrySaleStockService.batchUpdateOrInsert(result, queryEntrySaleStockBySync(realWarehouseId, entrySaleDate));
		return count;
	}
	
	/**
	 * 汇总数据及保存，用于生成历史进销存数据，
	 * 以+1天的进销存期初量作为期末量
	 * @param list
	 * @param realWarehouseId
	 * @param endTime
	 * @param saleStockDOs 为endTime+1天的进销存数据
	 * @return
	 */
	private int mergeDataSummaryAndSaveByHistory(List<EntrySaleStockDO> list, Long realWarehouseId, final Date endTime, List<EntrySaleStockDO> saleStockDOs) {
		if(saleStockDOs == null || saleStockDOs.isEmpty()) {
			return 0;
		}
		Date entrySaleDate = new Date(endTime.getTime());
		EntrySaleStockDO entrySaleStockDO;
		List<EntrySaleStockDO> result = new ArrayList<>(saleStockDOs.size());
		for(EntrySaleStockDO dto : saleStockDOs) {
			entrySaleStockDO = getEntrySaleStockDoByStock(list, dto);
			if(entrySaleStockDO == null) {
				entrySaleStockDO = new EntrySaleStockDO();
				entrySaleStockDO.setRealWarehouseId(dto.getRealWarehouseId());
				entrySaleStockDO.setSkuId(dto.getSkuId());
				entrySaleStockDO.setSkuCode(dto.getSkuCode());
				entrySaleStockDO.setInQty(BigDecimal.ZERO);
				entrySaleStockDO.setOutQty(BigDecimal.ZERO);
			}
			// 期末库存计算 start
			entrySaleStockDO.setFinalQty(dto.getInitialQty());
			// 期末库存计算 end
			// 期初库存计算 start
			// 期初库存量=期末+出量-入量
			entrySaleStockDO.setInitialQty(entrySaleStockDO.getFinalQty().add(entrySaleStockDO.getOutQty()).subtract(entrySaleStockDO.getInQty()));
			// 期初库存计算 end			
			entrySaleStockDO.setEntrySaleDate(entrySaleDate);
			result.add(entrySaleStockDO);
		}
		// 打印监控数据,生成进销存数据和前一天相比有差异监控,一般是生成进销存期初和前一天的期末相比
		printKibanaCreateEntrySaleStockDiff(result, entrySaleDate, realWarehouseId);
		int count = result.size();
		// save保存数据
		EntrySaleStockService entrySaleStockService = SpringBeanUtil.getBean(EntrySaleStockService.class);
		entrySaleStockService.batchUpdateOrInsert(result, queryEntrySaleStockBySync(realWarehouseId, entrySaleDate));
		return count;
	}
	
	/**
	 * 获取缓存索引
	 * @param size 大小
	 * @param sumIndex
	 * @return
	 */
	private int getCacheIndex(int size, int sumIndex) {
		return (sumIndex % size + size) % size;
	}
	
	/**
	 * 验证抵消量，并计算出抵消量，并设置进recordCodeListByOffsetMap
	 * @param skuIdOffsetQtyMap
	 * @param recordCodeListByOffset
	 * @param recordCodeList
	 * @param realWarehouseId
	 * @param startTime
	 * @param endTime
	 * @param cacheList 缓存已处理的单据编码
	 * @return
	 */
	private void validateOffsetRecordCode(Map<Long, BigDecimal> skuIdOffsetQtyMap, List<String> recordCodeListByOffset, List<String> recordCodeList, Long realWarehouseId, final Date startTime, final Date endTime, final List<String> cacheList) {
		if(recordCodeList == null || recordCodeList.size() == 0) {
			return;
		}
		for(int i = recordCodeList.size() - 1; i > -1; i--) {
			// 如果已在抵销单据列表里，或者已处理过的在缓存里的，不需要再验证
			if(recordCodeListByOffset.contains(recordCodeList.get(i)) || cacheList.contains(recordCodeList.get(i))) {
				recordCodeList.remove(i);
			}
		}
		if(recordCodeList.size() == 0) {
			return;
		}
		List<RwStockChangeFlowDO> flowList = rwStockChangeFlowMapper.queryFlowEntrySaleStockSumByRecordCodeList(recordCodeList, realWarehouseId, startTime, endTime);
		for(String recordCode : recordCodeList) {
			// 是否存在抵消量
			if(existFlowsOffset(flowList, recordCode)) {
				// 计算抵消量
				calculateOffsetQty(flowList, recordCode, skuIdOffsetQtyMap);
				recordCodeListByOffset.add(recordCode);
			}
		}
	}
	
	/**
	 * 判断是否存在可以抵消的，同一个单号在流水中如果出入库存量都有，表示可以抵消
	 * @param flowList 库存流水
	 * @param recordCode 单据编码
	 * @return
	 */
	private boolean existFlowsOffset(List<RwStockChangeFlowDO> flowList, String recordCode) {
		if(flowList == null) {
			return false;
		}
		// 入库量标识
		boolean isInFlag = false;
		// 出库量标识
		boolean isOutFlag = false;
		for(RwStockChangeFlowDO dto : flowList) {
			if(recordCode.equals(dto.getRecordCode())) {
				if(isIncreaseStockByStockType(dto.getStockType())) {
					isInFlag = true;
				} else if(isDecreaseStockByStockType(dto.getStockType())) {
					// 是减少库存
					isOutFlag = true;
				}
				// 若是出入库量都存在，返回
				if(isInFlag && isOutFlag) {
					return true;
				}
			}
		}
		return false;
	}
	
	/**
	 * 计算抵消量
	 * @param flowList
	 * @param recordCode
	 * @param skuIdOffsetQtyMap 抵消量
	 * @return
	 */
	private void calculateOffsetQty(List<RwStockChangeFlowDO> flowList, String recordCode, Map<Long, BigDecimal> skuIdOffsetQtyMap) {
		if(flowList == null) {
			return;
		}
		EntrySaleStockDO temp;
		Map<Long, EntrySaleStockDO> skuIdOffsetQtyTemp = new HashMap<>(8);
		for(RwStockChangeFlowDO dto : flowList) {
			if(dto.getSkuId() == null) {
				continue;
			}
			if(recordCode.equals(dto.getRecordCode())) {
				if(isIncreaseStockByStockType(dto.getStockType())) {
					temp = skuIdOffsetQtyTemp.get(dto.getSkuId());
					if(temp == null) {
						temp = new EntrySaleStockDO();
						temp.setInQty(BigDecimal.ZERO);
						temp.setOutQty(BigDecimal.ZERO);
						skuIdOffsetQtyTemp.put(dto.getSkuId(), temp);
					}
					temp.setInQty(temp.getInQty().add(dto.getChangeQty()));
				} else if(isDecreaseStockByStockType(dto.getStockType())) {
					// 是出库库存量
					temp = skuIdOffsetQtyTemp.get(dto.getSkuId());
					if(temp == null) {
						temp = new EntrySaleStockDO();
						temp.setInQty(BigDecimal.ZERO);
						temp.setOutQty(BigDecimal.ZERO);
						skuIdOffsetQtyTemp.put(dto.getSkuId(), temp);
					}
					temp.setOutQty(temp.getOutQty().add(dto.getChangeQty()));
				}
			}
		}
		BigDecimal minQty;
		BigDecimal qty1;
		for (Map.Entry<Long, EntrySaleStockDO> entry : skuIdOffsetQtyTemp.entrySet()) {
			minQty = entry.getValue().getInQty().compareTo(entry.getValue().getOutQty()) > 0 ? entry.getValue().getOutQty(): entry.getValue().getInQty();
			if(minQty.abs().compareTo(StockCoreConsts.MIN_VALUE_ZERO) > 0){
				qty1 = skuIdOffsetQtyMap.get(entry.getKey());
				if(qty1 == null) {
					skuIdOffsetQtyMap.put(entry.getKey(), minQty);
				} else {
					skuIdOffsetQtyMap.put(entry.getKey(), qty1.add(minQty));
				}
			}
		}
		skuIdOffsetQtyTemp.clear();
		skuIdOffsetQtyTemp = null;
	}
	
	/**
	 * 流水数据合并处理出入库量等
	 * @param list
	 * @param flowList
	 * @return 返回单据编码
	 */
	private List<String> mergeFlowToEntrySaleStock(List<EntrySaleStockDO> list, List<RwStockChangeFlowDO> flowList) {
		EntrySaleStockDO temp;
		// 是否为入库库存量
		boolean isInFlag = false;
		List<String> recordCodeList = new ArrayList<>();
		for(RwStockChangeFlowDO dto : flowList) {
			if(dto.getRealWarehouseId() == null || dto.getSkuId() == null || dto.getChangeQty() == null) {
				// 为null,跳过处理
				continue;
			}
			// 是增加库存
			if(isIncreaseStockByStockType(dto.getStockType())) {
				isInFlag = true;
			} else if(isDecreaseStockByStockType(dto.getStockType())) {
				// 是减少库存
				isInFlag = false;
			} else {
				// 跳过处理
				continue;
			}
			temp = getEntrySaleStockDoByFlow(list, dto);
			if(temp == null) {
				temp = new EntrySaleStockDO();
				temp.setRealWarehouseId(dto.getRealWarehouseId());
				temp.setSkuId(dto.getSkuId());
				temp.setSkuCode(dto.getSkuCode());
				temp.setInQty(BigDecimal.ZERO);
				temp.setOutQty(BigDecimal.ZERO);
				list.add(temp);
			}
			// 入库量
			if(isInFlag) {
				temp.setInQty(temp.getInQty().add(dto.getChangeQty()));
			} else {
				// 出库量
				temp.setOutQty(temp.getOutQty().add(dto.getChangeQty()));
			}
			if(StringUtils.isNotBlank(dto.getRecordCode())) {
				if(!recordCodeList.contains(dto.getRecordCode())) {
					recordCodeList.add(dto.getRecordCode());
				}
			}
		}
		return recordCodeList;
	}
	
	/**
	 * 获取进销存dto,根据流水数据
	 * @param list
	 * @param flowDto
	 */
	private EntrySaleStockDO getEntrySaleStockDoByFlow(List<EntrySaleStockDO> list, RwStockChangeFlowDO flowDto) {
		if(list == null || list.size() == 0) {
			return null;
		}
		for(EntrySaleStockDO dto : list) {
			if(dto.getRealWarehouseId().equals(flowDto.getRealWarehouseId())
					&& dto.getSkuId().equals(flowDto.getSkuId())) {
				return dto;
			}
		}
		return null;
	}
	
	/**
	 * 获取进销存dto,根据库存数据
	 * @param list
	 * @param flowDto
	 */
	private EntrySaleStockDO getEntrySaleStockDoByStock(List<EntrySaleStockDO> list, RealWarehouseStockDO dt) {
		if(list == null || list.size() == 0) {
			return null;
		}
		for(EntrySaleStockDO dto : list) {
			if(dto.getSkuId().equals(dt.getSkuId())) {
				return dto;
			}
		}
		return null;
	}
	
	/**
	 * 获取进销存dto,根据进销存库存数据
	 * @param list
	 * @param flowDto
	 */
	private EntrySaleStockDO getEntrySaleStockDoByStock(List<EntrySaleStockDO> list, EntrySaleStockDO dt) {
		if(list == null || list.size() == 0) {
			return null;
		}
		for(EntrySaleStockDO dto : list) {
			if(dto.getSkuId().equals(dt.getSkuId())) {
				return dto;
			}
		}
		return null;
	}
	
	/**
	 * 计算期末库存,
	 * 库存数据减去时间差产生加减量，等于期末库存，
	 * 比如，3:00 计算进货存，db中实仓库存去掉，0点到3点，库存变化就是前一天的期末库存
	 * @param list
	 * @param flowDto
	 */
	private void calculateFinalQtyDiffByFlow(List<RwStockChangeFlowDO> list, EntrySaleStockDO entrySaleStockDO) {
		for(RwStockChangeFlowDO dto : list) {
			if(entrySaleStockDO.getSkuId().equals(dto.getSkuId())) {
				// 是增加库存
				if(isIncreaseStockByStockType(dto.getStockType())) {
					entrySaleStockDO.setFinalQty(entrySaleStockDO.getFinalQty().subtract(dto.getChangeQty()));
				} else if(isDecreaseStockByStockType(dto.getStockType())) {
					// 是减少库存
					entrySaleStockDO.setFinalQty(entrySaleStockDO.getFinalQty().add(dto.getChangeQty()));
				} else {
					// 跳过处理
					continue;
				}
			}
		}
	}
	
	/**
     *是否是增加库存数据，根据库存类型
     * @param stockType
     * @return
     */
    private static boolean isIncreaseStockByStockType(Integer stockType) {
    	if(StockTransactionConsts.TRANS_TYPE_WAREHOUSE_STOCK_INCREASE.equals(stockType) // 增加真实库存
    			|| StockTransactionConsts.TRANS_TYPE_WAREHOUSE_QUALITY_STOCK_INCREASE.equals(stockType) // 增加质检库存
    			|| StockTransactionConsts.TRANS_TYPE_WAREHOUSE_UNQUALIFIED_STOCK_INCREASE.equals(stockType) // 增加质检不合格库存
    			) {
    		return true;
    	}
    	return false;
    }
    
    /**
     *是否是减少库存数据，根据库存类型
     * @param stockType
     * @return
     */
    private static boolean isDecreaseStockByStockType(Integer stockType) {
    	if(StockTransactionConsts.TRANS_TYPE_WAREHOUSE_STOCK_DECREASE.equals(stockType) // 减少实际库存
    			|| StockTransactionConsts.TRANS_TYPE_WAREHOUSE_REAL_STOCK_OUT.equals(stockType) // 真实库存出库
    			|| StockTransactionConsts.TRANS_TYPE_WAREHOUSE_QUALITY_STOCK_DECREASE.equals(stockType) // 减少质检库存
    			|| StockTransactionConsts.TRANS_TYPE_WAREHOUSE_UNQUALIFIED_STOCK_DECREASE.equals(stockType) // 减少质检不合格库存
    			) {
    		return true;
    	}
    	return false;
    }
    
    /**
     * 批量更新或者插入,手动同步时,
     * 注意list和existMap进销存日期是相同的
     * @param list
     * @param existMap
     */
    @Override
	@Transactional(rollbackFor = Exception.class)
	public void batchUpdateOrInsert(List<EntrySaleStockDO> list, Map<Long, EntrySaleStockDO> existMap) {
		List<EntrySaleStockDO> updateList = new ArrayList<>(existMap.size());
		EntrySaleStockDO temp,temp2;
		for (int i = list.size() - 1; i >= 0; i--) {
			temp2 = list.get(i);
			// 进销存生成，原始库存设置
			setOrginQty(temp2);
			temp = existMap.get(temp2.getSkuId());
			if(temp != null) {
				temp2.setId(temp.getId());
				updateList.add(temp2);
				list.remove(i);
			}
		}
		// 增加的
        if(list.size() > 0) {
        	if(list.size() > 1000) {
				int maxPage = ListUtil.getPageNum(list, 1000);
		        for(int i = 1; i <= maxPage; i++) {
		        	entrySaleStockMapper.batchInsert(ListUtil.getPageList(list, i,1000));
		        }
			}else {
				entrySaleStockMapper.batchInsert(list);
			}
		}
        // 更新的
        if(updateList.size() > 0) {
			if(updateList.size() > 1000) {
				int maxPage = ListUtil.getPageNum(updateList, 1000);
		        for(int i=1; i <= maxPage;i++) {
		        	entrySaleStockMapper.batchUpdateBySync(ListUtil.getPageList(updateList, i,1000));
		        }
			}else {
				entrySaleStockMapper.batchUpdateBySync(updateList);
			}
		}
	}
    
    /**
	 * 查询，同步时，
	 * 查询指定的一个仓和指定的进销存日期
	 * @param realWarehouseId 指定的一个仓
	 * @param entrySaleDate 进销存日期
	 * @return map  key>>>skuId  val>>>EntrySaleStockDO
	 */
	@SuppressWarnings("unchecked")
	private Map<Long, EntrySaleStockDO> queryEntrySaleStockBySync(Long realWarehouseId, Date entrySaleDate) {
		List<EntrySaleStockDO> list = entrySaleStockMapper.queryEntrySaleStockBySync(realWarehouseId, entrySaleDate);
		if(list == null || list.size() == 0) {
			return Collections.EMPTY_MAP;
		}
		Map<Long, EntrySaleStockDO> map = new HashMap<Long, EntrySaleStockDO>(list.size() * 100 /75 + 1);
		for(EntrySaleStockDO dto : list) {
			map.put(dto.getSkuId(), dto);
		}
		return map;
	}
	
	/**
     * 根据仓生成，生成进销存数据和前一天相比有差异监控,一般是生成进销存期初和前一天的期末相比,有差异就打印出来,且数据是同一个仓的
     * @param list
     * @param entrySaleDate
     * @param realWarehouseId
     */
    private void printKibanaCreateEntrySaleStockDiff(List<EntrySaleStockDO> list, Date entrySaleDate, Long realWarehouseId) {
    	if(list == null || list.size() == 0) {
    		return;
    	}
    	// 前一天的时间
    	Date preDate = DateUtil.offsiteDate(entrySaleDate, Calendar.DAY_OF_YEAR, -1);
    	// 前一天进销存
    	List<EntrySaleStockDO> entrySaleStockDOs = entrySaleStockMapper.queryEntrySaleStockBySync(realWarehouseId, preDate);
		if(entrySaleStockDOs == null || entrySaleStockDOs.size() == 0) {
			return;
		}
		EntrySaleStockDO temp;
		BigDecimal diffQty;
		String entrySaleDateStr = DateUtil.format(entrySaleDate, DateUtil.NORM_DATE_PATTERN);
		for(EntrySaleStockDO dto : list) {
			temp = getEntrySaleStockDoByStock(entrySaleStockDOs, dto);
			if(temp != null) {
				diffQty = temp.getOrginFinalQty().subtract(dto.getInitialQty());
				// 是否有差异，若有差异打印出来，收集
				if(diffQty.abs().compareTo(StockCoreConsts.MIN_VALUE_ZERO) > 0) {
					log.warn(KibanaLogUtils.getCreateEntrySaleStockPreDiff(realWarehouseId, dto.getSkuId(), entrySaleDateStr, dto.getInitialQty(), temp.getOrginFinalQty(), diffQty));
				}
			}
		}
		
    }
	
	/**
	 * 根据条件查询进销存表
	 * @param paramDto
	 * @return
	 */
	@SuppressWarnings("unchecked")
	@Override
	public PageInfo<EntrySaleStockDTO> queryCondition(EntrySaleStockDTO paramDto) {
		// 时间验证 开始和结束时间不能为空 时间范围不能超过一年
		DateFormatTools.validateDateEqualByYyyy(paramDto.getStartTime(), paramDto.getEndTime());
		EsIndexTypeConfig.setIndexNameSuffixByYear("1", paramDto.getStartTime());
		try {
			// 实仓列表取值，并且实仓列表不能为null
			if(paramDto.getRealWarehouseId() != null) {
				paramDto.setRealWarehouseIds(Collections.singletonList(paramDto.getRealWarehouseId()));
			} else if(StringUtils.isNotBlank(paramDto.getFactoryCode())) {
				// 工厂编码
				List<RealWarehouseDO> realWarehouseDOs = realWarehouseMapper.queryRealWarehouseByFactoryCode(paramDto.getFactoryCode());
				if(realWarehouseDOs != null && realWarehouseDOs.size() > 0) {
					List<Long> realWarehouseIds = new ArrayList<>(realWarehouseDOs.size());
					for(RealWarehouseDO dto : realWarehouseDOs) {
						realWarehouseIds.add(dto.getId());
					}
					paramDto.setRealWarehouseIds(realWarehouseIds);
				} else {
					PageInfo<EntrySaleStockDTO> pageInfo = new PageInfo<>(Collections.EMPTY_LIST);
					pageInfo.setTotal(0);
					return pageInfo;
				}
			}
			if(paramDto.getRealWarehouseIds() == null || paramDto.getRealWarehouseIds().size() == 0) {
				throw new RomeException(ResCode.STOCK_ERROR_1002, "参数有误,工厂或仓库编码不能同时为空");
			}
			// es查询实现 start
			// 请求参数
			NativeSearchQuery searchQuery = queryConditionParam(paramDto);
			org.springframework.data.domain.Page<EsEntrySaleStockDO> resultPage = esEntrySaleStockRepository.search(searchQuery);
	        if(resultPage == null || resultPage.getContent() == null || resultPage.getContent().size() == 0) {
	        	PageInfo<EntrySaleStockDTO> pageInfo = new PageInfo<>(Collections.EMPTY_LIST);
	        	pageInfo.setTotal(0);
				return pageInfo;
	        }
	        List<EntrySaleStockDTO> list = esEntrySaleStockConvertor.esEntrySaleStockDOEntityToDTOList(resultPage.getContent());
			// es查询实现 end
//			//开启分页
//			Page page = PageHelper.startPage(paramDto.getPageIndex(), paramDto.getPageSize(), paramDto.isCount());
//			List<EntrySaleStockDTO> list = entrySaleStockMapper.queryCondition(paramDto);
//			if(list == null || list.size() == 0) {
//				PageInfo<EntrySaleStockDTO> pageInfo = new PageInfo<>(Collections.EMPTY_LIST);
//				pageInfo.setTotal(0);
//				return pageInfo;
//			}
			// start 仓库信息组装
			List<RealWarehouseDO> warehouseES = realWarehouseMapper.queryWarehouseByIds(paramDto.getRealWarehouseIds());
			//遍历查询结果,设置仓库名称等
			RealWarehouseDO realWarehouseDO;
			for (EntrySaleStockDTO flowDTO : list) {
				realWarehouseDO = getMapValRealWarehouse(warehouseES, flowDTO.getRealWarehouseId());
				if(realWarehouseDO != null){
					flowDTO.setFactoryCode(realWarehouseDO.getFactoryCode());
					flowDTO.setRealWarehouseCode(realWarehouseDO.getRealWarehouseCode());
					flowDTO.setRealWarehouseName(realWarehouseDO.getRealWarehouseName());
				}
			}
			// end 仓库信息组装
			List<String> skuCodes = list.stream().filter(v -> StringUtils.isNotBlank(v.getSkuCode())).map(EntrySaleStockDTO :: getSkuCode).distinct().collect(Collectors.toList());
			if(skuCodes.size() > 0) {
				//根据skuCodes获取商品信息
				List<SkuInfoExtDTO> skuInfoExtDTOList = skuFacade.skusBySkuCode(skuCodes);
				Map<String, SkuInfoExtDTO> skuInfoExtDTOMap = skuInfoExtDTOList.stream().distinct().collect(Collectors.toMap(SkuInfoExtDTO :: getSkuCode, Function.identity(), (v1, v2) -> v1));
				list.forEach(item -> {
					if(skuInfoExtDTOMap.containsKey(item.getSkuCode())) {
						item.setSkuName(skuInfoExtDTOMap.get(item.getSkuCode()).getName());
						item.setUnit(skuInfoExtDTOMap.get(item.getSkuCode()).getSpuUnitName());
					}
				});
			}
			PageInfo<EntrySaleStockDTO> pageList = new PageInfo<>(list);
			pageList.setTotal(resultPage.getTotalElements());
			return pageList;
		} catch (IndexNotFoundException e) {
			log.error("查询历史数据", e);
			throw new RomeException(ResCode.STOCK_ERROR_1002, "参数有误,该时间段,无法查询到数据");
		} finally {
			EsIndexTypeConfig.clearIndexNameSuffix();
		}
	}
	
	/**
     * 获取请求参数
     * 出库单,历史数据
     */
    private NativeSearchQuery queryConditionParam(EntrySaleStockDTO condition){
    	BoolQueryBuilder boolQueryBuilder = QueryBuilders.boolQuery();
        // 条件 仓库Ids
    	if(condition.getRealWarehouseIds() != null && condition.getRealWarehouseIds().size() > 0) {
        	boolQueryBuilder.must(QueryBuilders.termsQuery("realWarehouseId", condition.getRealWarehouseIds()));
        }
        // 条件 skuId
        if(condition.getSkuIds() != null && condition.getSkuIds().size() > 0) {
        	boolQueryBuilder.must(QueryBuilders.termsQuery("skuId", condition.getSkuIds()));
        }
		//时间范围的设定
		if(condition.getStartTime() != null || condition.getEndTime() != null) {
			RangeQueryBuilder rangeQueryBuilder = QueryBuilders.rangeQuery("entrySaleDate");
			if(condition.getStartTime() != null) {
				condition.setStartTime(DateUtil.getDayBegin(condition.getStartTime()));
				rangeQueryBuilder.from(condition.getStartTime().getTime());
			}
			if(condition.getEndTime() != null) {
				condition.setEndTime(DateUtil.getDayEnd(condition.getEndTime()));
				rangeQueryBuilder.to(condition.getEndTime().getTime());
			}
			boolQueryBuilder.must(rangeQueryBuilder);
		}
		// id 主要应用于导出时，此id是最大maxId
		if(condition.getId() != null) {
			RangeQueryBuilder rangeQueryBuilder = QueryBuilders.rangeQuery("dbId");
			rangeQueryBuilder.lt(condition.getId());
			boolQueryBuilder.must(rangeQueryBuilder);
		}		
		Pageable pageable = PageRequest.of(condition.getPageIndex() - 1, condition.getPageSize() > 100000 ? 100000 : condition.getPageSize());
		NativeSearchQueryBuilder builder = new NativeSearchQueryBuilder();
		builder.withQuery(boolQueryBuilder);
		builder.withPageable(pageable);
//		if(condition.isSortDesc()) {
//			builder.withSort(SortBuilders.fieldSort("entrySaleDate").order(SortOrder.DESC));
//		} else {
//			builder.withSort(SortBuilders.fieldSort("entrySaleDate").order(SortOrder.ASC));
//		}
//		builder.withSort(SortBuilders.fieldSort("dbId").order(SortOrder.ASC));
		builder.withSort(SortBuilders.fieldSort("dbId").order(SortOrder.DESC));
		builder.withSearchType(SearchType.QUERY_THEN_FETCH);
		NativeSearchQuery query = builder.build();
		query.setTrackTotalHits(true);
		return query;
    }
	
	/**
	 * 获取仓信息
	 * @param list
	 * @param realWarehouseId
	 * @return
	 */
	private RealWarehouseDO getMapValRealWarehouse(List<RealWarehouseDO> list, Long realWarehouseId) {
		for(RealWarehouseDO dto : list) {
			if(dto.getId().equals(realWarehouseId)) {
				return dto;
			}
		}
		return null;
	}
	
	/**
	 * 进销存生成，原始库存设置
	 * @param dto
	 */
	private void setOrginQty(EntrySaleStockDO dto) {
		dto.setOrginInitialQty(dto.getInitialQty());
		dto.setOrginInQty(dto.getInQty());
		dto.setOrginOutQty(dto.getOutQty());
		dto.setOrginFinalQty(dto.getFinalQty());
	}
	
	/************* 以下为进销存库存调整 ***************/
	
	/**
	 * 每页查询处理的大小
	 */
	private static final Integer PAGE_SIZE = 1000;
	
	/**
	 * 进销存库存调整处理，根据调整单明细定时任务
	 * @param runStopTime 运行结束的指定毫秒数
	 * @return
	 */
	@Override
	public int adjustEntrySaleStockTask(long runStopTime) {
		int num = 0;
		try {
			// 实仓,缓存大小
			final int cacheSize = 2000;
			// 实仓,缓存
			List<Long> cacheList = new ArrayList<>(cacheSize);
			for(int i = 0; i < cacheSize; i++) {
				cacheList.add(null);
			}
			Map<Long, RealWarehouseDO> realWarehouseCacheMap = new HashMap<>(cacheSize * 100 / 75);			
			/// 最大重试次数
			final int maxTryTimes = 100;
			Date maxUpdateTime = new Date();
			
			int sumIndex = 0;		
			boolean runFlag = true;
			List<EntrySaleAdjustDetailDO> list = null;
			int newNum;
			// 支持的实仓类型列表
			List<Integer> rWTypeList = new ArrayList<>();
			// 支持的实仓Id列表
			List<Long> rWIdList = new ArrayList<>();
			getAllowWarehouseTypeOrIds(rWTypeList, rWIdList);	
			// 不存的实仓赋值
			RealWarehouseDO emptyRealWarehouse = new RealWarehouseDO();
			emptyRealWarehouse.setId(-1L);
			emptyRealWarehouse.setRealWarehouseType(-1);
			emptyRealWarehouse.setRealWarehouseStatus(RealWarehouseStatusVO.DISABLED.getStatus());
			RealWarehouseDO realWarehouseTempDO;
			Long temp;
			Date migrateDate = getMigrateToEsDateTime();
			do {
				if(System.currentTimeMillis() > runStopTime) {
					throw new RomeException(ResCode.STOCK_ERROR_1003, "进销存库存调整超过指定的时间,立即结束");
				}
				// 注：这个查询sql状态只能查询 0,1   （`status` in (0,1)）
				list = entrySaleAdjustDetailMapper.queryNeedAdjustDetailByAdjust(maxTryTimes, maxUpdateTime, PAGE_SIZE);
				if(list != null && list.size() > 0) {
					newNum = list.size();
					// 检查是否支持进销存
					for(EntrySaleAdjustDetailDO dto : list) {
						// 实仓信息获取
						realWarehouseTempDO = realWarehouseCacheMap.get(dto.getRealWarehouseId());
						if(realWarehouseTempDO == null) {
							realWarehouseTempDO = realWarehouseMapper.queryById(dto.getRealWarehouseId());
							if(realWarehouseTempDO == null) {
								realWarehouseTempDO = emptyRealWarehouse;
							}
							// 设入缓存中
							temp = cacheList.set(getCacheIndex(cacheSize, sumIndex++), dto.getRealWarehouseId());
							if(temp != null) {
								realWarehouseCacheMap.remove(temp);
							}
							realWarehouseCacheMap.put(dto.getRealWarehouseId(), realWarehouseTempDO);
						}
						// 验证，预先验证合法性
						validateInitData(dto, realWarehouseTempDO, rWTypeList, rWIdList);
					}
					// 执行处理
					adjustEntrySaleStockOperate(list, migrateDate);
					num += newNum;
					// 没有可用数据
					if(newNum < PAGE_SIZE) {
						runFlag = false;
					}
					list.clear();
					list = null;
				} else {
					log.info("本次进销存库存调整结束==========");
					runFlag = false;
				}
			} while(runFlag);
		} catch (Exception e) {
			log.error("本次进销存库存调整异常", e);
		} finally {
		}
		return num;
	}
	
	/**
	 * 根据调整单Id列表手动重试进销存库存调整
	 * @param idList
	 * @return 返回错误信息
	 */
	@Override
	public JSONObject retryAdjustEntrySaleStockByIdList(List<Long> idList) {
		if(idList == null || idList.size() == 0) {
			return null;
		}
		if(idList.size() > 100000) {
			throw new RomeException(ResCode.STOCK_ERROR_1003, "根据Id列表手动重试进销存库存调整执行,超出单次最大条数100000");
		}
		int num = 0;// 实际需要处理总条数
		int totalNum = 0;// 总条数
		JSONObject result = new JSONObject();
		JSONArray resultArray = new JSONArray();
		long time = System.currentTimeMillis();
		try {
			// 实仓,缓存大小
			final int cacheSize = 2000;
			// 实仓,缓存
			List<Long> cacheList = new ArrayList<>(cacheSize);
			for(int i = 0; i < cacheSize; i++) {
				cacheList.add(null);
			}
			Map<Long, RealWarehouseDO> realWarehouseCacheMap = new HashMap<>(cacheSize * 100 / 75);			
			int sumIndex = 0;		
			List<EntrySaleAdjustDetailDO> list = null;
			// 支持的实仓类型列表
			List<Integer> rWTypeList = new ArrayList<>();
			// 支持的实仓Id列表
			List<Long> rWIdList = new ArrayList<>();
			getAllowWarehouseTypeOrIds(rWTypeList, rWIdList);	
			// 不存的实仓赋值
			RealWarehouseDO emptyRealWarehouse = new RealWarehouseDO();
			emptyRealWarehouse.setId(-1L);
			emptyRealWarehouse.setRealWarehouseType(-1);
			emptyRealWarehouse.setRealWarehouseStatus(RealWarehouseStatusVO.DISABLED.getStatus());
			RealWarehouseDO realWarehouseTempDO;
			Long temp;
			// 分割处理的数据
			int maxPage = ListUtil.getPageNum(idList, PAGE_SIZE);
			List<Long> subIdList;	
			JSONObject errorItem;
			Date migrateDate = getMigrateToEsDateTime();
			for(int i=1; i <= maxPage;i++) {
				subIdList = ListUtil.getPageList(idList, i, PAGE_SIZE);
				list = entrySaleAdjustDetailMapper.queryNeedAdjustDetailByIdList(subIdList);
				totalNum += subIdList.size();
				if(list == null || list.size() == 0) {
					continue;
				}
				num += list.size();
				for(EntrySaleAdjustDetailDO dto : list) {
					// 实仓信息获取
					realWarehouseTempDO = realWarehouseCacheMap.get(dto.getRealWarehouseId());
					if(realWarehouseTempDO == null) {
						realWarehouseTempDO = realWarehouseMapper.queryById(dto.getRealWarehouseId());
						if(realWarehouseTempDO == null) {
							realWarehouseTempDO = emptyRealWarehouse;
						}
						// 设入缓存中
						temp = cacheList.set(getCacheIndex(cacheSize, sumIndex++), dto.getRealWarehouseId());
						if(temp != null) {
							realWarehouseCacheMap.remove(temp);
						}
						realWarehouseCacheMap.put(dto.getRealWarehouseId(), realWarehouseTempDO);
					}
					// 验证，预先验证合法性
					validateInitData(dto, realWarehouseTempDO, rWTypeList, rWIdList);
				}
				// 执行处理
				adjustEntrySaleStockOperate(list, migrateDate);
				// 错误信息收集
				for(EntrySaleAdjustDetailDO dto : list) {
					if(!EntrySaleAdjustDetailStatusVO.SUCCESS.getStatus().equals(dto.getStatus())) {
						errorItem = new JSONObject();
						errorItem.put("id", dto.getId());
						errorItem.put("errorMsg", dto.getErrorMsg());
						resultArray.add(errorItem);
					}
				}
			}
		} catch (Exception e) {
			log.error("根据id列表重试进销存库存调整,异常", e);
			result.put("errorMsg", StringUtils.getExceptionMessage(e));
		}
		result.put("actualNum", num);
		result.put("totalNum", totalNum);
		result.put("errorItem", resultArray);
		result.put("time", System.currentTimeMillis() - time);
		return result;
	}
	
	/**
	 * 根据进销存id列表，手动进销存数据同步到ES中
	 * @param idList
	 */
	@Override
	public void entrySaleStockSyncToEsByIdList(List<Long> idList) {
		if(idList == null || idList.size() == 0) {
			return;
		}
		if(idList.size() > 100000) {
			throw new RomeException(ResCode.STOCK_ERROR_1003, "根据进销存id列表，手动进销存数据同步到ES中，超出单次最大条数100000");
		}
		List<EntrySaleStockDO> list = new ArrayList<>(idList.size());
		EntrySaleStockDO dto;
		for(Long id : idList) {
			dto = new EntrySaleStockDO();
			dto.setId(id);
			list.add(dto);
		}
		// 进销存保存到es
		adjustEntrySaleStockQtySyncToEs(list);
	}
	
	/**
	 * 验证，预先验证合法性
	 * @param dto 设整单数据
	 * @param realWarehouseDO 实仓信息
	 * @param rWTypeList 配置支持的仓类型
	 * @param rWIdList 配置支持的仓Id
	 */
	private void validateInitData(EntrySaleAdjustDetailDO dto, RealWarehouseDO realWarehouseDO, List<Integer> rWTypeList, List<Long> rWIdList) {
		// 若仓库是禁用的，也不需要处理了
		if(RealWarehouseStatusVO.DISABLED.getStatus().equals(realWarehouseDO.getRealWarehouseStatus())) {
			// 扣减库存日期待处理时 仓禁用了
			if(EntrySaleAdjustDetailStatusVO.WAIT_OPT_DATE.getStatus().equals(dto.getStatus())) {
				dto.setStatus(EntrySaleAdjustDetailStatusVO.WAIT_OPT_DATE_FAIL.getStatus());
			} else {
				dto.setStatus(EntrySaleAdjustDetailStatusVO.FAIL.getStatus());
			}
			dto.setErrorMsg("仓库状态是禁用");
			dto.setFlag(false);
		} else if(!((realWarehouseDO.getRealWarehouseType() != null && rWTypeList.contains(realWarehouseDO.getRealWarehouseType()))
				|| (rWIdList.contains(dto.getRealWarehouseId())))) { // 没有配置仓库生成进销存，不需处理的
			// 扣减库存日期待处理时 仓库配置为不生成进销存
			if(EntrySaleAdjustDetailStatusVO.WAIT_OPT_DATE.getStatus().equals(dto.getStatus())) {
				dto.setStatus(EntrySaleAdjustDetailStatusVO.WAIT_OPT_DATE_FAIL.getStatus());
			} else {
				dto.setStatus(EntrySaleAdjustDetailStatusVO.FAIL.getStatus());
			}
			dto.setErrorMsg("仓库配置为不生成进销存");
			dto.setFlag(false);
		} else if(dto.getFinanceDate().getTime() > dto.getOperateDate().getTime()) {// 这里的时间，在db是日期，不是日期时间型，因此可以这样比较
			// 财务日期大于库存操作日期
			if(EntrySaleAdjustDetailStatusVO.WAIT_OPT_DATE.getStatus().equals(dto.getStatus())) {
				dto.setStatus(EntrySaleAdjustDetailStatusVO.WAIT_OPT_DATE_FAIL.getStatus());
			} else {
				dto.setStatus(EntrySaleAdjustDetailStatusVO.FAIL.getStatus());
			}
			dto.setErrorMsg("财务日期大于库存操作日期，无法处理");
			dto.setFlag(false);
		} else if(dto.getFinanceDate().getTime() == dto.getOperateDate().getTime()) {// 这里的时间，在db是日期，不是日期时间型，因此可以这样比较
			// 财务日期与库存操作时间相同
			if(EntrySaleAdjustDetailStatusVO.WAIT_OPT_DATE.getStatus().equals(dto.getStatus())) {
				dto.setStatus(EntrySaleAdjustDetailStatusVO.WAIT_OPT_DATE_FAIL.getStatus());
				dto.setErrorMsg("异常");
			} else {
				dto.setStatus(EntrySaleAdjustDetailStatusVO.NON.getStatus());
				dto.setErrorMsg(EntrySaleAdjustDetailStatusVO.NON.getDesc());
			}
			dto.setFlag(false);
		} else if(dto.getQty().compareTo(BigDecimal.ZERO) == 0) {// 调整库存不能等于0
			// 调整库存不能小于或等于0
			if(EntrySaleAdjustDetailStatusVO.WAIT_OPT_DATE.getStatus().equals(dto.getStatus())) {
				dto.setStatus(EntrySaleAdjustDetailStatusVO.WAIT_OPT_DATE_FAIL.getStatus());
			} else {
				dto.setStatus(EntrySaleAdjustDetailStatusVO.FAIL.getStatus());
			}
			dto.setErrorMsg("调整库存不能等于0");
			dto.setFlag(false);
		}
	}
	
	/**
	 * 进销存库存调整处理，根据调整单明细，
	 * 进销存库存调整，只会调整两种状态：0-待处理 1-扣减库存日期待处理 才处理
	 * 
	 * @param list
	 * @param migrateDate 获取进销存迁移时间，小于这个时间，都应迁到es了
	 */
	private void adjustEntrySaleStockOperate(List<EntrySaleAdjustDetailDO> list, Date migrateDate) {
		// 查询进销存数据 start
		// 查询条件获取，合并，按相同实仓Id,skuId，时间范围有交集的
		List<EntrySaleStockDO> queryParmList = mergeQueryParmByRwSkuIdDate(list);
		// 查询出所有的需要处理的进销存数据 start
		List<EntrySaleStockDO> allDataList = new ArrayList<>();
		List<EntrySaleStockDO> tempList;
		for(EntrySaleStockDO dto : queryParmList) {
			if(dto.getSkuIds() != null && dto.getSkuIds().size() > 0) {
				tempList = entrySaleStockMapper.queryByAdjustSkuIdList(dto);
			} else {
				tempList = entrySaleStockMapper.queryByAdjustSkuId(dto);
			}
			if(tempList != null && tempList.size() > 0) {
				allDataList.addAll(tempList);
			}
		}
		// 查询出所有的需要处理的进销存数据 end
		// 合并重复的，初始化值为0,这些字段后续用作改变量
		mergeAndInitDataByAdjust(allDataList);
		// 验证 调整单明细对应的进销存数据是否完整齐全，不能处理的设置flag=false;并设置下次重试时间等
		validateDataRangeByAdjustDetail(allDataList, list, migrateDate);
		// 计算调整库存量
		calculateDataRangeByAdjustDetail(allDataList, list);
		// 清理进销调整量为0的，期初、出库量、入库量、期末全为0的
		clearAdjustQtyZero(allDataList);
		// 调整进销存库存，批量更新增加，调整单明细状态，批量更新
		EntrySaleStockService entrySaleStockService = SpringBeanUtil.getBean(EntrySaleStockService.class);
		entrySaleStockService.adjustQtyBatchUpdate(allDataList, list);
		// 调整后的进销存保存到es
		adjustEntrySaleStockQtySyncToEs(allDataList);
	}

	/**
	 * 查询条件获取，分为两步合并：<br/>
	 * 1.合并，按相同实仓Id,skuId，时间范围有交集的 <br/>
	 * 2.合并，按相同实仓Id,skuId，时间范围
	 * @param list
	 * @return
	 */
	private List<EntrySaleStockDO> mergeQueryParmByRwSkuIdDate(List<EntrySaleAdjustDetailDO> list) {
		// 排序，查询条件获取前要排序操作
		sortByMergeQueryParmByRwSkuIdDate(list);
		// 查询条件
		List<EntrySaleStockDO> queryParmList = new ArrayList<>();
		// 合并，按相同实仓Id,skuId，时间范围有交集的 start
		EntrySaleStockDO temp;
		Date financeDate;
		for(EntrySaleAdjustDetailDO dto : list) {
			// 只有状态为：0-待处理 1-扣减库存日期待处理 才处理
			if(dto.isFlag() == false) {
				continue;
			}
			// 当为只需要处理操作库存调整时，只需要查询当天的即可
			if(EntrySaleAdjustDetailStatusVO.WAIT_OPT_DATE.getStatus().equals(dto.getStatus())) {
				financeDate = dto.getOperateDate();
			} else {
				financeDate = dto.getFinanceDate();
			}
			temp = getDataInnerByMergeQueryParmByRwSkuIdDate(queryParmList, dto, financeDate);
			if(temp == null) {
				temp = new EntrySaleStockDO();
				temp.setRealWarehouseId(dto.getRealWarehouseId());
				temp.setSkuId(dto.getSkuId());
				temp.setStartTime(financeDate);
				temp.setEndTime(dto.getOperateDate());
				temp.setSkuIds(null);
				queryParmList.add(temp);
			} else {
				// 存在相同实仓Id,skuId，时间范围有交集的
				// 若查询的开始时间大于当前的财务时间，要扩大范围
				if(temp.getStartTime().getTime() > financeDate.getTime()) {
					temp.setStartTime(financeDate);
				}
				// 若查询的结束时间小于当前的库存操作时间，要扩大范围
				if(temp.getEndTime().getTime() < dto.getOperateDate().getTime()) {
					temp.setEndTime(dto.getOperateDate());
				}
			}
		}
		// 合并，按相同实仓Id,skuId，时间范围有交集的 end
		// 合并，查询条件，按相同实仓Id,时间范围相同 start
		EntrySaleStockDO source;
		EntrySaleStockDO dest;
    	for (int i = 0; i < queryParmList.size(); i++) {
    		source = queryParmList.get(i);
    		for (int j = i + 1; j < queryParmList.size(); j++) {
    			dest = queryParmList.get(j);
    			if(source.getRealWarehouseId().equals(dest.getRealWarehouseId())
    	    			 && source.getStartTime().getTime() == dest.getStartTime().getTime()
    	    			 && source.getEndTime().getTime() == dest.getEndTime().getTime()) {
    				if(source.getSkuIds() == null) {
    					source.setSkuIds(new ArrayList<>());
    					source.getSkuIds().add(source.getSkuId());
    				}
    				if(!source.getSkuIds().contains(dest.getSkuId())) {
    					source.getSkuIds().add(dest.getSkuId());
    				}
    				queryParmList.remove(j);
    				j--;
    			}
    		}
    	}
    	// 合并，查询条件，按相同实仓Id,时间范围相同 start
		return queryParmList;
	}
	
	/**
	 * 排序，按财务日期升序，查询条件获取前要排序操作
	 * @param list
	 */
	private void sortByMergeQueryParmByRwSkuIdDate(List<EntrySaleAdjustDetailDO> list) {
		Collections.sort(list, new Comparator<EntrySaleAdjustDetailDO>() {
            @Override
            public int compare(EntrySaleAdjustDetailDO o1, EntrySaleAdjustDetailDO o2) {
            	long o1Time;
            	if(o1.isFlag() == false) {
            		o1Time = Long.MAX_VALUE;
            	} else if(EntrySaleAdjustDetailStatusVO.WAIT_OPT_DATE.getStatus().equals(o1.getStatus())) {
            		o1Time = o1.getOperateDate().getTime();
    			} else {
    				o1Time = o1.getFinanceDate().getTime();
    			}
            	long o2Time;
            	if(o2.isFlag() == false) {
            		o2Time = Long.MAX_VALUE;
            	} else if(EntrySaleAdjustDetailStatusVO.WAIT_OPT_DATE.getStatus().equals(o2.getStatus())) {
            		o2Time = o2.getOperateDate().getTime();
    			} else {
    				o2Time = o2.getFinanceDate().getTime();
    			}
            	return (o1Time<o2Time ? -1 : (o1Time==o2Time ? 0 : 1));
            }
        });
	}
	
	/**
	 * 合并重复的，初始化值为0,这些字段后续用作改变量
	 * @param list
	 * @return
	 */
	private void mergeAndInitDataByAdjust(List<EntrySaleStockDO> list) {
		if(list.size() == 0) {
			return;
		}
		// 
		EntrySaleStockDO source;
		EntrySaleStockDO dest;
    	for (int i = 0; i < list.size(); i++) {
    		source = list.get(i);
    		// 初始化值为0,这些字段后续用作改变量
    		source.setInitialQty(BigDecimal.ZERO);
    		source.setInQty(BigDecimal.ZERO);
    		source.setOutQty(BigDecimal.ZERO);
    		source.setFinalQty(BigDecimal.ZERO);
    		// 合并重复的，理论上查询出来的没有重复的，保险点，还是做下重复移除
    		for (int j = i + 1; j < list.size(); j++) {
    			dest = list.get(j);
    			if(source.getId().equals(dest.getId())) {
    				list.remove(j);
    				j--;
    			}
    		}
    	}
	}
	
	/**
	 * 清理进销调整量为0的，期初、出库量、入库量、期末全为0的
	 * @param allDataList
	 */
	private void clearAdjustQtyZero(List<EntrySaleStockDO> allDataList) {
		EntrySaleStockDO dto;
		for (int j = allDataList.size() - 1 ; j >= 0; j--) {
			dto = allDataList.get(j);
			if(dto.getInitialQty().compareTo(BigDecimal.ZERO) == 0 && dto.getInQty().compareTo(BigDecimal.ZERO) == 0
					 && dto.getOutQty().compareTo(BigDecimal.ZERO) == 0&& dto.getOutQty().compareTo(BigDecimal.ZERO) == 0) {
				allDataList.remove(j);
			}
		}
	}
	
	/**
	 * 获取数据,仓Id相同、skuId相同、时间范围有重叠的
	 * @param queryParmList
	 * @param data
	 * @param financeDate 这是只处理库存操作日期时，没必要其他日期也查出
	 * @return
	 */
	private EntrySaleStockDO getDataInnerByMergeQueryParmByRwSkuIdDate(List<EntrySaleStockDO> queryParmList, EntrySaleAdjustDetailDO data, Date financeDate) {
		for(EntrySaleStockDO dto : queryParmList) {
			if(data.getRealWarehouseId().equals(dto.getRealWarehouseId()) && data.getSkuId().equals(dto.getSkuId())
					 && validateDateRange(financeDate, data.getOperateDate(), dto.getStartTime(), dto.getEndTime())) {
				return dto;
			}
		}
		return null;
	}
	
	/**
	 * 验证时间范围是否有重叠部分
	 * @param startTime1 组1开始时间
	 * @param endTime1 组1结束时间
	 * @param startTime2 组2开始时间
	 * @param endTime2 组2结束时间
	 * @return
	 */
	private boolean validateDateRange(Date startTime1, Date endTime1, Date startTime2, Date endTime2) {
		if((startTime1.getTime() >= startTime2.getTime() && startTime1.getTime() <= endTime2.getTime())
				|| (startTime2.getTime() >= startTime1.getTime() && startTime2.getTime() <= endTime1.getTime())) {
			return true;
		}
		return false;
	}
	
	/**
	 * 验证 调整单明细对应的进销存数据是否完整齐全，不能处理的设置flag=false;并设置下次重试时间等
	 * @param allDataList
	 * @param list
	 * @param migrateDate 获取进销存迁移时间，小于这个时间，都应迁到es了
	 */
	private void validateDataRangeByAdjustDetail(List<EntrySaleStockDO> allDataList, List<EntrySaleAdjustDetailDO> list, Date migrateDate) {
		// 一天时间毫秒数
		final long gapTime = 24 * 60 * 60 * 1000L;
		EntrySaleStockDO temp;
		long startTime;
		long endTime;
		Date tomorrow = nextTimeByWaitOptDate(); // 明天下次重试时间，针对当天
		Date nextTime = nextTimeByNormal(); // 正常的下次处理时间，为60分钟后
		long nowBeginTime = DateUtil.getDayBegin(new Date()).getTime(); // 今天开始时间
		List<EntrySaleStockDO> insertList = new ArrayList<>();
		for(EntrySaleAdjustDetailDO dto : list) {
			if(dto.isFlag()) {
				if(insertList.size() > 0) {
					insertList.clear();
				}
				// 当为只需要处理操作库存调整时，只需要查询当天的即可
				if(EntrySaleAdjustDetailStatusVO.WAIT_OPT_DATE.getStatus().equals(dto.getStatus())) {
					startTime = dto.getOperateDate().getTime();
				} else {
					startTime = dto.getFinanceDate().getTime();
				}
				endTime = dto.getOperateDate().getTime();
				temp = null;
				do {
					temp = getDataInnerByValidateData(allDataList, dto, startTime);
					if(temp == null) {
						// 针对的，财务调整日期，库存记录还没有产生，没有产生进销存时，需要新增一条进销存数据
						temp = getNewEntrySaleStockByAdjust(dto, migrateDate, nowBeginTime, startTime);
						if(temp != null) {
							insertList.add(temp);
						}
					}
					if(temp == null) {
						if(startTime < endTime) {
							dto.setFlag(false);
							dto.setTryTimes(dto.getTryTimes() + 1);
							dto.setNextTime(nextTime);
							dto.setErrorMsg("进销存数据不完整无法处理");
						} else if(startTime == endTime && EntrySaleAdjustDetailStatusVO.WAIT_OPT_DATE.getStatus().equals(dto.getStatus())) {
							dto.setFlag(false);
							dto.setTryTimes(dto.getTryTimes() + 1);
							if(nowBeginTime == endTime) {
								dto.setNextTime(tomorrow);// 这种明天再处理
							} else {
								dto.setNextTime(nextTime);// 这种下次再处理
							}
							dto.setErrorMsg("操作库存当天的进销存数据不存在");
						}
						break;
					}
					startTime += gapTime;
				} while (startTime <= endTime);
				if(dto.isFlag()) {
					if(insertList.size() > 0) {
						allDataList.addAll(insertList);
					}
				}
			}
		}
	}
	
	/**
	 * 计算调整库存量
	 * @param allDataList
	 * @param list
	 */
	private void calculateDataRangeByAdjustDetail(List<EntrySaleStockDO> allDataList, List<EntrySaleAdjustDetailDO> list) {
		// 一天时间毫秒数
		final long gapTime = 24 * 60 * 60 * 1000L;
		EntrySaleStockDO temp;
		long startTime;
		long endTime;
		Date tomorrow = nextTimeByWaitOptDate(); // 明天下次重试时间，针对当天
		Date nextTime = nextTimeByNormal(); // 正常的下次处理时间，为60分钟后
		long nowBeginTime = DateUtil.getDayBegin(new Date()).getTime(); // 今天开始时间
		for(EntrySaleAdjustDetailDO dto : list) {
			if(dto.isFlag()) {
				// 当为只需要处理操作库存调整时，只需要查询当天的即可
				if(EntrySaleAdjustDetailStatusVO.WAIT_OPT_DATE.getStatus().equals(dto.getStatus())) {
					startTime = dto.getOperateDate().getTime();
				} else {
					startTime = dto.getFinanceDate().getTime();
				}
				endTime = dto.getOperateDate().getTime();
				temp = null;
				do {
					temp = getDataInnerByValidateData(allDataList, dto, startTime);
					if(temp == null) {
						if(startTime < endTime) {
							throw new RomeException(ResCode.STOCK_ERROR_1003, "库存调整计算操作库存量，进销存不完整，验证出错");
						} else if(startTime == endTime) {
							dto.setFlag(false);
							dto.setTryTimes(dto.getTryTimes() + 1);
							if(EntrySaleAdjustDetailStatusVO.WAIT.getStatus().equals(dto.getStatus())) {
								dto.setStatus(EntrySaleAdjustDetailStatusVO.WAIT_OPT_DATE.getStatus());
								dto.setErrorMsg("操作库存当天的进销存数据不存在");
							}
							if(nowBeginTime == endTime) {
								dto.setNextTime(tomorrow);// 这种明天再处理
							} else {
								dto.setNextTime(nextTime);// 这种下次再处理
							}
						}
					} else {
						// 如果销存时间为财务日期时，调整当天的出入库库存和期末
						if(startTime == dto.getFinanceDate().getTime()) {
							// 如果单据是出库单时 财务日期对应进销存出库量加调整库存 ,期末减调整库存
							if(WarehouseRecordBusinessTypeVO.OUT_WAREHOUSE_RECORD.getType().equals(dto.getBusinessType())) {
								temp.setOutQty(temp.getOutQty().add(dto.getQty()));
								temp.setFinalQty(temp.getFinalQty().subtract(dto.getQty()));
							} else {
								// 如果单据是入库单时 财务日期对应进销存入库量加调整库存 ,期末加调整库存
								temp.setInQty(temp.getInQty().add(dto.getQty()));
								temp.setFinalQty(temp.getFinalQty().add(dto.getQty()));
							}
						} else if(startTime == dto.getOperateDate().getTime()) {
							// 如果销存时间为库存操作日期时，调整当天的出入库库存和期初
							// 如果单据是出库单时 对应进销存出库量减调整库存 ,期初减调整库存
							if(WarehouseRecordBusinessTypeVO.OUT_WAREHOUSE_RECORD.getType().equals(dto.getBusinessType())) {
								temp.setOutQty(temp.getOutQty().subtract(dto.getQty()));
								temp.setInitialQty(temp.getInitialQty().subtract(dto.getQty()));
								//期末=期初-出量+入量
							} else {
								// 如果单据是入库单时 财务日期对应进销存入库量加调整库存 ,期末加调整库存
								temp.setInQty(temp.getInQty().subtract(dto.getQty()));
								temp.setInitialQty(temp.getInitialQty().add(dto.getQty()));
							}
							// 到这个时间，证明处理完成
							dto.setStatus(EntrySaleAdjustDetailStatusVO.SUCCESS.getStatus());
							dto.setErrorMsg(EntrySaleAdjustDetailStatusVO.SUCCESS.getDesc());
						} else {
							// 中间日期的进销存
							// 单据是出库单
							if(WarehouseRecordBusinessTypeVO.OUT_WAREHOUSE_RECORD.getType().equals(dto.getBusinessType())) {
								temp.setInitialQty(temp.getInitialQty().subtract(dto.getQty()));
								temp.setFinalQty(temp.getFinalQty().subtract(dto.getQty()));
							} else {
								// 如果单据是入库单
								temp.setInitialQty(temp.getInitialQty().add(dto.getQty()));
								temp.setFinalQty(temp.getFinalQty().add(dto.getQty()));
							}
						}
					}
					startTime += gapTime;
				} while (startTime <= endTime);
			}
		}
	}
	
	/**
	 * 调整进销存库存，批量更新增加，调整单明细状态，批量更新
	 * @param list
	 * @param adjustList
	 */
    @Override
	@Transactional(rollbackFor = Exception.class)
	public void adjustQtyBatchUpdate(List<EntrySaleStockDO> list, List<EntrySaleAdjustDetailDO> adjustList) {
    	// 调整进销存库存，批量更新库存
        if(list.size() > 0) {
        	// 新插入的
        	List<EntrySaleStockDO> insertList = new ArrayList<>();
        	List<EntrySaleStockDO> updateList = new ArrayList<>(list.size());
        	for (EntrySaleStockDO dto : list) {
    			if(dto.getId() == null) {
    				insertList.add(dto);
    			} else {
    				updateList.add(dto);
    			}
    		}
        	// 新插入
        	if(insertList.size() > 0) {
        		if(insertList.size() > 1000) {
    				int maxPage = ListUtil.getPageNum(insertList, 1000);
    		        for(int i = 1; i <= maxPage; i++) {
    		        	entrySaleStockMapper.batchInsert(ListUtil.getPageList(insertList, i,1000));
    		        }
    			}else {
    				entrySaleStockMapper.batchInsert(insertList);
    			}
        	}
            // 更新
        	if(updateList.size() > 0) {
        		if(updateList.size() > 1000) {
    				int maxPage = ListUtil.getPageNum(updateList, 1000);
    		        for(int i=1; i <= maxPage;i++) {
    		        	// 调整进销存库存，批量更新库存
    		        	adjustEntrySaleStockQtyBatchUpdate(ListUtil.getPageList(updateList, i,1000));
    		        }
    			}else {
    				// 调整进销存库存，批量更新库存
    				adjustEntrySaleStockQtyBatchUpdate(updateList);
    			}
        	}
		}
    	
		// 调整单明细状态，批量更新
        if(adjustList.size() > 0) {
        	if(adjustList.size() > 1000) {
				int maxPage = ListUtil.getPageNum(adjustList, 1000);
		        for(int i = 1; i <= maxPage; i++) {
		        	adjustDetailStatusBatchUpdate(ListUtil.getPageList(adjustList, i,1000));
		        }
			}else {
				adjustDetailStatusBatchUpdate(adjustList);
			}
		}
	}
    
    /**
     * 调整后的进销存保存到es
     * @param list
     */
    private void adjustEntrySaleStockQtySyncToEs(List<EntrySaleStockDO> list) {
    	if(list == null || list.size() == 0) {
    		return;
    	}
		if(list.size() > 0) {
			if(list.size() > 1000) {
				int maxPage = ListUtil.getPageNum(list, 1000);
		        for(int i=1; i <= maxPage;i++) {
		        	List<EntrySaleStockDO> listData = entrySaleStockMapper.queryByDataId(ListUtil.getPageList(list, i,1000));
		        	// 保存数据到es
		        	saveToEs(listData);
		        }
			}else {
				List<EntrySaleStockDO> listData = entrySaleStockMapper.queryByDataId(list);
	        	// 保存数据到es
	        	saveToEs(listData);
			}
		}
    }
    
    /**
     * 保存数据到es
     * @param list
     */
    private void saveToEs(List<EntrySaleStockDO> list) {
    	if(list != null && list.size() > 0) {
    		// es保存数据
    		EsEntrySaleStockDO esFlowDO;
    		EsIndexTypeConfig.clearIndexNameSuffix();
    		List<EsEntrySaleStockDO> eslist = new ArrayList<>(list.size());
			for(EntrySaleStockDO dto : list) {
				esFlowDO = esEntrySaleStockConvertor.entrySaleStockDOEntityToDo(dto);
				esFlowDO.setUniqId(dto.getEntrySaleDate().getTime() + "_" + dto.getRealWarehouseId() + "_" + dto.getSkuId());
				eslist.add(esFlowDO);
			}
			Map<String, List<EsEntrySaleStockDO>> map = esSplitMonthBySaveEntrySale(eslist);
			for (Map.Entry<String, List<EsEntrySaleStockDO>> entry : map.entrySet()) {
				EsIndexTypeConfig.setIndexNameSuffixByYear("1", entry.getValue().get(0).getEntrySaleDate());
				if(esTemplate.indexExists(EsEntrySaleStockDO.class) == false) {
					esTemplate.createIndex(EsEntrySaleStockDO.class);
					esTemplate.putMapping(EsEntrySaleStockDO.class);
				}
				esEntrySaleStockRepository.saveAll(entry.getValue());
				EsIndexTypeConfig.clearIndexNameSuffix();
	        }
		}
    }
    
    /**
	 * es保存时，根据不同的月份，存到不到位置
	 * @param dos
	 * @return
	 */
	private Map<String, List<EsEntrySaleStockDO>> esSplitMonthBySaveEntrySale(List<EsEntrySaleStockDO> dos) {
		SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM");
		Map<String, List<EsEntrySaleStockDO>> map = new HashMap<>(3);
		List<EsEntrySaleStockDO> list = null;
		String yy = null;
		for(EsEntrySaleStockDO dto : dos) {
			yy = dateFormat.format(dto.getEntrySaleDate());
			list = map.get(yy);
			if(list == null) {
				list = new ArrayList<>(1000);
				map.put(yy, list);
			}
			list.add(dto);
		}
		list = null;
		yy = null;
		dateFormat = null;
		return map;
	}
    
    /**
     * 调整进销存库存，批量更新库存，验证并发操作
     * @param list
     */
    private void adjustEntrySaleStockQtyBatchUpdate(List<EntrySaleStockDO> list) {
    	int num = entrySaleStockMapper.batchAdjustQty(list);
    	if(num != list.size()) {
			throw new RomeException(ResCode.STOCK_ERROR_1001, "调整进销存库存，批量更新库存，更新进销存库存失败," + JSON.toJSONString(list));
		}
    	List<EntrySaleStockDO> afterList = entrySaleStockMapper.queryByDataId(list);
    	if(afterList == null || afterList.size() == 0) {
    		throw new RomeException(ResCode.STOCK_ERROR_1001, "调整进销存库存，批量更新库存，操作时数据库中全不存在," + JSON.toJSONString(list));
    	}
    	EntrySaleStockDO temp;
    	for(EntrySaleStockDO dto : list) {
    		temp = null;
    		for(EntrySaleStockDO dto2 : afterList) {
    			if(dto2.getId().equals(dto.getId())) {
    				if(dto2.getVersionNo().intValue() != dto.getVersionNo().intValue() + 1) {
    					throw new RomeException(ResCode.STOCK_ERROR_1001, "调整进销存库存，批量更新库存，更新时有并发失败," + JSON.toJSONString(dto));
    				}
    				temp = dto2;
    				break;
    			}
    		}
    		if(temp == null) {
    			throw new RomeException(ResCode.STOCK_ERROR_1001, "调整进销存库存，批量更新库存，操作时数据库中不存在," + JSON.toJSONString(dto));
    		}
    	}
    }
    
    /**
     * 调整单明细状态，批量更新，验证并发操作
     * @param list
     */
    private void adjustDetailStatusBatchUpdate(List<EntrySaleAdjustDetailDO> list) {
    	int num = entrySaleAdjustDetailMapper.batchUpdateByStatus(list);
    	if(num != list.size()) {
			throw new RomeException(ResCode.STOCK_ERROR_1001, "调整进销存库存，批量更新调整单明细状态，失败," + JSON.toJSONString(list));
		}
    	List<EntrySaleAdjustDetailDO> afterList = entrySaleAdjustDetailMapper.queryByDataId(list);
    	if(afterList == null || afterList.size() == 0) {
    		throw new RomeException(ResCode.STOCK_ERROR_1001, "调整进销存库存，批量更新调整单明细状态，操作时数据库中全不存在," + JSON.toJSONString(list));
    	}
    	EntrySaleAdjustDetailDO temp;
    	for(EntrySaleAdjustDetailDO dto : list) {
    		temp = null;
    		for(EntrySaleAdjustDetailDO dto2 : afterList) {
    			if(dto2.getId().equals(dto.getId())) {
    				if(dto2.getVersionNo().intValue() != dto.getVersionNo().intValue() + 1) {
    					throw new RomeException(ResCode.STOCK_ERROR_1001, "调整进销存库存，批量更新调整单明细状态，更新时有并发失败," + JSON.toJSONString(dto));
    				}
    				temp = dto2;
    				break;
    			}
    		}
    		if(temp == null) {
    			throw new RomeException(ResCode.STOCK_ERROR_1001, "调整进销存库存，批量更新调整单明细状态，操作时数据库中不存在," + JSON.toJSONString(dto));
    		}
    	}
    }
	
	/**
	 * 获取数据,根据，仓Id、skuId、时间
	 * @param allDataList
	 * @param data
	 * @param time
	 * @return
	 */
	private EntrySaleStockDO getDataInnerByValidateData(List<EntrySaleStockDO> allDataList, EntrySaleAdjustDetailDO data, long time) {
		for(EntrySaleStockDO dto : allDataList) {
			if(data.getRealWarehouseId().equals(dto.getRealWarehouseId()) && data.getSkuId().equals(dto.getSkuId())
					 && dto.getEntrySaleDate().getTime() == time) {
				return dto;
			}
		}
		return null;
	}
	
	/**
	 * 下次处理时间，针对场景，
	 * 扣减库存日期需处理  </br>（注：当天扣减库存时，这时进销存还未生成，所以当天的库存不处理，其他日期已处理完成）
	 * @return
	 */
	private Date nextTimeByWaitOptDate() {
		Calendar cal = Calendar.getInstance();
		cal.set(Calendar.HOUR_OF_DAY, 5);
		cal.set(Calendar.MINUTE, 0);
		cal.set(Calendar.SECOND, 0);
		cal.set(Calendar.MILLISECOND, 0);
		cal.add(Calendar.DAY_OF_YEAR, 1);
		return cal.getTime(); // 明天下次重试时间,时间为明天5点
	}
	
	/**
	 * 正常的下次处理时间，为60分钟后
	 * @return
	 */
	private Date nextTimeByNormal() {
		return new Date(System.currentTimeMillis() + 60 * 60 * 1000); // 正常的下次处理时间，为60分钟后
	}
	
	/**
	 * 获取进销存迁移时间，小于这个时间，都应迁到es了
	 * @return
	 */
	private Date getMigrateToEsDateTime() {
		Integer dbSaveDay = SpringBeanUtil.getBean(EntrySaleStockInactiveTemplate.class).getDbSaveDay();
		if(dbSaveDay == null) {
			dbSaveDay = 7;
		}
		Calendar cal = Calendar.getInstance();
		cal.set(Calendar.HOUR_OF_DAY, 0);
		cal.set(Calendar.MINUTE, 0);
		cal.set(Calendar.SECOND, 0);
		cal.set(Calendar.MILLISECOND, 0);
		cal.add(Calendar.DAY_OF_MONTH, -dbSaveDay);
		cal.add(Calendar.MILLISECOND, -1);
		return cal.getTime();
	}
	
	/**
	 * 获取新增加进销存数据，根据调整单，
	 * 说明：针对的，财务调整日期，库存记录还没有产生，没有产生进销存时，需要新增一条进销存数据
	 * @param adjustDetailDO
	 * @param migrateDate
	 * @param nowBeginTime
	 * @param time
	 * @return
	 */
	private EntrySaleStockDO getNewEntrySaleStockByAdjust(EntrySaleAdjustDetailDO adjustDetailDO, Date migrateDate, long nowBeginTime, long time) {
		// 若时间为今天的，或者已超过迁移的时间，不能新增进销存记录
		if(time == nowBeginTime || time <= migrateDate.getTime()) {
			return null;
		}
		// 判断库存记录不合规的，若库存记录不存在
		RealWarehouseStockDO realWarehouseStockDO = realWarehouseStockMapper.queryBySkuIdReadWarehouseId(adjustDetailDO.getSkuId(), adjustDetailDO.getRealWarehouseId());
		if(realWarehouseStockDO == null || realWarehouseStockDO.getCreateTime() == null) {
			return null;
		}
		// 判断库存记录不合规的，库存记录创建时间（当天最小时间）小于等于调整时间
		long createTimeBegin = DateUtil.getDayBegin(realWarehouseStockDO.getCreateTime()).getTime(); // 最小时间
		if(createTimeBegin <= time) {
			return null;
		}
		EntrySaleStockDO entrySaleStockDO = new EntrySaleStockDO();
		entrySaleStockDO.setRealWarehouseId(adjustDetailDO.getRealWarehouseId());
		entrySaleStockDO.setSkuId(adjustDetailDO.getSkuId());
		entrySaleStockDO.setSkuCode(adjustDetailDO.getSkuCode());
		entrySaleStockDO.setInitialQty(BigDecimal.ZERO);
		entrySaleStockDO.setInQty(BigDecimal.ZERO);
		entrySaleStockDO.setOutQty(BigDecimal.ZERO);
		entrySaleStockDO.setFinalQty(BigDecimal.ZERO);
		entrySaleStockDO.setEntrySaleDate(new Date(time));
		entrySaleStockDO.setOrginInitialQty(BigDecimal.ZERO);
		entrySaleStockDO.setOrginInQty(BigDecimal.ZERO);
		entrySaleStockDO.setOrginOutQty(BigDecimal.ZERO);
		entrySaleStockDO.setOrginFinalQty(BigDecimal.ZERO);
		return entrySaleStockDO;
	}
	
}
