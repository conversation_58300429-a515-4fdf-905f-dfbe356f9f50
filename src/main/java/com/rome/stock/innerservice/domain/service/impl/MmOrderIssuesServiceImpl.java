package com.rome.stock.innerservice.domain.service.impl;

import com.rome.arch.core.clientobject.Response;
import com.rome.stock.common.dto.wms.OutOrderDto;
import com.rome.stock.innerservice.api.dto.wms.EntryOrderDto;
import com.rome.stock.innerservice.domain.service.MmOrderIssuesService;
import com.rome.stock.innerservice.remote.mm.facade.MmOrderIssuesFacade;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <AUTHOR>
 * @Description 下发到mm模块相关业务
 * @date 2020/7/15 9:53
 */
@Service
@Slf4j
public class MmOrderIssuesServiceImpl implements MmOrderIssuesService {
    @Autowired
    private MmOrderIssuesFacade mmOrderIssuesFacade;


    @Override
    public Response pushInOrderDataToMM(EntryOrderDto entryOrderDto) {
        return mmOrderIssuesFacade.pushInOrderData(entryOrderDto);
    }

    @Override
    public Response pushOutOrderDataToMM(OutOrderDto outOrderDto) {
        return mmOrderIssuesFacade.pushOutOrderData(outOrderDto);
    }


    @Override
    public Response cancelDeliveryOrder(List<String> deliveryOrderCode,Integer orderType) {
        return mmOrderIssuesFacade.cancelDeliveryOrder(deliveryOrderCode,orderType);
    }
}
   