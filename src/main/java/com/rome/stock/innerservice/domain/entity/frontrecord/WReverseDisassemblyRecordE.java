package com.rome.stock.innerservice.domain.entity.frontrecord;

import com.rome.stock.innerservice.constant.FrontRecordTypeVO;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * 仓库反拆加工单
 */
@Component
@Scope("prototype")
@Data
@EqualsAndHashCode(callSuper = false)
public class WReverseDisassemblyRecordE extends AbstractFrontRecord {

    /**
     * 创建前置单据
     */
    public void addRecord() {
        //单据编号生成
        initFrontRecord(FrontRecordTypeVO.WAREHOUSE_REVERSE_DISASSEMBLY_TASK_RECORD.getCode(), this.frontRecordDetails);
        //long id=frontRecordRepository.addFrontRecord(this);
       // this.setId(id);
       // this.frontRecordDetails.forEach(record -> record.setFrontRecordDetail(this) );
        //frontRecordRepository.addAllFrontRecordSku(this.frontRecordDetails);
    }

    /**
     * 取消订单
     */
    public void disableRecord(){

    }

    /**
     * 入向实体仓库id
     */
    private Long inRealWarehouseId;

    /**
     * 出向实体仓库id
     */
    private Long outRealWarehouseId;

    /**
     * 商品数量
     */
    private List<WReverseDisassemblyRecordDetailE> frontRecordDetails;
}
