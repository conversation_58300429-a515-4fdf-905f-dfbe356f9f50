package com.rome.stock.innerservice.domain.entity.warehouserecord;

import com.rome.arch.core.domain.EntityFactory;
import com.rome.stock.common.enums.warehouse.WarehouseRecordTypeVO;
import com.rome.stock.innerservice.common.SkuQtyUnitTools;
import com.rome.stock.innerservice.common.StringUtils;
import com.rome.stock.innerservice.constant.WarehouseRecordBusinessTypeVO;
import com.rome.stock.innerservice.constant.WmsSyncStatusVO;
import com.rome.stock.innerservice.domain.entity.frontrecord.AdjustMerchantRecordDetailE;
import com.rome.stock.innerservice.domain.entity.frontrecord.AdjustMerchantRecordE;
import com.rome.stock.innerservice.domain.repository.frontrecord.FrAdjustMerchantRepository;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;

/**
 * 商家库存调整
 *
 * <AUTHOR>
 */
@Data
@Slf4j
@Component
@Scope("prototype")
@EqualsAndHashCode(callSuper = false)
public class AdjustMerchantWarehouseRecordE extends AbstractWarehouseRecord {


    @Resource
    private EntityFactory entityFactory;
    @Resource
    private FrAdjustMerchantRepository frAdjustMerchantRepository;
    @Resource
    private SkuQtyUnitTools skuQtyUnitTools;

    /**
     * 保存入库单
     */
    public void addWarehouseRecord() {
        //保存入库单
        long id = frAdjustMerchantRepository.saveWarehouseRecord(this);
        this.setId(id);
        this.warehouseRecordDetails.forEach(record -> record.setWarehouseRecordDetail(this));
        frAdjustMerchantRepository.saveWarehouseRecordDetails(this.warehouseRecordDetails);
        this.addFrontRecordAndWarehouseRelation();
    }


    /**
     * 根据前置单生成出库单
     */
    public void createRecordByFrontRecord(AdjustMerchantRecordE frontRecord , WarehouseRecordBusinessTypeVO typeVO ) {
        if (WarehouseRecordBusinessTypeVO.OUT_WAREHOUSE_RECORD.getType().equals(typeVO.getType())) {
            createRecodeCode(WarehouseRecordTypeVO.ADJUST_MERCHANT_WAREHOUSE_OUT_RECORD.getCode());
            this.setBusinessType(WarehouseRecordBusinessTypeVO.OUT_WAREHOUSE_RECORD.getType());
            this.setRecordType(WarehouseRecordTypeVO.ADJUST_MERCHANT_WAREHOUSE_OUT_RECORD.getType());
        }else if (WarehouseRecordBusinessTypeVO.IN_WAREHOUSE_RECORD.getType().equals(typeVO.getType())) {
            createRecodeCode(WarehouseRecordTypeVO.ADJUST_MERCHANT_WAREHOUSE_IN_RECORD.getCode());
            this.setBusinessType(WarehouseRecordBusinessTypeVO.IN_WAREHOUSE_RECORD.getType());
            this.setRecordType(WarehouseRecordTypeVO.ADJUST_MERCHANT_WAREHOUSE_IN_RECORD.getType());
        }
        this.setAbstractFrontRecord(frontRecord);
        this.setRealWarehouseId(frontRecord.getRealWarehouseId());
        this.setDeliveryTime(frontRecord.getOutCreateTime());
        this.setMerchantId(frontRecord.getMerchantId());
        this.setOutCreateTime(frontRecord.getOutCreateTime());
        this.setSyncWmsStatus(WmsSyncStatusVO.NO_REQUIRED.getStatus());
        this.setChannelCode(frontRecord.getChannelCode());
        List<AdjustMerchantRecordDetailE> frontRecordDetails = frontRecord.getDetails();
        this.setWarehouseRecordDetails(new ArrayList<>(frontRecordDetails.size()));

        for (AdjustMerchantRecordDetailE detailE : frontRecordDetails) {
            WarehouseRecordDetail warehouseRecordDetail = entityFactory.createEntity(WarehouseRecordDetail.class);
            warehouseRecordDetail.createRecordDetailByActualFrontRecord(detailE);
            this.warehouseRecordDetails.add(warehouseRecordDetail);
        }
        Boolean flag=false;
        //基本单位不存在时，修改
        for(WarehouseRecordDetail warehouseRecordDetail:this.warehouseRecordDetails){
            if(StringUtils.isEmpty(warehouseRecordDetail.getUnitCode())){
                flag=true;
            }
        }
        if(flag){
            skuQtyUnitTools.queryBasicUnit(this.warehouseRecordDetails);
        }
        //设置skuCode和skuID
        this.initWarehouseRecodeDetail();

    }



}
