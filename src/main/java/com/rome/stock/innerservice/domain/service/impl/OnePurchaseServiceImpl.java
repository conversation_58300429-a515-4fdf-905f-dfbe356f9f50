package com.rome.stock.innerservice.domain.service.impl;

import java.util.ArrayList;
import java.util.Calendar;
import java.util.Date;
import java.util.List;

import javax.annotation.Resource;

import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.rome.stock.common.constants.warehouse.WarehouseRecordConstant;
import com.rome.stock.common.enums.warehouse.WarehouseRecordTypeVO;
import com.rome.stock.common.utils.DateUtil;
import com.rome.stock.innerservice.api.dto.frontrecord.PurchaseOrderDTO;
import com.rome.stock.innerservice.common.AlikAssert;
import com.rome.stock.innerservice.common.ResCode;
import com.rome.stock.innerservice.constant.WarehouseRecordBusinessTypeVO;
import com.rome.stock.innerservice.constant.WarehouseRecordStatusVO;
import com.rome.stock.innerservice.constant.WmsSyncStatusVO;
import com.rome.stock.innerservice.constant.WmsSyncTransferStatusVO;
import com.rome.stock.innerservice.domain.entity.RwBatchE;
import com.rome.stock.innerservice.domain.entity.frontrecord.PurchaseOrderE;
import com.rome.stock.innerservice.domain.entity.warehouserecord.AbstractWarehouseRecord;
import com.rome.stock.innerservice.domain.entity.warehouserecord.PurchaseWarehouseRecordE;
import com.rome.stock.innerservice.domain.entity.warehouserecord.WarehouseRecordDetail;
import com.rome.stock.innerservice.domain.repository.RwBatchRepository;
import com.rome.stock.innerservice.domain.repository.frontrecord.FrPurchaseOrderRepository;
import com.rome.stock.innerservice.domain.repository.warehouserecord.WarehouseRecordRepository;
import com.rome.stock.innerservice.domain.service.AbstractPurchase;

import lombok.extern.slf4j.Slf4j;

/**
 * 一件代发
 */
@Service(value = "onePurchase")
@Slf4j
public class OnePurchaseServiceImpl extends AbstractPurchase{

    @Resource
    private WarehouseRecordRepository warehouseRecordRepository;
    @Resource
    private RwBatchRepository rwBatchRepository;
    @Resource
    private FrPurchaseOrderRepository frPurchaseOrderRepository;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void createPurchaseIn(PurchaseOrderDTO purchaseOrderDTO, PurchaseWarehouseRecordE purchaseWarehouseRecordE) {
        PurchaseOrderE purchaseOrderE = super.createPurchaseFrontRecordCode(purchaseOrderDTO);
        //更新前置单状态为已入库
        frPurchaseOrderRepository.updateInitToInAllocation(purchaseOrderE.getId());
        purchaseWarehouseRecordE.createRecodeCode("PI");
        purchaseWarehouseRecordE.setBusinessType(WarehouseRecordBusinessTypeVO.IN_WAREHOUSE_RECORD.getType());
        //需要wms拉取，设置状态为未同步
        purchaseWarehouseRecordE.setSyncWmsStatus(WmsSyncStatusVO.NO_REQUIRED.getStatus());
        purchaseWarehouseRecordE.setRecordType(WarehouseRecordTypeVO.ONE_PURCHASE_RECORD.getType());
        //根据前置单生成入库单数据
        purchaseWarehouseRecordE.createInRecordByFrontRecord(purchaseOrderE);
        purchaseWarehouseRecordE.setRecordStatus(WarehouseRecordStatusVO.IN_ALLOCATION.getStatus());
        purchaseWarehouseRecordE.setAppId("1");
        super.createPurchaseIn(purchaseOrderDTO, purchaseWarehouseRecordE);
        //累加实收数量(基本单位数量)
        List<WarehouseRecordDetail> warehouseRecordDetails=warehouseRecordRepository.queryDetailListByRecordCode(purchaseWarehouseRecordE.getRecordCode());
        for (WarehouseRecordDetail detail : warehouseRecordDetails) {
            boolean flag = warehouseRecordRepository.increaseActualQtyById(detail.getPlanQty(), detail.getId(), detail.getSkuCode(), purchaseWarehouseRecordE.getRecordCode());
            AlikAssert.isTrue(flag, ResCode.STOCK_ERROR_1002, " 累加实收数量错误：skuCode=" + detail.getSkuCode());
        }
        //更新已收货完成状态标识并添加出入库时间
        warehouseRecordRepository.updateReceiptCompletedAndInTime(purchaseWarehouseRecordE.getRecordCode());
        //构建并且生成批次
        this.buildRwBatchDTO(purchaseWarehouseRecordE,warehouseRecordDetails);
    }


    /**
     * 构建批次信息
     *
     * @param warehouseRecordE
     * @return
     */
    private void buildRwBatchDTO(AbstractWarehouseRecord warehouseRecordE,List<WarehouseRecordDetail> detailList) {
        List<RwBatchE> list = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(detailList)) {
            for (WarehouseRecordDetail detail : detailList) {
                RwBatchE rwBatchDTO = new RwBatchE();
                String batchCode= "";
                rwBatchDTO.setBatchCode(batchCode);
                rwBatchDTO.setLineNo(String.valueOf(detail.getId()));
                rwBatchDTO.setSkuCode(detail.getSkuCode());
                rwBatchDTO.setRecordCode(detail.getRecordCode());
                rwBatchDTO.setWmsRecordCode(detail.getRecordCode());
                rwBatchDTO.setSkuId(detail.getSkuId());
                rwBatchDTO.setBusinessType(warehouseRecordE.getBusinessType());
                rwBatchDTO.setProduceCode(batchCode);
                rwBatchDTO.setProductDate(null);
                rwBatchDTO.setSyncTransferStatus(WmsSyncTransferStatusVO.NEED_TRANSFER.getStatus());
                rwBatchDTO.setSyncPurchaseStatus(WarehouseRecordConstant.INIT_SYNC_PURCHASE);
                rwBatchDTO.setSyncStatus(-1);
                rwBatchDTO.setQualityStatus(-1);
                rwBatchDTO.setActualQty(detail.getPlanQty());
                rwBatchDTO.setSkuQty(detail.getPlanQty());
                rwBatchDTO.setUnitCode(detail.getUnitCode());
                rwBatchDTO.setBasicSkuQty(detail.getPlanQty());
                rwBatchDTO.setInventoryType(1);
                rwBatchDTO.setCallbackNum(0);
                rwBatchDTO.setAppId("1");
                rwBatchDTO.setRealWarehouseId(warehouseRecordE.getRealWarehouseId());
                Date startDate = DateUtil.offsiteDate(new Date() , Calendar.HOUR,-24);
                rwBatchDTO.setQualityTime(startDate);
                list.add(rwBatchDTO);
            }
        }
        //生成批次
        rwBatchRepository.batchInsert(list, warehouseRecordE.getRealWarehouseId());
    }

}
