package com.rome.stock.innerservice.domain.entity;

import com.rome.arch.core.domain.EntityFactory;
import com.rome.arch.core.exception.RomeException;
import com.rome.stock.common.constants.warehouse.WarehouseRecordConstant;
import com.rome.stock.common.enums.warehouse.WarehouseRecordTypeVO;
import com.rome.stock.innerservice.api.dto.warehouserecord.CancelRecordDTO;
import com.rome.stock.innerservice.api.dto.warehouserecord.RecordDetailDTO;
import com.rome.stock.innerservice.common.AlikAssert;
import com.rome.stock.innerservice.common.ResCode;
import com.rome.stock.innerservice.common.StringUtils;
import com.rome.stock.innerservice.constant.WarehouseRecordBatchStatusVO;
import com.rome.stock.innerservice.constant.WarehouseRecordBusinessTypeVO;
import com.rome.stock.innerservice.constant.WarehouseRecordStatusVO;
import com.rome.stock.innerservice.constant.WmsSyncStatusVO;
import com.rome.stock.innerservice.domain.entity.warehouserecord.AbstractWarehouseRecord;
import com.rome.stock.innerservice.domain.entity.warehouserecord.WarehouseRecordDetail;
import com.rome.stock.innerservice.domain.entity.warehouserecord.WarehouseRecordE;
import com.rome.stock.core.domain.repository.CoreRealWarehouseStockRepository;
import com.rome.stock.innerservice.domain.repository.DisparityWarehouseRecordRepository;
import com.rome.stock.innerservice.domain.repository.RealWarehouseRepository;
import com.rome.stock.innerservice.domain.repository.warehouserecord.WarehouseRecordRepository;
import com.rome.stock.core.infrastructure.dataobject.core.CoreRealStockOpDO;
import com.rome.stock.core.infrastructure.dataobject.core.CoreRealStockOpDetailDO;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * @Doc:差异管理的出入库单领域模型
 * @Author: lchy
 * @Date: 2019/8/19
 * @Version 1.0
 */
@Data
@Slf4j
@Component
@Scope("prototype")
@EqualsAndHashCode(callSuper = false)
public class DisparityWarehouseRecordE extends AbstractWarehouseRecord{
    @Resource
    private EntityFactory entityFactory;
    @Resource
    private DisparityWarehouseRecordRepository disparityWarehouseRecordRepository;
    @Resource
    private RealWarehouseRepository realWarehouseRepository;
    @Resource
    private WarehouseRecordRepository warehouseRecordRepository;
    @Resource
    private CoreRealWarehouseStockRepository coreRealWarehouseStockRepository;


    /**
     * 添加出入库单
     */
    public void addRecord(){
        long id= disparityWarehouseRecordRepository.saveWarehouseRecord(this);
        this.setId(id);
        this.getWarehouseRecordDetails().forEach(record -> record.setWarehouseRecordDetail(this));
        disparityWarehouseRecordRepository.saveWarehouseRecordDetails(this.getWarehouseRecordDetails());
    }


    public void createWarehouseRecord(String code , WarehouseRecordTypeVO typeVO , Long rwId , List<RecordDetailDTO> details) {
        WarehouseRecordE recordE = warehouseRecordRepository.queryWarehouseRecordByRecordCode(code);
        AlikAssert.isNull(recordE, ResCode.STOCK_ERROR_1002, "该单号已存在" + code);
        this.setRecordCode(code);
        this.setRealWarehouseId(rwId);
        this.setBusinessType(typeVO.getBusinessType());
        this.setRecordType(typeVO.getType());
        this.setOutCreateTime(new Date());
        //差异管理的出入单单据都是一步即到已出库或已入库状态
        if (WarehouseRecordBusinessTypeVO.OUT_WAREHOUSE_RECORD.getType().equals(typeVO.getBusinessType())) {
            //出库单
            this.setRecordStatus(WarehouseRecordStatusVO.OUT_ALLOCATION.getStatus());
        } else {
            //入库单
            this.setRecordStatus(WarehouseRecordStatusVO.IN_ALLOCATION.getStatus());
        }
        this.setWarehouseRecordDetails(new ArrayList<>(details.size()));
        //默认无需同步wms
        this.setSyncWmsStatus(WmsSyncStatusVO.NO_REQUIRED.getStatus());
        //默认无需派车
        this.setSyncDispatchStatus(WarehouseRecordConstant.INIT_DISPATCH);
        //默认无需过账
        this.setSyncTransferStatus(WarehouseRecordConstant.INIT_TRANSFER);
        //默认无需同步交易中心，需要同步交易中心的 调用方自己处理
        this.setSyncTradeStatus(WarehouseRecordConstant.INIT_SYNC_TRADE);
        //默认无需处理批次库存 需要出库批次库存的 调用方自己处理
        this.setBatchStatus(WarehouseRecordBatchStatusVO.NO_HANDLING.getStatus());
        for (RecordDetailDTO detailE : details) {
            if (StringUtils.isBlank(detailE.getSapPoNo())) {
                throw new RomeException(ResCode.STOCK_ERROR_1002, "明细上的sapPoNo号不能为空");
            }
            if (StringUtils.isBlank(detailE.getLineNo())) {
                throw new RomeException(ResCode.STOCK_ERROR_1002, "明细上的lineNo号不能为空");
            }
            WarehouseRecordDetail warehouseRecordDetail = entityFactory.createEntity(WarehouseRecordDetail.class);
            warehouseRecordDetail.setLineNo(detailE.getLineNo());
            warehouseRecordDetail.setDeliveryLineNo(detailE.getDeliveryLineNo());
            warehouseRecordDetail.setSapPoNo(detailE.getSapPoNo());
            warehouseRecordDetail.setSkuCode(detailE.getSkuCode());
            warehouseRecordDetail.setPlanQty(detailE.getBasicSkuQty());
            warehouseRecordDetail.setUnit(detailE.getBasicUnit());
            warehouseRecordDetail.setUnitCode(detailE.getBasicUnitCode());
            warehouseRecordDetail.setActualQty(detailE.getBasicSkuQty());
            this.getWarehouseRecordDetails().add(warehouseRecordDetail);
        }
        this.initWarehouseRecodeDetail();
    }


    /**
     * 增加实体仓库库存的对象
     */
    public CoreRealStockOpDO initStockObj(AbstractWarehouseRecord recordE) {
        List<CoreRealStockOpDetailDO> increaseDetails = new ArrayList<>();
        for (WarehouseRecordDetail detail : recordE.getWarehouseRecordDetails()) {
            CoreRealStockOpDetailDO coreRealStockOpDetailDO = new CoreRealStockOpDetailDO();
            coreRealStockOpDetailDO.setSkuId(detail.getSkuId());
            coreRealStockOpDetailDO.setSkuCode(detail.getSkuCode());
            //差异过账允许扣为负库存
            coreRealStockOpDetailDO.setCheckBeforeOp(false);
            coreRealStockOpDetailDO.setRealQty(detail.getActualQty());
            coreRealStockOpDetailDO.setRealWarehouseId(recordE.getRealWarehouseId());
            increaseDetails.add(coreRealStockOpDetailDO);
        }
        CoreRealStockOpDO coreRealStockOpDO = new CoreRealStockOpDO();
        coreRealStockOpDO.setRecordCode(recordE.getRecordCode());
        coreRealStockOpDO.setTransType(recordE.getRecordType());
        coreRealStockOpDO.setDetailDos(increaseDetails);
        return coreRealStockOpDO;
    }

    /**
     * 取消出入库单
     * @param cancelRecordDTO
     */
    public void cancelWarehouseRecord(CancelRecordDTO cancelRecordDTO , List<CoreRealStockOpDO> rollBackList){
        WarehouseRecordE recordE = warehouseRecordRepository.queryWarehouseRecordByRecordCode(cancelRecordDTO.getRecordCode());
        if(recordE == null){
            //单据不存在无需取消，直接返回成功
            return;
        }
        warehouseRecordRepository.updateToCanceled(recordE.getId());
        if(WarehouseRecordTypeVO.DISPARITY_SHOP_IN_RECOED11.getType().equals(recordE.getRecordType())
                || WarehouseRecordTypeVO.DISPARITY_TRANSFER_WAREHOUSE_IN_RECOED12.getType().equals(recordE.getRecordType())
                || WarehouseRecordTypeVO.DISPARITY_WAREHOUSE_IN_RECOED22.getType().equals(recordE.getRecordType())
                || WarehouseRecordTypeVO.DISPARITY_SHOP_IN_RECOED21.getType().equals(recordE.getRecordType())){

             //正向门店责任、正向仓库责任、逆向门店责任、逆向仓库责任 对应的入库单，需要扣回库存
            CoreRealStockOpDO coreRealStockOpDO = null;
            try {
                recordE.setWarehouseRecordDetails(warehouseRecordRepository.queryDetailListByRecordId(recordE.getId()));
                coreRealStockOpDO = initStockObj(recordE);
                coreRealWarehouseStockRepository.decreaseRealQty(coreRealStockOpDO);
                if (recordE.getRecordType().equals(WarehouseRecordTypeVO.DISPARITY_SHOP_IN_RECOED11.getType())
                        || recordE.getRecordType().equals(WarehouseRecordTypeVO.DISPARITY_SHOP_IN_RECOED21.getType())) {
                    //门店入库时、需要处理批次库存
                    //将批次库存状态改成初始化 表示需求扣减批次库存【定时器处理】
                    warehouseRecordRepository.updateRecordBatchStatusToInitFromComplete(recordE.getRecordCode());
                }
            } catch (RomeException e) {
                throw new RomeException(e.getCode(), e.getMessage() );
            } catch (Exception e) {
                log.error(e.getMessage(),e);
                throw new RomeException(ResCode.STOCK_ERROR_1003, ResCode.STOCK_ERROR_1003_DESC );
            } finally {
                if (coreRealStockOpDO != null) {
                    rollBackList.add(coreRealStockOpDO);
                }
            }
        }
    }

}
