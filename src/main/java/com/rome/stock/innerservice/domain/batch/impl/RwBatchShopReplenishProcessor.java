package com.rome.stock.innerservice.domain.batch.impl;

import com.google.common.collect.Lists;
import com.rome.stock.common.enums.warehouse.WarehouseRecordTypeVO;
import com.rome.stock.innerservice.domain.batch.RwBatchProcessRecordType;
import com.rome.stock.innerservice.domain.batch.RwBatchStockRelationProcessor;
import com.rome.stock.innerservice.domain.batch.dto.RwRelationQueryDTO;
import com.rome.stock.innerservice.remote.orderCneter.facade.OrderCenterFacade;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * @Description 补货及差异
 * <AUTHOR>
@Service
@RwBatchProcessRecordType(recordVOTypes={
        WarehouseRecordTypeVO.DS_REPLENISH_IN_SHOP_RECORD,//直营门店补货入库单
        WarehouseRecordTypeVO.LS_REPLENISH_IN_SHOP_RECORD,//加盟门店补货入库单
        WarehouseRecordTypeVO.AB_PRESALE_DISTRIBUTION_IN_RECORD,//加盟预售配送入库单
        WarehouseRecordTypeVO.DIRECT_SALE_NEWPRODUCT_IN_RECORD,//直营预售配送入库单
        WarehouseRecordTypeVO.SHOP_COLD_CHAIN_IN_RECORD,//门店冷链入库单
        WarehouseRecordTypeVO.LS_COLD_OVER_STOCK_IN_RECORD,//加盟门店冷链入库单

        WarehouseRecordTypeVO.DS_REPLENISH_IN_SHOP_PLAN_RECORD,//直营门店补货计划入库单
        WarehouseRecordTypeVO.LS_REPLENISH_IN_SHOP_PLAN_RECORD,//加盟门店补货计划入库单

        WarehouseRecordTypeVO.DS_SHOP_COLD_IN_RECORD,//直营普通冷链入库单
        WarehouseRecordTypeVO.SHOP_NEWSTORE_PRECROSS_IN_RECORD,//直营新店预售越库入库单


        WarehouseRecordTypeVO.DISPARITY_SHOP_IN_RECOED11,//差异管理-增加门店库存[补货.门店责任]
        WarehouseRecordTypeVO.DISPARITY_TRANSFER_WAREHOUSE_IN_RECOED12,//差异管理-增加退货中转仓库存[补货.仓库责任]
        WarehouseRecordTypeVO.DISPARITY_TRANSFER_WAREHOUSE_IN_RECOED13,//差异管理-增加退货中转仓库存[补货.物流责任]
        WarehouseRecordTypeVO.DISPARITY_TRANSFER_WAREHOUSE_OUT_RECOED13,//差异管理-减少退货中转仓库存[补货.物流责任]
})
public class RwBatchShopReplenishProcessor implements RwBatchStockRelationProcessor {

    @Resource
    private OrderCenterFacade orderCenterFacade;

    @Override
    public Map<String, List<String>> getRelationRecordList(RwRelationQueryDTO rwRelationQueryDTO) {
        List<String> recordCodeList = rwRelationQueryDTO.getRecordCodeList();
        Map<String, String> outWhRecordMap = orderCenterFacade.queryOutWhRecordByInWhRecords(recordCodeList);
        Map<String, List<String>> resultMap = new HashMap<>();
        for(Map.Entry<String, String> entry :outWhRecordMap.entrySet()){
            if(resultMap.containsKey(entry.getKey())){
                resultMap.get(entry.getKey()).add(entry.getValue());
            }else{
                resultMap.put(entry.getKey(), Lists.newArrayList(entry.getValue()));
            }
        }
        return resultMap;
    }
}
