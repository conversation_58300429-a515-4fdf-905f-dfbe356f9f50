package com.rome.stock.innerservice.domain.service.impl;

import com.rome.arch.core.domain.EntityFactory;
import com.rome.arch.core.exception.RomeException;
import com.rome.stock.common.constants.warehouse.WarehouseRecordConstant;
import com.rome.stock.common.enums.warehouse.WarehouseRecordTypeVO;
import com.rome.stock.innerservice.api.dto.WarehouseRecordDTO;
import com.rome.stock.innerservice.api.dto.warehouserecord.InWarehouseRecordDTO;
import com.rome.stock.innerservice.api.dto.warehouserecord.OutWarehouseRecordDTO;
import com.rome.stock.innerservice.common.AlikAssert;
import com.rome.stock.innerservice.common.ResCode;
import com.rome.stock.innerservice.common.RomeCollectionUtil;

import com.rome.stock.innerservice.domain.entity.RealWarehouseE;
import com.rome.stock.innerservice.domain.entity.RwBatchE;
import com.rome.stock.innerservice.domain.entity.warehouserecord.PackageProcessWarehouseRecordE;
import com.rome.stock.innerservice.domain.entity.warehouserecord.WarehouseRecordDetail;
import com.rome.stock.innerservice.domain.entity.warehouserecord.WarehouseRecordE;
import com.rome.stock.core.domain.repository.CoreRealWarehouseStockRepository;
import com.rome.stock.innerservice.domain.repository.RealWarehouseRepository;
import com.rome.stock.innerservice.domain.repository.RwBatchRepository;
import com.rome.stock.innerservice.domain.repository.warehouserecord.WarehouseRecordRepository;
import com.rome.stock.innerservice.domain.service.PackageProcessService;
import com.rome.stock.innerservice.domain.service.WarehouseRecordService;
import com.rome.stock.core.facade.RedisRollBackFacade;
import com.rome.stock.core.infrastructure.dataobject.core.CoreRealStockOpDO;
import com.rome.stock.core.infrastructure.dataobject.core.CoreRealStockOpDetailDO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.Objects;

/**
 * 类WhAllocationServiceImpl的实现描述：仓库调拨
 *
 * <AUTHOR> 2019/5/29
 */
@Service
@Slf4j
public class PackageProcessServiceImpl implements PackageProcessService {

    @Autowired
    private WarehouseRecordRepository warehouseRecordRepository;
    @Autowired
    private RealWarehouseRepository realWarehouseRepository;
    @Resource
    private EntityFactory entityFactory;
    @Resource
    private CoreRealWarehouseStockRepository coreRealWarehouseStockRepository;
    @Resource
    private RwBatchRepository rwBatchRepository;
    @Resource
    private WarehouseRecordService warehouseRecordService;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void packageProcessOutRecord(OutWarehouseRecordDTO outWarehouseRecordDTO) {
        //幂等性判断
        WarehouseRecordE recordE = warehouseRecordRepository.queryWarehouseRecordByRecordCode(outWarehouseRecordDTO.getRecordCode());
        if(Objects.nonNull(recordE)){
            return;
        }
        //查询出库仓库
        RealWarehouseE realWarehouseE = realWarehouseRepository.getRealWarehouseByOutCodeAndFactoryCode(outWarehouseRecordDTO.getWarehouseCode(), outWarehouseRecordDTO.getFactoryCode());
        AlikAssert.isNotNull(realWarehouseE,ResCode.STOCK_ERROR_1002_DESC,"实仓不存在");
//        AlikAssert.isTrue(Objects.equals(realWarehouseE.getRealWarehouseType(), RealWarehouseTypeVO.RW_TYPE_2.getType()),ResCode.STOCK_ERROR_1002_DESC,"该仓库不是包装仓");
        //生成调拨出库单
        PackageProcessWarehouseRecordE outWarehouseRecord=entityFactory.createEntity(PackageProcessWarehouseRecordE.class);
        outWarehouseRecord.createOutRecordByFrontRecord(outWarehouseRecordDTO,realWarehouseE);
        outWarehouseRecord.addOutWarehouseRecord();
        boolean isSuccess=false;
        CoreRealStockOpDO decreaseRealStockOpDO = null;
        try{
            //扣减库存
            decreaseRealStockOpDO=outWarehouseRecord.initDecreaseStockObj(outWarehouseRecord.getRealWarehouseId(),true);
            coreRealWarehouseStockRepository.decreaseRealQty(decreaseRealStockOpDO);
            isSuccess = true;
        }catch (RomeException e){
            throw new RomeException(e.getCode(),e.getMessage());
        }finally {
            //模型成功，业务等处理失败，需要回滚
            if(isSuccess == false) {
                if(decreaseRealStockOpDO != null) {
                    RedisRollBackFacade.redisRollBack(decreaseRealStockOpDO);
                }
            }
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void packageProcessInRecord(InWarehouseRecordDTO inWarehouseRecordDTO) {
        //幂等性判断
        WarehouseRecordE recordE = warehouseRecordRepository.queryWarehouseRecordByRecordCode(inWarehouseRecordDTO.getRecordCode());
        if(Objects.nonNull(recordE)){
            return;
        }
        //查询包装入库仓
        RealWarehouseE realWarehouseE = realWarehouseRepository.getRealWarehouseByOutCodeAndFactoryCode(inWarehouseRecordDTO.getWarehouseCode(), inWarehouseRecordDTO.getFactoryCode());
        AlikAssert.isNotNull(realWarehouseE,ResCode.STOCK_ERROR_1002_DESC,"实仓不存在");
        //生成入库单
        PackageProcessWarehouseRecordE inRecordE = entityFactory.createEntity(PackageProcessWarehouseRecordE.class);
        inRecordE.createInRecordByOutRecord(inWarehouseRecordDTO,realWarehouseE);
        boolean isSuccess = false;
        CoreRealStockOpDO coreRecordRealStockIncreaseDO = null;
        try {
            //增加库存
            coreRecordRealStockIncreaseDO=inRecordE.initIncreaseStockObj(inRecordE.getRealWarehouseId());
            coreRealWarehouseStockRepository.increaseRealQty(coreRecordRealStockIncreaseDO);
//            WarehouseBatchStockE warehouseBatchStockE = entityFactory.createEntity(WarehouseBatchStockE.class);
            //增加批次库存
//            warehouseBatchStockE.increaseBatchStock(inRecordE);
            isSuccess = true;
        } catch (RomeException e) {
            log.error(e.getMessage(), e);
            throw new RomeException(e.getCode(), e.getMessage());
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            throw new RomeException(ResCode.STOCK_ERROR_1003, ResCode.STOCK_ERROR_1003_DESC);
        } finally {
            if (!isSuccess) {
                RedisRollBackFacade.redisRollBack(coreRecordRealStockIncreaseDO);
            }
        }
    }
}
