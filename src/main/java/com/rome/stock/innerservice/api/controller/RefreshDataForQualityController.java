package com.rome.stock.innerservice.api.controller;

import com.alibaba.fastjson.JSON;
import com.rome.arch.core.clientobject.Response;
import com.rome.arch.core.exception.RomeException;
import com.rome.arch.util.controller.RomeController;
import com.rome.stock.innerservice.api.dto.*;
import com.rome.stock.innerservice.api.dto.frontrecord.RwStockChangeFlowResultDTO;
import com.rome.stock.innerservice.api.dto.template.CrossSkuStockImportTemplate;
import com.rome.stock.innerservice.common.ResCode;
import com.rome.stock.innerservice.constant.ResponseMsg;
import com.rome.stock.innerservice.domain.entity.RealWarehouseE;
import com.rome.stock.innerservice.domain.repository.RealWarehouseRepository;
import com.rome.stock.innerservice.domain.repository.SapInterfaceLogRepository;
import com.rome.stock.innerservice.domain.service.RefreshDataForQualityService;
import com.rome.stock.innerservice.remote.item.dto.SkuInfoExtDTO;
import com.rome.stock.innerservice.remote.item.facade.SkuFacade;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import io.swagger.annotations.ApiResponse;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.util.StringUtils;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * @Doc:.....
 * @Author: lchy
 * @Date: 2019/8/22
 * @Version 1.0
 */
@Slf4j
@RomeController
@RequestMapping(value = "/stock/refresh")
@Api(tags = {"刷质检数据"})
public class RefreshDataForQualityController {
    @Resource
    private RefreshDataForQualityService refreshDataForQualityService;
    @Autowired
    private SkuFacade skuFacade;
    @Autowired
    private RealWarehouseRepository realWarehouseRepository;
    @Resource
    private SapInterfaceLogRepository sapInterfaceLogRepository;

    @ApiOperation(value = "刷质检数据", nickname = "refreshDataForQuality", produces = MediaType.APPLICATION_JSON_VALUE)
    @ApiResponse(code = 200, message = "success", response = RwStockChangeFlowResultDTO.class)
    @PostMapping(value = "queryRwStockChangeFlow")
    public Response<List<RefreshDataForQualityDTO>> refreshDataForQuality(@RequestBody RefreshDataForQualityParam paramDTO) {
        try {
            List<RefreshDataForQualityDTO> res = refreshDataForQualityService.execute(paramDTO);
            return ResponseMsg.SUCCESS.buildMsg(res);
        } catch (RomeException e) {
            log.error(e.getMessage(), e);
            return Response.builderFail(e.getCode(), e.getMessage());
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return Response.builderFail(ResCode.STOCK_ERROR_1003, ResCode.STOCK_ERROR_1003_DESC);
        }
    }

    @ApiOperation(value = "刷质检数据-简化版", nickname = "queryRwStockChangeFlowSimple", produces = MediaType.APPLICATION_JSON_VALUE)
    @ApiResponse(code = 200, message = "success", response = RwStockChangeFlowResultDTO.class)
    @PostMapping(value = "queryRwStockChangeFlowSimple")
    public Response<List<RefreshDataForQualityDTO>> queryRwStockChangeFlowSimple(@RequestBody RefreshDataForQualityParam paramDTO) {
        String message = "";
        boolean isSucc = false;
        String json = JSON.toJSONString(paramDTO);
        try {
            List<String> skuCodes=paramDTO.getDetails().stream().map(RefreshDataForQualityDTO::getSkuCode).collect(Collectors.toList());
            List<SkuInfoExtDTO> skuInfoExtDTOList=skuFacade.skusBySkuCode(skuCodes);
            Map<String,SkuInfoExtDTO> skuInfoExtDTOSMap=skuInfoExtDTOList.stream().collect(Collectors.toMap(SkuInfoExtDTO::getSkuCode, Function.identity(), (v1, v2) -> v1));
            for(RefreshDataForQualityDTO refreshDataForQualityDTO :paramDTO.getDetails()){
                if(!skuInfoExtDTOSMap.containsKey(refreshDataForQualityDTO.getSkuCode())){
                    throw new RomeException(ResCode.STOCK_ERROR_1003, "商品编号不存在，skuCode:"+refreshDataForQualityDTO.getSkuCode());
                }
                SkuInfoExtDTO skuInfoExtDTO=skuInfoExtDTOSMap.get(refreshDataForQualityDTO.getSkuCode());
                refreshDataForQualityDTO.setSkuId(skuInfoExtDTO.getId());
            }
            if(StringUtils.isEmpty(paramDTO.getRealWarehouseCode())){
                throw new RomeException(ResCode.STOCK_ERROR_1003, "仓库编号不能为空");
            }
            RealWarehouseE realWarehouseE=realWarehouseRepository.queryRealWarehouseByInCode(paramDTO.getRealWarehouseCode());
            if(null == realWarehouseE){
                throw new RomeException(ResCode.STOCK_ERROR_1003, "仓库编号对应仓不存在");
            }
            paramDTO.setRwId(realWarehouseE.getId());
            List<RefreshDataForQualityDTO> res = refreshDataForQualityService.execute(paramDTO);
            isSucc = true;
            message = "200";
            return ResponseMsg.SUCCESS.buildMsg(res);
        } catch (RomeException e) {
            message = e.getMessage();
            log.error(e.getMessage(), e);
            return Response.builderFail(e.getCode(), e.getMessage());
        } catch (Exception e) {
            message = e.getMessage();
            log.error(e.getMessage(), e);
            return Response.builderFail(ResCode.STOCK_ERROR_1003, ResCode.STOCK_ERROR_1003_DESC);
        }finally {
            sapInterfaceLogRepository.saveCallBackInterFaceLog(2, paramDTO.getRecordCode(), "queryRwStockChangeFlowSimple",
                    json, message, isSucc);
        }
    }

    @RequestMapping(value = "/importFileData", method = RequestMethod.POST)
    @ApiOperation(value = "导入excel文件中的数据", nickname = "importFileData", produces = MediaType.MULTIPART_FORM_DATA_VALUE)
    public Response<List<String>> importFileData(@RequestBody List<CrossSkuStockImportTemplate> dataList, @RequestParam("userId") Long userId) {
        try {
            return ResponseMsg.SUCCESS.buildMsg(refreshDataForQualityService.importFileData(dataList,userId));
        } catch (RomeException e) {
            log.error(e.getMessage(), e);
            return ResponseMsg.FAIL.buildMsg(e.getCode(), e.getMessage());
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return ResponseMsg.EXCEPTION.buildMsg();
        }
    }

    @RequestMapping(value = "/confirmImportFileData", method = RequestMethod.POST)
    @ApiOperation(value = "确认导入excel文件中的数据", nickname = "importFileData", produces = MediaType.MULTIPART_FORM_DATA_VALUE)
    public Response confirmImportFileData(@RequestParam("key") String key) {
        try {
            refreshDataForQualityService.confirmImportFileData(key);
            return ResponseMsg.SUCCESS.buildMsg();
        } catch (RomeException e) {
            log.error(e.getMessage(), e);
            return ResponseMsg.FAIL.buildMsg(e.getCode(), e.getMessage());
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return ResponseMsg.EXCEPTION.buildMsg();
        }
    }



    @ApiOperation(value = "刷质检数据-后台", nickname = "refreshSkuQty", produces = MediaType.APPLICATION_JSON_VALUE)
    @ApiResponse(code = 200, message = "success", response = RefreshDataForQualityDTO.class)
    @PostMapping(value = "refreshSkuQty")
    public Response<List<RefreshDataForQualityDTO>> refreshSkuQty(@RequestBody RefreshSkuQtyDTO refreshSkuQtyDTO) {
        try {
            List<RefreshDataForQualityDTO> res = refreshDataForQualityService.refreshSkuQty(refreshSkuQtyDTO);
            return ResponseMsg.SUCCESS.buildMsg(res);
        } catch (RomeException e) {
            log.error(e.getMessage(), e);
            return Response.builderFail(e.getCode(), e.getMessage());
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return Response.builderFail(ResCode.STOCK_ERROR_1003, ResCode.STOCK_ERROR_1003_DESC);
        }
    }


    @ApiOperation(value = "调整库存对比-中台库存调整", produces = MediaType.APPLICATION_JSON_VALUE)
    @ApiResponse(code = 200, message = "success", response = String.class)
    @RequestMapping(value = "/adjustCheckStock", method = RequestMethod.POST)
    public Response adjustSkuStock(@ApiParam(name = "adjustCheckStockDTO", value = "调整库存dto")@Validated @RequestBody AdjustCheckStockDTO adjustCheckStockDTO) {
        try {
            refreshDataForQualityService.adjustCheckStock(adjustCheckStockDTO);
            return  ResponseMsg.SUCCESS.buildMsg();
        } catch (RomeException e) {
            log.error(e.getMessage(), e);
            return Response.builderFail(e.getCode(),e.getMessage());
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return Response.builderFail(ResCode.STOCK_ERROR_1003_DESC,ResCode.STOCK_ERROR_1003_DESC);
        }
    }


    @ApiOperation(value = "查询库存", nickname = "queryRwStockBySkuCodeAndRealWarehouseCode", produces = MediaType.APPLICATION_JSON_VALUE)
    @ApiResponse(code = 200, message = "success", response = RwStockChangeFlowResultDTO.class)
    @PostMapping(value = "queryRwStockBySkuCodeAndRealWarehouseCode")
    public Response<QueryStockDTO> queryRwStockBySkuCodeAndRealWarehouseCode(@RequestBody QuerySkuStockDTO querySkuStockDTO) {
        try {
            QueryStockDTO res = refreshDataForQualityService.queryRwStockBySkuCodeAndRealWarehouseCode(querySkuStockDTO);
            return ResponseMsg.SUCCESS.buildMsg(res);
        } catch (RomeException e) {
            log.error(e.getMessage(), e);
            return Response.builderFail(e.getCode(), e.getMessage());
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return Response.builderFail(com.rome.stock.common.constants.ResCode.STOCK_ERROR_1003, e.getMessage());
        }
    }
    
    @ApiOperation(value = "刷库存，支持虚仓，测试专用接口", nickname = "refreshSkuQtyByTest", produces = MediaType.APPLICATION_JSON_VALUE)
    @ApiResponse(code = 200, message = "success", response = RwStockChangeFlowResultDTO.class)
    @PostMapping(value = "/refreshSkuQtyByTestVirtual")
    public Response<String> refreshSkuQtyByTest(@RequestBody RefreshDataForQualityParam paramDTO) {
        String message = "";
        boolean isSucc = false;
        String json = JSON.toJSONString(paramDTO);
        try {
            refreshDataForQualityService.refreshSkuQtyByTest(paramDTO);
            isSucc = true;
            message = "200";
            return ResponseMsg.SUCCESS.buildMsg("");
        } catch (RomeException e) {
            message = e.getMessage();
            log.error(e.getMessage(), e);
            return Response.builderFail(e.getCode(), e.getMessage());
        } catch (Exception e) {
            message = e.getMessage();
            log.error(e.getMessage(), e);
            return Response.builderFail(ResCode.STOCK_ERROR_1003, ResCode.STOCK_ERROR_1003_DESC);
        } finally {
            sapInterfaceLogRepository.saveCallBackInterFaceLog(2, paramDTO.getRecordCode(), "refreshSkuQtyByTest",
                    json, message, isSucc);
        }
    }


}
