package com.rome.stock.innerservice.api.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 *
 */
@Data
@NoArgsConstructor
public class RealWarehouseStockResDTO {

    @ApiModelProperty(value = "最大效期 分子,要求批次有效期需传,否则为null")
    private Integer molecule;

    @ApiModelProperty(value = "最大效期 分母,要求批次有效期需传,否则为null")
    private Integer denominator;

    @ApiModelProperty(value = "最小效期 分子,要求批次有效期需传,否则为null")
    private Integer lowerMolecule;

    @ApiModelProperty(value = "最小效期 分母,要求批次有效期需传,否则为null")
    private Integer lowerDenominator;

    @ApiModelProperty(value = "冗余天数")
    private Integer transDay;

    @ApiModelProperty(value = "批次段对应的物料库存")
    private List<RealWarehouseStockDTO> realWarehouseStockList;

    private String uniKey() {
        return this.molecule +"_"+this.denominator+"_"+this.lowerMolecule+"_"+this.lowerDenominator+"_"+this.transDay;
    }
}
