package com.rome.stock.innerservice.api.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;
/**
 * <p>
 * Description: 根据采购预约单号查询外部系统单据编号入参
 * </p>
 *
 * <AUTHOR>
 * @date 2022/10/10
 **/
@ApiModel("根据采购预约单号查询外部系统单据编号入参DTO")
@Data
public class OutRecordCodeParamDTO {

    @ApiModelProperty("业务中台单据号")
    List<String> appointRecordCode;
}
