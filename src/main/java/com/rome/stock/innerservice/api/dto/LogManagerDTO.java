package com.rome.stock.innerservice.api.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;


@Data
@EqualsAndHashCode(callSuper = false)
public class LogManagerDTO{

	/**
	 * 唯一主键
	 */
	@ApiModelProperty(value = "主键")
	private Long id;
	
	/**
     * 出入库单据编号
     */
    @ApiModelProperty(value = "单据号")
    private String recordCode;

	/**
	 * 出入库单据编号
	 */
	@ApiModelProperty(value = "业务单据号")
	private String sapOrderCode;

	/**
	 * 出入库单据编号
	 */
	@ApiModelProperty(value = "出入库单据编号")
	private String outInOrderCode;

	/**
	 * 出入库单据编号
	 */
	@ApiModelProperty(value = "外部业务单号")
	private String outRecordCode;
	/**
	 * 请求服务名
	 */
	@ApiModelProperty(value = "请求服务名")
	private String requestName;

	/**
	 * 请求服务名
	 */
	@ApiModelProperty(value = "交互系统")
	private String requestSystem;

	/**
	 * 请求服务名
	 */
    @ApiModelProperty(value = "请求服务名")
	private String requestService;
    
	/**
	 * 请求url
	 */
    @ApiModelProperty(value = "请求url")
	private String requestUrl;
    
	/**
	 * 请求内容
	 */
    @ApiModelProperty(value = "请求内容")
	private String requestContent;
    
	/**
	 * 响应内容
	 */
    @ApiModelProperty(value = "响应内容")
	private String responseContent;
    
	/**
	 * 交互状态 0：失败  1:成功
	 */
    @ApiModelProperty(value = "交互状态 0：失败  1:成功")
	private Integer status;
    
    /**
     * 调用类型 1：调用  2:回调
	 */
	@ApiModelProperty(value = "调用类型 1.主动调用 2.被动调用")
	private Integer requestType;

	private Integer type;
	/**
	 * 交互系统 1：sap  2:innerTrade
	 */
	@ApiModelProperty(value = "交互系统 1：sap  2:innerTrade")
	private Integer interacteSystem;

	/**
	 * 仓库系统编码
	 */
	private Integer wmsCode;
	/**
	 * 创建时间
	 */
	@ApiModelProperty(value = "创建时间")
	private Date createTime;

	private String requestTable;

	private String tableName;

	private String tableField;

	private String tableFieldBeforeValue;

	private String tableFieldAfterValue;

	private Integer requestWay;

	private String wmsRecordCode;



}
