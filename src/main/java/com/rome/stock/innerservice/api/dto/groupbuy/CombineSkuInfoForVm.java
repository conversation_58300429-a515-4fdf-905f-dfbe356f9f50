package com.rome.stock.innerservice.api.dto.groupbuy;

import java.util.ArrayList;
import java.util.List;

import com.rome.stock.innerservice.api.dto.frontrecord.SplitSkuInfo;

import lombok.Data;

@Data
public class CombineSkuInfoForVm {

    private Long virtualWarehouseId;
    private Long skuId;
    private String skuCode;
    private String unitCode;

    private List<SplitSkuInfoForVm> splits = new ArrayList<>();
}
