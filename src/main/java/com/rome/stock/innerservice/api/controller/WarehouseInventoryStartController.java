package com.rome.stock.innerservice.api.controller;

import com.github.pagehelper.PageInfo;
import com.rome.arch.core.clientobject.Response;
import com.rome.arch.core.exception.RomeException;
import com.rome.arch.util.controller.RomeController;
import com.rome.stock.innerservice.api.dto.InventoryRealWarehouseDTO;
import com.rome.stock.innerservice.api.dto.WarehouseInventoryStartCreateDTO;
import com.rome.stock.innerservice.api.dto.WarehouseInventoryStartDTO;
import com.rome.stock.innerservice.api.dto.WarehouseInventoryStartDetailPageDTO;
import com.rome.stock.innerservice.api.dto.WarehouseInventoryStartDetailQueryDTO;
import com.rome.stock.innerservice.api.dto.WarehouseInventoryStartOperateDTO;
import com.rome.stock.innerservice.api.dto.WarehouseInventoryStartPageDTO;
import com.rome.stock.innerservice.api.dto.WarehouseInventoryStartQueryDTO;
import com.rome.stock.innerservice.api.dto.WarehouseInventoryTempPageDTO;
import com.rome.stock.innerservice.api.dto.WarehouseInventoryTempQueryDTO;
import com.rome.stock.innerservice.api.dto.frontrecord.WarehouseInventoryDTO;
import com.rome.stock.innerservice.common.RedisUtil;
import com.rome.stock.innerservice.common.ResCode;
import com.rome.stock.innerservice.constant.ParamValidator;
import com.rome.stock.innerservice.constant.ResponseMsg;
import com.rome.stock.innerservice.domain.service.WarehouseInventoryStartService;
import com.rome.stock.innerservice.remote.base.dto.StoreDTO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import io.swagger.annotations.ApiResponse;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;

import javax.annotation.Resource;
import java.util.List;

@Slf4j
@RomeController
@RequestMapping("/stock/v1/warehouse_inventory_start/front_record")
@Api(tags={"盘点单管理"})
public class WarehouseInventoryStartController {

    private ParamValidator validator = ParamValidator.INSTANCE;

    @Resource
    private RedisUtil redisUtil;
    @Autowired
    private WarehouseInventoryStartService warehouseInventoryStartService;

    @ApiOperation(value = "根据条件查询盘点单信息,分页", nickname = "queryForAdminByCondition", produces = MediaType.APPLICATION_JSON_VALUE)
    @ApiResponse(code = 200, message = "success", response = WarehouseInventoryStartPageDTO.class)
    @PostMapping("/queryForAdminByCondition")
    public Response<PageInfo<WarehouseInventoryStartPageDTO>> queryForAdminByCondition(@RequestBody WarehouseInventoryStartQueryDTO warehouseInventoryStartQueryDTO) {
        if(! validator.validParam(warehouseInventoryStartQueryDTO)) {
            return ResponseMsg.PARAM_ERROR.buildMsg();
        }
        try {
            PageInfo<WarehouseInventoryStartPageDTO> pageInfo = warehouseInventoryStartService.queryForAdminByCondition(warehouseInventoryStartQueryDTO);
            return ResponseMsg.SUCCESS.buildMsg(pageInfo);
        } catch (RomeException e) {
            log.error(e.getMessage(), e);
            return ResponseMsg.FAIL.buildMsg(e.getCode(), e.getMessage());
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return ResponseMsg.EXCEPTION.buildMsg();
        }
    }


    @ApiOperation(value = "查询盘点单库存快照列表数据", nickname = "queryDetailPage", produces = MediaType.APPLICATION_JSON_VALUE)
    @ApiResponse(code = 200, message = "success", response = WarehouseInventoryStartPageDTO.class)
    @PostMapping("/queryDetailPage")
    public Response<PageInfo<WarehouseInventoryStartDetailPageDTO>> queryDetailPage(@RequestBody WarehouseInventoryStartDetailQueryDTO warehouseInventoryStartDetailQueryDTO) {
        if(! validator.validParam(warehouseInventoryStartDetailQueryDTO)) {
            return ResponseMsg.PARAM_ERROR.buildMsg();
        }
        try {
            PageInfo<WarehouseInventoryStartDetailPageDTO> pageInfo = warehouseInventoryStartService.queryDetailPage(warehouseInventoryStartDetailQueryDTO);
            return ResponseMsg.SUCCESS.buildMsg(pageInfo);
        } catch (RomeException e) {
            log.error(e.getMessage(), e);
            return ResponseMsg.FAIL.buildMsg(e.getCode(), e.getMessage());
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return ResponseMsg.EXCEPTION.buildMsg();
        }
    }

    @ApiOperation(value = "查询盘点上传结果列表数据", nickname = "queryTempPage", produces = MediaType.APPLICATION_JSON_VALUE)
    @ApiResponse(code = 200, message = "success", response = WarehouseInventoryStartPageDTO.class)
    @PostMapping("/queryTempPage")
    public Response<PageInfo<WarehouseInventoryTempPageDTO>> queryTempPage(@RequestBody WarehouseInventoryTempQueryDTO WarehouseInventoryTempQueryDTO) {
        if(! validator.validParam(WarehouseInventoryTempQueryDTO)) {
            return ResponseMsg.PARAM_ERROR.buildMsg();
        }
        try {
            PageInfo<WarehouseInventoryTempPageDTO> pageInfo = warehouseInventoryStartService.queryTempPage(WarehouseInventoryTempQueryDTO);
            return ResponseMsg.SUCCESS.buildMsg(pageInfo);
        } catch (RomeException e) {
            log.error(e.getMessage(), e);
            return ResponseMsg.FAIL.buildMsg(e.getCode(), e.getMessage());
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return ResponseMsg.EXCEPTION.buildMsg();
        }
    }



    @ApiOperation(value = "启动盘点", nickname = "startWarehouseInventory", produces = MediaType.APPLICATION_JSON_VALUE)
    @ApiResponse(code = 200, message = "success")
    @PostMapping("/startWarehouseInventory")
    public Response<List<WarehouseInventoryStartDTO>> startWarehouseInventory(@RequestBody WarehouseInventoryStartCreateDTO warehouseInventoryStartCreateDTO) {
        if(! validator.validParam(warehouseInventoryStartCreateDTO)) {
            return ResponseMsg.PARAM_ERROR.buildMsg();
        }

        String key =  "startWarehouseInventory_";
        if (warehouseInventoryStartCreateDTO.getId() != null) {
            key += "id_" + warehouseInventoryStartCreateDTO.getId();
        } else {
            key += "rw_" + warehouseInventoryStartCreateDTO.getId();
        }
        boolean isLock = false;
        try {
            isLock = redisUtil.tryLock(key, "1", 120);
            if (isLock) {
                List<WarehouseInventoryStartDTO> result = warehouseInventoryStartService.startWarehouseInventory(warehouseInventoryStartCreateDTO);
                return ResponseMsg.SUCCESS.buildMsg(result);
            } else {
                return Response.builderFail(ResCode.STOCK_ERROR_1003,"正在开启的盘点单，请稍后再试。。。");
            }
        } catch (RomeException e) {
            log.error(e.getMessage(), e);
            return ResponseMsg.FAIL.buildMsg(e.getCode(), e.getMessage());
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return ResponseMsg.EXCEPTION.buildMsg();
        }finally {
            if (isLock) {
                redisUtil.unLock(key, "1");
            }
        }
    }

    @ApiOperation(value = "统计查询数据", nickname = "queryTempSum", produces = MediaType.APPLICATION_JSON_VALUE)
    @ApiResponse(code = 200, message = "success", response = WarehouseInventoryStartPageDTO.class)
    @GetMapping("/queryTempSum")
    public Response<WarehouseInventoryTempPageDTO> queryTempSum(@RequestParam("inventoryId") Long inventoryId) {
        try {
            WarehouseInventoryTempPageDTO pageInfo = warehouseInventoryStartService.queryTempSum(inventoryId);
            return ResponseMsg.SUCCESS.buildMsg(pageInfo);
        } catch (RomeException e) {
            log.error(e.getMessage(), e);
            return ResponseMsg.FAIL.buildMsg(e.getCode(), e.getMessage());
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return ResponseMsg.EXCEPTION.buildMsg();
        }
    }


    @ApiOperation(value = "校验差异", nickname = "checkDiff", produces = MediaType.APPLICATION_JSON_VALUE)
    @ApiResponse(code = 200, message = "success")
    @PostMapping("/checkDiff")
    public Response<Void> checkDiff(@RequestBody WarehouseInventoryStartCreateDTO warehouseInventoryStartCreateDTO) {
        if(! validator.validParam(warehouseInventoryStartCreateDTO)) {
            return ResponseMsg.PARAM_ERROR.buildMsg();
        }
        try {
            warehouseInventoryStartService.checkDiff(warehouseInventoryStartCreateDTO);
            return ResponseMsg.SUCCESS.buildMsg();
        } catch (RomeException e) {
            log.error(e.getMessage(), e);
            return ResponseMsg.FAIL.buildMsg(e.getCode(), e.getMessage());
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return ResponseMsg.EXCEPTION.buildMsg();
        }
    }


    //关闭
    @ApiOperation(value = "关闭盘点", nickname = "stopWarehouseInventory", produces = MediaType.APPLICATION_JSON_VALUE)
    @ApiResponse(code = 200, message = "success")
    @PostMapping("/stopWarehouseInventory")
    public Response<Void> stopWarehouseInventory(@RequestBody WarehouseInventoryStartOperateDTO warehouseInventoryStartOperateDTO) {
        if(! validator.validParam(warehouseInventoryStartOperateDTO)) {
            return ResponseMsg.PARAM_ERROR.buildMsg();
        }
        String key = "stopWarehouseInventory_" + warehouseInventoryStartOperateDTO.getId();
        boolean isLock = false;
        try {
            isLock = redisUtil.tryLock(key, "1", 120);
            if (isLock) {
                warehouseInventoryStartService.stopWarehouseInventory(warehouseInventoryStartOperateDTO);
                return ResponseMsg.SUCCESS.buildMsg();
            } else {
                return Response.builderFail(ResCode.STOCK_ERROR_1003,"盘点结果正在处理中，请稍后再试。。。");
            }
        } catch (RomeException e) {
            log.error(e.getMessage(), e);
            return ResponseMsg.FAIL.buildMsg(e.getCode(), e.getMessage());
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return ResponseMsg.EXCEPTION.buildMsg();
        } finally {
            if (isLock) {
                redisUtil.unLock(key, "1");
            }
        }
    }


    //取消
    @ApiOperation(value = "取消盘点", nickname = "cancelWarehouseInventory", produces = MediaType.APPLICATION_JSON_VALUE)
    @ApiResponse(code = 200, message = "success")
    @PostMapping("/cancelWarehouseInventory")
    public Response<Void> cancelWarehouseInventory(@RequestBody WarehouseInventoryStartOperateDTO warehouseInventoryStartOperateDTO) {
        if(! validator.validParam(warehouseInventoryStartOperateDTO)) {
            return ResponseMsg.PARAM_ERROR.buildMsg();
        }
        try {
            warehouseInventoryStartService.cancelWarehouseInventory(warehouseInventoryStartOperateDTO);
            return ResponseMsg.SUCCESS.buildMsg();
        } catch (RomeException e) {
            log.error(e.getMessage(), e);
            return ResponseMsg.FAIL.buildMsg(e.getCode(), e.getMessage());
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return ResponseMsg.EXCEPTION.buildMsg();
        }
    }

    //取消
    @ApiOperation(value = "查询可盘点工厂列表", nickname = "queryAccessFactoryList", produces = MediaType.APPLICATION_JSON_VALUE)
    @ApiResponse(code = 200, message = "success", response = StoreDTO.class)
    @PostMapping("/queryAccessFactoryList")
    public Response<List<StoreDTO>> queryAccessFactoryList() {
        try {
            List<StoreDTO> list = warehouseInventoryStartService.queryAccessFactoryList();
            return ResponseMsg.SUCCESS.buildMsg(list);
        } catch (RomeException e) {
            log.error(e.getMessage(), e);
            return ResponseMsg.FAIL.buildMsg(e.getCode(), e.getMessage());
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return ResponseMsg.EXCEPTION.buildMsg();
        }
    }

    //查询可开启盘点仓库列表
    @ApiOperation(value = "查询可开启盘点仓库列表", nickname = "queryAccessRealHouseByFactoryCode", produces = MediaType.APPLICATION_JSON_VALUE)
    @ApiResponse(code = 200, message = "success", response = InventoryRealWarehouseDTO.class)
    @PostMapping("/queryAccessRealHouseByFactoryCode")
    public Response<List<InventoryRealWarehouseDTO>> queryAccessRealHouseByFactoryCode(@RequestParam("factoryCode") String factoryCode) {
        try {
            List<InventoryRealWarehouseDTO> list = warehouseInventoryStartService.queryAccessRealHouseByFactoryCode(factoryCode);
            return ResponseMsg.SUCCESS.buildMsg(list);
        } catch (RomeException e) {
            log.error(e.getMessage(), e);
            return ResponseMsg.FAIL.buildMsg(e.getCode(), e.getMessage());
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return ResponseMsg.EXCEPTION.buildMsg();
        }
    }
    @ApiOperation(value = "获取盘点单测试结果", nickname = "getTestWarehouseInventoryRecord", produces = MediaType.APPLICATION_JSON_VALUE)
    @ApiResponse(code = 200, message = "success")
    @PostMapping("/getTestWarehouseInventoryRecord")
    public Response<WarehouseInventoryDTO> getTestWarehouseInventoryRecord(@RequestParam("id") Long inventoryId, @ApiParam(name = "size", value = "sku数量数量") @RequestParam("size") Integer size, @ApiParam(name = "businessType", value = "业务类型：1-抽盘，2-全盘") @RequestParam("businessType")Integer businessType) {
        try {
            WarehouseInventoryDTO warehouseInventoryDTO = warehouseInventoryStartService.getTestWarehouseInventoryRecord(inventoryId, size, businessType);
            return ResponseMsg.SUCCESS.buildMsg(warehouseInventoryDTO);
        } catch (RomeException e) {
            log.error(e.getMessage(), e);
            return ResponseMsg.FAIL.buildMsg(e.getCode(), e.getMessage());
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return ResponseMsg.EXCEPTION.buildMsg();
        }
    }
}
