package com.rome.stock.innerservice.api.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import java.util.Date;
import java.util.List;

@Data
@NoArgsConstructor
@EqualsAndHashCode
public class EnableOrDisableDTO {

    @ApiModelProperty(value = "是否删除：0.否，1.是")
    private Integer isDeleted;

    @ApiModelProperty(value = "主键ID集合")
    private List<Long> idList;

    @ApiModelProperty(value = "更新人")
    private Long modifier;
}
