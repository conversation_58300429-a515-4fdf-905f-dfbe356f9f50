package com.rome.stock.innerservice.api.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.Digits;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;

@Data
public class WarehouseRecordDetailExtDTO {

    @ApiModelProperty(value = "唯一主键")
    @NotNull()
    @Digits(integer = 20, fraction = 0)
    private Long id;

    @ApiModelProperty(value = "单据编码")
    @NotEmpty()
    @Size(max = 30)
    private String recordCode;

    @ApiModelProperty(value = "wms收货单编号")
    @Size(max = 100)
    private String wmsRecordCode;

    @ApiModelProperty(value = "物料编号")
    @NotEmpty()
    @Size(max = 50)
    private String skuCode;

    @ApiModelProperty(value = "物料编号")
    @NotEmpty()
    @Size(max = 100)
    private String batchCode;

    @ApiModelProperty(value = "收货原因")
    @NotEmpty()
    @Size(max = 255)
    private String reason;

    @ApiModelProperty(value = "扩展字段1")
    @Size(max = 255)
    private String ext1;

    @ApiModelProperty(value = "扩展字段2")
    @Size(max = 255)
    private String ext2;

    @ApiModelProperty(value = "扩展字段3")
    @Size(max = 255)
    private String ext3;

    @ApiModelProperty(value = "数据描述")
    private String desc;
}
