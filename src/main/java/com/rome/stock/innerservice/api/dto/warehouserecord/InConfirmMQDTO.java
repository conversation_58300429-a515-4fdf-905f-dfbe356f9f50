package com.rome.stock.innerservice.api.dto.warehouserecord;

import com.rome.stock.innerservice.api.dto.ReceiptRecordDTO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

/**
 * 类ShopConfirmMQDTO的实现描述
 *
 */
@Data
@EqualsAndHashCode
public class InConfirmMQDTO {

    @ApiModelProperty(value = "单据编码")
    private String recordCode;

    @ApiModelProperty(value = "wms收货单号")
    private String wmsRecordCode;

    @ApiModelProperty(value = "批次明细")
    private List<ShopReceiptRWBatchDTO> shopBatchList;

    @ApiModelProperty(value = "推送类型(1: 仓库入库 2. 门店入库单 )")
    private Integer pushType;

    @ApiModelProperty(value = "操作员编码（工号）")
    private String operatorCode;

    @ApiModelProperty(value = "操作员名称")
    private String operatorName;

    public ReceiptRecordDTO initReceiptRecordDTO(){
        ReceiptRecordDTO receiptRecordDTO=new ReceiptRecordDTO();
        receiptRecordDTO.setWarehouseRecordCode(this.recordCode);
        receiptRecordDTO.setShopBatchList(this.shopBatchList);
        receiptRecordDTO.setWmsRecordCode(this.wmsRecordCode);
        receiptRecordDTO.setOperatorCode(this.operatorCode);
        receiptRecordDTO.setOperatorName(this.operatorName);
        receiptRecordDTO.setOperatorPersistence(false);
        return receiptRecordDTO;
    }
}
