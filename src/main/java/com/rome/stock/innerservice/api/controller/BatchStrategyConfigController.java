package com.rome.stock.innerservice.api.controller;


import com.github.pagehelper.PageInfo;
import com.rome.arch.core.clientobject.Response;
import com.rome.arch.core.exception.RomeException;
import com.rome.arch.util.controller.RomeController;
import com.rome.stock.innerservice.api.dto.BatchStrategyConfigDTO;
import com.rome.stock.innerservice.common.ResCode;
import com.rome.stock.innerservice.constant.ParamValidator;
import com.rome.stock.innerservice.constant.ResponseMsg;
import com.rome.stock.innerservice.domain.service.BatchStrategyConfigService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import io.swagger.annotations.ApiResponse;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;

import javax.annotation.Resource;
import java.util.List;

@Slf4j
@RomeController
@RequestMapping("/stock/v1/batchStrategyConfig")
@Api(tags={"批次出库策略配置相关接口"})
public class BatchStrategyConfigController {

    @Resource
    private BatchStrategyConfigService batchStrategyConfigService;

    private ParamValidator validator = ParamValidator.INSTANCE;

    @ApiOperation(value = "根据查询条件查询所有批次出库策略配置信息", nickname = "getInfoByQueryCondition", produces = MediaType.APPLICATION_JSON_VALUE)
    @ApiResponse(code = 200, message = "success", response = List.class)
    @RequestMapping(value = "/getInfoByQueryCondition", method = RequestMethod.POST)
    public Response<PageInfo<BatchStrategyConfigDTO>> getInfoByQueryCondition(@RequestBody BatchStrategyConfigDTO dto) {
        try {
            PageInfo<BatchStrategyConfigDTO> pageList = batchStrategyConfigService.getInfoByQueryCondition(dto);
            return Response.builderSuccess(pageList);
        } catch (RomeException e) {
            log.error(e.getMessage(), e);
            return ResponseMsg.EXCEPTION.buildMsg(e.getCode(), e.getMessage());
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return ResponseMsg.EXCEPTION.buildMsg(ResCode.STOCK_ERROR_1003, ResCode.STOCK_ERROR_1003_DESC);
        }
    }

    @ApiOperation(value = "新增或修改", nickname = "saveOrUpdate", produces = MediaType.APPLICATION_JSON_VALUE)
    @ApiResponse(code = 200, message = "success")
    @RequestMapping(value = "/saveOrUpdate", method = RequestMethod.POST)
    public Response saveOrUpdate(@RequestBody BatchStrategyConfigDTO configDTO) {
        try {
            batchStrategyConfigService.saveOrUpdate(configDTO);
            return ResponseMsg.SUCCESS.buildMsg("");
        } catch (RomeException e) {
            log.error(e.getMessage(), e);
            return ResponseMsg.FAIL.buildMsg(e.getCode(),e.getMessage());
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return ResponseMsg.EXCEPTION.buildMsg(ResCode.STOCK_ERROR_1003_DESC,ResCode.STOCK_ERROR_1003_DESC + e.getMessage());
        }
    }

    @ApiOperation(value = "删除出库策略配置信息", produces = MediaType.APPLICATION_JSON_VALUE)
    @ApiResponse(code = 200, message = "success", response = BatchStrategyConfigDTO.class)
    @RequestMapping(value = "/deleteBatchStrategyConfig", method = RequestMethod.POST)
    public Response deleteBatchStrategyConfig(@RequestBody BatchStrategyConfigDTO batchStrategyConfigDTO) {
        if (!validator.validPositiveLong(batchStrategyConfigDTO.getId())) {
            return ResponseMsg.PARAM_ERROR.buildMsg();
        }
        try {
            batchStrategyConfigService.deleteBatchStrategyConfig(batchStrategyConfigDTO);
            return ResponseMsg.SUCCESS.buildMsg();
        } catch (RomeException e) {
            log.error(e.getMessage(), e);
            return ResponseMsg.FAIL.buildMsg(e.getCode(), e.getMessage());
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return ResponseMsg.EXCEPTION.buildMsg();
        }
    }

    @ApiOperation(value = "更新批次出库策略可用状态", produces = MediaType.APPLICATION_JSON_VALUE)
    @ApiResponse(code = 200, message = "success")
    @PostMapping(value = "/updateAvailable")
    public Response updateBatchStrategyAvailable(@RequestBody BatchStrategyConfigDTO configDTO){
        try{
            batchStrategyConfigService.updateBatchStrategyAvailable(configDTO);
            return ResponseMsg.SUCCESS.buildMsg();
        }catch (RomeException e){
            log.error(e.getMessage(), e);
            return ResponseMsg.FAIL.buildMsg(e.getCode(), e.getMessage());
        }catch (Exception e){
            log.error(e.getMessage(), e);
            return ResponseMsg.EXCEPTION.buildMsg();
        }
    }

    @ApiOperation(value = "根据出库仓Id列表，查询所有批次出库策略配置信息", nickname = "queryOutStrategyByRwIdList", produces = MediaType.APPLICATION_JSON_VALUE)
    @ApiResponse(code = 200, message = "success",response = List.class)
    @RequestMapping(value = "/queryOutStrategyByRwIdList", method = RequestMethod.POST)
    public Response<List<BatchStrategyConfigDTO>> queryOutStrategyByRwIdList(@ApiParam(name = "rwIdList", value = "出库实仓Id列表") @RequestBody List<Long> rwIdList) {
        try {
            List<BatchStrategyConfigDTO> list = batchStrategyConfigService.queryOutStrategyByRwIdList(rwIdList);
            return Response.builderSuccess(list);
        } catch (RomeException e) {
            log.error(e.getMessage(), e);
            return ResponseMsg.FAIL.buildMsg(e.getCode(),e.getMessage());
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return ResponseMsg.EXCEPTION.buildMsg(ResCode.STOCK_ERROR_1003_DESC,ResCode.STOCK_ERROR_1003_DESC + e.getMessage());
        }
    }
}
