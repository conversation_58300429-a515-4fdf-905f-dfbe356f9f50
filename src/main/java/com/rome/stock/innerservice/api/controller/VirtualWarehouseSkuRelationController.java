package com.rome.stock.innerservice.api.controller;

import com.github.pagehelper.PageInfo;
import com.rome.arch.core.clientobject.Response;
import com.rome.arch.core.exception.RomeException;
import com.rome.arch.util.controller.RomeController;
import com.rome.stock.innerservice.api.dto.QuerySkuRealVirtualStockSyncDTO;
import com.rome.stock.innerservice.api.dto.RealWarehouse;
import com.rome.stock.innerservice.api.dto.SkuRealVirtualStockSyncRelationDTO;
import com.rome.stock.innerservice.common.ResCode;
import com.rome.stock.innerservice.constant.ResponseMsg;
import com.rome.stock.innerservice.domain.service.VirtualWarehouseSkuRelationService;
import com.rome.stock.innerservice.remote.item.dto.SkuInfoExtDTO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiResponse;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.MediaType;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

/**
 * @Description 虚仓sku分配比例controller
 * <AUTHOR>
 * @Date 2019/5/18 16:38
 * @Version 1.0
 */
@Slf4j
@RomeController
@RequestMapping("/stock/v1/vm_sku_relation")
@Api(tags={"虚仓sku分配比例"})
public class VirtualWarehouseSkuRelationController {

    @Resource
    VirtualWarehouseSkuRelationService virtualWarehouseSkuRelationService;

    @ApiOperation(value = "创建sku虚仓分配比例", nickname = "addVirtualWarehouseSkuRelation", produces = MediaType.APPLICATION_JSON_VALUE)
    @ApiResponse(code = 200, message = "success")
    @RequestMapping(value = "/addVirtualWarehouseSkuRelation", method = RequestMethod.POST)
    public Response addVirtualWarehouseSkuRelation(@RequestBody List<SkuRealVirtualStockSyncRelationDTO> relationList){

        try{
            virtualWarehouseSkuRelationService.addVirtualWarehouseSkuRelation(relationList);
            return ResponseMsg.SUCCESS.buildMsg();
        } catch (RomeException e) {
            log.error(e.getMessage(), e);
            return ResponseMsg.EXCEPTION.buildMsg(e.getCode(), e.getMessage());
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return ResponseMsg.EXCEPTION.buildMsg(ResCode.STOCK_ERROR_1003_DESC,ResCode.STOCK_ERROR_1003_DESC);
        }
    }

    @ApiOperation(value = "修改sku虚仓分配比例", nickname = "updateVirtualWarehouseSkuRelation", produces = MediaType.APPLICATION_JSON_VALUE)
    @ApiResponse(code = 200, message = "success")
    @RequestMapping(value = "/updateVirtualWarehouseSkuRelation", method = RequestMethod.POST)
    public Response updateVirtualWarehouseSkuRelation(@RequestBody List<SkuRealVirtualStockSyncRelationDTO> relationList){

        try{
            virtualWarehouseSkuRelationService.updateVirtualWarehouseSkuRelation(relationList);
            return ResponseMsg.SUCCESS.buildMsg();
        } catch (RomeException e) {
            log.error(e.getMessage(), e);
            return ResponseMsg.EXCEPTION.buildMsg(e.getCode(), e.getMessage());
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return ResponseMsg.EXCEPTION.buildMsg("50000","系统异常");
        }
    }

    @ApiOperation(value = "根据条件查询分配关系", nickname = "querySkuRelationByCondition", produces = MediaType.APPLICATION_JSON_VALUE)
    @ApiResponse(code = 200, message = "success")
    @RequestMapping(value = "/querySkuRelationByCondition", method = RequestMethod.POST)
    public Response querySkuRelationByCondition(@RequestBody SkuRealVirtualStockSyncRelationDTO relation){

        try{
            PageInfo<SkuRealVirtualStockSyncRelationDTO> pagelist = virtualWarehouseSkuRelationService.querySkuRelationByCondition(relation);
            return ResponseMsg.SUCCESS.buildMsg(pagelist);
        } catch (RomeException e) {
            log.error(e.getMessage(), e);
            return ResponseMsg.EXCEPTION.buildMsg(e.getCode(), e.getMessage());
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return ResponseMsg.EXCEPTION.buildMsg(ResCode.STOCK_ERROR_1003_DESC,ResCode.STOCK_ERROR_1003_DESC);
        }
    }


    @ApiOperation(value = "删除sku虚仓分配比例", nickname = "deleteVirtualWarehouseSkuRelation", produces = MediaType.APPLICATION_JSON_VALUE)
    @ApiResponse(code = 200, message = "success")
    @RequestMapping(value = "/deleteVirtualWarehouseSkuRelation", method = RequestMethod.POST)
    public Response deleteVirtualWarehouseSkuRelation(@RequestBody SkuRealVirtualStockSyncRelationDTO relation){

        try{
            virtualWarehouseSkuRelationService.deleteVirtualWarehouseSkuRelation(relation);
            return ResponseMsg.SUCCESS.buildMsg();
        } catch (RomeException e) {
            log.error(e.getMessage(), e);
            return ResponseMsg.EXCEPTION.buildMsg(e.getCode(), e.getMessage());
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return ResponseMsg.EXCEPTION.buildMsg("50000","系统异常");
        }
    }

    @ApiOperation(value = "根据工厂查询仓库信息", nickname = "queryRealWarehouseByFactoryCode", produces = MediaType.APPLICATION_JSON_VALUE)
    @ApiResponse(code = 200, message = "success")
    @RequestMapping(value = "/queryRealWarehouseByFactoryCode", method = RequestMethod.GET)
    public Response queryRealWarehouseByFactoryCode(@RequestParam("factoryCode") String factoryCode){
        try {
            List<RealWarehouse> warehouses = virtualWarehouseSkuRelationService.queryRealWarehouseByFactoryCode(factoryCode);
            return ResponseMsg.SUCCESS.buildMsg(warehouses);
        } catch (RomeException e) {
            log.error(e.getMessage(), e);
            return ResponseMsg.FAIL.buildMsg(e.getCode(),e.getMessage());
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return ResponseMsg.FAIL.buildMsg("500000",e.getMessage());
        }
    }

    @ApiOperation(value = "获取所有虚实分配表中的所有sku", nickname = "queryRelationSkuInfo", produces = MediaType.APPLICATION_JSON_VALUE)
    @ApiResponse(code = 200, message = "success")
    @RequestMapping(value = "/queryRelationSkuInfo", method = RequestMethod.GET)
    public Response queryRelationSkuInfo(){
        try {
            List<SkuInfoExtDTO> skuInfos = virtualWarehouseSkuRelationService.queryRelationSkuInfo();
            return ResponseMsg.SUCCESS.buildMsg(skuInfos);
        } catch (RomeException e) {
            log.error(e.getMessage(), e);
            return ResponseMsg.FAIL.buildMsg(e.getCode(),e.getMessage());
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return ResponseMsg.FAIL.buildMsg("500000",e.getMessage());
        }
    }

    @ApiOperation(value = "查询库存同步比例 物料级别", nickname = "querySyncRateBySkuIdWId")
    @ApiResponse(code = 200, message = "success")
    @PostMapping(value = "/querySyncRateBySkuIdWId")
    public Response<List<SkuRealVirtualStockSyncRelationDTO>> querySyncRateBySkuIdWId(@Validated @RequestBody QuerySkuRealVirtualStockSyncDTO querySkuRealVirtualStockSyncDTO){
        try{
            List<SkuRealVirtualStockSyncRelationDTO> skuRealVirtualStockSyncRelationDTOS = virtualWarehouseSkuRelationService.querySyncRateBySkuIdWId(querySkuRealVirtualStockSyncDTO);
            return ResponseMsg.SUCCESS.buildMsg(skuRealVirtualStockSyncRelationDTOS);
        }catch (RomeException e){
            log.error(e.getMessage(), e);
            return ResponseMsg.FAIL.buildMsg(e.getCode(),e.getMessage());
        }catch (Exception e){
            log.error(e.getMessage(), e);
            return ResponseMsg.FAIL.buildMsg("500000",e.getMessage());
        }
    }

}
