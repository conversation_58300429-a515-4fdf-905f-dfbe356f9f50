package com.rome.stock.innerservice.api.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

@Data
@EqualsAndHashCode
public class WarehouseReceiveDTO {

    @ApiModelProperty(value = "单据编号")
    private String recordCode;

    @ApiModelProperty(value = "收货单号")
    private String receiveCode;
    @ApiModelProperty(value = "单据类型名称")
    private String recordTypeName;

    @ApiModelProperty(value = "工厂编号")
    private String factoryCode;

    @ApiModelProperty(value = "仓库编号")
    private String realWarehouseCode;

    @ApiModelProperty(value = "业务单号")
    private String sapRecordCode;

    @ApiModelProperty(value = "出入库类型")
    private Integer businessType;

    @ApiModelProperty(value = "单据类型")
    private Integer recordType;

}
