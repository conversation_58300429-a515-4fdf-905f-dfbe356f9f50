package com.rome.stock.innerservice.api.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.Valid;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.util.Date;
import java.util.List;

/**
 * 真实库存调整Dto
 */
@Data
@EqualsAndHashCode
public class AdjustCheckStockDTO {

    @ApiModelProperty(value = "外部系统编码", required = true)
    @NotBlank(message="外部系统编码不能为空")
    private String outRecordCode;

    @ApiModelProperty(value = "调整库存的时间")
    private Date outCreateTime;

    @ApiModelProperty(value = "调整库存的备注信息")
    private String remark;

    @ApiModelProperty(value = "实仓ID", required = true)
    @NotNull(message="实仓ID不能为空")
    private Long realWarehouseId;

    @ApiModelProperty(value = "调整明细", required = true)
    @NotNull(message="调整明细不能为空")
    @Valid
    private List<AdjustCheckDetailDTO> details;

    @ApiModelProperty(value = "操作人编号")
    private Long modifier;
}
