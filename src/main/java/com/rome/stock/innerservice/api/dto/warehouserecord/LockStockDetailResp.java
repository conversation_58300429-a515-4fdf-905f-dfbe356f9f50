package com.rome.stock.innerservice.api.dto.warehouserecord;

import java.io.Serializable;
import java.math.BigDecimal;

import lombok.Data;

/**
 * @Description: 锁库明细响应结果
 * <p>
 * @Author: wwh 2020/8/12
 */
@Data
public class LockStockDetailResp implements Serializable {
	
	/**
	 * 商品编码
	 */
	private String skuCode;
	
	/**
	 * 单位编码
	 */
	private String unitCode;
	
	/**
	 * 计划锁定数量
	 */
	private BigDecimal planQty;
	
	/**
	 * 锁定数量（部分锁库专用）
	 */
	private BigDecimal lockQty;
	
	/**
	 * 负库存锁定数量（负库存锁定专用）
	 */
	private BigDecimal outLockQty;
	
}