package com.rome.stock.innerservice.api.dto.xuantian.virtual;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * 
 * 外卖销售发货单据表
 * 
 * <AUTHOR>
 * @since 3.0.0_2019年6月30日
 */
@ApiModel("销售发货单DeliveryDTO")
@Data
public class VirtualDeliveryDTO implements Serializable {
	private static final long serialVersionUID = 6597787278805377554L;

	@ApiModelProperty("发货单信息")
	private VirtualDeliveryOrderDTO deliveryOrder;

	@ApiModelProperty("单据列表")
	private List<VirtualDeliveryItemDTO> orderLines;

	
}
