/**
 * Filename BigdataController.java
 * Company 上海来伊份科技有限公司。
 * <AUTHOR>
 * @version 
 */
package com.rome.stock.innerservice.api.controller;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import com.rome.arch.core.clientobject.Response;
import com.rome.arch.core.exception.RomeException;
import com.rome.arch.util.controller.RomeController;
import com.rome.stock.innerservice.common.ResCode;
import com.rome.stock.innerservice.domain.service.BigdataDeliveryOnroadReportService;
import com.rome.stock.innerservice.domain.service.KpRwRelationService;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiResponse;
import lombok.extern.slf4j.Slf4j;

import java.util.List;

/**
 * 大数据相关接口
 * <AUTHOR>
 * @since 2020-8-17 16:33:49
 */
@Slf4j
@RomeController
@RequestMapping("/stock/v1/bigdata")
@Api(tags={"大数据相关接口"})
public class BigdataController {

	@Autowired
    private BigdataDeliveryOnroadReportService bigdataDeliveryOnroadReportService;
	
	@Autowired
    private KpRwRelationService kpRwRelationService;
	
	@ApiOperation(value = "大数据收货在途库存数据汇总-同步方式", nickname = "deliveryOnroadSummaryAll", produces = MediaType.APPLICATION_JSON_VALUE)
    @ApiResponse(code = 200, message = "success")
    @RequestMapping(value = "/deliveryOnroadSummaryAll", method = RequestMethod.POST)
    public Response deliveryOnroadSummaryAll(){
        try {
        	bigdataDeliveryOnroadReportService.deliveryOnroadSummaryAll();
            return Response.builderSuccess("success");
        } catch (RomeException e) {
            log.error(e.getMessage(), e);
            return Response.builderFail(e.getCode(), e.getMessage());
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return Response.builderFail(ResCode.STOCK_ERROR_1003, ResCode.STOCK_ERROR_1003_DESC + ","+ e.getMessage());
        }
    }
	
	@ApiOperation(value = "大数据收货在途库存数据汇总-异步方式", nickname = "deliveryOnroadSummaryAllByAsyn", produces = MediaType.APPLICATION_JSON_VALUE)
    @ApiResponse(code = 200, message = "success")
    @RequestMapping(value = "/deliveryOnroadSummaryAllByAsyn", method = RequestMethod.POST)
    public Response deliveryOnroadSummaryAllByAsyn(){
        try {
        	bigdataDeliveryOnroadReportService.deliveryOnroadSummaryAllByAsyn();
            return Response.builderSuccess("success");
        } catch (RomeException e) {
            log.error(e.getMessage(), e);
            return Response.builderFail(e.getCode(), e.getMessage());
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return Response.builderFail(ResCode.STOCK_ERROR_1003, ResCode.STOCK_ERROR_1003_DESC + ","+ e.getMessage());
        }
    }
	
	@ApiOperation(value = "鲲鹏实仓库存变化推到大数据-同步方式", nickname = "pushChangeStockByBigdataMsg", produces = MediaType.APPLICATION_JSON_VALUE)
    @ApiResponse(code = 200, message = "success")
    @RequestMapping(value = "/pushChangeStockByBigdataMsg", method = RequestMethod.POST)
    public Response pushChangeStockByBigdataMsg(){
        try {
        	kpRwRelationService.pushChangeStockByBigdataMsg();
            return Response.builderSuccess("success");
        } catch (RomeException e) {
            log.error(e.getMessage(), e);
            return Response.builderFail(e.getCode(), e.getMessage());
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return Response.builderFail(ResCode.STOCK_ERROR_1003, ResCode.STOCK_ERROR_1003_DESC + ","+ e.getMessage());
        }
    }
	
	@ApiOperation(value = "鲲鹏实仓库存变化推到大数据-异步方式", nickname = "pushChangeStockByBigdataMsgByAsyn", produces = MediaType.APPLICATION_JSON_VALUE)
    @ApiResponse(code = 200, message = "success")
    @RequestMapping(value = "/pushChangeStockByBigdataMsgByAsyn", method = RequestMethod.POST)
    public Response pushChangeStockByBigdataMsgByAsyn(){
        try {
        	kpRwRelationService.pushChangeStockByBigdataMsgByAsyn();
            return Response.builderSuccess("success");
        } catch (RomeException e) {
            log.error(e.getMessage(), e);
            return Response.builderFail(e.getCode(), e.getMessage());
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return Response.builderFail(ResCode.STOCK_ERROR_1003, ResCode.STOCK_ERROR_1003_DESC + ","+ e.getMessage());
        }
    }



    @ApiOperation(value = "鲲鹏实仓库存变化推到外卖-异步方式", nickname = "pushChangeStockBySaleOutSync", produces = MediaType.APPLICATION_JSON_VALUE)
    @ApiResponse(code = 200, message = "success")
    @RequestMapping(value = "/pushChangeStockBySaleOutAsync", method = RequestMethod.POST)
    public Response pushChangeStockBySaleOutAsync(){
        try {
            kpRwRelationService.pushChangeStockBySaleOutAsync();
            return Response.builderSuccess("success");
        } catch (RomeException e) {
            log.error(e.getMessage(), e);
            return Response.builderFail(e.getCode(), e.getMessage());
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return Response.builderFail(ResCode.STOCK_ERROR_1003, ResCode.STOCK_ERROR_1003_DESC + ","+ e.getMessage());
        }
    }


    @ApiOperation(value = "鲲鹏实仓库存变化推到外卖-手动批量推送", nickname = "pushChangeStockBySaleOutSync", produces = MediaType.APPLICATION_JSON_VALUE)
    @ApiResponse(code = 200, message = "success")
    @RequestMapping(value = "/pushChangeStockBySaleOutByHand", method = RequestMethod.POST)
    public Response pushChangeStockBySaleOutByHand(@RequestBody List<String>  shopCodes){
        try {
            kpRwRelationService.pushChangeStockBySaleOutByHand(shopCodes);
            return Response.builderSuccess("success");
        } catch (RomeException e) {
            log.error(e.getMessage(), e);
            return Response.builderFail(e.getCode(), e.getMessage());
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return Response.builderFail(ResCode.STOCK_ERROR_1003, ResCode.STOCK_ERROR_1003_DESC + ","+ e.getMessage());
        }
    }
}
