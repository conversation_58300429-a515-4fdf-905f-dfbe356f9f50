package com.rome.stock.innerservice.api.controller;

import com.rome.stock.innerservice.api.dto.RealWarehouse;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.*;
import com.github.pagehelper.PageInfo;
import com.rome.arch.core.clientobject.Response;
import com.rome.arch.core.exception.RomeException;
import com.rome.arch.util.controller.RomeController;
import com.rome.stock.innerservice.api.dto.frontrecord.RwStockChangeFlowParamDTO;
import com.rome.stock.innerservice.api.dto.frontrecord.RwStockChangeFlowResultDTO;
import com.rome.stock.innerservice.common.ResCode;
import com.rome.stock.innerservice.constant.ParamValidator;
import com.rome.stock.innerservice.constant.ResponseMsg;
import com.rome.stock.innerservice.domain.service.RwStockChangeFlowService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiResponse;
import lombok.extern.slf4j.Slf4j;

import java.util.List;

/**
 * Create by daiweiqiang on 2019/5/11
 * 实仓出入库流水Controller
 */
@Slf4j
@RomeController
@RequestMapping(value = "/stock/rw_stock_change_flow")
@Api(tags={"实仓出入库流水"})
public class RwStockChangeFlowController {

    @Autowired
    private RwStockChangeFlowService rwStockChangeFlowService;

    private ParamValidator validator = ParamValidator.INSTANCE;


    /**
     * 查询实仓出入库流水记录
     * @param paramDTO
     * @return
     */
    @ApiOperation(value = "查询实仓出入库流水", nickname = "query_rw_stock_change_flow", produces = MediaType.APPLICATION_JSON_VALUE)
    @ApiResponse(code = 200, message = "success", response = RwStockChangeFlowResultDTO.class)
    @PostMapping(value = "queryRwStockChangeFlow")
    public Response queryRwStockChangeFlow(@RequestBody RwStockChangeFlowParamDTO paramDTO) {
        if (!validator.validNumber(paramDTO.getQueryType())) {
            return ResponseMsg.PARAM_ERROR.buildMsg();
        }
        try {
            PageInfo<RwStockChangeFlowResultDTO> pageInfo = rwStockChangeFlowService.queryRwStockChangeFlow(paramDTO);
            return ResponseMsg.SUCCESS.buildMsg(pageInfo);
        } catch (RomeException e) {
            log.error(e.getMessage(), e);
            return Response.builderFail(e.getCode(), e.getMessage());
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return Response.builderFail(ResCode.STOCK_ERROR_1003, ResCode.STOCK_ERROR_1003_DESC);
        }
    }
    
    /**
     * 查询实仓库存流水记录历史数据
     * @param paramDTO
     * @return
     */
    @ApiOperation(value = "查询实仓库存流水记录历史数据", nickname = "query_rw_stock_change_flow_history", produces = MediaType.APPLICATION_JSON_VALUE)
    @ApiResponse(code = 200, message = "success", response = RwStockChangeFlowResultDTO.class)
    @PostMapping(value = "queryRwStockChangeFlowHistory")
    public Response queryRwStockChangeFlowHistory(@RequestBody RwStockChangeFlowParamDTO paramDTO) {
        try {
            PageInfo<RwStockChangeFlowResultDTO> pageInfo = rwStockChangeFlowService.queryRwStockChangeFlowHistory(paramDTO);
            return ResponseMsg.SUCCESS.buildMsg(pageInfo);
        } catch (RomeException e) {
            log.error(e.getMessage(), e);
            return Response.builderFail(e.getCode(), e.getMessage());
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return Response.builderFail(ResCode.STOCK_ERROR_1003, ResCode.STOCK_ERROR_1003_DESC);
        }
    }

    @ApiOperation(value = "查询跨境电商实仓(含沪威酒工厂实仓)", nickname = "queryCrossRealWarehouse", produces = MediaType.APPLICATION_JSON_VALUE)
    @ApiResponse(code = 200, message = "success", response = RealWarehouse.class)
    @GetMapping("/queryCrossRealWarehouse")
    public Response<List<RealWarehouse>> queryCrossRealWarehouse() {
        try {
            List<RealWarehouse> realWarehouseList = rwStockChangeFlowService.queryCrossAndWineRealWarehouse();
            return ResponseMsg.SUCCESS.buildMsg(realWarehouseList);
        } catch (RomeException e) {
            log.error(e.getMessage(), e);
            return ResponseMsg.FAIL.buildMsg(e.getCode(), e.getMessage());
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return ResponseMsg.EXCEPTION.buildMsg();
        }
    }


}
