package com.rome.stock.innerservice.api.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

/**
 * 入库单打印
 *
 * <AUTHOR>
 * @date 2020/11/03
 */
@Data
public class PwPrintDTO {

    /**
     * 供应商代码
     */
    @ApiModelProperty("供应商编码")
    private String supplierCode;
    /**
     * 采购订单号
     */
    @ApiModelProperty("采购订单号")
    private String purchaseOrderNo;
    /**
     * 打印日期
     */
    @ApiModelProperty("打印日期")
    private String printDate;
    /**
     * 仓库代码
     */
    @ApiModelProperty("配送中心")
    private String warehouseCode;
    /**
     * 采购入库单号
     */
    @ApiModelProperty("采购入库单号")
    private String purchaseEntryNo;
    /**
     * 创建时间
     */
    @ApiModelProperty("创建日期")
    private String createTime;
    /**
     * 打印机
     */
    @ApiModelProperty("打印人")
    private String printer;
    /**
     * 总数量
     */
    @ApiModelProperty("合计数量")
    private BigDecimal totalQty;
    /**
     * 计件总量
     */
    @ApiModelProperty("计件总量")
    private BigDecimal totalPieceQty;
    /**
     * 创建人
     */
    @ApiModelProperty("创建人")
    private String creatorName;
    /**
     * 总金额
     */
    @ApiModelProperty("总金额")
    private BigDecimal totalAmount;
    /**
     * 序号
     */
    @ApiModelProperty("序号")
    private Integer orderNumber;
    /**
     * sku码
     */
    @ApiModelProperty("物料编码")
    private String skuCode;
    /**
     * sku的名字
     */
    @ApiModelProperty("物料名称")
    private String skuName;
    /**
     * 规范
     */
    @ApiModelProperty("规格型号")
    private String specification;
    /**
     * 基本单位数量
     */
    @ApiModelProperty("数量")
    private BigDecimal quantity;
    /**
     * 基本单位
     */
    @ApiModelProperty("单位")
    private String unit;
    /**
     * 采购收货量
     */
    @ApiModelProperty("计件数")
    private BigDecimal receivedQty;
    /**
     * 入库日期
     */
    @ApiModelProperty("入库日期")
    private String warehousingDate;
    /**
     * 仓库地点
     */
    @ApiModelProperty("仓库地点")
    private String factoryName;
    /**
     * 生产日期
     */
    @ApiModelProperty("生产日期")
    private String productDate;
    /**
     * 物料凭证
     */
    @ApiModelProperty("物料凭证")
    private String wmsRecordCode;
}
