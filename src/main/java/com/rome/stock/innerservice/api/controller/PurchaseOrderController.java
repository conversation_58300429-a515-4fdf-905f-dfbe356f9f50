package com.rome.stock.innerservice.api.controller;

import com.alibaba.fastjson.JSON;
import com.github.pagehelper.PageInfo;
import com.google.common.collect.Lists;
import com.rome.arch.core.clientobject.Response;
import com.rome.arch.core.exception.RomeException;
import com.rome.arch.util.controller.RomeController;
import com.rome.stock.innerservice.api.dto.*;
import com.rome.stock.innerservice.api.dto.frontrecord.*;
import com.rome.stock.innerservice.api.dto.qry.PurchaseOrderRecordCondition;
import com.rome.stock.innerservice.api.dto.warehouserecord.CancelRecordDTO;
import com.rome.stock.innerservice.common.AlikAssert;
import com.rome.stock.innerservice.common.ResCode;
import com.rome.stock.innerservice.common.RomeCollectionUtil;
import com.rome.stock.innerservice.constant.ResponseMsg;
import com.rome.stock.innerservice.domain.convertor.RwBatchConvertor;
import com.rome.stock.innerservice.domain.repository.RwBatchRepository;
import com.rome.stock.innerservice.domain.repository.SapInterfaceLogRepository;
import com.rome.stock.innerservice.domain.service.PurchaseOrderOldService;
import com.rome.stock.innerservice.domain.service.PurchaseOrderService;
import com.rome.stock.innerservice.domain.service.PurchaseOrderSyncJobService;
import com.rome.stock.innerservice.domain.service.RecordRealVirtualStockSyncRelationService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import io.swagger.annotations.ApiResponse;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.http.MediaType;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.List;

/**
 * 采购到大仓
 */
@Slf4j
@RomeController
@RequestMapping("/stock/v1/purchaseOrder")
@Api(tags = {"大仓采购"})
public class PurchaseOrderController {
    @Resource
    private PurchaseOrderService purchaseOrderService;
    @Resource
    private PurchaseOrderOldService purchaseOrderOldService;
    @Resource
    private PurchaseOrderSyncJobService purchaseOrderSyncJobService;

    @Resource
    private RecordRealVirtualStockSyncRelationService recordRealVirtualStockSyncRelationService;

    @Resource
    private SapInterfaceLogRepository sapInterfaceLogRepository;
    @Resource
    private RwBatchConvertor rwBatchConvertor;
    @Resource
    private RwBatchRepository rwBatchRepository;



    @ApiOperation(value = "接收采购入库单", nickname = "receivePurchaseOrderBatch", produces = MediaType.APPLICATION_JSON_VALUE)
    @ApiResponse(code = 200, message = "success")
    @RequestMapping(value = "/receivePurchaseOrderBatch", method = RequestMethod.POST)
    public Response addPurchaseNoticeRecord(@ApiParam(name = "purchaseOrder", value = "dto") @RequestBody @Validated PurchaseOrderDTO purchaseOrder) {
        String message = "";
        boolean success = false;
        String json = JSON.toJSONString(purchaseOrder);
        try {
            purchaseOrderService.addPurchaseNoticeRecord(purchaseOrder);
            success = true;
            message = "200";
            return ResponseMsg.SUCCESS.buildMsg();
        } catch (RomeException e) {
            message = e.getMessage();
            log.error("创建大仓采购单异常 :{}", e);
            return ResponseMsg.FAIL.buildMsg(e.getCode(), e.getMessage());
        } catch (Exception e) {
            message = e.getMessage();
            log.error("创建大仓采购单异常 : {}", e);
            return ResponseMsg.EXCEPTION.buildMsg();
        } finally {
            sapInterfaceLogRepository.saveCallBackInterFaceLog(2, purchaseOrder.getOutRecordCode(), "addPurchaseNoticeRecord",
                    json, message, success);
        }
    }


    @ApiOperation(value = "定时查询待同步收货单单据", nickname = "queryReceiptRecordByPage", produces = MediaType.APPLICATION_JSON_VALUE)
    @ApiResponse(code = 200, message = "success", response = List.class)
    @RequestMapping(value = "/queryReceiptRecordByPage", method = RequestMethod.POST)
    public Response<List<ReceiptRecordDTO>> queryReceiptRecordByPage(@RequestParam Integer page, @RequestParam Integer maxResult) {
        try {
            List<ReceiptRecordDTO> pageInfo = purchaseOrderSyncJobService.queryWaitTransferReceiptRecordByPage(page, maxResult);
            return ResponseMsg.SUCCESS.buildMsg(pageInfo);
        } catch (RomeException e) {
            log.error("查询待同步的收货单据异常:{}", e);
            return ResponseMsg.FAIL.buildMsg(e.getCode(), e.getMessage());
        } catch (Exception e) {
            log.error("查询待同步的收货单据异常:{}", e);
            return ResponseMsg.EXCEPTION.buildMsg();
        }
    }


    @ApiOperation(value = "分页查询待推送采购中心的数据", nickname = "queryWaitPushToPurchaseByPage", produces = MediaType.APPLICATION_JSON_VALUE)
    @ApiResponse(code = 200, message = "success", response = List.class)
    @RequestMapping(value = "/queryWaitPushToPurchaseByPage", method = RequestMethod.POST)
    public Response<List<WarehouseRecordPageDTO>> queryWaitPushToPurchaseByPage(@RequestParam Integer page, @RequestParam Integer maxResult) {
        try {
            List<WarehouseRecordPageDTO> pageInfo = purchaseOrderSyncJobService.queryWaitPushToPurchaseByPage(page, maxResult);
            return ResponseMsg.SUCCESS.buildMsg(pageInfo);
        } catch (RomeException e) {
            log.error("查询待推送采购中心数据异常:{}", e);
            return ResponseMsg.FAIL.buildMsg(e.getCode(), e.getMessage());
        } catch (Exception e) {
            log.error("查询待推送采购中心数据异常:{}", e);
            return ResponseMsg.EXCEPTION.buildMsg();
        }
    }

    @ApiOperation(value = "分页查询质检完成待推送到采购中心数据", nickname = "queryQualityWaitPushToPurchaseByPage", produces = MediaType.APPLICATION_JSON_VALUE)
    @ApiResponse(code = 200, message = "success", response = List.class)
    @RequestMapping(value = "/queryQualityWaitPushToPurchaseByPage", method = RequestMethod.POST)
    public Response<List<WarehouseRecordPageDTO>> queryQualityWaitPushToPurchaseByPage(@RequestParam Integer page, @RequestParam Integer maxResult) {
        try {
            List<WarehouseRecordPageDTO> pageInfo = purchaseOrderSyncJobService.selectWaitQualityToPurchase(page, maxResult);
            return ResponseMsg.SUCCESS.buildMsg(pageInfo);
        } catch (RomeException e) {
            log.error("查询待推送采购中心数据异常:{}", e);
            return ResponseMsg.FAIL.buildMsg(e.getCode(), e.getMessage());
        } catch (Exception e) {
            log.error("查询待推送采购中心数据异常:{}", e);
            return ResponseMsg.EXCEPTION.buildMsg();
        }
    }

    @ApiOperation(value = "定时查询待同步质检结果到wms批次数据", nickname = "queryWaitTransferCheckResultByPage", produces = MediaType.APPLICATION_JSON_VALUE)
    @ApiResponse(code = 200, message = "success")
    @RequestMapping(value = "/queryWaitTransferCheckResultByPage", method = RequestMethod.POST)
    public Response<List<RwBatchDTO>> queryWaitTransferCheckResultByPage(@RequestParam Integer page, @RequestParam Integer maxResult) {
        try {
            List<RwBatchDTO> pageInfo = purchaseOrderSyncJobService.queryWaitTransferCheckResultByPage(page, maxResult);
            return ResponseMsg.SUCCESS.buildMsg(pageInfo);
        } catch (RomeException e) {
            log.error("查询待同步质检结果到wms异常:{}", e);
            return ResponseMsg.FAIL.buildMsg(e.getCode(), e.getMessage());
        } catch (Exception e) {
            log.error("查询待同步质检结果到wms异常:{}", e);
            return ResponseMsg.EXCEPTION.buildMsg();
        }
    }

    @ApiOperation(value = "定时任务查询待推送采购中心批次信息", nickname = "selectWaitPushPurchase", produces = MediaType.APPLICATION_JSON_VALUE)
    @ApiResponse(code = 200, message = "success", response = RwBatchDTO.class)
    @RequestMapping(value = "/selectWaitPushPurchase", method = RequestMethod.POST)
    public Response<List<RwBatchDTO>> selectWaitPushPurchase(@RequestParam Integer page, @RequestParam Integer maxResult) {
        try {
            List<RwBatchDTO> rwBatchDTOS = purchaseOrderSyncJobService.selectWaitPushPurchase(page, maxResult);
            return ResponseMsg.SUCCESS.buildMsg(rwBatchDTOS);
        } catch (RomeException e) {
            log.error("查询待推送采购中心批次信息异常:", e);
            return ResponseMsg.FAIL.buildMsg(e.getCode(), e.getMessage());
        } catch (Exception e) {
            log.error("查询待推送采购中心批次信息异常:", e);
            return ResponseMsg.EXCEPTION.buildMsg();
        }
    }

    @ApiOperation(value = "同步质检结果给wms", nickname = "synchronizationCheckResultToWmsByReceiveRecord", produces = MediaType.APPLICATION_JSON_VALUE)
    @ApiResponse(code = 200, message = "success", response = List.class)
    @RequestMapping(value = "/synchronizationCheckResultToWmsByReceiveRecord", method = RequestMethod.POST)
    public Response synchronizationCheckResultToWmsByReceiveRecord(@RequestParam String wmsRecordCode, @RequestBody List<RwBatchDTO> wmsRecordCodeBatchList) {
        String message = "";
        boolean isSucc = false;
        try {
            purchaseOrderService.synchronizationCheckResultToWmsByReceiveRecord(wmsRecordCode, wmsRecordCodeBatchList,false);
            message = "200";
            isSucc = true;
            return ResponseMsg.SUCCESS.buildMsg();
        } catch (RomeException e) {
            log.error("同步质检结果给wms异常:{}", e);
            message = e.getMessage();
            return ResponseMsg.FAIL.buildMsg(e.getCode(), e.getMessage());
        } catch (Exception e) {
            log.error("同步质检结果给wms异常:{}", e);
            message = e.getMessage();
            return ResponseMsg.EXCEPTION.buildMsg(e.getMessage());
        } finally {
            sapInterfaceLogRepository.saveCallBackInterFaceLog(1, wmsRecordCode
                    , "synchronizationCheckResultToWmsByReceiveRecord", JSON.toJSONString(wmsRecordCodeBatchList), message, isSucc);
        }
    }

    @ApiOperation(value = "中台质检结果强制处理", nickname = "handleCheckResultByForce", produces = MediaType.APPLICATION_JSON_VALUE)
    @ApiResponse(code = 200, message = "success", response = List.class)
    @RequestMapping(value = "/handleCheckResultByForce", method = RequestMethod.POST)
    public Response handleCheckResultByForce(@RequestParam String wmsRecordCode, @RequestBody List<Long> list) {
        if (CollectionUtils.isEmpty(list)) {
            return ResponseMsg.FAIL.buildMsg(ResCode.STOCK_ERROR_1002, ResCode.STOCK_ERROR_1002_DESC);
        }
        String message = "";
        boolean isSucc = false;
        try {
            List<RwBatchDTO> wmsRecordCodeBatchList = rwBatchConvertor.entitiesToDtos(rwBatchRepository.selectBatchInfoByIdS(wmsRecordCode,list));
            AlikAssert.isTrue(list.size() == wmsRecordCodeBatchList.size(), ResCode.STOCK_ERROR_1002, ResCode.STOCK_ERROR_1002_DESC + ":id输入有误，请检查");
            purchaseOrderService.synchronizationCheckResultToWmsByReceiveRecord(wmsRecordCode, wmsRecordCodeBatchList,true);
            message = "200";
            isSucc = true;
            return ResponseMsg.SUCCESS.buildMsg();
        } catch (RomeException e) {
            log.error(e.getMessage(), e);
            message = e.getMessage();
            return ResponseMsg.FAIL.buildMsg(e.getCode(), e.getMessage());
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            message = e.getMessage();
            return ResponseMsg.EXCEPTION.buildMsg(e.getMessage());
        } finally {
            sapInterfaceLogRepository.saveCallBackInterFaceLog(1, wmsRecordCode
                    , "handleCheckResultByForce", JSON.toJSONString(list), message, isSucc);
        }
    }


    @ApiOperation(value = "根据页面条件查询采购通知单", nickname = "queryPurchaseOrder", produces = MediaType.APPLICATION_JSON_VALUE)
    @ApiResponse(code = 200, message = "success")
    @RequestMapping(value = "/queryPurchaseOrder", method = RequestMethod.POST)
    public Response<PageInfo<PurchaseOrderPageDTO>> queryPurchaseOrder(@ApiParam(name = "purchaseSyncCheckResult", value = "dto") @RequestBody PurchaseOrderRecordCondition condition) {
        try {
            return Response.builderSuccess(purchaseOrderOldService.queryPurchaseRecordList(condition));
        } catch (RomeException e) {
            log.error(e.getMessage(), e);
            return Response.builderFail(e.getCode(), e.getMessage());
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return Response.builderFail(ResCode.STOCK_ERROR_1003_DESC, ResCode.STOCK_ERROR_1003_DESC);
        }
    }

    @ApiOperation(value = "采购单明细查询，包含前置单信息", nickname = "queryPurchaseRecordDetails", produces = MediaType.APPLICATION_JSON_VALUE)
    @ApiResponse(code = 200, message = "success")
    @RequestMapping(value = "/queryPurchaseRecordDetails/{recordId}", method = RequestMethod.GET)
    public Response<PurchaseOrderPageDTO> queryPurchaseOrderDetail(@ApiParam(name = "recordId", value = "recordId") @PathVariable Long recordId) {
        try {
            return Response.builderSuccess(purchaseOrderOldService.queryPurchaseRecordInfoById(recordId));
        } catch (RomeException e) {
            log.error(e.getMessage(), e);
            return Response.builderFail(e.getCode(), e.getMessage());
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return Response.builderFail(ResCode.STOCK_ERROR_1003_DESC, ResCode.STOCK_ERROR_1003_DESC);
        }
    }

    @ApiOperation(value = "查询采购单sku实仓虚仓分配关系", nickname = "queryAllocConfigInfo", produces = MediaType.APPLICATION_JSON_VALUE)
    @ApiResponse(code = 200, message = "success")
    @RequestMapping(value = "/queryAllocConfigInfo/{recordId}", method = RequestMethod.GET)
    public Response<PurchaseOrderAllocConfigPageDTO> queryAllocConfigInfo(@ApiParam(name = "recordId", value = "recordId") @PathVariable Long recordId) {
        try {
            return Response.builderSuccess(purchaseOrderOldService.queryAllocConfigInfo(recordId));
        } catch (RomeException e) {
            log.error(e.getMessage(), e);
            return Response.builderFail(e.getCode(), e.getMessage());
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return Response.builderFail(ResCode.STOCK_ERROR_1003_DESC, ResCode.STOCK_ERROR_1003_DESC);
        }
    }


    @ApiOperation(value = "批量查询采购单sku实仓虚仓分配关系", nickname = "queryAllocConfigInfo", produces = MediaType.APPLICATION_JSON_VALUE)
    @ApiResponse(code = 200, message = "success")
    @RequestMapping(value = "/queryAllocConfigInfoByRecords", method = RequestMethod.POST)
    public Response<List<PurchaseOrderAllocConfigPageDTO>> queryAllocConfigInfo(@ApiParam(name = "recordIds", value = "recordIds") @RequestBody List<Long> recordIds) {
        try {
            return Response.builderSuccess(purchaseOrderOldService.queryAllocConfigInfoByRecords(recordIds));
        } catch (RomeException e) {
            log.error(e.getMessage(), e);
            return Response.builderFail(e.getCode(), e.getMessage());
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return Response.builderFail("500000", e.getMessage());
        }
    }

    @ApiOperation(value = "配置单据级别的sku实仓虚仓分配比例", nickname = "allotConfig", produces = MediaType.APPLICATION_JSON_VALUE)
    @ApiResponse(code = 200, message = "success")
    @RequestMapping(value = "/allotConfig", method = RequestMethod.POST)
    public Response allotConfig(@ApiParam(name = "config", value = "config") @RequestBody List<RecordRealVirtualStockSyncRelationDTO> config) {
        try {
            recordRealVirtualStockSyncRelationService.insertRecordRealVirtualStockRelation(config);
            return Response.builderSuccess("success");
        } catch (RomeException e) {
            log.error(e.getMessage(), e);
            return Response.builderFail(e.getCode(), e.getMessage());
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return Response.builderFail("500000", e.getMessage());
        }
    }

    @ApiOperation(value = "取消采购入库单据", nickname = "purchaseCancel", produces = MediaType.APPLICATION_JSON_VALUE)
    @ApiResponse(code = 200, message = "success")
    @RequestMapping(value = "/purchaseCancel", method = RequestMethod.POST)
    public Response purchaseCancel(@ApiParam(name = "list", value = "取消大仓采购") @RequestBody CancelRecordDTO cancelRecordDTO) {
        String message = "";
        boolean success = false;
        try {
            purchaseOrderService.cancelPurchaseOrder(cancelRecordDTO.getRecordCode(), cancelRecordDTO.getRecordType());
            success = true;
            message = "200";
            return Response.builderSuccess("success");
        } catch (RomeException ex) {
            log.error("取消采购单据异常:", ex);
            message = ex.getMessage();
            return Response.builderFail(ex.getCode(), ex.getMessage());
        } catch (Exception ex) {
            log.error("取消采购单据异常:", ex);
            message = ex.getMessage();
            return Response.builderFail("500000", ex.getMessage());
        } finally {
            sapInterfaceLogRepository.saveCallBackInterFaceLog(1, cancelRecordDTO.getRecordCode(), "purchaseCancel",
                    JSON.toJSONString(cancelRecordDTO), message, success);
        }
    }

    @ApiOperation(value = "查询采购单批次信息", nickname = "purchaseCancel", produces = MediaType.APPLICATION_JSON_VALUE)
    @ApiResponse(code = 200, message = "success")
    @RequestMapping(value = "/queryRwBatchInfoByOutRecordCode/{outRecordCode}", method = RequestMethod.POST)
    public Response<List<RwBatchDTO>> queryRwBatchInfoByOutRecordCode(@PathVariable("outRecordCode") String outRecordCode) {
        try {
            List<RwBatchDTO> rwBatchDTOS = purchaseOrderService.queryRwBatchInfoByOutRecordCode(outRecordCode);
            return Response.builderSuccess(rwBatchDTOS);
        } catch (RomeException ex) {
            log.error("根据外采单号查询批次信息异常:{}", ex);
            return Response.builderFail(ex.getCode(), ex.getMessage());
        } catch (Exception ex) {
            log.error("根据外采单号查询批次信息异常:{}", ex);
            return Response.builderFail("500000", ex.getMessage());
        }
    }


    @ApiOperation(value = "通知采购中心采购入库数量", nickname = "purchaseEntryNotify", produces = MediaType.APPLICATION_JSON_VALUE)
    @ApiResponse(code = 200, message = "success")
    @RequestMapping(value = "/purchaseEntryNotify", method = RequestMethod.POST)
    public Response purchaseEntryNotify(@RequestParam("recordCode") String recordCode) {
        try {
            return purchaseOrderService.purchaseEntryNotify(recordCode);
        } catch (RomeException ex) {
            log.error("采购中心采购入库异常:", ex);
            return Response.builderFail(ex.getCode(), ex.getMessage());
        } catch (Exception ex) {
            log.error("采购中心采购入库异常:", ex);
            return Response.builderFail("500000", ex.getMessage());
        }
    }

    @ApiOperation(value = "通知采购中心质检批入库信息", nickname = "purchaseQualityNotify", produces = MediaType.APPLICATION_JSON_VALUE)
    @ApiResponse(code = 200, message = "success")
    @RequestMapping(value = "/purchaseQualityNotify", method = RequestMethod.POST)
    @Deprecated
    public Response purchaseQualityNotify(@RequestBody RwBatchDTO rwBatchDTO) {
        try {
            return purchaseOrderService.purchaseQualityNotify(rwBatchDTO);
        } catch (RomeException ex) {
            log.error("采购中心采购入库异常:", ex);
            return Response.builderFail(ex.getCode(), ex.getMessage());
        } catch (Exception ex) {
            log.error("采购中心采购入库异常:", ex);
            return Response.builderFail("500000", ex.getMessage());
        }
    }

    @ApiOperation(value = "通知采购中心质检完成", nickname = "qualityFinished", produces = MediaType.APPLICATION_JSON_VALUE)
    @ApiResponse(code = 200, message = "success")
    @RequestMapping(value = "/qualityFinished", method = RequestMethod.POST)
    public Response qualityFinished(@RequestParam(value = "recordCode") String recordCode, @RequestParam(value = "pwOrderNo") String pwOrderNo) {
        try {
            purchaseOrderService.qualityFinished(recordCode, pwOrderNo);
            return Response.builderSuccess("");
        } catch (RomeException e) {
            log.error("通知采购中心质检完成异常:", e);
            return Response.builderFail(e.getCode(), e.getMessage());
        } catch (Exception e) {
            log.error("通知采购中心质检完成异常:", e);
            return Response.builderFail("500000", e.getMessage());
        }
    }

    @ApiOperation(value = "通知采购中心采购退供出库", nickname = "purchaseReturnEntryNotify", produces = MediaType.APPLICATION_JSON_VALUE)
    @ApiResponse(code = 200, message = "success")
    @RequestMapping(value = "/purchaseReturnEntryNotify", method = RequestMethod.POST)
    public Response purchaseReturnEntryNotify(@RequestParam("recordCode") String recordCode) {
        try {
            return purchaseOrderService.purchaseReturnEntryNotify(recordCode);
        } catch (RomeException ex) {
            log.error("通知采购中心采购退供出库异常:{}", ex);
            return Response.builderFail(ex.getCode(), ex.getMessage());
        } catch (Exception ex) {
            log.error("通知采购中心采购退供出库异常:{}", ex);
            return Response.builderFail("500000", ex.getMessage());
        }
    }

    @ApiOperation(value = "查询待推送采购中心的退供单", nickname = "selectWaitNotifyToPurchase", produces = MediaType.APPLICATION_JSON_VALUE)
    @ApiResponse(code = 200, message = "success")
    @RequestMapping(value = "/selectWaitNotifyToPurchase", method = RequestMethod.POST)
    public Response<List<WarehouseRecordPageDTO>> selectWaitNotifyToPurchase(@RequestParam("page") Integer page, @RequestParam("maxResult") Integer maxResult) {
        try {
            List<WarehouseRecordPageDTO> warehouseRecordDTOS = purchaseOrderService.selectWaitNotifyToPurchase(page, maxResult);
            return Response.builderSuccess(warehouseRecordDTOS);
        } catch (RomeException ex) {
            log.error("查询待推送采购中心的退供单异常:{}", ex);
            return Response.builderFail(ex.getCode(), ex.getMessage());
        } catch (Exception ex) {
            log.error("查询待推送采购中心的退供单异常:{}", ex);
            return Response.builderFail("500000", ex.getMessage());
        }
    }


    @ApiOperation(value = "查询待推送采购中心的门店直送", nickname = "selectSupplierDirectDeliveryToPurchase", produces = MediaType.APPLICATION_JSON_VALUE)
    @ApiResponse(code = 200, message = "success")
    @RequestMapping(value = "/selectSupplierDirectDeliveryToPurchase", method = RequestMethod.POST)
    public Response<List<WarehouseRecordPageDTO>> selectSupplierDirectDeliveryToPurchase(@RequestParam("page") Integer page, @RequestParam("maxResult") Integer maxResult) {
        try {
            List<WarehouseRecordPageDTO> warehouseRecordDTOS = purchaseOrderService.selectSupplierDirectDeliveryToPurchase(page, maxResult);
            return Response.builderSuccess(warehouseRecordDTOS);
        } catch (RomeException ex) {
            log.error("查询待推送采购中心的门店直送异常:", ex);
            return Response.builderFail(ex.getCode(), ex.getMessage());
        } catch (Exception ex) {
            log.error("查询待推送采购中心的门店直送异常:", ex);
            return Response.builderFail("500000", ex.getMessage());
        }
    }


    @ApiOperation(value = "通知质检中心生成检验批", nickname = "qualityCenterNotify", produces = MediaType.APPLICATION_JSON_VALUE)
    @ApiResponse(code = 200, message = "success")
    @GetMapping("/notify/qualityCenter")
    public Response qualityCenterNotify(@RequestParam("wmsRecordCode") String wmsRecordCode, @RequestParam("recordCode") String recordCode) {
        try {
            return purchaseOrderService.qualityCenterNotify(wmsRecordCode, recordCode);
        } catch (RomeException e) {
            log.error("通知质检中心生成检验批异常 :", e);
            return ResponseMsg.FAIL.buildMsg(e.getCode(), e.getMessage());
        } catch (Exception e) {
            log.error("通知质检中心生成检验批异常 :", e);
            return ResponseMsg.EXCEPTION.buildMsg();
        }
    }

    @ApiOperation(value = "中台质检结果强制处理[老逻辑]", nickname = "handleCheckResultByForce", produces = MediaType.APPLICATION_JSON_VALUE)
    @ApiResponse(code = 200, message = "success", response = List.class)
    @RequestMapping(value = "/handleCheckResultByForceOld", method = RequestMethod.POST)
    public Response handleCheckResultByForceOld(@RequestParam String wmsRecordCode, @RequestBody List<RwBatchWithoutWmsDTO> list) {
        if (CollectionUtils.isEmpty(list)) {
            return ResponseMsg.FAIL.buildMsg(ResCode.STOCK_ERROR_1002, ResCode.STOCK_ERROR_1002_DESC);
        }
        try {
            purchaseOrderOldService.handleCheckResultByForce(wmsRecordCode, list);
            return ResponseMsg.SUCCESS.buildMsg();
        } catch (RomeException e) {
            log.error(e.getMessage(), e);
            return ResponseMsg.FAIL.buildMsg(e.getCode(), e.getMessage());
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return ResponseMsg.EXCEPTION.buildMsg();
        }
    }

    @ApiOperation(value = "根据采购单号查询收货信息", nickname = "queryPurchaseReceiptInfo", produces = MediaType.APPLICATION_JSON_VALUE)
    @ApiResponse(code = 200, message = "success", response = PurchaseReceiptDTO.class)
    @GetMapping(value = "/list/purchase/{purchaseRecordCode}")
    public Response<List<PurchaseReceiptDTO>> queryPurchaseReceiptInfo(@PathVariable("purchaseRecordCode") String purchaseRecordCode) {
        try {
            List<PurchaseReceiptDTO> purchaseReceiptDTOS = purchaseOrderService.queryRwBatchInfoByPWCode(purchaseRecordCode);
            return Response.builderSuccess(purchaseReceiptDTOS);
        } catch (RomeException e) {
            log.error("根据采购单号查询收货信息异常:{}", e);
            return ResponseMsg.FAIL.buildMsg(e.getCode(), e.getMessage());
        } catch (Exception e) {
            log.error("根据采购单号查询收货信息异常:{}", e);
            return ResponseMsg.EXCEPTION.buildMsg();
        }
    }


    @ApiOperation(value = "查询待推送同步收货信息给采购中心-虚仓标签", nickname = "queryReceiptWaitPushForVirtualTag", produces = MediaType.APPLICATION_JSON_VALUE)
    @ApiResponse(code = 200, message = "success", response = List.class)
    @RequestMapping(value = "/queryReceiptWaitPushForVirtualTag", method = RequestMethod.POST)
    public Response<List<RwBatchDTO>> queryReceiptWaitPushForVirtualTag(@RequestParam("page") Integer page, @RequestParam("maxResult") Integer maxResult) {
        try {
            List<RwBatchDTO> pageInfo = purchaseOrderSyncJobService.queryReceiptWaitPushForVirtualTag(page, maxResult);
            return ResponseMsg.SUCCESS.buildMsg(pageInfo);
        } catch (RomeException e) {
            log.error("查询待推送同步收货信息给采购中心-虚仓标签异常:{}", e);
            return ResponseMsg.FAIL.buildMsg(e.getCode(), e.getMessage());
        } catch (Exception e) {
            log.error("查询待推送同步收货信息给采购中心-虚仓标签异常:{}", e);
            return ResponseMsg.EXCEPTION.buildMsg(e.getMessage());
        }
    }

    @ApiOperation(value = "根据采购预约单号查询外部系统单据编号接口")
    @ApiResponse(code = 200, message = "success")
    @RequestMapping(value = "/queryOutRecordCode", method = RequestMethod.POST)
    public Response<List<OutRecordCodeResultDTO>> queryOutRecordCode(@RequestBody OutRecordCodeParamDTO appointRecordCode) {
        try {
            List<OutRecordCodeResultDTO> rwBatchDTOS = purchaseOrderService.queryOutRecordCode(appointRecordCode);
            return Response.builderSuccess(rwBatchDTOS);
        } catch (RomeException ex) {
            log.error("根据采购预约单号查询外部系统单据编号接口异常:{}", ex);
            return Response.builderFail(ex.getCode(), ex.getMessage());
        } catch (Exception ex) {
            log.error("根据采购预约单号查询外部系统单据编号接口异常:{}", ex);
            return Response.builderFail("500000", ex.getMessage());
        }
    }


    @ApiOperation(value = "品类通知库存中心商品状态变更", nickname = "skuStatusChangeNotice", produces = MediaType.APPLICATION_JSON_VALUE)
    @ApiResponse(code = 200, message = "success", response = List.class)
    @RequestMapping(value = "/skuStatusChangeNotice", method = RequestMethod.POST)
    public Response skuStatusChangeNotice(@RequestBody SkuStatusChangeSendMessageDTO messageDTO) {
        try {
            purchaseOrderService.skuStatusChangeNotice(messageDTO);
            return Response.builderSuccess("");
        } catch (RomeException ex) {
            log.error("品类通知库存中心商品状态变更:{}", ex);
            return Response.builderFail(ex.getCode(), ex.getMessage());
        } catch (Exception ex) {
            log.error("品类通知库存中心商品状态变更接口异常:{}", ex);
            return Response.builderFail("500000", ex.getMessage());
        }
    }
}
