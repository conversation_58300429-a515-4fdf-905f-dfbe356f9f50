package com.rome.stock.innerservice.api.dto;


import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

@ApiModel(value = "WarehouseInventoryStartOperateDTO", description = "盘点单操作对象")
@Data
@EqualsAndHashCode
public class WarehouseInventoryStartOperateDTO extends Pagination {

    private static final long serialVersionUID = 6241269605258899670L;


    @ApiModelProperty(value = "盘点单Id")
    private Long id;


    @ApiModelProperty(value = "创建人")
    private Long modifier;

    /**
     * 盘点单号
     */
    private String recordCode;

}
