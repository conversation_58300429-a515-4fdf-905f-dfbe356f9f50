package com.rome.stock.innerservice.api.dto.groupbuy;

import com.rome.arch.core.dto.DTO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * 退货通知对象
 */
@Data
public class NotifyReservationReturnDTO extends DTO {

    @ApiModelProperty(value="售后单号")
    private String afterSaleCode;

    @ApiModelProperty(value="销售单号")
    private String saleCode;

    @ApiModelProperty(value="退货详情")
    private List<NotifyReservationReturnDetailDTO> returnDetailNoticeList;



}    
   