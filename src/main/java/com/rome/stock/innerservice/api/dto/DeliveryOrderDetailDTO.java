package com.rome.stock.innerservice.api.dto;

import java.math.BigDecimal;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

import com.rome.stock.innerservice.domain.entity.SkuQtyUnitBaseE;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 类ShopReceiptOrderDetail的实现描述：交货单详情
 *
 * <AUTHOR> 2019/4/29 15:48
 */
@Data
@EqualsAndHashCode
public class DeliveryOrderDetailDTO extends SkuQtyUnitBaseE {

    @ApiModelProperty(value = "行号")
    @NotNull(message="行号不能为空")
    private Long lineNo;

    @ApiModelProperty(value = "交货单行号")
    private String deliverylineNo;

    @ApiModelProperty(value = "PO行号")
    private String polineNo;

    @ApiModelProperty(value = "交货数量")
    @NotNull(message="交货数量不能为空")
    private BigDecimal skuQty;

    @ApiModelProperty(value = "sku编号")
    @NotNull(message="sku编号不能为空")
    private String skuCode;

    @ApiModelProperty(value = "单位")
    @NotBlank(message="单位不能为空")
    private String unit;

    @ApiModelProperty(value = "单位code")
    @NotBlank(message="单位code不能为空")
    private String unitCode;
}
