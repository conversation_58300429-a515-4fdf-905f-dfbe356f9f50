package com.rome.stock.innerservice.api.dto.ehub;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlRootElement;

@XmlAccessorType(XmlAccessType.FIELD)
@XmlRootElement(name = "DETAIL")
@Data
public class Detail {

	@ApiModelProperty("箱号")
	@XmlElement(name="BOXNO")
	private String boxNo;

	@ApiModelProperty("客户单号")
	@XmlElement(name="LEG_NO")
	private String legNO;

	@ApiModelProperty("行号")
	@XmlElement(name="LINE_NO")
	private String lineNo;

	@ApiModelProperty("产品名称")
	@XmlElement(name="PRODUCT_NAME")
	private String productName;

	@ApiModelProperty("产品编码")
	@XmlElement(name="PRODUCT_CODE")
	private String productCode;

	@ApiModelProperty("件数")
	@XmlElement(name="QUANTITY")
	private String quantity;

	@ApiModelProperty("重量")
	@XmlElement(name="WEIGHT")
	private String weight;

	
}
