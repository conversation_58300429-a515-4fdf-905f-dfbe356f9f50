package com.rome.stock.innerservice.api.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * @Doc:.....
 * @Author: lchy
 * @Date: 2020/6/1
 * @Version 1.0
 */
@Data
@EqualsAndHashCode
public class ShortageSummaryDTO extends Pagination  {
    /**
     * 唯一主键
     */
    @ApiModelProperty(value = "主键")
    private Long id;
    /**
     * 仓储类型
     */
    @ApiModelProperty(value = "仓储类型")
    private Integer storageType;
    /**
     * 工厂编码
     */
    @ApiModelProperty(value = "工厂编码")
    private String factoryCode;
    /**
     * 工厂名称
     */
    @ApiModelProperty(value = "工厂名称")
    private String factoryName;

    /**
     * 仓库编码
     */
    @ApiModelProperty(value = "仓库编码")
    private String realWarehouseCode;

    /**
     * 虚仓code
     */
    @ApiModelProperty(value = "虚仓code")
    private String virtualWarehouseCode;

    /**
     * 虚仓id
     */
    @ApiModelProperty(value = "虚仓id")
    private Long virtualWarehouseId;


    /**
     * 虚仓name
     */
    @ApiModelProperty(value = "虚仓name")
    private String virtualWarehouseName;

    /**
     * 实仓id
     */
    @ApiModelProperty(value = "实仓id")
    private Long realWarehouseId;

    /**
     * 实仓id
     */
    @ApiModelProperty(value = "实仓ids")
    private List<Long> realWarehouseIds;
    /**
     * 实仓id
     */
    @ApiModelProperty(value = "虚仓ids")
    private List<Long> virtualWarehouseIds;
    /**
     * 仓库名称
     */
    @ApiModelProperty(value = "仓库名称")
    private String realWarehouseName;
    /**
     * 渠道编码
     */
    @ApiModelProperty(value = "渠道编码")
    private String channelCode;
    /**
     * 渠道字符串集合
     */
    @ApiModelProperty(value = "渠道字符串集合")
    private String channelCodes;
    /**
     * 渠道名称
     */
    @ApiModelProperty(value = "渠道名称")
    private String channelName;
    /**
     * 商品id
     */
    @ApiModelProperty(value = "商品id")
    private Long skuId;
    /**
     * 商品skuId集合
     */
    @ApiModelProperty(value = "商品skuId集合")
    private List<Long> skuIds;
    /**
     * 商品skuId集合
     */
    @ApiModelProperty(value = "商品skuId集合")
    private List<String> skuCodes;
    /**
     * 商品编号
     */
    @ApiModelProperty(value = "商品编码")
    private String skuCode;
    /**
     * 商品名称
     */
    @ApiModelProperty(value = "商品名称")
    private String skuName;
    /**
     * 商品标题
     */
    @ApiModelProperty(value = "商品标题")
    private String skuTitle;
    /**
     * 商品基本单位
     */
    @ApiModelProperty(value = "商品基本单位")
    private String skuUnit;
    /**
     * 商品基本单位编码
     */
    @ApiModelProperty(value = "商品基本单位编码")
    private String skuUnitCode;
    /**
     * 商品品类
     */
    @ApiModelProperty(value = "商品品类")
    private String skuType;
    /**
     * 库存现有量
     */
    @ApiModelProperty(value = "库存现有量")
    private BigDecimal realQty;
    /**
     * 库存可用量
     */
    @ApiModelProperty(value = "库存可用量")
    private BigDecimal availableQty;
    /**
     * 库存锁定量
     */
    @ApiModelProperty(value = "库存锁定量")
    private BigDecimal lockQty;
    /**
     * 库存质检锁定量
     */
    @ApiModelProperty(value = "库存质检锁定量")
    private BigDecimal qualityQty;

    /**
     * 库存质检锁定量
     */
    @ApiModelProperty(value = "库存质检预分配数量")
    private BigDecimal qualityPredictQty = BigDecimal.ZERO;
    /**
     * 冻结库存
     */
    @ApiModelProperty(value = "冻结库存")
    private BigDecimal unqualifiedQty;

    @ApiModelProperty(value = "69码集合")
    private List<String> barCodeList;


    private Long virtualGroupId;
    private List<Long> virtualGroupIds;
    private String virtualGroupCode;
    private String virtualGroupName;

    /**
     * 冻结库存
     */
    @ApiModelProperty(value = "缺货数量")
    private BigDecimal shortageQty;
    /**
     * 公司编号
     */
    @ApiModelProperty(value = "公司编号")
    private String corpCode;
    /**
     * 公司名称
     */
    @ApiModelProperty(value = "公司名称")
    private String corpName;
    /**
     * 仓库类型名称
     */
    @ApiModelProperty(value = "仓库类型名称")
    private String warehouseTypeName;
    /**
     * 仓库类型
     */
    @ApiModelProperty(value = "仓库类型")
    private Integer warehouseType;
    /**
     *可用库存量是否必须大于0
     */
    @ApiModelProperty(value = "可用库存量是否必须大于0")
    private Integer moreThanZero;
    /**
     * 质检库存量是否必须大于0
     */
    @ApiModelProperty(value = "质检库存量是否必须大于0")
    private Integer moreThanZeroQuality;
    /**
     * 锁定库存量是否必须大于0
     */
    @ApiModelProperty(value = "锁定库存量是否必须大于0")
    private Integer moreThanZeroLock;
    /**
     * 商家编号
     */
    @ApiModelProperty(value = "商家编号")
    private Long merchantId;

    /**
     * 虚仓分配数
     */
    private Integer virtualAllotNum;

    /**
     * 策略组关联的渠道数
     */
    private Integer channelAllotNum;
    
    @ApiModelProperty(value = "虚拟仓库类型")
    private Integer virtualWarehouseType;
    
    @ApiModelProperty(value = "虚拟仓库类型列表")
	private List<Integer> virtualWarehouseTypeList;

    private BigDecimal onroadQty;


    private List<VirtualWarehouse> virtualWarehouseList;

    private List<ChannelSales> channelSalesList;


    private Integer syncRate;

    public String getRwIdAndSkuId() {
        return realWarehouseId + "_" + skuId;
    }


    /**
     * 是否导出excel操作
     */
    private boolean isImportExcel = false;
    
    /**
     * 箱单位名称
     */
    @ApiModelProperty(value = "箱单位名称")
    private String boxUnitName;

    /**
     * 箱单位数量
     */
    @ApiModelProperty(value = "箱单位数量")
    private BigDecimal boxUnitCount;

    /**
     * 发货单位
     */
    @ApiModelProperty(value = "发货单位")
    private String deliveryUnitName;

    /**
     * 发货单位数量
     */
    @ApiModelProperty(value = "发货单位数量")
    private BigDecimal deliveryUnitCount;
    /**
     * 库存查询页面用，1表示查询锁定库存 2表示查询在途库存
     */
    private  Integer type;
    
    /**
     * 单据编码
     */
    private String recordCode;
    
    /**
     * 单据编码列表
     */
    private List<String> recordCodeList;
    
    /**
	 * SAP交货单号
	 */
	@ApiModelProperty(value = "SAP交货单号")
	private List<String> sapOrderCodes;
	
	/**
	 * 云商原始单号
	 */
	@ApiModelProperty(value = "云商原始单号")
	private List<String> originOrderCodeList;
	
	/**
	 * 交易单号
	 */
	@ApiModelProperty(value = "交易单号")
	private List<String> outRecordCodeList;
	
	/**
	 * 结束时间
	 */
	@ApiModelProperty(value = "结束时间")
	private Date endTime;
	
	/**
     * 查询排序，是否按降序
     */
	@ApiModelProperty(value="查询排序，是否按降序")
	private boolean sortDesc = true;
	
	/**
     * 包含count查询
     */
	@ApiModelProperty(value="是否统计条数")
    private boolean count = true;
	
	/**
     * 库存查询页面用，1表示查询锁定库存 2表示查询在途库存
     */
    private  List<Integer> subTypeList;
    
    /**
	 * 单据类型list
	 */
	@ApiModelProperty(value = "库存交易类型")
	private List<Integer> recordTypeList;


    @ApiModelProperty(value = "关联单号")
    private String relationRecordCode;

    /**
     * 关联单据编码
     */
    @ApiModelProperty(value = "关联单据编码")
    private List<String> relationRecordCodeList;
    
}
