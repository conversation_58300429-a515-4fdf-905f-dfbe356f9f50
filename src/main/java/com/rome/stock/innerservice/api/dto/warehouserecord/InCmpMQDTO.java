package com.rome.stock.innerservice.api.dto.warehouserecord;

import com.rome.stock.innerservice.api.dto.WarehouseRecordPageDTO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 类InCmpMQDTO的实现描述
 *
 */
@Data
@EqualsAndHashCode
public class InCmpMQDTO {

    @ApiModelProperty(value = "单据编码")
    private String recordCode;

    @ApiModelProperty(value = "单据类型")
    private Integer recordType;

    @ApiModelProperty(value = "cmp交互状态")
    private Integer cmpStatus;

    public WarehouseRecordPageDTO initWarehouseRecordPageDTO(){
        WarehouseRecordPageDTO recordDto = new WarehouseRecordPageDTO();
        recordDto.setRecordCode(this.recordCode);
        recordDto.setRecordType(this.recordType);
        recordDto.setSaveLog(true);
        return  recordDto;
    }
}
