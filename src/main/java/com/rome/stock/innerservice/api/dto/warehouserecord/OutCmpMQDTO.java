package com.rome.stock.innerservice.api.dto.warehouserecord;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 类InCmpMQDTO的实现描述
 *
 */
@Data
@EqualsAndHashCode
public class OutCmpMQDTO {

    @ApiModelProperty(value = "单据编码")
    private String recordCode;

    @ApiModelProperty(value = "单据类型")
    private Integer recordType;

    /**
     * 仓库业务类型: 1: 直营门店 2: 加盟门店 3: 仓库 4. 其他仓库"
     */
    private Integer rwBusinessType;

}
