package com.rome.stock.innerservice.api.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.Max;
import javax.validation.constraints.NotNull;

/**
 * @Description QueryDisplayDTO
 * <AUTHOR>
 * @Date 2024/7/15
 **/
@Data
public class QueryDisplayDTO {

    @ApiModelProperty(value = "页码")
    @NotNull(message = "分页参数,不能为空")
    private Integer pageNum;

    @ApiModelProperty(value = "每页大小")
    @NotNull(message = "分页参数,不能为空")
    @Max(value = 1000, message = "最大页数不能超过1000")
    private Integer pageSize;


    @ApiModelProperty(value = "门店编码")
    private String shopCode;
}
