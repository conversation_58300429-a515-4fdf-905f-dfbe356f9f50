package com.rome.stock.innerservice.api.controller;

import com.alibaba.fastjson.JSON;
import com.github.pagehelper.PageInfo;
import com.rome.arch.core.clientobject.Response;
import com.rome.arch.core.exception.RomeException;
import com.rome.arch.util.controller.RomeController;
import com.rome.stock.innerservice.api.dto.CloudShopDTO;
import com.rome.stock.innerservice.api.dto.FrSaleSupplierDTO;
import com.rome.stock.innerservice.constant.ResponseMsg;
import com.rome.stock.innerservice.domain.service.StockService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiResponse;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;

import javax.annotation.Resource;

/**
 * 云店
 */
@Slf4j
@RomeController
@RequestMapping("/stock/v1/cloudShop")
@Api(tags = {"云店服务接口"})
public class CloudShopController {

    @Resource
    private StockService stockService;


    /**
     * 云店集单
     * 标签类型(1.门店自提，2.仓库次日达，3.供应商外卖，4.供应商送门店)
     * @return
     */
    @ApiOperation(value = "云店集单", nickname = "mergeCloudShopRecord", produces = MediaType.APPLICATION_JSON_VALUE)
    @ApiResponse(code = 200, message = "success", response = String.class)
    @PostMapping("/mergeCloudShopRecord")
    public Response mergeCloudShopRecord(@RequestBody CloudShopDTO cloudShopDTO) {
        try {
            log.info("云店集单参数："+JSON.toJSONString(cloudShopDTO));
            stockService.mergeCloudShopRecord(cloudShopDTO);
            return ResponseMsg.SUCCESS.buildMsg();
        } catch (RomeException e) {
            log.error(e.getMessage(), e);
            return ResponseMsg.FAIL.buildMsg(e.getCode(), e.getMessage());
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return ResponseMsg.FAIL.buildMsg("-500", e.getMessage());
        }
    }


    /**
     * 云店供应商补货汇总
     * @return
     */
    @ApiOperation(value = "云店供应商补货汇总", nickname = "collectSupplierRecord", produces = MediaType.APPLICATION_JSON_VALUE)
    @ApiResponse(code = 200, message = "success", response = String.class)
    @PostMapping("/collectSupplierRecord")
    public Response collectSupplierRecord(@RequestBody CloudShopDTO cloudShopDTO) {
        try {
            log.info("云店供应商补货汇总："+JSON.toJSONString(cloudShopDTO));
            stockService.collectSupplierRecord(cloudShopDTO);
            return ResponseMsg.SUCCESS.buildMsg();
        } catch (RomeException e) {
            log.error(e.getMessage(), e);
            return ResponseMsg.FAIL.buildMsg(e.getCode(), e.getMessage());
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return ResponseMsg.FAIL.buildMsg("-500", e.getMessage());
        }
    }


    /**
     * 云店供应商补货汇总
     * @return
     */
    @ApiOperation(value = "云店供应商补货汇总查询", nickname = "queryCollectSupplierRecordByCondition", produces = MediaType.APPLICATION_JSON_VALUE)
    @ApiResponse(code = 200, message = "success", response = String.class)
    @PostMapping("/queryCollectSupplierRecordByCondition")
    public Response<PageInfo<FrSaleSupplierDTO>> queryCollectSupplierRecordByCondition(@RequestBody FrSaleSupplierDTO frSaleSupplierDTO) {
        try {
            PageInfo<FrSaleSupplierDTO> res=stockService.queryCollectSupplierRecordByCondition(frSaleSupplierDTO);
            return ResponseMsg.SUCCESS.buildMsg(res);
        } catch (RomeException e) {
            log.error(e.getMessage(), e);
            return ResponseMsg.FAIL.buildMsg(e.getCode(), e.getMessage());
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return ResponseMsg.FAIL.buildMsg("-500", e.getMessage());
        }
    }

}
