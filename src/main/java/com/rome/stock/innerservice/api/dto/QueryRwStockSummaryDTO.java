package com.rome.stock.innerservice.api.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotEmpty;
import java.math.BigDecimal;
import java.util.List;

/**
 * 查询实时库存DTO对象
 * <AUTHOR>
 */
@Data
public class QueryRwStockSummaryDTO {

    @ApiModelProperty(value = "门店编号",required = true)
    @NotEmpty(message = "门店编号列表不可为空")
    private List<String> shopCodes;

    @ApiModelProperty(value = "商品编码")
    private List<String> skuCodes;

    @ApiModelProperty("省份code")
    private String provinceCode;

    @ApiModelProperty("城市code")
    private String cityCode;

    @ApiModelProperty("区县code")
    private String countyCode;

    @ApiModelProperty("库存数量最小值")
    private BigDecimal minQty;

    @ApiModelProperty("库存数量最大值")
    private BigDecimal maxQty;

}    
   