/**
 * Filename WmsStockCheckController.java
 * Company 上海来伊份科技有限公司。
 * <AUTHOR>
 * @version 
 */
package com.rome.stock.innerservice.api.controller;

import com.github.pagehelper.PageInfo;
import com.rome.stock.innerservice.api.dto.RealWarehouse;
import com.rome.stock.innerservice.api.dto.WarehouseRecordDTO;
import com.rome.stock.innerservice.api.dto.WmsStockCheckFlowDTO;
import com.rome.stock.innerservice.domain.convertor.RealWarehouseConvertor;
import com.rome.stock.innerservice.domain.convertor.WarehouseRecordConvertor;
import com.rome.stock.innerservice.domain.repository.RealWarehouseRepository;
import com.rome.stock.innerservice.domain.repository.warehouserecord.WarehouseRecordRepository;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.*;

import com.alibaba.fastjson.JSONObject;
import com.rome.arch.core.clientobject.Response;
import com.rome.arch.core.exception.RomeException;
import com.rome.arch.util.controller.RomeController;
import com.rome.stock.innerservice.common.ResCode;
import com.rome.stock.innerservice.constant.ResponseMsg;
import com.rome.stock.innerservice.domain.service.WmsStockCheckService;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiResponse;
import lombok.extern.slf4j.Slf4j;

import java.util.List;

/**
 * wms库存核对
 * <AUTHOR>
 * @since 2019年7月7日 下午8:42:19
 */
@Slf4j
@RomeController
@RequestMapping("/stock/v1/wms_stock_check")
@Api(tags={"wms库存核对"})
public class WmsStockCheckController {

	@Autowired
	private WmsStockCheckService wmsStockCheckService;

	@Autowired
    private RealWarehouseRepository realWarehouseRepository;

	@Autowired
    private WarehouseRecordRepository warehouseRecordRepository;

	@Autowired
    private WarehouseRecordConvertor warehouseRecordConvertor;

	@Autowired
    private RealWarehouseConvertor realWarehouseConvertor;

	@ApiOperation(value = "wms库存核对-现在是大福和SAP系统仓", nickname = "wmsStockCheckJob", produces = MediaType.APPLICATION_JSON_VALUE)
    @ApiResponse(code = 200, message = "success")
    @RequestMapping(value = "/wmsStockCheckJob", method = RequestMethod.POST)
	public Response wmsStockCheckJob(@RequestBody JSONObject requestVO) {
		try {
			wmsStockCheckService.wmsStockCheckJob();
            return ResponseMsg.SUCCESS.buildMsg(null);
        } catch (RomeException e) {
            log.error(e.getMessage(), e);
            return ResponseMsg.FAIL.buildMsg(e.getCode(),e.getMessage());
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return ResponseMsg.EXCEPTION.buildMsg(ResCode.STOCK_ERROR_1003_DESC,ResCode.STOCK_ERROR_1003_DESC);
        }
	}

    @ApiOperation(value = "根据条件查询wms库存核对表", nickname = "queryWmsStockCheckByCondition", produces = MediaType.APPLICATION_JSON_VALUE)
    @ApiResponse(code = 200, message = "success")
    @RequestMapping(value = "/queryWmsStockCheckByCondition", method = RequestMethod.POST)
    public Response<PageInfo<WmsStockCheckFlowDTO>> queryWmsStockCheckByCondition(@RequestBody WmsStockCheckFlowDTO paramDto) {
        try {
            PageInfo<WmsStockCheckFlowDTO> dtoList = wmsStockCheckService.queryWmsStockCheckByCondition(paramDto);
            return ResponseMsg.SUCCESS.buildMsg(dtoList);
        } catch (RomeException e) {
            log.error(e.getMessage(), e);
            return ResponseMsg.FAIL.buildMsg(e.getCode(),e.getMessage());
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return ResponseMsg.EXCEPTION.buildMsg(e.getMessage());
        }
    }

    @ApiOperation(value = "根据工厂和单据类型查询仓库信息", nickname = "queryRealWarehouseByFactoryCode", produces = MediaType.APPLICATION_JSON_VALUE)
    @ApiResponse(code = 200, message = "success")
    @RequestMapping(value = "/queryWmsRealWarehouseByFactoryCode", method = RequestMethod.GET)
    public Response<List<RealWarehouse>> queryWmsRealWarehouseByFactoryCode(@RequestParam("factoryCode") String factoryCode){
        try {
            List<RealWarehouse> warehouses = wmsStockCheckService.queryWmsRealWarehouseByFactoryCode(factoryCode);
            return ResponseMsg.SUCCESS.buildMsg(warehouses);
        } catch (RomeException e) {
            log.error(e.getMessage(), e);
            return ResponseMsg.FAIL.buildMsg(e.getCode(),e.getMessage());
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return ResponseMsg.FAIL.buildMsg("500000",e.getMessage());
        }
    }
    
    @ApiOperation(value = "根据仓库Id重新同步wms库存核对表,一般是手动", nickname = "wmsStockCheckSync", produces = MediaType.APPLICATION_JSON_VALUE)
    @ApiResponse(code = 200, message = "success")
    @RequestMapping(value = "/wmsStockCheckSync", method = RequestMethod.POST)
    public Response<PageInfo<WmsStockCheckFlowDTO>> wmsStockCheckSync(@RequestBody WmsStockCheckFlowDTO paramDto) {
        try {
        	wmsStockCheckService.wmsStockCheckSync(paramDto);
            return ResponseMsg.SUCCESS.buildMsg();
        } catch (RomeException e) {
            log.error(e.getMessage(), e);
            return ResponseMsg.FAIL.buildMsg(e.getCode(),e.getMessage());
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return ResponseMsg.EXCEPTION.buildMsg(e.getMessage());
        }
    }

    @ApiOperation(value = "根据仓库Id查询仓库信息", nickname = "getRealWarehouseById", produces = MediaType.APPLICATION_JSON_VALUE)
    @ApiResponse(code = 200, message = "success")
    @RequestMapping(value = "/getRealWarehouseById", method = RequestMethod.POST)
    public Response<RealWarehouse> getRealWarehouseById(@RequestParam(value = "realWarehouseId") Long realWarehouseId) {
        try {
            return ResponseMsg.SUCCESS.buildMsg(realWarehouseConvertor.entityToDto(realWarehouseRepository.getRealWarehouseById(realWarehouseId)));
        } catch (RomeException e) {
            log.error(e.getMessage(), e);
            return ResponseMsg.FAIL.buildMsg(e.getCode(),e.getMessage());
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return ResponseMsg.EXCEPTION.buildMsg(e.getMessage());
        }
    }

    @ApiOperation(value = "根据单据编号查询仓库信息", nickname = "getWarehouseRecordByRecordCode", produces = MediaType.APPLICATION_JSON_VALUE)
    @ApiResponse(code = 200, message = "success")
    @RequestMapping(value = "/getWarehouseRecordByRecordCode", method = RequestMethod.POST)
    public Response<WarehouseRecordDTO> getWarehouseRecordByRecordCode(@RequestParam(value = "recordCode") String recordCode) {
        try {
            return ResponseMsg.SUCCESS.buildMsg(warehouseRecordConvertor.entityToDto(warehouseRecordRepository.queryWarehouseRecordByRecordCode(recordCode)));
        } catch (RomeException e) {
            log.error(e.getMessage(), e);
            return ResponseMsg.FAIL.buildMsg(e.getCode(),e.getMessage());
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return ResponseMsg.EXCEPTION.buildMsg(e.getMessage());
        }
    }
}
