package com.rome.stock.innerservice.api.controller;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;

import com.github.pagehelper.PageInfo;
import com.rome.arch.core.clientobject.Response;
import com.rome.arch.core.exception.RomeException;
import com.rome.arch.util.controller.RomeController;
import com.rome.stock.innerservice.api.dto.RealWarehouse;
import com.rome.stock.innerservice.api.dto.cloudshop.RwSkuServiceLabDTO;
import com.rome.stock.innerservice.common.ResCode;
import com.rome.stock.innerservice.constant.RealWarehouseTypeVO;
import com.rome.stock.innerservice.constant.ResponseMsg;
import com.rome.stock.innerservice.domain.service.RealWarehouseService;
import com.rome.stock.innerservice.domain.service.RwSkuServiceLabService;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiResponse;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@RomeController
@RequestMapping("/stock/v1/rw_sku_service_lab")
@Api(tags={"云店仓库sku时效服务标签接口"})
public class RwSkuServiceLabController {

    @Autowired
	private RwSkuServiceLabService rwSkuServiceLabService;
    
    @Autowired
    private RealWarehouseService realWarehouseService;
	
    @ApiOperation(value = "查询所有云店供应商仓", nickname = "getAllCloudWarehouse", produces = MediaType.APPLICATION_JSON_VALUE)
    @ApiResponse(code = 200, message = "success")
    @RequestMapping(value = "/getAllCloudWarehouse", method = RequestMethod.GET)
    public Response<List<RealWarehouse>> getAllCloudWarehouse() {
        try {
            List<Integer> typeList=new ArrayList<>();
            typeList.add(RealWarehouseTypeVO.RW_TYPE_1.getType());
            typeList.add(RealWarehouseTypeVO.CLOUD_SUPPLIER_TYPE_29.getType());
            List<RealWarehouse> realWarehouses = realWarehouseService.queryRealWarehouseByFactoryCodeAndRWType(null,typeList);
            return Response.builderSuccess(realWarehouses);
        } catch (RomeException e) {
            log.error(e.getMessage(), e);
            return ResponseMsg.EXCEPTION.buildMsg(e.getCode(), e.getMessage());
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return ResponseMsg.EXCEPTION.buildMsg(ResCode.STOCK_ERROR_1003,  e.getMessage());
        }
    }

    @ApiOperation(value = "查询云店仓库sku时效服务标签", nickname = "queryRwSkuServiceLab", produces = MediaType.APPLICATION_JSON_VALUE)
    @ApiResponse(code = 200, message = "success")
    @RequestMapping(value = "/queryRwSkuServiceLab", method = RequestMethod.POST)
    public Response<PageInfo<RwSkuServiceLabDTO>> queryRwSkuServiceLab(@RequestBody RwSkuServiceLabDTO paramDto) {
        try {
            PageInfo<RwSkuServiceLabDTO> pageList = rwSkuServiceLabService.queryRwSkuServiceLab(paramDto);
            return Response.builderSuccess(pageList);
        } catch (RomeException e) {
            log.error(e.getMessage(), e);
            return ResponseMsg.EXCEPTION.buildMsg(e.getCode(), e.getMessage());
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return ResponseMsg.EXCEPTION.buildMsg(ResCode.STOCK_ERROR_1003,  e.getMessage());
        }
    }
    
    @ApiOperation(value = "更新状态", nickname = "updateStatus", produces = MediaType.APPLICATION_JSON_VALUE)
    @ApiResponse(code = 200, message = "success", response =Response.class)
    @RequestMapping(value = "/updateStatus", method = RequestMethod.POST)
    public Response updateIsAvailable(@RequestBody List<RwSkuServiceLabDTO> list){
        try{
        	rwSkuServiceLabService.updateIsAvailable(list);
            return Response.builderSuccess(null);
        }catch (RomeException e){
            log.error(e.getMessage(), e);
            return ResponseMsg.EXCEPTION.buildMsg(e.getCode(), e.getMessage());
        }catch (Exception e){
            log.error(e.getMessage(), e);
            return ResponseMsg.EXCEPTION.buildMsg(ResCode.STOCK_ERROR_1003,  e.getMessage());
        }
    }
    
    @ApiOperation(value = "导入excel", nickname = "importExcel", produces = MediaType.APPLICATION_JSON_VALUE)
    @ApiResponse(code = 200, message = "success", response = List.class)
    @RequestMapping(value = "/importExcel", method = RequestMethod.POST)
    public Response importExcel(@RequestBody List<RwSkuServiceLabDTO> list){
        try {
            String result = rwSkuServiceLabService.importExcel(list);
            return Response.builderSuccess(result);
        } catch (RomeException e) {
            log.error(e.getMessage(), e);
            return ResponseMsg.EXCEPTION.buildMsg(e.getCode(), e.getMessage());
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return ResponseMsg.EXCEPTION.buildMsg(ResCode.STOCK_ERROR_1003,  e.getMessage());
        }
    }
    
}
