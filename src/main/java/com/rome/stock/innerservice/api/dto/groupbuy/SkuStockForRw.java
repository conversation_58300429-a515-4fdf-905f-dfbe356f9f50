package com.rome.stock.innerservice.api.dto.groupbuy;

import java.math.BigDecimal;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 商品库存
 */
@Data
@EqualsAndHashCode
public class SkuStockForRw {
    /**
     * 实仓ID
     */
    @ApiModelProperty(value = "实仓ID", name = "realWarehouseId", required=true)
    private Long realWarehouseId;

    @ApiModelProperty(value = "实仓code", name = "realWarehouseCode" )
    private String realWarehouseCode;

    @ApiModelProperty(value = "实仓名称", name = "realWarehouseName" )
    private String realWarehouseName;

    @ApiModelProperty(value = "merchantId,不传将会用默认的商家id", name = "merchantId" )
    private Long merchantId;
    @ApiModelProperty(value = "skuCode")
    private String skuCode;
    @ApiModelProperty(value = "sku真实数量")
    private BigDecimal availableQty;
    @ApiModelProperty(value = "查询单位")
    private String unitCode;
}
