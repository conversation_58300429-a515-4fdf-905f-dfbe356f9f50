package com.rome.stock.innerservice.api.dto.warehouserecord;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

/**
 * @Doc:.....
 * @Author: lchy
 * @Date: 2020/7/15
 * @Version 1.0
 */
@Data
@EqualsAndHashCode
public class BatchCreateRecordDTO {

    @ApiModelProperty(value = "出库单列表", required = true)
    private List<OutWarehouseRecordDTO> outList;
    @ApiModelProperty(value = "入库单列表", required = true)
    private List<InWarehouseRecordDTO> inList;
}
