package com.rome.stock.innerservice.api.controller;

import com.github.pagehelper.PageInfo;
import com.rome.arch.core.clientobject.Response;
import com.rome.arch.core.exception.RomeException;
import com.rome.arch.util.controller.RomeController;
import com.rome.stock.innerservice.api.dto.TradePathConfigDTO;
import com.rome.stock.innerservice.constant.ResponseMsg;
import com.rome.stock.innerservice.domain.service.TradePathConfigService;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiResponse;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;

import javax.annotation.Resource;

/**
 * @Description 交易路径配置
 */
@Slf4j
@RomeController
@RequestMapping("/stock/v1/tradePathConfig")
public class TradePathConfigController {

    @Resource
    private TradePathConfigService tradePathConfigService;

    @ApiOperation(value = "新增或修改", produces = MediaType.APPLICATION_JSON_VALUE)
    @ApiResponse(code = 200, message = "success")
    @PostMapping(value = "/addOrUpdateTradePathConfig")
    public Response addOrUpdateTradePathConfig(@RequestBody TradePathConfigDTO tradePathConfigDTO) {
        try {
            tradePathConfigService.addOrUpdateTradePathConfig(tradePathConfigDTO);
            return ResponseMsg.SUCCESS.buildMsg();
        } catch (RomeException e) {
            log.error(e.getMessage(), e);
            return ResponseMsg.FAIL.buildMsg(e.getCode(), e.getMessage());
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return ResponseMsg.FAIL.buildMsg("500000", e.getMessage());
        }
    }

    @ApiOperation(value = "分页查询交易路径配置", produces = MediaType.APPLICATION_JSON_VALUE)
    @ApiResponse(code = 200, message = "success")
    @PostMapping(value = "/selectByCondition")
    public Response<PageInfo<TradePathConfigDTO>> selectByCondition(@RequestBody TradePathConfigDTO tradePathConfigDTO) {
        try {
            PageInfo<TradePathConfigDTO> pageList = tradePathConfigService.selectByCondition(tradePathConfigDTO);
            return ResponseMsg.SUCCESS.buildMsg(pageList);
        } catch (RomeException e) {
            log.error(e.getMessage(), e);
            return ResponseMsg.FAIL.buildMsg(e.getCode(), e.getMessage());
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return ResponseMsg.FAIL.buildMsg("500000", e.getMessage());
        }
    }

    @ApiOperation(value = "删除交易路径配置", produces = MediaType.APPLICATION_JSON_VALUE)
    @ApiResponse(code = 200, message = "success")
    @PostMapping(value = "/deleteById")
    public Response deleteById(@RequestBody TradePathConfigDTO tradePathConfigDTO) {
        try {
            tradePathConfigService.deleteById(tradePathConfigDTO.getId());
            return ResponseMsg.SUCCESS.buildMsg();
        } catch (RomeException e) {
            log.error(e.getMessage(), e);
            return ResponseMsg.FAIL.buildMsg(e.getCode(), e.getMessage());
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return ResponseMsg.FAIL.buildMsg("500000", e.getMessage());
        }
    }

}
