package com.rome.stock.innerservice.api.dto.cmp7;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @description 门店收货对接cmp7明细对象
 * @date 2020/11/12 10:03
 * @throw
 */
@Data
public class ShopReceiptDetailCmp7DTO {

    /**
     * 交货单号
     */
    private String billNo;

    /**
     * 交货日期 格式 yyyy-MM-dd
     */
    private String billDate;

    /**
     * 门店编码
     */
    private String orgCode;

    /**
     * 采购行号
     */
    private String serialNo;

    /**
     * 商品编码
     */
    private String pluCode;

    /**
     * 商品名称
     */
    private String pluName;

    /**
     * 叫货数量
     */
    private BigDecimal quantity;

    /**
     * 单位
     */
    private String unit;

    /**
     * 单位编码
     */
    private String unitCode;

    /**
     * 无税单价
     */
    private BigDecimal price;

    /**
     * 无税金额
     */
    private BigDecimal total;

    /**
     * 税率
     */
    private BigDecimal taxRate;

    /**
     *含税单价
     */
    private BigDecimal taxPrice;

    /**
     * 含税金额
     */
    private BigDecimal taxTotal;

    /**
     * 批次号
     */
    private String batchNo;


    @JSONField(serialize = false)
    private Long detailId;




}    
   