package com.rome.stock.innerservice.api.controller;

import com.github.pagehelper.PageInfo;
import com.rome.arch.core.clientobject.Response;
import com.rome.arch.core.exception.RomeException;
import com.rome.arch.util.controller.RomeController;
import com.rome.stock.innerservice.api.dto.MonitorConfigDTO;
import com.rome.stock.innerservice.common.ResCode;
import com.rome.stock.innerservice.constant.ResponseMsg;
import com.rome.stock.innerservice.domain.service.MonitorConfigService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiResponse;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.Map;

@Slf4j
@RomeController
@RequestMapping("/stock/v1/monitorConfig")
@Api(tags={"监控可视化配置接口"})
public class MonitorConfigController {

    @Resource
	private MonitorConfigService monitorConfigService;


    @ApiOperation(value = "分页查询列表数据", nickname = "queryPage", produces = MediaType.APPLICATION_JSON_VALUE)
    @ApiResponse(code = 200, message = "success")
    @RequestMapping(value = "/queryPage", method = RequestMethod.POST)
    public Response queryPage(@RequestBody MonitorConfigDTO monitorConfigDTO) {
        try {
            PageInfo<MonitorConfigDTO> page = monitorConfigService.queryPage(monitorConfigDTO);
            return ResponseMsg.SUCCESS.buildMsg(page);
        } catch (RomeException e) {
            log.error(e.getMessage(), e);
            return ResponseMsg.FAIL.buildMsg(e.getCode(),e.getMessage());
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return ResponseMsg.EXCEPTION.buildMsg(ResCode.STOCK_ERROR_1003_DESC,ResCode.STOCK_ERROR_1003_DESC + e.getMessage());
        }
    }

    @ApiOperation(value = "查询POOL列表", nickname = "listPools", produces = MediaType.APPLICATION_JSON_VALUE)
    @ApiResponse(code = 200, message = "success", response = Map.class)
    @GetMapping(value = "/listPools")
    public Response listPools() {
        try {
            return ResponseMsg.SUCCESS.buildMsg(monitorConfigService.listPools());
        } catch (RomeException e) {
            log.error(e.getMessage(), e);
            return ResponseMsg.FAIL.buildMsg(e.getCode(),e.getMessage());
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return ResponseMsg.EXCEPTION.buildMsg(ResCode.STOCK_ERROR_1003_DESC,ResCode.STOCK_ERROR_1003_DESC + e.getMessage());
        }
    }

    @ApiOperation(value = "新增或修改", nickname = "saveOrUpdate", produces = MediaType.APPLICATION_JSON_VALUE)
    @ApiResponse(code = 200, message = "success")
    @RequestMapping(value = "/saveOrUpdate", method = RequestMethod.POST)
    public Response saveOrUpdate(@RequestBody MonitorConfigDTO monitorConfigDTO) {
        try {
            monitorConfigService.saveOrUpdate(monitorConfigDTO);
            return ResponseMsg.SUCCESS.buildMsg("");
        } catch (RomeException e) {
            log.error(e.getMessage(), e);
            return ResponseMsg.FAIL.buildMsg(e.getCode(),e.getMessage());
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return ResponseMsg.EXCEPTION.buildMsg(ResCode.STOCK_ERROR_1003_DESC,ResCode.STOCK_ERROR_1003_DESC + e.getMessage());
        }
    }

    @ApiOperation(value = "获取Pool信息", nickname = "getPoolByCallModeAndPool", produces = MediaType.APPLICATION_JSON_VALUE)
    @ApiResponse(code = 200, message = "success", response = MonitorConfigDTO.class)
    @GetMapping("/getPoolByCallModeAndPool")
    public Response<MonitorConfigDTO> getPoolByCallModeAndPool(
            @RequestParam("domain") String domain,@RequestParam("callMode") String callMode,
            @RequestParam("poolName") String poolName) {
        try {
            MonitorConfigDTO configDTO=monitorConfigService.getPoolByCallModeAndPool(domain,callMode,poolName);
            return ResponseMsg.SUCCESS.buildMsg(configDTO);
        } catch (RomeException e) {
            log.error(e.getMessage(), e);
            return ResponseMsg.FAIL.buildMsg(e.getCode(), e.getMessage());
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return ResponseMsg.FAIL.buildMsg(ResCode.STOCK_ERROR_1017, e.getMessage());
        }
    }

    @ApiOperation(value = "删除", nickname = "delete", produces = MediaType.APPLICATION_JSON_VALUE)
    @ApiResponse(code = 200, message = "success")
    @RequestMapping(value = "/delete", method = RequestMethod.POST)
    public Response delete(@RequestBody MonitorConfigDTO monitorConfigDTO) {
        try {
           monitorConfigService.delete(monitorConfigDTO);
            return ResponseMsg.SUCCESS.buildMsg("");
        } catch (RomeException e) {
            log.error(e.getMessage(), e);
            return ResponseMsg.FAIL.buildMsg(e.getCode(),e.getMessage());
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return ResponseMsg.EXCEPTION.buildMsg(ResCode.STOCK_ERROR_1003_DESC,ResCode.STOCK_ERROR_1003_DESC + e.getMessage());
        }
    }



}
