/**
 * Filename WarehouseStockRecordCheckController.java
 * Company 上海来伊份科技有限公司。
 * <AUTHOR>
 * @version 
 */
package com.rome.stock.innerservice.api.controller;

import com.rome.arch.core.clientobject.Response;
import com.rome.arch.core.exception.RomeException;
import com.rome.arch.util.controller.RomeController;
import com.rome.stock.common.utils.ListUtil;
import com.rome.stock.innerservice.common.ResCode;
import com.rome.stock.innerservice.domain.service.WarehouseStockRecordCheckService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import io.swagger.annotations.ApiResponse;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.Date;
import java.util.List;

/**
 * 仓库库存单据核对
 * <AUTHOR>
 * @since 2020-12-23 16:50:32
 */
@Slf4j
@RomeController
@RequestMapping("/stock/v1/warehouse_stock_record_check")
@Api(tags={"仓库库存单据核对"})
public class WarehouseStockRecordCheckController {
	
	@Autowired
    private WarehouseStockRecordCheckService warehouseStockRecordCheckService;
	
	@ApiOperation(value = "核对数据，根据单据编码", nickname = "checkByRecordCodeList", produces = MediaType.APPLICATION_JSON_VALUE)
    @ApiResponse(code = 200, message = "success", response = Integer.class)
    @RequestMapping(value = "/checkByRecordCodeList", method = RequestMethod.POST)
    public Response<Integer> checkByRecordCodeList(@RequestBody List<String> recordCodeList){
        try{
        	if(recordCodeList == null || recordCodeList.size() > 100000) {
        		return Response.builderFail(ResCode.STOCK_ERROR_1002, ResCode.STOCK_ERROR_1002_DESC + "单据号个数不能为空或超过100000");
        	}
        	int num = 0;
        	int maxPage = ListUtil.getPageNum(recordCodeList, 50);
	        for(int i = 1; i <= maxPage; i++) {
	        	num += warehouseStockRecordCheckService.checkByRecordCodeList(ListUtil.getPageList(recordCodeList, i,50));
	        }
            return Response.builderSuccess(num);
        }catch (RomeException e){
            log.error(e.getMessage(),e);
            return Response.builderFail(e.getCode(),e.getMessage());
        }catch (Exception e){
            log.error(e.getMessage(), e);
            return Response.builderFail(ResCode.STOCK_ERROR_1003,ResCode.STOCK_ERROR_1003_DESC);
        }
    }

	@ApiOperation(value = "物理删除，根据单据号", nickname = "delByRecordCodes", produces = MediaType.APPLICATION_JSON_VALUE)
    @ApiResponse(code = 200, message = "success", response = Integer.class)
    @RequestMapping(value = "delByRecordCodes", method = RequestMethod.DELETE)
    public Response<Integer> delByRecordCodes(@RequestBody List<String> recordCodeList){
        try{
        	if(recordCodeList == null || recordCodeList.size() > 100000) {
        		return Response.builderFail(ResCode.STOCK_ERROR_1002, ResCode.STOCK_ERROR_1002_DESC + "单据号个数不能为空或超过100000");
        	}
            int num = warehouseStockRecordCheckService.delByRecordCodes(recordCodeList);
            return Response.builderSuccess(num);
        }catch (RomeException e){
            log.error(e.getMessage(),e);
            return Response.builderFail(e.getCode(),e.getMessage());
        }catch (Exception e){
            log.error(e.getMessage(), e);
            return Response.builderFail(ResCode.STOCK_ERROR_1003,ResCode.STOCK_ERROR_1003_DESC);
        }
    }
	
	@ApiOperation(value = "物理删除，根据创建时间和状态", nickname = "delByCreateTimeAndStatus", produces = MediaType.APPLICATION_JSON_VALUE)
    @ApiResponse(code = 200, message = "success", response = Integer.class)
    @RequestMapping(value = "delByCreateTimeAndStatus", method = RequestMethod.DELETE)
    public Response<Integer> delByCreateTimeAndStatus(
    		@ApiParam(name = "endTime", value = "endTime", defaultValue = "2021-01-01 00:00:00") @RequestBody Date endTime,
            @ApiParam(name = "status", value = "status 状态： 1：正常 0：异常", defaultValue = "1") @RequestParam("status") Integer status){
        try{
            int num = warehouseStockRecordCheckService.delByCreateTimeAndStatus(endTime, status);
            return Response.builderSuccess(num);
        }catch (RomeException e){
            log.error(e.getMessage(),e);
            return Response.builderFail(e.getCode(),e.getMessage());
        }catch (Exception e){
            log.error(e.getMessage(), e);
            return Response.builderFail(ResCode.STOCK_ERROR_1003,ResCode.STOCK_ERROR_1003_DESC);
        }
    }
	
	@ApiOperation(value = "更新核对创建时间，根据单据编码", nickname = "updateCheckCreateTimeByRecordCodeList", produces = MediaType.APPLICATION_JSON_VALUE)
    @ApiResponse(code = 200, message = "success", response = Integer.class)
    @RequestMapping(value = "/updateCheckCreateTimeByRecordCodeList", method = RequestMethod.POST)
    public Response<Integer> updateCheckCreateTimeByRecordCodeList(@RequestBody List<String> recordCodeList){
        try{
        	if(recordCodeList == null || recordCodeList.size() > 200000) {
        		return Response.builderFail(ResCode.STOCK_ERROR_1002, ResCode.STOCK_ERROR_1002_DESC + "单据号个数不能为空或超过200000");
        	}
        	int num = 0;
        	int maxPage = ListUtil.getPageNum(recordCodeList, 1000);
	        for(int i = 1; i <= maxPage; i++) {
	        	num += warehouseStockRecordCheckService.updateCheckCreateTimeByRecordCodeList(ListUtil.getPageList(recordCodeList, i, 1000));
	        }
            return Response.builderSuccess(num);
        }catch (RomeException e){
            log.error(e.getMessage(),e);
            return Response.builderFail(e.getCode(),e.getMessage());
        }catch (Exception e){
            log.error(e.getMessage(), e);
            return Response.builderFail(ResCode.STOCK_ERROR_1003,ResCode.STOCK_ERROR_1003_DESC);
        }
    }
	
	@ApiOperation(value = "在其他pool中，不存在的单据，检查根据单据号", nickname = "existCheckOtherPoolByRecordCodeList", produces = MediaType.APPLICATION_JSON_VALUE)
    @ApiResponse(code = 200, message = "success", response = Integer.class)
    @RequestMapping(value = "existCheckOtherPoolByRecordCodeList", method = RequestMethod.POST)
    public Response<String> existCheckOtherPoolByRecordCodeList(
    		@ApiParam(name = "type", value = "类： 1：表sc_warehouse_record单据 2：表sc_warehouse_stock_record_check单据电商", defaultValue = "1") @RequestParam("type") Integer type, @RequestBody List<String> recordCodeList){
        try{
        	if(recordCodeList == null || recordCodeList.size() > 100000) {
        		return Response.builderFail(ResCode.STOCK_ERROR_1002, ResCode.STOCK_ERROR_1002_DESC + "单据号个数不能为空或超过100000");
        	}
        	int num = 0;
        	int maxPage = ListUtil.getPageNum(recordCodeList, 1000);
	        for(int i = 1; i <= maxPage; i++) {
	        	num += warehouseStockRecordCheckService.existCheckOtherPoolByRecordCodeList(type, ListUtil.getPageList(recordCodeList, i,1000));
	        }
            return Response.builderSuccess(num > 0 ? "成功，提示：实际单据数与所传单据数对不上：" + num : "成功");
        }catch (RomeException e){
            log.error(e.getMessage(),e);
            return Response.builderFail(e.getCode(),e.getMessage());
        }catch (Exception e){
            log.error(e.getMessage(), e);
            return Response.builderFail(ResCode.STOCK_ERROR_1003,ResCode.STOCK_ERROR_1003_DESC);
        }
    }
	
}
