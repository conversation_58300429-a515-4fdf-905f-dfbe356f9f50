/**
 * Filename KpChannelVwTypeRelationDTO.java
 * Company 上海来伊份科技有限公司。
 * <AUTHOR>
 * @version 
 */
package com.rome.stock.innerservice.api.dto;


import java.util.Date;
import java.util.List;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

/**
 * 鲲鹏渠道与虚仓类型关系DTO
 * <AUTHOR>
 * @since 2022-7-11 14:58:02
 */
@Data
@NoArgsConstructor
@EqualsAndHashCode
public class KpChannelVwTypeRelationDTO extends Pagination {

	@ApiModelProperty("唯一主键")
    private Long id;
	
	@ApiModelProperty("鲲鹏渠道code")
	private String kpChannelCode;
	
	@ApiModelProperty("鲲鹏渠道名称")
	private String kpChannelName;
	
	@ApiModelProperty("虚仓类型")
	private Integer virtualWarehouseType;
	
	@ApiModelProperty(value = "虚拟仓库类型名称")
	private String virtualWarehouseTypeName;

	@ApiModelProperty(value = "特殊标签")
	private Integer tag;

	@ApiModelProperty(value = "不可共享虚仓标签")
	private List<String> noShareTypes;

	@ApiModelProperty(value = "不可共享虚仓标签",hidden = true)
	private String noShareType;

	@ApiModelProperty("备注")
    private String remark;

	@ApiModelProperty("批次库存剩余天数配置")
	private Integer batchRemainExpireDay;

	@ApiModelProperty("生产日期与不可发货日期配置")
	private String validityConfigJson;
	
	@ApiModelProperty(value = "创建时间")
    private Date createTime;
	
	@ApiModelProperty(value = "修改时间")
    private Date updateTime;
	
	@ApiModelProperty(value = "创建人")
    private Long creator;
	
	@ApiModelProperty(value = "更新人")
    private Long modifier;
	
	@ApiModelProperty("虚仓类型列表")
	private List<Integer> virtualWarehouseTypeList;
	
	@ApiModelProperty("鲲鹏渠道code列表")
	private List<String> kpChannelCodeList;
}
