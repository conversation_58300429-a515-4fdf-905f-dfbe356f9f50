package com.rome.stock.innerservice.api.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

@Data
@NoArgsConstructor
@EqualsAndHashCode
@ApiModel(value = "酒附加信息", description = "酒附加信息")
public class SaleWineInfoDTO {
    @ApiModelProperty(value = "主键ID", hidden = true)
    private Long id;

    @ApiModelProperty(value = "单据唯一编码", hidden = true)
    private String recordCode;

    @ApiModelProperty(value = "酒订单交易单号")
    private String outRecordCode;

    @ApiModelProperty(value = "酒sku编码")
    private String skuCode;

    @ApiModelProperty(value = "桶skuCode")
    private String bucketSkuCode;

    @ApiModelProperty(value = "酒桶序列号")
    private String serialNo;

    @ApiModelProperty(value = "桶类型")
    private String bucketType;

    @ApiModelProperty(value = "桶类型名称")
    private String bucketSkuName;

    @ApiModelProperty(value = "酒精度数")
    private String vol;

    @ApiModelProperty(value = "当前剩余酒的体积")
    private String volume;

    @ApiModelProperty(value = "当前剩余酒的体积单位")
    private String volumeUnit;

    @ApiModelProperty(value = "开始熟成时间")
    private Date startRipeTime;

    @ApiModelProperty(value = "熟成要求天数")
    private int needRipeDays;

    @ApiModelProperty(value = "抽样时间")
    private Date sampleTime;

    @ApiModelProperty(value = "酒数量")
    private Integer skuQty;

    @ApiModelProperty(value = "酒单位")
    private String unit;

    @ApiModelProperty(value = "酒单位编码")
    private String unitCode;

    @ApiModelProperty(value = "附件地址列表")
    private List<FileInfo> attachments;

    @ApiModelProperty(value = "备注")
    private String remark;

    @ApiModelProperty(value = "创建时间")
    private Date createTime;

    @ApiModelProperty(value = "更新时间")
    private Date updateTime;

    @ApiModelProperty(value = "创建人")
    private String creator;

    @ApiModelProperty(value = "更新人")
    private String modifier;
}
