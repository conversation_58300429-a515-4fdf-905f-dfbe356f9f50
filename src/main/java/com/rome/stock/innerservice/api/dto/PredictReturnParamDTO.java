package com.rome.stock.innerservice.api.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.math.BigDecimal;

@Data
public class PredictReturnParamDTO implements Serializable {
    /**
     * 物流公司编码
     */
    @ApiModelProperty(value = "退货预入库单号", required = true)
    @NotBlank(message = "退货预入库单号不能为空")
    private String predictRecordCode;
    /**
     * 物流公司编码
     */
    @ApiModelProperty(value = "交易售后单号", required = true)
    @NotBlank(message = "交易售后单号不能为空")
    private String reverseOrderNo;
    /**
     * 运单号
     */
    @ApiModelProperty(value = "运单号", required = true)
    @NotBlank(message = "运单号不能为空")
    private String expressCode;

    /**
     * 物流公司编码
     */
    @ApiModelProperty(value = "物流公司编码", required = true)
    @NotBlank(message = "物流公司编码不能为空")
    private String logisticsCode;

    /**
     * 物流公司编码
     */
    @ApiModelProperty(value = "商品编码", required = true)
    @NotBlank(message = "商品编码不能为空")
    private String skuCode;

    /**
     * 物流公司编码
     */
    @ApiModelProperty(value = "渠道编码", required = true)
    @NotBlank(message = "渠道编码不能为空")
    private String channelCode;

    /**
     * 物流公司编码
     */
    @ApiModelProperty(value = "商品数量", required = true)
    @NotNull(message = "商品数量不能为空")
    private BigDecimal skuQty;

    /**
     * 单位
     */
    @ApiModelProperty(value = "单位，非必填")
    @NotNull(message = "商品单位不能为空")
    private String unit;

    /**
     * 单位编码
     */
    @ApiModelProperty(value = "单位编码，非必填")
    @NotNull(message = "商品单位编码不能为空")
    private String unitCode;

    @ApiModelProperty(value = "前置单id ,内部使用字段调用方无需关心")
    private Long frontRecordId;

    @ApiModelProperty(value = "商品id,非必填")
    private Long skuId;

}
