package com.rome.stock.innerservice.api.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import java.util.Date;

@Data
@NoArgsConstructor
@EqualsAndHashCode
public class FrSaleWineLogParamDTO extends Pagination{

    @ApiModelProperty("酒订单")
    private String frontRecordCode;


    @ApiModelProperty("酒订单附加信息id")
    private Long wineId;

    @ApiModelProperty("付款时间")
    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
    private Date payTime;
}
