package com.rome.stock.innerservice.api.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;

@ApiModel(value = "ChargeAgainstCreateDetailDTO", description = "")
@Data
@EqualsAndHashCode
public class ChargeAgainstCreateDetailDTO {

    /**
     * 商品skuID
     */
    @ApiModelProperty(value = "商品skuID", required = true)
    private Long skuId;

    /**
     * 商品sku编码
     */
    @ApiModelProperty(value = "商品sku编码", required = true)
    private String skuCode;

    /**
     * 计划冲销数量
     */
    @ApiModelProperty(value = "计划冲销数量", required = true)
    private BigDecimal skuQty;

    /**
     * 实际冲销数量
     */
    private BigDecimal accQty;

    /**
     * 单位
     */
    @ApiModelProperty(value = "单位", required = true)
    private String unit;

    /**
     * 单位编码
     */
    @ApiModelProperty(value = "单位编码", required = true)
    private String unitCode;

    /**
     * 批次表id
     */
    @ApiModelProperty(value = "批次表id")
    private Long rwBatchId;

    /**
     * 批次编号
     */
    @ApiModelProperty(value = "批次编号")
    private String batchCode;
    /**
     * 原出入库单明细id
     */
    @ApiModelProperty(value = "原出入库单明细id", required = true)
    private Long refDetailId;

    /**
     * 备注
     */
    @ApiModelProperty(value = "备注")
    private String remark;

    @ApiModelProperty(value = "skuName")
    private String skuName;
    /**
     * 是否已冲销 0--未操作 1--已操作 2--不支持冲销（质检不合格）
     */
    private Integer isReverse;

}
