/**
 * Filename RecordRelationResultDTO.java
 * Company 上海来伊份科技有限公司。
 *
 * <AUTHOR>
 * @version
 */
package com.rome.stock.innerservice.api.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * 关联关系结果查询
 * <AUTHOR>
 * @since 2024/7/3 14:44
 */
@Data
public class RecordRelationResultDTO implements Serializable {

    /**
     * 关联单据编码
     */
    @ApiModelProperty(value = "关联单据编码")
    private String relationRecordCode;

    /**
     * 关联单号类型
     */
    @ApiModelProperty(value = "关联单号类型")
    private Integer type;

    /**
     * 类型描述
     */
    @ApiModelProperty(value = "类型描述")
    private String typeDesc;

}
