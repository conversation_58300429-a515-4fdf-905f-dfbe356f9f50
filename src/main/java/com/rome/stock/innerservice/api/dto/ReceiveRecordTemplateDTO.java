package com.rome.stock.innerservice.api.dto;

import lombok.Data;

import java.io.Serializable;

/**
 * @Description: 领用导入模板字段
 * <p>
 * @Author: chuwenchao  2020/5/14
 */
@Data
public class ReceiveRecordTemplateDTO implements Serializable {

    private String receiveCompanyCode;

    private String realWarehouseCode;

    private String reasonCode;

    private String costCenterCode;

    private String receiveDate;

    private String transWay;

    private String applier;

    private String applierMobile;

    private String remark;

    private String approveOACode;

    private String skuCode;

    private String skuQty;

    private String unitCode;

    private String batchRemark;
}
