package com.rome.stock.innerservice.api.dto.cloudshop;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * 类FilterAddrDTO的实现描述：云店筛选地址对象
 *
 * <AUTHOR> 2021/9/26 15:45
 */
@Data
public class FilterAddrDTO {

    /**
     * 地址经纬度
     */
    @ApiModelProperty(value = "地址经纬度")
    private List<LngLatDTO> addrList;

    /**
     * 仓库编码列表
     */
    @ApiModelProperty(value = "仓库编码列表")
    private List<String> rwCodeList;
}
