package com.rome.stock.innerservice.api.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import javax.validation.constraints.NotBlank;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

@Data
public class SecurityWarningDetailDTO  implements Serializable {

    @ApiModelProperty(value="主键")
    private Long id;

    @ApiModelProperty(value="仓库编码")
    private String warehouseCode;

    @ApiModelProperty(value="库存预警ID")
    private Long securityWarningCode;

    @ApiModelProperty(value="商品ID")
    @NotBlank(message = "商品ID不能为空")
    private Long skuId;

    @ApiModelProperty(value="商品编码")
    @NotBlank(message = "商品编码不能为空")
    private String skuCode;

    @ApiModelProperty(value="商品名称")
    @NotBlank(message = "商品名称不能为空")
    private String skuName;

    @ApiModelProperty(value="预警上限数量")
    @NotBlank(message = "预警上限数量不能为空")
    private BigDecimal upperLimitQuantity;

    @ApiModelProperty(value="下限数量")
    private BigDecimal lowerLimitQuantity;

    @ApiModelProperty(value="通知类型(0短信 1--邮件 2-微信）")
    private Integer notifyType;


    @ApiModelProperty(value="上限通知状态(0 -未通知 1--已通知)")
    private Integer upperNotifyStatus;

    @ApiModelProperty(value="下限通知状态(0 -未通知 1--已通知)")
    private Integer lowerNotifyStatus;

    @ApiModelProperty(value="创建时间")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;


    @ApiModelProperty(value="修改时间")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date updateTime;


    @ApiModelProperty(value="仓库名称")
    private String warehouseName;



    @ApiModelProperty(value="创建人")
    private Long creator;


    @ApiModelProperty(value="更新人")
    private Long modifier;

    @ApiModelProperty(value="库存预警id集合",hidden = true)
    private List<String> securityWarningCodeList;



}