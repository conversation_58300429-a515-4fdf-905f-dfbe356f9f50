package com.rome.stock.innerservice.api.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;
import java.util.List;

@Data
@EqualsAndHashCode
public class WarehouseOutNotifyDTO {

    /**
     * 单据Id
     * */
    private Long id;

    /**
     * 单据编码
     */
    @ApiModelProperty(value = "渠道编码")
    private String recordCode;
    /**
     * 业务类型：
     */
    @ApiModelProperty(value = "业务类型")
    private Integer businessType;
    /**
     * do单状态，0 未同步  1 已同步 10 拣货 11 打包 12 装车 13 发运 21 接单 22 配送 23 完成
     */
    @ApiModelProperty(value = "do单状态")
    private Integer recordStatus;
    /**
     * 单据类型：1-销售出库订单，2-采购单
     */
    @ApiModelProperty(value = "单据类型：1-销售出库订单，2-采购单")
    private Integer recordType;
    /**
     * 用户code
     */
    @ApiModelProperty(value = "用户code")
    private String userCode;
    /**
     * 实仓ID
     */
    @ApiModelProperty(value = "实仓ID")
    private Long realWarehouseId;
    /**
     * 商家id
     */
    @ApiModelProperty(value = "商家id")
    private Long merchantId;

    @ApiModelProperty(value = "sku数量及明细")
    private List<WarehouseRecordDetailDTO> warehouseRecordDetails;


    @ApiModelProperty(value = "预计到货日期")
    private Date expeAogTime;

    @ApiModelProperty(value = "入库仓库")
    private Long inWarehouseId;

    @ApiModelProperty(value = "外部创建时间")
    private Date outCreateTime;

    @ApiModelProperty(value = "sapPoNo")
    private String sapPoNo;



}
