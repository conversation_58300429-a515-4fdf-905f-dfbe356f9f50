package com.rome.stock.innerservice.api.dto.template;

import lombok.Data;

import java.util.List;

/**
 * 仓库报废导入excel模板映射类
 */
@Data
public class ConsumeAdjustRecordTemplate {
    /**
     * 仓库编号
     */
    private String realWarehouseCode;
    /**
     * 成本中心
     */
    private String costCenterCode;
    /**
     * 成本中心名称
     */
    private String costCenterName;
    /**
     *业务原因
     */
    private String reasonName;
    /**
     *OA审批号
     */
    private String approveOaCode;
    /**
     *备注
     */
    private String remark;
    /**
     * 损耗调整单明细集合
     */
    private List<ConsumeAdjustRecordDetailTemplate> consumeAdjustDetails;
}
