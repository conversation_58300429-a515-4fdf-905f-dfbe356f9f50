package com.rome.stock.innerservice.api.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * DO 实体类
 * 
 * <AUTHOR>
 * @date 2024-05-17
 */
@Data
@NoArgsConstructor
@EqualsAndHashCode
public class BatchStockBoxCheckDTO {

	@ApiModelProperty(value = "仓库ID集合")
	private List<Long> realWarehouseIdList;

	@ApiModelProperty(value = "skuID集合")
	private List<Long> skuIdList;

	@ApiModelProperty(value = "是否进行初始化",example = "false")
	private boolean initFlag;
}
