package com.rome.stock.innerservice.api.dto;

import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.util.Date;

/**
 * @Doc:.....
 * @Author: lchy
 * @Date: 2020/4/28
 * @Version 1.0
 */
@Data
@EqualsAndHashCode
public class WDTLogPageParamDTO extends  Pagination implements Serializable {

    private Date startTime;
    private Date endTime ;
    private Long userId;
    private Integer type;
    private String recordCode;
    private String originOrderCode;
    private String outRecordCode;


}
