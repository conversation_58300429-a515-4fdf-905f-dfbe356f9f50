package com.rome.stock.innerservice.api.dto;

import com.rome.stock.innerservice.remote.base.dto.ChannelDTO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.Valid;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.List;

/**
 * @Doc:创建仓库入参DTO
 * @Author: lchy
 * @Date: 2019/7/31
 * @Version 1.0
 */
@Data
public class WarehouseDTO implements Serializable {
    @NotNull
    @ApiModelProperty(value = "商家ID", required = true)
    private Long merchantId;
    @NotBlank
    @ApiModelProperty(value = "仓库名称", required = true)
    private String warehouseName;
    @NotNull
    @ApiModelProperty(value = "仓库类型 1商家仓 2虚拟仓 3门店仓", required = true)
    private Integer warehouseType;
    @ApiModelProperty(value = "工厂编码")
    private String factoryCode;
    @ApiModelProperty(value = "仓库外部编码")
    private String warehouseOutCode;
    @ApiModelProperty(value = "门店编码")
    private String shopCode;
    @Valid
    @NotNull
    @ApiModelProperty(value = "渠道信息", required = true)
    private List<ChannelDTO> channelDTOList;
    @ApiModelProperty(value = "国家编码")
    private String countryCode;
    @ApiModelProperty(value = "省编码")
    private String provinceCode;
    @ApiModelProperty(value = "城市编码")
    private String cityCode;
    @ApiModelProperty(value = "仓库详细地址")
    private String realWarehouseAddress;
    @ApiModelProperty(value = "联系人手机号")
    private String realWarehouseMobile;
    @ApiModelProperty(value = "用户ID")
    private Long userId;
    @ApiModelProperty(value = "联系人")
    private String realWarehouseContactName;
    @ApiModelProperty(value = "仓库层级")
    private Integer realWarehouseRank;
    @ApiModelProperty(value = "省")
    private String realWarehouseProvince;
    @ApiModelProperty(value = "市")
    private String realWarehouseCity;
    @ApiModelProperty(value = "区")
    private String realWarehouseCounty;
    private String regionName;
    private Long regionId;
    
    @ApiModelProperty(value = "仓库状态：0-初始，1-启用，2-停用")
    private Integer realWarehouseStatus;
    @ApiModelProperty(value = "工厂名称")
    private String factoryName;
    @ApiModelProperty(value = "成本中心编码")
    private String costCenterCode;
    @ApiModelProperty(value = "成本中心名称")
    private String costCenterName;
    @ApiModelProperty(value = "仓库业务类型: 1: 直营门店 2: 加盟门店 3: 仓库 4. 其他仓库")
    private Integer rwBusinessType;
    @ApiModelProperty(value = "公司编码")
    private String companyCode;
    @ApiModelProperty(value = "公司名称")
    private String companyName;
    @ApiModelProperty(value = "是否允许负库存(0: 不允许 1: 允许)")
    private Integer allowNegtiveStock;

}
