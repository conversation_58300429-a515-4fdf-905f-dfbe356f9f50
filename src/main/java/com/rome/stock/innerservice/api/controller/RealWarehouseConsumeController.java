package com.rome.stock.innerservice.api.controller;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

import javax.annotation.Resource;

import com.alibaba.fastjson.JSON;
import com.rome.stock.innerservice.api.dto.warehouserecord.BatchResultDTO;
import com.rome.stock.innerservice.api.dto.warehouserecord.CancelRecordDTO;
import com.rome.stock.innerservice.api.dto.warehouserecord.OutWarehouseRecordDTO;
import com.rome.stock.innerservice.domain.repository.SapInterfaceLogRepository;
import com.rome.stock.innerservice.domain.service.RealWarehouseOrderService;
import io.swagger.annotations.ApiParam;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.http.MediaType;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;

import com.github.pagehelper.PageInfo;
import com.rome.arch.core.clientobject.Response;
import com.rome.arch.core.exception.RomeException;
import com.rome.arch.util.controller.RomeController;
import com.rome.stock.innerservice.api.dto.RealWarehouse;
import com.rome.stock.innerservice.api.dto.RealWarehouseStockDTO;
import com.rome.stock.innerservice.api.dto.ReceiveRecordTemplateDTO;
import com.rome.stock.innerservice.api.dto.frontrecord.ConsumeAdjustRecordDTO;
import com.rome.stock.innerservice.api.dto.template.ConsumeAdjustRecordTemplate;
import com.rome.stock.innerservice.common.ResCode;
import com.rome.stock.innerservice.constant.FrontRecordTypeVO;
import com.rome.stock.innerservice.constant.ResponseMsg;
import com.rome.stock.innerservice.domain.service.RealWarehouseConsumeService;
import com.rome.stock.innerservice.remote.base.dto.SaleOrgDTO;
import com.rome.stock.innerservice.remote.base.dto.StoreDTO;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiResponse;
import lombok.extern.slf4j.Slf4j;

/**
 * @Description 领用报废
 * <AUTHOR>
 * @Date 2019/5/10 19:46
 * @Version 1.0
 */
@Slf4j
@RomeController
@RequestMapping("/stock/v1/real_warehouse_consume")
@Api(tags={"仓库领用报废"})
public class RealWarehouseConsumeController {

    @Resource
    private RealWarehouseConsumeService realWarehouseConsumeService;

    @Resource
    private SapInterfaceLogRepository sapInterfaceLogRepository;
    @Resource
    private RealWarehouseOrderService realWarehouseOrderService;

    /**
     * 创建实仓损耗调整单
     * @param consumeAdjustRecordDTO
     * @return
     */
    @ApiOperation(value = "创建实仓损耗调整单", nickname = "createConsumeAdjustRecord", produces = MediaType.APPLICATION_JSON_VALUE)
    @ApiResponse(code = 200, message = "success")
    @RequestMapping(value = "/createConsumeAdjustRecord", method = RequestMethod.POST)
    public Response createConsumeAdjustRecord(@RequestBody ConsumeAdjustRecordDTO consumeAdjustRecordDTO){
        try{
            realWarehouseConsumeService.createConsumeAdjustRecord(consumeAdjustRecordDTO);
            return ResponseMsg.SUCCESS.buildMsg();
        } catch (RomeException e) {
            log.error(e.getMessage(), e);
            return ResponseMsg.EXCEPTION.buildMsg(e.getCode(), e.getMessage());
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return ResponseMsg.EXCEPTION.buildMsg(ResCode.STOCK_ERROR_1003_DESC,ResCode.STOCK_ERROR_1003_DESC);
        }
    }

    /**
     *  根据条件调整实仓损耗调整单
     * @param consumeAdjustRecord
     * @return
     */
    @ApiOperation(value = "根据条件查询实仓损耗调整单", nickname = "selectConsumeAdjustRecordByCondition", produces = MediaType.APPLICATION_JSON_VALUE)
    @ApiResponse(code = 200, message = "success",response = ConsumeAdjustRecordDTO.class)
    @RequestMapping(value = "/selectConsumeAdjustRecordByCondition", method = RequestMethod.POST)
    public Response selectConsumeAdjustRecordByCondition(@RequestBody ConsumeAdjustRecordDTO consumeAdjustRecord){
        try{
            PageInfo<ConsumeAdjustRecordDTO> pageList= realWarehouseConsumeService.selectConsumeAdjustRecordByCondition(consumeAdjustRecord);
            return ResponseMsg.SUCCESS.buildMsg(pageList);
        } catch (RomeException e) {
            log.error(e.getMessage(), e);
            return ResponseMsg.EXCEPTION.buildMsg(e.getCode(), e.getMessage());
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return ResponseMsg.EXCEPTION.buildMsg(ResCode.STOCK_ERROR_1003_DESC,ResCode.STOCK_ERROR_1003_DESC);
        }
    }

    @ApiOperation(value = "根据调整单id查询明细", nickname = "selectAdjustConsumeRecordDetail", produces = MediaType.APPLICATION_JSON_VALUE)
    @ApiResponse(code = 200, message = "success")
    @RequestMapping(value = "/selectAdjustConsumeRecordDetail", method = RequestMethod.GET)
    public Response selectAdjustConsumeRecordDetail(@RequestParam("id") String id){
        try {
            ConsumeAdjustRecordDTO detailList = realWarehouseConsumeService.selectAdjustConsumeRecordDetail(Long.parseLong(id));
            return ResponseMsg.SUCCESS.buildMsg(detailList);
        } catch (RomeException e) {
            log.error(e.getMessage(), e);
            return ResponseMsg.EXCEPTION.buildMsg(e.getCode(), e.getMessage());
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return ResponseMsg.EXCEPTION.buildMsg(ResCode.STOCK_ERROR_1003_DESC,ResCode.STOCK_ERROR_1003_DESC);
        }
    }


    @ApiOperation(value = "修改损耗调整单(信息修改)", nickname = "modifyAdjustConsumeRecord", produces = MediaType.APPLICATION_JSON_VALUE)
    @ApiResponse(code = 200, message = "success")
    @RequestMapping(value = "/modifyAdjustConsumeRecord", method = RequestMethod.POST)
    public Response modifyAdjustConsumeRecord(@RequestBody ConsumeAdjustRecordDTO consumeAdjustRecord){
        try {
            realWarehouseConsumeService.modifyAdjustConsumeRecord(consumeAdjustRecord);
            return ResponseMsg.SUCCESS.buildMsg();
        } catch (RomeException e) {
            log.error(e.getMessage(), e);
            return ResponseMsg.FAIL.buildMsg(e.getCode(),e.getMessage());
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return ResponseMsg.FAIL.buildMsg("500000",e.getMessage());
        }
    }

    @ApiOperation(value = "创建报废出库单", produces = MediaType.APPLICATION_JSON_VALUE)
    @ApiResponse(code = 200, message = "success")
    @RequestMapping(value = "/addAdjustConsumeOutRecord", method = RequestMethod.POST)
    public Response addAdjustConsumeOutRecord(@ApiParam(name = "outWarehouseRecordDTO", value = "创建报废出库单") @RequestBody @Validated OutWarehouseRecordDTO outWarehouseRecordDTO) {
        try {
            realWarehouseConsumeService.addAdjustConsumeOutRecord(outWarehouseRecordDTO);
            return Response.builderSuccess("");
        } catch (RomeException e) {
            log.error(e.getMessage(), e);
            return Response.builderFail(e.getCode(),e.getMessage());
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return Response.builderFail(ResCode.STOCK_ERROR_1003_DESC,ResCode.STOCK_ERROR_1003_DESC+e.getMessage()==null?e.toString():e.getMessage());
        }
    }


    @ApiOperation(value = "创建领用出库单", produces = MediaType.APPLICATION_JSON_VALUE)
    @ApiResponse(code = 200, message = "success")
    @RequestMapping(value = "/addReceiveOutRecord", method = RequestMethod.POST)
    public Response addReceiveOutRecord(@ApiParam(name = "outWarehouseRecordDTO", value = "创建领用报废出库单") @RequestBody @Validated OutWarehouseRecordDTO outWarehouseRecordDTO) {
        try {
            realWarehouseOrderService.addReceiveOutRecord(outWarehouseRecordDTO);
            return Response.builderSuccess("");
        } catch (RomeException e) {
            log.error(e.getMessage(), e);
            return Response.builderFail(e.getCode(),e.getMessage());
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return Response.builderFail(ResCode.STOCK_ERROR_1003_DESC,ResCode.STOCK_ERROR_1003_DESC+e.getMessage()==null?e.toString():e.getMessage());
        }
    }

    @ApiOperation(value = "创建领用报废-批量取消申请", nickname = "cancelAdjustConsumeRecord", produces = MediaType.APPLICATION_JSON_VALUE)
    @ApiResponse(code = 200, message = "success")
    @RequestMapping(value = "/cancelAdjustConsumeRecord", method = RequestMethod.POST)
    public Response cancelAdjustConsumeRecord(@RequestBody List<CancelRecordDTO> list){
        List<BatchResultDTO> resultList =  new ArrayList<>();
        try {
            if(CollectionUtils.isNotEmpty(list)){
                for (CancelRecordDTO cancelRecordDTO : list) {
                    BatchResultDTO dto = new BatchResultDTO();
                    dto.setRecordCode(cancelRecordDTO.getRecordCode());
                    dto.setStatus(false);
                    String message = "";
                    boolean isSucc = false;
                    try {
                        realWarehouseConsumeService.cancelAdjustConsumeRecord(cancelRecordDTO);
                        dto.setStatus(true);
                        isSucc = true;
                        message = "200";
                    }catch (Exception e){
                        message=e.getMessage();
                        log.error(e.getMessage(), e);
                    }finally {
                        dto.setMessage(message);
                        resultList.add(dto);
                        sapInterfaceLogRepository.saveCallBackInterFaceLog(1, dto.getRecordCode(), "cancelAdjustConsumeRecord",
                                JSON.toJSONString(cancelRecordDTO), message, isSucc);
                    }
                }
            }
            return ResponseMsg.SUCCESS.buildMsg(resultList);
        } catch (RomeException e) {
            log.error(e.getMessage(), e);
            return Response.builderFail(e.getCode(),e.getMessage());
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return Response.builderFail(ResCode.STOCK_ERROR_1003_DESC,e.getMessage());
        }
    }

    @ApiOperation(value = "损耗调整单推送", nickname = "confirmPushConsumeRecord", produces = MediaType.APPLICATION_JSON_VALUE)
    @ApiResponse(code = 200, message = "success")
    @RequestMapping(value = "/confirmPushConsumeRecord", method = RequestMethod.POST)
    public Response confirmPushConsumeRecord(@RequestBody List<String> recordCodes){
        List<BatchResultDTO> resultList =  new ArrayList<>();
        try {
            if(CollectionUtils.isNotEmpty(recordCodes)){
                for (String recordCode : recordCodes) {
                    BatchResultDTO dto = new BatchResultDTO();
                    dto.setRecordCode(recordCode);
                    dto.setStatus(false);
                    String message = "";
                    boolean isSucc = false;
                    try {
                        realWarehouseConsumeService.confirmPushConsumeRecord(recordCode);
                        dto.setStatus(true);
                        isSucc = true;
                        message = "200";
                    }catch (Exception e){
                        message=e.getMessage();
                        log.error(e.getMessage(), e);
                    }finally {
                        resultList.add(dto);
                        sapInterfaceLogRepository.saveCallBackInterFaceLog(1, dto.getRecordCode(), "confirmPushConsumeRecord",
                                JSON.toJSONString(dto), message, isSucc);
                    }
                }
            }
            return ResponseMsg.SUCCESS.buildMsg(resultList);
        } catch (RomeException e) {
            log.error(e.getMessage(), e);
            return Response.builderFail(e.getCode(),e.getMessage());
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return Response.builderFail(ResCode.STOCK_ERROR_1003_DESC,e.getMessage());
        }

    }
//    @ApiOperation(value = "损耗调整单取消", nickname = "cancelAdjustConsumeRecord", produces = MediaType.APPLICATION_JSON_VALUE)
//    @ApiResponse(code = 200, message = "success")
//    @RequestMapping(value = "/cancelAdjustConsumeRecord", method = RequestMethod.GET)
//    public Response cancelAdjustConsumeRecord(@RequestParam("id") String id,@RequestParam("modifier") Long modifier){
//        try {
//            realWarehouseConsumeService.cancelAdjustConsumeRecord(Long.parseLong(id),modifier);
//            return ResponseMsg.SUCCESS.buildMsg();
//        } catch (RomeException e) {
//            log.error(e.getMessage(), e);
//            return ResponseMsg.FAIL.buildMsg(e.getCode(),e.getMessage());
//        } catch (Exception e) {
//            log.error(e.getMessage(), e);
//            return ResponseMsg.FAIL.buildMsg("500000",e.getMessage());
//        }
//    }

    @ApiOperation(value = "领用单调整单取消", nickname = "cancelAdjustConsumeRecord", produces = MediaType.APPLICATION_JSON_VALUE)
    @ApiResponse(code = 200, message = "success")
    @RequestMapping(value = "/cancelReceiveRecord", method = RequestMethod.GET)
    public Response cancelReceiveRecord(@RequestParam("id") String id,@RequestParam("modifier") Long modifier){
        try {
            realWarehouseConsumeService.cancelReceiveRecord(Long.parseLong(id),modifier);
            return ResponseMsg.SUCCESS.buildMsg();
        } catch (RomeException e) {
            log.error(e.getMessage(), e);
            return ResponseMsg.FAIL.buildMsg(e.getCode(),e.getMessage());
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return ResponseMsg.FAIL.buildMsg("500000",e.getMessage());
        }
    }

    @ApiOperation(value = "根据id批量查询单子", nickname = "selectRealWarehouseConsumeRecordByIds", produces = MediaType.APPLICATION_JSON_VALUE)
    @ApiResponse(code = 200, message = "success")
    @RequestMapping(value = "/selectRealWarehouseConsumeRecordByIds", method = RequestMethod.POST)
    public Response selectRealWarehouseConsumeRecordByIds(@RequestBody List<Long> ids){
        try {
            List<ConsumeAdjustRecordDTO> adjustDTOS = realWarehouseConsumeService.selectRealWarehouseConsumeRecordByIds(ids);
            return ResponseMsg.SUCCESS.buildMsg(adjustDTOS);
        } catch (RomeException e) {
            log.error(e.getMessage(), e);
            return ResponseMsg.FAIL.buildMsg(e.getCode(),e.getMessage());
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return ResponseMsg.FAIL.buildMsg("500000",e.getMessage());
        }
    }

    @ApiOperation(value = "获取前置单类型和code集合", nickname = "getFrontRecordTypeCodeList", produces = MediaType.APPLICATION_JSON_VALUE)
    @ApiResponse(code = 200, message = "success")
    @RequestMapping(value = "/getFrontRecordTypeCodeList", method = RequestMethod.POST)
    public Response getFrontRecordTypeCodeList(){
        try {
            Map<String,Integer> map = FrontRecordTypeVO.getFrontRecordTypeCodeList();
            return ResponseMsg.SUCCESS.buildMsg(map);
        } catch (RomeException e) {
            log.error(e.getMessage(), e);
            return ResponseMsg.FAIL.buildMsg(e.getCode(),e.getMessage());
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return ResponseMsg.FAIL.buildMsg("500000",e.getMessage());
        }
    }

    @ApiOperation(value = "获取所有实仓工厂(含仓店一体)", nickname = "getRealWarehouseFactory", produces = MediaType.APPLICATION_JSON_VALUE)
    @ApiResponse(code = 200, message = "success")
    @RequestMapping(value = "/getRealWarehouseFactory", method = RequestMethod.GET)
    public Response getRealWarehouseFactory(){
        try {
            List<StoreDTO> result = realWarehouseConsumeService.getRealWarehouseFactory();
            return ResponseMsg.SUCCESS.buildMsg(result);
        } catch (RomeException e) {
            log.error(e.getMessage(), e);
            return ResponseMsg.FAIL.buildMsg(e.getCode(),e.getMessage());
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return ResponseMsg.FAIL.buildMsg("500000",e.getMessage());
        }
    }

    @ApiOperation(value = "根据仓库工厂code和仓库类型查询仓库信息", nickname = "queryRealWarehouseByFactoryCode", produces = MediaType.APPLICATION_JSON_VALUE)
    @ApiResponse(code = 200, message = "success")
    @RequestMapping(value = "/queryRealWarehouseByFactoryCode", method = RequestMethod.GET)
    public Response queryRealWarehouseByFactoryCode(@RequestParam("factoryCode") String factoryCode,@RequestParam("recordType") Integer recordType){
        try {
            if(recordType==0){
                recordType = null;
            }
            List<RealWarehouse> warehouses = realWarehouseConsumeService.queryRealWarehouseByFactoryCode(factoryCode,recordType);
            return ResponseMsg.SUCCESS.buildMsg(warehouses);
        } catch (RomeException e) {
            log.error(e.getMessage(), e);
            return ResponseMsg.FAIL.buildMsg(e.getCode(), e.getMessage());
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return ResponseMsg.FAIL.buildMsg("500000", e.getMessage());
        }
    }

    @ApiOperation(value = "根据实仓ID查询实仓的sku", nickname = "querySkuIdByWhId", produces = MediaType.APPLICATION_JSON_VALUE)
    @ApiResponse(code = 200, message = "success")
    @RequestMapping(value = "/querySkuIdByWhId", method = RequestMethod.POST)
    public Response<PageInfo<RealWarehouseStockDTO>> querySkuIdByWhId(@RequestBody RealWarehouseStockDTO stockDTO) {
        try {
            PageInfo<RealWarehouseStockDTO> stockList = realWarehouseConsumeService.querySkuIdByWhId(stockDTO);
            return ResponseMsg.SUCCESS.buildMsg(stockList);
        } catch (RomeException e) {
            log.error(e.getMessage(), e);
            return ResponseMsg.FAIL.buildMsg(e.getCode(), e.getMessage());
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return ResponseMsg.EXCEPTION.buildMsg();
        }
    }

    @RequestMapping(value = "/importFileData", method = RequestMethod.POST)
    @ApiOperation(value = "导入excel文件中的数据", nickname = "importFileData", produces = MediaType.MULTIPART_FORM_DATA_VALUE)
    public Response importFileData(@RequestBody List<ConsumeAdjustRecordTemplate> dataList, @RequestParam("userId") Long userId) {
        try {
            realWarehouseConsumeService.importFileData(dataList,userId);
            return ResponseMsg.SUCCESS.buildMsg();
        } catch (RomeException e) {
            log.error(e.getMessage(), e);
            return ResponseMsg.FAIL.buildMsg(e.getCode(), e.getMessage());
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return ResponseMsg.EXCEPTION.buildMsg();
        }
    }

    @ApiOperation(value = "领用导入", nickname = "receiveImport", produces = MediaType.MULTIPART_FORM_DATA_VALUE)
    @PostMapping(value = "/receiveImport")
    public Response receiveImport(@RequestBody List<ReceiveRecordTemplateDTO> dataList, @RequestParam("userId") Long userId) {
        try {
            realWarehouseConsumeService.receiveImport(dataList, userId);
            return Response.builderSuccess("");
        } catch (RomeException e){
            log.error("领用导入异常 ==> {}", e.getMessage(), e);
            return Response.builderFail(e.getCode(), e.getMessage());
        } catch (Exception e){
            log.error("领用导入系统异常 ==> {}", e.getMessage(), e);
            return Response.builderFail(ResCode.STOCK_ERROR_1003, ResCode.STOCK_ERROR_1003_DESC);
}
    }

    @ApiOperation(value = "根据工厂查询公司", nickname = "queryRealWarehouseByFactoryCode", produces = MediaType.APPLICATION_JSON_VALUE)
    @ApiResponse(code = 200, message = "success")
    @RequestMapping(value = "/queryCompanyByFactoryCode", method = RequestMethod.POST)
    public Response<StoreDTO> queryCompanyByFactoryCode(@RequestParam("factoryCode") String factoryCode) {
        try {
            StoreDTO storeDTO = realWarehouseConsumeService.queryCompanyByFactoryCode(factoryCode);
            return ResponseMsg.SUCCESS.buildMsg(storeDTO);
        } catch (RomeException e) {
            log.error(e.getMessage(), e);
            return ResponseMsg.FAIL.buildMsg(e.getCode(), e.getMessage());
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return ResponseMsg.FAIL.buildMsg("500000", e.getMessage());
        }
    }

    @ApiOperation(value = "查询公司列表", nickname = "queryCompanyList", produces = MediaType.APPLICATION_JSON_VALUE)
    @ApiResponse(code = 200, message = "success")
    @RequestMapping(value = "/queryCompanyList", method = RequestMethod.POST)
    public Response<List<SaleOrgDTO>> queryCompanyList() {
        try {
            List<SaleOrgDTO> saleOrgDTOList = realWarehouseConsumeService.queryCompanyList();
            return ResponseMsg.SUCCESS.buildMsg(saleOrgDTOList);
        } catch (RomeException e) {
            log.error(e.getMessage(), e);
            return ResponseMsg.FAIL.buildMsg(e.getCode(), e.getMessage());
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return ResponseMsg.FAIL.buildMsg("500000", e.getMessage());
        }
    }


    @ApiOperation(value = "创建虚仓自动分配出库单", produces = MediaType.APPLICATION_JSON_VALUE)
    @ApiResponse(code = 200, message = "success")
    @RequestMapping(value = "/addAutoVirtualOutRecord", method = RequestMethod.POST)
    public Response addAutoVirtualOutRecord(@ApiParam(name = "outWarehouseRecordDTO", value = "创建虚仓自动分配出库单") @RequestBody @Validated OutWarehouseRecordDTO outWarehouseRecordDTO) {
        try {
            realWarehouseOrderService.addAutoVirtualOutRecord(outWarehouseRecordDTO);
            return Response.builderSuccess("");
        } catch (RomeException e) {
            log.error(e.getMessage(), e);
            return Response.builderFail(e.getCode(),e.getMessage());
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return Response.builderFail(ResCode.STOCK_ERROR_1003_DESC,ResCode.STOCK_ERROR_1003_DESC+e.getMessage()==null?e.toString():e.getMessage());
        }
    }


}
