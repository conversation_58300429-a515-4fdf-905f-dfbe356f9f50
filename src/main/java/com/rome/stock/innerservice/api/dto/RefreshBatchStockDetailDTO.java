/**
 * Filename RefreshBatchStockDetailDTO.java
 * Company 上海来伊份科技有限公司。
 * <AUTHOR>
 * @version 
 */
package com.rome.stock.innerservice.api.dto;

import java.math.BigDecimal;
import java.util.Date;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 刷批次库存,明细
 * <AUTHOR>
 * @since 2021-4-13 15:07:42
 */
@Data
@EqualsAndHashCode
public class RefreshBatchStockDetailDTO {

    @ApiModelProperty(value = "商品skuID", required = true)
    private Long skuId;

    @ApiModelProperty(value = "数量", required = true)
    private BigDecimal skuQty;
    
    @ApiModelProperty(value = "批次编码-批次号")
    private String batchCode;
    
    @ApiModelProperty(value = "实体仓库id", required = true)
    private Long realWarehouseId;
    
    @ApiModelProperty(value = "单据编号", required = true)
    private String recordCode;
    
    @ApiModelProperty(value = "单据类型")
    private Integer recordType;
    
    @ApiModelProperty(value = "库存类型： 1:增加库存  2:减少库存 3：锁定库存 4：释放库存 5：真实出库库存  6：冻结出库库存", required = true)
    private Integer stockType;

    @ApiModelProperty(value = "生产日期")
    private Date productDate;

    @ApiModelProperty(value = "入库日期")
    private Date entryDate;
}
