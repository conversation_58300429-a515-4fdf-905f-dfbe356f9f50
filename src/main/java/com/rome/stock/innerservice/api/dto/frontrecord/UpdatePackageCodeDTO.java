package com.rome.stock.innerservice.api.dto.frontrecord;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotBlank;
import java.util.List;

@Data
@EqualsAndHashCode
public class UpdatePackageCodeDTO {

    @ApiModelProperty(value = "请求编号")
    @NotBlank
    private String requestCode;

    @ApiModelProperty(value = "包裹号运单号列表")
    @NotBlank
    private List<UpdateBillCodeDTO> updateBillCodeDTO;
}
