package com.rome.stock.innerservice.api.dto.cloudshop;

import com.github.pagehelper.PageInfo;
import com.rome.stock.innerservice.api.dto.Pagination;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;

/**
 * DO 实体类
 * 
 * <AUTHOR>
 * @date 2021-09-14
 */
@Data
@NoArgsConstructor
@EqualsAndHashCode
public class ServiceLabDTO  extends Pagination {

	private Long id;
	/**
     * 标签名称
     */
	private String labName;

	/**
	 * 标签类型(1: 门店自提 2: 仓库次日达 3: 供应商到家 4: 供应商自提)
     */
	private Integer labType;

	/**
     * 标签描述
     */
	private String labDesc;

	/**
	 * 时效
	 */
	private BigDecimal prescription;

	/**
	 * 业务类型(1: 门店 2: 仓库 3: 供应商)
	 */
	private Integer businessType;

	private String realWarehouseId;

	private Integer skuType;

}
