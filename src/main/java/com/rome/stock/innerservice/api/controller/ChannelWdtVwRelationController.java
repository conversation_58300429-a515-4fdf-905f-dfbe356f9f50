package com.rome.stock.innerservice.api.controller;

import com.alibaba.fastjson.JSON;
import com.github.pagehelper.PageInfo;
import com.rome.arch.core.clientobject.Response;
import com.rome.arch.core.exception.RomeException;
import com.rome.arch.util.controller.RomeController;
import com.rome.stock.innerservice.api.dto.ChannelWdtVwRelation;
import com.rome.stock.innerservice.common.ResCode;
import com.rome.stock.innerservice.constant.ResponseMsg;
import com.rome.stock.innerservice.domain.repository.SapInterfaceLogRepository;
import com.rome.stock.innerservice.domain.service.ChannelWdtVwRelationService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import io.swagger.annotations.ApiResponse;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;

import javax.annotation.Resource;

/**
 * @Author: zhoupeng
 * @createTime: 2022年07月22日 12:05:03
 * @version: 1.0
 * @Description:
 */
@Slf4j
@RomeController
@RequestMapping("/stock/v1/channelWdtVwRelation")
@Api(tags={"旺店通编码与渠道关系服务接口"})
public class ChannelWdtVwRelationController {

    @Resource
    private ChannelWdtVwRelationService channelWdtVwRelationService;


    @Autowired
    private SapInterfaceLogRepository sapInterfaceLogRepository;

    @ApiOperation(value = "查询旺店通编码与渠道", nickname = "getChannelWdtVwRelationPage", produces = MediaType.APPLICATION_JSON_VALUE)
    @ApiResponse(code = 200, message = "success")
    @RequestMapping(value = "/getChannelWdtVwRelationPage", method = RequestMethod.POST)
    public Response<PageInfo<ChannelWdtVwRelation>>  getChannelWdtVwRelationPage(@ApiParam(name = "ChannelWdtVwRelation", value = "旺店通编码与渠道") @RequestBody ChannelWdtVwRelation channelWdtVwRelation){
        try {
            PageInfo<ChannelWdtVwRelation> pageList = channelWdtVwRelationService.getChannelWdtVwRelationPage(channelWdtVwRelation);
            return Response.builderSuccess(pageList);
        } catch (RomeException e) {
            log.error(e.getMessage(), e);
            return ResponseMsg.EXCEPTION.buildMsg(e.getCode(), e.getMessage());
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return ResponseMsg.EXCEPTION.buildMsg(ResCode.STOCK_ERROR_1003, ResCode.STOCK_ERROR_1003_DESC);
        }
    }

    /**
     * 创建旺店通编码与渠道关系
     * @param channelWdtVwRelation
     * @return
     */
    @ApiOperation(value = "创建旺店通编码与渠道关系", nickname = "saveChannelWdtVwRelation", produces = MediaType.APPLICATION_JSON_VALUE)
    @ApiResponse(code = 200, message = "success", response = ChannelWdtVwRelation.class)
    @RequestMapping(value = "/saveChannelWdtVwRelation", method = RequestMethod.POST)
    public   Response   saveChannelWdtVwRelation(@ApiParam(name = "ChannelWdtVwRelation", value = "旺店通编码与渠道关系") @RequestBody ChannelWdtVwRelation channelWdtVwRelation){
        boolean flag = false;
        int result = 0;
        try {
            result = channelWdtVwRelationService.saveChannelWdtVwRelation(channelWdtVwRelation);
            flag = true;
            return ResponseMsg.SUCCESS.buildMsg(channelWdtVwRelation);
        } catch (RomeException e) {
            log.error(e.getMessage(), e);
            return ResponseMsg.EXCEPTION.buildMsg(e.getCode(), e.getMessage());
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return ResponseMsg.EXCEPTION.buildMsg();
        } finally {
            //记录日志信息
            String dataStr = JSON.toJSONString(channelWdtVwRelation);
            //成功的 不保存返回值，返回值太大 保存不下
            sapInterfaceLogRepository.saveSapInterFaceLog(channelWdtVwRelation.getWdtVwCode(), "/stock/v1/channelWdtVwRelation/saveChannelWdtVwRelation", "saveChannelWdtVwRelation",
                    dataStr, flag ? "200" : JSON.toJSONString(result), flag);
        }
    }

    /**
     * 修改旺店通编码与渠道关系
     * @param channelWdtVwRelation
     * @return
     */
    @ApiOperation(value = "修改旺店通编码与渠道关系", nickname = "modifyChannelWdtVwRelation", produces = MediaType.APPLICATION_JSON_VALUE)
    @ApiResponse(code = 200, message = "success", response = ChannelWdtVwRelation.class)
    @RequestMapping(value = "/modifyChannelWdtVwRelation", method = RequestMethod.POST)
    public  Response   modifyChannelWdtVwRelation(@ApiParam(name = "ChannelWdtVwRelation", value = "旺店通编码与渠道关系") @RequestBody ChannelWdtVwRelation channelWdtVwRelation){
        boolean flag = false;
        ChannelWdtVwRelation channelWdtVwRelationInfo = null;
        try {
            //查询是否存在
            Long id = channelWdtVwRelation.getId();
            channelWdtVwRelationInfo = channelWdtVwRelationService.getChannelWdtVwRelationById(id);
            if (channelWdtVwRelationInfo != null) {
                int result = channelWdtVwRelationService.modifyChannelWdtVwRelation(channelWdtVwRelation);
                if (result > 0) {
                    flag = true;
                }
                return ResponseMsg.SUCCESS.buildMsg(channelWdtVwRelationInfo);
            } else {
                throw new RomeException(ResCode.STOCK_ERROR_1002, ResCode.STOCK_ERROR_1002_DESC + "传入的Id:" + id);
            }
        } catch (RomeException e) {
            log.error(e.getMessage(), e);
            return ResponseMsg.EXCEPTION.buildMsg(e.getCode(), e.getMessage());
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return ResponseMsg.EXCEPTION.buildMsg();
        } finally {
            //记录日志信息
            String dataStr = JSON.toJSONString(channelWdtVwRelationInfo);
            //成功的 不保存返回值，返回值太大 保存不下
            sapInterfaceLogRepository.saveSapInterFaceLog(channelWdtVwRelationInfo.getWdtVwCode(), "/stock/v1/channelWdtVwRelation/modifyChannelWdtVwRelation", "modifyChannelWdtVwRelation",
                    dataStr, JSON.toJSONString(channelWdtVwRelationInfo), flag);
        }
    }


    /**
     * 删除旺店通编码与渠道关系
     *
     * @param channelWdtVwRelation
     * @return
     */
    @ApiOperation(value = "删除旺店通编码与渠道关系", nickname = "deleteChannelWdtVwRelation", produces = MediaType.APPLICATION_JSON_VALUE)
    @ApiResponse(code = 200, message = "success", response = ChannelWdtVwRelation.class)
    @RequestMapping(value = "/deleteChannelWdtVwRelation", method = RequestMethod.POST)
    public Response deleteBaseinfoConfig(@ApiParam(name = "ChannelWdtVwRelation", value = "旺店通编码与渠道关系") @RequestBody ChannelWdtVwRelation channelWdtVwRelation) {
        int result = 0;
        boolean flag = false;
        try {
            result = channelWdtVwRelationService.deleteChannelWdtVwRelation(channelWdtVwRelation);
            if (result > 0) {
                flag = true;
            }
            return ResponseMsg.SUCCESS.buildMsg(channelWdtVwRelation);
        } catch (RomeException e) {
            log.error(e.getMessage(), e);
            return ResponseMsg.EXCEPTION.buildMsg(e.getCode(), e.getMessage());
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return ResponseMsg.EXCEPTION.buildMsg();
        }finally {
            //记录日志信息
            String dataStr = JSON.toJSONString(channelWdtVwRelation);
            //成功的 不保存返回值，返回值太大 保存不下
            sapInterfaceLogRepository.saveSapInterFaceLog(channelWdtVwRelation.getWdtVwCode(), "/stock/v1/channelWdtVwRelation/deleteChannelWdtVwRelation", "deleteChannelWdtVwRelation",
                    dataStr, flag ? "200" : JSON.toJSONString(result), flag);
        }
    }
}
