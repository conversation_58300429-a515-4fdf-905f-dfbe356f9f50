package com.rome.stock.innerservice.api.dto;

import lombok.Data;

import java.util.Date;

/**
 * 类SapInterfaceLogDO的实现描述：sap接口调用日志
 *
 * <AUTHOR> 2019/6/24 17:28
 */
@Data
public class RemoteInterfaceLogDTO {

    /**
     * 唯一主键
     */
    private Long id;
    /**
     * 出入库单据编号
     */
    private String recordCode;
    /**
     * 业务唯一单号
     */
    private String bizId;

    /**
     * 业务名称
     */
    private String bizName;
    /**
     * 请求服务名
     */
    private String requestService;
    /**
     * 请求url
     */
    private String requestUrl;
    /**
     * 请求内容
     */
    private String requestContent;
    /**
     * 响应内容
     */
    private String responseContent;
    /**
     * 交互状态 0：失败  1:成功
     */
    private Integer status;

    /**
     * 调用类型 1：调用  2:回调
     */
    private Integer type;

    /**
     * 交互系统 1：sap  2:innerTrade
     */
    private Integer interacteSystem;

    /**
     * 创建时间
     */
    private Date createTime;
    /**
     * 修改时间
     */
    private Date updateTime;
    /**
     * 是否可用：0-否，1-是
     */
    private Byte isAvailable;
    /**
     * 是否逻辑删除：0-否，1-是
     */
    private Byte isDeleted;
    /**
     * 版本号:默认0,每次更新+1
     */
    private Integer versionNo;
}
