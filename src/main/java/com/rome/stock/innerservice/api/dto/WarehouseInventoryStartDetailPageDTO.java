package com.rome.stock.innerservice.api.dto;


import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;
import java.util.Date;

@ApiModel(value = "WarehouseInventoryStartPageDTO", description = "盘点单管理")
@Data
@EqualsAndHashCode
public class WarehouseInventoryStartDetailPageDTO extends Pagination {
    @ApiModelProperty(value = "")
    private static final long serialVersionUID = 6241269605258899670L;


    @ApiModelProperty(value = "盘点记录号")
    private Long inventoryStartId;

    @ApiModelProperty(value = "仓库编码")
    private String realWarehouseCode;

    @ApiModelProperty(value = "仓库名称")
    private String realWarehouseName;

    /**
     * 商品sku编码
     */
    @ApiModelProperty(value = "商品sku编码")
    private Long skuId;

    /**
     * 真实库存
     */
    @ApiModelProperty(value = "真实库存")
    private BigDecimal realQty;

    /**
     * 锁定库存
     */
    @ApiModelProperty(value = "锁定库存")
    private BigDecimal lockQty;

    /**
     * 在途库存
     */
    @ApiModelProperty(value = "在途库存")
    private BigDecimal onroadQty;

    /**
     * 质检库存
     */
    @ApiModelProperty(value = "质检库存")
    private BigDecimal qualityQty;

    /**
     * 不合格库存，注：一般是质检不合格库存
     */
    @ApiModelProperty(value = "不合格库存，注：一般是质检不合格库存")
    private BigDecimal unqualifiedQty;

    /**
     * 真实库存
     */
    @ApiModelProperty(value = "真实库存")
    private BigDecimal wmsRealQty;

    /**
     * 质检库存
     */
    @ApiModelProperty(value = "质检库存")
    private BigDecimal wmsQualityQty;

    /**
     * 不合格库存，注：一般是质检不合格库存
     */
    @ApiModelProperty(value = "不合格库存，注：一般是质检不合格库存")
    private BigDecimal wmsUnqualifiedQty;


    /** sku编号 */
    @ApiModelProperty(value = "sku编号")
    private String skuCode;

    /** sku名称 */
    @ApiModelProperty(value = "sku名称")
    private String skuName;

    /**
     * 基本数量
     */
    @ApiModelProperty(value = "基本单位")
    private String baseUnit;

    @ApiModelProperty(value = "创建人")
    private Long creator;

    @ApiModelProperty(value = "创建时间")
    private Date createTime;
}
