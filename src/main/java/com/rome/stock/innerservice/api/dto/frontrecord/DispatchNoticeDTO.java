package com.rome.stock.innerservice.api.dto.frontrecord;

import java.util.Date;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

/**
 * DispatchNoticeDTO类的实现描述： 派车
 *
 * <AUTHOR> 2019/6/17 19:48
 */
@Data
@NoArgsConstructor
@EqualsAndHashCode
public class DispatchNoticeDTO{

    @ApiModelProperty(value = "唯一标识")
    private String id;

    @ApiModelProperty(value = "sap单据编码", required = true)
    @NotBlank(message="sap单据编码")
    private String recordCode;

    @ApiModelProperty(value = "前置单Id")
    private Long frontRecordId;

    @ApiModelProperty(value = "前置单code")
    private String frontRecordCode;

    @ApiModelProperty(value = "前置单类型")
    private Integer frontRecordType;

    @ApiModelProperty(value = "派车类型(1:派车 2:自提)", required = true)
    @NotNull(message="派车类型(1:派车 2:自提)")
    private Integer dispatchType;

    @ApiModelProperty(value = "TMS派车单号", required = true)
    private String thirdRecordCode;

    @ApiModelProperty(value = "司机姓名")
    private String driverName;

    @ApiModelProperty(value = "司机手机")
    private String driverMobile;

    @ApiModelProperty(value = "推荐路线")
    private String recommendedRoute;

    @ApiModelProperty(value = "预测出车时间")
    private Date predictTime;

    @ApiModelProperty(value = "实际出车时间")
    private Date faceTime;

    @ApiModelProperty(value = "来源系统")
    private String sourceSystem;

    @ApiModelProperty(value = "邮件状态")
    private Integer mailStatus;

    @ApiModelProperty(value = "邮件id")
    private Long mailId;

    /**
     * 创建时间
     */
    private Date createTime;
    /**
     * 修改时间
     */
    private Date updateTime;
    /**
     * 创建人
     */
    private Long creator;
    /**
     * 更新人
     */
    private Long modifier;
    /**
     * 是否可用：0-否，1-是
     */
    private Byte isAvailable;
    /**
     * 是否逻辑删除：0-否，1-是
     */
    private Byte isDeleted;
    /**
     * 版本号:默认0,每次更新+1
     */
    private Integer versionNo;
    /**
     * 租户ID
     */
    private Long tenantId;
    /**
     * 业务应用ID
     */
    private String appId;
}
