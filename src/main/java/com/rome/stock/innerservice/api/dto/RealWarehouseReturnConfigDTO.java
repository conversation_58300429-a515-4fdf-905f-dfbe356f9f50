package com.rome.stock.innerservice.api.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import java.util.Date;
import java.util.List;

@Data
@NoArgsConstructor
@EqualsAndHashCode
public class RealWarehouseReturnConfigDTO extends Pagination {

    @ApiModelProperty(value = "主键")
    private Long id;

    @ApiModelProperty(value = "罗马渠道编码")
    private String channelCode;

    @ApiModelProperty(value = "罗马渠道名称")
    private String channelName;

    @ApiModelProperty(value = "单据类型")
    private String recordType;

    @ApiModelProperty(value = "单据类型名称")
    private String recordTypeName;

    @ApiModelProperty(value = "发货工厂编号")
    private String factoryCode;

    @ApiModelProperty(value = "发货仓库编码")
    private String realWarehouseCode;

    @ApiModelProperty(value = "发货仓库名称")
    private String realWarehouseName;

    @ApiModelProperty(value = "退货仓库编码")
    private String returnWarehouseCode;

    @ApiModelProperty(value = "退货仓ID")
    private Long returnWarehouseId;

    @ApiModelProperty(value = "退货仓库名称")
    private String returnRealWarehouseName;

    @ApiModelProperty(value = "实仓ID")
    private Long realWarehouseId;

    @ApiModelProperty(value = "备注")
    private String remark;

    @ApiModelProperty(value = "罗马渠道集合",hidden = true)
    private List<String> channelCodeList;

    @ApiModelProperty(value = "发货仓集合",hidden = true)
    private List<String> factoryCodeList;

    @ApiModelProperty(value = "发货仓集合",hidden = true)
    private List<String> realWarehouseCodeList;

    @ApiModelProperty(value = "退货仓集合",hidden = true)
    private List<String> returnRealWarehouseCodeList;

    @ApiModelProperty(value = "创建人ID")
    private Long creator;

    @ApiModelProperty(value = "更新人ID")
    private Long modifier;

    @ApiModelProperty(value = "是否删除")
    private Integer isDeleted;

    @ApiModelProperty(value = "创建时间")
    private Date createTime;

    @ApiModelProperty(value = "更新时间")
    private Date updateTime;
}
