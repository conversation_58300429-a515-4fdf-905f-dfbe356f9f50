package com.rome.stock.innerservice.api.controller;

import com.rome.arch.core.clientobject.Response;
import com.rome.arch.core.exception.RomeException;
import com.rome.arch.util.controller.RomeController;
import com.rome.stock.innerservice.common.ResCode;
import com.rome.stock.innerservice.constant.ResponseMsg;
import com.rome.stock.innerservice.infrastructure.mapper.WarehouseRecordMapper;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiResponse;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;

import javax.annotation.Resource;
import java.util.List;


@Slf4j
@RomeController
@RequestMapping(value = "/stock/v1/backend")
@Api(tags = "后台处理单据数据接口")
public class BackendController {

    @Resource
    private WarehouseRecordMapper warehouseRecordMapper;

    @ApiOperation(value = "批量根据单据号更改sync_wms_status", produces = MediaType.APPLICATION_JSON_VALUE)
    @ApiResponse(code = 200, message = "success")
    @RequestMapping(value = "/batchUpdateSyncWmsStatus", method = RequestMethod.POST)
    public Response batchUpdateSyncWmsStatus(@RequestBody List<String> recordCodes, @RequestParam(value = "syncWmsStatus") Integer syncWmsStatus) {
        try {
            if(CollectionUtils.isEmpty(recordCodes)){
                return ResponseMsg.FAIL.buildMsg(ResCode.STOCK_ERROR_1003,"recordCodes不能为空");
            }
            if(syncWmsStatus == null){
                return ResponseMsg.FAIL.buildMsg(ResCode.STOCK_ERROR_1003,"syncWmsStatus不能为空");
            }
            warehouseRecordMapper.batchUpdateSyncWmsStatus(recordCodes,syncWmsStatus);
            return ResponseMsg.SUCCESS.buildMsg();
        } catch (RomeException e) {
            log.error(e.getMessage(), e);
            return ResponseMsg.FAIL.buildMsg(e.getCode(), e.getMessage());
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return ResponseMsg.EXCEPTION.buildMsg();
        }
    }

}

