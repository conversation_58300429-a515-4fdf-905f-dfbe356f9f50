package com.rome.stock.innerservice.api.dto.template;

import cn.afterturn.easypoi.excel.annotation.Excel;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;

@Data
@EqualsAndHashCode
public class StockRecordExportTemplate{
    /**
     * 仓库编码
     */
    @Excel(name = "仓库编码")
    private String warehouseCode;
    /**
     * 仓库名称
     */
    @Excel(name = "仓库名称")
    private String warehouseName;
    /**
     * 物料编号
     */
    @Excel(name = "商品编号")
    private String skuCode;
    /**
     * 物料名称
     */
    @Excel(name = "商品名称")
    private String skuName;
    /**
     * 物料标题
     */
    @Excel(name = "商品标题")
    private String skuTitle;
    /**
     * 物料单位编码
     */
    @Excel(name = "商品单位编码")
    private String skuUnitCode;
    /**
     * 物料单位名称
     */
    @Excel(name = "商品单位名称")
    private String skuUnit;
    /**
     * 物料品类
     */
    @Excel(name = "商品品类")
    private String skuType;

    @Excel(name = "商品69码")
    private String barCode;
    /**
     * 库存现有量
     */
    @Excel(name = "库存总量")
    private BigDecimal realQty;
    /**
     * 库存可用量
     */
    @Excel(name = "库存可用量")
    private BigDecimal availableQty;
    /**
     * 锁定库存量
     */
    @Excel(name = "锁定库存量")
    private BigDecimal lockQty;
    /**
     * 质检锁定量
     */
    @Excel(name = "质检锁定量")
    private BigDecimal qualityQty;

    /**
     * 在途库存量
     */
    @Excel(name = "在途库存量")
    private BigDecimal onroadQty;

    @Excel(name = "采购在途库存")
    private BigDecimal purchaseOnRoadStock;

    /**
     * 未出库完成在途库存
     */
    @Excel(name = "未出库完成在途库存")
    private BigDecimal unCompleteOnRoadStock;

    /**
     * 冻结库存
     */
    @Excel(name = "冻结库存")
    private BigDecimal unqualifiedQty;

    /**
     * 箱单位名称
     */
    @Excel(name = "箱单位")
    private String boxUnitName;

    /**
     * 箱单位数量
     */
    @Excel(name = "箱单位数量")
    private BigDecimal boxUnitCount;

    /**
     * 发货单位
     */
    @Excel(name = "发货单位")
    private String deliveryUnitName;

    /**
     * 发货单位数量
     */
    @Excel(name = "发货单位数量")
    private BigDecimal deliveryUnitCount;

    /**
     * 库存质检锁定量
     */
    @Excel(name = "库存质检锁定量(箱)")
    private BigDecimal qualityQtyBox;

    /**
     * 商品经营状态
     */
    @Excel(name = "商品经营状态")
    private String saleStatusName;
}
