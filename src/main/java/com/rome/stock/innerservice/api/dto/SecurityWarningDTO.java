package com.rome.stock.innerservice.api.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.Date;
import java.util.List;

@Data
public class SecurityWarningDTO extends Pagination implements Serializable {

    @ApiModelProperty(value="主键")
    private Long id;


    @ApiModelProperty(value="仓库编号")
    @NotBlank(message = "仓库编号不能为空")
    private String warehouseCode;


    @ApiModelProperty(value="仓库ID")
    @NotNull(message = "仓库ID不能为空")
    private Long warehouseId;

    @ApiModelProperty(value="仓库名称")
    @NotBlank(message = "仓库名称不能为空")
    private String warehouseName;

    @ApiModelProperty(value="仓库类型")
    private Integer warehouseType;


    @ApiModelProperty(value="如果为门店仓，店铺编码不能为空")
    private String shopCode;


    @ApiModelProperty(value="状态（0--启用 1--禁用)")
    private Integer status;


    @ApiModelProperty(value="备注信息")
    private String remark;


    @ApiModelProperty(value="创建时间")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;

    @ApiModelProperty(value="仓库名称或仓库编码",hidden = true)
    private String nameOrCode;


    @ApiModelProperty(value="修改时间")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date updateTime;



    @ApiModelProperty(value="创建人")
    private Long creator;


    @ApiModelProperty(value="更新人")
    private Long modifier;

    @ApiModelProperty(value="明细集合")
    private List<SecurityWarningDetailDTO> securityWarningDetailList;
}
