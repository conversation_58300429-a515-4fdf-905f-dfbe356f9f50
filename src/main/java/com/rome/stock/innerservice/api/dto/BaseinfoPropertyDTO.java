/**
 * Filename BaseinfoPropertyDTO.java
 * Company 上海来伊份科技有限公司。
 *
 * <AUTHOR>
 * @version
 */
package com.rome.stock.innerservice.api.dto;

import com.rome.stock.wms.config.BaseinfoProperty;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 基本信息配置属性
 * <AUTHOR>
 * @since 2024/12/6 16:11
 */
@Data
@EqualsAndHashCode
public class BaseinfoPropertyDTO extends BaseinfoProperty {

    /**
     * 创建人
     */
    @ApiModelProperty(value = "创建人")
    private Long modifier;

    /**
     * 账号
     */
    @ApiModelProperty(value = "账号")
    private String accountName;

}
