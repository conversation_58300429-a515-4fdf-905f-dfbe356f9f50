package com.rome.stock.innerservice.api.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import java.util.Date;

/**
 * DO 实体类
 * 
 * <AUTHOR>
 * @date 2022-01-06
 */
@ApiModel(value = "ChargeAgainstConfigDTO", description = "DO实体类")
@Data
@NoArgsConstructor
@EqualsAndHashCode
public class ChargeAgainstConfigDTO extends Pagination{

	/**
	 * 冲销原单据类型16,"外采冲销" 125,"委外冲销" 28, "仓库报废冲销 17, "外采退货 9, "门店试吃 49, "领用出库
	 */
	@ApiModelProperty(value = "领用出库")
	private Integer originRecordType;

	/**
	 * 原单据出入库类型：1:入库单 2:出库单
	 */
	@ApiModelProperty(value = "原单据出入库类型：1:入库单2:出库单")
	private Integer originBusinessType;

	/**
	 * 冲销前置单据类型 3001,"外采冲销" 3002,"委外冲销" 3003, "仓库报废冲销 3004, "外采退货  3005, "门店试吃 3006, "领用出库
	 */
	@ApiModelProperty(value = "领用出库")
	private Integer frontRecordType;

	/**
	 * 后置单据类型 3016,"外采冲销" 3125,"委外冲销" 3028, "仓库报废冲销 3017, "外采退货 3009, "门店试吃 3049, "领用出库
	 */
	@ApiModelProperty(value = "领用出库")
	private Integer warehouseRecordType;

	/**
	 * 冲销单描述
	 */
	@ApiModelProperty(value = "冲销单描述")
	private String desc;

	/**
	 * 处理单据出入库回调的serviceName
	 */
	@ApiModelProperty(value = "处理单据出入库回调的serviceName")
	private String serviceName;

	/**
	 * 通知上游系统类型：0,无 1采购中心，2订单中心
	 */
	@ApiModelProperty(value = "通知上游系统类型：0,无1采购中心，2订单中心")
	private Integer notifyType;

	/**
	 * 仓库类型：1 门店 2 仓库
	 */
	@ApiModelProperty(value = "仓库类型：1门店2仓库")
	private Integer wmsType;
}
