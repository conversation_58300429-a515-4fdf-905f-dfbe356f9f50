package com.rome.stock.innerservice.api.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;

@Data
@EqualsAndHashCode
public class Base {
	/**
	 * 创建时间
	 */
	@ApiModelProperty(value = "创建时间")
	private Date createTime;
	/**
	 * 修改时间
	 */
	@ApiModelProperty(value = "修改时间")
	private Date updateTime;
	/**
	 * 创建人
	 */
	@ApiModelProperty(value = "创建人")
	private Long creator;
	/**
	 * 更新人
	 */
	@ApiModelProperty(value = "更新人")
	private Long modifier;
	/**
	 * 是否可用：0-否，1-是
	 */
	@ApiModelProperty(value = "是否可用")
	private Byte isAvailable;
	/**
	 * 是否逻辑删除：0-否，1-是
	 */
	@ApiModelProperty(value = "是否逻辑删除")
	private Byte isDeleted;
	/**
	 * 版本号
	 */
	@ApiModelProperty(value = "版本号")
	private Integer versionNo;
	/**
	 * 租户ID
	 */
	@ApiModelProperty(value = "租户ID")
	private Long tenantId;
	/**
	 * 业务应用ID
	 */
	@ApiModelProperty(value = "业务应用ID")
	private String appId;
}
