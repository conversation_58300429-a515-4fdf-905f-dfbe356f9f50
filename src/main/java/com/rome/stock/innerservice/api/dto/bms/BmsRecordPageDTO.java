package com.rome.stock.innerservice.api.dto.bms;

import com.rome.stock.innerservice.api.dto.Pagination;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;

/**
* bms出入库数据对应实体
* <AUTHOR>
* @since 2023-10-12
*/
@Data
@EqualsAndHashCode
public class BmsRecordPageDTO extends Pagination {

        @ApiModelProperty(hidden = true)
        private Long id;

        @ApiModelProperty(value = "来源系统")
        private String oriSys;

        @ApiModelProperty(value = "流水号")
        private String serialNo;


        @ApiModelProperty(value = "是否电商toc单据")
        private String isToc;

        @ApiModelProperty(value = "业务单号")
        private String custOrderNo;

        @ApiModelProperty(value = "后置单单据编码",hidden = true)
        private String warehouseRecordCode;

        @ApiModelProperty(value = "仓库编码")
        private String whseCode;

        @ApiModelProperty(value = "仓库名称")
        private String warehouseCodeDesc;

        @ApiModelProperty(value = "单据类型")
        private String orderType;

        @ApiModelProperty(value = "作业时间")
        private String stockDate;

        @ApiModelProperty(value = "作业模式")
        private String workModel;

        @ApiModelProperty(value = "客户编码")
        private String custCode;

        @ApiModelProperty(value = "客户名称")
        private String custName;

        @ApiModelProperty(value = "订单渠道")
        private String saleChannel;

        @ApiModelProperty(value = "多次确认标识:0-全部;1-部分;默认0")
        private String confirmFlag;

        @ApiModelProperty(value = "服务产品:1-收货;2-发货;")
        private String confirmType;


        @ApiModelProperty(value = "总体积")
        private BigDecimal totalVolume;

        @ApiModelProperty(value = "总重量")
        private BigDecimal totalWeight;

        @ApiModelProperty(value = "总商品数(件)")
        private BigDecimal totalCmmdtyQty;

        @ApiModelProperty(value = "总货值")
        private BigDecimal totalValue;

        @ApiModelProperty(value = "总包裹数(个)")
        private BigDecimal totalExpressQty;

        @ApiModelProperty(value = "转换后箱数")
        private BigDecimal totalBox;

        @ApiModelProperty(value = "拆零件数")
        private BigDecimal pieceNum;

        @ApiModelProperty(value = "创建时间")
        private String createTime;

        /**
         * 开始时间
         */
        @ApiModelProperty(value = "开始时间")
        private String startCreateTime;
        /**
         * 结束时间
         */
        @ApiModelProperty(value = "结束时间")
        private String endCreateTime;

        @ApiModelProperty(value = "作业开始时间")
        private String startStockTime;
        /**
         * 结束时间
         */
        @ApiModelProperty(value = "作业结束时间")
        private String endStockTime;

        @ApiModelProperty(value = "导出excel操作")
        private boolean importExcel;
}
