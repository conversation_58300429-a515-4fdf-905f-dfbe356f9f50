package com.rome.stock.innerservice.api.dto.ehub;

import lombok.Data;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlRootElement;

@XmlAccessorType(XmlAccessType.FIELD)
@XmlRootElement(name = "WLLINKED")
@Data
public class WLLINKED {
	
	@XmlElement(name="HEAD")
	private HEAD head;

	@XmlElement(name="XML_DATA")
	private XMLDATA xmlData;

	
}
