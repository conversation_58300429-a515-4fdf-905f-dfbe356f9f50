package com.rome.stock.innerservice.api.dto.assemble;

import java.math.BigDecimal;

import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 类AssembleDetail的实现描述：加工商品明细
 *
 * <AUTHOR> 2019/5/17 10:24
 */
@Data
@EqualsAndHashCode
public class AssembleProDetailDTO {

    /**
     * 商品sku编码
     */
    private Long skuId;
    /**
     * 商品sku编码
     */
    private String skuCode;
    /**
     * 商品sku名称
     */
    private String skuName;
    /**
     * 数量
     */
    private BigDecimal skuQty;
    /**
     * 单位
     */
    private String unit;

    /**
     * 单位code
     */
    private String unitCode;

    /**
     * 规格
     */
    private String spec;

    /**
     * 移动数量
     */
    private BigDecimal moveQty;

    /**
     * 移动类型(1.入库  2.出库)
     */
    private Integer moveType;

    /**
     * 成品skuId
     */
    private Long fullSkuId;

    /**
     * 成品skuId
     */
    private String fullSkuName;
}
