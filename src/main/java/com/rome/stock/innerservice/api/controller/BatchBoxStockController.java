package com.rome.stock.innerservice.api.controller;

import com.github.pagehelper.PageInfo;
import com.rome.arch.core.clientobject.Response;
import com.rome.arch.core.exception.RomeException;
import com.rome.arch.util.controller.RomeController;
import com.rome.stock.innerservice.api.dto.BatchStockBoxDetailDTO;
import com.rome.stock.innerservice.common.ResCode;
import com.rome.stock.innerservice.constant.ParamValidator;
import com.rome.stock.innerservice.constant.ResponseMsg;
import com.rome.stock.innerservice.domain.service.BatchStockBoxDetailService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiResponse;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;

/**
 * @Description 批次原箱管理
 * @Version 1.0
 */
@Slf4j
@RomeController
@RequestMapping("/stock/v1/batch_box_stock")
@Api(tags={"批次原箱管理"})
public class BatchBoxStockController {

    @Autowired
    private BatchStockBoxDetailService batchStockBoxDetailService;

    private ParamValidator validator = ParamValidator.INSTANCE;


    @ApiOperation(value = "根据条件查询批次原箱库存", nickname = "queryBatchBoxByPage", produces = MediaType.APPLICATION_JSON_VALUE)
    @ApiResponse(code = 200, message = "success")
    @RequestMapping(value = "/queryBatchBoxByPage", method = RequestMethod.POST)
    public Response<PageInfo<BatchStockBoxDetailDTO>> queryBatchBoxByPage(@RequestBody BatchStockBoxDetailDTO paramDto) {
        try {
            PageInfo<BatchStockBoxDetailDTO> dtoList = batchStockBoxDetailService.queryBatchBoxByPage(paramDto);
            return ResponseMsg.SUCCESS.buildMsg(dtoList);
        } catch (RomeException e) {
            log.error(e.getMessage(), e);
            return ResponseMsg.FAIL.buildMsg(e.getCode(),e.getMessage());
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return ResponseMsg.EXCEPTION.buildMsg(ResCode.STOCK_ERROR_1003, ResCode.STOCK_ERROR_1003_DESC + e.getMessage());
        }
    }
    

}
