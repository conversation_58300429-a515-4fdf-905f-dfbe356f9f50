package com.rome.stock.innerservice.api.dto.cmkanban;

import java.util.List;

import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 类KBChannelCode的实现描述：看板渠道类
 *
 * <AUTHOR> 2020/7/11 20:57
 */
@Data
@EqualsAndHashCode
public class KbWarehouseDTO {

    /**
     * 仓库code
     */
    private Long realWarehouseId;

    /**
     * 仓库名
     */
    private String warehouseName;

    /**
     * 仓库code
     */
    private String warehouseCode;

    /**
     * 看板渠道类
     */
    private List<KbChannelDTO> kbChannelList;
}
