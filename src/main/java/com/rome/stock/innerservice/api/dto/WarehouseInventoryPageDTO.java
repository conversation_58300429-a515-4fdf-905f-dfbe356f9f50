package com.rome.stock.innerservice.api.dto;

import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * 仓库盘点单
 */

@Data
@EqualsAndHashCode
public class WarehouseInventoryPageDTO extends Pagination {

    /**
     * 唯一主键
     */
    private Long id;



    /**
     * 盘点单id集合
     */
    private List<Long> frontRecordIds;

    /**
     * 盘点单号
     */
    private String recordCode;

    /**
     * 关联单号（wms盘点单号）
     */
    private String outRecordCode;

    /**
     * 仓库编码
     */
    private String realWarehouseCode;

    /**
     * 外部仓库编码
     */
    private String realWarehouseOutCode;

    /**
     * 盘点仓库
     */
    private String realWarehouseName;

    /**
     * 工厂编码
     */
    private String factoryCode;

    /**
     * 工厂名称
     */
    private String factoryName;

    /**
     * 盘点类型
     */
    private Integer businessType;

    /**
     * 盘点单状态
     */
    private Integer recordStatus;

    /**
     * 备注
     */
    private String remark;

    /**
     * 盘点日期
     */
    private Date outCreateTime;

    /**
     * 创建人
     */
    private Long creator;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 商品skuId集合
     */
    private List<Long> skuIds;

    /**
     * 开始时间
     */
    private Date startTime;

    /**
     * 结束时间
     */
    private Date endTime;

    /**
     * 商品实盘总数
     */
    private BigDecimal skuCount;
    /**
     * 商品快照总数(账面数)
     */
    private BigDecimal accCount;
    /**
     * 商品快照总数
     */
    private BigDecimal diffCount;

    /**
     * 审核状态
     */
    private Long checkStatus;
    /**
     * 操作人工号
     */
    private String modifierCode;
    /**
     * 是否有差异，0.否，1.是
     */
    private Integer isDiff;

    /**
     * 审核原因
     */
    private String recordStatusReason;
}
