package com.rome.stock.innerservice.api.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.Valid;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.util.Date;
import java.util.List;

/**
 * @Doc:.....
 * @Author: lchy
 * @Date: 2019/7/31
 * @Version 1.0
 */
@Data
@EqualsAndHashCode
public class AdjustSkuStockDTO {
    @ApiModelProperty(value = "外部系统编码,幂等用", required = true)
    @NotBlank
    private String outRecordCode;
    @ApiModelProperty(value = "玄天系统发起调整库存的时间")
    @NotNull(message="玄天系统发起调整库存的时间不能为空")
    private Date outCreateTime;
    @ApiModelProperty(value = "调整库存的备注信息")
    private String remark;
    @ApiModelProperty(value = "商家ID", required = true)
//    @NotNull
    private Long merchantId;

    @ApiModelProperty(value = "门店仓Code", required = true)
    private String shopCode;

    @ApiModelProperty(value = "非标品标识", required = true)
    private String unStandardFlag;

    @ApiModelProperty(value = "仓库编码", required = true)
    @NotBlank
    private String warehouseCode;

    @ApiModelProperty(value = "工厂编码", required = true)
    @NotBlank
    private String factoryCode;

    @ApiModelProperty(value = "调整明细", required = true)
    @NotNull
    @Valid
    private List<SkuDetailDTO> details;

    @ApiModelProperty(value = "是否虚拟物品：0-否，1-是", required = true)
    private Integer virtualSkuFlag;

    @ApiModelProperty(value = "操作人编号")
    private Long modifier;
    
    @ApiModelProperty(value = "渠道编号")
    private String channelCode;
}
