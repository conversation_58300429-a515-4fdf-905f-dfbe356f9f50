package com.rome.stock.innerservice.api.dto.qry;

import com.rome.stock.innerservice.api.dto.Pagination;
import com.rome.stock.innerservice.api.dto.RealWarehouseParamDTO;
import lombok.Data;

import java.util.List;

@Data
public class WareHouseRecordCondition extends Pagination {

    private List<RealWarehouseParamDTO> realWarehouseParamDTOS;

    private List<Long> realWareHouseIds;

    /**
     * 1- 出库单
     * 2- 入库单
     * */
    private Integer businessType;
}
