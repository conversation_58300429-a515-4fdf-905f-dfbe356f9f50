package com.rome.stock.innerservice.api.dto.cmkanban;

import java.util.List;

import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 类KBChannelDTO的实现描述：看板渠道类
 *
 * <AUTHOR> 2020/7/11 21:03
 */
@Data
@EqualsAndHashCode
public class KbChannelDTO {

    /**
     * 渠道code
     */
    private String channelCode;

    /**
     * 渠道类
     */
    private String channelName;

    /**
     * 渠道昵称
     */
    private String channelNickName;

    /**
     * 看板类型DTO列表
     */
    private List<KbOrderTypeDTO> orderTypeList;


}
