package com.rome.stock.innerservice.api.dto.assemble;

import java.util.Date;
import java.util.List;

import lombok.Data;

/**
 * 类AssembleOrderDetail的实现描述：加工单明细
 *
 * <AUTHOR> 2019/5/17 10:26
 */
@Data
public class AssembleOrderDetailDTO {

    /**
     * 单据编号
     */
    private Long id;

    /**
     * 单据编号
     */
    private String recordCode;

    /**
     * 门店ID
     */
    private String shopCode;

    /**
     * 门店名称
     */
    private String shopName;

    /**
     * 单据类型：12-加工需求，9-反拆需求单
     */
    private Integer recordType;

    /**
     * 要求要货时间
     */
    private Date requDeliveryTime;
    /**
     * 计划交货时间
     */
    private Date planDeliveryTime;
    /**
     * 实际叫货时间
     */
    private Date realDeliveryTime;
    /**
     * 单据状态审核原因
     */
    private String recordStatusReason;

    /**
     * 成品
     */
    private List<AssembleProDetailDTO> fullProList;


    /**
     * 拆品
     */
    private List<AssembleProDetailDTO> spitProList;

}
