package com.rome.stock.innerservice.api.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;

@Data
@EqualsAndHashCode
public class InventoryRealWarehouseDTO implements Serializable{

    private static final long serialVersionUID = 5277562597618552343L;

    @ApiModelProperty(value = "主键")
    private Long id;
    @ApiModelProperty(value = "仓库编码")
    private String realWarehouseCode;
    @ApiModelProperty(value = "仓库外部编号-wms")
    private String realWarehouseOutCode;
    @ApiModelProperty(value = "工厂编号")
    private String factoryCode;
    @ApiModelProperty(value = "工厂名称")
    private String factoryName;
    @ApiModelProperty(value = "仓库名称")
    private String realWarehouseName;
    @ApiModelProperty(value = "仓库类型")
    private Integer realWarehouseType;
    @ApiModelProperty(value = "仓库类型名称")
    private String realWarehouseTypeName;
    @ApiModelProperty(value = "仓库状态 1：可选 0：不可选")
    private Integer status;

}
