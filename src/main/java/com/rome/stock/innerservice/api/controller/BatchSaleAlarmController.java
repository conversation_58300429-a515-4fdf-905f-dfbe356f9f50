package com.rome.stock.innerservice.api.controller;

import com.github.pagehelper.PageInfo;
import com.google.common.collect.Lists;
import com.rome.arch.core.clientobject.Response;
import com.rome.arch.core.exception.RomeException;
import com.rome.arch.util.controller.RomeController;
import com.rome.stock.innerservice.api.dto.StockAlarmConfigDTO;
import com.rome.stock.innerservice.api.dto.StockAlarmConfigDetailDTO;
import com.rome.stock.innerservice.api.dto.alarm.BatchSaleAlarmDTO;
import com.rome.stock.innerservice.api.dto.alarm.BatchSaleAlarmDetailDTO;
import com.rome.stock.innerservice.api.dto.alarm.ExeStoreBatchHeadDTO;
import com.rome.stock.innerservice.api.dto.alarm.QueryNeedExeHeadListDTO;
import com.rome.stock.innerservice.common.ResCode;
import com.rome.stock.innerservice.constant.ResponseMsg;
import com.rome.stock.innerservice.domain.service.BatchSaleAlarmService;
import com.rome.stock.innerservice.domain.service.StockAlarmHeadService;
import com.rome.stock.innerservice.domain.service.StoreBatchSaleAlarmService;
import com.rome.stock.innerservice.infrastructure.dataobject.alarm.StockAlarmHeadDO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiResponse;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.exception.ExceptionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;

@Slf4j
@RomeController
@RequestMapping("/stock/v1/batchSaleAlarm")
@Api(tags = {"批次库存滞销预警服务接口"})
public class BatchSaleAlarmController {

    @Autowired
    private BatchSaleAlarmService batchSaleAlarmService;
    @Autowired
    private StockAlarmHeadService stockAlarmHeadService;
    @Autowired
    private StoreBatchSaleAlarmService storeBatchSaleAlarmService;

    @ApiOperation(value = "根据条件查询仓库批次库存滞销预警", nickname = "queryBatchSaleAlarmListByCondition", produces = MediaType.APPLICATION_JSON_VALUE)
    @ApiResponse(code = 200, message = "success")
    @RequestMapping(value = "/queryBatchSaleAlarmListByCondition", method = RequestMethod.POST)
    public Response<PageInfo<BatchSaleAlarmDTO>> queryBatchSaleAlarmListByCondition(@RequestBody BatchSaleAlarmDTO paramDto) {
        try {
            PageInfo<BatchSaleAlarmDTO> dtoList = batchSaleAlarmService.queryBatchSaleAlarmListByCondition(paramDto);
            return ResponseMsg.SUCCESS.buildMsg(dtoList);
        } catch (RomeException e) {
            log.error(e.getMessage(), e);
            return ResponseMsg.FAIL.buildMsg(e.getCode(), e.getMessage());
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return ResponseMsg.EXCEPTION.buildMsg(ResCode.STOCK_ERROR_1003, ResCode.STOCK_ERROR_1003_DESC + e.getMessage());
        }
    }


    @ApiOperation(value = "根据条件查询仓库批次库存滞销预警明细", nickname = "listByAlarmId", produces = MediaType.APPLICATION_JSON_VALUE)
    @ApiResponse(code = 200, message = "success")
    @RequestMapping(value = "/listByAlarmId", method = RequestMethod.POST)
    public Response<List<BatchSaleAlarmDetailDTO>> listByAlarmId(@RequestParam(value = "alarmId") Long alarmId) {
        try {
            List<BatchSaleAlarmDetailDTO> dtoList = batchSaleAlarmService.listByAlarmIdList(Lists.newArrayList(alarmId));
            return ResponseMsg.SUCCESS.buildMsg(dtoList);
        } catch (RomeException e) {
            log.error(e.getMessage(), e);
            return ResponseMsg.FAIL.buildMsg(e.getCode(), e.getMessage());
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return ResponseMsg.EXCEPTION.buildMsg(ResCode.STOCK_ERROR_1003, ResCode.STOCK_ERROR_1003_DESC + e.getMessage());
        }
    }

    @ApiOperation(value = "根据条件查询仓库批次库存滞销预警明细", nickname = "listByAlarmId", produces = MediaType.APPLICATION_JSON_VALUE)
    @ApiResponse(code = 200, message = "success")
    @RequestMapping(value = "/listByAlarmIdList", method = RequestMethod.POST)
    public Response<List<BatchSaleAlarmDetailDTO>> listByAlarmIdList(@RequestBody List<Long> alarmIdList) {
        try {
            List<BatchSaleAlarmDetailDTO> dtoList = batchSaleAlarmService.listByAlarmIdList(alarmIdList);
            return ResponseMsg.SUCCESS.buildMsg(dtoList);
        } catch (RomeException e) {
            log.error(e.getMessage(), e);
            return ResponseMsg.FAIL.buildMsg(e.getCode(), e.getMessage());
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return ResponseMsg.EXCEPTION.buildMsg(ResCode.STOCK_ERROR_1003, ResCode.STOCK_ERROR_1003_DESC + e.getMessage());
        }
    }


    @ApiOperation(value = "分页查询库存预警配置", nickname = "queryStockAlarmConfigList", produces = MediaType.APPLICATION_JSON_VALUE)
    @ApiResponse(code = 200, message = "success")
    @RequestMapping(value = "/queryStockAlarmConfigList", method = RequestMethod.POST)
    public Response<PageInfo<StockAlarmConfigDTO>> queryStockAlarmConfigList(@RequestBody StockAlarmConfigDTO stockAlarmConfigDTO) {
        try {
            PageInfo<StockAlarmConfigDTO> page = batchSaleAlarmService.queryStockAlarmConfigList(stockAlarmConfigDTO);
            return ResponseMsg.SUCCESS.buildMsg(page);
        } catch (RomeException e) {
            log.error(e.getMessage(), e);
            return ResponseMsg.FAIL.buildMsg(e.getCode(), e.getMessage());
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return ResponseMsg.EXCEPTION.buildMsg(ResCode.STOCK_ERROR_1003, ResCode.STOCK_ERROR_1003_DESC + e.getMessage());
        }
    }


    @ApiOperation(value = "查询库存预警配置详情", nickname = "listConfigDetailByAlarmId", produces = MediaType.APPLICATION_JSON_VALUE)
    @ApiResponse(code = 200, message = "success")
    @RequestMapping(value = "/listConfigDetailByAlarmId", method = RequestMethod.POST)
    public Response<List<StockAlarmConfigDetailDTO>> listConfigDetailByAlarmId(@RequestParam(value = "alarmId") Long alarmId) {
        try {
            List<StockAlarmConfigDetailDTO> list = batchSaleAlarmService.listConfigDetailByAlarmId(alarmId);
            return ResponseMsg.SUCCESS.buildMsg(list);
        } catch (RomeException e) {
            log.error(e.getMessage(), e);
            return ResponseMsg.FAIL.buildMsg(e.getCode(), e.getMessage());
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return ResponseMsg.EXCEPTION.buildMsg(ResCode.STOCK_ERROR_1003, ResCode.STOCK_ERROR_1003_DESC + e.getMessage());
        }
    }


    @ApiOperation(value = "新增或更新库存预警配置及详情", nickname = "saveOrUpdateConfigAndDetailList", produces = MediaType.APPLICATION_JSON_VALUE)
    @ApiResponse(code = 200, message = "success")
    @RequestMapping(value = "/saveOrUpdateConfigAndDetailList", method = RequestMethod.POST)
    public Response saveOrUpdateConfigAndDetailList(@RequestBody StockAlarmConfigDTO dto) {
        try {
            batchSaleAlarmService.saveOrUpdateConfigAndDetailList(dto);
            return ResponseMsg.SUCCESS.buildMsg();
        } catch (RomeException e) {
            log.error(e.getMessage(), e);
            return ResponseMsg.FAIL.buildMsg(e.getCode(), e.getMessage());
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return ResponseMsg.EXCEPTION.buildMsg(ResCode.STOCK_ERROR_1003, ResCode.STOCK_ERROR_1003_DESC + e.getMessage());
        }
    }


    @ApiOperation(value = "仓库库存滞销执行", nickname = "calAlarmStock", produces = MediaType.APPLICATION_JSON_VALUE)
    @ApiResponse(code = 200, message = "success")
    @RequestMapping(value = "/calAlarmStock", method = RequestMethod.POST)
    Response calAlarmStock(@RequestBody StockAlarmConfigDTO stockAlarmConfigDTO) {
        boolean isCalSuccess = false;
        String errorMsg = "";
        StockAlarmHeadDO stockAlarmHeadDO = null;
        try {
            stockAlarmHeadDO = stockAlarmHeadService.startAlarmHead(stockAlarmConfigDTO);
            //是否需要执行
            if (stockAlarmHeadDO.isNeedExecute()) {
                stockAlarmConfigDTO.setHeadId(stockAlarmHeadDO.getId());
                batchSaleAlarmService.calAlarmStock(stockAlarmConfigDTO);
                isCalSuccess = true;
            }
            return ResponseMsg.SUCCESS.buildMsg("");
        } catch (RomeException e) {
            log.error(e.getMessage(), e);
            errorMsg = e.getMessage();
            return ResponseMsg.FAIL.buildMsg(e.getCode(), e.getMessage());
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            errorMsg = ExceptionUtils.getFullStackTrace(e);
            return ResponseMsg.EXCEPTION.buildMsg(ResCode.STOCK_ERROR_1003, ResCode.STOCK_ERROR_1003_DESC + e.getMessage());
        } finally {
            if (stockAlarmHeadDO != null && stockAlarmHeadDO.isNeedExecute()) {
                stockAlarmHeadService.saveAlarmHeadResult(stockAlarmHeadDO.getId(), isCalSuccess, errorMsg);
            }
        }
    }


    @ApiOperation(value = "更新预警配置状态", nickname = "changeStatus", produces = MediaType.APPLICATION_JSON_VALUE)
    @ApiResponse(code = 200, message = "success")
    @RequestMapping(value = "/changeStatus", method = RequestMethod.POST)
    public Response changeStatus(@RequestBody StockAlarmConfigDTO dto) {
        try {
            batchSaleAlarmService.changeStatus(dto);
            return ResponseMsg.SUCCESS.buildMsg();
        } catch (RomeException e) {
            log.error(e.getMessage(), e);
            return ResponseMsg.FAIL.buildMsg(e.getCode(), e.getMessage());
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return ResponseMsg.EXCEPTION.buildMsg(ResCode.STOCK_ERROR_1003, ResCode.STOCK_ERROR_1003_DESC + e.getMessage());
        }
    }


    @ApiOperation(value = "查询需要执行的配置信息", nickname = "queryAllConfigList", produces = MediaType.APPLICATION_JSON_VALUE)
    @ApiResponse(code = 200, message = "success")
    @RequestMapping(value = "/queryAllConfigList", method = RequestMethod.POST)
    Response<List<StockAlarmConfigDTO>> queryAllConfigList(@RequestBody StockAlarmConfigDTO stockAlarmConfigDTO) {
        try {
            return ResponseMsg.SUCCESS.buildMsg(batchSaleAlarmService.queryAllConfigList(stockAlarmConfigDTO));
        } catch (RomeException e) {
            log.error(e.getMessage(), e);
            return ResponseMsg.FAIL.buildMsg(e.getCode(), e.getMessage());
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return ResponseMsg.EXCEPTION.buildMsg(ResCode.STOCK_ERROR_1003, ResCode.STOCK_ERROR_1003_DESC + e.getMessage());
        }
    }


    @ApiOperation(value = "仓库库存滞销发送飞书预警消息", nickname = "alarmStockNotice", produces = MediaType.APPLICATION_JSON_VALUE)
    @ApiResponse(code = 200, message = "success")
    @RequestMapping(value = "/alarmStockNotice", method = RequestMethod.GET)
    Response alarmStockNotice() {
        try {
            batchSaleAlarmService.alarmStockNotice();
            return ResponseMsg.SUCCESS.buildMsg("");
        } catch (RomeException e) {
            log.error(e.getMessage(), e);
            return ResponseMsg.FAIL.buildMsg(e.getCode(), e.getMessage());
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return ResponseMsg.EXCEPTION.buildMsg(ResCode.STOCK_ERROR_1003, ResCode.STOCK_ERROR_1003_DESC + e.getMessage());
        }
    }


    @ApiOperation(value = "仓库库存滞销执行记录删除多余的数据(小于库存日期)", nickname = "deleteExtData", produces = MediaType.APPLICATION_JSON_VALUE)
    @ApiResponse(code = 200, message = "success")
    @RequestMapping(value = "/deleteExtData", method = RequestMethod.GET)
    Response deleteExtData(@RequestParam("dateStr") String dateStr) {
        try {
            batchSaleAlarmService.deleteExtData(dateStr);
            return ResponseMsg.SUCCESS.buildMsg("");
        } catch (RomeException e) {
            log.error(e.getMessage(), e);
            return ResponseMsg.FAIL.buildMsg(e.getCode(), e.getMessage());
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return ResponseMsg.EXCEPTION.buildMsg(ResCode.STOCK_ERROR_1003, ResCode.STOCK_ERROR_1003_DESC + e.getMessage());
        }
    }


    @ApiOperation(value = "门店批次预警-生成执行记录", nickname = "generateStoreHead", produces = MediaType.APPLICATION_JSON_VALUE)
    @ApiResponse(code = 200, message = "success")
    @RequestMapping(value = "/generateStoreHead", method = RequestMethod.POST)
    Response generateStoreHead() {
        try {
            storeBatchSaleAlarmService.generateStoreHead();
            return ResponseMsg.SUCCESS.buildMsg();
        } catch (RomeException e) {
            log.error(e.getMessage(), e);
            return ResponseMsg.FAIL.buildMsg(e.getCode(), e.getMessage());
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return ResponseMsg.EXCEPTION.buildMsg(ResCode.STOCK_ERROR_1003, ResCode.STOCK_ERROR_1003_DESC + e.getMessage());
        }
    }

    @ApiOperation(value = "门店批次预警-查询待执行的记录", nickname = "queryNeedExeHeadList", produces = MediaType.APPLICATION_JSON_VALUE)
    @ApiResponse(code = 200, message = "success")
    @RequestMapping(value = "/queryNeedExeHeadList", method = RequestMethod.POST)
    Response<List<Long>> queryNeedExeHeadList(@RequestBody QueryNeedExeHeadListDTO dto) {
        try {
            return ResponseMsg.SUCCESS.buildMsg(storeBatchSaleAlarmService.queryNeedExeHeadList(dto));
        } catch (RomeException e) {
            log.error(e.getMessage(), e);
            return ResponseMsg.FAIL.buildMsg(e.getCode(), e.getMessage());
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return ResponseMsg.EXCEPTION.buildMsg(ResCode.STOCK_ERROR_1003, ResCode.STOCK_ERROR_1003_DESC + e.getMessage());
        }
    }

    @ApiOperation(value = "门店批次预警-执行预警记录", nickname = "exeStoreBatchHead", produces = MediaType.APPLICATION_JSON_VALUE)
    @ApiResponse(code = 200, message = "success")
    @RequestMapping(value = "/exeStoreBatchHead", method = RequestMethod.POST)
    Response<Boolean> exeStoreBatchHead(@RequestBody ExeStoreBatchHeadDTO dto) {
        try {
            return ResponseMsg.SUCCESS.buildMsg(storeBatchSaleAlarmService.exeStoreBatchHead(dto));
        } catch (RomeException e) {
            log.error(e.getMessage(), e);
            return ResponseMsg.FAIL.buildMsg(e.getCode(), e.getMessage());
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return ResponseMsg.EXCEPTION.buildMsg(ResCode.STOCK_ERROR_1003, ResCode.STOCK_ERROR_1003_DESC + e.getMessage());
        }
    }

}
