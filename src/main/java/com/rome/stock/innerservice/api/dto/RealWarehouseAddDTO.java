package com.rome.stock.innerservice.api.dto;

import lombok.Data;

import java.io.Serializable;
import java.util.List;

import io.swagger.annotations.ApiModelProperty;

@Data
public class RealWarehouseAddDTO implements Serializable {

	/**
     * 唯一主键
     */
    private Long id;
    private String realWarehouseCode;
    private String realWarehouseOutCode;
    private String factoryCode;
    private String realWarehouseName;
    private Integer realWarehouseType;
    private String shopCode;
    private String realWarehousePostcode;
    private String realWarehouseMobile;
    private String realWarehousePhone;
    private String realWarehouseEmail;
    private String realWarehouseCountry;
    private String realWarehouseProvince;
    private String realWarehouseCity;
    private String realWarehouseCounty;
    private String realWarehouseArea;
    private String realWarehouseCountryCode;
    private String realWarehouseProvinceCode;
    private String realWarehouseCityCode;
    private String realWarehouseCountyCode;
    private String realWarehouseAreaCode;
    private String realWarehouseAddress;
    private String realWarehouseContactName;
    private String realWarehouseRemark;
    private Long userId;
    private Integer realWarehouseRank;
    private String regionName;
    private Long regionId;
    private List<Long> labIds;
    
    @ApiModelProperty(value = "仓库状态：0-初始，1-启用，2-停用")
    private Integer realWarehouseStatus;
    @ApiModelProperty(value = "工厂名称")
    private String factoryName;
    @ApiModelProperty(value = "成本中心编码")
    private String costCenterCode;
    @ApiModelProperty(value = "成本中心名称")
    private String costCenterName;
    @ApiModelProperty(value = "仓库业务类型: 1: 直营门店 2: 加盟门店 3: 仓库 4. 其他仓库")
    private Integer rwBusinessType;
    @ApiModelProperty(value = "公司编码")
    private String companyCode;
    @ApiModelProperty(value = "公司名称")
    private String companyName;
    @ApiModelProperty(value = "是否允许负库存(0: 不允许 1: 允许)")
    private Integer allowNegtiveStock;

    @ApiModelProperty(value = "仓店一体标识 0 -非仓店一体   1仓店一体")
    private Integer warehouseStoreIdenti;
}
