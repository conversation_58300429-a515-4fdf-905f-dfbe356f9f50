package com.rome.stock.innerservice.api.dto.cmtransferlog;

import java.util.Date;
import java.util.List;

import com.rome.stock.innerservice.api.dto.Pagination;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 类CmTransferLogPageDTO的实现描述：301过账日志列表DTO
 *
 * <AUTHOR> 2021/5/19 23:31
 */
@ApiModel(value = "CmTransferLogPageDTO", description = "类CmTransferLogPageDTO的实现描述：301过账日志列表DTO")
@Data
public class CmTransferLogPageDTO extends Pagination {

    /**
     * 过账单号
     */
    @ApiModelProperty(value = "过账单号")
    private String recordCode;

    /**
     * 过账状态，0:初始 1:成功 2:失败 3:异常
     */
    @ApiModelProperty(value = "过账状态，0:初始1:成功2:失败3:异常")
    private Integer transferStatus;

    /**
     * 过账总行数
     */
    @ApiModelProperty(value = "过账总行数")
    private Integer lineTotal;

    /**
     * 过账时间
     */
    @ApiModelProperty(value = "过账时间")
    private String postingDate;

    /**
     * 推送时间
     */
    @ApiModelProperty(value = "推送时间")
    private String pushDate;

    @ApiModelProperty(value = "开始时间")
    private Date startTime;

    @ApiModelProperty(value = "结束时间")
    private Date endTime;


    /**
     * 出库工厂编码
     */
    @ApiModelProperty(value = "出库工厂编码")
    private String outFactoryCode;


    /**
     * 出库库存地点
     */
    @ApiModelProperty(value = "出库库存地点")
    private String outWarehouseCode;

    /**
     * 出库库存名称
     */
    @ApiModelProperty(value = "出库库存名称")
    private String outWarehouseName;

    /**
     * 入库工厂编码
     */
    @ApiModelProperty(value = "入库工厂编码")
    private String inFactoryCode;

    /**
     * 入库工厂编码
     */
    @ApiModelProperty(value = "入库工厂编码")
    private String inFactoryName;

    /**
     * 商品sku编码列表
     */
    @ApiModelProperty(value = "商品sku编码列表")
    private List<String> skuCodeList;
}
