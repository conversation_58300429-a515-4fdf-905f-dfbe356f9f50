package com.rome.stock.innerservice.api.dto.qry;

import com.rome.stock.innerservice.api.dto.Pagination;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;
import java.util.List;

@Data
@EqualsAndHashCode
public class SaleWarehouseRecordCondition extends Pagination {
	@ApiModelProperty(value = "出库单编号")
	private  String recordCode;
	@ApiModelProperty(value = "出库单编号集合")
	private  List<String> recordCodeList;
	@ApiModelProperty(value = "单据业务类型：比如门店销售")
	private  Integer recordType;
	@ApiModelProperty(value = "用户账号")
	private String userCode;
	@ApiModelProperty(value = "实仓id")
	private Long realWarehouseId;
	@ApiModelProperty(value = "实仓Type")
	private Integer realWarehouseType;
	@ApiModelProperty(value = "实仓IdList")
	private List<Long> realWarehouseIdList;
	@ApiModelProperty(value = "实仓Code")
	private String realWarehouseCode;
	@ApiModelProperty(value = "单据状态")
	private Integer recordStatus;
	@ApiModelProperty(value = "出库单创建开始时间")
	private Date startTime;
	@ApiModelProperty(value = "出库单创建结束时间")
	private Date endTime;
	@ApiModelProperty(value = "出库单支付开始时间")
	private Date startPayTime;
	@ApiModelProperty(value = "出库单支付结束时间")
	private Date endPayTime;
	@ApiModelProperty(value = "出库单出库开始时间")
	private Date startOutOrInTime;
	@ApiModelProperty(value = "出库单出库结束时间")
	private Date endOutOrInTime;
	@ApiModelProperty(value = "渠道类型")
	private String channelCodes;
	@ApiModelProperty(value = "订单包含的skuid过滤条件")
	private Long skuId;
	@ApiModelProperty(value = "订单包含的skucode过滤条件")
	private String skuCode;
	@ApiModelProperty(value = "订单编号")
	private String orderCode;

	@ApiModelProperty(value = "订单编号列表")
	private List<String> orderCodeList;

	private List<String> channelCodeList;

	@ApiModelProperty(value = "同步捋单系统状态 0-无需同步 1-待同步交货信息 2-已同步")
	private Integer syncFulfillmentStatus;

	@ApiModelProperty(value = "出入库单同步WMS状态：0-无需同步 1-未同步 2-已同步")
	private String syncWmsStatus;

	@ApiModelProperty(value = "出入库单同步WMS状态集合")
	private List<Integer> syncWmsStatusList;
	@ApiModelProperty(value = "原始单号集合")
	private List<String> originOrderCodeList;

	@ApiModelProperty(value = "运单号单号集合")
	private List<String> expressCodeList;

	@ApiModelProperty(value = "分页起始行")
	private Integer pIndex;

	@ApiModelProperty(value = "条数")
	private Integer num;

	@ApiModelProperty(value = "撤单原因")
	private String reasons;

	@ApiModelProperty(value = "订单备注")
	private String orderRemarkUser;

	@ApiModelProperty(value = "0--否 1--有货就发的  2--到时发货")
	private String isPreSale;

	@ApiModelProperty(value = "是否预售集合")
	private List<Integer> isPreSaleList;

	@ApiModelProperty(value = "承诺开始时间")
	private Date promiseStartTime;
	@ApiModelProperty(value = "承诺结束时间")
	private Date promiseEndTime;

	@ApiModelProperty(value = "导出包裹明细")
	private String isExportPackage;

	@ApiModelProperty(value = "导出明细")
	private String isExportDetail;

	@ApiModelProperty(value = "导出包裹明细查询")
	private String isExportPackageQuery;

	@ApiModelProperty(value = "快递公司")
	private List<String> logisticsCodeList;

	@ApiModelProperty(value = "取消标记：0：否 1：是")
	private Integer isCancelRecordStatus;

	@ApiModelProperty(value = "协同不允许查询到的同步wms状态集合",hidden = true)
	private  List<Integer> notSyncWmsStatusList;

	@ApiModelProperty(value = "实仓编码")
	private List<String> realWarehouseCodeList;

	@ApiModelProperty(value = "仓库系统编码")
	private Integer wmsCode;
}
