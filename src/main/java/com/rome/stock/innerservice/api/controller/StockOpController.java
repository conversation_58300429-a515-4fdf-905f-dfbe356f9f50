package com.rome.stock.innerservice.api.controller;

import java.util.List;

import com.rome.arch.core.exception.RomeException;
import com.rome.stock.common.enums.ResponseMsg;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiResponse;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.*;
import com.alibaba.fastjson.JSONObject;
import com.rome.arch.core.clientobject.Response;
import com.rome.stock.innerservice.common.ResCode;
import com.rome.stock.innerservice.domain.service.StockOpToolService;
import com.rome.stock.core.template.compensation.CompensationJob;

import io.swagger.annotations.Api;
import lombok.extern.slf4j.Slf4j;

/**
 * 库存工具
 * <AUTHOR>
 * @since 2019年5月17日 下午5:04:00
 */
@Slf4j
@RestController
@RequestMapping("/stock/stockOp")
@Api(tags={"库存小工具接口"})
public class StockOpController {

    @Autowired
    private StockOpToolService stockOpService;
    
    @Autowired
    private CompensationJob compensationJob;
    
    /**
     * 库存数量操作工具
     * @param requestVO
     * @return
     */
    @RequestMapping(value = "/stockSkuNumCacheOp", method = RequestMethod.POST)
    public Response stockSkuNumCacheOp(@RequestBody JSONObject requestVO) {
    	try {
        	String msg = stockOpService.stockSkuNumCacheOp(requestVO);
        	return Response.builderSuccess(msg);
    	}catch (Exception e) {
            log.error("保存数据出错", e);
            return Response.builderFail(ResCode.STOCK_ERROR_1003, ResCode.STOCK_ERROR_1003_DESC);
        }
    }

    /**
     * 库存数量操作工具-skuCode
     * @param requestVO
     * @return
     */
    @RequestMapping(value = "/stockSkuNumCacheOpSkuCode", method = RequestMethod.POST)
    public Response stockSkuNumCacheOpSkuCode(@RequestBody JSONObject requestVO) {
        try {
            String msg = stockOpService.stockSkuNumCacheOpSkuCode(requestVO);
            return Response.builderSuccess(msg);
        } catch (RomeException e) {
            log.error(e.getMessage(), e);
            return ResponseMsg.FAIL.buildMsg(e.getCode(), e.getMessage());
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return ResponseMsg.FAIL.buildMsg("500000", "系统异常" + e.getMessage());
        }
    }
    /**
     * 库存数量核对
     * @param requestVO
     * @return
     */
    @RequestMapping(value = "/stockNumRedisDBCheckJob", method = RequestMethod.POST)
    public Response stockNumRedisDBCheckJob(@RequestBody JSONObject requestVO) {
    	try {
    		stockOpService.stockNumRedisDBCheckJob();
        	return Response.builderSuccess("成功加入异步库存核对");
    	}catch (Exception e) {
            log.error("保存数据出错", e);
            return Response.builderFail(ResCode.STOCK_ERROR_1003, ResCode.STOCK_ERROR_1003_DESC);
        }
    }
    
    /**
     * 门店初始化库存
     * 
     * {"content":[{"shopCode":"107U","factoryCode":"0001","type":"A"}],"channelConfig":[{"type":"100","typeName":"传统零售"}]}
     * shopCode必须参数,其他为可选  factoryCode现为0001默认为0001,type可能值为A或I默认为A
     * channelConfig渠道配置，为空时，是传统零售
     * @param requestVO
     * @return
     */
    @RequestMapping(value = "/shopInitTool", method = RequestMethod.POST)
    public Response shopInitTool(@RequestBody JSONObject requestVO) {
    	try {
    		List<String> result = stockOpService.shopInitTool(requestVO);
        	return Response.builderSuccess(result);
    	}catch (Exception e) {
            log.error("出错", e);
            return Response.builderFail(ResCode.STOCK_ERROR_1003, ResCode.STOCK_ERROR_1003_DESC);
        }
    }
    
    /**
     * 手动执行补偿队列
     * @param requestVO
     * @return
     */
    @RequestMapping(value = "/runCompensationJob", method = RequestMethod.POST)
    public Response runCompensationJob(@RequestBody JSONObject requestVO) {
    	try {
    		boolean result = compensationJob.taskStart();
        	return Response.builderSuccess("结果" + result);
    	}catch (Exception e) {
            log.error("出错", e);
            return Response.builderFail(ResCode.STOCK_ERROR_1003, ResCode.STOCK_ERROR_1003_DESC);
        }
    }
    
    /**
     * 仓库初始化库存
     * 
     * {"content":[{"warehouseOutCode":"107U","factoryCode":"0001","type":"A","itemCode":["00","11"],"flag":0}],"recordCode":"","transType":0}
     * warehouseOutCode 必须参数,其他为可选  factoryCode现为0001默认为0001,type可能值为A或I默认为A  flag为1大幅仓,为2旺店通,为4欧电云仓,为5邮政EMS仓,为8科捷仓,其他为sap  itemCode为物料编码列表
     * recordCode 单据号,不填为uuid
     * transType 交易类型,不填为0
     * itemCode skuCodes
     * @param requestVO
     * @return
     */
    @RequestMapping(value = "/warehouseInitTool", method = RequestMethod.POST)
    public Response warehouseInitTool(@RequestBody JSONObject requestVO) {
    	try {
    		List<String> result = stockOpService.warehouseInitTool(requestVO);
        	return Response.builderSuccess(result);
    	}catch (Exception e) {
            log.error("出错", e);
            return Response.builderFail(ResCode.STOCK_ERROR_1003, ResCode.STOCK_ERROR_1003_DESC);
        }
    }
    
    /**
     * 批次库存与库存数量核对
     * @param requestVO
     * @return
     */
    @ApiOperation(value = "批次库存与库存数量核对", nickname = "batchStockNumCheckJob", produces = MediaType.APPLICATION_JSON_VALUE)
    @ApiResponse(code = 200, message = "success")
    @RequestMapping(value = "/batchStockNumCheckJob", method = RequestMethod.POST)
    public Response batchStockNumCheckJob(@RequestBody JSONObject requestVO) {
    	try {
    		stockOpService.batchStockNumCheckJob();
        	return Response.builderSuccess("成功加入异步批次库存与库存核对");
    	}catch (Exception e) {
            log.error("保存数据出错", e);
            return Response.builderFail(ResCode.STOCK_ERROR_1003, ResCode.STOCK_ERROR_1003_DESC);
        }
    }
    
}
