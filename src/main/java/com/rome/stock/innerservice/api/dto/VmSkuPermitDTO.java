package com.rome.stock.innerservice.api.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.util.Date;

/**
 * @Description: VmSkuPermitDTO
 * <p>
 * @Author: chuwenchao  2020/2/3
 */
@Data
public class VmSkuPermitDTO extends Pagination {
    /**
     * 主键ID
     */
    @ApiModelProperty(value = "ID")
    private Long id;
    
    /**
     * 虚仓组ID
     */
    @ApiModelProperty(value = "虚仓组ID", required = true)
    private Long virtualWarehouseGroupId;

    /**
     * 商品ID
     */
    @NotNull(message = "商品进货权不能为空")
    @ApiModelProperty(value = "商品ID", required = true)
    private Long skuId;
    
    /**
     * 商品编码
     */
    @NotBlank(message = "商品编码不能为空")
    @ApiModelProperty(value = "商品编码", required = true)
    private String skuCode;
    
    /**
     * 虚仓是否有商品进货权 0:没权限 1:有权限
     */
    @NotNull(message = "商品进货权不能为空")
    @ApiModelProperty(value = "虚仓是否有商品进货权 0:没权限 1:有权限")
    private Integer isPermit;

    /**
     * 创建人
     */
    private Long creator;

    /**
     * 更新人
     */
    private Long modifier;

    /**
     * 创建时间
     */
    private Date createTime;
    /**
     * 更新时间
     */
    private Date  updateTime;
}
