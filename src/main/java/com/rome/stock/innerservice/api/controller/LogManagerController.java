package com.rome.stock.innerservice.api.controller;

import com.github.pagehelper.PageInfo;
import com.google.common.collect.Lists;
import com.rome.arch.core.clientobject.Response;
import com.rome.arch.core.exception.RomeException;
import com.rome.arch.util.controller.RomeController;
import com.rome.stock.innerservice.api.dto.LogManagerDTO;
import com.rome.stock.innerservice.api.dto.LogManagerParamDTO;
import com.rome.stock.innerservice.common.ResCode;
import com.rome.stock.innerservice.common.StringUtils;
import com.rome.stock.innerservice.domain.entity.warehouserecord.WarehouseRecordE;
import com.rome.stock.innerservice.domain.service.impl.*;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import io.swagger.annotations.ApiResponse;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;

import javax.annotation.Resource;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;


@Slf4j
@RomeController
@RequestMapping(value = "/stock/v1/log_manager")
@Api(tags = "统一日志管理")
public class LogManagerController {

    @Resource(name = "logManagerServiceImpl")
    private LogManagerServiceImpl logManagerService;
    @Resource(name = "eshopLogManagerServiceImpl")
    private EshopLogManagerServiceImpl eshopLogManagerService;
    @Resource(name = "purchaseLogManagerServiceImpl")
    private PurchaseLogManagerServiceImpl purchaseLogManagerService;
    @Resource(name = "shopAllocationLogManagerServiceImpl")
    private ShopAllocationLogManagerServiceImpl shopAllocationLogManagerService;
    @Resource(name = "LogHandlerImpl")
    private LogHandlerImpl logHandler;


    @ApiOperation(value = "统一日志查询", nickname = "queryLogs", produces = MediaType.APPLICATION_JSON_VALUE)
    @ApiResponse(code = 200, message = "success", response = List.class)
    @RequestMapping(value = "/queryLogs", method = RequestMethod.POST)
    public Response<PageInfo<LogManagerDTO>> queryLogs(@ApiParam(name = "LogManagerParamDTO", value = "统一日志查询") @RequestBody LogManagerParamDTO paramDTO) {
        try {
            PageInfo<LogManagerDTO> pageInfo =new PageInfo<>();
            if (StringUtils.isNotEmpty(paramDTO.getBusinessType())&& Objects.nonNull(paramDTO.getEndTime())){
                pageInfo = logManagerService.queryLogs(paramDTO);
            }else {
                List<LogManagerDTO> queryList= Lists.newArrayList();
                if (StringUtils.isNotEmpty(paramDTO.getPostOrderCode())||StringUtils.isNotEmpty(paramDTO.getBusinessOrderCode())){
                    List<WarehouseRecordE> list=logHandler.queryBySapAndRecordCode(paramDTO.getBusinessOrderCode(),paramDTO.getPostOrderCode());
                    if (CollectionUtils.isNotEmpty(list)){
                        List<LogManagerDTO> results= Lists.newArrayList();
                        for(WarehouseRecordE warehouseRecordE:list){
                            paramDTO.setPostOrderCode(warehouseRecordE.getRecordCode());
                            if (eshopLogManagerService.getAllTypeMap().containsKey(warehouseRecordE.getRecordType())){
                                results=eshopLogManagerService.queryLogs(paramDTO);
                            }else if (purchaseLogManagerService.getAllTypeMap().containsKey(warehouseRecordE.getRecordType())){
                                results=purchaseLogManagerService.queryLogs(paramDTO);
                            }else if (shopAllocationLogManagerService.getAllTypeMap().containsKey(warehouseRecordE.getRecordType())){
                                paramDTO.setPostOrderCode(warehouseRecordE.getRecordCode());
                                paramDTO.setCacheRecordE(warehouseRecordE);
                                results=shopAllocationLogManagerService.queryLogs(paramDTO);
                            }else {
                                results=logHandler.queryLogsByWarehouseRecord(paramDTO,warehouseRecordE);
                            }
                            if (CollectionUtils.isNotEmpty(results)){
                                results.stream().forEach(dto->{
                                    dto.setSapOrderCode(warehouseRecordE.getSapOrderCode());
                                    dto.setOutInOrderCode(warehouseRecordE.getRecordCode());
                                });
                                queryList.addAll(results);
                            }
                        }
                    }else {
                        queryList=logHandler.queryLogs(paramDTO);
                    }
                }else if (StringUtils.isNotEmpty(paramDTO.getFrontOutRecordCode())){
                    if (eshopLogManagerService.checkTypeByFrontOutRecordCode(paramDTO.getFrontOutRecordCode())){
                        queryList=eshopLogManagerService.queryLogs(paramDTO);
                    }else {
                        queryList=logHandler.queryLogs(paramDTO);
                    }
                    if (CollectionUtils.isNotEmpty(queryList)){
                        queryList.stream().forEach(dto->{
                            dto.setOutRecordCode(paramDTO.getFrontOutRecordCode());
                        });
                    }
                }
                if (CollectionUtils.isNotEmpty(queryList)) {
                    queryList = queryList.stream().sorted((l1, l2) -> Long.compare(l1.getCreateTime().getTime(), l2.getCreateTime().getTime())).collect(Collectors.toList());
                    pageInfo=new PageInfo<>(queryList);
                    pageInfo.setTotal(queryList.size());
                }
            }
            //查询匹配其他展示信息
            if (CollectionUtils.isNotEmpty(pageInfo.getList())){
                logManagerService.queryForLogEnum(pageInfo.getList());
                logManagerService.queryForOrderInfo(pageInfo.getList());
            }
            return Response.builderSuccess(pageInfo);
        } catch (RomeException e) {
            log.error(e.getMessage(), e);
            return Response.builderFail(e.getCode(),e.getMessage());
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return Response.builderFail(ResCode.STOCK_ERROR_1003,ResCode.STOCK_ERROR_1003_DESC);
        }
    }


}

