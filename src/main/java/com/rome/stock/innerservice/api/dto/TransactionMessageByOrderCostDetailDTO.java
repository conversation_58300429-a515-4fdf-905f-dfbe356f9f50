/**
 * Filename TransactionMessageByOrderCostDetailDTO.java
 * Company 上海来伊份科技有限公司。
 * <AUTHOR>
 * @version 
 */
package com.rome.stock.innerservice.api.dto;

import java.math.BigDecimal;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 事务消息回查,
 * 对应订单成本单据明细
 * <AUTHOR>
 * @since 2022-1-10 17:39:56
 */
@Data
public class TransactionMessageByOrderCostDetailDTO {

	@ApiModelProperty(value="单据编码")
	private String recordCode;
	
	@ApiModelProperty(value="业务中台唯一单号")
	private String ztRecordCode;
	/**
	 * 中台业务单号
	 */
	@ApiModelProperty(value="中台业务单号")
	private String ztBusinessCode;
	
	@ApiModelProperty(value="业务类型：1:出库单 2:入库单")
	private Integer businessType;
	
	@ApiModelProperty(value="业务类型名称")
	private String businessTypeName;
	
	@ApiModelProperty(value="单据类型")
	private Integer recordType;
	
    @ApiModelProperty(value = "单类型名称")
    private String recordTypeName;
	
	@ApiModelProperty(value="二级子类型(单据标识)")
	private String recordSubType;
	
	@ApiModelProperty(value="业务中台仓库ID")
	private Long realWarehouseId;
	
	@ApiModelProperty(value="业务中台仓库Code")
	private String realWarehouseCode;
	
	@ApiModelProperty(value="采购主体公司编码")
	private String fromCompanyCode;
	
	@ApiModelProperty(value="采购主体库存组织代码")
	private String fromStockPlantCode;
	
	@ApiModelProperty(value="采购主体库存地点代码")
	private String fromStockLocationCode;

	@ApiModelProperty(value="销售主体公司编码")
	private String toCompanyCode;
	
	@ApiModelProperty(value="销售主体库存组织代码")
	private String toStockPlantCode;
	
	@ApiModelProperty(value="销售主体库存地点代码")
	private String toStockLocationCode;
	
	@ApiModelProperty(value="商品编码")
	private String skuCode;
	
	@ApiModelProperty(value="订单商品数量")
	private BigDecimal orderQty;
	
	@ApiModelProperty(value="已出库或已入库总数")
	private BigDecimal totalQty;
	
	@ApiModelProperty(value="本次出库或入库数")
	private BigDecimal actualQty;
}
