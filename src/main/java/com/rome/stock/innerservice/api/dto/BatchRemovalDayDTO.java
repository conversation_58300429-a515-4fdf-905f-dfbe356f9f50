package com.rome.stock.innerservice.api.dto;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import java.util.Date;

/**
 * 撤柜日期查询对象
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
@EqualsAndHashCode
public class BatchRemovalDayDTO {

    /**
     * 商品sku编码
     */
    private String skuCode;


    /**
     * 实体仓库id
     */
    private Long realWarehouseId;
    
    /**
     * 有效期,天数
     */
    private Integer validity;

    /**
     * 生产日期
     * */
    private Date productDate;
    /**
     * 物料对应的撤柜天数
     */
    private Integer removalDay;

}
