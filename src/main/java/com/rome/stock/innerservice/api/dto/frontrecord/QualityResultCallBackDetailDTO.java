package com.rome.stock.innerservice.api.dto.frontrecord;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @description 质检结果回调对象
 * @date 2020/9/27 13:53
 * @throw
 */
@Data
public class QualityResultCallBackDetailDTO {

    @ApiModelProperty(value = "入库行号")
    @NotBlank(message = "行号不能为空")
    private String lineNo;

    @NotBlank(message="检验单号不能为空")
    @ApiModelProperty(value = "检验单号")
    private String qualityCode;

    @ApiModelProperty(value = "skuId",hidden = true)
    private Long skuId;

    @NotBlank(message="商品编码不能为空")
    @ApiModelProperty(value = "商品编码")
    private String skuCode;

    @ApiModelProperty(value = "批次号：wms收货时会给的批次号")
    @NotBlank(message="批次号不能为空")
    private String batchCode;

    @ApiModelProperty(value = "质检结果 1合格 2不合格")
    @NotNull
    private Integer qualityResult;

    @ApiModelProperty(value = "库存操作结果 0--未操作 1--已操作",hidden = true)
    private Integer isOperate;

    @ApiModelProperty(value = "实际收货数量",hidden = true)
    private BigDecimal actualQty;

}    
   