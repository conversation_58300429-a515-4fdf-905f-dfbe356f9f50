package com.rome.stock.innerservice.api.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

/**
 * 类StockForTempInsPage的实现描述：临时性检验返回值
 *
 * <AUTHOR> 2020/10/6 15:23
 */
@Data
public class StockForTempInsPage extends Pagination {

    @ApiModelProperty(value = "物料编码")
    private String skuCode;

    @ApiModelProperty(value = "实仓编码")
    private String realWarehouseCode;

    @ApiModelProperty(value = "库存类型(1:非限制(可用库存) 2.质检库存 3. 冻结库存)")
    private Integer stockType;

    @ApiModelProperty(value = "库存数量")
    private BigDecimal stockQty;

    @ApiModelProperty(value = "工厂名称")
    private String  factoryName;

    @ApiModelProperty(value = "工厂编码")
    private String  factoryCode;

    @ApiModelProperty(value = "仓库外部编码")
    private String  realWarehouseOutCode;

    @ApiModelProperty(value = "仓库描述")
    private String realWarehouseTypeName;

    @ApiModelProperty(value = "实仓Id")
    private Long realWarehouseId;
}
