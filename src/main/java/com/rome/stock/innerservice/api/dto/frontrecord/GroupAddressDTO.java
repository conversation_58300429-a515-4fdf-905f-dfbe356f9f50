package com.rome.stock.innerservice.api.dto.frontrecord;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 类GroupAddressDTO的实现描述：团长收货信息
 *
 * <AUTHOR> 2022/5/23 19:40
 */
@Data
public class GroupAddressDTO {

    @ApiModelProperty(value = "收货人手机号")
    private String mobile;
    @ApiModelProperty(value = "详细地址")
    private String address;
    @ApiModelProperty(value = "收货人姓名")
    private String name;
    @ApiModelProperty(value = "省市区")
    private String area;
    @ApiModelProperty(value = "收货人信息")
    private String groupInfo;

}
