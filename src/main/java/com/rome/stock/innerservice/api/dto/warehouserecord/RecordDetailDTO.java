package com.rome.stock.innerservice.api.dto.warehouserecord;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

import com.rome.stock.innerservice.domain.entity.frontrecord.FrontBatchStockE;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 类WarehouseRecordDetailDTO的实现描述：TODO 类实现描述
 *
 * <AUTHOR> 2020/6/11 18:39
 */
@Data
@EqualsAndHashCode
public class RecordDetailDTO {

    @ApiModelProperty(value = "基础单位数量")
    @NotNull(message="明细数量不能为空")
    private BigDecimal basicSkuQty;

    @ApiModelProperty(value = "skuCode")
    @NotBlank(message="明细skuCode不能为空")
    private String skuCode;

    @ApiModelProperty(value = "基础单位编码")
    @NotBlank(message="单位code不能为空")
    private String basicUnitCode;

    @ApiModelProperty(value = "基础单位")
    private String basicUnit;

    @ApiModelProperty(value = "sap行号")
    private String lineNo;

    @ApiModelProperty(value = "交货行号,供应链行号")
    @NotBlank(message="交货行号不能为空")
    private String deliveryLineNo;

    @ApiModelProperty(value = "sapPoNo单号")
    private String sapPoNo;

    @ApiModelProperty(value = "期望交货日期")
    private Date deliveryData;

    @ApiModelProperty(value = "库存批次信息")
    private List<FrontBatchStockE> batchStocks;

    @ApiModelProperty(value = "skuId")
    private Long skuId;
}
