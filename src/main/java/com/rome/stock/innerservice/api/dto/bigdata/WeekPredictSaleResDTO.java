package com.rome.stock.innerservice.api.dto.bigdata;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

/**
 * 周销量预估查询查询DTO
 */
@Data
@ApiModel("周销量预估查询返回DTO")
public class WeekPredictSaleResDTO {

    @ApiModelProperty("预估日期")
    private String date;
    @ApiModelProperty("市")
    private String city;
    @ApiModelProperty("渠道")
    private String channel;
    @ApiModelProperty("商品品类1")
    private String cateL1;
    @ApiModelProperty("商品品类2")
    private String cateL2;
    @ApiModelProperty("商品品类3")
    private String cateL3;
    @ApiModelProperty("发货节点类型")
    private String deliverType;
    @ApiModelProperty("省")
    private String province;
    @ApiModelProperty("商品编码")
    private String skuKey;
    @ApiModelProperty("数据创建日期")
    private String createDate;
    @ApiModelProperty("商品等级")
    private String level;
    @ApiModelProperty("节点类型")
    private String nodeType;
    @ApiModelProperty("发货节点id")
    private String deliverId;
    @ApiModelProperty("实际销量")
    private BigDecimal realSales;
    @ApiModelProperty("区/县")
    private String district;
    @ApiModelProperty("修改规则")
    private String modifyRule;
    @ApiModelProperty("修改销量")
    private BigDecimal modifySales;
    @ApiModelProperty("预估销量")
    private BigDecimal predictSales;
    @ApiModelProperty("节点ID")
    private String nodeId;
    @ApiModelProperty("单位")
    private String unit;

}
