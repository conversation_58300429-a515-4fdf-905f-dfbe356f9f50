package com.rome.stock.innerservice.api.dto;

import java.util.List;

import javax.validation.Valid;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 类DeliveryOrderDTO的实现描述：交货单回调接口
 *
 * <AUTHOR> 2019/9/1 14:56
 */
@Data
@EqualsAndHashCode
public class DeliveryOrderDTO {

    @ApiModelProperty(value = "中台单号")
    @NotBlank(message = "中台单号不能为空")
    private String orderCode;

    @ApiModelProperty(value = "sapPO单据编码")
    @NotBlank(message="sapPO单号不能为空")
    private String sapRecordCode;

    @ApiModelProperty(value = "交货单单据编码")
    @NotBlank(message="交货单单据编码不能为空")
    private String deliveryRecordCode;

    @ApiModelProperty(value = "店铺Code")
    @NotBlank(message="店铺Code不能为空")
    private String shopCode;

    @Valid
    @ApiModelProperty(value = "sku数量及明细")
    @NotNull(message="sku数量及明细不能为空")
    private List<DeliveryOrderDetailDTO> warehouseRecordDetails;
}
