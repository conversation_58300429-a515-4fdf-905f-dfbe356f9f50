package com.rome.stock.innerservice.api.dto.warehouserecord;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

/**
 * 类ShopConfirmMQDTO的实现描述
 *
 */
@Data
@EqualsAndHashCode
public class ShopConfirmMQDTO {

    @ApiModelProperty(value = "单据编码")
    private String recordCode;

    @ApiModelProperty(value = "批次明细")
    private List<ShopReceiptRWBatchDTO> shopBatchList;

    @ApiModelProperty(value = "操作人编码")
    private String operatorCode;

    @ApiModelProperty(value = "操作人名称")
    private String operatorName;

    @ApiModelProperty(value = "是否空批次 1是， 0否")
    private Integer batchType;

}
