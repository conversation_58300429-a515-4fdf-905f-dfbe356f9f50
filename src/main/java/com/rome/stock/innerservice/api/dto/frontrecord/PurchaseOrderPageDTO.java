package com.rome.stock.innerservice.api.dto.frontrecord;


import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

@Data
@EqualsAndHashCode
public class PurchaseOrderPageDTO {

	/**
	 * 唯一主键
	 */
	private Long id;
	/**
	 * SAP采购单号
	 */
	private String recordCode;

	/**
	 * 采购单类型
	 */
	private Integer recordType;

	/**
	 * SAP采购单号
	 */
	private String outRecordCode;


	private Date outCreateTime;

	private Date createTime;


	private String factoryCode;


	private String factoryName;

	private Long realWarehouseId;

	private String realWarehouseCode;

	private String realWarehouseName;

	/**
	 * 采购单类型
	 */
	private Integer purchaseRecordType;


	private String supplierCode;

	private String supplierName;


	private String remark;

	private Integer recordStatus;

	private String recordStatusName;


	private String supplierContact;




	/**
	 * 采购sku种类数
	 */
	private Integer skuNum;

	/**
	 * 采购总数量
	 */
	private Long skuTotalQty;

	/**
	 * 实际收货总数量
	 */
	private Long receiveTotalQty;

	private List<PurchaseOrderDetailPageDTO> purchaseOrderDetails;
	/**
	 * 行数总和
	 */
	private Long totalRowNum;
	/**
	 * 物料合计总和
	 */
	private BigDecimal totalSkuQty;
	
	/**
     * 修改人
     */
    private Long modifier;
    
    /**
     * 修改时间
     */
    private Date updateTime;

	/**
	 * 是否是欧电云单据
	 */
	private Integer isOWms;

	/**
	 * 后置单单号
	 */
	private String warehouseRecordCode;

}
