package com.rome.stock.innerservice.api.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;

/**
 * @Author: zhoupeng
 * @createTime: 2022年07月14日 20:46:50
 * @version: 1.0
 * @Description:
 */
@Data
@EqualsAndHashCode
public class BaseinfoConfig   extends Pagination {


    /**
     * 唯一主键
     */
    @ApiModelProperty(value = "唯一主键")
    private Long id;

    /**
     * 业务code
     */
    @ApiModelProperty(value = "业务code")
    private  String  code;

    /**
     * 参数名称
     */
    @ApiModelProperty(value = "参数名称")
    private  String  paramName;

    /**
     * 参数值
     */
    @ApiModelProperty(value = "参数值")
    private  String   paramValue;

    /**
     * 参数描述
     */
    @ApiModelProperty(value = "参数描述")
    private  String   paramDesc;


    @ApiModelProperty(value = "额外字段")
    private  String  ext;

    /**
     * 是否启用
     */
    @ApiModelProperty(value = "是否启用")
    private Integer isAvailable;


    /**
     * 创建人
     */
    @ApiModelProperty(value = "创建人")
    private Long creator;

    /**
     * 创建人
     */
    @ApiModelProperty(value = "更新人")
    private Long modifier;


    /**
     * 创建的开始时间
     */
    @ApiModelProperty("创建的开始时间")
    private Date createStartTime;

    /**
     * 创建的结束时间
     */
    @ApiModelProperty("创建的结束时间")
    private Date createEndTime;

    /**
     * 修改的开始时间
     */
    @ApiModelProperty("修改的开始时间")
    private Date updateStartTime;

    /**
     * 修改的结束时间
     */
    @ApiModelProperty("修改的结束时间")
    private Date updateEndTime;


    /**
     * 创建时间
     */
    @ApiModelProperty("创建时间")
    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
    private Date createTime;

    /**
     * 更新时间
     */
    @ApiModelProperty("更新时间")
    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
    private Date updateTime;
}
