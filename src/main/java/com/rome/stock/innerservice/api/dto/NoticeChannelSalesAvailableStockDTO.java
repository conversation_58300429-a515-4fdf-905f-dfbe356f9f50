/**
 * Filename NoticeChannelSalesAvailableStockDTO.java
 * Company 上海来伊份科技有限公司。
 * <AUTHOR>
 * @version 
 */
package com.rome.stock.innerservice.api.dto;

import java.util.List;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 渠道可用库存有无通知
 * <AUTHOR>
 * @since 2021-11-24 11:03:16
 */
@Data
@EqualsAndHashCode
public class NoticeChannelSalesAvailableStockDTO {
	
    @ApiModelProperty(value = "关联的虚仓组id")
	private Long virtualWarehouseGroupId;
    
    @ApiModelProperty(value = "渠道code列表")
    private List<String> channelCodeList;
    
    @ApiModelProperty(value = "sku信息列表")
    private List<NoticeAvailableStockSkuInfoDTO> skuInfoList;
    
}
