package com.rome.stock.innerservice.api.controller;

import com.rome.arch.core.clientobject.Response;
import com.rome.arch.core.exception.RomeException;
import com.rome.arch.util.controller.RomeController;
import com.rome.stock.innerservice.api.dto.QueryRealTimeStock;
import com.rome.stock.innerservice.api.dto.RealTimeStock;
import com.rome.stock.innerservice.api.dto.RealWarehouse;
import com.rome.stock.innerservice.api.dto.bigdata.StockPlanQueryDTO;
import com.rome.stock.innerservice.api.dto.bigdata.StockPlanQueryResDTO;
import com.rome.stock.innerservice.constant.ResponseMsg;
import com.rome.stock.innerservice.domain.service.StockPlanService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiResponse;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

/**
 * 库存计划
 */
@Slf4j
@Api(tags = "库存计划")
@RequestMapping("/stock/v1/stock_plan")
@RomeController
public class StockPlanController {

    @Resource
    private StockPlanService stockPlanFacade;

    @Resource
    private StockPlanService stockPlanService;

    @ApiOperation(value = "模糊查询工厂编号", nickname = "queryFactoryCodeByLike", produces = MediaType.APPLICATION_JSON_VALUE)
    @ApiResponse(code = 200, message = "success")
    @RequestMapping(value = "/queryFactoryCodeByLike", method = RequestMethod.POST)
    public Response<List<String>> queryFactoryCodeByLike(@RequestParam("factoryCode") String factoryCode) {
        try {
            List<String> res = stockPlanService.queryFactoryCodeByLike(factoryCode);
            return Response.builderSuccess(res);
        } catch (RomeException e) {
            log.error(e.getMessage(), e);
            return ResponseMsg.EXCEPTION.buildMsg(e.getCode(), e.getMessage());
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return ResponseMsg.EXCEPTION.buildMsg("500000", e.getMessage());
        }
    }

    @ApiOperation(value = "模糊查询仓库或门店编号", nickname = "queryRealWarehouseCodeCodeByLike", produces = MediaType.APPLICATION_JSON_VALUE)
    @ApiResponse(code = 200, message = "success")
    @RequestMapping(value = "/queryRealWarehouseCodeCodeByLike", method = RequestMethod.POST)
    public Response<List<RealWarehouse>> queryRealWarehouseCodeCodeByLike(@RequestParam("realWarehouseCode") String realWarehouseCode, @RequestParam(value="factoryCode", required=false) String factoryCode) {
        try {
            List<RealWarehouse> res = stockPlanService.queryRealWarehouseCodeCodeByLike(realWarehouseCode,factoryCode);
            return Response.builderSuccess(res);
        } catch (RomeException e) {
            log.error(e.getMessage(), e);
            return ResponseMsg.EXCEPTION.buildMsg(e.getCode(), e.getMessage());
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return ResponseMsg.EXCEPTION.buildMsg("500000", e.getMessage());
        }
    }

    @ApiOperation(value = "日库存计划查询接口", nickname = "queryStockPlanListByPage", produces = MediaType.APPLICATION_JSON_VALUE)
    @ApiResponse(code = 200, message = "success")
    @RequestMapping(value = "/queryStockPlanListByPage", method = RequestMethod.POST)
    public Response<List<StockPlanQueryResDTO>> queryStockPlanListByPage(@RequestBody StockPlanQueryDTO stockPlanQueryDTO) {
        try {
            List<StockPlanQueryResDTO> pageInfo = stockPlanFacade.queryStockPlanListByPage(stockPlanQueryDTO);
            return Response.builderSuccess(pageInfo);
        } catch (RomeException e) {
            log.error(e.getMessage(), e);
            return ResponseMsg.EXCEPTION.buildMsg(e.getCode(), e.getMessage());
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return ResponseMsg.EXCEPTION.buildMsg("500000", e.getMessage());
        }
    }

    @ApiOperation(value = "查询实时可用库存接口", nickname = "queryRealTimeStock", produces = MediaType.APPLICATION_JSON_VALUE)
    @ApiResponse(code = 200, message = "success")
    @PostMapping(value = "/list/availableQty")
    public Response<List<RealTimeStock>> queryRealTimeStock(@RequestBody QueryRealTimeStock queryRealTimeStock){
            try{
                List<RealTimeStock> realTimeStocks = stockPlanFacade.queryRealTimeStock(queryRealTimeStock);
                return Response.builderSuccess(realTimeStocks);
            }catch (RomeException e){
                log.error(e.getMessage(), e);
                return ResponseMsg.EXCEPTION.buildMsg(e.getCode(), e.getMessage());
            }catch (Exception e){
                log.error(e.getMessage(), e);
                return ResponseMsg.EXCEPTION.buildMsg("500000", e.getMessage());
            }
    }

}
