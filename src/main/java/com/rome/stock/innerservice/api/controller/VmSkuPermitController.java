package com.rome.stock.innerservice.api.controller;

import com.github.pagehelper.PageInfo;
import com.rome.arch.core.clientobject.Response;
import com.rome.arch.core.exception.RomeException;
import com.rome.arch.util.controller.RomeController;
import com.rome.stock.innerservice.api.dto.*;
import com.rome.stock.innerservice.api.dto.message.SkuPublishPurchaseDTO;
import com.rome.stock.innerservice.common.ResCode;
import com.rome.stock.innerservice.domain.service.VmSkuPermitService;
import com.rome.stock.innerservice.facade.VmSkuPermitFacade;
import com.rome.stock.core.infrastructure.dataobject.core.CoreRealStockOpDO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import io.swagger.annotations.ApiResponse;
import lombok.extern.slf4j.Slf4j;

import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

@Slf4j
@RomeController
@RequestMapping("/stock/vmSkuPermit")
@Api(tags={"虚仓权限商品接口"})
public class VmSkuPermitController {

    @Autowired
    private VmSkuPermitService vmSkuPermitService;

    @ApiOperation(value = "根据条件分页查询虚仓权限商品", produces = MediaType.APPLICATION_JSON_VALUE)
    @ApiResponse(code = 200, message = "success", response = VmSkuPermitDTO.class)
    @RequestMapping(value = "/queryPageByCondition", method = RequestMethod.POST)
    public Response<PageInfo<VmSkuPermitDTO>> queryPageByCondition(@ApiParam(name = "vmSkuPermitDTO") @RequestBody VmSkuPermitDTO vmSkuPermitDTO) {
        try {
            PageInfo<VmSkuPermitDTO> result = vmSkuPermitService.queryPageByCondition(vmSkuPermitDTO);
            return Response.builderSuccess(result);
        } catch (RomeException e) {
            log.error(e.getMessage(), e);
            return Response.builderFail(e.getCode(), e.getMessage());
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return Response.builderFail(ResCode.STOCK_ERROR_1003_DESC, ResCode.STOCK_ERROR_1003_DESC);
        }
    }

    @ApiOperation(value = "改变虚仓进货权限商品", produces = MediaType.APPLICATION_JSON_VALUE)
    @ApiResponse(code = 200, message = "success", response = String.class)
    @RequestMapping(value = "/addOrUpdateVmSkuPermit", method = RequestMethod.POST)
    public Response addOrUpdateVmSkuPermit(@ApiParam(name = "vmSkuPermitDTO")@Validated @RequestBody List<VmSkuPermitDTO> vmSkuPermitDTO) {
        try {
            vmSkuPermitService.addOrUpdateVmSkuPermitByDTO(vmSkuPermitDTO);
            return Response.builderSuccess(true);
        } catch (RomeException e) {
            log.error(e.getMessage(), e);
            return Response.builderFail(e.getCode(),e.getMessage());
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return Response.builderFail(ResCode.STOCK_ERROR_1003_DESC,ResCode.STOCK_ERROR_1003_DESC);
        }
    }

    @ApiOperation(value = "生成虚仓Sku进货权限,全量同步", produces = MediaType.APPLICATION_JSON_VALUE)
    @ApiResponse(code = 200, message = "success")
    @RequestMapping(value = "/createVmSkuPermit", method = RequestMethod.POST)
    public Response createVmSkuPermit() {
        try {
            vmSkuPermitService.vmSkuPermitSyncByAllByAsyn();
            return Response.builderSuccess(true);
        } catch (RomeException e) {
            log.error(e.getMessage(), e);
            return Response.builderFail(e.getCode(),e.getMessage());
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return Response.builderFail(ResCode.STOCK_ERROR_1003_DESC,ResCode.STOCK_ERROR_1003_DESC);
        }
    }
    
    @ApiOperation(value = "根据策略组Id同步,生成虚仓Sku进货权限,返回同步数量", produces = MediaType.APPLICATION_JSON_VALUE)
    @ApiResponse(code = 200, message = "success", response = VmSkuPermitDTO.class)
    @RequestMapping(value = "/vmSkuPermitSyncByGroupId", method = RequestMethod.POST)
    public Response<Integer> vmSkuPermitSyncByGroupId(@ApiParam(name = "groupId")@RequestBody Long groupId) {
        try {
        	int count = vmSkuPermitService.vmSkuPermitSyncByGroupId(groupId);
            return Response.builderSuccess(count);
        } catch (RomeException e) {
            log.error(e.getMessage(), e);
            return Response.builderFail(e.getCode(), e.getMessage());
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return Response.builderFail(ResCode.STOCK_ERROR_1003_DESC, ResCode.STOCK_ERROR_1003_DESC);
        }
    }
    
    @ApiOperation(value = "测试根据sku进货权,计算虚仓sku同步比例,设置到", produces = MediaType.APPLICATION_JSON_VALUE)
    @ApiResponse(code = 200, message = "success", response = VmSkuPermitDTO.class)
    @RequestMapping(value = "/testSkuSyncRateCalculate", method = RequestMethod.POST)
    public Response<CoreRealStockOpDO> testSkuSyncRateCalculate(@ApiParam(name = "dto") @RequestBody CoreRealStockOpDO increaseDo) {
        try {
        	VmSkuPermitFacade.skuSyncRateCalculate(increaseDo);
            return Response.builderSuccess(increaseDo);
        } catch (RomeException e) {
            log.error(e.getMessage(), e);
            return Response.builderFail(e.getCode(), e.getMessage());
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return Response.builderFail(ResCode.STOCK_ERROR_1003_DESC, ResCode.STOCK_ERROR_1003_DESC);
        }
    }
    
    @ApiOperation(value = "通过门店或渠道编码改变虚仓进货权限商品", produces = MediaType.APPLICATION_JSON_VALUE)
    @ApiResponse(code = 200, message = "success", response = String.class)
    @RequestMapping(value = "/addUpdateByShopOrChannelCode", method = RequestMethod.POST)
    public Response addUpdateByShopOrChannelCode(@ApiParam(name = "list")@RequestBody List<SkuPublishPurchaseDTO> list) {
        try {
            vmSkuPermitService.addUpdateByMsg(list);
            return Response.builderSuccess(true);
        } catch (RomeException e) {
            log.error(e.getMessage(), e);
            return Response.builderFail(e.getCode(),e.getMessage());
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return Response.builderFail(ResCode.STOCK_ERROR_1003_DESC,ResCode.STOCK_ERROR_1003_DESC);
        }
    }
    
    @ApiOperation(value = "虚仓进货权限商品缓存操作", produces = MediaType.APPLICATION_JSON_VALUE)
    @ApiResponse(code = 200, message = "success", response = String.class)
    @RequestMapping(value = "/vmSkuPermitCacheOp", method = RequestMethod.POST)
    public Response vmSkuPermitCacheOp(@ApiParam(name = "tag", value= "操作标识,1重新加载组缓存,2删除组缓存,3获取组缓存数据,4重新加载门店缓存,5重新加载渠道缓存,6删除门店或渠道缓存,7获取门店或渠道对应组缓存") @RequestParam("tag") String tag, @ApiParam(name = "list", value= "门店编码或者渠道编码或者组Id")@RequestBody List<String> list) {
    	try {
    		String result = vmSkuPermitService.vmSkuPermitCacheOp(tag, list);
            return Response.builderSuccess(result);
        } catch (RomeException e) {
            log.error(e.getMessage(), e);
            return Response.builderFail(e.getCode(),e.getMessage());
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return Response.builderFail(ResCode.STOCK_ERROR_1003_DESC,ResCode.STOCK_ERROR_1003_DESC);
        }
    }


    @ApiOperation(value = "根据虚仓组ID和skuIds批量查询进货权限商品")
    @ApiResponse(code = 200, message = "success", response = VmSkuPermitDTO.class)
    @PostMapping(value = "/queryVmSkuPermitByGroupIdsAndSkuIds")
    public Response<List<VmSkuPermitDTO>> queryVmSkuPermitByGroupIdsAndSkuIds(@Validated @RequestBody QueryVmSkuPermitDTO queryVmSkuPermitDTO){
        try{
            List<VmSkuPermitDTO> vmSkuPermitDTOS = vmSkuPermitService.queryVmSkuPermitByGroupIdsAndSkuIds(queryVmSkuPermitDTO);
            return Response.builderSuccess(vmSkuPermitDTOS);
        }catch (RomeException e){
            log.error(e.getMessage(), e);
            return Response.builderFail(e.getCode(),e.getMessage());
        }catch (Exception e){
            log.error(e.getMessage(), e);
            return Response.builderFail(ResCode.STOCK_ERROR_1003_DESC,ResCode.STOCK_ERROR_1003_DESC);
        }
    }


}
