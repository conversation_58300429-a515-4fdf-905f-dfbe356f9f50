package com.rome.stock.innerservice.api.dto.cmtransferlog;

import java.math.BigDecimal;

import lombok.Data;

/**
 * 类CmTransferDetailExportDTO的实现描述：301过账日志明细导出DTO
 *
 * <AUTHOR> 2021/5/19 23:31
 */
@Data
public class CmTransferDetailExportDTO {

    /**
     * 过账单号
     */
    private String recordCode;


    /**
     * 过账状态，0:初始 1:成功 2:失败 3:异常
     */
    private Integer transferStatus;

    /**
     * 过账状态，0:初始 1:成功 2:失败 3:异常
     */
    private String transferStatusName;

    /**
     * 出库工厂编码
     */
    private String outFactoryCode;


    /**
     * 出库库存地点
     */
    private String outWarehouseCode;

    /**
     * 出库库存名称
     */
    private String outWarehouseName;

    /**
     * 商品sku编码
     */
    private String skuCode;

    /**
     * 商品名称
     */
    private String skuName;

    /**
     * 供应商编码
     */
    private String supplierCode;

    /**
     * 供应商名称
     */
    private String supplierName;

    /**
     * 调整总数
     */
    private BigDecimal skuQty;

    private String unitCode ;

    /**
     * 是否退货
     */
    private String isReturn;

    /**
     * 过账时间
     */
    private String postingDate;

    /**
     * 推送时间
     */
    private String pushDate;



}
