/**
 * Filename StockChangeNoticeController.java
 * Company 上海来伊份科技有限公司。
 * <AUTHOR>
 * @version 
 */
package com.rome.stock.innerservice.api.controller;

import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import com.rome.arch.core.clientobject.Response;
import com.rome.arch.core.exception.RomeException;
import com.rome.stock.innerservice.api.dto.VirtualWarehouseFlowRecord;
import com.rome.stock.innerservice.common.ResCode;
import com.rome.stock.innerservice.constant.ResponseMsg;
import com.rome.stock.innerservice.domain.service.StockChangeNoticeService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import io.swagger.annotations.ApiResponse;
import lombok.extern.slf4j.Slf4j;

/**
 * 库存变化通知
 * <AUTHOR>
 * @since 2021-12-21 14:51:27
 */
@Slf4j
@RestController
@RequestMapping("/stock/v1/stock_change_notice")
@Api(tags={"库存变化通知"})
public class StockChangeNoticeController {

	@Autowired
	private StockChangeNoticeService stockChangeNoticeService;
	
	/**
	 * 库存变化通知-渠道有无可用库存通知 
	 * @param runStopTime 最大运行时间，单位秒数
	 * @return
	 */
    @ApiOperation(value = "库存变化通知-渠道有无可用库存通知Job", nickname = "channelSalesAvailableStockNotice", produces = MediaType.APPLICATION_JSON_VALUE)
    @ApiResponse(code = 200, message = "success", response = String.class)
    @RequestMapping(value = "/channelSalesAvailableStockNotice", method = RequestMethod.POST)
    public Response channelSalesAvailableStockNotice(@ApiParam(name = "runStopTime", value = "最大运行时间，单位秒数") @RequestParam("runStopTime") Long runStopTime) {
        try {
        	// 为null或者等于0秒时，为1分钟
        	if(runStopTime == null || runStopTime < 1) {
        		runStopTime = System.currentTimeMillis() + (60 * 1000);
        	} else {
        		runStopTime = System.currentTimeMillis() + (runStopTime * 1000);
        	}
        	int num = stockChangeNoticeService.channelSalesAvailableStockNotice(runStopTime);
            return Response.builderSuccess("流水处理总条数：" + num);
        } catch (RomeException e) {
            log.error(e.getMessage(), e);
            return ResponseMsg.FAIL.buildMsg(e.getCode(),e.getMessage());
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return ResponseMsg.EXCEPTION.buildMsg(ResCode.STOCK_ERROR_1003_DESC,ResCode.STOCK_ERROR_1003_DESC);
        }
    }
    
    /**
	 * 库存变化通知-渠道有无可用库存通知-组合品
	 * @param runStopTime 最大运行时间，单位秒数
	 * @return
	 */
    @ApiOperation(value = "库存变化通知-渠道有无可用库存通知-组合品Job", nickname = "channelSalesAvailableStockNoticeByCombineSku", produces = MediaType.APPLICATION_JSON_VALUE)
    @ApiResponse(code = 200, message = "success", response = String.class)
    @RequestMapping(value = "/channelSalesAvailableStockNoticeByCombineSku", method = RequestMethod.POST)
    public Response channelSalesAvailableStockNoticeByCombineSku(@ApiParam(name = "runStopTime", value = "最大运行时间，单位秒数") @RequestParam("runStopTime") Long runStopTime) {
        try {
        	// 为null或者等于0秒时，为1分钟
        	if(runStopTime == null || runStopTime < 1) {
        		runStopTime = System.currentTimeMillis() + (60 * 1000);
        	} else {
        		runStopTime = System.currentTimeMillis() + (runStopTime * 1000);
        	}
        	int num = stockChangeNoticeService.channelSalesAvailableStockNoticeByCombineSku(runStopTime);
            return Response.builderSuccess("流水处理总条数：" + num);
        } catch (RomeException e) {
            log.error(e.getMessage(), e);
            return ResponseMsg.FAIL.buildMsg(e.getCode(),e.getMessage());
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return ResponseMsg.EXCEPTION.buildMsg(ResCode.STOCK_ERROR_1003_DESC,ResCode.STOCK_ERROR_1003_DESC);
        }
    }
    
    /**
	 * 库存变化通知-渠道有无可用库存通知，指定强制推数据
	 * 虚仓Id、skuId、skuCode不能为null
	 * @return
	 */
    @ApiOperation(value = "库存变化通知-渠道有无可用库存通知，指定强制推数据", nickname = "channelSalesAvailableStockNoticeByForce", produces = MediaType.APPLICATION_JSON_VALUE)
    @ApiResponse(code = 200, message = "success", response = String.class)
    @RequestMapping(value = "/channelSalesAvailableStockNoticeByForce", method = RequestMethod.POST)
    public Response channelSalesAvailableStockNoticeByForce(@ApiParam(name = "list", value = "虚仓Id、skuId、skuCode不能为null") @RequestBody List<VirtualWarehouseFlowRecord> list) {
        try {
        	if(list == null || list.size() > 100000) {
        		return Response.builderFail(ResCode.STOCK_ERROR_1002, ResCode.STOCK_ERROR_1002_DESC + "个数不能为空或超过100000");
        	}
        	stockChangeNoticeService.channelSalesAvailableStockNoticeByForce(list, false);
            return ResponseMsg.SUCCESS.buildMsg();
        } catch (RomeException e) {
            log.error(e.getMessage(), e);
            return ResponseMsg.FAIL.buildMsg(e.getCode(),e.getMessage());
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return ResponseMsg.EXCEPTION.buildMsg(ResCode.STOCK_ERROR_1003_DESC,ResCode.STOCK_ERROR_1003_DESC);
        }
    }
    
    /**
	 * 库存变化通知-渠道有无可用库存通知-组合品，指定强制推数据
	 * 虚仓Id、skuId、skuCode不能为null
	 * @return
	 */
    @ApiOperation(value = "库存变化通知-渠道有无可用库存通知-组合品，指定强制推数据", nickname = "channelSalesAvailableStockNoticeCombineSkuByForce", produces = MediaType.APPLICATION_JSON_VALUE)
    @ApiResponse(code = 200, message = "success", response = String.class)
    @RequestMapping(value = "/channelSalesAvailableStockNoticeCombineSkuByForce", method = RequestMethod.POST)
    public Response channelSalesAvailableStockNoticeCombineSkuByForce(@ApiParam(name = "list", value = "虚仓Id、skuId、skuCode不能为null") @RequestBody List<VirtualWarehouseFlowRecord> list) {
        try {
        	if(list == null || list.size() > 100000) {
        		return Response.builderFail(ResCode.STOCK_ERROR_1002, ResCode.STOCK_ERROR_1002_DESC + "个数不能为空或超过100000");
        	}
        	stockChangeNoticeService.channelSalesAvailableStockNoticeByForce(list, true);
            return ResponseMsg.SUCCESS.buildMsg();
        } catch (RomeException e) {
            log.error(e.getMessage(), e);
            return ResponseMsg.FAIL.buildMsg(e.getCode(),e.getMessage());
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return ResponseMsg.EXCEPTION.buildMsg(ResCode.STOCK_ERROR_1003_DESC,ResCode.STOCK_ERROR_1003_DESC);
        }
    }

    /**
     * 库存变化通知计算所有的为了安全库存配置
     * @return
     */
    @ApiOperation(value = "库存变化通知计算所有的为了安全库存配置", nickname = "calculateAllBySafeStockConfig", produces = MediaType.APPLICATION_JSON_VALUE)
    @ApiResponse(code = 200, message = "success", response = String.class)
    @RequestMapping(value = "/calculateAllBySafeStockConfig", method = RequestMethod.POST)
    public Response calculateAllBySafeStockConfig() {
        try {
            int num = stockChangeNoticeService.calculateAllBySafeStockConfig();
            return Response.builderSuccess("处理总条数：" + num);
        } catch (RomeException e) {
            log.error(e.getMessage(), e);
            return ResponseMsg.FAIL.buildMsg(e.getCode(),e.getMessage());
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return ResponseMsg.EXCEPTION.buildMsg(ResCode.STOCK_ERROR_1003_DESC,ResCode.STOCK_ERROR_1003_DESC);
        }
    }
	
}
