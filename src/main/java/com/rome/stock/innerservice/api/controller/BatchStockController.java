package com.rome.stock.innerservice.api.controller;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.function.Function;
import java.util.stream.Collectors;

import javax.annotation.Resource;

import com.rome.stock.innerservice.facade.CalculateRwBatchFacade;
import com.rome.stock.innerservice.domain.batch.dto.RwBatchStockDTO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;

import com.alibaba.fastjson.JSON;
import com.github.pagehelper.PageInfo;
import com.rome.arch.core.clientobject.Response;
import com.rome.arch.core.exception.RomeException;
import com.rome.arch.util.controller.RomeController;
import com.rome.stock.innerservice.api.dto.BatchStockDTO;
import com.rome.stock.innerservice.api.dto.BatchStockQueryDTO;
import com.rome.stock.innerservice.api.dto.RefreshBatchStockDTO;
import com.rome.stock.innerservice.api.dto.RefreshBatchStockDetailDTO;
import com.rome.stock.innerservice.api.dto.WarehouseToBRecord;
import com.rome.stock.innerservice.common.ResCode;
import com.rome.stock.innerservice.constant.ParamValidator;
import com.rome.stock.innerservice.constant.ResponseMsg;
import com.rome.stock.innerservice.domain.entity.RealWarehouseE;
import com.rome.stock.innerservice.domain.repository.RealWarehouseRepository;
import com.rome.stock.innerservice.domain.repository.SapInterfaceLogRepository;
import com.rome.stock.innerservice.domain.service.BatchStockService;
import com.rome.stock.innerservice.domain.service.OrderUtilService;
import com.rome.stock.innerservice.facade.BatchStockFacade;
import com.rome.stock.innerservice.infrastructure.mapper.BusinessReasonMapper;
import com.rome.stock.innerservice.remote.item.dto.SkuInfoExtDTO;
import com.rome.stock.innerservice.remote.item.facade.SkuFacade;
import com.rome.stock.innerservice.remote.trade.facade.TradePriceFacade;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import io.swagger.annotations.ApiResponse;
import lombok.extern.slf4j.Slf4j;

/**
 * @Description 批次管理
 * <AUTHOR>
 * @Date 2019/5/13 15:45
 * @Version 1.0
 */
@Slf4j
@RomeController
@RequestMapping("/stock/v1/batch_stock")
@Api(tags={"批次管理"})
public class BatchStockController {

    @Autowired
    private BatchStockService batchStockService;
    @Autowired
    private OrderUtilService orderUtilService;
    @Autowired
    private TradePriceFacade tradePriceFacade;

    private ParamValidator validator = ParamValidator.INSTANCE;

    @Value("${max.stock.queryNum}")
    private Integer MAX_SIZE;

    @Resource
    private BusinessReasonMapper businessReasonMapper;
    
    @Autowired
    private SkuFacade skuFacade;
    
    @Resource
    private SapInterfaceLogRepository sapInterfaceLogRepository;
    
    @Autowired
    private RealWarehouseRepository realWarehouseRepository;

    private final static String LOGTYPE = "appCall";
    
    @ApiOperation(value = "刷批次库存数据", nickname = "refreshForBatchStock", produces = MediaType.APPLICATION_JSON_VALUE)
    @ApiResponse(code = 200, message = "success", response = RefreshBatchStockDTO.class)
    @PostMapping(value = "/refreshForBatchStock")
    public Response refreshForBatchStock(@RequestBody RefreshBatchStockDTO paramDTO) {
    	String json = JSON.toJSONString(paramDTO);
    	String message = "";
        boolean isSucc = false;
        String recordCode = "";
        try {
        	recordCode = paramDTO.getDetails().get(0).getRecordCode();
        	List<Long> skuIds = paramDTO.getDetails().stream().map(RefreshBatchStockDetailDTO::getSkuId).collect(Collectors.toList());
            List<SkuInfoExtDTO> skuInfoExtDTOList=skuFacade.skusBySkuId(skuIds);
            Map<Long,SkuInfoExtDTO> skuInfoExtDTOSMap=skuInfoExtDTOList.stream().collect(Collectors.toMap(SkuInfoExtDTO::getId, Function.identity(), (v1, v2) -> v1));
            List<BatchStockDTO> list = new ArrayList<>(paramDTO.getDetails().size());
            List<Long> wIds = paramDTO.getDetails().stream().map(RefreshBatchStockDetailDTO::getRealWarehouseId).collect(Collectors.toList());
            List<RealWarehouseE> realWarehouseEs = realWarehouseRepository.getRealWarehouseByIds(wIds);
            Map<Long,RealWarehouseE> realWarehouseMap = realWarehouseEs.stream().collect(Collectors.toMap(RealWarehouseE::getId, Function.identity(), (v1, v2) -> v1));
            // 实仓类型过滤
            final Set<Integer> filterTypeSet = new HashSet<>();
 			// 实仓Id过滤
 			final Set<Long> filterIdSet = new HashSet<>();
 			// 初始化过滤条件
 			BatchStockFacade.getAllowWarehouseTypeOrIds(filterTypeSet, filterIdSet);
 			// 没有要处理的wms
 			if(filterTypeSet.size() == 0 && filterIdSet.size() == 0) {
 				throw new RomeException(ResCode.STOCK_ERROR_1003, "没有配置仓库批次库存核对");
 			}
 			for(RealWarehouseE realWarehouseE: realWarehouseMap.values()) {
 			    // 过滤不需处理的
 	 			if(!((realWarehouseE.getRealWarehouseType() != null && filterTypeSet.contains(realWarehouseE.getRealWarehouseType()))
 	 					|| (filterIdSet.contains(realWarehouseE.getId())))) {
 	 				throw new RomeException(ResCode.STOCK_ERROR_1003, "仓库不在批次库存核对范围内，id=" + realWarehouseE.getId());
 	 			}
 			}

            for(RefreshBatchStockDetailDTO dto : paramDTO.getDetails()){
                if(!skuInfoExtDTOSMap.containsKey(dto.getSkuId())){
                    throw new RomeException(ResCode.STOCK_ERROR_1002, "商品编号不存在，skuId:"+dto.getSkuId());
                }
                if(!realWarehouseMap.containsKey(dto.getRealWarehouseId())){
                    throw new RomeException(ResCode.STOCK_ERROR_1002, "实仓不存在，realWarehouseId:"+dto.getRealWarehouseId());
                }
                if(dto.getStockType() == null || dto.getStockType().intValue()<=0 || dto.getStockType().intValue()>6) {
                	throw new RomeException(ResCode.STOCK_ERROR_1002, "库存类型，不能为空");
                }
                if(dto.getSkuQty() == null || dto.getSkuQty().compareTo(BigDecimal.ZERO) <= 0) {
                	throw new RomeException(ResCode.STOCK_ERROR_1002, "操作库存数量，不能为空或小于等于0");
                }
                SkuInfoExtDTO skuInfoExtDTO=skuInfoExtDTOSMap.get(dto.getSkuId());
                BatchStockDTO batchStockDTO = new BatchStockDTO();
                batchStockDTO.setSkuCode(skuInfoExtDTO.getSkuCode());
                batchStockDTO.setSkuId(skuInfoExtDTO.getId());
                batchStockDTO.setValidity(skuInfoExtDTO.getTotalShelfLife());// 有效期,天数
                batchStockDTO.setRealWarehouseId(dto.getRealWarehouseId());
                batchStockDTO.setRecordCode(dto.getRecordCode());
                batchStockDTO.setRecordType(dto.getRecordType());
                batchStockDTO.setSkuQty(dto.getSkuQty());
                batchStockDTO.setBatchCode(dto.getBatchCode());
                batchStockDTO.setProductDate(dto.getProductDate());
                batchStockDTO.setEntryDate(dto.getEntryDate());
                batchStockDTO.setCreator(paramDTO.getUserId());
                batchStockDTO.setStockType(dto.getStockType());
                batchStockDTO.setManual(paramDTO.getManual());
                list.add(batchStockDTO);
            }
            batchStockService.refreshForBatchStock(list);
            isSucc = true;
            message = "200";
            return ResponseMsg.SUCCESS.buildMsg("");
        } catch (RomeException e) {
        	message = e.getMessage();
            log.error(e.getMessage(), e);
            return Response.builderFail(e.getCode(), e.getMessage());
        } catch (Exception e) {
        	message = e.getMessage();
            log.error(e.getMessage(), e);
            return Response.builderFail(ResCode.STOCK_ERROR_1003, ResCode.STOCK_ERROR_1003_DESC);
        }finally {
            sapInterfaceLogRepository.saveCallBackInterFaceLog(2, recordCode, "refreshForBatchStock",
                    json, message, isSucc);
        }
    }

    @ApiOperation(value = "获取单号", nickname = "getCode", produces = MediaType.APPLICATION_JSON_VALUE)
    @ApiResponse(code = 200, message = "success", response = WarehouseToBRecord.class)
    @RequestMapping(value = "/getCode", method = RequestMethod.POST)
    public Response<String> getOrderCode(@RequestParam("prefix") String prefix) {
        try {
            return ResponseMsg.SUCCESS.buildMsg(orderUtilService.queryOrderCode(prefix));
        } catch (RomeException e) {
            return Response.builderFail(e.getCode(),e.getMessage());
        } catch (Exception e) {
            return Response.builderFail(ResCode.STOCK_ERROR_1003_DESC,ResCode.STOCK_ERROR_1003_DESC);
        }
    }

    @ApiOperation(value = "获取单号", nickname = "tets", produces = MediaType.APPLICATION_JSON_VALUE)
    @ApiResponse(code = 200, message = "success", response = WarehouseToBRecord.class)
    @RequestMapping(value = "/tets", method = RequestMethod.POST)
    public Response<String> tets(@RequestParam("prefix") String prefix, @RequestParam("appId") String appId) {
        try {
            return ResponseMsg.SUCCESS.buildMsg(tradePriceFacade.getTradePriceByRecordCode(prefix, 1, appId));
        } catch (RomeException e) {
            return Response.builderFail(e.getCode(),e.getMessage());
        } catch (Exception e) {
            return Response.builderFail(ResCode.STOCK_ERROR_1003_DESC,ResCode.STOCK_ERROR_1003_DESC);
        }
    }
    
    @ApiOperation(value = "根据条件查询批次库存", nickname = "queryCondition", produces = MediaType.APPLICATION_JSON_VALUE)
    @ApiResponse(code = 200, message = "success")
    @RequestMapping(value = "/queryCondition", method = RequestMethod.POST)
    public Response<PageInfo<BatchStockQueryDTO>> queryCondition(@RequestBody BatchStockQueryDTO paramDto) {
        try {
            PageInfo<BatchStockQueryDTO> dtoList = batchStockService.queryCondition(paramDto);
            return ResponseMsg.SUCCESS.buildMsg(dtoList);
        } catch (RomeException e) {
            log.error(e.getMessage(), e);
            return ResponseMsg.FAIL.buildMsg(e.getCode(),e.getMessage());
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return ResponseMsg.EXCEPTION.buildMsg(ResCode.STOCK_ERROR_1003, ResCode.STOCK_ERROR_1003_DESC + e.getMessage());
        }
    }
    
    /**
	 * 批次库存处理，根据流水定时任务
	 * @param runStopTime 最大运行时间，单位秒数
	 * @return
	 */
    @ApiOperation(value = "批次库存处理，根据流水定时任务Job", nickname = "batchStockTask", produces = MediaType.APPLICATION_JSON_VALUE)
    @ApiResponse(code = 200, message = "success", response = String.class)
    @RequestMapping(value = "/batchStockTask", method = RequestMethod.POST)
    public Response batchStockTask(@ApiParam(name = "runStopTime", value = "最大运行时间，单位秒数") @RequestParam("runStopTime") Long runStopTime) {
        try {
        	// 为null或者等于0秒时，为1分钟
        	if(runStopTime == null || runStopTime < 1) {
        		runStopTime = System.currentTimeMillis() + (60 * 1000);
        	} else {
        		runStopTime = System.currentTimeMillis() + (runStopTime * 1000);
        	}
        	int num = batchStockService.batchStockTask(runStopTime);
            return Response.builderSuccess("流水处理总条数：" + num);
        } catch (RomeException e) {
            log.error(e.getMessage(), e);
            return ResponseMsg.FAIL.buildMsg(e.getCode(),e.getMessage());
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return ResponseMsg.EXCEPTION.buildMsg(ResCode.STOCK_ERROR_1003_DESC,ResCode.STOCK_ERROR_1003_DESC);
        }
    }

    /**
     * 批次库存处理，根据流水Id列表-慎用
     * @param idList 最大运行时间，单位秒数
     * @return
     */
    @ApiOperation(value = "批次库存处理，根据流水Id列表-慎用", nickname = "batchStockByFlowIdList", produces = MediaType.APPLICATION_JSON_VALUE)
    @ApiResponse(code = 200, message = "success", response = String.class)
    @RequestMapping(value = "/batchStockByFlowIdList", method = RequestMethod.POST)
    public Response batchStockByFlowIdList(@ApiParam(name = "idList", value = "实仓流水Id列表") @RequestBody List<Long> idList) {
        try {
            int num = batchStockService.batchStockByFlowIdList(idList);
            return Response.builderSuccess("流水处理总条数：" + num);
        } catch (RomeException e) {
            log.error(e.getMessage(), e);
            return ResponseMsg.FAIL.buildMsg(e.getCode(),e.getMessage());
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return ResponseMsg.EXCEPTION.buildMsg(ResCode.STOCK_ERROR_1003_DESC,ResCode.STOCK_ERROR_1003_DESC);
        }
    }

    @ApiOperation(value = "获取单据批次库存数据", nickname = "calRwBatch", produces = MediaType.APPLICATION_JSON_VALUE)
    @ApiResponse(code = 200, message = "success", response = String.class)
    @RequestMapping(value = "/calRwBatch", method = RequestMethod.POST)
    public Response calRwBatch(@RequestBody List<RwBatchStockDTO> list) {
        try {
            return Response.builderSuccess(CalculateRwBatchFacade.getRwBatch(list));
        } catch (RomeException e) {
            log.error(e.getMessage(), e);
            return ResponseMsg.FAIL.buildMsg(e.getCode(),e.getMessage());
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return ResponseMsg.EXCEPTION.buildMsg(ResCode.STOCK_ERROR_1003_DESC,ResCode.STOCK_ERROR_1003_DESC);
        }
    }


}
