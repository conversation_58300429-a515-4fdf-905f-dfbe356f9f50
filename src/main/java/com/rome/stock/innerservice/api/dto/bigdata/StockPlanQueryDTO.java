package com.rome.stock.innerservice.api.dto.bigdata;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * 库存计划查询DTO
 */
@Data
@ApiModel("库存计划查询DTO")
public class StockPlanQueryDTO {

    @ApiModelProperty("起始日期:YYYY-MM-DD格式")
    private String startDate;

    @ApiModelProperty("创建:YYYY-MM-DD格式")
    private String createDate;

    @ApiModelProperty("结束日期:YYYY-MM-DD格式")
    private String endDate;

    @ApiModelProperty(value = "开始时间")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date startTime;

    @ApiModelProperty(value = "结束时间")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date endTime;

    @ApiModelProperty("一级类目")
    private String firstCategoryCode;

    @ApiModelProperty("二级类目")
    private String secondCategoryCode;

    @ApiModelProperty("三级类目")
    private String thirdCategoryCode;

    @ApiModelProperty("商品编码")
    private String skuKey;

    @ApiModelProperty("商品Id")
    private Long skuId;

    @ApiModelProperty("skuName")
    private String skuName;

    @ApiModelProperty("商品等级")
    private String skuLevel;

    @ApiModelProperty("省节点:多个使用a,b,c形式")
    private String province;

    @ApiModelProperty("市节点:多个使用a,b,c形式")
    private String city;

    @ApiModelProperty("区县节点:多个使用a,b,c形式")
    private String district;

    @ApiModelProperty("门店")
    private String store;

    @ApiModelProperty("发货节点类型")
    private String deliverType;

    @ApiModelProperty("每页记录条数")
    private Integer pageSize=100;

    @ApiModelProperty("当前页码")
    private Integer pageNo=1;

    @ApiModelProperty("日期时间title")
    private List<Map<String, Date>> weekDates;

    @ApiModelProperty("日期时间title")
    private List<String> quota1;

    @ApiModelProperty("渠道编号")
    private String channel;

    @ApiModelProperty("工厂编号")
    private String factoryCode;

    private String deliverId;


}
