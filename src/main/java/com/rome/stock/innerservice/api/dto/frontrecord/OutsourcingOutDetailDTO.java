package com.rome.stock.innerservice.api.dto.frontrecord;

import com.fasterxml.jackson.annotation.JsonIgnore;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.Digits;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.math.BigDecimal;

@Data
@EqualsAndHashCode
public class OutsourcingOutDetailDTO {


    @ApiModelProperty(value = "行号")
    private String lineNo;

    @NotNull(message="sku数量不能为空")
    @ApiModelProperty(value = "数量")
    @Digits(integer = 9, fraction = 3,message="超过范围,小数3位有效位，整数9位有效位")
    private BigDecimal skuQty;

    @JsonIgnore
    @ApiModelProperty(value = "sku编号")
    private Long skuId;

    @NotBlank(message="sku编号不能为空")
    @ApiModelProperty(value = "sku编号")
    private String skuCode;

    @ApiModelProperty(value = "单位")
    @NotBlank(message="单位不能为空")
    private String unit;

    @ApiModelProperty(value = "单位编码")
    @NotEmpty(message="单位code不能为空")
    private String unitCode;
}
