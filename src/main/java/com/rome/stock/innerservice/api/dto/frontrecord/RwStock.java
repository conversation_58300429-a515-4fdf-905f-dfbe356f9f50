package com.rome.stock.innerservice.api.dto.frontrecord;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;

@Data
@EqualsAndHashCode
public class RwStock{

    @ApiModelProperty(value = "加盟商编码")
    private String franchisee;


    @ApiModelProperty(value = "加盟商名称")
    private String franchiseeName;

    @ApiModelProperty(value = "门店编码")
    private String shopCode;

    @ApiModelProperty(value = "门店名称")
    private String shopName;

    @ApiModelProperty(value = "商品编码")
    private String skuCode;

    @ApiModelProperty(value = "商品名称")
    private String skuName;

    @ApiModelProperty(value = "经营状态")
    private String saleStatus;

    @ApiModelProperty(value = "商品品类")
    private String skuType;

    @ApiModelProperty(value = "实际库存数量")
    private BigDecimal realQty;

    @ApiModelProperty(value = "基本单位")
    private String basicUnitCode;

    @ApiModelProperty(value = "基本单位名称")
    private String basicUnitName;

    @ApiModelProperty(value = "库存金额")
    private BigDecimal amount =BigDecimal.ZERO;

    @ApiModelProperty(value = "库存金额(含税)")
    private BigDecimal amountHasTax =BigDecimal.ZERO;

    @ApiModelProperty("箱库存数量(转换为箱规库存数量)")
    private BigDecimal boxScaleQty;

    @ApiModelProperty("箱规")
    private BigDecimal boxScale;

    @ApiModelProperty(value = "已出库在途库存")
    private BigDecimal onroadQty=BigDecimal.ZERO;

    @ApiModelProperty(value = "未出库完成的在途库存")
    private BigDecimal unCompleteOnRoadStock=BigDecimal.ZERO;

    private Long realWarehouseId;
}
