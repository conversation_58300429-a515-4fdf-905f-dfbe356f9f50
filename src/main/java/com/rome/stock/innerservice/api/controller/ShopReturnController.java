package com.rome.stock.innerservice.api.controller;

/**
 * 类ShopReturnController的实现描述：门店退货
 *
 * <AUTHOR> 2019/4/29 11:20
 */

import java.util.ArrayList;
import java.util.List;

import javax.annotation.Resource;

import com.google.common.collect.Lists;
import com.rome.stock.innerservice.api.dto.cmp7.ShopReturnResultDTO;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;

import com.alibaba.fastjson.JSON;
import com.github.pagehelper.PageInfo;
import com.rome.arch.core.clientobject.Response;
import com.rome.arch.core.exception.RomeException;
import com.rome.arch.util.controller.RomeController;
import com.rome.stock.innerservice.api.dto.RealWarehouse;
import com.rome.stock.innerservice.api.dto.WarehouseRecordPageDTO;
import com.rome.stock.innerservice.api.dto.WarehouseToBRecord;
import com.rome.stock.innerservice.api.dto.frontrecord.ShopReturnRecordDTO;
import com.rome.stock.innerservice.api.dto.warehouserecord.BatchResultDTO;
import com.rome.stock.innerservice.api.dto.warehouserecord.CancelRecordDTO;
import com.rome.stock.innerservice.api.dto.warehouserecord.ConfirmShopReturnDTO;
import com.rome.stock.innerservice.api.dto.warehouserecord.InWarehouseRecordDTO;
import com.rome.stock.innerservice.api.dto.warehouserecord.OutWarehouseRecordDTO;
import com.rome.stock.innerservice.common.CoreKibanaLog;
import com.rome.stock.innerservice.common.ResCode;
import com.rome.stock.innerservice.constant.ResponseMsg;
import com.rome.stock.innerservice.domain.repository.SapInterfaceLogRepository;
import com.rome.stock.innerservice.domain.service.RealWarehouseService;
import com.rome.stock.innerservice.domain.service.ShopReturnService;
import com.rome.stock.innerservice.remote.orderCneter.facade.OrderCenterFacade;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import io.swagger.annotations.ApiResponse;
import lombok.extern.slf4j.Slf4j;

/**
 * ShopReturnController类的实现描述：门店退货
 *
 * <AUTHOR> 2019/6/28 11:05
 */
@Slf4j
@RomeController
@RequestMapping("/stock/v1/shop_return")
@Api(tags={"门店退货"})
public class ShopReturnController {

    @Autowired
    private ShopReturnService shopReturnService;

    @Resource
    private SapInterfaceLogRepository sapInterfaceLogRepository;

    @Resource
    private RealWarehouseService realWarehouseService;
    @Resource
    private OrderCenterFacade orderCenterFacade;

    private final static String LOGTYPE = "innerTradeCall";


    @ApiOperation(value = "获取加盟退货仓库", nickname = "ShopSaleReturnGoodsRecord", produces = MediaType.APPLICATION_JSON_VALUE)
    @ApiResponse(code = 200, message = "success", response = WarehouseToBRecord.class)
    @RequestMapping(value = "/getReturnWarehouse", method = RequestMethod.POST)
    public Response getReturnWarehouse(@ApiParam(name = "shopCode", value = "申请单") @RequestParam("shopCode") String shopCode) {
        log.warn(CoreKibanaLog.getJobLog(LOGTYPE, "getReturnWarehouse", "获取加盟退货仓库: " + shopCode, shopCode));
        String message ="";
        boolean isSucc = false;
        String json = JSON.toJSONString(shopCode);
        try {
            RealWarehouse outRealWarehouse = realWarehouseService.getJoinReturnWarehouse(shopCode);
            isSucc = true;
            message = JSON.toJSONString(outRealWarehouse);
            return ResponseMsg.SUCCESS.buildMsg(outRealWarehouse);
        } catch (RomeException e) {
            message = e.getMessage();
            log.error("innerTrade调用 : " + e.getMessage(), e);
            return Response.builderFail(e.getCode(),e.getMessage());
        } catch (Exception e) {
            message = e.getMessage();
            log.error("innerTrade调用 : " + e.getMessage(), e);
            return Response.builderFail(ResCode.STOCK_ERROR_1003_DESC,ResCode.STOCK_ERROR_1003_DESC);
        }finally {
            sapInterfaceLogRepository.saveCallBackInterFaceLog(2, shopCode, "getReturnWarehouse",
                    json, message, isSucc);
        }
    }


    @ApiOperation(value = "门店退货申请", nickname = "addShopReturnApply", produces = MediaType.APPLICATION_JSON_VALUE)
    @ApiResponse(code = 200, message = "success", response = WarehouseToBRecord.class)
    @RequestMapping(value = "/addShopReturnApply", method = RequestMethod.POST)
    public Response addShopReturnApply(@ApiParam(name = "frontRecord", value = "申请单") @RequestBody @Validated OutWarehouseRecordDTO outWarehouseRecordDTO) {
        log.warn(CoreKibanaLog.getJobLog(LOGTYPE, "addShopReturnApply", "门店退货申请: " + outWarehouseRecordDTO.getRecordCode(), outWarehouseRecordDTO));
        try {
            shopReturnService.addShopReturn(outWarehouseRecordDTO);
            return ResponseMsg.SUCCESS.buildMsg();
        } catch (RomeException e) {
            log.error("门店退货申请 : "+outWarehouseRecordDTO.getRecordCode() + e.getMessage(), e);
            return Response.builderFail(e.getCode(),e.getMessage());
        } catch (Exception e) {
            log.error("门店退货申请 : "+outWarehouseRecordDTO.getRecordCode() + e.getMessage(), e);
            return Response.builderFail(ResCode.STOCK_ERROR_1003_DESC,e.getMessage());
        }
    }


    @ApiOperation(value = "门店退货申请确认", nickname = "confirmShopReturn", produces = MediaType.APPLICATION_JSON_VALUE)
    @ApiResponse(code = 200, message = "success")
    @RequestMapping(value = "/confirmShopReturn", method = RequestMethod.POST)
    public Response confirmShopReturn(@ApiParam(name = "confirmShopReturnDTO", value = "确认对象") @RequestBody @Validated ConfirmShopReturnDTO confimShopReturnDTO) {
        String json = JSON.toJSONString(confimShopReturnDTO);
        String message = "";
        boolean isSucc = false;
        log.warn(CoreKibanaLog.getJobLog(LOGTYPE, "confirmShopReturn", "门店退货申请确认: " + confimShopReturnDTO.getRecordCode(), json));
        try {
            confimShopReturnDTO.setType(1);
            shopReturnService.confirmShopReturn(confimShopReturnDTO);
            isSucc = true;
            message = "200";
            return ResponseMsg.SUCCESS.buildMsg();
        } catch (RomeException e) {
            message = e.getMessage();
            log.error("门店退货申请确认 : "+confimShopReturnDTO.getRecordCode() + e.getMessage(), e);
            return Response.builderFail(e.getCode(),e.getMessage());
        } catch (Exception e) {
            message = e.getMessage();
            log.error("门店退货申请确认 : "+confimShopReturnDTO.getRecordCode()+ e.getMessage(), e);
            return Response.builderFail(ResCode.STOCK_ERROR_1003_DESC,e.getMessage());
        } finally {
            sapInterfaceLogRepository.saveCallBackInterFaceLog(2, confimShopReturnDTO.getRecordCode(), "confirmShopReturn",
                    json, message, isSucc);
        }
    }

    @ApiOperation(value = "门店退货申请确认ForCmp", nickname = "confirmShopReturnForCmp", produces = MediaType.APPLICATION_JSON_VALUE)
    @ApiResponse(code = 200, message = "success")
    @RequestMapping(value = "/confirmShopReturnForCmp", method = RequestMethod.POST)
    public Response confirmShopReturnForCmp(@ApiParam(name = "confirmShopReturnDTO", value = "确认对象") @RequestBody @Validated ConfirmShopReturnDTO confimShopReturnDTO) {
        String json = JSON.toJSONString(confimShopReturnDTO);
        log.warn(CoreKibanaLog.getJobLog(LOGTYPE, "confirmShopReturn", "门店退货申请确认: " + confimShopReturnDTO.getRecordCode(), json));
        try {
            confimShopReturnDTO.setType(1);
            if(null == confimShopReturnDTO.getRecordCode()){
                throw new RomeException(ResCode.STOCK_ERROR_1002, "cmp单号不能为空");
            }
            List<String> warehouseRecordCodes=orderCenterFacade.queryOutWhRecordCodeByHisenseNo(confimShopReturnDTO.getRecordCode());
            if(CollectionUtils.isEmpty(warehouseRecordCodes)){
                return Response.builderFail(ResCode.STOCK_ERROR_1002, "根据cmp单号查询门店退货出库单为空");
            }
            for(String warehouseRecordCode :warehouseRecordCodes){
                String message = "";
                boolean isSucc = false;
                try{
                    confimShopReturnDTO.setRecordCode(warehouseRecordCode);
                    shopReturnService.confirmShopReturn(confimShopReturnDTO);
                    isSucc = true;
                    message = "200";
                } catch (RomeException e) {
                    log.error("门店退货申请确认 : "+confimShopReturnDTO.getRecordCode() + e.getMessage(), e);
                    return Response.builderFail(e.getCode(),e.getMessage());
                }catch (Exception e){
                    message=e.getMessage();
                    log.error(e.getMessage(), e);
                    return Response.builderFail(ResCode.STOCK_ERROR_1003_DESC,e.getMessage());
                }finally {
                    sapInterfaceLogRepository.saveCallBackInterFaceLog(1, warehouseRecordCode, "confirmShopReturnForCmp",
                            warehouseRecordCode, message, isSucc);
                }
            }
            return ResponseMsg.SUCCESS.buildMsg();
        } catch (RomeException e) {
            log.error("门店退货申请确认 : "+confimShopReturnDTO.getRecordCode() + e.getMessage(), e);
            return Response.builderFail(e.getCode(),e.getMessage());
        } catch (Exception e) {
            log.error("门店退货申请确认 : "+confimShopReturnDTO.getRecordCode()+ e.getMessage(), e);
            return Response.builderFail(ResCode.STOCK_ERROR_1003_DESC,e.getMessage());
        }
    }


    @ApiOperation(value = "门店退货-批量取消申请", nickname = "cancelShopReturnRecord", produces = MediaType.APPLICATION_JSON_VALUE)
    @ApiResponse(code = 200, message = "success")
    @RequestMapping(value = "/cancelShopReturnRecord", method = RequestMethod.POST)
    public Response cancelShopReturnRecord(@RequestBody List<CancelRecordDTO> list){
        List<BatchResultDTO> resultList =  new ArrayList<>();
        try {
            if(CollectionUtils.isNotEmpty(list)){
                for (CancelRecordDTO cancelRecordDTO : list) {
                    BatchResultDTO dto = new BatchResultDTO();
                    dto.setRecordCode(cancelRecordDTO.getRecordCode());
                    dto.setStatus(false);
                    String message = "";
                    boolean isSucc = false;
                    try {
                        shopReturnService.cancelShopReturnRecord(cancelRecordDTO);
                        dto.setStatus(true);
                        isSucc = true;
                        message = "200";
                    }catch (Exception e){
                        message=e.getMessage();
                        log.error(e.getMessage(), e);
                    }finally {
                        dto.setMessage(message);
                        resultList.add(dto);
                        sapInterfaceLogRepository.saveCallBackInterFaceLog(1, dto.getRecordCode(), "cancelShopReturnRecord",
                                JSON.toJSONString(cancelRecordDTO), message, isSucc);
                    }
                }
            }
            return ResponseMsg.SUCCESS.buildMsg(resultList);
        } catch (RomeException e) {
            log.error(e.getMessage(), e);
            return Response.builderFail(e.getCode(),e.getMessage());
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return Response.builderFail(ResCode.STOCK_ERROR_1003_DESC,e.getMessage());
        }
    }




    @ApiOperation(value = "门店退货通知创建入库", nickname = "shopReturnInCreateRecord", produces = MediaType.APPLICATION_JSON_VALUE)
    @ApiResponse(code = 200, message = "success")
    @RequestMapping(value = "/shopReturnInCreateRecord", method = RequestMethod.POST)
    public Response shopReturnInCreateRecord(@RequestBody @Validated InWarehouseRecordDTO inWarehouseRecordDTO) {
        String json = JSON.toJSONString(inWarehouseRecordDTO);
        log.warn(CoreKibanaLog.getJobLog(LOGTYPE, "shopReturnInCreateRecord", "门店退货通知创建入库: " + inWarehouseRecordDTO.getRecordCode(), json));
        try {
            shopReturnService.shopReturnInCreateRecord(inWarehouseRecordDTO);
            return ResponseMsg.SUCCESS.buildMsg();
        } catch (RomeException e) {
            log.error("门店退货通知创建入库 : "+inWarehouseRecordDTO.getRecordCode() + e.getMessage(), e);
            return Response.builderFail(e.getCode(),e.getMessage());
        } catch (Exception e) {
            log.error("门店退货通知创建入库 : "+inWarehouseRecordDTO.getRecordCode() + e.getMessage(), e);
            return Response.builderFail(ResCode.STOCK_ERROR_1003_DESC,e.getMessage());
        }
    }


    @ApiOperation(value = "根据条件查询门店退货单", nickname = "findShopReturnByCondition", produces = MediaType.APPLICATION_JSON_VALUE)
    @ApiResponse(code = 200, message = "success")
    @RequestMapping(value = "/findShopReturnByCondition", method = RequestMethod.POST)
    public Response findShopReturnByCondition(@RequestBody ShopReturnRecordDTO paramDTO) {
        try {
            PageInfo<ShopReturnRecordDTO> shopReturnRecordDTOPageInfo = shopReturnService.findShopReturnByCondition(paramDTO);
            return ResponseMsg.SUCCESS.buildMsg(shopReturnRecordDTOPageInfo);
        } catch (RomeException e) {
            log.error(e.getMessage(), e);
            return ResponseMsg.EXCEPTION.buildMsg(e.getCode(), e.getMessage());
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return ResponseMsg.EXCEPTION.buildMsg();
        }
    }

    @ApiOperation(value = "根据id查询明细", nickname = "queryShopReturnWithDetailById", produces = MediaType.APPLICATION_JSON_VALUE)
    @ApiResponse(code = 200, message = "success")
    @RequestMapping(value = "/queryShopReturnWithDetailById", method = RequestMethod.GET)
    public Response queryShopReturnWithDetailById(@RequestParam("id") String id) {
        try {
            ShopReturnRecordDTO shopReturnRecordDTOPageInfo = shopReturnService.queryShopReturnWithDetailById(id);
            return ResponseMsg.SUCCESS.buildMsg(shopReturnRecordDTOPageInfo);
        } catch (RomeException e) {
            log.error(e.getMessage(), e);
            return ResponseMsg.EXCEPTION.buildMsg(e.getCode(), e.getMessage());
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return ResponseMsg.EXCEPTION.buildMsg();
        }
    }

    @ApiOperation(value = "门店退货推送交易中心-单条", nickname = "pushDataTOTransaction", produces = MediaType.APPLICATION_JSON_VALUE)
    @ApiResponse(code = 200, message = "success")
    @RequestMapping(value = "/pushDataTOTransaction", method = RequestMethod.POST)
    public Response pushDataTOTransaction(@RequestBody ShopReturnRecordDTO shopReturnRecordDTO) {
        try {
            shopReturnService.pushDataTOTransaction(shopReturnRecordDTO);
            return ResponseMsg.SUCCESS.buildMsg();
        } catch (RomeException e) {
            log.error(e.getMessage(), e);
            return ResponseMsg.FAIL.buildMsg(e.getCode(),e.getMessage());
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return ResponseMsg.FAIL.buildMsg(ResCode.STOCK_ERROR_1003_DESC,ResCode.STOCK_ERROR_1003_DESC);
        }
    }

    @ApiOperation(value = "job批量查询门店退货单", nickname = "queryShopReturnRecord", produces = MediaType.APPLICATION_JSON_VALUE)
    @ApiResponse(code = 200, message = "success")
    @RequestMapping(value = "/queryShopReturnRecord", method = RequestMethod.POST)
    public Response queryShopReturnRecord(@RequestParam("page") Integer page, @RequestParam("pageSize") Integer pageSize) {
        try {
            List<ShopReturnRecordDTO> recordDTOS = shopReturnService.queryShopReturnRecord(page,pageSize);
            return ResponseMsg.SUCCESS.buildMsg(recordDTOS);
        } catch (RomeException e) {
            log.error(e.getMessage(), e);
            return ResponseMsg.EXCEPTION.buildMsg(e.getCode(), e.getMessage());
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return ResponseMsg.EXCEPTION.buildMsg();
        }
    }



    @ApiOperation(value = "job批量查询门店退货单推送Cmp", nickname = "queryShopReturnRecord", produces = MediaType.APPLICATION_JSON_VALUE)
    @ApiResponse(code = 200, message = "success")
    @RequestMapping(value = "/getWaitReturnNotifyToCmp", method = RequestMethod.POST)
    public Response<List<String>> getWaitReturnNotifyToCmp(@RequestParam("page") Integer page, @RequestParam("pageSize") Integer pageSize) {
        try {
            List<String> res = shopReturnService.getWaitReturnNotifyToCmp(page,pageSize);
            return ResponseMsg.SUCCESS.buildMsg(res);
        } catch (RomeException e) {
            log.error(e.getMessage(), e);
            return ResponseMsg.EXCEPTION.buildMsg(e.getCode(), e.getMessage());
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return ResponseMsg.EXCEPTION.buildMsg();
        }
    }

    @ApiOperation(value = "job批量查询门店退货单推送Cmp7", nickname = "getWaitReturnNotifyToCmp7", produces = MediaType.APPLICATION_JSON_VALUE)
    @ApiResponse(code = 200, message = "success")
    @RequestMapping(value = "/getWaitReturnNotifyToCmp7", method = RequestMethod.POST)
    public Response<List<String>> getWaitReturnNotifyToCmp7(@RequestParam("page") Integer page, @RequestParam("pageSize") Integer pageSize) {
        try {
            List<String> res = shopReturnService.getWaitReturnNotifyToCmp7(page,pageSize);
            return ResponseMsg.SUCCESS.buildMsg(res);
        } catch (RomeException e) {
            log.error(e.getMessage(), e);
            return ResponseMsg.EXCEPTION.buildMsg(e.getCode(), e.getMessage());
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return ResponseMsg.EXCEPTION.buildMsg();
        }
    }

    @ApiOperation(value = "job批量查询店务预约退货确认单结果推送cmp", nickname = "getWaitReturnNotifyToCmp7", produces = MediaType.APPLICATION_JSON_VALUE)
    @ApiResponse(code = 200, message = "success")
    @RequestMapping(value = "/getWaitReturnReceiveToCmp", method = RequestMethod.POST)
    public Response<List<WarehouseRecordPageDTO>> getWaitReturnReceiveToCmp(@RequestParam("page") Integer page, @RequestParam("pageSize") Integer pageSize) {
        try {
            List<WarehouseRecordPageDTO> res = shopReturnService.getWaitReturnReceiveToCmp(page,pageSize);
            return ResponseMsg.SUCCESS.buildMsg(res);
        } catch (RomeException e) {
            log.error(e.getMessage(), e);
            return ResponseMsg.EXCEPTION.buildMsg(e.getCode(), e.getMessage());
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return ResponseMsg.EXCEPTION.buildMsg();
        }
    }

    @ApiOperation(value = "直营退货结果推送cmp5", nickname = "pushReturnReceiveToCmp", produces = MediaType.APPLICATION_JSON_VALUE)
    @ApiResponse(code = 200, message = "success", response = Response.class)
    @RequestMapping(value = "/pushShopReturnResultCmp5", method = RequestMethod.POST)
    public void pushShopReturnResultCmp5(@RequestParam("recordCode")String recordCode){
        String message ="";
        boolean isSucc = false;
        List<ShopReturnResultDTO> list = Lists.newArrayList();
        try {
            Response response= shopReturnService.pushShopReturnResultCmp5(recordCode,list);
            message = JSON.toJSONString(response.getData());
            isSucc = true;
        } catch (RomeException e) {
            log.error(e.getMessage(), e);
            message = e.getMessage();
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            message = e.getMessage();
        } finally {
            sapInterfaceLogRepository.saveCallBackInterFaceLog(1, recordCode
                    , "pushShopReturnResultCmp5", JSON.toJSONString(list), message, isSucc);
        }
    }


    @ApiOperation(value = "加盟退货结果推送cmp6", nickname = "pushReturnReceiveToCmp", produces = MediaType.APPLICATION_JSON_VALUE)
    @ApiResponse(code = 200, message = "success", response = Response.class)
    @RequestMapping(value = "/pushShopReturnResultCmp6", method = RequestMethod.POST)
    public void pushShopReturnResultCmp6(@RequestParam("recordCode")String recordCode){
        String message ="";
        boolean isSucc = false;
        List<ShopReturnResultDTO> list=Lists.newArrayList();
        try {
            Response response= shopReturnService.pushShopReturnResultCmp6(recordCode,list);
            message = JSON.toJSONString(response.getData());
            isSucc = true;
        } catch (RomeException e) {
            log.error(e.getMessage(), e);
            message = e.getMessage();
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            message = e.getMessage();
        } finally {
            sapInterfaceLogRepository.saveCallBackInterFaceLog(1, recordCode
                    , "pushShopReturnResultCmp6", JSON.toJSONString(list), message, isSucc);
        }
    }


    @ApiOperation(value = "批量处理加盟门店退货过账单推送cmp", nickname = "ShopAllocationRecord", produces = MediaType.APPLICATION_JSON_VALUE)
    @ApiResponse(code = 200, message = "success", response = Response.class)
    @RequestMapping(value = "/handleJoinReturnPushCMP", method = RequestMethod.POST)
    public Response<String> handleJoinReturnPushCMP(@RequestParam("recordCode")String recordCode) {
        String message ="";
        boolean isSucc = false;
        try {
            shopReturnService.handleJoinReturnPushCMP(recordCode);
            message = "推送成功";
            isSucc = true;
            return Response.builderSuccess("");
        } catch (RomeException e) {
            log.error(e.getMessage(), e);
            message = e.getMessage();
            return Response.builderFail(e.getCode(),e.getMessage());
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            message = e.getMessage();
            return Response.builderFail(ResCode.STOCK_ERROR_1003_DESC,ResCode.STOCK_ERROR_1003_DESC);
        } finally {
            sapInterfaceLogRepository.saveCallBackInterFaceLog(1, recordCode
                    , "handleJoinReplenishPushCMP-LIST", recordCode, message, isSucc);
        }
    }

    @ApiOperation(value = "批量处理加盟门店退货过账单推送cmp7", nickname = "ShopAllocationRecord", produces = MediaType.APPLICATION_JSON_VALUE)
    @ApiResponse(code = 200, message = "success", response = Response.class)
    @RequestMapping(value = "/handleJoinReturnPushCMP7", method = RequestMethod.POST)
    public Response<String> handleJoinReturnPushCMP7(@RequestParam("recordCode")String recordCode) {
        String message ="";
        boolean isSucc = false;
        try {
            shopReturnService.handleJoinReturnPushCMP7(recordCode);
            message = "推送成功";
            isSucc = true;
            return Response.builderSuccess("");
        } catch (RomeException e) {
            log.error(e.getMessage(), e);
            message = e.getMessage();
            return Response.builderFail(e.getCode(),e.getMessage());
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            message = e.getMessage();
            return Response.builderFail(ResCode.STOCK_ERROR_1003_DESC,ResCode.STOCK_ERROR_1003_DESC);
        } finally {
            sapInterfaceLogRepository.saveCallBackInterFaceLog(1, recordCode
                    , "handleJoinReplenishPushCMP7-LIST", recordCode, message, isSucc);
        }
    }
}
