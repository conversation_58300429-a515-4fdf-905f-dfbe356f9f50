package com.rome.stock.innerservice.api.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

@Data
@EqualsAndHashCode
public class RealWarehouse extends Pagination  implements Serializable{

    @ApiModelProperty(value = "主键")
    private Long id;
    @ApiModelProperty(value = "仓库编码")
    private String realWarehouseCode;
    @ApiModelProperty(value = "仓库外部编号-wms")
    private String realWarehouseOutCode;
    @ApiModelProperty(value = "工厂编号")
    private String factoryCode;
    @ApiModelProperty(value = "工厂名称")
    private String factoryName;
    @ApiModelProperty(value = "仓库名称")
    private String realWarehouseName;
    @ApiModelProperty(value = "仓库类型")
    private Integer realWarehouseType;
    @ApiModelProperty(value = "仓库类型名称")
    private String realWarehouseTypeName;
    @ApiModelProperty(value = "门店编号")
    private String shopCode;
    @ApiModelProperty(value = "仓库状态")
    private Integer realWarehouseStatus;
    @ApiModelProperty(value = "优先级")
    private Integer realWarehousePriority;
    @ApiModelProperty(value = "邮编")
    private String realWarehousePostcode;
    @ApiModelProperty(value = "联系手机")
    private String realWarehouseMobile;
    @ApiModelProperty(value = "联系电话")
    private String realWarehousePhone;
    @ApiModelProperty(value = "联系邮箱")
    private String realWarehouseEmail;
    @ApiModelProperty(value = "国家")
    private String realWarehouseCountry;
    @ApiModelProperty(value = "省份")
    private String realWarehouseProvince;
    @ApiModelProperty(value = "城市")
    private String realWarehouseCity;
    @ApiModelProperty(value = "区县")
    private String realWarehouseCounty;
    @ApiModelProperty(value = "四级区域")
    private String realWarehouseArea;
    @ApiModelProperty(value = "创建日期")
    private String createDate;
    @ApiModelProperty(value = "国家code")
    private String realWarehouseCountryCode;
    @ApiModelProperty(value = "省份code")
    private String realWarehouseProvinceCode;
    @ApiModelProperty(value = "城市code")
    private String realWarehouseCityCode;
    @ApiModelProperty(value = "县城市code")
    private String realWarehouseCountyCode;
    @ApiModelProperty(value = "四级区域code")
    private String realWarehouseAreaCode;
    @ApiModelProperty(value = "详细地址")
    private String realWarehouseAddress;
    @ApiModelProperty(value = "联系人姓名")
    private String realWarehouseContactName;
    @ApiModelProperty(value = "备注信息")
    private String realWarehouseRemark;
    @ApiModelProperty(value = "操作人用户id", required = true)
    private Long userId;
    @ApiModelProperty(value = "是否归中台管控0否1是")
    private Integer hasConfig;
    @ApiModelProperty(value = "创建时间")
    private Date createTime;
    @ApiModelProperty(value = "创建人")
    private Long creator;
    @ApiModelProperty(value = "修改人")
    private Long modifier;
    @ApiModelProperty(value = "修改时间")
    private Date updateTime;
    @ApiModelProperty(value = "仓库层级: 1-一级，2.二级")
    private Integer realWarehouseRank;
    @ApiModelProperty(value = "仓库层级名称: 1-一级，2.二级")
    private String realWarehouseRankName;
    @ApiModelProperty(value = "行政区域名称")
    private String regionName;
    @ApiModelProperty(value = "行政区域ID")
    private Long regionId;
    //财务中台相关字段
    @ApiModelProperty(value = "成本中心编码")
    private String costCenterCode;
    @ApiModelProperty(value = "成本中心名称")
    private String costCenterName;
    @ApiModelProperty(value = "仓库业务类型: 1: 直营门店 2: 加盟门店 3: 仓库 4. 其他仓库")
    private Integer rwBusinessType;
    @ApiModelProperty(value = "公司编码")
    private String companyCode;
    @ApiModelProperty(value = "公司名称")
    private String companyName;
    @ApiModelProperty(value = "是否允许负库存(0: 不允许 1: 允许)")
    private Integer allowNegtiveStock;
    private List<Long> labIds;
    @ApiModelProperty(value = "对应退货仓ID")
    private Long returnWarehouseId;
    @ApiModelProperty(value = "物资存储类型 1:运输单位 2:基本单位")
    private Integer materialStorageType;
    @ApiModelProperty(value = "物资存储名称")
    private String materialStorageName;
    @ApiModelProperty(value = "实仓虚仓(0: 虚仓 1: 实仓)")
    private Integer solidEmptyWarehouseStatus;

    @ApiModelProperty(value = "是否收入计费:[0-否,1-是]")
    private Integer revenueStat;

    @ApiModelProperty(value = "是否成本计费:[0-否,1-是]")
    private Integer costStat;
    @ApiModelProperty(value = "仓店一体标识 0 -非仓店一体   1仓店一体")
    private Integer warehouseStoreIdenti;

    @ApiModelProperty(value = "虚仓集合")
    private List<VirtualWarehouse> virtualWarehouseList;
}
