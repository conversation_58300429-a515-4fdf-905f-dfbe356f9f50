package com.rome.stock.innerservice.api.controller;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

import com.alibaba.fastjson.JSON;
import com.rome.stock.innerservice.api.dto.RwBatchDTO;
import com.rome.stock.innerservice.domain.repository.SapInterfaceLogRepository;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;

import com.rome.arch.core.clientobject.Response;
import com.rome.arch.core.exception.RomeException;
import com.rome.arch.util.controller.RomeController;
import com.rome.stock.common.utils.DateUtil;
import com.rome.stock.innerservice.api.dto.WarehouseRecordPageDTO;
import com.rome.stock.innerservice.common.DateFormatTools;
import com.rome.stock.innerservice.common.ResCode;
import com.rome.stock.innerservice.domain.service.TransferOrderSyncJobService;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiResponse;
import lombok.extern.slf4j.Slf4j;

/**
 * @Doc:.....
 * @Author: lchy
 * @Date: 2019/7/22
 * @Version 1.0
 */
@Slf4j
@RomeController
@RequestMapping("/stock/v1/postingAccount")
@Api(tags = {"出库单过账"})
public class PostingAccountController {
    @Autowired
    private TransferOrderSyncJobService transferOrderSyncJobService;
    @Autowired
    private SapInterfaceLogRepository sapInterfaceLogRepository;


    @ApiOperation(value = "定时查询待过账的收货批次信息", nickname = "queryWaitTransferBatchRecord", produces = MediaType.APPLICATION_JSON_VALUE)
    @ApiResponse(code = 200, message = "success", response = List.class)
    @RequestMapping(value = "/queryWaitTransferBatchRecord", method = RequestMethod.POST)
    public Response<List<RwBatchDTO>> queryWaitTransferRecord(@RequestParam Integer page, @RequestParam Integer maxResult) {
        try {
            List<RwBatchDTO> pageInfo = transferOrderSyncJobService.queryWaitTransferBatchRecord(page, maxResult);
            return Response.builderSuccess(pageInfo);
        } catch (RomeException e) {
            log.error(e.getMessage(), e);
            return Response.builderFail(e.getCode(), e.getMessage());
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return Response.builderFail(ResCode.STOCK_ERROR_1003, ResCode.STOCK_ERROR_1003_DESC);
        }
    }

    @ApiOperation(value = "根据收货批次过账", nickname = "postAccountByWmsRecordCode", produces = MediaType.APPLICATION_JSON_VALUE)
    @ApiResponse(code = 200, message = "success", response = List.class)
    @RequestMapping(value = "/postAccountByWmsRecordCode", method = RequestMethod.POST)
    public Response postAccountByWmsRecordCode(@RequestBody RwBatchDTO rwBatchDTO) {
        try {
            transferOrderSyncJobService.postAccountByWmsRecordCode(rwBatchDTO);
            return Response.builderSuccess("success");
        } catch (RomeException e) {
            log.error(e.getMessage(), e);
            return Response.builderFail(e.getCode(), e.getMessage());
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return Response.builderFail(ResCode.STOCK_ERROR_1003, ResCode.STOCK_ERROR_1003_DESC);
        }
    }




    @ApiOperation(value = "定时查询待过账的出库单[非门店仓]", nickname = "queryWaitTransferOrder", produces = MediaType.APPLICATION_JSON_VALUE)
    @ApiResponse(code = 200, message = "success", response = List.class)
    @RequestMapping(value = "/queryWaitTransferOrder", method = RequestMethod.POST)
    public Response<List<WarehouseRecordPageDTO>> queryWaitTransferOrder(@RequestParam Long minId, @RequestParam Integer maxResult) {
        try {
            List<WarehouseRecordPageDTO> personPageInfo = transferOrderSyncJobService.queryWaitTransferOrder(minId, maxResult);
            return Response.builderSuccess(personPageInfo);
        } catch (RomeException e) {
            log.error(e.getMessage(), e);
            return Response.builderFail(e.getCode(), e.getMessage());
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return Response.builderFail(ResCode.STOCK_ERROR_1003, ResCode.STOCK_ERROR_1003_DESC);
        }
    }

    @ApiOperation(value = "定时查询待过账的出库单[门店仓]", nickname = "queryWaitTransferOrderForShopHouse", produces = MediaType.APPLICATION_JSON_VALUE)
    @ApiResponse(code = 200, message = "success", response = List.class)
    @RequestMapping(value = "/queryWaitTransferOrderForShopHouse", method = RequestMethod.POST)
    public Response<List<WarehouseRecordPageDTO>> queryWaitTransferOrderForShopHouse(@RequestParam Long minId, @RequestParam Integer maxResult) {
        try {
            List<WarehouseRecordPageDTO> personPageInfo = transferOrderSyncJobService.queryWaitTransferOrderForShopHouse(minId, maxResult);
            return Response.builderSuccess(personPageInfo);
        } catch (RomeException e) {
            log.error(e.getMessage(), e);
            return Response.builderFail(e.getCode(), e.getMessage());
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return Response.builderFail(ResCode.STOCK_ERROR_1003, ResCode.STOCK_ERROR_1003_DESC);
        }
    }

    @ApiOperation(value = "指定单据过账", nickname = "postAccountByRecordCode", produces = MediaType.APPLICATION_JSON_VALUE)
    @ApiResponse(code = 200, message = "success", response = List.class)
    @RequestMapping(value = "/postAccountByRecordCode", method = RequestMethod.POST)
    public Response<String> postAccountByRecordCode(@RequestParam String code) {
        try {
            transferOrderSyncJobService.postAccountByRecordCode(code);
            return Response.builderSuccess("");
        } catch (RomeException e) {
            log.error(e.getMessage(), e);
            return Response.builderFail(e.getCode(), e.getMessage());
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return Response.builderFail(ResCode.STOCK_ERROR_1003, ResCode.STOCK_ERROR_1003_DESC);
        }
    }

    @ApiOperation(value = "指定单据过账[批量]", nickname = "postAccountByRecordCodeList", produces = MediaType.APPLICATION_JSON_VALUE)
    @ApiResponse(code = 200, message = "success", response = List.class)
    @RequestMapping(value = "/postAccountByRecordCodeList", method = RequestMethod.POST)
    public Response<List<String>> postAccountByRecordCodeList(@RequestBody List<String> codes) {
        List<String> res = new ArrayList<>();
        for(String code :codes) {
            try {
                transferOrderSyncJobService.postAccountByRecordCode(code);
                res.add(code + ",处理成功");
            } catch (RomeException e) {
                log.error(e.getMessage(), e);
                res.add(code + ",处理失败：" + e.getMessage());
            } catch (Exception e) {
                log.error(e.getMessage(), e);
                res.add(code + ",处理失败：" + e.getMessage());
            }
        }
        return Response.builderSuccess(res);
    }


    @ApiOperation(value = "根据单据过账", nickname = "processSyncPostingAccountResultToSap", produces = MediaType.APPLICATION_JSON_VALUE)
    @ApiResponse(code = 200, message = "success", response = List.class)
    @RequestMapping(value = "/processSyncPostingAccountResultToSap", method = RequestMethod.POST)
    public Response processSyncPostingAccountResultToSap(@RequestBody WarehouseRecordPageDTO warehouseRecord) {
        try {
            transferOrderSyncJobService.syncPostingAccountToSap(warehouseRecord , null);
            return Response.builderSuccess("success");
        } catch (RomeException e) {
            log.error(e.getMessage(), e);
            return Response.builderFail(e.getCode(), e.getMessage());
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return Response.builderFail(ResCode.STOCK_ERROR_1003, ResCode.STOCK_ERROR_1003_DESC);
        }
    }

    @ApiOperation(value = "电商出入库单过账", nickname = "syncOnlinePostingAccountToSap", produces = MediaType.APPLICATION_JSON_VALUE)
    @ApiResponse(code = 200, message = "success", response = List.class)
    @RequestMapping(value = "/syncOnlinePostingAccountToSap", method = RequestMethod.POST)
    public Response syncOnlinePostingAccountToSap() {
        try {
            transferOrderSyncJobService.syncOnlinePostingAccountToSap();
            return Response.builderSuccess("success");
        } catch (RomeException e) {
            log.error(e.getMessage(), e);
            return Response.builderFail(e.getCode(), e.getMessage());
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return Response.builderFail(ResCode.STOCK_ERROR_1003, ResCode.STOCK_ERROR_1003_DESC);
        }
    }

    @ApiOperation(value = "电商出入库单过账[指定日期过账]", nickname = "syncOnlinePostingAccountToSapForAssignDate", produces = MediaType.APPLICATION_JSON_VALUE)
    @ApiResponse(code = 200, message = "success", response = List.class)
    @RequestMapping(value = "/syncOnlinePostingAccountToSapForAssignDate", method = RequestMethod.POST)
    public Response syncOnlinePostingAccountToSap(@RequestParam(value="dataStr") String dataStr,
                                                  @RequestParam(required = false, defaultValue = "false",name = "是否强制过账") Boolean forthPostAccount) {
        try {
            transferOrderSyncJobService.syncOnlinePostingAccountToSap(dataStr,forthPostAccount);
            return Response.builderSuccess("success");
        } catch (RomeException e) {
            log.error(e.getMessage(), e);
            return Response.builderFail(e.getCode(), e.getMessage());
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return Response.builderFail(ResCode.STOCK_ERROR_1003, ResCode.STOCK_ERROR_1003_DESC);
        }
    }

    @ApiOperation(value = "包材出入库单过账", nickname = "syncOnlinePostingAccountToSap", produces = MediaType.APPLICATION_JSON_VALUE)
    @ApiResponse(code = 200, message = "success", response = List.class)
    @RequestMapping(value = "/syncPackagePostingAccountToSap", method = RequestMethod.POST)
    public Response syncPackagePostingAccountToSap() {
        try {
            transferOrderSyncJobService.syncPackagePostingAccountToSap();
            return Response.builderSuccess("success");
        } catch (RomeException e) {
            log.error(e.getMessage(), e);
            return Response.builderFail(e.getCode(), e.getMessage());
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return Response.builderFail(ResCode.STOCK_ERROR_1003, ResCode.STOCK_ERROR_1003_DESC);
        }
    }

    @ApiOperation(value = "包材出入库单过账[指定日期过账]", nickname = "syncOnlinePostingAccountToSapForAssignDate", produces = MediaType.APPLICATION_JSON_VALUE)
    @ApiResponse(code = 200, message = "success", response = List.class)
    @RequestMapping(value = "/syncPackagePostingAccountToSapForAssignDate", method = RequestMethod.POST)
    public Response syncPackagePostingAccountToSap(@RequestParam(name="过账日期[如2019-11-21则过2019-11-20的单子的账]") String dataStr,
                                                  @RequestParam(required = false, defaultValue = "false",name = "是否强制过账") Boolean forthPostAccount) {
        try {
            transferOrderSyncJobService.syncPackagePostingAccountToSap(dataStr,forthPostAccount);
            return Response.builderSuccess("success");
        } catch (RomeException e) {
            log.error(e.getMessage(), e);
            return Response.builderFail(e.getCode(), e.getMessage());
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return Response.builderFail(ResCode.STOCK_ERROR_1003, ResCode.STOCK_ERROR_1003_DESC);
        }
    }

    @ApiOperation(value = "交易IF99过账", nickname = "syncOnlinePostingAccountToSap", produces = MediaType.APPLICATION_JSON_VALUE)
    @ApiResponse(code = 200, message = "success", response = List.class)
    @RequestMapping(value = "/syncTradeReturnPostingAccount", method = RequestMethod.POST)
    public Response syncTradeReturnPostingAccount() {
        try {
            String dateTime = DateFormatTools.dateToString(new Date(), DateUtil.NORM_DATE_PATTERN);
            transferOrderSyncJobService.syncTradeReturnPostingAccount(dateTime);
            return Response.builderSuccess("success");
        } catch (RomeException e) {
            log.error(e.getMessage(), e);
            return Response.builderFail(e.getCode(), e.getMessage());
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return Response.builderFail(ResCode.STOCK_ERROR_1003, ResCode.STOCK_ERROR_1003_DESC);
        }
    }

    @ApiOperation(value = "交易IF99指定日期过账", nickname = "syncOnlinePostingAccountToSap", produces = MediaType.APPLICATION_JSON_VALUE)
    @ApiResponse(code = 200, message = "success", response = List.class)
    @RequestMapping(value = "/syncTradeReturnPostingAccountForAssignDate", method = RequestMethod.POST)
    public Response syncTradeReturnPostingAccountForAssignDate(@RequestParam(name="过账日期[如2019-11-21则过2019-11-20的单子的账]") String dataStr) {
        try {
            transferOrderSyncJobService.syncTradeReturnPostingAccount(dataStr);
            return Response.builderSuccess("success");
        } catch (RomeException e) {
            log.error(e.getMessage(), e);
            return Response.builderFail(e.getCode(), e.getMessage());
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return Response.builderFail(ResCode.STOCK_ERROR_1003, ResCode.STOCK_ERROR_1003_DESC);
        }
    }


    @ApiOperation(value = "电商出入库单过账计算", nickname = "syncOnlinePostingAccountToSap", produces = MediaType.APPLICATION_JSON_VALUE)
    @ApiResponse(code = 200, message = "success", response = List.class)
    @RequestMapping(value = "/calculationOnlinePostingAccount", method = RequestMethod.POST)
    public Response calculationOnlinePostingAccount(@RequestParam(value = "type" ,defaultValue = "A") String type) {
        try {
            transferOrderSyncJobService.calculationOnlinePostingAccount(type);
            return Response.builderSuccess("success");
        } catch (RomeException e) {
            log.error(e.getMessage(), e);
            return Response.builderFail(e.getCode(), e.getMessage());
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return Response.builderFail(ResCode.STOCK_ERROR_1003, ResCode.STOCK_ERROR_1003_DESC);
        }
    }

    @ApiOperation(value = "电商出入库单过账计算[指定日期过账]", nickname = "syncOnlinePostingAccountToSapForAssignDate", produces = MediaType.APPLICATION_JSON_VALUE)
    @ApiResponse(code = 200, message = "success", response = List.class)
    @RequestMapping(value = "/calculationOnlinePostingAccountForAssignDate", method = RequestMethod.POST)
    public Response calculationOnlinePostingAccount(@RequestParam(name="过账日期[如2019-11-21则过2019-11-20的单子的账]") String dataStr,
                                                    @RequestParam(value = "type") String type,
                                                  @RequestParam(required = false, defaultValue = "false",name = "是否强制过账") Boolean forthPostAccount) {
        try {
            transferOrderSyncJobService.calculationOnlinePostingAccount(dataStr,forthPostAccount,type);
            return Response.builderSuccess("success");
        } catch (RomeException e) {
            log.error(e.getMessage(), e);
            return Response.builderFail(e.getCode(), e.getMessage());
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return Response.builderFail(ResCode.STOCK_ERROR_1003, ResCode.STOCK_ERROR_1003_DESC);
        }
    }


    @ApiOperation(value = "一件代发出出入库创建po、pr单[指定日期[如2019-11-21则过2019-11-20的单子]]", nickname = "createOnePostPoAndPr", produces = MediaType.APPLICATION_JSON_VALUE)
    @ApiResponse(code = 200, message = "success", response = List.class)
    @RequestMapping(value = "/createOnePostPoAndPr", method = RequestMethod.POST)
    public Response createOnePostPoAndPr(@RequestParam(value = "dataStr") String dataStr) {
        Response response = Response.builderSuccess("success");
        boolean result = false;
        try {
            transferOrderSyncJobService.createOnePostPoAndPr(dataStr);
            result = true;
            return response;
        } catch (RomeException e) {
            log.error(e.getMessage(), e);
            response = Response.builderFail("sc_1003", e.getMessage());
            return Response.builderFail(e.getCode(), e.getMessage());
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            response = Response.builderFail("sc_1003", e.getMessage());
            return Response.builderFail(ResCode.STOCK_ERROR_1003, ResCode.STOCK_ERROR_1003_DESC);
        } finally {
            String msg = String.valueOf(response.getMsg());
            if(!result && msg.length() > 3000){
                response.setMsg(msg.substring(0, 3000));
            }
            if (!result) {
                //成功的 不保存返回值，返回值太大 保存不下
                sapInterfaceLogRepository.saveSapInterFaceLog(dataStr, "/api/v1/po", "createPo",
                        dataStr, result ? "200" : JSON.toJSONString(response), result);
            }
        }
    }

}
