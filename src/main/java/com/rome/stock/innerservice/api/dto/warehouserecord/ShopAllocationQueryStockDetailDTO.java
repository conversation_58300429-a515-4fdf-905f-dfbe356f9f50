package com.rome.stock.innerservice.api.dto.warehouserecord;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;

/**
 * @Description
 * <AUTHOR>
 * @Date 2023-07-25
 * @Version 1.0
 */
@Data
@EqualsAndHashCode
public class ShopAllocationQueryStockDetailDTO {


    @ApiModelProperty(value = "商品编码")
    private String skuCode;


    @ApiModelProperty(value = "skuId")
    private Long skuId;

    @ApiModelProperty(value = "调拨数量")
    private BigDecimal skuQty;
}
