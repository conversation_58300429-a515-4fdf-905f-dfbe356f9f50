package com.rome.stock.innerservice.api.dto;

import com.rome.stock.common.page.Pagination;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR> zk
 * @since : 2023-10-10
 */
@Data
public class StockStatementDTO extends Pagination {
    @ApiModelProperty(value = "加盟商code  最多10个")
    private List<String> dsCodeList;
    @ApiModelProperty(value = "门店code ， 最多100个")
    private List<String> shopCodeList;
    @ApiModelProperty(value = "skuCode ，  最多100个")
    private List<String> skuCodeList;
    @ApiModelProperty(value = "业务单号，    最多100个")
    private List<String> businessCodeList;
    @ApiModelProperty(value = "记账类型： 1出，2入")
    private Integer queryType;
    @ApiModelProperty(value = "单据类型")
    private String recordTypeCode;
    @ApiModelProperty(value = "创建时间(开始) yyyy-MM-dd HH:mm:ss")
    private String startTime;
    @ApiModelProperty(value = "创建时间(结束) yyyy-MM-dd HH:mm:ss")
    private String endTime;

    @ApiModelProperty(value = "财务日期, yyyy-MM-dd")
    private String financeDate;
}
