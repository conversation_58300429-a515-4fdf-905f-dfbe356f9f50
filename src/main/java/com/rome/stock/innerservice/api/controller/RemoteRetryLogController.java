package com.rome.stock.innerservice.api.controller;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.github.pagehelper.PageInfo;
import com.rome.arch.core.clientobject.Response;
import com.rome.arch.util.controller.RomeController;
import com.rome.stock.common.enums.log.RemoteRetryMethodType;
import com.rome.stock.innerservice.api.dto.RemoteInterfaceLogDTO;
import com.rome.stock.innerservice.api.dto.RemoteInterfaceLogQuery;
import com.rome.stock.innerservice.api.dto.RemoteInterfaceLogRetryDTO;
import com.rome.stock.innerservice.api.dto.RemoteInterfacePackageMaterialDTO;
import com.rome.stock.innerservice.domain.service.RemoteRetryLogService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiResponse;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;

import javax.annotation.Resource;
import java.util.List;

/**
 * @Description :
 * @Author: nyacc
 * @Date: 2022/1/19 13:59
 */

@Slf4j
@RomeController
@RequestMapping(value = "/stock/remoteLog")
@Api(tags = {"远程请求相关记录"})
public class RemoteRetryLogController {


    @Resource
    private RemoteRetryLogService remoteRetryLogService;


    @ApiOperation(value = "分页查询记录", produces = MediaType.APPLICATION_JSON_VALUE)
    @ApiResponse(code = 200, message = "success")
    @PostMapping("/v1/page")
    public Response<PageInfo<RemoteInterfaceLogDTO>> getPageInfo(@RequestBody RemoteInterfaceLogQuery query){
        return Response.builderSuccess(remoteRetryLogService.getRemoteLogPageInfo(query));
    }


    @ApiOperation(value = "重试记录", produces = MediaType.APPLICATION_JSON_VALUE)
    @ApiResponse(code = 200, message = "success")
    @PostMapping("/v1/retry")
    public Response<List<Long>> retry(@RequestBody RemoteInterfaceLogRetryDTO remoteInterfaceLogRetryDTO){
        return Response.builderSuccess(remoteRetryLogService.retry(remoteInterfaceLogRetryDTO));
    }

    @ApiOperation(value = "查询需要重试的列表数据", produces = MediaType.APPLICATION_JSON_VALUE)
    @ApiResponse(code = 200, message = "success")
    @GetMapping("/v1/getRetryIds")
    public Response<List<Long>> getRetryIds(@RequestParam("minId")Long minId, @RequestParam("limit")Integer limit){
        return Response.builderSuccess(remoteRetryLogService.getRetryIds(minId, limit));
    }


    @ApiOperation(value = "查询业务类型", produces = MediaType.APPLICATION_JSON_VALUE)
    @ApiResponse(code = 200, message = "success")
    @GetMapping("/v1/type")
    public Response<String> getTypes(){
        JSONArray jsonArray = new JSONArray();
        for (RemoteRetryMethodType e : RemoteRetryMethodType.values()) {
            JSONObject object = new JSONObject();
            object.put("value", e.getCode());
            object.put("text", e.getDesc());
            jsonArray.add(object);
        }
        return Response.builderSuccess(jsonArray.toJSONString());
    }

    @ApiOperation(value = "数据导出", produces = MediaType.APPLICATION_JSON_VALUE)
    @ApiResponse(code = 200, message = "success")
    @PostMapping("/v1/expertPackageMaterialOut")
    public Response<PageInfo<RemoteInterfacePackageMaterialDTO>> expertPackageMaterialOut(@RequestBody RemoteInterfaceLogQuery query){
        return Response.builderSuccess( remoteRetryLogService.expertPackageMaterialOut(query));
    }


}
