package com.rome.stock.innerservice.api.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * @Doc:.....
 * @Author: lchy
 * @Date: 2020/4/28
 * @Version 1.0
 */
@Data
@EqualsAndHashCode
public class WDTPageInfoDTO {
    private  Long id;
    private Long merchantId;
    private String originOrderCode;
    private String outRecordCode;
    private String recordCode;

    private String channelCode;
    private String channelName;

    private Long realWarehouseId;
    private String realWarehouseCode;
    private String realWarehouseName;

    private Long virtualWarehouseId;
    private String virtualWarehouseCode;
    private String virtualWarehouseName;
    private String realWarehouseAddress;

    private Integer recordStatus;

    private Date payTime;

    private Date updateTime ;

    private Date createTime ;

    private Integer splitType;
    private Integer allotStatus;

    private String logisticsCode;

    private String provinceCode;
    private String cityCode;
    private String countyCode;
    private String province;
    private String city;
    private String county;
    private String address;
    private String name;
    private String mobile;
    private String addressSimply;
    /**
     * 页面操作的用户id，跟userCode 不是同一个东西
     */
    private Long userId;
    /**
     * 下单用户
     */
    private String userCode;

    private Integer detailSize;
    private BigDecimal totalQty;
    private List<WDTSaleDetailDTO> detailDTOList;

    private Integer splitColor;
    private String parentSkuCode;
    /**
     * 是否预售 0--否 1--是
     */
    private Integer isPreSale;

    private String recordStatusName;

    private String splitTypeName;

    /**
     * 拆单数
     */
    private Integer splitCount;

    private Integer recordType;
    private Integer tenantId;

    /**
     * 是否，强制重新计算，true表示停发仓正常落do
     */
    private Boolean forceReCal = false;

    @ApiModelProperty(value = "是否强制调用接口，1.是")
    private  Integer isForce;
}
