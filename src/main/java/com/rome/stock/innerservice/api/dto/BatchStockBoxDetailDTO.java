package com.rome.stock.innerservice.api.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * DO 实体类
 * 
 * <AUTHOR>
 * @date 2024-05-17
 */
@Data
@NoArgsConstructor
@EqualsAndHashCode
public class BatchStockBoxDetailDTO extends Pagination {
	/**
	 * 包含count查询
	 */
	@ApiModelProperty(value="是否统计条数")
	private boolean count = true;

	/**
     * 唯一主键
     */
	private Long id;

	/**
     * 批次编码-批次号
     */
	private String batchCode;

	/**
     * 生产日期
     */
	private Date productDate;

	/**
     * 商品skuID
     */
	private Long skuId;

	@ApiModelProperty("商品sku编码集合")
	private List<Long> skuIds;

	/**
     * 商品编码
     */
	private String skuCode;

	@ApiModelProperty("商品名称")
	private String skuName;

	@ApiModelProperty(value = "主单位(商品单位)")
	private String unit;

	/**
	 * 总库存量
	 */
	private BigDecimal skuQty=BigDecimal.ZERO;

	@ApiModelProperty(value = "锁定数量")
	private BigDecimal lockQty;

	/**
     * 原箱真实库存
     */
	private BigDecimal boxSkuQty;

	/**
     * 原箱锁定库存
     */
	private BigDecimal boxLockQty;

	/**
     * 非原箱真实库存
     */
	private BigDecimal mixSkuQty;

	/**
     * 非原箱锁定库存
     */
	private BigDecimal mixLockQty;

	/**
     * 实体仓库id
     */
	private Long realWarehouseId;

	/**
     * 商家id
     */
	private Long merchantId;
	/**
	 * 创建时间
	 */
	private Date createTime;
	/**
	 * 修改时间
	 */
	private Date updateTime;
	/**
	 * 创建人
	 */
	private Long creator;
	/**
	 * 更新人
	 */
	private Long modifier;
	/**
	 * 是否可用：0-否，1-是
	 */
	private Byte isAvailable;
	/**
	 * 是否逻辑删除：0-否，1-是
	 */
	private Byte isDeleted;

	@ApiModelProperty("工厂编号")
	private String factoryCode;

	@ApiModelProperty("实仓仓库编号")
	private String realWarehouseCode;

	@ApiModelProperty("实仓仓库名称")
	private String realWarehouseName;

	@ApiModelProperty(value = "生产日期")
	private Date productionDate;

	@ApiModelProperty(value = "入库日期")
	private Date entryDate;

	@ApiModelProperty(value = "保质期天数")
	private Integer totalShelfLifeDay;

	@ApiModelProperty(value = "有效期")
	private Date validityDate;

	@ApiModelProperty(value = "剩余到期天数")
	private Integer remainShelfLifeDay;

	@ApiModelProperty(value = "不可销售日期")
	private Date noSaleDate;

	@ApiModelProperty(value = "最佳销售日期")
	private Date bestSaleDate;

	@ApiModelProperty(value = "最晚发货日期")
	private Date latestDeliveryDate;

	@ApiModelProperty("查询,实仓仓库ID列表")
	private List<Long> realWarehouseIds;

	@ApiModelProperty("库存查询类型，0-小于0，1-等于0，2大于0")
	private Integer type;

	@ApiModelProperty("商品单位编码")
	private String unitCode;

}
