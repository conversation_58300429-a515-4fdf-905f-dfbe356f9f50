package com.rome.stock.innerservice.api.dto.bom;

import com.rome.stock.innerservice.common.VmAllocation.AllocationConstant;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.ToString;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @description  要转换成的目标对象
 * @date 2020/9/23 11:44
 * @throw
 */
@Data
@ToString
public class TargetBomDTO {

    @ApiModelProperty(value = "商品编码")
    private String skuCode;

    @ApiModelProperty(value = "单位编码")
    private String unitCode;

    @ApiModelProperty(value = "单位名称")
    private String unitName;

    @ApiModelProperty(value = "数量")
    private BigDecimal skuQty;

    /**
     * 单位编码和商品编码
     * @return
     */
    public String getSkuUnitCodeKey(){
        return this.skuCode+"_"+this.unitCode;
    }



    /**
     *基础单位skuCode和type组合key
     */
    public String getBaseSkuTypeKey(){
        return this.skuCode+"_"+ AllocationConstant.BASIC_TYPE;
    }
}    
   