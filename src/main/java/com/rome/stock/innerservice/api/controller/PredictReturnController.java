package com.rome.stock.innerservice.api.controller;

import com.github.pagehelper.PageInfo;
import com.rome.arch.core.clientobject.Response;
import com.rome.arch.core.exception.RomeException;
import com.rome.arch.util.controller.RomeController;
import com.rome.stock.innerservice.api.dto.frontrecord.PredictReturnDTO;
import com.rome.stock.innerservice.api.dto.frontrecord.PredictReturnDetailDTO;
import com.rome.stock.innerservice.common.ResCode;
import com.rome.stock.innerservice.domain.service.PredictReturnService;
import com.rome.stock.innerservice.remote.stockCore.facade.StockCoreFacade;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiResponse;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;

import javax.annotation.Resource;
import java.util.List;

/**
 * @Description: 退货入库
 * <p>
 * @Author: chuwenchao  2019/11/22
 */
@Slf4j
@RomeController
@RequestMapping("/stock/v1/predictReturn")
@Api(tags = {"退货入库"})
public class PredictReturnController {

    @Resource
    private PredictReturnService predictReturnService;
    @Resource
    private StockCoreFacade stockCoreFacade;

    @ApiOperation(value = "通过DTO查询退货预入库列表", produces = MediaType.APPLICATION_JSON_VALUE)
    @ApiResponse(code = 200, message = "success")
    @RequestMapping(value = "/queryPageListByDTO", method = RequestMethod.POST)
    public Response<PageInfo<PredictReturnDTO>> queryPageListByDTO(@RequestBody PredictReturnDTO predictReturnDTO) {
        try {
            PageInfo<PredictReturnDTO> predictReturnDTOS = predictReturnService.queryPageListByDTO(predictReturnDTO);
            return Response.builderSuccess(predictReturnDTOS);
        } catch (RomeException e) {
            log.error(e.getMessage(), e);
            return Response.builderFail(e.getCode(),e.getMessage());
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return Response.builderFail(ResCode.STOCK_ERROR_1003_DESC,ResCode.STOCK_ERROR_1003_DESC);
        }
    }

    @ApiOperation(value = "通过DTO查询退货预入库列表", produces = MediaType.APPLICATION_JSON_VALUE)
    @ApiResponse(code = 200, message = "success")
    @RequestMapping(value = "/queryDetailListByCode", method = RequestMethod.POST)
    public Response<PageInfo<PredictReturnDetailDTO>> queryDetailListByCode(@RequestParam("recordCode") String recordCode) {
        try {
            List<PredictReturnDetailDTO> detailDTOS = predictReturnService.queryDetailListByCode(recordCode);
            return Response.builderSuccess(detailDTOS);
        } catch (RomeException e) {
            log.error(e.getMessage(), e);
            return Response.builderFail(e.getCode(),e.getMessage());
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return Response.builderFail(ResCode.STOCK_ERROR_1003_DESC,ResCode.STOCK_ERROR_1003_DESC);
        }
    }

    @ApiOperation(value = "POP商城逆向退货查询待处理数据", produces = MediaType.APPLICATION_JSON_VALUE)
    @ApiResponse(code = 200, message = "success")
    @RequestMapping(value = "/queryNeedHandlePredictReturnData", method = RequestMethod.POST)
    public Response<List<PredictReturnDTO>> queryNeedHandlePredictReturnData(@RequestParam("handleNum") Integer handleNum) {
        try {
            List<PredictReturnDTO> predictReturnDTOS = predictReturnService.queryNeedHandlePredictReturnData(handleNum);
            return Response.builderSuccess(predictReturnDTOS);
        } catch (RomeException e) {
            log.error(e.getMessage(), e);
            return Response.builderFail(e.getCode(),e.getMessage());
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return Response.builderFail(ResCode.STOCK_ERROR_1003_DESC,ResCode.STOCK_ERROR_1003_DESC);
        }
    }

    @ApiOperation(value = "POP商城逆向退货定时处理库存", produces = MediaType.APPLICATION_JSON_VALUE)
    @ApiResponse(code = 200, message = "success")
    @RequestMapping(value = "/handlePredictReturnNeedJob", method = RequestMethod.POST)
    public Response handlePredictReturnNeedJob(@RequestBody PredictReturnDTO predictReturnDTO) {
        try {
            return stockCoreFacade.handlePredictReturnNeedJob(predictReturnDTO);
        } catch (RomeException e) {
            log.error(e.getMessage(), e);
            return Response.builderFail(e.getCode(),e.getMessage());
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return Response.builderFail(ResCode.STOCK_ERROR_1003_DESC,ResCode.STOCK_ERROR_1003_DESC);
        }
    }

    @ApiOperation(value = "处理失败，更新失败时间", produces = MediaType.APPLICATION_JSON_VALUE)
    @ApiResponse(code = 200, message = "success")
    @RequestMapping(value = "/updateNeedJobHandleFail", method = RequestMethod.POST)
    public Response<Integer> updateNeedJobHandleFail(@RequestParam("recordCode") String recordCode) {
        try {
            return Response.builderSuccess(predictReturnService.updateNeedJobHandleFail(recordCode));
        } catch (RomeException e) {
            log.error(e.getMessage(), e);
            return Response.builderFail(e.getCode(),e.getMessage());
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return Response.builderFail(ResCode.STOCK_ERROR_1003_DESC,ResCode.STOCK_ERROR_1003_DESC);
        }
    }
}
