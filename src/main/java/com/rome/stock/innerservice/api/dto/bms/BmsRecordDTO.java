package com.rome.stock.innerservice.api.dto.bms;

import com.rome.stock.innerservice.domain.entity.warehouserecord.WarehouseRecordE;
import lombok.Data;
import lombok.EqualsAndHashCode;

@Data
@EqualsAndHashCode
public class BmsRecordDTO {

    private WarehouseRecordE warehouseRecordE;

    private BmsDTO bmsDTO;

    public static BmsRecordDTO init(WarehouseRecordE recordE){
        BmsRecordDTO recordDTO =new BmsRecordDTO();
        recordDTO.setWarehouseRecordE(recordE);
        return recordDTO;
    }
}
