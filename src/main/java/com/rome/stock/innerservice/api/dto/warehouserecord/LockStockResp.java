package com.rome.stock.innerservice.api.dto.warehouserecord;

import java.io.Serializable;
import java.util.List;

import com.rome.stock.core.infrastructure.dataobject.core.CoreRealStockOpDO;
import lombok.Data;

/**
 * @Description: 锁库响应结果
 * <p>
 * @Author: wwh 2020/8/12
 */
@Data
public class LockStockResp implements Serializable {
	
	/**
	 * DO单号
	 */
	private String recordCode;
	
	/**
	 * 单据类型
	 */
	private Integer recordType;
	
	/**
	 * 锁库明细响应结果
	 */
	List<LockStockDetailResp> detailList;

	private CoreRealStockOpDO coreRealStockOpDO;

}