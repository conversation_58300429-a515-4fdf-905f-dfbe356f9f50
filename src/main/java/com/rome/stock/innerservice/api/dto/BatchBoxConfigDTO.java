package com.rome.stock.innerservice.api.dto;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import java.util.Date;

/**
 * 批次原箱配置表
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
@EqualsAndHashCode
public class BatchBoxConfigDTO extends Pagination {

    private Long id;
    /**
     * 仓库id
     */
    private Long realWarehouseId;

    /**
     * 仓库名称
     */
    private String realWarehouseName;


    /**
     * 仓库编码
     */
    private String realWarehouseCode;

    /**
     * 渠道编码集合
     */
    private String channelCodes;

    /**
     * 出库策略
     */
    private Integer boxStatus;

    /**
     * 创建时间
     */
    private Date createTime;
    /**
     * 修改时间
     */
    private Date updateTime;
    /**
     * 创建人
     */
    private Long creator;
    /**
     * 更新人
     */
    private Long modifier;
    /**
     * 是否可用：0-否，1-是
     */
    private Byte isAvailable;
    /**
     * 是否逻辑删除：0-否，1-是
     */
    private Byte isDeleted;
    /**
     * 是否强制开启批次：0-否，1-是
     */
    private boolean force;
}
