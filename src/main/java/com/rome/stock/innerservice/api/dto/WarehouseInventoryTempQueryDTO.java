package com.rome.stock.innerservice.api.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

@ApiModel(value = "WarehouseInventoryStartQueryDTO", description = "盘点单管理查询DTO")
@Data
@EqualsAndHashCode
public class WarehouseInventoryTempQueryDTO extends Pagination {

    private static final long serialVersionUID = 757840252883746949L;
    @ApiModelProperty(value = "盘点记录号列表")
    private List<Long> inventoryIds;

    @ApiModelProperty(value = "wms盘点单号")
    private String outRecordCode;

    /**
     * 商品编码
     */
    private String skuCode;
}
