package com.rome.stock.innerservice.api.controller;

import com.alibaba.fastjson.JSON;
import com.github.pagehelper.PageInfo;
import com.rome.arch.core.clientobject.Response;
import com.rome.arch.core.exception.RomeException;
import com.rome.arch.util.controller.RomeController;
import com.rome.stock.innerservice.api.dto.*;
import com.rome.stock.innerservice.api.dto.frontrecord.*;
import com.rome.stock.innerservice.common.CoreKibanaLog;
import com.rome.stock.innerservice.common.RedisUtil;
import com.rome.stock.innerservice.common.ResCode;
import com.rome.stock.innerservice.constant.ResponseMsg;
import com.rome.stock.innerservice.constant.WDTRecordConst;
import com.rome.stock.innerservice.domain.convertor.frontrecord.OrderAddressConvertor;
import com.rome.stock.innerservice.domain.repository.SapInterfaceLogRepository;
import com.rome.stock.innerservice.domain.service.FulfillmentJobService;
import com.rome.stock.innerservice.domain.service.WDTStockService;
import com.rome.stock.innerservice.domain.service.WDTSyncHandleService;
import com.rome.stock.wms.config.BaseinfoConfiguration;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import io.swagger.annotations.ApiResponse;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.http.MediaType;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

/**
 * 电商相关服务(外接交易中心)
 */
@Slf4j
@RomeController
@RequestMapping("/wdtStock/v1")
@Api(tags={"旺店通电商服务接口"})
public class WDTStockController {

    @Resource
    private WDTStockService wdtStockService;
    @Resource
    private SapInterfaceLogRepository sapInterfaceLogRepository;
    @Resource
    private FulfillmentJobService fulfillmentJobService;

    private final static String LOGTYPE = "wdtCall";
    @Resource
    private RedisUtil redisUtil;
    @Resource
    private WDTSyncHandleService wdtSyncHandleService;
    @Resource
    private OrderAddressConvertor orderAddressConvertor;

    /**
     * 电商下单锁定库存
     * @param stockOrderDTO 电商下单相关参数
     * @return
     */
    @ApiOperation(value = "下单锁定库存", nickname = "lockStockByRecord", produces = MediaType.APPLICATION_JSON_VALUE)
    @ApiResponse(code = 200, message = "success", response = String.class)
    @PostMapping("/lockStockByRecord")
    public Response<RealWarehouse> lockStockByRecord(@ApiParam(name = "stockOrderDTO", value = "下单锁定库存") @RequestBody @Validated StockOrderDTO stockOrderDTO) {
        String message = "";
        boolean isSucc = false;
        String json = JSON.toJSONString(stockOrderDTO);

        log.warn(CoreKibanaLog.getJobLog(LOGTYPE, "lockStockByRecord", "下单锁定库存, 订单号:" + stockOrderDTO.getOrderCode(), stockOrderDTO.getOrderCode()));
        try {
            //如果原始单号为空，将交易号直接替代
            if (StringUtils.isBlank(stockOrderDTO.getOriginOrderCode())) {
                stockOrderDTO.setOriginOrderCode(stockOrderDTO.getOrderCode());
            }
            RealWarehouse warehouse = wdtStockService.lockStockByRecord(stockOrderDTO);
            isSucc = true;
            message = "200";
            return ResponseMsg.SUCCESS.buildMsg(warehouse);
        } catch (RomeException e) {
            message = e.getMessage();
            log.error(e.getMessage(), e);
            return ResponseMsg.FAIL.buildMsg(e.getCode(), e.getMessage());
        } catch (Exception e) {
            message = e.getMessage();
            log.error(e.getMessage(), e);
            return ResponseMsg.EXCEPTION.buildMsg();
        } finally {
            sapInterfaceLogRepository.saveCallBackInterFaceLog(2, stockOrderDTO.getOrderCode(), "wdtLockStockByRecord",
                    json, message == null ? "" : message, isSucc);
        }
    }

    /**
     * 保存物流信息到do单
     * @param paramDTO SO单号
     * @return
     */
    @ApiOperation(value = "保存物流信息到so单.", nickname = "saveLogisticInfo", produces = MediaType.APPLICATION_JSON_VALUE)
    @ApiResponse(code = 200, message = "success", response = String.class)
    @PostMapping("/saveLogisticInfo")
    public Response<Boolean> saveLogisticInfo(@ApiParam(name = "paramDTO", value = "paramDTO") @RequestBody  List<WDTStockLogisticInfoParamDTO> paramDTO) {
        String message = "";
        boolean isSucc = false;
        try {
            wdtStockService.saveLogisticInfo(paramDTO);
            isSucc = true;
            message = "200";
            return ResponseMsg.SUCCESS.buildMsg(true);
        } catch (RomeException e) {
            message = e.getMessage();
            log.error(e.getMessage(), e);
            return ResponseMsg.FAIL.buildMsg(e.getCode(), e.getMessage());
        } catch (Exception e) {
            message = e.getMessage();
            log.error(e.getMessage(), e);
            return ResponseMsg.EXCEPTION.buildMsg();
        } finally {
            for (WDTStockLogisticInfoParamDTO dto : paramDTO) {
                sapInterfaceLogRepository.saveCallBackInterFaceLog(2, dto.getSoCode(), "wdtSaveLogisticInfo",
                        JSON.toJSONString(dto), message == null ? "" : message, isSucc);
            }
        }
    }


    @ApiOperation(value = "同步拆单do给捋单", nickname = "doSplitNotifyJob", produces = MediaType.APPLICATION_JSON_VALUE)
    @ApiResponse(code = 200, message = "success", response = String.class)
    @PostMapping("/doSplitNotifyJob")
    public Response<Boolean> doSplitNotifyJob() {
        try {
            fulfillmentJobService.doSplitNotifyJob();
            return ResponseMsg.SUCCESS.buildMsg(true);
        } catch (RomeException e) {
            return ResponseMsg.FAIL.buildMsg(e.getCode(), e.getMessage());
        } catch (Exception e) {
            return ResponseMsg.EXCEPTION.buildMsg();
        }
    }


    @ApiOperation(value = "根据子do单号取消do单", nickname = "cancelChildDO", produces = MediaType.APPLICATION_JSON_VALUE)
    @ApiResponse(code = 200, message = "success", response = String.class)
    @PostMapping("/cancelChildDo")
    public Response<Boolean> cancelChildDo(@ApiParam(name = "doCode", value = "doCode") @RequestParam String doCode) {
        String message = "";
        boolean isSucc = false;
        try {
            wdtStockService.cancelChildDo(doCode);
            isSucc = true;
            message = "200";
            return ResponseMsg.SUCCESS.buildMsg(true);
        } catch (RomeException e) {
            message = e.getMessage();
            log.error(e.getMessage(), e);
            return ResponseMsg.FAIL.buildMsg(e.getCode(), e.getMessage());
        } catch (Exception e) {
            message = e.getMessage();
            log.error(e.getMessage(), e);
            return ResponseMsg.EXCEPTION.buildMsg();
        } finally {
            sapInterfaceLogRepository.saveCallBackInterFaceLog(2, doCode, "wdtCancelChildDO",
                    doCode, message == null ? "" : message, isSucc);
        }
    }

    /**
     * 修改DO单
     * @param paramDTO 电商修改发货单相关参数
     * @return
     */
    @ApiOperation(value = "修改子DO单信息[地址、数量]", nickname = "updateChildDo", produces = MediaType.APPLICATION_JSON_VALUE)
    @ApiResponse(code = 200, message = "success", response = String.class)
    @PostMapping("/updateChildDo")
    public Response<Boolean> updateChildDo(@ApiParam(name = "paramDTO", value = "paramDTO") @RequestBody UpdateDoOrderDTO paramDTO) {
        String message = "";
        boolean isSucc = false;
        String json = JSON.toJSONString(paramDTO);
        try {
            UpdateDoOrderInfoDTO dto=orderAddressConvertor.dto2dto(paramDTO);
            wdtStockService.updateChildDo(dto);
            isSucc = true;
            message = "200";
            return ResponseMsg.SUCCESS.buildMsg(true);
        } catch (RomeException e) {
            message = e.getMessage();
            log.error(e.getMessage(), e);
            return ResponseMsg.FAIL.buildMsg(e.getCode(), e.getMessage());
        } catch (Exception e) {
            message = e.getMessage();
            log.error(e.getMessage(), e);
            return ResponseMsg.EXCEPTION.buildMsg();
        } finally {
            sapInterfaceLogRepository.saveCallBackInterFaceLog(2, paramDTO.getDoCode(), "wdtUpdateChildDo",
                    json, message == null ? "" : message, isSucc);
        }
    }

    /**
     * 修改DO单
     * @param paramDTO 电商修改发货单相关参数
     * @return
     */
    @ApiOperation(value = "修改so地址,拆单之前才允许修改", nickname = "updateAddressInfoBySoCode", produces = MediaType.APPLICATION_JSON_VALUE)
    @ApiResponse(code = 200, message = "success", response = String.class)
    @PostMapping("/updateAddressInfoBySoCode")
    public Response<Boolean> updateAddressInfoBySoCode(@ApiParam(name = "paramDTO", value = "paramDTO") @RequestBody StockOrderRecordDTO paramDTO) {
        String message = "";
        boolean isSucc = false;
        String json = JSON.toJSONString(paramDTO);
        try {
            StockOrderRecordInfoDTO dto=orderAddressConvertor.dto2dto(paramDTO);
            wdtStockService.updateAddressInfoBySoCode(dto);
            isSucc = true;
            message = "200";
            return ResponseMsg.SUCCESS.buildMsg(true);
        } catch (RomeException e) {
            message = e.getMessage();
            log.error(e.getMessage(), e);
            return ResponseMsg.FAIL.buildMsg(e.getCode(), e.getMessage());
        } catch (Exception e) {
            message = e.getMessage();
            log.error(e.getMessage(), e);
            return ResponseMsg.EXCEPTION.buildMsg();
        } finally {
            sapInterfaceLogRepository.saveCallBackInterFaceLog(2, paramDTO.getOrderCode(), "wdtUpdateAddressInfoBySoCode",
                    json, message == null ? "" : message, isSucc);
        }
    }

    @ApiOperation(value = "根据订单号取消so单", nickname = "cancelOrder", produces = MediaType.APPLICATION_JSON_VALUE)
    @ApiResponse(code = 200, message = "success", response = String.class)
    @PostMapping("/cancelOrder")
    public Response<Boolean> cancelOrder(@ApiParam(name = "soCode", value = "soCode") @RequestParam String soCode) {
        String message = "";
        boolean isSucc = false;
        try {
            wdtStockService.cancelOrder(soCode);
            isSucc = true;
            message = "200";
            return ResponseMsg.SUCCESS.buildMsg(true);
        } catch (RomeException e) {
            message = e.getMessage();
            log.error(e.getMessage(), e);
            return ResponseMsg.FAIL.buildMsg(e.getCode(), e.getMessage());
        } catch (Exception e) {
            message = e.getMessage();
            log.error(e.getMessage(), e);
            return ResponseMsg.EXCEPTION.buildMsg();
        } finally {
            sapInterfaceLogRepository.saveCallBackInterFaceLog(2, soCode, "wdtCancelOrder",
                    soCode, message == null ? "" : message, isSucc);
        }
    }

    @ApiOperation(value = "根据订单号取消so明细", nickname = "updateSoOrderInfo", produces = MediaType.APPLICATION_JSON_VALUE)
    @ApiResponse(code = 200, message = "success", response = String.class)
    @PostMapping("/updateSoOrderInfo")
    public Response<Boolean> updateSoOrderInfo(@ApiParam(name = "paramDTO", value = "paramDTO") @RequestBody UpdateSoOrderDTO paramDTO ) {
        String message = "";
        boolean isSucc = false;
        try {
            wdtStockService.updateSoOrderInfo(paramDTO);
            isSucc = true;
            message = "200";
            return ResponseMsg.SUCCESS.buildMsg(true);
        } catch (RomeException e) {
            message = e.getMessage();
            log.error(e.getMessage(), e);
            return ResponseMsg.FAIL.buildMsg(e.getCode(), e.getMessage());
        } catch (Exception e) {
            message = e.getMessage();
            log.error(e.getMessage(), e);
            return ResponseMsg.EXCEPTION.buildMsg();
        } finally {
            sapInterfaceLogRepository.saveCallBackInterFaceLog(2, paramDTO.getSoCode(), "wdtUpdateSoOrderInfo",
                    JSON.toJSONString(paramDTO), message == null ? "" : message, isSucc);
        }
    }


    /**
     * 旺店通拆单----查询
     * @param wdtPageParamDTO
     * @return
     */
    @ApiOperation(value = "拆单页面", nickname = "queryForSplitPage", produces = MediaType.APPLICATION_JSON_VALUE)
    @ApiResponse(code = 200, message = "success")
    @RequestMapping(value = "/queryForSplitPage", method = RequestMethod.POST)
    public Response<PageInfo> queryForSplitPage(@ApiParam(name = "wdtPageParamDTO", value = "dto") @RequestBody WDTPageParamDTO wdtPageParamDTO) {
        try {
            return Response.builderSuccess(wdtStockService.queryWDTSaleForSplitPage(wdtPageParamDTO));
        } catch (RomeException e) {
            log.error(e.getMessage(), e);
            return Response.builderFail(e.getCode(), e.getMessage());
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return Response.builderFail(ResCode.STOCK_ERROR_1003_DESC, e.getMessage());
        }
    }
    /**
     * 旺店通拆单----导出查询
     * @param wdtPageParamDTO
     * @return
     */
    @ApiOperation(value = "拆单页面-导出明细查询", nickname = "exportForSplitPage", produces = MediaType.APPLICATION_JSON_VALUE)
    @ApiResponse(code = 200, message = "success")
    @RequestMapping(value = "/exportList", method = RequestMethod.POST)
    public Response<List<WDTStockSplitExportDetailTemplate>> exportForSplitPage(@ApiParam(name = "wdtPageParamDTO", value = "dto") @RequestBody WDTPageParamDTO wdtPageParamDTO) {
        try {
            List<WDTStockSplitExportDetailTemplate> dtoList = wdtStockService.exportWDTSaleForSplitPage(wdtPageParamDTO);
            return Response.builderSuccess(dtoList);
        } catch (RomeException e) {
            log.error(e.getMessage(), e);
            return Response.builderFail(e.getCode(), e.getMessage());
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return Response.builderFail(ResCode.STOCK_ERROR_1003_DESC, e.getMessage());
        }
    }
    @ApiOperation(value = "拆单页面明细", nickname = "querySplitDetail", produces = MediaType.APPLICATION_JSON_VALUE)
    @ApiResponse(code = 200, message = "success")
    @RequestMapping(value = "/querySplitDetail", method = RequestMethod.POST)
    public Response<WDTPageInfoDTO > querySplitDetail(@ApiParam(name = "wdtPageInfoDTO", value = "dto") @RequestBody WDTPageInfoDTO wdtPageInfoDTO) {
        try {
            return Response.builderSuccess(wdtStockService.querySplitDetail(wdtPageInfoDTO));
        } catch (RomeException e) {
            log.error(e.getMessage(), e);
            return Response.builderFail(e.getCode(), e.getMessage());
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return Response.builderFail(ResCode.STOCK_ERROR_1003_DESC, ResCode.STOCK_ERROR_1003_DESC);
        }
    }

    @ApiOperation(value = "拆单", nickname = "splitOrder", produces = MediaType.APPLICATION_JSON_VALUE)
    @ApiResponse(code = 200, message = "success")
    @RequestMapping(value = "/splitOrder", method = RequestMethod.POST)
    public Response<List<String>> querySplitDetail(@ApiParam(name = "wdtPageInfoDTO", value = "dto") @RequestBody List<WDTPageInfoDTO> list) {
        this.tocSwitch(list.get(0).getIsForce());
        List<String> msg = new ArrayList<>();
        boolean isLock = redisUtil.lock(WDTRecordConst.WDT_ONLINE_OPERATE_LOCK, WDTRecordConst.CLIENT_ID, WDTRecordConst.WDT_ONLINE_OPERATE_LOCK_TIME);
        if (!isLock) {
            msg.add("当前有[拆单/重新计算仓库/改成改物流]任务在进行中...请稍后再试 ");
        } else {
            msg = wdtSyncHandleService.handleOperate(list, 1);
        }
        if(CollectionUtils.isNotEmpty(msg)){
            return Response.builderFail(ResCode.STOCK_ERROR_1001, msg.toArray());
        }else{
            return ResponseMsg.SUCCESS.buildMsg(msg);
        }
    }


    @ApiOperation(value = "拆单", nickname = "splitOrder", produces = MediaType.APPLICATION_JSON_VALUE)
    @ApiResponse(code = 200, message = "success")
    @RequestMapping(value = "/splitOrderFoxMax", method = RequestMethod.POST)
    public Response splitOrderFoxMax(@ApiParam(name = "splitOrderFoxMax", value = "dto") @RequestBody WDTPageInfoDTO list) {
        this.tocSwitch(list.getIsForce());
        String msg = null;
        boolean isLock = redisUtil.lock(WDTRecordConst.WDT_ONLINE_OPERATE_LOCK, WDTRecordConst.CLIENT_ID, WDTRecordConst.WDT_ONLINE_OPERATE_LOCK_TIME);
        if (!isLock) {
            msg = "当前有[拆单/重新计算仓库/改成改物流]任务在进行中...请稍后再试 ";
        } else {
            try {
                wdtStockService.splitOrderFoxMax(list);
            } catch (Exception e) {
               throw  e;
            } finally {
                redisUtil.unLock(WDTRecordConst.WDT_ONLINE_OPERATE_LOCK, WDTRecordConst.CLIENT_ID);
            }
        }
        return Response.builderSuccess(msg);

    }

    @ApiOperation(value = "重新计算仓库", nickname = "recalculateHouse", produces = MediaType.APPLICATION_JSON_VALUE)
    @ApiResponse(code = 200, message = "success")
    @RequestMapping(value = "/recalculateHouse", method = RequestMethod.POST)
    public Response<List<String>> recalculateHouse(@ApiParam(name = "wdtPageInfoDTO", value = "dto") @RequestBody List<WDTPageInfoDTO> list) {
        this.tocSwitch(list.get(0).getIsForce());
        List<String> msg = new ArrayList<>();
        boolean isLock = redisUtil.lock(WDTRecordConst.WDT_ONLINE_OPERATE_LOCK, WDTRecordConst.CLIENT_ID, WDTRecordConst.WDT_ONLINE_OPERATE_LOCK_TIME);
        if (!isLock) {
            msg.add("当前有[拆单/重新计算仓库/改成改物流]任务在进行中...请稍后再试 ");
        } else {
            msg = wdtSyncHandleService.handleOperate(list, 2);
        }
        if(CollectionUtils.isNotEmpty(msg)){
            return Response.builderFail(ResCode.STOCK_ERROR_1001, msg.toArray());
        }else{
            return ResponseMsg.SUCCESS.buildMsg(msg);
        }
    }

    @ApiOperation(value = "修改仓库和物流", nickname = "changeHouseAndLogistic", produces = MediaType.APPLICATION_JSON_VALUE)
    @ApiResponse(code = 200, message = "success")
    @RequestMapping(value = "/changeHouseAndLogistic", method = RequestMethod.POST)
    public Response<List<String>> changeHouseAndLogistic(@ApiParam(name = "wdtPageInfoDTO", value = "dto") @RequestBody List<WDTPageInfoDTO> list) {
        this.tocSwitch(list.get(0).getIsForce());
        List<String> msg = new ArrayList<>();
        boolean isLock = redisUtil.lock(WDTRecordConst.WDT_ONLINE_OPERATE_LOCK, WDTRecordConst.CLIENT_ID, WDTRecordConst.WDT_ONLINE_OPERATE_LOCK_TIME);
        if (!isLock) {
            msg.add("当前有[拆单/重新计算仓库/改成改物流]任务在进行中...请稍后再试 ");
        } else {
            msg = wdtSyncHandleService.handleOperate(list, 3);
        }
        if(CollectionUtils.isNotEmpty(msg)){
            return Response.builderFail(ResCode.STOCK_ERROR_1001, msg.toArray());
        }else{
            return ResponseMsg.SUCCESS.buildMsg(msg);
        }
    }

    @ApiOperation(value = "查询操作日志", nickname = "queryOperateLog", produces = MediaType.APPLICATION_JSON_VALUE)
    @ApiResponse(code = 200, message = "success")
    @RequestMapping(value = "/queryOperateLog", method = RequestMethod.POST)
    public Response<PageInfo> queryOperateLog(@ApiParam(name = "wdtLogPageParamDTO", value = "dto") @RequestBody WDTLogPageParamDTO wdtLogPageParamDTO) {
        try {
            return Response.builderSuccess(wdtStockService.queryOperateLog(wdtLogPageParamDTO));
        } catch (RomeException e) {
            log.error(e.getMessage(), e);
            return Response.builderFail(e.getCode(), e.getMessage());
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return Response.builderFail(ResCode.STOCK_ERROR_1003_DESC, ResCode.STOCK_ERROR_1003_DESC);
        }
    }


    @ApiOperation(value = "根据仓库ID获取物流公司", nickname = "getLogisticListByRwId", produces = MediaType.APPLICATION_JSON_VALUE)
    @ApiResponse(code = 200, message = "success")
    @RequestMapping(value = "/getLogisticListByRwId", method = RequestMethod.POST)
    public Response<List<TmsLogisticInfoDTO>> getLogisticListByRwId(@RequestParam("realWarehouseId") Long  realWarehouseId) {
        try {
            List<TmsLogisticInfoDTO> list = wdtStockService.getLogisticListByRwId(realWarehouseId);
            return Response.builderSuccess(list);
        }  catch (RomeException e) {
            log.error(e.getMessage(), e);
            return Response.builderFail(e.getCode(),e.getMessage());
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return ResponseMsg.EXCEPTION.buildMsg();
        }
    }

    @ApiOperation(value = "查询需要重新计算仓库的列表数据", nickname = "getLogisticListByRwId", produces = MediaType.APPLICATION_JSON_VALUE)
    @ApiResponse(code = 200, message = "success")
    @RequestMapping(value = "/queryNeedReCalRwList", method = RequestMethod.GET)
    public Response<List<WDTPageInfoDTO>> queryNeedReCalRwList(@RequestParam("minId")Long minId, @RequestParam("limit")Integer limit) {
        try {
            List<WDTPageInfoDTO> list = wdtStockService.queryNeedReCalRwList(minId, limit);
            return Response.builderSuccess(list);
        }  catch (RomeException e) {
            log.error(e.getMessage(), e);
            return Response.builderFail(e.getCode(),e.getMessage());
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return ResponseMsg.EXCEPTION.buildMsg();
        }
    }

    private void tocSwitch(Integer isForce){
        if(Objects.equals(isForce,1)){
            //强制使用，不拦截
            return;
        }
        String tocSwitch = BaseinfoConfiguration.getInstance().get("toc_switch", "toc_switch");
        if(Objects.equals(tocSwitch,"1")){
            throw new RomeException(ResCode.STOCK_ERROR_1017,"TOC接口已迁移至订单中心");
        }
    }


}
