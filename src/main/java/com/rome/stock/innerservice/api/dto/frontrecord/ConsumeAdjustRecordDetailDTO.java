package com.rome.stock.innerservice.api.dto.frontrecord;

import com.rome.stock.innerservice.api.dto.allocation.WhSkuUnitDTO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

import javax.validation.constraints.NotEmpty;

/**
 * @Description 仓库损耗调整单明细
 * <AUTHOR>
 * @Date 2019/5/11 9:44
 * @Version 1.0
 */
@Data
@EqualsAndHashCode
public class ConsumeAdjustRecordDetailDTO {

    @ApiModelProperty(value = "唯一主键")
    private Long id;

    @ApiModelProperty(value = "调整单id")
    private Long frontRecordId;

    @ApiModelProperty(value = "调整单编号")
    private String recordCode;

    @ApiModelProperty(value = "商品sku编号")
    private Long skuId;

    @ApiModelProperty(value = "商品sku编号")
    private String skuCode;

    @ApiModelProperty(value = "商品sku名称")
    private String skuName;

    @ApiModelProperty(value = "调整单位")
    private String unit;

    @ApiModelProperty(value = "单位编码")
    @NotEmpty(message="单位code不能为空")
    private String unitCode;

    @ApiModelProperty(value = "商品总数")
    private BigDecimal skuQty;

    @ApiModelProperty(value = "实际出库数量")
    private BigDecimal actualQty;

    @ApiModelProperty(value = "批次备注")
    private String remark;

    @ApiModelProperty(value = "创建时间")
    private Date createTime;

    @ApiModelProperty(value = "更新时间")
    private Date updateTime;

    @ApiModelProperty(value = "规格")
    private String skuStandard;

    @ApiModelProperty(value = "换算比例")
    private BigDecimal scale;

    @ApiModelProperty(value = "基本单位数量")
    private BigDecimal basicQty;

    @ApiModelProperty(value = "基本单位")
    private String basicUnit;

    @ApiModelProperty(value = "剩余数量")
    private BigDecimal remainStockQty;

    @ApiModelProperty(value = "sku的单位信息")
    private List<WhSkuUnitDTO> skuUnitList;
}
