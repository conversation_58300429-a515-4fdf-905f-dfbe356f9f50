package com.rome.stock.innerservice.api.controller;

import com.github.pagehelper.PageInfo;
import com.rome.arch.core.clientobject.Response;
import com.rome.arch.core.exception.RomeException;
import com.rome.arch.util.controller.RomeController;
import com.rome.stock.common.annotation.BathParamsValidate;
import com.rome.stock.innerservice.api.dto.*;
import com.rome.stock.innerservice.common.ResCode;
import com.rome.stock.innerservice.constant.ResponseMsg;
import com.rome.stock.innerservice.domain.service.SafeStockConfigService;
import com.rome.stock.innerservice.remote.base.dto.ChannelTypeDTO;
import com.rome.stock.innerservice.remote.base.facade.ChannelFacade;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiResponse;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

/**
 * @Description SafeStockConfigController
 * <AUTHOR>
 * @Date 2024/7/18
 **/
@Slf4j
@RomeController
@RequestMapping(value = "/stock/safeStockConfig")
@Api(tags={"安全库存 Controller"})
public class SafeStockConfigController {

    @Resource
    private SafeStockConfigService safeStockConfigService;

    @Resource
    private ChannelFacade channelFacade;


    @ApiOperation(value = "根据条件查询", nickname = "queryCondition", produces = MediaType.APPLICATION_JSON_VALUE)
    @ApiResponse(code = 200, message = "success")
    @RequestMapping(value = "/queryCondition", method = RequestMethod.POST)
    public Response<PageInfo<SafeStockConfigDTO>> queryCondition(@RequestBody SafeStockConfigQueryDTO paramDto) {
        try {
            PageInfo<SafeStockConfigDTO> dtoList = safeStockConfigService.queryCondition(paramDto);
            return ResponseMsg.SUCCESS.buildMsg(dtoList);
        } catch (RomeException e) {
            log.error(e.getMessage(), e);
            return ResponseMsg.FAIL.buildMsg(e.getCode(),e.getMessage());
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return ResponseMsg.EXCEPTION.buildMsg(ResCode.STOCK_ERROR_1003, ResCode.STOCK_ERROR_1003_DESC + e.getMessage());
        }
    }

    @ApiOperation(value = "根据channelType和channelCode查询明细数据", nickname = "queryCondition", produces = MediaType.APPLICATION_JSON_VALUE)
    @ApiResponse(code = 200, message = "success")
    @RequestMapping(value = "/queryDetail", method = RequestMethod.POST)
    public Response<SafeStockConfigDTO> queryDetail(@RequestBody SafeStockConfigQueryDTO paramDto) {
        try {
            SafeStockConfigDTO detail = safeStockConfigService.queryDetail(paramDto);
            return ResponseMsg.SUCCESS.buildMsg(detail);
        } catch (RomeException e) {
            log.error(e.getMessage(), e);
            return ResponseMsg.FAIL.buildMsg(e.getCode(),e.getMessage());
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return ResponseMsg.EXCEPTION.buildMsg(ResCode.STOCK_ERROR_1003, ResCode.STOCK_ERROR_1003_DESC + e.getMessage());
        }
    }

    @ApiOperation(value = "保存配置数据", nickname = "saveSafeStockConfig", produces = MediaType.APPLICATION_JSON_VALUE)
    @ApiResponse(code = 200, message = "success")
    @RequestMapping(value = "/saveSafeStockConfig", method = RequestMethod.POST)
    public Response saveSafeStockConfig(@RequestBody SafeStockConfigDTO paramDto) {
        try {
            safeStockConfigService.saveSafeStockConfig(paramDto);
            return ResponseMsg.SUCCESS.buildMsg("success");
        } catch (RomeException e) {
            log.error(e.getMessage(), e);
            return ResponseMsg.FAIL.buildMsg(e.getCode(),e.getMessage());
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return ResponseMsg.EXCEPTION.buildMsg(ResCode.STOCK_ERROR_1003, ResCode.STOCK_ERROR_1003_DESC + e.getMessage());
        }
    }


    @ApiOperation(value = "根据实仓code批量查询鲲鹏渠道code列表(最大个数默认100)", nickname = "selectKpChannelByRealWarehouses", produces = MediaType.APPLICATION_JSON_VALUE)
    @ApiResponse(code = 200, message = "success", response = List.class)
    @RequestMapping(value = "/selectChannelTypeList", method = RequestMethod.POST)
    public Response<List<ChannelTypeDTO>> selectChannelTypeList() {
        try{
            return ResponseMsg.SUCCESS.buildMsg(channelFacade.queryAllChannelTypeList());
        }catch (RomeException ex){
            log.error(ex.getMessage(),ex);
            return Response.builderFail(ex.getCode(), ex.getMessage());
        }catch (Exception ex){
            log.error(ex.getMessage(),ex);
            return ResponseMsg.EXCEPTION.buildMsg();
        }
    }

    @ApiOperation(value = "查询安全库存配置缓存,根据渠道列表", nickname = "queryCache", produces = MediaType.APPLICATION_JSON_VALUE)
    @ApiResponse(code = 200, message = "success", response = List.class)
    @RequestMapping(value = "/queryCache", method = RequestMethod.POST)
    public Response<List<RealWarehouseKpChannelDTO>> queryCache(@RequestBody List<String> channelCodeList) {
        try{
            return ResponseMsg.SUCCESS.buildMsg(safeStockConfigService.queryCache(channelCodeList));
        }catch (RomeException ex){
            log.error(ex.getMessage(),ex);
            return Response.builderFail(ex.getCode(), ex.getMessage());
        }catch (Exception ex){
            log.error(ex.getMessage(),ex);
            return ResponseMsg.EXCEPTION.buildMsg();
        }
    }

}
