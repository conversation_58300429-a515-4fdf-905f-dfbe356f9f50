/**
 * Filename TransactionMessageDTO.java
 * Company 上海来伊份科技有限公司。
 * <AUTHOR>
 * @version 
 */
package com.rome.stock.innerservice.api.dto;


import java.util.Date;
import java.util.List;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 事务消息回查
 * <AUTHOR>
 * @since 2022-1-10 15:32:07
 */
@Data
public class TransactionMessageDTO extends Pagination {
	
	/**
     * 包含count查询
     */
	@ApiModelProperty(value="是否统计条数")
    private boolean count = true;
	
	@ApiModelProperty(value="查询排序，是按日期降序")
	private boolean sortDesc = true;

	@ApiModelProperty(value="主键")
	private Long id;
	
	@ApiModelProperty(value="topic")
	private String topic;
	
	@ApiModelProperty(value="中台唯一单号")
	private String keyCode;
	
	@ApiModelProperty(value="消息内容")
	private String msg;
	
	@ApiModelProperty(value="单据编码")
	private String recordCode;
	
	@ApiModelProperty(value="发送消息是否成功1成功 0失败")
	private Integer sendStatus;
	
	@ApiModelProperty(value="消费状态0 未消费 1成功消费")
	private Integer status;
	
	@ApiModelProperty(value="重试次数")
	private Integer tryTimes;
	
	@ApiModelProperty(value="错误信息")
	private String errorMsg;
	
	@ApiModelProperty(value="创建时间")
    private Date createTime;
	
	@ApiModelProperty(value="更新时间")
    private Date  updateTime;
	
	@ApiModelProperty(value="开始时间")
    private Date startTime;
	
	@ApiModelProperty(value="结束时间")
    private Date endTime;
	
	@ApiModelProperty(value=" 单据编码列表")
	private List<String> recordCodeList;
	
	@ApiModelProperty(value="中台唯一单号列表")
	private List<String> keyCodeList;
	
	@ApiModelProperty(value="创建人")
    private Long creator;
	
	@ApiModelProperty(value="更新人")
	private Long modifier;
	
	@ApiModelProperty(value="更新人工号")
	private String modifyEmpNum;

	@ApiModelProperty(value="单据类型集合")
	private List<Integer> recordTypeList;

	@ApiModelProperty(value="单据类型")
	private Integer recordType;

	@ApiModelProperty(value="实仓ID")
	private Long realWarehouseId;

	@ApiModelProperty(value="实仓编号")
	private String realWarehouseCode;

	@ApiModelProperty(value="单据类型名称")
	private String recordTypeName;

	@ApiModelProperty(value="实仓编号（多个）")
	private String realWarehouseCodes;

	@ApiModelProperty(value="实仓ID集合")
	private List<Long> realWarehouseIdList;

	@ApiModelProperty(value="实仓Code集合")
	private List<String> realWarehouseCodeList;
}
