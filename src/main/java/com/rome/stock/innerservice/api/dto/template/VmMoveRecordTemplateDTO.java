package com.rome.stock.innerservice.api.dto.template;

import lombok.Data;

import java.io.Serializable;

/**
 * @Description: 虚仓转移导入模板
 * <p>
 * @Author: chuwenchao  2019/12/10
 */
@Data
public class VmMoveRecordTemplateDTO implements Serializable {

    /**
     * 转移单类型
     */
    private String recordTypeStr;

    /**
     * 实仓编号
     */
    private String realWarehouseCode;

    /**
     * 转入虚仓编号
     */
    private String inVirtualWarehouseCode;

    /**
     * 转出虚仓编号
     */
    private String outVirtualWarehouseCode;

    /**
     * 备注
     */
    private String remark;

    /**
     * 商品编码
     */
    private String skuCode;

    /**
     * 商品ID
     */
    private Long skuId;

    /**
     * 商品数量
     */
    private String skuQty;

    /**
     * 单位
     */
    private String unitCode;

    /**
     * 单据类型 1:普通 2:预留
     */
    private Integer recordType;

    /**
     * 实仓ID
     */
    private Long realWarehouseId;

    /**
     * 转入虚仓ID
     */
    private Long inVirtualWarehouseId;

    /**
     * 转出虚仓ID
     */
    private Long outVirtualWarehouseId;

    /**
     * 转入虚仓标签
     */
    private String inVirtualWarehouseType;

    /**
     * 转出虚仓标签
     */
    private String outVirtualWarehouseType;
}
