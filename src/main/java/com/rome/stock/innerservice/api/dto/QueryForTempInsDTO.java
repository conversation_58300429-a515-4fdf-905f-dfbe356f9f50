package com.rome.stock.innerservice.api.dto;

import java.util.List;

import com.rome.stock.common.page.Pagination;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 类QueryForTempInsDTO的实现描述：临时性检验查询DTO
 *
 * <AUTHOR> 2020/10/6 15:22
 */
@Data
public class QueryForTempInsDTO  extends Pagination {

    @ApiModelProperty(value = "物料编码")
    private List<String> skuCodes;

    @ApiModelProperty(value = "实仓编码")
    private List<String> realWarehouseCodes;

    @ApiModelProperty(value = "实仓Id")
    private List<Long> realWarehouseIds;

    @ApiModelProperty(value = "库存类型(1:非限制(可用库存) 2.质检库存 3. 冻结库存)")
    private Integer stockType;

    @ApiModelProperty(value = "是否包含0库存(1.包含, 0不包含)")
    private Integer isZeroStock;
}
