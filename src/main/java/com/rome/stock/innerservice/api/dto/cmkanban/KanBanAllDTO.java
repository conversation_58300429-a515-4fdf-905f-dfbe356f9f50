package com.rome.stock.innerservice.api.dto.cmkanban;

import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 类KanBanAllDTO的实现描述：看板云sql
 *
 * <AUTHOR> 2020/7/11 21:38
 */
@Data
@EqualsAndHashCode
public class KanBanAllDTO {
    /**
     * 仓库Id
     */
    private Long realWarehouseId;

    /**
     * 渠道code
     */
    private String channelCode;

    /**
     * 订单类型
     */
    private Integer orderType;

    /**
     * 成交订单数
     */
    private Integer dealOrderNum;

    /**
     * 已接收订单数
     */
    private Integer syncOrderNum;

    /**
     * 未接收订单数
     */
    private Integer unSyncOrderNum;

    /**
     * 已发货订单数
     */
    private Integer deliveryOrderNum;

    /**
     * 已取消订单数
     */
    private Integer cancleOrderNum;

    /**
     * 24小时未发货订单数
     */
    private Integer tfdeliveryOrderNum;

    /**
     * 48小时未发货订单数
     */
    private Integer feDeliveryOrderNum;

}
