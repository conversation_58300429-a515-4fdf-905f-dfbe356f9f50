package com.rome.stock.innerservice.api.dto;

import lombok.Data;

/**
 * @Doc:ChannelSalesWarehousePriorityPageDTO
 * @Author: lchy
 * @Date: 2019/6/3
 * @Version 1.0
 */
@Data
public class ChannelSalesWarehousePriorityPageDTO {

	/**
	 * 实仓id
	 */
	private Long id;
	/**
	 * 渠道实仓优先级表对应的id
	 */
	private Long channelSalesWarehousePriorityId;
	/**
	 * 实仓code
	 */
	private String realWarehouseCode;

	/**
	 * 实仓名称
	 */
	private String realWarehouseName;
	/**
	 * 区县
	 */
	private String realWarehouseCountry;
	/**
	 * 省份
	 */
	private String realWarehouseProvince;
	/**
	 * 城市
	 */
	private String realWarehouseCity;

	/**
	 * 省份code
	 */
	private String realWarehouseProvinceCode;
	/**
	 * 城市code
	 */
	private String realWarehouseCityCode;
	/**
	 * 区县code
	 */
	private String realWarehouseCountyCode;

	/**
	 * 仓库详细地址
	 */
	private String realWarehouseAddress;

	/**
	 * 仓库优先级 1为最高
	 */
	private Integer priority;



	/**
	 * 渠道code
	 */
	private String channelCode;

}
