package com.rome.stock.innerservice.api.dto.frontrecord;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 * @description TODO
 * @date 2020/9/27 19:24
 * @throw
 */
@Data
public class QualityOperateCallBackDetailDTO {
    @ApiModelProperty(value = "入库行号")
    @NotBlank(message = "行号不能为空")
    private String lineNo;

    @ApiModelProperty(value = "记账凭证",hidden = true)
    private Long skuId;

    @NotBlank(message="商品编码不能为空")
    @ApiModelProperty(value = "商品编码")
    private String skuCode;

    @ApiModelProperty(value = "批次号：wms收货时会给的批次号")
    @NotBlank(message="批次号不能为空")
    private String batchCode;

    @ApiModelProperty(value = "库存操作类型 1真实库存 2 冻结库存")
    @NotNull(message = "库存操作类型不能为空")
    private Integer qualityStockType;

}



   