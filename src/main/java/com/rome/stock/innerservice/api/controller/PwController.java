package com.rome.stock.innerservice.api.controller;

import com.rome.arch.core.clientobject.Response;
import com.rome.arch.util.controller.RomeController;
import com.rome.stock.innerservice.api.dto.PwPrintDTO;
import com.rome.stock.innerservice.domain.service.PwService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;

import javax.annotation.Resource;
import java.util.List;

/**
 * 采购入库
 *
 * <AUTHOR>
 * @date 2020/07/01
 */
@Slf4j
@Api(tags = "采购入库接口")
@RequestMapping("/stock/v1/pw")
@RomeController
public class PwController {

    @Resource
    private PwService pwService;

    @ApiOperation("采购入库单打印查询")
    @GetMapping("/print/{purchaseEntryNo}")
    public Response<List<PwPrintDTO>> pwPrint(@PathVariable String purchaseEntryNo) {
        return Response.builderSuccess(pwService.print(purchaseEntryNo));
    }
}
