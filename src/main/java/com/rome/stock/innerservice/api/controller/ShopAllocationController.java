package com.rome.stock.innerservice.api.controller;

import com.alibaba.fastjson.JSON;
import com.rome.arch.core.clientobject.Response;
import com.rome.arch.core.exception.RomeException;
import com.rome.arch.util.controller.RomeController;
import com.rome.stock.innerservice.api.dto.WarehouseRecordPageDTO;
import com.rome.stock.innerservice.api.dto.warehouserecord.*;
import com.rome.stock.innerservice.common.ResCode;
import com.rome.stock.innerservice.constant.ResponseMsg;
import com.rome.stock.innerservice.domain.repository.SapInterfaceLogRepository;
import com.rome.stock.innerservice.domain.service.ShopAllocationService;
import com.rome.stock.innerservice.remote.sap.dto.ZMMTMDQH3010;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiResponse;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.ArrayList;
import java.util.List;

/**
 * 门店调拨
 * <AUTHOR>
 */
@Slf4j
@RomeController
@RequestMapping("/stock/v1/shop_allocation")
@Api(tags={"门店调拨"})
public class ShopAllocationController {
    @Autowired
    private ShopAllocationService shopAllocationService;
    @Autowired
    private SapInterfaceLogRepository sapInterfaceLogRepository;

    @ApiOperation(value = "批量查询待处理的门店调拨单推送cmp", nickname = "ShopAllocationRecord", produces = MediaType.APPLICATION_JSON_VALUE)
    @ApiResponse(code = 200, message = "success", response = Response.class)
    @RequestMapping(value = "/getWaitPushRecordList", method = RequestMethod.POST)
    public Response<List<WarehouseRecordPageDTO>> getWaitPushRecordList(@RequestParam("page") int page, @RequestParam("maxResult") int maxResult) {
        try {
            List<WarehouseRecordPageDTO> pageList = shopAllocationService.getWaitPushRecordList(page, maxResult);
            return Response.builderSuccess(pageList);
        } catch (RomeException e) {
            log.error(e.getMessage(), e);
            return Response.builderFail(e.getCode(),e.getMessage());
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return Response.builderFail(ResCode.STOCK_ERROR_1003_DESC,ResCode.STOCK_ERROR_1003_DESC);
        }
    }

    @ApiOperation(value = "批量处理门店调拨推送cmp", nickname = "ShopAllocationRecord", produces = MediaType.APPLICATION_JSON_VALUE)
    @ApiResponse(code = 200, message = "success", response = Response.class)
    @RequestMapping(value = "/handleShopAllocationRecordsPushCmp", method = RequestMethod.POST)
    public Response<String> handleShopAllocationRecordsPushCmp(@RequestBody WarehouseRecordPageDTO dto) {
        try {
            shopAllocationService.handleShopAllocationRecordsPushCmp(dto);
            return Response.builderSuccess("");
        } catch (RomeException e) {
            log.error(e.getMessage(), e);
            return Response.builderFail(e.getCode(),e.getMessage());
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return Response.builderFail(ResCode.STOCK_ERROR_1003_DESC,ResCode.STOCK_ERROR_1003_DESC);
        }
    }

    @ApiOperation(value = "门店调拨出库", nickname = "createOutRecord", produces = MediaType.APPLICATION_JSON_VALUE)
    @ApiResponse(code = 200, message = "success", response = Response.class)
    @PostMapping("/createOutRecord")
    public Response createOutRecord(@Validated @RequestBody OutWarehouseRecordDTO outWarehouseRecordDTO){
        try{
            shopAllocationService.createOutRecord(outWarehouseRecordDTO);
            return Response.builderSuccess("");
        }catch (RomeException e){
            log.error(e.getMessage(), e);
            return Response.builderFail(e.getCode(),e.getMessage());
        }catch (Exception e){
            log.error(e.getMessage(), e);
            return Response.builderFail(ResCode.STOCK_ERROR_1003_DESC,ResCode.STOCK_ERROR_1003_DESC);
        }
    }

    @ApiOperation(value = "门店调拨入库", nickname = "createInRecord", produces = MediaType.APPLICATION_JSON_VALUE)
    @ApiResponse(code = 200, message = "success", response = Response.class)
    @PostMapping("/createInRecord")
    public Response createInRecord(@Validated @RequestBody InWarehouseRecordDTO inWarehouseRecordDTO){
        try{
            shopAllocationService.createInRecord(inWarehouseRecordDTO);
            return Response.builderSuccess("");
        }catch (RomeException e){
            log.error(e.getMessage(), e);
            return Response.builderFail(e.getCode(),e.getMessage());
        }catch (Exception e){
            log.error(e.getMessage(), e);
            return Response.builderFail(ResCode.STOCK_ERROR_1003_DESC,ResCode.STOCK_ERROR_1003_DESC);
        }
    }

    @ApiOperation(value = "批量取消申请", nickname = "cancelBatch", produces = MediaType.APPLICATION_JSON_VALUE)
    @ApiResponse(code = 200, message = "success")
    @RequestMapping(value = "/cancelBatch", method = RequestMethod.POST)
    public Response cancelBatch(@RequestBody List<CancelRecordDTO> list){
        List<BatchResultDTO> resultList =  new ArrayList<>();
        try {
            if(CollectionUtils.isNotEmpty(list)){
                for (CancelRecordDTO cancelRecordDTO : list) {
                    BatchResultDTO dto = new BatchResultDTO();
                    dto.setRecordCode(cancelRecordDTO.getRecordCode());
                    dto.setStatus(false);
                    String message = "";
                    boolean isSucc = false;
                    try {
                        //门店调拨没有强制取消
                        shopAllocationService.cancelShopAllocation(cancelRecordDTO.getRecordCode());
                        dto.setStatus(true);
                        isSucc = true;
                        message = "200";
                    }catch (Exception e){
                        message=e.getMessage();
                        log.error(e.getMessage(), e);
                    }finally {
                        dto.setMessage(message);
                        resultList.add(dto);
                        sapInterfaceLogRepository.saveCallBackInterFaceLog(1, dto.getRecordCode(), "shopAllocationCancleBatch",
                                JSON.toJSONString(cancelRecordDTO), message, isSucc);
                    }
                }
            }
            return ResponseMsg.SUCCESS.buildMsg(resultList);
        } catch (RomeException e) {
            log.error(e.getMessage(), e);
            return Response.builderFail(e.getCode(),e.getMessage());
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return ResponseMsg.EXCEPTION.buildMsg();
        }
    }





    @ApiOperation(value = "保存直营转加盟过账结果", nickname = "savePostingAccountResult", produces = MediaType.APPLICATION_JSON_VALUE)
    @ApiResponse(code = 200, message = "success")
    @RequestMapping(value = "/savePostingAccountResult", method = RequestMethod.POST)
    public Response selectStorePage(@RequestBody List<ZMMTMDQH3010> items) {

        try {
            log.info("保存直营转加盟过账结果，入参 >>>{}", items);
            shopAllocationService.savePostingAccountResult(items);
            return Response.builderSuccess("success");
        } catch (RomeException e) {
            log.error(e.getMessage(), e);
            return Response.builderFail(e.getCode(), e.getMessage());
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return Response.builderFail(ResCode.STOCK_ERROR_1003, ResCode.STOCK_ERROR_1003_DESC);
        }
    }

    @ApiOperation(value = "查询直营转加盟过账结果[查询的是最后一次过账结果]", nickname = "queryPostResultByRecordCode", produces = MediaType.APPLICATION_JSON_VALUE)
    @ApiResponse(code = 200, message = "success")
    @RequestMapping(value = "/queryPostResultByRecordCode", method = RequestMethod.POST)
    public Response<ZMMTMDQH3010> queryPostResultByRecordCode(@RequestParam String recordCode) {

        try {
            ZMMTMDQH3010 res = shopAllocationService.queryPostResultByRecordCode(recordCode);
            return Response.builderSuccess(res);
        } catch (RomeException e) {
            log.error(e.getMessage(), e);
            return Response.builderFail(e.getCode(), e.getMessage());
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return Response.builderFail(ResCode.STOCK_ERROR_1003, ResCode.STOCK_ERROR_1003_DESC);
        }
    }
    @ApiOperation(value = "手动过账", nickname = "postAccountByRecordCode", produces = MediaType.APPLICATION_JSON_VALUE)
    @ApiResponse(code = 200, message = "success")
    @RequestMapping(value = "/postAccountByRecordCode", method = RequestMethod.POST)
    public Response postAccountByRecordCode(@RequestParam String recordCode) {

        try {
            shopAllocationService.postAccountByRecordCode(recordCode);
            return Response.builderSuccess("success");
        } catch (RomeException e) {
            log.error(e.getMessage(), e);
            return Response.builderFail(e.getCode(), e.getMessage());
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return Response.builderFail(ResCode.STOCK_ERROR_1003, ResCode.STOCK_ERROR_1003_DESC);
        }
    }


    @ApiOperation(value = "查询调拨后的可用库存不足的记录", nickname = "queryAfterOutStock", produces = MediaType.APPLICATION_JSON_VALUE)
    @ApiResponse(code = 200, message = "success")
    @RequestMapping(value = "/queryAfterOutStock", method = RequestMethod.POST)
    public Response queryAfterOutStock(@RequestBody ShopAllocationQueryStockDTO shopAllocationQueryStockDTO) {

        try {
            List<ShopAllocationQueryStockDetailDTO> list = shopAllocationService.queryAfterOutStock(shopAllocationQueryStockDTO);
            return Response.builderSuccess(list);
        } catch (RomeException e) {
            log.error(e.getMessage(), e);
            return Response.builderFail(e.getCode(), e.getMessage());
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return Response.builderFail(ResCode.STOCK_ERROR_1003, ResCode.STOCK_ERROR_1003_DESC);
        }
    }


}
