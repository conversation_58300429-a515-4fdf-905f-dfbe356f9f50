package com.rome.stock.innerservice.api.dto;

import com.rome.stock.innerservice.api.dto.Base;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

@Data
@EqualsAndHashCode
public class SkuGroupChannelStockRelation extends Base {
	/**
	 * 唯一主键
	 */
	@ApiModelProperty(value = "唯一主键")
	private Long id;
	/**
	 * 商品sku编码
	 */
	@ApiModelProperty(value = "商品sku编码")
	private Long skuId;
	/**
	 * 虚拟仓库组id   所属虚拟仓库组
	 */
	@ApiModelProperty(value = "虚拟仓库组id")
	private Long virtualWarehouseGroupId;
	/**
	 * 渠道ID
	 */
	@ApiModelProperty(value = "渠道ID")
	private Long channelId;
	/**
	 * 共享放大比率（百分比），如1-1000区间可选数字
	 */
	@ApiModelProperty(value = "共享放大比率")
	private Integer showRate;
	/**
	 * 关系类型：1-共享，2-独享指定数量
	 */
	@ApiModelProperty(value = "关系类型")
	private Integer relationType;
	/**
	 * 真实库存
	 */
	@ApiModelProperty(value = "真实库存")
	private Long realQty;
	/**
	 * 锁定库存
	 */
	@ApiModelProperty(value = "锁定库存")
	private Long lockQty;
	/**
	 * 商家id
	 */
	@ApiModelProperty(value = "商家id")
	private Long merchantId;
}
