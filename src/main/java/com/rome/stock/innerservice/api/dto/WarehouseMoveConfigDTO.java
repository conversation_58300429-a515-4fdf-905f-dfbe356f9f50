package com.rome.stock.innerservice.api.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.util.Date;
import java.util.Objects;

/**
 * @description 虚仓转移配置信息实体对象
 * <AUTHOR>
 * @date 2020-06-10
 */
@Data
public class WarehouseMoveConfigDTO extends Pagination {
    /**
     * 主键
     */
    @ApiModelProperty(value="主键")
    private Long id;

    /**
     * 虚仓id
     */
    @ApiModelProperty(value="虚仓id")
    private Long virtualWarehouseId;

    /**
     * 实仓id
     */
    @ApiModelProperty(value="实仓id")
    @NotNull(message = "实仓id不能为空")
    private Long realWarehouseId;

    /**
     * 仓库类型 1--实仓 2--虚仓
     */
    @ApiModelProperty(value="仓库类型 1--实仓 2--虚仓")
    @NotNull(message = "仓库类型不能为空")
    private Integer warehouseType;

    /**
     * 实仓编码
     */
    @ApiModelProperty(value="实仓编码")
    private String realWarehouseCode;


    @ApiModelProperty(value="实仓名称")
    private String realWarehouseName;

    private String virtualWarehouseName;

    private String virtualWarehouseCode;

    @ApiModelProperty(value="实仓编码/名称",hidden = true)
    private String codeOrName;

    /**
     * skuId
     */
    @ApiModelProperty(value="skuId")
    private Long skuId;

    /**
     * 是否自动转移 0-- 不自动转移 1-自动转移
     */
    @ApiModelProperty(value="是否自动转移 0-- 不自动转移 1-自动转移 ")
    private Integer isMoved;

    /**
     * 库存安全数量
     */
    @ApiModelProperty(value="库存安全数量")
    private BigDecimal securityQty;

    /**
     * 级别  1--虚仓级别 2-物料级别
     */
    @ApiModelProperty(value="级别  1--虚仓级别 2-物料级别")
    private Integer securityLevel;

    /**
     * 创建时间
     */
    @ApiModelProperty(value="创建时间",hidden = true)
    private Date createTime;

    /**
     * 创建人
     */
    @ApiModelProperty(value="创建人",hidden = true)
    private Long creator;


    @ApiModelProperty(value="状态 1--启用 2--禁用")
    private Integer status;

    private String statusName;

    private String moveName;

    public String getStatusName(){
        return Objects.equals(this.status,1)?"启用":"禁用";
    }

    public String getMoveName(){
        return Objects.equals(this.isMoved,0)?"否":"是";
    }

}