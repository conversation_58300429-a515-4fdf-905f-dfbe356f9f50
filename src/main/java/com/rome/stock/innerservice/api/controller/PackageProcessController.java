package com.rome.stock.innerservice.api.controller;

import com.rome.arch.core.clientobject.Response;
import com.rome.arch.core.exception.RomeException;
import com.rome.arch.util.controller.RomeController;
import com.rome.stock.innerservice.api.dto.warehouserecord.InWarehouseRecordDTO;
import com.rome.stock.innerservice.api.dto.warehouserecord.OutWarehouseRecordDTO;
import com.rome.stock.innerservice.common.ResCode;
import com.rome.stock.innerservice.constant.ResponseMsg;
import com.rome.stock.innerservice.domain.service.PackageProcessService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiResponse;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;

import java.util.List;

/**
 * @Description 包装加工
 */
@Slf4j
@RomeController
@RequestMapping("/stock/v1/package_process")
@Api(tags={"包装加工对外接口"})
public class PackageProcessController {

    @Autowired
    private PackageProcessService packageProcessService;

    @ApiOperation(value = "订单中心通知库存生产出库单", produces = MediaType.APPLICATION_JSON_VALUE)
    @ApiResponse(code = 200, message = "success")
    @RequestMapping(value = "/packageProcessOutRecord", method = RequestMethod.POST)
    public Response packageProcessOutRecord(@RequestBody @Validated OutWarehouseRecordDTO outWarehouseRecordDTO) {
        try {
            packageProcessService.packageProcessOutRecord(outWarehouseRecordDTO);
            return Response.builderSuccess("");
        } catch (RomeException e) {
            log.error(e.getMessage(), e);
            return Response.builderFail(e.getCode(),e.getMessage());
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return Response.builderFail(ResCode.STOCK_ERROR_1003_DESC,ResCode.STOCK_ERROR_1003_DESC+e.getMessage()==null?e.toString():e.getMessage());
        }
    }

    @ApiOperation(value = "订单中心通知库存中心创建入库单", nickname = "warehouseInCreate", produces = MediaType.APPLICATION_JSON_VALUE)
    @ApiResponse(code = 200, message = "success", response = List.class)
    @RequestMapping(value = "/packageProcessInRecord", method = RequestMethod.POST)
    public Response packageProcessInRecord(@RequestBody InWarehouseRecordDTO inWarehouseRecordDTO){
        try{
            packageProcessService.packageProcessInRecord(inWarehouseRecordDTO);
            return ResponseMsg.SUCCESS.buildMsg();
        }catch (RomeException e){
            log.error(e.getMessage(),e);
            return Response.builderFail(e.getCode(),e.getMessage());
        }catch (Exception e){
            log.error(e.getMessage(), e);
            return Response.builderFail(ResCode.STOCK_ERROR_1003,e.getMessage());
        }
    }

}
