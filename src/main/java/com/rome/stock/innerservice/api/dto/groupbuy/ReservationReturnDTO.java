package com.rome.stock.innerservice.api.dto.groupbuy;

import java.util.Date;
import java.util.List;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

import com.github.pagehelper.PageInfo;
import com.rome.arch.core.dto.DTO;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 退货单
 */
@Data
public class ReservationReturnDTO extends DTO {

	/**
     * 主键
     */
    @ApiModelProperty(value="主键",hidden = true)
    private Long id;

    /**
     * 单据类型
     */
    @ApiModelProperty(value="单据类型",hidden = true)
    private Integer recordType;

    /**
     * 单据编号
     */
    @ApiModelProperty(value="单据编号",hidden = true)
    private String recordCode;

    /**
     * 单据状态 默认1
     */
    @ApiModelProperty(value="单据状态 默认1",hidden = true)
    private Integer recordStatus;

    /**
     * 实仓ID
     */
    @ApiModelProperty(value="实仓ID",hidden = true)
    private Long realWarehouseId;

    /**
     * 预约单号
     */
    @ApiModelProperty(value="预约单号")
    @NotNull(message = "预约单号不能为空")
    private String reservationNo;

    /**
     * 工厂编码
     */
    @ApiModelProperty(value="工厂编码")
    @NotBlank(message ="工厂编码不能为空")
    private String factoryCode;

    /**
     * 仓库外部编码
     */
    @ApiModelProperty(value="仓库外部编码")
    @NotBlank(message ="仓库外部编码不能为空")
    private String realWarehouseOutCode;

    /**
     * 售后单号
     */
    @ApiModelProperty(value="售后单号")
    @NotBlank(message = "售后单号不能为空")
    private String outRecordCode;

    /**
     * 销售单号
     */
    @ApiModelProperty(value="销售单号")
    private String saleCode;

    /**
     * 客户名称
     */
    @ApiModelProperty(value="客户名称")
    @NotBlank(message = "客户名称不能为空")
    private String customName;

    /**
     * 客户手机号
     */
    @ApiModelProperty(value="客户手机号")
    @NotBlank(message = "客户手机号不能为空")
    private String customMobile;

    /**
     * 退货原因
     */
    @ApiModelProperty(value="退货原因")
    @NotBlank(message = "退货原因不能为空")
    private String reason;

    /**
     * 快递单号
     */
    @ApiModelProperty(value="快递单号")
    @NotBlank(message = "快递单号不能为空")
    private String expressNo;

    @ApiModelProperty(value="明细集合")
    @NotNull
    private List<ReservationReturnDetailDTO> returnDetails;

    /**
     * 省
     */
    @ApiModelProperty(value = "省", required = true)
    @NotBlank(message ="省不能为空")
    private String province;


    /**
     * 省编码
     */
    @ApiModelProperty(value = "省编码", required = true)
    @NotBlank(message ="省编码不能为空")
    private String provinceCode;

    /**
     * 市
     */
    @ApiModelProperty(value = "市", required = true)
    @NotBlank(message ="市不能为空")
    private String city;

    /**
     * 市编码
     */
    @ApiModelProperty(value = "市编码", required = true)
    @NotBlank(message ="市编码不能为空")
    private String cityCode;

    /**
     * 区县
     */
    @ApiModelProperty(value = "区县", required = true)
    private String county;

    /**
     * 区县编码
     */
    @ApiModelProperty(value = "区县编码", required = true)
    private String countyCode;

    /**
     * 详细地址
     */
    @ApiModelProperty(value = "详细地址", required = true)
    @NotBlank(message ="详细地址不能为空")
    private String address;

    /**
     *收货人姓名
     */
    @ApiModelProperty(value = "收货人姓名", required = true)
    @NotBlank(message ="收货人姓名不能为空")
    private String name;
    
    /**
     * 实仓名称
     */
    @ApiModelProperty(value="实仓名称",hidden = true)
    private String realWarehouseName;
    
    /**
     * 实仓地址
     */
    @ApiModelProperty(value="实仓地址",hidden = true)
    private String realWarehouseAddress;
    
    /**
     * 单据状态描述
     */
    @ApiModelProperty(value="单据状态描述",hidden = true)
    private String recordStatusDesc;
    
    /**
     * 创建时间
     */
    @ApiModelProperty(value="创建时间",hidden = true)
    private Date createTime;

    /**
     * 更新时间
     */
    @ApiModelProperty(value="更新时间",hidden = true)
	private Date updateTime;
    
    /**
     * 更新人
     */
    @ApiModelProperty(value="更新人",hidden = true)
    private Long modifier;
    
    /**
     * 退货单详情-分页
     */
    @ApiModelProperty(value = "退货单详情-分页")
    PageInfo<ReservationReturnDetailDTO> returnDetailPageInfo;
    
}    
