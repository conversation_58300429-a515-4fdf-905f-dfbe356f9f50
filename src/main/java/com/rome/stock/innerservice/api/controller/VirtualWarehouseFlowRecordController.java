package com.rome.stock.innerservice.api.controller;

import com.github.pagehelper.PageInfo;
import com.rome.arch.core.clientobject.Response;
import com.rome.arch.core.exception.RomeException;
import com.rome.arch.util.controller.RomeController;
import com.rome.stock.innerservice.api.dto.VirtualWarehouse;
import com.rome.stock.innerservice.api.dto.VirtualWarehouseFlowRecord;
import com.rome.stock.innerservice.common.ResCode;
import com.rome.stock.innerservice.constant.ResponseMsg;
import com.rome.stock.innerservice.domain.service.VirtualWarehouseFlowRecordService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import io.swagger.annotations.ApiResponse;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;

import javax.annotation.Resource;
import java.util.List;

/**
 * 虚仓流水单
 */
@Slf4j
@RomeController
@RequestMapping("/stock/v1/virtual_warehouse_flow")
@Api(tags={"虚仓流水"})
public class VirtualWarehouseFlowRecordController {
    @Resource
    private VirtualWarehouseFlowRecordService virtualWarehouseFlowRecordService;

    @ApiOperation(value = "根据实仓id查询其下的所有虚仓信息", nickname = "getVirtualWarehouseByRealWarehouseId", produces = MediaType.APPLICATION_JSON_VALUE)
    @ApiResponse(code = 200, message = "success", response = List.class)
    @RequestMapping(value = "/getVirtualWarehouseByRealWarehouseId", method = RequestMethod.GET)
    public Response<List<VirtualWarehouse>> getVirtualWarehouseByRealWarehouseId(@ApiParam(name = "realWarehouseId", value = "实仓id") @RequestParam(required = false, value = "realWarehouseId") Long realWarehouseId) {
        try {
            List<VirtualWarehouse> virtualWarehouses = virtualWarehouseFlowRecordService.getVirtualWarehouseByRealWarehouseId(realWarehouseId);
            return Response.builderSuccess(virtualWarehouses);
        } catch (RomeException e) {
            log.error(e.getMessage(), e);
            return ResponseMsg.EXCEPTION.buildMsg(e.getCode(), e.getMessage());
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return ResponseMsg.EXCEPTION.buildMsg(ResCode.STOCK_ERROR_1003, ResCode.STOCK_ERROR_1003_DESC);
        }
    }

    @ApiOperation(value = "根据虚仓id查找对应实仓信息", nickname = "getRealWarehouseByVirtualWarehouseId", produces = MediaType.APPLICATION_JSON_VALUE)
    @ApiResponse(code = 200, message = "success", response = List.class)
    @RequestMapping(value = "/getRealWarehouseByVirtualWarehouseId", method = RequestMethod.GET)
    public Response<VirtualWarehouseFlowRecord> getRealWarehouseByVirtualWarehouseId(@ApiParam(name = "virtualWarehouseId", value = "虚仓id") @RequestParam(required = false, value = "virtualWarehouseId") Long virtualWarehouseId) {
        try {
            VirtualWarehouseFlowRecord realWarehouse = virtualWarehouseFlowRecordService.getRealWarehouseByVirtualWarehouseId(virtualWarehouseId);
            return Response.builderSuccess(realWarehouse);
        } catch (RomeException e) {
            log.error(e.getMessage(), e);
            return ResponseMsg.EXCEPTION.buildMsg(e.getCode(), e.getMessage());
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return ResponseMsg.EXCEPTION.buildMsg(ResCode.STOCK_ERROR_1003, ResCode.STOCK_ERROR_1003_DESC);
        }
    }
    @ApiOperation(value = "根据查询条件条件查找符合条件的虚仓流水记录", nickname = "getVirtualWarehouseStockChangeFlow", produces = MediaType.APPLICATION_JSON_VALUE)
    @ApiResponse(code = 200, message = "success", response = List.class)
    @RequestMapping(value = "/getVirtualWarehouseStockChangeFlow", method = RequestMethod.POST)
    public Response<PageInfo<List<VirtualWarehouseFlowRecord>>> getVirtualWarehouseStockChangeFlow(@ApiParam(name = "VirtualWarehouseFlowRecord", value = "虚仓仓库流水DTO") @RequestBody VirtualWarehouseFlowRecord virtualWarehouseFlowRecord) {
        try {
            PageInfo<List<VirtualWarehouseFlowRecord>> pageList = virtualWarehouseFlowRecordService.getVirtualWarehouseStockChangeFlow(virtualWarehouseFlowRecord);
            return Response.builderSuccess(pageList);
        } catch (RomeException e) {
            log.error(e.getMessage(), e);
            return ResponseMsg.EXCEPTION.buildMsg(e.getCode(), e.getMessage());
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return ResponseMsg.EXCEPTION.buildMsg(ResCode.STOCK_ERROR_1003, ResCode.STOCK_ERROR_1003_DESC);
        }
    }
    
    @ApiOperation(value = "查询虚仓库存流水记录历史数据", nickname = "query_vw_stock_change_flow_history", produces = MediaType.APPLICATION_JSON_VALUE)
    @ApiResponse(code = 200, message = "success", response = List.class)
    @RequestMapping(value = "/queryVwStockChangeFlowHistory", method = RequestMethod.POST)
    public Response<PageInfo<VirtualWarehouseFlowRecord>> queryVwStockChangeFlowHistory(@ApiParam(name = "VirtualWarehouseFlowRecord", value = "虚仓仓库流水DTO") @RequestBody VirtualWarehouseFlowRecord virtualWarehouseFlowRecord) {
        try {
            PageInfo<VirtualWarehouseFlowRecord> pageList = virtualWarehouseFlowRecordService.queryVwStockChangeFlowHistory(virtualWarehouseFlowRecord);
            return Response.builderSuccess(pageList);
        } catch (RomeException e) {
            log.error(e.getMessage(), e);
            return Response.builderFail(e.getCode(), e.getMessage());
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return Response.builderFail(ResCode.STOCK_ERROR_1003, ResCode.STOCK_ERROR_1003_DESC);
        }
    }
}
