package com.rome.stock.innerservice.api.dto.bigdata;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * 库存计划查询DTO
 */
@Data
@ApiModel("库存计划页面返回DTO")
public class StockPlanQueryResDTO {

    @ApiModelProperty("渠道")
    private String channel;

    @ApiModelProperty("区域")
    private String area;

    @ApiModelProperty("品类")
    private String cate;

    @ApiModelProperty("商品编码")
    private String skuCode;

    @ApiModelProperty("商品名称")
    private String skuName;

    @ApiModelProperty("指标")
    private String indexName;

    @ApiModelProperty("指标")
    private List<String> valueList;

    @ApiModelProperty("工厂编号")
    private String factoryCode;

//    @ApiModelProperty("weekDate")
//    private List<String> weekDate;

}
