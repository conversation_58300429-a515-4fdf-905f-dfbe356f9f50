package com.rome.stock.innerservice.api.controller;

import javax.annotation.Resource;

import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;

import com.github.pagehelper.PageInfo;
import com.rome.arch.core.clientobject.Response;
import com.rome.arch.core.exception.RomeException;
import com.rome.arch.util.controller.RomeController;
import com.rome.stock.innerservice.api.dto.qry.SalesReturnRecordParamDTO;
import com.rome.stock.innerservice.api.dto.warehouserecord.SalesReturnRecordDTO;
import com.rome.stock.innerservice.constant.ParamValidator;
import com.rome.stock.innerservice.constant.ResponseMsg;
import com.rome.stock.innerservice.domain.service.SalesReturnRecordService;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiResponse;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@RomeController
@RequestMapping("/stock/v1/sales_return_record")
@Api(tags={"销售退货"})
public class SalesReturnRecordController {

    private ParamValidator validator = ParamValidator.INSTANCE;

    @Resource
    private SalesReturnRecordService salesReturnRecordService;

    @ApiOperation(value = "根据条件查询退货单", nickname = "find_sales_return_record", produces = MediaType.APPLICATION_JSON_VALUE)
    @ApiResponse(code = 200, message = "success", response = SalesReturnRecordDTO.class)
    @RequestMapping(value = "/condition", method = RequestMethod.POST)
    public Response findByRealWarehouseCondition(@RequestBody SalesReturnRecordParamDTO paramDTO) {
        if(! validator.validParam(paramDTO)) {
            return ResponseMsg.PARAM_ERROR.buildMsg();
        }
        try {
            PageInfo<SalesReturnRecordDTO> salesReturnRecordPageInfo = salesReturnRecordService.findBySalesReturnRecordCondition(paramDTO);
            return ResponseMsg.SUCCESS.buildMsg(salesReturnRecordPageInfo);
        } catch (RomeException e) {
            log.error(e.getMessage(), e);
            return ResponseMsg.FAIL.buildMsg(e.getCode(), e.getMessage());
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return ResponseMsg.EXCEPTION.buildMsg();
        }
    }

    @ApiOperation(value = "根据退货单id查询退货详情", nickname = "queryDetail", produces = MediaType.APPLICATION_JSON_VALUE)
    @ApiResponse(code = 200, message = "success", response = SalesReturnRecordDTO.class)
    @RequestMapping(value = "/queryDetail/{recordId}", method = RequestMethod.GET)
    public Response<SalesReturnRecordDTO> findByRealWarehouseCondition(@PathVariable Long recordId) {
        try {
            SalesReturnRecordDTO salesReturnRecordPageInfo = salesReturnRecordService.querySaleReturnWarehouseRecordInfoById(recordId);
            return ResponseMsg.SUCCESS.buildMsg(salesReturnRecordPageInfo);
        } catch (RomeException e) {
            log.error(e.getMessage(), e);
            return ResponseMsg.FAIL.buildMsg(e.getCode(), e.getMessage());
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return ResponseMsg.EXCEPTION.buildMsg();
        }
    }
}
