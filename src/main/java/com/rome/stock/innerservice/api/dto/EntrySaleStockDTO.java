/**
 * Filename EntrySaleStockDTO.java
 * Company 上海来伊份科技有限公司。
 * <AUTHOR>
 * @version 
 */
package com.rome.stock.innerservice.api.dto;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

/**
 * 库存进销存表
 * <AUTHOR>
 * @since 2021-8-14 11:43:13
 */
@Data
@NoArgsConstructor
@EqualsAndHashCode
public class EntrySaleStockDTO extends Pagination{
	
	/**
     * 包含count查询
     */
	@ApiModelProperty(value="是否统计条数")
    private boolean count = true;
	
	/**
     * 查询排序，是按日期降序
     */
	@ApiModelProperty(value="查询排序，是按日期降序")
	private boolean sortDesc = true;

    @ApiModelProperty("唯一主键")
    private Long id;

    @ApiModelProperty("实仓仓库ID")
    private Long realWarehouseId;

    @ApiModelProperty("工厂编号")
    private String factoryCode;

    @ApiModelProperty("实仓仓库编号")
    private String realWarehouseCode;

    @ApiModelProperty("实仓仓库名称")
    private String realWarehouseName;

    @ApiModelProperty("商品sku编码")
    private Long skuId;

    @ApiModelProperty("商品sku编码集合")
    private List<Long> skuIds;

    @ApiModelProperty("商品编码")
    private String skuCode;

    @ApiModelProperty("期初库存量")
    private BigDecimal initialQty;

    @ApiModelProperty("入库库存量")
    private BigDecimal inQty;

    @ApiModelProperty("出库库存量")
    private BigDecimal outQty;

    @ApiModelProperty("期末库存量")
    private BigDecimal finalQty;
    
    @ApiModelProperty("原始期初库存量")
    private BigDecimal orginInitialQty;
    
    @ApiModelProperty("原始入库库存量")
    private BigDecimal orginInQty;
    
    @ApiModelProperty("原始出库库存量")
    private BigDecimal orginOutQty;
    
    @ApiModelProperty("原始期末库存量")
    private BigDecimal orginFinalQty;

    @ApiModelProperty("进销存日期")
    private Date entrySaleDate;

    @ApiModelProperty("创建时间")
    private Date createTime;
    
    @ApiModelProperty("更新时间")
    private Date updateTime;
    
    @ApiModelProperty("商品名称")
    private String skuName;
    
    @ApiModelProperty(value = "主单位(商品单位)")
    private String unit;
    
    @ApiModelProperty(value = "查询,开始时间")
	private Date startTime;
	
	@ApiModelProperty(value = "查询,结束时间")
	private Date endTime;
	
	@ApiModelProperty("查询,实仓仓库ID列表")
    private List<Long> realWarehouseIds;
    
}
