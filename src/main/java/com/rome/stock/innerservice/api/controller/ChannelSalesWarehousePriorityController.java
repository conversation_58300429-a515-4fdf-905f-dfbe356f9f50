package com.rome.stock.innerservice.api.controller;

import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;

import com.rome.arch.core.clientobject.Response;
import com.rome.arch.core.exception.RomeException;
import com.rome.arch.util.controller.RomeController;
import com.rome.stock.innerservice.api.dto.ChannelSalesWarehousePriorityDTO;
import com.rome.stock.innerservice.api.dto.ChannelSalesWarehousePriorityPageDTO;
import com.rome.stock.innerservice.constant.ResponseMsg;
import com.rome.stock.innerservice.domain.service.ChannelSalesWarehousePriorityService;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import io.swagger.annotations.ApiResponse;
import lombok.extern.slf4j.Slf4j;

/**
 * @Doc:配置渠道对应的实仓优先级页面
 * @Author: lchy
 * @Date: 2019/6/3
 * @Version 1.0
 */
@Slf4j
@RomeController
@RequestMapping("/stock/v1/channelSalesWarehousePriority")
@Api(tags={"渠道对应优先级接口"})
public class ChannelSalesWarehousePriorityController {
	@Autowired
	private ChannelSalesWarehousePriorityService channelSalesWarehousePriorityService;

	@ApiOperation(value = "批量更新和插入", nickname = "batchList", produces = MediaType.APPLICATION_JSON_VALUE)
	@ApiResponse(code = 200, message = "success")
	@RequestMapping(value = "/batchInsertOrUpdate", method = RequestMethod.POST)
	public Response batchInsertOrUpdate(@ApiParam(name = "batchUpdate", value = "batchUpdate") @RequestBody List<ChannelSalesWarehousePriorityDTO> batchList) {
		try {
			channelSalesWarehousePriorityService.batchInsertOrUpdate(batchList);
			return ResponseMsg.SUCCESS.buildMsg();
		} catch (RomeException e) {
			log.error(e.getMessage(), e);
			return ResponseMsg.FAIL.buildMsg(e.getCode(), e.getMessage());
		} catch (Exception e) {
			log.error(e.getMessage(), e);
			return ResponseMsg.EXCEPTION.buildMsg();
		}
	}

	@ApiOperation(value = "据虚仓组id,渠道code 查询渠道对应的仓库优先级信息", nickname = "queryChannelSalesWarehousePriorityByGroupId", produces = MediaType.APPLICATION_JSON_VALUE)
	@ApiResponse(code = 200, message = "success")
	@RequestMapping(value = "/query", method = RequestMethod.POST)
	public Response queryChannelSalesWarehousePriorityByGroupId(@ApiParam(name = "channelCode", value = "channelCode") @RequestParam String channelCode,
	                                                            @ApiParam(name = "groupId", value = "groupId") @RequestParam Long groupId) {
		try {
			List<ChannelSalesWarehousePriorityPageDTO> result = channelSalesWarehousePriorityService.queryChannelSalesWarehousePriorityByGroupId(channelCode, groupId);
			return ResponseMsg.SUCCESS.buildMsg(result);
		} catch (RomeException e) {
			log.error(e.getMessage(), e);
			return ResponseMsg.FAIL.buildMsg(e.getCode(), e.getMessage());
		} catch (Exception e) {
			log.error(e.getMessage(), e);
			return ResponseMsg.EXCEPTION.buildMsg();
		}
	}
}
