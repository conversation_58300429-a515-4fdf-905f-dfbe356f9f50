package com.rome.stock.innerservice.api.dto.wms;

import com.rome.stock.wms.dto.request.wms.order.Batch;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.xml.bind.annotation.XmlRootElement;
import java.math.BigDecimal;
import java.util.List;

/**
 * 类OrderLine的实现描述：单据列表
 *
 * <AUTHOR> 2019/4/11 20:55
 */
@Data
@EqualsAndHashCode
@XmlRootElement(name = "orderLine")
public class EmsOrderLine {

    @ApiModelProperty(value = "商品编码")
    private String itemCode;

    @ApiModelProperty(value = "行号")
    private String orderLineNo;

    @ApiModelProperty(value = "库存类型(ZP=正品;CC=残次;JS=机损;XS=箱损;ZT=在途库存;默认为查所有类型的库存)")
    private String inventoryType;

    @ApiModelProperty(value = "货主编码")
    private String ownerCode;

    @ApiModelProperty(value = "商品名称")
    private String itemName;

    @ApiModelProperty(value = "应发商品数量")
    private BigDecimal planQty;

    @ApiModelProperty(value = "预期到货时间(YYYY-MM-DD HH:MM:SS)")
    private String expectStartTime;

    @ApiModelProperty("单位")
    private String unit;

    @ApiModelProperty("超收比例")
    private Integer exceedRaio;

    @ApiModelProperty("采购单位")
    private String purchaseUnit;

    @ApiModelProperty("订单创建人")
    private String orderCreatorNo;

    @ApiModelProperty("订单创建人")
    private Integer lineStatus;

    @ApiModelProperty("sap单号")
    private String sapPoNo;

    @ApiModelProperty(value = "批次列表")
    private List<Batch> batchs;

}
