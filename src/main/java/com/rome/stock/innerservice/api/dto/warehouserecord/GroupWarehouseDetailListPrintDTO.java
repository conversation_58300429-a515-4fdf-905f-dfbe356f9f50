package com.rome.stock.innerservice.api.dto.warehouserecord;

import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;

@Data
@EqualsAndHashCode
public class GroupWarehouseDetailListPrintDTO {

	private Long id;
	/**
	 * 物料编码
	 */
	private String skuCode;

	/**
	 * 物料名称
	 */
	private String skuName;

	/**
	 * 规格、型号
	 */
	private String standards;

	/**
	 * 数量
	 */
	private BigDecimal basicQty;

	/**
	 * 计件数
	 */
	private BigDecimal countQty;

	/**
	 * 单位
	 */
	private String unitName;

}
