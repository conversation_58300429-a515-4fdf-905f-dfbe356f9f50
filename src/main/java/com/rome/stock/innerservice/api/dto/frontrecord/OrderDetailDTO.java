package com.rome.stock.innerservice.api.dto.frontrecord;

import java.math.BigDecimal;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

@Data
@EqualsAndHashCode
public class OrderDetailDTO {

    @ApiModelProperty(value = "行号")
    private String lineNo;

    @ApiModelProperty(value = "数量")
    @NotNull
    private BigDecimal skuQty;

    @ApiModelProperty(value = "sku编号")
    private Long skuId;

    @ApiModelProperty(value = "sku编号")
    @NotBlank
    private String skuCode;

    @ApiModelProperty(value = "该sku所属主品的编码,如果不是组合品，该字段不传")
    private String parentSkuCode;

    @ApiModelProperty(value = "1赠品 2非赠品")
    private Integer giftType;

    @ApiModelProperty(value = "单位")
    private String unit;

    @ApiModelProperty(value = "单位编码")
    @NotBlank(message="单位code不能为空")
    private String unitCode;
}
