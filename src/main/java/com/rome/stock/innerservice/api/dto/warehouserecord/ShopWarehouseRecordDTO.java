package com.rome.stock.innerservice.api.dto.warehouserecord;

import com.rome.stock.innerservice.domain.entity.warehouserecord.WarehouseRecordDetail;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

@Data
@EqualsAndHashCode
public class ShopWarehouseRecordDTO {

    @ApiModelProperty(value = "出库单号")
    private String recordCode;

    @ApiModelProperty(value = "sku数量及明细")
    private List<WarehouseRecordDetail> warehouseRecordDetails;
}
