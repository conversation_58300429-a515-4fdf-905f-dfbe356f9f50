package com.rome.stock.innerservice.api.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;

@Data
@EqualsAndHashCode
public class ShopSkuStock {

    @ApiModelProperty(value = "skuId")
    private Long skuId;

    @ApiModelProperty(value = "skuCode")
    private String skuCode;

    @ApiModelProperty(value = "sku真实数量")
    private BigDecimal availableQty;

    @ApiModelProperty(value = "真实库存")
    private BigDecimal realQty;

    @ApiModelProperty(value = "锁定库存")
    private BigDecimal lockQty;

    @ApiModelProperty(value = "在途库存")
    private BigDecimal onroadQty;

    @ApiModelProperty(value = "质检库存")
    private BigDecimal qualityQty;

    @ApiModelProperty(value = "一般是质检不合格库存")
    private BigDecimal unqualifiedQty;

    @ApiModelProperty(value = "查询单位")
    private String unitCode;

    @ApiModelProperty(value = "工厂编码")
    private String factoryCode;

    @ApiModelProperty(value = "工厂名称")
    private String factoryName;

    @ApiModelProperty(value = "仓库编码")
    private String realWarehouseOutCode;


}
