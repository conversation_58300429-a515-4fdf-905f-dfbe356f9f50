package com.rome.stock.innerservice.api.controller;

import java.util.List;

import javax.annotation.Resource;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;

import com.alibaba.fastjson.JSON;
import com.rome.arch.core.clientobject.Response;
import com.rome.arch.core.exception.RomeException;
import com.rome.arch.util.controller.RomeController;
import com.rome.stock.innerservice.api.dto.AdjustSkuStockDTO;
import com.rome.stock.innerservice.api.dto.RealWarehouse;
import com.rome.stock.innerservice.api.dto.RealWarehouseStockDTO;
import com.rome.stock.innerservice.api.dto.WarehouseDTO;
import com.rome.stock.innerservice.api.dto.frontrecord.SkuStock;
import com.rome.stock.innerservice.common.ResCode;
import com.rome.stock.innerservice.constant.ParamValidator;
import com.rome.stock.innerservice.constant.ResponseMsg;
import com.rome.stock.innerservice.domain.repository.SapInterfaceLogRepository;
import com.rome.stock.innerservice.domain.service.StockService;
import com.rome.stock.innerservice.domain.service.XTStockService;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import io.swagger.annotations.ApiResponse;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@RomeController
@RequestMapping("/stock/xt")
@Api(tags={"玄天服务接口"})
public class XTStockController {

    private ParamValidator validator = ParamValidator.INSTANCE;

    @Autowired
    private XTStockService xtStockService;

    @Autowired
    private StockService stockService;

    @Resource
    private SapInterfaceLogRepository sapInterfaceLogRepository;

    /**
     * 开通仓库
     * @param warehouseDTO
     * @return
     */
    @ApiOperation(value = "开通仓库", produces = MediaType.APPLICATION_JSON_VALUE)
    @ApiResponse(code = 200, message = "success", response = String.class)
    @RequestMapping(value = "/createWarehouse", method = RequestMethod.POST)
    public Response<RealWarehouse> createWarehouse(@ApiParam(name = "warehouseDTO", value = "开通仓库DTO") @RequestBody WarehouseDTO warehouseDTO) {
        if (! validator.validParam(warehouseDTO)) {
            return ResponseMsg.PARAM_ERROR.buildMsg();
        }
        if (! validator.validCollection(warehouseDTO.getChannelDTOList())) {
            return ResponseMsg.PARAM_ERROR.buildMsg();
        }
        try {
            RealWarehouse realWarehouse = xtStockService.createWarehouse(warehouseDTO);
            return ResponseMsg.SUCCESS.buildMsg(realWarehouse);
        } catch (RomeException e) {
            log.error(e.getMessage(), e);
            return ResponseMsg.FAIL.buildMsg(e.getCode(), e.getMessage());
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return ResponseMsg.EXCEPTION.buildMsg();
        }
    }


    /**
     * 查询商家仓库信息
     * @param merchantId
     * @param channelCode
     * @return
     */
    @ApiOperation(value = "查询仓库", produces = MediaType.APPLICATION_JSON_VALUE)
    @ApiResponse(code = 200, message = "success", response = String.class)
    @RequestMapping(value = "/queryWarehouse", method = RequestMethod.GET)
    public Response<List<RealWarehouse>> queryWarehouse(@ApiParam(name = "merchantId", value = "商家id") @RequestParam("merchantId") Long merchantId,
                                                  @ApiParam(name = "channelCode", value = "渠道编码") @RequestParam("channelCode") String channelCode) {
        if (! validator.validPositiveLong(merchantId)) {
            return ResponseMsg.PARAM_ERROR.buildMsg();
        }
        if (! validator.validStr(channelCode)) {
            return ResponseMsg.PARAM_ERROR.buildMsg();
        }
        try {
            List<RealWarehouse> realWarehouses = xtStockService.queryWarehouse(merchantId, channelCode);
            return ResponseMsg.SUCCESS.buildMsg(realWarehouses);
        } catch (RomeException e) {
            log.error(e.getMessage(), e);
            return ResponseMsg.FAIL.buildMsg(e.getCode(), e.getMessage());
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return ResponseMsg.EXCEPTION.buildMsg();
        }
    }

    @ApiOperation(value = "调整商家商品库存", produces = MediaType.APPLICATION_JSON_VALUE)
    @ApiResponse(code = 200, message = "success", response = String.class)
    @RequestMapping(value = "/adjustSkuStock", method = RequestMethod.POST)
    public Response<RealWarehouse> adjustSkuStock(@ApiParam(name = "adjustSkuStockDTO", value = "调整库存dto") @RequestBody AdjustSkuStockDTO adjustSkuStockDTO) {
        String message ="";
        boolean isSucc = false;
        String json = JSON.toJSONString(adjustSkuStockDTO);
        try {
            RealWarehouse warehouse = xtStockService.adjustSkuStock(adjustSkuStockDTO);
            isSucc = true;
            message = "200";
            return   Response.builderSuccess(warehouse);
        } catch (RomeException e) {
            message = e.getMessage();
            log.error(e.getMessage(), e);
            return Response.builderFail(e.getCode(),e.getMessage());
        } catch (Exception e) {
            message = e.getMessage();
            log.error(e.getMessage(), e);
            return Response.builderFail(ResCode.STOCK_ERROR_1003_DESC,ResCode.STOCK_ERROR_1003_DESC);
        }finally {
            sapInterfaceLogRepository.saveCallBackInterFaceLog(2, adjustSkuStockDTO.getOutRecordCode(), "adjustSkuStock",
                    json, message, isSucc);
        }
    }

    @ApiOperation(value = "查询商家商品库存", produces = MediaType.APPLICATION_JSON_VALUE)
    @ApiResponse(code = 200, message = "success", response = RealWarehouseStockDTO.class)
    @RequestMapping(value = "/querySkuStock", method = RequestMethod.POST)
    public Response<SkuStock> querySkuStock(@ApiParam(name = "skuCode", value = "skuCode") @RequestParam("skuCode")String skuCode
            ,@ApiParam(name = "merchantId", value = "商家编号") @RequestParam("merchantId")Long merchantId) {
        try {
            RealWarehouseStockDTO skuStock = stockService.queryWarehouseStockByMerchantId(skuCode,merchantId);
            return   Response.builderSuccess(skuStock);
        } catch (RomeException e) {
            log.error(e.getMessage(), e);
            return Response.builderFail(e.getCode(),e.getMessage());
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return Response.builderFail(ResCode.STOCK_ERROR_1003_DESC,ResCode.STOCK_ERROR_1003_DESC);
        }
    }


    @ApiOperation(value = "根据商家编号查询商家实仓信息", produces = MediaType.APPLICATION_JSON_VALUE)
    @ApiResponse(code = 200, message = "success", response = RealWarehouse.class)
    @RequestMapping(value = "/queryRealWarehouseByMerchantId", method = RequestMethod.POST)
    public Response<RealWarehouse> queryRealWarehouseByMerchantId(@ApiParam(name = "merchantId", value = "商家编号") @RequestParam("merchantId")Long merchantId) {
        try {
            RealWarehouse realWarehouse = stockService.queryRealWarehouseByMerchantId(merchantId);
            return   Response.builderSuccess(realWarehouse);
        } catch (RomeException e) {
            log.error(e.getMessage(), e);
            return Response.builderFail(e.getCode(),e.getMessage());
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return Response.builderFail(ResCode.STOCK_ERROR_1003_DESC,ResCode.STOCK_ERROR_1003_DESC);
        }
    }


    /**
     * 开通虚拟物品仓库
     * @param warehouseDTO
     * @return
     */
    @ApiOperation(value = "开通虚拟物品仓库", produces = MediaType.APPLICATION_JSON_VALUE)
    @ApiResponse(code = 200, message = "success", response = String.class)
    @RequestMapping(value = "/createVirtualSkuWarehouse", method = RequestMethod.POST)
    public Response<RealWarehouse> createVirtualSkuWarehouse(@ApiParam(name = "warehouseDTO", value = "开通仓库DTO") @RequestBody WarehouseDTO warehouseDTO) {
        if (! validator.validParam(warehouseDTO)) {
            return ResponseMsg.PARAM_ERROR.buildMsg();
        }
        if (! validator.validCollection(warehouseDTO.getChannelDTOList())) {
            return ResponseMsg.PARAM_ERROR.buildMsg();
        }
        //判断仓库类型是否为虚拟物品仓类型
        if(!warehouseDTO.getWarehouseType().equals(2)){
            return ResponseMsg.PARAM_ERROR.buildMsg(ResCode.STOCK_ERROR_9019,ResCode.STOCK_ERROR_9019_DESC);
        }
        try {
            xtStockService.createWarehouse(warehouseDTO);
            return ResponseMsg.SUCCESS.buildMsg();
        } catch (RomeException e) {
            log.error(e.getMessage(), e);
            return ResponseMsg.FAIL.buildMsg(e.getCode(), e.getMessage());
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return ResponseMsg.EXCEPTION.buildMsg();
        }
    }

    @ApiOperation(value = "调整虚拟物品库存", produces = MediaType.APPLICATION_JSON_VALUE)
    @ApiResponse(code = 200, message = "success", response = String.class)
    @RequestMapping(value = "/adjustVirtualSkuStock", method = RequestMethod.POST)
    public Response<RealWarehouse> adjustVirtualSkuStock(@ApiParam(name = "adjustSkuStockDTO", value = "调整库存dto") @RequestBody AdjustSkuStockDTO adjustSkuStockDTO) {
        //判断仓库类型是否为虚拟物品仓类型
        if(adjustSkuStockDTO.getVirtualSkuFlag() ==null || adjustSkuStockDTO.getVirtualSkuFlag()!=1){
            return ResponseMsg.PARAM_ERROR.buildMsg(ResCode.STOCK_ERROR_9019,ResCode.STOCK_ERROR_9019_DESC);
        }
        String message ="";
        boolean isSucc = false;
        String json = JSON.toJSONString(adjustSkuStockDTO);
        try {
            RealWarehouse warehouse = xtStockService.adjustSkuStock(adjustSkuStockDTO);
            isSucc = true;
            message = "200";
            return   Response.builderSuccess(warehouse);
        } catch (RomeException e) {
            message = e.getMessage();
            log.error(e.getMessage(), e);
            return Response.builderFail(e.getCode(),e.getMessage());
        } catch (Exception e) {
            message = e.getMessage();
            log.error(e.getMessage(), e);
            return Response.builderFail(ResCode.STOCK_ERROR_1003_DESC,ResCode.STOCK_ERROR_1003_DESC);
        }finally {
            sapInterfaceLogRepository.saveCallBackInterFaceLog(2, adjustSkuStockDTO.getOutRecordCode(), "adjustVirtualSkuStock",
                    json, message, isSucc);
        }
    }
    
    @ApiOperation(value = "根据渠道调整虚拟物品库存，不允许渠道下多个虚拟仓", produces = MediaType.APPLICATION_JSON_VALUE)
    @ApiResponse(code = 200, message = "success", response = String.class)
    @RequestMapping(value = "/adjustVirtualSkuStockByChannelCode", method = RequestMethod.POST)
    public Response<RealWarehouse> adjustVirtualSkuStockByChannelCode(@ApiParam(name = "adjustSkuStockDTO", value = "调整库存dto") @RequestBody AdjustSkuStockDTO adjustSkuStockDTO) {
        try {
            RealWarehouse warehouse = xtStockService.adjustVirtualSkuStockByChannelCode(adjustSkuStockDTO);
            return   Response.builderSuccess(warehouse);
        } catch (RomeException e) {
            log.error(e.getMessage(), e);
            return Response.builderFail(e.getCode(),e.getMessage());
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return Response.builderFail(ResCode.STOCK_ERROR_1003_DESC,ResCode.STOCK_ERROR_1003_DESC + e.getMessage());
        }
    }
    
    @ApiOperation(value = "根据shopCode调整供应商物品库存，不允许shopCode下多个供应商仓", produces = MediaType.APPLICATION_JSON_VALUE)
    @ApiResponse(code = 200, message = "success", response = String.class)
    @RequestMapping(value = "/adjustSupplierSkuStockByShopCode", method = RequestMethod.POST)
    public Response<RealWarehouse> adjustSupplierSkuStockByShopCode(@ApiParam(name = "adjustSkuStockDTO", value = "调整库存dto") @RequestBody AdjustSkuStockDTO adjustSkuStockDTO) {
        try {
            RealWarehouse warehouse = xtStockService.adjustSupplierSkuStockByShopCode(adjustSkuStockDTO);
            return   Response.builderSuccess(warehouse);
        } catch (RomeException e) {
            log.error(e.getMessage(), e);
            return Response.builderFail(e.getCode(),e.getMessage());
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return Response.builderFail(ResCode.STOCK_ERROR_1003_DESC,ResCode.STOCK_ERROR_1003_DESC + e.getMessage());
        }
    }




}
