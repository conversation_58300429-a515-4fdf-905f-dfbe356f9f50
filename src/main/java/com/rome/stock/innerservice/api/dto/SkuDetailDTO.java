package com.rome.stock.innerservice.api.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.math.BigDecimal;

/**
 * @Doc:.....
 * @Author: lchy
 * @Date: 2019/7/31
 * @Version 1.0
 */
@Data
@EqualsAndHashCode
public class SkuDetailDTO {
    /**
     * 商品skuID
     */
    @ApiModelProperty(value = "商品skuID", required = true)
    @NotNull(message = "商品skuID不能为空")
    private Long skuId;
    
    /**
     * 商品编码
     */
    @ApiModelProperty(value = "商品编码")
    private String skuCode;

    /**
     * 实际单位的数querySkuStockList量
     */
    @ApiModelProperty(value = "调整数量", required = true)
    @NotNull(message = "调整数量不能为空")
    private BigDecimal skuQty;

    /**
     * 实际单位code
     */
    @ApiModelProperty(value = "单位code", required = true)
    @NotBlank(message = "单位code不能为空")
    private String unitCode;

}
