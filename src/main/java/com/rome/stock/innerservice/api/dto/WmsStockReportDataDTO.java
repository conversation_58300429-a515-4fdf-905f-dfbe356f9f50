package com.rome.stock.innerservice.api.dto;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * wms报表上传数据
 * <AUTHOR>
 * @since 2020-8-7 10:04:04
 */
@Data
@EqualsAndHashCode
public class WmsStockReportDataDTO extends Pagination{

    // 工厂
    @ApiModelProperty(name = "工厂")
    private String factory;
    // 物料
    @ApiModelProperty(name = "物料")
    private String materials;
    // 描述
    @ApiModelProperty(name = "描述")
    private String descrip;
    // 库存地点
    @ApiModelProperty(name = "库存地点")
    private String stockLocation;
    // 存储类型
    @ApiModelProperty(name = "存储类型")
    private String storageType;
    // 仓位
    @ApiModelProperty(name = "仓位")
    private String stockPosition;
    // 库存类别
    @ApiModelProperty(name = "库存类别")
    private String stockType;
    // 数量
    @ApiModelProperty(name = "数量")
    private BigDecimal quantity;
    // 单位
    @ApiModelProperty(name = "单位")
    private String unit;
    // 仓库数量
    @ApiModelProperty(name = "仓库数量")
    private BigDecimal warehouseNum;
    // 仓库单位
    @ApiModelProperty(name = "仓库单位")
    private String warehouseUnit;
    // 标记日期
    @ApiModelProperty(name = "标记日期")
    private Date remarkDate;
    // 收货日期
    @ApiModelProperty(name = "收货日期")
    private Date acceptDate;
    // 生产日期
    @ApiModelProperty(name = "生产日期")
    private Date productionDate;
    // 最晚发货日期
    @ApiModelProperty(name = "最晚发货日期")
    private Date latestDeliveryDate;
    // 不得销售日期
    @ApiModelProperty(name = "不得销售日期")
    private Date noSaleDate;
    // 最佳销售日期
    @ApiModelProperty(name = "最佳销售日期")
    private Date bestSellingDate;
    // 库龄
    @ApiModelProperty(name = "库龄")
    private Long storageAge;
    
    @ApiModelProperty("工厂编号")
    private String factoryCode;

    @ApiModelProperty("实仓仓库编号")
    private String realWarehouseCode;
    
    @ApiModelProperty("sku编码列表")
    private List<String> skuCodes;
    
    @ApiModelProperty("创建时间")
    private Date createTime;
    
    @ApiModelProperty("更新时间")
    private Date updateTime;
    
    @ApiModelProperty("开始时间")
    private Date startTime;
    
    @ApiModelProperty("结束时间")
    private Date endTime;
}
