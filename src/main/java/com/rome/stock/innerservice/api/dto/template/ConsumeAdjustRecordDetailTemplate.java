package com.rome.stock.innerservice.api.dto.template;

import lombok.Data;
import lombok.EqualsAndHashCode;
import java.io.Serializable;
import java.math.BigDecimal;

@Data
@EqualsAndHashCode
public class ConsumeAdjustRecordDetailTemplate implements Serializable {
    /**
     * 商品编号
     */
    private String skuCode;
    /**
     * 调整数量
     */
    private BigDecimal skuQty;
    /**
     * 调整单位
     */
    private String unitCode;
    /**
     * 批次备注
     */
    private String remark;
}
