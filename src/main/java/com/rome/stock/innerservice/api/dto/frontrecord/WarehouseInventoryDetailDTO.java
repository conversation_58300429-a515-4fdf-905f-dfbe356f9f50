package com.rome.stock.innerservice.api.dto.frontrecord;


import lombok.Data;

import java.math.BigDecimal;

@Data
public class WarehouseInventoryDetailDTO {

    /**
     * 唯一主键
     */
    private Long id;

    /**
     * 所属单据编码
     */
    private String recordCode;

    /**
     * 单据id
     */
    private Integer frontRecordId;

    /**
     * 商品sku编码
     */
    private Long skuId;

    /**
     * 商品sku编码
     */
    private String skuCode;

    /**
     * 商品名称
     */
    private String skuName;

    /**
     * 商品规格
     */
    private String skuStandard;
    /**
     * 商品数量（实盘数量）
     */
    private BigDecimal skuQty;

    /**
     * 单位
     */
    private String unit;

    /**
     * 单位编码
     */
    private String unitCode;

    /**
     * 源系统账面数量
     */
    private BigDecimal accQty;

    /**
     * 差异数量
     * */
    private BigDecimal diffQty;

    /**
     * 盘点情况（1，盘盈，2，盘亏，3，平盘）
     */
    private Integer adjustType;

    /**
     * 库存状态（1，合格 2，质检不合格 3，待质检）
     */
    private Integer inventoryStatus;

    /**
     * 基本的单位
     */
    private String basicUnit;

    /**
     * 中台账面数量
     */
    private BigDecimal originAccQty;

    /**
     *唯一单号
     */
    private String uniqueKey;

    public String getUniqueKey(){
        return this.skuCode+"_"+this.inventoryStatus;
    }
}
