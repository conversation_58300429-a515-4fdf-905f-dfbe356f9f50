package com.rome.stock.innerservice.api.dto.frontrecord;

import java.util.Date;
import java.util.List;

import javax.validation.Valid;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

import com.rome.stock.innerservice.api.dto.Pagination;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 门店退货单
 */
@Data
@EqualsAndHashCode
public class ShopReturnRecordDTO extends Pagination {

    @ApiModelProperty(value = "id")
    private Long id;

    @ApiModelProperty(value = "退货单号")
    private String recordCode;

    @ApiModelProperty(value = "单据编码")
    @NotBlank(message="单据编码不能为空")
    private String outRecordCode;

    @ApiModelProperty(value = "店铺编码")
    @NotBlank(message="店铺编码不能为空")
    private String shopCode;

    @ApiModelProperty(value = "门店名称")
    @NotBlank(message="门店名称不能为空")
    private String shopName;

    @ApiModelProperty(value = "门店类型:门店类型：1加盟，2直营，3加盟联营")
    @NotNull(message="门店类型不能为空")
    private Integer shopType;

    @ApiModelProperty(value = "采购单类型 1直营普通采购 2加盟商采购 3供应商直送  4冷链 5加盟商叫货", required = true)
    @NotNull(message="采购单类型不能为空")
    private Integer recordType;

    @ApiModelProperty(value = "退货时间")
    @NotNull(message="退货时间不能为空")
    private Date outCreateTime;

    @ApiModelProperty(value = "入向仓库Id")
    private Long inRealWarehouseId;

    @ApiModelProperty(value = "出向仓库Id")
    private Long outRealWarehouseId;

    @ApiModelProperty(value = "入向仓库名称")
    private String inRealWarehouseName;

    @ApiModelProperty(value = "出向仓库名称")
    private String outRealWarehouseName;

    @ApiModelProperty(value = "备注")
    private String remark;

    @ApiModelProperty(value = "期望退货日期")
    private Date expectDate;

    @ApiModelProperty(value = "SAP的退货单号")
    @NotBlank(message="sap单号不能为空")
    private String sapReverseNo;

    @ApiModelProperty(value = "推送交易中心的状态")
    private Integer transStatus;

    @ApiModelProperty(value = "单据状态")
    private Integer recordStatus;

    @ApiModelProperty(value = "开始时间")
    private Date startTime;

    @ApiModelProperty(value = "结束时间")
    private Date endTime;

    @ApiModelProperty(value = "sku数量及明细")
    @NotNull(message="sku数量及明细不能为空")
    @Valid
    private List<ShopReturnDetailDTO> frontRecordDetails;
}
