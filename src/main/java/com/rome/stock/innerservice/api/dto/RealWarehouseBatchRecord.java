package com.rome.stock.innerservice.api.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

@Data
@EqualsAndHashCode
public class RealWarehouseBatchRecord extends Pagination {

    @ApiModelProperty(value = "主键")
    private Long id;
    @ApiModelProperty(value = "出入库单据编号")
    private String recordCode;
    @ApiModelProperty(value = "前置单号")
    private String frontRecordCode;
    @ApiModelProperty(value = "检验单号")
    private String qualityCode;
    @ApiModelProperty(value = "wms收货单编号")
    private String wmsRecordCode;
    @ApiModelProperty(value = "工厂编码")
    private String factoryCode;
    @ApiModelProperty(value = "仓库id")
    private Long realWarehouseId;
    @ApiModelProperty(value = "仓库code")
    private String realWarehouseCode;
    @ApiModelProperty(value = "仓库名称")
    private String realWarehouseName;
    @ApiModelProperty(value = "商品id")
    private Long skuId;
    @ApiModelProperty(value = "商品编号")
    private String skuCode;
    @ApiModelProperty(value = "商品名称")
    private String skuName;
    @ApiModelProperty(value = "商品skuId集合")
    private List<Long> skuIds;
    @ApiModelProperty(value = "商品实际出入库数量")
    private BigDecimal actualQty;
    @ApiModelProperty(value = "批次编号")
    private String batchCode;
    @ApiModelProperty(value = "质检状态")
    private Integer qualityStatus;
    @ApiModelProperty(value = "业务类型")
    private Integer businessType;
    @ApiModelProperty(value = "库存类型")
    private Integer inventoryType;
    @ApiModelProperty(value = "生产日期")
    private Date productDate;
    @ApiModelProperty(value = "创建日期")
    private Date createTime;
    @ApiModelProperty(value = "开始时间")
    private Date startTime;
    @ApiModelProperty(value = "结束时间")
    private Date endTime;
    @ApiModelProperty(value = "sap行号")
    private String sapLineNo;
    @ApiModelProperty(value = "修改人")
    private Long modifier;
    @ApiModelProperty(value = "修改时间")
    private Date updateTime;
    
}
