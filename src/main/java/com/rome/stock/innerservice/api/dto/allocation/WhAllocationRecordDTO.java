package com.rome.stock.innerservice.api.dto.allocation;

import com.rome.stock.innerservice.common.VmAllocation.AllocationCalQtyRes;
import com.rome.stock.innerservice.domain.entity.RealWarehouseE;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.Valid;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * WhAllocationRecordDTO类的实现描述：仓库调拨前置单据
 *
 * <AUTHOR> 2019/7/21 21:33
 */
@Data
@EqualsAndHashCode
public class WhAllocationRecordDTO {

    @ApiModelProperty(value = "单据编码")
    @NotBlank(message="单据编码不能为空")
    private String recordCode;

    @ApiModelProperty(value = "入向仓库id")
    private Long inWarehouseId;

    @ApiModelProperty(value = "出向仓库id")
    private Long outWarehouseId;

    @ApiModelProperty(value = "sku数量及明细")
    @NotNull(message="sku数量及明细不能为空")
    @Valid
    private List<WhAllocationDetailDTO> warehouseRecordDetails;

    /**
     * 是否质量问题调拨 1.是 0 不是
     */
    private Integer isQualityAllotcate;

    private RealWarehouseE inWarehouse;

    private RealWarehouseE outWarehouse;

    @ApiModelProperty(value = "sapPo号")
//    @NotBlank(message="sapPo号不能为空")
    private String sapPoNo;

    @ApiModelProperty(value = "入库工厂编号")
//    @NotBlank(message="入库工厂编号不能为空")
    private String inFactoryCode;
    @ApiModelProperty(value = "入库外部仓库编号")
//    @NotBlank(message="入库外部仓库编号不能为空")
    private String inRealWarehouseOutCode;

    @ApiModelProperty(value = "出库工厂编号")
//    @NotBlank(message="出库工厂编号不能为空")
    private String outFactoryCode;
    @ApiModelProperty(value = "出库外部仓库编号")
//    @NotBlank(message="出库外部仓库编号不能为空")
    private String outRealWarehouseOutCode;

}
