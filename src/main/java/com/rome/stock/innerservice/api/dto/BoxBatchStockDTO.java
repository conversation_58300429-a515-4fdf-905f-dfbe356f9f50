package com.rome.stock.innerservice.api.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 * @Description BoxBatchStockDTO
 * <AUTHOR>
 * @Date 2024/5/21
 **/

@Data
public class BoxBatchStockDTO {



    @ApiModelProperty(value = "唯一主键")
    private Long id;

    @ApiModelProperty(value = "商品skuID")
    private Long skuId;

    @ApiModelProperty(value = "商品编号")
    private String skuCode;

    @ApiModelProperty(value = "商品原箱数量")
    private BigDecimal boxSkuQty;

    @ApiModelProperty(value = "锁定原箱数量")
    private BigDecimal boxLockQty;

    @ApiModelProperty(value = "可用原箱库存")
    private BigDecimal boxAvailableQty;

    @ApiModelProperty(value = "商品非原箱数量")
    private BigDecimal mixSkuQty;

    @ApiModelProperty(value = "锁定非原箱数量")
    private BigDecimal mixLockQty;

    @ApiModelProperty(value = "可用非原箱库存")
    private BigDecimal mixAvailableQty;

    @ApiModelProperty(value = "批次编码-批次号")
    private String batchCode;

    @ApiModelProperty(value = "实体仓库id")
    private Long realWarehouseId;

    @ApiModelProperty(value = "生产日期")
    private Date productDate;

}
