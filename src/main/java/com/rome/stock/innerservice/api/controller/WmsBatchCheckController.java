/**
 * Filename WmsBatchCheckController.java
 * Company 上海来伊份科技有限公司。
 * <AUTHOR>
 * @version 
 */
package com.rome.stock.innerservice.api.controller;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import com.alibaba.fastjson.JSONObject;
import com.github.pagehelper.PageInfo;
import com.rome.arch.core.clientobject.Response;
import com.rome.arch.core.exception.RomeException;
import com.rome.arch.util.controller.RomeController;
import com.rome.stock.innerservice.api.dto.WmsBatchCheckFlowDTO;
import com.rome.stock.innerservice.common.ResCode;
import com.rome.stock.innerservice.constant.ResponseMsg;
import com.rome.stock.innerservice.domain.service.WmsBatchCheckService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiResponse;
import lombok.extern.slf4j.Slf4j;

/**
 * wms批次库存核对
 * <AUTHOR>
 * @since 2021-7-7 14:24:22
 */
@Slf4j
@RomeController
@RequestMapping("/stock/v1/wms_batch_check")
@Api(tags={"wms批次库存核对"})
public class WmsBatchCheckController {
	
	@Autowired
	private WmsBatchCheckService wmsBatchCheckService;

	@ApiOperation(value = "wms批次库存核对", nickname = "wmsBatchCheckJob", produces = MediaType.APPLICATION_JSON_VALUE)
    @ApiResponse(code = 200, message = "success")
    @RequestMapping(value = "/wmsBatchCheckJob", method = RequestMethod.POST)
	public Response wmsBatchCheckJob(@RequestBody JSONObject requestVO) {
		try {
			wmsBatchCheckService.wmsBatchCheckJob();
            return ResponseMsg.SUCCESS.buildMsg(null);
        } catch (RomeException e) {
            log.error(e.getMessage(), e);
            return ResponseMsg.FAIL.buildMsg(e.getCode(),e.getMessage());
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return ResponseMsg.EXCEPTION.buildMsg(ResCode.STOCK_ERROR_1003_DESC,ResCode.STOCK_ERROR_1003_DESC);
        }
	}

    @ApiOperation(value = "根据条件查询wms批次库存核对表", nickname = "queryWmsBatchCheckByCondition", produces = MediaType.APPLICATION_JSON_VALUE)
    @ApiResponse(code = 200, message = "success")
    @RequestMapping(value = "/queryWmsBatchCheckByCondition", method = RequestMethod.POST)
    public Response<PageInfo<WmsBatchCheckFlowDTO>> queryWmsBatchCheckByCondition(@RequestBody WmsBatchCheckFlowDTO paramDto) {
        try {
            PageInfo<WmsBatchCheckFlowDTO> dtoList = wmsBatchCheckService.queryWmsBatchCheckByCondition(paramDto);
            return ResponseMsg.SUCCESS.buildMsg(dtoList);
        } catch (RomeException e) {
            log.error(e.getMessage(), e);
            return ResponseMsg.FAIL.buildMsg(e.getCode(),e.getMessage());
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return ResponseMsg.EXCEPTION.buildMsg(e.getMessage());
        }
    }
    
    @ApiOperation(value = "根据仓库Id重新同步wms批次库存核对表,一般是手动", nickname = "wmsBatchCheckSync", produces = MediaType.APPLICATION_JSON_VALUE)
    @ApiResponse(code = 200, message = "success")
    @RequestMapping(value = "/wmsBatchCheckSync", method = RequestMethod.POST)
    public Response<PageInfo<WmsBatchCheckFlowDTO>> wmsBatchCheckSync(@RequestBody WmsBatchCheckFlowDTO paramDto) {
        try {
        	wmsBatchCheckService.wmsBatchCheckSync(paramDto);
            return ResponseMsg.SUCCESS.buildMsg();
        } catch (RomeException e) {
            log.error(e.getMessage(), e);
            return ResponseMsg.FAIL.buildMsg(e.getCode(),e.getMessage());
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return ResponseMsg.EXCEPTION.buildMsg(e.getMessage());
        }
    }
    
    @ApiOperation(value = "根据仓库Id和skuIds,根据wms批次库存初始化批次库存工具 【慎用】", nickname = "wmsBatchInitToolSync", produces = MediaType.APPLICATION_JSON_VALUE)
    @ApiResponse(code = 200, message = "success")
    @RequestMapping(value = "/wmsBatchInitToolSync", method = RequestMethod.POST)
    public Response<String> wmsBatchInitToolSync(@RequestBody WmsBatchCheckFlowDTO paramDto) {
        try {
        	log.warn("根据wms批次库存初始化批次库存,实仓id={},只是记录一下", paramDto.getRealWarehouseId());
        	wmsBatchCheckService.wmsBatchInitToolSync(paramDto);
            return ResponseMsg.SUCCESS.buildMsg();
        } catch (RomeException e) {
            log.error(e.getMessage(), e);
            return ResponseMsg.FAIL.buildMsg(e.getCode(),e.getMessage());
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return ResponseMsg.EXCEPTION.buildMsg(e.getMessage());
        }
    }
    
}
