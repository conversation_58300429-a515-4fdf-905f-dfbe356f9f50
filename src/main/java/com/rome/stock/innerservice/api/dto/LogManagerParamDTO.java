package com.rome.stock.innerservice.api.dto;

import com.rome.stock.innerservice.domain.entity.warehouserecord.WarehouseRecordE;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;
import java.util.List;


@Data
@EqualsAndHashCode
@ApiModel(value = "统一日志查询", description = "统一日志查询")
public class LogManagerParamDTO extends Pagination {

	@ApiModelProperty(value = "业务单号")
	private String businessOrderCode;

	@ApiModelProperty(value = "后置单号")
	private String postOrderCode;

	@ApiModelProperty(value = "前置单外部单号")
	private String frontOutRecordCode;

	@ApiModelProperty(value = "交互状态 0：失败  1:成功")
	private Integer status;

	@ApiModelProperty(value = "业务类型")
	private String businessType;

	@ApiModelProperty(value = "创建开始时间")
	private Date startTime;
	
	@ApiModelProperty(value = "创建结束时间")
	private Date endTime;

	@ApiModelProperty(value = "请求服务集",hidden = true)
	private List<String> requestServices;

	@ApiModelProperty(value = "出入库单同步WMS状态",hidden = true)
	private Integer syncWmsStatus;

	@ApiModelProperty(value = "出入库单订单状态",hidden = true)
	private List<Integer> recordTypes;

	@ApiModelProperty(value = "分页起始行")
	private Integer pIndex;

	@ApiModelProperty(value = "条数")
	private Integer num;

	@ApiModelProperty(hidden = true)
	private WarehouseRecordE cacheRecordE;
}
