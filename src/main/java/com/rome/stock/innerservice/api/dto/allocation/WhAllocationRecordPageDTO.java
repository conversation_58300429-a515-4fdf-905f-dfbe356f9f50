package com.rome.stock.innerservice.api.dto.allocation;

import java.util.Date;
import java.util.List;

import com.rome.stock.innerservice.api.dto.BusinessReasonDTO;
import com.rome.stock.innerservice.api.dto.Pagination;
import com.rome.stock.innerservice.api.dto.RealWarehouse;

import lombok.Data;

/**
 * 类WhAllocationRecordPage的实现描述：
 *
 * <AUTHOR> 2019/5/15 17:07
 */
@Data
public class WhAllocationRecordPageDTO extends Pagination {

    /**
     * 唯一主键
     */
    private Long id;
    /**
     * 单据编号
     */
    private String recordCode;
    /**
     * 单据类型：22: 调拨单
     */
    private Integer recordType;
    /**
     * 0-已新建，1-已确认  2.已取消  5-已派车 10.已出库 11.已入库
     */
    private Integer recordStatus;
    /**
     * 1.内部调拨 2.RDC调拨 3.退货调拨 4.电商仓调拨 4.质量问题调拨
     */
    private Integer businessType;
    /**
     * 是否差异入库：0-没有差异，1-有差异
     */
    private Integer isDiffIn;
    /**
     * 入向仓库id
     */
    private Long inWarehouseId;
    /**
     * 入向仓库联系人
     */
    private String inWarehouseName;
    /**
     * 入向仓库Code
     */
    private String inWarehouseCode;
    /**
     * 出向仓库code
     */
    private String outWarehouseCode;
    /**
     * 入向联系电话
     */
    private String inWarehouseMobile;
    /**
     * 出向仓库id
     */
    private Long outWarehouseId;
    /**
     * 出向仓库联系人
     */
    private String outWarehouseName;
    /**
     * 出向仓库电话
     */
    private String outWarehouseMobile;
    /**
     * 调拨日期
     */
    private Date allotTime;
    /**
     * 预计到货日期
     */
    private Date expeAogTime;
    /**
     * 审核人
     */
    private Long auditor;
    /**
     * 审核时间
     */
    private Date auditTime;
    /**
     * 备注
     */
    private String remark;

    /**
     * 开始日期
     */
    private Date startDate;

    /**
     * 结束日期
     */
    private Date endDate;

    /**
     * 明细信息
     */
    private List<WhAllocationDetailDTO> frontRecordDetails;


    /**
     * 入向仓库名称
     */
    private RealWarehouse inRealWarehouse;

    /**
     *
     */
    private RealWarehouse outRealWarehouse;

    /**
     * 创建时间
     */
    private Date createTime;
    /**
     * 创建人
     */
    private Long creator;

    /**
     * 是否退货调拨 1.是 0 不是
     */
    private Integer isReturnAllotcate;

    /**
     * 原因列表
     */
    private List<BusinessReasonDTO> reasonList;

    /**
     * sap采购单号
     */
    private String sapPoNo;

    /**
     * SAP下发状态：0-无需下发 1-待下发 2-已下发
     */
    private Integer syncStatus;
    /**
     * sap交货单号
     */
    private String sapOrderCode;
    /**
     * tms派车单号
     */
    private String tmsRecordCode;

    /**
     * sku编号
     */
    private String skuCode;

    /**
     * 1.出入都是中台 2.出中台入非中台  3. 出非中台入中台
     */
    private Integer whType;

    /***
     * 新增类型(1.页面新增 2. excel导入 3.差异调拨)
     */
    private Integer addType;

    /***
     * 是否存在差异(1. 存在差异 0: 不存在差异)
     */
    private Integer isDisparity;

    /***
     * 原始单据Id
     */
    private Long orginId;

    /***
     * 原始单据号
     */
    private String orginRecord;
    
    /**
     * 更新人
     */
    private Long modifier;
    
    /**
     * 修改时间
     */
    private Date updateTime;

    /**
     * 是否质量问题调拨 1.是 0 不是
     */
    private Integer isQualityAllotcate;
    
}
