package com.rome.stock.innerservice.api.dto;

import com.rome.stock.innerservice.domain.entity.SkuQtyUnitBaseE;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @Description 基础sku信息
 * @date 2020/5/26
 */
@Data
public class BaseSkuInfoDTO extends SkuQtyUnitBaseE {

    @ApiModelProperty(value = "merchantId,不传将会用默认的商家id", name = "merchantId" )
    private Long merchantId;

    /**
     * 商品skuId
     */
    @ApiModelProperty(value = "商品skuId")
    private Long skuId;

    /**
     * 商品sku编码
     */
    @ApiModelProperty(value = "商品sku编码")
    private String skuCode;


    @ApiModelProperty(value = "商品名称")
    private String skuName;

    @ApiModelProperty(value = "单位code")
    private String unitCode;

    @ApiModelProperty(value = "单位名称")
    private String unit;


}    
