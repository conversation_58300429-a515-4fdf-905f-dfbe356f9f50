package com.rome.stock.innerservice.api.controller;

import java.util.List;
import java.util.Map;

import com.github.pagehelper.PageInfo;
import com.rome.stock.innerservice.common.RedisUtil;
import com.rome.stock.innerservice.facade.StockOnlineOrderFacade;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;

import com.rome.arch.core.clientobject.Response;
import com.rome.arch.core.exception.RomeException;
import com.rome.arch.util.SpringBeanUtil;
import com.rome.arch.util.controller.RomeController;
import com.rome.stock.innerservice.api.dto.ChannelSales;
import com.rome.stock.innerservice.common.ResCode;
import com.rome.stock.innerservice.constant.ResponseMsg;
import com.rome.stock.innerservice.domain.service.ChannelSalesService;
import com.rome.stock.innerservice.infrastructure.redis.cache.GroupInfoCacheRedis;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import io.swagger.annotations.ApiResponse;
import lombok.extern.slf4j.Slf4j;

import javax.annotation.Resource;

@Slf4j
@RomeController
@RequestMapping("/stock/v1/channelsales")
@Api(tags={"渠道服务接口"})
public class ChannelSalesController {
	@Autowired
	private ChannelSalesService channelSalesService;
	@Resource
	private RedisUtil redisUtil;

	/**
	 * 创建策略组
	 *
	 * @param channelSalesDto
	 * @return
	 */
	@ApiOperation(value = "创建渠道关系", nickname = "createChannelSales", produces = MediaType.APPLICATION_JSON_VALUE)
	@ApiResponse(code = 200, message = "success", response = ChannelSales.class)
	@RequestMapping(value = "/createChannelSales", method = RequestMethod.POST)
	public Response createChannelSales(
			@ApiParam(name = "ChannelSales", value = "渠道") @RequestBody ChannelSales channelSalesDto) {
		try {
			channelSalesService.addChannelSales(channelSalesDto);
			// 清理组信息
			SpringBeanUtil.getBean(GroupInfoCacheRedis.class).delByGroupId(channelSalesDto.getVirtualWarehouseGroupId());
			StockOnlineOrderFacade.delRouteInfoByPriorityTemple(channelSalesDto.getChannelCode());
			// 清理或更新渠道数据
			StockOnlineOrderFacade.clearOrUpdateChannelSales(channelSalesDto.getChannelCode());
			return Response.builderSuccess("success");
		} catch (RomeException e) {
			log.error(e.getMessage(), e);
			return ResponseMsg.EXCEPTION.buildMsg(e.getCode(), e.getMessage());
		} catch (Exception e) {
			log.error(e.getMessage(), e);
			return ResponseMsg.EXCEPTION.buildMsg();
		}
	}

	@ApiOperation(value = "修改渠道", nickname = "modfiyChannelSales", produces = MediaType.APPLICATION_JSON_VALUE)
	@ApiResponse(code = 200, message = "success", response = ChannelSales.class)
	@RequestMapping(value = "/modfiyChannelSales", method = RequestMethod.POST)
	public Response modifyChannelSales(
			@ApiParam(name = "ChannelSales", value = "渠道") @RequestBody ChannelSales channelSaleDto) {
		try {
			channelSalesService.modfiyChannelSales(channelSaleDto);
			// 清理组信息
			SpringBeanUtil.getBean(GroupInfoCacheRedis.class).delByGroupId(channelSaleDto.getVirtualWarehouseGroupId());
			StockOnlineOrderFacade.delRouteInfoByPriorityTemple(channelSaleDto.getChannelCode());
			// 清理或更新渠道数据
			StockOnlineOrderFacade.clearOrUpdateChannelSales(channelSaleDto.getChannelCode());
			return Response.builderSuccess("success");
		} catch (RomeException e) {
			log.error(e.getMessage(), e);
			return ResponseMsg.EXCEPTION.buildMsg(e.getCode(), e.getMessage());
		} catch (Exception e) {
			log.error(e.getMessage(), e);
			return ResponseMsg.EXCEPTION.buildMsg();
		}
	}

	@ApiOperation(value = "获取全部渠道", nickname = "getChannelSalesAll", produces = MediaType.APPLICATION_JSON_VALUE)
	@ApiResponse(code = 200, message = "success", response = ChannelSales.class)
	@RequestMapping(value = "/getChannelSalesAll", method = RequestMethod.GET)
	public Response<List<ChannelSales>> getChannelSalesAll() {
		try {
			List<ChannelSales> channelDto = channelSalesService.getChannelSalesAll();
			return Response.builderSuccess(channelDto);
		} catch (RomeException e) {
			log.error(e.getMessage(), e);
			return ResponseMsg.EXCEPTION.buildMsg(e.getCode(), e.getMessage());
		} catch (Exception e) {
			log.error(e.getMessage(), e);
			return ResponseMsg.EXCEPTION.buildMsg(ResCode.STOCK_ERROR_1003, ResCode.STOCK_ERROR_1003_DESC);
		}
	}

	@ApiOperation(value = "根据策略组id获取渠道", nickname = "getChannelSales", produces = MediaType.APPLICATION_JSON_VALUE)
	@ApiResponse(code = 200, message = "success", response = List.class)
	@RequestMapping(value = "/getChannelSales", method = RequestMethod.POST)
	public Response<PageInfo<ChannelSales>> getChannelSales(@RequestBody ChannelSales channelSalesDto) {
		try {
			PageInfo<ChannelSales> channelSalesList = channelSalesService.getChannelSales(channelSalesDto);
			return Response.builderSuccess(channelSalesList);
		} catch (RomeException e) {
			log.error(e.getMessage(), e);
			return ResponseMsg.EXCEPTION.buildMsg(e.getCode(), e.getMessage());
		} catch (Exception e) {
			log.error(e.getMessage(), e);
			return ResponseMsg.EXCEPTION.buildMsg(ResCode.STOCK_ERROR_1003, ResCode.STOCK_ERROR_1003_DESC);
		}
	}

	@ApiOperation(value = "获取所有未关联策略组的渠道", nickname = "getNoRelateChannelSales", produces = MediaType.APPLICATION_JSON_VALUE)
	@ApiResponse(code = 200, message = "success", response = List.class)
	@RequestMapping(value = "/getNoRelateChannelSales", method = RequestMethod.GET)
	public Response<List<ChannelSales>> getNoRelateChannelSales() {
		try {
			List<ChannelSales> noRelateChannelSalesList = channelSalesService.getNoRelateChannelSales();
			return Response.builderSuccess(noRelateChannelSalesList);
		} catch (RomeException e) {
			log.error(e.getMessage(), e);
			return ResponseMsg.EXCEPTION.buildMsg(e.getCode(), e.getMessage());
		} catch (Exception e) {
			log.error(e.getMessage(), e);
			return ResponseMsg.EXCEPTION.buildMsg(ResCode.STOCK_ERROR_1003, ResCode.STOCK_ERROR_1003_DESC);
		}
	}

	@ApiOperation(value = "根据渠道code查询渠道", nickname = "getChannelSalesByChannelCode", produces = MediaType.APPLICATION_JSON_VALUE)
	@ApiResponse(code = 200, message = "success", response = List.class)
	@RequestMapping(value = "/getChannelSalesByChannelCode", method = RequestMethod.POST)
	public Response<List<ChannelSales>> getChannelSalesByChannelCode(@RequestBody ChannelSales channelSalesDto) {
		try {
			List<ChannelSales> channelSalesList = channelSalesService.getChannelSalesByChannelCode(channelSalesDto);
			return Response.builderSuccess(channelSalesList);
		} catch (RomeException e) {
			log.error(e.getMessage(), e);
			return ResponseMsg.EXCEPTION.buildMsg(e.getCode(), e.getMessage());
		} catch (Exception e) {
			log.error(e.getMessage(), e);
			return ResponseMsg.EXCEPTION.buildMsg(ResCode.STOCK_ERROR_1003, ResCode.STOCK_ERROR_1003_DESC);
		}
	}


	@ApiOperation(value = "根据渠道code批量查询渠道", nickname = "selectChannelSalesByChannelCodes", produces = MediaType.APPLICATION_JSON_VALUE)
	@ApiResponse(code = 200, message = "success", response = List.class)
	@RequestMapping(value = "/selectChannelSalesByChannelCodes", method = RequestMethod.POST)
	public Response<List<ChannelSales>> selectChannelSalesByChannelCodes(@RequestParam("channelCodes")List<String> channelCodes) {
		try {
			List<ChannelSales> channelSalesList = channelSalesService.selectChannelSalesByChannelCodes(channelCodes);
			return Response.builderSuccess(channelSalesList);
		} catch (RomeException e) {
			log.error(e.getMessage(), e);
			return ResponseMsg.EXCEPTION.buildMsg(e.getCode(), e.getMessage());
		} catch (Exception e) {
			log.error(e.getMessage(), e);
			return ResponseMsg.EXCEPTION.buildMsg(ResCode.STOCK_ERROR_1003, ResCode.STOCK_ERROR_1003_DESC);
		}
	}


	@ApiOperation(value = "批量绑定门店和渠道关系(一个渠道绑定到所有门店上)", nickname = "batchBindChannelSalesForAllShop", produces = MediaType.APPLICATION_JSON_VALUE)
	@ApiResponse(code = 200, message = "success", response = List.class)
	@RequestMapping(value = "/batchBindChannelSalesForAllShop", method = RequestMethod.POST)
	public Response batchBindChannelSalesForAllShop(@RequestBody ChannelSales channelSale) {
		String uniqueKey = "batchBindChannelSalesForAllShop" + "_" + channelSale.getChannelCode();
		boolean isLock = false;
		try {
			isLock = redisUtil.lock(uniqueKey, "1", 300);
			if (isLock) {
				Map<String, Object> result = channelSalesService.batchBindChannelSalesForAllShop(channelSale);
				return Response.builderSuccess(result);
			}else {
				return Response.builderFail(ResCode.STOCK_ERROR_1003,"批量绑定门店和渠道关系正在处理中，请稍后再试。。。");
			}
		} catch (RomeException e) {
			log.error(e.getMessage(), e);
			return Response.builderFail(e.getCode(),e.getMessage());
		} catch (Exception e) {
			log.error(e.getMessage(), e);
			return Response.builderFail(ResCode.STOCK_ERROR_1003,e.getMessage());
		}finally {
			if(isLock) {
				redisUtil.unLock(uniqueKey, "1");
			}
		}
	}




	@ApiOperation(value = "删除渠道", nickname = "deleteChannelSales", produces = MediaType.APPLICATION_JSON_VALUE)
	@ApiResponse(code = 200, message = "success")
	@RequestMapping(value = "/deleteChannelSales", method = RequestMethod.GET)
	public Response deleteChannelSales(@RequestParam("id") String id,@RequestParam("modifier")Long modifier) {
		try {
			Long groupId = channelSalesService.deleteChannelSales(Long.parseLong(id),modifier);
			// 清理组信息
			SpringBeanUtil.getBean(GroupInfoCacheRedis.class).delByGroupId(groupId);
			return ResponseMsg.SUCCESS.buildMsg();
		} catch (RomeException e) {
			log.error(e.getMessage(), e);
			return ResponseMsg.FAIL.buildMsg(e.getCode(), e.getMessage());
		} catch (Exception e) {
			log.error(e.getMessage(), e);
			return ResponseMsg.FAIL.buildMsg("500000", e.getMessage());
		}
	}
	
	@ApiOperation(value = "查询渠道列表（排除门店）", nickname = "queryChannelSalesList", produces = MediaType.APPLICATION_JSON_VALUE)
	@ApiResponse(code = 200, message = "success", response = ChannelSales.class)
	@RequestMapping(value = "/queryChannelSalesList", method = RequestMethod.GET)
	public Response<List<ChannelSales>> queryChannelSalesList() {
		try {
			List<ChannelSales> channelDto = channelSalesService.queryChannelSalesList();
			return Response.builderSuccess(channelDto);
		} catch (RomeException e) {
			log.error(e.getMessage(), e);
			return ResponseMsg.EXCEPTION.buildMsg(e.getCode(), e.getMessage());
		} catch (Exception e) {
			log.error(e.getMessage(), e);
			return ResponseMsg.EXCEPTION.buildMsg(ResCode.STOCK_ERROR_1003, ResCode.STOCK_ERROR_1003_DESC);
		}
	}
	
}