package com.rome.stock.innerservice.api.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotBlank;

/**
 * @Description cmp
 * <AUTHOR>
 * @Date 2019/7/14 15:21
 * @Version 1.0
 */
@Data
@NoArgsConstructor
@EqualsAndHashCode
public class CmpInterfaceLogDTO {


    @ApiModelProperty(value = "请求服务")
    @NotBlank(message="请求服务不能为空")
    private String requestService;

    @ApiModelProperty(value = "请求服务内容")
    @NotBlank(message="请求服务内容不能为空")
    private String requestContent;

    @ApiModelProperty(value = "响应内容")
    @NotBlank(message="响应内容不能为空")
    private String responseContent;

    @ApiModelProperty(value = "数据走向")
    private String transferContent;

    @ApiModelProperty(value = "cmp开关：0.中台 1.中台或sap 2.sap")
    private String cmpSwitch ;

    @ApiModelProperty(value = "调用类型 1：cmp调中台  2:中台调cmp")
    private Integer cmpType;

    @ApiModelProperty(value = "0.初始化 1.成功 2.失败")
    private Integer cmpStatus;


}
