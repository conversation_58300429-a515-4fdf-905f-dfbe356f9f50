package com.rome.stock.innerservice.api.dto;

import com.rome.arch.core.dto.DTO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * <AUTHOR>
 * @Description 虚仓库存查询DTO
 * @date 2020/6/10 17:43
 */
@Data
public class QueryVirtualWarehouseStockDTO extends DTO {

    @ApiModelProperty(value="skuId集合")
    @NotNull
    private List<Long> skuIds;

    @ApiModelProperty(value="虚仓编码")
    @NotBlank
    private String virtualWarehouseCode;

    @ApiModelProperty(value="虚仓id",hidden = true)
    private Long virtualWarehouseId;
}    
   