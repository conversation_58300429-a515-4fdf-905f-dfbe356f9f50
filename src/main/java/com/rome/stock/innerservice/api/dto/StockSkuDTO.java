package com.rome.stock.innerservice.api.dto;

import com.rome.stock.common.page.Pagination;
import com.rome.stock.innerservice.infrastructure.dataobject.BatchStockDO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

@Data
public class StockSkuDTO extends Pagination {


    /**
     * 实仓ID
     */
    @ApiModelProperty(value = "实仓ID")
    private Long realWarehouseId;



    @ApiModelProperty(value = "虚拟仓库ID")
    private Long virtualWarehouseId;


    /**
     * 查询类型 1：
     */
    @ApiModelProperty(value = " 查询类型: 1：可用库存大于0， 2：实际库存大于0")
    private Integer type;

    /**
     * 撤柜日期开始时间,格式:2025-01-01
     */
    private String startDate;

    /**
     * 撤柜日期结束时间,格式:2025-01-01
     */
    private String endDate;

    private List<BatchRemovalDayDTO> batchList;
}
