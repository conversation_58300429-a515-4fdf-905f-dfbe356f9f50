/**
 * Filename MqMessageController.java
 * Company 上海来伊份科技有限公司。
 *
 * <AUTHOR>
 * @version
 */
package com.rome.stock.innerservice.api.controller;

import com.rome.arch.core.clientobject.Response;
import com.rome.arch.core.exception.RomeException;
import com.rome.arch.util.controller.RomeController;
import com.rome.stock.common.constants.ResCode;
import com.rome.stock.innerservice.remote.stockCore.facade.StockCoreFacade;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiResponse;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;

import javax.annotation.Resource;

/**
 *
 * <AUTHOR>
 * @since 2023/8/9 10:25
 */
@Slf4j
@RomeController
@RequestMapping("/stock/v1/mq_message")
@Api(tags={"MQ消息"})
public class MqMessageController {

    @Resource
    private StockCoreFacade stockCoreFacade;

    @ApiOperation(value = "根据消息Id,重试消费失败的MQ，在stock-core-service上的", nickname = "retryConsumerByMsgId", produces = MediaType.APPLICATION_JSON_VALUE)
    @ApiResponse(code = 200, message = "success")
    @RequestMapping(value = "/retryConsumerByMsgIdCore", method = RequestMethod.POST)
    public Response retryConsumerByMsgIdCore(@RequestParam("id")Long id) {
        try {
            return stockCoreFacade.retryConsumerByMsgId(id);
        } catch (RomeException e) {
//            log.error(e.getMessage(), e);
            return Response.builderFail(e.getCode(),e.getMessage());
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return Response.builderFail(ResCode.STOCK_ERROR_1001, e.getMessage());
        }
    }

}
