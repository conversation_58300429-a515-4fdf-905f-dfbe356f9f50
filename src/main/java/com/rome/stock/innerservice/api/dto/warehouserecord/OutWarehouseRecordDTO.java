package com.rome.stock.innerservice.api.dto.warehouserecord;

import java.util.List;

import javax.validation.Valid;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

import com.rome.stock.innerservice.common.VmAllocation.AllocationCalQtyRes;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 类OutRecordDTO的实现描述：TODO 类实现描述
 *
 * <AUTHOR> 2020/6/11 18:26
 */
@Data
@EqualsAndHashCode
public class OutWarehouseRecordDTO {

    @ApiModelProperty(value = "单据编码", required = true)
    @NotBlank(message="单据编码不能为空")
    private String recordCode;

    @ApiModelProperty(value = "外部系统单据编码", required = true)
    private String outRecordCode;

    @ApiModelProperty(value = "SAP的交货单号")
    private String sapOrderCode;

    @ApiModelProperty(value = "渠道编码, 有就传,存储在后置单")
    private String channelCode;

    @ApiModelProperty(value = "出库仓库code")
    @NotBlank(message="出库仓库code不能为空")
    private String warehouseCode;


    @ApiModelProperty(value = "完整的仓库编码例 X001-C001")
    private String fullWarehouseCode;


    @ApiModelProperty(value = "出库工厂code")
    @NotBlank(message="出库工厂code不能为空")
    private String factoryCode;


    @ApiModelProperty(value = "单据类型", required = true)
    @NotNull(message="单据类型不能为空")
    private Integer recordType;

    @ApiModelProperty(value = "虚仓编号, 特定业务需要")
    private String virWarehouseCode;

    @ApiModelProperty(value = "是否检查真实库存,目前涵盖场景:质量调拨" , required = true)
    private Boolean checkRealStock;

    @Valid
    @ApiModelProperty(value = "sku数量及明细")
    @NotNull(message="sku数量及明细不能为空")
    private List<RecordDetailDTO> detailList;

    @ApiModelProperty(value = "是否新版930接口(1.是)")
    private String appId;

    @ApiModelProperty(value = "更改明细数量类型：1.部分锁定，2.全部锁定，3.不锁")
    private Integer updateType;

    @ApiModelProperty(value = "do单号集合")
    private List<String> doRecordCodes;

    private Boolean appIdFlag;

    /**
     * 是否强制生成出库单，销售系统专用
     */
    private Boolean isForce;

    @ApiModelProperty(value = "操作时间")
    private String outCreateTime;

    /**
     * 物流/快递公司编码
     */
    private String logisticsCode;

    /**
     * 仓库领用单业务类型：1:普通仓库领用单 2.内采仓库领用单
     */
    private Integer receiveBusinessType;

    /**
     * 内采领用专用字段
     */
    private String outCompanyCode;
    /**
     * 内采领用专用字段
     */
    private String receiveCompanyCode;

    @ApiModelProperty(value = "操作人id")
    private Long operator;


    @ApiModelProperty(value = "是否拆店盘点")
    private Integer isCloseStockPlant;

    @ApiModelProperty(value = "虚仓ID(虚仓标签使用)")
    private Long virtualWarehouseId;

    @ApiModelProperty(value = "分子(批次库存)")
    private Integer molecule;

    @ApiModelProperty(value = "分母(批次库存)")
    private Integer denominator;

    /**
     * 仓库领用单业务类型： 0:普通单据 1:大宗采购
     */
    private Integer purchaseBusinessType;
}
