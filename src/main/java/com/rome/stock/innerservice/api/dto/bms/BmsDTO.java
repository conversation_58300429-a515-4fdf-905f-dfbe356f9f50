package com.rome.stock.innerservice.api.dto.bms;

import lombok.Data;
import lombok.EqualsAndHashCode;

@Data
@EqualsAndHashCode
public class BmsDTO {

    /**
     * 下单方
     */
    private String orderCustCode;

    /**
     * 罗马渠道code
     */
    private String channelCode;

    /**
     * 鲲鹏渠道
     */
    private String kpChannelCode;
//
//    public static BmsDTO init(String orderCustCode){
//        BmsDTO bmsDTO=new BmsDTO();
//        bmsDTO.setOrderCustCode(orderCustCode);
//        return bmsDTO;
//    }
}
