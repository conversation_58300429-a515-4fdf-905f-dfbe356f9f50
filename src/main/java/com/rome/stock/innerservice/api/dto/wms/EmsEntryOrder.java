package com.rome.stock.innerservice.api.dto.wms;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 类EntryOrder的实现描述：入库单
 *
 * <AUTHOR> 2019/4/11 21:27
 */
@Data
@EqualsAndHashCode
public class EmsEntryOrder {

    @ApiModelProperty(value = "sap的入库单号")
    private String sapOrderCode;

    @ApiModelProperty(value = "入库单号")
    private String entryOrderCode;

    @ApiModelProperty(value = "货主编码")
    private String ownerCode;

    @ApiModelProperty(value = "采购单号(当orderType=CGRK时使用)")
    private String purchaseOrderCode;


    @ApiModelProperty(value = "采购单号(当orderType=CGRK时使用) po单号")
    private String purchaseOrderNo;

    @ApiModelProperty(value = "仓库编码(统仓统配等无需ERP指定仓储编码的情况填OTHER)")
    private String warehouseCode;

    @ApiModelProperty(value = "订单创建时间")
    private String orderCreateTime;

    @ApiModelProperty(value = "单据类型(THRK=退货入库;HHRK=换货入库;只传英文编码)")
    private String orderType;

    @ApiModelProperty(value = "预期到货时间")
    private String expectStartTime;

    @ApiModelProperty(value = "最迟预期到货时间")
    private String expectEndTime;

    @ApiModelProperty(value = "物流公司编码")
    private String logisticsCode;

    @ApiModelProperty(value = "物流公司名称")
    private String logisticsName;

    @ApiModelProperty(value = "运单号")
    private String expressCode;

    @ApiModelProperty(value = "编码")
    private String supplierCode;

    @ApiModelProperty(value = "supplierName")
    private String supplierName;

    @ApiModelProperty(value = "timestamp")
    private String timestamp;

    @ApiModelProperty(value = "备注")
    private String remark;

    @ApiModelProperty(value = "出库货主编码")
    private String sourceOwnerCode;

}
