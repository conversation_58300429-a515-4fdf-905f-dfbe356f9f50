package com.rome.stock.innerservice.api.dto.warehouserecord;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotNull;
import java.math.BigDecimal;

/**
 * 类ConfimShopReturnDTO的实现描述：确认退货
 *
 * <AUTHOR> 2019/5/9 17:23
 */
@Data
@EqualsAndHashCode
public class ConfirmShopReturnDTO {

//    @ApiModelProperty(value = "确认提货时间")
//    @NotNull(message="确认提货时间不能为空")
//    private Date pickedTime;

    @ApiModelProperty(value = "出库单号/cmp单号")
    @NotNull(message="出库单号/cmp单号不能为空")
    private String recordCode;

    @ApiModelProperty(value = "来源 1--pos7 其他非pos7")
    private Integer sourceType;


    @ApiModelProperty(value = "类型:1.退货确认，2.派车回调")
    private Integer type;

    @ApiModelProperty(value = "录入人编码")
    private String userCode;

    private BigDecimal coldChainNum;

    private BigDecimal otherContainer;

    private String remark;

    private String remark1;

}
