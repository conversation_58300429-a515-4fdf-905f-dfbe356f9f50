package com.rome.stock.innerservice.api.dto.cmp7;

import java.math.BigDecimal;
import java.util.Date;

import com.alibaba.fastjson.annotation.JSONField;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @description 门店退货结果对象
 * @date 2021/3/2 9:43
 * @throw
 */
@Data
public class ShopReturnResultDTO {
    @ApiModelProperty(value = "单据号")
    @JSONField(name = "BillNo")
    private String BillNo;

    @ApiModelProperty(value = "门店编码")
    @JSONField(name = "OrgCode")
    private String OrgCode;

    @ApiModelProperty(value = "部门ID")
    @JSONField(name = "DepID")
    private Long DepID;

    @ApiModelProperty(value = "部门编码")
    @JSONField(name = "DepCode")
    private String DepCode;

    @ApiModelProperty(value = "部门名称")
    @JSONField(name = "DepName")
    private String DepName;

    @ApiModelProperty(value = "序号")
    @JSONField(name = "SerialNo")
    private Long SerialNo;

    @ApiModelProperty(value = "商品ID")
    @JSONField(name = "PluID")
    private Long PluID;

    @ApiModelProperty(value = "商品编码")
    @JSONField(name = "PluCode")
    private String PluCode;

    @ApiModelProperty(value = "商品名称")
    @JSONField(name = "PluName")
    private String PluName;

    @ApiModelProperty(value = "单位")
    @JSONField(name = "Unit")
    private String Unit;

    @ApiModelProperty(value = "商品条码")
    @JSONField(name = "BarCode")
    private String BarCode;

    @ApiModelProperty(value = "规格")
    @JSONField(name = "Spec")
    private String Spec;

    @ApiModelProperty(value = "特征码编码")
    @JSONField(name = "ExPluCode")
    private String ExPluCode;

    @ApiModelProperty(value = "特征码名称")
    @JSONField(name = "ExPluName")
    private String ExPluName;

    @ApiModelProperty(value = "货号")
    @JSONField(name = "CargoNo")
    private String CargoNo;

    @ApiModelProperty(value = "业务类型")
    @JSONField(name = "YwType")
    private String YwType;

    @ApiModelProperty(value = "退货数量")
    @JSONField(name = "ThCount")
    private BigDecimal ThCount;



    @ApiModelProperty(value = "录入人ID")
    @JSONField(name = "userId")
    private Long UserID;

    @ApiModelProperty(value = "录入人编码")
    @JSONField(name = "UserCode")
    private String UserCode;


    @ApiModelProperty(value = "录入日期")
    @JSONField(name = "LrDate")
    private String LrDate;




    @ApiModelProperty(value = "供应商编码")
    @JSONField(name = "SupCode")
    private String SupCode;

    @ApiModelProperty(value = "提交日期")
    @JSONField(name = "TjDate")
    private Date TjDate;

    @ApiModelProperty(value = "记账日期")
    @JSONField(name = "JzDate")
    private Date JzDate;



    @ApiModelProperty(value = "仓库编码")
    @JSONField(name = "CkCode")
    private String CkCode;



    @ApiModelProperty(value = "备注")
    @JSONField(name = "Remark")
    private String Remark;

    @ApiModelProperty(value = "数据状态")
    @JSONField(name = "DataState")
    private String DataState;

    @ApiModelProperty(value = "退货原因")
    @JSONField(name = "Reason")
    private String Reason;

    @ApiModelProperty(value = "导入标志")
    @JSONField(name = "Tag")
    private Integer Tag;

    @ApiModelProperty(value = "批次号")
    @JSONField(name = "BatchNo")
    private String BatchNo;
}
