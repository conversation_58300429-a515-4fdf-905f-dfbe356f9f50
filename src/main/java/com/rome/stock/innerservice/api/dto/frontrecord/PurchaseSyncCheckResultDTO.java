package com.rome.stock.innerservice.api.dto.frontrecord;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.Valid;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * 采购入库同步校验单（sap调用入参）
 */
@Data
@EqualsAndHashCode
public class PurchaseSyncCheckResultDTO {

	@NotNull(message="质检结果:明细不能为空")
	@ApiModelProperty(value = "质检结果明细")
	@Valid
	private List<PurchaseSyncCheckResultItemDTO> items;

}
