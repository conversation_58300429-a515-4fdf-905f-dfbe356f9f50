package com.rome.stock.innerservice.api.dto.frontrecord;


import com.fasterxml.jackson.annotation.JsonIgnore;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.Valid;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.util.Date;
import java.util.List;

/**
 * 采购入库通知单
 */
@Data
@EqualsAndHashCode
public class OutsourcingOutDTO {
	@JsonIgnore
	private String recordCode;

	@JsonIgnore
	private Integer recordType;

	@NotBlank(message="SAP委外出库单号不能为空")
	@ApiModelProperty(value = "SAP委外出库单号")
	private String outRecordCode;

	@ApiModelProperty(value = "SAP委外出库单SAP创建时间")
	@NotNull(message="创建时间不能为空")
	private Date outCreateTime;

	@NotBlank(message="工厂代码不能为空")
	@ApiModelProperty(value = "工厂代码")
	private String factoryCode;

	@NotBlank(message="工厂名称不能为空")
	@ApiModelProperty(value = "工厂名称")
	private String factoryName;

	@NotBlank(message="仓库编号不能为空")
	@ApiModelProperty(value = "仓库编号")
	private String realWarehouseCode;



	@NotBlank(message="供应商编码不能为空")
	@ApiModelProperty(value = "供应商编码")
	private String supplierCode;

	@NotBlank(message="供应商名称不能为空")
	@ApiModelProperty(value = "供应商名称")
	private String supplierName;

	@ApiModelProperty(value = "备注")
	private String remark;

	@ApiModelProperty(value = "供应商联系人")
	private String supplierContact;

	@Valid
	@ApiModelProperty(value = "sku数量及明细")
	@NotNull(message="sku数量及明细不能为空")
	private List<OutsourcingOutDetailDTO> details;
}
