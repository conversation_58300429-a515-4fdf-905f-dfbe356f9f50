package com.rome.stock.innerservice.api.dto;

import com.rome.stock.innerservice.constant.RealWarehouseTypeVO;
import lombok.Data;

import java.util.List;

@Data
public class RealWarehouseParamDTO extends Pagination {

    private List<VWIdSyncRateDTO> vwIdSyncRateDTOList;
    private Long realWarehouseId;
    private String realWarehouseCode;
    private String realWarehouseOutCode;
    private String factoryCode;
    private String realWarehouseName;
    private List<RealWarehouseAreaAddDTO> realWarehouseAreaAddDTOList;
    //按照不包含的类型查询
    private Integer notInType;
    //根据仓库名和仓库编码模糊查询字段
    private String nameOrCode;
    private Long userId;
    private Long realWarehouseType;
    //门店仓枚举
    private static final Integer SHOP_TYPE =RealWarehouseTypeVO.RW_TYPE_1.getType();

    //仓库层级: 1-一级，2.二级
    private Integer realWarehouseRank;
    private Integer warehouseStoreIdenti;


    private List<String> realWarehouseCodeList;
}
