package com.rome.stock.innerservice.api.controller;

import java.util.List;
import java.util.UUID;

import com.alibaba.fastjson.JSON;
import com.rome.stock.innerservice.api.dto.*;
import com.rome.stock.innerservice.common.RedisUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.*;

import com.github.pagehelper.PageInfo;
import com.rome.arch.core.clientobject.Response;
import com.rome.arch.core.exception.RomeException;
import com.rome.arch.util.controller.RomeController;
import com.rome.stock.innerservice.api.dto.allocation.PredictReturnExportTemplate;
import com.rome.stock.innerservice.api.dto.frontrecord.PredictReturnDTO;
import com.rome.stock.innerservice.api.dto.frontrecord.PurchaseDetailExportFormDTO;
import com.rome.stock.innerservice.api.dto.qry.WareHouseRecordCondition;
import com.rome.stock.innerservice.common.ResCode;
import com.rome.stock.innerservice.constant.ResponseMsg;
import com.rome.stock.innerservice.domain.repository.warehouserecord.RealWarehouseWmsConfigRespository;
import com.rome.stock.innerservice.domain.service.WarehouseRecordService;
import com.rome.stock.innerservice.facade.BatchStockFacade;
import com.rome.stock.innerservice.remote.orderTrack.facade.OrderTrackFacade;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import io.swagger.annotations.ApiResponse;
import lombok.extern.slf4j.Slf4j;

/**
 * 出入库单
 */
@Slf4j
@RomeController
@RequestMapping("/stock/v1/warehouse_record")
@Api(tags={"出入库单管理"})
public class WarehouseRecordController {

    @Autowired
    private WarehouseRecordService warehouseRecordService;
    @Autowired
    private OrderTrackFacade orderTrackFacade;

    @Autowired
    private RealWarehouseWmsConfigRespository realWarehouseWmsConfigRespository;

    @Autowired
    private RedisUtil redisUtil;

    @ApiOperation(value = "查询入库单列表", nickname = "query_in_warehouse_record_list", produces = MediaType.APPLICATION_JSON_VALUE)
    @ApiResponse(code = 200, message = "success", response = List.class)
    @RequestMapping(value = "/in_warehouse_record/queryWarehouseRecordList", method = RequestMethod.POST)
    public Response<PageInfo<WarehouseRecordPageDTO>>  queryWarehouseRecordList(@ApiParam(name = "inRecord", value = "dto") @RequestBody WarehouseRecordPageDTO warehouseRecord) {
        try {
            PageInfo<WarehouseRecordPageDTO> personPageInfo = warehouseRecordService.queryWarehouseRecordPage(warehouseRecord);
            return Response.builderSuccess(personPageInfo);
        } catch (RomeException e) {
            log.error(e.getMessage(), e);
            return Response.builderFail(e.getCode(),e.getMessage());
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return Response.builderFail(ResCode.STOCK_ERROR_1003,ResCode.STOCK_ERROR_1003_DESC);
        }
    }

    @ApiOperation(value = "查询待同步入库单列表", nickname = "query_need_in_warehouse_record_list", produces = MediaType.APPLICATION_JSON_VALUE)
    @ApiResponse(code = 200, message = "success", response = List.class)
    @RequestMapping(value = "/wms/queryNeedSyncInWarehouseRecordList", method = RequestMethod.POST)
    public Response<List<WarehouseRecordPageDTO>> queryNeedSyncInWarehouseRecordList(@ApiParam(name = "inRecord", value = "dto") @RequestBody WarehouseRecordPageDTO warehouseRecord) {
        try {

            List<WarehouseRecordPageDTO> personPageInfo = warehouseRecordService.queryNeedSyncInWarehouseRecordList(warehouseRecord);
            return Response.builderSuccess(personPageInfo);
        } catch (RomeException e) {
            log.error(e.getMessage(), e);
            return Response.builderFail(e.getCode(),e.getMessage());
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return Response.builderFail(ResCode.STOCK_ERROR_1003,e.getMessage());
        }
    }

    @ApiOperation(value = "查询待同步出库单列表", nickname = "query_need_out_warehouse_record_list", produces = MediaType.APPLICATION_JSON_VALUE)
    @ApiResponse(code = 200, message = "success", response = List.class)
    @RequestMapping(value = "/wms/queryNeedSyncOutWarehouseRecordList", method = RequestMethod.POST)
    public Response<List<WarehouseRecordPageDTO>> queryNeedSyncOutWarehouseRecordList(@ApiParam(name = "inRecord", value = "dto") @RequestBody WarehouseRecordPageDTO warehouseRecord) {
        long time = System.currentTimeMillis();
        try {
            List<WarehouseRecordPageDTO> personPageInfo = warehouseRecordService.queryNeedSyncOutWarehouseRecordList(warehouseRecord);
            log.info("查询待同步出库单列表总耗时:{},参数{}" , (System.currentTimeMillis() - time), JSON.toJSONString(warehouseRecord) );
            return Response.builderSuccess(personPageInfo);
        } catch (RomeException e) {
            log.error(e.getMessage(), e);
            return Response.builderFail(e.getCode(),e.getMessage());
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return Response.builderFail(ResCode.STOCK_ERROR_1003,ResCode.STOCK_ERROR_1003_DESC);
        }
    }

    @ApiOperation(value = "查询出入库单详情列表", nickname = "query_warehouse_record_detail_list", produces = MediaType.APPLICATION_JSON_VALUE)
    @ApiResponse(code = 200, message = "success", response = List.class)
    @RequestMapping(value = "/warehouse_record/queryWarehouseRecordDetailList", method = RequestMethod.GET)
    public Response<List<WarehouseRecordDetailDTO>> queryWarehouseRecordDetailList(@ApiParam(name = "warehouseId", value = "warehouseId") @RequestParam("warehouseRecordId") Long warehouseRecordId) {
        try {
            List<WarehouseRecordDetailDTO> list = warehouseRecordService.queryWarehouseRecordDetailList(warehouseRecordId);
            return Response.builderSuccess(list);
        } catch (RomeException e) {
            log.error(e.getMessage(), e);
            return Response.builderFail(e.getCode(),e.getMessage());
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return Response.builderFail(ResCode.STOCK_ERROR_1003,ResCode.STOCK_ERROR_1003_DESC);
        }
    }


    @ApiOperation(value = "wms查询出/入库单列表，每次限制拉取100条", nickname = "query_out_warehouse_record_list", produces = MediaType.APPLICATION_JSON_VALUE)
    @ApiResponse(code = 200, message = "success", response = List.class)
    @RequestMapping(value = "/wms/queryWarehouseRecordList", method = RequestMethod.POST)
    public Response<List<WarehouseRecordDTO>> queryWareHouseRecordList(@RequestBody WareHouseRecordCondition wareHouseRecordCondition){
        try{
            List<WarehouseRecordDTO> pageInfo = warehouseRecordService.queryWareHouseRecordList(wareHouseRecordCondition);
            return Response.builderSuccess(pageInfo);
        }catch (RomeException e){
            log.error(e.getMessage(),e);

            return Response.builderFail(e.getCode(),e.getMessage());
        }catch (Exception e){
            log.error(e.getMessage(), e);
            return Response.builderFail(ResCode.STOCK_ERROR_1003,ResCode.STOCK_ERROR_1003_DESC);
        }
    }

    @ApiOperation(value = "更新出入库同步wms状态", nickname = "update_warehouse_record_sync_status", produces = MediaType.APPLICATION_JSON_VALUE)
    @ApiResponse(code = 200, message = "success", response = List.class)
    @RequestMapping(value = "/wms/updateSyncStatus", method = RequestMethod.POST)
    public Response<Boolean> updateWarehouseRecordSyncStatus(@RequestBody List<String> recordCodeList){
        try{
            boolean updateResult = warehouseRecordService.updateRecordSyncStatusToSynchronized(recordCodeList);
            if(updateResult){
                return Response.builderSuccess(Boolean.TRUE);
            }
            return Response.builderFail(ResCode.STOCK_ERROR_1001,ResCode.STOCK_ERROR_1001_DESC);
        }catch (RomeException e){
            log.error(e.getMessage(),e);
            return Response.builderFail(e.getCode(),e.getMessage());
        }catch (Exception e){
            log.error(e.getMessage(), e);
            return Response.builderFail(ResCode.STOCK_ERROR_1003,ResCode.STOCK_ERROR_1003_DESC);
        }
    }

    @ApiOperation(value = "更新出入库同步wms失败时间", nickname = "update_warehouse_record_sync_fail_time", produces = MediaType.APPLICATION_JSON_VALUE)
    @ApiResponse(code = 200, message = "success", response = List.class)
    @RequestMapping(value = "/wms/updateSyncWmsFailTime", method = RequestMethod.POST)
    public Response<Boolean> updateSyncWmsFailTime(@RequestBody List<String> recordCode){
        try{
            warehouseRecordService.updateRecordSyncWmsFailTime(recordCode);
            return Response.builderSuccess(Boolean.TRUE);
        }catch (RomeException e){
            log.error(e.getMessage(),e);
            return Response.builderFail(e.getCode(),e.getMessage());
        }catch (Exception e){
            log.error(e.getMessage(), e);
            return Response.builderFail(ResCode.STOCK_ERROR_1003,ResCode.STOCK_ERROR_1003_DESC);
        }
    }

    @ApiOperation(value = "更新出入库同步wms失败时间及状态", nickname = "update__sync_fail_time_and_status", produces = MediaType.APPLICATION_JSON_VALUE)
    @ApiResponse(code = 200, message = "success", response = List.class)
    @RequestMapping(value = "/wms/updateSyncWmsFailTimeAndStatus", method = RequestMethod.POST)
    public Response<Boolean> updateSyncWmsFailTimeAndStatus(@RequestBody String recordCode){
        try{
            boolean rs = warehouseRecordService.updateSyncWmsFailTimeAndStatus(recordCode);
            return Response.builderSuccess(rs);
        }catch (RomeException e){
            log.error(e.getMessage(),e);
            return Response.builderFail(e.getCode(),e.getMessage());
        }catch (Exception e){
            log.error(e.getMessage(), e);
            return Response.builderFail(ResCode.STOCK_ERROR_1003,ResCode.STOCK_ERROR_1003_DESC);
        }
    }

    @ApiOperation(value = "wms出库单回调", nickname = "warehouse_record_out_wms_callback", produces = MediaType.APPLICATION_JSON_VALUE)
    @ApiResponse(code = 200, message = "success", response = List.class)
    @RequestMapping(value = "/wms/outRecordCallBack", method = RequestMethod.POST)
    public Response outRecordCallBack(@RequestBody WarehouseRecordDTO warehouseRecord){
        String key = "wmsOut_" + warehouseRecord.getRecordCode();
        String clientId = UUID.randomUUID().toString();
        try {
            if (!redisUtil.lock(key, clientId, 120)) {
                return Response.builderFail(ResCode.STOCK_ERROR_1003, "并发异常：" + key);
            }
            warehouseRecordService.wmsOutRecordCallBack(warehouseRecord);
            return ResponseMsg.SUCCESS.buildMsg();
        } catch (RomeException e) {
            log.error(e.getMessage(), e);
            return Response.builderFail(e.getCode(), e.getMessage());
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return Response.builderFail(ResCode.STOCK_ERROR_1003, e.getMessage());
        } finally {
            redisUtil.unLock(key, clientId);
        }
    }

    @ApiOperation(value = "退货预入库", nickname = "warehouse_record_predict_return", produces = MediaType.APPLICATION_JSON_VALUE)
    @ApiResponse(code = 200, message = "success", response = List.class)
    @RequestMapping(value = "/wms/predictReturn", method = RequestMethod.POST)
    public Response predictReturn(@RequestBody PredictReturnDTO predictReturnDTO){
        try{
            Boolean aBoolean = warehouseRecordService.predictReturn(predictReturnDTO);
            return ResponseMsg.SUCCESS.buildMsg();
        }catch (RomeException e){
            log.error(e.getMessage(),e);
            return Response.builderFail(e.getCode(),e.getMessage());
        }catch (Exception e){
            log.error(e.getMessage(), e);
            return Response.builderFail(ResCode.STOCK_ERROR_1003,ResCode.STOCK_ERROR_1003_DESC);
        }
    }

    @ApiOperation(value = "查询仓库系统归属仓库", nickname = "getRealWarehouseWmsConfigByWmsCodes", produces = MediaType.APPLICATION_JSON_VALUE)
    @ApiResponse(code = 200, message = "success", response = List.class)
    @RequestMapping(value = "/getRealWarehouseWmsConfigByWmsCodes", method = RequestMethod.POST)
    public Response<List<Long>> getRealWarehouseWmsConfigByWmsCodes(@RequestParam(value = "wmsCodes") List<Integer> wmsCodes) {
        try {
            List<Long> data = realWarehouseWmsConfigRespository.getRealWarehouseWmsConfigByWmsCodes(wmsCodes);
            return ResponseMsg.SUCCESS.buildMsg(data);
        } catch (RomeException e) {
            log.error(e.getMessage(), e);
            return ResponseMsg.FAIL.buildMsg(e.getCode(),e.getMessage());
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return ResponseMsg.EXCEPTION.buildMsg(ResCode.STOCK_ERROR_1003_DESC,ResCode.STOCK_ERROR_1003_DESC);
        }
    }

    @ApiOperation(value = "查询仓库系统归属仓库排除禁用的", nickname = "getRealWarehouseWmsConfigByWmsCodesAndStatus", produces = MediaType.APPLICATION_JSON_VALUE)
    @ApiResponse(code = 200, message = "success", response = List.class)
    @RequestMapping(value = "/getRealWarehouseWmsConfigByWmsCodesAndStatus", method = RequestMethod.POST)
    public Response<List<Long>> getRealWarehouseWmsConfigByWmsCodesAndStatus(@RequestParam(value = "wmsCodes") List<Integer> wmsCodes) {
        try {
            List<Long> data = realWarehouseWmsConfigRespository.getRealWarehouseWmsConfigByWmsCodesAndStatus(wmsCodes);
            return ResponseMsg.SUCCESS.buildMsg(data);
        } catch (RomeException e) {
            log.error(e.getMessage(), e);
            return ResponseMsg.FAIL.buildMsg(e.getCode(),e.getMessage());
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return ResponseMsg.EXCEPTION.buildMsg(ResCode.STOCK_ERROR_1003_DESC,ResCode.STOCK_ERROR_1003_DESC);
        }
    }

    @ApiOperation(value = "查询单据明细", nickname = "queryWarehouseRecordDetailListByCode", produces = MediaType.APPLICATION_JSON_VALUE)
    @ApiResponse(code = 200, message = "success", response = List.class)
    @RequestMapping(value = "/wms/queryWarehouseRecordDetailListByCode", method = RequestMethod.POST)
    public Response<List<WarehouseRecordDetailDTO>> queryWarehouseRecordDetailListByCode(@ApiParam(name = "recordCode", value = "recordCode") String recordCode) {
        try {
            List<WarehouseRecordDetailDTO> recordList = warehouseRecordService.queryWarehouseRecordDetailListByCode(recordCode);
            return Response.builderSuccess(recordList);
        } catch (RomeException e) {
            log.error(e.getMessage(), e);
            return Response.builderFail(e.getCode(),e.getMessage());
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return Response.builderFail(ResCode.STOCK_ERROR_1003,ResCode.STOCK_ERROR_1003_DESC);
        }
    }
    @ApiOperation(value = "根据出库单保存订单轨迹", nickname = "saveOrderTrackInfo", produces = MediaType.APPLICATION_JSON_VALUE)
    @ApiResponse(code = 200, message = "success", response = List.class)
    @RequestMapping(value = "/wms/saveOrderTrackInfo", method = RequestMethod.POST)
    public Response saveOrderTrackInfo(@ApiParam(name = "saveOrderTrackInfo", value = "dto") @RequestBody String  recordCode) {
        try {
            orderTrackFacade.saveByRecordCode(recordCode,"订单已经进入仓库准备出库" ,"");
            return Response.builderSuccess("");
        } catch (RomeException e) {
            log.error(e.getMessage(), e);
            return Response.builderFail(e.getCode(),e.getMessage());
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return Response.builderFail(ResCode.STOCK_ERROR_1003,ResCode.STOCK_ERROR_1003_DESC);
        }
    }

    @ApiOperation(value = "根据运单号查询订单列表")
    @ApiResponse(code = 200, message = "success", response = List.class)
    @GetMapping(value = "/queryOrderNo/{expressCode}")
    public Response<List<String>> queryOrderNoByExpressCode(@PathVariable("expressCode")String expressCode){
        try{
            return Response.builderSuccess(orderTrackFacade.queryOrderNoList(expressCode));
        }catch (RomeException e){
            log.error(e.getMessage(), e);
            return Response.builderFail(e.getCode(),e.getMessage());
        }catch (Exception e){
            log.error(e.getMessage(), e);
            return Response.builderFail(ResCode.STOCK_ERROR_9049,ResCode.STOCK_ERROR_9049_DESC);
        }
    }

    /**
     * 预入库报表----查询
     * @param purchaseDetailExportFormDTO
     * @return
     */
    @ApiOperation(value = "退货预入库报表查询", nickname = "predictReturnReportForm", produces = MediaType.APPLICATION_JSON_VALUE)
    @ApiResponse(code = 200, message = "success", response = List.class)
    @RequestMapping(value = "/wms/predictReturnReportForm", method = RequestMethod.POST)
    public Response predictReturnReportForm(@RequestBody PurchaseDetailExportFormDTO purchaseDetailExportFormDTO){
        try{
            PageInfo<PurchaseDetailExportFormDTO> page=warehouseRecordService.predictReturnReportForm(purchaseDetailExportFormDTO);
            return ResponseMsg.SUCCESS.buildMsg(page);
        }catch (RomeException e){
            log.error(e.getMessage(),e);
            return Response.builderFail(e.getCode(),e.getMessage());
        }catch (Exception e){
            log.error(e.getMessage(), e);
            return Response.builderFail(ResCode.STOCK_ERROR_1003,ResCode.STOCK_ERROR_1003_DESC);
        }
    }


    @ApiOperation(value = "导出退货预入库报表", produces = MediaType.APPLICATION_JSON_VALUE)
    @ApiResponse(code = 200, message = "success", response = List.class)
    @RequestMapping(value = "/exportPredictReturnReportForm", method = RequestMethod.POST)
    public Response exportPredictReturnReportForm(@RequestBody PurchaseDetailExportFormDTO purchaseDetailExportFormDTO) {
        try {
            List<PredictReturnExportTemplate> exportData = warehouseRecordService.exportPredictReturnReportForm(purchaseDetailExportFormDTO);
            return Response.builderSuccess(exportData);
        } catch (RomeException e) {
            log.error(e.getMessage(), e);
            return Response.builderFail(e.getCode(),e.getMessage());
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return Response.builderFail(ResCode.STOCK_ERROR_1003_DESC,ResCode.STOCK_ERROR_1003_DESC);
        }
    }

    @ApiOperation(value = "根据后置单明细ID批量查询后置单明细", nickname = "queryWarehouseRecordDetailListByIds", produces = MediaType.APPLICATION_JSON_VALUE)
    @ApiResponse(code = 200, message = "success", response = List.class)
    @RequestMapping(value = "/warehouse_record/queryWarehouseRecordDetailListByIds", method = RequestMethod.GET)
    public Response<List<WarehouseRecordDetailDTO>> queryWarehouseRecordDetailListByIds(
            @ApiParam(name = "warehouseRecordIds", value = "warehouseRecordIds") @RequestParam("warehouseRecordIds") List<Long> warehouseRecordIds) {
        try {
            List<WarehouseRecordDetailDTO> list = warehouseRecordService.queryWarehouseRecordDetailListByIds(warehouseRecordIds);
            return Response.builderSuccess(list);
        } catch (RomeException e) {
            log.error(e.getMessage(), e);
            return Response.builderFail(e.getCode(),e.getMessage());
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return Response.builderFail(ResCode.STOCK_ERROR_1003,e.getMessage());
        }
    }

    @ApiOperation(value = "根据后置单明细code批量查询后置单明细", nickname = "queryWarehouseRecordDetailListByCodes", produces = MediaType.APPLICATION_JSON_VALUE)
    @ApiResponse(code = 200, message = "success", response = List.class)
    @PostMapping(value = "/detail/list")
    public Response<List<WarehouseRecordDetailDTO>> queryWarehouseRecordDetailListByCodes(
            @ApiParam(name = "recordCodeList", value = "recordCodeList") @RequestParam("recordCodeList") List<String> recordCodeList) {
        try {
            List<WarehouseRecordDetailDTO> list = warehouseRecordService.queryWarehouseRecordDetailListByCodes(recordCodeList);
            return Response.builderSuccess(list);
        } catch (RomeException e) {
            log.error(e.getMessage(), e);
            return Response.builderFail(e.getCode(),e.getMessage());
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return Response.builderFail(ResCode.STOCK_ERROR_1003,e.getMessage());
        }
    }


    @ApiOperation(value = "根据后置单号批量查询后置单附加信息表", nickname = "queryWrAdditionInfoListByRecordCode", produces = MediaType.APPLICATION_JSON_VALUE)
    @ApiResponse(code = 200, message = "success", response = List.class)
    @RequestMapping(value = "/warehouse_record/queryWrAdditionInfoListByRecordCode", method = RequestMethod.GET)
    public Response<List<WrAdditionInfoDTO>> queryWrAdditionInfoListByRecordCode(
            @ApiParam(name = "recordList", value = "recordList") @RequestParam("recordList") List<String> recordList) {
        try {
            List<WrAdditionInfoDTO> list = warehouseRecordService.queryWrAdditionInfoListByRecordCode(recordList);
            return Response.builderSuccess(list);
        } catch (RomeException e) {
            log.error(e.getMessage(), e);
            return Response.builderFail(e.getCode(),e.getMessage());
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return Response.builderFail(ResCode.STOCK_ERROR_1003,e.getMessage());
        }
    }

    @ApiOperation(value = "根据后置单号批量查询后置单信息", nickname = "queryWarehouseRecordByCodeList", produces = MediaType.APPLICATION_JSON_VALUE)
    @ApiResponse(code = 200, message = "success", response = List.class)
    @RequestMapping(value = "/queryWarehouseRecordByCodeList", method = RequestMethod.GET)
    public Response<List<WarehouseRecordDTO>> queryWarehouseRecordByCodeList(@ApiParam(name = "recordCodeList", value = "recordCodeList")@RequestParam("recordCodeList")List<String> recordCodeList){
        try {
            List<WarehouseRecordDTO> warehouseRecordDTOS = warehouseRecordService.queryWarehouseRecordByCodeList(recordCodeList);
            return Response.builderSuccess(warehouseRecordDTOS);
        } catch (RomeException e) {
            log.error(e.getMessage(), e);
            return Response.builderFail(e.getCode(),e.getMessage());
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return Response.builderFail(ResCode.STOCK_ERROR_1003,e.getMessage());
        }
    }


    @ApiOperation(value = "推送仓库成功后发送消息", nickname = "sendWmsSuccMessage", produces = MediaType.APPLICATION_JSON_VALUE)
    @ApiResponse(code = 200, message = "success", response = List.class)
    @RequestMapping(value = "/wms/sendWmsSuccMessage", method = RequestMethod.POST)
    public Response sendWmsSuccMessage(@RequestBody SendWmsMessageDTO sendWmsMessageDTO){
        try{
            warehouseRecordService.sendWmsSuccMessage(sendWmsMessageDTO);
            return ResponseMsg.SUCCESS.buildMsg();
        }catch (RomeException e){
            log.error(e.getMessage(),e);
            return Response.builderFail(e.getCode(),e.getMessage());
        }catch (Exception e){
            log.error(e.getMessage(), e);
            return Response.builderFail(ResCode.STOCK_ERROR_1003,ResCode.STOCK_ERROR_1003_DESC);
        }
    }


    @ApiOperation(value = "根据后置单明细单号和skuCode批量查询后置单明细", nickname = "queryWarehouseRecordDetailListByRecordCodeAndSkuCode", produces = MediaType.APPLICATION_JSON_VALUE)
    @ApiResponse(code = 200, message = "success", response = List.class)
    @RequestMapping(value = "/queryWarehouseRecordDetailListByRecordCodeAndSkuCode", method = RequestMethod.GET)
    public Response<List<WarehouseRecordDetailDTO>> queryWarehouseRecordDetailListByRecordCodeAndSkuCode(
            @ApiParam(name = "recordCode", value = "recordCode") @RequestParam("recordCode") String recordCode,
            @ApiParam(name = "skuCode", value = "skuCode") @RequestParam("skuCode") String skuCode) {
        try {
            List<WarehouseRecordDetailDTO> list = warehouseRecordService.queryWarehouseRecordDetailListByRecordCodeAndSkuCode(recordCode,skuCode);
            return Response.builderSuccess(list);
        } catch (RomeException e) {
            log.error(e.getMessage(), e);
            return Response.builderFail(e.getCode(),e.getMessage());
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return Response.builderFail(ResCode.STOCK_ERROR_1003,e.getMessage());
        }
    }
    
    @ApiOperation(value = "用于wms下发时，要求锁定批次，一般出库单", nickname = "wmsPushLockBatchStock", produces = MediaType.APPLICATION_JSON_VALUE)
    @ApiResponse(code = 200, message = "success", response = List.class)
    @RequestMapping(value = "/wms/wmsPushLockBatchStock", method = RequestMethod.POST)
    public Response<List<BatchStockDTO>> wmsPushLockBatchStock(@ApiParam(name = "inRecord", value = "dto") @RequestBody WarehouseRecordPageDTO warehouseRecord, @RequestParam("reverse") Boolean reverse) {
    	boolean isSucc = false;
    	String json = JSON.toJSONString(warehouseRecord);
    	String reponseContent = "";
    	try {
            List<BatchStockDTO> personPageInfo = warehouseRecordService.wmsPushLockBatchStock(warehouseRecord, reverse);
            isSucc = true;
            reponseContent = JSON.toJSONString(personPageInfo);
            return Response.builderSuccess(personPageInfo);
        } catch (RomeException e) {
            log.error(e.getMessage(), e);
            return Response.builderFail(e.getCode(),e.getMessage());
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return Response.builderFail(ResCode.STOCK_ERROR_1003,e.getMessage());
        } finally {
        	log.warn(BatchStockFacade.callByKibanaLogInterfaceLog(warehouseRecord.getRecordCode(), "wmsPushLockBatchStock", json, reponseContent, isSucc, "用于wms下发时，要求锁定批次" + reverse));
		}
    }

}

