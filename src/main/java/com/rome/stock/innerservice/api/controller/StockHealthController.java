package com.rome.stock.innerservice.api.controller;

import com.github.pagehelper.PageInfo;
import com.rome.arch.core.clientobject.Response;
import com.rome.arch.core.exception.RomeException;
import com.rome.arch.util.controller.RomeController;
import com.rome.stock.innerservice.common.ResCode;
import com.rome.stock.innerservice.constant.ResponseMsg;
import com.rome.stock.innerservice.domain.service.StockHealthService;
import com.rome.stock.innerservice.remote.bigdata.dto.health.*;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiResponse;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;

/**
 * 库存健康
 */
@Slf4j
@Api(tags = "库存健康")
@RequestMapping("/stock/v1/stock_health")
@RomeController
public class StockHealthController {

    @Resource
    private StockHealthService stockHealthService;

    @ApiOperation(value = "根据商品编码库存健康度状态查询商品所在省市", nickname = "getAreaBySku", produces = MediaType.APPLICATION_JSON_VALUE)
    @ApiResponse(code = 200, message = "success")
    @RequestMapping(value = "/getAreaBySku", method = RequestMethod.POST)
    public Response<HealthResDTO> getAreaBySku(@RequestBody HealthQueryDTO healthQueryDTO) {
        try {
            HealthResDTO res= stockHealthService.getAreaBySku(healthQueryDTO);
            return Response.builderSuccess(res);
        } catch (RomeException e) {
            log.error(e.getMessage(), e);
            return ResponseMsg.EXCEPTION.buildMsg(e.getCode(), e.getMessage());
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return Response.builderFail(ResCode.STOCK_ERROR_1003,null==e.getMessage()?e.toString():e.getMessage());
        }
    }

    @ApiOperation(value = "根据工厂编码或门店编码库存健康状态商品分层查询商品库存健康明细列表", nickname = "getFactorySkuList", produces = MediaType.APPLICATION_JSON_VALUE)
    @ApiResponse(code = 200, message = "success")
    @RequestMapping(value = "/getFactorySkuList", method = RequestMethod.POST)
    public Response<FactorySkuListVO> getFactorySkuList(@RequestBody HealthSkuFactoryQueryDTO healthSkuFactoryQueryDTO) {
        try {
            FactorySkuListVO res= stockHealthService.getFactorySkuList(healthSkuFactoryQueryDTO);
            return Response.builderSuccess(res);
        } catch (RomeException e) {
            log.error(e.getMessage(), e);
            return ResponseMsg.EXCEPTION.buildMsg(e.getCode(), e.getMessage());
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return Response.builderFail(ResCode.STOCK_ERROR_1003,null==e.getMessage()?e.toString():e.getMessage());
        }
    }

    @ApiOperation(value = "根据商品编码库存健康度状态查询商品所在省市", nickname = "getHealthByFactoryCode", produces = MediaType.APPLICATION_JSON_VALUE)
    @ApiResponse(code = 200, message = "success")
    @RequestMapping(value = "/getHealthByFactoryCode", method = RequestMethod.POST)
    public Response<HealthResDTO> getHealthByFactoryCode(@RequestBody HealthQueryDTO healthQueryDTO) {
        try {
            HealthResDTO res= stockHealthService.getHealthByFactoryCode(healthQueryDTO);
            return Response.builderSuccess(res);
        } catch (RomeException e) {
            log.error(e.getMessage(), e);
            return ResponseMsg.EXCEPTION.buildMsg(e.getCode(), e.getMessage());
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return Response.builderFail(ResCode.STOCK_ERROR_1003,null==e.getMessage()?e.toString():e.getMessage());
        }
    }

    @ApiOperation(value = "根据商品编码查询库存健康度", nickname = "getHealthBySku", produces = MediaType.APPLICATION_JSON_VALUE)
    @ApiResponse(code = 200, message = "success")
    @RequestMapping(value = "/getHealthBySku", method = RequestMethod.POST)
    public Response<HealthResDTO> getHealthBySku(@RequestBody HealthQueryDTO healthQueryDTO) {
        try {
            HealthResDTO res= stockHealthService.getHealthBySku(healthQueryDTO);
            return Response.builderSuccess(res);
        } catch (RomeException e) {
            log.error(e.getMessage(), e);
            return ResponseMsg.EXCEPTION.buildMsg(e.getCode(), e.getMessage());
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return Response.builderFail(ResCode.STOCK_ERROR_1003,null==e.getMessage()?e.toString():e.getMessage());
        }
    }

    @ApiOperation(value = "根据商品编码查询库存健康度", nickname = "getHealthBySku2", produces = MediaType.APPLICATION_JSON_VALUE)
    @ApiResponse(code = 200, message = "success")
    @RequestMapping(value = "/getHealthBySku2", method = RequestMethod.POST)
    public Response<PageInfo<HealthListResDTO>> getHealthBySku2(@RequestBody HealthQueryDTO healthQueryDTO) {
        try {
            HealthResDTO res= stockHealthService.getHealthBySku(healthQueryDTO);
            HealthResDTO areaRes= stockHealthService.getAreaBySku(healthQueryDTO);
            List<HealthListResDTO> resList=new ArrayList<>();
            for(ProvinceResDTO provinceResDTO:areaRes.getProvinceList().getList()){
                HealthListResDTO temp=new HealthListResDTO();
                BeanUtils.copyProperties(res.getStockHealthList().get(0),temp);
                temp.setProvinceCode(provinceResDTO.getProvinceCode());
                temp.setProvinceName(provinceResDTO.getProvinceName());
                resList.add(temp);
            }
            PageInfo page=new PageInfo();
            page.setTotal(areaRes.getProvinceList().getTotal());
            page.setList(resList);
            return Response.builderSuccess(page);
        } catch (RomeException e) {
            log.error(e.getMessage(), e);
            return ResponseMsg.EXCEPTION.buildMsg(e.getCode(), e.getMessage());
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return Response.builderFail(ResCode.STOCK_ERROR_1003,null==e.getMessage()?e.toString():e.getMessage());
        }
    }

    @ApiOperation(value = "根据商品编码库存健康度状态查询商品所在省市", nickname = "getHealthBySkuFactory", produces = MediaType.APPLICATION_JSON_VALUE)
    @ApiResponse(code = 200, message = "success")
    @RequestMapping(value = "/getHealthBySkuFactory", method = RequestMethod.POST)
    public Response<FactorySkuDetailVO> getHealthBySkuFactory(@RequestBody HealthQueryDTO healthQueryDTO) {
        try {
            FactorySkuDetailVO res= stockHealthService.getHealthBySkuFactory(healthQueryDTO);
            return Response.builderSuccess(res);
        } catch (RomeException e) {
            log.error(e.getMessage(), e);
            return ResponseMsg.EXCEPTION.buildMsg(e.getCode(), e.getMessage());
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return Response.builderFail(ResCode.STOCK_ERROR_1003,null==e.getMessage()?e.toString():e.getMessage());
        }
    }

    @ApiOperation(value = "根据商品编码,省，库存健康状态查询商品库存健康明细", nickname = "getHealthBySkuProvince", produces = MediaType.APPLICATION_JSON_VALUE)
    @ApiResponse(code = 200, message = "success")
    @RequestMapping(value = "/getHealthBySkuProvince", method = RequestMethod.POST)
    public Response<SkuProvinceDTO> getHealthBySkuProvince(@RequestBody HealthQueryDTO healthQueryDTO) {
        try {
            SkuProvinceDTO res= stockHealthService.getHealthBySkuProvince(healthQueryDTO);
            return Response.builderSuccess(res);
        } catch (RomeException e) {
            log.error(e.getMessage(), e);
            return ResponseMsg.EXCEPTION.buildMsg(e.getCode(), e.getMessage());
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return Response.builderFail(ResCode.STOCK_ERROR_1003,null==e.getMessage()?e.toString():e.getMessage());
        }
    }

    @ApiOperation(value = "根据工厂或门店编码和库存健康状态查询商品库存健康状态(按商品分层分组)", nickname = "getHealthClassByFactoryCode", produces = MediaType.APPLICATION_JSON_VALUE)
    @ApiResponse(code = 200, message = "success")
    @RequestMapping(value = "/getHealthClassByFactoryCode", method = RequestMethod.POST)
    public Response<HealthSkuClassDTO> getHealthClassByFactoryCode(@RequestBody HealthClassQueryDTO healthClassQueryDTO) {
        try {
            HealthSkuClassDTO res= stockHealthService.getHealthClassByFactoryCode(healthClassQueryDTO);
            return Response.builderSuccess(res);
        } catch (RomeException e) {
            log.error(e.getMessage(), e);
            return ResponseMsg.EXCEPTION.buildMsg(e.getCode(), e.getMessage());
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return Response.builderFail(ResCode.STOCK_ERROR_1003,null==e.getMessage()?e.toString():e.getMessage());
        }
    }



}
