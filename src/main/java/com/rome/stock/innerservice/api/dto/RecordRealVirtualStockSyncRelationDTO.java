package com.rome.stock.innerservice.api.dto;


import java.math.BigDecimal;

import lombok.Data;
import lombok.EqualsAndHashCode;

@Data
@EqualsAndHashCode
public class RecordRealVirtualStockSyncRelationDTO {
	private Long recordId;
	private String recordCode;
	private Long realWarehouseId;
	private Long skuId;
	private Long virtualWarehouseId;
	private BigDecimal configSyncRate;
	private Long creator;
	private Long modifier;
	private Integer allotType;

}
