package com.rome.stock.innerservice.api.controller;

import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;

import com.github.pagehelper.PageInfo;
import com.rome.arch.core.clientobject.Response;
import com.rome.arch.core.exception.RomeException;
import com.rome.arch.util.controller.RomeController;
import com.rome.stock.innerservice.api.dto.WarehouseToBRecord;
import com.rome.stock.innerservice.api.dto.assemble.AssembleOrderDetailDTO;
import com.rome.stock.innerservice.api.dto.assemble.ShopAssembleSpitPageDTO;
import com.rome.stock.innerservice.api.dto.assemble.ShopAssembleSpitRecordDTO;
import com.rome.stock.innerservice.constant.ResponseMsg;
import com.rome.stock.innerservice.domain.service.ShopAssembleSpitService;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import io.swagger.annotations.ApiResponse;
import lombok.extern.slf4j.Slf4j;

/**
 * 门店加工
 */
@Slf4j
@RomeController
@RequestMapping("/stock/v1/shopAsspit")
@Api(tags={"门店加工反拆"})
public class ShopAssembleSpitController {
    @Autowired
    private ShopAssembleSpitService shopAssembleService;

    @ApiOperation(value = "创建门店组装加工单", nickname = "addShopAssembleRecord", produces = MediaType.APPLICATION_JSON_VALUE)
    @ApiResponse(code = 200, message = "success", response = WarehouseToBRecord.class)
    @RequestMapping(value = "/addShopAssembleRecord", method = RequestMethod.POST)
    public Response addShopAssembleRecord(@ApiParam(name = "frontRecord", value = "dto") @RequestBody @Validated ShopAssembleSpitRecordDTO frontRecord) {
        try {

            shopAssembleService.addShopAssembleRecord(frontRecord);
            return ResponseMsg.SUCCESS.buildMsg();
        } catch (RomeException e) {
            log.error(e.getMessage(), e);
            return Response.builderFail(e.getCode(),e.getMessage());
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return ResponseMsg.EXCEPTION.buildMsg();
        }
    }

    @ApiOperation(value = "创建门店组装反拆单", nickname = "addShopAssembleRecord", produces = MediaType.APPLICATION_JSON_VALUE)
    @ApiResponse(code = 200, message = "success", response = WarehouseToBRecord.class)
    @RequestMapping(value = "/addShopSpitRecord", method = RequestMethod.POST)
    public Response addShopSpitRecord(@ApiParam(name = "frontRecord", value = "dto") @Validated @RequestBody ShopAssembleSpitRecordDTO frontRecord) {
        try {
            shopAssembleService.addShopSpitRecord(frontRecord);
            return ResponseMsg.SUCCESS.buildMsg();
        } catch (RomeException e) {
            log.error(e.getMessage(), e);
            return Response.builderFail(e.getCode(),e.getMessage());
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return ResponseMsg.EXCEPTION.buildMsg();
        }
    }

    

    @ApiOperation(value = "分页查询前置单据信息", nickname = "query_virtual_warehouse", produces = MediaType.APPLICATION_JSON_VALUE)
    @ApiResponse(code = 200, message = "success", response = List.class)
    @RequestMapping(value = "/listPage", method = RequestMethod.POST)
    public Response  listPage(@ApiParam(name = "virtualWarehouse", value = "dto") @RequestBody ShopAssembleSpitPageDTO frontRecord) {
        try {
            PageInfo<ShopAssembleSpitPageDTO> personPageInfo = shopAssembleService.queryList(frontRecord);
            return ResponseMsg.SUCCESS.buildMsg(personPageInfo);
        } catch (RomeException e) {
            log.error(e.getMessage(), e);
            return Response.builderFail(e.getCode(),e.getMessage());
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return ResponseMsg.EXCEPTION.buildMsg();
        }
    }

    @ApiOperation(value = "查询加工明细", nickname = "query_virtual_warehouse", produces = MediaType.APPLICATION_JSON_VALUE)
    @ApiResponse(code = 200, message = "success", response = List.class)
    @RequestMapping(value = "/getOrderDetail", method = RequestMethod.POST)
    public Response  getOrderDetail(@RequestParam("id") Long id) {
        try {
            AssembleOrderDetailDTO detail = shopAssembleService.getOrderDetail(id);
            return ResponseMsg.SUCCESS.buildMsg(detail);
        } catch (RomeException e) {
            log.error(e.getMessage(), e);
            return Response.builderFail(e.getCode(),e.getMessage());
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return ResponseMsg.EXCEPTION.buildMsg();
        }
    }
}
