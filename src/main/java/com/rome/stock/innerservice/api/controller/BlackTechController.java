package com.rome.stock.innerservice.api.controller;

import com.rome.arch.util.controller.RomeController;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.RequestMapping;

/**
 * @Description: 需谨慎使用 <p>
 */
@Slf4j
@RomeController
@RequestMapping("/stock/v1/black_tech")
public class BlackTechController {

    private static final String TP = "TRAFFIC_PERMIT";

//    @Autowired
//    private RedisUtil redisUtil;
//    @Autowired
//    private BlackTechService blackTechService;

//    @ApiOperation(value = "获取通行证", nickname = "queryTrafficPermit", produces = MediaType.APPLICATION_JSON_VALUE)
//    @ApiResponse(code = 200, message = "success")
//    @RequestMapping(value = "/queryTrafficPermit", method = RequestMethod.GET)
//    public Response queryTrafficPermit(String userId){
//        try {
//            if(StringUtils.isBlank(userId) || !"625754".equals(userId)) {
//                AlikAssert.isTrue(false, ResCode.STOCK_ERROR_1002, "用户不合法");
//            }
//            if(redisUtil.get(TP) == null) {
//                String uniquePermit = UUID.randomUUID().toString().replaceAll("-", "");
//                redisUtil.set(TP, uniquePermit, RedisTime.ONE_DAY);
//            }
//            return ResponseMsg.SUCCESS.buildMsg(redisUtil.get(TP));
//        } catch (RomeException e) {
//            log.error(e.getMessage(), e);
//            return ResponseMsg.FAIL.buildMsg(e.getCode(),e.getMessage());
//        } catch (Exception e) {
//            log.error(e.getMessage(), e);
//            return ResponseMsg.FAIL.buildMsg("500000",e.getMessage());
//        }
//    }

//    @ApiOperation(value = "修改Data", nickname = "queryTrafficPermit", produces = MediaType.APPLICATION_JSON_VALUE)
//    @ApiResponse(code = 200, message = "success")
//    @RequestMapping(value = "/changeData", method = RequestMethod.POST)
//    public Response changeData(@ApiParam(name = "修改DTO", value = "changeDataDTO") @RequestBody @Valid ChangeDataDTO changeDataDTO){
//        try {
//            Object redisData = redisUtil.get(TP);
//            AlikAssert.isNotNull(redisData, ResCode.STOCK_ERROR_1002, "数据异常");
//            AlikAssert.isTrue(redisData.toString().equals(changeDataDTO.getKey()), ResCode.STOCK_ERROR_1002, "key不合法");
//            String data = changeDataDTO.getData();
//            String lowCaseData = data.toLowerCase();
//            if(lowCaseData.contains("select") || lowCaseData.contains("delete") || lowCaseData.contains("insert")
//                    || lowCaseData.contains("alter") || lowCaseData.contains("drop")
//                    || lowCaseData.contains("truncate") || lowCaseData.contains("create")) {
//                throw new RomeException(ResCode.STOCK_ERROR_1002, "data包含非法字符");
//            }
//            if(!lowCaseData.contains("where")) {
//                throw new RomeException(ResCode.STOCK_ERROR_1002, "操作条件不能为空");
//            }
//            if(lowCaseData.contains(">") || lowCaseData.contains("<")
//                    || lowCaseData.contains("!=") || lowCaseData.contains(" in")
//                    || lowCaseData.contains(" or") || lowCaseData.contains(" like")
//                    || lowCaseData.contains(" null") || lowCaseData.contains(" between")
//                    || lowCaseData.contains(" exists")) {
//                throw new RomeException(ResCode.STOCK_ERROR_1002, "不支持范围操作");
//            }
//            lowCaseData = lowCaseData.replace(" ", "");
//            if(lowCaseData.matches("[0-9]{1,99}=[0-9]{1,99}")) {
//                throw new RomeException(ResCode.STOCK_ERROR_1002, "不支持数字=数字条件");
//            }
//            int rs = blackTechService.changeData(data);
//            return ResponseMsg.SUCCESS.buildMsg(rs);
//        } catch (RomeException e) {
//            log.error(e.getMessage(), e);
//            return ResponseMsg.FAIL.buildMsg(e.getCode(),e.getMessage());
//        } catch (Exception e) {
//            log.error(e.getMessage(), e);
//            return ResponseMsg.FAIL.buildMsg("500000",e.getMessage());
//        }
//
//    }

}

