package com.rome.stock.innerservice.api.dto.frontrecord;

import com.fasterxml.jackson.annotation.JsonIgnore;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * @Doc:采购入库同步校验单（sap调用入参）明细
 * @Author: lchy
 * @Date: 2019/6/19
 * @Version 1.0
 */
@Data
@EqualsAndHashCode
public class PurchaseSyncCheckResultItemDTO {

	@ApiModelProperty(value = "行号")
	@NotBlank(message = "行号不能为空")
	private String lineNo;
	/**
	 * 检验单号
	 */
	@NotBlank(message="检验单号不能为空")
	@ApiModelProperty(value = "检验单号")
	private String qualityCode;


	/**
	 * 收货单号
	 */
	@NotBlank(message="收货单号不能为空")
	@ApiModelProperty(value = "收货单号")
	private String wmsRecordCode;
	/**
	 * skuId
	 */
	@JsonIgnore
	private Long skuId;

	@NotBlank(message="商品编码不能为空")
	@ApiModelProperty(value = "商品编码")
	private String skuCode;
	/**
	 * 检验批
	 */
//	@NotBlank(message="批次号不能为空")
	@ApiModelProperty(value = "批次号：wms收货时会给的批次号")
	private String batchCode;

	/**
	 * 质检状态（2:不合格;1:合格）
	 */
	@NotNull(message="质检结果（2:不合格;1:合格）")
	@ApiModelProperty(value = "质检状态（2:不合格;1:合格）")
	private Integer qualityStatus;
}
