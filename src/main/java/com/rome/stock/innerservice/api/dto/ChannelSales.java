package com.rome.stock.innerservice.api.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

@Data
@EqualsAndHashCode
public class ChannelSales extends Pagination{
	/**
	 * 唯一主键
	 */
	@ApiModelProperty(value = "唯一主键")
	private Long id;
	/**
	 * 渠道id
	 */
	@ApiModelProperty(value = "渠道id")
	private String channelCode;
	/**
	 * 渠道名字
	 */
	@ApiModelProperty(value = "渠道名字")
	private String channelName;
	/**
	 * 虚拟仓库组id   所属虚拟仓库组
	 */
	@ApiModelProperty(value = "虚拟仓库组id")
	private Long virtualWarehouseGroupId;

	/**
	 * 虚仓code
	 */
	@ApiModelProperty(value = "虚仓code")
	private String virtualWarehouseGroupCode;
	/**
	 * 暂时不用，显示比率（百分比），大于0区间可选数字
	 */
	@ApiModelProperty(value = "显示比率")
	private Integer showRate;
	/**
	 * 商家id
	 */
	@ApiModelProperty(value = "商家id")
	private Long merchantId;
	/**
	 * 接单类型，0-库存不足拒绝，1-不可拒绝
	 */
	@ApiModelProperty(value = "接单类型，0-库存不足拒绝，1-不可拒绝")
	private Integer receiptType;

	@ApiModelProperty(value = "创建人")
	private Long creator;

	@ApiModelProperty(value = "更新人")
	private Long modifier;

	@ApiModelProperty(value = "门店编号集合")
	private List<String> shopCodes;
}
