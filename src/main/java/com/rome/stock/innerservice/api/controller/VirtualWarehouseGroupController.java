package com.rome.stock.innerservice.api.controller;

import com.github.pagehelper.PageInfo;
import com.rome.arch.core.clientobject.Response;
import com.rome.arch.core.exception.RomeException;
import com.rome.arch.util.controller.RomeController;
import com.rome.stock.innerservice.api.dto.VirtualWarehouseGroup;
import com.rome.stock.innerservice.common.ResCode;
import com.rome.stock.innerservice.constant.ResponseMsg;
import com.rome.stock.innerservice.domain.convertor.VirtualWarehouseGroupConvertor;
import com.rome.stock.innerservice.domain.repository.VirtualWarehouseGroupRepository;
import com.rome.stock.innerservice.domain.service.VirtualWarehouseGroupService;
import com.rome.stock.innerservice.facade.StockDataFacade;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import io.swagger.annotations.ApiResponse;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.Arrays;
import java.util.List;

@Slf4j
@RomeController
@RequestMapping("/stock/v1/virtual_warehouse_group")
@Api(tags={"策略组管理"})
public class VirtualWarehouseGroupController {
	@Resource
	private VirtualWarehouseGroupService virtualWarehouseGroupService;

	@Resource
	private VirtualWarehouseGroupRepository virtualWarehouseGroupRepository;

	@Resource
	private VirtualWarehouseGroupConvertor virtualWarehouseGroupConvertor;

	/**
	 * 创建策略组
	 *
	 * @param virDto
	 * @return
	 */
	@ApiOperation(value = "创建策略组", nickname = "createVirtualWarehouseGroup", produces = MediaType.APPLICATION_JSON_VALUE)
	@ApiResponse(code = 200, message = "success", response = VirtualWarehouseGroup.class)
	@RequestMapping(value = "/createVirtualWarehouseGroup", method = RequestMethod.POST)
	public Response createVirtualWarehouseGroup(
			@ApiParam(name = "VirtualWarehouseGroup", value = "策略组") @RequestBody VirtualWarehouseGroup virDto) {
		try {
			 virtualWarehouseGroupService.addVirtualWarehouseGroup(virDto);
			return ResponseMsg.SUCCESS.buildMsg(virDto);
		} catch (RomeException e) {
			log.error(e.getMessage(), e);
			return ResponseMsg.EXCEPTION.buildMsg(e.getCode(), e.getMessage());
		} catch (Exception e) {
			log.error(e.getMessage(), e);
			return ResponseMsg.EXCEPTION.buildMsg();
		}
	}

	@ApiOperation(value = "ID查询策略组", nickname = "getVirtualWarehouseGroupById", produces = MediaType.APPLICATION_JSON_VALUE)
	@ApiResponse(code = 200, message = "success", response = VirtualWarehouseGroup.class)
	@RequestMapping(value = "/getVirtualWarehouseGroupById/{id}", method = RequestMethod.GET)
	public Response getVirtualWarehouseGroupById(
			@ApiParam(name = "id", value = "策略组ID") @PathVariable long id) {
		try {
			VirtualWarehouseGroup virDto = virtualWarehouseGroupService.getVirtualWarehouseGroupById(id);
			return ResponseMsg.SUCCESS.buildMsg(virDto);
		} catch (RomeException e) {
			log.error(e.getMessage(), e);
			return ResponseMsg.EXCEPTION.buildMsg(e.getCode(), e.getMessage());
		} catch (Exception e) {
			log.error(e.getMessage(), e);
			return ResponseMsg.EXCEPTION.buildMsg();
		}
	}

	@ApiOperation(value = "修改策略组", nickname = "modfiyVirtualWarehouseGroup", produces = MediaType.APPLICATION_JSON_VALUE)
	@ApiResponse(code = 200, message = "success", response = VirtualWarehouseGroup.class)
	@RequestMapping(value = "/modfiyVirtualWarehouseGroup", method = RequestMethod.POST)
	public Response modifyVirtualWarehouseGroup(
			@ApiParam(name = "VirtualWarehouseGroup", value = "策略组") @RequestBody VirtualWarehouseGroup virDto) {
		try {
			virtualWarehouseGroupService.modifyVirtualWarehouseGroup(virDto);
			StockDataFacade.delRouteInfoByPriorityTempleByGroupIdList(Arrays.asList(virDto.getId()));
			return Response.builderSuccess("success");
		} catch (RomeException e) {
			log.error(e.getMessage(), e);
			return ResponseMsg.EXCEPTION.buildMsg(e.getCode(), e.getMessage());
		} catch (Exception e) {
			log.error(e.getMessage(), e);
			return ResponseMsg.EXCEPTION.buildMsg();
		}
	}

	@ApiOperation(value = "编码查询策略组", nickname = "getVirtualWarehouseGroupByCode", produces = MediaType.APPLICATION_JSON_VALUE)
	@ApiResponse(code = 200, message = "success", response = VirtualWarehouseGroup.class)
	@RequestMapping(value = "/getVirtualWarehouseGroupByCode/{virtualWarehouseGroupCode}", method = RequestMethod.GET)
	public Response getVirtualWarehouseGroupByCode(
			@ApiParam(name = "id", value = "策略组ID") @PathVariable String virtualWarehouseGroupCode) {
		try {
			VirtualWarehouseGroup virDto = virtualWarehouseGroupService.getVirtualWarehouseGroupByCode(virtualWarehouseGroupCode);
			return ResponseMsg.SUCCESS.buildMsg(virDto);
		} catch (RomeException e) {
			log.error(e.getMessage(), e);
			return ResponseMsg.EXCEPTION.buildMsg(e.getCode(), e.getMessage());
		} catch (Exception e) {
			log.error(e.getMessage(), e);
			return ResponseMsg.EXCEPTION.buildMsg();
		}
	}

	@ApiOperation(value = "获取全部策略组", nickname = "getVirtualWarehouseGroupAll", produces = MediaType.APPLICATION_JSON_VALUE)
	@ApiResponse(code = 200, message = "success", response = VirtualWarehouseGroup.class)
	@RequestMapping(value = "/getVirtualWarehouseGroupAll", method = RequestMethod.GET)
	public Response<List<VirtualWarehouseGroup>> getVirtualWarehouseGroupAll(@RequestParam("Id") Long id) {
		try {
			List<VirtualWarehouseGroup> virDto = virtualWarehouseGroupService.getVirtualWarehouseGroupAll(id);
			return Response.builderSuccess(virDto);
		} catch (RomeException e) {
			log.error(e.getMessage(), e);
			return ResponseMsg.EXCEPTION.buildMsg(e.getCode(), e.getMessage());
		} catch (Exception e) {
			log.error(e.getMessage(), e);
			return ResponseMsg.EXCEPTION.buildMsg(ResCode.STOCK_ERROR_1003, ResCode.STOCK_ERROR_1003_DESC);
		}
	}
	@ApiOperation(value = "获取全部策略组排除门店", nickname = "getVirtualGroupNotShop", produces = MediaType.APPLICATION_JSON_VALUE)
	@ApiResponse(code = 200, message = "success", response = VirtualWarehouseGroup.class)
	@RequestMapping(value = "/getVirtualGroupNotShop", method = RequestMethod.GET)
	public Response<List<VirtualWarehouseGroup>> getVirtualGroupNotShop() {
		try {
			List<VirtualWarehouseGroup> virDto = virtualWarehouseGroupService.getVirtualGroupNotShop();
			return Response.builderSuccess(virDto);
		} catch (RomeException e) {
			log.error(e.getMessage(), e);
			return ResponseMsg.EXCEPTION.buildMsg(e.getCode(), e.getMessage());
		} catch (Exception e) {
			log.error(e.getMessage(), e);
			return ResponseMsg.EXCEPTION.buildMsg(ResCode.STOCK_ERROR_1003, ResCode.STOCK_ERROR_1003_DESC);
		}
	}


	@ApiOperation(value = "自定义获取策略组", nickname = "getVirtualWarehouseGroup", produces = MediaType.APPLICATION_JSON_VALUE)
	@ApiResponse(code = 200, message = "success", response = List.class)
	@RequestMapping(value = "/getVirtualWarehouseGroup", method = RequestMethod.POST)
	public Response<PageInfo<VirtualWarehouseGroup>> getVirtualWarehouseGroup(@ApiParam(name = "VirtualWarehouseGroup", value = "策略組dto")@RequestBody VirtualWarehouseGroup virDto) {
		try {
			PageInfo<VirtualWarehouseGroup> pageList = virtualWarehouseGroupService.getVirtualWarehouseGroup(virDto);
			return Response.builderSuccess(pageList);
		} catch (RomeException e) {
			log.error(e.getMessage(), e);
			return ResponseMsg.EXCEPTION.buildMsg(e.getCode(), e.getMessage());
		} catch (Exception e) {
			log.error(e.getMessage(), e);
			return ResponseMsg.EXCEPTION.buildMsg(ResCode.STOCK_ERROR_1003, ResCode.STOCK_ERROR_1003_DESC);
		}
	}

	@ApiOperation(value = "删除策略组", nickname = "deleteVirtualWarehouseGroup", produces = MediaType.APPLICATION_JSON_VALUE)
	@ApiResponse(code = 200, message = "success")
	@RequestMapping(value = "/deleteVirtualWarehouseGroup", method = RequestMethod.GET)
	public Response deleteVirtualWarehouseGroup(@RequestParam("groupId") String groupId,@RequestParam("userId")Long userId) {
		try {
			virtualWarehouseGroupService.deleteVirtualWarehouseGroup(Long.parseLong(groupId),userId);
			StockDataFacade.delRouteInfoByPriorityTempleByGroupIdList(Arrays.asList(Long.parseLong(groupId)));
			return Response.builderSuccess("success");
		} catch (RomeException e) {
			log.error(e.getMessage(), e);
			return Response.builderFail(e.getCode(), e.getMessage());
		} catch (Exception e) {
			log.error(e.getMessage(), e);
			return Response.builderFail("500000", "系统异常");
		}
	}
	
	@ApiOperation(value = "根据虚仓id查询策略组列表", nickname = "queryByVirtualWarehouseId", produces = MediaType.APPLICATION_JSON_VALUE)
	@ApiResponse(code = 200, message = "success", response = VirtualWarehouseGroup.class)
	@RequestMapping(value = "/queryByVirtualWarehouseId", method = RequestMethod.GET)
	public Response<List<VirtualWarehouseGroup>> queryByVirtualWarehouseId(@RequestParam("Id") Long id) {
		try {
			List<VirtualWarehouseGroup> virDto = virtualWarehouseGroupService.queryByVirtualWarehouseId(id);
			return Response.builderSuccess(virDto);
		} catch (RomeException e) {
			log.error(e.getMessage(), e);
			return ResponseMsg.EXCEPTION.buildMsg(e.getCode(), e.getMessage());
		} catch (Exception e) {
			log.error(e.getMessage(), e);
			return ResponseMsg.EXCEPTION.buildMsg(ResCode.STOCK_ERROR_1003, ResCode.STOCK_ERROR_1003_DESC);
		}
	}
}
