package com.rome.stock.innerservice.api.controller;

import com.github.pagehelper.PageInfo;
import com.rome.arch.core.clientobject.Response;
import com.rome.arch.core.exception.RomeException;
import com.rome.arch.util.controller.RomeController;
import com.rome.stock.innerservice.api.dto.cloudshop.RwShopServiceLabAddDTO;
import com.rome.stock.innerservice.api.dto.cloudshop.RwShopServiceLabDTO;
import com.rome.stock.innerservice.api.dto.cloudshop.RwShopServiceLabImportDTO;
import com.rome.stock.innerservice.api.dto.cloudshop.RwShopServiceLabParamDTO;
import com.rome.stock.innerservice.common.ResCode;
import com.rome.stock.innerservice.domain.service.RwShopServiceLabService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiResponse;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@Slf4j
@RomeController
@RequestMapping("/stock/v1/rw_shop_service_lab")
@Api(tags = {"云店供应商仓门店关联关系"})
public class RwShopServiceLabController {

    @Autowired
    private RwShopServiceLabService rwShopServiceLabService;

    @ApiOperation(value = "分页查询云店供应商仓和门店关系", nickname = "queryRwShopServiceLab", produces = MediaType.APPLICATION_JSON_VALUE)
    @ApiResponse(code = 200, message = "success", response = Response.class)
    @RequestMapping(value = "/queryRwShopServiceLab", method = RequestMethod.POST)
    public Response<PageInfo<RwShopServiceLabDTO>> queryRwShopServiceLab(@RequestBody RwShopServiceLabParamDTO paramDTO) {
        try {
            PageInfo<RwShopServiceLabDTO> pageInfo = rwShopServiceLabService.queryRwShopServiceLab(paramDTO);
            return Response.builderSuccess(pageInfo);
        } catch (RomeException e) {
            log.error(e.getMessage(), e);
            return Response.builderFail(e.getCode(), e.getMessage());
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return Response.builderFail(ResCode.STOCK_ERROR_1003_DESC, ResCode.STOCK_ERROR_1003_DESC);
        }
    }


    @ApiOperation(value = "根据实仓id查询关联的服务标签", nickname = "deleteRwShopServiceLab", produces = MediaType.APPLICATION_JSON_VALUE)
    @ApiResponse(code = 200, message = "success", response = Response.class)
    @RequestMapping(value = "/deleteRwShopServiceLab", method = RequestMethod.DELETE)
    public Response deleteRwShopServiceLab(@RequestBody List<Long> ids) {
        try {
            return Response.builderSuccess(rwShopServiceLabService.deleteRwShopServiceLab(ids));
        } catch (RomeException e) {
            log.error(e.getMessage(), e);
            return Response.builderFail(e.getCode(), e.getMessage());
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return Response.builderFail(ResCode.STOCK_ERROR_1003_DESC, ResCode.STOCK_ERROR_1003_DESC);
        }
    }

    @ApiOperation(value = "新增实仓门店关联的服务标签", nickname = "addRwShopServiceLab", produces = MediaType.APPLICATION_JSON_VALUE)
    @ApiResponse(code = 200, message = "success", response = Response.class)
    @PostMapping(value = "/addRwShopServiceLab")
    public Response addRwShopServiceLab(@RequestBody RwShopServiceLabAddDTO addDTO) {
        try {
            return Response.builderSuccess(rwShopServiceLabService.addRwShopServiceLab(addDTO));
        } catch (RomeException e) {
            log.error(e.getMessage(), e);
            return Response.builderFail(e.getCode(), e.getMessage());
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return Response.builderFail(ResCode.STOCK_ERROR_1003_DESC, ResCode.STOCK_ERROR_1003_DESC);
        }
    }

    @ApiOperation(value = "新增实仓门店关联的服务标签", nickname = "importRwShopServiceLab", produces = MediaType.APPLICATION_JSON_VALUE)
    @ApiResponse(code = 200, message = "success", response = Response.class)
    @PostMapping(value = "/importRwShopServiceLab")
    public Response importFileData(@RequestBody List<RwShopServiceLabImportDTO> data, @RequestParam("userId") Long userId) {
        try {
            return Response.builderSuccess(rwShopServiceLabService.importRwShopServiceLab(data, userId));
        } catch (RomeException e) {
            log.error(e.getMessage(), e);
            return Response.builderFail(e.getCode(), e.getMessage());
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return Response.builderFail(ResCode.STOCK_ERROR_1003_DESC, ResCode.STOCK_ERROR_1003_DESC);
        }
    }
}
