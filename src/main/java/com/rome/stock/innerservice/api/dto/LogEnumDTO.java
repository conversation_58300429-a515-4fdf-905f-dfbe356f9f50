package com.rome.stock.innerservice.api.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode
public class LogEnumDTO extends Pagination {


    /**
     * 唯一主键
     */
    @ApiModelProperty(value = "唯一主键")
    private Long id;

    @ApiModelProperty(value = "请求业务名称")
    private String requestName;

    @ApiModelProperty(value = "请求服务名")
    private String requestService;

    @ApiModelProperty(value = "交互系统")
    private String requestSystem;

    @ApiModelProperty(value = "日志表名称")
    private String requestTable;

    @ApiModelProperty(value = "单据表名称")
    private String tableName;

    @ApiModelProperty(value = "日志表字段名称")
    private String tableField;

    @ApiModelProperty(value = "调用前字段值")
    private String tableFieldBeforeValue;

    @ApiModelProperty(value = "调用后字段值")
    private String tableFieldAfterValue;

    @ApiModelProperty(value = "1.feign调用 2.http调用")
    private Integer requestWay;

    @ApiModelProperty(value = "1.主动调用 2.被动调用")
    private Integer requestType;

    @ApiModelProperty(value = "备注")
    private String remark;

    /**
     * 创建人
     */
    private Long creator;
    /**
     * 更新人
     */
    private Long modifier;
}
