package com.rome.stock.innerservice.api.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

@Data
@EqualsAndHashCode
public class RealWarehouseArea {

    @ApiModelProperty(value = "主键")
    private Long id;
    @ApiModelProperty(value = "关联实仓主键")
    private Long realWarehouseId;
    @ApiModelProperty(value = "国家code")
    private String realWarehouseCountryCode;
    @ApiModelProperty(value = "省份code")
    private String realWarehouseProvinceCode;
    @ApiModelProperty(value = "城市code")
    private String realWarehouseCityCode;
    @ApiModelProperty(value = "实仓地区编码")
    private String realWarehouseAreaCode;
    @ApiModelProperty(value = "创建日期")
    private String createDate;

}
