package com.rome.stock.innerservice.api.controller;

import com.alibaba.fastjson.JSON;
import com.rome.arch.core.clientobject.Response;
import com.rome.arch.core.exception.RomeException;
import com.rome.arch.util.controller.RomeController;
import com.rome.stock.innerservice.api.dto.frontrecord.DispatchNoticeDTO;
import com.rome.stock.innerservice.api.dto.warehouserecord.LockStockResp;
import com.rome.stock.innerservice.api.dto.warehouserecord.OutWarehouseRecordDTO;
import com.rome.stock.innerservice.common.CoreKibanaLog;
import com.rome.stock.innerservice.common.RedisUtil;
import com.rome.stock.innerservice.common.ResCode;
import com.rome.stock.innerservice.constant.ResponseMsg;
import com.rome.stock.innerservice.domain.service.DispatchNoticeService;
import com.rome.stock.innerservice.domain.service.RealWarehouseConsumeService;
import com.rome.stock.innerservice.domain.service.ShopReplenishService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiResponse;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;

import javax.annotation.Resource;

/**
 * LaiPurchaseController类的实现描述：来团购接口
 *
 * <AUTHOR>
 */
@Slf4j
@RomeController
@RequestMapping("/stock/v1/lai_purchase")
@Api(tags={"来团购接口"})
public class LaiPurchaseController {
    @Autowired
    private ShopReplenishService shopReplenishService;
    @Resource
    private RedisUtil redisUtil;
    @Resource
    private RealWarehouseConsumeService realWarehouseConsumeService;
    @Resource
    private DispatchNoticeService dispatchNoticeService;

    private final static String LOGTYPE = "laiPurchase";

    @ApiOperation(value = "来团购订单推送", nickname = "pushLaiRecord", produces = MediaType.APPLICATION_JSON_VALUE)
    @ApiResponse(code = 200, message = "success")
    @RequestMapping(value = "/pushLaiRecord", method = RequestMethod.POST)
    public Response pushLaiRecord(@RequestParam(value = "recordCode" ) String recordCode){
        try {
            log.warn(CoreKibanaLog.getJobLog(LOGTYPE, "pushLaiRecord", "来团购订单推送: " , JSON.toJSONString(recordCode)));
            realWarehouseConsumeService.confirmPushConsumeRecord(recordCode);
            DispatchNoticeDTO dispatchNoticeDTO = new DispatchNoticeDTO();
            dispatchNoticeDTO.setRecordCode(recordCode);
            dispatchNoticeDTO.setDispatchType(1);
            dispatchNoticeDTO.setThirdRecordCode(recordCode);
            dispatchNoticeDTO.setSourceSystem("order-core-service");
            dispatchNoticeService.handleDispatchNotice(dispatchNoticeDTO);
            return ResponseMsg.SUCCESS.buildMsg();
        } catch (RomeException e) {
            log.error(e.getMessage(), e);
            return Response.builderFail(e.getCode(),e.getMessage());
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return Response.builderFail(ResCode.STOCK_ERROR_1003_DESC,e.getMessage());
        }
    }


    @ApiOperation(value = "来团购锁定库存", nickname = "laiLockStock", produces = MediaType.APPLICATION_JSON_VALUE)
    @ApiResponse(code = 200, message = "success")
    @RequestMapping(value = "/laiLockStock", method = RequestMethod.POST)
    public Response laiLockStock(@RequestBody OutWarehouseRecordDTO warehouseRecord) {
        boolean isLock = false;
        log.warn(CoreKibanaLog.getJobLog(LOGTYPE, "laiLockStock", "来团购锁定库存: " , JSON.toJSONString(warehouseRecord)));
        String uniqueKey = "laiLockStock" + "_" + warehouseRecord.getRecordCode();
        try {
            isLock = redisUtil.lock(uniqueKey, "1", 120);
            if (isLock) {
                shopReplenishService.laiLockStock(warehouseRecord);
                return ResponseMsg.SUCCESS.buildMsg("");
            } else {
                throw new RomeException(ResCode.STOCK_ERROR_1017, "来团购锁定库存正在处理中，请稍后再试。。。" + warehouseRecord.getRecordCode());
            }
        } catch (RomeException e) {
            return Response.builderFail(e.getCode(), e.getMessage());
        } catch (Exception e) {
            return Response.builderFail(ResCode.STOCK_ERROR_1003,ResCode.STOCK_ERROR_1003_DESC+e.getMessage()==null?e.toString():e.getMessage());
        } finally {
            if (isLock) {
                redisUtil.unLock(uniqueKey, "1");
            }
        }
    }



    @ApiOperation(value = "来团购修改do", nickname = "updateLaiDo", produces = MediaType.APPLICATION_JSON_VALUE)
    @ApiResponse(code = 200, message = "success")
    @RequestMapping(value = "/updateLaiDo", method = RequestMethod.POST)
    public Response<LockStockResp> updateLaiDo(@RequestBody OutWarehouseRecordDTO warehouseRecord) {
        boolean isLock = false;
        log.warn(CoreKibanaLog.getJobLog(LOGTYPE, "updateLaiDo", "来团购修改do: " , JSON.toJSONString(warehouseRecord)));
        String uniqueKey = "updateLaiDo" + "_" + warehouseRecord.getRecordCode();
        try {
            isLock = redisUtil.lock(uniqueKey, "1", 120);
            if (isLock) {
                LockStockResp lockStockResp=shopReplenishService.updateLaiDo(warehouseRecord);
                return ResponseMsg.SUCCESS.buildMsg(lockStockResp);
            } else {
                throw new RomeException(ResCode.STOCK_ERROR_1017, "来团购修改do正在处理中，请稍后再试。。。" + warehouseRecord.getRecordCode());
            }
        } catch (RomeException e) {
            return Response.builderFail(e.getCode(), e.getMessage());
        } catch (Exception e) {
            return Response.builderFail(ResCode.STOCK_ERROR_1003,ResCode.STOCK_ERROR_1003_DESC+e.getMessage()==null?e.toString():e.getMessage());
        } finally {
            if (isLock) {
                redisUtil.unLock(uniqueKey, "1");
            }
        }
    }



    @ApiOperation(value = "来团购创建调拨单", nickname = "createLaiWh", produces = MediaType.APPLICATION_JSON_VALUE)
    @ApiResponse(code = 200, message = "success")
    @RequestMapping(value = "/createLaiWh", method = RequestMethod.POST)
    public Response<LockStockResp> createLaiWh(@RequestBody OutWarehouseRecordDTO warehouseRecord) {
        boolean isLock = false;
        log.warn(CoreKibanaLog.getJobLog(LOGTYPE, "createLaiWh", "来团购创建调拨单: " , JSON.toJSONString(warehouseRecord)));
        String uniqueKey = "createLaiWh" + "_" + warehouseRecord.getRecordCode();
        try {
            isLock = redisUtil.lock(uniqueKey, "1", 120);
            if (isLock) {
                shopReplenishService.createLaiWh(warehouseRecord);
                return ResponseMsg.SUCCESS.buildMsg();
            } else {
                throw new RomeException(ResCode.STOCK_ERROR_1017, "来团购创建调拨单正在处理中，请稍后再试。。。" + warehouseRecord.getRecordCode());
            }
        } catch (RomeException e) {
            return Response.builderFail(e.getCode(), e.getMessage());
        } catch (Exception e) {
            return Response.builderFail(ResCode.STOCK_ERROR_1003,ResCode.STOCK_ERROR_1003_DESC+e.getMessage()==null?e.toString():e.getMessage());
        } finally {
            if (isLock) {
                redisUtil.unLock(uniqueKey, "1");
            }
        }
    }
}
