package com.rome.stock.innerservice.api.dto.frontrecord;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.Valid;
import javax.validation.constraints.NotBlank;
import java.io.Serializable;
import java.util.List;

/**
 * @Doc:.....
 * @Author: lchy
 * @Date: 2020/6/10
 * @Version 1.0
 */
@Data
@EqualsAndHashCode
public class UpdateSoOrderDTO  implements Serializable {
    @ApiModelProperty(value = "so单号", required = true)
    @NotBlank
    private String soCode;

    @Valid
    private List<UpdateOrderDetailDTO> frontRecordDetails;
}
