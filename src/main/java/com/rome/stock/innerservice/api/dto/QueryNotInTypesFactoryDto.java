package com.rome.stock.innerservice.api.dto;

import com.rome.stock.common.page.Pagination;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2022年07月28日 11:10
 */
@Data
@EqualsAndHashCode
public class QueryNotInTypesFactoryDto extends Pagination {

    @ApiModelProperty(value = "非指定类型")
    private List<Integer> notInTypes;
}
