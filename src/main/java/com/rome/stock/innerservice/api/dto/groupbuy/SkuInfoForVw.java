package com.rome.stock.innerservice.api.dto.groupbuy;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.rome.stock.innerservice.remote.item.dto.SkuCombineSimpleDTO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotBlank;
import java.util.List;

/**
 * 类SkuInfo的实现描述：sku信息
 *
 * <AUTHOR> 2019/4/16 9:51
 */
@Data
@EqualsAndHashCode
public class SkuInfoForVw {

    /**
     * 虚仓ID
     */
    @ApiModelProperty(value = "虚仓ID", name = "virtualWarehouseId", required=true)
    private Long virtualWarehouseId;

    @ApiModelProperty(value = "virtualWarehouseCode,虚仓code", name = "virtualWarehouseCode" )
    private String virtualWarehouseCode;


    @ApiModelProperty(value = "merchantId,不传将会用默认的商家id", name = "merchantId" )
    private Long merchantId;

    /**
     * 商品skuId
     */
    @ApiModelProperty(value = "商品skuId", name = "skuId")
    @JsonIgnore
    private Long skuId;

    /**
     * 商品sku编码
     */
    @ApiModelProperty(value = "商品sku编码", name = "skuCode", required=true)
    private String skuCode;

    /**
     * 单位
     */
    @ApiModelProperty(value = "单位code", name = "unit", required = true)
    @NotBlank(message="单位编码不能为空")
    private String unitCode;


    /**
     *   sku类型 0单sku，1组合sku，2组装sku
     */
    @JsonIgnore
    private Integer combineType;

    /**
     * 组合品信息
     */
    @JsonIgnore
    private List<SkuCombineSimpleDTO> combineInfo;

    /**
     * 根据skuId-merchantId-unitCode组装信息
     */
    @JsonIgnore
    private String  skuIdInfo;

    /**
     * 根据skuCode-merchantId-unitCode组装信息
     */
    @JsonIgnore
    private String  skuCodeInfo;


    public  String  getSkuCodeInfo(){
        StringBuilder sb = new StringBuilder();
        sb.append(this.getSkuCode()).append("-").append(this.getMerchantId());
        return sb.toString();

    }

}
