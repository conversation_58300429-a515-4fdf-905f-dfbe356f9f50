package com.rome.stock.innerservice.api.dto;

import com.rome.arch.core.dto.DTO;
import com.rome.stock.common.enums.warehouse.RealTimeBussTypeVO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * <AUTHOR>
 * @description 查询实时可用库存
 * @date 2020/9/10 10:41
 */
@Data
public class QueryRealTimeStock extends DTO {

    @ApiModelProperty(value = "条件编码集合")
    @NotNull(message = "条件编码集合不能为空")
    private List<String> codeList;

    @ApiModelProperty(value = "业务类型 1--省编码 2--市编码 3--区编码 4--门店编码 5--工厂")
    private Integer businessType;

    @ApiModelProperty(value = "省份code",hidden = true)
    private List<String> realWarehouseProvinceCodes;

    @ApiModelProperty(value = "城市code",hidden = true)
    private List<String> realWarehouseCityCodes;

    @ApiModelProperty(value = "区code",hidden = true)
    private List<String> realWarehouseCountyCodes;

    @ApiModelProperty(value = "门店集合",hidden = true)
    private List<String> shopCodes;

    @ApiModelProperty(value = "工厂编号",hidden = true)
    private List<String> factoryCodes;

    @ApiModelProperty(value = "商品编码集合")
    @NotNull(message = "商品编码集合不能为空")
    private List<String> skuCodeList;

    @ApiModelProperty(value = "仓库层级")
    private Integer realWarehouseRank;

    public List<String> getRealWarehouseProvinceCodes(){
        if(this.businessType.equals(RealTimeBussTypeVO.PROVINCE.getBussType())){
            return this.codeList;
        }
        return null;
    }

    public List<String> getRealWarehouseCityCodes(){
        if(this.businessType.equals(RealTimeBussTypeVO.CITY.getBussType())){
            return this.codeList;
        }
        return null;
    }

    public List<String> getRealWarehouseCountyCodes(){
        if(this.businessType.equals(RealTimeBussTypeVO.COUNTY.getBussType())){
            return this.codeList;
        }
        return null;
    }

    public List<String> getShopCodes(){
        if(this.businessType.equals(RealTimeBussTypeVO.SHOP.getBussType())){
            return this.codeList;
        }
        return null;
    }

    public List<String> getFactoryCodes(){
        if(this.businessType.equals(RealTimeBussTypeVO.FACTORY.getBussType())){
            return this.codeList;
        }
        return null;
    }

}
   