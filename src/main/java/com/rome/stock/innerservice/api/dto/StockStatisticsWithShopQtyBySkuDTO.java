/**
 * Filename EntrySaleStockDTO.java
 * Company 上海来伊份科技有限公司。
 * <AUTHOR>
 * @version 
 */
package com.rome.stock.innerservice.api.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * 库存统计子对象：根据skuCode查询门店总库存和在销门店数
 * <AUTHOR>
 * @since 2022-02-25 11:43:13
 */
@Data
@NoArgsConstructor
@EqualsAndHashCode
public class StockStatisticsWithShopQtyBySkuDTO {
    @ApiModelProperty("商品编码")
    private String skuCode;

    @ApiModelProperty("门店总库存")
    private BigDecimal shopTotalQty;

    @ApiModelProperty("在销门店数")
    private BigDecimal shopNumber;

}
