package com.rome.stock.innerservice.api.dto.bigdata;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * DayInventoryPlanResponse
 */
@Data
@ApiModel("DayInventoryPlanResponse")
public class DayPredictReplenishmentResponse {

    @ApiModelProperty("总数")
    private Integer total;
    @ApiModelProperty("总数页数")
    private Integer totalPage;
    @ApiModelProperty("当前页数")
    private Integer page;
    @ApiModelProperty("返回结果列表")
    private List<DayPredictReplenishmentResDTO> list;
}
