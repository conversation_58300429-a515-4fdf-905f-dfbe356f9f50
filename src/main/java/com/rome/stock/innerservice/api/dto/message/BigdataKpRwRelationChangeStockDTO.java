/**
 * Filename BigdataKpRwRelationStockDTO.java
 * Company 上海来伊份科技有限公司。
 * <AUTHOR>
 * @version 
 */
package com.rome.stock.innerservice.api.dto.message;

import java.math.BigDecimal;
import java.util.Date;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 大数据鲲鹏实仓关联库存变化数据
 * <AUTHOR>
 * @since 2020-9-11 14:21:42
 */
@Data
@ApiModel("大数据鲲鹏实仓关联库存变化数据DTO")
public class BigdataKpRwRelationChangeStockDTO {

	@ApiModelProperty("真实库存")
    private BigDecimal realQty;

    @ApiModelProperty("可用库存")
    private BigDecimal availableQty;

    @ApiModelProperty("在途库存")
    private BigDecimal onroadQty;
    
    @ApiModelProperty("锁定库存")
    private BigDecimal lockQty;
    
    @ApiModelProperty("质检库存")
    private BigDecimal qualityQty;
    
    @ApiModelProperty("不合格库存")
    private BigDecimal unqualifiedQty;

    @ApiModelProperty("工厂编号")
    private String factoryCode;

    @ApiModelProperty("商品编码")
    private String skuCode;

    @ApiModelProperty("仓库层级")
    private Integer realWarehouseRank;

    @ApiModelProperty("发送时间")
    private Date sendTime;

    @ApiModelProperty(value = "仓店一体标识 0 -非仓店一体   1仓店一体")
    private Integer warehouseStoreIdenti;


    
}
