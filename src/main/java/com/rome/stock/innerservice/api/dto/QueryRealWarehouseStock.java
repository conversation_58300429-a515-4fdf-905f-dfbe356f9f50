package com.rome.stock.innerservice.api.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;

/**
 * <p>
 * Description: MDM依据SKU编码、物资存储类型查询仓库库存传参DTO
 * </p>
 *
 * <AUTHOR>
 * @date 2022/12/15
 **/
@Data
public class QueryRealWarehouseStock {

    @ApiModelProperty(value = "sku编码")
    @NotEmpty(message = "sku编码不能为空")
    private String skuCode;

    @ApiModelProperty(value = "物资存储类型:[1-运输单位,2-基本单位]")
    @NotNull(message = "物资存储类型不能为空")
    private Integer materialStorageType;

}    
   