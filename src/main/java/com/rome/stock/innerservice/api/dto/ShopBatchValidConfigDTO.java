package com.rome.stock.innerservice.api.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

/**
 * 门店库存管理配置
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
@EqualsAndHashCode
public class ShopBatchValidConfigDTO {

    @ApiModelProperty("保质期范围起始值")
    private Integer startValue;

    @ApiModelProperty("保质期范围结束值")
    private Integer endValue;

    @ApiModelProperty("撤柜要求天数")
    private Integer removalDay;

}
