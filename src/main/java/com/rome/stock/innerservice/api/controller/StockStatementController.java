package com.rome.stock.innerservice.api.controller;

import com.github.pagehelper.PageInfo;
import com.rome.arch.core.clientobject.Response;
import com.rome.arch.core.exception.RomeException;
import com.rome.arch.util.controller.RomeController;
import com.rome.stock.common.constants.ResCode;
import com.rome.stock.innerservice.api.dto.StockStatementDTO;
import com.rome.stock.innerservice.api.dto.WmsStockReportDataDTO;
import com.rome.stock.innerservice.api.dto.frontrecord.RwStockStatementDTO;
import com.rome.stock.innerservice.domain.service.StockStatementService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiResponse;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.MediaType;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;

import javax.annotation.Resource;
import java.util.List;

/**
 *
 * <AUTHOR> zk
 * @since : 2023-10-10
 */
@Slf4j
@RomeController
@RequestMapping("/stock/v1/stockStatement")
@Api(tags={"库存流水"})
public class StockStatementController {

    @Resource
    private StockStatementService stockStatementService;
    @ApiOperation(value = "获取库存流水", nickname = "getStockStatement", produces = MediaType.APPLICATION_JSON_VALUE)
    @ApiResponse(code = 200, message = "success")
    @PostMapping("/getStockStatement")
    public Response<PageInfo<RwStockStatementDTO>> getStockStatement(@RequestBody StockStatementDTO stockStatementDTO){
        try{
            PageInfo<RwStockStatementDTO> result = stockStatementService.getStockStatement(stockStatementDTO);
            return Response.builderSuccess(result);
        }catch (RomeException ex){
            log.error(ex.getMessage(),ex);
            return Response.builderFail(ex.getCode(), ex.getMessage());
        }catch (Exception ex){
            log.error(ex.getMessage(),ex);
            return Response.builderFail(ResCode.STOCK_ERROR_1003_DESC,ex.getMessage());
        }
    }
}
