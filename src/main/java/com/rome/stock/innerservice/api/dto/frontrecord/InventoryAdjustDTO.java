package com.rome.stock.innerservice.api.dto.frontrecord;

import com.rome.stock.innerservice.api.dto.Pagination;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.Size;
import java.util.Date;
import java.util.List;

/**
 * 仓库调整
 */
@Data
@EqualsAndHashCode
public class InventoryAdjustDTO extends Pagination {

    @ApiModelProperty(value = "唯一主键")
    private Long id;

    @ApiModelProperty(value = "调整单单号")
    private String recordCode;

    @ApiModelProperty(value = "调整单状态 0-新建,1-确认,2-取消,14-完成")
    private Integer recordStatus;

    @ApiModelProperty(value = "盈亏状态 true-调盈,false-调亏")
    private Boolean isAdd;

    @ApiModelProperty(value = "单据类型")
    private Integer recordType;

    @ApiModelProperty(value = "实仓id")
    private Long realWarehouseId;

    @ApiModelProperty(value = "门店编码")
    private String shopCode;

    @ApiModelProperty(value = "外部仓库编码")
    private String realWareHouseOutCode;

    @ApiModelProperty(value = "库存调整编码")
    private String adjustCode;

    @ApiModelProperty(value = "调整原因code")
    private Integer reasonCode;

    @ApiModelProperty(value = "调整原因")
    private String reason;
    
    @ApiModelProperty(value = "SAP过账单号")
    private String sapRecordCode;

    @ApiModelProperty(value = "备注")
    private String remark;

    @ApiModelProperty(value = "OA审批号")
    private String approveOACode;

    @ApiModelProperty(value = "开始时间")
    private Date startTime;

    @ApiModelProperty(value = "结束时间")
    private Date endTime;

    @ApiModelProperty(value = "创建时间")
    private Date createTime;

    @ApiModelProperty(value = "工厂编号")
    private String factoryCode;

    @ApiModelProperty(value = "工厂名称")
    private String factoryName;

    @ApiModelProperty(value = "仓库外部编号")
    private String realWarehouseCode;

    @ApiModelProperty(value = "仓库名称")
    private String realWarehouseName;

    @ApiModelProperty(value = "创建人")
    private Long creator;

    @ApiModelProperty(value = "更新人")
    private Long modifier;

    @ApiModelProperty(value = "外部系统单据编号")
    private String outRecordCode;

    @Size(min = 1)
    @ApiModelProperty(value = "仓库调整明细")
    private List<InventoryAdjustDetailDTO> frontRecordDetails ;
    
    @ApiModelProperty(value = "修改时间")
    private Date updateTime;
    
}
