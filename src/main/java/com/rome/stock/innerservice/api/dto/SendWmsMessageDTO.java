package com.rome.stock.innerservice.api.dto;

import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;

@Data
@EqualsAndHashCode
public class SendWmsMessageDTO {

    /**
     * 所属单据编码
     */
    private String recordCode;

    /**
     * 推送时间
     */
    private Date createTime;

    /**
     * 同步wms状态    1-同步成功  0-同步失败
     */
    private Integer syncWmsStatus;


    /**
     *失败原因
     */
    private String syncFailReason;
    /**
     * 0.TOB,1.TOC
     */
    private Integer type;

}
