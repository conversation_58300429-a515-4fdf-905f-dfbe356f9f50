package com.rome.stock.innerservice.api.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @date 20/8/1
 */
@Data
public class SkuStatusChangeSendMessageDTO {


    @ApiModelProperty("商品编码")
    private String skuCode;

    @ApiModelProperty("采购入库单号集合")
    private List<String> purchaseEntryNoList;

    @ApiModelProperty("是否手动变更")
    private Boolean isManualChange;

    /**
     * OFFICIAL("OFFICIAL", "正式"),
     * NEW("NEW", "新品"),
     * NORMAL("NORMAL", "正常"),
     * PRECLOSE("PRECLOSE", "预下市"),
     * CLOSED("CLOSED", "已下市"),
     * OFFSHELF("OFFSHELF", "已下架"),
     * OLDUP("OLDUP", "老品新上"),
     * DELETED("DELETED", "已删档"),
     */
    @ApiModelProperty("变更前状态：OFFICIAL(正式),NEW(新品),NORMAL(正常),PRECLOSE(预下市),CLOSED(已下市),OFFSHELF(已下架),OLDUP(老品新上),DELETED(已删档)")
    private String preStatus;

    @ApiModelProperty("变更后状态：OFFICIAL(正式),NEW(新品),NORMAL(正常),PRECLOSE(预下市),CLOSED(已下市),OFFSHELF(已下架),OLDUP(老品新上),DELETED(已删档)")
    private String status;

}
