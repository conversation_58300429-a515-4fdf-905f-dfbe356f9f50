/**
 * Filename WmsBatchCheckFlowDTO.java
 * Company 上海来伊份科技有限公司。
 * <AUTHOR>
 * @version 
 */
package com.rome.stock.innerservice.api.dto;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

/**
 * wms批次库存核对流水记录表
 * <AUTHOR>
 * @since 2021-7-9 11:49:15
 */
@Data
@NoArgsConstructor
@EqualsAndHashCode
public class WmsBatchCheckFlowDTO extends Pagination {

    @ApiModelProperty("唯一主键")
    private Long id;

    @ApiModelProperty("实仓仓库ID")
    private Long realWarehouseId;

    @ApiModelProperty("工厂编号")
    private String factoryCode;

    @ApiModelProperty("实仓仓库编号")
    private String realWarehouseCode;

    @ApiModelProperty("实仓仓库名称")
    private String realWarehouseName;

    @ApiModelProperty("商品sku编码")
    private Long skuId;

    @ApiModelProperty("商品sku编码集合")
    private List<Long> skuIds;

    @ApiModelProperty("商品编码")
    private String skuCode;
    
    @ApiModelProperty("批次编码-批次号")
    private String batchCode;

    @ApiModelProperty("真实库存-实仓")
    private BigDecimal realQty;

    @ApiModelProperty("wms真实库存")
    private BigDecimal wmsRealQty;

    @ApiModelProperty("系统来源")
    private String sourceSystem;

    @ApiModelProperty("创建时间")
    private Date createTime;
    
    @ApiModelProperty("更新时间")
    private Date updateTime;
    
    @ApiModelProperty("商品名称")
    private String skuName;
    
    @ApiModelProperty(value = "主单位(商品单位)")
    private String unit;
    
    @ApiModelProperty("中台与wms差异，查询用 0无 1有")
    private Integer type;
    
}
