package com.rome.stock.innerservice.api.controller;

import com.github.pagehelper.PageInfo;
import com.rome.arch.core.clientobject.Response;
import com.rome.arch.core.exception.RomeException;
import com.rome.arch.util.controller.RomeController;
import com.rome.stock.common.utils.validate.ResponseValidateUtils;
import com.rome.stock.innerservice.common.ResCode;
import com.rome.stock.innerservice.constant.ResponseMsg;
import com.rome.stock.innerservice.remote.base.dto.DictionaryDTO;
import com.rome.stock.innerservice.remote.base.facade.BaseDataFacade;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiResponse;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;

/**
 * TODO
 *
 * <AUTHOR> zk
 * @since : 2023-10-09
 */
@Slf4j
@RomeController
@RequestMapping("/stock/v1/dictionary")
@Api(tags = {"获取字典"})
public class DictionaryController {

    @Resource
    private BaseDataFacade baseDataFacade;

    @ApiOperation(value = "根据类型编码获取字典", nickname = "getDictionaryByTypeCode", produces = MediaType.APPLICATION_JSON_VALUE)
    @ApiResponse(code = 200, message = "success", response = List.class)
    @RequestMapping(value = "/getDictionaryByTypeCode", method = RequestMethod.GET)
    public Response<List<DictionaryDTO>> getDictionaryByTypeCode(String typeCode){
        try {
            List<DictionaryDTO> response = baseDataFacade.searchByTypeCode(typeCode);
            return Response.builderSuccess(response);
        } catch (RomeException e) {
            log.error(e.getMessage(), e);
            return ResponseMsg.EXCEPTION.buildMsg(e.getCode(), e.getMessage());
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return ResponseMsg.EXCEPTION.buildMsg(ResCode.STOCK_ERROR_1003, ResCode.STOCK_ERROR_1003_DESC);
        }

    }
}
