package com.rome.stock.innerservice.api.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;

@Data
@EqualsAndHashCode
public class RealReturnWarehouseDTO implements Serializable {

    @ApiModelProperty(value = "主键")
    private Long id;

    @ApiModelProperty(value = "退货仓ID")
    private Long returnWarehouseId;

    @ApiModelProperty(value = "实仓ID")
    private Long realWarehouseId;

    @ApiModelProperty(value = "创建人编号")
    private Long creator;

    @ApiModelProperty(value = "更新编号")
    private Long modifier;
}
