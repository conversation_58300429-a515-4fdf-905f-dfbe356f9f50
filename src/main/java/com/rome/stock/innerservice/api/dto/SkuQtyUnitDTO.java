package com.rome.stock.innerservice.api.dto;

import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Component;

import com.rome.stock.innerservice.domain.entity.SkuQtyUnitBaseE;

import lombok.Data;
import lombok.EqualsAndHashCode;

@Data
@Component
@Scope("prototype")
@EqualsAndHashCode(callSuper = false)
public class SkuQtyUnitDTO extends SkuQtyUnitBaseE {

    /**
     * 商品sku编码
     */
    private String channelCode;

    /**
     * 虚仓ID
     */
    private Long virtualWarehouseId;

    /**
     * 实仓ID
     */
    private Long realWarehouseId;

}
