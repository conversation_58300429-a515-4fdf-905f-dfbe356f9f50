package com.rome.stock.innerservice.api.dto.cloudshop;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * 类CloudShopStockReqDTO的实现描述：云店库存查询参数请求
 *
 * <AUTHOR> 2021/9/26 14:38
 */
@Data
public class CloudShopStockReqDTO {

    /**
     * 经度
     */
    @ApiModelProperty(value = "经度")
    private String lng;

    /**
     * 纬度
     */
    @ApiModelProperty(value = "纬度")
    private String lat;

    @ApiModelProperty(value = "库存查询对象")
    private List<CloudShopStockDTO> list;
}
