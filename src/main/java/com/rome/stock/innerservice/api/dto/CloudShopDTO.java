package com.rome.stock.innerservice.api.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;

/**
 * 云商集单DTO
 * @Version 1.0
 */
@Data
@EqualsAndHashCode
public class CloudShopDTO {

    @ApiModelProperty("标签类型(1.门店自提，2.仓库次日达，3.供应商外卖，4.供应商送门店)")
    private Integer labType;

    @ApiModelProperty("开始时间")
    private Date startTime;

    @ApiModelProperty("结束时间")
    private Date endTime;

}
