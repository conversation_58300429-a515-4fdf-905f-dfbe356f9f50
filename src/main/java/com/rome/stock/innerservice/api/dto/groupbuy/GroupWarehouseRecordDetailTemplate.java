package com.rome.stock.innerservice.api.dto.groupbuy;

import cn.afterturn.easypoi.excel.annotation.Excel;
import lombok.Data;
import org.elasticsearch.search.DocValueFormat;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 预入库报表导出模板
 */
@Data
public class GroupWarehouseRecordDetailTemplate {

    private Long id;

    @Excel(name = "出库单号")
    private String recordCode;

    @Excel(name = "预约单号")
    private String outRecordCode;

    @Excel(name = "所属客户")
    private String userCode;

    @Excel(name = "派车单号")
    private String tmsRecordCode;

    @Excel(name = "商品编码")
    private String skuCode;
    @Excel(name = "商品名称")
    private String skuName;
    @Excel(name = "出库单位")
    private String unit;
    @Excel(name = "下单数量")
    private BigDecimal planQty;
    @Excel(name = "实际出库数量")
    private BigDecimal actualQty;

    @Excel(name = "团购出库单状态")
    private String recordStatusName;

    @Excel(name = "交货日期",exportFormat = "yyyy-MM-dd")
    private Date expectReceiveDateStart;

    @Excel(name = "供货工厂")
    private String factoryCode;

    @Excel(name = "仓库编号")
    private String realWarehouseCode;

    @Excel(name = "仓库名称")
    private String realWarehouseName;

    @Excel(name = "创建时间",exportFormat = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;

    @Excel(name = "更新时间",exportFormat = "yyyy-MM-dd HH:mm:ss")
    private Date updateTime;

    private Integer recordStatus;

    private Integer recordType;

    private Long realWarehouseId;

    private String recordTypeName;

    private Date OutCreateTime;
//    @Excel(name = "商品ID")
    private Long skuId;

}
