package com.rome.stock.innerservice.api.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

@Data
@EqualsAndHashCode
public class VirtualWarehouseFlowRecord extends Pagination {
    /**
     * 唯一主键
     */
    @ApiModelProperty(value = "主键")
    private Long id;
    /**
     * 单号
     */
    @ApiModelProperty(value = "单号")
    private String recordCode;

    /**
     * 出入库单据号
     */
    @ApiModelProperty(value = "出入库单据号")
    private List<String> recordCodeList;

    /**
     * 虚拟仓库id
     */
    @ApiModelProperty(value = "虚拟仓库id")
    private Long virtualWarehouseId;
    /**
     * 虚拟仓库code
     */
    @ApiModelProperty(value = "虚拟仓库code")
    private String virtualWarehouseCode;
    /**
     * 虚拟仓库name
     */
    @ApiModelProperty(value = "虚拟仓库name")
    private String virtualWarehouseName;

    /**
     * 虚仓标签
     */
    @ApiModelProperty(value = "虚仓标签")
    private String virtualWarehouseType;

    /**
     * 实仓id
     */
    @ApiModelProperty(value = "实仓id")
    private Long realWarehouseId;
    /**
     * 实仓code
     */
    @ApiModelProperty(value = "实仓code")
    private String realWarehouseCode;
    /**
     * 实仓name
     */
    @ApiModelProperty(value = "实仓name")
    private String realWarehouseName;

    /**
     * 关联的实仓编码
     */
    @ApiModelProperty(value = "关联的实仓code")
    private  String  relationWarehouseCode;

    /**
     * 起始时间
     */
    @ApiModelProperty(value = "起始时间")
    private Date startTime;
    /**
     * 结束时间
     */
    @ApiModelProperty(value = "结束时间")
    private Date endTime;
    /**
     * 商品id
     */
    @ApiModelProperty(value = "商品id")
    private Long skuId;
    /**
     * 商品编码
     */
    @ApiModelProperty(value = "商品编码")
    private String skuCode;
    /**
     * 商品名称
     */
    @ApiModelProperty(value = "商品名称")
    private String skuName;
    /**
     * 商品单位
     */
    @ApiModelProperty(value = "商品单位")
    private String skuUnit;
    /**
     * 变更数量
     */
    @ApiModelProperty(value = "变更数量")
    private BigDecimal changeQty;
    /**
     * 前置单据类型
     */
    @ApiModelProperty(value = "前置单据类型")
    private Integer frontRecordType;
    /**
     * 前置单据类型名称
     */
    @ApiModelProperty(value = "前置单据类型名称")
    private String frontRecordTypeName;
    /**
     * 后置单据类型
     */
    @ApiModelProperty(value = "后置单据类型")
    private Long warehouseRecordType;

    /**
     * 库存类型
     */
    @ApiModelProperty(value = "库存类型")
    private Integer stockType;
    /**
     * 库存类型名称
     */
    @ApiModelProperty(value = "库存类型名称")
    private String stockTypeName;
    /**
     * 创建时间
     */
    @ApiModelProperty(value = "创建时间")
    private Date createTime;
    /**
     * 商品ids
     */
    @ApiModelProperty(value = "商品ids")
    private List<Long> skuIds;
    
    /**
     * 变更前库存数量
     */
    @ApiModelProperty(value = "变更前库存数量")
    private BigDecimal beforeChangeQty;
    
    /**
     * 变更后库存数量
     */
    @ApiModelProperty(value = "变更后库存数量")
    private BigDecimal afterChangeQty;
    
    /**
     * 渠道ID
     */
    @ApiModelProperty(value = "渠道ID")
    private String channelCode;
    
    /**
	 * 库存类型
	 */
	@ApiModelProperty(value = "仓库出入库类型")
	private List<Integer> stockTypes;
	
	/**
	 * 库存交易类型
	 */
	@ApiModelProperty(value = "库存交易类型")
	private List<Integer> transTypes;
	
	/**
     * 交易类型，实质单据类型
     */
    @ApiModelProperty(value = "交易类型，实质单据类型")
    private Integer transType;
    
    /**
     * 交易类型，实质单据类型名
     */
    @ApiModelProperty(value = "交易类型，实质单据类型")
    private String transTypeName;

    /**
     * 仓库ID
     */
    @ApiModelProperty(value = "实仓仓库ID")
    private List<Long>  realWarehouseIds;

    /**
     * 仓库ID
     */
    @ApiModelProperty(value = "虚仓仓库ID")
    private List<Long>  virtualWarehouseIds;

    /**
     * 实仓编号
     */
    @ApiModelProperty(value = "实仓编号")
    private List<String>	realWarehouseCodes;

    /**
     * 虚仓编号
     */
    @ApiModelProperty(value = "虚仓编号")
    private List<String> virtualWarehouseCodes;

    /**
     * 交易单号
     */
    @ApiModelProperty(value = "交易单号")
    private List<String> outRecordCodeList;

    /**
     * 交易单号
     */
    @ApiModelProperty(value = "交易单号")
    private String outRecordCode;

    /**
     * 关联单据编码
     */
    @ApiModelProperty(value = "关联单据编码")
    private List<String> relationRecordCodeList;

    /**
     * 关联单据
     */
    @ApiModelProperty(value = "关联单据")
    private List<RecordRelationResultDTO> recordRelationList;

}
