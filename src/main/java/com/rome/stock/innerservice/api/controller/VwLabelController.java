/**
 * Filename VwLabelController.java
 * Company 上海来伊份科技有限公司。
 * <AUTHOR>
 * @version 
 */
package com.rome.stock.innerservice.api.controller;

import com.github.pagehelper.PageInfo;
import com.rome.arch.core.clientobject.Response;
import com.rome.arch.core.exception.RomeException;
import com.rome.arch.util.controller.RomeController;
import com.rome.stock.common.annotation.BathParamsValidate;
import com.rome.stock.innerservice.api.dto.*;
import com.rome.stock.innerservice.common.ResCode;
import com.rome.stock.innerservice.constant.ResponseMsg;
import com.rome.stock.innerservice.domain.service.VwLabelService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiResponse;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2022-7-11 11:56:55
 */
@Slf4j
@RomeController
@RequestMapping("/stock/vw_label_query")
@Api(tags={"虚仓标签接口"})
public class VwLabelController {
	
	@Autowired
    private VwLabelService vwLabelService;

	@ApiOperation(value = "根据条件查询", nickname = "queryCondition", produces = MediaType.APPLICATION_JSON_VALUE)
    @ApiResponse(code = 200, message = "success")
    @RequestMapping(value = "/queryCondition", method = RequestMethod.POST)
    public Response<PageInfo<KpChannelVwTypeRelationDTO>> queryCondition(@RequestBody KpChannelVwTypeRelationDTO paramDto) {
        try {
            PageInfo<KpChannelVwTypeRelationDTO> dtoList = vwLabelService.queryCondition(paramDto);
            return ResponseMsg.SUCCESS.buildMsg(dtoList);
        } catch (RomeException e) {
            log.error(e.getMessage(), e);
            return ResponseMsg.FAIL.buildMsg(e.getCode(),e.getMessage());
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return ResponseMsg.EXCEPTION.buildMsg(ResCode.STOCK_ERROR_1003, ResCode.STOCK_ERROR_1003_DESC + e.getMessage());
        }
    }
	
	@ApiOperation(value = "保存鲲鹏渠道与虚仓类型关系", nickname = "saveKpChannelVwTypeRelation", produces = MediaType.APPLICATION_JSON_VALUE)
    @ApiResponse(code = 200, message = "success")
    @RequestMapping(value = "/saveKpChannelVwTypeRelation", method = RequestMethod.POST)
    public Response saveKpChannelVwTypeRelation(@RequestBody KpChannelVwTypeRelationDTO paramDto) {
        try {
            vwLabelService.saveKpChannelVwTypeRelation(paramDto);
            return ResponseMsg.SUCCESS.buildMsg("success");
        } catch (RomeException e) {
            log.error(e.getMessage(), e);
            return ResponseMsg.FAIL.buildMsg(e.getCode(),e.getMessage());
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return ResponseMsg.EXCEPTION.buildMsg(ResCode.STOCK_ERROR_1003, ResCode.STOCK_ERROR_1003_DESC + e.getMessage());
        }
    }
	
	@ApiOperation(value = "查询鲲鹏渠道与虚仓类型关系编辑日志", nickname = "getOperatorLog", produces = MediaType.APPLICATION_JSON_VALUE)
    @ApiResponse(code = 200, message = "success")
    @RequestMapping(value = "/getOperatorLog", method = RequestMethod.GET)
    public Response<List<SapInterfaceLogDTO>> getOperatorLog(@RequestParam("kpChannelCode") String kpChannelCode) {
        try {
        	List<SapInterfaceLogDTO> list = vwLabelService.getOperatorLog(kpChannelCode);
            return ResponseMsg.SUCCESS.buildMsg(list);
        } catch (RomeException e) {
            log.error(e.getMessage(), e);
            return ResponseMsg.FAIL.buildMsg(e.getCode(),e.getMessage());
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return ResponseMsg.EXCEPTION.buildMsg(ResCode.STOCK_ERROR_1003, ResCode.STOCK_ERROR_1003_DESC + e.getMessage());
        }
    }
	
	@ApiOperation(value = "根据id批量删除关系", nickname = "deleteRelation", produces = MediaType.APPLICATION_JSON_VALUE)
    @ApiResponse(code = 200, message = "success", response = Response.class)
    @RequestMapping(value = "/deleteRelation", method = RequestMethod.DELETE)
    public Response deleteRelation(@RequestBody List<Long> ids, @RequestParam("modifier") Long modifier) {
        try {
            return Response.builderSuccess(vwLabelService.deleteRelation(ids, modifier));
        } catch (RomeException e) {
            log.error(e.getMessage(), e);
            return Response.builderFail(e.getCode(), e.getMessage());
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return Response.builderFail(ResCode.STOCK_ERROR_1003_DESC, ResCode.STOCK_ERROR_1003_DESC);
        }
    }


    @ApiOperation(value = "根据鲲鹏渠道查询鲲鹏渠道与虚仓类型关系表", nickname = "queryByKpChannelCode", produces = MediaType.APPLICATION_JSON_VALUE)
    @ApiResponse(code = 200, message = "success", response = QueryKpVwRelationResDTO.class)
    @PostMapping("/queryByKpChannelCode")
    public Response<KpChannelVwTypeRelationDTO> queryByKpChannelCode(@RequestParam("kpChannelCode") String kpChannelCode){
        try{
            return ResponseMsg.SUCCESS.buildMsg(vwLabelService.queryByKpChannelCode(kpChannelCode));
        }catch (RomeException e){
            log.error(e.getMessage(), e);
            return ResponseMsg.FAIL.buildMsg(e.getCode(), e.getMessage());
        }catch (Exception e){
            log.error(e.getMessage(), e);
            return ResponseMsg.EXCEPTION.buildMsg(e.getMessage());
        }
    }

    @ApiOperation(value = "根据实仓code批量查询鲲鹏渠道code列表(最大个数默认100)", nickname = "selectKpChannelByRealWarehouses", produces = MediaType.APPLICATION_JSON_VALUE)
    @ApiResponse(code = 200, message = "success", response = List.class)
    @RequestMapping(value = "/selectKpChannelByRealWarehouses", method = RequestMethod.POST)
    @BathParamsValidate
    public Response<List<RealWarehouseKpChannelDTO>> selectKpChannelByRealWarehouses(@RequestParam("realWarehouseCodes") List<String> realWarehouseCodes) {
        try{
            return ResponseMsg.SUCCESS.buildMsg(vwLabelService.selectKpChannelByRealWarehouses(realWarehouseCodes));
        }catch (RomeException ex){
            log.error(ex.getMessage(),ex);
            return Response.builderFail(ex.getCode(), ex.getMessage());
        }catch (Exception ex){
            log.error(ex.getMessage(),ex);
            return ResponseMsg.EXCEPTION.buildMsg();
        }
    }
}
