package com.rome.stock.innerservice.api.dto;


import cn.afterturn.easypoi.excel.annotation.Excel;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;
import java.util.Date;

/**
 * @Program: stock-admin
 * @Description: 旺店通拆单导出模板
 * @Author: Cocoa
 * @Date: 2020/7/27 10:25
 * @Version: v1.0.0
 */
@Data
@EqualsAndHashCode
public class WDTStockSplitExportTemplate {

    @Excel(name = "原始单号")
    private String originOrderCode;

    @Excel(name = "销售订单号")
    private String outRecordCode;

    @Excel(name = "渠道")
    private String channelCode;

    @Excel(name = "渠道名称")
    private String channelName;

//    @Excel(name = "单据状态")
//    private Integer recordStatus;

    @Excel(name = "单据状态")
    private String recordStatusName;

    @Excel(name = "仓库编码")
    private String realWarehouseCode;

    @Excel(name = "仓库名称")
    private String realWarehouseName;

    @Excel(name = "物流公司编码")
    private String logisticsCode;

    @Excel(name = "省市区")
    private String addressSimply;

    @Excel(name = "详细地址")
    private String address;

    @Excel(name = "货品种类数")
    private Integer detailSize;

    @Excel(name = "总物料数")
    private BigDecimal totalQty;

    //@Excel(name = "是否缺货")
    private Integer allotStatus;

    @Excel(name = "是否缺货")
    private String allotStatusName;

    //@Excel(name = "是否预售单")
    private Integer isPreSale;

    @Excel(name = "是否预售单")
    private String isPreSaleName;

    // 缺少  单类型
    private Integer splitType;

    @Excel(name = "单类型")
    private String splitTypeName;


    @Excel(name = "创建时间", exportFormat = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;

    @Excel(name = "支付时间", exportFormat = "yyyy-MM-dd HH:mm:ss")
    private Date payTime;

    @Excel(name = "更新时间", exportFormat = "yyyy-MM-dd HH:mm:ss")
    private Date updateTime ;

}
