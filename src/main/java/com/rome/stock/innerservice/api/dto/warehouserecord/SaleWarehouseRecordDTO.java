package com.rome.stock.innerservice.api.dto.warehouserecord;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

import com.rome.stock.innerservice.api.dto.frontrecord.GroupAddressDTO;

import com.rome.stock.innerservice.domain.entity.warehouserecord.WarehouseRecordDetail;
import com.rome.stock.innerservice.domain.entity.warehouserecord.WarehouseRecordDetailE;
import com.rome.stock.innerservice.infrastructure.dataobject.record.RecordPackageDO;
import lombok.Data;
import lombok.EqualsAndHashCode;

@Data
@EqualsAndHashCode
public class SaleWarehouseRecordDTO {

	private Long id;
	/**
	 * 所属单据编码
	 */
	private String recordCode;
	/**
	 * 业务类型：
	 */
	private Integer businessType;
	/**
	 * do单状态，0 未同步  1 已同步 10 拣货 11 打包 12 装车 13 发运 21 接单 22 配送 23 完成
	 */
	private Integer recordStatus;

	private String recordStatusName;
	/**
	 * 单据类型：1-销售出库订单，2-采购单
	 */
	private Integer recordType;

	/**
	 * 单据类型对应的单据名称
	 */
	private String recordTypeName;
	/**
	 * 用户code
	 */
	private String userCode;
	/**
	 * 虚拟仓库ID
	 */
	private Long virtualWarehouseId;
	/**
	 * 实仓ID
	 */
	private Long realWarehouseId;

	/**
	 * 实仓名称
	 */
	private String realWarehouseName;

	/**
	 * 实仓【门店】地址
	 */
	private String realWarehouseAddress;
	/**
	 * 渠道类型
	 */
	private Long channelType;

	/**
	 * 渠道类型
	 */
	private String channelTypeName;
	/**
	 * 渠道id
	 */
	private String channelCode;

	/**
	 * 渠道id名称
	 */
	private String channelCodeName;
	/**
	 * 商家id
	 */
	private Long merchantId;
	/**
	 * 订单备注(用户)
	 */
	private String orderRemarkUser;
	/**
	 * 外部系统数据创建时间:下单时间
	 */
	private Date outCreateTime;
	/**
	 * 支付时间
	 */
	private Date payTime;
	/**
	 * 收货人姓名
	 */
	private String name;
	/**
	 * 手机号
	 */
	private String mobile;
	/**
	 * 退货流程用：退货原因
	 */
	private String reason;
	/**
	 * 期望收货日期_开始
	 */
	private Date expectReceiveDateStart;
	/**
	 * 期望收货日期_截止
	 */
	private Date expectReceiveDateEnd;
	/**
	 * 撤回原因
	 */
	private String reasons;
	/**
	 * 异常原因
	 */
	private String errorMessage;
	/**
	 * 撤回时间
	 */
	private Date relinquishTime;
	/**
	 * 发货时间
	 */
	private Date deliveryTime;
	/**
	 * 收货时间
	 */
	private Date receiverTime;


	/**
	 * 订单编码
	 */
	private List<String> outRecordCode;

	private Date createTime;

	/**
	 * 商品详情
	 */
	private List<SaleWarehouseRecordDetailDTO> details;

	/**
	 * 同步捋单系统状态 0-无需同步 1-待同步交货信息 2-已同步
	 */
	private Integer syncFulfillmentStatus;

	/**
	 * 出入库单同步WMS状态：0-无需同步 1-未同步 2-已同步
	 */
	private Integer syncWmsStatus;

	/**
	 * 实仓编号
	 */
	private String realWarehouseCode;

	/**
	 * 实仓类型
	 */
	private Integer realWarehouseType;
    /**
     * 创建人
     */
    private Long creator;

	/**
	 * 更新人
	 */
	private Long modifier;

	/**
	 * 更新时间
	 */
	private Date updateTime;

	/**
	 * 原始单号
	 */
	private String originOrderCode;

	/**
	 * 运单号单号
	 */
	private String expressCode;

	/**
	 * 物流公司
	 */
	private String logisticsCode;

	/**
	 * 实际发货物流公司
	 */
	private String realLogisticsCode;
	/**
	 * 省份
	 */
	private String province;
	/**
	 * 城市
	 */
	private String city;
	/**
	 * 区/县城市
	 */
	private String county;
	/**
	 * 省市区
	 */
	private String area;

	/**
	 * 详细地址
	 */
	private String address;

	/**
	 *是否预售
	 */
	private Integer isPreSale;

	/**
	 * 出库或入库完成时间
	 */
	private Date outOrInTime;

	/**
	 * 卖家备注
	 */
	private String sellerMessage;

	/**
	 * 总金额
	 */
	private BigDecimal totalAmount;

	/**
	 * 团长信息
	 */
	private GroupAddressDTO groupAddress;

	/**
	 * 预售-承诺时间
	 */
	private Date promiseTime;

	/**
	 * 包裹明细
	 */
	private List<RecordPackageDO> packages;

	/**
	 * 出库单明细
	 */
	private List<WarehouseRecordDetailE> warehouseRecordDetailList;
}
