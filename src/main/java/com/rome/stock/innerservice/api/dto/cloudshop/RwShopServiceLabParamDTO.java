package com.rome.stock.innerservice.api.dto.cloudshop;

import com.rome.stock.common.page.Pagination;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

@Data
public class RwShopServiceLabParamDTO extends Pagination {

    @ApiModelProperty(value = "仓库id")
    private Long realWarehouseId;

    @ApiModelProperty(value = "仓库CODE")
    private String realWarehouseCode;

    @ApiModelProperty(value = "门店编号")
    private String shopCode;

    @ApiModelProperty(value = "仓库id集合")
    private List<Long> realWarehouseIds;
}
