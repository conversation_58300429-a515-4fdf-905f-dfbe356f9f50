package com.rome.stock.innerservice.api.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;

/**
 * @Author: zhoupeng
 * @createTime: 2022年07月21日 20:50:16
 * @version: 1.0
 * @Description:
 */
@Data
@EqualsAndHashCode
public class ChannelWdtVwRelation  extends  Pagination {

    /**
     * 唯一主键
     */
    @ApiModelProperty(value = "唯一主键")
    private Long id;

    /**
     * 渠道code
     */
    @ApiModelProperty(value = "渠道code")
    private  String   channelCode;


    /**
     * 渠道名称
     */
    @ApiModelProperty(value = "渠道名称")
    private  String   channelName;




    /**
     * 旺店通虚仓编号
     */
    @ApiModelProperty(value = "旺店通虚仓编号")
    private String  wdtVwCode;


    /**
     * 比率（百分比），1-100区间可选数字
     */
    @ApiModelProperty(value = "比率（百分比），1-100区间可选数字")
    private  Integer  syncRate;

    /**
     * 创建人
     */
    @ApiModelProperty(value = "创建人")
    private Long creator;

    /**
     * 创建人
     */
    @ApiModelProperty(value = "更新人")
    private Long modifier;


    /**
     * 创建的开始时间
     */
    @ApiModelProperty("创建的开始时间")
    private Date createStartTime;

    /**
     * 创建的结束时间
     */
    @ApiModelProperty("创建的结束时间")
    private Date createEndTime;

    /**
     * 修改的开始时间
     */
    @ApiModelProperty("修改的开始时间")
    private Date updateStartTime;

    /**
     * 修改的结束时间
     */
    @ApiModelProperty("修改的结束时间")
    private Date updateEndTime;

    /**
     * 创建时间
     */
    @ApiModelProperty("创建时间")
    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
    private Date createTime;

    /**
     * 更新时间
     */
    @ApiModelProperty("更新时间")
    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
    private Date updateTime;

    /**
     * 备注
     */
    @ApiModelProperty("备注")
    private   String   remark;

    /**
     * 虚拟仓库组名称
     */
    @ApiModelProperty("虚拟仓库组名称")
    private  String   name;

    /**
     * 虚拟仓库组编码
     */
    @ApiModelProperty("虚拟仓库组编码")
    private  String    virtualWarehouseGroupCode;

    /**
     * 模板名称
     */
    @ApiModelProperty("模板名称")
    private  String    template;

    /**
     * 模板描述
     */
    @ApiModelProperty("模板描述")
    private   String  templateDesc;










}
