/**
 * Filename TransactionMessageController.java
 * Company 上海来伊份科技有限公司。
 * <AUTHOR>
 * @version 
 */
package com.rome.stock.innerservice.api.controller;

import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;

import com.github.pagehelper.PageInfo;
import com.rome.arch.core.clientobject.Response;
import com.rome.arch.core.exception.RomeException;
import com.rome.arch.util.controller.RomeController;
import com.rome.stock.innerservice.api.dto.TransactionMessageByOrderCostDetailDTO;
import com.rome.stock.innerservice.api.dto.TransactionMessageDTO;
import com.rome.stock.innerservice.common.ResCode;
import com.rome.stock.innerservice.constant.ResponseMsg;
import com.rome.stock.innerservice.domain.service.TransactionMessageService;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import io.swagger.annotations.ApiResponse;
import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 * @since 2022-1-10 15:20:54
 */
@Slf4j
@RomeController
@RequestMapping("/stock/v1/transaction_message")
@Api(tags={"事务消息回查接口"})
public class TransactionMessageController {
	
	@Autowired
    private TransactionMessageService transactionMessageService;

	@ApiOperation(value = "根据条件查询", nickname = "queryByCondition", produces = MediaType.APPLICATION_JSON_VALUE)
    @ApiResponse(code = 200, message = "success", response = List.class)
    @RequestMapping(value = "/queryByCondition", method = RequestMethod.POST)
    public Response<PageInfo<TransactionMessageDTO>> queryByCondition(@ApiParam(name = "condition") @RequestBody TransactionMessageDTO condition) {
        try {
            PageInfo<TransactionMessageDTO> pageList = transactionMessageService.queryByCondition(condition);
            return Response.builderSuccess(pageList);
        } catch (RomeException e) {
            log.error(e.getMessage(), e);
            return ResponseMsg.EXCEPTION.buildMsg(e.getCode(), e.getMessage());
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return ResponseMsg.EXCEPTION.buildMsg(ResCode.STOCK_ERROR_1003, ResCode.STOCK_ERROR_1003_DESC);
        }
    }
	
	@ApiOperation(value = "查询明细(订单成本)，根据业务中台唯一单号", nickname = "getDetailByZtRecordCode", produces = MediaType.APPLICATION_JSON_VALUE)
    @ApiResponse(code = 200, message = "success", response = List.class)
    @RequestMapping(value = "/getDetailByZtRecordCode", method = RequestMethod.POST)
    public Response<List<TransactionMessageByOrderCostDetailDTO>> getDetailByZtRecordCode(@ApiParam(name = "ztRecordCode", value = "中台唯一单号") @RequestParam(value = "ztRecordCode",required = true) String ztRecordCode, @ApiParam(name = "recordCode", value = "出入库单号") @RequestParam(value = "recordCode",required = false) String recordCode) {
        try {
        	List<TransactionMessageByOrderCostDetailDTO> pageList = transactionMessageService.getDetailByZtRecordCode(ztRecordCode, recordCode);
            return Response.builderSuccess(pageList);
        } catch (RomeException e) {
            log.error(e.getMessage(), e);
            return ResponseMsg.EXCEPTION.buildMsg(e.getCode(), e.getMessage());
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return ResponseMsg.EXCEPTION.buildMsg(ResCode.STOCK_ERROR_1003, e.getMessage());
        }
    }
	
}
