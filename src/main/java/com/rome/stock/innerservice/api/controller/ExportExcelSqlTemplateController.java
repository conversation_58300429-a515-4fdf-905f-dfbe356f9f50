package com.rome.stock.innerservice.api.controller;

import com.github.pagehelper.PageInfo;
import com.rome.arch.core.clientobject.Response;
import com.rome.arch.core.exception.RomeException;
import com.rome.stock.innerservice.api.dto.ExportExcelSqlTemplateDTO;
import com.rome.stock.innerservice.common.ResCode;
import com.rome.stock.innerservice.constant.ResponseMsg;
import com.rome.stock.innerservice.export.aspect.AsyncExcelResultContext;
import com.rome.stock.innerservice.export.domain.service.dto.AsyncExportResult;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import com.rome.arch.util.controller.RomeController;

import com.rome.stock.innerservice.domain.service.ExportExcelSqlTemplateService;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiResponse;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.RequestMethod;

@Slf4j
@RomeController
@RequestMapping("/stock/v1/exportExcelSqlTemplate")
@Api(tags={"数据导出服务接口"})
public class ExportExcelSqlTemplateController {

    @Autowired
	private ExportExcelSqlTemplateService exportExcelSqlTemplateService;


    @ApiOperation(value = "分页查询列表数据", nickname = "queryPage", produces = MediaType.APPLICATION_JSON_VALUE)
    @ApiResponse(code = 200, message = "success")
    @RequestMapping(value = "/queryPage", method = RequestMethod.POST)
    public Response queryPage(@RequestBody ExportExcelSqlTemplateDTO exportExcelSqlTemplateDTO) {
        try {
            PageInfo<ExportExcelSqlTemplateDTO> page = exportExcelSqlTemplateService.queryPage(exportExcelSqlTemplateDTO);
            return ResponseMsg.SUCCESS.buildMsg(page);
        } catch (RomeException e) {
            log.error(e.getMessage(), e);
            return ResponseMsg.FAIL.buildMsg(e.getCode(),e.getMessage());
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return ResponseMsg.EXCEPTION.buildMsg(ResCode.STOCK_ERROR_1003_DESC,ResCode.STOCK_ERROR_1003_DESC + e.getMessage());
        }
    }

    @ApiOperation(value = "保持或者新增", nickname = "save", produces = MediaType.APPLICATION_JSON_VALUE)
    @ApiResponse(code = 200, message = "success")
    @RequestMapping(value = "/save", method = RequestMethod.POST)
    public Response save(@RequestBody ExportExcelSqlTemplateDTO exportExcelSqlTemplateDTO) {
        try {
            exportExcelSqlTemplateService.save(exportExcelSqlTemplateDTO);
            return ResponseMsg.SUCCESS.buildMsg("");
        } catch (RomeException e) {
            log.error(e.getMessage(), e);
            return ResponseMsg.FAIL.buildMsg(e.getCode(),e.getMessage());
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return ResponseMsg.EXCEPTION.buildMsg(ResCode.STOCK_ERROR_1003_DESC,ResCode.STOCK_ERROR_1003_DESC + e.getMessage());
        }
    }

    @ApiOperation(value = "删除", nickname = "delete", produces = MediaType.APPLICATION_JSON_VALUE)
    @ApiResponse(code = 200, message = "success")
    @RequestMapping(value = "/delete", method = RequestMethod.POST)
    public Response delete(@RequestBody ExportExcelSqlTemplateDTO exportExcelSqlTemplateDTO) {
        try {
           exportExcelSqlTemplateService.delete(exportExcelSqlTemplateDTO);
            return ResponseMsg.SUCCESS.buildMsg("");
        } catch (RomeException e) {
            log.error(e.getMessage(), e);
            return ResponseMsg.FAIL.buildMsg(e.getCode(),e.getMessage());
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return ResponseMsg.EXCEPTION.buildMsg(ResCode.STOCK_ERROR_1003_DESC,ResCode.STOCK_ERROR_1003_DESC + e.getMessage());
        }
    }



    @ApiOperation(value = "异步导出excel", nickname = "asyncExportExcel", produces = MediaType.APPLICATION_JSON_VALUE)
    @ApiResponse(code = 200, message = "success")
    @RequestMapping(value = "/asyncExportExcel", method = RequestMethod.POST)
    public Response asyncExportExcel(@RequestBody ExportExcelSqlTemplateDTO exportExcelSqlTemplateDTO) {
        try {
            exportExcelSqlTemplateService.asyncExportExcel(exportExcelSqlTemplateDTO);
            AsyncExportResult result  = AsyncExcelResultContext.getAsyncExportResult();
            return ResponseMsg.SUCCESS.buildMsg(result);
        } catch (RomeException e) {
            log.error(e.getMessage(), e);
            return ResponseMsg.FAIL.buildMsg(e.getCode(),e.getMessage());
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return ResponseMsg.EXCEPTION.buildMsg(ResCode.STOCK_ERROR_1003_DESC,ResCode.STOCK_ERROR_1003_DESC + e.getMessage());
        } finally {
            AsyncExcelResultContext.remove();
        }
    }


}
