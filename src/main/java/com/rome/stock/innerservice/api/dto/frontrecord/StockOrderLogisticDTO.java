package com.rome.stock.innerservice.api.dto.frontrecord;

import com.rome.stock.innerservice.api.dto.OnLineStockLogisticInfoParamDTO;
import lombok.Data;

import javax.validation.Valid;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.List;

@Data
public class StockOrderLogisticDTO implements Serializable {

    @Valid
    @NotNull
    private StockOrderDTO stockOrderDTO;

    @Valid
    @NotNull
    private List<OnLineStockLogisticInfoParamDTO> onLineStockLogisticInfoParam;

}
