package com.rome.stock.innerservice.api.dto.warehouserecord;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;

@Data
@EqualsAndHashCode
public class ShopWarehouseRecordDetailDTO {
    /**
     * 唯一主键
     */
    @ApiModelProperty(value = "唯一主键")
    private Long id;
    /**
     * 所属单据编码
     */
    @ApiModelProperty(value = "所属单据编码")
    private String recordCode;
    /**
     * 用户code
     */
    @ApiModelProperty(value = "用户code")
    private String userCode;
    /**
     * 渠道id
     */
    @ApiModelProperty(value = "渠道code")
    private String channelCode;
    /**
     * 店铺id
     */
    @ApiModelProperty(value = "店铺id")
    private Long shopId;
    /**
     * 商品sku编码
     */
    @ApiModelProperty(value = "商品sku编码")
    private Long skuId;
    /**
     * 商品数量
     */
    @ApiModelProperty(value = "商品数量")
    private Long skuQty;
    /**
     * 计件单位
     */
    @ApiModelProperty(value = "计件单位")
    private String unit;
    /**
     * 商品总金额
     */
    @ApiModelProperty(value = "商品总金额")
    private BigDecimal skuItemAmount;
    /**
     * 商品销售单价
     */
    @ApiModelProperty(value = "商品销售单价")
    private BigDecimal skuPriceFinal;
    /**
     * 商品编码
     */
    @ApiModelProperty(value = "商品编码")
    private String code;
    /**
     * 第三方商品编码
     */
    @ApiModelProperty(value = "第三方商品编码")
    private String thirdMerchantProductCode;
    /**
     * 虚拟仓库ID
     */
    @ApiModelProperty(value = "虚拟仓库ID")
    private Long virtualWarehouseId;
    /**
     * 实仓ID
     */
    @ApiModelProperty(value = "实仓ID")
    private Long realWarehouseId;
}
