package com.rome.stock.innerservice.api.dto;

import com.rome.stock.innerservice.api.dto.warehouserecord.ShopReceiptRWBatchDTO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * @Description: <p>
 * @Author: chuwen<PERSON><PERSON>  2019/5/31
 */
@Data
public class ReceiptRecordDTO {
    /**
     * 唯一主键
     */
    private Long id;

    /**
     * po前置单据编号
     */
    private String frontRecordCode;

    /**
     * sap采购单编号
     */
    private String outRecordCode;

    /**
     * 入库单据编号
     */
    private String warehouseRecordCode;

    /**
     * wms单据编号
     */
    private String wmsRecordCode;

    /**
     * 质检同步状态 0:待同步 1:已同步
     */
    private String qualityStatus;


    /**
     * 是否推送订单中心(0.无需同步，1.待推送，2.已推送)
     */
    private Integer syncOrderStatus;

    /**
     * 推送状态(1: 仓库入库 2. 门店入库单 )
     */
    private Integer pushType;

    /**
     * 门店收货直接传过来的批次
     */
    private List<ShopReceiptRWBatchDTO> shopBatchList;

    @ApiModelProperty(value = "操作员编码（工号）",hidden = true)
    private String operatorCode;

    @ApiModelProperty(value = "操作员名称",hidden = true)
    private String operatorName;

    @ApiModelProperty(value = "操作人信息持久化",hidden = true)
    private boolean OperatorPersistence=true;
}
