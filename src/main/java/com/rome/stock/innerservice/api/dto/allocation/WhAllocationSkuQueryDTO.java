package com.rome.stock.innerservice.api.dto.allocation;

import com.rome.stock.innerservice.api.dto.Pagination;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotBlank;
import java.util.List;

/**
 * 查询实物库存,根据skuId列表和realWarehouseId查询库存(仓库调拨专用)
 *
 */
@Data
@EqualsAndHashCode
public class WhAllocationSkuQueryDTO {

    @ApiModelProperty(value = "工厂编号")
    @NotBlank(message="工厂编号不能为空")
    private String factoryCode;

    @ApiModelProperty(value = "外部仓库编号")
    @NotBlank(message="外部仓库编号不能为空")
    private String realWarehouseOutCode;

    @ApiModelProperty(value = "是否质量调拨")
    private Integer isQualityAllocate;

    @ApiModelProperty(value = "skuId列表")
    private List<Long> skuIds;
}
