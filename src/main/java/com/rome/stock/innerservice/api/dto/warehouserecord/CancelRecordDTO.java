package com.rome.stock.innerservice.api.dto.warehouserecord;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 类CancleDTO的实现描述：单据取消DTO
 *
 * <AUTHOR> 2020/6/16 21:37
 */
@Data
@EqualsAndHashCode
public class CancelRecordDTO {

    @ApiModelProperty(value = "外部系统单据编码", required = true)
    @NotBlank(message="单据编码不能为空")
    private String recordCode;

    @ApiModelProperty(value = "单据类型", required = true)
    @NotNull(message="单据类型不能为空")
    private Integer recordType;

    @ApiModelProperty(value = "是否强制取消")
    @NotNull(message="是否强制取消不能为空")
    private Boolean isForceCancel;


}
