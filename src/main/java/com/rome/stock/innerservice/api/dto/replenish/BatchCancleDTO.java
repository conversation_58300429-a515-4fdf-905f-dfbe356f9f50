package com.rome.stock.innerservice.api.dto.replenish;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 类BatchCancleDTO的实现描述：批量取消
 *
 * <AUTHOR> 2020/2/14 16:59
 */
@Data
@EqualsAndHashCode
public class BatchCancleDTO {

    /**
     * 取消列表
     */
    private String list;

    /**
     * 用户ID
     */
    private Long userId;

    /**
     * 物流公司
     */
    private String logisticsCode;

    /**
     * 仓库编号
     */
    private Long realWarehouseId;

    /**
     * 是否默认
     */
    private Integer isDefault;

    /**
     * 更新人ID
     */
    private Long modifier;

    /**
     * 单号
     */
    private String recordCode;
    @ApiModelProperty(value = "是否强制调用接口，1.是")
    private  Integer isForce;
}
