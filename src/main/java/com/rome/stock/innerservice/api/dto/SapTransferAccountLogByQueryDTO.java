/**
 * Filename SapTransferAccountLogDTO.java
 * Company 上海来伊份科技有限公司。
 * <AUTHOR>
 * @version 
 */
package com.rome.stock.innerservice.api.dto;

import java.util.Date;
import java.util.List;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @since 2020-7-25 10:47:10
 */
@Data
public class SapTransferAccountLogByQueryDTO  extends Pagination {
	
	/**
     * 包含count查询
     */
	@ApiModelProperty(value="是否统计条数")
    private boolean count = true;

	@ApiModelProperty(value="主键")
	private Long id;
	
	@ApiModelProperty(value=" 单据编码")
	private String recordCode;

	@ApiModelProperty(value="交货单号单据编码")
	private String sapOrderCode;

	@ApiModelProperty(value=" 单据编码列表")
	private List<String> recordCodeList;

	@ApiModelProperty(value=" 交货单号列表")
	private List<String> sapOrderCodeList;

	@ApiModelProperty(value="收货单据编码过账用")
	private String receiptRecordCode;

	@ApiModelProperty(value="收货单据编码")
	private String wmsRecordCode;


	@ApiModelProperty(value="物料凭证")
	private String zbelnr;

	@ApiModelProperty(value="业务接口编码服务名")
	private String requestService;
	
	@ApiModelProperty(value="过账状态，0:初始 1:成功 2:失败 3:异常")
	private Integer transferStatus;
	
	@ApiModelProperty(value="过账总行数")
	private Integer lineTotal;
	
	@ApiModelProperty(value="错误信息")
	private String errorMsg;
	
	@ApiModelProperty(value="创建时间")
    private Date createTime;
	
	@ApiModelProperty(value="更新时间")
    private Date  updateTime;
	
	@ApiModelProperty(value = "页面大小")
	private Integer pageSize=10;
	
	@ApiModelProperty(value="过账状态")
	private List<Integer> transferStatusList;
	
	@ApiModelProperty(value="单据类型")
	private Integer recordType;
	
	@ApiModelProperty(value="开始时间")
    private Date startTime;
	
	@ApiModelProperty(value="结束时间")
    private Date endTime;
	
	@ApiModelProperty(value="单据类型")
	private List<Integer> recordTypeList;

	@ApiModelProperty(value="仓库编号")
	private String realWarehouseCode;

	@ApiModelProperty(value="仓库ID")
	private Long realWarehouseId;

	@ApiModelProperty(value="物料编号")
	private String skuCode;

	@ApiModelProperty(value="业务类型")
	private Integer businessType;

	/**
	 * 记录手动操作过账的人
	 */
	private String userInfo;


	/**
	 * 中台传入入库日期
	 **/
	private String ztidZtd1;
	/**
	 * sap过账日期
	 **/
	private String ztidGrd2;
	private String rev1;
	private String rev2;
	private String rev3;
	private String rev4;
	private String rev5;
	
	@ApiModelProperty(value="sap采购单据编码")
	private String sapPurchaseRecordCode;

	@ApiModelProperty(value="sap过账物料凭证")
	private String materialCertificate;

}
