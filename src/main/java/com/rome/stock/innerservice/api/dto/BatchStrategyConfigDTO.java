package com.rome.stock.innerservice.api.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

/**
* 批次出库策略配置DTO
*/
@Data
public class BatchStrategyConfigDTO extends Pagination{

        @ApiModelProperty(hidden = true)
        private Long id;

        @ApiModelProperty(value = "出库仓库id")
        private Long outRealWarehouseId;

        @ApiModelProperty(value = "出库仓库编码")
        private String outRealWarehouseCode;

        @ApiModelProperty(value = "出库仓库名称")
        private String outRealWarehouseName;

        @ApiModelProperty(value = "入库仓仓库id")
        private Long inRealWarehouseId;

        @ApiModelProperty(value = "入库仓库编码")
        private String inRealWarehouseCode;

        @ApiModelProperty(value = "入库仓库名称")
        private String inRealWarehouseName;

        @ApiModelProperty(value = "单据入库仓仓库是否全部:1-是")
        private Integer allInRealWarehouse;

        @ApiModelProperty(value = "单据类型")
        private Integer recordType;

        @ApiModelProperty(value = "单据类型是否全部:1-是")
        private Integer allRecordType;

        @ApiModelProperty(value = "出库策略")
        private Integer outStrategy;

        @ApiModelProperty(value = "创建时间")
        private Date createTime;

        @ApiModelProperty(value = "更新时间")
        private Date updateTime;

        @ApiModelProperty(value = "创建人")
        private Long creator;

        @ApiModelProperty(value = "更新人")
        private Long modifier;

        @ApiModelProperty(value = "是否启用:1-启用;0-禁用")
        private Integer isAvailable;
}
