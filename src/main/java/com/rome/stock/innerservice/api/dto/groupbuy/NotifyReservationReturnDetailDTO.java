package com.rome.stock.innerservice.api.dto.groupbuy;

import com.rome.arch.core.dto.DTO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

@Data
public class NotifyReservationReturnDetailDTO extends DTO {

    /**
     * 实际收货数量
     */
    @ApiModelProperty(value="实际收货数量")
    private BigDecimal entryQty;

    /**
     * sku编码
     */
    @ApiModelProperty(value="sku编码")
    private String skuCode;

    /**
     * 单位
     */
    @ApiModelProperty(value="单位")
    private String unit;

    /**
     * 单位编码
     */
    @ApiModelProperty(value="单位编码")
    private String unitCode;
}    
   