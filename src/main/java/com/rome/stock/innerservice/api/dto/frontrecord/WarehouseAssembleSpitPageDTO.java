package com.rome.stock.innerservice.api.dto.frontrecord;

import com.rome.stock.innerservice.api.dto.Pagination;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;

/**
 * 仓库加工
 *
 * <AUTHOR> 2019/4/23 20:29
 */
@Data
@EqualsAndHashCode
public class WarehouseAssembleSpitPageDTO extends Pagination {

    /**
     * 唯一主键
     */
    private Long id;
    /**
     * 单据编号
     */
    private String recordCode;

    /**
     * 关联单据
     */
    private String outRecordCode;


    /**
     * 单据类型
     */
    private Integer recordType;

    /**
     * 单据状态：已入库
     */
    private Integer recordStatus;

    /**
     * 工厂编号
     */
    private String factoryCode;

    /**
     * 工厂名称
     */
    private String factoryName;

    /**
     * 仓库编号
     */
    private String warehouseCode;

    /**
     * 仓库名称
     */
    private String warehouseName;

    /**
     * 备注
     */
    private String remark;

    /**
     * 外部系统数据创建时间
     */
    private Date outCreateTime;

    /**
     * 开始日期
     */
    private Date startDate;

    /**
     * 结束日期
     */
    private Date endDate;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 创建人
     */
    private Long creator;
}
