package com.rome.stock.innerservice.api.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

@Data
@EqualsAndHashCode
public class RefreshSkuQtyDTO {

	@ApiModelProperty(value = "实仓编码", required = true)
    private String rwCode;

    @ApiModelProperty(value = "SKU编码集合", required = true)
    private List<String> skuCodes;

}
