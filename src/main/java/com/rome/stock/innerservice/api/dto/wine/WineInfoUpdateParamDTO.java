package com.rome.stock.innerservice.api.dto.wine;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.rome.stock.innerservice.api.dto.FileInfoDto;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * <p>
 * Description: 更新附加信息入参DTO
 * </p>
 *
 * <AUTHOR>
 * @date 2023/4/26
 **/
@Data
@EqualsAndHashCode
public class WineInfoUpdateParamDTO {

    @ApiModelProperty(value = "唯一主键")
    private Long id;

    @ApiModelProperty(value = "开始熟成时间")
    @JsonFormat(pattern="yyyy-MM-dd",timezone = "GMT+8")
    private Date startRipeTime;

    @ApiModelProperty(value = "抽样时间")
    @JsonFormat(pattern="yyyy-MM-dd",timezone = "GMT+8")
    private Date sampleTime;

    @ApiModelProperty(value = "酒精度")
    private String vol;

    @ApiModelProperty(value = "酒体积")
    private String volume;

    @ApiModelProperty(value = "体积单位")
    private String volumeUnit;

    @ApiModelProperty(value = "酒单位(公斤)")
    private String unit;

    @ApiModelProperty(value = "酒单位编码(KG)")
    private String unitCode;

    @ApiModelProperty(value = "更新桶序列号")
    private String newSerialNo;

    @ApiModelProperty(value = "附件url")
    private List<FileInfoDto> linkUrl;

    @ApiModelProperty(value = "备注")
    private String remark;

    @ApiModelProperty(value = "修改人")
    private Long modifier;
    @ApiModelProperty(value = "是否强制调用接口，1.是")
    private  Integer isForce;
}
