package com.rome.stock.innerservice.api.controller;

import com.github.pagehelper.PageInfo;
import com.rome.arch.core.clientobject.Response;
import com.rome.arch.core.exception.RomeException;
import com.rome.arch.util.controller.RomeController;
import com.rome.stock.innerservice.api.dto.FrSaleWineLogDTO;
import com.rome.stock.innerservice.api.dto.FrSaleWineLogParamDTO;
import com.rome.stock.innerservice.domain.service.FrSaleWineLogService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import io.swagger.annotations.ApiResponse;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;

/**
 * <p>
 * Description: 酒日志信息
 * </p>
 *
 * <AUTHOR>
 * @date 2023/4/25
 **/
@Slf4j
@RomeController
@RequestMapping(value = "/stock/v1/saleWineLog")
@Api(tags = {"酒日志信息"})
public class FrSaleWineLogController {

    @Autowired
    private FrSaleWineLogService frSaleWineLogService;

    @ApiOperation(value = "分页查询酒日志", nickname = "queryFrSaleWineLog", produces = MediaType.APPLICATION_JSON_VALUE)
    @ApiResponse(code = 200, message = "success")
    @RequestMapping(value = "/queryFrSaleWineLog", method = RequestMethod.POST)
    public Response<PageInfo<FrSaleWineLogDTO>> queryFrSaleWineLog(@ApiParam(name = "condition", value = "查询条件") @RequestBody FrSaleWineLogParamDTO paramDTO) {
        try {
            PageInfo<FrSaleWineLogDTO> list = frSaleWineLogService.queryFrSaleWineLog(paramDTO);
            return Response.builderSuccess(list);
        } catch (RomeException e) {
            log.error(e.getMessage(), e);
            return Response.builderFail(e.getCode(), e.getMessage());
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return Response.builderFail("500000", "系统异常");
        }
    }
}
