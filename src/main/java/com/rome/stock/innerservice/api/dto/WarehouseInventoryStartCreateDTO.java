package com.rome.stock.innerservice.api.dto;


import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.util.List;

@ApiModel(value = "WarehouseInventoryStartCreateDTO", description = "启动盘点信息")
@Data
@EqualsAndHashCode
public class WarehouseInventoryStartCreateDTO implements Serializable {

    private static final long serialVersionUID = 6241269605258899670L;


    @ApiModelProperty(value = "盘点单Id")
    private Long id;

    @ApiModelProperty(value = "需要启动盘点的仓库id集合")
    private List<Long> realWarehouseIds;
    @ApiModelProperty(value = "需要启动盘点的仓库id")
    private Long realWarehouseId;

    @ApiModelProperty(value = "差异处理类型:1-以实盘数更新;2-以差异数更新")
    private Integer diffProccessType;

    @ApiModelProperty(value = "序列号，绑定盘点单")
    private String serialNo;

    @ApiModelProperty(value = "操作人")
    private Long creator;

}
