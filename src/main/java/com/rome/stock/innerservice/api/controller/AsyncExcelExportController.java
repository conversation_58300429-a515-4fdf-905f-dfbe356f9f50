package com.rome.stock.innerservice.api.controller;

import com.google.common.collect.Lists;
import com.rome.arch.core.clientobject.Response;
import com.rome.stock.innerservice.export.domain.service.AsyncExportService;
import com.rome.stock.innerservice.export.domain.service.dto.AsyncExportResult;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

/**
 * <p>
 * Description:
 * </p>
 *
 * <AUTHOR>
 * @date 2021/10/27
 **/
@Api(tags = "异步Excel导出任务")
@Slf4j
@RestController
@Validated
@RequestMapping("/stock/v1/async/excel")
public class AsyncExcelExportController {
    @Resource
    private AsyncExportService asyncExportService;

    /**
     * 保存异步任务
     * @param asyncExportParam
     * @return      新增异步任务
     */
    @ApiOperation("保存异步导出任务")
    @PostMapping("/saveTask")
    public Response<AsyncExportResult> saveExportTask(@RequestBody AsyncExportResult asyncExportParam){
        AsyncExportResult result = asyncExportService.saveExportTask(asyncExportParam);
        return Response.builderSuccess(result);
    }


    /**
     * 获取当前用户的任务列表
     * @return    用户任务列表
     */
    @ApiOperation("获取当前用户的任务列表")
    @GetMapping("/getUserAllTask")
    public Response<List<AsyncExportResult>> getUserAllTask(Long userId){
        List<AsyncExportResult> taskList = asyncExportService.getUserExportTask(userId);
        return Response.builderSuccess(taskList);
    }

    /**
     * 获取当前用户的任务列表
     * @return    用户任务列表
     */
    @ApiOperation("获取当前用户的任务列表")
    @GetMapping("/getUserTask")
    public Response<List<AsyncExportResult>> getUserTask(Long userId){
        List<AsyncExportResult> taskList = asyncExportService.getUserExportTask(userId);
        return Response.builderSuccess(taskList);
    }


    /**
     * 删除任务
     * @param userId
     * @param fid
     * @return
     */
    @ApiOperation("删除任务")
    @DeleteMapping("/deleteUserTask")
    public Response<List<AsyncExportResult>> deleteUserTask(@RequestParam("fid") Long fid, @RequestParam("userId")Long userId){
        List<Long> fidList = Lists.newArrayList(fid);
        List<AsyncExportResult> taskList = asyncExportService.deleteExportTask(userId, fidList);
        return Response.builderSuccess(taskList);
    }

}
