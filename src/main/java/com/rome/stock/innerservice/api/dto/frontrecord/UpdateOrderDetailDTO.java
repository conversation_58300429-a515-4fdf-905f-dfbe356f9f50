package com.rome.stock.innerservice.api.dto.frontrecord;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotBlank;
import java.io.Serializable;

@Data
@EqualsAndHashCode
public class UpdateOrderDetailDTO  implements Serializable {
    @NotBlank
    @ApiModelProperty(value = "行号" ,required = true)
    private String lineNo;

}
