package com.rome.stock.innerservice.api.dto.frontrecord;

import com.fasterxml.jackson.annotation.JsonIgnore;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.util.Date;
import java.util.List;

/**
 * 仓库加工单
 */
@Data
@EqualsAndHashCode
public class WarehouseAssembleSpitRecordDTO {

    @ApiModelProperty(value = "单据编码")
    @NotBlank(message = "单据编码不能为空")
    private String outRecordCode;

    @ApiModelProperty(value = "单据创建时间")
    @NotNull(message="单据创建时间不能为空")
    private Date outCreateTime;

    @JsonIgnore
    private int recordType;

    @ApiModelProperty(value = "工厂编号")
    @NotNull(message="工厂不能为空")
    private String factoryCode;

    @ApiModelProperty(value = "仓库编号")
    @NotNull(message="仓库编号不能为空")
    private String warehouseCode;

    @ApiModelProperty(value = "备注信息")
    private String remark;

    @ApiModelProperty(value = "sku数量及WarehouseAssembleSpitDetailDTO明细")
    @NotNull(message="sku数量及明细不能为空")
    private List<WarehouseAssembleSpitDetailDTO> frontRecordDetails;
}
