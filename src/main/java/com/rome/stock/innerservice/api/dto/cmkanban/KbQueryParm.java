package com.rome.stock.innerservice.api.dto.cmkanban;

import java.util.Date;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 类KbQueryParm的实现描述：TODO 类实现描述
 *
 * <AUTHOR> 2020/7/13 21:26
 */
@Data
@EqualsAndHashCode
public class KbQueryParm {

    @ApiModelProperty(value = "出库单创建开始时间")
    private Date startTime;
    @ApiModelProperty(value = "出库单创建结束时间")
    private Date endTime;
    @ApiModelProperty(value = "渠道")
    private String channelCodes;
    @ApiModelProperty(value = "实仓id")
    private Long realWarehouseId;
    @ApiModelProperty(value = "实仓Code")
    private String realWarehouseCode;
    /**
     * 订单类型
     */
    private Integer orderType;
}
