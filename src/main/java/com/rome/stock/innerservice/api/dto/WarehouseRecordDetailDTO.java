package com.rome.stock.innerservice.api.dto;

import com.fasterxml.jackson.annotation.JsonIgnore;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

@Data
@EqualsAndHashCode
public class WarehouseRecordDetailDTO {
    /**
     * 唯一主键
     */
    @ApiModelProperty(value = "唯一主键")
    private Long id;

    /**
     * 所属单据编码
     */
    @ApiModelProperty(value = "所属单据编码")
    private String recordCode;

    @ApiModelProperty(value = "所属单据id")
    private Long warehouseRecordId;

    @ApiModelProperty(value = "商品批次明细")
    private List<BatchStockDTO> batchStocks;
    /**
     * 用户code
     */
    @JsonIgnore
    @ApiModelProperty(value = "用户code")
    private String userCode;
    /**
     * 渠道id
     */
    @ApiModelProperty(value = "渠道id")
    private String channelId;

    @ApiModelProperty(value = "渠道编码")
    private String channelCode;
    /**
     * 店铺id
     */
    @ApiModelProperty(value = "店铺id")
    private Long shopId;
    /**
     * 商品sku编码
     */
    @ApiModelProperty(value = "商品sku主键")
    private Long skuId;

    /**
     * sku编码
     */
    @ApiModelProperty(value = "sku编码")
    private String skuCode;

    private String skuPicPath;

    private String skuCname;

    private String skuEname;

    /**
     * 商品名称
     */
    @ApiModelProperty(value = "商品名称")
    private String skuName;

    /**
     * 计划出/入库数量
     * */
    private BigDecimal planQty;

    /**
     * 实际出/入库数量
     * */
    private BigDecimal actualQty;

    /**
     * 计件单位
     */
    @ApiModelProperty(value = "计件单位名称")
    private String unit;

    /**
     * 计件单位
     */
    @ApiModelProperty(value = "计件单位")
    private String unitCode;
    /**
     * 商品总金额
     */
    @ApiModelProperty(value = "商品总金额")
    private BigDecimal skuItemAmount;
    /**
     * 商品销售单价
     */
    @ApiModelProperty(value = "商品销售单价")
    private BigDecimal skuPriceFinal;
    /**
     * 商品编码
     */
    @ApiModelProperty(value = "商品编码")
    private String code;
    /**
     * 第三方商品编码
     */
    @ApiModelProperty(value = "第三方商品编码")
    private String thirdMerchantProductCode;
    /**
     * 虚拟仓库ID
     */
    @ApiModelProperty(value = "虚拟仓库ID")
    private Long virtualWarehouseId;
    /**
     * 实仓ID
     */
    @ApiModelProperty(value = "实仓ID")
    private Long realWarehouseId;

    private String lineNo;


    /**
     *
     * 目前大仓采购使用
     * */
    private Integer overReceiveRatio;

    /**
     *  交货不足比例 1-100区间可选数字,目前大仓采购在使用
     * */
    private Integer insufficientRatio;

    private Integer lineStatus;

    /**
     * 实际单位的数量
     */
    private BigDecimal skuQty;

    private Date deliveryData;

    /**
     * 商品条码
     */
    private String barCode;

    /**
     * 中台单行号
     */
    private String ztLineNo;

    /**
     * 交货单行号
     * */
    private String deliveryLineNo;
    
    
    /**
     * sap采购单号
     */
    private String sapPoNo;

    /**
     * 单位比例关系
     */
    private BigDecimal scale;


    /**
     * 采购单位
     */
    private String purchaseUnit;

    /**
     * 退货原因
     */
    private String reason;

    /**
     * 超收比例
     */
    private Integer exceedRaio;

    /**
     * 是否需要质检，外采用
     */
    private Integer needInspect;
    /**
     * 退货原因code
     */
    private String reasonCode;

    /**
     * remark
     */
    private String remark;


    /**
     * 打包码（加盟或直营补货单出库单下发大福新增打包码）
     */
    private String packageCode;



    @ApiModelProperty(value = "最大效期 分子,要求批次有效期需传,否则为null")
    private Integer molecule;

    @ApiModelProperty(value = "最大效期 分母,要求批次有效期需传,否则为null")
    private Integer denominator;




    @ApiModelProperty(value = "最小效期 分子,要求批次有效期需传,否则为null")
    private Integer lowerMolecule;

    @ApiModelProperty(value = "最小效期 分母,要求批次有效期需传,否则为null")
    private Integer lowerDenominator;


    @ApiModelProperty(value = "冗余天数")
    private Integer transDay;

    @ApiModelProperty(value = "大包装组合-7字码物料ID")
    private Long largeSkuId;

    @ApiModelProperty(value = "大包装组合-7字码物料skuCode")
    private String largeSkuCode;

    @ApiModelProperty(value = "大包装组合-7字码物料名称")
    private String largeSkuName;

    @ApiModelProperty(value = "大包装组合-7字码物料单位")
    private String largeSkuUnit;

    @ApiModelProperty(value = "大包装组合-7字码物料单位Code")
    private String largeSkuUnitCode;

    @ApiModelProperty(value = "大包装组合-7字码对应2字码的转换比")
    private BigDecimal largeSkuScale;

    @ApiModelProperty(value = "采购单信息")
    private PurchaseRequest entrySubscribeVo;

}
