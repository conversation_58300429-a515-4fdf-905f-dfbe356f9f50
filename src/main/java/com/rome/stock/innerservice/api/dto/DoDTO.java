package com.rome.stock.innerservice.api.dto;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR>
 */
public class DoDTO implements Serializable{

    private static final long serialVersionUID = -6660138986871951015L;

    /**
     * do 编号
     */
    private String doCode;

    /**
     * 订单code
     */
    private String orderCode;

    /**
     * 订单类型（商品维度）: 0 普通 1 生鲜类
     */
    private Integer orderType;
    /**
     * 订单来源  0：普通 1：团购 4 拼单
     */
    private Integer orderSource;
    /**
     * 订单渠道 : 订单来源渠道 ：1 pc   2 android    3  微信    4 ios    5 h5
     */
    private Integer orderChannel;
    /**
     * 用户id
     */
    private Long userId;
    /**
     * 商家id
     */
    private Long merchantId;
    /**
     * 订单金额(不含运费/运费险)
     */
    private BigDecimal orderAmount;
    /**
     * 优惠总金额
     */
    private BigDecimal discountAmount;
    /**
     * 运费(实收)
     */
    private BigDecimal orderDeliveryFee;
    /**
     * 收货人地址
     */
    private String goodReceiverAddress;
    /**
     * 收货人地址邮编
     */
    private String goodReceiverPostcode;
    /**
     * 收货人姓名
     */
    private String goodReceiverName;
    /**
     * 收货人手机
     */
    private String goodReceiverMobile;
    /**
     * 收货人电话
     */
    private String goodReceiverPhone;
    /**
     * 收货人省份id
     */
    private Long goodReceiverProvinceId;
    /**
     * 收货人省份
     */
    private String goodReceiverProvince;
    /**
     * 收货人省份code
     */
    private String goodReceiverProvinceCode;
    /**
     * 收货人城市id
     */
    private Long goodReceiverCityId;
    /**
     * 收货人城市
     */
    private String goodReceiverCity;
    /**
     * 收货人城市code
     */
    private String goodReceiverCityCode;
    /**
     * 收货人地区id
     */
    private Long goodReceiverCountyId;
    /**
     * 收货人地区
     */
    private String goodReceiverCounty;
    /**
     * 收货人地区code
     */
    private String goodReceiverCountyCode;
    /**
     * 收货人四级区域id
     */
    private Long goodReceiverAreaId;
    /**
     * 收货人四级区域
     */
    private String goodReceiverArea;
    /**
     * 收货人四级区域code
     */
    private String goodReceiverAreaCode;
    /**
     * 仓库code
     */
    private String warehouseCode;
    /**
     * 仓库名称
     */
    private String warehouseName;
    /**
     * 系统来源
     */
    private String sysSource;

    /**
     * do创建时间
     */
    private Long doCreatTime;

    
    /**
     * 渠道编码
     */
    private String channelCode;

    public String getDoCode() {
        return doCode;
    }

    public void setDoCode(String doCode) {
        this.doCode = doCode;
    }

    public String getOrderCode() {
        return orderCode;
    }

    public void setOrderCode(String orderCode) {
        this.orderCode = orderCode;
    }

    public Integer getOrderType() {
        return orderType;
    }

    public void setOrderType(Integer orderType) {
        this.orderType = orderType;
    }

    public Integer getOrderSource() {
        return orderSource;
    }

    public void setOrderSource(Integer orderSource) {
        this.orderSource = orderSource;
    }

    public Integer getOrderChannel() {
        return orderChannel;
    }

    public void setOrderChannel(Integer orderChannel) {
        this.orderChannel = orderChannel;
    }

    public Long getUserId() {
        return userId;
    }

    public void setUserId(Long userId) {
        this.userId = userId;
    }

    public Long getMerchantId() {
        return merchantId;
    }

    public void setMerchantId(Long merchantId) {
        this.merchantId = merchantId;
    }

    public BigDecimal getOrderAmount() {
        return orderAmount;
    }

    public void setOrderAmount(BigDecimal orderAmount) {
        this.orderAmount = orderAmount;
    }

    public BigDecimal getDiscountAmount() {
        return discountAmount;
    }

    public void setDiscountAmount(BigDecimal discountAmount) {
        this.discountAmount = discountAmount;
    }

    public BigDecimal getOrderDeliveryFee() {
        return orderDeliveryFee;
    }

    public void setOrderDeliveryFee(BigDecimal orderDeliveryFee) {
        this.orderDeliveryFee = orderDeliveryFee;
    }

    public String getGoodReceiverAddress() {
        return goodReceiverAddress;
    }

    public void setGoodReceiverAddress(String goodReceiverAddress) {
        this.goodReceiverAddress = goodReceiverAddress;
    }

    public String getGoodReceiverPostcode() {
        return goodReceiverPostcode;
    }

    public void setGoodReceiverPostcode(String goodReceiverPostcode) {
        this.goodReceiverPostcode = goodReceiverPostcode;
    }

    public String getGoodReceiverName() {
        return goodReceiverName;
    }

    public void setGoodReceiverName(String goodReceiverName) {
        this.goodReceiverName = goodReceiverName;
    }

    public String getGoodReceiverMobile() {
        return goodReceiverMobile;
    }

    public void setGoodReceiverMobile(String goodReceiverMobile) {
        this.goodReceiverMobile = goodReceiverMobile;
    }

    public String getGoodReceiverPhone() {
        return goodReceiverPhone;
    }

    public void setGoodReceiverPhone(String goodReceiverPhone) {
        this.goodReceiverPhone = goodReceiverPhone;
    }

    public Long getGoodReceiverProvinceId() {
        return goodReceiverProvinceId;
    }

    public void setGoodReceiverProvinceId(Long goodReceiverProvinceId) {
        this.goodReceiverProvinceId = goodReceiverProvinceId;
    }

    public String getGoodReceiverProvince() {
        return goodReceiverProvince;
    }

    public void setGoodReceiverProvince(String goodReceiverProvince) {
        this.goodReceiverProvince = goodReceiverProvince;
    }

    public String getGoodReceiverProvinceCode() {
        return goodReceiverProvinceCode;
    }

    public void setGoodReceiverProvinceCode(String goodReceiverProvinceCode) {
        this.goodReceiverProvinceCode = goodReceiverProvinceCode;
    }

    public Long getDoCreatTime() {
        return doCreatTime;
    }

    public void setDoCreatTime(Long doCreatTime) {
        this.doCreatTime = doCreatTime;
    }

    public Long getGoodReceiverCityId() {
        return goodReceiverCityId;
    }

    public void setGoodReceiverCityId(Long goodReceiverCityId) {
        this.goodReceiverCityId = goodReceiverCityId;
    }

    public String getGoodReceiverCity() {
        return goodReceiverCity;
    }

    public void setGoodReceiverCity(String goodReceiverCity) {
        this.goodReceiverCity = goodReceiverCity;
    }

    public String getGoodReceiverCityCode() {
        return goodReceiverCityCode;
    }

    public void setGoodReceiverCityCode(String goodReceiverCityCode) {
        this.goodReceiverCityCode = goodReceiverCityCode;
    }

    public Long getGoodReceiverCountyId() {
        return goodReceiverCountyId;
    }

    public void setGoodReceiverCountyId(Long goodReceiverCountyId) {
        this.goodReceiverCountyId = goodReceiverCountyId;
    }

    public String getGoodReceiverCounty() {
        return goodReceiverCounty;
    }

    public void setGoodReceiverCounty(String goodReceiverCounty) {
        this.goodReceiverCounty = goodReceiverCounty;
    }

    public String getGoodReceiverCountyCode() {
        return goodReceiverCountyCode;
    }

    public void setGoodReceiverCountyCode(String goodReceiverCountyCode) {
        this.goodReceiverCountyCode = goodReceiverCountyCode;
    }

    public Long getGoodReceiverAreaId() {
        return goodReceiverAreaId;
    }

    public void setGoodReceiverAreaId(Long goodReceiverAreaId) {
        this.goodReceiverAreaId = goodReceiverAreaId;
    }

    public String getGoodReceiverArea() {
        return goodReceiverArea;
    }

    public void setGoodReceiverArea(String goodReceiverArea) {
        this.goodReceiverArea = goodReceiverArea;
    }

    public String getGoodReceiverAreaCode() {
        return goodReceiverAreaCode;
    }

    public void setGoodReceiverAreaCode(String goodReceiverAreaCode) {
        this.goodReceiverAreaCode = goodReceiverAreaCode;
    }

    public String getWarehouseCode() {
        return warehouseCode;
    }

    public void setWarehouseCode(String warehouseCode) {
        this.warehouseCode = warehouseCode;
    }

    public String getWarehouseName() {
        return warehouseName;
    }

    public void setWarehouseName(String warehouseName) {
        this.warehouseName = warehouseName;
    }



    public String getSysSource() {
        return sysSource;
    }

    public void setSysSource(String sysSource) {
        this.sysSource = sysSource;
    }

    public String getChannelCode() {
		return channelCode;
	}

	public void setChannelCode(String channelCode) {
		this.channelCode = channelCode;
	}

	@Override
    public String toString() {
        return "DoDTO{" +
                "doCode='" + doCode + '\'' +
                ", orderCode='" + orderCode + '\'' +
                ", orderType=" + orderType +
                ", orderSource=" + orderSource +
                ", orderChannel=" + orderChannel +
                ", userId=" + userId +
                ", merchantId=" + merchantId +
                ", orderAmount=" + orderAmount +
                ", discountAmount=" + discountAmount +
                ", orderDeliveryFee=" + orderDeliveryFee +
                ", goodReceiverAddress='" + goodReceiverAddress + '\'' +
                ", goodReceiverPostcode='" + goodReceiverPostcode + '\'' +
                ", goodReceiverName='" + goodReceiverName + '\'' +
                ", goodReceiverMobile='" + goodReceiverMobile + '\'' +
                ", goodReceiverPhone='" + goodReceiverPhone + '\'' +
                ", goodReceiverProvinceId=" + goodReceiverProvinceId +
                ", goodReceiverProvince='" + goodReceiverProvince + '\'' +
                ", goodReceiverProvinceCode='" + goodReceiverProvinceCode + '\'' +
                ", goodReceiverCityId=" + goodReceiverCityId +
                ", goodReceiverCity='" + goodReceiverCity + '\'' +
                ", goodReceiverCityCode='" + goodReceiverCityCode + '\'' +
                ", goodReceiverCountyId=" + goodReceiverCountyId +
                ", goodReceiverCounty='" + goodReceiverCounty + '\'' +
                ", goodReceiverCountyCode='" + goodReceiverCountyCode + '\'' +
                ", goodReceiverAreaId=" + goodReceiverAreaId +
                ", goodReceiverArea='" + goodReceiverArea + '\'' +
                ", goodReceiverAreaCode='" + goodReceiverAreaCode + '\'' +
                ", warehouseCode='" + warehouseCode + '\'' +
                ", warehouseName='" + warehouseName + '\'' +
                ", sysSource='" + sysSource + '\'' +
                ", doCreatTime=" + doCreatTime +

                ", channelCode=" + channelCode +
                '}';
    }
}
