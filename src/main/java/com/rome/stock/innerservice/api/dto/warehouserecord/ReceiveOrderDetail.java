package com.rome.stock.innerservice.api.dto.warehouserecord;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

@Data
public class ReceiveOrderDetail {

    @ApiModelProperty("入库单的行号")
    private String orderLineNo;

    @ApiModelProperty("收货数量")
    private BigDecimal receiveQty;

    @ApiModelProperty("生产日期")
    private Date productDate;

    @ApiModelProperty("批次")
    private String batchCode;

}