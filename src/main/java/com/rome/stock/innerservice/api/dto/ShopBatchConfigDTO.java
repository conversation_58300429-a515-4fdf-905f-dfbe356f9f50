package com.rome.stock.innerservice.api.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.util.List;

/**
 * 门店库存管理配置
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
@EqualsAndHashCode
public class ShopBatchConfigDTO {

    @ApiModelProperty("保质期区间配置")
    private List<ShopBatchValidConfigDTO> validConfigList;

    /**
     * 商品类型
     */
    private List<String> skuType;

    /**
     * 组合品类型
     */
    private List<Integer> combineType;

    /**
     * 预警天数,本次忽略,预留字段
     */
    private Integer alarmDay;

    /**
     * 计算取值类型,本次忽略,预留字段
     */
    private Integer calValue;
    /**
     * 称重容差设置
     */
    private BigDecimal weightDiff;

    /**
     * 计件容差设置
     */
    private BigDecimal countDiff;

}
