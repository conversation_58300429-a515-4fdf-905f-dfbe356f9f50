package com.rome.stock.innerservice.api.controller;

import com.github.pagehelper.PageInfo;
import com.rome.arch.core.clientobject.Response;
import com.rome.arch.core.exception.RomeException;
import com.rome.arch.util.controller.RomeController;
import com.rome.stock.innerservice.api.dto.RealWarehouse;
import com.rome.stock.innerservice.api.dto.VirtualWarehouse;
import com.rome.stock.innerservice.common.ResCode;
import com.rome.stock.innerservice.constant.ResponseMsg;
import com.rome.stock.common.enums.warehouse.VirtualWarehouseTypeVO;
import com.rome.stock.innerservice.domain.entity.VirtualWarehouseE;
import com.rome.stock.innerservice.domain.service.VirtualWarehouseService;
import com.rome.stock.innerservice.facade.StockDataFacade;
import com.rome.stock.innerservice.remote.item.dto.SkuInfoExtDTO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import io.swagger.annotations.ApiResponse;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;

import java.util.Collections;
import java.util.List;
import java.util.Map;

@Slf4j
@RomeController
@RequestMapping("/stock/v1/virtual_warehouse")
@Api(tags={"虚仓管理"})
public class VirtualWarehouseController {

	@Resource
	private VirtualWarehouseService virtualWarehouseService;

	@ApiOperation(value = "根据编码查询虚仓信息", nickname = "query_virtual_warehouse", produces = MediaType.APPLICATION_JSON_VALUE)
	@ApiResponse(code = 200, message = "success", response = VirtualWarehouseE.class)
	@RequestMapping(value = "/code/{code}", method = RequestMethod.GET)
	public Response queryByCode(@ApiParam(name = "code", value = "虚仓code") @PathVariable String code) {
		try {
			VirtualWarehouse virtualWarehouse = virtualWarehouseService.getVirtualWarehouseByCode(code);
			return Response.builderSuccess(virtualWarehouse);
		} catch (RomeException e) {
			log.error(e.getMessage(), e);
			return Response.builderFail(e.getCode(),e.getMessage());
		} catch (Exception e) {
			log.error(e.getMessage(), e);
			return Response.builderFail(ResCode.STOCK_ERROR_1003_DESC,ResCode.STOCK_ERROR_1003_DESC);
		}
	}

	@ApiOperation(value = "根据名称模糊查询虚仓信息", nickname = "query_virtual_warehouse", produces = MediaType.APPLICATION_JSON_VALUE)
	@ApiResponse(code = 200, message = "success", response = VirtualWarehouseE.class)
	@RequestMapping(value = "/name/{name}", method = RequestMethod.GET)
	public Response queryByName(@ApiParam(name = "name", value = "虚仓名称") @PathVariable String name) {
		try {
			List<VirtualWarehouse> virtualWarehouseLists = virtualWarehouseService.getVirtualWarehouseByName(name);
			return Response.builderSuccess(virtualWarehouseLists);
		} catch (RomeException e) {
			log.error(e.getMessage(), e);
			return Response.builderFail(e.getCode(),e.getMessage());
		} catch (Exception e) {
			log.error(e.getMessage(), e);
			return Response.builderFail(ResCode.STOCK_ERROR_1003_DESC,ResCode.STOCK_ERROR_1003_DESC);
		}
	}

	@ApiOperation(value = "根据id查询虚仓信息", nickname = "query_virtual_warehouse", produces = MediaType.APPLICATION_JSON_VALUE)
	@ApiResponse(code = 200, message = "success", response = VirtualWarehouseE.class)
	@RequestMapping(value = "/id/{id}", method = RequestMethod.GET)
	public Response queryById(@ApiParam(name = "id", value = "虚仓id") @PathVariable Long id) {
		try {
			VirtualWarehouse virtualWarehouse = virtualWarehouseService.getVirtualWarehouseById(id);
			return  Response.builderSuccess(virtualWarehouse);
		} catch (RomeException e) {
			log.error(e.getMessage(), e);
			return Response.builderFail(e.getCode(),e.getMessage());
		} catch (Exception e) {
			log.error(e.getMessage(), e);
			return Response.builderFail(ResCode.STOCK_ERROR_1003_DESC,ResCode.STOCK_ERROR_1003_DESC);
		}
	}


	@ApiOperation(value = "根据渠道组查询虚仓信息", nickname = "queryVmByChannelCode", produces = MediaType.APPLICATION_JSON_VALUE)
	@ApiResponse(code = 200, message = "success", response = VirtualWarehouse.class)
	@GetMapping("/queryVmByChannelCode")
	public Response<List<VirtualWarehouse>> queryVmByChannelCode(@RequestParam("channelCode")String channelCode){
			try{
				List<VirtualWarehouse> virtualWarehouses = virtualWarehouseService.queryVmByChannelCode(channelCode);
				return  Response.builderSuccess(virtualWarehouses);
			}catch (RomeException e){
				log.error(e.getMessage(), e);
				return Response.builderFail(e.getCode(),e.getMessage());
			}catch (Exception e){
				log.error(e.getMessage(), e);
				return Response.builderFail(ResCode.STOCK_ERROR_1003_DESC,ResCode.STOCK_ERROR_1003_DESC);
			}
	}




	@ApiOperation(value = "批量根据策略组编码查询虚仓信息", nickname = "query_virtual_warehouse", produces = MediaType.APPLICATION_JSON_VALUE)
	@ApiResponse(code = 200, message = "success", response = VirtualWarehouse.class)
	@GetMapping("/queryVmByVirtualGroupCodes")
	public Response<List<VirtualWarehouse>> queryVmByVirtualGroupCodes(@RequestParam("virtualGroupCodes")List<String> virtualGroupCodes){
		try{
			List<VirtualWarehouse> virtualWarehouses = virtualWarehouseService.queryVwByVirtualGroupCodes(virtualGroupCodes);
			return  Response.builderSuccess(virtualWarehouses);
		}catch (RomeException e){
			log.error(e.getMessage(), e);
			return Response.builderFail(e.getCode(),e.getMessage());
		}catch (Exception e){
			log.error(e.getMessage(), e);
			return Response.builderFail(ResCode.STOCK_ERROR_1003_DESC,ResCode.STOCK_ERROR_1003_DESC);
		}
	}


	@ApiOperation(value = "自定义批量查询虚仓信息", nickname = "query_virtual_warehouse", produces = MediaType.APPLICATION_JSON_VALUE)
	@ApiResponse(code = 200, message = "success", response = List.class)
	@RequestMapping(value = "/list", method = RequestMethod.POST)
	public Response<PageInfo<VirtualWarehouse>>  query(@ApiParam(name = "virtualWarehouse", value = "虚仓dto") @RequestBody VirtualWarehouse virtualWarehouse) {
		try {
			PageInfo<VirtualWarehouse> personPageInfo = virtualWarehouseService.queryVirtualWarehouse(virtualWarehouse);
			return Response.builderSuccess(personPageInfo);
		} catch (RomeException e) {
			log.error(e.getMessage(), e);
			return Response.builderFail(e.getCode(),e.getMessage());
		} catch (Exception e) {
			log.error(e.getMessage(), e);
			return Response.builderFail(ResCode.STOCK_ERROR_1003_DESC,ResCode.STOCK_ERROR_1003_DESC);
		}
	}


	@ApiOperation(value = "查询所有", nickname = "query_virtual_warehouse", produces = MediaType.APPLICATION_JSON_VALUE)
	@ApiResponse(code = 200, message = "success", response = List.class)
	@RequestMapping(value = "/listAll", method = RequestMethod.GET)
	public Response queryAll() {
		try {
			List<VirtualWarehouse> virtualWarehouseList = virtualWarehouseService.queryAllVirtualWarehouse();
			return  Response.builderSuccess(virtualWarehouseList);
		} catch (RomeException e) {
			log.error(e.getMessage(), e);
			return Response.builderFail(e.getCode(),e.getMessage());
		} catch (Exception e) {
			log.error(e.getMessage(), e);
			return Response.builderFail(ResCode.STOCK_ERROR_1003_DESC,ResCode.STOCK_ERROR_1003_DESC);
		}
	}

	@ApiOperation(value = "新增虚仓", nickname = "save_virtual_warehouse", produces = MediaType.APPLICATION_JSON_VALUE)
	@ApiResponse(code = 200, message = "success", response = VirtualWarehouseE.class)
	@RequestMapping(value = "/save", method = RequestMethod.POST)
	public Response save(@ApiParam(name = "virtualWarehouse", value = "虚仓dto") @RequestBody VirtualWarehouse virtualWarehouse) {
		try {
			virtualWarehouseService.saveVirtualWarehouse(virtualWarehouse);
			return   Response.builderSuccess("success");
		}catch (RomeException e) {
			log.error(e.getMessage(), e);
			return Response.builderFail(e.getCode(),e.getMessage());
		} catch (Exception e) {
			log.error(e.getMessage(), e);
			return Response.builderFail(ResCode.STOCK_ERROR_1003_DESC,ResCode.STOCK_ERROR_1003_DESC);
		}
	}


	@ApiOperation(value = "更新虚仓状态:enable", nickname = "update_virtual_warehouse", produces = MediaType.APPLICATION_JSON_VALUE)
	@ApiResponse(code = 200, message = "success")
	@RequestMapping(value = "/update/enable", method = RequestMethod.POST)
	public Response enableVirtualWarehouseState(@ApiParam(name = "id", value = "虚仓dto") @RequestParam Long id,
	                       @ApiParam(name = "id", value = "虚仓dto") @RequestParam Long modifier) {
		try {
			virtualWarehouseService.enableVirtualWarehouseState(id,modifier);
			return   Response.builderSuccess("success");
		} catch (RomeException e) {
			log.error(e.getMessage(), e);
			return Response.builderFail(e.getCode(),e.getMessage());
		} catch (Exception e) {
			log.error(e.getMessage(), e);
			return Response.builderFail(ResCode.STOCK_ERROR_1003_DESC,ResCode.STOCK_ERROR_1003_DESC);
		}
	}

	@ApiOperation(value = "更新虚仓状态:disable", nickname = "update_virtual_warehouse", produces = MediaType.APPLICATION_JSON_VALUE)
	@ApiResponse(code = 200, message = "success")
	@RequestMapping(value = "/update/disable", method = RequestMethod.POST)
	public Response disableVirtualWarehouseState(@ApiParam(name = "id", value = "虚仓dto") @RequestParam Long id,
	                       @ApiParam(name = "id", value = "虚仓dto") @RequestParam Long modifier) {
		try {
			virtualWarehouseService.disableVirtualWarehouseState(id,modifier);
			return   Response.builderSuccess("success");
		} catch (RomeException e) {
			log.error(e.getMessage(), e);
			return Response.builderFail(e.getCode(),e.getMessage());
		} catch (Exception e) {
			log.error(e.getMessage(), e);
			return Response.builderFail(ResCode.STOCK_ERROR_1003_DESC,ResCode.STOCK_ERROR_1003_DESC);
		}
	}

	@ApiOperation(value = "自定义修改虚仓", nickname = "update_virtual_warehouse", produces = MediaType.APPLICATION_JSON_VALUE)
	@ApiResponse(code = 200, message = "success", response = VirtualWarehouseE.class)
	@RequestMapping(value = "/update", method = RequestMethod.POST)
	public Response update(@ApiParam(name = "virtualWarehouse", value = "虚仓dto") @RequestBody VirtualWarehouse virtualWarehouse) {
		try {
			virtualWarehouseService.updateVirtualWarehouseById(virtualWarehouse);
			return   Response.builderSuccess("success");
		} catch (RomeException e) {
			log.error(e.getMessage(), e);
			return Response.builderFail(e.getCode(),e.getMessage());
		} catch (Exception e) {
			log.error(e.getMessage(), e);
			return Response.builderFail(ResCode.STOCK_ERROR_1003_DESC,ResCode.STOCK_ERROR_1003_DESC);
		}
	}

	@ApiOperation(value = "关联策略组", nickname = "relate_group", produces = MediaType.APPLICATION_JSON_VALUE)
	@ApiResponse(code = 200, message = "success", response = VirtualWarehouseE.class)
	@RequestMapping(value = "/update/relateGroup", method = RequestMethod.POST)
	public Response relateGroup(@ApiParam(name = "id", value = "id") @RequestParam Long id,
	                            @ApiParam(name = "groupIds", value = "groupIds") @RequestParam List<Long> groupIds,
	                            @ApiParam(name = "modifier", value = "modifier") @RequestParam Long modifier) {
		try {
			List<Long> groupIdChangeList = virtualWarehouseService.relateVirtualWarehouseGroup(id,modifier,groupIds);		
			if(groupIdChangeList != null && groupIdChangeList.size() > 0) {
				// 重刷虚仓库存
				if(id != null) {
					StockDataFacade.refreshVwStockByVwIdList(Collections.singletonList(id));
				}
				// 重刷策略组库存,有变化的组
				StockDataFacade.refreshGroupStockByGroupIdList(groupIdChangeList);
				// 删除缓存路由信息-渠道仓库优先级和路由模板,根据组id
				StockDataFacade.delRouteInfoByPriorityTempleByGroupIdList(groupIdChangeList);
			}
			return   Response.builderSuccess("success");
		} catch (RomeException e) {
			log.error(e.getMessage(), e);
			return Response.builderFail(e.getCode(),e.getMessage());
		} catch (Exception e) {
			log.error(e.getMessage(), e);
			return Response.builderFail(ResCode.STOCK_ERROR_1003_DESC,ResCode.STOCK_ERROR_1003_DESC);
		}
	}

	@ApiOperation(value = "虚仓关联实仓", nickname = "relate_real_virtual_house", produces = MediaType.APPLICATION_JSON_VALUE)
	@ApiResponse(code = 200, message = "success", response = VirtualWarehouseE.class)
	@RequestMapping(value = "/update/relateRealVirtualHouse", method = RequestMethod.GET)
	public Response relateRealVirtualHouse(@ApiParam(name = "virtualWarehouseId", value = "virtualWarehouseId") @RequestParam Long virtualWarehouseId,
	                                       @ApiParam(name = "realId", value = "realId") @RequestParam Long realId,
	                                       @ApiParam(name = "modifier", value = "modifier") @RequestParam Long modifier,
	                                       @ApiParam(name = "syncRate", value = "syncRate") @RequestParam Integer syncRate) {
		try {
			virtualWarehouseService.relateRealWarehouse(virtualWarehouseId, modifier, realId, syncRate);
			return Response.builderSuccess("success");
		} catch (RomeException e) {
			log.error(e.getMessage(), e);
			return Response.builderFail(e.getCode(), e.getMessage());
		} catch (Exception e) {
			log.error(e.getMessage(), e);
			return Response.builderFail(ResCode.STOCK_ERROR_1003_DESC, ResCode.STOCK_ERROR_1003_DESC);
		}
	}

	@ApiOperation(value = "删除虚仓", nickname = "delete_virtual_warehouse", produces = MediaType.APPLICATION_JSON_VALUE)
	@ApiResponse(code = 200, message = "success", response = VirtualWarehouseE.class)
	@RequestMapping(value = "/delete/{code}", method = RequestMethod.POST)
	public Response update(@ApiParam(name = "code", value = "虚仓code") @PathVariable String code ,  @ApiParam(name = "modifier", value = "modifier") @RequestParam Long modifier) {
		try {
			virtualWarehouseService.deleteVirtualWarehouseByCode(code,modifier);
			return   Response.builderSuccess("success");
		} catch (RomeException e) {
			log.error(e.getMessage(), e);
			return Response.builderFail(e.getCode(),e.getMessage());
		} catch (Exception e) {
			log.error(e.getMessage(), e);
			return Response.builderFail(ResCode.STOCK_ERROR_1003_DESC,ResCode.STOCK_ERROR_1003_DESC);
		}
	}
	@ApiOperation(value = "根据工厂编码查询所有实仓", produces = MediaType.APPLICATION_JSON_VALUE)
	@ApiResponse(code = 200, message = "success", response = RealWarehouse.class)
	@RequestMapping(value = "/queryRealWarehouseByFactoryCode", method = RequestMethod.GET)
	public Response<List<RealWarehouse>> queryRealWarehouseByFactoryCode(@ApiParam(name = "factoryCode", value = "工厂编码") @RequestParam(required = false,value = "factoryCode") String factoryCode) {
		try {
			List<RealWarehouse> list = virtualWarehouseService.queryRealWarehouseByFactoryCode(factoryCode);
			return ResponseMsg.SUCCESS.buildMsg(list);
		} catch (RomeException e) {
			log.error(e.getMessage(), e);
			return ResponseMsg.FAIL.buildMsg(e.getCode(), e.getMessage());
		} catch (Exception e) {
			log.error(e.getMessage(), e);
			return ResponseMsg.EXCEPTION.buildMsg();
		}
	}


	@ApiOperation(value = "根据商家编号查询商品列表", produces = MediaType.APPLICATION_JSON_VALUE)
	@ApiResponse(code = 200, message = "success")
	@RequestMapping(value = "/querySkuByMerchantId", method = RequestMethod.POST)
	public Response<PageInfo<SkuInfoExtDTO>> querySkuByMerchantId(@RequestBody SkuInfoExtDTO skuInfoExtDTO){
		try {
            PageInfo<SkuInfoExtDTO> page = virtualWarehouseService.querySkuByMerchantId(skuInfoExtDTO);
			return ResponseMsg.SUCCESS.buildMsg(page);
		} catch (RomeException e) {
			log.error(e.getMessage(), e);
			return ResponseMsg.FAIL.buildMsg(e.getCode(), e.getMessage());
		} catch (Exception e) {
			log.error(e.getMessage(), e);
			return ResponseMsg.EXCEPTION.buildMsg();
		}
	}


	@ApiOperation(value = "查询所有虚仓类型信息", nickname = "vwTypeList", produces = MediaType.APPLICATION_JSON_VALUE)
	@ApiResponse(code = 200, message = "success", response = RealWarehouse.class)
	@GetMapping("/vwTypeList")
	public Response<Map<Integer, String>> vwTypeList() {
		try {
			return ResponseMsg.SUCCESS.buildMsg(VirtualWarehouseTypeVO.getVirtualWarehouseTypeList());
		} catch (RomeException e) {
			log.error(e.getMessage(), e);
			return ResponseMsg.FAIL.buildMsg(e.getCode(), e.getMessage());
		} catch (Exception e) {
			log.error(e.getMessage(), e);
			return ResponseMsg.EXCEPTION.buildMsg();
		}
	}

	@ApiOperation(value = "根据实仓编号查询所有虚仓类型信息", nickname = "queryVirtualWarehouseTypeByRealWarehouseId", produces = MediaType.APPLICATION_JSON_VALUE)
	@ApiResponse(code = 200, message = "success", response = RealWarehouse.class)
	@RequestMapping(value = "/queryVirtualWarehouseTypeByRealWarehouseId", method = RequestMethod.GET)
	public Response<Map<Integer, String>> queryVirtualWarehouseTypeByRealWarehouseId(@RequestParam("realWarehouseId") Long realWarehouseId) {
		try {
			Map<Integer, String> virtualWarehouseList = virtualWarehouseService.queryVirtualWarehouseTypeByRealWarehouseId(realWarehouseId);
			return ResponseMsg.SUCCESS.buildMsg(virtualWarehouseList);
		} catch (RomeException e) {
			log.error(e.getMessage(), e);
			return ResponseMsg.FAIL.buildMsg(e.getCode(), e.getMessage());
		} catch (Exception e) {
			log.error(e.getMessage(), e);
			return ResponseMsg.EXCEPTION.buildMsg(e.getMessage());
		}
	}
}
