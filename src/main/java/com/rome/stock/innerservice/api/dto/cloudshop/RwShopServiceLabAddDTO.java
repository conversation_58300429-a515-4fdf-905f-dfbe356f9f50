package com.rome.stock.innerservice.api.dto.cloudshop;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

@Data
public class RwShopServiceLabAddDTO {

    @ApiModelProperty(value = "仓库id")
    private Long realWarehouseId;

    @ApiModelProperty(value = "门店编号集合")
    private List<String> shopCodes;

    @ApiModelProperty(value = "创建人")
    private Long creator;
}
