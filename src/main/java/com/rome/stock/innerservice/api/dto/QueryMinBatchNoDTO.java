package com.rome.stock.innerservice.api.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import java.util.List;

/**
 * 查询最小批次DTO对象
 * <AUTHOR>
 */
@Data
public class QueryMinBatchNoDTO {

    @ApiModelProperty(value = "仓库",required = true)
    @NotBlank(message = "仓库编码不可为空")
    private String realWarehouseCode;

    @ApiModelProperty(value = "商品编码",required = true)
    @NotEmpty(message = "商品列表不可为空")
    private List<String> skuCodes;


}    
   