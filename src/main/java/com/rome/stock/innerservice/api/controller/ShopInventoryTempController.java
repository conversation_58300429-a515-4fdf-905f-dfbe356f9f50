package com.rome.stock.innerservice.api.controller;

import com.github.pagehelper.PageInfo;
import com.rome.arch.core.clientobject.Response;
import com.rome.arch.core.exception.RomeException;
import com.rome.arch.util.controller.RomeController;
import com.rome.stock.innerservice.api.dto.ShopInventoryTempDTO;
import com.rome.stock.innerservice.common.ResCode;
import com.rome.stock.innerservice.common.ValidList;
import com.rome.stock.innerservice.domain.service.ShopInventoryTempService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiResponse;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

/**
 * 门店盘点结果明细临时表
 */
@Slf4j
@RomeController
@RequestMapping("/stock/v1/shopInventoryTemp")
@Api(tags={"门店盘点结果临时表"})
public class ShopInventoryTempController {

    @Resource
    private ShopInventoryTempService shopInventoryTempService;

    @ApiOperation(value = "批量新增门店盘点结果-幂等", nickname = "batchInsertWithIdempotent")
    @ApiResponse(code = 200, message = "success", response = Response.class)
    @PostMapping(value = "/batchInsertWithIdempotent")
    public Response batchInsertWithIdempotent(
            @Validated @RequestBody ValidList<ShopInventoryTempDTO> shopInventoryTempDTOList,
            @RequestParam(value = "outRecordCode") String outRecordCode,
            @RequestParam(value = "currentPage") Integer currentPage){
        try{
            shopInventoryTempService.batchInsertWithIdempotent(shopInventoryTempDTOList, outRecordCode, currentPage);
            return Response.builderSuccess(null);
        }catch (RomeException e){
            log.error(e.getMessage(), e);
            return Response.builderFail(e.getCode(),e.getMessage());
        }catch (Exception e){
            log.error(e.getMessage(), e);
            return Response.builderFail(ResCode.STOCK_ERROR_1003_DESC,e.getMessage());
        }
    }

    @ApiOperation(value = "批量更新门店盘点结果", nickname = "updateShopInventoryTemp")
    @ApiResponse(code = 200, message = "success", response = Response.class)
    @PostMapping(value = "/updateShopInventoryTemp")
    public Response updateShopInventoryTemp(@Validated @RequestBody ValidList<ShopInventoryTempDTO> shopInventoryTempDTOList){
        try{
            shopInventoryTempService.updateBatch(shopInventoryTempDTOList);
            return Response.builderSuccess(null);
        }catch (RomeException e){
            log.error(e.getMessage(), e);
            return Response.builderFail(e.getCode(),e.getMessage());
        }catch (Exception e){
            log.error(e.getMessage(), e);
            return Response.builderFail(ResCode.STOCK_ERROR_1003_DESC,ResCode.STOCK_ERROR_1003_DESC);
        }
    }

    @ApiOperation(value = "更新门店盘点记录状态", nickname = "updateStatusById")
    @ApiResponse(code = 200, message = "success", response = Response.class)
    @PostMapping(value = "/updateStatusById/{id}/{status}")
    public Response updateStatusById(@PathVariable("id")Long id,@PathVariable("status")Integer status){
        try{
            shopInventoryTempService.updateStatusById(id,status);
            return Response.builderSuccess(null);
        }catch (RomeException e){
            log.error(e.getMessage(), e);
            return Response.builderFail(e.getCode(),e.getMessage());
        }catch (Exception e){
            log.error(e.getMessage(), e);
            return Response.builderFail(ResCode.STOCK_ERROR_1003_DESC,ResCode.STOCK_ERROR_1003_DESC);
        }
    }

    @ApiOperation(value = "删除门店盘点记录", nickname = "deleteById")
    @ApiResponse(code = 200, message = "success", response = Response.class)
    @PostMapping(value = "/deleteById/{id}")
    public Response deleteById(@PathVariable("id")Long id){
        try{
            shopInventoryTempService.deleteById(id);
            return Response.builderSuccess(null);
        }catch (RomeException e){
            log.error(e.getMessage(), e);
            return Response.builderFail(e.getCode(),e.getMessage());
        }catch (Exception e){
            log.error(e.getMessage(), e);
            return Response.builderFail(ResCode.STOCK_ERROR_1003_DESC,ResCode.STOCK_ERROR_1003_DESC);
        }
    }

    @ApiOperation(value = "根据条件查询门店盘点结果", nickname = "queryByCondition")
    @ApiResponse(code = 200, message = "success", response = Response.class)
    @PostMapping(value = "/queryByCondition")
    public Response<PageInfo<List<ShopInventoryTempDTO>>> queryByCondition(@RequestBody ShopInventoryTempDTO shopInventoryTempDTO){
        try{
            PageInfo<List<ShopInventoryTempDTO>> response = shopInventoryTempService.queryByCondition(shopInventoryTempDTO);
            return Response.builderSuccess(response);
        }catch (RomeException e){
            log.error(e.getMessage(), e);
            return Response.builderFail(e.getCode(),e.getMessage());
        }catch (Exception e){
            log.error(e.getMessage(), e);
            return Response.builderFail(ResCode.STOCK_ERROR_1003_DESC,ResCode.STOCK_ERROR_1003_DESC);
        }
    }

    @ApiOperation(value = "根据外部单号查询门店盘点结果", nickname = "queryByOutRecordCode")
    @ApiResponse(code = 200, message = "success", response = ShopInventoryTempDTO.class)
    @PostMapping(value = "/queryByOutRecordCode")
    public Response<List<ShopInventoryTempDTO>> queryByOutRecordCode(@RequestParam("outRecordCode")String outRecordCode){
        try{
            List<ShopInventoryTempDTO> response = shopInventoryTempService.queryByOutRecordCode(outRecordCode);
            return Response.builderSuccess(response);
        }catch (RomeException e){
            log.error(e.getMessage(), e);
            return Response.builderFail(e.getCode(),e.getMessage());
        }catch (Exception e){
            log.error(e.getMessage(), e);
            return Response.builderFail(ResCode.STOCK_ERROR_1003_DESC,e.getMessage());
        }
    }
    
    @ApiOperation(value = "根据外部单号和页码查询门店盘点结果", nickname = "queryByOutRecordCodeAndPage")
    @ApiResponse(code = 200, message = "success", response = ShopInventoryTempDTO.class)
    @PostMapping(value = "/queryByOutRecordCodeAndPage")
    public Response<List<ShopInventoryTempDTO>> queryByOutRecordCodeAndPage(
            @RequestParam("outRecordCode") String outRecordCode,
            @RequestParam("currentPage") Integer currentPage){
        try{
            List<ShopInventoryTempDTO> response = shopInventoryTempService.queryByOutRecordCodeAndPage(outRecordCode, currentPage);
            return Response.builderSuccess(response);
        }catch (RomeException e){
            log.error(e.getMessage(), e);
            return Response.builderFail(e.getCode(),e.getMessage());
        }catch (Exception e){
            log.error(e.getMessage(), e);
            return Response.builderFail(ResCode.STOCK_ERROR_1003_DESC,e.getMessage());
        }
    }
} 