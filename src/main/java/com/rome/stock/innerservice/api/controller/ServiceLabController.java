package com.rome.stock.innerservice.api.controller;

import com.github.pagehelper.PageInfo;
import com.rome.arch.core.clientobject.Response;
import com.rome.arch.core.exception.RomeException;
import com.rome.arch.util.controller.RomeController;
import com.rome.stock.innerservice.api.dto.RealWarehouse;
import com.rome.stock.innerservice.api.dto.cloudshop.RwServiceLabDTO;
import com.rome.stock.innerservice.api.dto.cloudshop.RwServiceLabUpdateDTO;
import com.rome.stock.innerservice.api.dto.cloudshop.ServiceLabDTO;
import com.rome.stock.innerservice.common.ResCode;
import com.rome.stock.innerservice.constant.ResponseMsg;
import com.rome.stock.innerservice.domain.service.RwServiceLabService;
import com.rome.stock.innerservice.domain.service.ServiceLabService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiResponse;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

/**
 * 服务标签
 *
 * <AUTHOR>
 */
@Slf4j
@RomeController
@RequestMapping("/stock/v1/serviceLab")
@Api(tags = {"服务标签"})
public class ServiceLabController {

    @Resource
    private ServiceLabService serviceLabService;
    @Resource
    private RwServiceLabService rwServiceLabService;

    @ApiOperation(value = "根据标签类型查询所有服务标签", nickname = "getServiceLabByType", produces = MediaType.APPLICATION_JSON_VALUE)
    @ApiResponse(code = 200, message = "success", response = Response.class)
    @PostMapping(value = "/getServiceLabByType")
    public Response<List<ServiceLabDTO>> getServiceLabByType(@RequestBody ServiceLabDTO serviceLabDTO) {
        try {
            List<ServiceLabDTO> list = serviceLabService.getServiceLabByType(serviceLabDTO.getBusinessType(),serviceLabDTO.getSkuType());
            return Response.builderSuccess(list);
        } catch (RomeException e) {
            log.error(e.getMessage(), e);
            return Response.builderFail(e.getCode(), e.getMessage());
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return Response.builderFail(ResCode.STOCK_ERROR_1003_DESC,  e.getMessage());
        }
    }

    @ApiOperation(value = "根据实仓id查询关联的服务标签", nickname = "getRwServiceLabByRealWarehouseId", produces = MediaType.APPLICATION_JSON_VALUE)
    @ApiResponse(code = 200, message = "success", response = Response.class)
    @RequestMapping(value = "/getRwServiceLabByRealWarehouseId", method = RequestMethod.GET)
    public Response<List<RwServiceLabDTO>> getRwServiceLabByRealWarehouseId(@RequestParam("realWarehouseId") Long realWarehouseId) {
        try {
            List<RwServiceLabDTO> list = rwServiceLabService.getByRwId(realWarehouseId);
            return Response.builderSuccess(list);
        } catch (RomeException e) {
            log.error(e.getMessage(), e);
            return Response.builderFail(e.getCode(), e.getMessage());
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return Response.builderFail(ResCode.STOCK_ERROR_1003_DESC, e.getMessage());
        }
    }
    
    @ApiOperation(value = "增加或更新仓时效", nickname = "addOrUpdate", produces = MediaType.APPLICATION_JSON_VALUE)
    @ApiResponse(code = 200, message = "success", response = List.class)
    @RequestMapping(value = "/addOrUpdate", method = RequestMethod.POST)
    public Response addOrUpdate(@RequestBody RwServiceLabUpdateDTO rwServiceLabUpdateDTO){
        try {
            if(null!=rwServiceLabUpdateDTO.getCheckedVisible() && rwServiceLabUpdateDTO.getCheckedVisible()){
                rwServiceLabService.addOrUpdateBatch(rwServiceLabUpdateDTO);
            }else{
                rwServiceLabService.addOrUpdate(rwServiceLabUpdateDTO.getList());
            }
            return Response.builderSuccess("");
        } catch (RomeException e) {
            log.error(e.getMessage(), e);
            return ResponseMsg.EXCEPTION.buildMsg(e.getCode(), e.getMessage());
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return ResponseMsg.EXCEPTION.buildMsg(ResCode.STOCK_ERROR_1003, e.getMessage());
        }
    }


    @ApiOperation(value = "分页查询云店仓库时效服务标签（查仓库）", nickname = "queryServiceLabWarehouse", produces = MediaType.APPLICATION_JSON_VALUE)
    @ApiResponse(code = 200, message = "success", response = List.class)
    @RequestMapping(value = "/queryServiceLabWarehouse", method = RequestMethod.POST)
    public Response<PageInfo<RealWarehouse>> queryServiceLabWarehouse(@RequestBody ServiceLabDTO dto) {
        try {
            PageInfo<RealWarehouse> pageList = rwServiceLabService.queryServiceLabWarehouse(dto);
            return Response.builderSuccess(pageList);
        } catch (RomeException e) {
            log.error(e.getMessage(), e);
            return ResponseMsg.EXCEPTION.buildMsg(e.getCode(), e.getMessage());
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return ResponseMsg.EXCEPTION.buildMsg("500000", e.getMessage());
        }
    }
}
