package com.rome.stock.innerservice.api.dto.groupbuy;

import com.rome.stock.innerservice.api.dto.Pagination;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.Valid;
import javax.validation.constraints.NotNull;
import java.util.List;

@Data
@EqualsAndHashCode
public class VwMoveRecordDTO  extends Pagination{

    /**
     * 预约单号
     */
    @ApiModelProperty(value = "预约单号")
    @NotNull(message = "预约单号不能为空")
    private String orderCode;
    /**
     * 虚拟仓库code（入）
     */
    @ApiModelProperty(value = "虚拟仓库code（入）")
    @NotNull(message = "调入虚拟仓库code不能为空")
    private String inVirtualWarehouseCode;
    /**
     * 虚拟仓库code（出）
     */
    @ApiModelProperty(value = "虚拟仓库code（出）")
    @NotNull(message = "调入出虚拟仓库code不能为空")
    private String outVirtualWarehouseCode;

    /**
     * 转移商品细节
     */
    @ApiModelProperty(value = "转移商品细节")
    @Valid
    private List<VwDetailDTO> virtualWarehouseSkus;
}
