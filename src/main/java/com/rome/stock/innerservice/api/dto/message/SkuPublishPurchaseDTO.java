/**
 * Filename SkuPublishPurchaseDTO.java
 * Company 上海来伊份科技有限公司。
 * <AUTHOR>
 * @version 
 */
package com.rome.stock.innerservice.api.dto.message;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 进货权 消息
 * <AUTHOR>
 * @since 2020年4月21日 下午5:01:11
 */
@ApiModel(description = "进货权信息")
@Data
public class SkuPublishPurchaseDTO {
  
  /**
   * skuId
   */	
  private Long skuId;
  
  /** sku编号 */
  private String skuCode;
  
  /**
   * 门店编码
   */
  @ApiModelProperty(value = "门店编码")
  private String storeCode;
  
  /**
   * 渠道编码,备注：如果是销售权限就传channelCode，如果是进货权限就传storeCode
   */
  @ApiModelProperty(value = "渠道编码,备注：如果是销售权限就传channelCode，如果是进货权限就传storeCode")
  private String channelCode;
  
  /**
   * 类型, 0,销售权限；1，进货权限
   */
  @ApiModelProperty(value = "类型, 0,销售权限；1，进货权限")
  private Integer type;
  
  /**
   * 0,没有权限，1，有权限
   */
  @ApiModelProperty(value = "0,没有权限，1，有权限")
  private Integer isAuthority;
}
