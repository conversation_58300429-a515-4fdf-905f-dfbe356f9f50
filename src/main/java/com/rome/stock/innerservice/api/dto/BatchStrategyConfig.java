package com.rome.stock.innerservice.api.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;

/**
* 批次出库策略配置DTO
*/
@Data
public class BatchStrategyConfig {

        @ApiModelProperty(value = "出库仓库id",required = true)
        @NotNull(message = "出库仓库id不能为空")
        private Long outRealWarehouseId;

        @ApiModelProperty(value = "入库仓仓库id")
        private Long inRealWarehouseId;

        @ApiModelProperty(value = "订单单据类型",required = true)
        @NotNull(message = "订单单据类型不能为空")
        private Integer recordType;

}
