package com.rome.stock.innerservice.api.dto.frontrecord;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 类RealWarehouseDto的实现描述：
 *
 * <AUTHOR> 2019/4/18 18:04
 */
@Data
@EqualsAndHashCode
public class ShopRwDTO {

    @ApiModelProperty(value = "仓库外部编号")
    private String realWarehouseOutCode;

    @ApiModelProperty(value = "仓库名称")
    private String realWarehouseName;

    @ApiModelProperty(value = "门店编号")
    private String shopCode;
}
