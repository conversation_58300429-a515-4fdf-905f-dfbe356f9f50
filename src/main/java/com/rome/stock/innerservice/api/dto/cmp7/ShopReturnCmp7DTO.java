package com.rome.stock.innerservice.api.dto.cmp7;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @description cmp7退货过帐单信息
 * @date 2020/11/12 19:46
 * @throw
 */
@Data
public class ShopReturnCmp7DTO {

    @ApiModelProperty(value = "请求时间戳")
    private Long timestamp;

    @ApiModelProperty(value = "客户端签名")
    private String sign;

    /**
     * 退货明细信息
     */
    private List<ShopReturnDetailCmp7DTO> data;
}    
   