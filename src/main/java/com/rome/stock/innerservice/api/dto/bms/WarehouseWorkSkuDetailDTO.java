package com.rome.stock.innerservice.api.dto.bms;

import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;

/**
 * 商品统计对应实体
 */
@Data
@EqualsAndHashCode
public class WarehouseWorkSkuDetailDTO{

    /**
     * 单据号
     */
    private String orderNo;

    /**
     * 主单据id
     */
    private Long warehouseWorkId;

    /**
     * 门店
     */
    private String nextSite;

    /**
     * 商品id
     */
    private Long skuId;

    /**
     * 商品编码
     */
    private String skuCode;

    /**
     * 商品名称
     */
    private String skuName;

    /**
     * 商品类型
     */
    private String skuType;

    /**
     * 基础单位编码
     */
    private String basicUnitCode;

    /**
     * 基础单位名称
     */
    private String basicUnit;

    /**
     * 基础单位毛重
     */
    private BigDecimal basicWeight=BigDecimal.ZERO;

    /**
     * 箱数
     */
    private BigDecimal boxQty=BigDecimal.ZERO;

    /**
     * 商品总数量
     */
    private BigDecimal skuQty=BigDecimal.ZERO;

    /**
     * 每种类型的SKU种类数(适用toc)
     */
    private Integer skuNum;

    /**
     * 整箱数量
     */
    private BigDecimal fullBoxQty=BigDecimal.ZERO;

    /**
     * 拆零数量
     */
    private BigDecimal splitQty=BigDecimal.ZERO;

    /**
     * 毛重
     */
    private BigDecimal weight=BigDecimal.ZERO;

    /**
     * 体积
     */
    private BigDecimal volume=BigDecimal.ZERO;

    /**
     * 箱规数量
     */
    private BigDecimal boxScale=BigDecimal.ZERO;

    /**
     * 箱单位重量
     */
    private BigDecimal boxWeight=BigDecimal.ZERO;

    /**
     * 箱单位体积
     */
    private BigDecimal boxVolume=BigDecimal.ZERO;

}
