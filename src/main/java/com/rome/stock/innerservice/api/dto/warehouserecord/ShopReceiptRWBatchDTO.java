package com.rome.stock.innerservice.api.dto.warehouserecord;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotNull;
import java.math.BigDecimal;

/**
 * <p>
 * Description: 门店确认收货物料批次
 * </p>
 *
 * <AUTHOR>
 * @date 2022/11/2
 **/
@Data
@EqualsAndHashCode
public class ShopReceiptRWBatchDTO {

    @ApiModelProperty(value = "物料编码")
    private String skuCode;

    @ApiModelProperty(value = "批次号")
    private String batchNo;

    @ApiModelProperty(value = "数量")
    @NotNull(message = "确认收货数量不能为空")
    private BigDecimal actualQty;

}
