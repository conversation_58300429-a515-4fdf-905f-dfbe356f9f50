package com.rome.stock.innerservice.api.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;
import java.util.List;

@Data
@EqualsAndHashCode
public class VirtualWarehouseMoveRecord extends Pagination {
    /**
     * 唯一主键
     */
    @ApiModelProperty(value = "主键")
    private Long id;
    /**
     * 编号
     */
    @ApiModelProperty(value = "虚拟仓库移库单据编号")
    private String recordCode;
    /**
     * 单据类型
     */
    @ApiModelProperty(value = "单据类型")
    private Integer recordType;
    /**
     * 单据类型名称
     */
    @ApiModelProperty(value = "单据类型名称")
    private String recordTypeName;
    /**
     * 单据状态
     */
    @ApiModelProperty(value = "单据状态")
    private Integer recordStatus;
    /**
     * 单据状态名称
     */
    @ApiModelProperty(value = "单据名称")
    private String recordStatusName;
    /**
     * 虚拟仓库id入
     */
    @ApiModelProperty(value = "虚拟仓库id入")
    private Long inVirtualWarehouseId;
    /**
     * 虚拟仓库id出
     */
    @ApiModelProperty(value = "虚拟仓库id出")
    private Long outVirtualWarehouseId;
    /**
     * 实仓id
     */
    @ApiModelProperty(value = "实仓id")
    private Long realWarehouseId;
    /**
     * 实仓code
     */
    private String realWarehouseCode;
    /**
     * 实仓name
     */
    @ApiModelProperty(value = "实仓name")
    private String realWarehouseName;
    /**
     * 创建人
     */
    @ApiModelProperty(value = "创建人")
    private Long creator;
    /**
     * 创建时间
     */
    @ApiModelProperty(value = "创建时间")
    private Date createTime;
    /**
     * 起始时间
     */
    @ApiModelProperty(value = "起始时间")
    private Date startTime;
    /**
     * 结束时间
     */
    @ApiModelProperty(value = "结束时间")
    private Date endTime;
    /**
     * 虚拟仓库名称（入）
     */
    @ApiModelProperty(value = "虚拟仓库名称（入）")
    private String inVirtualWarehouseName;
    /**
     * 虚拟仓库名称（出）
     */
    @ApiModelProperty(value = "虚拟仓库名称（出）")
    private String outVirtualWarehouseName;
    /**
     * 创建人名称
     */
    @ApiModelProperty(value = "创建人名称")
    private String creatorName;

    @ApiModelProperty(value = "备注")
    private String remark;

    /**
     * 转移商品细节
     */
    @ApiModelProperty(value = "转移商品细节")
    private List<VirtualWarehouseSku> virtualWarehouseSkus;

    @ApiModelProperty(value = "虚拟仓库类型名称（入）")
    private String inVirtualWarehouseTypeName;

    @ApiModelProperty(value = "虚拟仓库类型名称（出）")
    private String outVirtualWarehouseTypeName;
    /**
     * 更新人
     */
    @ApiModelProperty(value = "更新人")
    private Long modifier;

    /**
     * 是否导出
     */
    @ApiModelProperty(value="是否导出")
    private boolean export = false;

    /**
     * 包含count查询
     */
    @ApiModelProperty(value="是否统计条数")
    private boolean count = true;

    /**
     * 转出虚仓编号
     */
    @ApiModelProperty(value="转出虚仓编号")
    private String outVirtualWarehouseCode;

    /**
     * 转入虚仓编号
     */
    @ApiModelProperty(value="转入虚仓编号")
    private String inVirtualWarehouseCode;
}
