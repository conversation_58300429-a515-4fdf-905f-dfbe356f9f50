package com.rome.stock.innerservice.api.dto;

import com.rome.stock.innerservice.remote.base.dto.AreaDTO;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;

@Data
@EqualsAndHashCode
public class AddressStopDetailTemplate  implements Serializable {

    private String countryCode;
    private String countryName;

    private String province;
    private String provinceName;
    private String provinceCode;

    private String city;
    private String cityName;
    private String cityCode;

    private String county;
    private String countyName;
    private String countyCode;

    private String area;

    private List<AreaDTO> areas=new ArrayList<>();//多个镇

    private String isStop;

    private String errorMsg;
}
