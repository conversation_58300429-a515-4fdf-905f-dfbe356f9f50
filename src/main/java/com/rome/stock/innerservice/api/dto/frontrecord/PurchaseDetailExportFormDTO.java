package com.rome.stock.innerservice.api.dto.frontrecord;

import com.github.pagehelper.PageInfo;
import com.rome.stock.common.page.Pagination;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

@Data
@EqualsAndHashCode
public class PurchaseDetailExportFormDTO extends Pagination {

    @ApiModelProperty(value = "入库单号")
    private String inRecordCode;

    @ApiModelProperty(value = "入库单号")
    private String inRecordCodes;

    @ApiModelProperty(value = "售后单号")
    private String reverseOrderNo;

    @ApiModelProperty(value = "预入库单号")
    private String recordCode;

    @ApiModelProperty(value = "预入库单号集合")
    private List<String> recordCodeList;

    @ApiModelProperty(value = "入库仓库编号")
    private String inWarehouseCode;

    @ApiModelProperty(value = "入库仓库编号")
    private String inWarehouseCodes;

    @ApiModelProperty(value = "入库仓库id集合")
    private List<Long> inRealWarehouseIds;

    @ApiModelProperty(value = "入库仓库名称")
    private String inWarehouseName;

    @ApiModelProperty(value = "sku编号")
    private String skuCode;

    @ApiModelProperty(value = "sku名称")
    private String skuName;

    @ApiModelProperty(value = "数量")
    private BigDecimal inQty;

    @ApiModelProperty(value = "匹配数量")
    private BigDecimal matchQty;

    @ApiModelProperty(value = "差异数量")
    private BigDecimal diffQty;

//    @ApiModelProperty(value = "已过账渠道")
//    private String postChannelCode;
//
//    @ApiModelProperty(value = "交易匹配渠道")
//    private String matchChannelCode;
//
    @ApiModelProperty(value = "是否渠道差异")
    private String channelCodeFlag;

    @ApiModelProperty(value = "开始时间")
    private String startTime;

    @ApiModelProperty(value = "结束时间")
    private String endTime;

    @ApiModelProperty(value = "创建时间")
    private Date createTime;
    /**
     * 批次匹配信息
     */
    private List<PurchaseDetailExportFormDetailDTO> pcMatchList;

}
