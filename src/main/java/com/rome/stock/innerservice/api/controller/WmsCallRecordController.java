package com.rome.stock.innerservice.api.controller;

import com.github.pagehelper.PageInfo;
import com.rome.arch.core.clientobject.Response;
import com.rome.arch.core.exception.RomeException;
import com.rome.arch.util.controller.RomeController;
import com.rome.stock.common.dto.WmsCallRecordDTO;
import com.rome.stock.innerservice.common.ResCode;
import com.rome.stock.innerservice.domain.convertor.WmsCallRecordConvertor;
import com.rome.stock.innerservice.domain.entity.WmsCallRecordE;
import com.rome.stock.innerservice.domain.repository.WmsCallRecordRepository;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import io.swagger.annotations.ApiResponse;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.List;

/**
 * @Description: 保存wms交互记录
 * <p>
 * @Author: chuwenchao  2019/6/13
 */
@Slf4j
@RomeController
@Api(tags = "wms交互记录")
@RequestMapping("/stock/v1/wmsCallRecord")
public class WmsCallRecordController {

    @Autowired
    private WmsCallRecordRepository wmsCallRecordRepository;

    @Resource
    private WmsCallRecordConvertor wmsCallRecordConvertor;

    @ApiOperation(value = "保存wms交互记录", produces = MediaType.APPLICATION_JSON_VALUE)
    @ApiResponse(code = 200, message = "success")
    @RequestMapping(value = "/saveWmsCallRecord", method = RequestMethod.POST)
    public Response saveWmsCallRecord(@ApiParam(name = "wmsCallRecordDTO", value = "wmsCallRecordDTO") @RequestBody @Valid WmsCallRecordDTO wmsCallRecordDTO) {
        try {
            WmsCallRecordE wmsCallRecordE = wmsCallRecordConvertor.convertDTO2E(wmsCallRecordDTO);
            wmsCallRecordRepository.saveWmsCallRecord(wmsCallRecordE);
            return Response.builderSuccess("");
        } catch (RomeException e) {
            log.error(e.getMessage(), e);
            return Response.builderFail(e.getCode(),e.getMessage());
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return Response.builderFail(ResCode.STOCK_ERROR_1003_DESC,ResCode.STOCK_ERROR_1003_DESC);
        }
    }
    
    @ApiOperation(value = "查询wms交互记录历史数据", produces = MediaType.APPLICATION_JSON_VALUE)
    @ApiResponse(code = 200, message = "success")
    @RequestMapping(value = "/listHistory", method = RequestMethod.POST)
    public Response<PageInfo<com.rome.stock.innerservice.api.dto.WmsCallRecordDTO>> listHistory(@ApiParam(name = "condition", value = "查询条件") @RequestBody com.rome.stock.innerservice.api.dto.WmsCallRecordDTO condition) {
        try {
            return Response.builderSuccess(wmsCallRecordRepository.queryWmsCallRecordListHistory(condition));
        } catch (RomeException e) {
            log.error(e.getMessage(), e);
            return Response.builderFail(e.getCode(),e.getMessage());
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return Response.builderFail(ResCode.STOCK_ERROR_1003_DESC,ResCode.STOCK_ERROR_1003_DESC);
        }
    }

    @ApiOperation(value = "查询wms交互记录实时日志", produces = MediaType.APPLICATION_JSON_VALUE)
    @ApiResponse(code = 200, message = "success")
    @PostMapping(value = "/list/realtimeLog")
    public Response<List<WmsCallRecordDTO>> queryWmsLogByRecordCode(@RequestParam(value = "recordCode",required = false)String recordCode,@RequestParam(value = "sapOrderCode",required = false)String sapOrderCode,@RequestParam("pageSize")Integer pageSize){
     try{
         List<WmsCallRecordDTO> wmsCallRecordDTOS = wmsCallRecordRepository.queryWmsLogByRecordCode(recordCode, sapOrderCode, pageSize);
         return Response.builderSuccess(wmsCallRecordDTOS);
     }catch (RomeException e){
         log.error(e.getMessage(), e);
         return Response.builderFail(e.getCode(),e.getMessage());
     }catch (Exception e){
         log.error(e.getMessage(), e);
         return Response.builderFail(ResCode.STOCK_ERROR_1003_DESC,ResCode.STOCK_ERROR_1003_DESC);
     }
    }


}
