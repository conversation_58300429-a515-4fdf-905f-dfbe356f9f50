package com.rome.stock.innerservice.api.controller;

import com.alibaba.fastjson.JSON;
import com.rome.arch.core.clientobject.Response;
import com.rome.arch.core.exception.RomeException;
import com.rome.arch.util.controller.RomeController;
import com.rome.stock.innerservice.api.dto.RealWarehouse;
import com.rome.stock.innerservice.api.dto.warehouserecord.BatchCreateRecordDTO;
import com.rome.stock.innerservice.api.dto.warehouserecord.CancelRecordDTO;
import com.rome.stock.innerservice.api.dto.warehouserecord.InWarehouseRecordDTO;
import com.rome.stock.innerservice.api.dto.warehouserecord.OutWarehouseRecordDTO;
import com.rome.stock.innerservice.constant.ResponseMsg;
import com.rome.stock.innerservice.domain.repository.SapInterfaceLogRepository;
import com.rome.stock.innerservice.domain.service.DisparityRecordService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiResponse;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;

import javax.annotation.Resource;
import java.util.List;


@Slf4j
@RomeController
@RequestMapping("/stock/v1/disparity")
@Api(tags={"差异单管理接口"})
public class DisparityRecordController {

    @Autowired
    private DisparityRecordService disparityRecordService;

    @Resource
    private SapInterfaceLogRepository sapInterfaceLogRepository;


    @ApiOperation(value = "差异处理：批量创建出入库单", nickname = "createOutAndInRecordBatchForDisparity", produces = MediaType.APPLICATION_JSON_VALUE)
    @ApiResponse(code = 200, message = "success", response = RealWarehouse.class)
    @PostMapping(value = "/createOutAndInRecordBatchForDisparity")
    public Response createOutAndInRecordBatchForDisparity(@RequestBody BatchCreateRecordDTO list) {
        String message = "";
        boolean isSucc = false;
        try {
            disparityRecordService.createOutAndInRecordBatchForDisparity(list);
            message = "200";
            isSucc = true;
            return ResponseMsg.SUCCESS.buildMsg();
        } catch (RomeException e) {
            log.error(e.getMessage(), e);
            message = e.getMessage();
            return ResponseMsg.FAIL.buildMsg(e.getCode(), e.getMessage());
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            message = e.getMessage();
            return ResponseMsg.EXCEPTION.buildMsg();
        } finally {
            if (CollectionUtils.isNotEmpty(list.getOutList())) {
                for (OutWarehouseRecordDTO out : list.getOutList()) {
                    sapInterfaceLogRepository.saveCallBackInterFaceLog(2, out.getRecordCode(), "createOutAndInRecordBatchForDisparity",
                            JSON.toJSONString(list), message, isSucc);
                }
            }
            if (CollectionUtils.isNotEmpty(list.getInList())) {
                for (InWarehouseRecordDTO in : list.getInList()) {
                    sapInterfaceLogRepository.saveCallBackInterFaceLog(2, in.getRecordCode(), "createOutAndInRecordBatchForDisparity",
                            JSON.toJSONString(list), message, isSucc);
                }
            }
        }
    }

    @ApiOperation(value = "差异出入库单取消", nickname = "cancelBatch", produces = MediaType.APPLICATION_JSON_VALUE)
    @ApiResponse(code = 200, message = "success", response = RealWarehouse.class)
    @PostMapping(value = "/cancelBatch" )
    public Response cancelBatch(@RequestBody List<CancelRecordDTO> list) {
        String message = "";
        boolean isSucc = false;
        try {
            disparityRecordService.cancelBatch(list);
            message = "200";
            isSucc = true;
            return ResponseMsg.SUCCESS.buildMsg();
        } catch (RomeException e) {
            log.error(e.getMessage(), e);
            message = e.getMessage();
            return ResponseMsg.FAIL.buildMsg(e.getCode(), e.getMessage());
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            message = e.getMessage();
            return ResponseMsg.EXCEPTION.buildMsg();
        } finally {

            for (CancelRecordDTO out : list) {
                sapInterfaceLogRepository.saveCallBackInterFaceLog(2, out.getRecordCode(), "cancelBatch",
                        JSON.toJSONString(list), message, isSucc);
            }

        }
    }

}
