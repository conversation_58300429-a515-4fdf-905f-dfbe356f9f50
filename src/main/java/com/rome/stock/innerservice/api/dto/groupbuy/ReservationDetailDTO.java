package com.rome.stock.innerservice.api.dto.groupbuy;

import com.rome.arch.core.dto.DTO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.math.BigDecimal;

/**
 * 预约单明细DTO
 */
@Data
public class ReservationDetailDTO extends DTO {

    /**
     * 主键
     */
    @ApiModelProperty(value="主键",hidden = true)
    private Long id;

    /**
     * 所属单据编号
     */
    @ApiModelProperty(value="所属单据编号",hidden = true)
    private String recordCode;

    /**
     * 前置单ID
     */
    @ApiModelProperty(value="前置单ID",hidden = true)
    private Long frontRecordId;

    /**
     * 商品skuId
     */
    @ApiModelProperty(value="商品skuId")
    private Long skuId;

    /**
     * sku编码
     */
    @ApiModelProperty(value="sku编码")
    @NotBlank(message = "sku编码不能为空")
    private String skuCode;

    /**
     * 商品数量
     */
    @ApiModelProperty(value="商品数量")
    @NotNull(message = "商品数量不能为空")
    private BigDecimal skuQty;

    /**
     * 已分配数量
     */
    @ApiModelProperty(value="已分配数量",hidden = true)
    private BigDecimal assignedQty;

    /**
     * 未分配数量
     */
    @ApiModelProperty(value="未分配数量",hidden = true)
    private BigDecimal unassignedQty;

    /**
     * 单位
     */
    @ApiModelProperty(value="单位")
    private String unit;

    /**
     * 单位编码
     */
    @ApiModelProperty(value="单位编码")
    @NotBlank(message = "单位编码")
    private String unitCode;

}    
