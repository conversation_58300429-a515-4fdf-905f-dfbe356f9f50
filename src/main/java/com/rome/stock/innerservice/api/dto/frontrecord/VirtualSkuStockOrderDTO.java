package com.rome.stock.innerservice.api.dto.frontrecord;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.Valid;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * 虚拟物品下单
 */
@Data
public class VirtualSkuStockOrderDTO implements Serializable {

    @ApiModelProperty(value = "渠道编码", required = true)
    @NotBlank(message ="渠道编码不能为空")
    private String channelCode;
    @NotBlank
    @ApiModelProperty(value = "外部单号(SO单号)", required = true)
    private  String orderCode;
    @ApiModelProperty(value = "商户id", required = true)
    private Long merchantId;
    @ApiModelProperty(value = "外部系统数据创建时间(下单时间)", required = true)
    @NotNull
    private Date outCreateTime;
    @ApiModelProperty(value = "用户code")
    private String userCode;
    @ApiModelProperty(value = "交易类型:1-普通,2-预售,3-拼团,4-拼券,5-旺店通,6-POS门店,7-外卖自营,8-外卖第三方,9-电商超市,10-2B分销,11-加盟商，12.虚拟商品")
    private Integer transType;
    @ApiModelProperty(value = "sku数量及明细")
    @Valid
    @NotNull
    private List<OrderDetailDTO> frontRecordDetails;

}
