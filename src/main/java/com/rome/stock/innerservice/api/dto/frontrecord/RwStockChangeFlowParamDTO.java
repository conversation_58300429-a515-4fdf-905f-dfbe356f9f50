package com.rome.stock.innerservice.api.dto.frontrecord;

import java.util.List;

import com.rome.stock.innerservice.api.dto.Pagination;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * Create by <PERSON><PERSON><PERSON><PERSON><PERSON> on 2019/5/11 实仓出入库流水查询DTO类
 */
@Data
public class RwStockChangeFlowParamDTO extends Pagination {

	/**
	 * 仓库流水Id
	 */
	@ApiModelProperty(value = "仓库流水Id")
	private Long id;
	/**
	 * 出入库单号
	 */
	@ApiModelProperty(value = "出入库单号")
	private String recordCode;
	/**
	 * 仓库ID
	 */
	@ApiModelProperty(value = "仓库ID")
	private Long realWarehouseId;
	/**
	 * 仓库Code
	 */
	@ApiModelProperty(value = "仓库CODE")
	private String realWarehouseCode;
	/**
	 * 开始时间
	 */
	@ApiModelProperty(value = "开始时间")
	private String startTime;
	/**
	 * 结束时间
	 */
	@ApiModelProperty(value = "结束时间")
	private String endTime;

	/**
	 * 开始时间
	 */
	@ApiModelProperty(value = "采购单创建时间-开始时间")
	private String poStartTime;
	/**
	 * 结束时间
	 */
	@ApiModelProperty(value = "采购单创建时间-结束时间")
	private String poEndTime;

	/**
	 * 创建人
	 */
	@ApiModelProperty(value = "创建人")
	private Integer creator;
	/**
	 * 仓库出入库类型
	 */
	@ApiModelProperty(value = "仓库出入库类型 1门店零售出库 2门店退货入库")
	private Integer realWarehouseType;
	/**
	 * 出入库单据号
	 */
	@ApiModelProperty(value = "出入库单据号")
	private List<String> recordCodeList;
	/**
	 * 前置单据号
	 */
	@ApiModelProperty(value = "前置单据号")
	private String frontRecordCode;
	/**
	 * 仓库出入库查询类型 1出库 2入库
	 */
	@ApiModelProperty(value = "仓库出入库查询类型 1出 2入")
	private String queryType;
	/**
	 * 仓库出入库类型
	 */
	@ApiModelProperty(value = "仓库出入库类型")
	private Integer[] stockTypes;
	/**
	 * skuIds
	 */
	@ApiModelProperty(value = "skuIds")
	private List<Long> skuIds;

	/**
	 * SAP交货单号
	 */
	@ApiModelProperty(value = "SAP交货单号")
	private List<String> sapOrderCodes;
	
	/**
	 * 库存交易类型
	 */
	@ApiModelProperty(value = "库存交易类型")
	private List<Integer> transTypes;


	/**
	 * 实仓编号
	 */
	@ApiModelProperty(value = "仓库编号")
	private List<String> realWarehouseCodes;

	/**
	 * 仓库ID
	 */
	@ApiModelProperty(value = "仓库ID")
	private List<Long>  realWarehouseIds;

	/**
	 * 采购单/PO单号
	 */
	@ApiModelProperty(value = "采购单/PO单号")
	private String purchaseOrderNo;

	/**
	 * 采购单/PO单号
	 */
	@ApiModelProperty(value = "采购单/PO单号")
	private List<String> purchaseOrderNos;

	/**
	 * 渠道ID
	 */
	@ApiModelProperty(value = "渠道ID")
	private String channelCode;

	/**
	 * 交易单号
	 */
	@ApiModelProperty(value = "交易单号")
	private List<String> outRecordCodeList;
}
