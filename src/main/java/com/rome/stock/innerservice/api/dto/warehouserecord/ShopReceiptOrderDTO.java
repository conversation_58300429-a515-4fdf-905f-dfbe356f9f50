package com.rome.stock.innerservice.api.dto.warehouserecord;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.Valid;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * 类ShopReceiptOrder的实现描述：门店确认收货单据
 *
 * <AUTHOR> 2019/4/29 15:48
 */
@Data
@EqualsAndHashCode
public class ShopReceiptOrderDTO {

    @ApiModelProperty(value = "单据编码")
    private String recordCode;

    @ApiModelProperty(value = "业务编码")
    private String sapOrderCode;


    @ApiModelProperty(value = "店铺Code")
    private String shopCode;

    /**
     * 来源 1--pos7 不传和传0都是非pos7
     */
    private Integer sourceType;

    @Valid
    @ApiModelProperty(value = "sku数量及明细")
    @NotNull(message="sku数量及明细不能为空")
    private List<ShopReceiptDetailDTO> warehouseRecordDetails;
}
