package com.rome.stock.innerservice.api.dto.alarm;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

@Data
public class StoreAlarmConfig {

    @ApiModelProperty(value = "主键ID")
    private Long id;

    @ApiModelProperty(value = "预警通知人员，多个逗号分割")
    private String accountCodes;

    @ApiModelProperty(value = "预警方式：1-飞书")
    private Integer alarmWay;

    @ApiModelProperty(value = "预警值来源:1.需求计划 2.最晚销售日期消耗量")
    private Integer alarmSourceType;

    @ApiModelProperty(value = "比较关系:-1.小于,0.等于,1.大于")
    private Integer compareType;

    @ApiModelProperty(value = "预警比较值:1.生产日期库存量 2.当前批次库存量")
    private Integer compareValueType;

    @ApiModelProperty(value = "预警上限分子")
    private Integer molecule;

    @ApiModelProperty(value = "预警上限分母")
    private Integer denominator;

    @ApiModelProperty(value="预警下限数量")
    private BigDecimal lowerLimitQty;

    @ApiModelProperty(value="预警类型=1时，预警天数/ 类型=2时，历史销量天数")
    private Integer alarmDayCount;

    private String storeName;

}
