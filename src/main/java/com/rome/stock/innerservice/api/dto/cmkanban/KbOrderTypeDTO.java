package com.rome.stock.innerservice.api.dto.cmkanban;

import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 类KbOrderTypeDTO的实现描述：看板订单类型
 *
 * <AUTHOR> 2020/7/11 21:06
 */
@Data
@EqualsAndHashCode
public class KbOrderTypeDTO {

    /**
     * 订单类型
     */
    private Integer orderType;

    /**
     * 订单类型名
     */
    private String orderTypeName;

    /**
     * 成交订单数
     */
    private Integer dealOrderNum;

    /**
     * 已接收订单数
     */
    private Integer syncOrderNum;

    /**
     * 未接收订单数
     */
    private Integer unSyncOrderNum;

    /**
     * 已发货订单数
     */
    private Integer deliveryOrderNum;

    /**
     * 已取消订单数
     */
    private Integer cancleOrderNum;

    /**
     * 24小时未发货订单数
     */
    private Integer tfdeliveryOrderNum;

    /**
     * 48小时未发货订单数
     */
    private Integer feDeliveryOrderNum;
}
