package com.rome.stock.innerservice.api.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import java.util.List;

/**
 * <AUTHOR>
 * @description 查询门店发货工厂实时库存
 * @date 2020/9/16 14:15
 * @throw
 */
@Data
public class QueryShipFactoryStock {

    @ApiModelProperty(value = "门店编码")
    @NotBlank(message = "门店编码不能为空")
    private String shopCode;

    @ApiModelProperty(value = "sku编码集合")
    @NotEmpty(message = "sku编码不能为空")
    private List<String> skuCodeList;

}    
   