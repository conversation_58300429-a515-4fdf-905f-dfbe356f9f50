package com.rome.stock.innerservice.api.dto;

import lombok.Data;

@Data
public class PurchaseRequest {
    
    // 申购人工号
    private String subscribeUserCode;

    // 申购人姓名
    private String subscribeUserName;

    // 申购人联系方式
    private String subscribeUserContact;

    // 采购人工号
    private String purchaseUserCode;

    // 采购人姓名
    private String purchaseUserName;

    // 采购人联系方式
    private String purchaseUserContact;

    // 申购人/采购人领导工号
    private String managerCode;

    // 申购人/采购人领导姓名
    private String managerName;

    // 申购/采购人部门
    private String department;

    // 申购/采购人部门成本中心
    private String costCenter;
}