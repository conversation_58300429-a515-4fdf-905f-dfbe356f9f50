package com.rome.stock.innerservice.api.controller;


import com.github.pagehelper.PageInfo;
import com.rome.arch.core.clientobject.Response;
import com.rome.arch.core.exception.RomeException;
import com.rome.arch.util.controller.RomeController;
import com.rome.stock.innerservice.api.dto.BatchBoxConfigDTO;
import com.rome.stock.innerservice.common.ResCode;
import com.rome.stock.innerservice.constant.ParamValidator;
import com.rome.stock.innerservice.constant.ResponseMsg;
import com.rome.stock.innerservice.domain.service.BatchBoxConfigService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiResponse;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;

import javax.annotation.Resource;
import java.util.List;

@Slf4j
@RomeController
@RequestMapping("/stock/v1/batchBoxConfig")
@Api(tags={"批次原箱配置相关接口"})
public class BatchBoxConfigController {

    @Resource
    private BatchBoxConfigService batchBoxConfigService;

    private ParamValidator validator = ParamValidator.INSTANCE;

    @ApiOperation(value = "根据查询条件查询所有批次原箱配置信息", nickname = "getInfoByQueryCondition", produces = MediaType.APPLICATION_JSON_VALUE)
    @ApiResponse(code = 200, message = "success", response = List.class)
    @RequestMapping(value = "/getInfoByQueryCondition", method = RequestMethod.POST)
    public Response<PageInfo<BatchBoxConfigDTO>> getInfoByQueryCondition(@RequestBody BatchBoxConfigDTO dto) {
        try {
            PageInfo<BatchBoxConfigDTO> pageList = batchBoxConfigService.getInfoByQueryCondition(dto);
            return Response.builderSuccess(pageList);
        } catch (RomeException e) {
            log.error(e.getMessage(), e);
            return ResponseMsg.EXCEPTION.buildMsg(e.getCode(), e.getMessage());
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return ResponseMsg.EXCEPTION.buildMsg(ResCode.STOCK_ERROR_1003, ResCode.STOCK_ERROR_1003_DESC);
        }
    }

    @ApiOperation(value = "新增或修改", nickname = "saveOrUpdate", produces = MediaType.APPLICATION_JSON_VALUE)
    @ApiResponse(code = 200, message = "success")
    @RequestMapping(value = "/saveOrUpdate", method = RequestMethod.POST)
    public Response saveOrUpdate(@RequestBody BatchBoxConfigDTO configDTO) {
        try {
            batchBoxConfigService.saveOrUpdate(configDTO);
            return ResponseMsg.SUCCESS.buildMsg("");
        } catch (RomeException e) {
            log.error(e.getMessage(), e);
            return ResponseMsg.FAIL.buildMsg(e.getCode(), e.getMessage());
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return ResponseMsg.EXCEPTION.buildMsg(ResCode.STOCK_ERROR_1003_DESC, ResCode.STOCK_ERROR_1003_DESC + e.getMessage());
        }
    }

    @ApiOperation(value = "开启原箱库存", nickname = "enableBox", produces = MediaType.APPLICATION_JSON_VALUE)
    @ApiResponse(code = 200, message = "success")
    @RequestMapping(value = "/enableBox", method = RequestMethod.POST)
    public Response enableBox(@RequestBody BatchBoxConfigDTO configDTO) {
        try {
            batchBoxConfigService.enableBox(configDTO);
            return ResponseMsg.SUCCESS.buildMsg("");
        } catch (RomeException e) {
            log.error(e.getMessage(), e);
            return ResponseMsg.FAIL.buildMsg(e.getCode(), e.getMessage());
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return ResponseMsg.EXCEPTION.buildMsg(ResCode.STOCK_ERROR_1003_DESC, ResCode.STOCK_ERROR_1003_DESC + e.getMessage());
        }
    }


    @ApiOperation(value = "根据仓库编码和库存渠道查询对应原箱配置", nickname = "queryBatchBoxConfig", produces = MediaType.APPLICATION_JSON_VALUE)
    @ApiResponse(code = 200, message = "success", response = List.class)
    @RequestMapping(value = "/queryBatchBoxConfig", method = RequestMethod.POST)
    public Response<BatchBoxConfigDTO> queryBatchBoxConfig(@RequestParam("realWarehouseCode") String realWarehouseCode) {
        try {
            BatchBoxConfigDTO res = batchBoxConfigService.queryBatchBoxConfig(realWarehouseCode);
            return Response.builderSuccess(res);
        } catch (RomeException e) {
            log.error(e.getMessage(), e);
            return ResponseMsg.EXCEPTION.buildMsg(e.getCode(), e.getMessage());
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return ResponseMsg.EXCEPTION.buildMsg(ResCode.STOCK_ERROR_1003, e.getMessage());
        }
    }

}
