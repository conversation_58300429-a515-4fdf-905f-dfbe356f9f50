package com.rome.stock.innerservice.api.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;
import java.util.List;

/**
 * @descroption:收货单信息带明细
 */
@Data
public class ReceiptRecordWithDetailDTO {
    /**
     * 唯一主键
     */
    @ApiModelProperty(value = "唯一主键")
    private Long id;

    /**
     * po前置单据编号
     */
    @ApiModelProperty(value = "po前置单据编号")
    private String frontRecordCode;

    /**
     * sap采购单编号
     */
    @ApiModelProperty(value = "sap采购单编号")
    private String outRecordCode;

    /**
     * 入库单据编号
     */
    @ApiModelProperty(value = "入库单据编号")
    private String warehouseRecordCode;

    /**
     * wms单据编号
     */
    @ApiModelProperty(value = "wms单据编号")
    private String wmsRecordCode;

    /**
     * 质检同步状态 0:待同步 1:已同步
     */
    @ApiModelProperty(value = "质检同步状态")
    private String qualityStatus;

    /**
     * 是否推送订单中心
     */
    @ApiModelProperty(value = "是否推送订单中心")
    private Integer syncOrderStatus;

    private Date createTime;

    private Date updateTime;

    /***
     * 收货单明细
     */
    @ApiModelProperty(value = "收货单明细")
    List<RwBatchDTO> details;
    
    @ApiModelProperty(value = "操作员编码（工号）")
    private String operatorCode;
    
    @ApiModelProperty(value = "操作员名称")
    private String operatorName;
}
