package com.rome.stock.innerservice.api.dto;

import com.rome.arch.core.dto.DTO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * <AUTHOR>
 * @Description 虚仓权限查询DTO对象
 * @date 2020/6/9 14:35
 */
@Data
public class QueryVmSkuPermitDTO extends DTO {

    @ApiModelProperty(value="虚仓组id集合")
    @NotNull
    private List<Long> groupIds;

    @ApiModelProperty(value="skuId集合")
    @NotNull
    private List<Long> skuIds;

    @ApiModelProperty(value="是否有权限")
    private Integer isPermit;

}    
   