package com.rome.stock.innerservice.api.dto.alarm;


import com.rome.stock.innerservice.infrastructure.dataobject.BaseDo;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.util.Date;

/**
 * DO 实体类
 * 
 * <AUTHOR>
 * @date 2024-04-11
 */
@Data
@NoArgsConstructor
@EqualsAndHashCode
public class BatchSaleAlarmDetailDTO extends BaseDo {

	/**
     * 唯一主键
     */
	private Long id;

	/**
     * 执行记录id
     */
	private Long headId;

	/**
     * 预警id
     */
	private Long alarmId;

	/**
     * 商品skuId
     */
	private Long skuId;

	/**
     * sku编码
     */
	private String skuCode;

	/**
	 * 商品名称
	 */
	private String skuName;


	/**
     * 库存日期
     */
	private Date stockDate;

	/**
     * 生产日期
     */
	private Date productDate;

	/**
	 * 生产日期
	 */
	private String batchCode;

	/**
     * 有效期,天数
     */
	private Integer validity;

	/**
     * 到达配置效期需要的天数
     */
	private Integer alarmDayCount;

	/**
     * 物料批次处理的顺序，从小到大依次处理
     */
	private Integer sortNum;

	/**
     * 累计需求计划量
     */
	private BigDecimal currentSumSaleQty;

	/**
     * 计划量每个生产日期的消耗量
     */
	private BigDecimal saleQty;

	/**
     * 批次日期库存量
     */
	private BigDecimal stockQty;

	/**
     * 累计的批次库存量
     */
	private BigDecimal currentSumStockQty;

	/**
     * 累计的预警滞销量
     */
	private BigDecimal currentSumAlarmQty;

	/**
     * 每个批次滞销量
     */
	private BigDecimal alarmQty;

	/**
     * 配置预警下限数量
     */
	private BigDecimal configAlarmQty;

	/**
     * 预警状态：0-无需发送 1-需要发送
     */
	private Integer alarmStatus;

	@ApiModelProperty(value = "创建时间")
	private Date createTime;

	@ApiModelProperty(value = "修改时间")
	private Date updateTime;

	@ApiModelProperty(value = "创建人")
	private Long creator;

	@ApiModelProperty(value = "更新人")
	private Long modifier;

	/**
	 * 销售状态
	 */
	private String saleStatus;

}
