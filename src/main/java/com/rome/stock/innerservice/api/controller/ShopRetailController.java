package com.rome.stock.innerservice.api.controller;

import com.alibaba.fastjson.JSON;
import com.rome.arch.core.clientobject.Response;
import com.rome.arch.core.exception.RomeException;
import com.rome.arch.util.controller.RomeController;
import com.rome.stock.innerservice.api.dto.BatchStockChangeFlowDTO;
import com.rome.stock.innerservice.api.dto.warehouserecord.BatchResultDTO;
import com.rome.stock.innerservice.api.dto.warehouserecord.CancelRecordDTO;
import com.rome.stock.innerservice.api.dto.warehouserecord.LockStockResp;
import com.rome.stock.innerservice.api.dto.warehouserecord.OutWarehouseRecordDTO;
import com.rome.stock.innerservice.common.ResCode;
import com.rome.stock.innerservice.constant.ResponseMsg;
import com.rome.stock.innerservice.domain.repository.SapInterfaceLogRepository;
import com.rome.stock.innerservice.domain.service.ShopRetailService;
import com.rome.stock.wms.util.StringUtils;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import io.swagger.annotations.ApiResponse;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

/**
 * 门店零售
 */
@Slf4j
@RomeController
@RequestMapping("/stock/v1/shop_retail")
@Api(tags={"门店零售"})
public class ShopRetailController {
    @Autowired
    private ShopRetailService shopRetailService;
    @Autowired
    private SapInterfaceLogRepository sapInterfaceLogRepository;

    /**
     * 门店零售出库
     * @return
     */
    @ApiOperation(value = "门店零售出库", nickname = "addShopRetailRecord", produces = MediaType.APPLICATION_JSON_VALUE)
    @ApiResponse(code = 200, message = "success", response = String.class)
    @RequestMapping(value = "/addShopRetailRecord", method = RequestMethod.POST)
    public Response addShopRetailRecord(@ApiParam(name = "outWarehouseRecordDTO", value = "门店零售前置单") @RequestBody @Validated OutWarehouseRecordDTO outWarehouseRecordDTO) {
        try {
            shopRetailService.addShopRetailRecord(outWarehouseRecordDTO);
            return ResponseMsg.SUCCESS.buildMsg();
        } catch (RomeException e) {
            log.error(e.getMessage(), e);
            return ResponseMsg.FAIL.buildMsg(e.getCode(),e.getMessage());
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return ResponseMsg.EXCEPTION.buildMsg(ResCode.STOCK_ERROR_1003_DESC,ResCode.STOCK_ERROR_1003_DESC);
        }
    }

    @ApiOperation(value = "批量取消申请", nickname = "cancleWhAllocation", produces = MediaType.APPLICATION_JSON_VALUE)
    @ApiResponse(code = 200, message = "success")
    @RequestMapping(value = "/cancleBatch", method = RequestMethod.POST)
    public Response cancleBatch(@RequestBody List<CancelRecordDTO> list){
        List<BatchResultDTO> resultList =  new ArrayList<>();
        try {
            if(CollectionUtils.isNotEmpty(list)){
                for (CancelRecordDTO cancelRecordDTO : list) {
                    BatchResultDTO dto = new BatchResultDTO();
                    dto.setRecordCode(cancelRecordDTO.getRecordCode());
                    dto.setStatus(false);
                    String message = "";
                    boolean isSucc = false;
                    try {
                        //门店零售没有强制取消
                        shopRetailService.cancelShopRetailRecord(cancelRecordDTO.getRecordCode());
                        dto.setStatus(true);
                        isSucc = true;
                        message = "200";
                    }catch (Exception e){
                        message=e.getMessage();
                        log.error(e.getMessage(), e);
                    }finally {
                        dto.setMessage(message);
                        resultList.add(dto);
                        sapInterfaceLogRepository.saveCallBackInterFaceLog(1, dto.getRecordCode(), "shopRetailCancleBatch",
                                JSON.toJSONString(cancelRecordDTO), message, isSucc);
                    }

                }
            }
            return ResponseMsg.SUCCESS.buildMsg(resultList);
        } catch (RomeException e) {
            log.error(e.getMessage(), e);
            return Response.builderFail(e.getCode(),e.getMessage());
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return ResponseMsg.EXCEPTION.buildMsg();
        }
    }




    @ApiOperation(value = "批量取消申请（刷数据用）", nickname = "cancelByRecordCodes", produces = MediaType.APPLICATION_JSON_VALUE)
    @ApiResponse(code = 200, message = "success")
    @RequestMapping(value = "/cancelShopRetailByRecordCodes", method = RequestMethod.POST)
    public Response cancelByRecordCodes(@RequestBody String recordCodes){
        List<BatchResultDTO> resultList =  new ArrayList<>();
        if(StringUtils.isEmpty(recordCodes)){
            throw new RomeException(ResCode.STOCK_ERROR_1002, "单号不能为空");
        }
        String ss[]=recordCodes.split("\n");
        try {
            List<String> list= Arrays.asList(ss);
            if(CollectionUtils.isNotEmpty(list)){
                for (String recordCode : list) {
                    BatchResultDTO dto = new BatchResultDTO();
                    dto.setRecordCode(recordCode);
                    dto.setStatus(false);
                    String message = "";
                    boolean isSucc = false;
                    try {
                        shopRetailService.cancelShopRetailRecord(recordCode);
                        dto.setStatus(true);
                        isSucc = true;
                        message = "200";
                    }catch (Exception e){
                        message=e.getMessage();
                        log.error(e.getMessage(), e);
                    }finally {
                        dto.setMessage(message);
                        resultList.add(dto);
                        sapInterfaceLogRepository.saveCallBackInterFaceLog(1, dto.getRecordCode(), "cancelShopRetailByRecordCodes",
                                recordCode, message, isSucc);
                    }
                }
            }
            return ResponseMsg.SUCCESS.buildMsg(resultList);
        } catch (RomeException e) {
            log.error(e.getMessage(), e);
            return Response.builderFail(e.getCode(),e.getMessage());
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return Response.builderFail("-50000",e.getMessage());
        }
    }

    @ApiOperation(value = "根据出入库单查询批次信息", nickname = "queryBatchInfoByRecordCode", produces = MediaType.APPLICATION_JSON_VALUE)
    @ApiResponse(code = 200, message = "success",response =List.class )
    @GetMapping("/queryBatchInfoByRecordCode")
    public Response<List<BatchStockChangeFlowDTO>> queryBatchInfoByRecordCode(@RequestParam("recordCode")String recordCode){
        try{
            List<BatchStockChangeFlowDTO> batchStockList = shopRetailService.queryBatchInfoByRecordCode(recordCode);
            return Response.builderSuccess(batchStockList);
        }catch (RomeException e){
            log.error(e.getMessage(), e);
            return Response.builderFail(e.getCode(),e.getMessage());
        }catch (Exception e){
            log.error(e.getMessage(), e);
            return ResponseMsg.EXCEPTION.buildMsg();
        }
    }




    @ApiOperation(value = "减负库存并返回Sku缺货数量", nickname = "negativeLockStockAndReturn", produces = MediaType.APPLICATION_JSON_VALUE)
    @ApiResponse(code = 200, message = "success", response = Response.class)
    @RequestMapping(value = "/negativeLockStockAndReturn", method = RequestMethod.POST)
    public Response<LockStockResp> negativeLockStockAndReturn(@RequestBody OutWarehouseRecordDTO dto) {
        try {
            LockStockResp res=shopRetailService.negativeLockStockAndReturn(dto);
            return Response.builderSuccess(res);
        } catch (RomeException e) {
            log.error(e.getMessage(), e);
            return Response.builderFail(e.getCode(),e.getMessage());
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return Response.builderFail(ResCode.STOCK_ERROR_1003,ResCode.STOCK_ERROR_1003_DESC+e.getMessage()==null?e.toString():e.getMessage());
        }
    }


}
