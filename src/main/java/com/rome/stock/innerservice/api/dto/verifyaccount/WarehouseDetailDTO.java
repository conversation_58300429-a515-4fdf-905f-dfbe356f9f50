package com.rome.stock.innerservice.api.dto.verifyaccount;

import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.math.BigDecimal;

/**
 * 仓库明细DTO
 *
 * <AUTHOR>
 * @date 2019/06/05
 */
@Data
public class WarehouseDetailDTO {
    /**
     * 仓库代码
     */
    @NotBlank(message = "[ warehouseCode ]仓库代码不能为空")
    private String warehouseCode;
    /**
     * 入库/出库数量
     */
    @NotNull(message = "[ quantity ]数量不能为空")
    private BigDecimal quantity;
    /**
     * 入库/出库单号
     */
    private String orderNo;

}