package com.rome.stock.innerservice.api.dto.frontrecord;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;

@Data
@EqualsAndHashCode
public class RwStockQty {


    @ApiModelProperty(value = "门店编码")
    private String shopCode;


    @ApiModelProperty(value = "商品编码")
    private String skuCode;

    @ApiModelProperty(value = "实际库存数量")
    private BigDecimal realQty;

}
