package com.rome.stock.innerservice.api.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import java.io.Serializable;

@Data
public class WDTStockLogisticInfoParamDTO implements Serializable {

    @ApiModelProperty(value = "订单编号",required = true)
    @NotBlank(message="订单编号不能为空")
    private String soCode;


    @ApiModelProperty(value = "物流公司编码" , required = true)
    @NotBlank(message="物流公司编码不能为空")
    private String logisticsCode;

}
