/**
 * Filename RefreshBatchStockParam.java
 * Company 上海来伊份科技有限公司。
 * <AUTHOR>
 * @version 
 */
package com.rome.stock.innerservice.api.dto;

import java.util.List;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 刷批次库存
 * <AUTHOR>
 * @since 2021-4-13 15:05:24
 */
@Data
@EqualsAndHashCode
public class RefreshBatchStockDTO {
	
	@ApiModelProperty(value = "明细")
	private List<RefreshBatchStockDetailDTO> details;

	@ApiModelProperty(value = "原因备注")
	private String reason;
	
	@ApiModelProperty(value = "用户Id")
	private Long userId;

	@ApiModelProperty(value = "是否手动处理，否则自动处理，一般在扣减批次库存，为false时不按 fingerPrint来扣减")
	private Boolean manual;
}
