package com.rome.stock.innerservice.api.dto;


import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;

@Data
@EqualsAndHashCode
public class WarehouseInventoryStartDTO implements Serializable {

    private static final long serialVersionUID = 6241269605258899670L;

    @ApiModelProperty(value = "盘点单Id")
    private Long warehouseInventoryStartId;

    @ApiModelProperty(value = "需要启动盘点的仓库id集合")
    private Long realWarehouseId;

}
