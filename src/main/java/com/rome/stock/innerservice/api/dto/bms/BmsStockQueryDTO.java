package com.rome.stock.innerservice.api.dto.bms;

import com.rome.stock.innerservice.api.dto.Pagination;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

@Data
public class BmsStockQueryDTO extends Pagination {

    @ApiModelProperty(value="实仓编码，多个换行分割")
    private String warehouseCode;


    private List<String> warehouseCodeList;

    @ApiModelProperty(value="仓库编码，多个换行分割")
    private String zoneCode;

    private List<Long> realWarehouseIdList;

    private List<String> factoryCodeList;

    @ApiModelProperty(value="库龄范围")
    private String ageType;

    private Integer startAge;



    private String serialNo;

    private Integer endAge;

    @ApiModelProperty(value="库存日期结束时间")
    private String endCreateTime;

    @ApiModelProperty(value="库存日期开始时间")
    private String startCreateTime;


    @ApiModelProperty(value = "导出excel操作")
    private boolean importExcel;
}
