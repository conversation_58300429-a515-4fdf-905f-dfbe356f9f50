package com.rome.stock.innerservice.api.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

@Data
@EqualsAndHashCode
public class SkuOfStockRecord {
    /**
     * skuId
     */
    @ApiModelProperty(value = "主键")
    private Long id;
    /**
     * skuName
     */
    @ApiModelProperty(value = "sku名称")
    private String name;
    /**
     * skuCode
     */
    @ApiModelProperty(value = "sku编码")
    private String skuCode;
}
