package com.rome.stock.innerservice.api.dto.groupbuy;

import cn.afterturn.easypoi.excel.annotation.Excel;
import lombok.Data;

import java.util.Date;

/**
 * 预入库报表导出模板
 */
@Data
public class GroupWarehouseRecordTemplate {

    private Long id;

    @Excel(name = "出库单号")
    private String recordCode;

    @Excel(name = "预约单号")
    private String outRecordCode;

    @Excel(name = "所属客户")
    private String userCode;

    @Excel(name = "派车单号")
    private String tmsRecordCode;

    @Excel(name = "团购出库单状态")
    private String recordStatusName;

    @Excel(name = "预计交货时间",exportFormat = "yyyy-MM-dd HH:mm:ss")
    private Date expectReceiveDateStart;

    @Excel(name = "供应工厂")
    private String factoryCode;

    @Excel(name = "发货仓库编号")
    private String realWarehouseCode;

    @Excel(name = "发货仓库名称")
    private String realWarehouseName;

    @Excel(name = "创建时间",exportFormat = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;

    @Excel(name = "更新时间",exportFormat = "yyyy-MM-dd HH:mm:ss")
    private Date updateTime;

    private Integer recordStatus;

    private Integer recordType;

    private Long realWarehouseId;

    private String recordTypeName;

    private Date OutCreateTime;
}
