package com.rome.stock.innerservice.api.dto.frontrecord;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 库存流水
 * <AUTHOR>
 */
@Data
public class RwStockStatementDTO extends RwStockChangeFlowResultDTO implements Serializable {
    /**
     * 加盟商编码
     */
    @ApiModelProperty(value = "加盟商编码")
    private String franchisee;

    /**
     * 加盟商名称
     */
    @ApiModelProperty(value = "加盟商名称")
    private String franchiseeName;

    /**
     * 门店编码
     */
    @ApiModelProperty(value = "门店编码")
    private String storeCode;

    /**
     * 门店名称
     */
    @ApiModelProperty(value = "门店名称")
    private String storeName;

    /**
     * 记账类型
     */
    @ApiModelProperty(value = "记账类型")
    private String stockTypeDesc;

    /**
     * 业务类型
     */
    @ApiModelProperty(value = "业务类型")
    private String recordTypeDesc;

    /**
     * 库存发生金额
     */
    @ApiModelProperty(value = "库存发生金额")
    private BigDecimal totalPrice;

    /**
     * 含税金额
     */
    @ApiModelProperty(value = "含税金额")
    private BigDecimal totalPriceTax;

    /**
     * 商品品类
     */
    @ApiModelProperty(value = "商品品类")
    private String categoryName4;


    /**
     * 财务时间
     */
    @ApiModelProperty(value = "财务时间")
    @JsonFormat(pattern = "yyyy-MM-dd" ,timezone = "GMT+8")
    private Date financeDate;
}
