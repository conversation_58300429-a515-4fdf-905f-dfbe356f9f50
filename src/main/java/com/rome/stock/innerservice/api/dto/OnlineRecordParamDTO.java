package com.rome.stock.innerservice.api.dto;

import lombok.Data;

import java.util.List;

@Data
public class OnlineRecordParamDTO extends Pagination  {

    private String doRecordCode;
    private String soRecordCode;
    private String userCode;
    private String channelCode;
    private Long realWarehouseId;
    private Integer recordStatus;
    private String startTime;
    private String endTime;
    private List<Long> skuIds;

}
