package com.rome.stock.innerservice.api.dto;

import com.rome.stock.innerservice.domain.entity.frontrecord.PurchaseOrderE;
import com.rome.stock.innerservice.domain.entity.warehouserecord.WarehouseRecordE;
import lombok.Data;

/**
 * <AUTHOR>
 * @Description 采购信息组织对象
 * @date 2020/8/28 19:27
 */
@Data
public class BuildPurchaseInfoDTO {

    private WarehouseRecordE warehouseRecordE;

    private PurchaseOrderE purchaseOrderE;



}    
   