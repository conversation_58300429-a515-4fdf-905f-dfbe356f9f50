package com.rome.stock.innerservice.api.dto.warehouserecord;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 类CancelResultDTO的实现描述：取消结果
 *
 * <AUTHOR> 2020/6/17 10:25
 */
@Data
@EqualsAndHashCode
public class BatchResultDTO {

    @ApiModelProperty(value = "外部系统单据编码", required = true)
    private String recordCode;

    @ApiModelProperty(value = "取消状态", required = true)
    private Boolean status;

    @ApiModelProperty(value = "错误信息")
    private String message;

    public BatchResultDTO() {
        super();
    }

    public BatchResultDTO(String recordCode, Boolean status) {
        this.recordCode = recordCode;
        this.status = status;
    }

    public BatchResultDTO(String recordCode, Boolean status,String message) {
        this.recordCode = recordCode;
        this.status = status;
        this.message=message;
    }
}
