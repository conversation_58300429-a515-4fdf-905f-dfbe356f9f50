package com.rome.stock.innerservice.api.dto.cloudshop;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

@Data
@NoArgsConstructor
@EqualsAndHashCode
public class RwServiceLabDTO {

    private Long id;

    private Long labId;

    private Long realWarehouseId;

    /**
     * 时效
     */
    private BigDecimal prescription;

    /**
     * 标签名称
     */
    private String labName;

    /**
     * 标签类型(1: 门店 2: 仓库 3: 供应商)
     */
    private Integer labType;

    /**
     * 标签描述
     */
    private String labDesc;

    /**
     * 业务类型(1: 门店 2: 仓库 3: 供应商)
     */
    private Integer businessType;
    
    @ApiModelProperty(value = "创建人")
    private Long creator;
    
    @ApiModelProperty(value = "更新人")
    private Long modifier;
    
    @ApiModelProperty(value = "开始截单时间")
    private Date startOffTime;
    
    @ApiModelProperty(value = "结束截单时间")
    private Date endOffTime;
    
    @ApiModelProperty(value = "截单后配送状态：0-不配送 1-配送")
    private Integer deliveryOffStatus;
    
    @ApiModelProperty(value = "截单后时效")
    private BigDecimal prescriptionOff;

    @ApiModelProperty(value = "时效标签集合")
    private List<Long> labIds;


    
}
