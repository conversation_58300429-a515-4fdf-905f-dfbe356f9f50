/**
 * Filename SapInterfaceLogController.java
 * Company 上海来伊份科技有限公司。
 * <AUTHOR>
 * @version 
 */
package com.rome.stock.innerservice.api.controller;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;

import com.github.pagehelper.PageInfo;
import com.rome.arch.core.clientobject.Response;
import com.rome.arch.core.exception.RomeException;
import com.rome.arch.util.controller.RomeController;
import com.rome.stock.innerservice.api.dto.SapInterfaceLogDTO;
import com.rome.stock.innerservice.common.ResCode;
import com.rome.stock.innerservice.domain.repository.SapInterfaceLogRepository;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import io.swagger.annotations.ApiResponse;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.RequestParam;

/**
 * sap交互记录
 * <AUTHOR>
 * @since 2019年12月25日 上午11:43:27
 */
@Slf4j
@RomeController
@Api(tags = "sap交互记录")
@RequestMapping("/stock/v1/sapInterfaceLog")
public class SapInterfaceLogController {

	@Autowired
    private SapInterfaceLogRepository sapInterfaceLogRepository;
	
	@ApiOperation(value = "查询sap交互记录历史数据", produces = MediaType.APPLICATION_JSON_VALUE)
    @ApiResponse(code = 200, message = "success")
    @RequestMapping(value = "/listHistory", method = RequestMethod.POST)
    public Response<PageInfo<SapInterfaceLogDTO>> listHistory(@ApiParam(name = "condition", value = "查询条件") @RequestBody SapInterfaceLogDTO condition) {
        try {
            return Response.builderSuccess(sapInterfaceLogRepository.querySapInterfaceLogListHistory(condition));
        } catch (RomeException e) {
            log.error(e.getMessage(), e);
            return Response.builderFail(e.getCode(),e.getMessage());
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return Response.builderFail(ResCode.STOCK_ERROR_1003_DESC,ResCode.STOCK_ERROR_1003_DESC);
        }
    }

    @ApiOperation(value = "查询sap过账推送日志最新一条", produces = MediaType.APPLICATION_JSON_VALUE)
    @ApiResponse(code = 200, message = "success")
    @RequestMapping(value = "/sapPostLog", method = RequestMethod.POST)
    public Response<SapInterfaceLogDTO> listHistory(@ApiParam(name = "recordCode") @RequestParam String recordCode , @ApiParam(name = "requestService") @RequestParam String requestService) {
        try {
            return Response.builderSuccess(sapInterfaceLogRepository.queryByCodeAndRequestService(recordCode , requestService));
        } catch (RomeException e) {
            log.error(e.getMessage(), e);
            return Response.builderFail(e.getCode(),e.getMessage());
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return Response.builderFail(ResCode.STOCK_ERROR_1003_DESC,ResCode.STOCK_ERROR_1003_DESC);
        }
    }
}
