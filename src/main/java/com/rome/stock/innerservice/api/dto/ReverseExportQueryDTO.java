package com.rome.stock.innerservice.api.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;


/**
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode
public class ReverseExportQueryDTO extends Pagination {

    @ApiModelProperty(value = "冲销类型")
    private Integer businessType;

    @ApiModelProperty(value = "冲销维度:1.业务单号,2.出入库单号")
    private Integer type;

    @ApiModelProperty(value = "业务单号,多个使用逗号拼接")
    private String sapOrderCode;

    @ApiModelProperty(value = "冲销出入库单号,多个使用逗号拼接")
    private String originWarehouseRecordCode;

}
