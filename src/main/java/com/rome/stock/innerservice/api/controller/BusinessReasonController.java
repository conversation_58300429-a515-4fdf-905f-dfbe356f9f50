package com.rome.stock.innerservice.api.controller;

import java.util.List;

import javax.annotation.Resource;

import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;

import com.rome.arch.core.clientobject.Response;
import com.rome.arch.core.exception.RomeException;
import com.rome.arch.util.controller.RomeController;
import com.rome.stock.innerservice.api.dto.BusinessReasonDTO;
import com.rome.stock.innerservice.constant.ResponseMsg;
import com.rome.stock.innerservice.domain.service.BusinessReasonService;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiResponse;
import lombok.extern.slf4j.Slf4j;

/**
 * @Description
 * <AUTHOR>
 * @Date 2019/5/13 15:45
 * @Version 1.0
 */
@Slf4j
@RomeController
@RequestMapping("/stock/v1/business_reason")
@Api(tags={"业务原因服务接口"})
public class BusinessReasonController {

    @Resource
    BusinessReasonService businessReasonService;

    @ApiOperation(value = "根据单据类型查询业务原因", nickname = "queryBusinessReason", produces = MediaType.APPLICATION_JSON_VALUE)
    @ApiResponse(code = 200, message = "success")
    @RequestMapping(value = "/queryBusinessReason", method = RequestMethod.POST)
    public Response queryBusinessReason(@RequestBody Integer recordType){
        try {
            List<BusinessReasonDTO> reasonList = businessReasonService.queryBusinessReason(recordType);
            return ResponseMsg.SUCCESS.buildMsg(reasonList);
        } catch (RomeException e) {
            log.error(e.getMessage(), e);
            return ResponseMsg.FAIL.buildMsg(e.getCode(),e.getMessage());
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return ResponseMsg.FAIL.buildMsg("500000","系统异常");
        }
    }
}
