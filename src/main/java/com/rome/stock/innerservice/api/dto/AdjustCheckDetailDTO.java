package com.rome.stock.innerservice.api.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotNull;
import java.math.BigDecimal;

/**
 * 库存对比调整-sku详情
 */
@Data
@EqualsAndHashCode
public class AdjustCheckDetailDTO {
    /**
     * 商品skuCode
     */
    @ApiModelProperty(value = "商品skuCode", required = true)
    @NotNull(message = "商品skuCode不能为空")
    private String skuCode;

    /**
     * 商品skuId
     */
    @ApiModelProperty(value = "商品skuId", required = true)
    private Long skuId;

    /**
     * 实际单位的数querySkuStockList量
     */
    @ApiModelProperty(value = "调整数量", required = true)
    @NotNull(message = "调整数量不能为空")
    private BigDecimal skuQty;

    /**
     * 实际单位code
     */
    @ApiModelProperty(value = "单位code", required = true)
    private String unitCode;

    /**
     * 库存对比id
     */
    @ApiModelProperty(value = "库存对比id", required = true)
    private Long checkFlowId;
}
