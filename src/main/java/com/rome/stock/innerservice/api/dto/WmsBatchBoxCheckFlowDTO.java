/**
 * Filename WmsBatchCheckFlowDTO.java
 * Company 上海来伊份科技有限公司。
 * <AUTHOR>
 * @version 
 */
package com.rome.stock.innerservice.api.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * wms批次库存核对流水记录表
 * <AUTHOR>
 * @since 2021-7-15 10:23:37
 */
@Data
@NoArgsConstructor
@EqualsAndHashCode
public class WmsBatchBoxCheckFlowDTO extends Pagination {

    @ApiModelProperty("唯一主键")
    private Long id;

    @ApiModelProperty("实仓仓库ID")
    private Long realWarehouseId;

    @ApiModelProperty("工厂编号")
    private String factoryCode;

    @ApiModelProperty("实仓仓库编号")
    private String realWarehouseCode;

    @ApiModelProperty("实仓仓库名称")
    private String realWarehouseName;

    @ApiModelProperty("商品sku编码")
    private Long skuId;

    @ApiModelProperty("商品sku编码集合")
    private List<Long> skuIds;

    @ApiModelProperty("商品编码")
    private String skuCode;
    
    @ApiModelProperty("批次编码-批次号")
    private String batchCode;

    @ApiModelProperty("真实原箱库存-实仓")
    private BigDecimal boxRealQty;

    @ApiModelProperty("wms真实原箱库存")
    private BigDecimal wmsBoxRealQty;

    @ApiModelProperty("真实非原箱库存-实仓")
    private BigDecimal mixRealQty;

    @ApiModelProperty("wms真实非原箱库存")
    private BigDecimal wmsMixRealQty;

    @ApiModelProperty("系统来源")
    private String sourceSystem;

    @ApiModelProperty("创建时间")
    private Date createTime;
    
    @ApiModelProperty("更新时间")
    private Date updateTime;
    
    @ApiModelProperty("商品名称")
    private String skuName;
    
    @ApiModelProperty(value = "主单位(商品单位)")
    private String unit;
    
    @ApiModelProperty("中台与wms差异，查询用 0无 1有")
    private Integer type;
    
    @ApiModelProperty("库存差异量，中台-WMS真实的值")
    private BigDecimal diffRealQty;


    /**
     * 开始时间
     */
    private Date startTime;

    /**
     * 结束时间
     */
    private Date endTime;

    /**
     * 仓库id集合(条件查询用)
     */
    private List<Long> realWarehouseIds;

	/**
	 * @return the diffRealQty
	 */
	public BigDecimal getDiffRealQty() {
		if(this.boxRealQty != null && this.wmsBoxRealQty != null && this.mixRealQty != null && this.wmsMixRealQty != null){
			return this.boxRealQty.add(this.mixRealQty).subtract(this.wmsBoxRealQty.add(this.wmsMixRealQty));
        }
		return null;
	}

	/**
	 * @param diffRealQty the diffRealQty to set
	 */
	public void setDiffRealQty(BigDecimal diffRealQty) {
		this.diffRealQty = diffRealQty;
	}
    
}
