package com.rome.stock.innerservice.api.controller;

import com.alibaba.fastjson.JSON;
import com.github.pagehelper.PageInfo;
import com.rome.arch.core.clientobject.Response;
import com.rome.arch.core.exception.RomeException;
import com.rome.arch.util.controller.RomeController;
import com.rome.stock.innerservice.api.dto.statistics.StatisticsSkuByDatePageDTO;
import com.rome.stock.innerservice.api.dto.statistics.StatisticsSkuByDateQueryConditionDTO;
import com.rome.stock.innerservice.common.ResCode;
import com.rome.stock.innerservice.constant.ResponseMsg;
import com.rome.stock.innerservice.domain.service.StatisticsSkuByDateService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiResponse;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @date 2022/2/24   15:42
 * @since 1.0.0
 */
@Api(tags = {"商品按时间统计"})
@Slf4j
@RomeController
@RequestMapping(value = "/stock/v1/sku/statistics")
public class StatisticsSkuByDateController {

    @Resource
    private StatisticsSkuByDateService statisticsSkuByDateService;

    /**
     * 分页查询商品统计数据
     * @param queryCondition 查询条件
     */
    @SuppressWarnings("unchecked")
    @ApiOperation(value = "分页查询", nickname = "queryPage", produces = MediaType.APPLICATION_JSON_VALUE)
    @ApiResponse(code = 200, message = "success", response = String.class)
    @GetMapping("/page")
    public Response<PageInfo<StatisticsSkuByDatePageDTO>> queryPage(StatisticsSkuByDateQueryConditionDTO queryCondition) {
        try {
            return ResponseMsg.SUCCESS.buildMsg(statisticsSkuByDateService.queryPage(queryCondition));
        } catch (RomeException e) {
            log.error(e.getMessage(), e);
            return ResponseMsg.FAIL.buildMsg(e.getCode(), e.getMessage());
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return ResponseMsg.EXCEPTION.buildMsg();
        }
    }

    @ApiOperation(value = "【定时任务】生成商品库存统计数据，生成当前时间的前一天的商品库存统计数据", nickname = "createStockStatisticsByJob", produces = MediaType.APPLICATION_JSON_VALUE)
    @ApiResponse(code = 200, message = "success")
    @RequestMapping(value = "/createSkuStockStatisticsByJob", method = RequestMethod.POST)
    public Response createSkuStockStatisticsByJob() {
        try {
            log.info("【定时任务】 生成商品库存统计报表数据");
            statisticsSkuByDateService.createSkuStockStatisticsByJob();
            return ResponseMsg.SUCCESS.buildMsg(null);
        } catch (RomeException e) {
            log.error(e.getMessage(), e);
            return ResponseMsg.FAIL.buildMsg(e.getCode(),e.getMessage());
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return ResponseMsg.EXCEPTION.buildMsg(ResCode.STOCK_ERROR_1003_DESC,ResCode.STOCK_ERROR_1003_DESC + e.getMessage());
        }
    }

    @ApiOperation(value = "生成商品库存统计数据，生成当前时间的前一天的商品库存统计数据", nickname = "createStockStatistics", produces = MediaType.APPLICATION_JSON_VALUE)
    @ApiResponse(code = 200, message = "success")
    @RequestMapping(value = "/createSkuStockStatistics", method = RequestMethod.POST)
    public Response createSkuStockStatistics() {
        try {
            log.info("生成商品库存统计报表数据");
            statisticsSkuByDateService.createSkuStockStatistics();
            return ResponseMsg.SUCCESS.buildMsg(null);
        } catch (RomeException e) {
            log.error(e.getMessage(), e);
            return ResponseMsg.FAIL.buildMsg(e.getCode(),e.getMessage());
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return ResponseMsg.EXCEPTION.buildMsg(ResCode.STOCK_ERROR_1003_DESC,ResCode.STOCK_ERROR_1003_DESC + e.getMessage());
        }
    }

}
