package com.rome.stock.innerservice.api.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;

/**
 * 分配关系管理类
 */
@Data
@EqualsAndHashCode
public class DistributiveRelationship extends Pagination {
    /**
     * 唯一主键
     */
    @ApiModelProperty(value = "主键")
    private Long id;
    /**
     * 分配关系类型
     */
    @ApiModelProperty(value = "分配关系类型")
    private Integer relationType;
    /**
     * 实仓仓库Id
     */
    @ApiModelProperty(value = "实仓仓库Id")
    private Long realWarehouseId;
    /**
     * 实仓仓库编码
     */
    @ApiModelProperty(value = "实仓仓库编码")
    private String realWarehouseCode;
    /**
     * 实仓仓库名称
     */
    @ApiModelProperty(value = "实仓仓库名称")
    private String realWarehouseName;
    /**
     * 分配比例
     */
    @ApiModelProperty(value = "分配比例")
    private Integer syncRate;
    /**
     * 渠道策略组比例
     */
    @ApiModelProperty(value = "渠道策略组比例")
    private Integer showRate;
    /**
     * 虚拟仓库Id
     */
    @ApiModelProperty(value = "虚仓仓库Id")
    private String virtualWarehouseId;
    /**
     * 虚仓仓库编码
     */
    @ApiModelProperty(value = "虚仓仓库编码")
    private String virtualWarehouseCode;
    /**
     * 虚拟仓库名称
     */
    @ApiModelProperty(value = "虚仓仓库名称")
    private String virtualWarehouseName;
    /**
     * 策略组Id
     */
    @ApiModelProperty(value = "策略组Id")
    private Long virtualWarehouseGroupId;
    /**
     * 策略组编码
     */
    @ApiModelProperty(value = "策略组编码")
    private String virtualWarehouseGroupCode;
    /**
     * 策略组名称
     */
    @ApiModelProperty(value = "策略组名称")
    private String virtualWarehouseGroupName;
    /**
     * 渠道Id
     */
    @ApiModelProperty(value = "渠道Id")
    private Long channelId;
    /**
     * 渠道编码
     */
    @ApiModelProperty(value = "渠道编码")
    private String channelCode;
    /**
     * 渠道名称
     */
    @ApiModelProperty(value = "渠道名称")
    private String channelName;

    /**
     * skuCode
     */
    @ApiModelProperty(value = "skuCode")
    private  String  skuCode;

    /**
     * 实仓真实库存
     */
    @ApiModelProperty(value = "实仓真实库存")
    private BigDecimal realRealQty;


    /**
     * 实仓锁定库存
     */
    @ApiModelProperty(value = "实仓锁定库存")
    private BigDecimal realLockQty;

    /**
     * 实仓可用库存
     */
    @ApiModelProperty(value = "实仓可用库存")
    private BigDecimal realAvailableQty;

    /**
     * 虚仓真实库存
     */
    @ApiModelProperty(value = "虚仓真实库存")
    private BigDecimal virtualRealQty;


    /**
     * 虚仓锁定库存
     */
    @ApiModelProperty(value = "虚仓锁定库存")
    private BigDecimal virtualLockQty;

    /**
     * 虚仓可用库存
     */
    @ApiModelProperty(value = "虚仓可用库存")
    private BigDecimal virtualAvailableQty;


    /**
     * 策略组真实库存
     */
    @ApiModelProperty(value = "策略组真实库存")
    private BigDecimal groupRealQty;


    /**
     * 策略组锁定库存
     */
    @ApiModelProperty(value = "策略组锁定库存")
    private BigDecimal groupLockQty;

    /**
     * 策略组可用库存
     */
    @ApiModelProperty(value = "策略组可用库存")
    private BigDecimal groupAvailableQty;



    /**
     * 渠道真实库存
     */
    @ApiModelProperty(value = "渠道真实库存")
    private BigDecimal  channelRealQty;


    /**
     * 渠道锁定库存
     */
    @ApiModelProperty(value = "渠道锁定库存")
    private BigDecimal channelLockQty;

    /**
     * 渠道可用库存
     */
    @ApiModelProperty(value = "渠道可用库存")
    private BigDecimal channelAvailableQty;
}
