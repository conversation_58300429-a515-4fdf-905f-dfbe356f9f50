package com.rome.stock.innerservice.api.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.math.BigDecimal;
import java.util.Date;

@Data
public class RwBatchDTO {

    /**
     * 主键
     * */

    private Long id;

    /**
     * skuID
     * */
    @ApiModelProperty(value = "skuId")
    private Long skuId;

    @ApiModelProperty(value = "skuCode")
    private String skuCode;

    /**
     * 仓库ID
     * */
    @ApiModelProperty(value = "仓库ID")
    private Long realWarehouseId;

    /**
     * 出库单编号
     * */
    @ApiModelProperty(value = "出库单编号")
    private String recordCode;

    /**
     * 批次编号
     * */
    @ApiModelProperty(value = "批次编号")
    private String batchCode;

    /**
     * 业务类型：1:出库单 2:入库单
     */
    @ApiModelProperty(value = "业务类型")
    private Integer businessType;

    /**
     * 生产日期
     * */
    @ApiModelProperty(value = "生产日期(YYYY-MM-DD)")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    private Date productDate;

    /**
     * 过期日期
     * */
    @ApiModelProperty(value = "过期日期")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date expireDate;

    /**
     * 生产批次
     * */
    @ApiModelProperty(value = "生产批次")
    private String produceCode;

    /**
     * 库存类型
     * */
    @ApiModelProperty(value = "库存类型")
    private Integer inventoryType;

    /**
     * 商品数量
     */
    @ApiModelProperty(value = "商品数量")
    private BigDecimal actualQty;
    /**
     * wms收货单编号
     */
    @ApiModelProperty(value = "wms收货单编号")
    private String wmsRecordCode;

    /**
     * 质检状态 -1:无需质检 0:待质检 1:质检合格 2:质检不合格
     */
    @ApiModelProperty(value = "质检状态")
    private Integer qualityStatus;

    /**
     * 质检批号
     */
    @ApiModelProperty(value = "质检批号")
    private String qualityCode;

    /**
     * 商品数量
     */
    @ApiModelProperty(value = "实际单位数量")
    private BigDecimal skuQty;

    /**
     *单位编码
     */
    @ApiModelProperty(value = "单位编码")
    private String unitCode;

    /**
     * 中台行号
     */
    private String ztLineNo;

    /**
     * 行号
     */
    private String lineNo;

    /**
     * 发货单行号
     */
    private String deliveryLineNo;

    /**
     * 过账状态
     */
    private Integer syncTransferStatus;

    /**
     * 回调次数
     */
    private Integer callbackNum;

    //创建时间
    private Date createTime;

    /**
     * 是否已操作 0--未操作 1--已操作
     */
    private Integer isOperate;


    /**
     * 推送质检中心状态 0--无需推送 1--待推送 2--推送成功
     */
    private Integer syncPurchaseStatus;

    /**
     * 质检回调时间
     */
    private Date qualityTime;

    private String remark;
}
