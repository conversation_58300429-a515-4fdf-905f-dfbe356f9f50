/**
 * Filename EntrySaleStockController.java
 * Company 上海来伊份科技有限公司。
 * <AUTHOR>
 * @version 
 */
package com.rome.stock.innerservice.api.controller;

import com.alibaba.fastjson.JSONObject;
import com.github.pagehelper.PageInfo;
import com.rome.arch.core.clientobject.Response;
import com.rome.arch.core.exception.RomeException;
import com.rome.arch.util.controller.RomeController;
import com.rome.stock.common.utils.DateUtil;
import com.rome.stock.innerservice.api.dto.EntrySaleStockDTO;
import com.rome.stock.innerservice.common.ResCode;
import com.rome.stock.innerservice.constant.ResponseMsg;
import com.rome.stock.innerservice.domain.service.EntrySaleStockService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import io.swagger.annotations.ApiResponse;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.ArrayList;
import java.util.Calendar;
import java.util.Date;
import java.util.List;

/**
 * 进销存
 * <AUTHOR>
 * @since 2021-8-14 18:02:36
 */
@Slf4j
@RomeController
@RequestMapping("/stock/v1/entry_sale_stock")
@Api(tags={"进销存"})
public class EntrySaleStockController {

	@Autowired
	private EntrySaleStockService entrySaleStockService;
	
	@ApiOperation(value = "生成进销存数据，生成当前时间的前一天的进销存数据，定时任务调用", nickname = "createEntrySaleStockTask", produces = MediaType.APPLICATION_JSON_VALUE)
    @ApiResponse(code = 200, message = "success")
    @RequestMapping(value = "/createEntrySaleStockJob", method = RequestMethod.POST)
	public Response createEntrySaleStockJob(@RequestBody JSONObject requestVO) {
		try {
			entrySaleStockService.createEntrySaleStockJob();
            return ResponseMsg.SUCCESS.buildMsg(null);
        } catch (RomeException e) {
            log.error(e.getMessage(), e);
            return ResponseMsg.FAIL.buildMsg(e.getCode(),e.getMessage());
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return ResponseMsg.EXCEPTION.buildMsg(ResCode.STOCK_ERROR_1003_DESC,ResCode.STOCK_ERROR_1003_DESC + e.getMessage());
        }
	}
	
	@ApiOperation(value = "生成进销存数据，生成当前时间的前一天的进销存数据", nickname = "createEntrySaleStockByAll", produces = MediaType.APPLICATION_JSON_VALUE)
    @ApiResponse(code = 200, message = "success")
    @RequestMapping(value = "/createEntrySaleStockByAll", method = RequestMethod.POST)
	public Response createEntrySaleStockByAll() {
		try {
			entrySaleStockService.createEntrySaleStockByAll();
            return ResponseMsg.SUCCESS.buildMsg(null);
        } catch (RomeException e) {
            log.error(e.getMessage(), e);
            return ResponseMsg.FAIL.buildMsg(e.getCode(),e.getMessage());
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return ResponseMsg.EXCEPTION.buildMsg(ResCode.STOCK_ERROR_1003_DESC,ResCode.STOCK_ERROR_1003_DESC + e.getMessage());
        }
	}
	
	@ApiOperation(value = "根据仓库Id重新生成进销存数据表,一般是手动,参数只需要传realWarehouseId", nickname = "createEntrySaleStockSync", produces = MediaType.APPLICATION_JSON_VALUE)
    @ApiResponse(code = 200, message = "success")
    @RequestMapping(value = "/createEntrySaleStockSync", method = RequestMethod.POST)
    public Response createEntrySaleStockSync(@RequestBody EntrySaleStockDTO paramDto) {
        try {
        	entrySaleStockService.createEntrySaleStockSync(paramDto);
            return ResponseMsg.SUCCESS.buildMsg();
        } catch (RomeException e) {
            log.error(e.getMessage(), e);
            return ResponseMsg.FAIL.buildMsg(e.getCode(),e.getMessage());
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return ResponseMsg.EXCEPTION.buildMsg(e.getMessage());
        }
    }
	
	@ApiOperation(value = "根据仓库Id列表和时间范围生成进销存历史，必填参数realWarehouseIds、startTime、endTime",
			notes = "根据仓库Id重新生成历史进销存数据表,一般是手动,根据仓和指定时间，重新生成历史进销存数据，若date+1天的没有进销存数据无法生成，"
			+ "以date+1天的进销存数据的期初库存作为期末库存", nickname = "createEntrySaleStockSyncHistory", produces = MediaType.APPLICATION_JSON_VALUE)
    @ApiResponse(code = 200, message = "success")
    @RequestMapping(value = "/createEntrySaleStockSyncHistory", method = RequestMethod.POST)
    public Response createEntrySaleStockSyncHistory(@RequestBody EntrySaleStockDTO paramDto) {
        try {
        	if(paramDto.getRealWarehouseIds() == null || paramDto.getRealWarehouseIds().size() == 0) {
        		return ResponseMsg.FAIL.buildMsg(ResCode.STOCK_ERROR_1003, "仓库Id列表不能为空");
        	}
        	if(paramDto.getStartTime() == null || paramDto.getEndTime() == null) {
        		return ResponseMsg.FAIL.buildMsg(ResCode.STOCK_ERROR_1003, "时间范围不能为空");
        	}
        	if(paramDto.getStartTime().getTime() > paramDto.getEndTime().getTime()) {
        		return ResponseMsg.FAIL.buildMsg(ResCode.STOCK_ERROR_1003, "开始时间不能大于结束时间");
        	}
        	
        	// 当前时间
        	Date currentTime = DateUtil.getDayBegin(paramDto.getEndTime());
        	long dayNum = DateUtil.diff(paramDto.getStartTime(), currentTime, 1000*3600*24);
        	if(dayNum >= 30) {
        		throw new RomeException(ResCode.STOCK_ERROR_1002, ResCode.STOCK_ERROR_1002_DESC + "，时间范围超过30天");
        	}
        	// 开始执行
        	String msg;
        	List<String> result = new ArrayList<>((int)((dayNum + 1) * paramDto.getRealWarehouseIds().size()));
        	String dateStr;
        	paramDto.setStartTime(DateUtil.getDayBegin(paramDto.getStartTime()));
        	while(paramDto.getStartTime().getTime() <= currentTime.getTime()) {
        		dateStr = DateUtil.format(currentTime, DateUtil.NORM_DATE_PATTERN);
        		for(Long realWarehouseId : paramDto.getRealWarehouseIds()) {
        			msg = "成功";
        			try {
        				entrySaleStockService.createEntrySaleStockSyncHistory(realWarehouseId, currentTime);
					} catch (Exception e) {
						msg = e.getMessage();
                        log.error(e.getMessage(), e);
					} finally {
						result.add(new StringBuffer()
								       .append("realWarehouseId=").append(realWarehouseId)
								       .append(",date=").append(dateStr)
								       .append(",msg=").append(msg)
								       .toString());
					}
        		}
        		// 减1天
        		currentTime = DateUtil.offsiteDate(currentTime, Calendar.DAY_OF_YEAR, -1);
        	}
            return ResponseMsg.SUCCESS.buildMsg(result);
        } catch (RomeException e) {
            log.error(e.getMessage(), e);
            return ResponseMsg.FAIL.buildMsg(e.getCode(),e.getMessage());
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return ResponseMsg.EXCEPTION.buildMsg(ResCode.STOCK_ERROR_1003, ResCode.STOCK_ERROR_1003_DESC + e.getMessage());
        }
    }
	
	@ApiOperation(value = "根据条件查询进销存表", nickname = "queryCondition", produces = MediaType.APPLICATION_JSON_VALUE)
    @ApiResponse(code = 200, message = "success")
    @RequestMapping(value = "/queryCondition", method = RequestMethod.POST)
    public Response<PageInfo<EntrySaleStockDTO>> queryCondition(@RequestBody EntrySaleStockDTO paramDto) {
        try {
            PageInfo<EntrySaleStockDTO> dtoList = entrySaleStockService.queryCondition(paramDto);
            return ResponseMsg.SUCCESS.buildMsg(dtoList);
        } catch (RomeException e) {
            log.error(e.getMessage(), e);
            return ResponseMsg.FAIL.buildMsg(e.getCode(),e.getMessage());
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return ResponseMsg.EXCEPTION.buildMsg(ResCode.STOCK_ERROR_1003, ResCode.STOCK_ERROR_1003_DESC + e.getMessage());
        }
    }
	
	/**
	 * 进销存库存调整处理，根据调整单明细定时任务
	 * @param runStopTime 最大运行时间，单位秒数
	 * @return
	 */
    @ApiOperation(value = "进销存库存调整处理，根据调整单明细定时任务 Job", nickname = "adjustEntrySaleStockTask", produces = MediaType.APPLICATION_JSON_VALUE)
    @ApiResponse(code = 200, message = "success", response = String.class)
    @RequestMapping(value = "/adjustEntrySaleStockTask", method = RequestMethod.POST)
    public Response adjustEntrySaleStockTask(@ApiParam(name = "runStopTime", value = "最大运行时间，单位秒数") @RequestParam("runStopTime") Long runStopTime) {
        try {
        	// 为null或者等于0秒时，为1分钟
        	if(runStopTime == null || runStopTime < 1) {
        		runStopTime = System.currentTimeMillis() + (60 * 1000);
        	} else {
        		runStopTime = System.currentTimeMillis() + (runStopTime * 1000);
        	}
        	int num = entrySaleStockService.adjustEntrySaleStockTask(runStopTime);
            return Response.builderSuccess("调整单明细处理总条数：" + num);
        } catch (RomeException e) {
            log.error(e.getMessage(), e);
            return ResponseMsg.FAIL.buildMsg(e.getCode(),e.getMessage());
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return ResponseMsg.EXCEPTION.buildMsg(ResCode.STOCK_ERROR_1003_DESC, e.getMessage());
        }
    }
    
    /**
	 * 根据调整单Id列表手动重试进销存库存调整
	 * @param idList 调整单Id列表
	 * @return
	 */
    @ApiOperation(value = "根据调整单Id列表手动重试进销存库存调整", nickname = "retryAdjustEntrySaleStockByIdList", produces = MediaType.APPLICATION_JSON_VALUE)
    @ApiResponse(code = 200, message = "success", response = String.class)
    @RequestMapping(value = "/retryAdjustEntrySaleStockByIdList", method = RequestMethod.POST)
    public Response retryAdjustEntrySaleStockByIdList(@ApiParam(name = "idList", value = "调整单Id列表") @RequestBody List<Long> idList) {
        try {
        	JSONObject jsonObject = entrySaleStockService.retryAdjustEntrySaleStockByIdList(idList);
            return Response.builderSuccess(jsonObject == null ? "" : jsonObject.toJSONString());
        } catch (RomeException e) {
            log.error(e.getMessage(), e);
            return ResponseMsg.FAIL.buildMsg(e.getCode(),e.getMessage());
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return ResponseMsg.EXCEPTION.buildMsg(ResCode.STOCK_ERROR_1003_DESC, e.getMessage());
        }
    }
    
    /**
	 * 根据进销存id列表，手动进销存数据同步到ES中
	 * @param idList 进销存Id列表
	 * @return
	 */
    @ApiOperation(value = "根据进销存id列表，手动进销存数据同步到ES中", nickname = "entrySaleStockSyncToEsByIdList", produces = MediaType.APPLICATION_JSON_VALUE)
    @ApiResponse(code = 200, message = "success", response = String.class)
    @RequestMapping(value = "/entrySaleStockSyncToEsByIdList", method = RequestMethod.POST)
    public Response entrySaleStockSyncToEsByIdList(@ApiParam(name = "idList", value = "进销存Id列表") @RequestBody List<Long> idList) {
        try {
        	entrySaleStockService.entrySaleStockSyncToEsByIdList(idList);
        	return ResponseMsg.SUCCESS.buildMsg("成功");
        } catch (RomeException e) {
            log.error(e.getMessage(), e);
            return ResponseMsg.FAIL.buildMsg(e.getCode(),e.getMessage());
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return ResponseMsg.EXCEPTION.buildMsg(ResCode.STOCK_ERROR_1003_DESC, e.getMessage());
        }
    }
	
}
