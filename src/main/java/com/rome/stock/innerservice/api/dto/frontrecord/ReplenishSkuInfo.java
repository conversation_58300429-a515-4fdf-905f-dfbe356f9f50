package com.rome.stock.innerservice.api.dto.frontrecord;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * @Description: ReplenishSkuInfo
 * <p>
 * @Author: chuwenchao  2019/9/17
 */
@Data
@EqualsAndHashCode
public class ReplenishSkuInfo {

    /**
     * 渠道id
     */
    @ApiModelProperty(value = "渠道id", name = "channelCode", required=true)
    private String channelCode;

    /**
     * 渠道组id
     */
    @ApiModelProperty(value = "渠道组id", name = "parentChannelCode", required=true)
    private String parentChannelCode;

    /**
     * 商品skuId
     */
    @ApiModelProperty(value = "商品skuId", name = "skuId")
    private Long skuId;

    /**
     * 商品sku编码
     */
    @ApiModelProperty(value = "商品sku编码", name = "skuCode", required=true)
    private String skuCode;

    /**
     * 单位
     */
    @ApiModelProperty(value = "单位code", name = "unit", required = true)
    private String unitCode;
}
