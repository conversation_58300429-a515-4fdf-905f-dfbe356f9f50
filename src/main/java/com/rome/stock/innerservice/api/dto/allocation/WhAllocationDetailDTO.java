package com.rome.stock.innerservice.api.dto.allocation;

import com.rome.stock.innerservice.api.dto.BatchStockDTO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.hibernate.validator.constraints.Range;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.math.BigDecimal;

/**
 * 门店调拨单详情
 *
 * <AUTHOR> 2019/4/21 17:06
 */
@Data
@EqualsAndHashCode
public class WhAllocationDetailDTO {

    @ApiModelProperty(value = "sku编号")
    @NotNull(message="sku编码为空")
    private Long skuId;

    /** sku编号 */
    @NotEmpty(message="skuCode不能为空")
    private String skuCode;
    /**
	 * sap行号(过账用)
	 * */
//    @NotBlank(message="lineNo号不能为空")
	private String lineNo;

    /**
     * 供应链行号，收发货匹配用
     * */
    @NotBlank(message="deliveryLineNo号不能为空")
    private String deliveryLineNo;

    /**
     * 基础数量
     */
    @NotNull(message="调拨基础数量不能为空")
    @Range(min=0, message = "调拨基础数量不能为负数")
    private BigDecimal basicSkuQty;

    /**
     * 集装箱需求 01整箱 02拆零
     */
    private String container;
    /**
     * 基础单位名称
     */
    @NotBlank(message="基本单位不能为空")
    private String basicUnit;
    /**
     * 基础单位code
     */
    @NotBlank(message="基本单位Code不能为空")
    private String basicUnitCode;

}
