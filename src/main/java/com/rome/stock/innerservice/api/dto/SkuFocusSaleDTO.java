package com.rome.stock.innerservice.api.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @Description 导入指定寻源商品的模板
 * @date 2020/6/29 17:36
 */
@Data
public class SkuFocusSaleDTO {

    @ApiModelProperty(value = "仓库编码", required = true)
    private String realWarehouseCode;

    @ApiModelProperty(value = "商品编码", required = true)
    private String skuCode;

    @ApiModelProperty(value = "商品编码")
    private Long creator;

    @ApiModelProperty(value = "实仓id")
    private Long realWarehouseId;

    @ApiModelProperty(value = "skuId")
    private Long skuId;

    @ApiModelProperty(value = "更新人")
    private Long modifier;

    @ApiModelProperty(value = "创建人工号")
    private Long creatorNum;

    @ApiModelProperty(value = "更新人工号")
    private Long modifyEmpNum;
}    
   