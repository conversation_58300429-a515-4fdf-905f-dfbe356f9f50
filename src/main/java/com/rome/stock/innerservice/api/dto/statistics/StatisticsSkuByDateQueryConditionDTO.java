package com.rome.stock.innerservice.api.dto.statistics;

import com.rome.stock.innerservice.api.dto.Pagination;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;

/**
 * 商品根据时间 统计  查询条件
 *
 * <AUTHOR>
 * @date 2022/2/24   14:24
 * @since 1.0.0
 */
@Data
public class StatisticsSkuByDateQueryConditionDTO extends Pagination {

    /**
     * 统计开始时间
     */
    @ApiModelProperty(value = "查询开始时间", example = "2022-02-01 14:12:12")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date statisticsStartDate;

    /**
     * 统计结束时间
     */
    @ApiModelProperty(value = "查询结束时间", example = "2022-02-01 15:12:12")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date statisticsEndDate;

    /**
     * 精准查询  供应商编码
     */
    @ApiModelProperty(value = "供应商编码")
    private String supplierCode;

    /**
     * 精准查询  供应商名称
     */
    @ApiModelProperty(value = "合作伙伴名称")
    private String supplierName;

    /**
     * 关键字查询  商品编码/商品名称
     */
    @ApiModelProperty(value = "关键字搜索")
    private String keyword;

    /**
     * 是否按照统计时间倒序排列
     */
    @ApiModelProperty(hidden = true)
    private Boolean orderByStatisticsDateDesc;
}
