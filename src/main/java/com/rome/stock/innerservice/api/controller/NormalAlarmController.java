package com.rome.stock.innerservice.api.controller;

import com.github.pagehelper.PageInfo;
import com.google.common.collect.Lists;
import com.rome.arch.core.clientobject.Response;
import com.rome.arch.core.exception.RomeException;
import com.rome.arch.util.controller.RomeController;
import com.rome.stock.innerservice.api.dto.StockAlarmConfigDTO;
import com.rome.stock.innerservice.api.dto.StockAlarmConfigDetailDTO;
import com.rome.stock.innerservice.api.dto.alarm.BatchSaleAlarmDTO;
import com.rome.stock.innerservice.api.dto.alarm.BatchSaleAlarmDetailDTO;
import com.rome.stock.innerservice.common.ResCode;
import com.rome.stock.innerservice.constant.ResponseMsg;
import com.rome.stock.innerservice.domain.service.BatchSaleAlarmService;
import com.rome.stock.innerservice.domain.service.NormalAlarmService;
import com.rome.stock.innerservice.domain.service.StockAlarmHeadService;
import com.rome.stock.innerservice.infrastructure.dataobject.alarm.StockAlarmHeadDO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiResponse;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.exception.ExceptionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;

@Slf4j
@RomeController
@RequestMapping("/stock/v1/normalAlarm")
@Api(tags={"普通预警Controller"})
public class NormalAlarmController {

    @Autowired
	private NormalAlarmService normalAlarmService;



    @ApiOperation(value = "门店闭店库存未清0，飞书预警", nickname = "shopCloseAlarm", produces = MediaType.APPLICATION_JSON_VALUE)
    @ApiResponse(code = 200, message = "success")
    @RequestMapping(value = "/shopCloseAlarm", method = RequestMethod.GET)
    Response shopCloseAlarm() {
        try {
            normalAlarmService.shopCloseAlarm();
            return ResponseMsg.SUCCESS.buildMsg("");
        } catch (RomeException e) {
            log.error(e.getMessage(), e);
            return ResponseMsg.FAIL.buildMsg(e.getCode(),e.getMessage());
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return ResponseMsg.EXCEPTION.buildMsg(ResCode.STOCK_ERROR_1003, ResCode.STOCK_ERROR_1003_DESC + e.getMessage());
        }
    }
    
}
