package com.rome.stock.innerservice.api.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.util.Date;

/**
 * @Description
 * <AUTHOR>
 * @Date 2019/5/18 16:54
 * @Version 1.0
 */
@Data
@EqualsAndHashCode
public class SkuRealVirtualStockSyncRelationDTO extends Pagination implements Serializable {

    @ApiModelProperty(value = "唯一主键")
    private Long id;

    @ApiModelProperty(value = "sku编号")
    private Long skuId;

    @ApiModelProperty(value = "商品sku编号")
    private String skuCode;

    @ApiModelProperty(value = "商品sku名称")
    private String skuName;

    @ApiModelProperty(value = "工厂code")
    private String factoryCode;

    @ApiModelProperty(value = "实仓id")
    private Long realWarehouseId;

    @ApiModelProperty(value = "虚仓id")
    private Long virtualWarehouseId;

    @ApiModelProperty(value = "分配比例")
    private Integer syncRate;

    @ApiModelProperty(value = "实仓编号")
    private String realWarehouseCode;

    @ApiModelProperty(value = "虚仓编号")
    private String virtualWarehouseCode;

    @ApiModelProperty(value = "创建时间")
    private Date createTime;

    @ApiModelProperty(value = "创建人")
    private Long creator;

    @ApiModelProperty(value = "更新人")
    private Long modifier;
}
