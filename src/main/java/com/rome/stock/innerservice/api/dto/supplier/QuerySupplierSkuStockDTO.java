/**
 * Filename SupplierSkuStockDTO.java
 * Company 上海来伊份科技有限公司。
 * <AUTHOR>
 * @version 
 */
package com.rome.stock.innerservice.api.dto.supplier;

import java.util.List;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 供应商库存查询
 * <AUTHOR>
 * @since 2020-8-9 11:09:33
 */
@Data
@EqualsAndHashCode
public class QuerySupplierSkuStockDTO {

    @ApiModelProperty(value = "供应商代码")
    private String supplierCode;

    @ApiModelProperty(value = "商品编码列表")
    private List<String> skuCodeList;
    
    @ApiModelProperty(value = "商品名称")
    private String skuName;

    @ApiModelProperty(value = "第几页")
    private Integer page;
    
    @ApiModelProperty(value = "每页条数")
    private Integer pageSize;
}
