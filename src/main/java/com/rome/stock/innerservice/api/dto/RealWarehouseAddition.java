package com.rome.stock.innerservice.api.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.util.Date;

@Data
@EqualsAndHashCode
public class RealWarehouseAddition implements Serializable{

    @ApiModelProperty(value = "主键")
    private Long id;
    @ApiModelProperty(value = "实仓ID")
    private Long realWarehouseId;
    @ApiModelProperty(value = "保税仓名称")
    private String name;
    @ApiModelProperty(value = "保税仓编码")
    private String storeCode;
    @ApiModelProperty(value = "保税区编号")
    private String areaCode;
    @ApiModelProperty(value = "保税区名称")
    private String areaName;
    @ApiModelProperty(value = "海关编号")
    private String customsCode;
    @ApiModelProperty(value = "海关名称")
    private String customsName;
    @ApiModelProperty(value = "备注")
    private String remark;
    @ApiModelProperty(value = "创建时间")
    private Date createTime;
    @ApiModelProperty(value = "修改时间")
    private Date updateTime;
    @ApiModelProperty(value = "创建人")
    private Long creator;
    @ApiModelProperty(value = "修改人")
    private Long modifier;

}
