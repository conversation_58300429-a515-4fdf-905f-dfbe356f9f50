package com.rome.stock.innerservice.api.dto.verifyaccount;

import lombok.Data;

import javax.validation.Valid;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.util.List;

/**
 * 出入库DTO
 *
 * <AUTHOR>
 * @date 2019/06/05
 */
@Data
public class InboundOutboundDTO {
    /**
     * 店号
     */
    @NotBlank(message = "[ storeCode ]店号不能为空")
    private String storeCode;
    /**
     * 传输日期
     */
    private String currentDate;
    /**
     * 过账日期
     */
    private String postingDate;
    /**
     * 总数量
     */
    @NotNull(message = "[ aggregateAmount ]总数量不能为空")
    private BigDecimal aggregateAmount;
    /**
     * 入库1出库0
     */
    private Integer flag;
    /**
     * 明细
     */
    @NotEmpty(message = "[ details ]明细不能为空")
    @Valid
    private List<InboundOutboundDetailDTO> details;

}