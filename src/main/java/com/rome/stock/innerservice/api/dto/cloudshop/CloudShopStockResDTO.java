package com.rome.stock.innerservice.api.dto.cloudshop;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

/**
 * 类CloudShopStockDTO的实现描述：云店库存返回DTO
 *
 * <AUTHOR> 2021/9/15 11:15
 */
@Data
public class CloudShopStockResDTO {

    /**
     * 门店编码
     */
    @ApiModelProperty(value = "门店编码")
    private String shopCode;

    /**
     * 商品编码
     */
    @ApiModelProperty(value = "商品编码")
    private String skuCode;

    /**
     * 可用库存
     */
    @ApiModelProperty(value = "可用库存")
    private BigDecimal availableQty;

    /**
     * 单位
     */
    @ApiModelProperty(value = "单位编码")
    private String unitCode;

    /**
     * 仓库编码
     */
    @ApiModelProperty(value = "仓库编码")
    private String realWarehouseCode;

    /**
     * 仓库名称
     */
    @ApiModelProperty(value = "仓库名称")
    private  String realWarehouseName;

    /**
     * 供应商编码
     */
    @ApiModelProperty(value = "供应商编码")
    private String supplierCode;

    /**
     * 服务时效
     */
    private List<ServiceLabDTO> services;

}
