package com.rome.stock.innerservice.api.dto.replenish;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

@Data
@EqualsAndHashCode
public class ReplenishAssignAllotDTO {

    @ApiModelProperty(value = "实仓ID")
    @NotNull(message="实仓ID不能为空")
    private String realWarehouseCode;

    @ApiModelProperty(value = "虚仓ID")
    @NotNull(message="虚仓ID不能为空")
    private String virtualWarehouseCode;

    @ApiModelProperty(value = "外部单据编码")
    @NotBlank(message="外部单据编码不能为空")
    private String outRecordCode;
}
