package com.rome.stock.innerservice.api.controller;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import com.rome.arch.util.controller.RomeController;

import com.rome.stock.innerservice.domain.service.StockAlarmHeadService;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiResponse;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@RomeController
@RequestMapping("/stock/v1/stockAlarmHead")
@Api(tags={"服务接口"})
public class StockAlarmHeadController {

    @Autowired
	private StockAlarmHeadService stockAlarmHeadService;
	
}
