package com.rome.stock.innerservice.api.dto;


import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;

@ApiModel(value = "WarehouseInventoryStartPageDTO", description = "盘点单管理")
@Data
@EqualsAndHashCode
public class WarehouseInventoryStartPageDTO  extends Pagination {
    private static final long serialVersionUID = 6241269605258899670L;


    @ApiModelProperty(value = "盘点记录号")
    private Long id;

    @ApiModelProperty(value = "盘点记录号")
    private String recordCode;

    @ApiModelProperty(value = "仓库编码")
    private String realWarehouseCode;

    @ApiModelProperty(value = "时仓id")
    private Long realWarehouseId;

    @ApiModelProperty(value = "工厂编号")
    private String factoryCode;

    @ApiModelProperty(value = "工厂名称")
    private String factoryName;

    @ApiModelProperty(value = "仓库名称")
    private String realWarehouseName;

    @ApiModelProperty(value = "差异处理类型:1-以实盘数更新;2-以差异数更新")
    private Integer diffProccessType;

    @ApiModelProperty(value = "盘点原因")
    private String recordStatusReason;

    @ApiModelProperty(value = "序列号，绑定盘点单")
    private String serialNo;

    @ApiModelProperty(value = "状态")
    private Integer recordStatus;

    @ApiModelProperty(value = "盘点状态名称")
    private String recordStatusName;

    @ApiModelProperty(value = "创建人")
    private Long creator;

    @ApiModelProperty(value = "创建时间")
    private Date createTime;

    @ApiModelProperty(value = "更新人")
    private Long modifier;

    @ApiModelProperty(value = "更新时间")
    private Date updateTime;

}
