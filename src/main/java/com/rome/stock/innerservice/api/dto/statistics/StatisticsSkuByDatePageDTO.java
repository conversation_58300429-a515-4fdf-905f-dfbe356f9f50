package com.rome.stock.innerservice.api.dto.statistics;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 商品根据时间 统计  分页查询结果
 *
 * <AUTHOR>
 * @date 2022/2/24   14:24
 * @since 1.0.0
 */
@Data
public class StatisticsSkuByDatePageDTO {

    private Long id;

    @ApiModelProperty(value = "日期")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date statisticsDate;

    @ApiModelProperty(value = "商品编码")
    private String skuCode;

    @ApiModelProperty(value = "商品名称")
    private String skuName;

    private String supplierCode;

    @ApiModelProperty(value = "合作伙伴名称")
    private String supplierName;

    @ApiModelProperty(value = "单位")
    private String unitCode;

    private String unitName;

    @ApiModelProperty(value = "销售门店数")
    private BigDecimal saleShopStatisticsNumber;

    @ApiModelProperty(value = "仓库库存数")
    private BigDecimal warehouseStockNumber;

    @ApiModelProperty(value = "门店库存数")
    private BigDecimal shopStockNumber;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createTime;
}
