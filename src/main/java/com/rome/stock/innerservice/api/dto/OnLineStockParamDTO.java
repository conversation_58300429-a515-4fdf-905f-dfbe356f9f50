package com.rome.stock.innerservice.api.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

@Data
public class OnLineStockParamDTO implements Serializable {

    /**
     * SO单号
     */
    @ApiModelProperty(value = "SO单号", required = true)
    private String orderCode;

    @ApiModelProperty(value = "支付时间")
    private Date payTime;

}
