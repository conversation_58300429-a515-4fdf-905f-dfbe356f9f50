package com.rome.stock.innerservice.api.dto.frontrecord;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.Digits;
import javax.validation.constraints.NotEmpty;
import java.math.BigDecimal;

@Data
@EqualsAndHashCode
public class PurchaseDetailDTO {

    @ApiModelProperty(value = "数量")
    @Digits(integer = 9, fraction = 3,message="超过范围,小数3位有效位，整数9位有效位")
    private BigDecimal skuQty;

    @ApiModelProperty(value = "sku编号")
    private Long skuId;

    @ApiModelProperty(value = "单位")
    private String unit;

    @ApiModelProperty(value = "单位编码")
    @NotEmpty(message="单位code不能为空")
    private String unitCode;
}
