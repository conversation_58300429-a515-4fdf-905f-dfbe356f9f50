package com.rome.stock.innerservice.api.dto.cloudshop;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import java.util.Date;

@Data
@NoArgsConstructor
@EqualsAndHashCode
public class RwShopServiceLabDTO {

    private Long id;
    /**
     * 仓库id
     */
    @ApiModelProperty(value = "仓库id")
    private Long realWarehouseId;

    /**
     * 仓库CODE
     */
    @ApiModelProperty(value = "仓库CODE")
    private String realWarehouseCode;
    /**
     * 仓库名称
     */
    @ApiModelProperty(value = "仓库名称")
    private String realWarehouseName;
    /**
     * 门店编号
     */
    @ApiModelProperty(value = "门店编号")
    private String shopCode;
    /**
     * 门店名称
     */
    @ApiModelProperty(value = "门店名称")
    private String shopName;

    /**
     * 创建人
     */
    @ApiModelProperty(value = "创建人")
    private Long creator;
    /**
     * 创建时间
     */
    @ApiModelProperty(value = "创建时间")
    private Date createTime;

    /**
     * 创建人
     */
    @ApiModelProperty(value = "更新人")
    private Long modifier;
    /**
     * 创建时间
     */
    @ApiModelProperty(value = "更新时间")
    private Date updateTime;
}
