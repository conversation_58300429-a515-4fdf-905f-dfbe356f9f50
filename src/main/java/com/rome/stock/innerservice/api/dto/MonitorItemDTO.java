package com.rome.stock.innerservice.api.dto;

import cn.hutool.core.lang.Validator;
import com.rome.arch.core.exception.RomeException;
import com.rome.stock.innerservice.common.ResCode;
import com.rome.stock.innerservice.common.StringUtils;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.apache.commons.collections.CollectionUtils;

import java.math.BigDecimal;
import java.util.List;
import java.util.regex.Pattern;

/**
 */
@Data
@EqualsAndHashCode
public class MonitorItemDTO {

    private String type;

    private BigDecimal percentage;

    private BigDecimal count;

    private BigDecimal timeGap;

    private BigDecimal noticeTimeGap;

    private BigDecimal timeOut;

    private Boolean kibanaLog;

    private Boolean contentUnescape;

    /**
     * log日志预警数据配置
     */
    private LogConfigData logData;

    /**
     * 验证log日志预警数据配置，要保存时
     */
    void validateLogConfigDataBySave() {
        if (logData == null || CollectionUtils.isEmpty(logData.getDataList())){
            return;
        }
        for(int i = 0; i < logData.getDataList().size(); i++) {
            String msg = "";
            try {
                LogConfigDetailData dto = logData.getDataList().get(i);
                if (StringUtils.isBlank(dto.getTitle())){
                    dto.setTitle(null);
                }
                if (StringUtils.isBlank(dto.getLogType())){
                    dto.setLogType(null);
                }
                if (StringUtils.isBlank(dto.getPoolName())){
                    throw new RomeException(ResCode.STOCK_ERROR_1003,"日志预警配置，pool名不能为空");
                }
                if (StringUtils.isBlank(dto.getClassName())){
                    throw new RomeException(ResCode.STOCK_ERROR_1003,"日志预警配置，类名不能为空");
                }
                if (StringUtils.isBlank(dto.getMethodName())){
                    throw new RomeException(ResCode.STOCK_ERROR_1003,"日志预警配置，方法名不能为空");
                }
                if(StringUtils.isNotBlank(dto.getLogType()) && !"warn".equals(dto.getLogType()) && !"error".equals(dto.getLogType())) {
                    throw new RomeException(ResCode.STOCK_ERROR_1003,"日志预警配置，日志类型不支持");
                }
                // 过滤条件
                if(StringUtils.isNotBlank(dto.getFilterPattern())) {
                    msg = "满足正则条件";
                    Pattern.compile(dto.getFilterPattern(), Pattern.DOTALL);
                    msg = "";
                } else {
                    dto.setFilterPattern(null);
                }
                if(StringUtils.isBlank(dto.getContentPattern())) {
                    dto.setContentPattern(null);
                }
                if(StringUtils.isBlank(dto.getContentTemplate())) {
                    dto.setContentTemplate(null);
                }
                // 显示模板
                if(StringUtils.isNotBlank(dto.getContentPattern())) {
                    msg = "提取内容正则";
                    Pattern.compile(dto.getContentPattern(), Pattern.DOTALL);
                    msg = "";
                    if(StringUtils.isBlank(dto.getContentTemplate())) {
                        throw new RomeException(ResCode.STOCK_ERROR_1003,"日志预警配置，显示模板不为空时，内容模板不能为空");
                    }
                }
                // @人处理，若仅@人，只需要输入预警内容模板，提取内容正则不需要填写。当为all表示@所有人;多个以英文逗号分开,可以是工号或者公司邮箱
                else if(StringUtils.isNotBlank(dto.getContentTemplate())) {
                    //兼容中文逗号
                    String temp = dto.getContentTemplate().replaceAll("，",",");
                    String[] arr = temp.split(",");
                    StringBuffer result = new StringBuffer();
                    boolean flag = true;
                    for(String t : arr) {
                        if(StringUtils.isBlank(t)) {
                            continue;
                        }
                        if("all".equals(t)) {
                            result.append("<at id=\"all\"></at>");
                        }
                        // 邮箱
                        else if(Validator.isEmail(t)) {
                            result.append("<at email=\"").append(t).append("\"></at>");
                        }
                        // 工号 8位数字
                        else if(t.length()==8 && StringUtils.isNumeric(t)) {
                            result.append("<at id=\"").append(t).append("\"></at>");
                        } else {
                            flag=false;
                            break;
                        }
                    }
                    if(flag && result.length() > 0) {
                        result.append("$1");
                        dto.setContentTemplate(result.toString());
                        dto.setContentPattern("([\\w\\W]*)");
                    }
                }
            } catch (RomeException e) {
                throw new RomeException(e.getCode(), e.getMessage() + "，第" + (i+1)+ "行");
            } catch (Throwable e) {
                throw new RomeException(ResCode.STOCK_ERROR_1003,"日志预警配置，" + msg + "出错，第" + (i+1)+ "行，" + e.getMessage());
            }
        }
    }

    /**
     * 日志配置类
     */
    @Data
    final static class LogConfigData {

        private List<LogConfigDetailData> dataList;

    }

    /**
     * 日志配置类
     */
    @Data
    final static class LogConfigDetailData {

        /**
         * pool名字，就是应用的pool的名字，比如说，这个配置项就是应用于 stock-inner-service 上的
         */
        private String poolName;

        /**
         * 类全名，写log日志的类名，包括包名，比如，com.rome.test.Test
         */
        private String className;

        /**
         * 方法名，写log日志方法名，即在哪个方法里写日志
         */
        private String methodName;

        /**
         * 日志类型：这里可能值为error、warn，空时，表示所有。
         */
        private String logType;

        /**
         * 日志内容，过滤条件，正则条件：可以空不填，即类和方法下的所有的日志。
         */
        private String filterPattern;

        /**
         * 发送显示内容模板，比如：商品中心又错了，商品信息skuCode=$1,skuId=$2。不设置就按日志实际内容。
         */
        private String contentTemplate;

        /**
         * 发送显示内容模板，提取正则表达式，与 contentTemplate 对应的，一一对应（$1、$2），会替换掉 contentTemplate 里的内容
         */
        private String contentPattern;

        /**
         * 小标题：自定义小标题，不填显示为枚举的描述。即：返回值code不为0、异常、超时。
         */
        private String title;

    }
}
