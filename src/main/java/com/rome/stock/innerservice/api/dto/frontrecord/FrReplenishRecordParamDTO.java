package com.rome.stock.innerservice.api.dto.frontrecord;

import com.rome.stock.innerservice.api.dto.Pagination;
import lombok.Data;

@Data
public class FrReplenishRecordParamDTO extends Pagination {

    private String recordCode;
    private Integer inRealWarehouseId;
    private Integer outRealWarehouseId;
    private Integer recordType;
    private Integer recordStatus;
    private String goodsCode;
    private String goodsName;
    private String startTime;
    private String endTime;

}
