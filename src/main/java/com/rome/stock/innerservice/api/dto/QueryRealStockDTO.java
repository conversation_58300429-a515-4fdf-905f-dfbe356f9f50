package com.rome.stock.innerservice.api.dto;

import com.rome.arch.core.dto.DTO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * <AUTHOR>
 * @Description 查询真实库存DTO对象
 * @date 2020/5/26
 */
@Data
public class QueryRealStockDTO extends DTO {

    @ApiModelProperty(value="仓库外部编码")
    @NotBlank
    private String warehouseOutCode;


    @ApiModelProperty(value="工厂编码")
    @NotBlank
    private String factoryCode;

    @ApiModelProperty(value="商品信息")
    @NotNull
    private List<BaseSkuInfoDTO> baseSkuInfoDTOS;

}    
   