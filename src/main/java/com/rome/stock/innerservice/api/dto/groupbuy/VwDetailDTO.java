package com.rome.stock.innerservice.api.dto.groupbuy;

import java.math.BigDecimal;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

@Data
@EqualsAndHashCode
public class VwDetailDTO {
    /**
     * 商品code
     */
    @ApiModelProperty(value = "商品code")
    @NotBlank(message = "商品编码不能为空")
    private String skuCode;
    /**
     * 商品转移数量
     */
    @ApiModelProperty(value = "商品转移数量")
    @NotNull(message = "商品转移数量不能为空")
    private BigDecimal skuQty;
}
