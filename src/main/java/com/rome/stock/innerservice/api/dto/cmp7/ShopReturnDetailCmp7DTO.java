package com.rome.stock.innerservice.api.dto.cmp7;

import java.math.BigDecimal;

import lombok.Data;

/**
 * <AUTHOR>
 * @description cmp7退货过帐单明细信息
 * @date 2020/11/12 19:48
 * @throw
 */
@Data
public class ShopReturnDetailCmp7DTO {

    /**
     * 加盟商组织编码
     */
    private String preOrgCode;

    /**
     * 门店编码
     */
    private String orgCode;

    /**
     * 过账日期（yyyy-MM-dd）
     */
    private String accDate;

    /**
     * CMP退货申请单号
     */
    private String billNo;

    /**
     * CMP退货申请单行号
     */
    private String serialNo;

    /**
     * SAP退货单号
     */
    private String sapBillNo;

    /**
     * sap行号
     */
    private String sapSerialNo;

    /**
     * SAP退货单号
     */
    private String kpBillNo;

    /**
     * 商品编码
     */
    private String pluCode;

    /**
     * 包装换算率
     */
    private BigDecimal packRate;

    /**
     *  退货数量
     */
    private BigDecimal quantity;

    /**
     * 单位
     */
    private String unit;

    /**
     * 单位编码
     */
    private String unitCode;

    /**
     * 无税单价
     */
    private BigDecimal price;

    /**
     * 无税金额
     */
    private BigDecimal total;

    /**
     * 税率
     */
    private BigDecimal taxRate;

    /**
     * 含税单价
     */
    private BigDecimal taxPrice;

    /**
     * 含税金额
     */
    private BigDecimal taxTotal;

}    
