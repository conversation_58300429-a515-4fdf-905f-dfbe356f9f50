package com.rome.stock.innerservice.api.dto;

import java.math.BigDecimal;
import java.util.List;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

@Data
@EqualsAndHashCode
public class StockRecord extends Pagination {
    /**
     * 唯一主键
     */
    @ApiModelProperty(value = "主键")
    private Long id;
    /**
     * 仓储类型
     */
    @ApiModelProperty(value = "仓储类型")
    private Integer storageType;
    /**
     * 工厂编码
     */
    @ApiModelProperty(value = "工厂编码")
    private String factoryCode;
    /**
     * 工厂名称
     */
    @ApiModelProperty(value = "工厂名称")
    private String factoryName;
    /**
     * 仓库id
     */
    @ApiModelProperty(value = "仓库id")
    private Long warehouseId;
    /**
     * 虚仓code
     */
    @ApiModelProperty(value = "虚仓code")
    private String virtualWarehouseCode;
    /**
     * 虚仓code
     */
    @ApiModelProperty(value = "虚仓codeList")
    private List<String> virtualWarehouseCodeList;
    /**
     * 实仓id
     */
    @ApiModelProperty(value = "实仓id")
    private Long realWarehouseId;

    /**
     * 仓库编码
     */
    @ApiModelProperty(value = "仓库编码")
    private String warehouseCode;
    /**
     * 仓库编码集合
     */
    @ApiModelProperty(value = "仓库编码集合")
    private List<String> warehouseCodes;
    /**
     * 仓库名称
     */
    @ApiModelProperty(value = "仓库名称")
    private String warehouseName;
    /**
     * 渠道编码
     */
    @ApiModelProperty(value = "渠道编码")
    private String channelCode;
    /**
     * 渠道字符串集合
     */
    @ApiModelProperty(value = "渠道字符串集合")
    private String channelCodes;
    /**
     * 渠道名称
     */
    @ApiModelProperty(value = "渠道名称")
    private String channelName;
    /**
     * 商品编码
     */
    @ApiModelProperty(value = "商品编码")
    private Long skuId;
    /**
     * 商品skuId集合
     */
    @ApiModelProperty(value = "商品skuId集合")
    private List<Long> skuIds;
    /**
     * 商品编号
     */
    @ApiModelProperty(value = "商品编号")
    private String skuCode;
    /**
     * 商品名称
     */
    @ApiModelProperty(value = "商品名称")
    private String skuName;
    /**
     * 商品标题
     */
    @ApiModelProperty(value = "商品标题")
    private String skuTitle;
    /**
     * 商品基本单位
     */
    @ApiModelProperty(value = "商品基本单位")
    private String skuUnit;
    /**
     * 商品基本单位编码
     */
    @ApiModelProperty(value = "商品基本单位编码")
    private String skuUnitCode;
    /**
     * 商品品类
     */
    @ApiModelProperty(value = "商品品类")
    private String skuType;
    /**
     * 库存现有量
     */
    @ApiModelProperty(value = "库存现有量")
    private BigDecimal realQty;
    /**
     * 库存可用量
     */
    @ApiModelProperty(value = "库存可用量")
    private BigDecimal availableQty;

    /**
     * 安全库存
     */
    @ApiModelProperty(value = "安全库存")
    private BigDecimal safeConfigQty;

    /**
     * 库存锁定量
     */
    @ApiModelProperty(value = "库存锁定量")
    private BigDecimal lockQty;
    /**
     * 库存质检锁定量
     */
    @ApiModelProperty(value = "库存质检锁定量")
    private BigDecimal qualityQty;
    /**
     * 库存质检锁定量(箱)
     */
    @ApiModelProperty(value = "库存质检锁定量(箱)")
    private BigDecimal qualityQtyBox;
    /**
     * 冻结库存
     */
    @ApiModelProperty(value = "冻结库存")
    private BigDecimal unqualifiedQty;

    @ApiModelProperty(value = "未出库完成的在途库存")
    private BigDecimal unCompleteOnRoadStock=BigDecimal.ZERO;

    @ApiModelProperty(value = "采购在途库存")
    private BigDecimal purchaseOnRoadStock=BigDecimal.ZERO;

    /**
     * 公司编号
     */
    @ApiModelProperty(value = "公司编号")
    private String corpCode;
    /**
     * 公司名称
     */
    @ApiModelProperty(value = "公司名称")
    private String corpName;
    /**
     * 仓库类型名称
     */
    @ApiModelProperty(value = "仓库类型名称")
    private String warehouseTypeName;
    /**
     * 仓库类型
     */
    @ApiModelProperty(value = "仓库类型")
    private Integer warehouseType;
    /**
     * 可用库存量是否必须大于0
     */
    @ApiModelProperty(value = "可用库存量是否必须大于0")
    private Integer moreThanZero;
    /**
     * 质检库存量是否必须大于0
     */
    @ApiModelProperty(value = "质检库存量是否必须大于0")
    private Integer moreThanZeroQuality;
    /**
     * 锁定库存量是否必须大于0
     */
    @ApiModelProperty(value = "锁定库存量是否必须大于0")
    private Integer moreThanZeroLock;
    /**
     *真实库存量是否必须大于0
     */
    @ApiModelProperty(value = "真实库存量是否必须大于0")
    private Integer moreThanZeroReal;

    /**
     *真实库存量+质检库存+质检不合格库存是否必须大于0
     */
    @ApiModelProperty(value = "质检不合格库存是否必须大于0")
    private Integer moreThanZeroBaoYou;

    /**
     * 冻结库存（质检不合格库存）是否必须大于0
     */
    @ApiModelProperty(value = "冻结库存是否必须大于0")
    private Integer moreThanZeroUnqualifiedQty;


    /**
     * 商家编号
     */
    @ApiModelProperty(value = "商家编号")
    private Long merchantId;

    /**
     * 箱单位名称
     */
    @ApiModelProperty(value = "箱单位名称")
    private String boxUnitName;

    /**
     * 箱单位数量
     */
    @ApiModelProperty(value = "箱单位数量")
    private BigDecimal boxUnitCount;

    /**
     * 发货单位
     */
    @ApiModelProperty(value = "发货单位")
    private String deliveryUnitName;

    /**
     * 发货单位数量
     */
    @ApiModelProperty(value = "发货单位数量")
    private BigDecimal deliveryUnitCount;

    @ApiModelProperty(value = "商品69码")
    private List<String> barCodeList;
    
    @ApiModelProperty(value = "库存在途量")
    private BigDecimal onroadQty;


    @ApiModelProperty(value = "区域编码")
    private List<List<String>> areaList;

    /**
     * 销售状态名称
     */
    @ApiModelProperty(value = "销售状态名称")
    private String saleStatusName;
    
    /**
     * 仓库类型列表
     */
    @ApiModelProperty(value = "仓库类型列表")
    private List<Integer> warehouseTypeList;

    @ApiModelProperty(value = "实仓编码")
    private String realWarehouseCode;

    @ApiModelProperty(value = "实仓名称")
    private String realWarehouseName;

    @ApiModelProperty(value = "是否执行count查询操作")
    private Boolean queryCount=true;

    @ApiModelProperty(value = "是否执行导出查询操作")
    private Boolean export=false;

    private Long userId;

    @ApiModelProperty(value = "导出文件名称")
    private String exportName;

    /**
     * 实仓类型
     */
    @ApiModelProperty(value = "实仓类型")
    private Integer realWarehouseType;

    @ApiModelProperty(value = "商品69码")
    private String barCode;

    @ApiModelProperty(value = "仓库编码集合")
    private List<String> realWarehouseCodeList;

    /**
     * 是否量贩店查询库存
     */
    private boolean isSpecial;
}
