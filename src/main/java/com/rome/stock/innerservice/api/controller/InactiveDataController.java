/**
 * Filename InactiveDataController.java
 * Company 上海来伊份科技有限公司。
 * <AUTHOR>
 * @version 
 */
package com.rome.stock.innerservice.api.controller;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import com.alibaba.fastjson.JSONObject;
import com.rome.arch.core.clientobject.Response;
import com.rome.stock.innerservice.common.ResCode;
import com.rome.stock.innerservice.template.active.ActiveDataToEsJob;
import com.rome.stock.innerservice.template.inactive.InactiveDataJob;

import lombok.extern.slf4j.Slf4j;

/**
 * 冷数据处理
 * <AUTHOR>
 * @since 2019年11月10日 下午5:17:45
 */
@Slf4j
@RestController
@RequestMapping("/stock/v1/inactive_data")
public class InactiveDataController {

	@Autowired
    private InactiveDataJob inactiveDataJob;
	
	@Autowired
    private ActiveDataToEsJob activeDataToEsJob;
	
	/**
     * 冷数据定时任务
     * @param requestVO
     * @return
     */
    @RequestMapping(value = "/inactiveDataJob", method = RequestMethod.POST)
    public Response inactiveDataJob(@RequestBody JSONObject requestVO) {
    	try {
    		boolean result = inactiveDataJob.taskStart();
        	return Response.builderSuccess("结果" + result);
    	}catch (Exception e) {
            log.error("出错", e);
            return Response.builderFail(ResCode.STOCK_ERROR_1003, ResCode.STOCK_ERROR_1003_DESC);
        }
    }
    
    /**
     * 冷数据处理,指定模板名,和结束时间
     * @param requestVO
     * @return
     */
    @RequestMapping(value = "/inactiveDataByRunTemplate", method = RequestMethod.POST)
    public Response inactiveDataByRunTemplate(@RequestBody JSONObject requestVO) {
    	try {
    		int num = inactiveDataJob.runTemplate(requestVO.getString("templateName"), requestVO.getString("stopTime"));
        	return Response.builderSuccess("结果运行成功,数量=" + num);
    	}catch (Exception e) {
            log.error("出错", e);
            return Response.builderFail(ResCode.STOCK_ERROR_1003, ResCode.STOCK_ERROR_1003_DESC);
        }
    }
    
    /**
     * 活跃数据到es定时任务
     * @param requestVO
     * @return
     */
    @RequestMapping(value = "/activeDataToEsJob", method = RequestMethod.POST)
    public Response activeDataToEsJob(@RequestBody JSONObject requestVO) {
    	try {
    		boolean result = activeDataToEsJob.taskStart();
        	return Response.builderSuccess("结果" + result);
    	}catch (Exception e) {
            log.error("出错", e);
            return Response.builderFail(ResCode.STOCK_ERROR_1003, ResCode.STOCK_ERROR_1003_DESC);
        }
    }
    
    /**
     * 活跃数据到es处理,指定模板名,和结束时间
     * @param requestVO
     * @return
     */
    @RequestMapping(value = "/activeDataToEsByRunTemplate", method = RequestMethod.POST)
    public Response activeDataToEsByRunTemplate(@RequestBody JSONObject requestVO) {
    	try {
    		int num = activeDataToEsJob.runTemplate(requestVO.getString("templateName"), requestVO.getIntValue("runTime"));
        	return Response.builderSuccess("结果运行成功,数量=" + num);
    	}catch (Exception e) {
            log.error("出错", e);
            return Response.builderFail(ResCode.STOCK_ERROR_1003, ResCode.STOCK_ERROR_1003_DESC + e.getMessage());
        }
    }
    
}
