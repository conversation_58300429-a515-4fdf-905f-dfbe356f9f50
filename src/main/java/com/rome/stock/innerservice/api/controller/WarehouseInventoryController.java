package com.rome.stock.innerservice.api.controller;

import com.github.pagehelper.PageInfo;
import com.rome.arch.core.clientobject.Response;
import com.rome.arch.core.exception.RomeException;
import com.rome.arch.util.controller.RomeController;
import com.rome.stock.innerservice.api.dto.WarehouseInventoryPageDTO;
import com.rome.stock.innerservice.api.dto.WarehouseToBRecord;
import com.rome.stock.innerservice.api.dto.frontrecord.InventoryAdjustDTO;
import com.rome.stock.innerservice.api.dto.frontrecord.WarehouseInventoryDTO;
import com.rome.stock.innerservice.api.dto.frontrecord.WarehouseInventoryDetailDTO;
import com.rome.stock.innerservice.common.RedisUtil;
import com.rome.stock.innerservice.common.ResCode;
import com.rome.stock.innerservice.constant.FrontRecordTypeVO;
import com.rome.stock.innerservice.constant.ResponseMsg;
import com.rome.stock.innerservice.domain.service.WarehouseInventoryService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import io.swagger.annotations.ApiResponse;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;

import javax.annotation.Resource;
import java.util.List;

@Slf4j
@RomeController
@RequestMapping("/stock/v1/warehouse_inventory")
@Api(tags={"仓库盘点"})
public class WarehouseInventoryController {

    @Autowired
    private WarehouseInventoryService warehouseInventoryService;
    @Resource
    private RedisUtil redisUtil;

    /**
     * 仓库盘点单
     * @param frontRecord
     * @return
     */
    @ApiOperation(value = "仓库盘点单", nickname = "WareInventoryRecord", produces = MediaType.APPLICATION_JSON_VALUE)
    @ApiResponse(code = 200, message = "success", response = WarehouseToBRecord.class)
    @RequestMapping(value = "/front_record/addWarehouseInventoryRecord", method = RequestMethod.POST)
    public Response<WarehouseInventoryDTO> addShopInventoryRecord(@ApiParam(name = "frontRecord", value = "仓库盘点单") @RequestBody @Validated WarehouseInventoryDTO frontRecord) {
        try {
            //设置单据类型
            frontRecord.setRecordType(FrontRecordTypeVO.WAREHOUSE_INVENTORY_RECORD.getType());
            WarehouseInventoryDTO record=warehouseInventoryService.addWarehouseInventoryRecord(frontRecord);
            return Response.builderSuccess(record);
        } catch (RomeException e) {
            log.error(e.getMessage(), e);
            return Response.builderFail(e.getCode(),e.getMessage());
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return Response.builderFail("-50000",e.getMessage());
        }
    }
    /**
     * 仓库盘点单
     * @param frontRecord
     * @return
     */
    @ApiOperation(value = "仓库盘点单", nickname = "WareInventoryRecord", produces = MediaType.APPLICATION_JSON_VALUE)
    @ApiResponse(code = 200, message = "success", response = WarehouseToBRecord.class)
    @RequestMapping(value = "/front_record/addKjWarehouseInventoryRecord", method = RequestMethod.POST)
    public Response<WarehouseInventoryDTO> addKjWarehouseInventoryRecord(@ApiParam(name = "frontRecord", value = "仓库盘点单") @RequestBody @Validated WarehouseInventoryDTO frontRecord) {
        try {
            //设置单据类型
            frontRecord.setRecordType(FrontRecordTypeVO.WAREHOUSE_INVENTORY_RECORD.getType());
            WarehouseInventoryDTO record=warehouseInventoryService.addKjWarehouseInventoryRecord(frontRecord);
            return Response.builderSuccess(record);
        } catch (RomeException e) {
            log.error(e.getMessage(), e);
            return Response.builderFail(e.getCode(),e.getMessage());
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return Response.builderFail(ResCode.STOCK_ERROR_1003_DESC,ResCode.STOCK_ERROR_1003_DESC);
        }
    }

    /**
     * 仓库盘点单(中台云仓)
     * @param frontRecord
     * @return
     */
    @ApiOperation(value = "仓库盘点单", nickname = "WareInventoryRecord", produces = MediaType.APPLICATION_JSON_VALUE)
    @ApiResponse(code = 200, message = "success", response = WarehouseToBRecord.class)
    @RequestMapping(value = "/front_record/addZtoWarehouseInventoryRecord", method = RequestMethod.POST)
    public Response<WarehouseInventoryDTO> addZtoWarehouseInventoryRecord(@ApiParam(name = "frontRecord", value = "仓库盘点单") @RequestBody @Validated WarehouseInventoryDTO frontRecord) {
        try {
            //设置单据类型
            frontRecord.setRecordType(FrontRecordTypeVO.WAREHOUSE_INVENTORY_RECORD.getType());
            WarehouseInventoryDTO record=warehouseInventoryService.addZtoWarehouseInventoryRecord(frontRecord);
            return Response.builderSuccess(record);
        } catch (RomeException e) {
            log.error(e.getMessage(), e);
            return Response.builderFail(e.getCode(),e.getMessage());
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return Response.builderFail(ResCode.STOCK_ERROR_1003_DESC,ResCode.STOCK_ERROR_1003_DESC);
        }
    }


    /**
     * @Description: C端仓库盘点 <br>
     *
     * <AUTHOR> 2019/9/12
     * @param frontRecord
     * @return
     */
    @ApiOperation(value = "C端仓库盘点", nickname = "CWareInventoryRecord", produces = MediaType.APPLICATION_JSON_VALUE)
    @ApiResponse(code = 200, message = "success", response = WarehouseToBRecord.class)
    @RequestMapping(value = "/front_record/addToCInventoryRecord", method = RequestMethod.POST)
    public Response<WarehouseInventoryDTO> addToCInventoryRecord(@ApiParam(name = "frontRecord", value = "仓库盘点单") @RequestBody @Validated WarehouseInventoryDTO frontRecord) {
        try {
            WarehouseInventoryDTO record = warehouseInventoryService.addToCInventoryRecord(frontRecord);
            return Response.builderSuccess(record);
        } catch (RomeException e) {
            log.error(e.getMessage(), e);
            return Response.builderFail(e.getCode(),e.getMessage());
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return Response.builderFail(ResCode.STOCK_ERROR_1003_DESC,ResCode.STOCK_ERROR_1003_DESC);
        }
    }

    /**
     * 仓库盘点页面
     * @param warehouseInventoryPageDTO
     * @return
     */
    @ApiOperation(value = "仓库盘点页面",  produces = MediaType.APPLICATION_JSON_VALUE)
    @ApiResponse(code = 200, message = "success", response = WarehouseToBRecord.class)
    @RequestMapping(value = "/queryWarehouseInventoryList", method = RequestMethod.POST)
    public Response<PageInfo<WarehouseInventoryPageDTO>> queryWarehouseInventoryList(@ApiParam(name = "warehouseInventoryPageDTO", value = "仓库盘点页面") @RequestBody @Validated WarehouseInventoryPageDTO warehouseInventoryPageDTO) {
        try {
            PageInfo<WarehouseInventoryPageDTO> pageList = warehouseInventoryService.queryWarehouseInventoryList(warehouseInventoryPageDTO);
            return Response.builderSuccess(pageList);
        } catch (RomeException e) {
            log.error(e.getMessage(), e);
            return Response.builderFail(e.getCode(),e.getMessage());
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return Response.builderFail(ResCode.STOCK_ERROR_1003_DESC,ResCode.STOCK_ERROR_1003_DESC);
        }
    }

    @ApiOperation(value = "仓库盘点详情页面", produces = MediaType.APPLICATION_JSON_VALUE)
    @ApiResponse(code = 200, message = "success")
    @RequestMapping(value = "/selectWarehouseInventoryDetail", method = RequestMethod.GET)
    public Response<WarehouseInventoryDTO> selectWarehouseInventoryDetail(@RequestParam("id") String id){

        try {
            WarehouseInventoryDTO warehouseInventoryDTO = warehouseInventoryService.selectWarehouseInventoryDetail(Long.valueOf(id));
            return ResponseMsg.SUCCESS.buildMsg(warehouseInventoryDTO);
        } catch (RomeException e) {
            log.error(e.getMessage(), e);
            return ResponseMsg.FAIL.buildMsg(e.getCode(),e.getMessage());
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return Response.builderFail(ResCode.STOCK_ERROR_1003_DESC,ResCode.STOCK_ERROR_1003_DESC);
        }
    }
    /**
     * 库存调整
     * @param dto
     * @return
     */
    @ApiOperation(value = "库存调整单", produces = MediaType.APPLICATION_JSON_VALUE)
    @ApiResponse(code = 200, message = "success", response = InventoryAdjustDTO.class)
    @RequestMapping(value = "/front_record/inventoryAdjustRecord", method = RequestMethod.POST)
    public Response inventoryAdjustRecord(@ApiParam(name = "inventoryAdjustDTO", value = "库存调整单") @RequestBody @Validated InventoryAdjustDTO dto) {
        String uniqueKey = "inventoryAdjustRecord" + "_" + dto.getOutRecordCode();
        boolean isLock = false;
        try {
            isLock = redisUtil.lock(uniqueKey, "1", 120);
            if (isLock) {
                 warehouseInventoryService.inventoryAdjustRecord(dto);
                 return ResponseMsg.SUCCESS.buildMsg();
            }else {
                return Response.builderFail(ResCode.STOCK_ERROR_1003,"库存调整单正在处理中，请稍后再试。。。");
            }
        } catch (RomeException e) {
            log.error(e.getMessage(), e);
            return Response.builderFail(e.getCode(),e.getMessage());
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return Response.builderFail(ResCode.STOCK_ERROR_1003,e.getMessage());
        }finally {
            if(isLock) {
                redisUtil.unLock(uniqueKey, "1");
            }
        }
    }

    @ApiOperation(value = "新增仓库损益调整单-中台发起",nickname = "addRealWarehouseInventoryAdjustRecord",produces = MediaType.APPLICATION_JSON_VALUE)
    @ApiResponse(code = 200, message = "success")
    @RequestMapping(value = "/addRealWarehouseInventoryAdjustRecord", method = RequestMethod.POST)
    public Response addRealWarehouseInventoryAdjustRecord(@RequestBody InventoryAdjustDTO inventoryDTO){

        try {
            warehouseInventoryService.addRealWarehouseInventoryRecord(inventoryDTO);
            return ResponseMsg.SUCCESS.buildMsg();
        } catch (RomeException e) {
            log.error(e.getMessage(), e);
            return ResponseMsg.FAIL.buildMsg(e.getCode(),e.getMessage());
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return ResponseMsg.FAIL.buildMsg("500000",e.getMessage());
        }
    }

    @ApiOperation(value = "修改仓库损益调整单-中台发起", nickname = "modifyRealWarehouseInventoryRecord",produces = MediaType.APPLICATION_JSON_VALUE)
    @ApiResponse(code = 200, message = "success")
    @RequestMapping(value = "/modifyRealWarehouseInventoryRecord", method = RequestMethod.POST)
    public Response modifyRealWarehouseInventoryRecord(@RequestBody InventoryAdjustDTO inventoryDTO){

        try {
            warehouseInventoryService.modifyRealWarehouseInventoryRecord(inventoryDTO);
            return ResponseMsg.SUCCESS.buildMsg();
        } catch (RomeException e) {
            log.error(e.getMessage(), e);
            return ResponseMsg.FAIL.buildMsg(e.getCode(),e.getMessage());
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return ResponseMsg.FAIL.buildMsg("500000",e.getMessage());
        }
    }

    @ApiOperation(value = "确认仓库损益调整单--中台发起", nickname = "confirmRealWarehouseInventoryRecord", produces = MediaType.APPLICATION_JSON_VALUE)
    @ApiResponse(code = 200, message = "success")
    @RequestMapping(value = "/confirmRealWarehouseInventoryRecord", method = RequestMethod.POST)
    public Response confirmRealWarehouseInventoryRecord(@RequestBody List<Long> ids, @RequestParam("modifier") Long modifier){
        try {
            warehouseInventoryService.confirmRealWarehouseInventoryRecord(ids,modifier);
            return ResponseMsg.SUCCESS.buildMsg();
        } catch (RomeException e) {
            log.error(e.getMessage(), e);
            return ResponseMsg.FAIL.buildMsg(e.getCode(),e.getMessage());
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return ResponseMsg.FAIL.buildMsg("500000",e.getMessage());
        }
    }

    @ApiOperation(value = "取消仓库损益调整单--中台发起", nickname = "cancelRealWarehouseInventoryRecord", produces = MediaType.APPLICATION_JSON_VALUE)
    @ApiResponse(code = 200, message = "success")
    @RequestMapping(value = "/cancelRealWarehouseInventoryRecord", method = RequestMethod.GET)
    public Response cancelRealWarehouseInventoryRecord(@RequestParam("id") String id,@RequestParam("modifier") Long modifier){

        try {
            warehouseInventoryService.cancelRealWarehouseInventoryRecord(id,modifier);
            return ResponseMsg.SUCCESS.buildMsg();
        } catch (RomeException e) {
            log.error(e.getMessage(), e);
            return ResponseMsg.FAIL.buildMsg(e.getCode(),e.getMessage());
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return ResponseMsg.FAIL.buildMsg("500000",e.getMessage());
        }
    }

    @ApiOperation(value = "根据条件查询仓库损益调整单--中台发起", nickname = "selectRWInventoryRecordByCondition", produces = MediaType.APPLICATION_JSON_VALUE)
    @ApiResponse(code = 200, message = "success")
    @RequestMapping(value = "/selectRWInventoryRecordByCondition", method = RequestMethod.POST)
    public Response selectRWInventoryRecordByCondition(@RequestBody InventoryAdjustDTO inventoryDTO){
        try {
            PageInfo<InventoryAdjustDTO> pageList = warehouseInventoryService.selectRWInventoryRecordByCondition(inventoryDTO);
            return ResponseMsg.SUCCESS.buildMsg(pageList);
        } catch (RomeException e) {
            log.error(e.getMessage(), e);
            return ResponseMsg.FAIL.buildMsg(e.getCode(),e.getMessage());
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return ResponseMsg.FAIL.buildMsg("500000",e.getMessage());
        }
    }

    @ApiOperation(value = "根据调整单id查询明细--中台发起", nickname = "selectRWInventoryRecordDetail", produces = MediaType.APPLICATION_JSON_VALUE)
    @ApiResponse(code = 200, message = "success")
    @RequestMapping(value = "/selectRWInventoryRecordDetail", method = RequestMethod.GET)
    public Response selectRWInventoryRecordDetail(@RequestParam("id") String id){
        try {
            InventoryAdjustDTO detailList = warehouseInventoryService.selectRWInventoryRecordDetail(Long.parseLong(id));
            return ResponseMsg.SUCCESS.buildMsg(detailList);
        } catch (RomeException e) {
            log.error(e.getMessage(), e);
            return ResponseMsg.EXCEPTION.buildMsg(e.getCode(), e.getMessage());
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return ResponseMsg.EXCEPTION.buildMsg(ResCode.STOCK_ERROR_1003_DESC,ResCode.STOCK_ERROR_1003_DESC);
        }
    }

    @ApiOperation(value = "根据id批量查询单子", nickname = "selectRWInventoryRecordByIds", produces = MediaType.APPLICATION_JSON_VALUE)
    @ApiResponse(code = 200, message = "success")
    @RequestMapping(value = "/selectRWInventoryRecordByIds", method = RequestMethod.POST)
    public Response selectRWInventoryRecordByIds(@RequestBody List<Long> ids){
        try {
            List<InventoryAdjustDTO> adjustDTOS = warehouseInventoryService.selectRWInventoryRecordByIds(ids);
            return ResponseMsg.SUCCESS.buildMsg(adjustDTOS);
        } catch (RomeException e) {
            log.error(e.getMessage(), e);
            return ResponseMsg.FAIL.buildMsg(e.getCode(),e.getMessage());
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return ResponseMsg.FAIL.buildMsg("500000",e.getMessage());
        }
    }

    @ApiOperation(value = "审核盘点", nickname = "inventoryCheck", produces = MediaType.APPLICATION_JSON_VALUE)
    @ApiResponse(code = 200, message = "success")
    @RequestMapping(value = "/inventoryCheck", method = RequestMethod.POST)
    public Response inventoryCheck(@RequestBody WarehouseInventoryDTO warehouseInventoryDTO){
        String key = "inventoryCheck_" + warehouseInventoryDTO.getId();
        boolean isLock = false;
        try {
            isLock = redisUtil.tryLock(key, "1", 120);
            if (isLock) {
                warehouseInventoryService.inventoryCheck(warehouseInventoryDTO);
                return ResponseMsg.SUCCESS.buildMsg("");
            } else {
                return Response.builderFail(ResCode.STOCK_ERROR_1003,"盘点结果正在处理中，请稍后再试。。。");
            }
        } catch (RomeException e) {
            log.error(e.getMessage(), e);
            return ResponseMsg.FAIL.buildMsg(e.getCode(),e.getMessage());
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return  Response.builderFail("-5000", e.getMessage());
        } finally {
            if (isLock) {
                redisUtil.unLock(key, "1");
            }
        }
    }

    @ApiOperation(value = "查询明细列表数据", nickname = "queryWarehouseInventoryDetailList", produces = MediaType.APPLICATION_JSON_VALUE)
    @ApiResponse(code = 200, message = "success")
    @RequestMapping(value = "/queryWarehouseInventoryDetailList", method = RequestMethod.POST)
    public Response<PageInfo<WarehouseInventoryDetailDTO>> queryWarehouseInventoryDetailList(@RequestBody WarehouseInventoryPageDTO param){
        try {
            return warehouseInventoryService.queryWarehouseInventoryDetailList(param);
        } catch (RomeException e) {
            log.error(e.getMessage(), e);
            return ResponseMsg.FAIL.buildMsg(e.getCode(),e.getMessage());
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return  Response.builderFail("-5000", e.getMessage());
        }
    }


    @ApiOperation(value = "财务中台盘点过账", nickname = "warehouseInventoryPostAccount", produces = MediaType.APPLICATION_JSON_VALUE)
    @ApiResponse(code = 200, message = "success")
    @RequestMapping(value = "/warehouseInventoryPostAccount", method = RequestMethod.POST)
    public Response warehouseInventoryPostAccount(@RequestBody List<Long> ids){
        return ResponseMsg.SUCCESS.buildMsg("");
//        try {
//            warehouseInventoryService.warehouseInventoryPostAccount(ids);
//
//        } catch (RomeException e) {
//            log.error(e.getMessage(), e);
//            return ResponseMsg.FAIL.buildMsg(e.getCode(),e.getMessage());
//        } catch (Exception e) {
//            log.error(e.getMessage(), e);
//            return  Response.builderFail("-5000", e.getMessage());
//        }
    }
}
