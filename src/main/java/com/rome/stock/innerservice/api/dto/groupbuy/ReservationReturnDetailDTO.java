package com.rome.stock.innerservice.api.dto.groupbuy;

import com.rome.arch.core.dto.DTO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 退货单详情
 */
@Data
public class ReservationReturnDetailDTO extends DTO {
    /**
     * 主键
     */
    @ApiModelProperty(value="主键")
    private Long id;

    /**
     * 所属单据编号
     */
    @ApiModelProperty(value="所属单据编号")
    private String recordCode;

    /**
     * 前置单ID
     */
    @ApiModelProperty(value="前置单ID")
    private Long frontRecordId;

    /**
     * 商品skuId
     */
    @ApiModelProperty(value="商品skuId")
    private Long skuId;

    /**
     * sku编码
     */
    @ApiModelProperty(value="sku编码")
    private String skuCode;

    /**
     * 商品数量
     */
    @ApiModelProperty(value="商品数量")
    private BigDecimal skuQty;

    /**
     * 单位
     */
    @ApiModelProperty(value="单位")
    private String unit;

    /**
     * 单位编码
     */
    @ApiModelProperty(value="单位编码")
    private String unitCode;
    
    /**
     * sku名称
     */
    @ApiModelProperty(value="sku名称")
    private String skuName;
    
    /**
     * 实际发货数量
     */
    @ApiModelProperty(value="实际发货数量")
    private BigDecimal deliveryQty;
    
    /**
     * 实际收货数量
     */
    @ApiModelProperty(value="实际收货数量")
    private BigDecimal actualQty;
    
    /**
     * 创建时间
     */
    @ApiModelProperty(value="创建时间",hidden = true)
    private Date createTime;

    /**
     * 更新时间
     */
    @ApiModelProperty(value="更新时间",hidden = true)
	private Date updateTime;
    
}    
