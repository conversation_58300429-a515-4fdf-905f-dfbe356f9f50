/**
 * Filename WmsShopStockFtpTemplate.java
 * Company 上海来伊份科技有限公司。
 * <AUTHOR>
 * @version 
 */
package com.rome.stock.innerservice.api.dto.template;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

import cn.afterturn.easypoi.excel.annotation.Excel;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 仓库和门库库存上传到ftp模板映射类
 * <AUTHOR>
 * @since 2020-9-15 17:51:24
 */
@Data
@EqualsAndHashCode
public class ShopStockFtpTemplate implements Serializable {

	@Excel(name = "工厂",orderNum = "0")
    private String factoryCode;
	
	@Excel(name = "仓库编号",orderNum = "1")
    private String realWarehouseOutCode;
	
	@Excel(name = "商品编码",orderNum = "2")
    private String skuCode;
    
    @Excel(name = "商品名称",orderNum = "3")
    private String skuName;
    
    @Excel(name = "单位名称",orderNum = "4")
    private String unit;
    
    @Excel(name = "单位编码",orderNum = "5")
    private String unitCode;
    
    @Excel(name = "现有库存",orderNum = "6")
    private BigDecimal realQty;
    
    @Excel(name = "可用库存",orderNum = "7")
    private BigDecimal availableQty;
    
    @Excel(name = "已占用库存",orderNum = "8")
    private BigDecimal lockQty;
    
    @Excel(name = "质检库存",orderNum = "9")
    private BigDecimal qualityQty;
    
    @Excel(name = "冻结库存",orderNum = "10")
    private BigDecimal unqualifiedQty;

    @Excel(name = "在途库存",orderNum = "11")
    private BigDecimal onroadQty;

    @Excel(name = "已开单未发货",orderNum = "12")
    private BigDecimal unOutQty;
    
    @Excel(name = "当前时间",orderNum = "13",exportFormat = "yyyy-MM-dd HH:mm:ss")
    private Date currentDate;


    public ShopStockFtpTemplate(){
        super();
    }

    public ShopStockFtpTemplate(String factoryCode, String skuCode){
       this.factoryCode = factoryCode;
       this.realWarehouseOutCode = "0001";
       this.skuCode = skuCode;
       this.realQty = BigDecimal.ZERO;
       this.availableQty =BigDecimal.ZERO;
       this.lockQty =BigDecimal.ZERO;
       this.qualityQty =BigDecimal.ZERO;
       this.unqualifiedQty =BigDecimal.ZERO;
       this.onroadQty =BigDecimal.ZERO;
       this.setCurrentDate(new Date());
    }
}
