package com.rome.stock.innerservice.api.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 门店盘点结果明细临时表
 */
@Data
@ApiModel("门店盘点结果明细临时DTO")
public class ShopInventoryTempDTO {

    @ApiModelProperty("唯一主键")
    private Long id;

    @ApiModelProperty("外部盘点单号")
    @NotBlank(message = "外部盘点单号不能为空")
    private String outRecordCode;

    @ApiModelProperty("业务类型：1-抽盘，2-全盘")
    @NotNull(message = "业务类型不能为空")
    private Integer businessType;

    @ApiModelProperty("仓库编码(门店编码)")
    @NotBlank(message = "仓库编码不能为空")
    private String factoryCode;

    @ApiModelProperty("商品skuID")
    @NotNull(message = "商品skuID不能为空")
    private Long skuId;

    @ApiModelProperty("商品sku编码")
    @NotBlank(message = "商品sku编码不能为空")
    private String skuCode;

    @ApiModelProperty("商品数量")
    @NotNull(message = "商品数量不能为空")
    private BigDecimal skuQty;

    @ApiModelProperty("账面数量")
    @NotNull(message = "账面数量不能为空")
    private BigDecimal accQty;

    @ApiModelProperty("备注")
    private String remark;

    @ApiModelProperty("盘点时间")
    private Date outCreateTime;

    @ApiModelProperty("创建时间")
    private Date createTime;

    @ApiModelProperty("更新时间")
    private Date updateTime;

    @ApiModelProperty("创建人")
    private Long creator;

    @ApiModelProperty("更新人")
    private Long modifier;

    @ApiModelProperty("是否可用：0-否，1-是")
    private Byte isAvailable;

    @ApiModelProperty("是否删除：0-否，1-是")
    private Byte isDeleted;

    @ApiModelProperty("版本号")
    private Integer versionNo;

    @ApiModelProperty("租户ID")
    private Long tenantId;

    @ApiModelProperty("业务应用ID")
    private String appId;

    @ApiModelProperty("当前页码")
    private Integer currentPage;

    @ApiModelProperty("页码")
    private Integer pageNum;

    @ApiModelProperty("每页数量")
    private Integer pageSize;
} 