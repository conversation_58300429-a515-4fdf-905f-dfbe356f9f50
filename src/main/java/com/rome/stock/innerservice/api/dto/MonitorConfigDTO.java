package com.rome.stock.innerservice.api.dto;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.rome.stock.innerservice.common.StringUtils;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import org.apache.commons.collections.CollectionUtils;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * <AUTHOR> @Description
 */
@Data
@NoArgsConstructor
@EqualsAndHashCode
public class MonitorConfigDTO extends Pagination{

    @ApiModelProperty("唯一主键")
    private Long id;

    @ApiModelProperty("应用域")
    private String domain;

    private Long poolId;

    @ApiModelProperty("paramName")
    private String paramName;

    @ApiModelProperty("Pool表示符")
    private String poolAlias;

    @ApiModelProperty("Pool名称")
    private String poolName;

    @ApiModelProperty("调用模式")
    private String callMode;

    @ApiModelProperty("飞书地址")
    private String feishuUrl;

    @ApiModelProperty("公共通知时间间隔")
    private BigDecimal noticeTimeGap;

    @ApiModelProperty("Pool归属团队")
    private String team;

    @ApiModelProperty("监控URL")
    private String urlKeys;

    @ApiModelProperty("创建时间")
    private Date createTime;

    @ApiModelProperty("修改时间")
    private Date updateTime;

    @ApiModelProperty("创建人")
    private Long creator;

    @ApiModelProperty("更新人")
    private Long modifier;

    @ApiModelProperty("描述")
    private String desc;

    @ApiModelProperty("@指定人或者@所有人")
    private String at;

    private Long userId;

    private String callPoolNameList;

    private String items;

//    private List<MonitorItemDTO> itemConfigs;

    private String url;

    private String paramJSON;

    @ApiModelProperty("url指定的飞书地址")
    private String nodeFeishuUrl;

    public static MonitorConfigDTO parseJson(String json){
        if (StringUtils.isNotEmpty(json)){
            return JSON.parseObject(json,MonitorConfigDTO.class);
        }
        return new MonitorConfigDTO();
    }


    public JSONArray saveItemJsonArray(){
        List list=Lists.newArrayList();
        List<MonitorItemDTO> itemDTOS= parseItemConfigs();
        for (MonitorItemDTO dto:itemDTOS){
            Map<String,Object> map= Maps.newHashMap();
            if (StringUtils.isNotEmpty(dto.getType())){
                map.put("type",dto.getType());
            }
            if (Objects.nonNull(dto.getPercentage())){
                map.put("percentage",dto.getPercentage());
            }
            if (Objects.nonNull(dto.getCount())){
                map.put("count",dto.getCount());
            }
            if (Objects.nonNull(dto.getTimeGap())){
                map.put("timeGap",dto.getTimeGap());
            }
            if (Objects.nonNull(dto.getNoticeTimeGap())){
                map.put("noticeTimeGap",dto.getNoticeTimeGap());
            }
            if (Objects.nonNull(dto.getTimeOut())){
                map.put("timeOut",dto.getTimeOut());
            }
            if (Objects.nonNull(dto.getKibanaLog())){
                map.put("kibanaLog",dto.getKibanaLog());
            }
            if (Objects.nonNull(dto.getContentUnescape())){
                map.put("contentUnescape",dto.getContentUnescape());
            }
            if (dto.getLogData() != null && CollectionUtils.isNotEmpty(dto.getLogData().getDataList())){
                // 验证log日志预警数据配置，要保存时
                dto.validateLogConfigDataBySave();
                map.put("logData",dto.getLogData());
            }
            list.add(map);
        }
        return JSON.parseArray(JSON.toJSONString(list));
    }

    public List<MonitorItemDTO> parseItemConfigs(){
        if (StringUtils.isNotEmpty(this.items)){
            return JSON.parseArray(this.items,MonitorItemDTO.class);
        }
        return Lists.newArrayList();
    }

    public String getAt() {
        //兼容中文逗号
        if (StringUtils.isNotEmpty(at)){
            return at.replace("，",",");
        }
        return at;
    }

    /**
     * 参数信息
     */
    private Map<String, String> paramMap;
}
