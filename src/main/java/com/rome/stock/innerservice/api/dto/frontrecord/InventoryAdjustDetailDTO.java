package com.rome.stock.innerservice.api.dto.frontrecord;

import com.rome.stock.innerservice.api.dto.allocation.WhSkuUnitDTO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotEmpty;
import java.math.BigDecimal;
import java.util.List;

/**
 * 仓库损益调整明细
 */
@Data
@EqualsAndHashCode
public class InventoryAdjustDetailDTO {

    @ApiModelProperty(value = "唯一主键")
    private Long id;

    @ApiModelProperty(value = "调整单id")
    private Long frontRecordId;

    @ApiModelProperty(value = "商品sku编号")
    private Long skuId;

    @ApiModelProperty(value = "商品sku编号")
    private String skuCode;

    @ApiModelProperty(value = "商品sku名称")
    private String skuName;

    @ApiModelProperty(value = "调整单位")
    private String unit;

    @ApiModelProperty(value = "单位编码")
    @NotEmpty(message="单位code不能为空")
    private String unitCode;

    @ApiModelProperty(value = "实际出库数量")
    private BigDecimal actualQty;

    @ApiModelProperty(value = "规格")
    private String skuStandard;

    @ApiModelProperty(value = "调整数量")
    private BigDecimal skuQty;

    @ApiModelProperty(value = "备注")
    private String remark;

    @ApiModelProperty(value = "损益类型(1,损 2，益)")
    private Integer adjustType;

    @ApiModelProperty(value = "库存类型(1,合格 2，不合格)")
    private Integer inventoryType;

    @ApiModelProperty(value = "换算比例")
    private BigDecimal scale;

    @ApiModelProperty(value = "基本单位数量")
    private BigDecimal basicQty;

    @ApiModelProperty(value = "基本单位")
    private String basicUnit;

    @ApiModelProperty(value = "剩余数量")
    private BigDecimal remainStockQty;

    @ApiModelProperty(value = "sku的单位信息")
    private List<WhSkuUnitDTO> skuUnitList;

    @ApiModelProperty(value = "生产日期")
    private String productDate;
}
