package com.rome.stock.innerservice.api.controller;

import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;

import com.rome.arch.core.clientobject.Response;
import com.rome.arch.core.exception.RomeException;
import com.rome.arch.util.controller.RomeController;
import com.rome.stock.innerservice.api.dto.cmkanban.FirstKanBanDTO;
import com.rome.stock.innerservice.api.dto.cmkanban.KbQueryParm;
import com.rome.stock.innerservice.api.dto.cmkanban.KbWarehouseDTO;
import com.rome.stock.innerservice.constant.ResponseMsg;
import com.rome.stock.innerservice.domain.service.CmKanBanService;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiResponse;
import lombok.extern.slf4j.Slf4j;

/**
 * 类KanBanController的实现描述：云商看板
 *
 * <AUTHOR> 2020/7/11 20:33
 */
@Slf4j
@RomeController
@RequestMapping("/stock/v1/cmKanBan")
@Api(tags={"云商看板"})
public class CmKanBanController {

    @Autowired
    private CmKanBanService cmKanBanService;

    private final  static String  DEFAULT_MY_PASSWORD = "lyfkb";

    @ApiOperation(value = "保存日志", nickname = "fixShopinventoryData", produces = MediaType.APPLICATION_JSON_VALUE)
    @ApiResponse(code = 200, message = "success")
    @RequestMapping(value = "/saveLog", method = RequestMethod.POST)
    public Response saveLog(@RequestParam("type") Integer type, @RequestParam("passswd") String passwd, @RequestBody String mylog){
        try {
            if(passwd.equals(DEFAULT_MY_PASSWORD)){
                cmKanBanService.saveLog(type, mylog);
                return ResponseMsg.SUCCESS.buildMsg("设置日志成功");
            }else{
                return ResponseMsg.FAIL.buildMsg("设置日志失败");
            }
        } catch (RomeException e) {
            log.error(e.getMessage(), e);
            return ResponseMsg.FAIL.buildMsg(e.getCode(),e.getMessage());
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return ResponseMsg.FAIL.buildMsg("500000",e.getMessage());
        }
    }

    @ApiOperation(value = "第一块看板查询", nickname = "getKanBanInfoForFirst", produces = MediaType.APPLICATION_JSON_VALUE)
    @ApiResponse(code = 200, message = "success")
    @RequestMapping(value = "/getKanBanInfoForFirst", method = RequestMethod.POST)
    public Response<List<FirstKanBanDTO>> getKanBanInfoForFirst(@RequestBody KbQueryParm kbQueryParm){
        try {
            List<FirstKanBanDTO> list = cmKanBanService.getKanBanInfoForFirst(kbQueryParm);
            return ResponseMsg.SUCCESS.buildMsg(list);
        } catch (RomeException e) {
            log.error(e.getMessage(), e);
            return ResponseMsg.FAIL.buildMsg(e.getCode(),e.getMessage());
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return ResponseMsg.FAIL.buildMsg("500000",e.getMessage());
        }
    }

    @ApiOperation(value = "第二块看板查询", nickname = "getKanBanInfoForSecond", produces = MediaType.APPLICATION_JSON_VALUE)
    @ApiResponse(code = 200, message = "success")
    @RequestMapping(value = "/getKanBanInfoForSecond", method = RequestMethod.POST)
    public Response<List<KbWarehouseDTO>> getKanBanInfoForSecond(@RequestBody KbQueryParm kbQueryParm){
        try {
            List<KbWarehouseDTO> list = cmKanBanService.getKanBanInfoForSecond(kbQueryParm);
            return ResponseMsg.SUCCESS.buildMsg(list);
        } catch (RomeException e) {
            log.error(e.getMessage(), e);
            return ResponseMsg.FAIL.buildMsg(e.getCode(),e.getMessage());
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return ResponseMsg.FAIL.buildMsg("500000",e.getMessage());
        }
    }


}
