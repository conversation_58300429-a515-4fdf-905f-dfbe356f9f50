package com.rome.stock.innerservice.api.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * wms库存核对流水记录表
 */
@Data
@NoArgsConstructor
@EqualsAndHashCode
public class WmsStockCheckFlowDTO extends Pagination{

    @ApiModelProperty("唯一主键")
    private Long id;

    @ApiModelProperty("实仓仓库ID")
    private Long realWarehouseId;

    @ApiModelProperty("工厂编号")
    private String factoryCode;

    @ApiModelProperty("实仓仓库编号")
    private String realWarehouseCode;

    @ApiModelProperty("实仓仓库名称")
    private String realWarehouseName;

    @ApiModelProperty("商品sku编码")
    private Long skuId;

    @ApiModelProperty("商品sku编码集合")
    private List<Long> skuIds;

    @ApiModelProperty("商品编码")
    private String skuCode;

    @ApiModelProperty("真实库存-实仓")
    private BigDecimal realQty;

    @ApiModelProperty("可用库存-实仓")
    private BigDecimal availableQty;

    @ApiModelProperty("锁定库存-实仓")
    private BigDecimal lockQty;

    @ApiModelProperty("质检库存")
    private BigDecimal qualityQty;

    @ApiModelProperty("不合格库存，注：一般是质检不合格库存")
    private BigDecimal unqualifiedQty;

    @ApiModelProperty("wms真实库存")
    private BigDecimal wmsRealQty;

    @ApiModelProperty("wms锁定库存")
    private BigDecimal wmsLockQty;

    @ApiModelProperty("wms质检库存")
    private BigDecimal wmsQualityQty;

    @ApiModelProperty("wms不合格库存，注：一般是质检不合格库存")
    private BigDecimal wmsUnqualifiedQty;

    @ApiModelProperty("类型")
    private Integer type;

    @ApiModelProperty("系统来源")
    private String sourceSystem;

    @ApiModelProperty("创建时间")
    private Date createTime;
    
    @ApiModelProperty("更新时间")
    private Date updateTime;

    @ApiModelProperty("wms真实库存,额外wms仓参与比较")
    private BigDecimal extWmsRealQty;

    @ApiModelProperty("wms质检库存,额外wms仓参与比较")
    private BigDecimal extWmsQualityQty;

    @ApiModelProperty("wms不合格库存，注：一般是质检不合格库存,额外wms仓参与比较")
    private BigDecimal extWmsUnqualifiedQty;
    
    @ApiModelProperty("商品名称")
    private String skuName;
    
    @ApiModelProperty(value = "主单位(商品单位)")
    private String unit;
    
    @ApiModelProperty("中台与wms差异，查询用 0无 1有")
    private Integer diffTypeWms;
    
    @ApiModelProperty("中台与额外sap系统差异，查询用 0无 1有")
    private Integer diffTypeExtWms;
}
