package com.rome.stock.innerservice.api.dto.groupbuy;

import java.math.BigDecimal;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
public class SplitSkuInfoForRm {

    @ApiModelProperty(value = "实仓ID", name = "realWarehouseId", required=true)
    private Long realWarehouseId;

    @ApiModelProperty(value = "实仓code", name = "realWarehouseCode" )
    private String realWarehouseCode;

    private Long skuId;
    private String skuCode;
    private String unitCode;
    private Long combineSkuId;
    private String combineSkuCode;
    private BigDecimal num;

}
