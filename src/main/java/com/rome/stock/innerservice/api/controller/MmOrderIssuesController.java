package com.rome.stock.innerservice.api.controller;

import com.alibaba.fastjson.JSON;
import com.rome.arch.core.clientobject.Response;
import com.rome.arch.util.controller.RomeController;
import com.rome.stock.common.dto.wms.OutOrderDto;
import com.rome.stock.innerservice.api.dto.wms.EntryOrderDto;
import com.rome.stock.innerservice.common.CoreKibanaLog;
import com.rome.stock.innerservice.domain.service.MmOrderIssuesService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiResponse;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;

/**
 * <AUTHOR>
 * @Description wms-MM模块 相关业务
 * @date 2020/7/15 9:33
 */
@Slf4j
@RomeController
@RequestMapping("/stock/v1/mMOrderIssues")
@Api(tags={"MM操作台相关接口"})
public class MmOrderIssuesController {

    @Autowired
    private MmOrderIssuesService mmOrderIssuesService;

    private final static String LOGTYPE = "stock-wms-mm";



    @ApiOperation(value = "出库单下发wms-MM模块", nickname = "pushDataToMM", produces = MediaType.APPLICATION_JSON_VALUE)
    @ApiResponse(code = 200, message = "success")
    @RequestMapping(value = "/pushOutOrderDataToMm", method = RequestMethod.POST)
    public Response mMOutOrderIssues(@RequestBody OutOrderDto outOrderDto){
        log.warn(CoreKibanaLog.getJobLog(LOGTYPE, "pushOutOrderDataToMm", "出库单下发wms-mm, 单号:" +outOrderDto.getDeliveryOrder().getDeliveryOrderCode(),JSON.toJSONString(outOrderDto)));
        return mmOrderIssuesService.pushOutOrderDataToMM(outOrderDto);
    }


    @ApiOperation(value = "入库单下发wms-MM模块", nickname = "pushDataToMM", produces = MediaType.APPLICATION_JSON_VALUE)
    @ApiResponse(code = 200, message = "success")
    @RequestMapping(value = "/pushInOrderDataToMm", method = RequestMethod.POST)
    public Response mMInOrderIssues(@RequestBody EntryOrderDto entryOrderDto){
        log.warn(CoreKibanaLog.getJobLog(LOGTYPE, "pushInOrderDataToMm", "入库单下发wms-mm, 单号:" +entryOrderDto.getEntryOrder().getEntryOrderCode(),JSON.toJSONString(entryOrderDto)));
        return  mmOrderIssuesService.pushInOrderDataToMM(entryOrderDto);
    }

    @ApiOperation(value = "取消MM出库单据", nickname = "cancelDeliveryOrder", produces = MediaType.APPLICATION_JSON_VALUE)
    @ApiResponse(code = 200, message = "success")
    @RequestMapping(value = "/cancelDeliveryOrder", method = RequestMethod.POST)
    public Response cancelDeliveryOrder(@RequestBody List<String> deliveryOrderCode, @RequestParam("orderType")Integer orderType){
        log.warn(CoreKibanaLog.getJobLog(LOGTYPE, "cancelDeliveryOrder", "取消wms-mm出库单据, 单号:" + JSON.toJSONString(deliveryOrderCode),deliveryOrderCode));
        return mmOrderIssuesService.cancelDeliveryOrder(deliveryOrderCode,orderType);
    }

}    
   