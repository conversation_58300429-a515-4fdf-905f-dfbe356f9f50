package com.rome.stock.innerservice.api.controller;

import com.github.pagehelper.PageInfo;
import com.rome.arch.core.clientobject.Response;
import com.rome.arch.core.exception.RomeException;
import com.rome.arch.util.controller.RomeController;
import com.rome.stock.innerservice.api.dto.RealWarehouse;
import com.rome.stock.innerservice.api.dto.RealWarehouseBatchRecord;
import com.rome.stock.innerservice.constant.ResponseMsg;
import com.rome.stock.innerservice.domain.service.RealWarehouseBatchRecordService;
import com.rome.stock.innerservice.remote.base.dto.StoreDTO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import io.swagger.annotations.ApiResponse;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.*;
import java.util.List;


@Slf4j
@RomeController
@RequestMapping("/stock/v1/real_warehouse_batch_record")
@Api(tags = {"检验单管理"})
public class RealWarehouseBatchRecordController {

    @Autowired
    private RealWarehouseBatchRecordService realWarehouseBatchRecordService;

    @ApiOperation(value = "根据工厂编码查询所有非门店仓", produces = MediaType.APPLICATION_JSON_VALUE)
    @ApiResponse(code = 200, message = "success", response = RealWarehouse.class)
    @RequestMapping(value = "/queryNotShopWarehouse", method = RequestMethod.GET)
    public Response<List<RealWarehouse>> queryNotShopWarehouse(@ApiParam(name = "factoryCode", value = "工厂编码") @RequestParam(required = false,value = "factoryCode") String factoryCode) {
        try {
            List<RealWarehouse> list = realWarehouseBatchRecordService.queryNotShopWarehouse(factoryCode);
            return ResponseMsg.SUCCESS.buildMsg(list);
        } catch (RomeException e) {
            log.error(e.getMessage(), e);
            return ResponseMsg.FAIL.buildMsg(e.getCode(), e.getMessage());
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return ResponseMsg.EXCEPTION.buildMsg();
        }
    }

    @ApiOperation(value = "根据工厂编码查询所有非门店仓和仓店一体", produces = MediaType.APPLICATION_JSON_VALUE)
    @ApiResponse(code = 200, message = "success", response = RealWarehouse.class)
    @RequestMapping(value = "/queryNotShopWarehouseAndIdenti", method = RequestMethod.GET)
    public Response<List<RealWarehouse>> queryNotShopWarehouseAndIdenti(@ApiParam(name = "factoryCode", value = "工厂编码") @RequestParam(required = false,value = "factoryCode") String factoryCode) {
        try {
            List<RealWarehouse> list = realWarehouseBatchRecordService.queryNotShopWarehouseAndIdenti(factoryCode);
            return ResponseMsg.SUCCESS.buildMsg(list);
        } catch (RomeException e) {
            log.error(e.getMessage(), e);
            return ResponseMsg.FAIL.buildMsg(e.getCode(), e.getMessage());
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return ResponseMsg.EXCEPTION.buildMsg();
        }
    }



    @ApiOperation(value = "获取所有非门店仓下的工厂", produces = MediaType.APPLICATION_JSON_VALUE)
    @ApiResponse(code = 200, message = "success", response = StoreDTO.class)
    @RequestMapping(value = "/queryNotShopFactory", method = RequestMethod.GET)
    public Response<List<StoreDTO>> queryNotShopFactory() {
        try {
            List<StoreDTO> list = realWarehouseBatchRecordService.queryNotShopFactory();
            return ResponseMsg.SUCCESS.buildMsg(list);
        } catch (RomeException e) {
            log.error(e.getMessage(), e);
            return ResponseMsg.FAIL.buildMsg(e.getCode(), e.getMessage());
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return ResponseMsg.EXCEPTION.buildMsg();
        }
    }

    @ApiOperation(value = "获取所有非门店仓和仓店一体下的工厂", produces = MediaType.APPLICATION_JSON_VALUE)
    @ApiResponse(code = 200, message = "success", response = StoreDTO.class)
    @RequestMapping(value = "/queryNotShopAndIdentiFactory", method = RequestMethod.GET)
    public Response<List<StoreDTO>> queryNotShopAndIdentiFactory() {
        try {
            List<StoreDTO> list = realWarehouseBatchRecordService.queryNotShopAndIdentiFactory();
            return ResponseMsg.SUCCESS.buildMsg(list);
        } catch (RomeException e) {
            log.error(e.getMessage(), e);
            return ResponseMsg.FAIL.buildMsg(e.getCode(), e.getMessage());
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return ResponseMsg.EXCEPTION.buildMsg();
        }
    }




    @ApiOperation(value = "查询列表信息", produces = MediaType.APPLICATION_JSON_VALUE)
    @ApiResponse(code = 200, message = "success", response = RealWarehouseBatchRecord.class)
    @RequestMapping(value = "/queryRealWarehouseBatch", method = RequestMethod.POST)
    public Response<PageInfo<RealWarehouseBatchRecord>> queryRealWarehouseBatch(@ApiParam(name = "RealWarehouseBatchRecord", value = "查询条件对象") @RequestBody RealWarehouseBatchRecord realWarehouseBatchRecord) {
        try {
            PageInfo<RealWarehouseBatchRecord> pageList = realWarehouseBatchRecordService.queryRealWarehouseBatch(realWarehouseBatchRecord);
            return ResponseMsg.SUCCESS.buildMsg(pageList);
        } catch (RomeException e) {
            log.error(e.getMessage(), e);
            return ResponseMsg.FAIL.buildMsg(e.getCode(), e.getMessage());
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return ResponseMsg.EXCEPTION.buildMsg();
        }
    }

    @ApiOperation(value = "更新批次号/生产日期", produces = MediaType.APPLICATION_JSON_VALUE)
    @ApiResponse(code = 200, message = "success", response = RealWarehouseBatchRecord.class)
    @RequestMapping(value = "/updateRealWarehouseBatch", method = RequestMethod.POST)
    public Response updateRealWarehouseBatch(@RequestParam(value = "id") Long id, @RequestParam(value = "batchCode") String batchCode, @RequestParam("userId") Long userId, @RequestParam(required = false, value = "productDate") String productDate) {
        try {
        	realWarehouseBatchRecordService.updateRealWarehouseBatch(id, batchCode, userId, productDate);
        	return ResponseMsg.SUCCESS.buildMsg();
        } catch (RomeException e) {
            log.error(e.getMessage(), e);
            return ResponseMsg.FAIL.buildMsg(e.getCode(), e.getMessage());
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return ResponseMsg.EXCEPTION.buildMsg();
        }
    }
    
    @ApiOperation(value = "取消（逻辑删除）外采收货单", produces = MediaType.APPLICATION_FORM_URLENCODED_VALUE)
    @ApiResponse(code = 200, message = "success", response = Response.class)
    @RequestMapping(value = "/cancelReceipt", method = RequestMethod.POST)
    public Response cancelReceipt(@RequestParam(value = "id") Long id, @RequestParam("userId") Long userId) {
        try {
            if(id == null || userId == null) {
            	return ResponseMsg.PARAM_ERROR.buildMsg();
            }
            realWarehouseBatchRecordService.cancelReceipt(id, userId);
            return ResponseMsg.SUCCESS.buildMsg();
        } catch (RomeException e) {
            log.error(e.getMessage(), e);
            return ResponseMsg.FAIL.buildMsg(e.getCode(), e.getMessage());
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return ResponseMsg.EXCEPTION.buildMsg();
        }
    }
    
}
