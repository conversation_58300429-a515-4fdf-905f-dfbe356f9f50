package com.rome.stock.innerservice.api.controller;

import com.alibaba.fastjson.JSON;
import com.rome.arch.core.clientobject.Response;
import com.rome.arch.core.exception.RomeException;
import com.rome.arch.util.controller.RomeController;
import com.rome.stock.common.enums.redis.RedisTime;
import com.rome.stock.innerservice.api.dto.WarehouseToBRecord;
import com.rome.stock.innerservice.api.dto.frontrecord.DispatchNoticeDTO;
import com.rome.stock.innerservice.api.dto.replenish.UpdateTmsCodeDTO;
import com.rome.stock.innerservice.api.dto.warehouserecord.BatchResultDTO;
import com.rome.stock.innerservice.common.RedisUtil;
import com.rome.stock.innerservice.common.ResCode;
import com.rome.stock.innerservice.constant.ResponseMsg;
import com.rome.stock.innerservice.domain.repository.SapInterfaceLogRepository;
import com.rome.stock.innerservice.domain.service.DispatchNoticeService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiResponse;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;

/**
 * 类DispatchNoticeController的实现描述：派车信息
 *
 * <AUTHOR> 2020/4/22 16:59
 */
@Slf4j
@RomeController
@RequestMapping("/stock/v1/dispatchNotice")
@Api(tags={"派车通知信息接口"})
public class DispatchNoticeController {

    @Autowired
    private DispatchNoticeService dispatchNoticeService;

    @Resource
    private SapInterfaceLogRepository sapInterfaceLogRepository;

    @Resource
    private RedisUtil redisUtil;

    @ApiOperation(value = "根据tms单号获取派车信息", nickname = "fixShopinventoryData", produces = MediaType.APPLICATION_JSON_VALUE)
    @ApiResponse(code = 200, message = "success")
    @RequestMapping(value = "/getDispatchInfoByTmsCode", method = RequestMethod.POST)
    public Response<DispatchNoticeDTO> getDispatchInfoByTmsCode(@RequestBody List<String> tmsCodeList){
        try {
            List<DispatchNoticeDTO> dto = dispatchNoticeService.getDispatchInfoByTmsCode(tmsCodeList);
            return ResponseMsg.SUCCESS.buildMsg(dto);
        } catch (RomeException e) {
            log.error(e.getMessage(), e);
            return ResponseMsg.FAIL.buildMsg(e.getCode(),e.getMessage());
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return ResponseMsg.FAIL.buildMsg("500000",e.getMessage());
        }
    }


    /**
     * 批量接收派车回调
     *
     * @return
     */
    @ApiOperation(value = "批量接收派车回调", nickname = "batchDispatchingNotify", produces = MediaType.APPLICATION_JSON_VALUE)
    @ApiResponse(code = 200, message = "success", response = WarehouseToBRecord.class)
    @RequestMapping(value = "/batchDispatchingNotify", method = RequestMethod.POST)
    public  Response<List<BatchResultDTO>> batchDispatchingNotify(@RequestBody List<DispatchNoticeDTO> dispatchNoticeList) {
        List<BatchResultDTO> resultList =  new ArrayList<>();
        try {
            if(CollectionUtils.isNotEmpty(dispatchNoticeList)){
                for (DispatchNoticeDTO dispatchNoticeDTO : dispatchNoticeList) {
                    BatchResultDTO dto = new BatchResultDTO();
                    dto.setRecordCode(dispatchNoticeDTO.getRecordCode());
                    dto.setStatus(false);
                    String message = "";
                    boolean isSucc = false;
                    String json = JSON.toJSONString(dispatchNoticeDTO);
                    try {
                        String uniqueKey = dispatchNoticeDTO.getRecordCode() + "_" + "batchDispatchingNotify";
                        if (!redisUtil.hasKey(uniqueKey)) {
                            redisUtil.set(uniqueKey, "111", RedisTime.ONE_MINUTES);
                            try{
                                dispatchNoticeService.handleDispatchNotice(dispatchNoticeDTO);
                                dto.setStatus(true);
                                isSucc = true;
                                message = "200";
                            }catch (RomeException e){
                                throw e;
                            }catch (Exception e){
                                throw e;
                            }finally {
                                redisUtil.del(uniqueKey);
                            }
                        } else {
                            log.info("出库单据【{}】正在处理派车中，请稍后再试。。。", dispatchNoticeDTO.getRecordCode());
                            throw new RomeException(ResCode.STOCK_ERROR_1017,"出库单据【{}】正在处理派车中，请稍后再试"+ dispatchNoticeDTO.getRecordCode());
                        }
                    }catch (Exception e){
                        message = e.getMessage();
                        log.error(e.getMessage(), e);
                    }finally {
                        dto.setMessage(message);
                        resultList.add(dto);
                        sapInterfaceLogRepository.saveCallBackInterFaceLog(2, dispatchNoticeDTO.getRecordCode(), "batchDispatchingNotify",
                                json, message, isSucc);
                    }
                }
            }
            return ResponseMsg.SUCCESS.buildMsg(resultList);
        } catch (RomeException e) {
            log.error(e.getMessage(), e);
            return Response.builderFail(e.getCode(),e.getMessage());
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return ResponseMsg.EXCEPTION.buildMsg();
        }
    }

    @ApiOperation(value = "批量更新派车单号", nickname = "updateWarehouseSaleTobTmsOrder", produces = MediaType.APPLICATION_JSON_VALUE)
    @ApiResponse(code = 200, message = "success")
    @RequestMapping(value = "/updateWarehouseTmsOrder", method = RequestMethod.POST)
    public Response<List<BatchResultDTO>> updateWarehouseTmsOrder(@RequestBody UpdateTmsCodeDTO updateTmsCodeDTO) {
        String message = "";
        boolean isSucc = false;
        String json = JSON.toJSONString(updateTmsCodeDTO);
        try {
            List<BatchResultDTO> resultList = dispatchNoticeService.updateRealWarehouseBatch(updateTmsCodeDTO.getRecordCodeList(), updateTmsCodeDTO.getTmsCode());
            isSucc = true;
            message = "200";
            //20201225 改为全部返回成功
            for (BatchResultDTO dto : resultList) {
                dto.setStatus(true);
            }
            return Response.builderSuccess(resultList);
        } catch (RomeException e) {
            message = e.getMessage();
            log.error(e.getMessage(), e);
            return Response.builderFail(e.getCode(), e.getMessage());
        } catch (Exception e) {
            message = e.getMessage();
            log.error(e.getMessage(), e);
            return Response.builderFail(ResCode.STOCK_ERROR_1003_DESC, ResCode.STOCK_ERROR_1003_DESC);
        }finally {
            sapInterfaceLogRepository.saveCallBackInterFaceLog(2, updateTmsCodeDTO.getTmsCode(), "updateWarehouseTmsOrder",
                    json, message, isSucc);
        }
    }
}
