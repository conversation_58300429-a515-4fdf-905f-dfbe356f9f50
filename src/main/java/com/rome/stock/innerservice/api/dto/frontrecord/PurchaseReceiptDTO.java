package com.rome.stock.innerservice.api.dto.frontrecord;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @description 采购收货单信息
 * @date 2020/11/3 15:42
 * @throw
 */
@Data
public class PurchaseReceiptDTO {

    @ApiModelProperty(value = "sku编码")
    private String skuCode;

    @ApiModelProperty(value = "物料凭证")
    private String wmsRecordCode;

    @ApiModelProperty(value = "生产日期")
    private String productDate;

    @ApiModelProperty(value = "仓库名称")
    private String factoryName;

    @ApiModelProperty(value = "收货时间")
    private String receiveDate;

    @ApiModelProperty(value = "商品实际入库数量")
    private BigDecimal actualQty;

    @ApiModelProperty(value = "入库单号")
    private String warehouseRecordCode;

    @ApiModelProperty(value = "批次号")
    private String bathCode;



}    
   