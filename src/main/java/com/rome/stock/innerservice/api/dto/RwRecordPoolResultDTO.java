package com.rome.stock.innerservice.api.dto;

import com.rome.stock.innerservice.infrastructure.dataobject.RwRecordPoolDetailResultDTO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

@Data
public class RwRecordPoolResultDTO {

    @ApiModelProperty("SO单号")
    private String frontRecordCode;
    @ApiModelProperty("DO单号,拆单后的pool表的单号")
    private String doCode;
    @ApiModelProperty("渠道编码")
    private String channelCode;
    @ApiModelProperty("仓库id")
    private Long realWarehouseId;
    @ApiModelProperty("仓库外部编码")
    private String outRealWarehouseCode;
    @ApiModelProperty("仓库编码")
    private String realWarehouseCode;
    @ApiModelProperty("工厂编码")
    private String factoryCode;
    @ApiModelProperty("商户ID")
    private Long merchantId;
    @ApiModelProperty("订单状态：0-初始状态,1-审核通过状态,2-取消订单,3-已锁定,4-已派车,5-确认派车,6-已发货,7-已部分派车,8-已部分发货,9-待处理,10-WMS已拉取,11-已出库,12-已入库,13-已部分出库,14-已部分入库,15-已完成,99-待合单,100-已合单")
    private Integer recordStatus;
    @ApiModelProperty("SO明细")
    private List<RwRecordPoolDetailResultDTO> rwRecordPoolDetails;
    @ApiModelProperty("拆合单md5指纹")
    private String mergeFingerprint;

}
