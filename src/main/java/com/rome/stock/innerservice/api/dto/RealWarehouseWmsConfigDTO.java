package com.rome.stock.innerservice.api.dto;

import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;

/**
 * 实仓wms配置
 */
@Data
@EqualsAndHashCode
public class RealWarehouseWmsConfigDTO extends Pagination {
    /**
     * 唯一id
     */
    private Long id;

    /**
     * 实体仓库id
     */
    private Long realWarehouseId;

    /**
     * 实体仓库编码
     */
    private String realWarehouseCode;

    /**
     * 工厂编码
     */
    private String factoryCode;

    /**
     * 实仓外部编码
     */
    private String realWarehouseOutCode;

    /**
     * wms编码
     */
    private Integer wmsCode;

    /**
     * wms名称
     */
    private String wmsName;

    /**
     * 创建人
     */
    private Long creator;

    /**
     * 更新人
     */
    private Long modifier;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     *下发wms的状态
     */
    private Integer status;

    /**
     * 一件代发类型：0  普通， 1 一件代发-TOC、 2 一件代发-商家、3 一件代发-跨境
     */
    private Integer purchaseType;

    /**
     * 调用接口  0 默认 无、 1  采购单自动创建、 2 入库单创建
     */
    private Integer reqType;



    private Integer purchaseOrderType;
    /**
     * 逆向调用接口:0.默认无、1.采购单自动创建、2.入库单创建、3.退货创建
     */
    private Integer returnReqType;

    /**
     * 退供类型：1：一件代发-跨境，2：一件代发-商家
     */
    private Integer refundType;

    /**
     * 供应商代码
     */
    private String supplierCode;

    /**
     * 供应商名称
     */
    private String supplierName;

    /**
     * 运输供应商代码
     */
    private String logisticsCode;

    /**
     * 运输供应商名称
     */
    private String logisticsName;


    /**
     * 批次开启状态
     */
    private Integer batchStatus;
}
