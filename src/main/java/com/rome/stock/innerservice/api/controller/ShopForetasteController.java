package com.rome.stock.innerservice.api.controller;

import com.alibaba.fastjson.JSON;
import com.github.pagehelper.PageInfo;
import com.rome.arch.core.clientobject.Response;
import com.rome.arch.core.exception.RomeException;
import com.rome.arch.util.controller.RomeController;
import com.rome.stock.innerservice.api.dto.RealWarehouse;
import com.rome.stock.innerservice.api.dto.WarehouseToBRecord;
import com.rome.stock.innerservice.api.dto.frontrecord.ShopAdjustRecordDTO;
import com.rome.stock.innerservice.api.dto.warehouserecord.CancelRecordDTO;
import com.rome.stock.innerservice.api.dto.warehouserecord.BatchResultDTO;
import com.rome.stock.innerservice.api.dto.warehouserecord.OutWarehouseRecordDTO;
import com.rome.stock.innerservice.common.ResCode;
import com.rome.stock.innerservice.constant.ResponseMsg;
import com.rome.stock.innerservice.domain.repository.SapInterfaceLogRepository;
import com.rome.stock.innerservice.domain.service.ShopForetasteService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import io.swagger.annotations.ApiResponse;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;

/**
 * 门店试吃
 */
@Slf4j
@RomeController
@RequestMapping("/stock/v1/shop_foretaste")
@Api(tags={"门店试吃"})
public class ShopForetasteController {

    @Autowired
    private ShopForetasteService shopForetasteService;

    @Resource
    private SapInterfaceLogRepository sapInterfaceLogRepository;

    @ApiOperation(value = "创建门店试吃调整单", nickname = "ShopForetasteRecord", produces = MediaType.APPLICATION_JSON_VALUE)
    @ApiResponse(code = 200, message = "success", response = WarehouseToBRecord.class)
    @RequestMapping(value = "/addShopForetasteRecord", method = RequestMethod.POST)
    public Response addShopForetasteRecord(@ApiParam(name = "outWarehouseRecordDTO", value = "门店试吃调整单") @RequestBody OutWarehouseRecordDTO outWarehouseRecordDTO) {
        try {
            shopForetasteService.addShopForetasteRecord(outWarehouseRecordDTO);
            return ResponseMsg.SUCCESS.buildMsg();
        } catch (RomeException e) {
            log.error(e.getMessage(), e);
            return Response.builderFail(e.getCode(),e.getMessage());
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return Response.builderFail(ResCode.STOCK_ERROR_1003_DESC,e.getMessage());
        }
    }

    @ApiOperation(value = "批量取消门店试吃", nickname = "cancelBatchShopForetaste", produces = MediaType.APPLICATION_JSON_VALUE)
    @ApiResponse(code = 200, message = "success")
    @RequestMapping(value = "/cancelBatchShopForetaste", method = RequestMethod.POST)
    public Response cancelBatchShopForetaste(@RequestBody List<CancelRecordDTO> list){
        List<BatchResultDTO> resultList =  new ArrayList<>();
        try {
            if(CollectionUtils.isNotEmpty(list)){
                for (CancelRecordDTO cancelRecordDTO : list) {
                    BatchResultDTO dto = new BatchResultDTO();
                    dto.setRecordCode(cancelRecordDTO.getRecordCode());
                    dto.setStatus(false);
                    String message = "";
                    boolean isSucc = false;
                    try {
                        //门店试吃没有强制取消
                        shopForetasteService.cancelBatchShopForetaste(cancelRecordDTO.getRecordCode());
                        dto.setStatus(true);
                        isSucc = true;
                        message = "200";
                    }catch (Exception e){
                        message=e.getMessage();
                        log.error(e.getMessage(), e);
                    }finally {
                        dto.setMessage(message);
                        resultList.add(dto);
                        sapInterfaceLogRepository.saveCallBackInterFaceLog(1, dto.getRecordCode(), "cancelBatchShopForetaste",
                                JSON.toJSONString(cancelRecordDTO), message, isSucc);
                    }
                }
            }
            return ResponseMsg.SUCCESS.buildMsg(resultList);
        } catch (RomeException e) {
            log.error(e.getMessage(), e);
            return Response.builderFail(e.getCode(),e.getMessage());
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return ResponseMsg.EXCEPTION.buildMsg();
        }
    }


    @ApiOperation(value = "自定义查询试吃单", nickname = "ShopForetasteRecord", produces = MediaType.APPLICATION_JSON_VALUE)
    @ApiResponse(code = 200, message = "success", response = List.class)
    @RequestMapping(value = "/condition", method = RequestMethod.POST)
    public Response<PageInfo<ShopAdjustRecordDTO>> getShopAdjustRecordDTO(@ApiParam(name = "ShopAdjustRecordDTO", value = "试吃单dto")@RequestBody ShopAdjustRecordDTO virDto) {
        try {
            PageInfo<ShopAdjustRecordDTO> pageList = shopForetasteService.getShopForetasteRecord(virDto);
            return Response.builderSuccess(pageList);
        } catch (RomeException e) {
            log.error(e.getMessage(), e);
            return ResponseMsg.EXCEPTION.buildMsg(e.getCode(), e.getMessage());
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return ResponseMsg.EXCEPTION.buildMsg(ResCode.STOCK_ERROR_1003, ResCode.STOCK_ERROR_1003_DESC);
        }
    }

    @ApiOperation(value = "根据单号查询门店调整单", nickname = "getRecordByRecordId", produces = MediaType.APPLICATION_JSON_VALUE)
    @ApiResponse(code = 200, message = "success")
    @RequestMapping(value = "/getRecordByRecordId", method = RequestMethod.GET)
    public Response getRecordByRecordId(@RequestParam("recordId") String recordId) {
        try {
            ShopAdjustRecordDTO salesReturnRecordPageInfo = shopForetasteService.getRecordByRecordId(recordId);
            return ResponseMsg.SUCCESS.buildMsg(salesReturnRecordPageInfo);
        } catch (RomeException e) {
            log.error(e.getMessage(), e);
            return ResponseMsg.EXCEPTION.buildMsg(e.getCode(), e.getMessage());
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return ResponseMsg.EXCEPTION.buildMsg();
        }
    }

    @ApiOperation(value = "查询所有门店", nickname = "queryShopList", produces = MediaType.APPLICATION_JSON_VALUE)
    @ApiResponse(code = 200, message = "success")
    @RequestMapping(value = "/queryShopList", method = RequestMethod.GET)
    public Response queryShopList(){
        try {
            List<RealWarehouse> warehouses = shopForetasteService.queryShopList();
            return ResponseMsg.SUCCESS.buildMsg(warehouses);
        } catch (RomeException e) {
            log.error(e.getMessage(), e);
            return ResponseMsg.FAIL.buildMsg(e.getCode(),e.getMessage());
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return ResponseMsg.FAIL.buildMsg("500000",e.getMessage());
        }
    }

}
