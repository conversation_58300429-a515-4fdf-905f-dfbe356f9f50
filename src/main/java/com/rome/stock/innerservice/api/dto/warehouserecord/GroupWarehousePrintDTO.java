package com.rome.stock.innerservice.api.dto.warehouserecord;

import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;
import java.util.List;

@Data
@EqualsAndHashCode
public class GroupWarehousePrintDTO {

	private Long id;
	/**
	 * 团购出库单号
	 */
	private String recordCode;

	/**
	 * 仓库名称
	 */
	private String warehouseName;
	/**
	 * 所属客户code
	 */
	private String userCode;

	/**
	 * 收货地址
	 */
	private String address;
	/**
	 * 外部系统数据创建时间:下单时间
	 */
	private Date outCreateTime;
	/**
	 * 客户电话
	 */
	private String mobile;
	/**
	 * 交货日期
	 */
	private Date deliveryTime;
	/**
	 * 收货时间
	 */
	private Date receiverTime;

	private Date createTime;

	private Date updateTime;

	/**
	 * 整箱计件数
	 */
	private Integer foodCount;

	/**
	 * 非食品计件数
	 */
	private Integer unFoodCount;

	/**
	 * 商品详情
	 */
	private List<GroupWarehouseDetailListPrintDTO> details;


}
