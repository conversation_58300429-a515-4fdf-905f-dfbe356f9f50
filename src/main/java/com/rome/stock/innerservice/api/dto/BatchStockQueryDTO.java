/**
 * Filename BatchStockQueryDTO.java
 * Company 上海来伊份科技有限公司。
 * <AUTHOR>
 * @version 
 */
package com.rome.stock.innerservice.api.dto;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 批次库存，后台查询
 * <AUTHOR>
 * @since 2022-1-3 17:53:12
 */
@Data
@EqualsAndHashCode
public class BatchStockQueryDTO extends Pagination{
	
	/**
     * 包含count查询
     */
	@ApiModelProperty(value="是否统计条数")
    private boolean count = true;
	
	/**
     * 查询排序，是按日期降序
     */
	@ApiModelProperty(value="查询排序，是按日期降序")
	private boolean sortDesc = true;

    @ApiModelProperty("唯一主键")
    private Long id;

    @ApiModelProperty("实仓仓库ID")
    private Long realWarehouseId;

    @ApiModelProperty("工厂编号")
    private String factoryCode;

    @ApiModelProperty("实仓仓库编号")
    private String realWarehouseCode;

    @ApiModelProperty("实仓仓库名称")
    private String realWarehouseName;

    @ApiModelProperty("商品sku编码")
    private Long skuId;

    @ApiModelProperty("商品sku编码集合")
    private List<Long> skuIds;

    @ApiModelProperty("商品编码")
    private String skuCode;
    
    @ApiModelProperty("商品名称")
    private String skuName;
    
    @ApiModelProperty(value = "主单位(商品单位)")
    private String unit;
    
    @ApiModelProperty(value = "数量")
    private BigDecimal skuQty;

    @ApiModelProperty(value = "锁定数量")
    private BigDecimal lockQty;

    @ApiModelProperty(value = "批次编码-批次号")
    private String batchCode;
    
    @ApiModelProperty(value = "生产日期")
    private Date productionDate;

    @ApiModelProperty(value = "入库日期")
    private Date entryDate;

    @ApiModelProperty(value = "保质期天数")
    private Integer totalShelfLifeDay;
    
    @ApiModelProperty(value = "有效期")
    private Date validityDate;
    
    @ApiModelProperty(value = "剩余到期天数")
    private Integer remainShelfLifeDay;
    
    @ApiModelProperty(value = "不可销售日期")
    private Date noSaleDate;
    
    @ApiModelProperty(value = "最佳销售日期")
    private Date bestSaleDate;
    
    @ApiModelProperty(value = "最晚发货日期")
    private Date latestDeliveryDate;
    
    @ApiModelProperty("查询,实仓仓库ID列表")
    private List<Long> realWarehouseIds;

    @ApiModelProperty("创建时间")
    private Date createTime;
    
    @ApiModelProperty("更新时间")
    private Date updateTime;
    
    @ApiModelProperty("库存查询类型，0-小于0，1-等于0，2大于0")
    private Integer type;
	
}
