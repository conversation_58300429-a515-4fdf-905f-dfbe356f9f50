package com.rome.stock.innerservice.api.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

@Data
@EqualsAndHashCode
public class DeliverRelation extends Pagination{
    /**
     * 单据子表id
     */
    @ApiModelProperty(value = "单据子表id")
    private Long id;
    /**
     * Z工厂code
     */
    @ApiModelProperty(value = "Z工厂code")
    private String factoryCode;
    /**
     * Z工厂name
     */
    @ApiModelProperty(value = "Z工厂name")
    private String factoryName;
    /**
     * 发货工厂code
     */
    @ApiModelProperty(value = "发货工厂code")
    private String deliverFactoryCode;
    /**
     * 发货工厂name
     */
    @ApiModelProperty(value = "发货工厂code")
    private String deliverFactoryName;
    /**
     * 创建人
     */
    @ApiModelProperty(value = "创建人")
    private Long creator;
}
