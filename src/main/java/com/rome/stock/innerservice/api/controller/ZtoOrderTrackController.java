package com.rome.stock.innerservice.api.controller;

import com.rome.arch.core.clientobject.Response;
import com.rome.arch.core.exception.RomeException;
import com.rome.arch.util.controller.RomeController;
import com.rome.stock.innerservice.api.dto.wms.ZtoOrderTrackQueryParam;
import com.rome.stock.innerservice.api.dto.wms.ZtoOrderTrackQueryResult;
import com.rome.stock.innerservice.common.StringUtils;
import com.rome.stock.innerservice.constant.ResponseMsg;
import com.rome.stock.innerservice.domain.service.XTStockService;
import com.rome.stock.innerservice.domain.service.ZtoOrderTrackService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import io.swagger.annotations.ApiResponse;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;

import java.util.List;

@Slf4j
@RomeController
@RequestMapping("/stock/zto/orderTrack")
@Api(tags={"中通云仓物流轨迹"})
public class ZtoOrderTrackController {

	@Autowired
	private ZtoOrderTrackService ztoOrderTrackService;

	@ApiOperation(value = "物流轨迹查询", produces = MediaType.APPLICATION_JSON_VALUE)
	@ApiResponse(code = 200, message = "success", response = String.class)
	@RequestMapping(value = "/query", method = RequestMethod.POST)
	public Response<List<ZtoOrderTrackQueryResult>> query(@ApiParam(name = "param", value = "查询参数") @RequestBody ZtoOrderTrackQueryParam param) {
		if (StringUtils.isBlank(param.getLogisticsCode())) {
			return ResponseMsg.PARAM_ERROR.buildMsg();
		}
		try {
			List<ZtoOrderTrackQueryResult>  result = ztoOrderTrackService.query(param);
			return ResponseMsg.SUCCESS.buildMsg(result);
		} catch (RomeException e) {
			log.error(e.getMessage(), e);
			return ResponseMsg.FAIL.buildMsg(e.getCode(), e.getMessage());
		} catch (Exception e) {
			log.error(e.getMessage(), e);
			return ResponseMsg.EXCEPTION.buildMsg();
		}
	}
}
