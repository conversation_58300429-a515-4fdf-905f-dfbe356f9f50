package com.rome.stock.innerservice.api.dto.frontrecord;

import java.math.BigDecimal;
import java.util.Date;

import org.springframework.format.annotation.DateTimeFormat;

import com.fasterxml.jackson.annotation.JsonFormat;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 类WmsBatchAdjustDTO的实现描述：仓库库存调整
 *
 * <AUTHOR> 2022/7/12 15:22
 */
@Data
public class WmsBatchAdjustDTO {

    @ApiModelProperty(value = "工厂编码")
    private String factoryCode;

    @ApiModelProperty(value = "仓库编码")
    private String outWarehouseCode;

    @ApiModelProperty(value = "外部单据编码")
    private String outRecordCode;

    @ApiModelProperty(value = "商品编码")
    private String skuCode;

    @ApiModelProperty(value = "批次编号")
    private String batchCode;

    @ApiModelProperty(value = "生产日期(YYYY-MM-DD)")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    private Date productDate;

    @ApiModelProperty(value = "商品数量")
    private BigDecimal skuQty;

    private BigDecimal boxQty;

    private BigDecimal mixQty;

    private Integer boxType;

    private Long realWarehouseId;

    private String recordCode;

    private Long skuId;


    @ApiModelProperty(value = "单位")
    private String unitCode;

    @ApiModelProperty(value = "损益类型(1,损 2,益)")
    private Integer adjustType;
}
