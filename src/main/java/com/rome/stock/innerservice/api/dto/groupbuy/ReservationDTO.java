package com.rome.stock.innerservice.api.dto.groupbuy;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.rome.arch.core.dto.DTO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.util.Date;
import java.util.List;

/**
 * 预约单DTO对象
 */
@Data
public class ReservationDTO extends DTO {
    /**
     * 主键
     */
    @ApiModelProperty(value="主键",hidden = true)
    private Long id;

    /**
     * 渠道编码
     */
    @ApiModelProperty(value="渠道编码")
    @NotBlank(message = "渠道编码不能为空")
    private String channelCode;

    /**
     * 单据编号
     */
    @ApiModelProperty(value="单据编号",hidden = true)
    private String recordCode;

    /**
     * 单据类型
     */
    @ApiModelProperty(value="单据类型",hidden = true)
    private Integer recordType;

    /**
     * 单据状态   19--部分锁定 20--全部锁定  23--已转调拨单 24--待发货(已创建DO) 6--已发货
     */
    @ApiModelProperty(value="单据状态0---初始化 2--取消订单   19--部分锁定 20--全部锁定  23--已转调拨单 24--待发货(已创建DO) 10--已发货",hidden = true)
    private Integer recordStatus;

    /**
     * 是否可分配 1--可分配 2--不可分配
     */
    @ApiModelProperty(value="是否可分配 1--可分配 2--不可分配",hidden = true)
    private Integer isAssigned;

    /**
     * 实仓ID
     */
    @ApiModelProperty(value="实仓ID",hidden = true)
    private Long realWarehouseId;

    @ApiModelProperty(value="实仓编码",hidden = true)
    private String realWarehouseCode;

    @ApiModelProperty(value="实仓工厂编码",hidden = true)
    private String realFactoryCode;

    /**
     * 虚仓ID
     */
    @ApiModelProperty(value="虚仓ID",hidden = true)
    private Long virtualWarehouseId;

    @ApiModelProperty(value="虚仓编码",hidden = true)
    private String virtualWarehouseCode;

    @ApiModelProperty(value="虚仓工厂编码",hidden = true)
    private String virtualFactoryCode;


    /**
     * 外部单号
     */
    @ApiModelProperty(value="外部单号")
    @NotBlank(message = "外部单号不能为空")
    private String outRecordCode;

    /**
     * 业务类型：1-DIY　２-普通订单 3--卡订单
     */
    @ApiModelProperty(value="业务类型：1-DIY　２-普通订单 3--卡订单")
    @NotNull(message = "业务类型不能为空")
    private Integer businessType;

    /**
     * 手机号
     */
    @ApiModelProperty(value="手机号")
    private String mobile;

    /**
     * 外部订单下单时间
     */
    @ApiModelProperty(value="外部订单下单时间")
    @NotNull(message = "外部订单创建时间不能为空")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    private Date outCreateTime;

    /**
     * 用户code
     */
    @ApiModelProperty(value="用户code")
    private String userCode;

    @ApiModelProperty(value="预计收货时间")
    @NotNull(message = "预计收货时间不能为空")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    private Date expectReceiveDateStart;

    /**
     * 备注
     */
    @ApiModelProperty(value="备注")
    private String remark;

    @ApiModelProperty(value="预约单明细集合")
    @NotNull
    private List<ReservationDetailDTO> reservationDetails;


  //**************地址相关信息*************************
    /**
     * 省
     */
    @ApiModelProperty(value = "省", required = true)
    @NotBlank(message ="省不能为空")
    private String province;


    /**
     * 省编码
     */
    @ApiModelProperty(value = "省编码", required = true)
    @NotBlank(message ="省编码不能为空")
    private String provinceCode;

    /**
     * 市
     */
    @ApiModelProperty(value = "市", required = true)
    @NotBlank(message ="市不能为空")
    private String city;

    /**
     * 市编码
     */
    @ApiModelProperty(value = "市编码", required = true)
    @NotBlank(message ="市编码不能为空")
    private String cityCode;

    /**
     * 区县
     */
    @ApiModelProperty(value = "区县", required = true)
    private String county;

    /**
     * 区县编码
     */
    @ApiModelProperty(value = "区县编码", required = true)
    private String countyCode;

    /**
     * 详细地址
     */
    @ApiModelProperty(value = "详细地址", required = true)
    @NotBlank(message ="详细地址不能为空")
    private String address;

    /**
     *收货人姓名
     */
    @ApiModelProperty(value = "收货人姓名", required = true)
    private String name;


}
