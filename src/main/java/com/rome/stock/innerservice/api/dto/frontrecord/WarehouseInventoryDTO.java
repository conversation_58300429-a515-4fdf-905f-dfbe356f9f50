package com.rome.stock.innerservice.api.dto.frontrecord;

import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.util.Date;
import java.util.List;

@Data
public class WarehouseInventoryDTO {

    /**
     * 唯一主键
     */
    private Integer id;

    /**
     * 单据编号
     */
    private String recordCode;

    /**
     * 仓库编码
     */
    private String realWarehouseCode;

    /**
     * 仓库名称
     */
    private String realWarehouseName;
    /**
     * 业务类型：1-抽盘，2-全盘
     */
    private Integer businessType;

    /**
     * 单据类型
     */
    private Integer recordType;

    /**
     * 单据状态
     */
    private Integer recordStatus;

    /**
     * 盘点备注
     */
    private String remark;

    /**
     * 单据状态审核原因
     */
    private String recordStatusReason;

    /**
     * 实体仓库id
     */
    private Long realWarehouseId;

    /**
     * 盘点管理单id
     */
    private Long inventoryStartId;

  /**
     * 外部系统单据编号
     */
    @NotBlank
    private String outRecordCode;

    /**
     * 工厂编码
     */
    @NotBlank(message = "工厂编码不能为空")
    private String factoryCode;

    /**
     * 工厂名称
     */
    private String factoryName;

    /**
     * 盘点时间
     */
    @NotNull
    private Date outCreateTime;


    /**
     * 外部仓库编码
     * */
    @NotBlank(message = "仓库编码不能为空")
    private String realWarehouseOutCode;

    /**
     * 仓库盘点单明细表
     * */
    @Size(min = 1)
    private List<WarehouseInventoryDetailDTO> frontRecordDetails;

    /**
     * 库存商品code集合
     */
    private List<String> skuCodes;
    /**
     * 更新人
     */
    private Long modifier;

}
