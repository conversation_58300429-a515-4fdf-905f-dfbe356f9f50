package com.rome.stock.innerservice.api.controller;

import com.rome.stock.innerservice.api.dto.frontrecord.WarehouseAssembleSpitDetailDTO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;

import com.github.pagehelper.PageInfo;
import com.rome.arch.core.clientobject.Response;
import com.rome.arch.core.exception.RomeException;
import com.rome.arch.util.controller.RomeController;
import com.rome.stock.innerservice.api.dto.frontrecord.WarehouseAssembleSpitPageDTO;
import com.rome.stock.innerservice.api.dto.frontrecord.WarehouseAssembleSpitRecordDTO;
import com.rome.stock.innerservice.common.ResCode;
import com.rome.stock.innerservice.domain.service.WarehouseAssembleSpitService;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import io.swagger.annotations.ApiResponse;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;

/**
 * 仓库加工
 */
@Slf4j
@RequestMapping("/stock/v1/warehouseAsspit")
@RomeController
@Api(tags={"仓库加工反拆"})
public class WarehouseAssembleSpitController {
    @Autowired
    private WarehouseAssembleSpitService warehouseAssembleSpitService;

    @ApiOperation(value = "创建仓库组装加工单", nickname = "addWarehouseAssembleRecord", produces = MediaType.APPLICATION_JSON_VALUE)
    @ApiResponse(code = 200, message = "success", response = Response.class)
    @RequestMapping(value = "/front_record/addWarehouseAssembleRecord", method = RequestMethod.POST)
    public Response addWarehouseAssembleRecord(@ApiParam(name = "frontRecord", value = "dto") @RequestBody @Validated WarehouseAssembleSpitRecordDTO frontRecord) {
        try {

            warehouseAssembleSpitService.addWarehouseAssembleRecord(frontRecord);
            return Response.builderSuccess("");
        } catch (RomeException e) {
            log.error(e.getMessage(), e);
            return Response.builderFail(e.getCode(),e.getMessage());
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return Response.builderFail(ResCode.STOCK_ERROR_1003_DESC,ResCode.STOCK_ERROR_1003_DESC);
        }
    }

    @ApiOperation(value = "创建仓库反拆加工单", nickname = "addWarehouseSpitRecord", produces = MediaType.APPLICATION_JSON_VALUE)
    @ApiResponse(code = 200, message = "success", response = Response.class)
    @RequestMapping(value = "/front_record/addWarehouseSpitRecord", method = RequestMethod.POST)
    public Response addWarehouseSpitRecord(@ApiParam(name = "frontRecord", value = "dto") @RequestBody @Validated WarehouseAssembleSpitRecordDTO frontRecord) {
        try {

            warehouseAssembleSpitService.addWarehouseSpitRecord(frontRecord);
            return Response.builderSuccess("");
        } catch (RomeException e) {
            log.error(e.getMessage(), e);
            return Response.builderFail(e.getCode(),e.getMessage());
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return Response.builderFail(ResCode.STOCK_ERROR_1003_DESC,ResCode.STOCK_ERROR_1003_DESC);
        }
    }

    @ApiOperation(value = "查询仓库加工列表", nickname = "query_warehouse_assemble_spit_list", produces = MediaType.APPLICATION_JSON_VALUE)
    @ApiResponse(code = 200, message = "success", response = Response.class)
    @RequestMapping(value = "/front_record/queryWarehouseAssembleSpitList", method = RequestMethod.POST)
    public Response queryWarehouseAssembleSpitList(@ApiParam(name = "frontRecord", value = "dto") @RequestBody WarehouseAssembleSpitPageDTO frontRecord) {
        try {
            PageInfo<WarehouseAssembleSpitPageDTO> personPageInfo = warehouseAssembleSpitService.queryWarehouseAssembleSpitList(frontRecord);
            return Response.builderSuccess(personPageInfo);
        } catch (RomeException e) {
            log.error(e.getMessage(), e);
            return Response.builderFail(e.getCode(),e.getMessage());
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return Response.builderFail(ResCode.STOCK_ERROR_1003_DESC,ResCode.STOCK_ERROR_1003_DESC);
        }
    }

    @ApiOperation(value = "查询仓库加工详情列表", nickname = "query_warehouse_assemble_spit_detail_list", produces = MediaType.APPLICATION_JSON_VALUE)
    @ApiResponse(code = 200, message = "success", response = Response.class)
    @RequestMapping(value = "/front_record/queryWarehouseAssembleSpitDetailList", method = RequestMethod.POST)
    public Response<List<WarehouseAssembleSpitDetailDTO>> queryWarehouseAssembleSpitDetailList(@ApiParam(name = "前置单id", value = "frontRecordId") @RequestParam Long frontRecordId) {
        try {
            List<WarehouseAssembleSpitDetailDTO> result = warehouseAssembleSpitService.queryWarehouseAssembleSpitDetailList(frontRecordId);
            return Response.builderSuccess(result);
        } catch (RomeException e) {
            log.error(e.getMessage(), e);
            return Response.builderFail(e.getCode(),e.getMessage());
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return Response.builderFail(ResCode.STOCK_ERROR_1003_DESC,ResCode.STOCK_ERROR_1003_DESC);
        }
    }


}
