package com.rome.stock.innerservice.api.controller;

import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;

import com.github.pagehelper.PageInfo;
import com.rome.arch.core.clientobject.Response;
import com.rome.arch.core.exception.RomeException;
import com.rome.arch.util.controller.RomeController;
import com.rome.stock.common.dto.wdt.ChannelWdtVwRelationDTO;
import com.rome.stock.common.dto.wdt.WdtChannelStockDTO;
import com.rome.stock.common.dto.wdt.WdtStockSyncParmDTO;
import com.rome.stock.innerservice.common.ResCode;
import com.rome.stock.innerservice.domain.service.WdtErpStockSyncService;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiResponse;
import lombok.extern.slf4j.Slf4j;

/**
 * 类WdtErpStockSyncController的实现描述：旺店通erp库存同步
 *
 * <AUTHOR> 2020/4/23 16:51
 */
@Slf4j
@RomeController
@RequestMapping("/stock/v1/wdtErp_stock_sync")
@Api(tags={"旺店通erp库存同步"})
public class WdtErpStockSyncController {

    @Autowired
    private WdtErpStockSyncService wdtErpStockSyncService;



    @ApiOperation(value = "获取关联配置列表", nickname = "listAllConfig", produces = MediaType.APPLICATION_JSON_VALUE)
    @ApiResponse(code = 200, message = "success", response = List.class)
    @RequestMapping(value = "/listAllConfig", method = RequestMethod.POST)
    public Response<List<ChannelWdtVwRelationDTO>> listAllConfig() {
        try {
            List<ChannelWdtVwRelationDTO> allConfig = wdtErpStockSyncService.listAllConfig();
            return Response.builderSuccess(allConfig);
        } catch (RomeException e) {
            log.error(e.getMessage(), e);
            return Response.builderFail(e.getCode(),e.getMessage());
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return Response.builderFail(ResCode.STOCK_ERROR_1003,ResCode.STOCK_ERROR_1003_DESC);
        }
    }

    @ApiOperation(value = "根据参数获取渠道下库存数据", nickname = "getChangeStockByParm", produces = MediaType.APPLICATION_JSON_VALUE)
    @ApiResponse(code = 200, message = "success", response = List.class)
    @RequestMapping(value = "/getChannelStockByParm", method = RequestMethod.POST)
    public Response<PageInfo<WdtChannelStockDTO>> getChannelStockByParm(@RequestBody WdtStockSyncParmDTO parm) {
        try {
        	PageInfo<WdtChannelStockDTO> stockList = wdtErpStockSyncService.getChannelStockByParm(parm);
            return Response.builderSuccess(stockList);
        } catch (RomeException e) {
            log.error(e.getMessage(), e);
            return Response.builderFail(e.getCode(),e.getMessage());
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return Response.builderFail(ResCode.STOCK_ERROR_1003,ResCode.STOCK_ERROR_1003_DESC);
        }
    }

    @ApiOperation(value = "清除缓存", nickname = "delConfigCache", produces = MediaType.APPLICATION_JSON_VALUE)
    @ApiResponse(code = 200, message = "success", response = List.class)
    @RequestMapping(value = "/delConfigCache", method = RequestMethod.GET)
    public Response delCache() {
        try {
            wdtErpStockSyncService.delConfigCache();
            return Response.builderSuccess("");
        } catch (RomeException e) {
            log.error(e.getMessage(), e);
            return Response.builderFail(e.getCode(),e.getMessage());
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return Response.builderFail(ResCode.STOCK_ERROR_1003,ResCode.STOCK_ERROR_1003_DESC);
        }
    }


    @ApiOperation(value = "推送数据至wdtERP", nickname = "delConfigCache", produces = MediaType.APPLICATION_JSON_VALUE)
    @ApiResponse(code = 200, message = "success", response = List.class)
    @RequestMapping(value = "/pushStockToWdtErp", method = RequestMethod.POST)
    public Response pushStockToWdtErp(@RequestParam("syncType") Integer syncType, @RequestBody List<WdtChannelStockDTO> stockList) {
        try {
            wdtErpStockSyncService.pushStockToWdtErp(syncType, stockList);
            return Response.builderSuccess("");
        } catch (RomeException e) {
            log.error(e.getMessage(), e);
            return Response.builderFail(e.getCode(),e.getMessage());
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return Response.builderFail(ResCode.STOCK_ERROR_1003,ResCode.STOCK_ERROR_1003_DESC);
        }
    }

    @ApiOperation(value = "推送盘点数据至wdtERP", nickname = "pushPdToWdtErp", produces = MediaType.APPLICATION_JSON_VALUE)
    @ApiResponse(code = 200, message = "success", response = List.class)
    @RequestMapping(value = "/pushPdToWdtErp", method = RequestMethod.POST)
    public Response pushPdToWdtErp(@RequestParam("syncType") Integer syncType, @RequestBody List<WdtChannelStockDTO> stockList) {
        try {
            wdtErpStockSyncService.pushPdToWdtErp(syncType, stockList);
            return Response.builderSuccess("");
        } catch (RomeException e) {
            log.error(e.getMessage(), e);
            return Response.builderFail(e.getCode(),e.getMessage());
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return Response.builderFail(ResCode.STOCK_ERROR_1003,ResCode.STOCK_ERROR_1003_DESC);
        }
    }
}
