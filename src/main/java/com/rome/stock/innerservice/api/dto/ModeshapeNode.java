/**
 * Filename ModeshapeNode.java
 * Company 上海来伊份科技有限公司。
 * <AUTHOR>
 * @version 
 */
package com.rome.stock.innerservice.api.dto;

import java.util.ArrayList;
import java.util.List;

import lombok.Data;

/**
 * 模型图结点数据
 * <AUTHOR>
 * @since 2022-10-26 14:48:02
 */
@Data
public class ModeshapeNode {

	/**
     * id:标识.
     */
    private String id;
    
    /**
     * dbId
     */
    private Long dbId;
    
    /**
     * 节点名称,这里是编码，如实仓编码、虚仓编码、策略组编码、渠道编码
     */
    private  String  name;
    
    /**
     * 名字
     */
    private  String  value;
    
    /**
     * x:坐标.
     */
    private int x;

    /**
     * y:坐标.
     */
    private int y;

    /**
     * type:节点类型
     * 1-实仓	2-虚仓	3-策略组	4-渠道
     * 0-根结点
     */
    private Integer type;

    /**
     * deep:层.
     */
    private int deep;

    /**
     * children:子集合
     */
    private List<ModeshapeNode> children = new ArrayList<>();

    /**
     * parent:父节点集合
     */
    private List<ModeshapeNode> parents;

}
