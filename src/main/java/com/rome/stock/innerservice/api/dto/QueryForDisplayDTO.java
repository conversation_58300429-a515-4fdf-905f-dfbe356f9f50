package com.rome.stock.innerservice.api.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

/**
 * 陈列系统查询门店库存，返回结果
 *
 * <AUTHOR>
 */
@Data
public class QueryForDisplayDTO {

    @ApiModelProperty(value = "物料编码")
    private String  skuCode;

    private Long  skuId;

    private Long realWarehouseId;

    @ApiModelProperty(value = "快照库存(前一天期末库存)")
    private BigDecimal finalQty;

    @ApiModelProperty(value = "实时库存")
    private BigDecimal realQty;
}
