package com.rome.stock.innerservice.api.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <p>
 * Description: 根据采购预约单号查询外部系统单据编号出参
 * </p>
 *
 * <AUTHOR>
 * @date 2022/10/10
 **/
@ApiModel("根据采购预约单号查询外部系统单据编号出参DTO")
@Data
public class OutRecordCodeResultDTO {

    @ApiModelProperty("采购预约单号")
    String appointRecordCode;

    @ApiModelProperty("外部系统单据编号")
    String outRecordCode;
}
