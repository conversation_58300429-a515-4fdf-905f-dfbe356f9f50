package com.rome.stock.innerservice.api.controller;

import com.github.pagehelper.PageInfo;
import com.rome.arch.core.clientobject.Response;
import com.rome.arch.core.exception.RomeException;
import com.rome.arch.util.controller.RomeController;
import com.rome.stock.innerservice.api.dto.BaseinfoConfig;
import com.rome.stock.innerservice.api.dto.LogEnumDTO;
import com.rome.stock.innerservice.common.ResCode;
import com.rome.stock.innerservice.constant.ResponseMsg;
import com.rome.stock.innerservice.domain.service.LogEnumService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import io.swagger.annotations.ApiResponse;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 */
@Slf4j
@RomeController
@RequestMapping("/stock/v1/logEnum")
@Api(tags = {"日志枚举信息配置服务接口"})
public class LogEnumController {

	@Resource
	private LogEnumService logEnumService;


	@ApiOperation(value = "日志枚举配置信息", nickname = "getLogEnum", produces = MediaType.APPLICATION_JSON_VALUE)
	@ApiResponse(code = 200, message = "success")
	@RequestMapping(value = "/getLogEnum", method = RequestMethod.POST)
	public Response<PageInfo<LogEnumDTO>> getLogEnum(@ApiParam(name = "getLogEnum", value = "日志枚举配置信息") @RequestBody LogEnumDTO logEnumDTO) {
		try {
			PageInfo<LogEnumDTO> pageList = logEnumService.getLogEnum(logEnumDTO);
			return Response.builderSuccess(pageList);
		} catch (RomeException e) {
			log.error(e.getMessage(), e);
			return ResponseMsg.EXCEPTION.buildMsg(ResCode.STOCK_ERROR_1003, e.getMessage());
		} catch (Exception e) {
			log.error(e.getMessage(), e);
			return ResponseMsg.EXCEPTION.buildMsg(ResCode.STOCK_ERROR_1003, e.getMessage());
		}
	}

	@ApiOperation(value = "更新或者插入数据，当id为空时插入，否则为更新", nickname = "updateOrInsert", produces = MediaType.APPLICATION_JSON_VALUE)
	@ApiResponse(code = 200, message = "success")
	@RequestMapping(value = "/updateOrInsert", method = RequestMethod.POST)
	public Response updateOrInsert(@RequestBody LogEnumDTO logEnumDTO) {
		try {
			logEnumService.saveOrUpdate(logEnumDTO);
			return Response.builderSuccess("操作成功");
		} catch (RomeException e) {
			log.error(e.getMessage(), e);
			return ResponseMsg.EXCEPTION.buildMsg(ResCode.STOCK_ERROR_1003, e.getMessage());
		} catch (Exception e) {
			log.error(e.getMessage(), e);
			return ResponseMsg.EXCEPTION.buildMsg(ResCode.STOCK_ERROR_1003, e.getMessage());
		}
	}

	/**
	 * 删除基本信息配置
	 *
	 * @param
	 * @return
	 */
	@ApiOperation(value = "删除日志枚举配置", nickname = "deleteLogEnum", produces = MediaType.APPLICATION_JSON_VALUE)
	@ApiResponse(code = 200, message = "success", response = BaseinfoConfig.class)
	@RequestMapping(value = "/deleteLogEnum", method = RequestMethod.POST)
	public Response deleteLogEnum(@ApiParam(name = "deleteLogEnum", value = "删除日志枚举配置") @RequestBody LogEnumDTO logEnumDTO) {
		try {
			logEnumService.deleteLogEnum(logEnumDTO);
			return ResponseMsg.SUCCESS.buildMsg();
		} catch (RomeException e) {
			log.error(e.getMessage(), e);
			return ResponseMsg.EXCEPTION.buildMsg(e.getCode(), e.getMessage());
		} catch (Exception e) {
			log.error(e.getMessage(), e);
			return ResponseMsg.EXCEPTION.buildMsg();
		}
	}
}
