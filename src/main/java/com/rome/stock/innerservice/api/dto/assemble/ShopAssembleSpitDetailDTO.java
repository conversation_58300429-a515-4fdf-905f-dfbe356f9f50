package com.rome.stock.innerservice.api.dto.assemble;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.math.BigDecimal;

/**
 * 类ShopTradeDetail的实现描述：门店加工单明细
 *
 * <AUTHOR> 2019/4/21 17:06
 */
@Data
@EqualsAndHashCode
public class ShopAssembleSpitDetailDTO {

    @ApiModelProperty(value = "成品数量")
    @NotNull(message="成品数量不能为空")
    private BigDecimal skuQty;

    @ApiModelProperty(value = "成品sku编号")
    @NotNull(message="成品sku编号不能为空")
    private String skuCode;

    @ApiModelProperty(value = "单位")
    @NotBlank(message="单位不能为空")
    private String unit;

    @ApiModelProperty(value = "单位")
    @NotEmpty(message="单位code不能为空")
    private String unitCode;

    @ApiModelProperty(value = "拆品数量")
    @NotNull(message="拆品数量不能为空")
    private BigDecimal spitSkuQty;

    @ApiModelProperty(value = "拆品sku编号")
    @NotNull(message="拆品sku编号不能为空")
    private Long spitSkuCode;
}
