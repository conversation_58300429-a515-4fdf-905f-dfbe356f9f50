package com.rome.stock.innerservice.api.dto.template;

import com.rome.stock.innerservice.infrastructure.dataobject.RealWarehouseStockDO;

import lombok.Data;

/**
 * 类ShopStockReportDTO的实现描述：门店库存报表
 *
 * <AUTHOR> 2020/10/7 17:36
 */
@Data
public class ShopStockReportDTO extends RealWarehouseStockDO {

    /**
     * 工厂编码
     */
    private String factoryCode;

    /**
     * 仓库编码
     */
    private String realWarehouseOutCode;
}
