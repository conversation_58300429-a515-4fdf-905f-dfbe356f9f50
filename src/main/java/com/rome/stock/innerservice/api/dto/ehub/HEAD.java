package com.rome.stock.innerservice.api.dto.ehub;

import lombok.Data;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlRootElement;

@XmlAccessorType(XmlAccessType.FIELD)
@XmlRootElement(name = "HEAD")
@Data
public class HEAD {
	
	@XmlElement(name="TYPE")
	private String type;

	@XmlElement(name="CODE")
	private String code;

	@XmlElement(name="NAME")
	private String name;

	@XmlElement(name="DISTRIBUTE_CODE")
	private String distributeCode;

	@XmlElement(name="DATE")
	private String date;

	@XmlElement(name="BUSICODE")
	private String busiCode;

	@XmlElement(name="OPERATION_TYPE")
	private String operationType;

	private String uuid;

}
