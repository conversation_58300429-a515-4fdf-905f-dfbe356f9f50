package com.rome.stock.innerservice.api.dto.frontrecord;

import com.rome.stock.innerservice.api.dto.Pagination;
import com.rome.stock.innerservice.api.dto.RwBatchDTO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;
import java.util.List;


/**
 * 退货预入库
 */
@Data
public class PredictReturnDTO extends Pagination {

    /**
     * 唯一主键
     */
    private Long id;

    /**
     * 单据编号
     */
    @ApiModelProperty(value = "单据编号")
    private String recordCode;
    private String recordCodes;


    private List<String> recordCodeList;

    /**
     * 工厂编码
     */
    private String factoryCode;

    /**
     * 仓库编码
     */
    private String warehouseCode;


    /**
     * 仓库编码
     */
    private String warehouseCodes;

    /**
     * 实体仓库id
     */
    @ApiModelProperty(value = "实仓Id")
    private Long realWarehouseId;


    private List<Long> realWarehouseIds;

    /**
     * 仓库名称
     */
    private String realWarehouseName;

    /**
     * 入库单号
     */
    @ApiModelProperty(value = "入库单号")
    private String entryOrderCode;

    /**
     * 运单号
     */
    @ApiModelProperty(value = "运单号")
    private String expressCode;

    private String expressCodes;


    private List<String> expressCodeList;

    /**
     * 物流公司编码
     */
    @ApiModelProperty(value = "物流公司编码")
    private String logisticsCode;

    /**
     * 物流公司名称
     */
    @ApiModelProperty(value = "物流公司名称")
    private String logisticsName;

    /**
     * 备注
     */
    @ApiModelProperty(value = "备注")
    private String remark;

    /**
     * 外部仓库编码
     */
    @ApiModelProperty(value = "外部仓库编码")
    private String realWareHouseOutCode;

    /**
     * 退货原因
     */
    private String returnReason;

    /**
     * 单据状态 0-初始 19-部分匹配 20-完全匹配
     */
    @ApiModelProperty(value = "单据状态 0-初始 19-部分匹配 20-完全匹配")
    private Integer recordStatus;

    /**
     * 退货预入库时间
     */
    @ApiModelProperty(value = "退货预入库时间")
    private Date outCreateTime;

    /**
     * 创建时间
     */
    @ApiModelProperty(value = "创建时间")
    private Date createTime;
    /**
     * 创建人
     */
    @ApiModelProperty(value = "创建人")
    private Long creator;
    /**
     * 更新人
     */
    @ApiModelProperty(value = "更新人")
    private Long modifier;

    /**
     * 退货预入库单据明细
     */
    @ApiModelProperty(value = "退货预入库单据明细")
    private List<PredictReturnDetailDTO> frontRecordDetails;

    /**
     * 批次信息
     */
    private List<RwBatchDTO> batches;

    /**
     * 开始时间
     */
    private Date startTime;

    /**
     * 结束时间
     */
    private Date endTime;

    @ApiModelProperty(value = "定时任务处理标识：0-无需处理(默认)，1-待处理，2-已处理")
    private Integer needJobHandle;
}
