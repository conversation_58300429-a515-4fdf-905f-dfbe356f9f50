package com.rome.stock.innerservice.api.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;
import java.util.List;

@Data
@EqualsAndHashCode
public class WmsStockQueryDTO extends Pagination {
    /**
     * 工厂编码
     */
    @ApiModelProperty(value = "工厂编码")
    private String factoryCode;
    /**
     * 工厂名称
     */
    @ApiModelProperty(value = "工厂名称")
    private String factoryName;
    /**
     * 仓库id
     */
    @ApiModelProperty(value = "仓库id")
    private Long warehouseId;

    /**
     * 仓库编码
     */
    @ApiModelProperty(value = "仓库编码")
    private String warehouseCode;

    /**
     * 仓库编码
     */
    @ApiModelProperty(value = "内部仓库编码")
    private String realWarehouseCode;

    /**
     * 仓库编码
     */
    @ApiModelProperty(value = "外部仓库编码")
    private String realWarehouseOutCode;

    /**
     * 仓库名称
     */
    @ApiModelProperty(value = "仓库名称")
    private String warehouseName;


    /**
     * 商品编码
     */
    @ApiModelProperty(value = "商品编码")
    private String skuId;

    /**
     * 商品skuId集合
     */
    @ApiModelProperty(value = "商品skuId集合")
    private List<Long> skuIds;

    /**
     * 商品编号
     */
    @ApiModelProperty(value = "商品编码")
    private String skuCode;

    /**
     * 商品名称
     */
    @ApiModelProperty(value = "商品名称")
    private String skuName;

    /**
     * 商品标题
     */
    @ApiModelProperty(value = "商品标题")
    private String skuTitle;

    /**
     * 商品基本单位
     */
    @ApiModelProperty(value = "商品基本单位")
    private String skuUnit;

    /**
     * 商品品类
     */
    @ApiModelProperty(value = "商品品类")
    private String skuType;

    /**
     * 库存现有量
     */
    @ApiModelProperty(value = "库存现有量")
    private BigDecimal realQty;

    /**
     * 库存可用量
     */
    @ApiModelProperty(value = "库存可用量")
    private BigDecimal availableQty;

    /**
     *库存锁定量
     */
    @ApiModelProperty(value = "库存锁定量")
    private BigDecimal lockQty;

    /**
     * 库存质检锁定量
     */
    @ApiModelProperty(value = "库存质检锁定量")
    private BigDecimal qualityQty;

    /**
     * 冻结库存
     */
    @ApiModelProperty(value = "冻结库存")
    private BigDecimal unqualifiedQty;


    /**
     * 仓库类型名称
     */
    @ApiModelProperty(value = "仓库类型名称")
    private String warehouseTypeName;

    /**
     * 仓库类型
     */
    @ApiModelProperty(value = "仓库类型")
    private Integer warehouseType;

    /**
     * 批次号
     */
    private String beachCode;

    /**
     * 在途库存
     */
    private BigDecimal inventoryTransfer;
}
