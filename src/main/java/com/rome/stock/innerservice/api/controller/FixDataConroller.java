package com.rome.stock.innerservice.api.controller;

import com.rome.arch.core.clientobject.Response;
import com.rome.arch.core.exception.RomeException;
import com.rome.arch.util.controller.RomeController;
import com.rome.stock.innerservice.api.dto.message.StoreDTO;
import com.rome.stock.innerservice.common.ResCode;
import com.rome.stock.innerservice.constant.ResponseMsg;
import com.rome.stock.innerservice.domain.message.AddShopConsumer;
import com.rome.stock.innerservice.domain.service.FixDataService;
import com.rome.stock.innerservice.domain.service.ShopReplenishService;
import com.rome.stock.innerservice.remote.stockCore.facade.StockCoreFacade;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import io.swagger.annotations.ApiResponse;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;

import javax.annotation.Resource;
import java.util.List;

/**
 * 类FixDataConroller的实现描述：数据修复程序
 *
 * <AUTHOR> 2019/10/11 19:08
 */
@Slf4j
@RomeController
@RequestMapping("/stock/v1/stock_mail")
@Api(tags={"数据修复程序"})
public class FixDataConroller {

    @Resource
    private FixDataService fixDataService;
    @Resource
    private AddShopConsumer shopConsumer;

    @Resource
    private ShopReplenishService shopReplenishService;
    @Resource
    private StockCoreFacade stockCoreFacade;

    @ApiOperation(value = "修复盘点外部单号数据", nickname = "fixShopinventoryData", produces = MediaType.APPLICATION_JSON_VALUE)
    @ApiResponse(code = 200, message = "success")
    @RequestMapping(value = "/fixShopinventoryData", method = RequestMethod.GET)
    public Response fixShopinventoryData(){
        try {
            fixDataService.fixShopinventoryData();
            return ResponseMsg.SUCCESS.buildMsg();
        } catch (RomeException e) {
            log.error(e.getMessage(), e);
            return ResponseMsg.FAIL.buildMsg(e.getCode(),e.getMessage());
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return ResponseMsg.FAIL.buildMsg("500000",e.getMessage());
        }
    }


    @ApiOperation(value = "修复调拨单数量入库数量计算的数据", nickname = "fixWhallotData", produces = MediaType.APPLICATION_JSON_VALUE)
    @ApiResponse(code = 200, message = "success")
    @RequestMapping(value = "/fixWhallotData", method = RequestMethod.GET)
    public Response fixWhallotData(String list){
        try {
            if(StringUtils.isNotBlank(list)){
                String [] array = list.split(",");
                if(array.length > 0){
                    for (String recordCode : array) {
                       //根据后置单更新入库数量
                        fixDataService.fixWhallotData(recordCode);
                    }
                }
            }
            return ResponseMsg.SUCCESS.buildMsg();
        } catch (RomeException e) {
            log.error(e.getMessage(), e);
            return Response.builderFail(e.getCode(),e.getMessage());
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return ResponseMsg.EXCEPTION.buildMsg();
        }
    }


    @ApiOperation(value = "新开门店", nickname = "addShop", produces = MediaType.APPLICATION_JSON_VALUE)
    @ApiResponse(code = 200, message = "success")
    @RequestMapping(value = "/addShop", method = RequestMethod.POST)
    public Response fixShopData(@ApiParam(name = "新开门店信息", value = "storeDTO") @RequestBody StoreDTO storeDTO){
        try {
            StringBuffer sb = new StringBuffer("");
            boolean rs = shopConsumer.addShop(sb, storeDTO);
            if(!rs) {
            	stockCoreFacade.addRealWarehouseServiceLab(storeDTO.getCode());
                return ResponseMsg.SUCCESS.buildMsg(sb.toString());
            } else {
                return ResponseMsg.SUCCESS.buildMsg("success");
            }
        } catch (RomeException e) {
            log.error(e.getMessage(), e);
            return Response.builderFail(e.getCode(),e.getMessage());
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return ResponseMsg.EXCEPTION.buildMsg();
        }
    }

    @ApiOperation(value = "修复盘点外部单号数据", nickname = "fixShopinventoryData", produces = MediaType.APPLICATION_JSON_VALUE)
    @ApiResponse(code = 200, message = "success")
    @RequestMapping(value = "/fixJoinReceiptData", method = RequestMethod.GET)
    public Response fixJoinReceiptData(@RequestParam("recordCode") String recordCode){
        try {
            fixDataService.fixJoinReceiptData(recordCode);
            return ResponseMsg.SUCCESS.buildMsg();
        } catch (RomeException e) {
            log.error(e.getMessage(), e);
            return ResponseMsg.FAIL.buildMsg(e.getCode(),e.getMessage());
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return ResponseMsg.FAIL.buildMsg("500000",e.getMessage());
        }
    }



    @ApiOperation(value = "查询所有正常开店的门店", nickname = "selectAllNormalStore", produces = MediaType.APPLICATION_JSON_VALUE)
    @ApiResponse(code = 200, message = "success")
    @RequestMapping(value = "/selectAllNormalStore", method = RequestMethod.POST)
    public Response<List<String>> selectAllNormalStore(){
        try {
            List<String> res=fixDataService.selectAllNormalStoreContainNoAndPreOpen();
            return ResponseMsg.SUCCESS.buildMsg(res);
        } catch (RomeException e) {
            log.error(e.getMessage(), e);
            return ResponseMsg.FAIL.buildMsg(e.getCode(),e.getMessage());
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return ResponseMsg.FAIL.buildMsg(e.getMessage());
        }
    }

    @ApiOperation(value = "补偿开店", nickname = "openNewShop", produces = MediaType.APPLICATION_JSON_VALUE)
    @ApiResponse(code = 200, message = "success")
    @RequestMapping(value = "/openNewShop", method = RequestMethod.POST)
    public Response openNewShop(@ApiParam(name = "shopCodeList", value = "门店code列表") @RequestBody List<String> shopCodeList){
        try {
            String errorMsg = fixDataService.openNewShop(shopCodeList);
            if(StringUtils.isNotBlank(errorMsg)){
            	return ResponseMsg.FAIL.buildMsg(ResCode.STOCK_ERROR_1001, errorMsg); 
            }
            return ResponseMsg.SUCCESS.buildMsg();
        } catch (RomeException e) {
            log.error(e.getMessage(), e);
            return ResponseMsg.FAIL.buildMsg(e.getCode(),e.getMessage());
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return ResponseMsg.FAIL.buildMsg(ResCode.STOCK_ERROR_1001, e.getMessage());
        }
    }

    @ApiOperation(value = "刷拼接表财务日期", nickname = "updateCostFinanceDate", produces = MediaType.APPLICATION_JSON_VALUE)
    @ApiResponse(code = 200, message = "success")
    @RequestMapping(value = "/updateCostFinanceDate", method = RequestMethod.GET)
    public Response updateCostFinanceDate(@ApiParam(name = "ztRecordCodeStr", value = "多个逗号分割，优先取此值") @RequestParam(name = "ztRecordCodeStr",required = false)  String ztRecordCodeStr,
                                          @ApiParam(name = "ztBusinessCodeStr", value = "多个逗号分割，ztRecordCodeStr为空时取此值") @RequestParam(name = "ztBusinessCodeStr" ,required = false)  String ztBusinessCodeStr,
                                          @ApiParam(name = "businessType", value = "出入库单类型， 1：出 2：入，不填为全部") @RequestParam(name = "businessType" ,required = false)  Integer businessType,
                                          @ApiParam(name = "financDate", required = true,value = "财务日期, yyyy-MM-dd") @RequestParam(name = "financDate")  String financDate
                                          ){
        try {
            String errorMsg = fixDataService.updateCostFinanceDate(ztRecordCodeStr,ztBusinessCodeStr,financDate, businessType);
            if(StringUtils.isNotBlank(errorMsg)){
                return ResponseMsg.FAIL.buildMsg(ResCode.STOCK_ERROR_1001, errorMsg);
            }
            return ResponseMsg.SUCCESS.buildMsg();
        } catch (RomeException e) {
            log.error(e.getMessage(), e);
            return ResponseMsg.FAIL.buildMsg(e.getCode(),e.getMessage());
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return ResponseMsg.FAIL.buildMsg(ResCode.STOCK_ERROR_1001, e.getMessage());
        }
    }


}
