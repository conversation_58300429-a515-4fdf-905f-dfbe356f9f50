package com.rome.stock.innerservice.api.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * 批次原箱库存流水
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
@EqualsAndHashCode
public class BatchBoxFlowDTO extends Pagination {
    /**
     * 唯一主键
     */
    private Long id;

    @ApiModelProperty("实体仓库id")
    private Long realWarehouseId;

    /**
     * 商品sku编码
     */
    private Long skuId;

    /**
     * 商品sku编码
     */
    private String skuCode;
    
    /**
     * 真实库存,变更数量
     */
    private BigDecimal skuQty;

    /**
     * 原箱类型:1.原箱 2.非原箱
     */
    private Integer boxType;

    /**
     * 批次编码-批次号
     */
    private String batchCode;

    /**
     * 单据编号
     */
    private String recordCode;

    /**
     * 生产日期
     * */
    private Date productDate;

    /**
     * 修改之前库存数量
     */
    private BigDecimal beforeChangeQty;

    /**
     * 修改之后库存数量
     */
    private BigDecimal afterChangeQty;

    /**
     * 1：增加库存 2:减少库存 3：锁定库存 4：释放库存 5：真实出库库存  6：冻结出库库存
     */
    private Integer stockType;

    /**
     * 单据类型
     */
    private Integer recordType;

    /**
     * 创建时间
     */
    private Date createTime;
    /**
     * 修改时间
     */
    private Date updateTime;
    /**
     * 创建人
     */
    private Long creator;
    /**
     * 更新人
     */
    private Long modifier;
    /**
     * 是否可用：0-否，1-是
     */
    private Byte isAvailable;
    /**
     * 是否逻辑删除：0-否，1-是
     */
    private Byte isDeleted;

    @ApiModelProperty(value = "实仓code")
    private String realWarehouseCode;

    @ApiModelProperty(value = "sap交货单号")
    private String sapOrderCode;

    @ApiModelProperty(value = "查询,开始时间")
    private Date startTime;

    @ApiModelProperty(value = "查询,结束时间")
    private Date endTime;

    @ApiModelProperty(value = "查询,skuIds")
    private List<Long> skuIds;

    @ApiModelProperty(value = "查询,SAP交货单号")
    private List<String> sapOrderCodes;

    @ApiModelProperty("查询,单据类型列表")
    private List<Integer> recordTypes;

    @ApiModelProperty(value = "查询,出入库单据号")
    private List<String> recordCodeList;

    @ApiModelProperty("查询,批次编码-批次号")
    private List<String> batchCodeList;

    /**
     * 库存类型名称
     */
    @ApiModelProperty(value = "库存类型名称")
    private String stockTypeName;

    /**
     * 仓库出入库类型
     */
    @ApiModelProperty(value = "仓库出入库类型")
    private List<Integer> stockTypes;

    @ApiModelProperty(value = "商品名称")
    private String skuCname;

    @ApiModelProperty(value = "主单位(商品单位)")
    private String unit;

    @ApiModelProperty(value = "仓库名称")
    private String realWarehouseName;

    @ApiModelProperty(value = "出库单类型名称")
    private String recordTypeName;

    //使用的rwBatch中的批次号
    private String rwBatchCode;
}
