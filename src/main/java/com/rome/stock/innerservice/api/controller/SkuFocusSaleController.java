package com.rome.stock.innerservice.api.controller;

import java.util.List;

import javax.annotation.Resource;

import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;

import com.github.pagehelper.PageInfo;
import com.rome.arch.core.clientobject.Response;
import com.rome.arch.core.exception.RomeException;
import com.rome.arch.util.controller.RomeController;
import com.rome.stock.innerservice.api.dto.RealWarehouse;
import com.rome.stock.innerservice.api.dto.SkuFocusSale;
import com.rome.stock.innerservice.api.dto.SkuFocusSaleDTO;
import com.rome.stock.innerservice.common.ResCode;
import com.rome.stock.innerservice.constant.ResponseMsg;
import com.rome.stock.innerservice.domain.service.SkuFocusSaleService;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import io.swagger.annotations.ApiResponse;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@RomeController
@RequestMapping("/stock/v1/sku_focus_sale")
@Api(tags = {"寻源仓库管理"})
public class SkuFocusSaleController {
    @Resource
    private SkuFocusSaleService skuFocusSaleService;

    @ApiOperation(value = "查询所有电商仓", nickname = "getAllECommerceWarehouse", produces = MediaType.APPLICATION_JSON_VALUE)
    @ApiResponse(code = 200, message = "success")
    @RequestMapping(value = "/getAllECommerceWarehouse", method = RequestMethod.GET)
    public Response<List<RealWarehouse>> getAllECommerceWarehouse() {
        try {
            List<RealWarehouse> realWarehouses = skuFocusSaleService.getAllECommerceWarehouse();
            return Response.builderSuccess(realWarehouses);
        } catch (RomeException e) {
            log.error(e.getMessage(), e);
            return ResponseMsg.EXCEPTION.buildMsg(e.getCode(), e.getMessage());
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return ResponseMsg.EXCEPTION.buildMsg(ResCode.STOCK_ERROR_1003, ResCode.STOCK_ERROR_1003_DESC);
        }
    }
    @ApiOperation(value = "查询寻源仓库", nickname = "getSkuFocusSale", produces = MediaType.APPLICATION_JSON_VALUE)
    @ApiResponse(code = 200, message = "success")
    @RequestMapping(value = "/getSkuFocusSale", method = RequestMethod.POST)
    public Response<PageInfo<SkuFocusSale>> getSkuFocusSale(@ApiParam(name = "SkuFocusSale", value = "Sku寻源仓库") @RequestBody SkuFocusSale skuFocusSale) {
        try {
            PageInfo<SkuFocusSale> pageList = skuFocusSaleService.getSkuFocusSale(skuFocusSale);
            return Response.builderSuccess(pageList);
        } catch (RomeException e) {
            log.error(e.getMessage(), e);
            return ResponseMsg.EXCEPTION.buildMsg(e.getCode(), e.getMessage());
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return ResponseMsg.EXCEPTION.buildMsg(ResCode.STOCK_ERROR_1003, ResCode.STOCK_ERROR_1003_DESC);
        }
    }

    @ApiOperation(value = "新增寻源仓库信息", nickname = "addSkuFocusSale", produces = MediaType.APPLICATION_JSON_VALUE)
    @ApiResponse(code = 200, message = "success", response = List.class)
    @RequestMapping(value = "/addSkuFocusSale", method = RequestMethod.POST)
    public Response addSkuFocusSale(@ApiParam(name = "SkuFocusSale", value = "Sku寻源仓库") @RequestBody SkuFocusSale skuFocusSale) {
        try {
            skuFocusSaleService.addSkuFocusSale(skuFocusSale);
            return Response.builderSuccess("");
        } catch (RomeException e) {
            log.error(e.getMessage(), e);
            return ResponseMsg.EXCEPTION.buildMsg(e.getCode(), e.getMessage());
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return ResponseMsg.EXCEPTION.buildMsg(ResCode.STOCK_ERROR_1003, ResCode.STOCK_ERROR_1003_DESC);
        }
    }

    @ApiOperation(value = "根据ids删除SKU寻源仓库", nickname = "delSkuFocusSaleByIds", produces = MediaType.APPLICATION_JSON_VALUE)
    @ApiResponse(code = 200, message = "success", response = List.class)
    @RequestMapping(value = "/delSkuFocusSaleByIds", method = RequestMethod.POST)
    public Response delSkuFocusSaleByIds(@ApiParam(name = "skuFocusSales", value = "Sku寻源仓库ids") @RequestBody List<SkuFocusSale> skuFocusSales) {
        try {
            skuFocusSaleService.delSkuFocusSaleByIds(skuFocusSales);
            return Response.builderSuccess("");
        } catch (RomeException e) {
            log.error(e.getMessage(), e);
            return ResponseMsg.EXCEPTION.buildMsg(e.getCode(), e.getMessage());
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return ResponseMsg.EXCEPTION.buildMsg(ResCode.STOCK_ERROR_1003, ResCode.STOCK_ERROR_1003_DESC);
        }
    }

    @ApiOperation(value = "根据isAvailable改变状态", nickname = "changeIsAvailable", produces = MediaType.APPLICATION_JSON_VALUE)
    @ApiResponse(code = 200, message = "success", response = List.class)
    @RequestMapping(value = "/changeIsAvailable", method = RequestMethod.POST)
    public Response changeIsAvailable(@ApiParam(name = "isAvailable", value = "是否启用值") @RequestParam("isAvailable") Integer isAvailable,@ApiParam(name = "id", value = "主键") @RequestParam("id") Long id,@ApiParam(name = "realWarehouseId", value = "实仓id") @RequestParam("realWarehouseId") Long realWarehouseId,@ApiParam(name = "用户id", value = "userId") @RequestParam("userId") Long userId) {
        try {
            skuFocusSaleService.changeIsAvailable(isAvailable,id,realWarehouseId,userId);
            return Response.builderSuccess("");
        } catch (RomeException e) {
            log.error(e.getMessage(), e);
            return ResponseMsg.EXCEPTION.buildMsg(e.getCode(), e.getMessage());
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return ResponseMsg.EXCEPTION.buildMsg(ResCode.STOCK_ERROR_1003, ResCode.STOCK_ERROR_1003_DESC);
        }
    }

    @ApiOperation(value = "导入excel", nickname = "importExcel", produces = MediaType.APPLICATION_JSON_VALUE)
    @ApiResponse(code = 200, message = "success", response = List.class)
    @RequestMapping(value = "/excel", method = RequestMethod.POST)
    public Response importExcel(@RequestBody List<SkuFocusSaleDTO> list){
        try {
            String result = skuFocusSaleService.importExcel(list);
            return Response.builderSuccess(result);
        } catch (RomeException e) {
            log.error(e.getMessage(), e);
            return ResponseMsg.EXCEPTION.buildMsg(e.getCode(), e.getMessage());
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return ResponseMsg.EXCEPTION.buildMsg(ResCode.STOCK_ERROR_1003, ResCode.STOCK_ERROR_1003_DESC);
        }
    }

    @ApiOperation(value = "更新状态", nickname = "updateStatus", produces = MediaType.APPLICATION_JSON_VALUE)
    @ApiResponse(code = 200, message = "success", response =Response.class)
    @RequestMapping(value = "/updateStatus", method = RequestMethod.POST)
    public Response updateIsAvailable(@RequestBody List<SkuFocusSale> list){
        try{
            skuFocusSaleService.updateIsAvailable(list);
            return Response.builderSuccess(null);
        }catch (RomeException e){
            log.error(e.getMessage(), e);
            return ResponseMsg.EXCEPTION.buildMsg(e.getCode(), e.getMessage());
        }catch (Exception e){
            log.error(e.getMessage(), e);
            return ResponseMsg.EXCEPTION.buildMsg(ResCode.STOCK_ERROR_1003, ResCode.STOCK_ERROR_1003_DESC);
        }
    }
}
