package com.rome.stock.innerservice.api.controller;

import com.github.pagehelper.PageInfo;
import com.rome.arch.core.clientobject.Response;
import com.rome.arch.core.exception.CodeMessage;
import com.rome.arch.core.exception.RomeException;
import com.rome.arch.util.controller.RomeController;
import com.rome.stock.innerservice.api.dto.*;
import com.rome.stock.innerservice.common.RedisUtil;
import com.rome.stock.innerservice.common.ResCode;
import com.rome.stock.innerservice.constant.RealWarehouseTypeVO;
import com.rome.stock.innerservice.constant.ResponseMsg;
import com.rome.stock.innerservice.domain.service.StockRecordService;
import com.rome.stock.innerservice.domain.service.VmsStockReportService;
import com.rome.stock.innerservice.export.aspect.AsyncExcelResultContext;
import com.rome.stock.innerservice.export.domain.service.dto.AsyncExportResult;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import io.swagger.annotations.ApiResponse;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;

/**
 * 库存查询
 */
@Slf4j
@RomeController
@RequestMapping("/stock/v1/stock_record")
@Api(tags={"测试库存"})
public class StockRecordController {
    @Resource
    private StockRecordService stockRecordService;

    @Resource
    private VmsStockReportService vmsStockReportService;
    @Resource
    private RedisUtil redisUtil;

    @ApiOperation(value = "通过仓库编码查询所有实仓拥有的工厂信息", nickname = "getFactoryInfoByAllRealWarehouse", produces = MediaType.APPLICATION_JSON_VALUE)
    @ApiResponse(code = 200, message = "success", response = List.class)
    @RequestMapping(value = "/getFactoryInfoByAllRealWarehouse", method = RequestMethod.POST)
    public Response<List<StockRecord>> getFactoryInfoByAllRealWarehouse(@ApiParam(name = "warehouseCode", value = "仓库编码") @RequestParam(value = "warehouseCode",required = false) String warehouseCode,@ApiParam(name = "warehouseType", value = "仓库类型") @RequestParam(value = "warehouseType",required = false) String warehouseType) {
        try {
            List<StockRecord> stockRecords = stockRecordService.getFactoryInfoByAllRealWarehouse(warehouseCode,warehouseType);
            return Response.builderSuccess(stockRecords);
        } catch (RomeException e) {
            log.error(e.getMessage(), e);
            return ResponseMsg.EXCEPTION.buildMsg(e.getCode(), e.getMessage());
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return ResponseMsg.EXCEPTION.buildMsg(ResCode.STOCK_ERROR_1003, ResCode.STOCK_ERROR_1003_DESC);
        }
    }
    @ApiOperation(value = "通过仓库工厂编码和仓库类型查询所有实仓拥有的仓库编码和名称信息", nickname = "getRealWarehouseCodesAndNames", produces = MediaType.APPLICATION_JSON_VALUE)
    @ApiResponse(code = 200, message = "success", response = List.class)
    @RequestMapping(value = "/getRealWarehouseCodesAndNames", method = RequestMethod.POST)
    public Response<List<StockRecord>> getRealWarehouseCodesAndNames(@ApiParam(name = "factoryCode", value = "工厂编码") @RequestParam(value = "factoryCode",required = false) String factoryCode,@ApiParam(name = "warehouseType", value = "仓库类型") @RequestParam(value = "warehouseType",required = false) String warehouseType) {
        try {
            List<StockRecord> stockRecords = stockRecordService.getRealWarehouseCodesAndNames(factoryCode,warehouseType);
            return Response.builderSuccess(stockRecords);
        } catch (RomeException e) {
            log.error(e.getMessage(), e);
            return ResponseMsg.EXCEPTION.buildMsg(e.getCode(), e.getMessage());
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return ResponseMsg.EXCEPTION.buildMsg(ResCode.STOCK_ERROR_1003, ResCode.STOCK_ERROR_1003_DESC);
        }
    }
    @ApiOperation(value = "根据查询条件查询所有商品信息", nickname = "getSkusInfoByQueryCondition", produces = MediaType.APPLICATION_JSON_VALUE)
    @ApiResponse(code = 200, message = "success", response = List.class)
    @RequestMapping(value = "/getSkusInfoByQueryCondition", method = RequestMethod.POST)
    public Response<PageInfo<StockRecord>> getSkusInfoByQueryCondition(@RequestBody StockRecord stockRecord) {
        try {
            PageInfo<StockRecord> pageList = stockRecordService.getSkusInfoByQueryCondition(stockRecord);
            return Response.builderSuccess(pageList);
        } catch (RomeException e) {
            log.error(e.getMessage(), e);
            return ResponseMsg.EXCEPTION.buildMsg(e.getCode(), e.getMessage());
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return ResponseMsg.EXCEPTION.buildMsg(ResCode.STOCK_ERROR_1003, ResCode.STOCK_ERROR_1003_DESC);
        }
    }

    @ApiOperation(value = "根据查询条件查询商家所有商品信息", nickname = "getMerchantSkusInfoByQueryCondition", produces = MediaType.APPLICATION_JSON_VALUE)
    @ApiResponse(code = 200, message = "success", response = List.class)
    @RequestMapping(value = "/getMerchantSkusInfoByQueryCondition", method = RequestMethod.POST)
    public Response<PageInfo<StockRecord>> getMerchantSkusInfoByQueryCondition(@RequestBody StockRecord stockRecord) {
        try {
            PageInfo<StockRecord> pageList = stockRecordService.getMerchantSkusInfoByQueryCondition(stockRecord);
            return Response.builderSuccess(pageList);
        } catch (RomeException e) {
            log.error(e.getMessage(), e);
            return ResponseMsg.EXCEPTION.buildMsg(e.getCode(), e.getMessage());
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return ResponseMsg.EXCEPTION.buildMsg(ResCode.STOCK_ERROR_1003, ResCode.STOCK_ERROR_1003_DESC);
        }
    }



    @ApiOperation(value = "根据查询条件查询量贩店所有商品信息", nickname = "getSpacialSkusInfoByQueryCondition", produces = MediaType.APPLICATION_JSON_VALUE)
    @ApiResponse(code = 200, message = "success", response = List.class)
    @RequestMapping(value = "/getSpacialSkusInfoByQueryCondition", method = RequestMethod.POST)
    public Response<PageInfo<StockRecord>> getSpacialSkusInfoByQueryCondition(@RequestBody StockRecord stockRecord) {
        try {
            PageInfo<StockRecord> pageList = stockRecordService.getSpacialSkusInfoByQueryCondition(stockRecord);
            return Response.builderSuccess(pageList);
        } catch (RomeException e) {
            log.error(e.getMessage(), e);
            return ResponseMsg.EXCEPTION.buildMsg(e.getCode(), e.getMessage());
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return ResponseMsg.EXCEPTION.buildMsg(ResCode.STOCK_ERROR_1003, ResCode.STOCK_ERROR_1003_DESC);
        }
    }


    @ApiOperation(value = "查询所有虚仓拥有的仓库编码和名称信息", nickname = "getVirtualWarehouseCodesAndNames", produces = MediaType.APPLICATION_JSON_VALUE)
    @ApiResponse(code = 200, message = "success", response = List.class)
    @RequestMapping(value = "/getVirtualWarehouseCodesAndNames", method = RequestMethod.GET)
    public Response<List<StockRecord>> getVirtualWarehouseCodesAndNames() {
        try {
            List<StockRecord> stockRecords = stockRecordService.getVitualWarehouseCodesAndNames();
            return Response.builderSuccess(stockRecords);
        } catch (RomeException e) {
            log.error(e.getMessage(), e);
            return ResponseMsg.EXCEPTION.buildMsg(e.getCode(), e.getMessage());
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return ResponseMsg.EXCEPTION.buildMsg(ResCode.STOCK_ERROR_1003, ResCode.STOCK_ERROR_1003_DESC);
        }
    }
    @ApiOperation(value = "查询所有渠道拥有的渠道编码和名称信息", nickname = "getChannelCodesAndNames", produces = MediaType.APPLICATION_JSON_VALUE)
    @ApiResponse(code = 200, message = "success", response = List.class)
    @RequestMapping(value = "/getChannelCodesAndNames", method = RequestMethod.GET)
    public Response<List<StockRecord>> getChannelCodesAndNames() {
        try {
            List<StockRecord> stockRecords = stockRecordService.getChannelCodesAndNames();
            return Response.builderSuccess(stockRecords);
        } catch (RomeException e) {
            log.error(e.getMessage(), e);
            return ResponseMsg.EXCEPTION.buildMsg(e.getCode(), e.getMessage());
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return ResponseMsg.EXCEPTION.buildMsg(ResCode.STOCK_ERROR_1003, ResCode.STOCK_ERROR_1003_DESC);
        }
    }
    @ApiOperation(value = "通过warehouseId和skuId查询该商品的批次号", nickname = "/getBatchInfoByWarehouseIdAndSkuId",produces = MediaType.APPLICATION_JSON_VALUE)
    @ApiResponse(code = 200, message = "success", response = List.class)
    @RequestMapping(value = "/getBatchInfoByWarehouseIdAndSkuId", method = RequestMethod.GET)
    public Response<List<SkuBatchStockDTO>> getBatchInfoByWarehouseIdAndSkuId(@RequestParam("skuId") String skuId, @RequestParam("warehouseId") Integer warehouseId) {
        try {
            List<SkuBatchStockDTO> batchStockDTOs = stockRecordService.getBatchInfoByWarehouseIdAndSkuId(skuId,warehouseId);
            return Response.builderSuccess(batchStockDTOs);
        } catch (RomeException e) {
            log.error(e.getMessage(), e);
            return ResponseMsg.EXCEPTION.buildMsg(e.getCode(), e.getMessage());
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return ResponseMsg.EXCEPTION.buildMsg(ResCode.STOCK_ERROR_1003, ResCode.STOCK_ERROR_1003_DESC);
        }
    }
    /**
     * 获取所有仓库类型(从类型枚举类中获取)
     * @return
     */
    @ApiOperation(value = "获取所有仓库类型", nickname = "list_warehouse_type", produces = MediaType.APPLICATION_JSON_VALUE)
    @ApiResponse(code = 200, message = "success", response = Map.class)
    @GetMapping(value = "/listWarehouseType")
    public Response<Map<Integer, String>> getWarehouseType() {
        try {
            Map<Integer, String> warehouseType = RealWarehouseTypeVO.getRealWarehouseTypeList();
            return Response.builderSuccess(warehouseType);
        } catch (RomeException e){
            log.error(e.getMessage(), e);
            return Response.builderFail(e.getCode(), e.getMessage());
        } catch (Exception e){
            log.error(e.getMessage(), e);
            return Response.builderFail(ResCode.STOCK_ERROR_1003, ResCode.STOCK_ERROR_1003_DESC);
        }
    }

    @ApiOperation(value = "获取wms库存信息", produces = MediaType.APPLICATION_JSON_VALUE)
    @ApiResponse(code = 200, message = "success", response = List.class)
    @RequestMapping(value = "/queryWmsWarehouseStock", method = RequestMethod.POST)
    public Response<PageInfo<WmsStockQueryDTO>> queryWmsWarehouseStock(@RequestBody WmsStockQueryDTO dfWmsStockQueryDTO) {
        try {
            PageInfo<WmsStockQueryDTO> dfWmsStockQueryDTOS = stockRecordService.queryWmsWarehouseStock(dfWmsStockQueryDTO);
            return Response.builderSuccess(dfWmsStockQueryDTOS);
        } catch (RomeException e){
            log.error(e.getMessage(), e);
            return Response.builderFail(e.getCode(), e.getMessage());
        } catch (Exception e){
            log.error(e.getMessage(), e);
            return Response.builderFail(ResCode.STOCK_ERROR_1003, ResCode.STOCK_ERROR_1003_DESC);
        }
    }

    @ApiOperation(value = "根据仓库工厂编码和仓库类型查询所有WMS仓库信息",  produces = MediaType.APPLICATION_JSON_VALUE)
    @ApiResponse(code = 200, message = "success")
    @RequestMapping(value = "/queryWmsRealWarehouseByFactoryCode", method = RequestMethod.GET)
    public Response queryWmsRealWarehouseByFactoryCode(@RequestParam("factoryCode") String factoryCode){
        try {
            List<RealWarehouse> warehouses = stockRecordService.queryWmsRealWarehouseByFactoryCode(factoryCode);
            return ResponseMsg.SUCCESS.buildMsg(warehouses);
        } catch (RomeException e) {
            log.error(e.getMessage(), e);
            return ResponseMsg.FAIL.buildMsg(e.getCode(),e.getMessage());
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return ResponseMsg.FAIL.buildMsg("500000",e.getMessage());
        }
    }
    @ApiOperation(value = "导出实仓库存",  produces = MediaType.APPLICATION_JSON_VALUE)
    @ApiResponse(code = 200, message = "success")
    @RequestMapping(value = "/exportRealWarehouseStock", method = RequestMethod.POST)
    public Response exportRealWarehouseStock(@RequestBody StockRecord stockRecord){
        try {
            stockRecordService.exportRealWarehouseStock(stockRecord);
            // 获取导出结果
            AsyncExportResult result = AsyncExcelResultContext.getAsyncExportResult();
            if(result.getTaskStatus().equals(AsyncExportResult.TASK_STATUS_REPEAT)){
                return Response.builderFail(CodeMessage.FAIL.getCode(), "导出实仓库存列表异常，30分钟内请不要重复提交");
            }
            return ResponseMsg.SUCCESS.buildMsg("");
        } catch (RomeException e) {
            log.error(e.getMessage(), e);
            return ResponseMsg.FAIL.buildMsg(e.getCode(),e.getMessage());
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return ResponseMsg.FAIL.buildMsg("500000",e.getMessage());
        }
    }

    @ApiOperation(value = "导出虚仓库存",  produces = MediaType.APPLICATION_JSON_VALUE)
    @ApiResponse(code = 200, message = "success")
    @RequestMapping(value = "/exportVirtualWarehouseStock", method = RequestMethod.POST)
    public Response<PageInfo<StockRecord>> exportVirtualWarehouseStock(@RequestBody StockRecord stockRecord){
        try {
            PageInfo<StockRecord> StockRecords = stockRecordService.exportVirtualWarehouseStock(stockRecord);
            return ResponseMsg.SUCCESS.buildMsg(StockRecords);
        } catch (RomeException e) {
            log.error(e.getMessage(), e);
            return ResponseMsg.FAIL.buildMsg(e.getCode(),e.getMessage());
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return ResponseMsg.FAIL.buildMsg("500000",e.getMessage());
        }
    }

    @ApiOperation(value = "门店库存上传ftp,页面操作", nickname = "shopStockUploadToFtpForPageByAsync", produces = MediaType.APPLICATION_JSON_VALUE)
    @ApiResponse(code = 200, message = "success", response = List.class)
    @RequestMapping(value = "/shopStockUploadToFtpForPageByAsync", method = RequestMethod.POST)
    public Response<String> shopStockUploadToFtpForPageByAsync() {
        try {
            boolean isLock = redisUtil.lock("shopStockUploadToFtpForPageByAsync", "clientId", 600);
            if (isLock) {
                this.vmsStockReportService.shopStockUploadToFtpForPageByAsync();
                return Response.builderSuccess("上传成功");
            }
            return Response.builderSuccess("任务在进行中...请稍后再试");
        } catch (RomeException e) {
            log.error(e.getMessage(), e);
            redisUtil.unLock("shopStockUploadToFtpForPageByAsync", "clientId");
            return Response.builderFail(e.getCode(),e.getMessage());
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            redisUtil.unLock("shopStockUploadToFtpForPageByAsync", "clientId");
            return Response.builderFail(ResCode.STOCK_ERROR_1003_DESC,ResCode.STOCK_ERROR_1003_DESC);
        }
    }
}
