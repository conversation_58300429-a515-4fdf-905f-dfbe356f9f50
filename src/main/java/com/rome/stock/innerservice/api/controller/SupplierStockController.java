package com.rome.stock.innerservice.api.controller;

import com.alibaba.fastjson.JSON;
import com.github.pagehelper.PageInfo;
import com.rome.arch.core.clientobject.Response;
import com.rome.arch.core.exception.RomeException;
import com.rome.stock.common.enums.log.RemoteRetryMethodType;
import com.rome.stock.innerservice.api.dto.supplier.QuerySupplierSkuStockDTO;
import com.rome.stock.innerservice.api.dto.supplier.SupplierSkuStockDTO;
import com.rome.stock.innerservice.constant.ParamValidator;
import com.rome.stock.innerservice.constant.ResponseMsg;
import com.rome.stock.innerservice.domain.repository.RemoteInterfaceLogRepository;
import com.rome.stock.innerservice.domain.service.RemoteRetryLogService;
import com.rome.stock.innerservice.infrastructure.dataobject.RemoteInterfaceLogDO;
import com.rome.stock.innerservice.remote.sap.SapRemoteService;
import io.swagger.annotations.ApiParam;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import com.rome.arch.util.controller.RomeController;

import com.rome.stock.innerservice.domain.service.SupplierStockService;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiResponse;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.RequestParam;

import javax.annotation.Resource;

@Slf4j
@RomeController
@RequestMapping("/stock/v1/supplier_stock")
@Api(tags={"供应商库存服务接口"})
public class SupplierStockController {

    @Autowired
	private SupplierStockService supplierStockService;
    @Resource
    private RemoteInterfaceLogRepository remoteInterfaceLogRepository;


    private ParamValidator validator = ParamValidator.INSTANCE;

    /**
     * 供应商库存查询
     */
    @ApiOperation(value = "计算供应商日结库存", nickname = "calSupplierDailyStock", produces = MediaType.APPLICATION_JSON_VALUE)
    @ApiResponse(code = 200, message = "success", response = SupplierSkuStockDTO.class)
    @GetMapping("/calSupplierDailyStock")
    public Response<Void> calSupplierDailyStock(@ApiParam(name = "stockDate", value= "库存日期") @RequestParam("stockDate") String stockDate) {
        boolean isSuccess = false;
        Response response = null;
        RemoteInterfaceLogDO remoteInterfaceLogDO = remoteInterfaceLogRepository.queryByRequestServiceBizId(RemoteRetryMethodType.CAL_SUPPLIER_DAILY_STOCK.getCode(), stockDate);
        try {
            supplierStockService.calSupplierDailyStock(stockDate);
            isSuccess = true;
            response = ResponseMsg.SUCCESS.buildMsg();
            return response;
        } catch (RomeException e){
            log.error(e.getMessage(), e);
            response = ResponseMsg.FAIL.buildMsg(e.getCode(), e.getMessage());
            return response;
        } catch (Exception e){
            log.error(e.getMessage(), e);
            response = ResponseMsg.EXCEPTION.buildMsg(e.getMessage());
            return response;
        } finally {
            if (!isSuccess) {
                supplierStockService.rollBackCal(stockDate);
            } else {
                supplierStockService.deleteSnapshot(stockDate);
            }
            if (remoteInterfaceLogDO == null){
                remoteInterfaceLogRepository.saveRemoteInterFaceLog(stockDate, stockDate,"local",
                        RemoteRetryMethodType.CAL_SUPPLIER_DAILY_STOCK.getCode(),stockDate, response ,isSuccess);
            } else {
                remoteInterfaceLogRepository.updateRemoteInterFaceLogStatus(remoteInterfaceLogDO.getId(), isSuccess?1:0, JSON.toJSONString(response), 0L);
            }
        }
    }

    /**
     * 供应商库存查询
     */
    @ApiOperation(value = "供应商日结保存库存快照", nickname = "saveStockSnapshotForSupplierDailyStock", produces = MediaType.APPLICATION_JSON_VALUE)
    @ApiResponse(code = 200, message = "success", response = SupplierSkuStockDTO.class)
    @GetMapping("/saveStockSnapshotForSupplierDailyStock")
    public Response<Void> saveStockSnapshotForSupplierDailyStock(@ApiParam(name = "stockDate", value= "库存日期") @RequestParam("stockDate") String stockDate) {
        boolean isSuccess = false;
        Response response = null;
        RemoteInterfaceLogDO remoteInterfaceLogDO = remoteInterfaceLogRepository.queryByRequestServiceBizId(RemoteRetryMethodType.SAVE_STOCK_SNAPSHOT_FOR_SUPPLIER_DAILY_STOCK.getCode(), stockDate);
        try {
            supplierStockService.saveStockSnapshotForSupplierDailyStock(stockDate);
            isSuccess = true;
            response = ResponseMsg.SUCCESS.buildMsg();
            return response;
        } catch (RomeException e){
            log.error(e.getMessage(), e);
            response = ResponseMsg.FAIL.buildMsg(e.getCode(), e.getMessage());
            return response;
        }catch (Exception e){
            log.error(e.getMessage(), e);
            response = ResponseMsg.EXCEPTION.buildMsg(e.getMessage());
            return response;
        } finally {
            if (remoteInterfaceLogDO == null){
                remoteInterfaceLogRepository.saveRemoteInterFaceLog(stockDate, stockDate,"local",
                        RemoteRetryMethodType.SAVE_STOCK_SNAPSHOT_FOR_SUPPLIER_DAILY_STOCK.getCode(),stockDate, response ,isSuccess);
            } else {
                remoteInterfaceLogRepository.updateRemoteInterFaceLogStatus(remoteInterfaceLogDO.getId(), isSuccess?1:0, JSON.toJSONString(response), 0L);
            }
        }
    }



}
