package com.rome.stock.innerservice.api.dto.cloudshop;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

@Data
@NoArgsConstructor
@EqualsAndHashCode
public class RwServiceLabUpdateDTO {

    private Long id;
    /**
     * 标签类型(1: 门店自提 2: 仓库次日达 3: 供应商到家 4: 供应商自提)
     */
    private Integer labType;
    /**
     * 业务类型(1: 门店 2: 仓库 3: 供应商)
     */
    private Integer businessType;

    private List<Long> realWarehouseIdList;

    private Integer skuType;

    private List<RwServiceLabDTO> list;

    private Boolean checkedVisible;

    private Long modifier;
    
}
