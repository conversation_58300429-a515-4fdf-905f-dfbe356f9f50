package com.rome.stock.innerservice.api.dto;

import com.rome.arch.core.dto.DTO;
import com.rome.stock.innerservice.api.dto.groupbuy.ReservationDetailDTO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.util.List;

@Data
public class DeliveryReservationDTO extends DTO {

    /**
     * 外部单号
     */
    @ApiModelProperty(value="外部单号")
    @NotBlank(message = "外部单号不能为空")
    private String outRecordCode;

    @NotNull
    @ApiModelProperty(value="明细集合")
    private List<ReservationDetailDTO>  reservationDetails;
}    
