/**
 * Filename EntrySaleStockDTO.java
 * Company 上海来伊份科技有限公司。
 * <AUTHOR>
 * @version 
 */
package com.rome.stock.innerservice.api.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;

/**
 * 库存统计子对象：根据skuCode查询仓库总库存
 * <AUTHOR>
 * @since 2022-02-25 11:43:13
 */
@Data
@NoArgsConstructor
@EqualsAndHashCode
public class StockStatisticsWithWarehouseQtyBySkuDTO {
    @ApiModelProperty("商品编码")
    private String skuCode;

    @ApiModelProperty("仓库总库存")
    private BigDecimal warehouseTotalQty;
}
