package com.rome.stock.innerservice.api.dto.frontrecord;

import javax.validation.constraints.Digits;
import javax.validation.constraints.NotEmpty;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;

/**
 * 类ShopTradeDetail的实现描述：门店调整单明细
 *
 * <AUTHOR> 2019/4/21 17:06
 */
@Data
@EqualsAndHashCode
public class ShopAdjustDetailDTO {

	@ApiModelProperty(value = "数量")
	@Digits(integer = 9, fraction = 3,message="超过范围,小数3位有效位，整数9位有效位")
	private BigDecimal skuQty;

	@ApiModelProperty(value = "sku编号")
	private Long skuId;

	@ApiModelProperty(value = "所属单据编码")
	private String recordCode;

	@ApiModelProperty(value = "单位")
	private String unit;

	@ApiModelProperty(value = "单位编码")
	@NotEmpty(message="单位code不能为空")
	private String unitCode;

	@ApiModelProperty(value = "商品编号")
	private String skuCode;

	@ApiModelProperty(value = "商品名称")
	private String skuName;

	@ApiModelProperty(value = "商品规格")
	private String skuStandard;

	@ApiModelProperty(value = "基本单位数量")
	private BigDecimal basicQty;

	@ApiModelProperty(value = "基本单位")
	private String basicUnit;

	@ApiModelProperty(value = "实际出库数量")
	private BigDecimal actualQty;

	@ApiModelProperty(value = "剩余数量")
	private BigDecimal remindQty;
}
