package com.rome.stock.innerservice.api.dto.frontrecord;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotBlank;

@Data
@EqualsAndHashCode
public class UpdateBillCodeDTO {

    @ApiModelProperty(value = "物流公司编码")
    @NotBlank
    private String logisticsCode;

    @ApiModelProperty(value = "物流公司名称")
    @NotBlank
    private String logisticsName;

    @ApiModelProperty(value = "运单号")
    @NotBlank
    private String billCode;

    @ApiModelProperty(value = "包裹号")
    @NotBlank
    private String packageCode;
}
