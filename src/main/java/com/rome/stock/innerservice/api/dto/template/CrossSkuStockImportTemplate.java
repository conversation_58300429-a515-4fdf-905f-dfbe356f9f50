/**
 * Filename RwStockChangeFlowExportTemplate.java
 * Company 上海来伊份科技有限公司。
 * <AUTHOR>
 * @version 
 */
package com.rome.stock.innerservice.api.dto.template;

import cn.afterturn.easypoi.excel.annotation.Excel;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 跨境电商导入
 * <AUTHOR>
 * @since 2021-04-21 14:26:56
 */
@Data
@EqualsAndHashCode
public class CrossSkuStockImportTemplate {

    @Excel(name = "实仓编码")
    private String realWarehouseCode;

    @Excel(name = "商品编号")
    private String skuCode;
    
    @Excel(name = "数量")
    private String skuQty;

}
