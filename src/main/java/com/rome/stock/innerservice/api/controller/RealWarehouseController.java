package com.rome.stock.innerservice.api.controller;

import com.github.pagehelper.PageInfo;
import com.rome.arch.core.clientobject.Response;
import com.rome.arch.core.exception.RomeException;
import com.rome.arch.util.controller.RomeController;
import com.rome.stock.common.annotation.BathParamsValidate;
import com.rome.stock.common.enums.warehouse.WarehouseRecordTypeVO;
import com.rome.stock.innerservice.api.dto.*;
import com.rome.stock.innerservice.common.ResCode;
import com.rome.stock.innerservice.common.ValidList;
import com.rome.stock.innerservice.constant.ParamValidator;
import com.rome.stock.innerservice.constant.RealWarehouseRankVO;
import com.rome.stock.innerservice.constant.RealWarehouseTypeVO;
import com.rome.stock.innerservice.constant.ResponseMsg;
import com.rome.stock.innerservice.domain.repository.SapInterfaceLogRepository;
import com.rome.stock.innerservice.domain.service.RealWarehouseService;
import com.rome.stock.innerservice.domain.service.UnstandardService;
import com.rome.stock.innerservice.facade.StockDataFacade;
import com.rome.stock.innerservice.facade.StockOnlineOrderFacade;
import com.rome.stock.innerservice.remote.base.dto.StoreDTO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import io.swagger.annotations.ApiResponse;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;


@Slf4j
@RomeController
@RequestMapping("/stock/real_warehouse")
@Api(tags={"实仓管理接口"})
public class RealWarehouseController {

    private ParamValidator validator = ParamValidator.INSTANCE;

    @Autowired
    private RealWarehouseService realWarehouseService;

    @Autowired
    private UnstandardService unstandardService;

    @Resource
    private SapInterfaceLogRepository sapInterfaceLogRepository;

    @ApiOperation(value = "根据实仓主键查询实仓信息", nickname = "find_real_warehouse", produces = MediaType.APPLICATION_JSON_VALUE)
    @ApiResponse(code = 200, message = "success", response = RealWarehouse.class)
    @GetMapping("/id/{realWarehouseId}")
    public Response<RealWarehouse> findByRealWarehouseId(@PathVariable("realWarehouseId") String realWarehouseId) {
        if (! validator.validNumber(realWarehouseId)) {
            return ResponseMsg.PARAM_ERROR.buildMsg();
        }
        try {
            RealWarehouse realWarehouse = realWarehouseService.findByRealWarehouseId(Long.parseLong(realWarehouseId));
            return ResponseMsg.SUCCESS.buildMsg(realWarehouse);
        } catch (RomeException e) {
            log.error(e.getMessage(), e);
            return ResponseMsg.FAIL.buildMsg(e.getCode(), e.getMessage());
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return ResponseMsg.EXCEPTION.buildMsg();
        }
    }

    @ApiOperation(value = "根据实仓仓库编码查询实仓信息", nickname = "find_real_warehouse", produces = MediaType.APPLICATION_JSON_VALUE)
    @ApiResponse(code = 200, message = "success", response = RealWarehouse.class)
    @GetMapping("/code/{realWarehouseCode}")
    public Response<RealWarehouse> findByRealWarehouseCode(@PathVariable("realWarehouseCode") String realWarehouseCode) {
        if(! validator.validStr(realWarehouseCode)) {
            return ResponseMsg.PARAM_ERROR.buildMsg();
        }
        try {
            RealWarehouse realWarehouse = realWarehouseService.findByRealWarehouseCode(realWarehouseCode);
            return ResponseMsg.SUCCESS.buildMsg(realWarehouse);
        } catch (RomeException e) {
            log.error(e.getMessage(), e);
            return ResponseMsg.FAIL.buildMsg(e.getCode(), e.getMessage());
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return ResponseMsg.EXCEPTION.buildMsg();
        }
    }

    @ApiOperation(value = "根据条件查询实仓信息,分页", nickname = "find_real_warehouse", produces = MediaType.APPLICATION_JSON_VALUE)
    @ApiResponse(code = 200, message = "success", response = RealWarehouse.class)
    @PostMapping("/condition")
    public Response<PageInfo<RealWarehouse>> findByRealWarehouseConditionPage(@RequestBody RealWarehouseParamDTO paramDTO) {
        if(! validator.validParam(paramDTO)) {
            return ResponseMsg.PARAM_ERROR.buildMsg();
        }
        try {
            PageInfo<RealWarehouse> realWarehouseList = realWarehouseService.findByRealWarehouseCondition(paramDTO);
            return ResponseMsg.SUCCESS.buildMsg(realWarehouseList);
        } catch (RomeException e) {
            log.error(e.getMessage(), e);
            return ResponseMsg.FAIL.buildMsg(e.getCode(), e.getMessage());
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return ResponseMsg.EXCEPTION.buildMsg();
        }
    }

    @ApiOperation(value = "查询所有实仓信息", nickname = "find_real_warehouse", produces = MediaType.APPLICATION_JSON_VALUE)
    @ApiResponse(code = 200, message = "success", response = RealWarehouse.class)
    @GetMapping("/list")
    public Response<List<RealWarehouse>> findRealWarehouseAllList() {
        try {
            List<RealWarehouse> realWarehouseList = realWarehouseService.findRealWarehouseAllList();
            return ResponseMsg.SUCCESS.buildMsg(realWarehouseList);
        } catch (RomeException e) {
            log.error(e.getMessage(), e);
            return ResponseMsg.FAIL.buildMsg(e.getCode(), e.getMessage());
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return ResponseMsg.EXCEPTION.buildMsg();
        }
    }
    @ApiOperation(value = "根据类型查询实仓", nickname = "queryByType", produces = MediaType.APPLICATION_JSON_VALUE)
    @ApiResponse(code = 200, message = "success", response = RealWarehouse.class)
    @GetMapping("/queryByType")
    public Response<List<RealWarehouse>> queryByType(@RequestParam Integer type) {
        try {
            List<RealWarehouse> realWarehouseList = realWarehouseService.queryRealWarehouseByRWType(type);
            return ResponseMsg.SUCCESS.buildMsg(realWarehouseList);
        } catch (RomeException e) {
            log.error(e.getMessage(), e);
            return ResponseMsg.FAIL.buildMsg(e.getCode(), e.getMessage());
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return ResponseMsg.EXCEPTION.buildMsg();
        }
    }

    @ApiOperation(value = "根据仓库标识查仓库", nickname = "warehouseStoreIdenti", produces = MediaType.APPLICATION_JSON_VALUE)
    @ApiResponse(code = 200, message = "success", response = RealWarehouse.class)
    @GetMapping("/queryByIdenti")
    public Response<List<RealWarehouse>> queryByIdenti(@RequestParam Integer warehouseStoreIdenti) {
        try {
            List<RealWarehouse> realWarehouseList = realWarehouseService.queryRealWarehouseByWarehouseStoreIdenti(warehouseStoreIdenti);
            return ResponseMsg.SUCCESS.buildMsg(realWarehouseList);
        } catch (RomeException e) {
            log.error(e.getMessage(), e);
            return ResponseMsg.FAIL.buildMsg(e.getCode(), e.getMessage());
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return ResponseMsg.EXCEPTION.buildMsg();
        }
    }





    @ApiOperation(value = "根据类型查询实仓Frontend", nickname = "queryByTypeForFrontend", produces = MediaType.APPLICATION_JSON_VALUE)
    @ApiResponse(code = 200, message = "success", response = RealWarehouse.class)
    @GetMapping("/queryByTypeForFrontend")
    public Response<List<RealWarehouse>> queryByTypeForFrontend(@RequestParam Integer type) {
        try {
            List<RealWarehouse> realWarehouseList = realWarehouseService.queryByTypeForFrontend(type);
            return ResponseMsg.SUCCESS.buildMsg(realWarehouseList);
        } catch (RomeException e) {
            log.error(e.getMessage(), e);
            return ResponseMsg.FAIL.buildMsg(e.getCode(), e.getMessage());
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return ResponseMsg.EXCEPTION.buildMsg(e.getMessage());
        }
    }



    @ApiOperation(value = "查询所有实仓类型信息", nickname = "rwTypeList", produces = MediaType.APPLICATION_JSON_VALUE)
    @ApiResponse(code = 200, message = "success", response = RealWarehouse.class)
    @GetMapping("/rwTypeList")
    public Response<Map<Integer, String>> rwTypeList() {
        try {
            return ResponseMsg.SUCCESS.buildMsg(RealWarehouseTypeVO.getRealWarehouseTypeList());
        } catch (RomeException e) {
            log.error(e.getMessage(), e);
            return ResponseMsg.FAIL.buildMsg(e.getCode(), e.getMessage());
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return ResponseMsg.EXCEPTION.buildMsg();
        }
    }

    @ApiOperation(value = "查询所有实仓层级信息", nickname = "rwRankList", produces = MediaType.APPLICATION_JSON_VALUE)
    @ApiResponse(code = 200, message = "success", response = RealWarehouse.class)
    @GetMapping("/rwRankList")
    public Response<Map<Integer, String>> rwRankList() {
        try {
            return ResponseMsg.SUCCESS.buildMsg(RealWarehouseRankVO.getRealWarehouseRankList());
        } catch (RomeException e) {
            log.error(e.getMessage(), e);
            return ResponseMsg.FAIL.buildMsg(e.getCode(), e.getMessage());
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return ResponseMsg.EXCEPTION.buildMsg();
        }
    }

    @ApiOperation(value = "新增实仓信息", nickname = "add_real_warehouse", produces = MediaType.APPLICATION_JSON_VALUE)
    @ApiResponse(code = 200, message = "success", response = RealWarehouse.class)
    @PostMapping("/add_real_warehouse")
    public Response addRealWarehouse(@RequestBody RealWarehouseAddDTO realWarehouseAddDTO) {
        try {
            realWarehouseService.addRealWarehouse(realWarehouseAddDTO);
            return ResponseMsg.SUCCESS.buildMsg();
        } catch (RomeException e) {
            log.error(e.getMessage(), e);
            return ResponseMsg.FAIL.buildMsg(e.getCode(), e.getMessage());
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return ResponseMsg.EXCEPTION.buildMsg();
        }
    }

    @ApiOperation(value = "编辑实仓信息", nickname = "update_real_warehouse", produces = MediaType.APPLICATION_JSON_VALUE)
    @ApiResponse(code = 200, message = "success", response = RealWarehouse.class)
    @PostMapping("/update_real_warehouse/{id}")
    public Response updateRealWarehouse(@PathVariable("id")String id, @RequestBody RealWarehouse realWarehouse) {
        try {
            if (! validator.validNumber(id)) {
                return ResponseMsg.PARAM_ERROR.buildMsg();
            }
            if (! validator.validParam(realWarehouse)) {
                return ResponseMsg.PARAM_ERROR.buildMsg();
            }
            realWarehouse.setId(Long.parseLong(id));
            realWarehouseService.updateRealWarehouse(realWarehouse);
            return ResponseMsg.SUCCESS.buildMsg();
        } catch (RomeException e) {
            log.error(e.getMessage(), e);
            return ResponseMsg.FAIL.buildMsg(e.getCode(), e.getMessage());
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return ResponseMsg.EXCEPTION.buildMsg();
        }
    }

    @ApiOperation(value = "启用实仓", nickname = "enable_real_warehouse", produces = MediaType.APPLICATION_JSON_VALUE)
    @ApiResponse(code = 200, message = "success", response = RealWarehouse.class)
    @PostMapping("/enable/{realWarehouseId}")
    public Response enableRealWarehouse(@PathVariable("realWarehouseId") String realWarehouseId, Long userId) {
        if (! validator.validNumber(realWarehouseId)) {
            return ResponseMsg.PARAM_ERROR.buildMsg();
        }
        try {
            realWarehouseService.enableRealWarehouse(Long.parseLong(realWarehouseId), userId);
            return ResponseMsg.SUCCESS.buildMsg();
        } catch (RomeException e) {
            log.error(e.getMessage(), e);
            return ResponseMsg.FAIL.buildMsg(e.getCode(), e.getMessage());
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return ResponseMsg.EXCEPTION.buildMsg();
        }
    }

    @ApiOperation(value = "停用实仓", nickname = "disable_real_warehouse", produces = MediaType.APPLICATION_JSON_VALUE)
    @ApiResponse(code = 200, message = "success", response = RealWarehouse.class)
    @PostMapping("/disable/{realWarehouseId}")
    public Response disableRealWarehouse(@PathVariable("realWarehouseId") String realWarehouseId, Long userId) {
        if (! validator.validNumber(realWarehouseId)) {
            return ResponseMsg.PARAM_ERROR.buildMsg();
        }
        try {
            realWarehouseService.disableRealWarehouse(Long.parseLong(realWarehouseId), userId);
            return ResponseMsg.SUCCESS.buildMsg();
        } catch (RomeException e) {
            log.error(e.getMessage(), e);
            return ResponseMsg.FAIL.buildMsg(e.getCode(), e.getMessage());
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return ResponseMsg.EXCEPTION.buildMsg();
        }
    }

    @ApiOperation(value = "实仓分配虚仓", nickname = "allot_virtual_warehouse", produces = MediaType.APPLICATION_JSON_VALUE)
    @ApiResponse(code = 200, message = "success", response = RealWarehouse.class)
    @PostMapping("/allot_vm/{id}")
    public Response allotVirtualWarehouse(@PathVariable("id") String realWarehouseId, @RequestBody RealWarehouseParamDTO paramDTO) {
        if (! validator.validParam(paramDTO)
                || ! validator.validCollection(paramDTO.getVwIdSyncRateDTOList())){
            return ResponseMsg.PARAM_ERROR.buildMsg();
        }
        if (! validator.validNumber(realWarehouseId)) {
            return ResponseMsg.PARAM_ERROR.buildMsg();
        }
        /*if (! validator.validNumber(paramDTO.getUserId())) {
            return ResponseMsg.PARAM_ERROR.buildMsg();
        }*/
        try {
            paramDTO.setRealWarehouseId(Long.parseLong(realWarehouseId));
            realWarehouseService.allotVirtualWarehouse(paramDTO);
            if(paramDTO.getVwIdSyncRateDTOList() != null) {
                List<Long> vIdList = new ArrayList<>(paramDTO.getVwIdSyncRateDTOList().size());
                for (VWIdSyncRateDTO dto : paramDTO.getVwIdSyncRateDTOList()) {
                    vIdList.add(dto.getId());
                }
                StockDataFacade.delRouteInfoByPriorityTempleByVmIdList(vIdList);
            }

            return ResponseMsg.SUCCESS.buildMsg();
        } catch (RomeException e) {
            log.error(e.getMessage(), e);
            return ResponseMsg.FAIL.buildMsg(e.getCode(), e.getMessage());
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return ResponseMsg.EXCEPTION.buildMsg();
        }
    }

    @ApiOperation(value = "根据实仓主键ID查询实仓区域信息", nickname = "query_real_warehouse_area", produces = MediaType.APPLICATION_JSON_VALUE)
    @ApiResponse(code = 200, message = "success", response = RealWarehouse.class)
    @GetMapping("/area_list/{realWarehouseId}")
    public Response<List<RealWarehouseArea>> findAreaListByRealWarehouseId(@PathVariable("realWarehouseId") String realWarehouseId) {
        if (! validator.validNumber(realWarehouseId)) {
            return ResponseMsg.PARAM_ERROR.buildMsg();
        }
        try {
            List<RealWarehouseArea> realWarehouseAreaDoList =
                    realWarehouseService.findAreaListByRealWarehouseId(Long.parseLong(realWarehouseId));
            return ResponseMsg.SUCCESS.buildMsg(realWarehouseAreaDoList);
        } catch (RomeException e) {
            log.error(e.getMessage(), e);
            return ResponseMsg.FAIL.buildMsg(e.getCode(), e.getMessage());
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return ResponseMsg.EXCEPTION.buildMsg();
        }
    }

    @ApiOperation(value = "分配实仓覆盖区域区域", nickname = "allot_real_warehouse_area", produces = MediaType.APPLICATION_JSON_VALUE)
    @ApiResponse(code = 200, message = "success", response = RealWarehouse.class)
    @PostMapping("/allot_area/{realWarehouseId}")
    public Response allotRealWarehouseArea(@PathVariable("realWarehouseId") Long realWarehouseId, @RequestBody RealWarehouseParamDTO paramDTO) {
        if (! validator.validParam(paramDTO)
                || ! validator.validCollection(paramDTO.getRealWarehouseAreaAddDTOList())) {
            return ResponseMsg.PARAM_ERROR.buildMsg();
        }
        try {
            realWarehouseService.allotRealWarehouseArea(realWarehouseId, paramDTO);
            StockOnlineOrderFacade.delRouteInfoByCoverFocusSale(realWarehouseId);
            return ResponseMsg.SUCCESS.buildMsg();
        } catch (RomeException e) {
            log.error(e.getMessage(), e);
            return ResponseMsg.FAIL.buildMsg(e.getCode(), e.getMessage());
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return ResponseMsg.EXCEPTION.buildMsg();
        }
    }

    /**
     * 获取仓库所有类型(从仓库类型枚举类中获取)
     * @return
     */
    @ApiOperation(value = "获取仓库所有类型", nickname = "list_real_warehouse_type", produces = MediaType.APPLICATION_JSON_VALUE)
    @ApiResponse(code = 200, message = "success", response = Map.class)
    @GetMapping("/listRealWarehouseType/{type}")
    public Response<Map<Integer, Object>> getWarehouseRecordType(@PathVariable("type") String type) {
        // 字符串非空判断
        if(!validator.validStr(type)) {
            return ResponseMsg.PARAM_ERROR.buildMsg();
        }
        try {
            Map<Integer, Object> warehouseType = WarehouseRecordTypeVO.getRealWareHouseType(type);
            return Response.builderSuccess(warehouseType);
        } catch (RomeException e){
            log.error(e.getMessage(), e);
            return Response.builderFail(e.getCode(), e.getMessage());
        } catch (Exception e){
            log.error(e.getMessage(), e);
            return Response.builderFail(ResCode.STOCK_ERROR_1003, ResCode.STOCK_ERROR_1003_DESC);
        }
    }

    @ApiOperation(value = "根据条件查询实仓信息-运营平台查询接口,不分页", nickname = "find_real_warehouse", produces = MediaType.APPLICATION_JSON_VALUE)
    @ApiResponse(code = 200, message = "success", response = RealWarehouse.class)
    @PostMapping("/queryForAdmin")
    public Response<List<RealWarehouse>> findByWhConditionForAdmin(@RequestBody RealWarehouseParamDTO paramDTO) {
        if(! validator.validParam(paramDTO)) {
            return ResponseMsg.PARAM_ERROR.buildMsg();
        }
        try {
            List<RealWarehouse> realWarehouseList = realWarehouseService.queryWhByConditionForAdmin(paramDTO);
            return ResponseMsg.SUCCESS.buildMsg(realWarehouseList);
        } catch (RomeException e) {
            log.error(e.getMessage(), e);
            return ResponseMsg.FAIL.buildMsg(e.getCode(), e.getMessage());
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return ResponseMsg.EXCEPTION.buildMsg();
        }
    }

    @ApiOperation(value = "根据工厂code查询仓库信息-含门店", nickname = "queryRealWarehouseByFactoryCode", produces = MediaType.APPLICATION_JSON_VALUE)
    @ApiResponse(code = 200, message = "success")
    @PostMapping("/queryRealWarehouseByFactoryCode")
    public Response<List<RealWarehouse>> queryRealWarehouseByFactoryCode(@RequestBody String factoryCode) {
        try {
            List<RealWarehouse> realWarehouseList = realWarehouseService.queryRealWarehouseByFactoryCode(factoryCode);
            return ResponseMsg.SUCCESS.buildMsg(realWarehouseList);
        } catch (RomeException e) {
            log.error(e.getMessage(), e);
            return ResponseMsg.FAIL.buildMsg(e.getCode(), e.getMessage());
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return ResponseMsg.EXCEPTION.buildMsg();
        }
    }

    @ApiOperation(value = "根据工厂code查询仓库信息-非门店", nickname = "queryRealWarehouseByFactoryCodeNoShop", produces = MediaType.APPLICATION_JSON_VALUE)
    @ApiResponse(code = 200, message = "success")
    @PostMapping("/queryRealWarehouseByFactoryCodeNoShop")
    public Response<List<RealWarehouse>> queryRealWarehouseByFactoryCodeNoShop(@RequestBody String factoryCode) {
        try {
            List<RealWarehouse> realWarehouseList = realWarehouseService.queryRealWarehouseByFactoryCodeNoShop(factoryCode);
            return ResponseMsg.SUCCESS.buildMsg(realWarehouseList);
        } catch (RomeException e) {
            log.error(e.getMessage(), e);
            return ResponseMsg.FAIL.buildMsg(e.getCode(), e.getMessage());
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return ResponseMsg.EXCEPTION.buildMsg();
        }
    }

    @ApiOperation(value = "查询非门店仓", nickname = "queryRealWarehouseNoShop", produces = MediaType.APPLICATION_JSON_VALUE)
    @ApiResponse(code = 200, message = "success")
    @GetMapping("/queryRealWarehouseNoShop")
    public Response<List<RealWarehouse>> queryRealWarehouseNoShop() {
        try {
            List<RealWarehouse> realWarehouseList = realWarehouseService.queryRealWarehouseNoShop();
            return ResponseMsg.SUCCESS.buildMsg(realWarehouseList);
        } catch (RomeException e) {
            log.error(e.getMessage(), e);
            return ResponseMsg.FAIL.buildMsg(e.getCode(), e.getMessage());
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return ResponseMsg.EXCEPTION.buildMsg();
        }
    }

    @ApiOperation(value = "根据实仓ID查询实仓的sku", nickname = "querySkuIdByWhId", produces = MediaType.APPLICATION_JSON_VALUE)
    @ApiResponse(code = 200, message = "success")
    @GetMapping("/querySkuIdByWhId")
    public Response<List<RealWarehouseStockDTO>> querySkuIdByWhId(@RequestParam("realWarehouseId") String realWarehouseId) {
        try {
            List<RealWarehouseStockDTO> stockList = realWarehouseService.querySkuIdByWhId(realWarehouseId);
            return ResponseMsg.SUCCESS.buildMsg(stockList);
        } catch (RomeException e) {
            log.error(e.getMessage(), e);
            return ResponseMsg.FAIL.buildMsg(e.getCode(), e.getMessage());
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return ResponseMsg.EXCEPTION.buildMsg();
        }
    }
    @ApiOperation(value = "根据批量工厂编码查询实仓信息", nickname = "queryRealWarehousesByFactoryCodes", produces = MediaType.APPLICATION_JSON_VALUE)
    @ApiResponse(code = 200, message = "success")
    @GetMapping("/queryRealWarehousesByFactoryCodes")
    public Response<List<RealWarehouse>> queryRealWarehousesByFactoryCodes(@RequestParam("factoryCodes") List<String> factoryCodes) {
        try {
            List<RealWarehouse> RealWarehouses = realWarehouseService.queryRealWarehousesByFactoryCodes(factoryCodes);
            return ResponseMsg.SUCCESS.buildMsg(RealWarehouses);
        } catch (RomeException e) {
            log.error(e.getMessage(), e);
            return ResponseMsg.FAIL.buildMsg(e.getCode(), e.getMessage());
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return ResponseMsg.EXCEPTION.buildMsg();
        }
    }

    @ApiOperation(value = "根据条件查询非标品信息,分页", nickname = "queryUnStandardByCondition", produces = MediaType.APPLICATION_JSON_VALUE)
    @ApiResponse(code = 200, message = "success", response = UnStandardDTO.class)
    @PostMapping("/queryUnStandardByCondition")
    public Response<PageInfo<UnStandardDTO>> queryUnStandardByCondition(@RequestBody UnStandardDTO unStandardDTO) {
        if(! validator.validParam(unStandardDTO)) {
            return ResponseMsg.PARAM_ERROR.buildMsg();
        }
        try {
            PageInfo<UnStandardDTO> unStandardList = unstandardService.queryUnStandardByCondition(unStandardDTO);
            return ResponseMsg.SUCCESS.buildMsg(unStandardList);
        } catch (RomeException e) {
            log.error(e.getMessage(), e);
            return ResponseMsg.FAIL.buildMsg(e.getCode(), e.getMessage());
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return ResponseMsg.EXCEPTION.buildMsg();
        }
    }


    @ApiOperation(value = "新增非标品信息", nickname = "saveUnStandard", produces = MediaType.APPLICATION_JSON_VALUE)
    @ApiResponse(code = 200, message = "success", response = String.class)
    @PostMapping("/saveUnStandard")
    public Response saveUnStandard(@RequestBody List<UnStandardDTO> unStandardDTOList) {
        try {
            unstandardService.saveUnStandard(unStandardDTOList);
            return ResponseMsg.SUCCESS.buildMsg();
        } catch (RomeException e) {
            log.error(e.getMessage(), e);
            return ResponseMsg.FAIL.buildMsg(e.getCode(), e.getMessage());
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return ResponseMsg.EXCEPTION.buildMsg();
        }
    }

    @ApiOperation(value = "编辑非标品信息", nickname = "updateUnStandardByWhere", produces = MediaType.APPLICATION_JSON_VALUE)
    @ApiResponse(code = 200, message = "success", response = String.class)
    @PostMapping("/updateUnStandardByWhere")
    public Response updateUnStandardByWhere(@RequestBody UnStandardDTO unStandardDTO) {
        try {
            if (!validator.validParam(unStandardDTO)) {
                return ResponseMsg.PARAM_ERROR.buildMsg();
            }
            unstandardService.updateUnStandardByWhere(unStandardDTO);
            return ResponseMsg.SUCCESS.buildMsg();
        } catch (RomeException e) {
            log.error(e.getMessage(), e);
            return ResponseMsg.FAIL.buildMsg(e.getCode(), e.getMessage());
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return ResponseMsg.EXCEPTION.buildMsg();
        }
    }

    @ApiOperation(value = "非标品库存调整", nickname = "unStandardStockAdjust", produces = MediaType.APPLICATION_JSON_VALUE)
    @ApiResponse(code = 200, message = "success", response = String.class)
    @PostMapping("/unStandardStockAdjust")
    public Response unStandardStockAdjust() {
        try {
            unstandardService.unStandardStockAdjust();
            return ResponseMsg.SUCCESS.buildMsg();
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return ResponseMsg.EXCEPTION.buildMsg();
        }
    }

    @ApiOperation(value = "查询虚拟商品实仓信息", nickname = "queryVirtualSkuRealWarehouse", produces = MediaType.APPLICATION_JSON_VALUE)
    @ApiResponse(code = 200, message = "success", response = RealWarehouse.class)
    @GetMapping("/queryVirtualSkuRealWarehouse")
    public Response<RealWarehouse> queryVirtualSkuRealWarehouse() {
        try {
            RealWarehouse realWarehouse = realWarehouseService.queryVirtualSkuRealWarehouse();
            return ResponseMsg.SUCCESS.buildMsg(realWarehouse);
        } catch (RomeException e) {
            log.error(e.getMessage(), e);
            return ResponseMsg.FAIL.buildMsg(e.getCode(), e.getMessage());
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return ResponseMsg.EXCEPTION.buildMsg();
        }
    }

@ApiOperation(value = "根据工厂code和仓库类型查询仓库信息", nickname = "queryRealWarehouseByFactoryCodeAndRWType", produces = MediaType.APPLICATION_JSON_VALUE)
    @ApiResponse(code = 200, message = "success")
    @GetMapping("/queryRealWarehouseByFactoryCodeAndRWType")
    public Response<List<RealWarehouse>> queryRealWarehouseByFactoryCodeAndRWType(@RequestParam("factoryCode") String factoryCode, @RequestParam("realWarehouseTypeList") List<Integer> realWarehouseTypeList) {
        try {
            if(factoryCode == null || factoryCode.equals("")) {
                return ResponseMsg.FAIL.buildMsg(ResCode.STOCK_ERROR_1002, "工厂编码为空");
            }
            if(realWarehouseTypeList == null || realWarehouseTypeList.isEmpty()) {
                return ResponseMsg.FAIL.buildMsg(ResCode.STOCK_ERROR_1002, "仓库类型为空");
            }
            List<RealWarehouse> RealWarehouses = realWarehouseService.queryRealWarehouseByFactoryCodeAndRWType(factoryCode, realWarehouseTypeList);
            return ResponseMsg.SUCCESS.buildMsg(RealWarehouses);
        } catch (RomeException e) {
            log.error(e.getMessage(), e);
            return ResponseMsg.FAIL.buildMsg(e.getCode(), e.getMessage());
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return ResponseMsg.EXCEPTION.buildMsg();
        }
    }
	
	@ApiOperation(value = "根据门店编码查询默认发货仓", nickname = "queryDefaultRealWarehouseByShopCode", produces = MediaType.APPLICATION_JSON_VALUE)
	@ApiResponse(code = 200, message = "success", response = Map.class)
	@RequestMapping(value = "/queryDefaultRealWarehouseByShopCode/{shopCode}", method = RequestMethod.GET)
	public Response<Map<String, String>> queryDefaultRealWarehouseByShopCode(@PathVariable("shopCode") String shopCode) {
		if (!validator.validStr(shopCode)) {
	         return ResponseMsg.PARAM_ERROR.buildMsg();
	     }
	    try {
	    	Map<String, String> map = realWarehouseService.queryDefaultRealWarehouseByShopCode(shopCode);
	        return ResponseMsg.SUCCESS.buildMsg(map);
	    } catch (RomeException e) {
	        log.error(e.getMessage(), e);
	        return ResponseMsg.FAIL.buildMsg(e.getCode(), e.getMessage());
	    } catch (Exception e) {
	        log.error(e.getMessage(), e);
	        return ResponseMsg.EXCEPTION.buildMsg();
	    }
	}

	@ApiOperation(value = "查询实仓信息列表（排除门店）", nickname = "query_rw_list", produces = MediaType.APPLICATION_JSON_VALUE)
    @ApiResponse(code = 200, message = "success", response = RealWarehouse.class)
    @GetMapping("/queryRWList")
    public Response<List<RealWarehouse>> queryRWList(@RequestParam(value = "nameOrCode",required = false)String nameOrCode) {
        try {
            List<RealWarehouse> realWarehouseList = realWarehouseService.queryRWList(nameOrCode);
            return ResponseMsg.SUCCESS.buildMsg(realWarehouseList);
        } catch (RomeException e) {
            log.error(e.getMessage(), e);
            return ResponseMsg.FAIL.buildMsg(e.getCode(), e.getMessage());
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return ResponseMsg.EXCEPTION.buildMsg();
        }
    }

    @ApiOperation(value = "根据门店code + 仓库类型 查询门店发货仓", nickname = "queryRealWarehouseByShopCodeAndType", produces = MediaType.APPLICATION_JSON_VALUE)
    @ApiResponse(code = 200, message = "success", response = RealWarehouse.class)
    @GetMapping("/queryRealWarehouseByShopCodeAndType")
    public Response<RealWarehouse> queryRealWarehouseByShopCodeAndType(@RequestParam("shopCode")String shopCode,@RequestParam("type")Integer type){
        try{
            RealWarehouseTypeVO realWarehouseTypeVO= RealWarehouseTypeVO.getTypeVOByType(type);
            if(Objects.nonNull(realWarehouseTypeVO)){
                RealWarehouse realWarehouse = realWarehouseService.queryRealWarehouseByShopCodeAndType(shopCode, realWarehouseTypeVO);
                return ResponseMsg.SUCCESS.buildMsg(realWarehouse);
            }
             return ResponseMsg.SUCCESS.buildMsg(null);
        }catch (RomeException e){
            log.error(e.getMessage(), e);
            return ResponseMsg.FAIL.buildMsg(e.getCode(), e.getMessage());
        }catch (Exception e){
            log.error(e.getMessage(), e);
            return ResponseMsg.EXCEPTION.buildMsg();
        }
    }


    @ApiOperation(value = "根据虚仓code查询实仓", nickname = "queryRealWarehouseByVmCode", produces = MediaType.APPLICATION_JSON_VALUE)
    @ApiResponse(code = 200, message = "success", response = RealWarehouse.class)
    @PostMapping("/queryRealWarehouseByVmCode")
    public Response<RealWarehouse> queryRealWarehouseByVmCode(@RequestParam("virtualWarehouseCode")String virtualWarehouseCode){
        try{
            RealWarehouse realWarehouse = realWarehouseService.queryRealWarehouseByVmCode(virtualWarehouseCode);
            return ResponseMsg.SUCCESS.buildMsg(realWarehouse);
        }catch (RomeException e){
            log.error(e.getMessage(), e);
            return ResponseMsg.FAIL.buildMsg(e.getCode(), e.getMessage());
        }catch (Exception e){
            log.error(e.getMessage(), e);
            return ResponseMsg.EXCEPTION.buildMsg();
        }
    }

    @ApiOperation(value = "根据实仓内部编码查询实仓", nickname = "queryRealWarehouseByRWCode", produces = MediaType.APPLICATION_JSON_VALUE)
    @ApiResponse(code = 200, message = "success", response = RealWarehouse.class)
    @PostMapping("/queryRealWarehouseByRWCode")
    public Response<RealWarehouse> queryRealWarehouseByRWCode(@RequestParam("realWarehouseCode")String realWarehouseCode){
        try{
            RealWarehouse realWarehouse = realWarehouseService.queryRealWarehouseByRealWarehouseCode(realWarehouseCode);
            return ResponseMsg.SUCCESS.buildMsg(realWarehouse);
        }catch (RomeException e){
            log.error(e.getMessage(), e);
            return ResponseMsg.FAIL.buildMsg(e.getCode(), e.getMessage());
        }catch (Exception e){
            log.error(e.getMessage(), e);
            return ResponseMsg.EXCEPTION.buildMsg();
        }
    }


    @ApiOperation(value = "批量查询根据实仓内部编码查询实仓", nickname = "queryRealWarehouseByRWCodeList", produces = MediaType.APPLICATION_JSON_VALUE)
    @ApiResponse(code = 200, message = "success", response = RealWarehouse.class)
    @PostMapping("/queryRealWarehouseByRWCodeList")
    public Response<List<RealWarehouse>> queryRealWarehouseByRWCodeList(@RequestParam("realWarehouseCodeList")List<String> realWarehouseCodeList){
        try{
            List<RealWarehouse> realWarehouses = realWarehouseService.queryRealWarehouseByRealWarehouseCodeList(realWarehouseCodeList);
            return ResponseMsg.SUCCESS.buildMsg(realWarehouses);
        }catch (RomeException e){
            log.error(e.getMessage(), e);
            return ResponseMsg.FAIL.buildMsg(e.getCode(), e.getMessage());
        }catch (Exception e){
            log.error(e.getMessage(), e);
            return ResponseMsg.EXCEPTION.buildMsg();
        }
    }

    @ApiOperation(value = "根据条件查询实仓信息", nickname = "queryRWByCondition", produces = MediaType.APPLICATION_JSON_VALUE)
    @ApiResponse(code = 200, message = "success", response = RealWarehouse.class)
    @PostMapping("/list/realWarehouse")
    public Response<List<RealWarehouse>> queryRWByCondition(@RequestBody QueryAreaRWarehouse queryRealWarehouse){
        try{
            List<RealWarehouse> realWarehouses = realWarehouseService.queryRWByCondition(queryRealWarehouse);
            return ResponseMsg.SUCCESS.buildMsg(realWarehouses);
        }catch (RomeException e){
            log.error(e.getMessage(), e);
            return ResponseMsg.FAIL.buildMsg(e.getCode(), e.getMessage());
        }catch (Exception e){
            log.error(e.getMessage(), e);
            return ResponseMsg.EXCEPTION.buildMsg();
        }
    }

    @ApiOperation(value = "根据渠道查询实仓信息", nickname = "queryRealWarehouseByChannelCode", produces = MediaType.APPLICATION_JSON_VALUE)
    @ApiResponse(code = 200, message = "success", response = RealWarehouse.class)
    @GetMapping("/realWarehouse/list/{channelCode}")
    public Response<List<RealWarehouse>> queryRealWarehouseByChannelCode(@PathVariable("channelCode") String channelCode){
        try{
            List<RealWarehouse> realWarehouses = realWarehouseService.queryRealWarehouseByChannelCode(channelCode);
           return ResponseMsg.SUCCESS.buildMsg(realWarehouses);
        }catch (RomeException e){
            log.error(e.getMessage(), e);
            return ResponseMsg.FAIL.buildMsg(e.getCode(), e.getMessage());
        }catch (Exception e){
            log.error(e.getMessage(), e);
            return ResponseMsg.EXCEPTION.buildMsg();
        }
    }

    @ApiOperation(value = "根据仓库类型查询分组工厂", nickname = "queryRWByCondition", produces = MediaType.APPLICATION_JSON_VALUE)
    @ApiResponse(code = 200, message = "success", response = RealWarehouse.class)
    @GetMapping("/queryFactoryByRwType")
    public Response<List<RealWarehouse>> queryFactoryByRwType(@RequestParam("realWarehouseType") Integer realWarehouseType){
        try{
            List<StoreDTO> storeDTOList = realWarehouseService.queryFactoryByRwType(realWarehouseType);
            return ResponseMsg.SUCCESS.buildMsg(storeDTOList);
        }catch (RomeException e){
            log.error(e.getMessage(), e);
            return ResponseMsg.FAIL.buildMsg(e.getCode(), e.getMessage());
        }catch (Exception e){
            log.error(e.getMessage(), e);
            return ResponseMsg.EXCEPTION.buildMsg();
        }
    }

    @ApiOperation(value = "查询sap过账推送日志最新一条", produces = MediaType.APPLICATION_JSON_VALUE)
    @ApiResponse(code = 200, message = "success")
    @RequestMapping(value = "/queryTransferStatusList", method = RequestMethod.POST)
    public Response<List<SapInterfaceLogDTO>> listHistory(@RequestBody List<String> recordCodes, @ApiParam(name = "requestService") @RequestParam("requestService") String requestService) {
        try {
            List<SapInterfaceLogDTO> res=sapInterfaceLogRepository.queryByCodeAndRequestService(recordCodes,requestService);
            return Response.builderSuccess(res);
        } catch (RomeException e) {
            log.error(e.getMessage(), e);
            return Response.builderFail(e.getCode(),e.getMessage());
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return Response.builderFail(ResCode.STOCK_ERROR_1003_DESC,ResCode.STOCK_ERROR_1003_DESC);
        }
    }


    @ApiOperation(value = "新增、修改实仓附加信息", nickname = "addOrUpdateInformation", produces = MediaType.APPLICATION_JSON_VALUE)
    @ApiResponse(code = 200, message = "success", response = RealWarehouseAddition.class)
    @PostMapping("/addOrUpdateInformation")
    public Response addOrUpdateInformation(@RequestBody RealWarehouseAddition realWarehouseAddition) {
        try {
            realWarehouseService.addOrUpdateInformation(realWarehouseAddition);
            return ResponseMsg.SUCCESS.buildMsg();
        } catch (RomeException e) {
            log.error(e.getMessage(), e);
            return ResponseMsg.FAIL.buildMsg(e.getCode(), e.getMessage());
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return ResponseMsg.FAIL.buildMsg(ResCode.STOCK_ERROR_1017, e.getMessage());
        }
    }

    @ApiOperation(value = "获取实仓附加信息", nickname = "getByRealWarehouseId", produces = MediaType.APPLICATION_JSON_VALUE)
    @ApiResponse(code = 200, message = "success", response = RealWarehouseAddition.class)
    @GetMapping("/getByRealWarehouseId")
    public Response<RealWarehouseAddition> getByRealWarehouseId(@RequestParam("realWarehouseId") Long realWarehouseId) {
        try {
            RealWarehouseAddition realWarehouseAddition=realWarehouseService.getByRealWarehouseId(realWarehouseId);
            return ResponseMsg.SUCCESS.buildMsg(realWarehouseAddition);
        } catch (RomeException e) {
            log.error(e.getMessage(), e);
            return ResponseMsg.FAIL.buildMsg(e.getCode(), e.getMessage());
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return ResponseMsg.FAIL.buildMsg(ResCode.STOCK_ERROR_1017, e.getMessage());
        }
    }

    @ApiOperation(value = "创建鲲鹏关联关系数据", nickname = "createKpRwRelation", produces = MediaType.APPLICATION_JSON_VALUE)
    @ApiResponse(code = 200, message = "success", response = RealWarehouseAddition.class)
    @GetMapping("/createKpRwRelation")
    public Response<String> createKpRwRelation(@ApiParam(name = "realWarehouseIds", value = "仓库id集合,','分割") @RequestParam("realWarehouseIds") String realWarehouseIds) {
        try {
            String result = realWarehouseService.createKpRwRelation(realWarehouseIds);
            return ResponseMsg.SUCCESS.buildMsg(result);
        } catch (RomeException e) {
            log.error(e.getMessage(), e);
            return ResponseMsg.FAIL.buildMsg(e.getCode(), e.getMessage());
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return ResponseMsg.FAIL.buildMsg(ResCode.STOCK_ERROR_1017, e.getMessage());
        }
    }

    @ApiOperation(value = "删除鲲鹏关联关系数据", nickname = "deleteKpRwRelation", produces = MediaType.APPLICATION_JSON_VALUE)
    @ApiResponse(code = 200, message = "success", response = RealWarehouseAddition.class)
    @GetMapping("/deleteKpRwRelation")
    public Response<String> deleteKpRwRelation(@ApiParam(name = "realWarehouseIds", value = "仓库id集合,','分割") @RequestParam("realWarehouseIds") String realWarehouseIds) {
        try {
            realWarehouseService.deleteKpRwRelation(realWarehouseIds);
            return ResponseMsg.SUCCESS.buildMsg();
        } catch (RomeException e) {
            log.error(e.getMessage(), e);
            return ResponseMsg.FAIL.buildMsg(e.getCode(), e.getMessage());
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return ResponseMsg.FAIL.buildMsg(ResCode.STOCK_ERROR_1017, e.getMessage());
        }
    }

    @ApiOperation(value = "查询实仓表所有退货仓", nickname = "queryRealReturnWarehouses", produces = MediaType.APPLICATION_JSON_VALUE)
    @ApiResponse(code = 200, message = "success")
    @GetMapping("/queryRealReturnWarehouses")
    public Response<List<RealWarehouse>> queryRealReturnWarehouses() {
        try {
            List<RealWarehouse> RealWarehouses = realWarehouseService.queryRealReturnWarehouses();
            return ResponseMsg.SUCCESS.buildMsg(RealWarehouses);
        } catch (RomeException e) {
            log.error(e.getMessage(), e);
            return ResponseMsg.FAIL.buildMsg(e.getCode(), e.getMessage());
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return ResponseMsg.EXCEPTION.buildMsg(e.getMessage());
        }
    }


    @ApiOperation(value = "绑定退货仓", nickname = "addOrUpdateReturnConfig", produces = MediaType.APPLICATION_JSON_VALUE)
    @ApiResponse(code = 200, message = "success")
    @PostMapping("/addOrUpdateReturnConfig")
    public Response addOrUpdateReturnConfig(@RequestBody RealReturnWarehouseDTO realReturnWarehouseDTO) {
        try {
            realWarehouseService.addOrUpdateReturnConfig(realReturnWarehouseDTO);
            return ResponseMsg.SUCCESS.buildMsg();
        } catch (RomeException e) {
            log.error(e.getMessage(), e);
            return ResponseMsg.FAIL.buildMsg(e.getCode(), e.getMessage());
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return ResponseMsg.EXCEPTION.buildMsg(e.getMessage());
        }
    }
    
    @ApiOperation(value = "查询实仓表，根据工厂列表和实仓内部编码列表，有最大条数", nickname = "queryByCodeListAndFactoryCodeList", produces = MediaType.APPLICATION_JSON_VALUE)
    @ApiResponse(code = 200, message = "success")
    @PostMapping("/queryByCodeListAndFactoryCodeList")
    public Response<List<RealWarehouse>> queryByCodeListAndFactoryCodeList(@RequestBody RealWarehouseAdminParamDTO paramDTO) {
        try {
            List<RealWarehouse> RealWarehouses = realWarehouseService.queryByCodeListAndFactoryCodeList(paramDTO);
            return ResponseMsg.SUCCESS.buildMsg(RealWarehouses);
        } catch (RomeException e) {
            log.error(e.getMessage(), e);
            return ResponseMsg.FAIL.buildMsg(e.getCode(), e.getMessage());
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return ResponseMsg.EXCEPTION.buildMsg(e.getMessage());
        }
    }

    @ApiOperation(value = "根据skuCode、物资存储类型查询实体仓库存信息", nickname = "queryRWStockBySkuCodeAndMaterialStorageType", produces = MediaType.APPLICATION_JSON_VALUE)
    @ApiResponse(code = 200, message = "success", response = List.class)
    @PostMapping("/queryRWStockBySkuCodeAndMaterialStorageType")
    @BathParamsValidate
    public Response<List<RealWarehouseStockDTO>> queryRWStockBySkuCodeAndMaterialStorageType(@Validated @RequestBody ValidList<QueryRealWarehouseStock> list) {
        try {
            List<RealWarehouseStockDTO> warehouseStockDTOList = realWarehouseService.queryRWStockBySkuCodeAndMaterialStorageType(list);
            return ResponseMsg.SUCCESS.buildMsg(warehouseStockDTOList);
        } catch (RomeException e) {
            log.error(e.getMessage(), e);
            return ResponseMsg.FAIL.buildMsg(e.getCode(), e.getMessage());
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return ResponseMsg.EXCEPTION.buildMsg();
        }
    }

    @ApiOperation(value = "公司+仓类型+是否虚仓查询", nickname = "queryWarehousrForInner", produces = MediaType.APPLICATION_JSON_VALUE)
    @ApiResponse(code = 200, message = "success", response = RealWarehouse.class)
    @PostMapping("/queryWarehousrForInner")
    public Response<List<RealWarehouse>> queryWarehousrForInner(@RequestBody QueryForInnerProDto query){
        try{
            List<RealWarehouse> realWarehouses = realWarehouseService.queryWarehousrForInner(query);
            return ResponseMsg.SUCCESS.buildMsg(realWarehouses);
        }catch (RomeException e){
            log.error(e.getMessage(), e);
            return ResponseMsg.FAIL.buildMsg(e.getCode(), e.getMessage());
        }catch (Exception e){
            log.error(e.getMessage(), e);
            return ResponseMsg.EXCEPTION.buildMsg();
        }
    }


    @ApiOperation(value = "获取非指定类型下的工厂集合", produces = MediaType.APPLICATION_JSON_VALUE)
    @ApiResponse(code = 200, message = "success", response = RealWarehouse.class)
    @RequestMapping(value = "/queryNotInTypesFactory", method = RequestMethod.POST)
    public Response<PageInfo<RealWarehouse>> queryNotShopFactory(@RequestBody QueryNotInTypesFactoryDto dto) {
        try {
            PageInfo<RealWarehouse> list = realWarehouseService.queryNotInTypesFactory(dto);
            return ResponseMsg.SUCCESS.buildMsg(list);
        } catch (RomeException e) {
            log.error(e.getMessage(), e);
            return ResponseMsg.FAIL.buildMsg(e.getCode(), e.getMessage());
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return ResponseMsg.EXCEPTION.buildMsg();
        }
    }

    @ApiOperation(value = "根据wmsCode查询对应仓库信息", nickname = "selectKpChannelByRealWarehouses", produces = MediaType.APPLICATION_JSON_VALUE)
    @ApiResponse(code = 200, message = "success", response = List.class)
    @RequestMapping(value = "/queryRealWarehouseByWmsCode", method = RequestMethod.GET)
    public Response<List<RealWarehouse>> queryRealWarehouseByWmsCode(@ApiParam(name = "wmsCode", value = "wms 编码 1:大福  2:旺店通 3:SAP-WM 4:欧电云")  @RequestParam("wmsCode") Integer wmsCode) {
        try{
            return Response.builderSuccess(realWarehouseService.queryRealWarehouseByWmsCode(wmsCode));
        }catch (RomeException ex){
            log.error(ex.getMessage(),ex);
            return Response.builderFail(ex.getCode(), ex.getMessage());
        }catch (Exception ex){
            log.error(ex.getMessage(),ex);
            return ResponseMsg.EXCEPTION.buildMsg();
        }
    }



    @ApiOperation(value = "根据公司编码查询对应仓库信息", nickname = "queryRealWarehouseListByCompanyCode", produces = MediaType.APPLICATION_JSON_VALUE)
    @ApiResponse(code = 200, message = "success", response = List.class)
    @RequestMapping(value = "/queryRealWarehouseListByCompanyCode", method = RequestMethod.POST)
    public Response<List<RealWarehouse>> queryRealWarehouseListByCompanyCode(@RequestParam("companyCode")String companyCode) {
        try{
            List<RealWarehouse> list=realWarehouseService.queryRealWarehouseListByCompanyCode(companyCode);
            return Response.builderSuccess(list);
        }catch (RomeException ex){
            log.error(ex.getMessage(),ex);
            return Response.builderFail(ex.getCode(), ex.getMessage());
        }catch (Exception ex){
            log.error(ex.getMessage(),ex);
            return ResponseMsg.EXCEPTION.buildMsg(ex.getMessage());
        }
    }

    @ApiOperation(value = "根据实仓ID集合查询实仓信息", nickname = "queryRealWarehouseByIdList", produces = MediaType.APPLICATION_JSON_VALUE)
    @ApiResponse(code = 200, message = "success", response = List.class)
    @RequestMapping(value = "/queryRealWarehouseByIdList", method = RequestMethod.POST)
    public Response<List<RealWarehouse>> queryRealWarehouseByIdList(@RequestBody List<Long> warehouseIdList) {
        try{
            List<RealWarehouse> list=realWarehouseService.queryRealWarehouseByIdList(warehouseIdList);
            return Response.builderSuccess(list);
        }catch (RomeException ex){
            log.error(ex.getMessage(),ex);
            return Response.builderFail(ex.getCode(), ex.getMessage());
        }catch (Exception ex){
            log.error(ex.getMessage(),ex);
            return ResponseMsg.EXCEPTION.buildMsg(ex.getMessage());
        }
    }

    @ApiOperation(value = "查询仓店一体的仓库ID集合", nickname = "querySpecialWarehouseIdList", produces = MediaType.APPLICATION_JSON_VALUE)
    @ApiResponse(code = 200, message = "success", response = List.class)
    @RequestMapping(value = "/querySpecialWarehouseIdList", method = RequestMethod.POST)
    public Response<List<Long>> querySpecialWarehouseIdList() {
        try{
            List<Long> list=realWarehouseService.querySpecialWarehouseIdList();
            return Response.builderSuccess(list);
        }catch (RomeException ex){
            log.error(ex.getMessage(),ex);
            return Response.builderFail(ex.getCode(), ex.getMessage());
        }catch (Exception ex){
            log.error(ex.getMessage(),ex);
            return ResponseMsg.EXCEPTION.buildMsg(ex.getMessage());
        }
    }


    @ApiOperation(value = "查询所有的仓店一体的仓库列表", nickname = "querySpecialRealWarehouseList", produces = MediaType.APPLICATION_JSON_VALUE)
    @ApiResponse(code = 200, message = "success")
    @PostMapping("/querySpecialRealWarehouseList")
    public Response<List<RealWarehouse>> querySpecialRealWarehouseList() {
        try {
            List<RealWarehouse> RealWarehouses = realWarehouseService.querySpecialRealWarehouseList();
            return ResponseMsg.SUCCESS.buildMsg(RealWarehouses);
        } catch (RomeException e) {
            log.error(e.getMessage(), e);
            return ResponseMsg.FAIL.buildMsg(e.getCode(), e.getMessage());
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return ResponseMsg.EXCEPTION.buildMsg(e.getMessage());
        }
    }



    @ApiOperation(value = "查询所有的仓店一体的仓库列表", nickname = "queryRealWarehouseListLikeName", produces = MediaType.APPLICATION_JSON_VALUE)
    @ApiResponse(code = 200, message = "success")
    @PostMapping("/queryRealWarehouseListLikeName")
    public Response<List<RealWarehouse>> queryRealWarehouseListLikeName(@RequestParam("realWarehouseName") String realWarehouseName) {
        try {
            List<RealWarehouse> res = realWarehouseService.queryRealWarehouseListLikeName(realWarehouseName);
            return ResponseMsg.SUCCESS.buildMsg(res);
        } catch (RomeException e) {
            log.error(e.getMessage(), e);
            return ResponseMsg.FAIL.buildMsg(e.getCode(), e.getMessage());
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return ResponseMsg.EXCEPTION.buildMsg(e.getMessage());
        }
    }

}
