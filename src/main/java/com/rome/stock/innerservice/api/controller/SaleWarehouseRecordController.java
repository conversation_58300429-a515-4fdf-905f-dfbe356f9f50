package com.rome.stock.innerservice.api.controller;

import com.alibaba.fastjson.JSON;
import com.github.pagehelper.PageInfo;
import com.rome.arch.core.clientobject.Response;
import com.rome.arch.core.exception.RomeException;
import com.rome.arch.util.controller.RomeController;
import com.rome.stock.innerservice.api.dto.*;
import com.rome.stock.innerservice.api.dto.qry.SaleWarehouseRecordCondition;
import com.rome.stock.innerservice.api.dto.replenish.BatchCancleDTO;
import com.rome.stock.innerservice.api.dto.replenish.CancelReasonDTO;
import com.rome.stock.innerservice.api.dto.replenish.ReCalculateDTO;
import com.rome.stock.innerservice.api.dto.warehouserecord.SaleWarehouseRecordDTO;
import com.rome.stock.innerservice.api.dto.wine.WineInfoUpdateBySerialNoParamDTO;
import com.rome.stock.innerservice.api.dto.wine.WineInfoUpdateParamDTO;
import com.rome.stock.innerservice.api.dto.wine.WinePageInfoResultDTO;
import com.rome.stock.innerservice.api.dto.wine.WinePageParamDTO;
import com.rome.stock.innerservice.common.CoreKibanaLog;
import com.rome.stock.innerservice.common.ResCode;
import com.rome.stock.innerservice.constant.ResponseMsg;
import com.rome.stock.innerservice.constant.WarehouseRecordStatusVO;
import com.rome.stock.innerservice.domain.repository.SapInterfaceLogRepository;
import com.rome.stock.innerservice.domain.service.SaleWineService;
import com.rome.stock.innerservice.domain.service.ShopRetailService;
import com.rome.stock.innerservice.domain.service.WarehouseRecordService;
import com.rome.stock.wms.config.BaseinfoConfiguration;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import io.swagger.annotations.ApiResponse;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.*;

@Slf4j
@RomeController
@RequestMapping("/stock/v1/warehouse_sale")
@Api(tags={"运营平台零售出库toc查询"})
public class SaleWarehouseRecordController {
	//给admin pool用的接口：零售出库toc 查询页面


	@Resource
	private ShopRetailService shopRetailService;
	@Resource
	private SaleWineService saleWineService;
	@Resource
	private SapInterfaceLogRepository sapInterfaceLogRepository;
	@Resource
	private WarehouseRecordService warehouseRecordService;


	private final static String LOGTYPE = "adminCall";


	@ApiOperation(value = "查询电商出库单（新）", nickname = "query_sale_warehouse_record_new", produces = MediaType.APPLICATION_JSON_VALUE)
	@ApiResponse(code = 200, message = "success")
	@RequestMapping(value = "/listNew", method = RequestMethod.POST)
	public Response<PageInfo<SaleWarehouseRecordDTO>> queryNewByCondition(@ApiParam(name = "condition", value = "查询条件") @RequestBody SaleWarehouseRecordCondition condition) {
		try {
			return Response.builderSuccess(shopRetailService.queryNewWarehouseRecordList(condition));
		} catch (RomeException e) {
			log.error(e.getMessage(), e);
			return Response.builderFail(e.getCode(), e.getMessage());
		} catch (Exception e) {
			log.error(e.getMessage(), e);
			return Response.builderFail(ResCode.STOCK_ERROR_1003_DESC, ResCode.STOCK_ERROR_1003_DESC);
		}
	}

	@ApiOperation(value = "查询电商出库单（协同）", nickname = "query_sale_warehouse_record_new", produces = MediaType.APPLICATION_JSON_VALUE)
	@ApiResponse(code = 200, message = "success")
	@RequestMapping(value = "/queryNewByConditionToSynergy", method = RequestMethod.POST)
	public Response<PageInfo<SaleWarehouseRecordDTO>> queryNewByConditionToSynergy(@ApiParam(name = "condition", value = "查询条件") @RequestBody SaleWarehouseRecordCondition condition) {
		try {
			return Response.builderSuccess(shopRetailService.queryNewByConditionToSynergy(condition));
		} catch (RomeException e) {
			log.error(e.getMessage(), e);
			return Response.builderFail(e.getCode(), e.getMessage());
		} catch (Exception e) {
			log.error(e.getMessage(), e);
			return Response.builderFail(ResCode.STOCK_ERROR_1003_DESC, ResCode.STOCK_ERROR_1003_DESC);
		}
	}

	@ApiOperation(value = "查询电商出库单明细", nickname = "query_Warehouse_record_detail", produces = MediaType.APPLICATION_JSON_VALUE)
	@ApiResponse(code = 200, message = "success")
	@RequestMapping(value = "/queryDetailByWarehouseRecordCode", method = RequestMethod.POST)
	public Response<List<WarehouseRecordPostDTO>> queryDetailByWarehouseRecordCode(@ApiParam(name = "warehouseRecordCodeList", value = "查询条件") @RequestBody List<String> warehouseRecordCodeList) {
		try {
			return Response.builderSuccess(shopRetailService.queryWarehouseRecordDetailByCode(warehouseRecordCodeList));
		} catch (RomeException e) {
			log.error(e.getMessage(), e);
			return Response.builderFail(e.getCode(), e.getMessage());
		} catch (Exception e) {
			log.error(e.getMessage(), e);
			return Response.builderFail(ResCode.STOCK_ERROR_1003_DESC, ResCode.STOCK_ERROR_1003_DESC);
		}
	}

	@ApiOperation(value = "获取出库单所有的枚举状态", nickname = "get_warehouse_record_status", produces = MediaType.APPLICATION_JSON_VALUE)
	@ApiResponse(code = 200, message = "success")
	@RequestMapping(value = "/recordStatus", method = RequestMethod.GET)
	public Response<List> getRecordStatus() {
		List<Map> result = new ArrayList<>();
		for(WarehouseRecordStatusVO vo:WarehouseRecordStatusVO.values()){
			Map<String , Object> map = new HashMap<>();
			map.put("status",vo.getStatus());
			map.put("desc",vo.getDesc());
			result.add(map);
		}
		return Response.builderSuccess(result);
	}

	@ApiOperation(value = "跟据门店销售出库单id查询详情", nickname = "query_sale_warehouse_record_detail", produces = MediaType.APPLICATION_JSON_VALUE)
	@ApiResponse(code = 200, message = "success")
	@RequestMapping(value = "/querySaleWarehouseRecordInfo/{recordId}", method = RequestMethod.GET)
	public Response<SaleWarehouseRecordDTO> queryByCondition(@ApiParam(name = "recordId", value = "recordId") @PathVariable Long recordId) {
		try {
			return Response.builderSuccess(shopRetailService.querySaleWarehouseRecordInfoById(recordId));
		} catch (RomeException e) {
			log.error(e.getMessage(), e);
			return Response.builderFail(e.getCode(), e.getMessage());
		} catch (Exception e) {
			log.error(e.getMessage(), e);
			return Response.builderFail(ResCode.STOCK_ERROR_1003_DESC, ResCode.STOCK_ERROR_1003_DESC);
		}
	}
	
	@ApiOperation(value = "查询出库单历史数据", nickname = "query_sale_warehouse_record_history", produces = MediaType.APPLICATION_JSON_VALUE)
	@ApiResponse(code = 200, message = "success")
	@RequestMapping(value = "/listHistory", method = RequestMethod.POST)
	public Response<PageInfo<SaleWarehouseRecordDTO>> queryByConditionHistory(@ApiParam(name = "condition", value = "查询条件") @RequestBody SaleWarehouseRecordCondition condition) {
		try {
			return Response.builderSuccess(shopRetailService.queryWarehouseRecordListHistory(condition));
		} catch (RomeException e) {
			log.error(e.getMessage(), e);
			return Response.builderFail(e.getCode(), e.getMessage());
		} catch (Exception e) {
			log.error(e.getMessage(), e);
			return Response.builderFail(ResCode.STOCK_ERROR_1003_DESC, ResCode.STOCK_ERROR_1003_DESC);
		}
	}
	
	@ApiOperation(value = "跟据销售出库单code查询详情历史数据", nickname = "query_sale_warehouse_record_detail_history", produces = MediaType.APPLICATION_JSON_VALUE)
	@ApiResponse(code = 200, message = "success")
	@RequestMapping(value = "/detailInfoHistory/{recordCode}/{date}", method = RequestMethod.GET)
	public Response<SaleWarehouseRecordDTO> queryByDetailHistory(@ApiParam(name = "recordCode", value = "recordCode") @PathVariable String recordCode,
			@ApiParam(name = "date", value = "date")@DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss") @PathVariable Date date) {
		try {
			return Response.builderSuccess(shopRetailService.querySaleWarehouseRecordInfoByRecordCodeHistory(recordCode, date));
		} catch (RomeException e) {
			log.error(e.getMessage(), e);
			return Response.builderFail(e.getCode(), e.getMessage());
		} catch (Exception e) {
			log.error(e.getMessage(), e);
			return Response.builderFail(ResCode.STOCK_ERROR_1003_DESC, ResCode.STOCK_ERROR_1003_DESC);
		}
	}


	@ApiOperation(value = "批量撤回订单", nickname = "cancleOrderBatch", produces = MediaType.APPLICATION_JSON_VALUE)
	@ApiResponse(code = 200, message = "success")
	@RequestMapping(value = "/cancleBatch", method = RequestMethod.POST)
	public Response cancleOrderBatch(@RequestBody CancelReasonDTO dto){
		this.tocSwitch(dto.getIsForce());
		log.warn(CoreKibanaLog.getJobLog(LOGTYPE, "cancleBatch", "批量撤回电商发货单: " + JSON.toJSONString(dto), JSON.toJSONString(dto)));
		List<String> errorList =  new ArrayList<>();
		try {
			try {
				shopRetailService.cancleOrderBatch(dto);
			}catch (RomeException e){
				errorList.add("[" + dto.getRecordCode() + "] :" + e.getMessage());
				log.error(dto.getRecordCode() + e.getMessage(), e);
			}catch (Exception e){
				errorList.add("[" + dto.getRecordCode() + "] :" + "系统异常:"+ e.getMessage());
				log.error(dto.getRecordCode() + e.getMessage(), e);
			}
			if(CollectionUtils.isNotEmpty(errorList)){
				return Response.builderFail(ResCode.STOCK_ERROR_1001,errorList.toArray());
			}else{
				return ResponseMsg.SUCCESS.buildMsg("全部撤回成功");
			}
		} catch (RomeException e) {
			log.error(e.getMessage(), e);
			return Response.builderFail(e.getCode(),e.getMessage());
		} catch (Exception e) {
			log.error(e.getMessage(), e);
			return ResponseMsg.EXCEPTION.buildMsg(e.getMessage());
		}
	}

	@ApiOperation(value = "批量再次推送订单", nickname = "cancleWhAllocation", produces = MediaType.APPLICATION_JSON_VALUE)
	@ApiResponse(code = 200, message = "success")
	@RequestMapping(value = "/pushAgain", method = RequestMethod.POST)
	public Response pushAgain(@RequestBody BatchCancleDTO dto){
		this.tocSwitch(dto.getIsForce());
		String list = dto.getList();
		log.warn(CoreKibanaLog.getJobLog(LOGTYPE, "cancleBatch", "批量重推电商发货单: " + list, JSON.toJSONString(dto)));
		List<String> errorList =  new ArrayList<>();
		try {
			if(StringUtils.isNotBlank(list)){
				String [] array = list.split(",");
				if(array.length > 0){
					for (String orderCode : array) {
						try {
							shopRetailService.pushAgain(orderCode, dto.getUserId());
						}catch (RomeException e){
							errorList.add("[" + orderCode + "] :" + e.getMessage());
							log.error(orderCode + e.getMessage(), e);
						}catch (Exception e){
							errorList.add("[" + orderCode + "] :" + "系统异常:"+ e.getMessage());
							log.error(orderCode + e.getMessage(), e);
						}

					}
				}
			}
			if(CollectionUtils.isNotEmpty(errorList)){
				return Response.builderFail(ResCode.STOCK_ERROR_1001, errorList.toArray());
			}else{
				return ResponseMsg.SUCCESS.buildMsg("全部成功");
			}
		} catch (RomeException e) {
			log.error(e.getMessage(), e);
			return Response.builderFail(e.getCode(),e.getMessage());
		} catch (Exception e) {
			log.error(e.getMessage(), e);
			return ResponseMsg.EXCEPTION.buildMsg(e.getMessage());
		}
	}


	@ApiOperation(value = "修改发货仓", nickname = "cancleWhAllocation", produces = MediaType.APPLICATION_JSON_VALUE)
	@ApiResponse(code = 200, message = "success")
	@RequestMapping(value = "/changeWarehouse", method = RequestMethod.POST)
	public Response changeWarehouse(@RequestBody BatchCancleDTO dto){
		this.tocSwitch(dto.getIsForce());
		log.warn(CoreKibanaLog.getJobLog(LOGTYPE, "changeWarehouse", "修改发货仓为"+dto.getRealWarehouseId() , JSON.toJSONString(dto)));
		try {
			shopRetailService.changeWarehouse(dto.getRecordCode(), dto);
		return ResponseMsg.SUCCESS.buildMsg("");
		} catch (RomeException e) {
			log.error(e.getMessage(), e);
			return Response.builderFail(e.getCode(),e.getMessage());
		} catch (Exception e) {
			log.error(e.getMessage(), e);
			return ResponseMsg.EXCEPTION.buildMsg(e.getMessage());
		}
	}




	@ApiOperation(value = "重新计算仓库（非当前仓）", nickname = "reCalculateWarehouse", produces = MediaType.APPLICATION_JSON_VALUE)
	@ApiResponse(code = 200, message = "success")
	@RequestMapping(value = "/reCalculateWarehouse", method = RequestMethod.POST)
	public Response reCalculateWarehouse(@RequestBody ReCalculateDTO dto){
		this.tocSwitch(dto.getIsForce());
		String message ="";
		boolean isSucc = false;
		String json = JSON.toJSONString(dto);
		try {
			shopRetailService.reCalculateWarehouse(dto);
			isSucc = true;
			message="200";
			return ResponseMsg.SUCCESS.buildMsg("成功");
		} catch (RomeException e) {
			log.error(e.getMessage(), e);
			message = e.getMessage();
			return Response.builderFail(e.getCode(),e.getMessage());
		} catch (Exception e) {
			log.error(e.getMessage(), e);
			message = e.getMessage();
			return ResponseMsg.EXCEPTION.buildMsg(e.getMessage());
		}finally {
			sapInterfaceLogRepository.saveCallBackInterFaceLog(2, dto.getRecordCode(), "reCalculateWarehouse",
					json, message, isSucc);
		}
	}
	
	@ApiOperation(value = "计算电商停发,根据仓库Id,只计算创建时间，近6个月的单子", nickname = "calculateStopDeliveryByRwId", produces = MediaType.APPLICATION_JSON_VALUE)
    @ApiResponse(code = 200, message = "success", response = Integer.class)
    @RequestMapping(value = "/calculateStopDeliveryByRwId", method = RequestMethod.POST)
    public Response<String> calculateStopDeliveryByRwId(@RequestParam(value = "realWarehouseId") Long realWarehouseId,
    		@ApiParam(name = "userId", value = "用户Id") @RequestParam("userId") Long userId){
        try{
        	if(realWarehouseId == null) {
        		return Response.builderFail(ResCode.STOCK_ERROR_1002, ResCode.STOCK_ERROR_1002_DESC + "仓库ID不能为空");
        	}
        	shopRetailService.calculateStopDeliveryByRwId(realWarehouseId, userId);
            return Response.builderSuccess("成功");
        }catch (RomeException e){
            log.error(e.getMessage(),e);
            return Response.builderFail(e.getCode(),e.getMessage());
        }catch (Exception e){
            log.error(e.getMessage(), e);
            return Response.builderFail(ResCode.STOCK_ERROR_1003,ResCode.STOCK_ERROR_1003_DESC + e.getMessage());
        }
    }
	
	@ApiOperation(value = "计算电商停发,根据单据列表", nickname = "calculateStopDeliveryByRecordCodeList", produces = MediaType.APPLICATION_JSON_VALUE)
    @ApiResponse(code = 200, message = "success", response = Integer.class)
    @RequestMapping(value = "/calculateStopDeliveryByRecordCodeList", method = RequestMethod.POST)
    public Response<String> calculateStopDeliveryByRecordCodeList(
    		@ApiParam(name = "userId", value = "用户Id") @RequestParam("userId") Long userId, @RequestBody List<String> recordCodeList){
        try{
        	if(recordCodeList == null || recordCodeList.size() == 0) {
        		return Response.builderFail(ResCode.STOCK_ERROR_1002, ResCode.STOCK_ERROR_1002_DESC + "单据号个数不能为空");
        	}
        	String result = shopRetailService.calculateStopDeliveryByRecordCodeList(recordCodeList, userId);
            return Response.builderSuccess(result);
        }catch (RomeException e){
            log.error(e.getMessage(),e);
            return Response.builderFail(e.getCode(),e.getMessage());
        }catch (Exception e){
            log.error(e.getMessage(), e);
            return Response.builderFail(ResCode.STOCK_ERROR_1003,ResCode.STOCK_ERROR_1003_DESC + e.getMessage());
        }
    }
	
	@ApiOperation(value = "获取，取消原因为停发的key", produces = MediaType.APPLICATION_JSON_VALUE)
    @ApiResponse(code = 200, message = "success", response = String.class)
    @RequestMapping(value = "/getCancleReasonsByStopKey", method = RequestMethod.GET)
    public Response<String> getCancleReasonsByStopKey() {
        try {
            String data = shopRetailService.getCancleReasonsByStopKey();
            return ResponseMsg.SUCCESS.buildMsg(data);
        } catch (RomeException e) {
            log.error(e.getMessage(), e);
            return ResponseMsg.FAIL.buildMsg(e.getCode(), e.getMessage());
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return ResponseMsg.EXCEPTION.buildMsg();
        }
    }
	
	@ApiOperation(value = "查询需要重新计算撤回或停发后置单数据的列表数据", produces = MediaType.APPLICATION_JSON_VALUE)
	@ApiResponse(code = 200, message = "success")
	@RequestMapping(value = "/queryNeedReCalWrCancelList", method = RequestMethod.POST)
	public Response<List<WarehouseRecordPageDTO>> queryNeedReCalWrCancelList(@ApiParam(name = "condition", value = "查询条件") @RequestBody WarehouseRecordPageDTO condition) {
		try {
			return Response.builderSuccess(shopRetailService.queryNeedReCalWrCancelList(condition));
		} catch (RomeException e) {
			log.error(e.getMessage(), e);
			return Response.builderFail(e.getCode(), e.getMessage());
		} catch (Exception e) {
			log.error(e.getMessage(), e);
			return Response.builderFail(ResCode.STOCK_ERROR_1003_DESC, ResCode.STOCK_ERROR_1003_DESC);
		}
	}
	@ApiOperation(value = "附加信息表分页查询", nickname = "querySaleWineByFrontRecordCode", produces = MediaType.APPLICATION_JSON_VALUE)
	@ApiResponse(code = 200, message = "success")
	@RequestMapping(value = "/querySaleWineByFrontRecordCode", method = RequestMethod.POST)
	public Response<PageInfo<WinePageInfoResultDTO>> querySaleWineByFrontRecordCode(@ApiParam(name = "condition", value = "查询条件") @RequestBody WinePageParamDTO paramDTO) {
		try {
			PageInfo<WinePageInfoResultDTO> list = saleWineService.querySaleWineByFrontRecordCode(paramDTO);
			return Response.builderSuccess(list);
		} catch (RomeException e) {
			log.error(e.getMessage(), e);
			return Response.builderFail(e.getCode(), e.getMessage());
		} catch (Exception e) {
			log.error(e.getMessage(), e);
			return Response.builderFail("500000", "系统异常");
		}
	}

	@ApiOperation(value = "更新附加信息", nickname = "updateSaleWineById", produces = MediaType.APPLICATION_JSON_VALUE)
	@ApiResponse(code = 200, message = "success")
	@RequestMapping(value = "/updateSaleWineById", method = RequestMethod.POST)
	public Response updateSaleWineById(@RequestBody List<WineInfoUpdateParamDTO> paramDTO) {
		try {
			this.tocSwitch(paramDTO.get(0).getIsForce());
			saleWineService.updateSaleWineById(paramDTO);
			return ResponseMsg.SUCCESS.buildMsg();
		} catch (RomeException e) {
			log.error(e.getMessage(), e);
			return Response.builderFail(e.getCode(), e.getMessage());
		} catch (Exception e) {
			log.error(e.getMessage(), e);
			return Response.builderFail("500000", "系统异常");
		}
	}

	@ApiOperation(value = "换桶", nickname = "updateSaleWineBySerialNo", produces = MediaType.APPLICATION_JSON_VALUE)
	@ApiResponse(code = 200, message = "success")
	@RequestMapping(value = "/updateSaleWineBySerialNo", method = RequestMethod.POST)
	public Response updateSaleWineBySerialNo(@RequestBody List<WineInfoUpdateBySerialNoParamDTO> wineInfoUpdateBySerialNoParamDTOList, @RequestParam("userId") Long userId) {
		try {
			this.tocSwitch(wineInfoUpdateBySerialNoParamDTOList.get(0).getIsForce());
			saleWineService.updateSaleWineBySerialNo(wineInfoUpdateBySerialNoParamDTOList,userId);
			return ResponseMsg.SUCCESS.buildMsg();
		} catch (RomeException e) {
			log.error(e.getMessage(), e);
			return Response.builderFail(e.getCode(), e.getMessage());
		} catch (Exception e) {
			log.error(e.getMessage(), e);
			return Response.builderFail("500000", "系统异常");
		}
	}


	private void tocSwitch(Integer isForce){
		if(Objects.equals(isForce,1)){
			//强制使用，不拦截
			return;
		}
		String tocSwitch = BaseinfoConfiguration.getInstance().get("toc_switch", "toc_switch");
		if(Objects.equals(tocSwitch,"1")){
			throw new RomeException(ResCode.STOCK_ERROR_1017,"TOC接口已迁移至订单中心");
		}
	}

	@ApiOperation(value = "根据sap订单号查询指定类型的单据", nickname = "queryBySapOrderCodeAndRecordTypeList", produces = MediaType.APPLICATION_JSON_VALUE)
	@ApiResponse(code = 200, message = "success")
	@RequestMapping(value = "/queryBySapOrderCodeAndRecordTypeList", method = RequestMethod.POST)
	public Response<List<WarehouseRecordDTO>> queryBySapOrderCodeAndRecordTypeList(@RequestBody WarehouseQueryDTO warehouseQueryDTO) {
		try {
			List<WarehouseRecordDTO> warehouseRecordDTOList=warehouseRecordService.queryBySapOrderCodeAndRecordTypeList(warehouseQueryDTO);
			return ResponseMsg.SUCCESS.buildMsg(warehouseRecordDTOList);
		} catch (RomeException e) {
			log.error(e.getMessage(), e);
			return Response.builderFail(e.getCode(), e.getMessage());
		} catch (Exception e) {
			log.error(e.getMessage(), e);
			return Response.builderFail("500000", "系统异常");
		}
	}

}
