package com.rome.stock.innerservice.api.controller;

import java.util.ArrayList;
import java.util.List;

import javax.annotation.Resource;
import javax.validation.Valid;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.MediaType;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.rome.arch.core.clientobject.Response;
import com.rome.arch.core.exception.RomeException;
import com.rome.arch.util.controller.RomeController;
import com.rome.stock.innerservice.api.dto.RealWarehouse;
import com.rome.stock.innerservice.api.dto.VirtualWarehouse;
import com.rome.stock.innerservice.api.dto.groupbuy.GroupPurchaseDTO;
import com.rome.stock.innerservice.api.dto.groupbuy.QueryRealWarehouse;
import com.rome.stock.innerservice.api.dto.groupbuy.ReservationDTO;
import com.rome.stock.innerservice.api.dto.groupbuy.SkuInfoForVw;
import com.rome.stock.innerservice.api.dto.groupbuy.SkuStockForVw;
import com.rome.stock.innerservice.api.dto.groupbuy.VwMoveRecordDTO;
import com.rome.stock.innerservice.api.dto.groupbuy.WarehouseQueryDTO;
import com.rome.stock.innerservice.api.dto.warehouserecord.BatchResultDTO;
import com.rome.stock.innerservice.api.dto.warehouserecord.CancelRecordDTO;
import com.rome.stock.innerservice.api.dto.warehouserecord.InWarehouseRecordDTO;
import com.rome.stock.innerservice.api.dto.warehouserecord.OutWarehouseRecordDTO;
import com.rome.stock.innerservice.common.CoreKibanaLog;
import com.rome.stock.innerservice.common.ResCode;
import com.rome.stock.innerservice.constant.ParamValidator;
import com.rome.stock.innerservice.constant.ResponseMsg;
import com.rome.stock.innerservice.domain.repository.SapInterfaceLogRepository;
import com.rome.stock.innerservice.domain.service.GroupBuyService;
import com.rome.stock.innerservice.domain.service.RealWarehouseService;
import com.rome.stock.innerservice.domain.service.ReservationReturnService;
import com.rome.stock.innerservice.domain.service.ReservationService;
import com.rome.stock.innerservice.domain.service.VirtualWarehouseService;

import cn.hutool.core.collection.CollUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import io.swagger.annotations.ApiResponse;
import lombok.extern.slf4j.Slf4j;

/**
 * 类GroupByController的实现描述：团购
 *
 * <AUTHOR> 2020/4/7 10:35
 */
@Slf4j
@RomeController
@RequestMapping("/stock/v1/groupBy")
@Api(tags={"团购服务接口"})
public class GroupBuyController {

    @Autowired
    private RealWarehouseService realWarehouseService;

    @Autowired
    private VirtualWarehouseService virtualWarehouseService;

    @Autowired
    private GroupBuyService groupBuyService;

    @Autowired
    private ReservationService reservationService;

    @Autowired
    private ReservationReturnService reservationReturnService;


    @Resource
    private SapInterfaceLogRepository sapInterfaceLogRepository;

    private ParamValidator validator = ParamValidator.INSTANCE;

    @Value("${max.stock.queryNum}")
    private Integer MAX_SIZE;

    private final static String LOGTYPE = "groupBy";

    @ApiOperation(value = "根据工厂code和仓库类型查询仓库信息", nickname = "queryRealWarehouseByFactoryCodeAndType", produces = MediaType.APPLICATION_JSON_VALUE)
    @ApiResponse(code = 200, message = "success")
    @PostMapping("/queryRealWarehouseByFactoryCodeAndType")
    public Response<List<RealWarehouse>> queryRealWarehouseByFactoryCodeAndType(@RequestBody WarehouseQueryDTO warehouseQueryDTO) {
        try {
            log.info("根据工厂code和仓库类型查询仓库信息，参数 ==> {}", JSONObject.toJSONString(warehouseQueryDTO));
            List<RealWarehouse> RealWarehouses = realWarehouseService.queryRealWarehouseByFactoryCodeAndType(warehouseQueryDTO.getFactoryCode(), warehouseQueryDTO.getType());
            return ResponseMsg.SUCCESS.buildMsg(RealWarehouses);
        } catch (RomeException e) {
            log.error(e.getMessage(), e);
            return ResponseMsg.FAIL.buildMsg(e.getCode(), e.getMessage());
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return ResponseMsg.EXCEPTION.buildMsg();
        }
    }

    @ApiOperation(value = "根据工厂code和实仓类型查询仓库信息", nickname = "queryRealWarehouseByFactoryCodeAndType", produces = MediaType.APPLICATION_JSON_VALUE)
    @ApiResponse(code = 200, message = "success")
    @PostMapping("/queryRealWarehouseByFactoryCodeAndRealWarehouseType")
    public Response<List<RealWarehouse>> queryRealWarehouseByFactoryCodeAndRealWarehouseType(@RequestParam("factoryCode") String factoryCode, @RequestParam("type") Integer type) {
        try {
            List<RealWarehouse> RealWarehouses = realWarehouseService.queryRealWarehouseByFactoryCodeAndRealWarehouseType(factoryCode, type);
            return ResponseMsg.SUCCESS.buildMsg(RealWarehouses);
        } catch (RomeException e) {
            log.error(e.getMessage(), e);
            return ResponseMsg.FAIL.buildMsg(e.getCode(), e.getMessage());
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return ResponseMsg.EXCEPTION.buildMsg();
        }
    }

    @ApiOperation(value = "根据实仓outCode和factoryCode查找虚仓接口", nickname = "queryVwByRealWarehouseCode", produces = MediaType.APPLICATION_JSON_VALUE)
    @ApiResponse(code = 200, message = "success")
    @PostMapping("/queryVwByRealWarehouseCode")
    public Response<List<VirtualWarehouse>> queryVwByRLCodeAndFactoryCode(@RequestParam("realWarehouseOutCode") String realWarehouseOutCode,@RequestParam("factoryCode")String factoryCode) {
        try {
            List<VirtualWarehouse> virtualWarehouses = virtualWarehouseService.queryByRealWarehouseCode(realWarehouseOutCode,factoryCode);
            return ResponseMsg.SUCCESS.buildMsg(virtualWarehouses);
        } catch (RomeException e) {
            log.error(e.getMessage(), e);
            return ResponseMsg.FAIL.buildMsg(e.getCode(), e.getMessage());
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return ResponseMsg.EXCEPTION.buildMsg();
        }
    }

    @ApiOperation(value = "根据虚仓编码批量查询虚仓信息", nickname = "queryVirtualWarehouseByCodes", produces = MediaType.APPLICATION_JSON_VALUE)
    @ApiResponse(code = 200, message = "success",response = List.class)
    @PostMapping("/queryVirtualWarehouseByCodes")
    public Response<List<VirtualWarehouse>> queryVirtualWarehouseByCodes(@RequestParam List<String> virtualWarehouseCodes){
        try {
            List<VirtualWarehouse> virtualWarehouseByCodes = virtualWarehouseService.getVirtualWarehouseByCodes(virtualWarehouseCodes);
            return ResponseMsg.SUCCESS.buildMsg(virtualWarehouseByCodes);
        }catch (RomeException e){
            log.error(e.getMessage(), e);
            return ResponseMsg.FAIL.buildMsg(e.getCode(), e.getMessage());
        }
        catch (Exception e){
            log.error(e.getMessage(), e);
            return ResponseMsg.EXCEPTION.buildMsg();
        }
    }


    @ApiOperation(value = "虚仓库存查询接口", nickname = "queryVwStockById", produces = MediaType.APPLICATION_JSON_VALUE)
    @ApiResponse(code = 200, message = "success", response = SkuStockForVw.class)
    @PostMapping("/queryVwStockById")
    public Response<List<SkuStockForVw>> queryVwStockById(@ApiParam(name = "skuInfoList", value = "sku集合") @RequestBody List<SkuInfoForVw> skuInfoList) {
        if (!validator.validCollection(skuInfoList)) {
            return ResponseMsg.PARAM_ERROR.buildMsg();
        }
        log.warn(CoreKibanaLog.getJobLog(LOGTYPE, "queryStock", "获取库存, 商品总数" + skuInfoList.size(), ""));
        //最大支持100个批量查询，查过的直接截取
        if (skuInfoList.size() > MAX_SIZE) {
            skuInfoList = skuInfoList.subList(0, MAX_SIZE);
        }
        try {
            List<SkuStockForVw> skuStockList = groupBuyService.querySkuStockList(skuInfoList);
            return ResponseMsg.SUCCESS.buildMsg(skuStockList);
        } catch (RomeException e) {
            log.error(e.getMessage(), e);
            return ResponseMsg.FAIL.buildMsg(e.getCode(), e.getMessage());
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return ResponseMsg.EXCEPTION.buildMsg();
        }
    }

    @ApiOperation(value = "根据outCode和factoryCode查询实仓信息", nickname = "queryByOutCodeAndFactoryCode", produces = MediaType.APPLICATION_JSON_VALUE)
    @ApiResponse(code = 200, message = "success", response = RealWarehouse.class)
    @GetMapping("/queryByOutCodeAndFactoryCode")
    public Response<RealWarehouse> queryByOutCodeAndFactoryCode(@RequestParam("outCode") String outCode, @RequestParam("factoryCode") String factoryCode) {
        try{
           return ResponseMsg.SUCCESS.buildMsg(realWarehouseService.findByRealWarehouseOutCodeAndFactoryCode(outCode,factoryCode));
        }catch (RomeException e){
            log.error(e.getMessage(), e);
            return ResponseMsg.FAIL.buildMsg(e.getCode(), e.getMessage());
        }catch (Exception e){
            log.error(e.getMessage(), e);
            return ResponseMsg.EXCEPTION.buildMsg();
        }
    }

    @ApiOperation(value = "根据outCode和factoryCode批量查询实仓信息", nickname = "queryByOutCodeAndFactoryCodeList", produces = MediaType.APPLICATION_JSON_VALUE)
    @ApiResponse(code = 200, message = "success", response =List.class)
    @PostMapping("/queryByOutCodeAndFactoryCodeList")
    public Response<List<RealWarehouse>> queryByOutCodeAndFactoryCodeList(@Validated @RequestBody List<QueryRealWarehouse> list) {
        try{
            return ResponseMsg.SUCCESS.buildMsg(realWarehouseService.findByRealWarehouseOutCodeAndFactoryCodeList(list));
        }catch (RomeException e){
            log.error(e.getMessage(), e);
            return ResponseMsg.FAIL.buildMsg(e.getCode(), e.getMessage());
        }catch (Exception e){
            log.error(e.getMessage(), e);
            return ResponseMsg.EXCEPTION.buildMsg();
        }
    }


    @ApiOperation(value = "保存虚仓转移单据", nickname = "saveVirtualWarehouseMoveRecord", produces = MediaType.APPLICATION_JSON_VALUE)
    @ApiResponse(code = 200, message = "success", response = List.class)
    @RequestMapping(value = "/saveVirtualWarehouseMoveRecord", method = RequestMethod.POST)
    public Response saveVirtualWarehouseMoveRecord(@ApiParam(name = "VirtualWarehouseMoveRecord", value = "保存单据信息")@Valid @RequestBody VwMoveRecordDTO dto) {
        String message = "";
        boolean isSucc = false;
        String json = JSON.toJSONString(dto);
        log.info("保存虚仓转移单据:参数为=>{}",json);
        try {
           String  recordCode = groupBuyService.saveVirtualWarehouseMoveRecord(dto);
            isSucc = true;
            message = "200,"+recordCode;
            return Response.builderSuccess(recordCode);
        } catch (RomeException e) {
            message = e.getMessage();
            log.error(e.getMessage(), e);
            return ResponseMsg.EXCEPTION.buildMsg(e.getCode(), e.getMessage());
        } catch (Exception e) {
            message = e.getMessage();
            log.error(e.getMessage(), e);
            return ResponseMsg.EXCEPTION.buildMsg(ResCode.STOCK_ERROR_1003, e.getMessage());
        }finally {
            sapInterfaceLogRepository.saveCallBackInterFaceLog(2, dto.getOrderCode(), "saveVirtualWarehouseMoveRecord",
                    json, message, isSucc);
        }
    }


    @ApiOperation(value = "团购仓批量发货", nickname = "groupOutRecordCallBack", produces = MediaType.APPLICATION_JSON_VALUE)
    @ApiResponse(code = 200, message = "success", response = List.class)
    @RequestMapping(value = "/groupOutRecordCallBack", method = RequestMethod.POST)
    public Response groupOutRecordCallBack(@RequestBody List<String> warehouseRecordCodes){
        String message = "";
        boolean isSucc = false;
        String json = JSON.toJSONString(warehouseRecordCodes);
        log.info("团购仓批量发货:参数为=>{}",json);
        try{
            groupBuyService.groupOutRecordCallBack(warehouseRecordCodes);
            isSucc = true;
            message = "200";
            return ResponseMsg.SUCCESS.buildMsg();
        }catch (RomeException e){
            message = e.getMessage();
            log.error(e.getMessage(),e);
            return Response.builderFail(e.getCode(),e.getMessage());
        }catch (Exception e){
            message = e.getMessage();
            log.error(e.getMessage(), e);
            return Response.builderFail(ResCode.STOCK_ERROR_1003,e.getMessage());
        }finally {
            sapInterfaceLogRepository.saveCallBackInterFaceLog(2, warehouseRecordCodes.get(0), "groupOutRecordCallBack",
                    json, message, isSucc);
        }
    }

    @ApiOperation(value = "创建预约单", nickname = "createReservation", produces = MediaType.APPLICATION_JSON_VALUE)
    @ApiResponse(code = 200, message = "success", response = List.class)
    @PostMapping("/createReservation")
    public Response<ReservationDTO> createReservation(@Validated @RequestBody ReservationDTO reservationDTO){
        String msg="200";
        boolean success=false;
        try{
            ReservationDTO reservation = reservationService.addReservation(reservationDTO);
            success=true;
            msg=JSON.toJSONString(reservation);
            return Response.builderSuccess(reservation);
        }catch (RomeException e){
            log.error(e.getMessage(),e);
            msg=e.getMessage();
            return Response.builderFail(e.getCode(),e.getMessage());
        }catch (Exception e){
            log.error(e.getMessage(), e);
            msg=e.getMessage();
            return Response.builderFail(ResCode.STOCK_ERROR_1003,ResCode.STOCK_ERROR_1003_DESC);
        }finally{
            sapInterfaceLogRepository.saveCallBackInterFaceLog(2, reservationDTO.getOutRecordCode(), "createReservation",
                    JSON.toJSONString(reservationDTO), msg, success);
        }
    }

    @ApiOperation(value = "创建do出库单", nickname = " createOutRecord", produces = MediaType.APPLICATION_JSON_VALUE)
    @ApiResponse(code = 200, message = "success", response = String.class)
    @PostMapping("/createOutRecord")
    public Response<String> createReservationDo(@Validated @RequestBody OutWarehouseRecordDTO outWarehouseRecordDTO){
        String msg="200";
        boolean success=false;
        try{
            String record = reservationService.addReservationDo(outWarehouseRecordDTO);
            success=true;
            return Response.builderSuccess(record);
        }catch (RomeException e){
            log.error(e.getMessage(),e);
            msg=e.getMessage();
            return Response.builderFail(e.getCode(),e.getMessage());
        }catch (Exception e){
            log.error(e.getMessage(), e);
            msg=e.getMessage();
            return Response.builderFail(ResCode.STOCK_ERROR_1003,ResCode.STOCK_ERROR_1003_DESC);
        }finally{
            sapInterfaceLogRepository.saveCallBackInterFaceLog(2, outWarehouseRecordDTO.getRecordCode(), "createOutRecord",
                    JSON.toJSONString(outWarehouseRecordDTO), msg, success);
        }
    }

    @ApiOperation(value = "二次锁定预约单", nickname = "createReservation", produces = MediaType.APPLICATION_JSON_VALUE)
    @ApiResponse(code = 200, message = "success", response = List.class)
    @GetMapping("/lockReservationByRecordCode/{outRecordCode}")
    public Response<ReservationDTO> lockReservationByRecordCode(@PathVariable("outRecordCode")String outRecordCode){
        String msg="200";
        boolean success=false;
        try{
            ReservationDTO reservationDTO = reservationService.lockReservation(outRecordCode);
            msg=JSON.toJSONString(reservationDTO);
            success=true;
            return Response.builderSuccess(reservationDTO);
        }catch (RomeException e){
            log.error(e.getMessage(),e);
            msg=e.getMessage();
            return Response.builderFail(e.getCode(),e.getMessage());
        }catch (Exception e){
            log.error(e.getMessage(), e);
            msg=e.getMessage();
            return Response.builderFail(ResCode.STOCK_ERROR_1003,ResCode.STOCK_ERROR_1003_DESC);
        }finally{
            sapInterfaceLogRepository.saveCallBackInterFaceLog(2, outRecordCode, "lockReservationByRecordCode",
                    outRecordCode, msg, success);
        }
    }

    @ApiOperation(value = "取消预约单", nickname = "cancelReservation", produces = MediaType.APPLICATION_JSON_VALUE)
    @ApiResponse(code = 200, message = "success")
    @PostMapping("/cancelReservation/{outRecordCode}")
    public Response cancelReservation(@PathVariable("outRecordCode")String outRecordCode){
        try{
            log.info("开始取消预约单:{}",outRecordCode);
            reservationService.cancelReservation(outRecordCode);
            return ResponseMsg.SUCCESS.buildMsg();
        }catch (RomeException ex){
            log.error(ex.getMessage(), ex);
            return ResponseMsg.FAIL.buildMsg(ex.getCode(), ex.getMessage());
        }catch (Exception ex){
            log.error(ex.getMessage(), ex);
            return ResponseMsg.EXCEPTION.buildMsg();
        }
    }

    @ApiOperation(value = "批量取消do单", nickname = "cancelBatch", produces = MediaType.APPLICATION_JSON_VALUE)
    @ApiResponse(code = 200, message = "success")
    @RequestMapping(value = "/cancelBatch", method = RequestMethod.POST)
    public Response<List<BatchResultDTO>> cancelBatch(@RequestBody List<CancelRecordDTO> list){
        List<BatchResultDTO> resultList =  new ArrayList<>();
        try{
            if(CollUtil.isNotEmpty(list)){
                for(CancelRecordDTO recordDTO:list){
                    BatchResultDTO dto = new BatchResultDTO();
                    dto.setRecordCode(recordDTO.getRecordCode());
                    String message = "";
                    boolean isSucc = false;
                    try{
                        reservationService.cancelGroupBuyDo(recordDTO);
                        isSucc = true;
                    }catch (Exception e){
                        message=e.getMessage();
                        log.error(e.getMessage(), e);
                    }finally {
                        dto.setMessage(message);
                        dto.setStatus(isSucc);
                        resultList.add(dto);
                        sapInterfaceLogRepository.saveCallBackInterFaceLog(1, dto.getRecordCode(), "cancelGroupBuyDo",
                                JSON.toJSONString(recordDTO), message, isSucc);
                    }
                }
            }
            return ResponseMsg.SUCCESS.buildMsg(resultList);
        }catch (RomeException ex){
            log.error(ex.getMessage(), ex);
            return ResponseMsg.FAIL.buildMsg(ex.getCode(), ex.getMessage());
        }catch (Exception ex){
            log.error(ex.getMessage(), ex);
            return ResponseMsg.EXCEPTION.buildMsg();
        }
    }


    @ApiOperation(value = "检查do单状态", nickname = "cancelReservation", produces = MediaType.APPLICATION_JSON_VALUE)
    @ApiResponse(code = 200, message = "success")
    @PostMapping("/checkDoStatus/{recordCode}")
    public Response checkDoStatus(@PathVariable("recordCode")String recordCode){
        try{
            Integer res = reservationService.checkDoStatus(recordCode);
            return ResponseMsg.SUCCESS.buildMsg(res);
        }catch (RomeException ex){
            log.error(ex.getMessage(), ex);
            return ResponseMsg.FAIL.buildMsg(ex.getCode(), ex.getMessage());
        }catch (Exception ex){
            log.error(ex.getMessage(), ex);
            return ResponseMsg.EXCEPTION.buildMsg();
        }
    }

    @ApiOperation(value = "定时查询待同步交货信息给销售中心", nickname = "getWaitSyncDeliveryToGroupPurchase", produces = MediaType.APPLICATION_JSON_VALUE)
    @ApiResponse(code = 200, message = "success", response = List.class)
    @RequestMapping(value = "/getWaitSyncDeliveryToGroupPurchase", method = RequestMethod.POST)
    public Response<List<GroupPurchaseDTO>> getWaitSyncDeliveryToGroupPurchase(@RequestParam(value = "page") Integer page, @RequestParam(value = "maxResult") Integer maxResult) {
        try {
            List<GroupPurchaseDTO> pageInfo = groupBuyService.getWaitSyncDeliveryToGroupPurchase(page, maxResult);
            return ResponseMsg.SUCCESS.buildMsg(pageInfo);
        } catch (RomeException e) {
            log.error(e.getMessage(), e);
            return ResponseMsg.FAIL.buildMsg(e.getCode(), e.getMessage());
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return ResponseMsg.EXCEPTION.buildMsg();
        }
    }

    @ApiOperation(value = "同步交货信息给销售中心", nickname = "syncDeliveryFulfillment", produces = MediaType.APPLICATION_JSON_VALUE)
    @ApiResponse(code = 200, message = "success", response = List.class)
    @RequestMapping(value = "/syncDeliveryGroupPurchase", method = RequestMethod.POST)
    public Response syncDeliveryGroupPurchase(@RequestBody GroupPurchaseDTO groupPurchaseDTO) {
        try {
            groupBuyService.syncDeliveryGroupPurchase(groupPurchaseDTO);
            return ResponseMsg.SUCCESS.buildMsg();
        } catch (RomeException e) {
            log.error(e.getMessage(), e);
            return ResponseMsg.FAIL.buildMsg(e.getCode(), e.getMessage());
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return ResponseMsg.EXCEPTION.buildMsg();
        }
    }


    @ApiOperation(value = "创建退货单", nickname = "createInRecord", produces = MediaType.APPLICATION_JSON_VALUE)
    @ApiResponse(code = 200, message = "success", response = String.class)
    @PostMapping(value = "/createInRecord")
    public Response<String> createReturnRecord(@Validated @RequestBody InWarehouseRecordDTO inWarehouseRecordDTO){
        try{
            String recordCode = reservationReturnService.saveReturnRecord(inWarehouseRecordDTO);
            return ResponseMsg.SUCCESS.buildMsg(recordCode);
        }catch (RomeException ex){
            log.error(ex.getMessage(), ex);
            return ResponseMsg.FAIL.buildMsg(ex.getCode(), ex.getMessage());
        }catch (Exception ex){
            log.error(ex.getMessage(), ex);
            return ResponseMsg.EXCEPTION.buildMsg();
        }
    }


    @ApiOperation(value = "释放预约单锁定库存", nickname = "unLockStock", produces = MediaType.APPLICATION_JSON_VALUE)
    @ApiResponse(code = 200, message = "success", response = Response.class)
    @PostMapping("unLockStock/{outRecordCode}")
    public Response unLockReservationStock(@PathVariable("outRecordCode")String outRecordCode){
        String msg="";
        boolean success = false;
        try{
            groupBuyService.unLockReservationStock(outRecordCode);
            success=true;
            return ResponseMsg.SUCCESS.buildMsg();
        }catch (RomeException e){
            log.error(e.getMessage(),e);
            msg=e.getMessage()==null?e.toString():e.getMessage();
            return Response.builderFail(e.getCode(),e.getMessage());
        }catch (Exception e){
            log.error(e.getMessage(), e);
            msg=e.getMessage()==null?e.toString():e.getMessage();
            return Response.builderFail(ResCode.STOCK_ERROR_1003,"释放预约单锁定库存失败");
        }finally {
            sapInterfaceLogRepository.saveCallBackInterFaceLog(2, outRecordCode, "unLockReservationStock",
                    outRecordCode, msg, success);
        }
    }

    @ApiOperation(value = "更新团购do单", nickname = "unLockStock", produces = MediaType.APPLICATION_JSON_VALUE)
    @ApiResponse(code = 200, message = "success", response = Response.class)
    @PostMapping("/updateGroupByDo")
    public Response updateGroupByDo(@RequestBody OutWarehouseRecordDTO outWarehouseRecordDTO){
        try{
            groupBuyService.updateGroupByDo(outWarehouseRecordDTO);
            return ResponseMsg.SUCCESS.buildMsg();
        }catch (RomeException e){
            log.error(e.getMessage(),e);
            return Response.builderFail(e.getCode(),e.getMessage());
        }catch (Exception e){
            log.error(e.getMessage(), e);
            return Response.builderFail(ResCode.STOCK_ERROR_1003,"更新do单失败");
        }
    }



}


