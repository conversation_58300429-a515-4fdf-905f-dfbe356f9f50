package com.rome.stock.innerservice.api.controller;

import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;

import com.rome.arch.core.clientobject.Response;
import com.rome.arch.core.exception.RomeException;
import com.rome.arch.util.controller.RomeController;
import com.rome.stock.innerservice.api.dto.SkuGroupChannelStockRelation;
import com.rome.stock.innerservice.constant.ResponseMsg;
import com.rome.stock.innerservice.domain.service.SkuGroupChannelStockRelationService;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import io.swagger.annotations.ApiResponse;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@RomeController
@RequestMapping("/stock/v1")
@Api(tags={"策略组关联渠道"})
public class SkuGroupChannelStockRelationController {
	@Autowired
	private SkuGroupChannelStockRelationService sgcrService;

	@ApiOperation(value = "创建渠道关系体", nickname = "addSkuGroupChannelStockRelation", produces = MediaType.APPLICATION_JSON_VALUE)
	@ApiResponse(code = 200, message = "success", response = SkuGroupChannelStockRelation.class)
	@RequestMapping(value = "/addSkuGroupChannelStockRelation", method = RequestMethod.POST)
	public Response addSkuGroupChannelStockRelation(
			@ApiParam(name = "SkuGroupChannelStockRelation", value = "渠道关系体") @RequestBody SkuGroupChannelStockRelation sgcrDto) {
		try {
			sgcrService.addSkuGroupChannelStockRelation(sgcrDto);
			return ResponseMsg.SUCCESS.buildMsg(sgcrDto);
		} catch (RomeException e) {
			log.error(e.getMessage(), e);
			return ResponseMsg.EXCEPTION.buildMsg(e.getCode(), e.getMessage());
		} catch (Exception e) {
			log.error(e.getMessage(), e);
			return ResponseMsg.EXCEPTION.buildMsg();
		}
	}

	@ApiOperation(value = "修改渠道关系体", nickname = "modfiySkuGroupChannelStockRelation", produces = MediaType.APPLICATION_JSON_VALUE)
	@ApiResponse(code = 200, message = "success", response = SkuGroupChannelStockRelation.class)
	@RequestMapping(value = "/modfiySkuGroupChannelStockRelation", method = RequestMethod.POST)
	public Response modfiySkuGroupChannelStockRelation(
			@ApiParam(name = "SkuGroupChannelStockRelation", value = "渠道关系体") @RequestBody SkuGroupChannelStockRelation sgcrDto) {
		try {
			sgcrService.modifySkuGroupChannelStockRelation(sgcrDto);
			return ResponseMsg.SUCCESS.buildMsg(sgcrDto);
		} catch (RomeException e) {
			log.error(e.getMessage(), e);
			return ResponseMsg.EXCEPTION.buildMsg(e.getCode(), e.getMessage());
		} catch (Exception e) {
			log.error(e.getMessage(), e);
			return ResponseMsg.EXCEPTION.buildMsg();
		}
	}

	@ApiOperation(value = "策略组ID查渠道", nickname = "getSkuGroupChannelStockRelationByVGId", produces = MediaType.APPLICATION_JSON_VALUE)
	@ApiResponse(code = 200, message = "success", response = SkuGroupChannelStockRelation.class)
	@RequestMapping(value = "/getSkuGroupChannelStockRelationByVGId/{virtualWarehouseGroupId}", method = RequestMethod.GET)
	public Response getSkuGroupChannelStockRelationByVGId(
			@ApiParam(name = "virtualWarehouseGroupId", value = "策略组ID") @PathVariable long virtualWarehouseGroupId) {
		try {
			List<SkuGroupChannelStockRelation> sgcrDto = sgcrService.getSkuGroupChannelStockRelationByVGId(virtualWarehouseGroupId);
			return ResponseMsg.SUCCESS.buildMsg(sgcrDto);
		} catch (RomeException e) {
			log.error(e.getMessage(), e);
			return ResponseMsg.EXCEPTION.buildMsg(e.getCode(), e.getMessage());
		} catch (Exception e) {
			log.error(e.getMessage(), e);
			return ResponseMsg.EXCEPTION.buildMsg();
		}
	}

	@ApiOperation(value = "渠道组ID查关系体", nickname = "getSkuGroupChannelStockRelationByChannelId", produces = MediaType.APPLICATION_JSON_VALUE)
	@ApiResponse(code = 200, message = "success", response = SkuGroupChannelStockRelation.class)
	@RequestMapping(value = "/getSkuGroupChannelStockRelationByChannelId/{channelId}", method = RequestMethod.GET)
	public Response getSkuGroupChannelStockRelationByChannelId(
			@ApiParam(name = "channelId", value = "策略组ID") @PathVariable long channelId) {
		try {
			SkuGroupChannelStockRelation sgcrDto = sgcrService.getSkuGroupChannelStockRelationBychannelId(channelId);
			return ResponseMsg.SUCCESS.buildMsg(sgcrDto);
		} catch (RomeException e) {
			log.error(e.getMessage(), e);
			return ResponseMsg.EXCEPTION.buildMsg(e.getCode(), e.getMessage());
		} catch (Exception e) {
			log.error(e.getMessage(), e);
			return ResponseMsg.EXCEPTION.buildMsg();
		}
	}
	@ApiOperation(value = "查渠道所有关系体", nickname = "getSkuGroupChannelStockRelationAll", produces = MediaType.APPLICATION_JSON_VALUE)
	@ApiResponse(code = 200, message = "success", response = SkuGroupChannelStockRelation.class)
	@RequestMapping(value = "/getSkuGroupChannelStockRelationAll", method = RequestMethod.GET)
	public Response getSkuGroupChannelStockRelationAll() {
		try {
			List<SkuGroupChannelStockRelation> sgcrDto = sgcrService.getSkuGroupChannelStockRelationAll();
			return ResponseMsg.SUCCESS.buildMsg(sgcrDto);
		} catch (RomeException e) {
			log.error(e.getMessage(), e);
			return ResponseMsg.EXCEPTION.buildMsg(e.getCode(), e.getMessage());
		} catch (Exception e) {
			log.error(e.getMessage(), e);
			return ResponseMsg.EXCEPTION.buildMsg();
		}
	}
}
