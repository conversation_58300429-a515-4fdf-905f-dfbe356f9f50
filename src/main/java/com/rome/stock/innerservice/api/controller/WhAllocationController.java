package com.rome.stock.innerservice.api.controller;

import java.util.ArrayList;
import java.util.List;

import javax.annotation.Resource;

import com.rome.stock.common.annotation.RedisLock;
import com.rome.stock.innerservice.api.dto.*;
import com.rome.stock.innerservice.api.dto.warehouserecord.*;
import com.rome.stock.innerservice.domain.repository.BatchStockRepository;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;

import com.alibaba.fastjson.JSON;
import com.github.pagehelper.PageInfo;
import com.rome.arch.core.clientobject.Response;
import com.rome.arch.core.exception.RomeException;
import com.rome.arch.util.controller.RomeController;
import com.rome.stock.common.annotation.RedisLock;
import com.rome.stock.innerservice.api.dto.*;
import com.rome.stock.innerservice.api.dto.allocation.*;
import com.rome.stock.innerservice.api.dto.warehouserecord.*;
import com.rome.stock.innerservice.api.dto.allocation.WhAllocationExportTemplate;
import com.rome.stock.innerservice.api.dto.allocation.WhAllocationRecordDTO;
import com.rome.stock.innerservice.api.dto.allocation.WhAllocationRecordPageDTO;
import com.rome.stock.innerservice.api.dto.allocation.WhAllocationSkuQueryDTO;
import com.rome.stock.innerservice.api.dto.allocation.WhAllocationTemplateDTO;
import com.rome.stock.innerservice.api.dto.allocation.WhAllocationWarehouseDetailDTO;
import com.rome.stock.innerservice.common.RedisUtil;
import com.rome.stock.innerservice.common.ResCode;
import com.rome.stock.innerservice.constant.ResponseMsg;
import com.rome.stock.innerservice.domain.entity.warehouserecord.WarehouseRecordE;
import com.rome.stock.innerservice.domain.repository.BatchStockRepository;
import com.rome.stock.innerservice.domain.repository.SapInterfaceLogRepository;
import com.rome.stock.innerservice.domain.repository.warehouserecord.WarehouseRecordRepository;
import com.rome.stock.innerservice.domain.service.ShopReplenishService;
import com.rome.stock.innerservice.domain.service.WarehouseRecordService;
import com.rome.stock.innerservice.domain.service.WhAllocationService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import io.swagger.annotations.ApiResponse;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;

/**
 * 类WhAllocationController的实现描述:仓库调拨
 *
 * <AUTHOR> 2019/5/13 10:20
 */
@Slf4j
@RomeController
@RequestMapping("/stock/v1/wh_allocation")
@Api(tags={"仓库调拨"})
public class WhAllocationController {

    @Autowired
    private WhAllocationService whAllocationService;

    @Resource
    private SapInterfaceLogRepository sapInterfaceLogRepository;

    @Autowired
    private WarehouseRecordService warehouseRecordService;

    @Autowired
    private ShopReplenishService shopReplenishService;
    @Resource
    private RedisUtil redisUtil;
    @Resource
    private WarehouseRecordRepository warehouseRecordRepository;
    @Resource
    private BatchStockRepository batchStockRepository;

    @ApiOperation(value = "test", produces = MediaType.APPLICATION_JSON_VALUE)
    @ApiResponse(code = 200, message = "success")
    @RequestMapping(value = "/test", method = RequestMethod.POST)
    public Response test(@RequestParam("recordCode") String recordCode) {
        try {
            return Response.builderSuccess(batchStockRepository.queryBatchStockChangeFlowESByRecordCode(recordCode));
        } catch (RomeException e) {
            log.error(e.getMessage(), e);
            return Response.builderFail(e.getCode(),e.getMessage());
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return Response.builderFail(ResCode.STOCK_ERROR_1003,ResCode.STOCK_ERROR_1003_DESC+e.getMessage()==null?e.toString():e.getMessage());
        }
    }



    @ApiOperation(value = "根据sku集合、realWarehouseId、是否质量问题调拨查询实仓库存列表", produces = MediaType.APPLICATION_JSON_VALUE)
    @ApiResponse(code = 200, message = "success")
    @RequestMapping(value = "/listStockBySkuWhIdForWhAllot", method = RequestMethod.POST)
    public Response listStockBySkuWhIdForWhAllot(@ApiParam(name = "whAllocationSkuQueryDTO", value = "调拨查询实仓库存列表") @RequestBody WhAllocationSkuQueryDTO whAllocationSkuQueryDTO) {
        try {
            List<RealWarehouseStockDTO> page=whAllocationService.listStockBySkuWhIdForWhAllot(whAllocationSkuQueryDTO);
            return Response.builderSuccess(page);
        } catch (RomeException e) {
            log.error(e.getMessage(), e);
            return Response.builderFail(e.getCode(),e.getMessage());
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return Response.builderFail(ResCode.STOCK_ERROR_1003,ResCode.STOCK_ERROR_1003_DESC+e.getMessage()==null?e.toString():e.getMessage());
        }
    }

    @ApiOperation(value = "保存sapInterFaceLog", produces = MediaType.APPLICATION_JSON_VALUE)
    @ApiResponse(code = 200, message = "success")
    @RequestMapping(value = "/saveSapInterFaceLog", method = RequestMethod.POST)
    public Response saveSapInterFaceLog(@RequestBody SapInterfaceLogDTO sapInterfaceLogDTO) {
        try {
            sapInterfaceLogRepository.saveInterfaceLogByDTO(sapInterfaceLogDTO);
            return Response.builderSuccess("");
        } catch (RomeException e) {
            log.error(e.getMessage(), e);
            return Response.builderFail(e.getCode(),e.getMessage());
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return Response.builderFail(ResCode.STOCK_ERROR_1003,ResCode.STOCK_ERROR_1003_DESC+e.getMessage()==null?e.toString():e.getMessage());
        }
    }

    @ApiOperation(value = "订单中心通知库存生产出库单", produces = MediaType.APPLICATION_JSON_VALUE)
    @ApiResponse(code = 200, message = "success")
    @RequestMapping(value = "/addWhAllocationOutRecord", method = RequestMethod.POST)
    public Response addWhAllocationOutRecord(@ApiParam(name = "frontRecord", value = "订单中心通知库存生产出库单") @RequestBody @Validated OutWarehouseRecordDTO outWarehouseRecordDTO) {
        try {
            log.info("订单中心通知库存生产出库单,入参=>"+JSON.toJSONString(outWarehouseRecordDTO));
            whAllocationService.addWhAllocationOutRecord(outWarehouseRecordDTO, null);
            return Response.builderSuccess("");
        } catch (RomeException e) {
            log.error(e.getMessage(), e);
            return Response.builderFail(e.getCode(),e.getMessage());
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return Response.builderFail(ResCode.STOCK_ERROR_1003,ResCode.STOCK_ERROR_1003_DESC+e.getMessage()==null?e.toString():e.getMessage());
        }
    }
    @ApiOperation(value = "订单中心通知库存生产出库单(非中台调中台)", produces = MediaType.APPLICATION_JSON_VALUE)
    @ApiResponse(code = 200, message = "success")
    @RequestMapping(value = "/addWhAllocationOutRecordByNotZT", method = RequestMethod.POST)
    public Response addWhAllocationOutRecordByNotZT(@ApiParam(name = "frontRecord", value = "订单中心通知库存生产出库单") @RequestBody @Validated OutWarehouseRecordDTO outWarehouseRecordDTO) {
        try {
            log.info("订单中心通知库存生产出库单(非中台调中台),入参=>"+JSON.toJSONString(outWarehouseRecordDTO));
            whAllocationService.addWhAllocationOutRecordByNotZT(outWarehouseRecordDTO);
            return Response.builderSuccess("");
        } catch (RomeException e) {
            log.error(e.getMessage(), e);
            return Response.builderFail(e.getCode(),e.getMessage());
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return Response.builderFail(ResCode.STOCK_ERROR_1003,ResCode.STOCK_ERROR_1003_DESC+e.getMessage()==null?e.toString():e.getMessage());
        }
    }

    @ApiOperation(value = "创建后置单后-更新LineNoAndSapPoNo", produces = MediaType.APPLICATION_JSON_VALUE)
    @ApiResponse(code = 200, message = "success")
    @RequestMapping(value = "/updateLineNoAndSapPoNo", method = RequestMethod.POST)
    public Response updateLineNoAndSapPoNo(@ApiParam(name = "frontRecord", value = "创建后置单后-更新LineNoAndSapPoNo")
                                               @RequestBody @Validated List<WhAllocationWarehouseDetailDTO> whAllocationWarehouseDetailDTOList) {
        String message = "";
        boolean isSucc = false;
        String json = JSON.toJSONString(whAllocationWarehouseDetailDTOList);
        try {
            whAllocationService.updateLineNoAndSapPoNo(whAllocationWarehouseDetailDTOList);
            isSucc = true;
            message = "200";
            return Response.builderSuccess("");
        } catch (RomeException e) {
            message = e.getMessage();
            log.error(e.getMessage(), e);
            return Response.builderFail(e.getCode(),e.getMessage());
        } catch (Exception e) {
            message = e.getMessage();
            log.error(e.getMessage(), e);
            return Response.builderFail(ResCode.STOCK_ERROR_1003,ResCode.STOCK_ERROR_1003_DESC+e.getMessage()==null?e.toString():e.getMessage());
        } finally {
            sapInterfaceLogRepository.saveCallBackInterFaceLog(2, whAllocationWarehouseDetailDTOList.get(0).getRecordCode(), "updateLineNoAndSapPoNo",
                    json, message, isSucc);
        }
    }

    @ApiOperation(value = "查询待推订单中心的调拨出库单", produces = MediaType.APPLICATION_JSON_VALUE)
    @ApiResponse(code = 200, message = "success")
    @RequestMapping(value = "/getWaitWhAllocationNotify", method = RequestMethod.POST)
    public Response getWaitWhAllocationNotify(@ApiParam(name = "page", value = "page") @RequestParam("page") Integer page,
                                                 @ApiParam(name = "maxResult", value = "maxResult") @RequestParam("maxResult") Integer maxResult) {
        try {
            List<WarehouseRecordPageDTO> resList=whAllocationService.getWaitWhAllocationNotify(page,maxResult);
            return Response.builderSuccess(resList);
        } catch (RomeException e) {
            log.error(e.getMessage(), e);
            return Response.builderFail(e.getCode(),e.getMessage());
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return Response.builderFail(ResCode.STOCK_ERROR_1003,ResCode.STOCK_ERROR_1003_DESC+e.getMessage()==null?e.toString():e.getMessage());
        }
    }

    @ApiOperation(value = "查询待推订单中心的入库单", produces = MediaType.APPLICATION_JSON_VALUE)
    @ApiResponse(code = 200, message = "success")
    @RequestMapping(value = "/getWaitWhAllocationInNotify", method = RequestMethod.POST)
    public Response getWaitWhAllocationInNotify(@ApiParam(name = "page", value = "page") @RequestParam("page") Integer page,
                                              @ApiParam(name = "maxResult", value = "maxResult") @RequestParam("maxResult") Integer maxResult) {
        try {
            List<ReceiptRecordDTO> resList=whAllocationService.getWaitWhAllocationInNotify(page,maxResult);
            return Response.builderSuccess(resList);
        } catch (RomeException e) {
            log.error(e.getMessage(), e);
            return Response.builderFail(e.getCode(),e.getMessage());
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return Response.builderFail(ResCode.STOCK_ERROR_1003,ResCode.STOCK_ERROR_1003_DESC+e.getMessage()==null?e.toString():e.getMessage());
        }
    }


    @ApiOperation(value = "推送出库单到订单中心", produces = MediaType.APPLICATION_JSON_VALUE)
    @ApiResponse(code = 200, message = "success")
    @RequestMapping(value = "/pushOutNotify", method = RequestMethod.POST)
    public Response pushOutNotify(@ApiParam(name = "recordCode", value = "recordCode") @RequestParam("recordCode") String recordCode) {
        try {
            whAllocationService.pushOutNotify(recordCode);
            return Response.builderSuccess("");
        } catch (RomeException e) {
            log.error(e.getMessage(), e);
            return Response.builderFail(e.getCode(),e.getMessage());
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return Response.builderFail(ResCode.STOCK_ERROR_1003,ResCode.STOCK_ERROR_1003_DESC+e.getMessage()==null?e.toString():e.getMessage());
        }
    }

    @ApiOperation(value = "推送入库单到订单中心", produces = MediaType.APPLICATION_JSON_VALUE)
    @ApiResponse(code = 200, message = "success")
    @RequestMapping(value = "/pushInNotify", method = RequestMethod.POST)
    @RedisLock(lockKeyCode = "#receiptRecordDTO.warehouseRecordCode")
    public Response pushInNotify(@RequestBody ReceiptRecordDTO receiptRecordDTO) {
        String message = "";
        boolean isSucc = false;
        try {
            if(1 == receiptRecordDTO.getPushType()){
                whAllocationService.pushInNotifyForRw(receiptRecordDTO);
            }else{
                whAllocationService.pushInNotifyForShop(receiptRecordDTO);
            }
            isSucc = true;
            message = "200";
            return Response.builderSuccess("");
        } catch (RomeException e) {
            log.error(e.getMessage(), e);
            message = e.getMessage();
            return Response.builderFail(e.getCode(),e.getMessage());
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            message = e.getMessage();
            return Response.builderFail(ResCode.STOCK_ERROR_1003,ResCode.STOCK_ERROR_1003_DESC+e.getMessage()==null?e.toString():e.getMessage());
        }finally {
            //调用失败的时候才保存日志
            if(!isSucc){
                sapInterfaceLogRepository.saveSapInterFaceLog(receiptRecordDTO.getWarehouseRecordCode(),
                        "pushInNotify", "pushInNotify", receiptRecordDTO.getWarehouseRecordCode(), message,isSucc);
            }
        }
    }

    @ApiOperation(value = "sapMM仓接收订单中心回调", nickname = "sapMmWarehouseOutNotify", produces = MediaType.APPLICATION_JSON_VALUE)
    @ApiResponse(code = 200, message = "success", response = List.class)
    @RequestMapping(value = "/sapMmWarehouseOutNotify", method = RequestMethod.POST)
    public Response sapMmWarehouseOutNotify(@RequestBody WarehouseRecordDTO warehouseRecord){
        try{
            warehouseRecordService.wmsOutRecordCallBack(warehouseRecord);
            return ResponseMsg.SUCCESS.buildMsg();
        }catch (RomeException e){
            log.error(e.getMessage(),e);
            return Response.builderFail(e.getCode(),e.getMessage());
        }catch (Exception e){
            log.error(e.getMessage(), e);
            return Response.builderFail(ResCode.STOCK_ERROR_1003,ResCode.STOCK_ERROR_1003_DESC);
        }
    }

    @ApiOperation(value = "订单中心通知库存中心创建入库单", nickname = "warehouseInCreate", produces = MediaType.APPLICATION_JSON_VALUE)
    @ApiResponse(code = 200, message = "success", response = List.class)
    @RequestMapping(value = "/warehouseInCreate", method = RequestMethod.POST)
    public Response warehouseInCreate(@RequestBody InWarehouseRecordDTO inWarehouseRecordDTO){
        try{
            log.info("订单中心通知库存中心创建入库单,入参=>"+JSON.toJSONString(inWarehouseRecordDTO));
            whAllocationService.warehouseInCreate(inWarehouseRecordDTO);
            return ResponseMsg.SUCCESS.buildMsg();
        }catch (RomeException e){
            log.error(e.getMessage(),e);
            return Response.builderFail(e.getCode(),e.getMessage());
        }catch (Exception e){
            log.error(e.getMessage(), e);
            return Response.builderFail(ResCode.STOCK_ERROR_1003,ResCode.STOCK_ERROR_1003_DESC);
        }
    }

    @ApiOperation(value = "订单中心通知库存中心创建入库单(中台调非中台)", nickname = "warehouseInCreateByZT", produces = MediaType.APPLICATION_JSON_VALUE)
    @ApiResponse(code = 200, message = "success", response = List.class)
    @RequestMapping(value = "/warehouseInCreateByZT", method = RequestMethod.POST)
    public Response warehouseInCreateByZT(@RequestBody InWarehouseRecordDTO inWarehouseRecordDTO){
        try{
            log.error("订单中心通知库存中心创建入库单(中台调非中台),入参=>"+JSON.toJSONString(inWarehouseRecordDTO));
            whAllocationService.warehouseInCreateByZT(inWarehouseRecordDTO);
            return ResponseMsg.SUCCESS.buildMsg();
        }catch (RomeException e){
            log.error(e.getMessage(),e);
            return Response.builderFail(e.getCode(),e.getMessage());
        }catch (Exception e){
            log.error(e.getMessage(), e);
            return Response.builderFail(ResCode.STOCK_ERROR_1003,ResCode.STOCK_ERROR_1003_DESC);
        }
    }

    @ApiOperation(value = "批量取消申请", nickname = "cancleWhAllocation", produces = MediaType.APPLICATION_JSON_VALUE)
    @ApiResponse(code = 200, message = "success")
    @RequestMapping(value = "/cancleBatch", method = RequestMethod.POST)
    public Response cancleBatch(@RequestBody List<CancelRecordDTO> list){
        List<BatchResultDTO> resultList =  new ArrayList<>();
        try {
            if(CollectionUtils.isNotEmpty(list)){
                for (CancelRecordDTO cancelRecordDTO : list) {
                    BatchResultDTO dto = new BatchResultDTO();
                    dto.setRecordCode(cancelRecordDTO.getRecordCode());
                    dto.setStatus(false);
                    String message = "";
                    boolean isSucc = false;
                    try {
                        Integer isForceCancle = 0;
                        if(cancelRecordDTO.getIsForceCancel()){
                            isForceCancle = 1;
                        }
                        whAllocationService.cancelWhAllocation(cancelRecordDTO.getRecordCode(), isForceCancle);
                        dto.setStatus(true);
                        isSucc = true;
                        message = "200";
                    }catch (Exception e){
                        message=e.getMessage();
                        log.error(e.getMessage(), e);
                    }finally {
                        dto.setMessage(message);
                        resultList.add(dto);
                        sapInterfaceLogRepository.saveCallBackInterFaceLog(1, dto.getRecordCode(), "cancleWhAllocation",
                                JSON.toJSONString(cancelRecordDTO), message, isSucc);
                    }

                }
            }
            return ResponseMsg.SUCCESS.buildMsg(resultList);
        } catch (RomeException e) {
            log.error(e.getMessage(), e);
            return Response.builderFail(e.getCode(),e.getMessage());
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return ResponseMsg.EXCEPTION.buildMsg();
        }
    }


    @ApiOperation(value = "分页查询前置单据信息", nickname = "query_virtual_warehouse", produces = MediaType.APPLICATION_JSON_VALUE)
    @ApiResponse(code = 200, message = "success", response = List.class)
    @RequestMapping(value = "/listPage", method = RequestMethod.POST)
    public Response listPage(@ApiParam(name = "virtualWarehouse", value = "dto") @RequestBody WhAllocationRecordPageDTO frontRecord) {
        try {
            PageInfo<WhAllocationRecordPageDTO> personPageInfo = whAllocationService.queryList(frontRecord);
            return Response.builderSuccess(personPageInfo);
        } catch (RomeException e) {
            log.error(e.getMessage(), e);
            return Response.builderFail(e.getCode(),e.getMessage());
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return Response.builderFail(ResCode.STOCK_ERROR_1003_DESC,ResCode.STOCK_ERROR_1003_DESC+e.getMessage()==null?e.toString():e.getMessage());
        }
    }

    @ApiOperation(value = "查询单据sku实仓虚仓分配关系[调拨入库]", nickname = "queryAllocConfigInfo", produces = MediaType.APPLICATION_JSON_VALUE)
    @ApiResponse(code = 200, message = "success")
    @RequestMapping(value = "/queryAllocConfigInfo/{recordId}", method = RequestMethod.GET)
    public Response queryAllocConfigInfo(@ApiParam(name = "recordId", value = "recordId") @PathVariable Long recordId) {
        try {
            return Response.builderSuccess(whAllocationService.queryAllocConfigInfo(recordId));
        } catch (RomeException e) {
            log.error(e.getMessage(), e);
            return Response.builderFail(e.getCode(), e.getMessage());
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return Response.builderFail(ResCode.STOCK_ERROR_1003_DESC, ResCode.STOCK_ERROR_1003_DESC);
        }
    }
    @ApiOperation(value = "查询单据sku实仓虚仓分配关系[调拨出库]", nickname = "queryOutAllocConfigInfo", produces = MediaType.APPLICATION_JSON_VALUE)
    @ApiResponse(code = 200, message = "success")
    @RequestMapping(value = "/queryOutAllocConfigInfo/{recordId}", method = RequestMethod.GET)
    public Response queryOutAllocConfigInfo(@ApiParam(name = "recordId", value = "recordId") @PathVariable Long recordId) {
        try {
            return Response.builderSuccess(whAllocationService.queryOutAllocConfigInfo(recordId));
        } catch (RomeException e) {
            log.error(e.getMessage(), e);
            return Response.builderFail(e.getCode(), e.getMessage());
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return Response.builderFail(ResCode.STOCK_ERROR_1003_DESC, ResCode.STOCK_ERROR_1003_DESC);
        }
    }

    @ApiOperation(value = "查询单据sku实仓虚仓分配关系[调拨入库]", nickname = "queryAllocConfigInfoByRecords", produces = MediaType.APPLICATION_JSON_VALUE)
    @ApiResponse(code = 200, message = "success")
    @RequestMapping(value = "/queryAllocConfigInfoByRecords", method = RequestMethod.POST)
    public Response<List<WhAllocationRecordPageDTO>> queryAllocConfigInfoByRecords(@ApiParam(name = "recordIds", value = "recordIds") @RequestBody List<Long> recordIds) {
        try {
            return Response.builderSuccess(whAllocationService.queryAllocConfigInfoByRecords(recordIds));
        } catch (RomeException e) {
            log.error(e.getMessage(), e);
            return Response.builderFail(e.getCode(), e.getMessage());
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return Response.builderFail("500000", "系统异常");
        }
    }

    @ApiOperation(value = "查询单据sku实仓虚仓分配关系[调拨出库]", nickname = "queryOutAllocConfigInfoByRecords", produces = MediaType.APPLICATION_JSON_VALUE)
    @ApiResponse(code = 200, message = "success")
    @RequestMapping(value = "/queryOutAllocConfigInfoByRecords", method = RequestMethod.POST)
    public Response<List<WhAllocationRecordPageDTO>> queryOutAllocConfigInfoByRecords(@ApiParam(name = "recordIds", value = "recordIds") @RequestBody List<Long> recordIds) {
        try {
            return Response.builderSuccess(whAllocationService.queryOutAllocConfigInfoByRecords(recordIds));
        } catch (RomeException e) {
            log.error(e.getMessage(), e);
            return Response.builderFail(e.getCode(), e.getMessage());
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return Response.builderFail("500000", "系统异常");
        }
    }

    @ApiOperation(value = "初始化新增页面", nickname = "initAddPage", produces = MediaType.APPLICATION_JSON_VALUE)
    @ApiResponse(code = 200, message = "success")
    @RequestMapping(value = "/initAddPage", method = RequestMethod.GET)
    public Response initAddPage() {
        try {
            return Response.builderSuccess(whAllocationService.initAddPage());
        } catch (RomeException e) {
            log.error(e.getMessage(), e);
            return Response.builderFail(e.getCode(), e.getMessage());
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return Response.builderFail(ResCode.STOCK_ERROR_1003_DESC, ResCode.STOCK_ERROR_1003_DESC);
        }
    }


    @ApiOperation(value = "获取待同步的订单", nickname = "getWaitSyncOrder", produces = MediaType.APPLICATION_JSON_VALUE)
    @ApiResponse(code = 200, message = "success")
    @RequestMapping(value = "/getWaitSyncOrder", method = RequestMethod.POST)
    public Response getWaitSyncOrder(@RequestParam("page") int page, @RequestParam("maxResult") int maxResult) {
        try {
            List<WhAllocationRecordDTO> list =  whAllocationService.getWaitSyncOrder(page, maxResult);
            return ResponseMsg.SUCCESS.buildMsg(list);
        } catch (RomeException e) {
            log.error(e.getMessage(), e);
            return ResponseMsg.FAIL.buildMsg(e.getCode(),e.getMessage());
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return ResponseMsg.EXCEPTION.buildMsg(ResCode.STOCK_ERROR_1003_DESC,ResCode.STOCK_ERROR_1003_DESC);
        }
    }


    @ApiOperation(value = "下发同公司PO单给SAP", nickname = "processWhAllocationOrderToSap", produces = MediaType.APPLICATION_JSON_VALUE)
    @ApiResponse(code = 200, message = "success")
    @RequestMapping(value = "/processWhAllocationOrderToSap", method = RequestMethod.POST)
    public Response processWhAllocationOrderToSap(@RequestBody WhAllocationRecordDTO order) {
        try {
            whAllocationService.processWhAllocationOrderToSap(order);
            return ResponseMsg.SUCCESS.buildMsg(null);
        } catch (RomeException e) {
            log.error(e.getMessage(), e);
            return ResponseMsg.FAIL.buildMsg(e.getCode(),e.getMessage());
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return ResponseMsg.EXCEPTION.buildMsg(ResCode.STOCK_ERROR_1003_DESC,ResCode.STOCK_ERROR_1003_DESC);
        }
    }

    @RequestMapping(value = "/importWhAllocation", method = RequestMethod.POST)
    @ApiOperation(value = "导入excel文件中的数据", nickname = "importFileData", produces = MediaType.MULTIPART_FORM_DATA_VALUE)
    public Response importWhAllocation(@RequestBody List<WhAllocationTemplateDTO> dataList, @RequestParam("userId") Long userId) {
        try {
            whAllocationService.importFileData(dataList,userId);
            return ResponseMsg.SUCCESS.buildMsg();
        } catch (RomeException e) {
            log.error(e.getMessage(), e);
            return ResponseMsg.FAIL.buildMsg(e.getCode(), e.getMessage());
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return ResponseMsg.EXCEPTION.buildMsg();
        }
    }

	@ApiOperation(value = "创建差异调拨单", produces = MediaType.APPLICATION_JSON_VALUE)
    @ApiResponse(code = 200, message = "success")
    @RequestMapping(value = "/createDisparityAllot", method = RequestMethod.POST)
    public Response createDisparityAllot(@RequestParam("id")Long id, @RequestParam("userId")Long userId) {
        try {
            String tipLogs = whAllocationService.createDisparityAllot(id, userId);
            return Response.builderSuccess(tipLogs);
        } catch (RomeException e) {
            log.error(e.getMessage(), e);
            return Response.builderFail(e.getCode(),e.getMessage());
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return Response.builderFail(ResCode.STOCK_ERROR_1003_DESC,ResCode.STOCK_ERROR_1003_DESC+e.getMessage()==null?e.toString():e.getMessage());
        }
    }

    @ApiOperation(value = "导出调拨单", produces = MediaType.APPLICATION_JSON_VALUE)
    @ApiResponse(code = 200, message = "success", response = List.class)
    @RequestMapping(value = "/exportWhallot", method = RequestMethod.POST)
    public Response exportWhallot(@RequestBody WhAllocationRecordPageDTO frontRecord) {
        try {
            List<WhAllocationExportTemplate> exportWhallot = whAllocationService.exportWhallot(frontRecord);
            return Response.builderSuccess(exportWhallot);
        } catch (RomeException e) {
            log.error(e.getMessage(), e);
            return Response.builderFail(e.getCode(),e.getMessage());
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return Response.builderFail(ResCode.STOCK_ERROR_1003_DESC,ResCode.STOCK_ERROR_1003_DESC+e.getMessage()==null?e.toString():e.getMessage());
        }
    }


    @ApiOperation(value = "移动pos根据门店编号查询实仓库存", nickname = "queryStockByShopCodeForPos", produces = MediaType.APPLICATION_JSON_VALUE)
    @ApiResponse(code = 200, message = "success", response = RealWarehouse.class)
    @RequestMapping(value = "/queryStockByShopCodeForPos", method = RequestMethod.POST)
    public Response<PageInfo<PosResultDTO>> queryStockByShopCodeForPos(@RequestBody PosQueryDTO posQueryDTO) {
        try {
            PageInfo<PosResultDTO> stockList = whAllocationService.queryStockByShopCodeForPos(posQueryDTO);
            return ResponseMsg.SUCCESS.buildMsg(stockList);
        } catch (RomeException e) {
            log.error(e.getMessage(), e);
            return ResponseMsg.FAIL.buildMsg(e.getCode(), e.getMessage());
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return ResponseMsg.EXCEPTION.buildMsg();
        }
    }

    @ApiOperation(value = "创建团购调拨单", nickname = "createWhAllocationOut", produces = MediaType.APPLICATION_JSON_VALUE)
    @ApiResponse(code = 200, message = "success", response = RealWarehouse.class)
    @PostMapping("/groupBuy/createWhAllocationOut")
    public Response createWhAllocationOut(@Validated @RequestBody OutWarehouseRecordDTO outWarehouseRecordDTO){
        try{
            whAllocationService.createWhAllocationOutByGroupBuy(outWarehouseRecordDTO);
            return ResponseMsg.SUCCESS.buildMsg();
        }catch (RomeException e){
            log.error(e.getMessage(), e);
            return ResponseMsg.FAIL.buildMsg(e.getCode(), "生成调拨单失败" + e.getMessage());
        }catch (Exception e){
            log.error(e.getMessage(), e);
            return ResponseMsg.EXCEPTION.buildMsg();
        }
    }


    @ApiOperation(value = "调拨-部分锁定库存并返回Sku锁定数量", nickname = "partLockStockAndReturnForWh", produces = MediaType.APPLICATION_JSON_VALUE)
    @ApiResponse(code = 200, message = "success", response = Response.class)
    @RequestMapping(value = "/partLockStockAndReturnForWh", method = RequestMethod.POST)
    public Response<LockStockResp> partLockStockAndReturnForWh(@RequestBody OutWarehouseRecordDTO dto) {
        String uniqueKey ="partLockStockAndReturnForWh" + "_" + dto.getRecordCode() ;
        boolean isLock = false;
        try {
            isLock = redisUtil.lock(uniqueKey, "1", 120);
            if (isLock) {
                dto.setAppIdFlag(false);
                LockStockResp res = shopReplenishService.partLockStockAndReturn(dto);
                return Response.builderSuccess(res);
            } else {
                WarehouseRecordE recordE = warehouseRecordRepository.queryWarehouseRecordByRecordCode(dto.getRecordCode());
                if(null ==recordE){
                    throw new RomeException(ResCode.STOCK_ERROR_1017,"调拨-部分锁定库存并返回Sku锁定数量正在初始处理中，请稍后再试。。。"+ dto.getRecordCode());
                }
                log.info("调拨-部分锁定库存并返回Sku锁定数量【{}】正在处理中，请稍后再试。。。", dto.getRecordCode());
                LockStockResp res = shopReplenishService.buildResDTO(dto);
                return Response.builderSuccess(res);
            }
        } catch (RomeException e) {
            log.error(e.getMessage(), e);
            return Response.builderFail(e.getCode(),e.getMessage());
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return Response.builderFail(ResCode.STOCK_ERROR_1003,ResCode.STOCK_ERROR_1003_DESC+e.getMessage()==null?e.toString():e.getMessage());
        }finally {
            if(isLock) {
                redisUtil.unLock(uniqueKey, "1");
            }
        }
    }

    @ApiOperation(value = "调拨合单-释放小单库存并重新全部锁定大单", nickname = "unLockLittleAndLockBigOrder", produces = MediaType.APPLICATION_JSON_VALUE)
    @ApiResponse(code = 200, message = "success")
    @RequestMapping(value = "/unLockLittleAndLockBigOrder", method = RequestMethod.POST)
    public Response unLockLittleAndLockBigOrder(@RequestBody OutWarehouseRecordDTO dto) {
        String uniqueKey = "unLockLittleAndLockBigOrder" + "_" + dto.getRecordCode();
        boolean isLock = false;
        try {
            isLock = redisUtil.lock(uniqueKey, "1", 120);
            if (isLock) {
                shopReplenishService.unLockLittleAndLockBigOrder(dto);
                return Response.builderSuccess("");
            } else {
                log.info("调拨转移-释放小单库存并重新全部锁定大单【{}】正在处理中，请稍后再试。。。", dto.getRecordCode());
                throw new RomeException(ResCode.STOCK_ERROR_1017, "调拨转移-释放小单库存并重新全部锁定大单正在初始处理中，请稍后再试。。。" + dto.getRecordCode());
            }
        } catch (RomeException e) {
            log.error(e.getMessage(), e);
            return Response.builderFail(e.getCode(), e.getMessage());
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return Response.builderFail(ResCode.STOCK_ERROR_1003, ResCode.STOCK_ERROR_1003_DESC + e.getMessage() == null ? e.toString() : e.getMessage());
        } finally {
            if (isLock) {
                redisUtil.unLock(uniqueKey, "1");
            }
        }
    }

}
