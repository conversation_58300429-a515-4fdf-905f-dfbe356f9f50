package com.rome.stock.innerservice.api.dto.allocation;

import cn.afterturn.easypoi.excel.annotation.Excel;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 预入库报表导出模板
 */
@Data
public class PredictReturnExportTemplate {

    @Excel(name = "入库单号")
    private String inRecordCode;

    @Excel(name = "售后单号")
    private String reverseOrderNo;

    @Excel(name = "入库仓库编号")
    private String inWarehouseCode;

    @Excel(name = "入库仓库名称")
    private String inWarehouseName;

    @Excel(name = "sku编号")
    private String skuCode;

    @Excel(name = "sku名称")
    private String skuName;

    @Excel(name = "数量")
    private BigDecimal inQty;

    @Excel(name = "匹配数量")
    private BigDecimal matchQty;

    @Excel(name = "差异数量")
    private BigDecimal diffQty;

    @Excel(name = "已过账渠道")
    private String postChannelCode;

    @Excel(name = "交易匹配渠道")
    private String matchChannelCode;

    @Excel(name = "是否渠道差异")
    private String channelCodeFlag;

    @Excel(name = "创建时间",exportFormat = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;
}
