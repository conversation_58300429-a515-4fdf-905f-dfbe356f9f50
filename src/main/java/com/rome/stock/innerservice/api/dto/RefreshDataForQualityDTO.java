package com.rome.stock.innerservice.api.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;

/**
 * @Doc:.....
 * @Author: lchy
 * @Date: 2019/8/22
 * @Version 1.0
 */
@Data
@EqualsAndHashCode
public class RefreshDataForQualityDTO {

    private String skuCode;
    @ApiModelProperty(value = "调整数量 正是加 负是减")
    private BigDecimal skuQty;
    private Long skuId;
    @ApiModelProperty(value = "需要操作的明细", hidden = true)
    private boolean needHandle = true;
    @ApiModelProperty(value = "虚仓ID，指定虚仓，必须所有全空，或全不为空")
    private Long virtualWarehouseId;
}
