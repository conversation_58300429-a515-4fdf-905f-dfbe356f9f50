package com.rome.stock.innerservice.api.dto;


import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;
import java.util.Date;

/**
 * @Program: stock-admin
 * @Description: 旺店通拆单导出模板
 * @Author: Cocoa
 * @Date: 2020/7/27 10:25
 * @Version: v1.0.0
 */
@Data
@EqualsAndHashCode
public class WDTStockSplitExportDetailTemplate extends WDTStockSplitExportTemplate{
    private Long id;
    private String recordCode;
    private String province;
    private String city;
    private String county;
    private String realWarehouseAddress;
    private Integer recordStatus;
    private Integer splitColor;
    private Long merchantId;
    /**
     * 商品sku编码
     */
    private Long skuId;
    /**
     * 商品sku编码
     */
    private String skuCode;
    /**
     * 商品sku编码
     */
    private String skuName;
    /**
     * 数量
     */
    private BigDecimal skuQty;
    /**
     * 单位
     */
    private String unit;
    /**
     * 单位code
     */
    private String unitCode;

    /**
     * 拆单状态： 0待分拆到do单 1 已分拆到do单
     */
    private Integer splitStatus;

    private String lineNo ;

    /**
     * 1赠品 2非赠品
     */
    private Integer giftType;

    /**
     * 所属主品的商品编码,为null则表示非组合品
     */
    private String parentSkuCode;
    private String parentSkuName;

    private String doCode;
    private BigDecimal allotQty;
    private Integer doRecordStatus;


    private String wareHouseRecordCode;
    private String wmsSyncStatus;
    private String wareHouseRecordStatus;
    private String fulfillmentSyncStatus;


    private Long realWarehouseId;
    private String realWarehouseCode;
    private String realWarehouseName;
    private Date mergeTime;
    /**
     * 拆单数
     */
    private Integer splitCount;
    private Integer tenantId;

}
