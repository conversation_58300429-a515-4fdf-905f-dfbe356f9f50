/**
 * Filename TransactionMessageDTO.java
 * Company 上海来伊份科技有限公司。
 * <AUTHOR>
 * @version 
 */
package com.rome.stock.innerservice.api.dto;


import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

/**
 * 交易路径配置
 */
@Data
public class TradePathConfigDTO extends Pagination {
	/**
	 * 唯一主键
	 */
	private Long id;

	@ApiModelProperty(value = "公司间交易类型配置：11直营门店补货 12云商2C销售 12销售系统2B销售 14跨公司领用 15加盟门店补货 21直营门店退货 22云商2C退货  23销售系统2B退货 25加盟门店退货")
	private Integer tradeType;

	@ApiModelProperty(value = "from公司编码, * 代表所有")
	private String fromCompanyCode;

	@ApiModelProperty(value = "to公司编码 * 代表所有")
	private String toCompanyCode;

	@ApiModelProperty(value = "选择类型：0必须配置交易路径  1 无需配置交易路径（例外）")
	private Integer type;
	
	@ApiModelProperty(value="创建时间")
    private Date createTime;
	
	@ApiModelProperty(value="更新时间")
    private Date  updateTime;
	
	@ApiModelProperty(value="开始时间")
    private Date startTime;
	
	@ApiModelProperty(value="结束时间")
    private Date endTime;
	
	@ApiModelProperty(value="创建人")
    private Long creator;
	
	@ApiModelProperty(value="更新人")
	private Long modifier;
	
	@ApiModelProperty(value="更新人工号")
	private String modifyEmpNum;

	private Integer isDeleted;


	@ApiModelProperty(value="开始时间")
	private Date startEffectiveDate;

	@ApiModelProperty(value="结束时间")
	private Date endEffectiveDate;

}
