package com.rome.stock.innerservice.api.controller;

import com.github.pagehelper.PageInfo;
import com.rome.arch.core.clientobject.Response;
import com.rome.arch.core.exception.RomeException;
import com.rome.arch.util.controller.RomeController;
import com.rome.stock.innerservice.api.dto.RealWarehouse;
import com.rome.stock.innerservice.api.dto.RealWarehouseWmsConfigDTO;
import com.rome.stock.innerservice.api.dto.WmsBatchCheckFlowDTO;
import com.rome.stock.innerservice.api.dto.groupbuy.QueryRealWarehouse;
import com.rome.stock.innerservice.common.RedisUtil;
import com.rome.stock.innerservice.common.ResCode;
import com.rome.stock.innerservice.constant.ParamValidator;
import com.rome.stock.innerservice.constant.ResponseMsg;
import com.rome.stock.innerservice.domain.service.RealWarehouseWmsConfigService;
import com.rome.stock.core.infrastructure.redis.StockCacheKeyEnum;
import com.rome.stock.innerservice.domain.service.WmsBatchCheckService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import io.swagger.annotations.ApiResponse;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Objects;


@Slf4j
@RomeController
@RequestMapping("/stock/v1/real_warehouse_wms_config")
@Api(tags = {"实仓WMS配置接口"})
public class RealWarehouseWmsConfigController {

    @Autowired
    private RealWarehouseWmsConfigService realWarehouseWmsConfigService;


    @Autowired
    private WmsBatchCheckService wmsBatchCheckService;

    @Autowired
    private RedisUtil redisUtil;
    private ParamValidator validator = ParamValidator.INSTANCE;

    @ApiOperation(value = "查询实仓WMS配置信息", produces = MediaType.APPLICATION_JSON_VALUE)
    @ApiResponse(code = 200, message = "success", response = RealWarehouseWmsConfigDTO.class)
    @RequestMapping(value = "/queryWmsConfig", method = RequestMethod.POST)
    public Response<PageInfo<RealWarehouseWmsConfigDTO>> queryWmsConfig(@ApiParam(name = "realWarehouseWmsConfigDTO", value = "查询实仓WMS配置信息") @RequestBody RealWarehouseWmsConfigDTO realWarehouseWmsConfigDTO) {
        try {
            PageInfo<RealWarehouseWmsConfigDTO> pageList = realWarehouseWmsConfigService.queryWmsConfig(realWarehouseWmsConfigDTO);
            return ResponseMsg.SUCCESS.buildMsg(pageList);
        } catch (RomeException e) {
            log.error(e.getMessage(), e);
            return ResponseMsg.FAIL.buildMsg(e.getCode(), e.getMessage());
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return ResponseMsg.EXCEPTION.buildMsg();
        }
    }

    @ApiOperation(value = "根据仓库编码和工厂编码批量查询仓库wmsConfig信息", produces = MediaType.APPLICATION_JSON_VALUE)
    @ApiResponse(code = 200, message = "success", response = RealWarehouseWmsConfigDTO.class)
    @PostMapping("/queryWmsConfigByFactoryCodeAndOutCode")
    public Response<List<RealWarehouseWmsConfigDTO>> queryWmsConfigByFactoryCodeAndOutCode(@RequestBody List<QueryRealWarehouse> queryRealWarehouseList){
        try{
            List<RealWarehouseWmsConfigDTO> realWarehouseWmsConfigDTOS = realWarehouseWmsConfigService.queryWmsConfigByFactoryAndOutCode(queryRealWarehouseList);
            return ResponseMsg.SUCCESS.buildMsg(realWarehouseWmsConfigDTOS);
        }catch (RomeException e){
            log.error(e.getMessage(), e);
            return ResponseMsg.FAIL.buildMsg(e.getCode(), e.getMessage());
        }catch (Exception e){
            log.error(e.getMessage(), e);
            return ResponseMsg.EXCEPTION.buildMsg();
        }
    }

    @ApiOperation(value = "新增实仓WMS配置信息", produces = MediaType.APPLICATION_JSON_VALUE)
    @ApiResponse(code = 200, message = "success", response = RealWarehouseWmsConfigDTO.class)
    @RequestMapping(value = "/addRealWarehouseWmsConfig", method = RequestMethod.POST)
    public Response addRealWarehouseWmsConfig(@ApiParam(name = "realWarehouseWmsConfigDTO", value = "新增实仓WMS配置信息") @RequestBody RealWarehouseWmsConfigDTO realWarehouseWmsConfigDTO) {
        try {
            realWarehouseWmsConfigService.addRealWarehouseWmsConfig(realWarehouseWmsConfigDTO);
            return ResponseMsg.SUCCESS.buildMsg();
        } catch (RomeException e) {
            log.error(e.getMessage(), e);
            return ResponseMsg.EXCEPTION.buildMsg(e.getCode(), e.getMessage());
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return ResponseMsg.EXCEPTION.buildMsg(ResCode.STOCK_ERROR_1003, ResCode.STOCK_ERROR_1003_DESC);
        }
    }

    @ApiOperation(value = "删除实仓WMS配置信息", produces = MediaType.APPLICATION_JSON_VALUE)
    @ApiResponse(code = 200, message = "success", response = RealWarehouseWmsConfigDTO.class)
    @RequestMapping(value = "/deleteRealWarehouseWmsConfig", method = RequestMethod.POST)
    public Response deleteRealWarehouseWmsConfig(@RequestBody RealWarehouseWmsConfigDTO realWarehouseWmsConfigDTO) {
        if (!validator.validPositiveLong(realWarehouseWmsConfigDTO.getId())) {
            return ResponseMsg.PARAM_ERROR.buildMsg();
        }
        try {
            realWarehouseWmsConfigService.deleteRealWarehouseWmsConfig(realWarehouseWmsConfigDTO);
            return ResponseMsg.SUCCESS.buildMsg();
        } catch (RomeException e) {
            log.error(e.getMessage(), e);
            return ResponseMsg.FAIL.buildMsg(e.getCode(), e.getMessage());
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return ResponseMsg.EXCEPTION.buildMsg();
        }
    }

    @ApiOperation(value = "更新real_wms_ config下发状态", produces = MediaType.APPLICATION_JSON_VALUE)
    @ApiResponse(code = 200, message = "success", response = RealWarehouseWmsConfigDTO.class)
    @PostMapping(value = "/update")
    public Response updateWmsConfigStatus(@RequestBody RealWarehouseWmsConfigDTO realWarehouseWmsConfigDTO){
        try{
            realWarehouseWmsConfigService.updateWmsConfigStatus(realWarehouseWmsConfigDTO);
            return ResponseMsg.SUCCESS.buildMsg();
        }catch (RomeException e){
            log.error(e.getMessage(), e);
            return ResponseMsg.FAIL.buildMsg(e.getCode(), e.getMessage());
        }catch (Exception e){
            log.error(e.getMessage(), e);
            return ResponseMsg.EXCEPTION.buildMsg();
        }
    }


    @ApiOperation(value = "更新批次开启状态", produces = MediaType.APPLICATION_JSON_VALUE)
    @ApiResponse(code = 200, message = "success", response = RealWarehouseWmsConfigDTO.class)
    @PostMapping(value = "/updateBatchStatus")
    public Response updateBatchStatus(@RequestBody RealWarehouseWmsConfigDTO realWarehouseWmsConfigDTO){
        try{
            Long realWarehouseId=realWarehouseWmsConfigService.updateBatchStatus(realWarehouseWmsConfigDTO);
            if(Objects.equals(-1L,realWarehouseId)){
                return ResponseMsg.EXCEPTION.buildMsg("开启批次库存成功，批次库存信息同步中...");
            }
            return ResponseMsg.SUCCESS.buildMsg();
        }catch (RomeException e){
            log.error(e.getMessage(), e);
            return ResponseMsg.FAIL.buildMsg(e.getCode(), e.getMessage());
        }catch (Exception e){
            log.error(e.getMessage(), e);
            return ResponseMsg.EXCEPTION.buildMsg(e.getMessage());
        }
    }


    @ApiOperation(value = "查询所有非门店仓", produces = MediaType.APPLICATION_JSON_VALUE)
    @ApiResponse(code = 200, message = "success", response = RealWarehouseWmsConfigDTO.class)
    @RequestMapping(value = "/queryNotShopWarehouse", method = RequestMethod.GET)
    public Response<List<RealWarehouse>> queryNotShopWarehouse() {
        try {
            List<RealWarehouse> list = realWarehouseWmsConfigService.queryNotShopWarehouse();
            return ResponseMsg.SUCCESS.buildMsg(list);
        } catch (RomeException e) {
            log.error(e.getMessage(), e);
            return ResponseMsg.FAIL.buildMsg(e.getCode(), e.getMessage());
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return ResponseMsg.EXCEPTION.buildMsg();
        }
    }

    @ApiOperation(value = "查询所有非门店仓和仓店一体", produces = MediaType.APPLICATION_JSON_VALUE)
    @ApiResponse(code = 200, message = "success", response = RealWarehouseWmsConfigDTO.class)
    @RequestMapping(value = "/queryNotShopWarehouseAndIdenti", method = RequestMethod.GET)
    public Response<List<RealWarehouse>> queryNotShopWarehouseAndIdenti() {
        try {
            List<RealWarehouse> list = realWarehouseWmsConfigService.queryNotShopWarehouseAndIdenti();
            return ResponseMsg.SUCCESS.buildMsg(list);
        } catch (RomeException e) {
            log.error(e.getMessage(), e);
            return ResponseMsg.FAIL.buildMsg(e.getCode(), e.getMessage());
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return ResponseMsg.EXCEPTION.buildMsg();
        }
    }




    @ApiOperation(value = "根据仓库code查询配置信息", produces = MediaType.APPLICATION_JSON_VALUE)
    @ApiResponse(code = 200, message = "success", response = RealWarehouseWmsConfigDTO.class)
    @RequestMapping(value = "/getRealWarehouseWmsConfigByVMCode", method = RequestMethod.POST)
    public Response<List<RealWarehouseWmsConfigDTO>> getRealWarehouseWmsConfigByVMCode(@ApiParam(name = "code", value = "仓库编码") @RequestParam("realWareCode") String realWareCode) {
        if (!validator.validStr(realWareCode)) {
            return ResponseMsg.PARAM_ERROR.buildMsg();
        }
        try {
            List<RealWarehouseWmsConfigDTO> realWarehouseWmsConfigDTO = realWarehouseWmsConfigService.getRealWarehouseWmsConfigByVMCode(realWareCode);
            return ResponseMsg.SUCCESS.buildMsg(realWarehouseWmsConfigDTO);
        } catch (RomeException e) {
            log.error(e.getMessage(), e);
            return ResponseMsg.FAIL.buildMsg(e.getCode(), e.getMessage());
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return ResponseMsg.EXCEPTION.buildMsg();
        }
    }

    @ApiOperation(value = "删除实仓WMS对应的key",produces = MediaType.APPLICATION_JSON_VALUE)
    @ApiResponse(code = 200, message = "success", response = String.class)
    @RequestMapping(value = "/deleteRedisKey",method = {RequestMethod.POST, RequestMethod.GET})
    public Response deleteRedisKey(Long realWarehouseId){
        try {
            StockCacheKeyEnum cacheKey = StockCacheKeyEnum.REAL_WAREHOUSE_WMS_CONFIG;
            redisUtil.del(cacheKey.getKey()+realWarehouseId);
            if (redisUtil.get(cacheKey.getKey()+realWarehouseId) != null){
                return Response.builderFail("5001","删除实仓wms配置缓存失败");
            }
            return Response.builderSuccess("删除实仓WMS对应的key成功");
        } catch (RomeException e) {
            log.error(e.getMessage(), e);
            return ResponseMsg.FAIL.buildMsg(e.getCode(), e.getMessage());
        }catch (Exception e) {
            log.error(e.getMessage(), e);
            return ResponseMsg.EXCEPTION.buildMsg();
        }
    }
    
    @ApiOperation(value = "根据实仓Id查询仓库wmsConfig信息", produces = MediaType.APPLICATION_JSON_VALUE)
    @ApiResponse(code = 200, message = "success", response = RealWarehouseWmsConfigDTO.class)
    @PostMapping("/findRealWarehouseWmsConfigById")
    public Response<RealWarehouseWmsConfigDTO> findRealWarehouseWmsConfigById(@ApiParam(name = "realWarehouseId") @RequestBody Long realWarehouseId){
        try{
            RealWarehouseWmsConfigDTO realWarehouseWmsConfigDTO = realWarehouseWmsConfigService.findRealWarehouseWmsConfigById(realWarehouseId);
            return ResponseMsg.SUCCESS.buildMsg(realWarehouseWmsConfigDTO);
        }catch (RomeException e){
            log.error(e.getMessage(), e);
            return ResponseMsg.FAIL.buildMsg(e.getCode(), e.getMessage());
        }catch (Exception e){
            log.error(e.getMessage(), e);
            return ResponseMsg.EXCEPTION.buildMsg();
        }
    }
    
    @ApiOperation(value = "根据实仓外部和工厂编码查询仓库wmsConfig信息", produces = MediaType.APPLICATION_JSON_VALUE)
    @ApiResponse(code = 200, message = "success", response = RealWarehouseWmsConfigDTO.class)
    @RequestMapping(value = "/findWmsConfigByOutCodeAndFactoryCode", method = RequestMethod.POST)
    public Response<RealWarehouseWmsConfigDTO> findWmsConfigByOutCodeAndFactoryCode(@ApiParam(name = "realWarehouseWmsConfigDTO", value = "查询实仓WMS配置信息") @RequestBody RealWarehouseWmsConfigDTO realWarehouseWmsConfigDTO) {
        try {
            RealWarehouseWmsConfigDTO data = realWarehouseWmsConfigService.findWmsConfigByOutCodeAndFactoryCode(realWarehouseWmsConfigDTO);
            return ResponseMsg.SUCCESS.buildMsg(data);
        } catch (RomeException e) {
            log.error(e.getMessage(), e);
            return ResponseMsg.FAIL.buildMsg(e.getCode(), e.getMessage());
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return ResponseMsg.EXCEPTION.buildMsg();
        }
    }
}
