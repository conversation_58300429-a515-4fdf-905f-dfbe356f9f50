package com.rome.stock.innerservice.api.dto.cmp7;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @description cmp7返回结果集对象
 * @date 2020/11/12 15:41
 * @throw
 */
@Data
public class Cmp7ReturnDTO {

    @ApiModelProperty(value = "请求时间戳")
    private Long timestamp;

    @ApiModelProperty(value = "客户端签名")
    private String sign;


    @ApiModelProperty(value = "返回消息")
    private String message;

    @ApiModelProperty(value = "返回代码")
    private Integer code;
}    
   