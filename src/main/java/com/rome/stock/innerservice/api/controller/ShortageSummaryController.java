package com.rome.stock.innerservice.api.controller;

import com.github.pagehelper.PageInfo;
import com.rome.arch.core.clientobject.Response;
import com.rome.arch.core.exception.RomeException;
import com.rome.arch.util.controller.RomeController;
import com.rome.stock.innerservice.api.dto.ShortageSummaryDTO;
import com.rome.stock.innerservice.api.dto.ShortageSummaryRecordDTO;
import com.rome.stock.innerservice.common.ResCode;
import com.rome.stock.innerservice.constant.ResponseMsg;
import com.rome.stock.innerservice.domain.service.ShortageSummaryService;
import com.rome.stock.innerservice.domain.service.VwStockMoveJobService;
import com.rome.stock.innerservice.domain.service.VwStockMoveService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiResponse;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;

import javax.annotation.Resource;
import java.util.List;

/**
 * @Doc:缺货汇总
 * @Author: lchy
 * @Date: 2020/6/1
 * @Version 1.0
 */
@Slf4j
@RomeController
@RequestMapping("/stock/v1/shortageSummary")
@Api(tags={"缺货汇总"})
public class ShortageSummaryController {

    @Resource
    private ShortageSummaryService shortageSummaryService;
    @Resource
    private VwStockMoveJobService vwStockMoveJobService;

    @ApiOperation(value = "根据查询条件查询所有商品信息", nickname = "queryForShortageSummary", produces = MediaType.APPLICATION_JSON_VALUE)
    @ApiResponse(code = 200, message = "success", response = List.class)
    @RequestMapping(value = "/query", method = RequestMethod.POST)
    public Response<PageInfo<ShortageSummaryDTO>> query(@RequestBody ShortageSummaryDTO stockRecord) {
        try {
            PageInfo<ShortageSummaryDTO> pageList = shortageSummaryService.getSkusInfoByQueryCondition(stockRecord);
            return Response.builderSuccess(pageList);
        } catch (RomeException e) {
            log.error(e.getMessage(), e);
            return ResponseMsg.EXCEPTION.buildMsg(e.getCode(), e.getMessage());
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return ResponseMsg.EXCEPTION.buildMsg(ResCode.STOCK_ERROR_1003, ResCode.STOCK_ERROR_1003_DESC);
        }
    }

    @ApiOperation(value = "查询占用库存的单据信息", nickname = "queryHasLockQtyRecord", produces = MediaType.APPLICATION_JSON_VALUE)
    @ApiResponse(code = 200, message = "success", response = List.class)
    @RequestMapping(value = "/queryHasLockQtyRecord", method = RequestMethod.POST)
    public Response<PageInfo<ShortageSummaryRecordDTO>> queryHasLockQtyRecord(@RequestBody ShortageSummaryDTO stockRecord) {
        try {
        	PageInfo<ShortageSummaryRecordDTO> pageList = shortageSummaryService.queryHasLockQtyRecord(stockRecord);
            return Response.builderSuccess(pageList);
        } catch (RomeException e) {
            log.error(e.getMessage(), e);
            return ResponseMsg.EXCEPTION.buildMsg(e.getCode(), e.getMessage());
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return ResponseMsg.EXCEPTION.buildMsg(ResCode.STOCK_ERROR_1003, ResCode.STOCK_ERROR_1003_DESC);
        }
    }
    
    @ApiOperation(value = "查询占用库存（实仓层）的单据包括锁定、在途、质检查询", nickname = "queryHasDiffQtyRecord", produces = MediaType.APPLICATION_JSON_VALUE)
    @ApiResponse(code = 200, message = "success", response = List.class)
    @RequestMapping(value = "/queryHasDiffQtyRecord", method = RequestMethod.POST)
    public Response<PageInfo<ShortageSummaryRecordDTO>> queryHasDiffQtyRecord(@RequestBody ShortageSummaryDTO stockRecord) {
        try {
        	PageInfo<ShortageSummaryRecordDTO> pageList = shortageSummaryService.queryHasDiffQtyRecord(stockRecord);
            return Response.builderSuccess(pageList);
        } catch (RomeException e) {
            log.error(e.getMessage(), e);
            return ResponseMsg.EXCEPTION.buildMsg(e.getCode(), e.getMessage());
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return ResponseMsg.EXCEPTION.buildMsg(ResCode.STOCK_ERROR_1003, ResCode.STOCK_ERROR_1003_DESC);
        }
    }

    @ApiOperation(value = "虚仓自动转移", nickname = "handleVwStockMove", produces = MediaType.APPLICATION_JSON_VALUE)
    @ApiResponse(code = 200, message = "success", response = List.class)
    @RequestMapping(value = "/handleVwStockMove", method = RequestMethod.POST)
    public Response<String> handleVwStockMove(@RequestBody List<ShortageSummaryDTO> stockRecord) {
        try {
            String result = shortageSummaryService.handleVwStockMove(stockRecord);
            return Response.builderSuccess(result);
        } catch (RomeException e) {
            log.error(e.getMessage(), e);
            return ResponseMsg.EXCEPTION.buildMsg(e.getCode(), e.getMessage());
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return ResponseMsg.EXCEPTION.buildMsg(ResCode.STOCK_ERROR_1003, ResCode.STOCK_ERROR_1003_DESC);
        }
    }

    @ApiOperation(value = "虚仓自动转移job", nickname = "handleVwStockMoveJob", produces = MediaType.APPLICATION_JSON_VALUE)
    @ApiResponse(code = 200, message = "success", response = List.class)
    @RequestMapping(value = "/handleVwStockMoveJob", method = RequestMethod.POST)
    public Response<String> vwStockMoveService() {
        try {
            vwStockMoveJobService.handleVwStockMove();
            return Response.builderSuccess("success");
        } catch (RomeException e) {
            log.error(e.getMessage(), e);
            return ResponseMsg.EXCEPTION.buildMsg(e.getCode(), e.getMessage());
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return ResponseMsg.EXCEPTION.buildMsg(ResCode.STOCK_ERROR_1003, ResCode.STOCK_ERROR_1003_DESC);
        }
    }
}
