/**
 * Filename WarehouseRouteDTO.java
 * Company 上海来伊份科技有限公司。
 * <AUTHOR>
 * @version 
 */
package com.rome.stock.innerservice.api.dto;

import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.util.Set;

/**
 * 仓库路由信息 dto
 * <AUTHOR>
 * @since 2019年5月22日 下午8:08:58
 */
@Data
@EqualsAndHashCode
public class RouteWarehouseDTO implements Serializable {

	/**
	 * 
	 */
	private static final long serialVersionUID = 8682370429988176238L;

	/**
	 * 实仓id
	 */
	private Long realWarehouseId; 
	
	/**
	 * 仓库,优先级
	 */
    private Integer priority;
    
    /**
     * 市级编码code
     * 现在寻源是到市
     */
    private String cityCode;
    
    /**
     * 是否，覆盖全国
     */
    private Boolean coverAll;
    
    /**
     * 是否，覆盖市
     */
    private Boolean coverCity;
    
    /**
     * 集中销售列表
     */
    private Set<Long> focusSaleSkuIds;
    
    /**
     * 是否，实仓停发，默认为false
     */
    private boolean rwStop = false;
    
    /**
     * 实仓停发人
     */
    private Long rwStopper;
}
