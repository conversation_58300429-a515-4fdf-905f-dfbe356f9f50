package com.rome.stock.innerservice.api.controller;

import java.util.List;

import javax.annotation.Resource;

import com.rome.stock.innerservice.constant.ShopTypeVO;
import com.rome.stock.innerservice.remote.base.dto.SaleOrgDTO;
import com.rome.stock.innerservice.remote.base.dto.StoreDTO;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.*;

import com.github.pagehelper.PageInfo;
import com.rome.arch.core.clientobject.Response;
import com.rome.arch.core.exception.RomeException;
import com.rome.arch.util.controller.RomeController;
import com.rome.stock.innerservice.api.dto.RealWarehouse;
import com.rome.stock.innerservice.api.dto.frontrecord.ShopConsumeAdjustRecordDTO;
import com.rome.stock.innerservice.common.ResCode;
import com.rome.stock.innerservice.constant.ResponseMsg;
import com.rome.stock.innerservice.domain.service.ShopConsumeService;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiResponse;
import lombok.extern.slf4j.Slf4j;

/**
 * @Description 门店报废调整单
 * <AUTHOR>
 * @Date 2019/5/10 19:46
 * @Version 1.0
 */
@Slf4j
@RomeController
@RequestMapping("/stock/v1/shop_consume")
@Api(tags={"门店报废调整"})
public class ShopConsumeController {

    @Resource
    ShopConsumeService shopConsumeService;

    /**
     * 创建门店报废调整单
     * @param shopConsumeAdjustRecordDTO
     * @return
     */
    @ApiOperation(value = "创建门店报废调整单", nickname = "createShopConsumeAdjustRecord", produces = MediaType.APPLICATION_JSON_VALUE)
    @ApiResponse(code = 200, message = "success")
    @RequestMapping(value = "/createShopConsumeAdjustRecord", method = RequestMethod.POST)
    public Response createShopConsumeAdjustRecord(@RequestBody ShopConsumeAdjustRecordDTO shopConsumeAdjustRecordDTO){
        try{
            shopConsumeService.createShopConsumeAdjustRecord(shopConsumeAdjustRecordDTO);
            return ResponseMsg.SUCCESS.buildMsg();
        } catch (RomeException e) {
            log.error(e.getMessage(), e);
            return ResponseMsg.EXCEPTION.buildMsg(e.getCode(), e.getMessage());
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return ResponseMsg.EXCEPTION.buildMsg(ResCode.STOCK_ERROR_1003_DESC,ResCode.STOCK_ERROR_1003_DESC);
        }
    }

    /**
     *  根据条件调整门店报废调整单
     * @param shopConsumeAdjustRecordDTO
     * @return
     */
    @ApiOperation(value = "根据条件查询门店报废调整单", nickname = "selectShopConsumeAdjustRecordByCondition", produces = MediaType.APPLICATION_JSON_VALUE)
    @ApiResponse(code = 200, message = "success",response = ShopConsumeAdjustRecordDTO.class)
    @RequestMapping(value = "/selectShopConsumeAdjustRecordByCondition", method = RequestMethod.POST)
    public Response selectShopConsumeAdjustRecordByCondition(@RequestBody ShopConsumeAdjustRecordDTO shopConsumeAdjustRecordDTO){
        try{
            PageInfo<ShopConsumeAdjustRecordDTO> pageList= shopConsumeService.selectShopConsumeAdjustRecordByCondition(shopConsumeAdjustRecordDTO);
            return ResponseMsg.SUCCESS.buildMsg(pageList);
        } catch (RomeException e) {
            log.error(e.getMessage(), e);
            return ResponseMsg.EXCEPTION.buildMsg(e.getCode(), e.getMessage());
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return ResponseMsg.EXCEPTION.buildMsg(ResCode.STOCK_ERROR_1003_DESC,ResCode.STOCK_ERROR_1003_DESC);
        }
    }

    @ApiOperation(value = "根据调整单id查询明细", nickname = "selectShopConsumeRecordDetail", produces = MediaType.APPLICATION_JSON_VALUE)
    @ApiResponse(code = 200, message = "success")
    @RequestMapping(value = "/selectShopConsumeRecordDetail", method = RequestMethod.GET)
    public Response selectShopConsumeRecordDetail(@RequestParam("id") String id){
        try {
            ShopConsumeAdjustRecordDTO detailList = shopConsumeService.selectShopConsumeRecordDetail(Long.parseLong(id));
            return ResponseMsg.SUCCESS.buildMsg(detailList);
        } catch (RomeException e) {
            log.error(e.getMessage(), e);
            return ResponseMsg.EXCEPTION.buildMsg(e.getCode(), e.getMessage());
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return ResponseMsg.EXCEPTION.buildMsg(ResCode.STOCK_ERROR_1003_DESC,ResCode.STOCK_ERROR_1003_DESC);
        }
    }


    @ApiOperation(value = "修改门店报废调整单(信息修改)", nickname = "modifyShopConsumeRecord", produces = MediaType.APPLICATION_JSON_VALUE)
    @ApiResponse(code = 200, message = "success")
    @RequestMapping(value = "/modifyShopConsumeRecord", method = RequestMethod.POST)
    public Response modifyShopConsumeRecord(@RequestBody ShopConsumeAdjustRecordDTO shopConsumeAdjustRecordDTO){
        try {
            shopConsumeService.modifyShopConsumeRecord(shopConsumeAdjustRecordDTO);
            return ResponseMsg.SUCCESS.buildMsg();
        } catch (RomeException e) {
            log.error(e.getMessage(), e);
            return ResponseMsg.FAIL.buildMsg(e.getCode(),e.getMessage());
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return ResponseMsg.FAIL.buildMsg("500000",e.getMessage());
        }
    }

    @ApiOperation(value = "门店报废调整单确认", nickname = "confirmShopConsumeRecord", produces = MediaType.APPLICATION_JSON_VALUE)
    @ApiResponse(code = 200, message = "success")
    @RequestMapping(value = "/confirmShopConsumeRecord", method = RequestMethod.POST)
    public Response confirmShopConsumeRecord(@RequestBody List<Long> ids,@RequestParam("modifier") Long modifier){
        try {
            shopConsumeService.confirmShopConsumeRecord(ids,modifier);
            return ResponseMsg.SUCCESS.buildMsg();
        } catch (RomeException e) {
            log.error(e.getMessage(), e);
            return ResponseMsg.FAIL.buildMsg(e.getCode(),e.getMessage());
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return ResponseMsg.FAIL.buildMsg("500000",e.getMessage());
        }
    }

    @ApiOperation(value = "门店报废调整单取消", nickname = "cancelShopConsumeRecord", produces = MediaType.APPLICATION_JSON_VALUE)
    @ApiResponse(code = 200, message = "success")
    @RequestMapping(value = "/cancelShopConsumeRecord", method = RequestMethod.GET)
    public Response cancelShopConsumeRecord(@RequestParam("id") String id,@RequestParam("modifier")Long modifier){
        try {
            shopConsumeService.cancelShopConsumeRecord(Long.parseLong(id),modifier);
            return ResponseMsg.SUCCESS.buildMsg();
        } catch (RomeException e) {
            log.error(e.getMessage(), e);
            return ResponseMsg.FAIL.buildMsg(e.getCode(),e.getMessage());
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return ResponseMsg.FAIL.buildMsg("500000",e.getMessage());
        }
    }

    @ApiOperation(value = "根据id批量查询单子", nickname = "selectShopConsumeRecordByIds", produces = MediaType.APPLICATION_JSON_VALUE)
    @ApiResponse(code = 200, message = "success")
    @RequestMapping(value = "/selectShopConsumeRecordByIds", method = RequestMethod.POST)
    public Response selectShopConsumeRecordByIds(@RequestBody List<Long> ids){
        try {
            List<ShopConsumeAdjustRecordDTO> adjustDTOS = shopConsumeService.selectShopConsumeRecordByIds(ids);
            return ResponseMsg.SUCCESS.buildMsg(adjustDTOS);
        } catch (RomeException e) {
            log.error(e.getMessage(), e);
            return ResponseMsg.FAIL.buildMsg(e.getCode(),e.getMessage());
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return ResponseMsg.FAIL.buildMsg("500000",e.getMessage());
        }
    }


    @ApiOperation(value = "查询所有门店仓库", nickname = "queryShopList", produces = MediaType.APPLICATION_JSON_VALUE)
    @ApiResponse(code = 200, message = "success")
    @RequestMapping(value = "/queryShopList", method = RequestMethod.GET)
    public Response queryShopList(){
        try {
            List<StoreDTO> warehouses = shopConsumeService.queryShopList();
            return ResponseMsg.SUCCESS.buildMsg(warehouses);
        } catch (RomeException e) {
            log.error(e.getMessage(), e);
            return ResponseMsg.FAIL.buildMsg(e.getCode(),e.getMessage());
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return ResponseMsg.FAIL.buildMsg("500000",e.getMessage());
        }
    }

    @ApiOperation(value = "根据组织code查询组织信息", nickname = "getOrgByOrgCode", produces = MediaType.APPLICATION_JSON_VALUE)
    @ApiResponse(code = 200, message = "success")
    @RequestMapping(value = "/getOrgByOrgCode/{orgCode}", method = RequestMethod.GET)
    public Response getOrgByOrgCode(@PathVariable("orgCode") String orgCode){
        try {
            SaleOrgDTO orgByOrgCode = shopConsumeService.getOrgByOrgCode(orgCode);
            return ResponseMsg.SUCCESS.buildMsg(orgByOrgCode);
        } catch (RomeException e) {
            log.error(e.getMessage(), e);
            return ResponseMsg.FAIL.buildMsg(e.getCode(),e.getMessage());
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return ResponseMsg.FAIL.buildMsg("500000",e.getMessage());
        }
    }

    @ApiOperation(value = "根据工厂名称查询模糊查询门店", nickname = "queryShopFactory", produces = MediaType.APPLICATION_JSON_VALUE)
    @ApiResponse(code = 200, message = "success")
    @RequestMapping(value = "/queryShopFactory", method = RequestMethod.POST)
    public Response<PageInfo<StoreDTO>> queryShopFactory(@RequestBody StoreDTO query){
        try {
            query.setStoreProperties(ShopTypeVO.DIRECT_SALE.getType().toString());
            PageInfo<StoreDTO> storeDTOS = shopConsumeService.queryShopFactory(query);
            return ResponseMsg.SUCCESS.buildMsg(storeDTOS);
        } catch (RomeException e) {
            log.error(e.getMessage(), e);
            return ResponseMsg.FAIL.buildMsg(e.getCode(),e.getMessage());
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return ResponseMsg.FAIL.buildMsg("500000",e.getMessage());
        }
    }
}
