/**
 * Filename SupplierSkuStockDTO.java
 * Company 上海来伊份科技有限公司。
 * <AUTHOR>
 * @version 
 */
package com.rome.stock.innerservice.api.dto.supplier;

import java.math.BigDecimal;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 供应商库存查询
 * <AUTHOR>
 * @since 2020-8-9 11:09:33
 */
@Data
@EqualsAndHashCode
public class SupplierSkuStockDTO {

    @ApiModelProperty(value = "skuCode")
    private String skuCode;

    @ApiModelProperty(value = "可用库存")
    private BigDecimal availableQty;

    @ApiModelProperty(value = "真实库存")
    private BigDecimal realQty;

    @ApiModelProperty(value = "锁定库存")
    private BigDecimal lockQty;

    @ApiModelProperty(value = "基础单位编码")
    private String unitCode;
    
    @ApiModelProperty(value = "单位")
    private String unit;
    
    @ApiModelProperty(value = "商品名称")
    private String name;
    
    @ApiModelProperty(value = "商品名称", hidden = true)
    private String skuName;
    
    @ApiModelProperty(value = "sku主键id", hidden = true)
    private Long skuId;
}
