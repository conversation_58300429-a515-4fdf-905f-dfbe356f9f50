package com.rome.stock.innerservice.api.dto.frontrecord;

import lombok.Data;

/**
 * 仓库调整明细
 */
@Data
public class AdjustConsumeDetailDTO {

    /**
     * 唯一主键
     */
    private Long id;

    /**
     * 前置单id
     */
    private Long frontRecordId;

    /**
     * 调整单单号
     */
    private String recordCode;

    /**
     * 商品sku编号
     */
    private Long skuId;

    /**
     * 调整数量
     */
    private Long adjQty;

    /**
     * 调整单位
     */
    private String unit;

    /**
     *单位编码
     */
    private String unitCode;

    /**
     * 规格
     */
    private String skuStandard;

    /**
     * 调整总数
     */
    private Long skuQty;

    /**
     * 损益数
     */
    private Long qty;

}
