package com.rome.stock.innerservice.api.controller;

import java.util.Arrays;
import java.util.List;
import java.util.Map;

import javax.annotation.Resource;
import javax.validation.Valid;

import com.rome.stock.innerservice.domain.convertor.frontrecord.OrderAddressConvertor;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.util.CollectionUtils;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;

import com.alibaba.fastjson.JSON;
import com.rome.arch.core.clientobject.Response;
import com.rome.arch.core.exception.RomeException;
import com.rome.arch.util.controller.RomeController;
import com.rome.stock.common.constants.CommonConsts;
import com.rome.stock.common.constants.KibanaLogConstants;
import com.rome.stock.innerservice.api.dto.OnLineStockLogisticInfoParamDTO;
import com.rome.stock.innerservice.api.dto.OnLineStockParamDTO;
import com.rome.stock.innerservice.api.dto.PredictReturnParamDTO;
import com.rome.stock.innerservice.api.dto.RealWarehouse;
import com.rome.stock.innerservice.api.dto.RwRecordPoolDTO;
import com.rome.stock.innerservice.api.dto.RwRecordPoolResultDTO;
import com.rome.stock.innerservice.api.dto.WarehouseRecordPageDTO;
import com.rome.stock.innerservice.api.dto.frontrecord.CrossStockDTO;
import com.rome.stock.innerservice.api.dto.frontrecord.PredictReturnDTO;
import com.rome.stock.innerservice.api.dto.frontrecord.StockOrderDTO;
import com.rome.stock.innerservice.api.dto.frontrecord.StockOrderRecordDTO;
import com.rome.stock.innerservice.api.dto.frontrecord.UpdateBillCodeDTO;
import com.rome.stock.innerservice.api.dto.frontrecord.UpdateDoOrderDTO;
import com.rome.stock.innerservice.api.dto.frontrecord.UpdatePackageCodeDTO;
import com.rome.stock.innerservice.api.dto.frontrecord.VirtualSkuStockOrderDTO;
import com.rome.stock.innerservice.api.dto.warehouserecord.RecordPackageDTO;
import com.rome.stock.innerservice.common.CoreKibanaLog;
import com.rome.stock.innerservice.common.ResCode;
import com.rome.stock.innerservice.constant.ParamValidator;
import com.rome.stock.innerservice.constant.ResponseMsg;
import com.rome.stock.innerservice.domain.convertor.record.RecordPackageConvertor;
import com.rome.stock.innerservice.domain.entity.record.RecordPackageE;
import com.rome.stock.innerservice.domain.repository.SapInterfaceLogRepository;
import com.rome.stock.innerservice.domain.repository.warehouserecord.RecordPackageRepository;
import com.rome.stock.innerservice.domain.service.FulfillmentJobService;
import com.rome.stock.innerservice.domain.service.StockService;
import com.rome.stock.innerservice.remote.trade.dto.DoCodeMergeDTO;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import io.swagger.annotations.ApiResponse;
import lombok.extern.slf4j.Slf4j;

/**
 * 电商相关服务(外接交易中心)
 */
@Slf4j
@RomeController
@RequestMapping("/stock/v1")
@Api(tags={"电商服务接口"})
public class StockController {

    @Autowired
    private StockService stockService;

    @Resource
    private SapInterfaceLogRepository sapInterfaceLogRepository;
    @Autowired
    private FulfillmentJobService fulfillmentJobService;


    private ParamValidator validator = ParamValidator.INSTANCE;

    private final static String LOGTYPE = "appCall";

    @Resource
    private RecordPackageRepository recordPackageRepository;

    @Resource
    private RecordPackageConvertor recordPackageConvertor;
    @Resource
    private OrderAddressConvertor orderAddressConvertor;

    /**
     * 根据SO单号查询发货单信息
     * @param orderCode SO单号
     * 根据so单号查前置单
     * @return
     */
    @ApiOperation(value = "根据SO单号查询发货单信息", nickname = "queryDoInfo", produces = MediaType.APPLICATION_JSON_VALUE)
    @ApiResponse(code = 200, message = "success", response = String.class)
    @GetMapping("/queryDoInfo/{orderCode}")
    public Response<List<RwRecordPoolDTO>> queryDoInfo(@ApiParam(name = "orderCode", value = "SO单号") @PathVariable("orderCode") String orderCode) {
        if (! validator.validStr(orderCode)) {
            return ResponseMsg.PARAM_ERROR.buildMsg();
        }
        try {
            List<RwRecordPoolDTO> rwRecordPoolDoList = stockService.queryDoInfo(orderCode);
            return ResponseMsg.SUCCESS.buildMsg(rwRecordPoolDoList);
        } catch (RomeException e) {
            log.error(e.getMessage(), e);
            return ResponseMsg.FAIL.buildMsg(e.getCode(), e.getMessage());
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return ResponseMsg.EXCEPTION.buildMsg();
        }
    }

    /**
     * 电商下单锁定库存
     * @param stockOrderDTO 电商下单相关参数
     * @return
     */
    @ApiOperation(value = "电商下单锁定库存", nickname = "lockStockByRecord", produces = MediaType.APPLICATION_JSON_VALUE)
    @ApiResponse(code = 200, message = "success", response = String.class)
    @PostMapping("/lockStockByRecord")
    public Response<Boolean> lockStockByRecord(@ApiParam(name = "stockOrderDTO", value = "电商下单锁定库存") @RequestBody @Validated StockOrderDTO stockOrderDTO) {
        String message = "";
        boolean isSucc = false;
        String json = JSON.toJSONString(stockOrderDTO);
        if (! validator.validParam(stockOrderDTO)) {
            return ResponseMsg.PARAM_ERROR.buildMsg();
        }
        if (! validator.validPositiveInt(stockOrderDTO.getTransType())) {
            return ResponseMsg.PARAM_ERROR.buildMsg();
        }
        log.warn(CoreKibanaLog.getJobLog(LOGTYPE, "lockStockByRecord", "下单锁定库存, 订单号:" + stockOrderDTO.getOrderCode(), stockOrderDTO.getOrderCode()));
        try {
            stockService.lockStockByRecord(stockOrderDTO);
            isSucc = true;
            message = "200";
            return ResponseMsg.SUCCESS.buildMsg(true);
        } catch (RomeException e) {
            message = e.getMessage();
            log.error(e.getMessage(), e);
            return ResponseMsg.FAIL.buildMsg(e.getCode(), e.getMessage());
        } catch (Exception e) {
            message = e.getMessage();
            log.error(e.getMessage(), e);
            return ResponseMsg.EXCEPTION.buildMsg();
        } finally {
            sapInterfaceLogRepository.saveCallBackInterFaceLog(2, stockOrderDTO.getOrderCode(), "lockStockByRecord",
                    json, message, isSucc);
        }
    }

    /**
     * 跨境电商下单锁定库存
     * @param crossStockDTO 跨境电商下单相关参数
     * @return
     */
    @ApiOperation(value = "跨境电商下单锁定库存", nickname = "lockStockByCross", produces = MediaType.APPLICATION_JSON_VALUE)
    @ApiResponse(code = 200, message = "success", response = String.class)
    @PostMapping("/lockStockByCross")
    public Response<Boolean> lockStockByCross(@ApiParam(name = "stockOrderDTO", value = "跨境电商下单锁定库存")
                                                  @RequestBody @Validated CrossStockDTO crossStockDTO) {
        String message = "";
        boolean isSucc = false;
        String json = JSON.toJSONString(crossStockDTO);
        if (! validator.validParam(crossStockDTO)) {
            return ResponseMsg.PARAM_ERROR.buildMsg();
        }
        log.warn(CoreKibanaLog.getJobLog(LOGTYPE, "lockStockByCross", "跨境电商下单锁定库存, 订单号:" + crossStockDTO.getOrderCode(), crossStockDTO.getOrderCode()));
        try {
            stockService.lockStockByCross(crossStockDTO);
            isSucc = true;
            message = "200";
            return ResponseMsg.SUCCESS.buildMsg(true);
        } catch (RomeException e) {
            message = e.getMessage();
            log.error(e.getMessage(), e);
            return ResponseMsg.FAIL.buildMsg(e.getCode(), e.getMessage());
        } catch (Exception e) {
            message = e.getMessage();
            log.error(e.getMessage(), e);
            return ResponseMsg.FAIL.buildMsg("-500", e.getMessage());
        } finally {
            sapInterfaceLogRepository.saveCallBackInterFaceLog(2, crossStockDTO.getOrderCode(), "lockStockByCross",
                    json, message, isSucc);
        }
    }



    /**
     * 根据子do单批量查询父子关系
     * @param doCodes 电商下单相关参数
     * @return
     */
    @ApiOperation(value = "根据子do单批量查询父子关系", nickname = "queryMergeInfoByChildDoCode", produces = MediaType.APPLICATION_JSON_VALUE)
    @ApiResponse(code = 200, message = "success", response = String.class)
    @PostMapping("/queryMergeInfoByChildDoCode")
    public Response<List<DoCodeMergeDTO>> queryMergeInfoByChildDoCode(@ApiParam(name = "doCodes", value = "doCodes") @RequestBody List<String> doCodes) {
        if ( doCodes==null || doCodes.size() == 0 || doCodes.size()> 100) {
            return ResponseMsg.PARAM_ERROR.buildMsg();
        }
        try {
            List<DoCodeMergeDTO> res = stockService.queryMergeInfoByChildDoCode(doCodes);
            return ResponseMsg.SUCCESS.buildMsg(res);
        } catch (RomeException e) {
            log.error(e.getMessage(), e);
            return ResponseMsg.FAIL.buildMsg(e.getCode(), e.getMessage());
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return ResponseMsg.EXCEPTION.buildMsg();
        }
    }

    /**
     * 根据父doCode查询子do列表
     * @param parentCode 电商下单相关参数
     * @return
     */
    @ApiOperation(value = "根据父doCode查询子do列表", nickname = "queryChildDoCodeByParentDo", produces = MediaType.APPLICATION_JSON_VALUE)
    @ApiResponse(code = 200, message = "success", response = String.class)
    @PostMapping("/queryChildDoCodeByParentDo")
    public Response<List<String>> queryChildDoCodeByParentDo(@ApiParam(name = "parentCode", value = "parentCode") @RequestParam String parentCode) {
        if (parentCode == null) {
            return ResponseMsg.PARAM_ERROR.buildMsg();
        }
        try {
            List<String> res = stockService.queryChildDoCodeByParentDo(parentCode);
            return ResponseMsg.SUCCESS.buildMsg(res);
        } catch (RomeException e) {
            log.error(e.getMessage(), e);
            return ResponseMsg.FAIL.buildMsg(e.getCode(), e.getMessage());
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return ResponseMsg.EXCEPTION.buildMsg();
        }
    }

    /**
     * 订单支付成功通知，推送do单
     * @param paramDTO SO单号
     * @return
     */
    @ApiOperation(value = "订单支付成功通知，推送do单", nickname = "toCombineDo", produces = MediaType.APPLICATION_JSON_VALUE)
    @ApiResponse(code = 200, message = "success", response = String.class)
    @PostMapping("/toCombineDo")
    public Response<List<RwRecordPoolResultDTO>> toCombineDo(@ApiParam(name = "orderCode", value = "SO单号") @RequestBody OnLineStockParamDTO paramDTO) {
        if (! validator.validParam(paramDTO)) {
            return ResponseMsg.PARAM_ERROR.buildMsg();
        }
        if (! validator.validStr(paramDTO.getOrderCode())) {
            return ResponseMsg.PARAM_ERROR.buildMsg();
        }
        log.warn(CoreKibanaLog.getJobLog(LOGTYPE, "toCombineDo", "订单支付成功通知, 订单号:" + paramDTO.getOrderCode(), paramDTO.getOrderCode()));
        String message = "";
        boolean isSucc = false;
        String json = JSON.toJSONString(paramDTO);
        try {
            List<RwRecordPoolResultDTO> res = stockService.toCombineDo(paramDTO.getOrderCode() , paramDTO.getPayTime());
            isSucc = true;
            message = "200";
            return ResponseMsg.SUCCESS.buildMsg(res);
        } catch (RomeException e) {
            log.error(e.getMessage(), e);
            message = e.getMessage();
            return ResponseMsg.FAIL.buildMsg(e.getCode(), e.getMessage());
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            message = e.getMessage();
            return ResponseMsg.EXCEPTION.buildMsg();
        }finally {
            sapInterfaceLogRepository.saveCallBackInterFaceLog(2, paramDTO.getOrderCode(), "toCombineDo",
                    json, message, isSucc);
        }
    }

    /**
     * 保存物流信息到do单
     * @param paramDTO SO单号
     * @return
     */
    @ApiOperation(value = "保存物流信息到do单", nickname = "saveLogisticInfo", produces = MediaType.APPLICATION_JSON_VALUE)
    @ApiResponse(code = 200, message = "success", response = String.class)
    @PostMapping("/saveLogisticInfo")
    public Response<Boolean> saveLogisticInfo(@ApiParam(name = "paramDTO", value = "paramDTO") @RequestBody @Validated List<OnLineStockLogisticInfoParamDTO> paramDTO) {
        if (paramDTO == null || paramDTO.size()==0) {
            return ResponseMsg.PARAM_ERROR.buildMsg();
        }
        String message = "";
        boolean isSucc = false;
        String json = JSON.toJSONString(paramDTO);
        try {
            stockService.saveLogisticInfo(paramDTO);
            isSucc = true;
            message = "200";
            return ResponseMsg.SUCCESS.buildMsg(true);
        } catch (RomeException e) {
            message = e.getMessage();
            log.error(e.getMessage(), e);
            return ResponseMsg.FAIL.buildMsg(e.getCode(), e.getMessage());
        } catch (Exception e) {
            message = e.getMessage();
            log.error(e.getMessage(), e);
            return ResponseMsg.EXCEPTION.buildMsg();
        } finally {
            sapInterfaceLogRepository.saveCallBackInterFaceLog(2,paramDTO.get(0).getDoCode(), "saveLogisticInfo",
                    json, message, isSucc);
        }
    }

    /**
     * 根据do单号查询包裹信息
     * @param doCode do单号
     * @return
     */
    @ApiOperation(value = "根据do单号查询包裹信息.", nickname = "queryPackageInfoByDoCode", produces = MediaType.APPLICATION_JSON_VALUE)
    @ApiResponse(code = 200, message = "success", response = String.class)
    @PostMapping("/queryPackageInfoByDoCode")
    public Response<List<RecordPackageDTO>> queryPackageInfoByDoCode(@ApiParam(name = "doCode", value = "doCode") @RequestParam String doCode) {
        if (doCode == null || "".equals(doCode)) {
            return ResponseMsg.PARAM_ERROR.buildMsg();
        }
        try {
            List<RecordPackageDTO> res = stockService.queryPackageInfoByDoCode(doCode);
            return ResponseMsg.SUCCESS.buildMsg(res);
        } catch (RomeException e) {
            log.error(e.getMessage(), e);
            return ResponseMsg.FAIL.buildMsg(e.getCode(), e.getMessage());
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return ResponseMsg.EXCEPTION.buildMsg();
        }
    }

    /**
     * 根据do单号查询包裹信息
     * @param doCodeList do单号列表
     * @return
     */
    @ApiOperation(value = "根据do单号批量查询包裹信息.", nickname = "queryPackageInfoByDoCodeList", produces = MediaType.APPLICATION_JSON_VALUE)
    @ApiResponse(code = 200, message = "success", response = String.class)
    @PostMapping("/queryPackageInfoByDoCodeList")
    public Response<Map<String , List<RecordPackageDTO>>> queryPackageInfoByDoCodeList(@ApiParam(name = "doCodeList", value = "doCodeList") @RequestBody List<String> doCodeList) {
        if (CollectionUtils.isEmpty(doCodeList)) {
            return ResponseMsg.PARAM_ERROR.buildMsg();
        }
        try {
            Map<String , List<RecordPackageDTO>> res = stockService.queryPackageInfoByDoCodeList(doCodeList);
            return ResponseMsg.SUCCESS.buildMsg(res);
        } catch (RomeException e) {
            log.error(e.getMessage(), e);
            return ResponseMsg.FAIL.buildMsg(e.getCode(), e.getMessage());
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return ResponseMsg.EXCEPTION.buildMsg();
        }
    }

    /**
     * 保存物流信息到do单
     * @param packageCode 包裹号
     * @return
     */
    @ApiOperation(value = "通过包裹号查询包裹信息", nickname = "queryPackageInfoByPackageCode", produces = MediaType.APPLICATION_JSON_VALUE)
    @ApiResponse(code = 200, message = "success", response = String.class)
    @PostMapping("/queryPackageInfoByPackageCode")
    public Response<RecordPackageDTO> queryPackageInfoByPackageCode(@ApiParam(name = "packageCode", value = "packageCode") @RequestParam String packageCode) {
        if (packageCode == null || "".equals(packageCode)) {
            return ResponseMsg.PARAM_ERROR.buildMsg();
        }
        try {
            RecordPackageDTO res = stockService.queryPackageInfoByPackageCode(packageCode);
            return ResponseMsg.SUCCESS.buildMsg(res);
        } catch (RomeException e) {
            log.error(e.getMessage(), e);
            return ResponseMsg.FAIL.buildMsg(e.getCode(), e.getMessage());
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return ResponseMsg.EXCEPTION.buildMsg();
        }
    }

    @ApiOperation(value = "根据子do单号取消do单", nickname = "cancelChildDO", produces = MediaType.APPLICATION_JSON_VALUE)
    @ApiResponse(code = 200, message = "success", response = String.class)
    @PostMapping("/cancelChildDO")
    public Response<Boolean> cancelChildDO(@ApiParam(name = "doCode", value = "doCode") @RequestParam String doCode) {
        String message = "";
        boolean isSucc = false;
        if (doCode == null || "".equals(doCode)) {
            return ResponseMsg.PARAM_ERROR.buildMsg();
        }
        try {
            stockService.cancelChildDO(doCode);
            isSucc = true;
            message = "200";
            return ResponseMsg.SUCCESS.buildMsg(true);
        } catch (RomeException e) {
            message = e.getMessage();
            log.error(e.getMessage(), e);
            return ResponseMsg.FAIL.buildMsg(e.getCode(), e.getMessage());
        } catch (Exception e) {
            message = e.getMessage();
            log.error(e.getMessage(), e);
            return ResponseMsg.FAIL.buildMsg(ResCode.STOCK_ERROR_1003, e.getMessage());
        } finally {
            sapInterfaceLogRepository.saveCallBackInterFaceLog(2, doCode, "cancelChildDO",
                    doCode, message, isSucc);
        }
    }

    /**
     * 取消订单，解冻库存
     * @param paramDTO SO单号
     * @return
     */
    @ApiOperation(value = "取消订单，解锁库存", nickname = "unLockStockByRecord", produces = MediaType.APPLICATION_JSON_VALUE)
    @ApiResponse(code = 200, message = "success", response = String.class)
    @PostMapping("/unLockStockByRecord")
    public Response unLockStockByRecord(@ApiParam(name = "paramDTO", value = "paramDTO") @RequestBody OnLineStockParamDTO paramDTO) {
        if (! validator.validParam(paramDTO)) {
            return ResponseMsg.PARAM_ERROR.buildMsg();
        }
        if (! validator.validStr(paramDTO.getOrderCode())) {
            return ResponseMsg.PARAM_ERROR.buildMsg();
        }
        String message = "";
        boolean isSucc = false;
        String json = JSON.toJSONString(paramDTO);
        try {
            stockService.unLockStockByRecord(paramDTO.getOrderCode());
            isSucc = true;
            message = "200";
            return ResponseMsg.SUCCESS.buildMsg();
        } catch (RomeException e) {
            log.error(e.getMessage(), e);
            message = e.getMessage();
            return ResponseMsg.FAIL.buildMsg(e.getCode(), e.getMessage());
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            message = e.getMessage();
            return ResponseMsg.EXCEPTION.buildMsg();
        } finally {
            sapInterfaceLogRepository.saveCallBackInterFaceLog(2, paramDTO.getOrderCode(), "unLockStockByRecord",
                    json, message, isSucc);
        }
    }



    /**
     * 取消跨境电商订单，解冻库存
     * @param paramDTO SO单号
     * @return
     */
    @ApiOperation(value = "取消跨境电商订单，解锁库存", nickname = "unLockStockByCross", produces = MediaType.APPLICATION_JSON_VALUE)
    @ApiResponse(code = 200, message = "success", response = String.class)
    @PostMapping("/unLockStockByCross")
    public Response unLockStockByCross(@ApiParam(name = "paramDTO", value = "paramDTO") @RequestBody OnLineStockParamDTO paramDTO) {
        if (! validator.validParam(paramDTO)) {
            return ResponseMsg.PARAM_ERROR.buildMsg();
        }
        if (! validator.validStr(paramDTO.getOrderCode())) {
            return ResponseMsg.PARAM_ERROR.buildMsg();
        }
        String message = "";
        boolean isSucc = false;
        String json = JSON.toJSONString(paramDTO);
        try {
            stockService.unLockStockByCross(paramDTO.getOrderCode());
            isSucc = true;
            message = "200";
            return ResponseMsg.SUCCESS.buildMsg();
        } catch (RomeException e) {
            log.error(e.getMessage(), e);
            message = e.getMessage();
            return ResponseMsg.FAIL.buildMsg(e.getCode(), e.getMessage());
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            message = e.getMessage();
            return ResponseMsg.FAIL.buildMsg("-500", e.getMessage());
        } finally {
            sapInterfaceLogRepository.saveCallBackInterFaceLog(2, paramDTO.getOrderCode(), "unLockStockByCross",
                    json, message, isSucc);
        }
    }


    /**
     * 修改发货单信息(详细地址、备注、手机号码，用户姓名等)
     * @param paramDTO 电商修改发货单相关参数
     * @return
     */
    @ApiOperation(value = "修改发货单信息(详细地址、备注、手机号码，用户姓名等)", nickname = "updateOrderInfo", produces = MediaType.APPLICATION_JSON_VALUE)
    @ApiResponse(code = 200, message = "success", response = String.class)
    @PostMapping("/updateOrderInfo")
    public Response updateOrderInfo(@ApiParam(name = "paramDTO", value = "修改发货单信息(详细地址、备注、手机号码，用户姓名等)") @RequestBody StockOrderRecordDTO paramDTO) {
        if (! validator.validParam(paramDTO)) {
            return ResponseMsg.PARAM_ERROR.buildMsg();
        }
        if (! validator.validStr(paramDTO.getOrderCode())) {
            return ResponseMsg.PARAM_ERROR.buildMsg();
        }
        String message = "";
        boolean isSucc = false;
        String json = JSON.toJSONString(paramDTO);
        try {
            stockService.updateOrderInfo(paramDTO);
            isSucc = true;
            message = "200";
            return ResponseMsg.SUCCESS.buildMsg();
        } catch (RomeException e) {
            log.error(e.getMessage(), e);
            message = e.getMessage();
            return ResponseMsg.FAIL.buildMsg(e.getCode(), e.getMessage());
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            message = e.getMessage();
            return ResponseMsg.EXCEPTION.buildMsg();
        }finally {
            sapInterfaceLogRepository.saveCallBackInterFaceLog(2, paramDTO.getOrderCode(), "updateOrderInfo",
                    json, message, isSucc);
        }
    }


    @ApiOperation(value = "下单前查询满足下单条件的所有仓库列表)", nickname = "querySatisfyRealhouse", produces = MediaType.APPLICATION_JSON_VALUE)
    @ApiResponse(code = 200, message = "success", response = String.class)
    @PostMapping("/querySatisfyRealhouse")
    public Response<List<RealWarehouse>> querySatisfyRealhouse(@ApiParam(name = "stockOrderDTO", value = "stockOrderDTO") @RequestBody StockOrderDTO stockOrderDTO) {
        if (! validator.validParam(stockOrderDTO)) {
            return ResponseMsg.PARAM_ERROR.buildMsg();
        }
        if (! validator.validPositiveInt(stockOrderDTO.getTransType())) {
            return ResponseMsg.PARAM_ERROR.buildMsg();
        }
        try {
            List<RealWarehouse> res = stockService.querySatisfyRealhouse(stockOrderDTO);
            return ResponseMsg.SUCCESS.buildMsg(res);
        } catch (RomeException e) {
            log.error(e.getMessage(), e);
            return ResponseMsg.FAIL.buildMsg(e.getCode(), e.getMessage());
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return ResponseMsg.EXCEPTION.buildMsg();
        }
    }



    @ApiOperation(value = "定时查询待同步交货信息给屡单系统的单据", nickname = "getWaitSyncDeliveryToFulfillmentOrder", produces = MediaType.APPLICATION_JSON_VALUE)
    @ApiResponse(code = 200, message = "success", response = List.class)
    @RequestMapping(value = "/getWaitSyncDeliveryToFulfillmentOrder", method = RequestMethod.POST)
    public Response<List<WarehouseRecordPageDTO>> getWaitSyncDeliveryToFulfillmentOrder(@RequestParam Integer page, @RequestParam Integer maxResult) {
        try {
            List<WarehouseRecordPageDTO> pageInfo = fulfillmentJobService.getWaitSyncDeliveryToFulfillmentOrder(page, maxResult);
            return ResponseMsg.SUCCESS.buildMsg(pageInfo);
        } catch (RomeException e) {
            log.error(e.getMessage(), e);
            return ResponseMsg.FAIL.buildMsg(e.getCode(), e.getMessage());
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return ResponseMsg.EXCEPTION.buildMsg();
        }
    }

    @ApiOperation(value = "同步交货信息给捋单系统", nickname = "syncDeliveryFulfillment", produces = MediaType.APPLICATION_JSON_VALUE)
    @ApiResponse(code = 200, message = "success", response = List.class)
    @RequestMapping(value = "/syncDeliveryFulfillment", method = RequestMethod.POST)
    public Response syncDeliveryFulfillment(@RequestBody WarehouseRecordPageDTO orderDto) {
        try {
            fulfillmentJobService.syncDeliveryFulfillment(orderDto);
            return ResponseMsg.SUCCESS.buildMsg();
        } catch (RomeException e) {
            log.error(e.getMessage(), e);
            return ResponseMsg.FAIL.buildMsg(e.getCode(), e.getMessage());
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return ResponseMsg.EXCEPTION.buildMsg();
        }
    }

    @ApiOperation(value = "查询待同步给tms的包裹单", nickname = "getWaitTransferToTMSPackage", produces = MediaType.APPLICATION_JSON_VALUE)
    @ApiResponse(code = 200, message = "success", response = List.class)
    @RequestMapping(value = "/getWaitTransferToTMSPackage", method = RequestMethod.POST)
    public Response<List<RecordPackageDTO>> getWaitTransferToTMSPackage(@RequestParam Integer page, @RequestParam Integer maxResult, @RequestParam Integer flag) {
        try {
            List<RecordPackageDTO> pageInfo = fulfillmentJobService.getWaitTransferToTMSPackage(page, maxResult,flag);
            return ResponseMsg.SUCCESS.buildMsg(pageInfo);
        } catch (RomeException e) {
            log.error(e.getMessage(), e);
            return ResponseMsg.FAIL.buildMsg(e.getCode(), e.getMessage());
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return ResponseMsg.EXCEPTION.buildMsg();
        }
    }

    @ApiOperation(value = "推送包裹信息给tms", nickname = "syncPackageInfoToTMS", produces = MediaType.APPLICATION_JSON_VALUE)
    @ApiResponse(code = 200, message = "success", response = List.class)
    @RequestMapping(value = "/syncPackageInfoToTMS", method = RequestMethod.POST)
    public Response syncPackageInfoToTMS(@RequestBody RecordPackageDTO orderDto) {
        try {
            fulfillmentJobService.syncPackageInfoToTMS(orderDto);
            return ResponseMsg.SUCCESS.buildMsg();
        } catch (RomeException e) {
            log.error(e.getMessage(), e);
            return ResponseMsg.FAIL.buildMsg(e.getCode(), e.getMessage());
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return ResponseMsg.EXCEPTION.buildMsg();
        }
    }

    @ApiOperation(value = "推送包裹信息给Ehub", nickname = "syncPackageInfoToTMS", produces = MediaType.APPLICATION_JSON_VALUE)
    @ApiResponse(code = 200, message = "success", response = List.class)
    @RequestMapping(value = "/syncPackageInfoToEhub", method = RequestMethod.POST)
    public Response syncPackageInfoToEhub(@RequestBody RecordPackageDTO orderDto) {
        try {
//            fulfillmentJobService.syncPackageInfoToEhub(orderDto);
            return ResponseMsg.SUCCESS.buildMsg();
        } catch (RomeException e) {
            log.error(e.getMessage(), e);
            return ResponseMsg.FAIL.buildMsg(e.getCode(), e.getMessage());
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return ResponseMsg.FAIL.buildMsg("-50000", e.getMessage());
        }
    }

    @ApiOperation(value = "测试推送包裹信息给Ehub", nickname = "syncPackageInfoToTMS", produces = MediaType.APPLICATION_JSON_VALUE)
    @ApiResponse(code = 200, message = "success", response = List.class)
    @RequestMapping(value = "/testPackageInfoToEhub", method = RequestMethod.POST)
    public Response testPackageInfoToEhub(@RequestBody String packageCode) {
        try {
            RecordPackageE recordPackageE=recordPackageRepository.getRecordPackageWithDetailByPackageCode(packageCode);
            if(null== recordPackageE){
                throw new RomeException(ResCode.STOCK_ERROR_1019, "包裹号不存在");
            }
            RecordPackageDTO dto= recordPackageConvertor.convertE2DTO(recordPackageE);
            fulfillmentJobService.syncPackageInfoToEhub(dto);
            return ResponseMsg.SUCCESS.buildMsg();
        } catch (RomeException e) {
            log.error(e.getMessage(), e);
            return ResponseMsg.FAIL.buildMsg(e.getCode(), e.getMessage());
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return ResponseMsg.FAIL.buildMsg("-50000", e.getMessage());
        }
    }


//    @Autowired
//    private TmsTools tmsTools;
//    @Autowired
//    private CoreConfig coreConfig;
//    @ApiOperation(value = "测试接口联通性", nickname = "test", produces = MediaType.APPLICATION_JSON_VALUE)
//    @ApiResponse(code = 200, message = "success", response = String.class)
//    @PostMapping("/test")
//    public Response  test(@RequestParam String outWarehouseCode) {
//        try {
//
//
////            List<TmsLogisticInfoDTO> res = tmsTools.queryLogisticByOutHouseCode(outWarehouseCode, coreConfig.getTmsBaseUrl(), coreConfig.getTmsClientId(), TmsConstants.TMS_QUERY_LOGISTOCS);
////            return ResponseMsg.SUCCESS.buildMsg(res);
//        } catch (RomeException e) {
//            log.error(e.getMessage(), e);
//            return ResponseMsg.FAIL.buildMsg(e.getCode(), e.getMessage());
//        } catch (Exception e) {
//            log.error(e.getMessage(), e);
//            return ResponseMsg.EXCEPTION.buildMsg();
//        }
//    }





    /**
     * 修改DO单
     * @param paramDTO 电商修改发货单相关参数
     * @return
     */
    @ApiOperation(value = "修改子DO单信息", nickname = "updateChildDoInfo", produces = MediaType.APPLICATION_JSON_VALUE)
    @ApiResponse(code = 200, message = "success", response = String.class)
    @PostMapping("/updateChildDoInfo")
    public Response<Boolean> updateChildDoInfo(@ApiParam(name = "paramDTO", value = "paramDTO") @RequestBody UpdateDoOrderDTO paramDTO) {
        String message = "";
        boolean isSucc = false;
        String json = JSON.toJSONString(paramDTO);
        if (! validator.validParam(paramDTO)) {
            return ResponseMsg.PARAM_ERROR.buildMsg();
        }
        try {
            stockService.updateChildDoInfo(orderAddressConvertor.dto2dto(paramDTO));
            isSucc = true;
            message = "200";
            return ResponseMsg.SUCCESS.buildMsg(true);
        } catch (RomeException e) {
            message = e.getMessage();
            log.error(e.getMessage(), e);
            return ResponseMsg.FAIL.buildMsg(e.getCode(), e.getMessage());
        } catch (Exception e) {
            message = e.getMessage();
            log.error(e.getMessage(), e);
            return ResponseMsg.EXCEPTION.buildMsg();
        } finally {
            sapInterfaceLogRepository.saveCallBackInterFaceLog(2, paramDTO.getDoCode(), "updateChildDoInfo",
                    json, message, isSucc);
        }
    }

    /**
     * 合并订单
     * @return
     */
    @ApiOperation(value = "合并订单", nickname = "mergeOrders", produces = MediaType.APPLICATION_JSON_VALUE)
    @ApiResponse(code = 200, message = "success", response = String.class)
    @PostMapping("/mergeOrders")
    public Response mergeOrders(@ApiParam(name = "needCombine", value = "是否需要合单") @RequestParam Integer needCombine) {
        try {
            stockService.mergeOrders(needCombine);
            return ResponseMsg.SUCCESS.buildMsg();
        } catch (RomeException e) {
            log.error(e.getMessage(), e);
            return ResponseMsg.FAIL.buildMsg(e.getCode(), e.getMessage());
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return ResponseMsg.EXCEPTION.buildMsg();
        }
    }

    /**
     * 合并订单
     * @return
     */
    @ApiOperation(value = "指定do合并订单", nickname = "assignRecordToMerge", produces = MediaType.APPLICATION_JSON_VALUE)
    @ApiResponse(code = 200, message = "success", response = String.class)
    @PostMapping("/assignRecordToMerge")
    public Response assignRecordToMerge(@ApiParam(name = "assignRecordToMerge", value = "指定do合单") @RequestBody List<String> doCodes) {
        if (null == doCodes || doCodes.size() == 0) {
            return ResponseMsg.PARAM_ERROR.buildMsg();
        }
        try {
            stockService.assignRecordToMerge(doCodes,1);
            return ResponseMsg.SUCCESS.buildMsg();
        } catch (RomeException e) {
            log.error(e.getMessage(), e);
            return ResponseMsg.FAIL.buildMsg(e.getCode(), e.getMessage());
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return ResponseMsg.EXCEPTION.buildMsg();
        }
    }


    /**
     * 根据运单号和物流公司编码，查询退货预入库单信息
     * @return
     */
    @ApiOperation(value = "根据运单号和物流公司编码，查询退货预入库单信息", nickname = "queryPredictReturnInfo", produces = MediaType.APPLICATION_JSON_VALUE)
    @ApiResponse(code = 200, message = "success", response = String.class)
    @PostMapping("/queryPredictReturnInfo")
    public Response<PredictReturnDTO> queryPredictReturnInfo(@ApiParam(name = "params", value = "运单号+物流公司编码") @RequestBody PredictReturnParamDTO paramDTO) {
        if (! validator.validParam(paramDTO)) {
            return ResponseMsg.PARAM_ERROR.buildMsg();
        }
        if (! validator.validStr(paramDTO.getExpressCode())) {
            return ResponseMsg.FAIL.buildMsg(ResCode.STOCK_ERROR_5020, ResCode.STOCK_ERROR_5020_DESC);
        }
        if (! validator.validStr(paramDTO.getLogisticsCode())) {
            return ResponseMsg.FAIL.buildMsg(ResCode.STOCK_ERROR_5021, ResCode.STOCK_ERROR_5021_DESC);
        }
        try {
            PredictReturnDTO predictReturnDTO = stockService.queryPredictReturnInfo(paramDTO.getExpressCode(), paramDTO.getLogisticsCode());
            return ResponseMsg.SUCCESS.buildMsg(predictReturnDTO);
        } catch (RomeException e) {
            log.error(e.getMessage(), e);
            return ResponseMsg.FAIL.buildMsg(e.getCode(), e.getMessage());
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return ResponseMsg.EXCEPTION.buildMsg();
        }
    }

    /**
     * 根据退货预入库单号，查询退货预入库单信息
     * @return
     */
    @ApiOperation(value = "根据退货预入库单号，查询退货预入库单信息", nickname = "queryPredictReturnInfoByCode", produces = MediaType.APPLICATION_JSON_VALUE)
    @ApiResponse(code = 200, message = "success", response = String.class)
    @GetMapping("/queryPredictReturnInfoByCode")
    public Response<PredictReturnDTO> queryPredictReturnInfoByCode(String recordCode) {
        if(recordCode == null || recordCode.equals("")) {
            return ResponseMsg.PARAM_ERROR.buildMsg();
        }
        try {
            PredictReturnDTO predictReturnDTO = stockService.queryPredictReturnInfoByCode(recordCode);
            return ResponseMsg.SUCCESS.buildMsg(predictReturnDTO);
        } catch (RomeException e) {
            log.error(e.getMessage(), e);
            return ResponseMsg.FAIL.buildMsg(e.getCode(), e.getMessage());
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return ResponseMsg.EXCEPTION.buildMsg();
        }
    }
    /**
     * 根据退货预入库单号，查询退货预入库单信息
     * @return
     */
    @ApiOperation(value = "根据退货预入库单号，查询退货预入库单信息，扣除已匹配数量", nickname = "queryPredictReturnInfoByCode", produces = MediaType.APPLICATION_JSON_VALUE)
    @ApiResponse(code = 200, message = "success", response = String.class)
    @GetMapping("/queryPredictReturnNotMatchInfoByCode")
    public Response<PredictReturnDTO> queryPredictReturnNotMatchInfoByCode(String recordCode) {
        if(recordCode == null || recordCode.equals("")) {
            return ResponseMsg.PARAM_ERROR.buildMsg();
        }
        try {
            PredictReturnDTO predictReturnDTO = stockService.queryPredictReturnNotMatchInfoByCode(recordCode);
            return ResponseMsg.SUCCESS.buildMsg(predictReturnDTO);
        } catch (RomeException e) {
            log.error(e.getMessage(), e);
            return ResponseMsg.FAIL.buildMsg(e.getCode(), e.getMessage());
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return ResponseMsg.EXCEPTION.buildMsg();
        }
    }


    /**
     * 退货预入库单完全匹配回调通知
     * @param paramDTO
     * @return
     */
    @Deprecated
    @ApiOperation(value = "退货预入库单完全匹配回调通知", nickname = "matchingNotify", produces = MediaType.APPLICATION_JSON_VALUE)
    @ApiResponse(code = 200, message = "success", response = String.class)
    @PostMapping("/matchFullNotify")
    public Response matchFullNotify(@ApiParam(name = "paramDTO", value = "运单号+物流公司编码") @RequestBody @Valid PredictReturnParamDTO paramDTO) {
        log.warn(CoreKibanaLog.getJobLog(KibanaLogConstants.CORE_LOG, "matchFullNotify", "退货预入库单完全匹配回调通知", paramDTO));
        if (! validator.validParam(paramDTO)) {
            return ResponseMsg.PARAM_ERROR.buildMsg();
        }
        if (! validator.validStr(paramDTO.getExpressCode())) {
            return ResponseMsg.FAIL.buildMsg(ResCode.STOCK_ERROR_5020, ResCode.STOCK_ERROR_5020_DESC);
        }
        if (! validator.validStr(paramDTO.getLogisticsCode())) {
            return ResponseMsg.FAIL.buildMsg(ResCode.STOCK_ERROR_5021, ResCode.STOCK_ERROR_5021_DESC);
        }
        try {
            stockService.matchFullNotify(paramDTO.getExpressCode(), paramDTO.getLogisticsCode());
            return ResponseMsg.SUCCESS.buildMsg();
        } catch (RomeException e) {
            log.error(e.getMessage(), e);
            return ResponseMsg.FAIL.buildMsg(e.getCode(), e.getMessage());
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return ResponseMsg.EXCEPTION.buildMsg();
        }
    }

    /**
     * 退货预入库单部分匹配回调通知
     * @param paramDTO
     * @return
     */
    @Deprecated
    @ApiOperation(value = "退货预入库单部分匹配回调通知", nickname = "matchPartNotify", produces = MediaType.APPLICATION_JSON_VALUE)
    @ApiResponse(code = 200, message = "success", response = String.class)
    @PostMapping("/matchPartNotify")
    public Response matchPartNotify(@ApiParam(name = "paramDTO", value = "运单号+物流公司编码") @RequestBody @Valid PredictReturnParamDTO paramDTO) {
        log.warn(CoreKibanaLog.getJobLog(KibanaLogConstants.CORE_LOG, "matchPartNotify", "退货预入库单部分匹配回调通知", paramDTO));
        if (! validator.validParam(paramDTO)) {
            return ResponseMsg.PARAM_ERROR.buildMsg();
        }
        if (! validator.validStr(paramDTO.getExpressCode())) {
            return ResponseMsg.FAIL.buildMsg(ResCode.STOCK_ERROR_5020, ResCode.STOCK_ERROR_5020_DESC);
        }
        if (! validator.validStr(paramDTO.getLogisticsCode())) {
            return ResponseMsg.FAIL.buildMsg(ResCode.STOCK_ERROR_5021, ResCode.STOCK_ERROR_5021_DESC);
        }
        try {
            stockService.matchPartNotify(paramDTO.getExpressCode(), paramDTO.getLogisticsCode());
            return ResponseMsg.SUCCESS.buildMsg();
        } catch (RomeException e) {
            log.error(e.getMessage(), e);
            return ResponseMsg.FAIL.buildMsg(e.getCode(), e.getMessage());
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return ResponseMsg.EXCEPTION.buildMsg();
        }
    }


    @ApiOperation(value = "退货预入库单匹配回调通知", nickname = "matchPartNotify", produces = MediaType.APPLICATION_JSON_VALUE)
    @ApiResponse(code = 200, message = "success", response = String.class)
    @PostMapping("/matchNotify")
    public Response matchNotify(@ApiParam(name = "paramDTO", value = "匹配通知入参") @RequestBody List<PredictReturnParamDTO> paramDTO) {
        log.warn(CoreKibanaLog.getJobLog(KibanaLogConstants.CORE_LOG, "matchNotify", "退货预入库单匹配回调通知", paramDTO));
        if (null == paramDTO || paramDTO.size() == 0 || paramDTO.size() > 200) {
            return ResponseMsg.PARAM_ERROR.buildMsg();
        }
        for (PredictReturnParamDTO dto : paramDTO) {
            if (null == dto.getPredictRecordCode()) {
                return ResponseMsg.PARAM_ERROR.buildMsg();
            }
            if (null == dto.getReverseOrderNo()) {
                return ResponseMsg.PARAM_ERROR.buildMsg();
            }
            if (null == dto.getExpressCode()) {
                return ResponseMsg.PARAM_ERROR.buildMsg();
            }
            if (null == dto.getLogisticsCode()) {
                return ResponseMsg.PARAM_ERROR.buildMsg();
            }

            if (null == dto.getSkuCode()) {
                return ResponseMsg.PARAM_ERROR.buildMsg();
            }
            if (null == dto.getSkuQty()) {
                return ResponseMsg.PARAM_ERROR.buildMsg();
            }
            if (null == dto.getChannelCode()) {
                return ResponseMsg.PARAM_ERROR.buildMsg();
            }
        }
        try {
            stockService.matchNotify(paramDTO);
            return ResponseMsg.SUCCESS.buildMsg();
        } catch (RomeException e) {
            log.error(e.getMessage(), e);
            return ResponseMsg.FAIL.buildMsg(e.getCode(), e.getMessage());
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return ResponseMsg.EXCEPTION.buildMsg();
        }
    }

    /**
     * 根据单据池DO单号查询单据池信息
     */
    @ApiOperation(value = "根据单据池DO单号查询单据池信息", nickname = "queryPoolRecordByPoolRecordCode", produces = MediaType.APPLICATION_JSON_VALUE)
    @ApiResponse(code = 200, message = "success", response = String.class)
    @GetMapping("/queryPoolRecordByPoolRecordCode")
    public Response<RwRecordPoolDTO> queryPoolRecordByPoolRecordCode(String[] poolRecordCodes) {
        if(poolRecordCodes == null || poolRecordCodes.length == 0 || poolRecordCodes.length > CommonConsts.BATCH_PARAM_MAX_LENGTH) {
            return ResponseMsg.PARAM_ERROR.buildMsg();
        }

        List<String> codeList = Arrays.asList(poolRecordCodes);
        try {
            List<RwRecordPoolDTO> res = stockService.queryPoolRecordByPoolRecordCode(codeList);
            return ResponseMsg.SUCCESS.buildMsg(res);
        } catch (RomeException e) {
            log.error(e.getMessage(), e);
            return ResponseMsg.FAIL.buildMsg(e.getCode(), e.getMessage());
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return ResponseMsg.EXCEPTION.buildMsg();
        }
    }

    /**
     * 虚拟物品下单锁定库存
     * @param virtualSkuStockOrderDTO 电商下单相关参数
     * @return
     */
    @ApiOperation(value = "虚拟物品下单锁定库存", nickname = "lockVirtualSkuStock", produces = MediaType.APPLICATION_JSON_VALUE)
    @ApiResponse(code = 200, message = "success", response = String.class)
    @PostMapping("/lockVirtualSkuStock")
    public Response<Boolean> lockVirtualSkuStockByRecord(@ApiParam(name = "stockOrderDTO", value = "虚拟物品下单锁定库存") @RequestBody @Validated VirtualSkuStockOrderDTO virtualSkuStockOrderDTO) {
        String message = "";
        boolean isSucc = false;
        String json = JSON.toJSONString(virtualSkuStockOrderDTO);
        if (! validator.validParam(virtualSkuStockOrderDTO)) {
            return ResponseMsg.PARAM_ERROR.buildMsg();
        }
        if (! validator.validPositiveInt(virtualSkuStockOrderDTO.getTransType())) {
            return ResponseMsg.PARAM_ERROR.buildMsg();
        }
        log.warn(CoreKibanaLog.getJobLog(LOGTYPE, "lockVirtualSkuStock", "虚拟物品下单锁定库存, 订单号:" + virtualSkuStockOrderDTO.getOrderCode(), virtualSkuStockOrderDTO.getOrderCode()));
        try {
            stockService.lockVirtualSkuStock(virtualSkuStockOrderDTO);
            isSucc = true;
            message = "200";
            return ResponseMsg.SUCCESS.buildMsg(true);
        } catch (RomeException e) {
            log.error(e.getMessage(), e);
            message = e.getMessage();
            return ResponseMsg.FAIL.buildMsg(e.getCode(), e.getMessage());
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            message = e.getMessage();
            return ResponseMsg.EXCEPTION.buildMsg();
        }finally {
            long start = System.currentTimeMillis();
            sapInterfaceLogRepository.saveCallBackInterFaceLog(2, virtualSkuStockOrderDTO.getOrderCode(), "lockVirtualSkuStock",
                    json, message, isSucc);
            log.info("虚拟物品下单锁定库存写日志耗时：单号{},耗时{}",virtualSkuStockOrderDTO.getOrderCode(),(System.currentTimeMillis() - start));
        }
    }

    /**
     * 虚拟物品支付成功
     * @param orderCode
     * @return
     */
    @ApiOperation(value = "虚拟物品支付成功", nickname = "virtualSkuStockAfterPay", produces = MediaType.APPLICATION_JSON_VALUE)
    @ApiResponse(code = 200, message = "success", response = String.class)
    @PostMapping("/virtualSkuStockAfterPay")
    public Response<List<RwRecordPoolResultDTO>> virtualSkuStockAfterPay(@ApiParam(name = "orderCode", value = "虚拟物品支付成功") @RequestParam("orderCode")  String orderCode) {
        String message = "";
        boolean isSucc = false;
        if (StringUtils.isEmpty(orderCode)) {
            return ResponseMsg.PARAM_ERROR.buildMsg();
        }
        log.warn(CoreKibanaLog.getJobLog(LOGTYPE, "virtualSkuStockAfterPay", "虚拟物品支付成功, 订单号:" + orderCode, orderCode));
        try {
            List<RwRecordPoolResultDTO> recordCode =stockService.virtualSkuStockAfterPay(orderCode);
            isSucc = true;
            message = "200";
            return ResponseMsg.SUCCESS.buildMsg(recordCode);
        } catch (RomeException e) {
            message = e.getMessage();
            log.error(e.getMessage(), e);
            return ResponseMsg.FAIL.buildMsg(e.getCode(), e.getMessage());
        } catch (Exception e) {
            message = e.getMessage();
            log.error(e.getMessage(), e);
            return ResponseMsg.EXCEPTION.buildMsg();
        }finally {
            sapInterfaceLogRepository.saveCallBackInterFaceLog(2, orderCode, "virtualSkuStockAfterPay",
                    orderCode, message, isSucc);
        }
    }

    /**
     * 推送TMS后修改运单号
     * @param updatePackageCodeDTO
     * @return
     */
    @ApiOperation(value = "修改运单号", nickname = "updateBillCodeByPackageCode", produces = MediaType.APPLICATION_JSON_VALUE)
    @ApiResponse(code = 200, message = "success", response = String.class)
    @PostMapping("/updateBillCodeByPackageCode")
    public Response updateBillCodeByPackageCode(@ApiParam(name = "updatePackageCodeDTO", value = "推送TMS后修改运单号") @RequestBody  UpdatePackageCodeDTO updatePackageCodeDTO) {
        String message = "";
        boolean isSucc = false;
        String json = JSON.toJSONString(updatePackageCodeDTO);
        if (CollectionUtils.isEmpty(updatePackageCodeDTO.getUpdateBillCodeDTO()) || StringUtils.isEmpty(updatePackageCodeDTO.getRequestCode())) {
            return ResponseMsg.PARAM_ERROR.buildMsg();
        }
        try {
            List<UpdateBillCodeDTO> res=stockService.updateBillCodeByPackageCode(updatePackageCodeDTO.getUpdateBillCodeDTO());
            isSucc = true;
            message = "200";
            return ResponseMsg.SUCCESS.buildMsg(res);
        } catch (RomeException e) {
            message = e.getMessage();
            log.error(e.getMessage(), e);
            return ResponseMsg.FAIL.buildMsg(e.getCode(), e.getMessage());
        } catch (Exception e) {
            message = e.getMessage();
            log.error(e.getMessage(), e);
            return ResponseMsg.EXCEPTION.buildMsg();
        }finally {
            sapInterfaceLogRepository.saveCallBackInterFaceLog(2, updatePackageCodeDTO.getRequestCode(), "updateBillCodeByPackageCode",
                    json, message, isSucc);
        }
    }

    @ApiOperation(value = "推送toc出库单到订单中心", produces = MediaType.APPLICATION_JSON_VALUE)
    @ApiResponse(code = 200, message = "success")
    @RequestMapping(value = "/pushOutNotify", method = RequestMethod.POST)
    public Response pushOutNotify(@ApiParam(name = "recordCode", value = "recordCode") @RequestParam("recordCode") String recordCode) {
        try {
            stockService.pushOutNotify(recordCode);
            return Response.builderSuccess("");
        } catch (RomeException e) {
            log.error(e.getMessage(), e);
            return Response.builderFail(e.getCode(),e.getMessage());
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return Response.builderFail(ResCode.STOCK_ERROR_1003,ResCode.STOCK_ERROR_1003_DESC+e.getMessage()==null?e.toString():e.getMessage());
        }
    }

}
