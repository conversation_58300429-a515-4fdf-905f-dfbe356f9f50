package com.rome.stock.innerservice.api.controller;

import com.alibaba.fastjson.JSON;
import com.github.pagehelper.PageInfo;
import com.rome.arch.core.clientobject.Response;
import com.rome.arch.core.exception.RomeException;
import com.rome.arch.util.controller.RomeController;
import com.rome.stock.innerservice.api.dto.*;
import com.rome.stock.innerservice.api.dto.frontrecord.QualityResultCallBackDTO;
import com.rome.stock.innerservice.common.RedisUtil;
import com.rome.stock.innerservice.common.ResCode;
import com.rome.stock.innerservice.constant.ResponseMsg;
import com.rome.stock.innerservice.domain.message.consumer.InitUnOutOnRoadStockConsumer;
import com.rome.stock.innerservice.domain.message.consumer.QualityNotifyAutoWmsConsumer;
import com.rome.stock.innerservice.domain.service.AddressStopService;
import com.rome.stock.innerservice.domain.service.ShopRetailService;
import com.rome.stock.innerservice.facade.StockOnlineOrderFacade;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import io.swagger.annotations.ApiResponse;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.rocketmq.common.message.MessageExt;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;


@Slf4j
@RomeController
@RequestMapping(value = "/stock/v1/address_stop")
@Api(tags = "仓库停发配置")
public class AddressStopController {

    @Autowired
    private AddressStopService addressStopService;
    @Autowired
    private ShopRetailService shopRetailService;
    @Autowired
    private RedisUtil redisUtil;
    @Resource
    private QualityNotifyAutoWmsConsumer qualityNotifyAutoWmsConsumer;

    @ApiOperation(value = "查询所有电商实仓", produces = MediaType.APPLICATION_JSON_VALUE)
    @ApiResponse(code = 200, message = "success", response = RealWarehouseWmsConfigDTO.class)
    @RequestMapping(value = "/queryEShopWarehouse", method = RequestMethod.GET)
    public Response<List<RealWarehouse>> queryEShopWarehouse() {
        try {
            List<RealWarehouse> list = addressStopService.queryEShopWarehouse();
            return ResponseMsg.SUCCESS.buildMsg(list);
        } catch (RomeException e) {
            log.error(e.getMessage(), e);
            return ResponseMsg.FAIL.buildMsg(e.getCode(), e.getMessage());
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return ResponseMsg.EXCEPTION.buildMsg();
        }
    }

    @ApiOperation(value = "查询仓库停发配置", nickname = "getAddressStop", produces = MediaType.APPLICATION_JSON_VALUE)
    @ApiResponse(code = 200, message = "success", response = List.class)
    @RequestMapping(value = "/getAddressStop", method = RequestMethod.POST)
    public Response<PageInfo<AddressStopDTO>> getAddressStop(@ApiParam(name = "AddressStopDTO", value = "仓库停发配置") @RequestBody AddressStopDTO paramDTO) {
        try {
            PageInfo<AddressStopDTO> personPageInfo = addressStopService.getAddressStop(paramDTO);
            return Response.builderSuccess(personPageInfo);
        } catch (RomeException e) {
            log.error(e.getMessage(), e);
            return Response.builderFail(e.getCode(),e.getMessage());
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return Response.builderFail(ResCode.STOCK_ERROR_1003,ResCode.STOCK_ERROR_1003_DESC);
        }
    }

    @ApiOperation(value = "根据ids删除仓库停发配置", nickname = "delAddressStopByIds", produces = MediaType.APPLICATION_JSON_VALUE)
    @ApiResponse(code = 200, message = "success", response = List.class)
    @RequestMapping(value = "/delAddressStopByIds", method = RequestMethod.POST)
    public Response delAddressStopByIds(
            @ApiParam(name = "ids", value = "发货仓库配置ids") @RequestBody List<AddressStopDelDTO> ids,
            @ApiParam(name = "userId", value = "userId") @RequestParam("userId") Long userId) {
        List<String> locks=new ArrayList<>();
        try {
            if (CollectionUtils.isNotEmpty(ids)){
                List<String> realWarehouseCodes=ids.stream().map(AddressStopDelDTO::getRealWarehouseCode).distinct().collect(Collectors.toList());
                for (String realWarehouseCode:realWarehouseCodes){
                    String uniqueKey = "addressStop" + "_" + realWarehouseCode;
                    if (!redisUtil.lock(uniqueKey,"1",120)){
                        return Response.builderFail(ResCode.STOCK_ERROR_1003,String.format("仓库%s停发配置正在处理中，请稍后再试。。。",realWarehouseCode));
                    }else {
                        locks.add(uniqueKey);
                    }
                }
                List<Long> stopIds=ids.stream().map(AddressStopDelDTO::getId).distinct().collect(Collectors.toList());
                if (CollectionUtils.isNotEmpty(stopIds)){
                    List<Long> realWarehouseIds=addressStopService.delAddressStopByIds(stopIds,userId);
                    delRouteInfoAndCalculateSD(realWarehouseIds,userId);
                }
            }
            return Response.builderSuccess("");
        } catch (RomeException e) {
            log.error(e.getMessage(), e);
            return ResponseMsg.EXCEPTION.buildMsg(e.getCode(), e.getMessage());
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return ResponseMsg.EXCEPTION.buildMsg(ResCode.STOCK_ERROR_1003, ResCode.STOCK_ERROR_1003_DESC);
        } finally {
            if (CollectionUtils.isNotEmpty(locks)){
                locks.stream().forEach(lock->{
                    redisUtil.unLock(lock,"1");
                });
            }
        }
    }

    @ApiOperation(value = "停发导入", nickname = "addressStopImport", produces = MediaType.MULTIPART_FORM_DATA_VALUE)
    @PostMapping(value = "/addressStopImport")
    public Response<List<AddressStopTemplateDTO>> addressStopImport(@RequestBody List<AddressStopTemplateDTO> dataList, @RequestParam("userId") Long userId) {
        List<String> locks=new ArrayList<>();
        try {
            for (AddressStopTemplateDTO dto:dataList){
                String uniqueKey = "addressStop" + "_" + dto.getRealWarehouseCode();
                if (!redisUtil.lock(uniqueKey,"1",120)){
                    return Response.builderFail(ResCode.STOCK_ERROR_1003,"停发导入正在处理中，请稍后再试。。。");
                }else {
                    locks.add(uniqueKey);
                }
            }
            List<AddressStopTemplateDTO> list=addressStopService.addressStopImport(dataList,userId);
            if (CollectionUtils.isEmpty(list)&&CollectionUtils.isNotEmpty(dataList)){
                List<Long> realWarehouseIds=dataList.stream().map(AddressStopTemplateDTO::getRealWarehouseId).distinct().collect(Collectors.toList());
                delRouteInfoAndCalculateSD(realWarehouseIds,userId);
            }
            return Response.builderSuccess(list);
        } catch (RomeException e){
            log.error("停发导入异常 ==> {}", e.getMessage(), e);
            return Response.builderFail(e.getCode(), e.getMessage());
        } catch (Exception e){
            log.error("停发导入系统异常 ==> {}", e.getMessage(), e);
            return Response.builderFail(ResCode.STOCK_ERROR_1003, ResCode.STOCK_ERROR_1003_DESC);
        }finally {
            if (CollectionUtils.isNotEmpty(locks)){
                locks.stream().forEach(lock->{
                    redisUtil.unLock(lock,"1");
                });
            }
        }
    }

    /**
     * 先清缓存，再停发计算
     * @param realWarehouseIds
     * @param userId
     */
    private void delRouteInfoAndCalculateSD(List<Long> realWarehouseIds,Long userId){
        if (CollectionUtils.isNotEmpty(realWarehouseIds)){
            realWarehouseIds.stream().forEach(realWarehouseId->{
                StockOnlineOrderFacade.delRouteInfoByCoverFocusSale(realWarehouseId);//清楚缓存
//                shopRetailService.calculateStopDeliveryByRwIdAsyn(realWarehouseId,userId);//停发计算
            });
        }
    }
    
    @ApiOperation(value = "查询包含停发的实仓列表", produces = MediaType.APPLICATION_JSON_VALUE)
    @ApiResponse(code = 200, message = "success", response = RealWarehouse.class)
    @RequestMapping(value = "/queryAddressStopRwAll", method = RequestMethod.GET)
    public Response<List<RealWarehouse>> queryAddressStopRwAll() {
        try {
            List<RealWarehouse> list = addressStopService.queryAddressStopRwAll();
            return ResponseMsg.SUCCESS.buildMsg(list);
        } catch (RomeException e) {
            log.error(e.getMessage(), e);
            return ResponseMsg.FAIL.buildMsg(e.getCode(), e.getMessage());
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return ResponseMsg.EXCEPTION.buildMsg();
        }
    }

    @ApiOperation(value = "initUnOutOnRoadStockConsumer", produces = MediaType.APPLICATION_JSON_VALUE)
    @ApiResponse(code = 200, message = "success", response = RealWarehouse.class)
    @RequestMapping(value = "/qualityNotifyAutoWmsConsumer", method = RequestMethod.POST)
    public Response<List<RealWarehouse>> initUnOutOnRoadStockConsumer(@RequestBody QualityResultCallBackDTO dto) {
        try {
            MessageExt messageExt = new MessageExt();
            messageExt.setBody(JSON.toJSONString(dto).getBytes());
            qualityNotifyAutoWmsConsumer.onMessage(messageExt, dto.getWmsRecordCode(), dto.getWmsRecordCode());
            return ResponseMsg.SUCCESS.buildMsg();
        } catch (RomeException e) {
            log.error(e.getMessage(), e);
            return ResponseMsg.FAIL.buildMsg(e.getCode(), e.getMessage());
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return ResponseMsg.EXCEPTION.buildMsg(e.getMessage());
        }
    }



}

