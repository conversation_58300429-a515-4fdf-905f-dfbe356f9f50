package com.rome.stock.innerservice.api.dto;

import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 * @Description BoxBatchStockOpDTO
 * <AUTHOR>
 * @Date 2024/5/23
 **/

@Data
public class BoxBatchStockOpDTO {

    private Long id;


    private String batchCode;


    /**
     * 生产日期
     */
    private Date productDate;

    /**
     * 商品skuID
     */
    private Long skuId;

    /**
     * 商品编码
     */
    private String skuCode;

    /**
     * 真实库存
     */
    private BigDecimal skuQty;

    /**
     * 锁定库存
     */
    private BigDecimal lockQty;


    /**
     * 锁定库存
     */
    private BigDecimal unlockQty;

    /**
     * 实体仓库id
     */
    private Long realWarehouseId;

    private String recordCode;

    private Integer recordType;

    private Integer boxType;

    private Integer stockType;


    /**
     * 版本号:默认0,每次更新+1
     */
    private Integer versionNo;

    private String rwBatchCode;

    private Long batchFlowId;


    public String getKey() {
        return realWarehouseId + "-" + skuId + "-" + batchCode;
    }
}
