/**
 * Filename NoticeAvailableStockSkuInfoDTO.java
 * Company 上海来伊份科技有限公司。
 * <AUTHOR>
 * @version 
 */
package com.rome.stock.innerservice.api.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 可用库存有无通知sku信息
 * <AUTHOR>
 * @since 2021-11-24 14:24:29
 */
@Data
@EqualsAndHashCode
public class NoticeAvailableStockSkuInfoDTO {

	/**
     * 商品skuID
     */
    @ApiModelProperty(value = "商品skuID")
    private Long skuId;
    
    @ApiModelProperty(value = "商品编码")
    private String skuCode;
    
    @ApiModelProperty(value = "库存有无标识-为0无，1有")
    private Integer flag;
    
    @ApiModelProperty(value = "单位code")
    private String unitCode;
    
    @ApiModelProperty(value = "单位类型")
    private Integer unitType;
    
}
