package com.rome.stock.innerservice.api.controller;

import java.util.List;

import javax.annotation.Resource;

import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;

import com.github.pagehelper.PageInfo;
import com.rome.arch.core.clientobject.Response;
import com.rome.arch.core.exception.RomeException;
import com.rome.arch.util.controller.RomeController;
import com.rome.stock.innerservice.api.dto.DeliverRelation;
import com.rome.stock.innerservice.common.ResCode;
import com.rome.stock.innerservice.constant.ResponseMsg;
import com.rome.stock.innerservice.domain.entity.DeliverRelationE;
import com.rome.stock.innerservice.domain.service.DeliverRelationService;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import io.swagger.annotations.ApiResponse;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@RomeController
@RequestMapping("/stock/v1/deliver_relation")
@Api(tags = {"发货仓库配置"})
public class DeliverRelationController {
    @Resource
    private DeliverRelationService deliverRelationService;

    @ApiOperation(value = "查询发货仓库配置", nickname = "getDeliverRelation", produces = MediaType.APPLICATION_JSON_VALUE)
    @ApiResponse(code = 200, message = "success", response = List.class)
    @RequestMapping(value = "/getDeliverRelation", method = RequestMethod.POST)
    public Response<PageInfo<DeliverRelation>> getDeliverRelation(@ApiParam(name = "DeliverRelation", value = "Sku寻源仓库") @RequestBody DeliverRelation deliverRelation) {
        try {
            PageInfo<DeliverRelation> pageList = deliverRelationService.getDeliverRelationPage(deliverRelation);
            return Response.builderSuccess(pageList);
        } catch (RomeException e) {
            log.error(e.getMessage(), e);
            return ResponseMsg.EXCEPTION.buildMsg(e.getCode(), e.getMessage());
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return ResponseMsg.EXCEPTION.buildMsg(ResCode.STOCK_ERROR_1003, ResCode.STOCK_ERROR_1003_DESC);
        }
    }

    @ApiOperation(value = "新增发货仓库配置", nickname = "addDeliverRelation", produces = MediaType.APPLICATION_JSON_VALUE)
    @ApiResponse(code = 200, message = "success", response = List.class)
    @RequestMapping(value = "/addDeliverRelation", method = RequestMethod.POST)
    public Response addDeliverRelation(@ApiParam(name = "deliverRelation", value = "发货仓库配置") @RequestBody List<DeliverRelation> deliverRelation) {
        try {
            deliverRelationService.addDeliverRelation(deliverRelation);
            return Response.builderSuccess("");
        } catch (RomeException e) {
            log.error(e.getMessage(), e);
            throw new RomeException(e.getCode(), e.getMessage());
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return ResponseMsg.EXCEPTION.buildMsg(ResCode.STOCK_ERROR_1003, e.getMessage());
        }
    }

    @ApiOperation(value = "根据ids删除发货仓库配置", nickname = "delDeliverRelationByIds", produces = MediaType.APPLICATION_JSON_VALUE)
    @ApiResponse(code = 200, message = "success", response = List.class)
    @RequestMapping(value = "/delDeliverRelationByIds", method = RequestMethod.POST)
    public Response delDeliverRelationByIds(@ApiParam(name = "ids", value = "发货仓库配置ids") @RequestParam List<Long> ids, @ApiParam(name = "userId", value = "userId") @RequestParam("userId") Long userId) {
        try {
            deliverRelationService.delDeliverRelationByIds(ids, userId);
            return Response.builderSuccess("");
        } catch (RomeException e) {
            log.error(e.getMessage(), e);
            return ResponseMsg.EXCEPTION.buildMsg(e.getCode(), e.getMessage());
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return ResponseMsg.EXCEPTION.buildMsg(ResCode.STOCK_ERROR_1003, ResCode.STOCK_ERROR_1003_DESC);
        }
    }

    @ApiOperation(value = "获取发货工厂", nickname = "getDeliverFactory", produces = MediaType.APPLICATION_JSON_VALUE)
    @ApiResponse(code = 200, message = "success", response = List.class)
    @RequestMapping(value = "/getDeliverFactory", method = RequestMethod.POST)
    public Response getDeliverFactory( @ApiParam(name = "zFactory", value = "zFactory") @RequestParam("zFactory") String zFactory) {
        try {
            return Response.builderSuccess(deliverRelationService.getDeliverFactory(zFactory));
        } catch (RomeException e) {
            log.error(e.getMessage(), e);
            return ResponseMsg.EXCEPTION.buildMsg(e.getCode(), e.getMessage());
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return ResponseMsg.EXCEPTION.buildMsg(ResCode.STOCK_ERROR_1003, ResCode.STOCK_ERROR_1003_DESC);
        }
    }

    @ApiOperation(value = "查询所有发货仓库配置", nickname = "getDeliverRelation", produces = MediaType.APPLICATION_JSON_VALUE)
    @ApiResponse(code = 200, message = "success", response = List.class)
    @RequestMapping(value = "/listAllDeliverRelation", method = RequestMethod.GET)
    public Response listAllDeliverRelation() {
        try {
            List<DeliverRelationE> pageList = deliverRelationService.getDeliverRelation();
            return Response.builderSuccess(pageList);
        } catch (RomeException e) {
            log.error(e.getMessage(), e);
            return ResponseMsg.EXCEPTION.buildMsg(e.getCode(), e.getMessage());
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return ResponseMsg.EXCEPTION.buildMsg(ResCode.STOCK_ERROR_1003, ResCode.STOCK_ERROR_1003_DESC);
        }
    }


}
