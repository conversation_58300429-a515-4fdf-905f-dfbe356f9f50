package com.rome.stock.innerservice.api.dto;

import cn.afterturn.easypoi.excel.annotation.Excel;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;
import java.util.Date;

/**
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode
public class ReverseDownLoadDTO {

    @Excel(name = "冲销类型",replace = { "入库单冲销_1", "出库单冲销_2" },addressList = true,width = 20)
    private Integer businessType;

    @Excel(name = "冲销原单类型",width = 20)
    private Integer recordType;

    @Excel(name = "出入库单号")
    private String recordCode;

    @Excel(name = "原单号（入库单冲销为收货单号，出库单冲销为出库单号）")
    private String originRecordCode;

    @Excel(name = "业务单号")
    private String sapRecordCode;

    @Excel(name = "财务日期")
    private String financeDate;

    @Excel(name = "原仓库编号")
    private String originRealWarehouseCode;

    @Excel(name = "冲销原因",replace = { "操作错误_01", "税率错误_02", "价格错误_03", "用户取消领用_04", "无实物领用_05", "主数据错误_06" },addressList = true,width = 20)
    private String reasonCode;

    @Excel(name = "冲销出入库仓库编号",width = 120)
    private String realWarehouseCode;

    @Excel(name = "冲销人员")
    private String creator;

    @Excel(name = "序列号")
    private String serialNo;

    @Excel(name = "备注")
    private String remark;

    @Excel(name = "原明细ID")
    private Long refDetailId;

    @Excel(name = "原批次ID")
    private Long rwBatchId;

    @Excel(name = "原批次编号")
    private String batchCode;

    @Excel(name = "单位")
    private String unit;

    @Excel(name = "单位编码")
    private String unitCode;

    @Excel(name = "商品ID")
    private Long skuId;

    @Excel(name = "商品编号")
    private String skuCode;

    @Excel(name = "原单数量")
    private BigDecimal skuQty;

    @Excel(name = "冲销数量")
    private BigDecimal accQty;

}
