package com.rome.stock.innerservice.api.dto.allocation;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;

/**
 * 后置单详情
 */
@Data
@EqualsAndHashCode
public class WhAllocationWarehouseDetailDTO {

    @ApiModelProperty(value = "后置单编号")
    @NotNull(message="后置单编号不能为空")
    private String recordCode;

    /** sku编号 */
    @NotEmpty(message="skuCode不能为空")
    private String skuCode;
    /**
	 * sap行号(过账用)
	 * */
    @NotBlank(message="lineNo号不能为空")
	private String lineNo;

    /**
     * 供应链行号，收发货匹配用
     * */
    @NotBlank(message="deliveryLineNo号不能为空")
    private String deliveryLineNo;

    /**
     * sap采购单据编号
     * */
    @NotBlank(message="sapPoNo号不能为空")
    private String sapPoNo;


}
