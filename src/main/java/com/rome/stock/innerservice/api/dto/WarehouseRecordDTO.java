package com.rome.stock.innerservice.api.dto;

import com.rome.stock.innerservice.api.dto.warehouserecord.RecordPackageDTO;
import com.rome.stock.innerservice.constant.WmsSyncStatusVO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;
import java.util.List;

@Data
@EqualsAndHashCode
public class WarehouseRecordDTO {

    /**
     * 单据Id
     * */
    private Long id;

    /**
     * 单据编码
     */
    @ApiModelProperty(value = "单据编码")
    private String recordCode;
    /**
     * 业务类型：
     */
    @ApiModelProperty(value = "业务类型")
    private Integer businessType;
    /**
     * do单状态，0 未同步  1 已同步 10 拣货 11 打包 12 装车 13 发运 21 接单 22 配送 23 完成
     */
    @ApiModelProperty(value = "do单状态")
    private Integer recordStatus;
    /**
     * 单据类型：1-销售出库订单，2-采购单
     */
    @ApiModelProperty(value = "单据类型：1-销售出库订单，2-采购单")
    private Integer recordType;
    /**
     * 用户code
     */
    @ApiModelProperty(value = "用户code")
    private String userCode;
    /**
     * 虚拟仓库ID
     */
    @ApiModelProperty(value = "虚拟仓库ID")
    private Long virtualWarehouseId;
    /**
     * 实仓ID
     */
    @ApiModelProperty(value = "实仓ID")
    private Long realWarehouseId;
    /**
     * 渠道id
     */
    @ApiModelProperty(value = "渠道id")
    private String channelCode;
    /**
     * 店铺id
     */
    @ApiModelProperty(value = "店铺id")
    private Long shopId;
    /**
     * 商家id
     */
    @ApiModelProperty(value = "商家id")
    private Long merchantId;
    /**
     * 订单备注(用户)
     */
    @ApiModelProperty(value = "订单备注(用户)")
    private String orderRemarkUser;
    /**
     * 期望收货日期_开始
     */
    @ApiModelProperty(value = "期望收货日期_开始")
    private Date expectReceiveDateStart;
    /**
     * 期望收货日期_截止
     */
    @ApiModelProperty(value = "期望收货日期_截止")
    private Date expectReceiveDateEnd;
    /**
     * 收货人地址邮编
     */
    @ApiModelProperty(value = "收货人地址邮编")
    private String receiverPostcode;
    /**
     * 收货人手机
     */
    @ApiModelProperty(value = "收货人手机")
    private String receiverMobile;
    /**
     * 收货人电话
     */
    @ApiModelProperty(value = "收货人电话")
    private String receiverPhone;
    /**
     * 收货人邮箱
     */
    @ApiModelProperty(value = "收货人邮箱")
    private String receiverEmail;
    /**
     * 收件人证件类型(1-身份证、2-军官证、3-护照、4-其他)
     */
    @ApiModelProperty(value = "收件人证件类型(1-身份证、2-军官证、3-护照、4-其他)")
    private Integer receiverIdType;
    /**
     * 收货人证件号码
     */
    @ApiModelProperty(value = "收货人证件号码")
    private String receiverIdNumber;
    /**
     * 收货人国家
     */
    @ApiModelProperty(value = "收货人国家")
    private String receiverCountry;
    /**
     * 收货人省份
     */
    @ApiModelProperty(value = "收货人省份")
    private String receiverProvince;
    /**
     * 收货人城市
     */
    @ApiModelProperty(value = "收货人城市")
    private String receiverCity;
    /**
     * 收货人区/县城市
     */
    @ApiModelProperty(value = "收货人区/县城市")
    private String receiverCounty;
    /**
     * 收货人四级区域
     */
    @ApiModelProperty(value = "收货人四级区域")
    private String receiverArea;
    /**
     * 收货人国家code
     */
    @ApiModelProperty(value = "收货人国家code")
    private String receiverCountryCode;
    /**
     * 收货人省份code
     */
    @ApiModelProperty(value = "收货人省份code")
    private String receiverProvinceCode;
    /**
     * 收货人城市code
     */
    @ApiModelProperty(value = "收货人城市code")
    private String receiverCityCode;
    /**
     * 收货人区/县城市code
     */
    @ApiModelProperty(value = "收货人区/县城市code")
    private String receiverCountyCode;
    /**
     * 收货人四级区域code
     */
    @ApiModelProperty(value = "收货人四级区域code")
    private String receiverAreaCode;
    /**
     * 收货人详细地址
     */
    @ApiModelProperty(value = "收货人详细地址")
    private String receiverAddress;
    /**
     * 收货人姓名
     */
    @ApiModelProperty(value = "收货人姓名")
    private String receiverName;
    /**
     * 撤回原因
     */
    @ApiModelProperty(value = "撤回原因")
    private String reasons;
    /**
     * 异常原因
     */
    @ApiModelProperty(value = "异常原因")
    private String errorMessage;
    /**
     * 撤回时间
     */
    @ApiModelProperty(value = "撤回时间")
    private Date relinquishTime;
    /**
     * 发货时间
     */
    @ApiModelProperty(value = "发货时间")
    private Date deliveryTime;
    /**
     * 收货时间
     */
    @ApiModelProperty(value = "收货时间")
    private Date receiverTime;

    /**
     * 单据同步wms状态
     * 具体状态定义请查看
     * @see WmsSyncStatusVO
     * */
    private Integer syncWmsStatus;

    private String realWarehouseCode;

    private Date createTime;

    @ApiModelProperty(value = "sku数量及明细")
    private List<WarehouseRecordDetailDTO> warehouseRecordDetails;

    @ApiModelProperty(value = "包裹信息")
    private List<RecordPackageDTO> doPackages;

    @ApiModelProperty(value = "批次信息")
    private List<RwBatchDTO> batches;

    @ApiModelProperty(value = "wms入库单编码")
    private String entryOrder;

    @ApiModelProperty(value = "工厂编码")
    private String factoryCode;

    /**
     * 外部仓库地址编码
     */
    @ApiModelProperty(value = "仓库编码")
    private String realWarehouseOutCode;

    /**
     * sap单号
     */
    @ApiModelProperty(value = "sap单号")
    private String sapOrderCode;

    /**
     * tms派车单号
     */
    @ApiModelProperty(value = "tms派车单号")
    private String tmsRecordCode;

    @ApiModelProperty(value = "wms入库单状态")
    private String wmsStatus;

    @ApiModelProperty(value = "周转箱List")
    private List<TurnoverBoxDTO> turnoverBoxDTOS;

    /**
     * 订单完成时间
     */
    private String operateTime;

    private String appId;

    @ApiModelProperty(value = "附加参数-MM快递单号")
    private String remark;

    @ApiModelProperty(value = "查询支持冲销的单据类型")
    private List<Integer> recordTypes;

}
