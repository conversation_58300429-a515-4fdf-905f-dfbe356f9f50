package com.rome.stock.innerservice.api.dto.qry;

import com.rome.stock.innerservice.api.dto.Pagination;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;

@Data
@EqualsAndHashCode
public class ReturnCondition extends Pagination {
	
	@ApiModelProperty(value="售后单号")
    private String outRecordCode;

	@ApiModelProperty(value="预约单号")
	private String reservationNo;
	
	@ApiModelProperty(value="团购入库单号")
	private String recordCode;
	
	@ApiModelProperty(value="所属客户")
    private String customName;
	
	@ApiModelProperty(value="团购入库单状态")
	private Integer recordStatus;
	
	@ApiModelProperty(value="快递单号")
	private String expressNo;
	
	@ApiModelProperty(value = "开始时间")
	private Date startTime;
	
	@ApiModelProperty(value = "结束时间")
	private Date endTime;
	
}