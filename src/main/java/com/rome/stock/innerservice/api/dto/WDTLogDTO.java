package com.rome.stock.innerservice.api.dto;

import com.alibaba.fastjson.JSON;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * @Doc:.....
 * @Author: lchy
 * @Date: 2020/4/28
 * @Version 1.0
 */
@Data
@EqualsAndHashCode
public class WDTLogDTO {

    /**
     * 唯一主键
     */
    private Long id;
    /**
     * 所属单据编码
     */
    private String recordCode;
    private String originOrderCode;
    private String outRecordCode;
    /**
     * 类型 1：拆单  2:修改仓库 3、修改物流 4、仓库物流一起修改
     */
    private Integer type;

    private String beforeValue;
    private String afterValue;

    private List<String> beforeValues;
    private List<String> afterValues;
    private Long creator;
    private Date createTime;
    private Long modifier;



    /**
     * 添加修改项的前后值，对于beforeValue为空的就传null
     * @param beforeValue
     * @param afterValue
     */
    public void addChangedKeyValue(String beforeValue, String afterValue){
        addBeforeValues(beforeValue);
        addAfterValues(afterValue);
    }

    private void addBeforeValues(String beforeValue) {
        if (this.beforeValues == null) {
            this.beforeValues = new ArrayList<>();
        }
        this.beforeValues.add(beforeValue == null ? "null" : beforeValue);
        this.setBeforeValue(JSON.toJSONString(beforeValues));
    }

    private void addAfterValues(String afterValue) {
        if (this.afterValues == null) {
            this.afterValues = new ArrayList<>();
        }
        this.afterValues.add(afterValue);
        this.setAfterValue(JSON.toJSONString(afterValues));
    }
}
