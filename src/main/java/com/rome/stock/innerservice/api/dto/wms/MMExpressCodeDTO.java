package com.rome.stock.innerservice.api.dto.wms;

import com.rome.arch.core.dto.DTO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.ToString;

/**
 * <AUTHOR>
 * @Description 更新MM派车单号接口
 * @date 2020/8/22 13:57
 */
@Data
@ToString
public class MMExpressCodeDTO extends DTO {

    @ApiModelProperty(value = "出库需求单号")
    private String deliveryOrderCode;

    @ApiModelProperty(value = "派车单号")
    private String sendCarCode;

}    
   