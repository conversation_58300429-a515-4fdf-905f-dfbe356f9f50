package com.rome.stock.innerservice.api.dto.frontrecord;

import java.math.BigDecimal;

import javax.validation.constraints.Digits;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;

import com.fasterxml.jackson.annotation.JsonIgnore;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 类FrontReturnDetailDTO的实现描述：门店退货详情
 *
 * <AUTHOR> 2019/4/21 17:45
 */
@Data
@EqualsAndHashCode
public class ShopReturnDetailDTO {

    @ApiModelProperty(value = "数量")
    @NotNull(message="商品数量不能为空")
    @Digits(integer = 9, fraction = 3,message="超过范围,小数3位有效位，整数9位有效位")
    private BigDecimal skuQty;

    @ApiModelProperty(value = "skuId")
    @JsonIgnore
    private Long skuId;

    @ApiModelProperty(value = "sku名称")
    private String skuName;

    @ApiModelProperty(value = "sku编号")
    @NotBlank(message="sku编码为空")
    private String skuCode;

    @ApiModelProperty(value = "单位")
    @NotEmpty(message="sku单位不能为空")
    private String unit;

    @ApiModelProperty(value = "单位编码")
    @NotEmpty(message="单位code不能为空")
    private String unitCode;

    @ApiModelProperty(value = "退货原因")
    private String reason;

    @ApiModelProperty(value = "实际入库数量")
    private BigDecimal actualQty;

    @ApiModelProperty(value = "sap行号")
    private String lineNo;
}
