package com.rome.stock.innerservice.api.controller;

import java.util.List;
import java.util.Objects;

import com.rome.stock.common.constants.ResCode;
import com.rome.stock.innerservice.api.dto.*;
import com.rome.stock.innerservice.api.dto.frontrecord.RwStock;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.MediaType;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;

import com.github.pagehelper.PageInfo;
import com.rome.arch.core.clientobject.Response;
import com.rome.arch.core.exception.RomeException;
import com.rome.arch.util.controller.RomeController;
import com.rome.stock.core.api.dto.ChannelSalesStockDTO;
import com.rome.stock.innerservice.api.dto.frontrecord.SkuInfo;
import com.rome.stock.innerservice.api.dto.frontrecord.SkuStock;
import com.rome.stock.innerservice.api.dto.groupbuy.SkuStockForRw;
import com.rome.stock.innerservice.api.dto.supplier.QuerySupplierSkuStockDTO;
import com.rome.stock.innerservice.api.dto.supplier.SupplierSkuStockDTO;
import com.rome.stock.innerservice.common.CoreKibanaLog;
import com.rome.stock.innerservice.constant.ParamValidator;
import com.rome.stock.innerservice.constant.ResponseMsg;
import com.rome.stock.innerservice.domain.service.RealWarehouseService;
import com.rome.stock.innerservice.domain.service.StockQueryService;
import com.rome.stock.innerservice.remote.item.facade.SkuFacade;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import io.swagger.annotations.ApiResponse;
import lombok.extern.slf4j.Slf4j;

import javax.validation.Valid;

/**
 * 类StockQueryController的实现描述：通用库存查询接口
 *
 * <AUTHOR> 2021/4/23 16:07
 */
@Slf4j
@RomeController
@RequestMapping("/stock/v1")
@Api(tags={"通用库存查询接口"})
public class StockQueryController {

    @Autowired
    private SkuFacade skuFacade;

    @Autowired
    private StockQueryService stockQueryService;

    @Autowired
    private RealWarehouseService realWarehouseService;

    @Value("${max.stock.queryNum}")
    private Integer MAX_SIZE;

    private final static String LOGTYPE = "stockQuery";

    private ParamValidator validator = ParamValidator.INSTANCE;


    /**
     * 批量查询渠道可售库存
     * @param skuInfoList 需要查询的sku信息列表
     * @return
     */
    @ApiOperation(value = "批量查询渠道可售库存 最大支持100个批量查询，超过的直接截取", nickname = "queryStock", produces = MediaType.APPLICATION_JSON_VALUE)
    @ApiResponse(code = 200, message = "success", response = SkuStock.class)
    @PostMapping("/queryStock")
    public Response<List<SkuStock>> queryStock(@ApiParam(name = "skuInfoList", value = "sku集合") @RequestBody List<SkuInfo> skuInfoList) {
        if (! validator.validCollection(skuInfoList)) {
            return ResponseMsg.PARAM_ERROR.buildMsg();
        }
        log.warn(CoreKibanaLog.getJobLog(LOGTYPE, "queryStock", "获取库存, 商品总数" + skuInfoList.size(), ""));
        //最大支持100个批量查询，超过的直接截取
        if (skuInfoList.size() > MAX_SIZE) {
            skuInfoList = skuInfoList.subList(0, MAX_SIZE);
        }
        try {
            for (SkuInfo sku : skuInfoList) {
                if (sku.getMerchantId() == null) {
                    sku.setMerchantId(skuFacade.getDefaultMerchantId());
                }
            }
            List<SkuStock> skuStockList = stockQueryService.querySkuStockList(skuInfoList);
            return ResponseMsg.SUCCESS.buildMsg(skuStockList);
        } catch (RomeException e) {
            log.error(e.getMessage(), e);
            return ResponseMsg.FAIL.buildMsg(e.getCode(), e.getMessage());
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return ResponseMsg.EXCEPTION.buildMsg();
        }
    }

    /**
     * 批量查询渠道可售库存
     * @param skuInfoList 需要查询的sku信息列表
     * @return
     */
    @ApiOperation(value = "批量查询各仓库下商品库存 最大支持100个批量查询，超过的直接截取", nickname = "queryStockWithRwInfo", produces = MediaType.APPLICATION_JSON_VALUE)
    @ApiResponse(code = 200, message = "success", response = SkuStock.class)
    @PostMapping("/queryStockWithRwInfo")
    public Response<List<SkuStockForRw>> queryStockWithRwInfo( @ApiParam(name = "rwType", value = "仓库类型(17:电商仓 27: 跨境电商仓)") @RequestParam("rwType") Integer rwType, @ApiParam(name = "skuInfoList", value = "sku集合") @RequestBody List<BaseSkuInfoDTO> skuInfoList) {
        if (! validator.validCollection(skuInfoList)) {
            return ResponseMsg.PARAM_ERROR.buildMsg();
        }
        log.warn(CoreKibanaLog.getJobLog(LOGTYPE, "queryStockWithRwInfo", "获取仓库下商品库存, 商品总数" + skuInfoList.size(), ""));
        //最大支持100个批量查询，超过的直接截取
        if (skuInfoList.size() > MAX_SIZE) {
            skuInfoList = skuInfoList.subList(0, MAX_SIZE);
        }
        try {
            List<SkuStockForRw> skuStockList = stockQueryService.querySkuStockListByRwType(skuInfoList, rwType);
            return ResponseMsg.SUCCESS.buildMsg(skuStockList);
        } catch (RomeException e) {
            log.error(e.getMessage(), e);
            return ResponseMsg.FAIL.buildMsg(e.getCode(), e.getMessage());
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return ResponseMsg.EXCEPTION.buildMsg();
        }
    }

    /**
     * 批量查询渠道可售库存
     * @param skuInfoList 需要查询的sku信息列表
     * @return
     */
    @ApiOperation(value = "批量查询渠道可售库存，指定商家id", nickname = "queryStockWithMerchantId", produces = MediaType.APPLICATION_JSON_VALUE)
    @ApiResponse(code = 200, message = "success", response = SkuStock.class)
    @PostMapping("/queryStockWithMerchantId")
    public Response<List<SkuStock>> queryStock(@RequestParam Long merchantId , @ApiParam(name = "skuInfoList", value = "sku集合") @RequestBody List<SkuInfo> skuInfoList) {
        if (! validator.validCollection(skuInfoList)) {
            return ResponseMsg.PARAM_ERROR.buildMsg();
        }
        log.warn(CoreKibanaLog.getJobLog(LOGTYPE, "queryStockWithMerchantId", "指定商家获取库存, 商品总数" + skuInfoList.size(), ""));
        //最大支持100个批量查询，查过的直接截取
        if (skuInfoList.size() > MAX_SIZE) {
            skuInfoList = skuInfoList.subList(0, MAX_SIZE);
        }
        try {
            for (SkuInfo skuInfo : skuInfoList) {
                skuInfo.setMerchantId(merchantId);
            }
            List<SkuStock> skuStockList = stockQueryService.querySkuStockList(skuInfoList);
            return ResponseMsg.SUCCESS.buildMsg(skuStockList);
        } catch (RomeException e) {
            log.error(e.getMessage(), e);
            return ResponseMsg.FAIL.buildMsg(e.getCode(), e.getMessage());
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return ResponseMsg.EXCEPTION.buildMsg();
        }
    }

    @ApiOperation(value = "批量查询虚仓库存根据虚仓编码", nickname = "queryVwStockByVwCode",produces = MediaType.APPLICATION_JSON_VALUE)
    @ApiResponse(code = 200, message = "success", response = VirtualWarehouseStockDTO.class)
    @PostMapping("/queryVwStockByVwCode")
    public Response<List<VirtualWarehouseStockDTO>> queryVwStockByVwCode(@RequestBody QueryVirtualWarehouseStockDTO queryVirtualWarehouseStockDTO) {
        try {
            if (queryVirtualWarehouseStockDTO.getSkuIds().size() > MAX_SIZE) {
                queryVirtualWarehouseStockDTO.setSkuIds(queryVirtualWarehouseStockDTO.getSkuIds().subList(0,MAX_SIZE));
            }
            List<VirtualWarehouseStockDTO> list = stockQueryService.listStockBySkuIdsWarehouseId(queryVirtualWarehouseStockDTO);
            return ResponseMsg.SUCCESS.buildMsg(list);
        } catch (RomeException e) {
            log.error(e.getMessage(), e);
            return ResponseMsg.FAIL.buildMsg(e.getCode(), e.getMessage());
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return ResponseMsg.EXCEPTION.buildMsg();
        }
    }

    /**
     * 分页查询渠道下SKU可售库存
     */
    @ApiOperation(value = "分页查询渠道下SKU可售库存", nickname = "queryStockByChannelCode", produces = MediaType.APPLICATION_JSON_VALUE)
    @ApiResponse(code = 200, message = "success", response = String.class)
    @GetMapping("/queryStockByChannelCode/{channelCode}/{pageNum}")
    public Response<PageInfo<VWSkuStock>> queryStockByChannelCode(@PathVariable("channelCode") String channelCode, @PathVariable("pageNum") int pageNum) {
        if (! validator.validStr(channelCode)) {
            return ResponseMsg.PARAM_ERROR.buildMsg();
        }
        try {
            PageInfo<VWSkuStock> skuStockList = stockQueryService.queryStockByChannelCode(channelCode, pageNum);
            return ResponseMsg.SUCCESS.buildMsg(skuStockList);
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return ResponseMsg.EXCEPTION.buildMsg();
        }
    }

    /**
     * 分页查询渠道下SKU已售罄商品
     */
    @ApiOperation(value = "分页查询渠道下SKU已售罄商品", nickname = "queryStockByChannelCode", produces = MediaType.APPLICATION_JSON_VALUE)
    @ApiResponse(code = 200, message = "success", response = String.class)
    @GetMapping("/queryFloridianSkuByChannelCode/{channelCode}/{pageNum}")
    public Response<PageInfo<VWSkuStock>> queryFloridianSkuByChannelCode(@PathVariable("channelCode") String channelCode, @PathVariable("pageNum") int pageNum) {
        if (! validator.validStr(channelCode)) {
            return ResponseMsg.PARAM_ERROR.buildMsg();
        }
        try {
            PageInfo<VWSkuStock> skuStockList = stockQueryService.queryFloridianSkuByChannelCode(channelCode, pageNum);
            return ResponseMsg.SUCCESS.buildMsg(skuStockList);
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return ResponseMsg.EXCEPTION.buildMsg();
        }
    }


    @ApiOperation(value = "门店发货仓库存查询", nickname = "queryShipFactoryStock", produces = MediaType.APPLICATION_JSON_VALUE)
    @ApiResponse(code = 200, message = "success", response = SkuStock.class)
    @PostMapping("/shopShipFactory")
    public Response<List<ShopSkuStock>> queryShipFactoryStock(@Validated @RequestBody QueryShipFactoryStock queryShipFactoryStock ){
        try{
            List<ShopSkuStock> list = stockQueryService.queryShipFactoryStock(queryShipFactoryStock);
            return ResponseMsg.SUCCESS.buildMsg(list);
        }catch (RomeException e){
            log.error(e.getMessage(), e);
            return ResponseMsg.FAIL.buildMsg(e.getCode(), e.getMessage());
        } catch (Exception e){
            log.error(e.getMessage(), e);
            return ResponseMsg.EXCEPTION.buildMsg();
        }
    }


    @ApiOperation(value = "临时性检验查询", nickname = "queryShipFactoryStock", produces = MediaType.APPLICATION_JSON_VALUE)
    @ApiResponse(code = 200, message = "success", response = SkuStock.class)
    @PostMapping("/queryStockForTempIns")
    public Response<PageInfo<StockForTempInsPage>> queryStockForTempIns(@RequestBody QueryForTempInsDTO queryForTempIns){
        try{
            PageInfo<StockForTempInsPage> list = stockQueryService.queryStockForTempIns(queryForTempIns);
            return ResponseMsg.SUCCESS.buildMsg(list);
        }catch (RomeException e){
            log.error(e.getMessage(), e);
            return ResponseMsg.FAIL.buildMsg(e.getCode(), e.getMessage());
        } catch (Exception e){
            log.error(e.getMessage(), e);
            return ResponseMsg.EXCEPTION.buildMsg();
        }
    }


    @ApiOperation(value = "根据仓库和商品信息查询实仓库存", nickname = "queryRealStockBySkuInfo", produces = MediaType.APPLICATION_JSON_VALUE)
    @ApiResponse(code = 200, message = "success", response = SkuStock.class)
    @PostMapping("/queryRealStockBySkuInfo")
    public Response<List<SkuStock>> queryRealStockBySkuInfo(@Validated @RequestBody QueryRealStockDTO queryRealStockDTO){
        try{
            List<SkuStock> list = stockQueryService.queryRealStockBySkuInfo(queryRealStockDTO);
            return ResponseMsg.SUCCESS.buildMsg(list);
        }catch (RomeException e){
            log.error(e.getMessage(), e);
            return ResponseMsg.FAIL.buildMsg(e.getCode(), e.getMessage());
        }catch (Exception e){
            log.error(e.getMessage(), e);
            return ResponseMsg.EXCEPTION.buildMsg();
        }
    }

    @ApiOperation(value = "根据门店编号查询仓库ID", nickname = "queryRealWarehouseIdByShopCodes", produces = MediaType.APPLICATION_JSON_VALUE)
    @ApiResponse(code = 200, message = "success", response = SkuStock.class)
    @PostMapping("/queryRealWarehouseIdByShopCodes")
    public Response<List<Long>> queryRealWarehouseIdByShopCodes(@RequestBody List<String> shopCodes){
        try{
            List<Long> list = realWarehouseService.queryRealWarehouseIdByShopCodes(shopCodes);
            return ResponseMsg.SUCCESS.buildMsg(list);
        }catch (RomeException e){
            log.error(e.getMessage(), e);
            return ResponseMsg.FAIL.buildMsg(e.getCode(), e.getMessage());
        }catch (Exception e){
            log.error(e.getMessage(), e);
            return ResponseMsg.EXCEPTION.buildMsg();
        }
    }

    @ApiOperation(value = "根据门店编号查询仓库ID", nickname = "queryRealWarehouseIdByShopCodes", produces = MediaType.APPLICATION_JSON_VALUE)
    @ApiResponse(code = 200, message = "success", response = SkuStock.class)
    @PostMapping("/queryRealWarehouseByShopCodes")
    public Response<List<RealWarehouse>> queryRealWarehouseByShopCodes(@RequestBody List<String> shopCodes){
        try{
            List<RealWarehouse> list = realWarehouseService.queryRealWarehouseByShopCodes(shopCodes);
            return ResponseMsg.SUCCESS.buildMsg(list);
        }catch (RomeException e){
            log.error(e.getMessage(), e);
            return ResponseMsg.FAIL.buildMsg(e.getCode(), e.getMessage());
        }catch (Exception e){
            log.error(e.getMessage(), e);
            return ResponseMsg.EXCEPTION.buildMsg();
        }
    }

    /**
     * 供应商库存查询
     */
    @ApiOperation(value = "供应商库存查询", nickname = "querySupplierStockBySkuInfo", produces = MediaType.APPLICATION_JSON_VALUE)
    @ApiResponse(code = 200, message = "success", response = SupplierSkuStockDTO.class)
    @PostMapping("/querySupplierStockBySkuInfo")
    public Response<PageInfo<SupplierSkuStockDTO>> querySupplierStockBySkuInfo(@RequestBody QuerySupplierSkuStockDTO skuStockDTO) {
        if (! validator.validStr(skuStockDTO.getSupplierCode())) {
            return ResponseMsg.PARAM_ERROR.buildMsg();
        }
        try {
            PageInfo<SupplierSkuStockDTO> skuStockList = stockQueryService.querySupplierStockBySkuInfo(skuStockDTO);
            return ResponseMsg.SUCCESS.buildMsg(skuStockList);
        } catch (RomeException e){
            log.error(e.getMessage(), e);
            return ResponseMsg.FAIL.buildMsg(e.getCode(), e.getMessage());
        }catch (Exception e){
            log.error(e.getMessage(), e);
            return ResponseMsg.EXCEPTION.buildMsg();
        }
    }

    @ApiOperation(value = "根据仓库和商品信息查询加盟门店实时库存", nickname = "queryFranchiseeShopRealStock", produces = MediaType.APPLICATION_JSON_VALUE)
    @ApiResponse(code = 200, message = "success", response = RwStock.class)
    @PostMapping("/queryFranchiseeShopRealStock")
    public Response<PageInfo<RwStock>> queryFranchiseeShopRealStock(@Validated @RequestBody QueryRwStockDTO queryRwStockDTO){
        try{
            if (Objects.isNull(queryRwStockDTO.getPageIndex())){
                queryRwStockDTO.setPageIndex(1);
            }
            PageInfo<RwStock> pageInfo;
            //加盟商维度门店汇总统计
            if (queryRwStockDTO.isFranchiseeLevel()){
                pageInfo = stockQueryService.queryFranchiseeSumRealStock(queryRwStockDTO);
            }else {
                pageInfo = stockQueryService.queryFranchiseeShopRealStock(queryRwStockDTO);
            }
            return ResponseMsg.SUCCESS.buildMsg(pageInfo);
        }catch (RomeException e){
            log.error(e.getMessage(), e);
            return ResponseMsg.FAIL.buildMsg(e.getCode(), e.getMessage());
        }catch (Exception e){
            log.error(e.getMessage(), e);
            return ResponseMsg.EXCEPTION.buildMsg();
        }
    }

    @ApiOperation(value = "加盟商门店根据门店库存排序", nickname = "orderByFranchiseeShop", produces = MediaType.APPLICATION_JSON_VALUE)
    @ApiResponse(code = 200, message = "success")
    @PostMapping("/orderByFranchiseeShop")
    public Response<List<String>> orderByFranchiseeShop(@Validated @RequestBody QueryRwStockDTO queryRwStockDTO){
        try{
            return ResponseMsg.SUCCESS.buildMsg(stockQueryService.orderByFranchiseeShop(queryRwStockDTO));
        }catch (RomeException e){
            log.error(e.getMessage(), e);
            return ResponseMsg.FAIL.buildMsg(e.getCode(), e.getMessage());
        }catch (Exception e){
            log.error(e.getMessage(), e);
            return ResponseMsg.EXCEPTION.buildMsg();
        }
    }

    @ApiOperation(value = "加盟商维度实时库存汇总(库存金额)", nickname = "queryFranchiseeSumQtyAndAmount", produces = MediaType.APPLICATION_JSON_VALUE)
    @ApiResponse(code = 200, message = "success", response = RwStockSummary.class)
    @PostMapping("/queryFranchiseeSumQtyAndAmount")
    public Response<RwStockSummary> queryFranchiseeSumQtyAndAmount(@Validated @RequestBody QueryRwStockSummaryDTO stockDTO){
        try{
            return Response.builderSuccess(stockQueryService.queryFranchiseeSumQtyAndAmount(stockDTO));
        }catch (RomeException ex){
            log.error(ex.getMessage(),ex);
            return Response.builderFail(ex.getCode(), ex.getMessage());
        }catch (Exception ex){
            log.error(ex.getMessage(),ex);
            return Response.builderFail(ResCode.STOCK_ERROR_1003_DESC,ex.getMessage());
        }
    }

    /**
     * 【纯属测试人员用】根据skuId和渠道，批量查询渠道可售库存不含单位
     * @param skuInfoList 需要查询的sku信息列表
     * @return
     */
    @ApiOperation(value = "【纯属测试人员用】根据skuId和渠道，批量查询渠道可售库存不含单位 最大支持1000个批量查询，超过的直接截取", nickname = "testQueryStock", produces = MediaType.APPLICATION_JSON_VALUE)
    @ApiResponse(code = 200, message = "success", response = SkuStock.class)
    @PostMapping("/testQueryStock")
    public Response<List<ChannelSalesStockDTO>> testQueryStock(@ApiParam(name = "skuInfoList", value = "sku集合") @RequestBody List<SkuInfo> skuInfoList) {
        if (! validator.validCollection(skuInfoList)) {
            return ResponseMsg.PARAM_ERROR.buildMsg();
        }
        //最大支持1000个批量查询，超过的直接截取
        if (skuInfoList.size() > 1000) {
            skuInfoList = skuInfoList.subList(0, 1000);
        }
        try {
            List<ChannelSalesStockDTO> skuStockList = stockQueryService.testQuerySkuStockList(skuInfoList);
            return ResponseMsg.SUCCESS.buildMsg(skuStockList);
        } catch (RomeException e) {
            log.error(e.getMessage(), e);
            return ResponseMsg.FAIL.buildMsg(e.getCode(), e.getMessage());
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return ResponseMsg.EXCEPTION.buildMsg();
        }
    }


    @ApiOperation(value = "根据仓库+SKU，返回库存大于0的当前最差批次号", nickname = "querySkuMinBatchNo", produces = MediaType.APPLICATION_JSON_VALUE)
    @ApiResponse(code = 200, message = "success", response = SkuStock.class)
    @PostMapping("/querySkuMinBatchNo")
    public Response<List<MinBatchNoResultDTO>> querySkuMinBatchNo(@Validated @RequestBody QueryMinBatchNoDTO queryMinBatchNoDTO){
        try{
            List<MinBatchNoResultDTO> list = stockQueryService.querySkuMinBatchNo(queryMinBatchNoDTO);
            return ResponseMsg.SUCCESS.buildMsg(list);
        }catch (RomeException e){
            log.error(e.getMessage(), e);
            return ResponseMsg.FAIL.buildMsg(e.getCode(), e.getMessage());
        }catch (Exception e){
            log.error(e.getMessage(), e);
            return ResponseMsg.EXCEPTION.buildMsg();
        }
    }


    @ApiOperation(value = "查询 实仓库存、虚仓 物料分页(不传递分页参数时，查询全部)", nickname = "queryWarehouseSkuCodePage", produces = MediaType.APPLICATION_JSON_VALUE)
    @ApiResponse(code = 200, message = "success", response = SkuStock.class)
    @PostMapping("/queryWarehouseSkuCodePage")
    public  Response<PageInfo<String>> queryWarehouseSkuCodePage(@RequestBody StockSkuDTO param){
        try{
            PageInfo<String> pageInfo = stockQueryService.queryWarehouseSkuCodePage(param);
            return ResponseMsg.SUCCESS.buildMsg(pageInfo);
        }catch (RomeException e){
            log.error(e.getMessage(), e);
            return ResponseMsg.FAIL.buildMsg(e.getCode(), e.getMessage());
        }catch (Exception e){
            log.error(e.getMessage(), e);
            return ResponseMsg.EXCEPTION.buildMsg();
        }
    }


    @ApiOperation(value = "查询 实仓库存、虚仓 物料分页(不传递分页参数时，查询全部)", nickname = "queryDistinctBatchSkuCodeByPage", produces = MediaType.APPLICATION_JSON_VALUE)
    @ApiResponse(code = 200, message = "success", response = SkuStock.class)
    @PostMapping("/queryDistinctBatchSkuCodeByPage")
    public  Response<PageInfo<String>> queryDistinctBatchSkuCodeByPage(@RequestBody StockSkuDTO param){
        try{
            PageInfo<String> pageInfo = stockQueryService.queryDistinctBatchSkuCodeByPage(param);
            return ResponseMsg.SUCCESS.buildMsg(pageInfo);
        }catch (RomeException e){
            log.error(e.getMessage(), e);
            return ResponseMsg.FAIL.buildMsg(e.getCode(), e.getMessage());
        }catch (Exception e){
            log.error(e.getMessage(), e);
            return ResponseMsg.EXCEPTION.buildMsg(e.getMessage());
        }
    }

    @ApiOperation(value = "查询门店库存信息（陈列系统调用）", notes = "通过门店编号查询库存数据，分页返回", produces = MediaType.APPLICATION_JSON_VALUE)
    @ApiResponse(code = 200, message = "success", response = QueryForDisplayDTO.class)
    @PostMapping("/queryShopStockForDisplay")
    public Response<PageInfo<QueryForDisplayDTO>> queryShopStockForDisplay(@Valid @RequestBody QueryDisplayDTO queryDisplayDTO){
        try{
            PageInfo<QueryForDisplayDTO> pageInfo=stockQueryService.queryShopStockForDisplay(queryDisplayDTO);
            return Response.builderSuccess(pageInfo);
        }catch (RomeException ex){
            log.error(ex.getMessage(),ex);
            return Response.builderFail(ex.getCode(), ex.getMessage());
        }catch (Exception ex){
            log.error(ex.getMessage(),ex);
            return ResponseMsg.EXCEPTION.buildMsg(ex.getMessage());
        }
    }
}
