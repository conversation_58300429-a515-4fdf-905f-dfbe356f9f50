/**
 * Filename BigdataController.java
 * Company 上海来伊份科技有限公司。
 * <AUTHOR>
 * @version 
 */
package com.rome.stock.innerservice.api.controller;

import com.rome.arch.core.clientobject.Response;
import com.rome.arch.core.exception.RomeException;
import com.rome.arch.util.controller.RomeController;
import com.rome.stock.innerservice.common.ResCode;
import com.rome.stock.innerservice.domain.service.VmAndRealStockSnapshotService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiResponse;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;

import javax.annotation.Resource;

/**
 * 虚仓库存和实仓库存快照接口
 * <AUTHOR>
 */
@Slf4j
@RomeController
@RequestMapping("/stock/v1/stockSnapshot")
@Api(tags={"虚仓库存和实仓库存快照接口"})
public class VmAndRealStockSnapshotController {

    @Resource
    private VmAndRealStockSnapshotService vmAndRealStockSnapshotService;

    @ApiOperation(value = "异步处理虚仓和实仓库存快照", nickname = "handleVmAndRealStockSnapshot", produces = MediaType.APPLICATION_JSON_VALUE)
    @ApiResponse(code = 200, message = "success")
    @RequestMapping(value = "/syncHandleVmAndRealStockSnapshot", method = RequestMethod.POST)
    public Response syncHandleVmAndRealStockSnapshot(@RequestParam("snapshotDate") String snapshotDate){
        try {
            vmAndRealStockSnapshotService.syncHandleVmAndRealStockSnapshot(snapshotDate);
            return Response.builderSuccess("");
        } catch (RomeException e) {
            log.error(e.getMessage(), e);
            return Response.builderFail(e.getCode(), e.getMessage());
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return Response.builderFail(ResCode.STOCK_ERROR_1003, ResCode.STOCK_ERROR_1003_DESC + ","+ e.getMessage());
        }
    }



    @ApiOperation(value = "同步处理虚仓和实仓库存快照", nickname = "handleVmAndRealStockSnapshot", produces = MediaType.APPLICATION_JSON_VALUE)
    @ApiResponse(code = 200, message = "success")
    @RequestMapping(value = "/handleVmAndRealStockSnapshot", method = RequestMethod.POST)
    public Response handleVmAndRealStockSnapshot(@RequestParam("snapshotDate") String snapshotDate){
        try {
            vmAndRealStockSnapshotService.handleVmAndRealStockSnapshot(snapshotDate);
            return Response.builderSuccess("");
        } catch (RomeException e) {
            log.error(e.getMessage(), e);
            return Response.builderFail(e.getCode(), e.getMessage());
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return Response.builderFail(ResCode.STOCK_ERROR_1003, ResCode.STOCK_ERROR_1003_DESC + ","+ e.getMessage());
        }
    }
}
