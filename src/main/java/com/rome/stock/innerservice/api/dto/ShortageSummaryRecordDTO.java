package com.rome.stock.innerservice.api.dto;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

import io.swagger.annotations.ApiModelProperty;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Component;

import com.rome.stock.innerservice.domain.entity.SkuQtyUnitBaseE;

import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * @Doc:.....
 * @Author: lchy
 * @Date: 2020/6/3
 * @Version 1.0
 */
@Component
@Scope("prototype")
@Data
@EqualsAndHashCode(callSuper = false)
public class ShortageSummaryRecordDTO  extends SkuQtyUnitBaseE {
    private Long skuId;
    private String skuCode;
    private BigDecimal skuQty;
    private BigDecimal basicSkuQty;
    private String unitCode;


    private Long realWarehouseId;
    private String realWarehouseCode;
    private String realWarehouseName;
    private Long virtualWarehouseId;
    private String virtualWarehouseCode;
    private String virtualWarehouseName;

    private String doCode;
    private String recordCode;
    private String frontRecordCode;
    private String outRecordCode;
    private String originOrderCode;

    private Integer recordType;
    private String recordTypeDesc;

    private Date createTime;
    private Long frontRecordId;

    /**
     * 业务单号
     */
    private String sapOrderCode;
    
    private Long warehouseId;
    
    /**
     * 商品名称
     */
    private String skuName;
    
    /**
     * 工厂编码
     */
    private String factoryCode;
    
    /**
     * 占用类型描述
     */
    private String subTypeDesc;

    /**
     * 关联单据编码
     */
    @ApiModelProperty(value = "关联单据编码")
    private List<String> relationRecordCodeList;

    /**
     * 关联单据
     */
    @ApiModelProperty(value = "关联单据")
    private List<RecordRelationResultDTO> recordRelationList;

    @ApiModelProperty(value = "渠道编号")
    private String channelCode;

}
