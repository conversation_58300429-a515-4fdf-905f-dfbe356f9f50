package com.rome.stock.innerservice.api.dto;

import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * @Doc:.....
 * @Author: lchy
 * @Date: 2020/4/28
 * @Version 1.0
 */
@Data
@EqualsAndHashCode
public class WDTPageParamDTO  extends  Pagination implements Serializable {
    private String originOrderCode;
    private String outRecordCode;
    private List<String> originOrderCodeList;
    private List<String> outRecordCodeList;
    private String channelCode;
    private List<String> channelCodeList;
    private Long realWarehouseId;
    private Integer recordStatus;
    private Date startTime;
    private Date endTime ;
    private Integer splitType;
    private List<Integer> splitTypeList;
    private Integer allotStatus;
    private String logisticsCode;
    private String provinceCode;
    private List<String> provinceCodes;

    private String skuCodeStr;
    private List<String> skuCodes ;

    private List<Long> frontRecordIds;
    private String warehouseCodeStr;
    private Integer parentSku;

    private Integer skuFlag;
    private List<Integer> isPreSaleList;
    private List<Integer> recordStatusList;
    private List<Integer> recordTypeList;
    private Integer changeBucketFlag;


    /**
     * 如果查询只是为了拆单用，那么就可以去掉很多无关紧要的查询
     */
    private Boolean isForMaxSplit = false ;
}
