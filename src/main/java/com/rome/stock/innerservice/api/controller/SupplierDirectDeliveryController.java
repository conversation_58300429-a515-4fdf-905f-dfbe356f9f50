package com.rome.stock.innerservice.api.controller;

import javax.annotation.Resource;

import com.rome.stock.innerservice.api.dto.frontrecord.PurchaseOrderOldDTO;
import org.springframework.http.MediaType;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import com.rome.arch.core.clientobject.Response;
import com.rome.arch.core.exception.RomeException;
import com.rome.stock.innerservice.api.dto.frontrecord.PurchaseOrderDTO;
import com.rome.stock.innerservice.constant.ResponseMsg;
import com.rome.stock.innerservice.domain.service.SupplierDirectDeliveryService;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import io.swagger.annotations.ApiResponse;
import lombok.extern.slf4j.Slf4j;

/**
 * 采购到大仓
 */
@Slf4j
@RestController
@RequestMapping("/stock/v1/supplierDirectDelivery")
@Api(tags={"门店直送"})
public class SupplierDirectDeliveryController {
	@Resource
	private SupplierDirectDeliveryService supplierDirectDeliveryService;


	@ApiOperation(value = "接受直送大仓采购单", nickname = "addPurchaseNoticeRecord", produces = MediaType.APPLICATION_JSON_VALUE)
	@ApiResponse(code = 200, message = "success")
	@RequestMapping(value = "/addPurchaseNoticeRecord", method = RequestMethod.POST)
	public Response addPurchaseNoticeRecord(@ApiParam(name = "purchaseOrder", value = "dto") @RequestBody @Validated PurchaseOrderOldDTO purchaseOrder) {
		if (purchaseOrder.getPurchaseRecordType() != 6 || purchaseOrder.getSapPoNo() == null) {
			//purchaseRecordType =6 表示供应商直送,该接口只接受这种
			//直送采购单必须传关联的直送门店补货单单号
			return ResponseMsg.PARAM_ERROR.buildMsg();
		}
		try {
			supplierDirectDeliveryService.addPurchaseNoticeRecord(purchaseOrder);
			return ResponseMsg.SUCCESS.buildMsg();
		} catch (RomeException e) {
			log.error(e.getMessage(), e);
			return ResponseMsg.FAIL.buildMsg(e.getCode(), e.getMessage());
		} catch (Exception e) {
			log.error(e.getMessage(), e);
			return ResponseMsg.EXCEPTION.buildMsg();
		}
	}

}
