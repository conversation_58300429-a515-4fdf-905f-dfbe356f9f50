package com.rome.stock.innerservice.api.dto.warehouserecord;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotBlank;
import java.util.List;

/**
 * DeleteDetailDTO
 */
@Data
@EqualsAndHashCode
public class DeleteDetailDTO {

    @ApiModelProperty(value = "单据编码", required = true)
    @NotBlank(message="单据编码不能为空")
    private String recordCode;

    @ApiModelProperty(value = "物料编号集合")
    private List<String> skuCodes;


}
