package com.rome.stock.innerservice.api.dto.cmp7;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @description 门店收货对接cmp7主表对象
 * @date 2020/11/12 9:53
 * @throw
 */
@Data
public class ShopReceiptCmp7DTO {

    @ApiModelProperty(value = "请求时间戳")
    private Long timestamp;

    @ApiModelProperty(value = "客户端签名")
    private String sign;

    /**
     * 门店收货单明细
     */
    private List<ShopReceiptDetailCmp7DTO> data;
}    
   