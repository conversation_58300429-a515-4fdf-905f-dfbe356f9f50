package com.rome.stock.innerservice.api.dto.xuantian.virtual;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 外卖销售发货单据表
 * 
 * <AUTHOR>
 * @email 
 * @date 2019-06-25 22:58:29
 */
@ApiModel("销售发货单DTO")
@Data
public class VirtualDeliveryOrderDTO implements Serializable {
	
	private static final long serialVersionUID = 3146727856833809430L;

	@ApiModelProperty("订单编码")
	private String orderNo;


	@ApiModelProperty("渠道code")
	private String channelCode;

	/**
	 * 支付时间
	 */
	@ApiModelProperty("支付时间")
	private Date payTime;
	

	@ApiModelProperty("订单实际支付金额")
	private Float orderAmount;
	
	/**
	 * 购买人id
	 */
	private Long buyerId;

	/**
	 * 出库单号
	 */
	@ApiModelProperty("出库单号")
	private String warehouseRecordCode;

	private Integer syncTransferStatus;



}
