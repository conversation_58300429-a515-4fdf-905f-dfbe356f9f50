/**
 * Filename RealWarehouseStockDo.java
 * Company 上海来伊份科技有限公司。
 * <AUTHOR>
 * @version 
 */
package com.rome.stock.innerservice.api.dto;

import java.math.BigDecimal;
import java.util.List;

import com.rome.stock.innerservice.api.dto.allocation.WhSkuUnitDTO;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotBlank;

/**
 * 类BaseDo的实现描述：真实仓库存Do表
 * <AUTHOR>
 * @since 2019年4月21日 上午10:06:24
 */
@Data
@NoArgsConstructor
public class RealWarehouseStockDTO extends Pagination {

    /**
     * 唯一主键
     */
    private Long id;

    /**
     * 实仓ID
     */
    private Long realWarehouseId;

    @ApiModelProperty(value = "工厂编号")
    @NotBlank(message="工厂编号不能为空")
    private String factoryCode;

    @ApiModelProperty(value = "外部仓库编号")
    @NotBlank(message="外部仓库编号不能为空")
    private String realWarehouseOutCode;

    /**
     * 商品sku编码
     */
    private Long skuId;

    /**
     * 真实库存
     */
    private BigDecimal realQty;

    /**
     * 锁定库存
     */
    private BigDecimal lockQty;

    /**
     * 在途库存
     */
    private BigDecimal onroadQty;

    /**
     * 质检库存
     */
    private BigDecimal qualityQty;

    /**
     * 不合格库存，注：一般是质检不合格库存
     */
    private BigDecimal unqualifiedQty;

    /**
     * 可用库存
     */
    private BigDecimal availableQty;
    /**
     * 商家id
     */
    private Long merchantId;

    /** sku编号 */
    private String skuCode;

    /** sku名称 */
    private String skuName;

    /** 类目名称 */
    private String categoryName;

    /**
     * sku的单位信息
     */
    private List<WhSkuUnitDTO> skuUnitList;

    /**
     * sku的单位信息
     */
    private List<WhSkuUnitDTO> allSkuUnitList;

    /**
     * 基本数量
     */
    private Long baseNum;

    /**
     * 基本数量
     */
    private String baseUnit;
    /** 批量sku编号字符串 */
    private String skuCodes;
    /** 批量sku编号集合 */
    private List<String> skuCodeList;

    /**
     * 是否质量问题调拨 1.是 0 不是
     */
    private Integer isQualityAllotcate;


    private Integer pageNo;

    private Integer rows;

    @ApiModelProperty(value = "虚拟仓库ID")
    private Long virtualWarehouseId;

    @ApiModelProperty(value = "最大效期 分子,要求批次有效期需传,否则为null")
    private Integer molecule;

    @ApiModelProperty(value = "最大效期 分母,要求批次有效期需传,否则为null")
    private Integer denominator;




    @ApiModelProperty(value = "最小效期 分子,要求批次有效期需传,否则为null")
    private Integer lowerMolecule;

    @ApiModelProperty(value = "最小效期 分母,要求批次有效期需传,否则为null")
    private Integer lowerDenominator;


    @ApiModelProperty(value = "冗余天数")
    private Integer transDay;



    @ApiModelProperty(value = "sku明细查询")
    private List<BatchStockPeriodQueryDTO> batchStockQueryDTOList;

}
