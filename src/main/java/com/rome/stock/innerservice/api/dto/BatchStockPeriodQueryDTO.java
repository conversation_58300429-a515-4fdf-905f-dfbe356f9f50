package com.rome.stock.innerservice.api.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @Description
 * <AUTHOR>
 * @Date 2023-09-08
 * @Version 1.0
 */
@Data
public class BatchStockPeriodQueryDTO {



    @ApiModelProperty(value = "最大效期 分子,要求批次有效期需传,否则为null")
    private Integer molecule;

    @ApiModelProperty(value = "最大效期 分母,要求批次有效期需传,否则为null")
    private Integer denominator;




    @ApiModelProperty(value = "最小效期 分子,要求批次有效期需传,否则为null")
    private Integer lowerMolecule;

    @ApiModelProperty(value = "最小效期 分母,要求批次有效期需传,否则为null")
    private Integer lowerDenominator;


    @ApiModelProperty(value = "冗余天数")
    private Integer transDay;


    @ApiModelProperty(value = "商品编码")
    private String skuCode;

}
