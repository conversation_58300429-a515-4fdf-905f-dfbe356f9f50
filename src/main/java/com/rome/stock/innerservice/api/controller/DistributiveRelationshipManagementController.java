package com.rome.stock.innerservice.api.controller;

import com.github.pagehelper.PageInfo;
import com.rome.arch.core.clientobject.Response;
import com.rome.arch.core.exception.RomeException;
import com.rome.arch.util.controller.RomeController;
import com.rome.stock.innerservice.api.dto.DistributiveRelationship;
import com.rome.stock.innerservice.api.dto.DistributiveRelationshipEchart;
import com.rome.stock.innerservice.common.ResCode;
import com.rome.stock.innerservice.constant.ResponseMsg;
import com.rome.stock.innerservice.domain.service.DistributiveRelationshipManagementService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import io.swagger.annotations.ApiResponse;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;

import javax.annotation.Resource;
import java.util.List;

/**
 * 分配关系管理
 */
@Slf4j
@RomeController
@RequestMapping("/stock/v1/distributive_relationship")
@Api(tags={"分配关系管理"})
public class DistributiveRelationshipManagementController {
    @Resource
    private DistributiveRelationshipManagementService distributiveRelationshipManagementService;
    @ApiOperation(value = "根据分配关系类型查询相关列表信息", nickname = "getDistributiveRelationshipByType",produces = MediaType.APPLICATION_JSON_VALUE)
    @ApiResponse(code = 200, message = "success", response = List.class)
    @RequestMapping(value = "/getDistributiveRelationshipByType", method = RequestMethod.POST)
    public Response<PageInfo<DistributiveRelationship>> getDistributiveRelationshipByType(@ApiParam(name = "distributiveRelationship", value = "分配关系参数") @RequestBody DistributiveRelationship distributiveRelationship) {
        try {
            PageInfo<DistributiveRelationship> pageList = distributiveRelationshipManagementService.getDistributiveRelationshipByType(distributiveRelationship);
            return Response.builderSuccess(pageList);
        } catch (RomeException e) {
            log.error(e.getMessage(), e);
            return ResponseMsg.EXCEPTION.buildMsg(e.getCode(), e.getMessage());
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return ResponseMsg.EXCEPTION.buildMsg(ResCode.STOCK_ERROR_1003, ResCode.STOCK_ERROR_1003_DESC);
        }
    }

    @ApiOperation(value = "查询所有策略组信息", nickname = "getVirtualWarehouseGroupInfo",produces = MediaType.APPLICATION_JSON_VALUE)
    @ApiResponse(code = 200, message = "success", response = List.class)
    @RequestMapping(value = "/getVirtualWarehouseGroupInfo", method = RequestMethod.GET)
    public Response<List<DistributiveRelationship>> getVirtualWarehouseGroupInfo() {
        try {
            List<DistributiveRelationship> pageList = distributiveRelationshipManagementService.getVirtualWarehouseGroupInfo();
            return Response.builderSuccess(pageList);
        } catch (RomeException e) {
            log.error(e.getMessage(), e);
            return ResponseMsg.EXCEPTION.buildMsg(e.getCode(), e.getMessage());
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return ResponseMsg.EXCEPTION.buildMsg(ResCode.STOCK_ERROR_1003, ResCode.STOCK_ERROR_1003_DESC);
        }
    }


    @ApiOperation(value = "根据策略组查询相关库存模型信息", nickname = "getDistributiveRelationshipEchart",produces = MediaType.APPLICATION_JSON_VALUE)
    @ApiResponse(code = 200, message = "success", response = List.class)
    @RequestMapping(value = "/getDistributiveRelationshipEchart", method = RequestMethod.POST)
    public Response<DistributiveRelationshipEchart> getDistributiveRelationshipEchart(@ApiParam(name = "distributiveRelationship", value = "分配关系参数") @RequestBody DistributiveRelationship distributiveRelationship) {
        try {
            DistributiveRelationshipEchart distributiveRelationshipEchart = distributiveRelationshipManagementService.getDistributiveRelationshipEchart(distributiveRelationship);
            return Response.builderSuccess(distributiveRelationshipEchart);
        } catch (RomeException e) {
            log.error(e.getMessage(), e);
            return ResponseMsg.EXCEPTION.buildMsg(e.getCode(), e.getMessage());
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return ResponseMsg.EXCEPTION.buildMsg(ResCode.STOCK_ERROR_1003, ResCode.STOCK_ERROR_1003_DESC);
        }
    }


    @ApiOperation(value = "根据条件查询策略组信息", nickname = "getVirtualWarehouseGroupList",produces = MediaType.APPLICATION_JSON_VALUE)
    @ApiResponse(code = 200, message = "success", response = List.class)
    @RequestMapping(value = "/getVirtualWarehouseGroupList", method = RequestMethod.POST)
    public Response<List<DistributiveRelationship>> getVirtualWarehouseGroupList(@ApiParam(name = "distributiveRelationship", value = "分配关系参数") @RequestBody DistributiveRelationship distributiveRelationship){
        try {
            List<DistributiveRelationship> virtualWarehouseGroupList = distributiveRelationshipManagementService.getVirtualWarehouseGroupList(distributiveRelationship);
            return Response.builderSuccess(virtualWarehouseGroupList);
        } catch (RomeException e) {
            log.error(e.getMessage(), e);
            return ResponseMsg.EXCEPTION.buildMsg(e.getCode(), e.getMessage());
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return ResponseMsg.EXCEPTION.buildMsg(ResCode.STOCK_ERROR_1003, ResCode.STOCK_ERROR_1003_DESC);
        }
    }


    @ApiOperation(value = "根据策略组查询相关明细", nickname = "getDistributiveRelationshipDetail",produces = MediaType.APPLICATION_JSON_VALUE)
    @ApiResponse(code = 200, message = "success", response = List.class)
    @RequestMapping(value = "/getDistributiveRelationshipDetail", method = RequestMethod.POST)
    public Response<List<DistributiveRelationship>> getDistributiveRelationshipDetail(@ApiParam(name = "distributiveRelationship", value = "分配关系参数") @RequestBody DistributiveRelationship distributiveRelationship) {
        try {
            List<DistributiveRelationship> pageList = distributiveRelationshipManagementService.getDistributiveRelationshipDetail(distributiveRelationship);
            return Response.builderSuccess(pageList);
        } catch (RomeException e) {
            log.error(e.getMessage(), e);
            return ResponseMsg.EXCEPTION.buildMsg(e.getCode(), e.getMessage());
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return ResponseMsg.EXCEPTION.buildMsg(ResCode.STOCK_ERROR_1003, ResCode.STOCK_ERROR_1003_DESC);
        }
    }
}
