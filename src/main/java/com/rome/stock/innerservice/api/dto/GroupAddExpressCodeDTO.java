package com.rome.stock.innerservice.api.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 添加运单号DTO
 */
@Data
@EqualsAndHashCode
public class GroupAddExpressCodeDTO {

    @ApiModelProperty(value = "出库单号", required = true)
    private String warehouseCode;

    @ApiModelProperty(value = "运单号", required = true)
    private String expressCode;

}
