package com.rome.stock.innerservice.api.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import java.util.Date;

@Data
@NoArgsConstructor
@EqualsAndHashCode
@ApiModel(value = "附件信息", description = "附件信息")
public class FileInfo {

    @ApiModelProperty(value = "附件名称")
    private String fileName;

    @ApiModelProperty(value = "附件路径地址")
    private String fileUrl;

    @ApiModelProperty(value = "创建时间")
    private Date createTime;
}
