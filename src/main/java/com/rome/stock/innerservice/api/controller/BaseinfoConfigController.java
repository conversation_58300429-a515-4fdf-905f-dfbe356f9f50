/**
 * Filename BaseinfoConfigController.java
 * Company 上海来伊份科技有限公司。
 * <AUTHOR>
 * @version 
 */
package com.rome.stock.innerservice.api.controller;

import java.util.List;

import com.alibaba.fastjson.JSON;
import com.github.pagehelper.PageInfo;
import com.rome.stock.innerservice.api.dto.BaseinfoConfig;
import com.rome.stock.innerservice.api.dto.BaseinfoPropertyDTO;
import com.rome.stock.innerservice.api.dto.ShopBatchConfigDTO;
import com.rome.stock.innerservice.common.StringUtils;
import com.rome.stock.innerservice.domain.repository.SapInterfaceLogRepository;
import com.rome.stock.innerservice.domain.service.BaseinfoConfigService;
import io.swagger.annotations.ApiParam;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import com.rome.arch.core.clientobject.Response;
import com.rome.arch.core.exception.RomeException;
import com.rome.arch.util.controller.RomeController;
import com.rome.stock.innerservice.common.ResCode;
import com.rome.stock.innerservice.constant.ResponseMsg;
import com.rome.stock.wms.config.BaseinfoConfiguration;
import com.rome.stock.wms.config.BaseinfoProperty;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiResponse;
import lombok.extern.slf4j.Slf4j;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @since 2020-11-24 19:00:02
 */
@Slf4j
@RomeController
@RequestMapping("/stock/v1/baseinfo")
@Api(tags={"基础配置服务接口"})
public class BaseinfoConfigController {

	@Resource
	private BaseinfoConfigService baseinfoConfigService;

	@Autowired
	private SapInterfaceLogRepository sapInterfaceLogRepository;


	@ApiOperation(value = "查询配置列表，根据根key", nickname = "getDataListByRootkey", produces = MediaType.APPLICATION_JSON_VALUE)
	@ApiResponse(code = 200, message = "success")
	@RequestMapping(value = "/getDataListByRootkey", method = RequestMethod.GET)
	public Response<List<BaseinfoProperty>> getDataListByRootkey(@RequestParam("rootKey") String rootKey) {
		try {
			List<BaseinfoProperty> list = BaseinfoConfiguration.getInstance().getList(rootKey);
			return Response.builderSuccess(list);
		} catch (RomeException e) {
			log.error(e.getMessage(), e);
			return ResponseMsg.EXCEPTION.buildMsg(e.getCode(), e.getMessage());
		} catch (Exception e) {
			log.error(e.getMessage(), e);
			return ResponseMsg.EXCEPTION.buildMsg("500000", e.getMessage());
		}
	}

	@ApiOperation(value = "查询配置，根据根key和key", nickname = "getDataByKey", produces = MediaType.APPLICATION_JSON_VALUE)
	@ApiResponse(code = 200, message = "success")
	@RequestMapping(value = "/getDataByKey", method = RequestMethod.GET)
	public Response<BaseinfoProperty> getDataByKey(@RequestParam("rootKey") String rootKey, @RequestParam("key") String key) {
		try {
			BaseinfoProperty data = BaseinfoConfiguration.getInstance().getObject(rootKey, key);
			return Response.builderSuccess(data);
		} catch (RomeException e) {
			log.error(e.getMessage(), e);
			return ResponseMsg.EXCEPTION.buildMsg(e.getCode(), e.getMessage());
		} catch (Exception e) {
			log.error(e.getMessage(), e);
			return ResponseMsg.EXCEPTION.buildMsg("500000", e.getMessage());
		}
	}

	@ApiOperation(value = "查询基本信息配置", nickname = "getBaseinfoConfig", produces = MediaType.APPLICATION_JSON_VALUE)
	@ApiResponse(code = 200, message = "success")
	@RequestMapping(value = "/getBaseinfoConfig", method = RequestMethod.POST)
	public Response<PageInfo<BaseinfoConfig>> getBaseinfoConfig(@ApiParam(name = "BaseinfoConfig", value = "基本信息配置") @RequestBody BaseinfoConfig baseinfoConfig) {
		try {
			PageInfo<BaseinfoConfig> pageList = baseinfoConfigService.getBaseinfoConfig(baseinfoConfig);
			return Response.builderSuccess(pageList);
		} catch (RomeException e) {
			log.error(e.getMessage(), e);
			return ResponseMsg.EXCEPTION.buildMsg(e.getCode(), e.getMessage());
		} catch (Exception e) {
			log.error(e.getMessage(), e);
			return ResponseMsg.EXCEPTION.buildMsg(ResCode.STOCK_ERROR_1003, ResCode.STOCK_ERROR_1003_DESC);
		}
	}


	@ApiOperation(value = "更新或者插入数据，当id为空时插入，否则为更新，更新时versionNo不能为空", nickname = "updateOrInsert", produces = MediaType.APPLICATION_JSON_VALUE)
	@ApiResponse(code = 200, message = "success")
	@RequestMapping(value = "/updateOrInsertInfo", method = RequestMethod.POST)
	public Response updateOrInsertInfo(@RequestBody BaseinfoPropertyDTO info) {
		boolean flag = false;
		int result = 0;
		// 更新时，变化前值
		String beforeInfo = "";
		String errorMsg = "失败";
		try {
			List<BaseinfoProperty> list = BaseinfoConfiguration.getInstance().getListByNoCache(info.getRootKey());
			if(CollectionUtils.isNotEmpty(list)){
				BaseinfoProperty data=null;
				for (BaseinfoProperty baseinfoProperty: list) {
					if(baseinfoProperty.getId().equals(info.getId())){
						data=baseinfoProperty;
						break;
					}
				}
				if(data!=null){
					//进行更新
					String ext = data.getExt();
					if(StringUtils.isNotBlank(ext)&&ext.equals(info.getExt())){
						info.setId(data.getId());
					}
					info.setVersionNo(data.getVersionNo());
					beforeInfo = JSON.toJSONString(data);
				}
			}
			result = BaseinfoConfiguration.getInstance().updateOrInsert(info);
			if (result == 1) {
				flag = true;
				return ResponseMsg.SUCCESS.buildMsg("成功");
			} else {
				return ResponseMsg.FAIL.buildMsg("失败");
			}
		} catch (RomeException e) {
			errorMsg = e.getMessage();
			log.error(e.getMessage(), e);
			return ResponseMsg.EXCEPTION.buildMsg(ResCode.STOCK_ERROR_1003,e.getMessage());
		} catch (Exception e) {
			errorMsg = e.getMessage();
			log.error(e.getMessage(), e);
			return ResponseMsg.EXCEPTION.buildMsg(ResCode.STOCK_ERROR_1003, ResCode.STOCK_ERROR_1003_DESC);
		} finally {
			//记录日志信息
			String dataStr = JSON.toJSONString(info);
			//成功的 不保存返回值，返回值太大 保存不下
			sapInterfaceLogRepository.saveSapInterFaceLog(info.getRootKey(), "/stock/v1/baseinfo/updateOrInsertInfo", "updateOrInsertInfo",
					dataStr, flag ? beforeInfo : errorMsg, flag);
		}
	}


	@ApiOperation(value = "更新或者插入数据，当id为空时插入，否则为更新，更新时versionNo不能为空", nickname = "updateOrInsert", produces = MediaType.APPLICATION_JSON_VALUE)
	@ApiResponse(code = 200, message = "success")
	@RequestMapping(value = "/updateOrInsert", method = RequestMethod.POST)
	public Response updateOrInsert(@RequestBody BaseinfoProperty info) {
		boolean flag = false;
		int result = 0;
		try {
			result = BaseinfoConfiguration.getInstance().updateOrInsert(info);
			if (result == 1) {
				flag = true;
				return ResponseMsg.SUCCESS.buildMsg("成功");
			} else {
				return ResponseMsg.FAIL.buildMsg("失败");
			}
		} catch (RomeException e) {
			log.error(e.getMessage(), e);
			return ResponseMsg.EXCEPTION.buildMsg(e.getCode(), e.getMessage());
		} catch (Exception e) {
			log.error(e.getMessage(), e);
			return ResponseMsg.EXCEPTION.buildMsg(ResCode.STOCK_ERROR_1003, ResCode.STOCK_ERROR_1003_DESC);
		} finally {
			//记录日志信息
			String dataStr = JSON.toJSONString(info);
			//成功的 不保存返回值，返回值太大 保存不下
			sapInterfaceLogRepository.saveSapInterFaceLog(info.getRootKey(), "/stock/v1/baseinfo", "updateOrInsert",
					dataStr, flag ? "200" : JSON.toJSONString(result), flag);
		}
	}


	/**
	 * 删除基本信息配置
	 *
	 * @param baseinfoConfig
	 * @return
	 */
	@ApiOperation(value = "删除基本信息配置", nickname = "deleteBaseinfoConfig", produces = MediaType.APPLICATION_JSON_VALUE)
	@ApiResponse(code = 200, message = "success", response = BaseinfoConfig.class)
	@RequestMapping(value = "/deleteBaseinfoConfig", method = RequestMethod.POST)
	public Response deleteBaseinfoConfig(@ApiParam(name = "BaseinfoConfig", value = "基本信息配置") @RequestBody BaseinfoConfig baseinfoConfig) {
		int result = 0;
		boolean flag = false;
		try {
			result = baseinfoConfigService.deleteBaseinfoConfig(baseinfoConfig);
			if (result > 0) {
				flag = true;
			}
			return ResponseMsg.SUCCESS.buildMsg(baseinfoConfig);
		} catch (RomeException e) {
			log.error(e.getMessage(), e);
			return ResponseMsg.EXCEPTION.buildMsg(e.getCode(), e.getMessage());
		} catch (Exception e) {
			log.error(e.getMessage(), e);
			return ResponseMsg.EXCEPTION.buildMsg();
		}finally {
			//记录日志信息
			String dataStr = JSON.toJSONString(baseinfoConfig);
			//成功的 不保存返回值，返回值太大 保存不下
			//成功的 不保存返回值，返回值太大 保存不下
			sapInterfaceLogRepository.saveSapInterFaceLog(baseinfoConfig.getCode(), "/stock/v1/baseinfo/deleteBaseinfoConfig", "deleteBaseinfoConfig",
					dataStr, flag ? "200" : JSON.toJSONString(result), flag);
		}
	}



	@ApiOperation(value = "更新门店批次库存配置", nickname = "updateBatchConfig", produces = MediaType.APPLICATION_JSON_VALUE)
	@ApiResponse(code = 200, message = "success")
	@RequestMapping(value = "/updateBatchConfig", method = RequestMethod.POST)
	public Response updateBatchConfig(@RequestBody ShopBatchConfigDTO shopBatchConfigDTO) {
		try {
			baseinfoConfigService.updateBatchConfig(shopBatchConfigDTO);
			return Response.builderSuccess("");
		} catch (RomeException e) {
			log.error(e.getMessage(), e);
			return ResponseMsg.EXCEPTION.buildMsg(ResCode.STOCK_ERROR_1003, e.getMessage());
		} catch (Exception e) {
			log.error(e.getMessage(), e);
			return ResponseMsg.EXCEPTION.buildMsg(ResCode.STOCK_ERROR_1003, e.getMessage());
		}
	}
}
