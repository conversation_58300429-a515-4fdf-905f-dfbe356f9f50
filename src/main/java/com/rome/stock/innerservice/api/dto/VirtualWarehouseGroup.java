package com.rome.stock.innerservice.api.dto;

import com.rome.stock.innerservice.domain.entity.ChannelSalesE;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;
import java.util.List;

@Data
@EqualsAndHashCode
public class VirtualWarehouseGroup extends Pagination {
	//columns START
	/**
	 * 唯一主键
	 */
	@ApiModelProperty(value = "虚仓组主键id")
	private Long id;
	/**
	 * 名称
	 */
	@ApiModelProperty(value = "虚仓组名称")
	private String name;
	/**
	 * 虚拟仓库组编码
	 */
	@ApiModelProperty(value = "虚仓组编码")
	private String virtualWarehouseGroupCode;
	/**
	 * 模板名称，一般是指寻源规则和合单规则等等，内容如为模板类名
	 */
	@ApiModelProperty(value = "模板名称")
	private String template;
	/**
	 * 模板描述
	 */
	@ApiModelProperty(value = "模板描述")
	private String templateDesc;
	/**
	 * 备注信息
	 */
	@ApiModelProperty(value = "备注信息")
	private String remark;
	/**
	 * 商家id
	 */
	@ApiModelProperty(value = "商家id")
	private Long merchantId;

	@ApiModelProperty("渠道code集合,作查询用")
	private List<String> channelCodes;

	@ApiModelProperty(value = "关联渠道集合")
	private List<ChannelSales> channelSalesList;

	@ApiModelProperty(value = "关联渠道集合大小")
	private int channelSalesListSize;

	@ApiModelProperty(value = "创建人")
	private Long creator;

	@ApiModelProperty(value = "更新人")
	private Long modifier;
	
	@ApiModelProperty(value = "创建时间")
    private Date createTime;
	
    @ApiModelProperty(value = "修改时间")
    private Date updateTime;

}
