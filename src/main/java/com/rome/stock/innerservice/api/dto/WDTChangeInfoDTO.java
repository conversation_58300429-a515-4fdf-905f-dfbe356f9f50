package com.rome.stock.innerservice.api.dto;

import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 类WDTChangeInfoDTO的实现描述：旺店通信息修改DTO
 *
 *
 * <AUTHOR> 2020/5/7 15:55
 */
@Data
@EqualsAndHashCode
public class WDTChangeInfoDTO {

    /**
     * 旧的仓库编码
     */
    private String oldRwCode;

    /**
     * 新的仓库编码
     */
    private String newRwCode;

    /**
     * 旧的物流编号
     */
    private String oldLogisticsCode;

    /**
     * 新的物流编号
     */
    private String newLogisticsCode;

    /**
     * -1:啥都没改 2:改仓库 3:改物流 4:物流和仓库都改了
     */
    private Integer dealType;

    /**
     * 用户ID
     */
    private Long userId;

    private Long oldRealWarehouseId;

    private Long oldVirtualWarehouseId;

}
