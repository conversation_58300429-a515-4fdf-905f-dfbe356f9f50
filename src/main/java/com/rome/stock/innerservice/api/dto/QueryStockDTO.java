package com.rome.stock.innerservice.api.dto;

import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode
public class QueryStockDTO {

    private String skuCode;
    private BigDecimal realQty=BigDecimal.ZERO;
    private BigDecimal lockQty=BigDecimal.ZERO;
    private BigDecimal onroadQty=BigDecimal.ZERO;
    private BigDecimal qualityQty=BigDecimal.ZERO;
    private BigDecimal unqualifiedQty=BigDecimal.ZERO;

}
