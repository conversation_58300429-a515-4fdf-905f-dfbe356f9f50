package com.rome.stock.innerservice.api.dto;

import com.rome.arch.core.dto.DTO;
import lombok.Data;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @Description 虚仓库存对象
 * @date 2020/6/10 17:33
 */
@Data
public class VirtualWarehouseStockDTO extends DTO {

    /**
     * 唯一主键
     */
    private Long id;
    /**
     * 虚拟仓库ID
     */
    private Long virtualWarehouseId;
    /**
     * 商品sku编码
     */
    private Long skuId;

    /**
     * 商品编码
     */
    private String skuCode;
    /**
     * 真实库存
     */
    private BigDecimal realQty;
    /**
     * 锁定库存
     */
    private BigDecimal lockQty;

    /**
     * 商家id
     */
    private Long merchantId;

    /**
     * 不可用库存
     */
    private BigDecimal unUseQty;

    /**
     * 可用库存
     */
    private BigDecimal availableQty;

}    
   