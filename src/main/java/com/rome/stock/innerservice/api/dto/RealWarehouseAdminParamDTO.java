package com.rome.stock.innerservice.api.dto;

import com.rome.stock.innerservice.constant.RealWarehouseTypeVO;
import lombok.Data;

import java.util.List;

/**
 * 页面查询专用的
 * <AUTHOR>
 * @since 2022-7-5 15:13:32
 */
@Data
public class RealWarehouseAdminParamDTO {

	/**
	 * 仓库编码列表
	 */
    private List<String> realWarehouseCodeList;
    
    /**
     * 工厂编码列表
     */
    private List<String> factoryCodeList;
    
    /**
     * 一页条数
     */
    private Integer pageSize;
    
}
