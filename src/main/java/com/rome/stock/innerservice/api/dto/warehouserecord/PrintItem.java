package com.rome.stock.innerservice.api.dto.warehouserecord;

import lombok.Data;

import java.math.BigDecimal;

/**
 * 打印输出对象
 */
@Data
public class PrintItem {
    /**
     * 团购出库单号
     */
    private String doCode;
    /**
     * 数据总条数
     */
    private Integer count;
    /**
     * 物料编码
     */
    private String skuCode;
    /**
     * 物料名称
     */
    private String skuName;
    /**
     * 规格、型号
     */
    private String skuSpec;
    /**
     * 数量
     */
    private BigDecimal skuQty;
    /**
     * 计件数
     */
    private BigDecimal countQty;
    /**
     * 单位
     */
    private String unitName;
    /**
     * 创建时间
     */
    private String createTime;
    /**
     * 预计交货日期
     */
    private String deliveryDate;
    /**
     * 客户名称
     */
    private String customName;
    /**
     * 客户电话
     */
    private String customMobile;
    /**
     * 送货地址
     */
    private String customAddress;
    /**
     *
     * 仓库名称
     */
    private String factoryName;
    /**
     * 食品计件数
     */
    private Integer carTotal;
    /**
     * 非食品计件数
     */
    private Integer nonFoodTotal;
}
