/**
 * Filename SapTransferAccountLogController.java
 * Company 上海来伊份科技有限公司。
 * <AUTHOR>
 * @version 
 */
package com.rome.stock.innerservice.api.controller;

import java.util.ArrayList;
import java.util.List;

import com.rome.stock.innerservice.api.dto.QuerySapTransferAccountLog;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.*;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.github.pagehelper.PageInfo;
import com.rome.arch.core.clientobject.Response;
import com.rome.arch.core.exception.RomeException;
import com.rome.arch.util.controller.RomeController;
import com.rome.stock.innerservice.api.dto.SapTransferAccountLogByQueryDTO;
import com.rome.stock.innerservice.api.dto.SapTransferAccountDetailByQueryDTO;
import com.rome.stock.innerservice.api.dto.StockRecord;
import com.rome.stock.innerservice.common.ResCode;
import com.rome.stock.innerservice.constant.ResponseMsg;
import com.rome.stock.innerservice.domain.service.SapTransferAccountLogService;
import com.rome.stock.innerservice.facade.SapAccountLogMigrateTemporaryToolFacade;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import io.swagger.annotations.ApiResponse;
import lombok.extern.slf4j.Slf4j;

/**
 * sap过账调用日志查询
 * <AUTHOR>
 * @since 2020-7-25 11:05:37
 */
@Slf4j
@RomeController
@RequestMapping("/stock/sapTransferAccountLog")
@Api(tags={"sap过账调用日志"})
public class SapTransferAccountLogController {

	@Autowired
    private SapTransferAccountLogService sapTransferAccountLogService;
	
	@ApiOperation(value = "查询大于指定的时间，和id和状态", produces = MediaType.APPLICATION_JSON_VALUE)
    @ApiResponse(code = 200, message = "success", response = SapTransferAccountLogByQueryDTO.class)
    @RequestMapping(value = "/queryListByCreateTimeAndGtId", method = RequestMethod.POST)
    public Response<List<SapTransferAccountLogByQueryDTO>> queryListByCreateTimeAndGtId(@ApiParam(name = "condition") @RequestBody SapTransferAccountLogByQueryDTO condition) {
        try {
        	List<SapTransferAccountLogByQueryDTO> result = sapTransferAccountLogService.queryListByCreateTimeAndGtId(condition);
            return Response.builderSuccess(result);
        } catch (RomeException e) {
            log.error(e.getMessage(), e);
            return Response.builderFail(e.getCode(), e.getMessage());
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return Response.builderFail(ResCode.STOCK_ERROR_1003, ResCode.STOCK_ERROR_1003_DESC + e.getMessage());
        }
    }
	
	@ApiOperation(value = "sap日志同步，根据Id列表同步，返回成功的Id", produces = MediaType.APPLICATION_JSON_VALUE)
    @ApiResponse(code = 200, message = "success", response = List.class)
    @RequestMapping(value = "/sapLogSyncByIds", method = RequestMethod.POST)
    public Response<List<Long>> sapLogSyncByIds(@ApiParam(name = "ids")@RequestBody List<Long> ids) {
        try {
        	List<Long> result = sapTransferAccountLogService.sapLogSyncByIds(ids);
            return Response.builderSuccess(result);
        } catch (RomeException e) {
            log.error(e.getMessage(), e);
            return Response.builderFail(e.getCode(), e.getMessage());
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return Response.builderFail(ResCode.STOCK_ERROR_1003, ResCode.STOCK_ERROR_1003_DESC + e.getMessage());
        }
    }
	
	@ApiOperation(value = "sap日志同步，根据Id列表同步，返回成功的Id 测试用的", produces = MediaType.APPLICATION_JSON_VALUE)
    @ApiResponse(code = 200, message = "success", response = List.class)
    @RequestMapping(value = "/sapLogSyncByIdsTest", method = RequestMethod.POST)
    public Response<List<Long>> sapLogSyncByIdsTest(@ApiParam(name = "jsonObject")@RequestBody JSONObject jsonObject) {
        try {
        	List<Long> ids = new ArrayList<Long>();
        	JSONArray jsonArray = jsonObject.getJSONArray("ids");
        	if(jsonArray == null) {
        		return Response.builderFail(ResCode.STOCK_ERROR_1002, ResCode.STOCK_ERROR_1002_DESC + "ids为空");
        	}
        	for(int i = 0; i < jsonArray.size(); i++) {
        		ids.add(jsonArray.getLong(i));
        	}
        	List<Long> result = sapTransferAccountLogService.sapLogSyncByIdsTest(ids, jsonObject.getJSONObject("context"), jsonObject.getString("flag"));
            return Response.builderSuccess(result);
        } catch (RomeException e) {
            log.error(e.getMessage(), e);
            return Response.builderFail(e.getCode(), e.getMessage());
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return Response.builderFail(ResCode.STOCK_ERROR_1003, ResCode.STOCK_ERROR_1003_DESC + e.getMessage());
        }
    }
	
	@ApiOperation(value = "保存明细，test用 测试用的", produces = MediaType.APPLICATION_JSON_VALUE)
    @ApiResponse(code = 200, message = "success", response = List.class)
    @RequestMapping(value = "/sapLogInsertInitTest", method = RequestMethod.POST)
    public Response<List<Long>> sapLogInsertInitTest(@ApiParam(name = "jsonObject")@RequestBody JSONObject jsonObject) {
        try {
        	sapTransferAccountLogService.sapLogInsertInitTest(jsonObject.getString("recordCode"), jsonObject.getString("method"), jsonObject.getJSONObject("context"));
            return Response.builderSuccess("");
        } catch (RomeException e) {
            log.error(e.getMessage(), e);
            return Response.builderFail(e.getCode(), e.getMessage());
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return Response.builderFail(ResCode.STOCK_ERROR_1003, ResCode.STOCK_ERROR_1003_DESC + e.getMessage());
        }
    }
	
	@ApiOperation(value = "根据查询条件查询sap过账日志", nickname = "queryByCondition", produces = MediaType.APPLICATION_JSON_VALUE)
    @ApiResponse(code = 200, message = "success", response = List.class)
    @RequestMapping(value = "/queryByCondition", method = RequestMethod.POST)
    public Response<PageInfo<SapTransferAccountLogByQueryDTO>> queryByCondition(@ApiParam(name = "condition") @RequestBody SapTransferAccountLogByQueryDTO condition) {
        try {
            PageInfo<SapTransferAccountLogByQueryDTO> pageList = sapTransferAccountLogService.queryByCondition(condition);
            return Response.builderSuccess(pageList);
        } catch (RomeException e) {
            log.error(e.getMessage(), e);
            return ResponseMsg.EXCEPTION.buildMsg(e.getCode(), e.getMessage());
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return ResponseMsg.EXCEPTION.buildMsg(ResCode.STOCK_ERROR_1003, ResCode.STOCK_ERROR_1003_DESC);
        }
    }
    @ApiOperation(value = "指定单据过账", nickname = "postAccount", produces = MediaType.APPLICATION_JSON_VALUE)
    @ApiResponse(code = 200, message = "success", response = List.class)
    @RequestMapping(value = "/postAccount", method = RequestMethod.POST)
    public Response  postAccount(@ApiParam(name = "condition") @RequestBody SapTransferAccountLogByQueryDTO condition) {
        try {
            sapTransferAccountLogService.postAccount(condition);
            return Response.builderSuccess("");
        } catch (RomeException e) {
            log.error(e.getMessage(), e);
            return ResponseMsg.EXCEPTION.buildMsg(e.getCode(), e.getMessage());
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return ResponseMsg.EXCEPTION.buildMsg(ResCode.STOCK_ERROR_1003, ResCode.STOCK_ERROR_1003_DESC);
        }
    }

	@ApiOperation(value = "根据所属id查询sap过账日志明细", nickname = "queryByCondition", produces = MediaType.APPLICATION_JSON_VALUE)
    @ApiResponse(code = 200, message = "success", response = List.class)
    @RequestMapping(value = "/getDetailByLogId", method = RequestMethod.POST)
    public Response<List<SapTransferAccountDetailByQueryDTO>> getDetailByLogId(@ApiParam(name = "condition") @RequestBody SapTransferAccountDetailByQueryDTO condition) {
        try {
        	List<SapTransferAccountDetailByQueryDTO> pageList = sapTransferAccountLogService.getDetailByLogId(condition.getLogId());
            return Response.builderSuccess(pageList);
        } catch (RomeException e) {
            log.error(e.getMessage(), e);
            return ResponseMsg.EXCEPTION.buildMsg(e.getCode(), e.getMessage());
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return ResponseMsg.EXCEPTION.buildMsg(ResCode.STOCK_ERROR_1003, e.getMessage());
        }
    }


    @ApiOperation(value = "根据查询条件导出sap过账日志明细", nickname = "exportExcelForDetail", produces = MediaType.APPLICATION_JSON_VALUE)
    @ApiResponse(code = 200, message = "success", response = List.class)
    @RequestMapping(value = "/exportExcelForDetail", method = RequestMethod.POST)
    public Response<PageInfo<SapTransferAccountDetailByQueryDTO>> exportExcelForDetail(@ApiParam(name = "condition") @RequestBody SapTransferAccountLogByQueryDTO condition) {
        try {
        	PageInfo<SapTransferAccountDetailByQueryDTO> pageList = sapTransferAccountLogService.exportExcelForDetail(condition);
            return Response.builderSuccess(pageList);
        } catch (RomeException e) {
            log.error(e.getMessage(), e);
            return ResponseMsg.EXCEPTION.buildMsg(e.getCode(), e.getMessage());
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return ResponseMsg.EXCEPTION.buildMsg(ResCode.STOCK_ERROR_1003, e.getMessage());
        }
    }

    @ApiOperation(value = "手动备份过账日志", nickname = "backUpTransferLog", produces = MediaType.APPLICATION_JSON_VALUE)
    @ApiResponse(code = 200, message = "success", response = Response.class)
    @PostMapping("/backUpTransferLog")
    public Response backUpTransferLog(@RequestParam("date")String date){
	    try{
            sapTransferAccountLogService.backUpTransferLog(date);
            return Response.builderSuccess("");
        }catch (RomeException e){
            log.error(e.getMessage(), e);
            return ResponseMsg.EXCEPTION.buildMsg(e.getCode(), e.getMessage());
        }catch (Exception e){
            log.error(e.getMessage(), e);
            return ResponseMsg.EXCEPTION.buildMsg(ResCode.STOCK_ERROR_1003, e.getMessage());
        }
    }

    @ApiOperation(value = "根据单据号批量查询过账日志", nickname = "queryByRecordCodes", produces = MediaType.APPLICATION_JSON_VALUE)
    @ApiResponse(code = 200, message = "success", response = Response.class)
    @PostMapping("/queryByRecordCodes")
    public Response<List<SapTransferAccountLogByQueryDTO>> queryByRecordCodes(@RequestBody QuerySapTransferAccountLog querySapTransferAccountLog){
	    try{
            List<SapTransferAccountLogByQueryDTO> sapTransferAccountLogByQueryDTOS = sapTransferAccountLogService.queryByRecordCode(querySapTransferAccountLog.getRecordCodes());
            return Response.builderSuccess(sapTransferAccountLogByQueryDTOS);
        }catch (RomeException e){
            log.error(e.getMessage(), e);
            return ResponseMsg.EXCEPTION.buildMsg(e.getCode(), e.getMessage());
        }catch (Exception e){
            log.error(e.getMessage(), e);
            return ResponseMsg.EXCEPTION.buildMsg(ResCode.STOCK_ERROR_1003, e.getMessage());
        }
    }
    
    @ApiOperation(value = "老数据，迁移数据根据id范围", nickname = "migrateByIdRange", produces = MediaType.APPLICATION_JSON_VALUE)
    @ApiResponse(code = 200, message = "success", response = Response.class)
    @PostMapping("/migrateByIdRange")
    public Response migrateByIdRange(@RequestParam("minId") Long minId, @RequestParam("maxId") Long maxId, @RequestParam(name = "flag是1代表根据单号判断，否则根据单据类型", defaultValue = "0") String flag){
	    try{
	    	String result = SapAccountLogMigrateTemporaryToolFacade.migrateByIdRange(minId, maxId, flag);
            return Response.builderSuccess(result);
        }catch (RomeException e){
            log.error(e.getMessage(), e);
            return ResponseMsg.EXCEPTION.buildMsg(e.getCode(), e.getMessage());
        }catch (Exception e){
            log.error(e.getMessage(), e);
            return ResponseMsg.EXCEPTION.buildMsg(ResCode.STOCK_ERROR_1003, e.getMessage());
        }
    }
    
    @ApiOperation(value = "老数据，迁移数据根据id范围", nickname = "migrateByIdList", produces = MediaType.APPLICATION_JSON_VALUE)
    @ApiResponse(code = 200, message = "success", response = Response.class)
    @PostMapping("/migrateByIdList")
    public Response migrateByIdList(@ApiParam("idList") @RequestBody List<Long> idList, @RequestParam(name = "flag是1代表根据单号判断，否则根据单据类型", defaultValue = "0") String flag){
	    try{
	    	String result = SapAccountLogMigrateTemporaryToolFacade.migrateByIdList(idList, flag);
            return Response.builderSuccess(result);
        }catch (RomeException e){
            log.error(e.getMessage(), e);
            return ResponseMsg.EXCEPTION.buildMsg(e.getCode(), e.getMessage());
        }catch (Exception e){
            log.error(e.getMessage(), e);
            return ResponseMsg.EXCEPTION.buildMsg(ResCode.STOCK_ERROR_1003, e.getMessage());
        }
    }
	
}
