package com.rome.stock.innerservice.api.dto.frontrecord;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import java.util.List;

/**
 * <AUTHOR>
 * @description 质检结果回调对象
 * @date 2020/9/27 13:53
 * @throw
 */
@Data
public class QualityResultCallBackDTO {

    @NotBlank(message="记账凭证不能为空")
    @ApiModelProperty(value = "记账凭证")
    private String wmsRecordCode;

    @ApiModelProperty(value = "入库凭证")
    @NotBlank(message="入库凭证不能为空")
    private String recordCode;

    @ApiModelProperty(value = "质检接口批次明细")
    @NotEmpty(message = "明细信息不能为空")
    private List<QualityResultCallBackDetailDTO> detailDTOList;



}    
   