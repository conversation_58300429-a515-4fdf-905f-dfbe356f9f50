package com.rome.stock.innerservice.api.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * @Description StockAlarmConfigDTO
 * <AUTHOR>
 * @Date 2024/4/15
 **/
@Data
public class StockAlarmConfigDTO extends Pagination{

    private Long headId;

    @ApiModelProperty(value = "主键ID")
    private Long id;

    @ApiModelProperty(value = "预警类型 1-仓库滞销预警 2-门店滞销预警")
    private Integer alarmType;

    @ApiModelProperty(value = "预警名称")
    private String alarmName;

//    @ApiModelProperty(value = "预警内容")
//    private String alarmContent;

    @ApiModelProperty(value = "预警通知人员，多个逗号分割")
    private String accountCodes;

    @ApiModelProperty(value = "预警方式：1-飞书")
    private Integer alarmWay;

    @ApiModelProperty(value = "预警状态：0-禁用 1-启用")
    private Integer status;

    @ApiModelProperty(value = "备注")
    private String remark;

    @ApiModelProperty(value = "预警值来源:1.需求计划 2.最晚销售日期消耗量")
    private Integer alarmSourceType;

    @ApiModelProperty(value = "比较关系:-1.小于,0.等于,1.大于")
    private Integer compareType;

    @ApiModelProperty(value = "预警比较值:1.生产日期库存量 2.当前批次库存量")
    private Integer compareValueType;

    @ApiModelProperty(value = "预警上限分子")
    private Integer molecule;

    @ApiModelProperty(value = "预警上限分母")
    private Integer denominator;

    @ApiModelProperty(value="预警下限数量")
    private BigDecimal lowerLimitQty;

    @ApiModelProperty(value = "预警明细")
    private List<StockAlarmConfigDetailDTO> itemDTOList;

    @ApiModelProperty(value="预警类型=1时，预警天数；类型=2时，历史销量天数")
    private Integer alarmDayCount;

    @ApiModelProperty(value="是否全部门店，true则预警明细为空，false则配置预警明细")
    private Boolean isAll;

    /**
     * 创建时间
     */
    private Date createTime;
    /**
     * 修改时间
     */
    private Date updateTime;
    /**
     * 创建人
     */
    private Long creator;
    /**
     * 更新人
     */
    private Long modifier;
    /**
     * 是否可用：0-否，1-是
     */
    private Byte isAvailable;
    /**
     * 是否逻辑删除：0-否，1-是
     */
    private Byte isDeleted;
    /**
     * 版本号:默认0,每次更新+1
     */
    private Integer versionNo;

    private String stockDateStr;

}
