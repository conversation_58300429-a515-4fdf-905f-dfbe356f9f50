package com.rome.stock.innerservice.api.controller;

import java.util.List;

import com.alibaba.fastjson.JSON;
import com.rome.stock.innerservice.remote.sap.dto.ZMMTMDQH3010;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;

import com.github.pagehelper.PageInfo;
import com.rome.arch.core.clientobject.Response;
import com.rome.arch.core.exception.RomeException;
import com.rome.arch.util.controller.RomeController;
import com.rome.stock.innerservice.api.dto.cmtransferlog.CmTransferDetailExportDTO;
import com.rome.stock.innerservice.api.dto.cmtransferlog.CmTransferLogPageDTO;
import com.rome.stock.innerservice.common.ResCode;
import com.rome.stock.innerservice.domain.service.CmTransferLogService;
import com.rome.stock.innerservice.infrastructure.dataobject.CmTransferLogDO;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiResponse;
import lombok.extern.slf4j.Slf4j;

/**
 * 类CmTransferLogController的实现描述：云商301接口过账日志实现
 *
 * <AUTHOR> 2020/11/5 22:57
 */
@Slf4j
@RomeController
@RequestMapping("/stock/v1/cmTransferLog")
@Api(tags={"云商301接口过账日志实现"})
public class CmTransferLogController {

    @Autowired
    private CmTransferLogService cmTransferLogService;

    @ApiOperation(value = "按单据手动过账", nickname = "postAccountByRecordCode", produces = MediaType.APPLICATION_JSON_VALUE)
    @ApiResponse(code = 200, message = "success")
    @RequestMapping(value = "/postAccountByRecordCode", method = RequestMethod.POST)
    public Response postAccountByRecordCode(@RequestParam String recordCode) {

        try {
            cmTransferLogService.postAccountByRecordCode(recordCode);
            return Response.builderSuccess("success");
        } catch (RomeException e) {
            log.error(e.getMessage(), e);
            return Response.builderFail(e.getCode(), e.getMessage());
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return Response.builderFail(ResCode.STOCK_ERROR_1003, e.getMessage());
        }
    }
    @ApiOperation(value = "按日期手动过账", nickname = "postAccountByRecordCode", produces = MediaType.APPLICATION_JSON_VALUE)
    @ApiResponse(code = 200, message = "success")
    @RequestMapping(value = "/postAccountByPushDate", method = RequestMethod.POST)
    public Response postAccountByPushDate(@RequestParam String pushDate) {
        try {
            cmTransferLogService.postAccountByPushDate(pushDate ,false);
            return Response.builderSuccess("success");
        } catch (RomeException e) {
            log.error(e.getMessage(), e);
            return Response.builderFail(e.getCode(), e.getMessage());
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return Response.builderFail(ResCode.STOCK_ERROR_1003, e.getMessage());
        }
    }

    @ApiOperation(value = "分页查询列表", nickname = "selectByPushDateList", produces = MediaType.APPLICATION_JSON_VALUE)
    @ApiResponse(code = 200, message = "success")
    @RequestMapping(value = "/selectByPushDateList", method = RequestMethod.POST)
    public Response<PageInfo<CmTransferLogPageDTO>>  selectByPushDateList(@RequestBody CmTransferLogPageDTO cmTransferLogDO) {
        try {
            PageInfo<CmTransferLogPageDTO> page=cmTransferLogService.selectByPushDateList(cmTransferLogDO);
            return Response.builderSuccess(page);
        } catch (RomeException e) {
            log.error(e.getMessage(), e);
            return Response.builderFail(e.getCode(), e.getMessage());
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return Response.builderFail(ResCode.STOCK_ERROR_1003, e.getMessage());
        }
    }

    @ApiOperation(value = "查询列表", nickname = "selectListByCmTransferLogDTO", produces = MediaType.APPLICATION_JSON_VALUE)
    @ApiResponse(code = 200, message = "success")
    @RequestMapping(value = "/selectListByCmTransferLogDTO", method = RequestMethod.POST)
    public Response<List<CmTransferLogDO>> selectListByCmTransferLogDTO(@RequestBody CmTransferLogDO cmTransferLogDO){
        try {
            List<CmTransferLogDO> page=cmTransferLogService.selectListByCmTransferLogDTO(cmTransferLogDO);
            return Response.builderSuccess(page);
        } catch (RomeException e) {
            log.error(e.getMessage(), e);
            return Response.builderFail(e.getCode(), e.getMessage());
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return Response.builderFail(ResCode.STOCK_ERROR_1003, e.getMessage());
        }
    }


    @ApiOperation(value = "导出全部云商301接口过账失败日志", nickname = "exportExcelAllCmTransferLog", produces = MediaType.APPLICATION_JSON_VALUE)
    @ApiResponse(code = 200, message = "success")
    @RequestMapping(value = "/exportExcelAllCmTransferLog", method = RequestMethod.POST)
    public Response<List<CmTransferLogDO>> exportExcelAllCmTransferLog(@RequestBody CmTransferLogDO cmTransferLogDO){
        try {
            List<CmTransferLogDO> page=cmTransferLogService.exportExcelAllCmTransferLog(cmTransferLogDO);
            return Response.builderSuccess(page);
        } catch (RomeException e) {
            log.error(e.getMessage(), e);
            return Response.builderFail(e.getCode(), e.getMessage());
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return Response.builderFail(ResCode.STOCK_ERROR_1003, e.getMessage());
        }
    }

    @ApiOperation(value = "导出301过账单明细", nickname = "exportExcelAllCmTransferLog", produces = MediaType.APPLICATION_JSON_VALUE)
    @ApiResponse(code = 200, message = "success")
    @RequestMapping(value = "/exportDetailList", method = RequestMethod.POST)
    public Response<List<CmTransferDetailExportDTO>> exportDetailList(@RequestBody CmTransferLogPageDTO cmTransferLogDO){
        try {
            List<CmTransferDetailExportDTO> page=cmTransferLogService.exportDetailList(cmTransferLogDO);
            return Response.builderSuccess(page);
        } catch (RomeException e) {
            log.error(e.getMessage(), e);
            return Response.builderFail(e.getCode(), e.getMessage());
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return Response.builderFail(ResCode.STOCK_ERROR_1003, e.getMessage());
        }
    }

    @ApiOperation(value = "根据recordCode查询过账返回信息详情", nickname = "selectCmTransferResultByRecordCode", produces = MediaType.APPLICATION_JSON_VALUE)
    @ApiResponse(code = 200, message = "success")
    @RequestMapping(value = "/selectCmTransferResultByRecordCode", method = RequestMethod.POST)
    public Response<ZMMTMDQH3010> selectCmTransferResultByRecordCode(@RequestParam String recordCode){
        try {
            CmTransferLogDO cmTransferLogDO =cmTransferLogService.getCmTransferByRecordCode(recordCode);
            ZMMTMDQH3010 zmmtmdqh3010 = JSON.parseObject(cmTransferLogDO != null ? cmTransferLogDO.getErrorMsg() : null, ZMMTMDQH3010.class);
            return Response.builderSuccess(zmmtmdqh3010);
        } catch (RomeException e) {
            log.error(e.getMessage(), e);
            return Response.builderFail(e.getCode(), e.getMessage());
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return Response.builderFail(ResCode.STOCK_ERROR_1003, e.getMessage());
        }
    }

    @ApiOperation(value = "根据recordCode查询过账明细列表数据（分页）", nickname = "selectCmTransferDetailPage", produces = MediaType.APPLICATION_JSON_VALUE)
    @ApiResponse(code = 200, message = "success")
    @RequestMapping(value = "/selectCmTransferDetailPage", method = RequestMethod.POST)
    public Response<ZMMTMDQH3010> selectCmTransferDetailPage(@RequestBody CmTransferLogPageDTO cmTransferLogDO){
        try {
            PageInfo<CmTransferDetailExportDTO> pageInfo =cmTransferLogService.selectCmTransferDetailPage(cmTransferLogDO);
            return Response.builderSuccess(pageInfo);
        } catch (RomeException e) {
            log.error(e.getMessage(), e);
            return Response.builderFail(e.getCode(), e.getMessage());
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return Response.builderFail(ResCode.STOCK_ERROR_1003, e.getMessage());
        }
    }




}
