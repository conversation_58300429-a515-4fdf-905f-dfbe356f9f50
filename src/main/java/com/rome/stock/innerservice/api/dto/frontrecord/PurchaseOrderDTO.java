package com.rome.stock.innerservice.api.dto.frontrecord;


import com.fasterxml.jackson.annotation.JsonIgnore;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.Valid;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.util.Date;
import java.util.List;

/**
 * 采购入库通知单
 */
@Data
@EqualsAndHashCode
public class PurchaseOrderDTO {
	@JsonIgnore
	private String recordCode;

	@JsonIgnore
	private Integer recordType;

	@NotBlank(message="SAP采购单号不能为空")
	@ApiModelProperty(value = "SAP采购单号")
	private String outRecordCode;

	@ApiModelProperty(value = "采购时间")
	@NotNull(message="采购时间不能为空")
	private Date outCreateTime;

	@NotBlank(message="工厂代码不能为空")
	@ApiModelProperty(value = "工厂代码")
	private String factoryCode;

	@ApiModelProperty(value = "原料工厂代码")
	private String osFactoryCode;


	@NotBlank(message="工厂名称不能为空")
	@ApiModelProperty(value = "工厂名称")
	private String factoryName;

	@NotBlank(message="仓库编号不能为空")
	@ApiModelProperty(value = "仓库编号")
	private String realWarehouseCode;

	@ApiModelProperty(value = "采购单类型：1、普通采购 2、紧急入库单 3、委外原料入库单  4、委外成品入库单 5、 越库采购入库单 6、直送入库单 7、折让入库单 8、更正入库单、10 手工新增、11 一件代发 ")
	private Integer purchaseRecordType;

	@NotBlank(message="供应商编码不能为空")
	@ApiModelProperty(value = "供应商编码")
	private String supplierCode;

	@NotBlank(message="供应商名称不能为空")
	@ApiModelProperty(value = "供应商名称")
	private String supplierName;

	@ApiModelProperty(value = "盘点备注")
	private String remark;

	@ApiModelProperty(value = "业务类型")
	@NotBlank(message = "业务类型不能为空")
	private String businessType;

	@ApiModelProperty(value = "供应商联系人")
	private String supplierContact;

	@ApiModelProperty(value = "关联的sap内采单号：直送流程必传")
	private String sapPoNo;

	@ApiModelProperty(value = "门店编码：直送流程必传")
	private String shopCode;

	@ApiModelProperty(value = "sku数量及明细")
	@NotNull(message="sku数量及明细不能为空")
	@Valid
	private List<PurchaseOrderDetailDTO> purchaseOrderDetails;
}
