package com.rome.stock.innerservice.api.dto.cloudshop;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 类LngLatDTO的实现描述：经纬度对象
 *
 * <AUTHOR> 2021/9/26 15:46
 */
@Data
public class LngLatDTO {
    /**
     * 经度
     */
    @ApiModelProperty(value = "经度")
    private String lng;

    /**
     * 纬度
     */
    @ApiModelProperty(value = "纬度")
    private String lat;

    /**
     * 唯一编码
     */
    @ApiModelProperty(value = "唯一编码(方便前端获取map, 可用经纬度逗号拼接)")
    private String code;
}
