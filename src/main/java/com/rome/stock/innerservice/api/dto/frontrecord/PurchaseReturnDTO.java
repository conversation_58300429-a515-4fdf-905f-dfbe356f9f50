package com.rome.stock.innerservice.api.dto.frontrecord;


import com.fasterxml.jackson.annotation.JsonIgnore;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

import javax.validation.Valid;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.util.Date;
import java.util.List;

/**
 * 采购退货通知单
 */
@Data
@EqualsAndHashCode
@ToString
public class PurchaseReturnDTO {
	@JsonIgnore
	private String recordCode;

	@JsonIgnore
	private Integer recordType;

	@NotBlank(message="SAP采购退货单号不能为空")
	@ApiModelProperty(value = "SAP采购退货单号")
	private String outRecordCode;

	@ApiModelProperty(value = "采购时间")
	@NotNull(message="采购时间不能为空")
	private Date outCreateTime;

	@NotBlank(message="工厂代码不能为空")
	@ApiModelProperty(value = "工厂代码")
	private String factoryCode;

	@NotBlank(message="工厂名称不能为空")
	@ApiModelProperty(value = "工厂名称")
	private String factoryName;

	@NotBlank(message="仓库编号不能为空")
	@ApiModelProperty(value = "仓库编号")
	private String realWarehouseCode;

	@ApiModelProperty(value = "9 大仓采购退供")
	private Integer purchaseRecordType;

	@ApiModelProperty(value = "采购单类型：1.SP/食品类采购订单 2.FS/非食品类采购订单 3、WW/委外入库  5、ZYK/冷链采购单 6、ZZS/供应商直送")
	private String purchaseRecordCode;

	@NotBlank(message="供应商编码不能为空")
	@ApiModelProperty(value = "供应商编码")
	private String supplierCode;

	@NotBlank(message="供应商名称不能为空")
	@ApiModelProperty(value = "供应商名称")
	private String supplierName;

	@ApiModelProperty(value = "业务类型")
	private String businessType;

	@ApiModelProperty(value = "盘点备注")
	private String remark;

	@ApiModelProperty(value = "供应商联系人")
	private String supplierContact;

	@ApiModelProperty(value = "sku数量及明细")
	@NotNull(message="sku数量及明细不能为空")
	@Valid
	private List<PurchaseOrderDetailDTO> purchaseOrderDetails;
}
