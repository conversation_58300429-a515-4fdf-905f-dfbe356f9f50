package com.rome.stock.innerservice.api.dto.cloudshop;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 类CloudShopStockReqDTO的实现描述：云店库存列表
 *
 * <AUTHOR> 2021/9/15 19:06
 */
@Data
public class CloudShopStockDTO {

    /**
     * 门店编码
     */
    @ApiModelProperty(value = "门店编码")
    private String shopCode;

    /**
     * 商品编码
     */
    @ApiModelProperty(value = "商品编码")
    private String skuCode;

    /**
     * 单位
     */
    @ApiModelProperty(value = "单位编码")
    private String unitCode;
}
