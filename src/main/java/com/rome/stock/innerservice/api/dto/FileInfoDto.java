package com.rome.stock.innerservice.api.dto;

import com.rome.stock.innerservice.infrastructure.dataobject.BaseDo;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
@EqualsAndHashCode
public class FileInfoDto{
	/**
	 * 唯一主键
	 */
	private Long id;
	/**
	 * 业务单据编号
	 */
	private Long bizId;
	/**
	 * 业务类型（1.wine，2.wine_log）
	 */
	private Integer bizType;
	/**
	 * 文件类型，1图片、2video、3music、4css、5js、6html、7other
	 */
	private Integer fileType;
	/**
	 * 文件名
	 */
	private String fileName;
	/**
	 * 文件路径
	 */
	private String linkUrl;
	/**
	 * 文件有效期时间
	 */
	private Integer expireDays;

	public String getBizIdType() {
		return bizId + "#" + bizType;
	}
}
