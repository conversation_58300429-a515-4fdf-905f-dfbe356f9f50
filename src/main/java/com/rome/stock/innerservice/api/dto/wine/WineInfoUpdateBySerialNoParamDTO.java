package com.rome.stock.innerservice.api.dto.wine;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * <p>
 * Description: 换桶参数DTO
 * </p>
 *
 * <AUTHOR>
 * @date 2023/4/26
 **/
@Data
@EqualsAndHashCode
public class WineInfoUpdateBySerialNoParamDTO {

    @ApiModelProperty(value = "唯一主键")
    @NotNull
    private Long id;

    @ApiModelProperty(value = "更新桶类型")
    @NotBlank(message="更新桶类型不能为空")
    private String newBucketType;

    @ApiModelProperty(value = "更新桶sku编码")
    @NotBlank(message="更新桶sku编码不能为空")
    private String newBucketSkuCode;

    @ApiModelProperty(value = "更新桶序列号")
    @NotBlank(message="更新桶序列号不能为空")
    private String newSerialNo;

    @ApiModelProperty(value = "空桶订单号")
    @NotBlank(message="空桶订单号不能为空")
    private String bucketRecordCode;


    @ApiModelProperty(value = "备注")
    private String remark;

    private Long userId;

    @ApiModelProperty(value = "是否强制调用接口，1.是")
    private  Integer isForce;
}
