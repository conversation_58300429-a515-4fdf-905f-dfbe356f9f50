package com.rome.stock.innerservice.api.dto;

import java.io.Serializable;
import java.util.List;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @Description: DO单锁库传输对象
 * <p>
 * @Author: wwh 2020/8/11
 */
@Data
public class LockStockDTO implements Serializable {
	
	@ApiModelProperty(value = "单据编号（DO单号）")
	private String recordCode;
	
	@ApiModelProperty(value = "工厂编码")
	private String factoryCode;
	
	@ApiModelProperty(value = "实仓编码")
	private String realWarehouseCode;

	@ApiModelProperty(value = "SKU集合")
	private List<String> skuCodes;

}