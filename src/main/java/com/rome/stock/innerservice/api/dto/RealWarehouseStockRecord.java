package com.rome.stock.innerservice.api.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;
import java.util.List;

@Data
@EqualsAndHashCode
public class RealWarehouseStockRecord{

	/**
	 * 唯一主键
	 */
	@ApiModelProperty(value = "唯一主键")
	private Long id;
	/**
	 * 前置单据编号
	 */
	@ApiModelProperty(value = "前置单据编号")
	private String frontRecordCode;
	/**
	 * 单据编号
	 */
	@ApiModelProperty(value = "单据编号")
	private String recordCode;
	/**
	 * wms合单后的单据编号
	 */
	@ApiModelProperty(value = "wms合单后的单据编号")
	private String wmsRecordCode;
	/**
	 * 渠道id
	 */
	@ApiModelProperty(value = "渠道id")
	private Long channelId;
	/**
	 * 店铺id
	 */
	@ApiModelProperty(value = "店铺id")
	private Long shopId;
	/**
	 * 实仓ID 单据对一个实仓操作可以填写
	 */
	@ApiModelProperty(value = "实仓ID 单据对一个实仓操作可以填写")
	private Long realWarehouseId;
	/**
	 * 商家id
	 */
	@ApiModelProperty(value = "商家id")
	private Long merchantId;
	/**
	 * 业务类型：
	 */
	@ApiModelProperty(value = "业务类型")
	private Integer businessType;
	/**
	 * 单据类型：1-销售出库订单，101-入库单，20019-盘盈，20020-盘亏，20021-调拨入库，20022-调拨出库，20023-调拨出库取消，20024-退货入库，20025-领用，30001-移库单，30002-采购单
	 */
	@ApiModelProperty(value = "单据类型")
	private Integer recordType;
	/**
	 * 单据状态：0-初始，1-待审核，2-待合单，3 已同步 10 拣货 11 打包 12 装车 13 发运 21 接单 22 配送 23 完成
	 */
	@ApiModelProperty(value = "单据状态")
	private Integer recordStatus;
	/**
	 * 单据状态审核原因
	 */
	@ApiModelProperty(value = "单据状态审核原因")
	private String recordStatusReason;
	/**
	 * 关联销售业务的来源单据编号，例如：采购订单、销售订单、调拨订单的对应编号
	 */
	@ApiModelProperty(value = "关联销售业务的来源单据编号，例如：采购订单、销售订单、调拨订单的对应编号")
	private String sourceRecordCode;
	/**
	 * 来源类型，例如：app、门店零售、小程序、天猫、京东、app外卖、云店、新美大、饿了么
	 */
	@ApiModelProperty(value = "来源类型，例如：app、门店零售、小程序、天猫、京东、app外卖、云店、新美大、饿了么")
	private String sourceRecordType;
	/**
	 * 发货人信息id
	 */
	@ApiModelProperty(value = "发货人信息id")
	private Long senderInfoId;
	/**
	 * 收货人信息id
	 */
	@ApiModelProperty(value = "收货人信息id")
	private Long receiverInfoId;
	/**
	 * 发货时间
	 */
	@ApiModelProperty(value = "发货时间")
	private Date deliveryTime;
	/**
	 * 收货时间
	 */
	@ApiModelProperty(value = "收货时间")
	private Date receiverTime;


	@ApiModelProperty(value = "明细")
	private List<RealWarehouseStockRecordDetail> realWarehouseStockRecordDetails;

}

	
