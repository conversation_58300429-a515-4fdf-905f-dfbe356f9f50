package com.rome.stock.innerservice.api.dto.bms;

import com.rome.stock.innerservice.api.dto.Pagination;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;

/**
* 仓库作业数据对应实体
* <AUTHOR>
* @since 2023-10-12
*/
@Data
@EqualsAndHashCode
public class WarehouseWorkPageDTO extends Pagination {

        @ApiModelProperty(hidden = true)
        private Long id;

        @ApiModelProperty(value = "来源系统")
        private String sourceSys;

        @ApiModelProperty(value = "作业单号")
        private String orderNo;

        @ApiModelProperty(value = "后置单单据编码",hidden = true)
        private String warehouseRecordCode;

        @ApiModelProperty(value = "是否电商toc单据")
        private String isToc;

        @ApiModelProperty(value = "物流产品")
        private String logisticProduct;

        @ApiModelProperty(value = "仓库编码")
        private String warehouseCode;

        @ApiModelProperty(value = "库区编码")
        private String zoneCode;

        @ApiModelProperty(value = "作业环节")
        private String workType;

        @ApiModelProperty(value = "作业时间")
        private String generateTime;

        @ApiModelProperty(value = "作业模式")
        private String workModel;

        @ApiModelProperty(value = "服务商编码")
        private String tdlnr;

        @ApiModelProperty(value = "服务商描述")
        private String tdlnrName;

        @ApiModelProperty(value = "作业人员")
        private String userCode;

        @ApiModelProperty(value = "人员类型")
        private String userType;

        @ApiModelProperty(value = "岗位")
        private String userPosition;

        @ApiModelProperty(value = "班组")
        private String classGroup;

        @ApiModelProperty(value = "门店")
        private String nextSite;

        @ApiModelProperty(value = "收货人地址-省")
        private String receiveProvince;

        @ApiModelProperty(value = "收货人地址-市")
        private String receiveCity;

        @ApiModelProperty(value = "收货人地址-区")
        private String receiveArea;

        @ApiModelProperty(value = "运输单号")
        private String transportNo;

        @ApiModelProperty(value = "业务订单")
        private String sourceOrderNo;

        @ApiModelProperty(value = "订单渠道")
        private String orderChannel;

        @ApiModelProperty(value = "下单方")
        private String custCode;

        @ApiModelProperty(value = "直播订单标识")
        private String orderFlag;

        @ApiModelProperty(value = "单据类型")
        private String orderType;

        @ApiModelProperty(value = "商品类型")
        private String skuType;

        @ApiModelProperty(value = "箱数")
        private BigDecimal boxNum;

        @ApiModelProperty(value = "整箱数")
        private BigDecimal boxNumInt;

        @ApiModelProperty(value = "拆零件数")
        private BigDecimal pieceNum;

        @ApiModelProperty(value = "sku行数")
        private BigDecimal skuNum;

        @ApiModelProperty(value = "商品数量")
        private BigDecimal goodsNum;

        @ApiModelProperty(value = "重量")
        private BigDecimal weight;

        @ApiModelProperty(value = "体积")
        private BigDecimal volume;

        @ApiModelProperty(value = "创建时间")
        private String createTime;

        /**
         * 开始时间
         */
        @ApiModelProperty(value = "开始时间")
        private String startCreateTime;
        /**
         * 结束时间
         */
        @ApiModelProperty(value = "结束时间")
        private String endCreateTime;

        @ApiModelProperty(value = "作业开始时间")
        private String startGenerateTime;
        /**
         * 结束时间
         */
        @ApiModelProperty(value = "作业结束时间")
        private String endGenerateTime;

        @ApiModelProperty(value = "导出excel操作")
        private boolean importExcel;
}
