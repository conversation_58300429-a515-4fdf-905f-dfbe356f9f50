package com.rome.stock.innerservice.api.dto.template;

import cn.afterturn.easypoi.excel.annotation.Excel;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 类ShopSaleDataDTO的实现描述：门店销量数据模板
 *
 * <AUTHOR> 2020/9/22 17:32
 */
@Data
@EqualsAndHashCode
public class ShopSaleDataDTO implements Serializable {

    @Excel(name = "工厂",orderNum = "1")
    private String factoryCode;

    @Excel(name = "商品编码",orderNum = "2")
    private String skuCode;

    @Excel(name = "销售数",orderNum = "3")
    private BigDecimal saleNum;

    @Excel(name = "真实库存",orderNum = "4")
    private BigDecimal realQty;

    @Excel(name = "在途库存",orderNum = "5")
    private BigDecimal onroadQty;

    @Excel(name = "当前时间",orderNum = "6",exportFormat = "yyyy-MM-dd HH:mm:ss")
    private Date nowDate;
}
