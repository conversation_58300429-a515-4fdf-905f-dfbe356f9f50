package com.rome.stock.innerservice.api.dto.warehouserecord;

import com.rome.stock.innerservice.api.dto.WarehouseRecordDetailDTO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotEmpty;
import java.util.List;

/**
 * @Description
 * <AUTHOR>
 * @Date 2023-07-25
 * @Version 1.0
 */

@Data
@EqualsAndHashCode
public class ShopAllocationQueryStockDTO {


    @ApiModelProperty(value = "门店编号")
    @NotEmpty
    private String shopCode;


    @ApiModelProperty(value = "调拨明细")
    @NotEmpty
    private List<ShopAllocationQueryStockDetailDTO> details;
}
