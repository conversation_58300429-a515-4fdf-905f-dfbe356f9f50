/**
 * Filename RwSkuServiceLabDTO.java
 * Company 上海来伊份科技有限公司。
 * <AUTHOR>
 * @version 
 */
package com.rome.stock.innerservice.api.dto.cloudshop;


import com.rome.stock.common.page.Pagination;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import java.util.Date;
import java.util.List;

/**
 * 云店仓库sku时效服务标签
 * <AUTHOR>
 * @since 2021-9-23 14:30:10
 */
@Data
@NoArgsConstructor
@EqualsAndHashCode
public class RwSkuServiceLabDTO extends Pagination {

    @ApiModelProperty("唯一主键")
    private Long id;

    @ApiModelProperty("实仓仓库ID")
    private Long realWarehouseId;

    @ApiModelProperty("实仓仓库编号")
    private String realWarehouseCode;

    @ApiModelProperty("实仓仓库名称")
    private String realWarehouseName;

    @ApiModelProperty("商品sku编码")
    private Long skuId;

    @ApiModelProperty("商品sku编码集合")
    private List<Long> skuIds;

    @ApiModelProperty("商品编码")
    private String skuCode;
    
    @ApiModelProperty("商品名称")
    private String skuName;
    
    @ApiModelProperty("云店服务标签主键")
	private Long labId;
    
    @ApiModelProperty("云店服务标签名称")
	private String labName;

    @ApiModelProperty("创建时间")
    private Date createTime;
    
    @ApiModelProperty("更新时间")
    private Date updateTime;
    
    @ApiModelProperty(value = "创建人")
    private Long creator;
    
    @ApiModelProperty(value = "更新人")
    private Long modifier;
    
    @ApiModelProperty(value = "标签类型(1: 门店自提 2: 仓库次日达 3: 供应商到家 4: 供应商自提)")
	private Integer labType;
    
    @ApiModelProperty(value = "标签描述")
	private String labDesc;
    
    @ApiModelProperty(value = "是否启用")
    private Byte isAvailable;

    @Override
    public String toString() {
        return "RwSkuServiceLabDTO{" +
                "skuCode='" + skuCode + '\'' +
                ", realWarehouseCode='" + realWarehouseCode + '\'' +
                ", labId=" + labId +
                '}';
    }
}
