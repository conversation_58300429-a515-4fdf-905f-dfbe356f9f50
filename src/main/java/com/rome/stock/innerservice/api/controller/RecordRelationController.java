/**
 * Filename RecordRelationController.java
 * Company 上海来伊份科技有限公司。
 *
 * <AUTHOR>
 * @version
 */
package com.rome.stock.innerservice.api.controller;

import com.rome.arch.core.clientobject.Response;
import com.rome.arch.core.exception.RomeException;
import com.rome.arch.util.SpringBeanUtil;
import com.rome.arch.util.controller.RomeController;
import com.rome.stock.common.utils.ListUtil;
import com.rome.stock.innerservice.common.ResCode;
import com.rome.stock.innerservice.constant.ResponseMsg;
import com.rome.stock.innerservice.domain.service.RecordRelationService;
import com.rome.stock.innerservice.handler.recordrelation.RecordRelationHandlerExecutor;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import io.swagger.annotations.ApiResponse;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;

/**
 * 单据关联关系
 * <AUTHOR>
 * @since 2024/4/24 11:55
 */
@Slf4j
@RomeController
@RequestMapping("/stock/v1/record_relation")
@Api(tags={"单据关联关系"})
public class RecordRelationController {

    @Autowired
    private RecordRelationService recordRelationService;

    /**
     * 单据关联关系处理，定时任务
     * @param runStopTime 最大运行时间，单位秒数
     * @return
     */
    @ApiOperation(value = "单据关联关系处理，定时任务Job", nickname = "recordRelationTask", produces = MediaType.APPLICATION_JSON_VALUE)
    @ApiResponse(code = 200, message = "success", response = String.class)
    @RequestMapping(value = "/recordRelationTask", method = RequestMethod.POST)
    public Response recordRelationTask(@ApiParam(name = "runStopTime", value = "最大运行时间，单位秒数") @RequestParam("runStopTime") Long runStopTime) {
        try {
            // 为null或者等于0秒时，为1分钟
            if(runStopTime == null || runStopTime < 1) {
                runStopTime = System.currentTimeMillis() + (60 * 1000);
            } else {
                runStopTime = System.currentTimeMillis() + (runStopTime * 1000);
            }
            int num = recordRelationService.recordRelationTask(runStopTime);
            return Response.builderSuccess("处理总条数：" + num);
        } catch (RomeException e) {
            log.error(e.getMessage(), e);
            return ResponseMsg.FAIL.buildMsg(e.getCode(),e.getMessage());
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return ResponseMsg.EXCEPTION.buildMsg(ResCode.STOCK_ERROR_1003_DESC,ResCode.STOCK_ERROR_1003_DESC);
        }
    }

    @ApiOperation(value = "单据关联关系处理，根据单据编码列表", nickname = "recordRelationByRecordCodeList", produces = MediaType.APPLICATION_JSON_VALUE)
    @ApiResponse(code = 200, message = "success", response = Integer.class)
    @RequestMapping(value = "recordRelationByRecordCodeList", method = RequestMethod.POST)
    public Response<String> recordRelationByRecordCodeList(
            @ApiParam(name = "sourceType", value = "来源类型： 1：表sc_warehouse_record单据 2：实仓流水表", defaultValue = "1") @RequestParam("sourceType") Integer sourceType, @RequestBody List<String> recordCodeList){
        try{
            if(recordCodeList == null || recordCodeList.size() > 100000) {
                return Response.builderFail(ResCode.STOCK_ERROR_1002, ResCode.STOCK_ERROR_1002_DESC + "单据号个数不能为空或超过100000");
            }
            int num = 0;
            int maxPage = ListUtil.getPageNum(recordCodeList, 1000);
            for(int i = 1; i <= maxPage; i++) {
                num += recordRelationService.recordRelationByRecordCodeList(sourceType, ListUtil.getPageList(recordCodeList, i,1000));
            }
            return Response.builderSuccess("成功：" + num);
        }catch (RomeException e){
            log.error(e.getMessage(),e);
            return Response.builderFail(e.getCode(),e.getMessage());
        }catch (Exception e){
            log.error(e.getMessage(), e);
            return Response.builderFail(ResCode.STOCK_ERROR_1003,ResCode.STOCK_ERROR_1003_DESC);
        }
    }

    @ApiOperation(value = "单据关联关系处理，重试初始化handler，测试专用", nickname = "reInitHandler", produces = MediaType.APPLICATION_JSON_VALUE)
    @ApiResponse(code = 200, message = "success", response = String.class)
    @RequestMapping(value = "/reInitHandler", method = RequestMethod.POST)
    public Response reInitHandler() {
        try {
            RecordRelationHandlerExecutor executor = SpringBeanUtil.getBean(RecordRelationHandlerExecutor.class);
            executor.afterPropertiesSet();
            executor.afterSingletonsInstantiated();
            return Response.builderSuccess("");
        } catch (RomeException e) {
            log.error(e.getMessage(), e);
            return ResponseMsg.FAIL.buildMsg(e.getCode(),e.getMessage());
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return ResponseMsg.EXCEPTION.buildMsg(ResCode.STOCK_ERROR_1003_DESC, e.getMessage());
        }
    }

}
