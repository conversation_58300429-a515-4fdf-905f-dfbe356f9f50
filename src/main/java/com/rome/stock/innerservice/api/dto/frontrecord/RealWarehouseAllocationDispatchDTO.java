package com.rome.stock.innerservice.api.dto.frontrecord;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

/**
 * DispatchNoticeDTO类的实现描述： 派车
 *
 * <AUTHOR> 2019/6/17 19:48
 */
@Data
@NoArgsConstructor
@EqualsAndHashCode
public class RealWarehouseAllocationDispatchDTO extends DispatchNoticeDTO{

    @ApiModelProperty("调出仓库id")
    private Long outWarehouseId;

    @ApiModelProperty("调出仓库工厂编号")
    private String outFactoryCode;

    @ApiModelProperty("调出仓库编号")
    private String outWarehouseCode;

    @ApiModelProperty("调入仓库id")
    private Long inWarehouseId;

    @ApiModelProperty("调入仓库工厂编号")
    private String inFactoryCode;

    @ApiModelProperty("调入仓库编号")
    private String inWarehouseCode;

    @ApiModelProperty("调拨类型")
    private Integer businessType;
}
