package com.rome.stock.innerservice.api.dto;

import com.alibaba.fastjson.annotation.JSONField;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.rome.stock.core.infrastructure.dataobject.core.CoreStockOpFactoryDO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;
import java.util.List;

@ApiModel(value = "ChargeAgainstCreateDTO", description = "")
@Data
@EqualsAndHashCode
public class ChargeAgainstCreateDTO {

    /**
     * id
     */
    private Long id;

    /**
     * 单据编号
     */
    private String recordCode;

    /**
     * 单据类型
     */
    private Integer recordType;

    /**
     * 业务类型：1:入库单冲销（出库单） 2:出库单冲销（入库单）
     */
    private Integer businessType;

    /**
     * 单据状态：0初始状态 -1 已确认 -2已取消 -3部分完成 -4全部完成
     */
    private Integer recordStatus;

    /**
     * 原单据实体仓库id
     */
    @ApiModelProperty(value = "原单据实体仓库id", required = true)
    private Long originRealWarehouseId;

    @ApiModelProperty(value = "原单据实体仓库Code", required = true)
    private String originRealWarehouseCode;

    @ApiModelProperty(value = "原单据工厂编号", required = true)
    private String originFactoryCode;

    /**
     * 冲销单据实体仓库id
     */
    @ApiModelProperty(value = "冲销单据实体仓库id", required = true)
    private Long realWarehouseId;

    @ApiModelProperty(value = "冲销单据实体仓库Code", required = true)
    private String realWarehouseCode;

    /**
     * 原出入库单号
     */
    @ApiModelProperty(value = "原出入库单号", required = true)
    private String originWarehouseRecordCode;

    /**
     * 原单号（入库单冲销为收货单号，出库单冲销为出库单号）
     */
    @ApiModelProperty(value = "原单号（入库单冲销为收货单号，出库单冲销为出库单号）", required = true)
    private String originRecordCode;

    /**
     * 原业务单号
     */
    @ApiModelProperty(value = "原业务单号", required = true)
    private String originBusinessCode;

    /**
     * 业务单号
     */
    private String businessCode;

    /**
     * 备注
     */
    @ApiModelProperty(value = "备注")
    private String remark;

    /**
     * 冲销原因code
     */
    @ApiModelProperty(value = "冲销原因code", required = true)
    private String reasonCode;

    /**
     * 推送上游系统状态 0---无需推送 1--待推送 2--已推送
     */
    private Integer syncStatus;

    /**
     * 冲销操作人
     */
    @ApiModelProperty(value = "冲销操作人", required = true)
    private String operator;

    /**
     * 冲销财务时间
     */
    @ApiModelProperty(value = "冲销财务时间", required = true)
    private Date chargeAgainstDate;

    /**
     * 创建人
     */
    @ApiModelProperty(value = "创建人")
    private Long creator;
    /**
     * 更新人
     */
    @ApiModelProperty(value = "更新人")
    private Long modifier;

    @ApiModelProperty(value = "成本中心编号")
    private String costCenterCode;

    @ApiModelProperty(value = "原始单据类型")
    private Integer originRecordType;

    /**
     * 冲销明细数据
     */
    @ApiModelProperty(value = "冲销明细数据", required = true)
    private List<ChargeAgainstCreateDetailDTO> chargeAgainstDetails;

    @ApiModelProperty(value = "操作库存对象（内部接口使用）",hidden = true)
    @JsonIgnore
    @JSONField(serialize = false)
    private CoreStockOpFactoryDO stockOpFactoryDO = new CoreStockOpFactoryDO();
}
