package com.rome.stock.innerservice.api.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @Description 批次信息DTO
 * @date 2020/6/18 17:09
 */
@Data
public class BatchStockChangeFlowDTO extends Pagination {
	
	@ApiModelProperty("唯一主键")
    private Long id;

	@ApiModelProperty("商品sku编码")
    private Long skuId;

	@ApiModelProperty("商品sku编码")
    private String skuCode;
	
	@ApiModelProperty("变更前库存数量")
    private BigDecimal beforeChangeQty;
    
    @ApiModelProperty("变更后库存数量")
    private BigDecimal afterChangeQty;

	@ApiModelProperty("真实库存,变更数量")
    private BigDecimal skuQty;

	@ApiModelProperty("批次编码-批次号")
    private String batchCode;

	@ApiModelProperty("实体仓库id")
    private Long realWarehouseId;

	@ApiModelProperty("库存类型： 1-出库,2-入库")
    private Integer stockType;
	
	@ApiModelProperty("单据类型")
    private Integer recordType;

	@ApiModelProperty("单据编号")
    private String recordCode;
	
    @ApiModelProperty(value = "创建时间")
    private Date createTime;
    
    @ApiModelProperty(value = "出入库时间")
    private Date updateTime;
    
    @ApiModelProperty(value = "商品名称")
    private String skuCname;
    
    @ApiModelProperty(value = "主单位(商品单位)")
    private String unit;
    
    @ApiModelProperty(value = "仓库名称")
    private String realWarehouseName;
    
    @ApiModelProperty(value = "实仓code")
    private String realWarehouseCode;
    
    @ApiModelProperty(value = "出库单类型名称")
    private String recordTypeName;
    
    @ApiModelProperty(value = "sap交货单号")
    private String sapOrderCode;
	
	@ApiModelProperty(value = "查询,开始时间")
	private Date startTime;
	
	@ApiModelProperty(value = "查询,结束时间")
	private Date endTime;
	
	@ApiModelProperty(value = "查询,skuIds")
	private List<Long> skuIds;

	@ApiModelProperty(value = "查询,SAP交货单号")
	private List<String> sapOrderCodes;
	
	@ApiModelProperty("查询,单据类型列表")
	private List<Integer> recordTypes;
	
	@ApiModelProperty(value = "查询,出入库单据号")
	private List<String> recordCodeList;
	
	@ApiModelProperty("查询,批次编码-批次号")
    private List<String> batchCodeList;
	
	/**
     * 库存类型名称
     */
    @ApiModelProperty(value = "库存类型名称")
    private String stockTypeName;
    
    /**
	 * 仓库出入库类型
	 */
	@ApiModelProperty(value = "仓库出入库类型")
	private List<Integer> stockTypes;

    @ApiModelProperty(value = "生产日期")
    private Date productDate;

    @ApiModelProperty(value = "入库日期")
    private Date entryDate;

    @ApiModelProperty(value = "是否支持原箱库存")
    public boolean supportBox;
}
   