package com.rome.stock.innerservice.api.controller;

import com.github.pagehelper.PageInfo;
import com.rome.arch.core.clientobject.Response;
import com.rome.arch.core.exception.RomeException;
import com.rome.arch.util.controller.RomeController;
import com.rome.stock.innerservice.api.dto.GroupAddExpressCodeDTO;
import com.rome.stock.innerservice.api.dto.groupbuy.GroupWarehouseRecordDetailTemplate;
import com.rome.stock.innerservice.api.dto.groupbuy.GroupWarehouseRecordTemplate;
import com.rome.stock.innerservice.api.dto.qry.GroupWarehouseRecordCondition;
import com.rome.stock.innerservice.api.dto.warehouserecord.GroupWarehouseRecordDTO;
import com.rome.stock.innerservice.api.dto.warehouserecord.PrintItem;
import com.rome.stock.innerservice.common.ResCode;
import com.rome.stock.innerservice.domain.service.ShopRetailService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import io.swagger.annotations.ApiResponse;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;

import javax.annotation.Resource;
import java.util.List;

@Slf4j
@RomeController
@RequestMapping("/stock/v1/warehouse_group")
@Api(tags={"运营平台团购出库查询"})
public class GroupWarehouseRecordController {

	@Resource
	private ShopRetailService shopRetailService;


	@ApiOperation(value = "查询团购发货出库单", nickname = "query_group_warehouse_record", produces = MediaType.APPLICATION_JSON_VALUE)
	@ApiResponse(code = 200, message = "success")
	@RequestMapping(value = "/list", method = RequestMethod.POST)
	public Response<PageInfo<GroupWarehouseRecordDTO>> queryByCondition(@ApiParam(name = "condition", value = "查询条件") @RequestBody GroupWarehouseRecordCondition groupWarehouseRecordCondition) {
		try {
			return Response.builderSuccess(shopRetailService.queryGroupWarehouseRecordList(groupWarehouseRecordCondition));
		} catch (RomeException e) {
			log.error(e.getMessage(), e);
			return Response.builderFail(e.getCode(), e.getMessage());
		} catch (Exception e) {
			log.error(e.getMessage(), e);
			return Response.builderFail(ResCode.STOCK_ERROR_1003_DESC, ResCode.STOCK_ERROR_1003_DESC);
		}
	}

	@ApiOperation(value = "跟据团购出库单id查询详情", nickname = "queryGroupWarehouseRecordInfoByRecordId", produces = MediaType.APPLICATION_JSON_VALUE)
	@ApiResponse(code = 200, message = "success")
	@RequestMapping(value = "/queryGroupWarehouseRecordInfoByRecordId", method = RequestMethod.POST)
	public Response<GroupWarehouseRecordDTO> queryGroupWarehouseRecordInfoByRecordId(@RequestBody GroupWarehouseRecordCondition groupWarehouseRecordCondition) {
		try {
			return Response.builderSuccess(shopRetailService.queryGroupWarehouseRecordInfoByRecordId(groupWarehouseRecordCondition));
		} catch (RomeException e) {
			log.error(e.getMessage(), e);
			return Response.builderFail(e.getCode(), e.getMessage());
		} catch (Exception e) {
			log.error(e.getMessage(), e);
			return Response.builderFail(ResCode.STOCK_ERROR_1003_DESC, ResCode.STOCK_ERROR_1003_DESC);
		}
	}

    @ApiOperation(value = "添加或修改运单号DTO", nickname = "addOrUpdateExpressCode", produces = MediaType.APPLICATION_JSON_VALUE)
    @ApiResponse(code = 200, message = "success")
    @RequestMapping(value = "/addOrUpdateExpressCode", method = RequestMethod.POST)
    public Response addOrUpdateExpressCode(@ApiParam(name = "groupAddExpressCodeDTO", value = "添加或修改运单号DTO") @RequestBody GroupAddExpressCodeDTO groupAddExpressCodeDTO) {
        try {
            shopRetailService.addOrUpdateExpressCode(groupAddExpressCodeDTO);
            return Response.builderSuccess(true);
        } catch (RomeException e) {
            log.error(e.getMessage(), e);
            return Response.builderFail(e.getCode(), e.getMessage());
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return Response.builderFail(ResCode.STOCK_ERROR_1003_DESC, ResCode.STOCK_ERROR_1003_DESC);
        }
    }

	@ApiOperation(value = "导出查询团购发货出库单", nickname = "exportGroupWarehouseRecord", produces = MediaType.APPLICATION_JSON_VALUE)
	@ApiResponse(code = 200, message = "success")
	@RequestMapping(value = "/exportGroupWarehouseRecord", method = RequestMethod.POST)
	public Response<List<GroupWarehouseRecordTemplate>> exportGroupWarehouseRecord(@ApiParam(name = "condition", value = "查询条件") @RequestBody GroupWarehouseRecordCondition groupWarehouseRecordCondition) {
		try {
			return Response.builderSuccess(shopRetailService.exportGroupWarehouseRecord(groupWarehouseRecordCondition));
		} catch (RomeException e) {
			log.error(e.getMessage(), e);
			return Response.builderFail(e.getCode(), e.getMessage());
		} catch (Exception e) {
			log.error(e.getMessage(), e);
			return Response.builderFail(ResCode.STOCK_ERROR_1003_DESC, ResCode.STOCK_ERROR_1003_DESC);
		}
	}

	@ApiOperation(value = "导出查询团购发货出库单明细", nickname = "exportGroupWarehouseRecordDetail", produces = MediaType.APPLICATION_JSON_VALUE)
	@ApiResponse(code = 200, message = "success")
	@RequestMapping(value = "/exportGroupWarehouseRecordDetail", method = RequestMethod.POST)
	public Response<List<GroupWarehouseRecordDetailTemplate>> exportGroupWarehouseRecordDetail(@ApiParam(name = "condition", value = "查询条件") @RequestBody GroupWarehouseRecordCondition groupWarehouseRecordCondition) {
		try {
			return Response.builderSuccess(shopRetailService.exportGroupWarehouseRecordDetail(groupWarehouseRecordCondition));
		} catch (RomeException e) {
			log.error(e.getMessage(), e);
			return Response.builderFail(e.getCode(), e.getMessage());
		} catch (Exception e) {
			log.error(e.getMessage(), e);
			return Response.builderFail(ResCode.STOCK_ERROR_1003_DESC, ResCode.STOCK_ERROR_1003_DESC);
		}
	}

    @ApiOperation(value = "团购发货出库单明细打印数据", nickname = "groupWarehousePrintDetail", produces = MediaType.APPLICATION_JSON_VALUE)
    @ApiResponse(code = 200, message = "success")
    @RequestMapping(value = "/groupWarehousePrintDetail", method = RequestMethod.POST)
    public Response<List<PrintItem>> groupWarehousePrintDetail(@ApiParam(name = "recordCode", value = "出库单号") @RequestParam("recordCode")String recordCode) {
        try {
            return Response.builderSuccess(shopRetailService.groupWarehousePrintDetail(recordCode));
        } catch (RomeException e) {
            log.error(e.getMessage(), e);
            return Response.builderFail(e.getCode(), e.getMessage());
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return Response.builderFail(ResCode.STOCK_ERROR_1003_DESC, ResCode.STOCK_ERROR_1003_DESC);
        }
    }

}
