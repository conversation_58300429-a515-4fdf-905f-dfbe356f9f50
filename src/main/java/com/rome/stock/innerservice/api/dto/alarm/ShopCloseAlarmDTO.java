package com.rome.stock.innerservice.api.dto.alarm;

import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 * @Description ShopCloseAlarmQueryDTO
 * <AUTHOR>
 * @Date 2024/6/27
 **/
@Data
public class ShopCloseAlarmDTO {

    /**
     * 门店编号
     */
    private String shopCode;

    /**
     * 最大更新时间
     */
    private Date maxUpdateTime;

    /**
     * 距离当前天数
     */
    private Integer dayCount;

    /**
     * 在途单据数量
     */
    private Integer sumRecordCount;

    /**
     * 结余物料数量
     */
    private Integer skuCount;

    /**
     * 结余库存数量
     */
    private BigDecimal sumStockQty;

    /**
     * 门店对应的仓库id
     */
    private Long realWarehouseId;


}
