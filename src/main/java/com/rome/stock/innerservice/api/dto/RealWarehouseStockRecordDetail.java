package com.rome.stock.innerservice.api.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;


@Data
@EqualsAndHashCode
public class RealWarehouseStockRecordDetail{

	/**
	 * 唯一主键
	 */
	@ApiModelProperty(value = "唯一主键")
	private Long id;
	/**
	 * 所属单据编码
	 */
	@ApiModelProperty(value = "所属单据编码")
	private String recordCode;
	/**
	 * 商品sku编码
	 */
	@ApiModelProperty(value = "商品sku编码")
	private Long skuId;
	/**
	 * 商品数量
	 */
	@ApiModelProperty(value = "商品数量")
	private Long skuQty;
	/**
	 * 虚拟仓库ID
	 */
	@ApiModelProperty(value = "虚拟仓库ID")
	private Long virtualWarehouseId;
	/**
	 * 实仓ID
	 */
	@ApiModelProperty(value = "实仓ID")
	private Long realWarehouseId;
}

	
