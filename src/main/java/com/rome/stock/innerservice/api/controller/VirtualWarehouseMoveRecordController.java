package com.rome.stock.innerservice.api.controller;

import com.github.pagehelper.PageInfo;
import com.rome.arch.core.clientobject.Response;
import com.rome.arch.core.exception.RomeException;
import com.rome.arch.util.controller.RomeController;
import com.rome.stock.innerservice.api.dto.VirtualWarehouse;
import com.rome.stock.innerservice.api.dto.VirtualWarehouseMoveRecord;
import com.rome.stock.innerservice.api.dto.VirtualWarehouseSku;
import com.rome.stock.innerservice.api.dto.template.VmMoveRecordTemplateDTO;
import com.rome.stock.innerservice.common.ResCode;
import com.rome.stock.innerservice.constant.RecordTypeVO;
import com.rome.stock.innerservice.constant.ResponseMsg;
import com.rome.stock.innerservice.domain.service.VirtualWarehouseMoveRecordService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import io.swagger.annotations.ApiResponse;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;

@Slf4j
@RomeController
@RequestMapping("/stock/v1/virtual_warehouse_move")
@Api(tags={"虚仓转移单管理"})
public class VirtualWarehouseMoveRecordController {
    @Resource
    private VirtualWarehouseMoveRecordService virtualWarehouseMoveRecordService;

    @ApiOperation(value = "虚仓转移单管理列表页面", nickname = "getVirtualWarehouseMoveRecord", produces = MediaType.APPLICATION_JSON_VALUE)
    @ApiResponse(code = 200, message = "success", response = List.class)
    @RequestMapping(value = "/getVirtualWarehouseMoveRecord", method = RequestMethod.POST)
    public Response<PageInfo<VirtualWarehouseMoveRecord>> getVirtualWarehouseMoveRecord(@ApiParam(name = "VirtualWarehouseMoveRecord", value = "虚拟仓库移库单dto") @RequestBody VirtualWarehouseMoveRecord virDto) {
        try {
            PageInfo<VirtualWarehouseMoveRecord> pageList = virtualWarehouseMoveRecordService.getVirtualWarehouseMoveRecord(virDto);
            return Response.builderSuccess(pageList);
        } catch (RomeException e) {
            log.error(e.getMessage(), e);
            return ResponseMsg.EXCEPTION.buildMsg(e.getCode(), e.getMessage());
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return ResponseMsg.EXCEPTION.buildMsg(ResCode.STOCK_ERROR_1003, ResCode.STOCK_ERROR_1003_DESC);
        }
    }

    @ApiOperation(value = "查询某虚仓中所有商品信息", nickname = "getVirtualWarehouseSkusById", produces = MediaType.APPLICATION_JSON_VALUE)
    @ApiResponse(code = 200, message = "success", response = List.class)
    @RequestMapping(value = "/getVirtualWarehouseSkusById", method = RequestMethod.GET)
    public Response<List<VirtualWarehouseSku>> getVirtualWarehouseSkusById(@ApiParam(name = "VirtualWarehouseId", value = "虚拟仓库id") @RequestParam(value = "virId") String virId) {
        try {
            List<VirtualWarehouseSku> skuList = virtualWarehouseMoveRecordService.getVirtualWarehouseSkusById(Long.parseLong(virId));
            return Response.builderSuccess(skuList);
        } catch (RomeException e) {
            log.error(e.getMessage(), e);
            return ResponseMsg.EXCEPTION.buildMsg(e.getCode(), e.getMessage());
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return ResponseMsg.EXCEPTION.buildMsg(ResCode.STOCK_ERROR_1003, ResCode.STOCK_ERROR_1003_DESC);
        }
    }

    @ApiOperation(value = "提交单据", nickname = "updateVirtualWarehouseMoveRecord", produces = MediaType.APPLICATION_JSON_VALUE)
    @ApiResponse(code = 200, message = "success", response = List.class)
    @RequestMapping(value = "/updateVirtualWarehouseMoveRecord", method = RequestMethod.POST)
    public Response updateVirtualWarehouseMoveRecord(@ApiParam(name = "VirtualWarehouseMoveRecord", value = "提交单据信息") @RequestBody VirtualWarehouseMoveRecord virtualWarehouseMoveRecord) {
        try {
            String result = virtualWarehouseMoveRecordService.updateVirtualWarehouseMoveRecord(virtualWarehouseMoveRecord);
            if(result.indexOf("商品库存不足") > -1) {
            	return ResponseMsg.EXCEPTION.buildMsg(ResCode.STOCK_ERROR_1003, result);
            }
            return Response.builderSuccess("");
        } catch (RomeException e) {
            log.error(e.getMessage(), e);
            return ResponseMsg.EXCEPTION.buildMsg(e.getCode(), e.getMessage());
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return ResponseMsg.EXCEPTION.buildMsg(ResCode.STOCK_ERROR_1003, ResCode.STOCK_ERROR_1003_DESC);
        }
    }

    @ApiOperation(value = "根据id查询单据详情", nickname = "getVirtualWarehouseMoveRecordDetail", produces = MediaType.APPLICATION_JSON_VALUE)
    @ApiResponse(code = 200, message = "success", response = List.class)
    @RequestMapping(value = "/getVirtualWarehouseMoveRecordDetail", method = RequestMethod.GET)
    public Response<VirtualWarehouseMoveRecord> getVirtualWarehouseMoveRecordDetail(@ApiParam(name = "recordId", value = "单据id") @RequestParam(value = "recordId") String recordId) {
        try {
            return Response.builderSuccess(virtualWarehouseMoveRecordService.getVirtualWarehouseMoveRecordDetailById(Long.parseLong(recordId)));
        } catch (RomeException e) {
            log.error(e.getMessage(), e);
            return ResponseMsg.EXCEPTION.buildMsg(e.getCode(), e.getMessage());
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return ResponseMsg.EXCEPTION.buildMsg(ResCode.STOCK_ERROR_1003, ResCode.STOCK_ERROR_1003_DESC);
        }
    }

    @ApiOperation(value = "保存单据", nickname = "saveVirtualWarehouseMoveRecord", produces = MediaType.APPLICATION_JSON_VALUE)
    @ApiResponse(code = 200, message = "success", response = List.class)
    @RequestMapping(value = "/saveVirtualWarehouseMoveRecord", method = RequestMethod.POST)
    public Response saveVirtualWarehouseMoveRecord(@ApiParam(name = "VirtualWarehouseMoveRecord", value = "保存单据信息") @RequestBody VirtualWarehouseMoveRecord virtualWarehouseMoveRecord) {
        try {
            virtualWarehouseMoveRecordService.addOrUpdateVirtualWarehouseMoveRecord(virtualWarehouseMoveRecord);
            return Response.builderSuccess("");
        } catch (RomeException e) {
            log.error(e.getMessage(), e);
            return ResponseMsg.EXCEPTION.buildMsg(e.getCode(), e.getMessage());
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return ResponseMsg.EXCEPTION.buildMsg(ResCode.STOCK_ERROR_1003, ResCode.STOCK_ERROR_1003_DESC);
        }
    }

    @ApiOperation(value = "删除保存的单据", nickname = "delVirtualWarehouseMoveRecord", produces = MediaType.APPLICATION_JSON_VALUE)
    @ApiResponse(code = 200, message = "success", response = List.class)
    @RequestMapping(value = "/delVirtualWarehouseMoveRecord", method = RequestMethod.POST)
    public Response delVirtualWarehouseMoveRecord(@ApiParam(name = "recordId", value = "保存单据的id") @RequestParam("recordId") Long recordId,@ApiParam(name = "userId", value = "用户id") @RequestParam("userId") Long userId) {
        try {
            virtualWarehouseMoveRecordService.delVirtualWarehouseMoveRecord(recordId,userId);
            return Response.builderSuccess("");
        } catch (RomeException e) {
            log.error(e.getMessage(), e);
            return ResponseMsg.EXCEPTION.buildMsg(e.getCode(), e.getMessage());
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return ResponseMsg.EXCEPTION.buildMsg(ResCode.STOCK_ERROR_1003, ResCode.STOCK_ERROR_1003_DESC);
        }
    }
    @ApiOperation(value = "根据虚仓id和转移单类型查询相同实仓下面的其它虚仓信息", nickname = "getOtherVirtualWarehouseById",produces = MediaType.APPLICATION_JSON_VALUE)
    @ApiResponse(code = 200, message = "success", response = List.class)
    @RequestMapping(value = "/getOtherVirtualWarehouseById", method = RequestMethod.POST)
    public Response<List<VirtualWarehouse>> getOtherVirtualWarehouseById(@RequestParam("recordType") Integer recordType, @RequestParam("virId") Long virId) {
        try {
            return Response.builderSuccess(virtualWarehouseMoveRecordService.getOtherVirtualWarehouse(recordType,virId));
        } catch (RomeException e) {
            log.error(e.getMessage(), e);
            return ResponseMsg.EXCEPTION.buildMsg(e.getCode(), e.getMessage());
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return ResponseMsg.EXCEPTION.buildMsg(ResCode.STOCK_ERROR_1003, ResCode.STOCK_ERROR_1003_DESC);
        }
    }
    /**
     * 获取所有单据类型(从类型枚举类中获取)
     * @return
     */
    @ApiOperation(value = "获取单据所有类型", nickname = "list_move_record_type", produces = MediaType.APPLICATION_JSON_VALUE)
    @ApiResponse(code = 200, message = "success", response = Map.class)
    @GetMapping(value = "/listMoveRecordType")
    public Response<Map<Integer, String>> getMoveRecordType() {
        try {
            Map<Integer, String> moveRecordType = RecordTypeVO.getMoveRecordType();
            return Response.builderSuccess(moveRecordType);
        } catch (RomeException e){
            log.error(e.getMessage(), e);
            return Response.builderFail(e.getCode(), e.getMessage());
        } catch (Exception e){
            log.error(e.getMessage(), e);
            return Response.builderFail(ResCode.STOCK_ERROR_1003, ResCode.STOCK_ERROR_1003_DESC);
        }
    }

    /**
     * @Description: 虚仓转移批量导入 <br>
     *
     * <AUTHOR> 2019/12/10
     * @param dataList
     * @return 
     */
    @ApiOperation(value = "虚仓转移批量导入", nickname = "VWBatchImport", produces = MediaType.MULTIPART_FORM_DATA_VALUE)
    @PostMapping(value = "/VWBatchImport")
    public Response VWBatchImport(@RequestBody List<VmMoveRecordTemplateDTO> dataList, @RequestParam("userId") Long userId) {
        try {
            virtualWarehouseMoveRecordService.VWBatchImport(dataList, userId);
            return Response.builderSuccess("");
        } catch (RomeException e){
            log.error("虚仓导入异常 ==> {}", e.getMessage(), e);
            return Response.builderFail(e.getCode(), e.getMessage());
        } catch (Exception e){
            log.error("虚仓导入系统异常 ==> {}", e.getMessage(), e);
            return Response.builderFail(ResCode.STOCK_ERROR_1003, e.getMessage());
        }
    }
}
