package com.rome.stock.innerservice.api.dto.qry;

import com.rome.stock.innerservice.api.dto.Pagination;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;
import java.util.List;

@Data
public class SalesReturnRecordParamDTO extends Pagination {

    private String recordCode;

    private String outRecordCode;

    private String channelCodes;

    private List<String> channelCodeList;

    private Integer recordStatus;

    private Long realWarehouseId;

    private String realWarehouseCode;

    private List<Long> warehouseRecordIds;

    private Integer recordType;

    @ApiModelProperty(required = true, dataType = "date", value = "时间（yyyy-MM-dd）")
    private Date startCrateTime;

    @ApiModelProperty
    private Date endCreateTime;

    private String reason;
}
