package com.rome.stock.innerservice.api.dto;

import lombok.Data;

import java.math.BigDecimal;

/**
 * @Description SafeStockConfigQueryDTO
 * <AUTHOR>
 * @Date 2024/7/18
 **/
@Data
public class SafeStockConfigQueryDTO {


    private Integer pageSize;

    private Integer pageIndex;



    /**
     * 渠道Code
     */
    private String channelCode;

    /**
     * 渠道类型
     */
    private String channelType;


    /**
     * 是否可用：0-否，1-是
     */
    private Byte isAvailable;

    /**
     * 开始创建时间
     */
    private String startCreateTime;

    /**
     * 结束创建时间
     */
    private String endCreateTime;
}
