package com.rome.stock.innerservice.api.dto.wms;

import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;


/**
 * 订单信息
 */
@Data
@EqualsAndHashCode
public class OrderLine {

    /**
     * 入库单的行号
     */
    private String orderLineNo;

    /**
     * 行状态1待收货，2已收货
     */
    private Integer status;

    /**
     * 商品编码
     */
    private String itemCode;

    /**
     * 应收商品数量
     */
    private BigDecimal planQty;

    /**
     * 单位
     */
    private String unit;

    /**
     * 预期到货时间(YYYY-MM-DD HH:MM:SS)
     */
    private String expectStartTime;

    private Integer isFree;
}
