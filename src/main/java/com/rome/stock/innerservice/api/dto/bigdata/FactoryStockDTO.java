package com.rome.stock.innerservice.api.dto.bigdata;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

/**
 * 工厂、skuCode维度查询库存
 */
@Data
@ApiModel("工厂、skuCode维度查询库存DTO")
public class FactoryStockDTO {

    @ApiModelProperty("真实库存")
    private BigDecimal realQty;

    @ApiModelProperty("可用库存")
    private BigDecimal availableQty;

    @ApiModelProperty("在途库存")
    private BigDecimal onroadQty;

    @ApiModelProperty("工厂编号")
    private String factoryCode;

    @ApiModelProperty("商品编码")
    private String skuCode;

    @ApiModelProperty("仓库层级")
    private Integer realWarehouseRank;

}
