package com.rome.stock.innerservice.api.dto;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;

@Data
public class OnlineRecordResult implements Serializable {

    private Long id;
    private String doRecordCode;
    private String soRecordCode;
    private String userCode;
    private Integer recordType;
    private Integer recordStatus;
    private String channelCode;
    private Long realWarehouseId;
    private String realWarehouseName;
    private Date createTime;

}
