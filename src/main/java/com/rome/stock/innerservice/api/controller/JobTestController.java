package com.rome.stock.innerservice.api.controller;

import com.rome.arch.core.clientobject.Response;
import com.rome.arch.util.controller.RomeController;
import com.rome.stock.innerservice.constant.ResponseMsg;
import com.rome.stock.innerservice.domain.service.BatchStockBoxDetailService;
import com.rome.stock.innerservice.infrastructure.dataobject.BatchStockChangeFlowDO;
import com.rome.stock.innerservice.infrastructure.mapper.BatchStockChangeFlowMapper;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiResponse;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;

import javax.annotation.Resource;
import java.util.Arrays;
import java.util.List;

/**
 * @Doc:Job任务测试
 * @Author: lchy
 * @Date: 2019/5/22
 * @Version 1.0
 */
@Slf4j
@RomeController
@RequestMapping("/stock/v1/jobTest")
@Api(tags={"定时任务测试接入"})
public class JobTestController {

	@Resource
	private BatchStockBoxDetailService boxDetailService;
	@Resource
	private BatchStockChangeFlowMapper batchStockChangeFlowMapper;

	@ApiOperation(value = "jobTest", nickname = "jobTest", produces = MediaType.APPLICATION_JSON_VALUE)
	@ApiResponse(code = 200, message = "success")
	@RequestMapping(value = "/first", method = RequestMethod.GET)
	public Response jobTest() {
		return ResponseMsg.SUCCESS.buildMsg("this job has executed。。。");
	}

	@ApiOperation(value = "根据库存批次流水来操作原箱库存", nickname = "operateBoxStockByFlowList", produces = MediaType.APPLICATION_JSON_VALUE)
	@ApiResponse(code = 200, message = "success")
	@RequestMapping(value = "/operateBoxStockByFlowList", method = RequestMethod.GET)
	public Response operateBoxStockByFlowList(@RequestParam("recordCodes")String recordCodes) {
		List<String> recordCodeList = Arrays.asList(recordCodes.split(","));
		List<BatchStockChangeFlowDO> batchStockChangeFlowDOS = batchStockChangeFlowMapper.queryBatchStockChangeFlowByRecordCodeList(recordCodeList);
		boxDetailService.operateBoxStockByFlowList(batchStockChangeFlowDOS);
		return ResponseMsg.SUCCESS.buildMsg("操作成功");
	}



}
