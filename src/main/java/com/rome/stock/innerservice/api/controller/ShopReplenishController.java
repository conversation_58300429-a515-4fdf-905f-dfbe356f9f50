package com.rome.stock.innerservice.api.controller;

import com.alibaba.fastjson.JSON;
import com.rome.arch.core.clientobject.Response;
import com.rome.arch.core.exception.RomeException;
import com.rome.arch.util.controller.RomeController;
import com.rome.stock.common.constants.KibanaLogConstants;
import com.rome.stock.common.enums.warehouse.WarehouseRecordTypeVO;
import com.rome.stock.innerservice.api.dto.RealWarehouse;
import com.rome.stock.innerservice.api.dto.WarehouseRecordPageDTO;
import com.rome.stock.innerservice.api.dto.WarehouseToBRecord;
import com.rome.stock.innerservice.api.dto.frontrecord.DispatchNoticeDTO;
import com.rome.stock.innerservice.api.dto.frontrecord.ReplenishSkuInfo;
import com.rome.stock.innerservice.api.dto.frontrecord.SkuStock;
import com.rome.stock.innerservice.api.dto.warehouserecord.*;
import com.rome.stock.innerservice.common.AlikAssert;
import com.rome.stock.innerservice.common.CoreKibanaLog;
import com.rome.stock.innerservice.common.RedisUtil;
import com.rome.stock.innerservice.common.ResCode;
import com.rome.stock.innerservice.constant.ParamValidator;
import com.rome.stock.innerservice.constant.ResponseMsg;
import com.rome.stock.innerservice.constant.WarehouseRecordBusinessTypeVO;
import com.rome.stock.innerservice.domain.entity.warehouserecord.WarehouseRecordE;
import com.rome.stock.innerservice.domain.repository.SapInterfaceLogRepository;
import com.rome.stock.innerservice.domain.repository.warehouserecord.WarehouseRecordRepository;
import com.rome.stock.innerservice.domain.service.DispatchNoticeService;
import com.rome.stock.innerservice.domain.service.ShopReplenishService;
import com.rome.stock.core.infrastructure.dataobject.core.CoreRealStockOpDO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import io.swagger.annotations.ApiResponse;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

/**
 * ShopReplenishController类的实现描述：门店补货
 *
 * <AUTHOR> 2019/6/28 10:49
 */
@Slf4j
@RomeController
@RequestMapping("/stock/v1/shop_replenish")
@Api(tags={"门店补货"})
public class ShopReplenishController {
    @Autowired
    private ShopReplenishService shopReplenishService;
    @Autowired
    private DispatchNoticeService dispatchNoticeService;
    @Resource
    private SapInterfaceLogRepository sapInterfaceLogRepository;

    private ParamValidator validator = ParamValidator.INSTANCE;
    @Resource
    private WarehouseRecordRepository warehouseRecordRepository;
    @Resource
    private RedisUtil redisUtil;

    private final static String LOGTYPE = "scOrderCall";

    @ApiOperation(value = "创建门店补货出库单", nickname = "createReplenishRecordBatch", produces = MediaType.APPLICATION_JSON_VALUE)
    @ApiResponse(code = 200, message = "success")
    @RequestMapping(value = "/createOutRecord", method = RequestMethod.POST)
    public Response<RealWarehouse> createOutRecord(@RequestBody @Valid OutWarehouseRecordDTO warehouseRecord) {
        log.warn(CoreKibanaLog.getJobLog(LOGTYPE, "createReplenishRecord", "创建门店补货需求单: " + warehouseRecord.getRecordCode()
                , warehouseRecord));
        String message ="";
        boolean isSucc = false;
        String json = JSON.toJSONString(warehouseRecord);
        try {
            RealWarehouse realWarehouse = shopReplenishService.createOutRecord(warehouseRecord);
            isSucc = true;
            message = "200";
            return ResponseMsg.SUCCESS.buildMsg(realWarehouse);
        } catch (RomeException e) {
            message = e.getMessage();
            log.error("scOrder调用 : " + e.getMessage(), e);
            return Response.builderFail(e.getCode(),e.getMessage());
        } catch (Exception e) {
            message = e.getMessage();
            log.error("scOrder调用 : " + e.getMessage(), e);
            return Response.builderFail(ResCode.STOCK_ERROR_1003_DESC,e.getMessage());
        }finally {
            sapInterfaceLogRepository.saveCallBackInterFaceLog(2, warehouseRecord.getRecordCode(), "createReplenishRecord",
                    json, message, isSucc);
        }
    }

    @ApiOperation(value = "加盟店补货确认(扣减额度成功后调用)", nickname = "confimJoinReplenish", produces = MediaType.APPLICATION_JSON_VALUE)
    @ApiResponse(code = 200, message = "success")
    @RequestMapping(value = "/confimJoinReplenish", method = RequestMethod.POST)
    public Response confimJoinReplenish(@RequestParam("recordCode") String recordCode, @RequestParam("sapPoNo") String sapPoNo){
        log.warn(CoreKibanaLog.getJobLog(LOGTYPE, "confimJoinReplenish", "加盟店补货确认: " + recordCode, sapPoNo));
        String message ="";
        boolean isSucc = false;
        try {
            shopReplenishService.confimJoinReplenish(recordCode, sapPoNo);
            isSucc = true;
            message = "200";
            return ResponseMsg.SUCCESS.buildMsg();
        } catch (RomeException e) {
            message = e.getMessage();
            log.error("scOrder调用 : " + e.getMessage(), e);
            return Response.builderFail(e.getCode(),e.getMessage());
        } catch (Exception e) {
            message = e.getMessage();
            log.error("scOrder调用 : " + e.getMessage(), e);
            return Response.builderFail(ResCode.STOCK_ERROR_1003_DESC,e.getMessage());
        }finally {
            sapInterfaceLogRepository.saveCallBackInterFaceLog(2, recordCode, "confimJoinReplenish",
                    JSON.toJSONString(recordCode), message, isSucc);
        }
    }

    @ApiOperation(value = "创建门店补货入库单", nickname = "createReplenishRecordBatch", produces = MediaType.APPLICATION_JSON_VALUE)
    @ApiResponse(code = 200, message = "success")
    @RequestMapping(value = "/createInRecord", method = RequestMethod.POST)
    public Response<RealWarehouse> createInRecord(@RequestBody @Valid InWarehouseRecordDTO warehouseRecord) {
        log.warn(CoreKibanaLog.getJobLog(LOGTYPE, "createReplenishRecord", "创建门店补货需求单: " + warehouseRecord.getRecordCode()
                , warehouseRecord));
        String message ="";
        boolean isSucc = false;
        String json = JSON.toJSONString(warehouseRecord);
        try {
            RealWarehouse realWarehouse = shopReplenishService.createInRecord(warehouseRecord);
            isSucc = true;
            message = "200";
            return ResponseMsg.SUCCESS.buildMsg(realWarehouse);
        } catch (RomeException e) {
            message = e.getMessage();
            log.error("scOrder调用 : " + e.getMessage(), e);
            return Response.builderFail(e.getCode(),e.getMessage());
        } catch (Exception e) {
            message = e.getMessage();
            log.error("scOrder调用 : " + e.getMessage(), e);
            return Response.builderFail(ResCode.STOCK_ERROR_1003_DESC,e.getMessage());
        }finally {
            sapInterfaceLogRepository.saveCallBackInterFaceLog(2, warehouseRecord.getRecordCode(), "createReplenishRecord",
                    json, message, isSucc);
        }
    }


    @ApiOperation(value = "门店确认采购入库(给POS调用)", nickname = "confirmInWarehouseRecords", produces = MediaType.APPLICATION_JSON_VALUE)
    @ApiResponse(code = 200, message = "success")
    @RequestMapping(value = "/shopConfirmReceiptForPos", method = RequestMethod.POST)
    public Response shopConfirmReceiptForPos(@ApiParam(name = "warehouse", value = "门店采购入库单")
                                       @Valid @RequestBody ShopReceiptOrderDTO shopReceiptOrder) {
        String message ="";
        boolean isSucc = false;
        String json = JSON.toJSONString(shopReceiptOrder);
        try {
            if (StringUtils.isBlank(shopReceiptOrder.getRecordCode())) {
                throw  new RomeException(ResCode.STOCK_ERROR_1002, ResCode.STOCK_ERROR_1002_DESC + ",单据编码不能空");
            }
            //兼容直营转加盟单据
            WarehouseRecordE warehouseRecordE = warehouseRecordRepository.queryWarehouseRecordByRecordCode(shopReceiptOrder.getRecordCode());
            if(Objects.nonNull(warehouseRecordE) && Objects.equals(WarehouseRecordTypeVO.SHOP_INVENTORY_IN_RECORD.getType(),warehouseRecordE.getRecordType())){
                return ResponseMsg.SUCCESS.buildMsg();
            }
            shopReplenishService.shopConfirmReceipt(shopReceiptOrder ,true);
            isSucc = true;
            message = "200";
            return ResponseMsg.SUCCESS.buildMsg();
        } catch (RomeException e) {
            message = e.getMessage();
            log.error("门店确认采购入库(给POS调用) : " + e.getMessage(), e);
            return Response.builderFail(e.getCode(),e.getMessage());
        } catch (Exception e) {
            message = e.getMessage();
            log.error("门店确认采购入库(给POS调用) : " + e.getMessage(), e);
            return Response.builderFail(ResCode.STOCK_ERROR_1003_DESC,e.getMessage());
        }finally {
            sapInterfaceLogRepository.saveCallBackInterFaceLog(1, StringUtils.isEmpty(shopReceiptOrder.getRecordCode()) ? shopReceiptOrder.getRecordCode() : shopReceiptOrder.getRecordCode()
                    , "shopConfirmReceiptForPos", json, message, isSucc);
        }
    }

    @ApiOperation(value = "门店确认采购入库", nickname = "confirmInWarehouseRecords", produces = MediaType.APPLICATION_JSON_VALUE)
    @ApiResponse(code = 200, message = "success")
    @RequestMapping(value = "/shopConfirmReceipt", method = RequestMethod.POST)
    public Response shopConfirmReceipt(@ApiParam(name = "warehouse", value = "门店采购入库单")
                                                               @Valid @RequestBody ShopReceiptOrderDTO shopReceiptOrder) {
        String message ="";
        boolean isSucc = false;
        String json = JSON.toJSONString(shopReceiptOrder);
        try {
            if (StringUtils.isBlank(shopReceiptOrder.getRecordCode()) && StringUtils.isBlank(shopReceiptOrder.getSapOrderCode())) {
                throw  new RomeException(ResCode.STOCK_ERROR_1002, ResCode.STOCK_ERROR_1002_DESC + ",单据编码不能空");
            }
            shopReplenishService.shopConfirmReceipt(shopReceiptOrder ,false);
            isSucc = true;
            message = "200";
            return ResponseMsg.SUCCESS.buildMsg();
        } catch (RomeException e) {
            message = e.getMessage();
            log.error("门店确认采购入库 : " + e.getMessage(), e);
            return Response.builderFail(e.getCode(),e.getMessage());
        } catch (Exception e) {
            message = e.getMessage();
            log.error("门店确认采购入库 : " + e.getMessage(), e);
            return Response.builderFail(ResCode.STOCK_ERROR_1003_DESC,e.getMessage());
        }finally {
            sapInterfaceLogRepository.saveCallBackInterFaceLog(1, StringUtils.isEmpty(shopReceiptOrder.getRecordCode()) ? shopReceiptOrder.getRecordCode() : shopReceiptOrder.getRecordCode()
                    , "shopConfirmReceipt", json, message, isSucc);
        }
    }

    @ApiOperation(value = "批量查询加盟门店渠道可售库存", nickname = "queryStock", produces = MediaType.APPLICATION_JSON_VALUE)
    @ApiResponse(code = 200, message = "success", response = SkuStock.class)
    @RequestMapping(value = "/queryReplenishStock", method = RequestMethod.POST)
    public Response<List<SkuStock>> queryReplenishStock(@ApiParam(name = "skuInfoList", value = "sku集合") @RequestBody List<ReplenishSkuInfo> skuInfoList) {
        if (CollectionUtils.isEmpty(skuInfoList)) {
            return ResponseMsg.PARAM_ERROR.buildMsg();
        }
        try {
            log.warn(CoreKibanaLog.getJobLog(KibanaLogConstants.QUERY, "queryReplenishStock", "批量查询加盟门店渠道可售库存", skuInfoList));
            List<SkuStock> skuStockList = shopReplenishService.querySkuStockList(skuInfoList);
            return ResponseMsg.SUCCESS.buildMsg(skuStockList);
        } catch (RomeException e) {
            log.error(e.getMessage(), e);
            return Response.builderFail(e.getCode(), e.getMessage());
        }catch (Exception e) {
            log.error(e.getMessage(), e);
            return ResponseMsg.EXCEPTION.buildMsg();
        }
    }

    @ApiOperation(value = "批量取消申请", nickname = "cancleReplenish", produces = MediaType.APPLICATION_JSON_VALUE)
    @ApiResponse(code = 200, message = "success")
    @RequestMapping(value = "/cancleBatch", method = RequestMethod.POST)
    public Response cancleReplenishBatch(@RequestBody List<CancelRecordDTO> list){
        log.warn(CoreKibanaLog.getJobLog(LOGTYPE, "cancleReplenish", "批量取消申请: " + list, list));
        List<BatchResultDTO> resultList =  new ArrayList<>();
        try {
            if(CollectionUtils.isNotEmpty(list)){
                for (CancelRecordDTO cancelRecordDTO : list) {
                    BatchResultDTO dto = new BatchResultDTO();
                    dto.setRecordCode(cancelRecordDTO.getRecordCode());
                    dto.setStatus(false);
                    String message = "";
                    boolean isSucc = false;
                    try {
                        Integer isForceCancle = 0;
                        if(cancelRecordDTO.getIsForceCancel()){
                            isForceCancle = 1;
                        }
                        shopReplenishService.cancleReplenish(cancelRecordDTO.getRecordCode(), isForceCancle);
                        dto.setStatus(true);
                        isSucc = true;
                        message = "200";
                    }catch (Exception e){
                        log.error(e.getMessage(), e);
                        message=e.getMessage();
                    }finally {
                        dto.setMessage(message);
                        resultList.add(dto);
                        sapInterfaceLogRepository.saveCallBackInterFaceLog(1, dto.getRecordCode(), "cancleReplenish",
                                JSON.toJSONString(cancelRecordDTO), message, isSucc);
                    }

                }
            }
            return ResponseMsg.SUCCESS.buildMsg(resultList);
        } catch (RomeException e) {
            log.error(e.getMessage(), e);
            return Response.builderFail(e.getCode(),e.getMessage());
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return Response.builderFail(ResCode.STOCK_ERROR_1003_DESC,e.getMessage());
        }
    }




    @ApiOperation(value = "批量删除订单(订单回滚用)", nickname = "batchDeleteOrder", produces = MediaType.APPLICATION_JSON_VALUE)
    @ApiResponse(code = 200, message = "success")
    @RequestMapping(value = "/batchDeleteOrder", method = RequestMethod.POST)
    public Response batchDeleteOrder(@RequestBody List<String> list){
        List<BatchResultDTO> resultList =  new ArrayList<>();
        try {
            if(CollectionUtils.isNotEmpty(list)){
                for (String recordCode : list) {
                    BatchResultDTO dto = new BatchResultDTO();
                    dto.setRecordCode(recordCode);
                    dto.setStatus(false);
                    String message = "";
                    boolean isSucc = false;
                    try {
                        shopReplenishService.batchDeleteOrder(recordCode);
                        dto.setStatus(true);
                        isSucc = true;
                        message = "200";
                    }catch (Exception e){
                        log.error(e.getMessage(), e);
                        message=e.getMessage();
                    }finally {
                        dto.setMessage(message);
                        resultList.add(dto);
                        sapInterfaceLogRepository.saveCallBackInterFaceLog(1, dto.getRecordCode(), "batchDeleteOrder",
                                recordCode, message, isSucc);
                    }

                }
            }
            return ResponseMsg.SUCCESS.buildMsg(resultList);
        } catch (RomeException e) {
            log.error(e.getMessage(), e);
            return Response.builderFail(e.getCode(),e.getMessage());
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return Response.builderFail(ResCode.STOCK_ERROR_1003_DESC,e.getMessage());
        }
    }





    @ApiOperation(value = "批量查询待处理的门店出入库单推送cmp", nickname = "ShopAllocationRecord", produces = MediaType.APPLICATION_JSON_VALUE)
    @ApiResponse(code = 200, message = "success", response = Response.class)
    @RequestMapping(value = "/getWaitPushRecordList", method = RequestMethod.POST)
    public Response<List<WarehouseRecordPageDTO>> getWaitPushRecordList(@RequestParam("page") int page, @RequestParam("maxResult") int maxResult) {
        try {
            List<WarehouseRecordPageDTO> pageList = shopReplenishService.getWaitPushRecordList(page, maxResult);
            return Response.builderSuccess(pageList);
        } catch (RomeException e) {
            log.error(e.getMessage(), e);
            return Response.builderFail(e.getCode(),e.getMessage());
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return Response.builderFail(ResCode.STOCK_ERROR_1003_DESC,ResCode.STOCK_ERROR_1003_DESC);
        }
    }

    @ApiOperation(value = "批量查询待处理的门店出入库单推送cmp7", nickname = "getWaitPushCmp7RecordList", produces = MediaType.APPLICATION_JSON_VALUE)
    @ApiResponse(code = 200, message = "success", response = Response.class)
    @RequestMapping(value = "/getWaitPushCmp7RecordList", method = RequestMethod.POST)
    public Response<List<WarehouseRecordPageDTO>> getWaitPushCmp7RecordList(@RequestParam("page") int page, @RequestParam("maxResult") int maxResult) {
        try {
            List<WarehouseRecordPageDTO> pageList = shopReplenishService.getWaitPushCmp7RecordList(page, maxResult);
            return Response.builderSuccess(pageList);
        } catch (RomeException e) {
            log.error(e.getMessage(), e);
            return Response.builderFail(e.getCode(),e.getMessage());
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return Response.builderFail(ResCode.STOCK_ERROR_1003_DESC,ResCode.STOCK_ERROR_1003_DESC);
        }
    }


    @ApiOperation(value = "批量查询补货结果待推送cmp单据列表", nickname = "queryWaitCmpResultList", produces = MediaType.APPLICATION_JSON_VALUE)
    @ApiResponse(code = 200, message = "success", response = Response.class)
    @RequestMapping(value = "/queryWaitCmpResultList", method = RequestMethod.POST)
    public Response<List<WarehouseRecordPageDTO>> queryWaitCmpResultList(@RequestParam("page") int page, @RequestParam("maxResult") int maxResult) {
        try {
            List<WarehouseRecordPageDTO> pageList = shopReplenishService.queryWaitCmpResultList(page, maxResult);
            return Response.builderSuccess(pageList);
        } catch (RomeException e) {
            log.error(e.getMessage(), e);
            return Response.builderFail(e.getCode(),e.getMessage());
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return Response.builderFail(ResCode.STOCK_ERROR_1003_DESC,ResCode.STOCK_ERROR_1003_DESC);
        }
    }

    @ApiOperation(value = "批量处理直营门店交货单结果推送cmp5", nickname = "pushShopReplenishToCmp", produces = MediaType.APPLICATION_JSON_VALUE)
    @ApiResponse(code = 200, message = "success", response = Response.class)
    @RequestMapping(value = "/pushShopReplenishResultCmp5", method = RequestMethod.POST)
    public void pushShopReplenishResultCmp5(@RequestBody WarehouseRecordPageDTO dto){
        String message ="";
        boolean isSucc = false;
        try {
            Response response = shopReplenishService.pushShopReplenishResultCmp5(dto);
            message =JSON.toJSONString(response.getData());;
            isSucc = true;
        } catch (RomeException e) {
            log.error(e.getMessage(), e);
            message = e.getMessage();
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            message = e.getMessage();
        }finally {
            sapInterfaceLogRepository.saveCallBackInterFaceLog(1, dto.getRecordCode()
                    , "pushShopReplenishResultCmp5", JSON.toJSONString(dto), message, isSucc);
        }
    }


    @ApiOperation(value = "批量处理加盟门店交货单结果推送cmp6", nickname = "pushShopReplenishToCmp", produces = MediaType.APPLICATION_JSON_VALUE)
    @ApiResponse(code = 200, message = "success", response = Response.class)
    @RequestMapping(value = "/pushShopReplenishResultCmp6", method = RequestMethod.POST)
    public void pushShopReplenishResultCmp6(@RequestBody WarehouseRecordPageDTO dto){
        String message ="";
        boolean isSucc = false;
        try {
            Response response = shopReplenishService.pushShopReplenishResultCmp6(dto);
            message =JSON.toJSONString(response.getData());
            isSucc = true;
        } catch (RomeException e) {
            log.error(e.getMessage(), e);
            message = e.getMessage();
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            message = e.getMessage();
        }finally {
            sapInterfaceLogRepository.saveCallBackInterFaceLog(1, dto.getRecordCode()
                    , "pushShopReplenishResultCmp6", JSON.toJSONString(dto), message, isSucc);
        }
    }


    @ApiOperation(value = "批量处理直营门店交货单推送cmp", nickname = "ShopAllocationRecord", produces = MediaType.APPLICATION_JSON_VALUE)
    @ApiResponse(code = 200, message = "success", response = Response.class)
    @RequestMapping(value = "/handleDirectReplenishPushCMP", method = RequestMethod.POST)
    public Response<String> handleDirectReplenishPushCMP(@RequestBody WarehouseRecordPageDTO dto) {
        String message ="";
        boolean isSucc = false;
        try {
            shopReplenishService.handleDirectReplenishPushCMP(dto);
            message = "推送成功";
            isSucc = true;
            return Response.builderSuccess("");
        } catch (RomeException e) {
            log.error(e.getMessage(), e);
            message = e.getMessage();
            return Response.builderFail(e.getCode(),e.getMessage());
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            message = e.getMessage();
            return Response.builderFail(ResCode.STOCK_ERROR_1003_DESC,ResCode.STOCK_ERROR_1003_DESC);
        }finally {
            sapInterfaceLogRepository.saveCallBackInterFaceLog(1, dto.getRecordCode()
                    , "handleDirectReplenishPushCMP-LIST", JSON.toJSONString(dto), message, isSucc);
        }
    }

    @ApiOperation(value = "批量处理直营门店交货单推送cmp7", nickname = "ShopAllocationRecord", produces = MediaType.APPLICATION_JSON_VALUE)
    @ApiResponse(code = 200, message = "success", response = Response.class)
    @RequestMapping(value = "/handleDirectReplenishPushCMP7", method = RequestMethod.POST)
    public Response<String> handleDirectReplenishPushCMP7(@RequestBody WarehouseRecordPageDTO dto) {
        String message ="";
        boolean isSucc = false;
        try {
            shopReplenishService.handleDirectReplenishPushCMP7(dto);
            message = "推送成功";
            isSucc = true;
            return Response.builderSuccess("");
        } catch (RomeException e) {
            log.error(e.getMessage(), e);
            message = e.getMessage();
            return Response.builderFail(e.getCode(),e.getMessage());
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            message = e.getMessage();
            return Response.builderFail(ResCode.STOCK_ERROR_1003_DESC,ResCode.STOCK_ERROR_1003_DESC);
        }finally {
            sapInterfaceLogRepository.saveCallBackInterFaceLog(1, dto.getRecordCode()
                    , "handleDirectReplenishPushCMP7-LIST", JSON.toJSONString(dto), message, isSucc);
        }
    }


    @ApiOperation(value = "批量处理加盟门店交货单推送cmp", nickname = "ShopAllocationRecord", produces = MediaType.APPLICATION_JSON_VALUE)
    @ApiResponse(code = 200, message = "success", response = Response.class)
    @RequestMapping(value = "/handleJoinReplenishPushCMP", method = RequestMethod.POST)
    public Response<String> handleJoinReplenishPushCMP(@RequestBody WarehouseRecordPageDTO dto) {
        String message ="";
        boolean isSucc = false;
        try {
            shopReplenishService.handleJoinReplenishPushCMP(dto);
            message = "推送成功";
            isSucc = true;
            return Response.builderSuccess("");
        } catch (RomeException e) {
            log.error(e.getMessage(), e);
            message = e.getMessage();
            return Response.builderFail(e.getCode(),e.getMessage());
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            message = e.getMessage();
            return Response.builderFail(ResCode.STOCK_ERROR_1003_DESC,ResCode.STOCK_ERROR_1003_DESC);
        } finally {
            sapInterfaceLogRepository.saveCallBackInterFaceLog(1, dto.getRecordCode()
                    , "handleJoinReplenishPushCMP-LIST", JSON.toJSONString(dto), message, isSucc);
        }
    }

    @ApiOperation(value = "批量处理加盟门店交货单推送cmp7", nickname = "ShopAllocationRecord", produces = MediaType.APPLICATION_JSON_VALUE)
    @ApiResponse(code = 200, message = "success", response = Response.class)
    @RequestMapping(value = "/handleJoinReplenishPushCMP7", method = RequestMethod.POST)
    public Response<String> handleJoinReplenishPushCMP7(@RequestBody WarehouseRecordPageDTO dto) {
        String message ="";
        boolean isSucc = false;
        try {
            shopReplenishService.handleJoinReplenishPushCMP7(dto);
            message = "推送成功";
            isSucc = true;
            return Response.builderSuccess("");
        } catch (RomeException e) {
            log.error(e.getMessage(), e);
            message = e.getMessage();
            return Response.builderFail(e.getCode(),e.getMessage());
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            message = e.getMessage();
            return Response.builderFail(ResCode.STOCK_ERROR_1003_DESC,ResCode.STOCK_ERROR_1003_DESC);
        } finally {
            sapInterfaceLogRepository.saveCallBackInterFaceLog(1, dto.getRecordCode()
                    , "handleJoinReplenishPushCMP7-LIST", JSON.toJSONString(dto), message, isSucc);
        }
    }


    @ApiOperation(value = "批量整单拒收", nickname = "cancleReplenish", produces = MediaType.APPLICATION_JSON_VALUE)
    @ApiResponse(code = 200, message = "success")
    @RequestMapping(value = "/rejectShopReceipt", method = RequestMethod.POST)
    public Response rejectShopReceipt(@RequestBody List<String> recordCodeList){
        log.warn(CoreKibanaLog.getJobLog(LOGTYPE, "cancleReplenish", "批量整单拒收: " + recordCodeList, recordCodeList));
        List<BatchResultDTO> resultList =  new ArrayList<>();
        try {
            if(CollectionUtils.isNotEmpty(recordCodeList)){
                for (String recordCode : recordCodeList) {
                    BatchResultDTO dto = new BatchResultDTO();
                    dto.setRecordCode(recordCode);
                    dto.setStatus(false);
                    String message = "";
                    boolean isSucc = false;
                    try {
                        shopReplenishService.rejectShopReceipt(recordCode);
                        dto.setStatus(true);
                        isSucc = true;
                        message = "200";
                    }catch (Exception e){
                        log.error(e.getMessage(), e);
                    }finally {
                        resultList.add(dto);
                        sapInterfaceLogRepository.saveCallBackInterFaceLog(1, dto.getRecordCode(), "rejectShopReceipt",
                                dto.toString(), message, isSucc);
                    }

                }
            }
            return ResponseMsg.SUCCESS.buildMsg(resultList);
        } catch (RomeException e) {
            log.error(e.getMessage(), e);
            return Response.builderFail(e.getCode(),e.getMessage());
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return ResponseMsg.EXCEPTION.buildMsg();
        }
    }


    @ApiOperation(value = "批量取消tob单据", nickname = "tobBatchCancel", produces = MediaType.APPLICATION_JSON_VALUE)
    @ApiResponse(code = 200, message = "success")
    @RequestMapping(value = "/tobBatchCancel", method = RequestMethod.POST)
    public Response tobBatchCancel(@RequestBody List<CancelRecordDTO> list){
        log.warn(CoreKibanaLog.getJobLog(LOGTYPE, "tobBatchCancel", "批量取消tob单据: " , JSON.toJSONString(list)));
        List<BatchResultDTO> resultList =  new ArrayList<>();
        try {
            if(CollectionUtils.isNotEmpty(list)){
                for (CancelRecordDTO cancelRecordDTO : list) {
                    BatchResultDTO dto = new BatchResultDTO();
                    dto.setRecordCode(cancelRecordDTO.getRecordCode());
                    dto.setStatus(false);
                    String message = "";
                    boolean isSucc = false;
                    String uniqueKey = "joinSplitLockLeftStock" + "_" + cancelRecordDTO.getRecordCode();
                    boolean isLock = false;
                    try {
                        Integer isForceCancle = 0;
                        if(cancelRecordDTO.getIsForceCancel()){
                            isForceCancle = 1;
                        }
                        isLock = redisUtil.lock(uniqueKey, "1", 120);
                        if (isLock) {
                        shopReplenishService.cancleReplenish(cancelRecordDTO.getRecordCode(), isForceCancle);
                        dto.setStatus(true);
                        isSucc = true;
                        message = "200";
                        }else{
                            message="加盟拆单(扣减大单剩余库存)正在初始处理中，请稍后再试。。。";
                        }
                    }catch (Exception e){
                        log.error(e.getMessage(), e);
                        message=e.getMessage();
                    }finally {
                        if (isLock) {
                            redisUtil.unLock(uniqueKey, "1");
                        }
                        dto.setMessage(message);
                        resultList.add(dto);
                        sapInterfaceLogRepository.saveCallBackInterFaceLog(1, dto.getRecordCode(), "tobBatchCancel",
                                JSON.toJSONString(cancelRecordDTO), message, isSucc);
                    }
                }
            }
            return ResponseMsg.SUCCESS.buildMsg(resultList);
        } catch (RomeException e) {
            log.error(e.getMessage(), e);
            return Response.builderFail(e.getCode(),e.getMessage());
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return ResponseMsg.EXCEPTION.buildMsg();
        }
    }



    /**
     * 批量接收派车回调
     *
     * @return
     */
    @ApiOperation(value = "tob批量接收派车回调", nickname = "tobBatchDispatchingNotify", produces = MediaType.APPLICATION_JSON_VALUE)
    @ApiResponse(code = 200, message = "success", response = WarehouseToBRecord.class)
    @RequestMapping(value = "/tobBatchDispatchingNotify", method = RequestMethod.POST)
    public Response<List<BatchResultDTO>> tobBatchDispatchingNotify(@RequestBody List<DispatchNoticeDTO> dispatchNoticeList) {
        List<BatchResultDTO> resultList =  new ArrayList<>();
        try {
            if(CollectionUtils.isNotEmpty(dispatchNoticeList)){
                for (DispatchNoticeDTO dispatchNoticeDTO : dispatchNoticeList) {
                    BatchResultDTO dto = new BatchResultDTO();
                    dto.setRecordCode(dispatchNoticeDTO.getRecordCode());
                    dto.setStatus(false);
                    String message = "";
                    boolean isSucc = false;
                    String json = JSON.toJSONString(dispatchNoticeDTO);
                    try {
                        dispatchNoticeService.handleDispatchNotice(dispatchNoticeDTO);
                        dto.setStatus(true);
                        isSucc = true;
                        message = "200";
                    }catch (Exception e){
                        message = e.getMessage();
                        log.error(e.getMessage(), e);
                    }finally {
                        dto.setMessage(message);
                        resultList.add(dto);
                        sapInterfaceLogRepository.saveCallBackInterFaceLog(2, dispatchNoticeDTO.getRecordCode(), "tobBatchDispatchingNotify",
                                json, message, isSucc);
                    }
                }
            }
            return ResponseMsg.SUCCESS.buildMsg(resultList);
        } catch (RomeException e) {
            log.error(e.getMessage(), e);
            return Response.builderFail(e.getCode(),e.getMessage());
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return ResponseMsg.EXCEPTION.buildMsg();
        }
    }




    @ApiOperation(value = "不锁定创建出库单", nickname = "unLockAllReplenishRecord", produces = MediaType.APPLICATION_JSON_VALUE)
    @ApiResponse(code = 200, message = "success", response = Response.class)
    @RequestMapping(value = "/unLockAllReplenishRecord", method = RequestMethod.POST)
    public Response unLockAllReplenishRecord(@RequestBody OutWarehouseRecordDTO dto) {
        try {
            shopReplenishService.unLockAllReplenishRecord(dto);
            return Response.builderSuccess("");
        } catch (RomeException e) {
            log.error(e.getMessage(), e);
            return Response.builderFail(e.getCode(),e.getMessage());
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return Response.builderFail(ResCode.STOCK_ERROR_1003,ResCode.STOCK_ERROR_1003_DESC+e.getMessage()==null?e.toString():e.getMessage());
        }
    }


    @ApiOperation(value = "全部锁定库存", nickname = "allLockStockAndReturn", produces = MediaType.APPLICATION_JSON_VALUE)
    @ApiResponse(code = 200, message = "success")
    @RequestMapping(value = "/allLockStockAndReturn", method = RequestMethod.POST)
    public Response allLockStockAndReturn(@RequestBody OutWarehouseRecordDTO warehouseRecord) {
        try {
            warehouseRecord.setAppIdFlag(true);
            shopReplenishService.allLockStockAndReturn(warehouseRecord);
            return ResponseMsg.SUCCESS.buildMsg("");
        } catch (RomeException e) {
            return Response.builderFail(e.getCode(), e.getMessage());
        } catch (Exception e) {
            return Response.builderFail(ResCode.STOCK_ERROR_1003,ResCode.STOCK_ERROR_1003_DESC+e.getMessage()==null?e.toString():e.getMessage());
        }
    }

    @ApiOperation(value = "部分锁定库存并返回Sku锁定数量", nickname = "partLockStockAndReturn", produces = MediaType.APPLICATION_JSON_VALUE)
    @ApiResponse(code = 200, message = "success", response = Response.class)
    @RequestMapping(value = "/partLockStockAndReturn", method = RequestMethod.POST)
    public Response<LockStockResp> partLockStockAndReturn(@RequestBody OutWarehouseRecordDTO dto) {
        String uniqueKey ="partLockStockAndReturn" + "_" + dto.getRecordCode() ;
        boolean isLock = false;
        try {
            isLock = redisUtil.lock(uniqueKey, "1", 120);
            if (isLock) {
                dto.setAppIdFlag(true);
                LockStockResp res = shopReplenishService.partLockStockAndReturn(dto);
                return Response.builderSuccess(res);
            } else {
                WarehouseRecordE recordE = warehouseRecordRepository.queryWarehouseRecordByRecordCode(dto.getRecordCode());
                if(null ==recordE){
                    throw new RomeException(ResCode.STOCK_ERROR_1017,"部分锁定库存并返回Sku锁定数量正在初始处理中，请稍后再试。。。"+ dto.getRecordCode());
                }
                log.info("部分锁定库存并返回Sku锁定数量【{}】正在处理中，请稍后再试。。。", dto.getRecordCode());
                LockStockResp res = shopReplenishService.buildResDTO(dto);
                return Response.builderSuccess(res);
            }
        } catch (RomeException e) {
            log.error(e.getMessage(), e);
            return Response.builderFail(e.getCode(),e.getMessage());
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return Response.builderFail(ResCode.STOCK_ERROR_1003,ResCode.STOCK_ERROR_1003_DESC+e.getMessage()==null?e.toString():e.getMessage());
        }finally {
            if(isLock) {
                redisUtil.unLock(uniqueKey, "1");
            }
        }
    }



    @ApiOperation(value = "创建tob入库单", nickname = "tobCreateInRecord", produces = MediaType.APPLICATION_JSON_VALUE)
    @ApiResponse(code = 200, message = "success")
    @RequestMapping(value = "/tobCreateInRecord", method = RequestMethod.POST)
    public Response tobCreateInRecord(@RequestBody InWarehouseRecordDTO warehouseRecord) {
        try {
            shopReplenishService.tobCreateInRecord(warehouseRecord);
            return ResponseMsg.SUCCESS.buildMsg();
        } catch (RomeException e) {
            log.error("scOrder调用 : " + e.getMessage(), e);
            return Response.builderFail(e.getCode(),e.getMessage());
        } catch (Exception e) {
            log.error("scOrder调用 : " + e.getMessage(), e);
            return Response.builderFail(ResCode.STOCK_ERROR_1003_DESC,e.getMessage());
        }
    }


    @ApiOperation(value = "大单部分锁定库存并返回Sku锁定数量--寻源", nickname = "partLockStockAndReturn", produces = MediaType.APPLICATION_JSON_VALUE)
    @ApiResponse(code = 200, message = "success", response = Response.class)
    @RequestMapping(value = "/partLockStockAndReturnForSourcing", method = RequestMethod.POST)
    public Response<LockStockResp> partLockStockAndReturnForSourcing(@RequestBody OutWarehouseRecordDTO dto) {
        String uniqueKey ="partLockStockAndReturnForSourcing" + "_" + dto.getRecordCode() ;
        boolean isLock = false;
        try {
            isLock = redisUtil.lock(uniqueKey, "1", 120);
            if (isLock) {
                LockStockResp res=shopReplenishService.partLockStockAndReturnForSourcing(dto);
                return Response.builderSuccess(res);
            } else {
                WarehouseRecordE recordE = warehouseRecordRepository.queryWarehouseRecordByRecordCode(dto.getRecordCode());
                if(null ==recordE){
                    throw new RomeException(ResCode.STOCK_ERROR_1017,"大单部分锁定库存并返回Sku锁定数量--寻源正在初始处理中，请稍后再试。。。"+ dto.getRecordCode());
                }
                log.info("大单部分锁定库存并返回Sku锁定数量--寻源【{}】正在处理中，请稍后再试。。。", dto.getRecordCode());
                LockStockResp res = shopReplenishService.buildResDTO(dto);
                return Response.builderSuccess(res);
            }
        } catch (RomeException e) {
            log.error(e.getMessage(), e);
            return Response.builderFail(e.getCode(),e.getMessage());
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return Response.builderFail(ResCode.STOCK_ERROR_1003,ResCode.STOCK_ERROR_1003_DESC+e.getMessage()==null?e.toString():e.getMessage());
        }finally {
            if(isLock) {
                redisUtil.unLock(uniqueKey, "1");
            }
        }
    }

    @ApiOperation(value = "释放大单部分库存并锁定小单库存", nickname = "allLockStockAndReturn", produces = MediaType.APPLICATION_JSON_VALUE)
    @ApiResponse(code = 200, message = "success")
    @RequestMapping(value = "/unLockBigOrderAndLockLittleOrder", method = RequestMethod.POST)
    public Response unLockBigOrderAndLockLittleOrder(@RequestBody OutWarehouseRecordDTO warehouseRecord) {
        try {
            shopReplenishService.unLockBigOrderAndLockLittleOrder(warehouseRecord,false);
            return ResponseMsg.SUCCESS.buildMsg("");
        } catch (RomeException e) {
            log.error(e.getMessage(), e);
            return Response.builderFail(e.getCode(), e.getMessage());
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return Response.builderFail(ResCode.STOCK_ERROR_1003,ResCode.STOCK_ERROR_1003_DESC+e.getMessage()==null?e.toString():e.getMessage());
        }
    }

    @ApiOperation(value = "释放大单部分库存并直接出库小单", nickname = "unLockBigOrderAndDecreaseLittleOrder", produces = MediaType.APPLICATION_JSON_VALUE)
    @ApiResponse(code = 200, message = "success")
    @RequestMapping(value = "/unLockBigOrderAndDecreaseLittleOrder", method = RequestMethod.POST)
    public Response unLockBigOrderAndDecreaseLittleOrder(@RequestBody OutWarehouseRecordDTO warehouseRecord) {
        try {
            shopReplenishService.unLockBigOrderAndLockLittleOrder(warehouseRecord,true);
            return ResponseMsg.SUCCESS.buildMsg("");
        } catch (RomeException e) {
            log.error(e.getMessage(), e);
            return Response.builderFail(e.getCode(), e.getMessage());
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return Response.builderFail(ResCode.STOCK_ERROR_1003,ResCode.STOCK_ERROR_1003_DESC+e.getMessage()==null?e.toString():e.getMessage());
        }
    }

    @ApiOperation(value = "取消大单", nickname = "cancelBigOrder", produces = MediaType.APPLICATION_JSON_VALUE)
    @ApiResponse(code = 200, message = "success")
    @RequestMapping(value = "/cancelBigOrder", method = RequestMethod.POST)
    public Response cancelBigOrder(@RequestParam("recordCode") String recordCode) {
        String message = "";
        boolean isSucc = false;
        try {
            shopReplenishService.cancelBigOrder(recordCode);
            isSucc = true;
            message="200";
            return ResponseMsg.SUCCESS.buildMsg("");
        } catch (RomeException e) {
            message=e.getMessage();
            log.error(e.getMessage(), e);
            return Response.builderFail(e.getCode(), e.getMessage());
        } catch (Exception e) {
            message=e.getMessage();
            log.error(e.getMessage(), e);
            return Response.builderFail(ResCode.STOCK_ERROR_1003,ResCode.STOCK_ERROR_1003_DESC+e.getMessage()==null?e.toString():e.getMessage());
        }finally {
            sapInterfaceLogRepository.saveCallBackInterFaceLog(2, recordCode, "cancelBigOrder",recordCode, message, isSucc);
        }
    }


    @ApiOperation(value = "释放部分库存并修改计划数量", nickname = "unLockPartAndUpdate", produces = MediaType.APPLICATION_JSON_VALUE)
    @ApiResponse(code = 200, message = "success")
    @RequestMapping(value = "/unLockPartAndUpdate", method = RequestMethod.POST)
    public Response<LockStockResp> unLockPartAndUpdate(@RequestBody OutWarehouseRecordDTO dto) {
        String uniqueKey = "partLockStockAndReturn" + "_" + dto.getRecordCode();
        boolean isLock = false;
        try {
            isLock = redisUtil.lock(uniqueKey, "1", 120);
            if (isLock) {
                LockStockResp res = shopReplenishService.unLockPartAndUpdate(dto);
                return Response.builderSuccess(res);
            } else {
                throw new RomeException(ResCode.STOCK_ERROR_1017, "释放部分库存并修改计划数量时，正在部分锁定库存并返回Sku锁定数量正在初始处理中，请稍后再试。。。" + dto.getRecordCode());
            }
        } catch (RomeException e) {
            log.error(e.getMessage(), e);
            return Response.builderFail(e.getCode(), e.getMessage());
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return Response.builderFail(ResCode.STOCK_ERROR_1003, ResCode.STOCK_ERROR_1003_DESC + e.getMessage() == null ? e.toString() : e.getMessage());
        } finally {
            if (isLock) {
                redisUtil.unLock(uniqueKey, "1");
            }
        }
    }


    @ApiOperation(value = "加盟拆单(扣减大单剩余库存)", nickname = "joinSplitLockLeftStock", produces = MediaType.APPLICATION_JSON_VALUE)
    @ApiResponse(code = 200, message = "success")
    @RequestMapping(value = "/joinSplitLockLeftStock", method = RequestMethod.POST)
    public Response<LockStockResp> joinSplitLockLeftStock(@RequestBody OutWarehouseRecordDTO warehouseRecord) {
        String uniqueKey = "joinSplitLockLeftStock" + "_" + warehouseRecord.getRecordCode();
        boolean isLock = false;
        try {
            isLock = redisUtil.lock(uniqueKey, "1", 120);
            if (isLock) {
                LockStockResp res = shopReplenishService.joinSplitLockLeftStock(warehouseRecord);
                return Response.builderSuccess(res);
            } else {
                throw new RomeException(ResCode.STOCK_ERROR_1017, "加盟拆单(扣减大单剩余库存)正在初始处理中，请稍后再试。。。" + warehouseRecord.getRecordCode());
            }
        } catch (RomeException e) {
            log.error(e.getMessage(), e);
            return Response.builderFail(e.getCode(), e.getMessage());
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return Response.builderFail(ResCode.STOCK_ERROR_1003,ResCode.STOCK_ERROR_1003_DESC+e.getMessage()==null?e.toString():e.getMessage());
        } finally {
            if (isLock) {
                redisUtil.unLock(uniqueKey, "1");
            }
        }
    }


    @ApiOperation(value = "删除单据明细并释放库存", nickname = "deleteDetailAndUnlockStock", produces = MediaType.APPLICATION_JSON_VALUE)
    @ApiResponse(code = 200, message = "success")
    @RequestMapping(value = "/deleteDetailAndUnlockStock", method = RequestMethod.POST)
    public Response deleteDetailAndUnlockStock(@RequestBody @Valid DeleteDetailDTO dto) {
        try {
            shopReplenishService.deleteDetailAndUnlockStock(dto);
            return Response.builderSuccess("");
        } catch (RomeException e) {
            log.error(e.getMessage(), e);
            return Response.builderFail(e.getCode(), e.getMessage());
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return Response.builderFail(ResCode.STOCK_ERROR_1003, ResCode.STOCK_ERROR_1003_DESC + e.getMessage() == null ? e.toString() : e.getMessage());
        }
    }


    @ApiOperation(value = "释放部分锁定库存并重新全部锁定", nickname = "unLockPartAndAllLock", produces = MediaType.APPLICATION_JSON_VALUE)
    @ApiResponse(code = 200, message = "success")
    @RequestMapping(value = "/unLockPartAndAllLock", method = RequestMethod.POST)
    public Response<LockStockResp> unLockPartAndAllLock(@RequestBody OutWarehouseRecordDTO dto) {
        try {
            shopReplenishService.unLockPartAndAllLock(dto);
            return Response.builderSuccess("");
        } catch (RomeException e) {
            log.error(e.getMessage(), e);
            return Response.builderFail(e.getCode(), e.getMessage());
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return Response.builderFail(ResCode.STOCK_ERROR_1003, ResCode.STOCK_ERROR_1003_DESC + e.getMessage() == null ? e.toString() : e.getMessage());
        }
    }


    @ApiOperation(value = "释放部分锁定库存", nickname = "unLockPartStock", produces = MediaType.APPLICATION_JSON_VALUE)
    @ApiResponse(code = 200, message = "success")
    @RequestMapping(value = "/unLockPartStock", method = RequestMethod.POST)
    public Response unLockPartStock(@RequestBody OutWarehouseRecordDTO dto) {
        String uniqueKey = "unLockPartStock" + "_" + dto.getRecordCode();
        boolean isLock = false;
        try {
            isLock = redisUtil.lock(uniqueKey, "1", 120);
            if (isLock) {
                shopReplenishService.unLockPartStock(dto);
                return Response.builderSuccess("");
            } else {
                throw new RomeException(ResCode.STOCK_ERROR_1017, "释放部分锁定库存正在初始处理中，请稍后再试。。。" + dto.getRecordCode());
            }
        } catch (RomeException e) {
            log.error(e.getMessage(), e);
            return Response.builderFail(e.getCode(), e.getMessage());
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return Response.builderFail(ResCode.STOCK_ERROR_1003, ResCode.STOCK_ERROR_1003_DESC + e.getMessage() == null ? e.toString() : e.getMessage());
        } finally {
            if (isLock) {
                redisUtil.unLock(uniqueKey, "1");
            }
        }
    }
}
