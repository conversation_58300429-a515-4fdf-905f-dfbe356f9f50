/**
 * Filename ChannelSalesRouteDTO.java
 * Company 上海来伊份科技有限公司。
 * <AUTHOR>
 * @version 
 */
package com.rome.stock.innerservice.api.dto;

import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.util.List;

/**
 * 渠道路由信息
 * <AUTHOR>
 * @since 2019年5月24日 上午10:54:27
 */
@Data
@EqualsAndHashCode
public class RouteChannelSalesDTO implements Serializable {

	/**
	 * 
	 */
	private static final long serialVersionUID = -6537768824515955400L;

	/**
	 * 渠道编码
	 */
	private String channelCode;
	
	/**
	 * 路由模板
	 */
	private String template;
	
	/**
	 * 路由模板,参数
	 */
	private String templateParam;
	
	/**
	 * 组id
	 */
	private Long groupId;
	
	/**
	 * 仓库路由信息
	 */
	private List<RouteWarehouseDTO> routeWarehouses;
	
}
