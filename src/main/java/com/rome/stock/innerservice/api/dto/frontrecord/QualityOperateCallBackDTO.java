package com.rome.stock.innerservice.api.dto.frontrecord;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import java.util.List;

/**
 * <AUTHOR>
 * @description 质检操作库存对象
 * @date 2020/9/27 13:53
 * @throw
 */
@Data
public class QualityOperateCallBackDTO {

    @ApiModelProperty(value = "入库凭证")
    @NotBlank(message="入库凭证不能为空")
    private String recordCode;

    @ApiModelProperty(value = "采购单号")
    @NotBlank(message="采购单号不能为空")
    private String purchaseRecordCode;

    @NotBlank(message="记账凭证不能为空")
    @ApiModelProperty(value = "记账凭证")
    private String wmsRecordCode;

    @ApiModelProperty(value = "批次信息")
    @NotEmpty(message = "明细信息不能为空")
    private List<QualityOperateCallBackDetailDTO> detailDTOList;
}    
   