package com.rome.stock.innerservice.api.dto.frontrecord;

import lombok.Data;

import javax.validation.constraints.Size;
import java.util.List;

/**
 * 仓库调整
 */
@Data
public class AdjustConsumeDTO {

    /**
     * 唯一主键
     */
    private Long id;

    /**
     * 调整单单号
     */
    private String recordCode;

    /**
     * 调整单状态 0-新建,1-确认,2-取消,14-出库
     */
    private Integer recordStatus;

    /**
     * 单据类型
     */
    private Integer recordType;

    /**
     * 实仓id
     */
    private Long realWarehouseId;

    /**
     * 外部仓库编码
     */
    private String realWareHouseOutCode;

    /**
     * 仓库调整明细
     */
    @Size(min = 1)
    private List<AdjustConsumeDetailDTO> frontRecordDetails ;
}
