package com.rome.stock.innerservice.api.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;

@Data
@EqualsAndHashCode
public class VirtualWarehouseSku  {
    /**
     * 单据子表id
     */
    @ApiModelProperty(value = "单据子表id")
    private Long id;
    /**
     * 商品编码
     */
    @ApiModelProperty(value = "商品编码")
    private Long skuId;
    /**
     * 商品名称
     */
    @ApiModelProperty(value = "商品名称")
    private String skuName;
    /**
     * 商品code
     */
    @ApiModelProperty(value = "商品code")
    private String skuCode;
    /**
     * 商品计量单位
     */
    @ApiModelProperty(value = "商品计量单位")
    private String skuUnit;
    /**
     * 商品计量单位
     */
    private String skuUnitCode;
    /**
     * 库存可用数量
     */
    @ApiModelProperty(value = "库存可用数量")
    private BigDecimal availableQty;
    /**
     * 商品转移数量
     */
    @ApiModelProperty(value = "商品转移数量")
    private BigDecimal skuQty;
    /**
     * 单据子表对应的单据id
     */
    @ApiModelProperty(value = "单据子表对应的单据id")
    private Long moveRecordId;
    /**
     * 所属单据编码
     */
    @ApiModelProperty(value = "所属单据编码")
    private String recordCode;
    
    /**
	 * 实际转移库存数量
	 */
    @ApiModelProperty(value = "实际转移库存数量")
	private BigDecimal actualQty;

    private Long outVirtualWarehouseId;

    private String outVirtualWarehouseCode;

    private String outVirtualWarehouseName;

    private String outVirtualWarehouseTypeName;
}
