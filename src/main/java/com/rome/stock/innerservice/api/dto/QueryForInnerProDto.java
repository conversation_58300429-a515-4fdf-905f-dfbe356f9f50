package com.rome.stock.innerservice.api.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2022年07月28日 11:10
 */
@Data
public class QueryForInnerProDto {

    @ApiModelProperty(value = "公司编码")
    private String companyCode;
    @ApiModelProperty(value = "仓类型")
    private Integer realWarehouseType;
    @ApiModelProperty(value = "实仓虚仓(0: 虚仓 1: 实仓)")
    private Integer solidEmptyWarehouseStatus;
}
