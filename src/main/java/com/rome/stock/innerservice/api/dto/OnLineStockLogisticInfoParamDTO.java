package com.rome.stock.innerservice.api.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import java.io.Serializable;

@Data
public class OnLineStockLogisticInfoParamDTO implements Serializable {

    @ApiModelProperty(value = "DO单号,拆单后的pool表的单号",required = true)
    @NotBlank
    private String doCode;


    private Integer hasRecommend;

    @ApiModelProperty(value = "物流公司编码" , required = true)
    @NotBlank(message="物流公司编码不能为空")
    private String logisticsCode;

}
