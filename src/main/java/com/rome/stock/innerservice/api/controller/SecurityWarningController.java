package com.rome.stock.innerservice.api.controller;

import com.github.pagehelper.PageInfo;
import com.rome.arch.core.clientobject.Response;
import com.rome.arch.core.exception.RomeException;
import com.rome.arch.util.controller.RomeController;
import com.rome.stock.innerservice.api.dto.QuerySecurityWarningDTO;
import com.rome.stock.innerservice.api.dto.SecurityWarningDTO;
import com.rome.stock.innerservice.common.ResCode;
import com.rome.stock.innerservice.constant.ResponseMsg;
import com.rome.stock.innerservice.domain.service.SecurityWarningService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiResponse;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@Slf4j
@RomeController
@RequestMapping(value = "/stock/v1/securityWarning")
@Api(tags={"库存安全预警"})
public class SecurityWarningController {

    @Autowired
    private SecurityWarningService securityWarningService;


    @ApiOperation(value = "查询库存告警信息列表", produces = MediaType.APPLICATION_JSON_VALUE)
    @ApiResponse(code = 200, message = "success",response = List.class)
    @PostMapping(value = "/querySecurityByCondition")
    public Response<PageInfo<SecurityWarningDTO>> querySecurityWarningByCondition(@RequestBody QuerySecurityWarningDTO querySecurityWarningDTO){
       try{
           PageInfo<SecurityWarningDTO> securityWarningDTOPageInfo = securityWarningService.querySecurityWarningByCondition(querySecurityWarningDTO);
           return ResponseMsg.SUCCESS.buildMsg(securityWarningDTOPageInfo);
       }catch (RomeException ex){
           log.error("查询库存告警信息列表失败，{}",ex);
           return Response.builderFail(ex.getCode(),ex.getMessage());
       }catch (Exception ex){
           log.error("查询库存告警信息列表失败，{}",ex);
           return Response.builderFail(ResCode.STOCK_ERROR_9039,ResCode.STOCK_ERROR_9039_DESC);
       }
    }

    @ApiOperation(value = "更新预警信息", produces = MediaType.APPLICATION_JSON_VALUE)
    @ApiResponse(code = 200, message = "success")
    @PostMapping(value = "/updateSecurityWarning")
    public Response updateSecurityWarning(@Validated @RequestBody SecurityWarningDTO securityWarningDTO){
        try{
            securityWarningService.updateSecurityWarning(securityWarningDTO);
        }catch (RomeException ex){
            log.error("更新库存告警信息失败，{}",ex);
            return Response.builderFail(ex.getCode(),ex.getMessage());
        }catch (Exception ex){
            log.error("更新库存告警信息失败，{}",ex);
            return  Response.builderFail(ResCode.STOCK_ERROR_9040,ResCode.STOCK_ERROR_9040_DESC);
        }
        return Response.builderSuccess(null);
    }

    @ApiOperation(value = "查询预警详情")
    @ApiResponse(code = 200, message = "success")
    @GetMapping(value = "/querySecurityWarningById/{id}")
    public  Response<SecurityWarningDTO> querySecurityWarningById(@PathVariable Long id){
        try{
             SecurityWarningDTO securityWarningDTO = securityWarningService.querySecurityWarningById(id);
             return Response.builderSuccess(securityWarningDTO);
        }catch (RomeException ex){
             log.error("查询库存告警详情失败，{}",ex);
             return Response.builderFail(ex.getCode(),ex.getMessage());
        }catch (Exception ex){
            log.error("查询库存告警详情失败，{}",ex);
            return  Response.builderFail(ResCode.STOCK_ERROR_9044,ResCode.STOCK_ERROR_9044_DESC);
        }
    }

    @ApiOperation(value = "告警通知 定时任务触发", produces = MediaType.APPLICATION_JSON_VALUE)
    @ApiResponse(code = 200, message = "success")
    @PostMapping(value = "/handlerSecurityWarning")
    public Response handlerSecurityWarning(){
        try{
            securityWarningService.handlerSecurityWarning();
        }catch (RomeException ex){
            log.error("告警信息通知失败，{}",ex);
            return Response.builderFail(ex.getCode(),ex.getMessage());
        }catch (Exception ex){
            log.error("告警信息通知失败，{}",ex);
            return  Response.builderFail(ResCode.STOCK_ERROR_9043,ResCode.STOCK_ERROR_9043_DESC);
        }
        return Response.builderSuccess(null);
    }


    @ApiOperation(value = "新增预警信息", produces = MediaType.APPLICATION_JSON_VALUE)
    @ApiResponse(code = 200, message = "success")
    @PostMapping(value = "/saveSecurityWarning")
    public Response saveSecurityWarning(@Validated @RequestBody  SecurityWarningDTO securityWarningDTO){
        try{
            securityWarningService.saveSecurityWarning(securityWarningDTO);
        }catch (RomeException ex){
            log.error("新增库存告警信息失败，{}",ex);
            return Response.builderFail(ex.getCode(),ex.getMessage());
        }catch (Exception ex){
            log.error("新增库存告警信息失败，{}",ex);
            return  Response.builderFail(ResCode.STOCK_ERROR_9041,ResCode.STOCK_ERROR_9041_DESC);
        }
        return Response.builderSuccess(null);
    }


    @ApiOperation(value = "禁用告警信息", produces = MediaType.APPLICATION_JSON_VALUE)
    @ApiResponse(code = 200, message = "success")
    @GetMapping(value = "/update/{id}/{status}")
    public Response updateSecurityWarningById(@PathVariable("id")Long id,@PathVariable("status")Integer status){
        try{
            securityWarningService.updateSecurityWarningById(id,status);
        }catch (RomeException ex){
            log.error("禁用告警信息失败，{}",ex);
            return Response.builderFail(ex.getCode(),ex.getMessage());
        }catch (Exception ex){
            log.error("禁用告警信息失败，{}",ex);
            return  Response.builderFail(ResCode.STOCK_ERROR_9045,ResCode.STOCK_ERROR_9045_DESC);
        }
        return Response.builderSuccess(null);
    }


    @ApiOperation(value = "测试发短信", produces = MediaType.APPLICATION_JSON_VALUE)
    @ApiResponse(code = 200, message = "success")
    @PostMapping(value = "/sendMessage")
    public Response sendMessage(@RequestParam("phoneList") List<String> phoneList){
        try{
             securityWarningService.sendMessage(phoneList);
             return Response.builderSuccess(null);
        }catch (RomeException ex){
            log.error("发送短信失败，{}",ex);
            return Response.builderFail(ex.getCode(),ex.getMessage());
        }catch (Exception ex){
            log.error("发送短信失败，{}",ex);
            return  Response.builderFail(ResCode.STOCK_ERROR_9047,ResCode.STOCK_ERROR_9047_DESC);
        }
    }

}
