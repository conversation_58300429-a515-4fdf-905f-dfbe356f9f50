package com.rome.stock.innerservice.api.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import java.util.Date;
import java.util.Map;

/**
 * <AUTHOR>
 * @Description
 * @date 2022/9/9.
 */
@Data
@NoArgsConstructor
@EqualsAndHashCode
public class ExportExcelSqlTemplateDTO extends Pagination{

    @ApiModelProperty("唯一主键")
    private Long id;

    @ApiModelProperty("任务标题")
    private String name;

    @ApiModelProperty("描述")
    private String desc;

    @ApiModelProperty("创建时间开始")
    private Date startCreateTime;

    @ApiModelProperty("结束时间开始")
    private Date endCreateTime;

    @ApiModelProperty("sql文本")
    private String sqlText;

    @ApiModelProperty("参数JSON")
    private String paramJson;

    @ApiModelProperty("可执行人员的工号")
    private String executeJson;

    @ApiModelProperty("创建时间")
    private Date createTime;

    @ApiModelProperty("修改时间")
    private Date updateTime;

    @ApiModelProperty("创建人")
    private Long creator;

    @ApiModelProperty("更新人")
    private Long modifier;

    @ApiModelProperty("执行人工号")
    private String executorNum;


    @ApiModelProperty("执行人id")
    private Long executorId;


    @ApiModelProperty("是否可以执行 1是， 0 否")
    private Integer canExecute;

    @ApiModelProperty("是否可以编辑 1是， 0 否")
    private Integer canEdit;

    /**
     * 参数信息
     */
    private Map<String, String> paramMap;
}
