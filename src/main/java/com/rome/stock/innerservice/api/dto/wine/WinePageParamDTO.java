package com.rome.stock.innerservice.api.dto.wine;

import com.rome.stock.innerservice.api.dto.Pagination;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotNull;

/**
 * <p>
 * Description: 酒附加明细查询参数
 * </p>
 *
 * <AUTHOR>
 * @date 2023/4/24
 **/

@Data
@EqualsAndHashCode
public class WinePageParamDTO extends Pagination {

    @ApiModelProperty(value = "酒单据号", required = true)
    @NotNull(message = "酒单据号不能为空")
    private String frontRecordCode;

}
