package com.rome.stock.innerservice.api.dto.message;

import com.fasterxml.jackson.annotation.JsonIgnore;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

import java.io.Serializable;
import java.util.Date;

/**
 * @Description: StoreDTO
 * <p>
 * @Author: chuwenchao  2019/8/21
 */
@ApiModel(description = "门店信息")
@Data
@EqualsAndHashCode
@ToString
public class StoreDTO implements Serializable {

    @ApiModelProperty(value = "只用作sql中name或code的模糊查询")
    private String codeOrName;

    @ApiModelProperty(value = "门店code")
    private String code;

    @ApiModelProperty(value = "门店名称")
    private String name;

    @ApiModelProperty(value = "类型")
    private String type;

    @ApiModelProperty(value = "门店类型")
    private String storeType;

    @ApiModelProperty(value = "区域")
    private String area;

    @ApiModelProperty(value = "省份")
    private String province;

    @ApiModelProperty(value = "城市")
    private String city;

    @ApiModelProperty(value = "区县")
    private String county;

    @ApiModelProperty(value = "商圈名称")
    private String tradeAreaName;

    @ApiModelProperty(value = "描述")
    private String desc;

    @ApiModelProperty(value = "销售面积")
    private String saleArea;

    @ApiModelProperty(value = "营业面积")
    private String businessArea;

    @ApiModelProperty(value = "当地税率")
    private String localTaxRate;

    @ApiModelProperty(value = "格斗数量")
    private Integer cabinetQuantity;

    @ApiModelProperty(value = "电话号码: 拨区号 + 号码")
    private String tel;

    @ApiModelProperty(value = "区域负责人")
    private String areaOwner;

    @ApiModelProperty(value = "大区负责人")
    private String regionOwner;

    @ApiModelProperty(value = "公司代码")
    private String companyCode;

    @ApiModelProperty(value = "实际&预计开店日期")
    @JsonIgnore
    private Date actualOpenDate;

    @ApiModelProperty(value = "冗余")
    @JsonIgnore
    private Date expectOpenDate;

    @ApiModelProperty(value = "住宅号及街道")
    private String address;

    @ApiModelProperty(value = "备注")
    private String remark;

    @ApiModelProperty(value = "营业时间")
    private String saleTime;

    @ApiModelProperty(value = "柜台布置类型")
    private String counterLayoutType;

    @ApiModelProperty(value = "税务登记证号")
    private String taxRegistNum;

    @ApiModelProperty(value = "税务登记证开始日期")
    private Date taxRegistStartDate;

    @ApiModelProperty(value = "组织机构代码")
    private String orgCode;

    @ApiModelProperty(value = "组织机构代码开始日期")
    @JsonIgnore
    private Date orgCodeStartDate;

    @ApiModelProperty(value = "组织机构代码结束日期")
    @JsonIgnore
    private Date orgCodeEndDate;

    @ApiModelProperty(value = "统计证号")
    private String statisticsCertificateNum;

    @ApiModelProperty(value = "统计证开始日期")
    @JsonIgnore
    private Date scNumStartDate;

    @ApiModelProperty(value = "统计证结束日期")
    @JsonIgnore
    private Date scNumEndDate;

    @ApiModelProperty(value = "经度")
    private String longitude;

    @ApiModelProperty(value = "纬度")
    private String latitude;

    @ApiModelProperty(value = "门店代购")
    private String storeAgent;

    @ApiModelProperty(value = "门店自提")
    private String storePickup;

    @ApiModelProperty(value = "闭店日期")
    @JsonIgnore
    private Date closeDate;

    @ApiModelProperty(value = "停车服务")
    private String parkingService;

    @ApiModelProperty(value = "wifi")
    private String wifi;

    @ApiModelProperty(value = "监控")
    private String monitor;

    @ApiModelProperty(value = "要货周期")
    private Integer requireGoodsCycle;

    @ApiModelProperty(value = "门店登记")
    private String storeLevel;

    @ApiModelProperty(value = "风幕柜")
    private String airCurtainCabinet;

    @ApiModelProperty(value = "商圈性质")
    private String tradeAreaQuality;

    @ApiModelProperty(value = "交货工厂")
    private String deliveryFactory;

    @ApiModelProperty(value = "翻新实际竣工时间")
    @JsonIgnore
    private Date renovationFinishDate;

    @ApiModelProperty(value = "人员号")
    private Integer personCode;

    @ApiModelProperty(value = "门店装修风格")
    private String renovationStyle;

    @ApiModelProperty(value = "带教中心门店")
    private String teachingCenterStore;

    @ApiModelProperty(value = "HR组织代码")
    private String hrOrgCode;

    @ApiModelProperty(value = "门店性质：1 直营,2 非分支,3 加盟,4 联营,5，加盟托管")
    private String storeProperties;

    @ApiModelProperty(value = "交货模式")
    private String requireGoodsPattern;

    @ApiModelProperty(value = "所属加盟商")
    private String franchisee;
}
