package com.rome.stock.innerservice.api.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;

@Data
@EqualsAndHashCode
public class TobWarehouseOutPushDTO {
    /**
     * 单据编码
     */
    @ApiModelProperty(value = "单据编号")
    private String recordCode;

    /**
     * 出库时间
     */
    @ApiModelProperty(value = "出库时间")
    private Date notifyTime;


}
