package com.rome.stock.innerservice.api.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;


@Data
public class FrSaleWineLogDTO implements Serializable {

    @ApiModelProperty("id")
    private Long id;

    @ApiModelProperty("wineId")
    private Long wineId;

    @ApiModelProperty("酒订单交易单号")
    private String outRecordCode;

    @ApiModelProperty("操作类型：0.初始化,1.更新信息，2.换桶")
    private Integer operateType;

    @ApiModelProperty("酒sku编码")
    private String skuCode;

    @ApiModelProperty("酒sku名称")
    private String skuCodeName;

    @ApiModelProperty("酒桶序列号（后台操作换桶,更新此字段）")
    private String serialNo;

    @ApiModelProperty("桶sku编码")
    private String bucketSkuCode;

    @ApiModelProperty("桶类型")
    private String bucketType;

    @ApiModelProperty("桶类型名称")
    private String bucketSkuName;

    @ApiModelProperty("原酒桶序列号")
    private String beforeSerialNo;

    @ApiModelProperty("原桶sku编码")
    private String beforeBucketSkuCode;

    @ApiModelProperty("原桶类型")
    private String beforeBucketType;

    @ApiModelProperty("原桶类型名称")
    private String beforeBucketSkuName;

    @ApiModelProperty("酒精度数")
    private String vol;

    @ApiModelProperty("体积")
    private String volume;

    @ApiModelProperty("体积单位")
    private String volumeUnit;

    @ApiModelProperty("开始熟成时间")
    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
    private Date startRipeTime;

    @ApiModelProperty("熟成要求天数")
    private String needRipeDays;

    @ApiModelProperty("已熟成天数")
    private Integer afterRipeDays;

    @ApiModelProperty("抽样时间")
    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
    private Date sampleTime;

    @ApiModelProperty("上次抽样时间")
    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
    private Date lastSampleTime;

    @ApiModelProperty("备注")
    private String remark;

    @ApiModelProperty("更新时间")
    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
    private Date updateTime;

    @ApiModelProperty("更新人")
    private Long modifier;

    @ApiModelProperty("更新人名")
    private String modifierName;

    @ApiModelProperty("主表单据编码（sc_fr_sale_wdt表record_code）")
    private String frontRecordCode;

    @ApiModelProperty("付款时间")
    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
    private Date payTime;

    @ApiModelProperty(value = "原桶订单号")
    private String beforeBucketRecordCode;

    @ApiModelProperty(value = "桶订单号")
    private String bucketRecordCode;

    @ApiModelProperty(value = "附件url")
    private List<FileInfoDto> linkUrl;

}
