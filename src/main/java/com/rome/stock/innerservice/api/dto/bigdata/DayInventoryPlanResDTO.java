package com.rome.stock.innerservice.api.dto.bigdata;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

/**
 * 库存计划查询DTO
 */
@Data
@ApiModel("库存计划返回DTO")
public class DayInventoryPlanResDTO {

    @ApiModelProperty("预估日期")
    private String date;
    @ApiModelProperty("预测需求(BP3)")
    private BigDecimal demandBp3;
    @ApiModelProperty("预测需求(BP2)")
    private BigDecimal demandBp2;
    @ApiModelProperty("预测需求(BP1)")
    private BigDecimal demandBp1;
    @ApiModelProperty("市")
    private String city;
    @ApiModelProperty("渠道")
    private String channel;
    @ApiModelProperty("商品品类1")
    private String cateL1;
    @ApiModelProperty("商品品类2")
    private String cateL2;
    @ApiModelProperty("商品品类3")
    private String cateL3;
//    @ApiModelProperty("补货点")
//    private String lop;
    @ApiModelProperty("库存水位")
    private BigDecimal inventory;
    @ApiModelProperty("发货节点类型")
    private String deliverType;
    @ApiModelProperty("省")
    private String province;
    @ApiModelProperty("商品编码")
    private String skuKey;
    @ApiModelProperty("采购在途库存")
    private BigDecimal buyFlyInventory;
    @ApiModelProperty("数据创建日期")
    private String createDate;
    @ApiModelProperty("安全库存")
    private BigDecimal ss;
    @ApiModelProperty("商品等级")
    private String level;
    @ApiModelProperty("库存下限")
    private BigDecimal lowerLimit;
    @ApiModelProperty("节点类型")
    private String nodeType;
    @ApiModelProperty("调拨在途库存")
    private BigDecimal allocateFlyInventory;
    @ApiModelProperty("发货节点id")
    private String deliverId;
    @ApiModelProperty("配送在途库存")
    private BigDecimal distFlyInventory;
    @ApiModelProperty("预测需求(VLT)")
    private BigDecimal demandVlt;
    @ApiModelProperty("目标库存")
    private BigDecimal ti;
    @ApiModelProperty("区/县")
    private String district;
    @ApiModelProperty("可售库存（期初库存）")
    private BigDecimal saleInventory;
    @ApiModelProperty("预测需求(NRT)")
    private BigDecimal demandNrt;
    @ApiModelProperty("库存上限")
    private BigDecimal upperLimit;
    @ApiModelProperty("节点ID")
    private String nodeId;
    @ApiModelProperty("建议补货量")
    private BigDecimal execReplishment=BigDecimal.ZERO;


}
