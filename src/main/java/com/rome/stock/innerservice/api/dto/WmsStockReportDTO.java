package com.rome.stock.innerservice.api.dto;

import cn.afterturn.easypoi.excel.annotation.Excel;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;
import java.util.Date;

/**
 * @Program: stock-core-service
 * @Description:
 * @Author: Cocoa
 * @Date: 2020/7/21 16:41
 * @Version: v1.0.0
 */
@Data
@EqualsAndHashCode
public class WmsStockReportDTO extends Pagination{

    // 工厂
    @Excel(name = "工厂",orderNum = "0")
    private String factory;
    // 物料
    //@ApiModelProperty(value = "物料")
    @Excel(name = "物料",orderNum = "1")
    private String materials;
    // 描述
    //@ApiModelProperty(value = "描述")
    @Excel(name = "描述",orderNum = "2")
    private String descrip;
    // 库存地点
    //@ApiModelProperty(value = "库存地点")
    @Excel(name = "库存地点",orderNum = "3")
    private String stockLocation;
    // 存储类型
    //@ApiModelProperty(value = "存储类型")
    @Excel(name = "存储类型",orderNum = "4")
    private String storageType;
    // 仓位
    //@ApiModelProperty(value = "仓位")
    @Excel(name = "仓位",orderNum = "5")
    private String stockPosition;
    // 库存类别
    //@ApiModelProperty(value = "库存类别")
    @Excel(name = "库存类别",orderNum = "6")
    private String stockType;
    // 数量
    //@ApiModelProperty(value = "数量")
    @Excel(name = "数量",orderNum = "7")
    private BigDecimal quantity;
    // 单位
    //@ApiModelProperty(value = "单位")
    @Excel(name = "单位",orderNum = "8")
    private String unit;
    // 仓库数量
    //@ApiModelProperty(value = "仓库数量")
    @Excel(name = "仓库数量",orderNum = "9")
    private BigDecimal warehouseNum;
    // 仓库单位
    //@ApiModelProperty(value = "仓库单位")
    @Excel(name = "仓库单位",orderNum = "10")
    private String warehouseUnit;
    // 标记日期
    //@ApiModelProperty(value = "标记日期")
    @Excel(name = "标记日期",orderNum = "11",exportFormat = "yyyy-MM-dd HH:mm:ss")
    private Date remarkDate;
    // 收货日期
    //@ApiModelProperty(value = "收货日期")
    @Excel(name = "收货日期",orderNum = "12",exportFormat = "yyyy-MM-dd HH:mm:ss")
    private Date acceptDate;
    // 生产日期
    //@ApiModelProperty(value = "生产日期")
    @Excel(name = "生产日期",orderNum = "13",exportFormat = "yyyy-MM-dd HH:mm:ss")
    private Date productionDate;
    // 最晚发货日期
    //@ApiModelProperty(value = "最晚发货日期")
    @Excel(name = "最晚发货日期",orderNum = "14",exportFormat = "yyyy-MM-dd HH:mm:ss")
    private Date latestDeliveryDate;
    // 不得销售日期
    //@ApiModelProperty(value = "不得销售日期")
    @Excel(name = "不得销售日期",orderNum = "15",exportFormat = "yyyy-MM-dd HH:mm:ss")
    private Date noSaleDate;
    // 最佳销售日期
    //@ApiModelProperty(value = "最佳销售日期")
    @Excel(name = "最佳销售日期",orderNum = "16",width = 30,exportFormat = "yyyy-MM-dd HH:mm:ss")
    private Date bestSellingDate;
    // 库龄
   //@ApiModelProperty(value = "库龄")
    @Excel(name = "库龄",orderNum = "17")
    private Long storageAge;
}
