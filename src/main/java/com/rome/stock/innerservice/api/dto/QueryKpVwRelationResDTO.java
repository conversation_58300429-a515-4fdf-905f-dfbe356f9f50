/**
 * Filename QueryOriginalStockResDTO.java
 * Company 上海来伊份科技有限公司。
 * <AUTHOR>
 * @version 
 */
package com.rome.stock.innerservice.api.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * 鲲鹏渠道与虚仓类型关系响应DTO对象
 */
@Data
public class QueryKpVwRelationResDTO {
	
	@ApiModelProperty(value="实仓id")
    private Long realWarehouseId;

    @ApiModelProperty(value="虚仓Id列表")
    private List<Long> virtualWarehouseIdList;

    /**
     * 是否纯实仓库存查询
     */
    @ApiModelProperty(value="是否纯实仓库存查询")
    private Boolean realStock;
}
