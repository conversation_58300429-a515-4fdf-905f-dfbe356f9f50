package com.rome.stock.innerservice.api.controller;

import com.github.pagehelper.PageInfo;
import com.rome.arch.core.clientobject.Response;
import com.rome.arch.core.exception.RomeException;
import com.rome.arch.util.controller.RomeController;
import com.rome.stock.innerservice.api.dto.RealWarehouse;
import com.rome.stock.innerservice.api.dto.VirtualWarehouse;
import com.rome.stock.innerservice.api.dto.WarehouseMoveConfigDTO;
import com.rome.stock.innerservice.common.ResCode;
import com.rome.stock.innerservice.common.ValidList;
import com.rome.stock.innerservice.domain.service.WarehouseMoveConfigService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiResponse;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;

import java.util.List;

/**
 * <AUTHOR>
 * @Description 虚仓转移配置
 * @date 2020/6/10 16:11
 */
@Slf4j
@RomeController
@RequestMapping("/stock/v1/warehouse_move_config")
@Api(tags={"虚仓转移配置"})
public class WarehouseMoveConfigController {

    @Autowired
    private WarehouseMoveConfigService warehouseMoveConfigService;

    @ApiOperation(value = "新增虚仓转移配置", nickname = "saveWarehouseMoveConfig")
    @ApiResponse(code = 200, message = "success", response = Response.class)
    @PostMapping(value = "/saveWarehouseMoveConfig")
    public Response saveWarehouseMoveConfig(@Validated @RequestBody ValidList<WarehouseMoveConfigDTO> warehouseMoveConfigDTOList){
        try{
            warehouseMoveConfigService.batchInsert(warehouseMoveConfigDTOList);
            return Response.builderSuccess(null);
        }catch (RomeException e){
            log.error(e.getMessage(), e);
            return Response.builderFail(e.getCode(),e.getMessage());
        }catch (Exception e){
            log.error(e.getMessage(), e);
            return Response.builderFail(ResCode.STOCK_ERROR_1003_DESC,ResCode.STOCK_ERROR_1003_DESC);
        }
    }

    @ApiOperation(value = "更新虚仓转移", nickname = "updateWarehouseMoveConfig")
    @ApiResponse(code = 200, message = "success", response = Response.class)
    @PostMapping(value = "/updateWarehouseMoveConfig")
    public Response updateWarehouseMoveConfig(@Validated @RequestBody ValidList<WarehouseMoveConfigDTO> warehouseMoveConfigDTOList){
        try{
            warehouseMoveConfigService.updateBatch(warehouseMoveConfigDTOList);
            return Response.builderSuccess(null);
        }catch (RomeException e){
            log.error(e.getMessage(), e);
            return Response.builderFail(e.getCode(),e.getMessage());
        }catch (Exception e){
            log.error(e.getMessage(), e);
            return Response.builderFail(ResCode.STOCK_ERROR_1003_DESC,ResCode.STOCK_ERROR_1003_DESC);
        }
    }

    @ApiOperation(value = "更新虚仓转移标识", nickname = "updateById")
    @ApiResponse(code = 200, message = "success", response = Response.class)
    @PostMapping(value = "/updateById/{id}/{status}")
    public Response updateById(@PathVariable("id")Long id,@PathVariable("status")Integer status){
        try{
            warehouseMoveConfigService.updateById(id,status);
            return Response.builderSuccess(null);
        }catch (RomeException e){
            log.error(e.getMessage(), e);
            return Response.builderFail(e.getCode(),e.getMessage());
        }catch (Exception e){
            log.error(e.getMessage(), e);
            return Response.builderFail(ResCode.STOCK_ERROR_1003_DESC,ResCode.STOCK_ERROR_1003_DESC);
        }
    }

    @ApiOperation(value = "根据条件查询实仓转移列表", nickname = "queryWarehouseConfigByCondition")
    @ApiResponse(code = 200, message = "success", response = Response.class)
    @PostMapping(value = "/queryWarehouseConfigByCondition")
    public Response<PageInfo<List<WarehouseMoveConfigDTO>>> queryWarehouseConfigByCondition(@RequestBody WarehouseMoveConfigDTO warehouseMoveConfigDTO){
        try{
            PageInfo<List<WarehouseMoveConfigDTO>> response = warehouseMoveConfigService.queryWarehouseConfigByCondition(warehouseMoveConfigDTO);
            return Response.builderSuccess(response);
        }catch (RomeException e){
            log.error(e.getMessage(), e);
            return Response.builderFail(e.getCode(),e.getMessage());
        }catch (Exception e){
            log.error(e.getMessage(), e);
            return Response.builderFail(ResCode.STOCK_ERROR_1003_DESC,ResCode.STOCK_ERROR_1003_DESC);
        }
    }


    @ApiOperation(value = "查询实仓列表", nickname = "queryRealWarehouse")
    @ApiResponse(code = 200, message = "success", response = Response.class)
    @PostMapping(value = "/queryRealWarehouse")
    public Response<List<RealWarehouse>> queryRealWarehouse(){
        try{
            List<RealWarehouse> response = warehouseMoveConfigService.queryRealWarehouse();
            return Response.builderSuccess(response);
        }catch (RomeException e){
            log.error(e.getMessage(), e);
            return Response.builderFail(e.getCode(),e.getMessage());
        }catch (Exception e){
            log.error(e.getMessage(), e);
            return Response.builderFail(ResCode.STOCK_ERROR_1003_DESC,ResCode.STOCK_ERROR_1003_DESC);
        }
    }

    @ApiOperation(value = "查询虚仓根据实仓ID", nickname = "queryVirtualWarehouseByRealId")
    @ApiResponse(code = 200, message = "success", response = VirtualWarehouse.class)
    @PostMapping(value = "/queryVirtualWarehouseByRealId/{realWarehouseId}")
    public Response<List<VirtualWarehouse>> queryVirtualWarehouseByRealId(@PathVariable("realWarehouseId")Long realWarehouseId){
        try{
            List<VirtualWarehouse> response = warehouseMoveConfigService.queryVirtualWarehouseByRealId(realWarehouseId);
            return Response.builderSuccess(response);
        }catch (RomeException e){
            log.error(e.getMessage(), e);
            return Response.builderFail(e.getCode(),e.getMessage());
        }catch (Exception e){
            log.error(e.getMessage(), e);
            return Response.builderFail(ResCode.STOCK_ERROR_1003_DESC,ResCode.STOCK_ERROR_1003_DESC);
        }
    }

    @ApiOperation(value = "查询实仓id查询配置信息", nickname = "queryConfigByRealWarehouseId")
    @ApiResponse(code = 200, message = "success", response = WarehouseMoveConfigDTO.class)
    @PostMapping(value = "/queryConfigByRealWarehouseId/{realWarehouseId}")
    public Response<List<WarehouseMoveConfigDTO>> queryConfigByRealWarehouseId(@PathVariable("realWarehouseId")Long realWarehouseId){
        try{
            List<WarehouseMoveConfigDTO> response = warehouseMoveConfigService.queryConfigByRealWarehouseId(realWarehouseId);
            return Response.builderSuccess(response);
        }catch (RomeException e){
            log.error(e.getMessage(), e);
            return Response.builderFail(e.getCode(),e.getMessage());
        }catch (Exception e){
            log.error(e.getMessage(), e);
            return Response.builderFail(ResCode.STOCK_ERROR_1003_DESC,ResCode.STOCK_ERROR_1003_DESC);
        }
    }




}    
   