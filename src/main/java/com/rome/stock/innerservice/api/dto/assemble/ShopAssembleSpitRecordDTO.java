package com.rome.stock.innerservice.api.dto.assemble;

import com.fasterxml.jackson.annotation.JsonIgnore;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.util.Date;
import java.util.List;

/**
 * 门店加工单
 */
@Data
@EqualsAndHashCode
public class ShopAssembleSpitRecordDTO {

    @ApiModelProperty(value = "单据编码")
    @NotBlank(message = "单据编码不能为空")
    private String outRecordCode;

    @ApiModelProperty(value = "店铺编码")
    @NotBlank(message = "店铺编码不能为空")
    private String shopCode;

    @ApiModelProperty(value = "单据创建时间")
    @NotNull(message="单据创建时间不能为空")
    private Date assembleCreateTime;

    @JsonIgnore
    private int recordType;

    @ApiModelProperty(value = "应用Id")
    @NotBlank(message = "应用Id不能为空")
    private String appId;

    @ApiModelProperty(value = "sku数量及明细")
    @NotNull(message="sku数量及明细不能为空")
    private List<ShopAssembleSpitDetailDTO> frontRecordDetails;
}
