package com.rome.stock.innerservice.api.controller;

import java.util.List;

import javax.annotation.Resource;

import org.springframework.http.MediaType;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;

import com.alibaba.fastjson.JSON;
import com.rome.arch.core.clientobject.Response;
import com.rome.arch.core.exception.RomeException;
import com.rome.arch.util.controller.RomeController;
import com.rome.stock.innerservice.api.dto.ReceiptRecordWithDetailDTO;
import com.rome.stock.innerservice.api.dto.frontrecord.PurchaseReturnDTO;
import com.rome.stock.innerservice.common.ResCode;
import com.rome.stock.innerservice.common.StringUtils;
import com.rome.stock.innerservice.constant.ResponseMsg;
import com.rome.stock.innerservice.domain.repository.SapInterfaceLogRepository;
import com.rome.stock.innerservice.domain.service.PurchaseOrderOldService;
import com.rome.stock.innerservice.domain.service.PurchaseOrderService;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import io.swagger.annotations.ApiResponse;
import lombok.extern.slf4j.Slf4j;

/**
 * 采购退货供应商
 */
@Slf4j
@RomeController
@RequestMapping("/stock/v1/purchaseReturn")
@Api(tags={"大仓退货"}, description="PurchaseReturnController")
public class PurchaseReturnController {
	@Resource
	private PurchaseOrderOldService purchaseOrderOldService;
	@Resource
	private PurchaseOrderService purchaseOrderService;

	@Resource
	private SapInterfaceLogRepository sapInterfaceLogRepository;

	@ApiOperation(value = "接收采购退货单[老逻辑]", nickname = "receivePurchaseReturn", produces = MediaType.APPLICATION_JSON_VALUE)
	@ApiResponse(code = 200, message = "success")
	@PostMapping("/receivePurchaseReturn")
	public Response receivePurchaseReturn(@ApiParam(name = "purchaseReturn", value = "dto") @RequestBody @Validated PurchaseReturnDTO purchaseReturn) {
		String message = "";
		boolean success = false;
		String json = JSON.toJSONString(purchaseReturn);
		try {
			if (purchaseReturn.getOutRecordCode().startsWith("40")
					|| purchaseReturn.getOutRecordCode().startsWith("45")
					|| purchaseReturn.getOutRecordCode().startsWith("90")
			        || purchaseReturn.getOutRecordCode().startsWith("80")){
				success = true;
				message = "200[40/45/90/80开头退供单直接返回]";
				return ResponseMsg.SUCCESS.buildMsg();
			}
			if (StringUtils.isBlank(purchaseReturn.getPurchaseRecordCode())) {
				return ResponseMsg.FAIL.buildMsg(ResCode.STOCK_ERROR_1003, ResCode.STOCK_ERROR_1003_DESC + ":采购单类型不能空");
			}
			purchaseOrderOldService.receivePurchaseReturn(purchaseReturn);
			success = true;
			message = "200";
			return ResponseMsg.SUCCESS.buildMsg();
		} catch (RomeException e) {
			message = e.getMessage();
			log.error( e.getMessage(), e);
			return ResponseMsg.FAIL.buildMsg(e.getCode(),e.getMessage());
		} catch (Exception e) {
			message = e.getMessage();
			log.error( e.getMessage(), e);
			return ResponseMsg.EXCEPTION.buildMsg(ResCode.STOCK_ERROR_1003_DESC,ResCode.STOCK_ERROR_1003_DESC);
		} finally {
			sapInterfaceLogRepository.saveCallBackInterFaceLog(1, purchaseReturn.getOutRecordCode(), "receivePurchaseReturn",
					json, message, success);
		}
	}

	@ApiOperation(value = "接收采购退货单(新流程)", nickname = "receivePurchaseBatchReturn", produces = MediaType.APPLICATION_JSON_VALUE)
	@ApiResponse(code = 200, message = "success")
	@PostMapping("/receivePurchaseBatchReturn")
	public Response receivePurchaseBatchReturn(@ApiParam(name = "receivePurchaseBatchReturn", value = "dto") @RequestBody @Validated PurchaseReturnDTO purchaseReturn) {
		String message = "";
		boolean success = false;
		String json = JSON.toJSONString(purchaseReturn);
		try {
			purchaseOrderService.receivePurchaseReturn(purchaseReturn);
			success = true;
			message = "200";
			return ResponseMsg.SUCCESS.buildMsg();
		} catch (RomeException e) {
			message = e.getMessage();
			log.error( e.getMessage(), e);
			return ResponseMsg.FAIL.buildMsg(e.getCode(),e.getMessage());
		} catch (Exception e) {
			message = e.getMessage();
			log.error( e.getMessage(), e);
			return ResponseMsg.EXCEPTION.buildMsg(ResCode.STOCK_ERROR_1003_DESC,ResCode.STOCK_ERROR_1003_DESC);
		} finally {
			sapInterfaceLogRepository.saveCallBackInterFaceLog(1, purchaseReturn.getOutRecordCode(), "receivePurchaseReturn",
					json, message, success);
		}
	}


	@ApiOperation(value = "取消退厂单", nickname = "cancelPurchaseReturnRecord", produces = MediaType.APPLICATION_JSON_VALUE)
	@ApiResponse(code = 200, message = "success")
	@PostMapping("/cancelPurchaseReturnRecord")
	public Response cancelPurchaseReturnRecord(@ApiParam(name = "sapRecordCode", value = "sapRecordCode") @RequestParam @Validated String sapRecordCode) {
		String message = "";
		boolean isSucc = false;
		try {
			purchaseOrderOldService.cancelPurchaseReturnRecord(sapRecordCode);
			isSucc = true;
			message = "200";
			return ResponseMsg.SUCCESS.buildMsg();
		} catch (RomeException e) {
			message = e.getMessage();
			log.error( e.getMessage(), e);
			return ResponseMsg.FAIL.buildMsg(e.getCode(), e.getMessage());
		} catch (Exception e) {
			message = e.getMessage();
			log.error( e.getMessage(), e);
			return ResponseMsg.EXCEPTION.buildMsg();
		} finally {
			sapInterfaceLogRepository.saveCallBackInterFaceLog(1, sapRecordCode, "cancelPurchaseReturnRecord",
					sapRecordCode, message, isSucc);
		}
	}

	@ApiOperation(value = "供应商确认退货(新流程)", nickname = "confimPurchaseReturnRecord", produces = MediaType.APPLICATION_JSON_VALUE)
	@ApiResponse(code = 200, message = "success")
	@PostMapping("/confimPurchaseReturnRecord")
	public Response confimPurchaseReturnRecord(@ApiParam(name = "sapRecordCode", value = "sapRecordCode") @RequestParam @Validated String sapRecordCode) {
		String message = "";
		boolean isSucc = false;
		try {
			purchaseOrderOldService.confimPurchaseReturnRecord(sapRecordCode);
			isSucc = true;
			message = "200";
			return ResponseMsg.SUCCESS.buildMsg();
		} catch (RomeException e) {
			message = e.getMessage();
			log.error( e.getMessage(), e);
			return ResponseMsg.FAIL.buildMsg(e.getCode(), e.getMessage());
		} catch (Exception e) {
			message = e.getMessage();
			log.error( e.getMessage(), e);
			return ResponseMsg.EXCEPTION.buildMsg();
		} finally {
			sapInterfaceLogRepository.saveCallBackInterFaceLog(1, sapRecordCode, "confimPurchaseReturnRecord",
					sapRecordCode, message, isSucc);
		}
	}


	@ApiOperation(value = "根据出入库单号查询收货信息", nickname = "queryReceiptByRecordCode", produces = MediaType.APPLICATION_JSON_VALUE)
	@ApiResponse(code = 200, message = "success")
	@PostMapping("/queryReceiptByRecordCode")
	public Response<List<ReceiptRecordWithDetailDTO>> queryReceiptByRecordCode(@RequestParam("recordCode")String recordCode,@RequestParam(value = "wmsRecordCode",required = false)String wmsRecordCode){
		try{
			List<ReceiptRecordWithDetailDTO> receiptRecordWithDetailDTOS = purchaseOrderService.queryReceiptByRecordCode(recordCode,wmsRecordCode);
			return Response.builderSuccess(receiptRecordWithDetailDTOS);
		}catch (RomeException e){
			log.error( e.getMessage(), e);
			return ResponseMsg.FAIL.buildMsg(e.getCode(), e.getMessage());
		}catch (Exception e){
			log.error( e.getMessage(), e);
			return ResponseMsg.EXCEPTION.buildMsg();
		}
	}

}
