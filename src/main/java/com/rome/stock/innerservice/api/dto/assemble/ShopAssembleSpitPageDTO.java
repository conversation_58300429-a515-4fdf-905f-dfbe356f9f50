package com.rome.stock.innerservice.api.dto.assemble;

import java.util.Date;

import com.rome.stock.innerservice.api.dto.Pagination;

import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 类ShopAssembleSpitPage的实现描述：门店组装分拆
 *
 * <AUTHOR> 2019/4/23 20:29
 */
@Data
@EqualsAndHashCode
public class ShopAssembleSpitPageDTO extends Pagination {

    /**
     * 唯一主键
     */
    private Long id;
    /**
     * 单据编号
     */
    private String recordCode;

    /**
     * 门店ID
     */
    private String shopCode;

    /**
     * 门店名称
     */
    private String shopName;

    /**
     * 单据类型：12-加工需求，9-反拆需求单
     */
    private Integer recordType;
    /**
     * 单据状态：11:已入库
     */
    private Integer recordStatus;
    /**
     * 单据状态审核原因
     */
    private String recordStatusReason;
    /**
     * 要求要货时间
     */
    private Date requDeliveryTime;
    /**
     * 计划交货时间
     */
    private Date planDeliveryTime;
    /**
     * 实际叫货时间
     */
    private Date realDeliveryTime;
    /**
     * 指令部门ID(一期暂不考虑)
     */
    private Long instructDeptId;
    /**
     * 指令部门名称(一期暂不考虑)
     */
    private String instructDeptName;
    /**
     * 外部系统单据编号
     */
    private String outRecordCode;
    /**
     * 外部系统数据创建时间
     */
    private Date outCreateTime;

    /**
     * 入向实体仓库id
     */
    private Integer realWarehouseId;

    /**
     * 开始日期
     */
    private Date startDate;

    /**
     * 结束日期
     */
    private Date endDate;

}
