package com.rome.stock.innerservice.api.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

@Data
public class QuerySecurityWarningDTO extends Pagination implements Serializable {


    @ApiModelProperty(value="仓库名称或仓库编码")
    private String nameOrCode;

    @ApiModelProperty(value="商品编码")
    private String skuCode;

    @ApiModelProperty(value="告警信息id集合",hidden = true)
    private List<Long> securityWarningCodeList;


}
