package com.rome.stock.innerservice.api.dto;

import com.github.pagehelper.PageInfo;
import lombok.Data;

import java.util.Date;

/**
 * @Description :
 * @Author: nyacc
 * @Date: 2022/1/19 14:21
 */

@Data
public class RemoteInterfaceLogQuery{

    /**
     * 分页大小
     */
    private Integer pageSize;

    /**
     * 页面编号
     */
    private Integer pageNum;

    /**
     * 状态
     */
    private Integer status;

    /**
     * 业务标识
     */
    private String method;

    /**
     * 出入库单据编号
     */
    private String recordCode;

    /**
     * 开始时间
     */
    private Date startTime;

    /**
     * 结束时间
     */
    private Date endTime;
}
