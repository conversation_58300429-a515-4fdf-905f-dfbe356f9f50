package com.rome.stock.innerservice.api.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import java.util.Date;
import java.util.List;

@Data
@EqualsAndHashCode
public class SkuFocusSale extends Pagination{
    /**
     * 单据子表id
     */
    @ApiModelProperty(value = "单据子表id")
    private Long id;
    /**
     * 创建时间
     */
    @ApiModelProperty(value = "创建时间")
    private Date createTime;
    /**
     * 实仓id
     */
    @ApiModelProperty(value = "实仓id")
    private Long realWarehouseId;
    /**
     * 实仓名称
     */
    @ApiModelProperty(value = "实仓名称")
    private String realWarehouseName;
    /**
     * 实仓编码
     */
    @ApiModelProperty(value = "实仓编码")
    private String realWarehouseCode;
    /**
     * SkuId
     */
    @ApiModelProperty(value = "SkuId")
    private Long skuId;
    /**
     * SkuIds
     */
    @ApiModelProperty(value = "SkuIds")
    private List<Long> skuIds;
    /**
     * sku编码
     */
    @ApiModelProperty(value = "SkuCode")
    private String skuCode;
    /**
     * sku名称
     */
    @ApiModelProperty(value = "Sku名称")
    private String skuName;
    /**
     * 是否启用
     */
    @ApiModelProperty(value = "是否启用")
    private Byte isAvailable;
    /**
     * 创建人
     */
    @ApiModelProperty(value = "创建人")
    private Long creator;

    /**
     * 创建人
     */
    @ApiModelProperty(value = "更新人")
    private Long modifier;


    @ApiModelProperty(value = "创建人工号")
    private String creatorNum;

    @ApiModelProperty(value = "更新人工号")
    private String modifyEmpNum;
}
