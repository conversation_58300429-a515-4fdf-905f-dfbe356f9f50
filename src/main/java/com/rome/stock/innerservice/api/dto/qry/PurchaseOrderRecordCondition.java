package com.rome.stock.innerservice.api.dto.qry;

import com.rome.stock.innerservice.api.dto.Pagination;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;


@Data
@EqualsAndHashCode
public class PurchaseOrderRecordCondition extends Pagination {

	@ApiModelProperty(value = "采购通知单")
	private String recordCode;
	@ApiModelProperty(value = "实仓id")
	private Long realWarehouseId;
	@ApiModelProperty(value = "单据状态")
	private Integer recordStatus;
	@ApiModelProperty(value = "是否退货")
	private Integer isReturn;
	@ApiModelProperty(value = "单据创建开始时间")
	private Date startTime;
	@ApiModelProperty(value = "单据创建结束时间")
	private Date endTime;
	@ApiModelProperty(value = "单据包含的skuid过滤条件")
	private Long skuId;
	@ApiModelProperty(value = "单据包含的skuCode过滤条件")
	private String skuCode;
	@ApiModelProperty(value = "SAP采购单号")
	private String outRecordCode;
	@ApiModelProperty(value = "工厂编号")
	private String factoryCode;
	@ApiModelProperty(value = "采购单类型")
	private Integer purchaseRecordType;
	@ApiModelProperty(value = "供应商编码")
	private String supplierCode;
}
