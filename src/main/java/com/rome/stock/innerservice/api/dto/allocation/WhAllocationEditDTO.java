package com.rome.stock.innerservice.api.dto.allocation;

import java.util.List;

import com.rome.stock.innerservice.api.dto.RealWarehouseStockDTO;

import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 类WhAllocationEditDTO的实现描述：仓库调拨编辑页面
 *
 * <AUTHOR> 2019/5/29 11:02
 */
@Data
@EqualsAndHashCode
public class WhAllocationEditDTO {

    /**
     * 单据信息
     */
    private WhAllocationRecordPageDTO recordDTO;

    /**
     * 选择的商品信息
     */
    private List<RealWarehouseStockDTO> rwDetail;
}
