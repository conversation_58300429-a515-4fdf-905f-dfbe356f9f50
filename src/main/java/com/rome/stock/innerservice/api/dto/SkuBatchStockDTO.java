package com.rome.stock.innerservice.api.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;

/**
 * 用于sku批次号信息展示
 */
@Data
@EqualsAndHashCode
public class SkuBatchStockDTO {
    /**
     * 商品名称
     */
    @ApiModelProperty(value = "商品名称")
    private String skuName;

    /**
     * 商品skuId
     */
    @ApiModelProperty(value = "商品skuId")
    private Long skuId;
    /**
     * 商品sku编码
     */
    @ApiModelProperty(value = "商品编码")
    private String skuCode;
    /**
     * 商品数量
     */
    @ApiModelProperty(value = "商品数量")
    private BigDecimal skuQty;

    /**
     * 单位
     */
    @ApiModelProperty(value = "单位")
    private String unit;

    /**
     * 批次编码-批次号
     */
    @ApiModelProperty(value = "批次编码-批次号")
    private String batchCode;
}
