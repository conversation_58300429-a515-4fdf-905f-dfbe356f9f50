package com.rome.stock.innerservice.api.dto;

import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * @Description SafeStockConfigDTO
 * <AUTHOR>
 * @Date 2024/7/18
 **/
@Data
public class SafeStockConfigDTO {

    private Long id;

    /**
     * 渠道Code
     */
    private String channelCode;

    /**
     * 渠道类型
     */
    private String channelType;

    /**
     * 渠道类型
     */
    private String channelTypeName;

    /**
     * sku类型：1.称重，2.计件
     */
    private Integer skuType;




    /**
     * 是否可用：0-否，1-是
     */
    private Byte isAvailable;




    private Date createTime;


    private String channelName;

    /**
     * 创建人
     */
    private Long creator;

    /**
     * 安全库存量
     */
    private BigDecimal qty;

    private List<SafeStockConfigDTO> subList;

    private List<Long> updateIds;

}
