package com.rome.stock.innerservice.api.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2024-05-17
 */
@Data
@NoArgsConstructor
@EqualsAndHashCode
public class BatchStockBoxOperateDTO {

	@ApiModelProperty(value = "单据类型")
	private Integer recordType;

	@ApiModelProperty(value = "单据编号")
	private String recordCode;

	@ApiModelProperty(value = "实仓ID")
	@NotNull(message="实仓ID不能为空")
	private Long realWarehouseId;

	@ApiModelProperty(value = "原箱批次操作明细")
	private List<BatchStockBoxDetailDTO> detailList;
}
