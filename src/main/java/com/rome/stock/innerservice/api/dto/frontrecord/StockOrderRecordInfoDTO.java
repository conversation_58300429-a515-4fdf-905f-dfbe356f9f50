package com.rome.stock.innerservice.api.dto.frontrecord;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

@Data
public class StockOrderRecordInfoDTO implements Serializable {

    @ApiModelProperty(value = "SO单号", required = true)
    private String orderCode;
    @ApiModelProperty(value = "收货人手机号")
    private String mobile;
    @ApiModelProperty(value = "收货人姓名")
    private String name;
    @ApiModelProperty(value = "详细地址")
    private String address;
    @ApiModelProperty(value = "收货地址邮编")
    private String postcode;
    @ApiModelProperty(value = "备注")
    private String remark;
    @ApiModelProperty(value = "收货人邮箱")
    private String email;


    //省市区
    @ApiModelProperty(value = "省份")
    private String province;
    @ApiModelProperty(value = "省份code")
    private String provinceCode;
    @ApiModelProperty(value = "城市")
    private String city;
    @ApiModelProperty(value = "城市code")
    private String cityCode;
    @ApiModelProperty(value = "四级区域")
    private String county;
    @ApiModelProperty(value = "四级区域code")
    private String countyCode;

}
