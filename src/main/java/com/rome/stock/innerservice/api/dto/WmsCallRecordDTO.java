/**
 * Filename WmsCallRecordDTO.java
 * Company 上海来伊份科技有限公司。
 * <AUTHOR>
 * @version 
 */
package com.rome.stock.innerservice.api.dto;

import java.util.Date;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <AUTHOR>
 * @since 2019年12月25日 上午10:14:23
 */
@Data
@EqualsAndHashCode
@ApiModel(value = "与wms交互接口日志数据", description = "与wms交互接口日志数据")
public class WmsCallRecordDTO extends Pagination {

	/**
	 * 唯一主键
	 */
	@ApiModelProperty(value = "主键")
	private Long id;
	
	/**
	 * 仓库系统编码
	 */
	private Integer wmsCode;
	
	/**
	 * 仓库系统编码描述
	 */
	@ApiModelProperty(value = "仓库系统编码描述")
	private String wmsCodeDesc;
	
	/**
     * 出入库单据编号
     */
    @ApiModelProperty(value = "出入库单据编号")
    private String recordCode;

	/**
	 * 请求服务名
	 */
    @ApiModelProperty(value = "请求服务名")
	private String requestService;
    
	/**
	 * 请求url
	 */
    @ApiModelProperty(value = "请求url")
	private String requestUrl;
    
	/**
	 * 请求内容
	 */
    @ApiModelProperty(value = "请求内容")
	private String requestContent;
    
	/**
	 * 响应内容
	 */
    @ApiModelProperty(value = "响应内容")
	private String responseContent;
    
	/**
	 * 交互状态 0：失败  1:成功
	 */
    @ApiModelProperty(value = "交互状态 0：失败  1:成功")
	private Integer status;
    
	/**
	 * 创建时间
	 */
    @ApiModelProperty(value = "创建时间")
	private Date createTime;
    
	/**
	 * 更新时间
	 */
    @ApiModelProperty(value = "更新时间")
	private Date updateTime;
	
	@ApiModelProperty(value = "创建开始时间")
	private Date startTime;
	
	@ApiModelProperty(value = "创建结束时间")
	private Date endTime;
	
}
