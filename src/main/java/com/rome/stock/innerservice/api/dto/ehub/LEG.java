package com.rome.stock.innerservice.api.dto.ehub;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.xml.bind.annotation.*;
import java.math.BigDecimal;
import java.util.List;

@XmlAccessorType(XmlAccessType.FIELD)
@XmlRootElement(name = "LEG")
@Data
public class LEG {

	@ApiModelProperty("客户单号")
	@XmlElement(name="LEG_NO")
	private String legNo;

	@ApiModelProperty("物流运输号（一般是快递单号）")
	@XmlElement(name="TRACK_NO")
	private String trackNo;

	@ApiModelProperty("订单所属公司编码")
	@XmlElement(name="OWNER_COMPANY_CODE")
	private String ownerCompanyCode;

	@ApiModelProperty("默认客户")
	@XmlElement(name="CUSTOMER_NAME")
	private String customerName="默认客户";

	@ApiModelProperty("承运商名称")
	@XmlElement(name="CARRIER_NAME")
	private String carrierName="默认客户";

	@ApiModelProperty("业务分组")
	@XmlElement(name="BUSI_GROUP")
	private String busiGroup="默认";

	@ApiModelProperty("订单类型")
	@XmlElement(name="ORDER_TYPE")
	private String orderType="电商订单";

	@ApiModelProperty("发货人")
	@XmlElement(name="FROM_LOCATION_NAME")
	private String fromLocationName;

	@ApiModelProperty("发货省")
	@XmlElement(name="FROMPROVINCE")
	private String fromProvince;

	@ApiModelProperty("发货市")
	@XmlElement(name="FROMCITY")
	private String fromCity;

	@ApiModelProperty("发货区域")
	@XmlElement(name="FROMAREA")
	private String fromArea;

	@ApiModelProperty("发货地址")
	@XmlElement(name="FROMADDRESS")
	private String fromAddress;



	@ApiModelProperty("收货人")
	@XmlElement(name="TO_LOCATION_NAME")
	private String toLocationName;

    @ApiModelProperty("收货人电话")
    @XmlElement(name="TOMOBILE")
    private String toMobile;




	@ApiModelProperty("发货省")
	@XmlElement(name="TOPROVINCE")
	private String toProvince;

	@ApiModelProperty("发货市")
	@XmlElement(name="TOCITY")
	private String toCity;

	@ApiModelProperty("发货区域")
	@XmlElement(name="TOAREA")
	private String toArea;

	@ApiModelProperty("发货地址")
	@XmlElement(name="TOADDRESS")
	private String toAddress;

    @ApiModelProperty("重量")
    @XmlElement(name="WEIGHT")
    private BigDecimal weight;

	@ApiModelProperty("体积")
	@XmlElement(name="VOLUME")
	private BigDecimal volume;

//	@ApiModelProperty("件数")
//	@XmlElement(name="QUANTITY")
//	private String quantity;

    @XmlElementWrapper(name = "DETAILS")
	@XmlElement(name="DETAIL")
	private List<Detail> details;
	
}
