package com.rome.stock.innerservice.api.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * @Description KpRemainExpireDayConfig
 * <AUTHOR>
 * @Date 2024/3/22
 **/
@Data
public class KpRemainExpireDayConfigDTO {

    @ApiModelProperty("不可发货天数")
    private Integer batchRemainExpireDay;


    @ApiModelProperty("生产日期效期配置")
    private List<KpChannelValidityConfigDayDTO> validityConfigDayDTO;
}
