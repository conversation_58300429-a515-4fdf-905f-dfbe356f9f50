package com.rome.stock.innerservice.api.dto.replenish;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;


/**
 * APP重新计算仓库
 *
 */
@Data
@EqualsAndHashCode
public class CancelReasonDTO {

    /**
     * 后置单单号
     */
    private String recordCode;

    /**
     * 撤单原因
     */
    private String reasons;

    /**
     * 撤单备注
     */
    private String orderRemarkUser;

    /**
     * 用户ID
     */
    private Long userId;
    @ApiModelProperty(value = "是否强制调用接口，1.是")
    private  Integer isForce;
}
