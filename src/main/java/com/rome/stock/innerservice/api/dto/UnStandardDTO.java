package com.rome.stock.innerservice.api.dto;

import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

@Data
public class UnStandardDTO extends Pagination {

    //主键
    private Long id;
    //商品skuId
    private Long skuId;
    //商品skuCode
    private String skuCode;
    //非标品库存
    private BigDecimal standardQty;
    //创建时间
    private Date createTime;
    //更新时间
    private Date updateTime;
    //创建人
    private Long creator;
    //更新人
    private Long modifier;
    //是否删除：0-否，1-是
    private byte isDelete;

    //sku名称
    private String skuName;

    //sku单位名称
    private String basicUnit;

    private List<Long> skuIds;
    

}
