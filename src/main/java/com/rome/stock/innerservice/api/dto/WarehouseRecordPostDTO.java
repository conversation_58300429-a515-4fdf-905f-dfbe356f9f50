package com.rome.stock.innerservice.api.dto;

import com.rome.stock.innerservice.api.dto.warehouserecord.RecordPackageDTO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

/**
 * 协同一件代发查询明细
 */
@Data
@EqualsAndHashCode
public class WarehouseRecordPostDTO {

    @ApiModelProperty(value = "单据明细")
    private List<WarehouseRecordDetailDTO> warehouseRecordDetailDTOList;

    @ApiModelProperty(value = "包裹明细")
    private List<RecordPackageDTO> packages;
}
