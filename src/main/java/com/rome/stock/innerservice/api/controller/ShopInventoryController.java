package com.rome.stock.innerservice.api.controller;

import com.alibaba.fastjson.JSON;
import com.github.pagehelper.PageInfo;
import com.rome.arch.core.clientobject.Response;
import com.rome.arch.core.exception.RomeException;
import com.rome.arch.util.controller.RomeController;
import com.rome.stock.innerservice.api.dto.RealWarehouseStockDTO;
import com.rome.stock.innerservice.api.dto.frontrecord.InventoryRecordDTO;
import com.rome.stock.innerservice.api.dto.frontrecord.ShopInventoryDetailDTO;
import com.rome.stock.innerservice.api.dto.frontrecord.ShopInventoryPageDTO;
import com.rome.stock.innerservice.api.dto.warehouserecord.BatchResultDTO;
import com.rome.stock.innerservice.api.dto.warehouserecord.CancelRecordDTO;
import com.rome.stock.innerservice.api.dto.warehouserecord.InWarehouseRecordDTO;
import com.rome.stock.innerservice.api.dto.warehouserecord.OutWarehouseRecordDTO;
import com.rome.stock.innerservice.common.ResCode;
import com.rome.stock.innerservice.constant.ResponseMsg;
import com.rome.stock.innerservice.domain.repository.SapInterfaceLogRepository;
import com.rome.stock.innerservice.domain.service.ShopInventoryService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import io.swagger.annotations.ApiResponse;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;

/**
 * 门店盘点
 * <AUTHOR>
 */
@Slf4j
@RomeController
@RequestMapping("/stock/v1/shop_inventory")
@Api(tags={"门店盘点"})
public class ShopInventoryController {

    @Autowired
    private ShopInventoryService shopInventoryService;

    @Resource
    private SapInterfaceLogRepository sapInterfaceLogRepository;


    @ApiOperation(value = "查询未处理盘点单详情列表", nickname = "query_init_shop_inventory_list", produces = MediaType.APPLICATION_JSON_VALUE)
    @ApiResponse(code = 200, message = "success", response = List.class)
    @RequestMapping(value = "/front_record/queryInitShopInventoryRecords", method = RequestMethod.GET)
    public Response<List<Long>>  queryInitShopInventoryRecords( Integer startPage, Integer endPage) {
        try {
            List<Long> list = shopInventoryService.queryInitShopInventoryRecords(startPage, endPage);
            return Response.builderSuccess(list);
        } catch (RomeException e) {
            log.error(e.getMessage(), e);
            return Response.builderFail(e.getCode(),e.getMessage());
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return Response.builderFail(ResCode.STOCK_ERROR_1003_DESC,ResCode.STOCK_ERROR_1003_DESC);
        }
    }

    @Deprecated
    @ApiOperation(value = "批量处理盘点单", nickname = "handleShopInventoryRecords", produces = MediaType.APPLICATION_JSON_VALUE)
    @ApiResponse(code = 200, message = "success", response = Response.class)
    @RequestMapping(value = "/front_record/handleShopInventoryRecords", method = RequestMethod.POST)
    public Response<String> handleShopInventoryRecords(@ApiParam(name = "前置单id", value = "id") @RequestParam Long id) {
        try {
            shopInventoryService.handleShopInventoryRecords(id);
            return Response.builderSuccess("");
        } catch (RomeException e) {
            log.error(e.getMessage(), e);
            return Response.builderFail(e.getCode(),e.getMessage());
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return Response.builderFail(ResCode.STOCK_ERROR_1003_DESC,ResCode.STOCK_ERROR_1003_DESC);
        }
    }

    @ApiOperation(value = "批量保存门店盘点单", nickname = "ShopInventoryRecords", produces = MediaType.APPLICATION_JSON_VALUE)
    @ApiResponse(code = 200, message = "success", response = Response.class)
    @RequestMapping(value = "/front_record/addShopInventoryRecords", method = RequestMethod.POST)
    public Response<String> addShopInventoryRecords(@ApiParam(name = "frontRecord", value = "门店盘点单") @RequestBody @Validated List<InventoryRecordDTO> frontRecords) {
        try {
            log.info("批量盘点单数据addShopInventoryRecords："+JSON.toJSONString(frontRecords));
            shopInventoryService.addShopInventoryRecords(frontRecords);
            return Response.builderSuccess("");
        } catch (RomeException e) {
            log.error(e.getMessage(), e);
            return Response.builderFail(e.getCode(),e.getMessage());
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return Response.builderFail(ResCode.STOCK_ERROR_1003_DESC,ResCode.STOCK_ERROR_1003_DESC);
        }
    }

    @ApiOperation(value = "门店盘点单出库单创建", nickname = "addShopInventoryOutRecord", produces = MediaType.APPLICATION_JSON_VALUE)
    @ApiResponse(code = 200, message = "success", response = Response.class)
    @RequestMapping(value = "/front_record/addShopInventoryOutRecord", method = RequestMethod.POST)
    public Response<String> addShopInventoryOutRecord(@ApiParam(name = "frontRecord", value = "门店盘点出库单") @RequestBody @Validated OutWarehouseRecordDTO dto) {
        try {
            shopInventoryService.addShopInventoryOutRecord(dto);
            return Response.builderSuccess("");
        } catch (RomeException e) {
            log.error(e.getMessage(), e);
            return Response.builderFail(e.getCode(),e.getMessage());
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return Response.builderFail(ResCode.STOCK_ERROR_1003_DESC,e.getMessage());
        }
    }

    @ApiOperation(value = "门店盘点单入库单创建", nickname = "addShopInventoryOutRecord", produces = MediaType.APPLICATION_JSON_VALUE)
    @ApiResponse(code = 200, message = "success", response = Response.class)
    @RequestMapping(value = "/front_record/addShopInventoryInRecord", method = RequestMethod.POST)
    public Response<String> addShopInventoryInRecord(@ApiParam(name = "dto", value = "门店盘点入库单") @RequestBody @Validated InWarehouseRecordDTO dto) {
        try {
            shopInventoryService.addShopInventoryInRecord(dto);
            return Response.builderSuccess("");
        } catch (RomeException e) {
            log.error(e.getMessage(), e);
            return Response.builderFail(e.getCode(),e.getMessage());
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return Response.builderFail(ResCode.STOCK_ERROR_1003_DESC,e.getMessage());
        }
    }

    @ApiOperation(value = "批量取消门店盘点", nickname = "cancelShopInventory", produces = MediaType.APPLICATION_JSON_VALUE)
    @ApiResponse(code = 200, message = "success")
    @RequestMapping(value = "/cancelShopInventory", method = RequestMethod.POST)
    public Response cancelShopInventory(@RequestBody List<CancelRecordDTO> list){
        List<BatchResultDTO> resultList =  new ArrayList<>();
        try {
            if(CollectionUtils.isNotEmpty(list)){
                for (CancelRecordDTO cancelRecordDTO : list) {
                    BatchResultDTO dto = new BatchResultDTO();
                    dto.setRecordCode(cancelRecordDTO.getRecordCode());
                    dto.setStatus(false);
                    String message = "";
                    boolean isSucc = false;
                    try {
                        //门店盘点没有强制取消
                        shopInventoryService.cancelShopInventory(cancelRecordDTO.getRecordCode());
                        dto.setStatus(true);
                        isSucc = true;
                        message = "200";
                    }catch (Exception e){
                        message=e.getMessage();
                        log.error(e.getMessage(), e);
                    }finally {
                        dto.setMessage(message);
                        resultList.add(dto);
                        sapInterfaceLogRepository.saveCallBackInterFaceLog(1, dto.getRecordCode(), "cancelShopInventory",
                                JSON.toJSONString(cancelRecordDTO), message, isSucc);
                    }
                }
            }
            return ResponseMsg.SUCCESS.buildMsg(resultList);
        } catch (RomeException e) {
            log.error(e.getMessage(), e);
            return Response.builderFail(e.getCode(),e.getMessage());
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return ResponseMsg.EXCEPTION.buildMsg();
        }
    }





    @ApiOperation(value = "查询盘点单列表", nickname = "query_shop_inventory_list", produces = MediaType.APPLICATION_JSON_VALUE)
    @ApiResponse(code = 200, message = "success", response = List.class)
    @RequestMapping(value = "/front_record/queryShopInventoryList", method = RequestMethod.POST)
    public Response<PageInfo<ShopInventoryPageDTO>>  queryShopInventoryList(@ApiParam(name = "shopInventory", value = "dto") @RequestBody ShopInventoryPageDTO frontRecord) {
        try {
            PageInfo<ShopInventoryPageDTO> personPageInfo = shopInventoryService.queryShopInventoryList(frontRecord);
            return Response.builderSuccess(personPageInfo);
        } catch (RomeException e) {
            log.error(e.getMessage(), e);
            return Response.builderFail(e.getCode(),e.getMessage());
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return Response.builderFail(ResCode.STOCK_ERROR_1003_DESC,ResCode.STOCK_ERROR_1003_DESC);
        }
    }


    @ApiOperation(value = "查询盘点单详情列表", nickname = "query_shop_inventory_detail_list", produces = MediaType.APPLICATION_JSON_VALUE)
    @ApiResponse(code = 200, message = "success", response = List.class)
    @RequestMapping(value = "/front_record/queryShopInventoryDetailList", method = RequestMethod.GET)
    public Response<List<ShopInventoryDetailDTO>>  queryShopInventoryDetailList(@ApiParam(name = "前置单id", value = "frontRecordId") @RequestParam Long frontRecordId) {
        try {
            List<ShopInventoryDetailDTO> list = shopInventoryService.queryShopInventoryDetailList(frontRecordId);
            return Response.builderSuccess(list);
        } catch (RomeException e) {
            log.error(e.getMessage(), e);
            return Response.builderFail(e.getCode(),e.getMessage());
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return Response.builderFail(ResCode.STOCK_ERROR_1003_DESC,ResCode.STOCK_ERROR_1003_DESC);
        }
    }

    @ApiOperation(value = "查询实仓库存根据仓库编码和工厂编码", nickname = "queryAllWarehouseStock", produces = MediaType.APPLICATION_JSON_VALUE)
    @ApiResponse(code = 200, message = "success", response = RealWarehouseStockDTO.class)
    @RequestMapping(value = "/queryAllWarehouseStock", method = RequestMethod.GET)
    public Response<List<RealWarehouseStockDTO>> queryAllStockByFCodeAndOutCode(@RequestParam("realWarehouseOutCode")String realWarehouseOutCode,@RequestParam("factoryCode")String factoryCode){
        try{
            List<RealWarehouseStockDTO> realWarehouseStockDTOS = shopInventoryService.queryAllWarehouseStockById(realWarehouseOutCode, factoryCode);
            return Response.builderSuccess(realWarehouseStockDTOS);
        }catch (RomeException e){
            log.error(e.getMessage(), e);
            return Response.builderFail(e.getCode(),e.getMessage());
        }catch (Exception e){
            log.error(e.getMessage(), e);
            return Response.builderFail(ResCode.STOCK_ERROR_1003_DESC,ResCode.STOCK_ERROR_1003_DESC);
        }
    }

}
