package com.rome.stock.innerservice.api.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

@ApiModel(value = "RemoteInterfacePackageMaterialDTO", description = "")
@Data
public class RemoteInterfacePackageMaterialDTO {

    @ApiModelProperty(value = "单据号")
    private String recordCode;

    @ApiModelProperty(value = "工厂编号")
    private String factoryCode;

    @ApiModelProperty(value = "外部仓库编码")
    private String realWarehouseOutCode;

    @ApiModelProperty(value = "仓库编码")
    private String realWarehouseCode;

    @ApiModelProperty(value = "skuCode")
    private String skuCode;

    @ApiModelProperty(value = "数量")
    private BigDecimal skuQty;

    @ApiModelProperty(value = "错误信息")
    private String errorMsg;

    @ApiModelProperty(value = "创建时间")
    private Date createTime;

    @ApiModelProperty(value = "更新时间")
    private Date updateTime;

    @ApiModelProperty(value = "操作人")
    private Long modifier;

    private Integer status;

}
