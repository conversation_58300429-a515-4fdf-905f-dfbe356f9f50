package com.rome.stock.innerservice.api.dto.warehouserecord;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * <p>
 * Description: 库存中心通知收货信息
 * </p >
 *
 * <AUTHOR>
 * @date 2023/12/25
 **/
@Data
public class ReceiveOrderStockForm {

    @NotNull(message = "入库单号不能为空")
    @ApiModelProperty("入库单号")
    private String inOrderNo;

    @NotNull(message = "收货单号不能为空")
    @ApiModelProperty("收货单号")
    private String receiveOrderNo;

    @NotEmpty(message = "收货详情不能为空")
    @ApiModelProperty("收货详情")
    private List<ReceiveOrderDetail> details;

    @NotNull(message = "收货人ID不能为空")
    @ApiModelProperty("收货人ID")
    private Long creator;

    @ApiModelProperty("收货人名称")
    private String creatorName;



}
