package com.rome.stock.innerservice.api.dto.frontrecord;

import com.rome.stock.innerservice.api.dto.Pagination;
import com.rome.stock.innerservice.domain.entity.frontrecord.ShopConsumeAdjustRecordDetailE;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;
import java.util.List;

/**
 * @Description
 * <AUTHOR>
 * @Date 2019/5/10 15:33
 * @Version 1.0
 */
@Data
@EqualsAndHashCode
public class ShopConsumeAdjustRecordDTO extends Pagination {

    @ApiModelProperty(value = "唯一主键")
    private Long id;

    @ApiModelProperty(value = "调整单单号")
    private String recordCode;

    @ApiModelProperty(value = "调整单状态")
    private Integer recordStatus;

    @ApiModelProperty(value = "单据类型")
    private Integer recordType;

    @ApiModelProperty(value = "业务原因编号")
    private String reasonCode;

    @ApiModelProperty(value = "SAP过账单号")
    private String sapRecordCode;

    @ApiModelProperty(value = "创建人")
    private Long creator;

    @ApiModelProperty(value = "修改人")
    private Long modifier;

    @ApiModelProperty(value = "归属组织编号")
    private String organizationCode;

    @ApiModelProperty(value = "归属组织名称")
    private String organizationName;

    @ApiModelProperty(value = "门店id")
    private Long realWarehouseId;

    @ApiModelProperty(value = "创建时间")
    private Date createTime;

    @ApiModelProperty(value = "更新时间")
    private Date updateTime;

    @ApiModelProperty(value = "开始时间")
    private Date startTime;

    @ApiModelProperty(value = "结束时间")
    private Date endTime;

    @ApiModelProperty(value = "工厂编号")
    private String factoryCode;

    @ApiModelProperty(value = "工厂名称")
    private String factoryName;

    @ApiModelProperty(value = "仓库外部编号")
    private String realWarehouseCode;

    @ApiModelProperty(value = "仓库名称")
    private String realWarehouseName;

    @ApiModelProperty(value = "备注")
    private String remark;

    @ApiModelProperty(value = "OA审批号")
    private String approveOACode;

    @ApiModelProperty(value = "损耗调整单明细集合")
    private List<ShopConsumeAdjustRecordDetailDTO> detailDTOS;


}
