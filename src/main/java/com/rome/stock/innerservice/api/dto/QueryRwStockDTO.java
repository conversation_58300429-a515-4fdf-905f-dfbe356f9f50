package com.rome.stock.innerservice.api.dto;

import com.rome.stock.common.page.Pagination;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

/**
 * 查询实时库存DTO对象
 */
@Data
public class QueryRwStockDTO  extends Pagination {

    @ApiModelProperty(value = "门店编号")
    private List<String> shopCodes;

    @ApiModelProperty(value = "商品编码")
    private List<String> skuCodes;

    @ApiModelProperty("库存数量最小值")
    private BigDecimal minQty;

    @ApiModelProperty("库存数量最大值")
    private BigDecimal maxQty;

    @ApiModelProperty("省份code")
    private String provinceCode;

    @ApiModelProperty("城市code")
    private String cityCode;

    @ApiModelProperty("区县code")
    private String countyCode;

    @ApiModelProperty("是否加盟商维度汇总统计")
    private boolean franchiseeLevel;


    @ApiModelProperty("是否仅统计总库存和总金额")
    private boolean onlySumQtyAndAmount;

}    
   