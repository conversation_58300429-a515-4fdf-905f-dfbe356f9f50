package com.rome.stock.innerservice.api.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;

/**
 * @description 仓库停发实体对象
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class AddressStopDTO extends Pagination {

    @ApiModelProperty(value="主键")
    private Long id;

    @ApiModelProperty(value="实仓Id")
    private Long realWarehouseId;

    @ApiModelProperty(value="实仓编码")
    private String realWarehouseCode;

    @ApiModelProperty(value="实仓名称")
    private String realWarehouseName;

    @ApiModelProperty(value="停发类型")
    private Integer stopType;

    @ApiModelProperty(value="省份")
    private String province;

    @ApiModelProperty(value="实仓编码/名称",hidden = true)
    private String city;

    @ApiModelProperty(value="区/县")
    private String county;

    @ApiModelProperty(value="乡/镇")
    private String area;

    @ApiModelProperty(value="备注")
    private String remark;

    @ApiModelProperty(value="创建时间",hidden = true)
    private Date createTime;

    @ApiModelProperty(value="创建时间",hidden = true)
    private Date updateTime;

    @ApiModelProperty(value="创建人",hidden = true)
    private Long creator;


    @ApiModelProperty(value="修改人",hidden = true)
    private Long modifier;

    private String address;


    @ApiModelProperty(value = "分页起始行")
    private Integer pIndex;

    @ApiModelProperty(value = "条数")
    private Integer num;
}