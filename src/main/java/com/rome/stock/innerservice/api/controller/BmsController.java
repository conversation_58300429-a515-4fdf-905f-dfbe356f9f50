package com.rome.stock.innerservice.api.controller;

import com.github.pagehelper.PageInfo;
import com.rome.arch.core.clientobject.Response;
import com.rome.arch.core.exception.RomeException;
import com.rome.arch.util.controller.RomeController;
import com.rome.stock.common.enums.bms.ClassGroupVO;
import com.rome.stock.common.enums.bms.UserPositionVO;
import com.rome.stock.innerservice.api.dto.bms.WarehouseWorkPageDTO;
import com.rome.stock.innerservice.api.dto.bms.WarehouseWorkSkuDetailDTO;
import com.rome.stock.innerservice.api.dto.message.BmsPushAgainDTO;
import com.rome.stock.innerservice.api.dto.bms.*;
import com.rome.stock.innerservice.common.ResCode;
import com.rome.stock.innerservice.constant.ResponseMsg;
import com.rome.stock.innerservice.domain.service.BmsRecordService;
import com.rome.stock.innerservice.domain.service.BmsService;
import com.rome.stock.innerservice.remote.bms.dto.BmsDailyBatchStockDTO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import io.swagger.annotations.ApiResponse;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;

@Slf4j
@RomeController
@RequestMapping("/stock/v1/bms")
@Api(tags={"BMS相关接口"})
public class BmsController {


    @Resource
    private BmsService bmsService;
    @Resource
    private BmsRecordService bmsRecordService;

    @ApiOperation(value = "根据查询条件查询所有库存作为信息", nickname = "getWorkInfoByQueryCondition", produces = MediaType.APPLICATION_JSON_VALUE)
    @ApiResponse(code = 200, message = "success", response = List.class)
    @RequestMapping(value = "/getWorkInfoByQueryCondition", method = RequestMethod.POST)
    public Response<PageInfo<WarehouseWorkPageDTO>> getWorkInfoByQueryCondition(@RequestBody WarehouseWorkPageDTO workPageDTO) {
        try {
            PageInfo<WarehouseWorkPageDTO> pageList = bmsService.getWorkInfoByQueryCondition(workPageDTO);
            return Response.builderSuccess(pageList);
        } catch (RomeException e) {
            log.error(e.getMessage(), e);
            return ResponseMsg.EXCEPTION.buildMsg(e.getCode(), e.getMessage());
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return ResponseMsg.EXCEPTION.buildMsg(ResCode.STOCK_ERROR_1003, ResCode.STOCK_ERROR_1003_DESC);
        }
    }

    @ApiOperation(value = "根据查询条件查询所有出入库信息", nickname = "getRecordListByQueryCondition", produces = MediaType.APPLICATION_JSON_VALUE)
    @ApiResponse(code = 200, message = "success", response = List.class)
    @RequestMapping(value = "/getRecordListByQueryCondition", method = RequestMethod.POST)
    public Response<PageInfo<BmsRecordPageDTO>> getRecordListByQueryCondition(@RequestBody BmsRecordPageDTO recordPageDTO) {
        try {
            PageInfo<BmsRecordPageDTO> pageList = bmsRecordService.getListByQueryCondition(recordPageDTO);
            return Response.builderSuccess(pageList);
        } catch (RomeException e) {
            log.error(e.getMessage(), e);
            return ResponseMsg.EXCEPTION.buildMsg(e.getCode(), e.getMessage());
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return ResponseMsg.EXCEPTION.buildMsg(ResCode.STOCK_ERROR_1003, ResCode.STOCK_ERROR_1003_DESC);
        }
    }

    @ApiOperation(value = "查询作业详情", nickname = "querySkuDetail", produces = MediaType.APPLICATION_JSON_VALUE)
    @ApiResponse(code = 200, message = "success")
    @RequestMapping(value = "/querySkuDetail", method = RequestMethod.POST)
    public Response<List<WarehouseWorkSkuDetailDTO>> querySkuDetail(@RequestBody WarehouseWorkPageDTO pageDTO) {
        try {
            return Response.builderSuccess(bmsService.querySkuDetail(pageDTO));
        } catch (RomeException e) {
            log.error(e.getMessage(), e);
            return Response.builderFail(e.getCode(), e.getMessage());
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return Response.builderFail(ResCode.STOCK_ERROR_1003_DESC, ResCode.STOCK_ERROR_1003_DESC);
        }
    }

    @ApiOperation(value = "查询出入库明细商品详情", nickname = "queryRecordSkuDetail", produces = MediaType.APPLICATION_JSON_VALUE)
    @ApiResponse(code = 200, message = "success")
    @RequestMapping(value = "/queryRecordSkuDetail", method = RequestMethod.POST)
    public Response<List<BmsRecordSkuDetailDTO>> queryRecordSkuDetail(@RequestBody BmsRecordPageDTO pageDTO) {
        try {
            return Response.builderSuccess(bmsRecordService.querySkuDetail(pageDTO));
        } catch (RomeException e) {
            log.error(e.getMessage(), e);
            return Response.builderFail(e.getCode(), e.getMessage());
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return Response.builderFail(ResCode.STOCK_ERROR_1003_DESC, ResCode.STOCK_ERROR_1003_DESC);
        }
    }

    @ApiOperation(value = "根据消息Id,重新推送", nickname = "retryById", produces = MediaType.APPLICATION_JSON_VALUE)
    @ApiResponse(code = 200, message = "success")
    @RequestMapping(value = "/retryById", method = RequestMethod.POST)
    public Response retryByMsgId(@RequestParam("id")Long id, @RequestParam("modifier")Long modifier) {
        try {
            bmsService.pushAgain(id);
            return Response.builderSuccess("success");
        }catch (RomeException e) {
            log.error(e.getMessage(), e);
            return ResponseMsg.FAIL.buildMsg(e.getCode(),e.getMessage());
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return ResponseMsg.EXCEPTION.buildMsg(ResCode.STOCK_ERROR_1003_DESC,ResCode.STOCK_ERROR_1003_DESC + e.getMessage());
        }

    }

    @ApiOperation(value = "根据消息Id,重新推送bms出入库数据", nickname = "retryRecordById", produces = MediaType.APPLICATION_JSON_VALUE)
    @ApiResponse(code = 200, message = "success")
    @RequestMapping(value = "/retryRecordById", method = RequestMethod.POST)
    public Response retryRecordById(@RequestParam("id")Long id, @RequestParam("modifier")Long modifier) {
        try {
            bmsRecordService.pushAgain(id);
            return Response.builderSuccess("success");
        }catch (RomeException e) {
            log.error(e.getMessage(), e);
            return ResponseMsg.FAIL.buildMsg(e.getCode(),e.getMessage());
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return ResponseMsg.EXCEPTION.buildMsg(ResCode.STOCK_ERROR_1003_DESC,ResCode.STOCK_ERROR_1003_DESC + e.getMessage());
        }

    }

    @ApiOperation(value = "获取人员岗位列表", nickname = "listUsePosition", produces = MediaType.APPLICATION_JSON_VALUE)
    @ApiResponse(code = 200, message = "success", response = Map.class)
    @GetMapping(value = "/listUsePosition")
    public Response listUsePosition() {
        try {
            return ResponseMsg.SUCCESS.buildMsg(UserPositionVO.getTypeMap());
        } catch (RomeException e) {
            log.error(e.getMessage(), e);
            return ResponseMsg.FAIL.buildMsg(e.getCode(),e.getMessage());
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return ResponseMsg.EXCEPTION.buildMsg(ResCode.STOCK_ERROR_1003_DESC,ResCode.STOCK_ERROR_1003_DESC + e.getMessage());
        }
    }

    @ApiOperation(value = "获取人员班组列表", nickname = "listClassGroup", produces = MediaType.APPLICATION_JSON_VALUE)
    @ApiResponse(code = 200, message = "success", response = Map.class)
    @GetMapping(value = "/listClassGroup")
    public Response listClassGroup() {
        try {
            return ResponseMsg.SUCCESS.buildMsg(ClassGroupVO.getTypeMap());
        } catch (RomeException e) {
            log.error(e.getMessage(), e);
            return ResponseMsg.FAIL.buildMsg(e.getCode(),e.getMessage());
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return ResponseMsg.EXCEPTION.buildMsg(ResCode.STOCK_ERROR_1003_DESC,ResCode.STOCK_ERROR_1003_DESC + e.getMessage());
        }
    }


    @ApiOperation(value = "生成批次库存日结快照", nickname = "saveDailyBatchStockSnapshot", produces = MediaType.APPLICATION_JSON_VALUE)
    @ApiResponse(code = 200, message = "success", response = Map.class)
    @GetMapping(value = "/saveDailyBatchStockSnapshot")
    public Response saveDailyBatchStockSnapshot(@ApiParam(name = "stockDate", value = "库存日期") @RequestParam("stockDate")String stockDate) {
        try {
            bmsService.saveDailyBatchStockSnapshot(stockDate);
            return ResponseMsg.SUCCESS.buildMsg();
        } catch (RomeException e) {
            log.error(e.getMessage(), e);
            return ResponseMsg.FAIL.buildMsg(e.getCode(),e.getMessage());
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return ResponseMsg.EXCEPTION.buildMsg(ResCode.STOCK_ERROR_1003_DESC,ResCode.STOCK_ERROR_1003_DESC + e.getMessage());
        }
    }

    @ApiOperation(value = "推送库存(每日)数据到bms", nickname = "pushDailyBatchStockToBmsBySerialNo", produces = MediaType.APPLICATION_JSON_VALUE)
    @ApiResponse(code = 200, message = "success", response = Map.class)
    @GetMapping(value = "/pushDailyBatchStockToBmsBySerialNo")
    public Response pushDailyBatchStockToBmsBySerialNo(@ApiParam(name = "serialNo", value = "序列号") @RequestParam("serialNo")String serialNo,  @RequestParam(name = "userId", required = false)Long userId) {
        try {
            bmsService.pushDailyBatchStockToBmsBySerialNo(serialNo, userId);
            return ResponseMsg.SUCCESS.buildMsg("");
        } catch (RomeException e) {
            log.error(e.getMessage(), e);
            return ResponseMsg.FAIL.buildMsg(e.getCode(), serialNo + "推送失败，异常信息：" + e.getMessage());
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return ResponseMsg.EXCEPTION.buildMsg(ResCode.STOCK_ERROR_1003_DESC,serialNo + "推送失败，" + ResCode.STOCK_ERROR_1003_DESC + e.getMessage());
        }
    }

    @ApiOperation(value = "查询bms库存数据", nickname = "getBmsStockByQueryCondition", produces = MediaType.APPLICATION_JSON_VALUE)
    @ApiResponse(code = 200, message = "success", response = Map.class)
    @PostMapping(value = "/getBmsStockByQueryCondition")
    Response<PageInfo<BmsDailyBatchStockDTO>> getBmsStockByQueryCondition(@RequestBody BmsStockQueryDTO bmsStockQueryDTO) {
        try {

            return ResponseMsg.SUCCESS.buildMsg(bmsService.getBmsStockByQueryCondition(bmsStockQueryDTO));
        } catch (RomeException e) {
            log.error(e.getMessage(), e);
            return ResponseMsg.FAIL.buildMsg(e.getCode(),e.getMessage());
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return ResponseMsg.EXCEPTION.buildMsg(ResCode.STOCK_ERROR_1003_DESC,ResCode.STOCK_ERROR_1003_DESC + e.getMessage());
        }
    }

    @ApiOperation(value = "批量重推bms数据", nickname = "batchPushAgain", produces = MediaType.APPLICATION_JSON_VALUE)
    @ApiResponse(code = 200, message = "success")
    @RequestMapping(value = "/batchPushAgain", method = RequestMethod.POST)
    public Response<List<String>> batchPushAgain(@RequestBody BmsPushAgainDTO againDTO) {
        try {
            log.info("批量重推bms数据,【{}】", againDTO);
            List<String> resultList = new ArrayList<>();
            for(Long id : againDTO.getIds()) {
                try {
                    if (Objects.equals(againDTO.getType(),1)){
                        bmsService.pushAgain(id);
                    }
                    resultList.add(id + "-处理成功");
                } catch (Exception e) {
                    log.error(e.getMessage(), e);
                    resultList.add(id + "-处理失败-" + e.getMessage());
                }
            }
            return ResponseMsg.SUCCESS.buildMsg(resultList);
        } catch (RomeException e) {
            log.error(e.getMessage(), e);
            return Response.builderFail(e.getCode(),e.getMessage());
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return Response.builderFail(com.rome.stock.common.constants.ResCode.STOCK_ERROR_1001, e.getMessage());
        }
    }
}
