package com.rome.stock.innerservice.api.dto;

import com.rome.stock.innerservice.constant.WDTRecordConst;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;

/**
 * @Doc:.....
 * @Author: lchy
 * @Date: 2020/4/28
 * @Version 1.0
 */
@Data
@EqualsAndHashCode
public class WDTLogPageDTO {

    /**
     * 唯一主键
     */
    private Long id;
    /**
     * 所属单据编码
     */
    private String recordCode;
    private String originOrderCode;
    private String outRecordCode;
    /**
     * 类型 1：拆单  2:修改仓库 3、修改物流 4、仓库物流一起修改
     */
    private Integer type;

    private String beforeValue;
    private String afterValue;


    private Long creator;

    private String empNum;

    private Date createTime;



    private String operateLogInfo;

    public String getOperateLogInfo() {
        if (WDTRecordConst.OPERATE_SPLIT_ORDER.equals(type)) {
            //拆单
            return "拆单创建子do单号" + afterValue;
        }
        if (WDTRecordConst.OPERATE_SPLIT_MAX_ORDER.equals(type)) {
            //拆单
            return "匹配拆单，原仓库为" + beforeValue.replace(",\"null\"" , "") +"拆单后，仓库和do单为" + afterValue;
        }
        if (WDTRecordConst.OPERATE_EDIT_HOUSE.equals(type)) {
            //改仓
            return "原仓" + beforeValue + "修改为新仓" + afterValue;
        }
        if (WDTRecordConst.OPERATE_EDIT_LOGISTICS.equals(type)) {
            //改物流
            return "原物流编码为" + beforeValue + "修改为新物流编码为" + afterValue;
        }
        if (WDTRecordConst.OPERATE_EDIT_HOUSE_AND_LOGISTICS.equals(type)) {
            //改物流和仓库
            return "原仓库和物流编码为" + beforeValue + "修改为新仓库和物流编码为" + afterValue;
        }
        if (WDTRecordConst.OPERATE_RECALCULATE_HOUSE.equals(type)) {
            //重新寻源
            return "原仓库和物流编码为" + beforeValue.replace(",\"null\"" , "") + "重新计算的新仓库、物流编码以及生成的do为" + afterValue;
        }
        if (WDTRecordConst.OPERATE_CANCEL_WAREHOUSE_RECORD.equals(type)) {
            //撤单
            return "出库单" + beforeValue +  afterValue;
        }
        if (WDTRecordConst.OPERATE_PUSH_AGAIN.equals(type)) {
            //重新推送
            return "出库单" + beforeValue + "重新推送，新出库单为" + afterValue;
        }
        if (WDTRecordConst.OPERATE_EDIT_HOUSE_AFTER_CANCEL.equals(type)) {
            //撤单后的改仓
            return "原单" + beforeValue + "，改之后为" + afterValue;
        }
        if (WDTRecordConst.OPERATE_EDIT_LOGISTICS_AFTER_CANCEL.equals(type)) {
            //撤单后的改仓
            return "原单" + beforeValue + "，改之后为" + afterValue;
        }
        if (WDTRecordConst.OPERATE_EDIT_HOUSE_AND_LOGISTICS_AFTER_CANCEL.equals(type)) {
            //撤单后的改仓和物流
            return "原单" + beforeValue + "，改之后为" + afterValue;
        }
        if (WDTRecordConst.OPERATE_CHANGE_WAREHOUSE_APP.equals(type)) {
            //撤单后的重新计算仓库
            return "原单" + beforeValue + "，改之后为" + afterValue;
        }
        if (WDTRecordConst.OPERATE_FORCE_RECALCULATE_HOUSE.equals(type)) {
            //强制重新寻源
            return "原仓库和物流编码为" + beforeValue.replace(",\"null\"" , "") + "强制重新计算的新仓库、物流编码以及生成的do为" + afterValue;
        }
        if (WDTRecordConst.OPERATE_STOP_DELIVERY.equals(type)) {
            //仓库停发
        	return "仓库停发，出库单为" + beforeValue + "，仓库为" + afterValue;
        }
        if (WDTRecordConst.OPERATE_STOP_DELIVERY_CANCEL.equals(type)) {
            //仓库停发恢复发货
        	return "仓库停发恢复发货，出库单为" + beforeValue + "，仓库为" + afterValue;
        }
        if (WDTRecordConst.OPERATE_CANCEL_SO_ORDER.equals(type)) {
            //取消SO单
            return "单据"+beforeValue.replace(",\"null\"" , "").replace(",null" , "")+
                    afterValue.replace("\"null\"" , "").replace(",null" , "");
        }
        if (WDTRecordConst.OPERATE_CANCEL_DO_ORDER.equals(type)) {
            //取消DO单
            return "子do单号"+beforeValue.replace(",\"null\"" , "").replace(",null" , "")+
                    afterValue.replace("\"null\"" , "").replace(",null" , "");
        }
        if (WDTRecordConst.OPERATE_EDIT_SO_ADDRESS.equals(type)) {
            //修改地址信息
            return "原地址为" + beforeValue.replace("\\\"","") + ",新地址为" + afterValue.replace("\\\"","") ;
        }
        if (WDTRecordConst.OPERATE_EDIT_SO_DETAILS.equals(type)) {
            //明细修改信息
            return "以下skuCode数量被修改"+afterValue;
        }
        if (WDTRecordConst.OPERATE_EDIT_DO_ADDRESS.equals(type)) {
            //修改地址信息
            return "原地址为" + beforeValue.replace("\\\"","") + ",新地址为" + afterValue.replace("\\\"","") ;
        }
        if (WDTRecordConst.OPERATE_EDIT_DO_DETAILS.equals(type)) {
            //明细修改信息
            return "以下skuCode数量被修改"+afterValue;
        }
        if (WDTRecordConst.OPERATE_EDIT_PRE_FLAG.equals(type)) {
            //修改预售标志
            return "原预售标志为" + beforeValue + ",新预售标志为" + afterValue;
        }
        return "";
    }

    public void setOperateLogInfo(String operateLogInfo) {
        this.operateLogInfo = operateLogInfo;
    }

}
