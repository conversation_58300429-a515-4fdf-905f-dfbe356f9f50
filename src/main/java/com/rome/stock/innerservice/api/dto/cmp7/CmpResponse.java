package com.rome.stock.innerservice.api.dto.cmp7;

import lombok.Data;

/**
 * <AUTHOR>
 * @description 返回结果信息
 * @date 2021/3/8 17:18
 * @throw
 */
@Data
public class CmpResponse {

    /**
     * cmp系统返回标识码
     * 接收只能整单接收，且一次仅接收一单；
     * 返回值RtnCode为4时表示接收到的数据存在多个单据号，接口无法处理；
     * 返回值RtnCode为3时表示接收到的数据合法性校验异常，接口无法处理；
     * 返回值RtnCode为2时表示单据号重复，表中已存在当前单据，视为数据保存成功，无需重新推送；
     * 返回值RtnCode为1时表示数据保存成功；
     * 返回值RtnCode为-1时，表示数据保存至数据库异常，需重新推送。
     * 若未接收到返回值信息，需尝试重新推送；
     */
    private String RtnCode;


    /**
     * cmp返回信息
     */
    private String Msg;

}    
   