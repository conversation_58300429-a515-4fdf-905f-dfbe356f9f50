package com.rome.stock.innerservice.api.dto.frontrecord;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import java.io.Serializable;

/**
 * 虚拟物品库存调整
 */
@Data
public class AdjustVirtualSkuStockOrderDTO implements Serializable {

    @ApiModelProperty(value = "渠道编码", required = true)
    @NotBlank(message ="渠道编码不能为空")
    private String channelCode;
    @NotBlank
    @ApiModelProperty(value = "外部单号(SO单号)", required = true)
    private  String orderCode;
    @ApiModelProperty(value = "商户id", required = true)
    private Long merchantId;
    @ApiModelProperty(value = "用户code")
    private String userCode;
//    @ApiModelProperty(value = "sku数量及明细")
//    @Valid
//    @NotNull
//    private List<OrderDetailDTO> frontRecordDetails;

}
