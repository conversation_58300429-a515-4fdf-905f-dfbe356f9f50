package com.rome.stock.innerservice.api.dto.frontrecord;

import com.rome.stock.innerservice.api.dto.VirtualWarehouse;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * 采购入库通知单明细
 */
@Data
@EqualsAndHashCode
public class PurchaseOrderDetailPageDTO {
	private String lineNo;

	/**
	 * 期望收货或退货数量【采购单位】
	 */
	private BigDecimal skuQty;
	private Long skuId;
	private String skuCode;


	private String skuName;
	/**
	 * 采购单位名称
	 */
	private String unit;
	/**
	 * 采购单位编码
	 */
	private String unitCode;

	/**
	 * 基本单位名称
	 */
	private String basicUnit;
	/**
	 * 基本单位编码
	 */
	private String basicUnitCode;

	/**
	 * 基础单位的数量
	 */
	private Long basicSkuQty;
	/**
	 * 规格
	 */
	private String spec;

	/**
	 *    转换成基础单位的比例
	 */
	private BigDecimal scale;
	/**
	 * 实际收货或退货数量【采购单位】
	 */
	private BigDecimal actualQty;

	/**
	 * 剩余数量【采购单位】
	 */
	private BigDecimal remindQty;

	private Integer overReceiveRatio ;


	private Integer insufficientRatio ;

	/**
	 * 	行项目状态:已删除、未删除、冻结、交货完成
	 */
	private Integer status;

	/**
	 * 交货日期
	 */
	private Date deliveryData;


	private List<VirtualWarehouse> vmSyncRate;

}
