package com.rome.stock.innerservice.api.dto.frontrecord;

import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

@Data
@EqualsAndHashCode
public class PurchaseOrderAllocConfigPageDTO {
	private Long recordId;
	private String recordCode;
	private Integer recordStatus;
	private String realWarehouseCode;
	private Long realWarehouseId;
	private String realWarehouseName;

	private List<PurchaseOrderDetailPageDTO> skuDetails;

}
