package com.rome.stock.innerservice.api.controller;

import com.alibaba.fastjson.JSON;
import com.github.pagehelper.PageInfo;
import com.rome.arch.core.clientobject.Response;
import com.rome.arch.core.exception.RomeException;
import com.rome.arch.util.controller.RomeController;
import com.rome.stock.innerservice.api.dto.BatchStockBoxCheckDTO;
import com.rome.stock.innerservice.api.dto.BatchStockBoxDetailDTO;
import com.rome.stock.innerservice.api.dto.BatchStockBoxOperateDTO;
import com.rome.stock.innerservice.api.dto.WmsBatchBoxCheckFlowDTO;
import com.rome.stock.innerservice.common.ResCode;
import com.rome.stock.innerservice.constant.ResponseMsg;
import com.rome.stock.innerservice.domain.service.BatchStockBoxDetailService;
import com.rome.stock.innerservice.domain.service.WmsBatchCheckService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiResponse;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;

import java.util.List;

@Slf4j
@RomeController
@RequestMapping("/stock/v1/wms_batch_box_check_flow")
@Api(tags={"WMS批次原箱库存核对"})
public class WmsBatchBoxCheckFlowController {

    @Autowired
    private WmsBatchCheckService wmsBatchCheckService;

    @Autowired
    private BatchStockBoxDetailService batchStockBoxDetailService;

    @ApiOperation(value = "WMS批次原箱库存核对", nickname = "wmsBatchCheckJob", produces = MediaType.APPLICATION_JSON_VALUE)
    @ApiResponse(code = 200, message = "success")
    @RequestMapping(value = "/wmsBatchBoxCheckJob", method = RequestMethod.POST)
    public Response wmsBatchBoxCheckJob(@RequestBody BatchStockBoxCheckDTO batchStockBoxCheckDTO) {
        try {
            wmsBatchCheckService.wmsBatchBoxCheckJob(batchStockBoxCheckDTO);
            return ResponseMsg.SUCCESS.buildMsg(null);
        } catch (RomeException e) {
            log.error(e.getMessage(), e);
            return ResponseMsg.FAIL.buildMsg(e.getCode(),e.getMessage());
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return ResponseMsg.EXCEPTION.buildMsg(ResCode.STOCK_ERROR_1003_DESC,ResCode.STOCK_ERROR_1003_DESC);
        }
    }


    @ApiOperation(value = "刷批次原箱库存", nickname = "operateBatchBoxStock", produces = MediaType.APPLICATION_JSON_VALUE)
    @ApiResponse(code = 200, message = "success")
    @RequestMapping(value = "/operateBatchBoxStock", method = RequestMethod.POST)
    public Response operateBatchBoxStock(@RequestBody @Validated BatchStockBoxOperateDTO dto) {
        try {
            log.info("刷原箱库存开始,参数:"+ JSON.toJSONString(dto));
            batchStockBoxDetailService.operateBatchBoxStock(dto);
            log.info("刷原箱库存结束,参数:"+ JSON.toJSONString(dto));
            return ResponseMsg.SUCCESS.buildMsg(null);
        } catch (RomeException e) {
            log.error(e.getMessage(), e);
            return ResponseMsg.FAIL.buildMsg(e.getCode(),e.getMessage());
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return ResponseMsg.EXCEPTION.buildMsg(ResCode.STOCK_ERROR_1003_DESC,e.getMessage());
        }
    }


    @ApiOperation(value = "根据条件查询wms批次库存核对表", nickname = "queryWmsBatchCheckByCondition", produces = MediaType.APPLICATION_JSON_VALUE)
    @ApiResponse(code = 200, message = "success")
    @RequestMapping(value = "/queryWmsBatchBoxCheckByCondition", method = RequestMethod.POST)
    public Response<PageInfo<WmsBatchBoxCheckFlowDTO>> queryWmsBatchBoxCheckByCondition(@RequestBody WmsBatchBoxCheckFlowDTO paramDto) {
        try {
            PageInfo<WmsBatchBoxCheckFlowDTO> dtoList = wmsBatchCheckService.queryWmsBatchBoxCheckByCondition(paramDto);
            return ResponseMsg.SUCCESS.buildMsg(dtoList);
        } catch (RomeException e) {
            log.error(e.getMessage(), e);
            return ResponseMsg.FAIL.buildMsg(e.getCode(),e.getMessage());
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return ResponseMsg.EXCEPTION.buildMsg(e.getMessage());
        }
    }

}
