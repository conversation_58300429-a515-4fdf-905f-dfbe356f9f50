/**
 * Filename BatchStockChangeFlowController.java
 * Company 上海来伊份科技有限公司。
 * <AUTHOR>
 * @version 
 */
package com.rome.stock.innerservice.api.controller;

import com.rome.stock.innerservice.api.dto.BatchBoxFlowDTO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import com.github.pagehelper.PageInfo;
import com.rome.arch.core.clientobject.Response;
import com.rome.arch.core.exception.RomeException;
import com.rome.arch.util.controller.RomeController;
import com.rome.stock.innerservice.api.dto.BatchStockChangeFlowDTO;
import com.rome.stock.innerservice.common.ResCode;
import com.rome.stock.innerservice.constant.ResponseMsg;
import com.rome.stock.innerservice.domain.service.BatchStockChangeFlowService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiResponse;
import lombok.extern.slf4j.Slf4j;

import java.util.List;

/**
 * 批次库存流水
 * <AUTHOR>
 * @since 2021-7-29 10:22:58
 */
@Slf4j
@RomeController
@RequestMapping(value = "/stock/v1/batch_stock_change_flow")
@Api(tags={"批次库存流水"})
public class BatchStockChangeFlowController {
	
	@Autowired
    private BatchStockChangeFlowService batchStockChangeFlowService;

	/**
     * 查询批次库存流水记录历史数据
     * @param paramDTO
     * @return
     */
    @ApiOperation(value = "查询批次库存流水记录历史数据", nickname = "query_batch_stock_change_flow_history", produces = MediaType.APPLICATION_JSON_VALUE)
    @ApiResponse(code = 200, message = "success", response = BatchStockChangeFlowDTO.class)
    @PostMapping(value = "queryBatchStockChangeFlowHistory")
    public Response queryBatchStockChangeFlowHistory(@RequestBody BatchStockChangeFlowDTO paramDTO) {
        try {
            PageInfo<BatchStockChangeFlowDTO> pageInfo = batchStockChangeFlowService.queryBatchStockChangeFlowHistory(paramDTO);
            return ResponseMsg.SUCCESS.buildMsg(pageInfo);
        } catch (RomeException e) {
            log.error(e.getMessage(), e);
            return Response.builderFail(e.getCode(), e.getMessage());
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return Response.builderFail(ResCode.STOCK_ERROR_1003, ResCode.STOCK_ERROR_1003_DESC);
        }
    }

    /**
     * 查询批次原箱库存流水记录
     * @param paramDTO
     * @return
     */
    @ApiOperation(value = "查询批次原箱库存流水记录", nickname = "queryBatchBoxFlowList", produces = MediaType.APPLICATION_JSON_VALUE)
    @ApiResponse(code = 200, message = "success", response = BatchBoxFlowDTO.class)
    @PostMapping(value = "queryBatchBoxFlowList")
    public Response queryBatchBoxFlowList(@RequestBody BatchBoxFlowDTO paramDTO) {
        try {
            PageInfo<BatchBoxFlowDTO> pageInfo = batchStockChangeFlowService.queryBatchBoxFlowList(paramDTO);
            return ResponseMsg.SUCCESS.buildMsg(pageInfo);
        } catch (RomeException e) {
            log.error(e.getMessage(), e);
            return Response.builderFail(e.getCode(), e.getMessage());
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return Response.builderFail(ResCode.STOCK_ERROR_1003, e.getMessage());
        }
    }
}
