package com.rome.stock.innerservice.api.dto.frontrecord;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * Create by <PERSON><PERSON><PERSON><PERSON><PERSON> on 2019/5/11
 * 实仓出入库流水结果
 */
@Data
public class RwStockChangeFlowResultDTO implements Serializable {

    /**
     * 仓库流水Id
     */
    @ApiModelProperty(value = "仓库流水Id")
    private Long id;
    /**
     * 出入库单号
     */
    @ApiModelProperty(value = "出入库单号")
    private String recordCode;
    /**
     * 前置单据号
     */
    @ApiModelProperty(value = "前置单据号")
    private String frontRecordCode;
    /**
     * 仓库类型
     */
    @ApiModelProperty(value = "仓库类型")
    private Integer transType;
    /**
     * 出库单类型
     */
    @ApiModelProperty(value = "出库单类型")
    private Integer recordType;
    /**
     * 出库单类型名称
     */
    @ApiModelProperty(value = "出库单类型名称")
    private String recordTypeName;
    /**
     * 库存类型
     */
    @ApiModelProperty(value = "库存类型")
    private Integer stockType;
    /**
     * 转出入仓库Id
     */
    @ApiModelProperty(value = "转出入仓库Id")
    private Long realWarehouseId;
    /**
     * 转出入仓库
     */
    @ApiModelProperty(value = "转出入仓库")
    private String realWarehouseName;
    /**
     * 创建人
     */
    @ApiModelProperty(value = "创建人")
    private Long creator;
    /**
     * 创建时间
     */
    @ApiModelProperty(value = "创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss" ,timezone = "GMT+8")
    private Date createTime;
    /**
     * 出入库时间
     */
    @ApiModelProperty(value = "出入库时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss" ,timezone = "GMT+8")
    private Date updateTime;
    /**
     * 商品Id
     */
    @ApiModelProperty(value = "商品Id")
    private Long skuId;
    /**
     * 商品Code
     */
    @ApiModelProperty(value = "商品Code")
    private String skuCode;
    /**
     * 商品名称
     */
    @ApiModelProperty(value = "商品名称")
    private String skuCname;
    /**
     * 单据数量
     */
    @ApiModelProperty(value = "单据数量")
    private BigDecimal changeQty;
    /**
     * 主单位
     */
    @ApiModelProperty(value = "主单位(商品单位)")
    private String unit;

    /**
     * 单位编码
     */
    @ApiModelProperty(value = "单位编码")
    private String unitCode;
    
    /**
     * 变更前库存数量
     */
    @ApiModelProperty(value = "变更前库存数量")
    private BigDecimal beforeChangeQty;
    
    /**
     * 变更后库存数量
     */
    @ApiModelProperty(value = "变更后库存数量")
    private BigDecimal afterChangeQty;
    
    /**
     * 渠道ID
     */
    @ApiModelProperty(value = "渠道ID")
    private String channelCode;
    
    /**
     * 库存类型名称
     */
    @ApiModelProperty(value = "库存类型名称")
    private String stockTypeName;
    
    /**
     * 实仓code
     */
    @ApiModelProperty(value = "实仓code")
    private String realWarehouseCode;

    /**
     * 关联的实仓编码
     */
    @ApiModelProperty(value = "关联的实仓code")
    private  String  relationWarehouseCode;
    
    /**
     * 箱单位名称
     */
    @ApiModelProperty(value = "箱单位名称")
    private String boxUnitName;

    /**
     * 箱单位数量
     */
    @ApiModelProperty(value = "箱单位数量")
    private BigDecimal boxUnitCount;

    @ApiModelProperty(value = "sap交货单号")
    private String sapOrderCode;

    /**
     * 交易单号
     */
    @ApiModelProperty(value = "交易单号")
    private String outRecordCode;

}
