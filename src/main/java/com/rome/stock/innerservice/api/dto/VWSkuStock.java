package com.rome.stock.innerservice.api.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;

@Data
@EqualsAndHashCode
public class VWSkuStock {
    /**
     * Channel shop sku id 商品sku编码
     */
    @ApiModelProperty(value = "skuCode")
    private String skuCode;
    /**
     * Available sales stock qty 渠道中具体商品sku的可销售库存数
     */
    @ApiModelProperty(value = "sku真实数量")
    private BigDecimal availableQty;
}
