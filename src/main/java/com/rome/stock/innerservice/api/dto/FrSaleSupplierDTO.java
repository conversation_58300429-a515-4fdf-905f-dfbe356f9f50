package com.rome.stock.innerservice.api.dto;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.util.List;

@Data
@NoArgsConstructor
@EqualsAndHashCode
public class FrSaleSupplierDTO extends Pagination{
	/**
	 * 唯一主键
	 */
	private Long id;
	/**
	 * 门店编号
	 */
	private String shopCode;
	/**
	 * 门店名称
	 */
	private String shopName;
	/**
	 * 商品编码
	 */
	private String skuCode;
	/**
	 * 单位编号
	 */
	private String unitCode;
	/**
	 * 物料数量
	 */
	private BigDecimal skuQty;
	/**
	 * 供应商仓Code
	 */
	private String supplierRealWarehouseCode;
	/**
	 * 供应商名称
	 */
	private String supplierName;
	/**
	 * 供应商数据生成时间
	 */
	private String supplierDate;

	/**
	 * 开始时间
	 */
	private String startTime;
	/**
	 * 结束时间
	 */
	private String endTime;

	private List<String> shopCodeList;

	private List<String> skuCodeList;

	private List<String> supplierRealWarehouseCodeList;
}
