package com.rome.stock.innerservice.api.dto.verifyaccount;

import lombok.Data;

import javax.validation.Valid;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.util.List;

/**
 * 出入库明细DTO
 *
 * <AUTHOR>
 * @date 2019/06/05
 */
@Data
public class InboundOutboundDetailDTO {
    /**
     * 物料号
     */
    @NotBlank(message = "[ materialNo ]物料编号不能为空")
    private String materialNo;
    /**
     * 该物料总数量
     */
    @NotNull(message = "[ quantity ]数量不能为空")
    private BigDecimal quantity;
    /**
     * 仓库明细
     */
    @NotEmpty(message = "[ warehouseDetails ]仓库明细不能为空")
    @Valid
    private List<WarehouseDetailDTO> warehouseDetails;

}