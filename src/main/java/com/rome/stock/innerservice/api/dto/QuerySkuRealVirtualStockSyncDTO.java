package com.rome.stock.innerservice.api.dto;

import com.rome.arch.core.dto.DTO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 * @Description 查询库存同步比例
 * @date 2020/6/9 15:12
 */
@Data
public class QuerySkuRealVirtualStockSyncDTO extends DTO {

    @ApiModelProperty(value="skuId")
    @NotNull
    private Long skuId;

    @ApiModelProperty(value="虚仓编码")
    private String virtualWarehouseCode;

    @ApiModelProperty(value="实仓外部编码")
    private String warehouseOutCode;

    @ApiModelProperty(value="工厂编码")
    private String factoryCode;


    @ApiModelProperty(value="实仓id",hidden = true)
    private Long realWarehouseId;

    @ApiModelProperty(value="虚仓ID",hidden = true)
    private Long virtualWarehouseId;

}    
   