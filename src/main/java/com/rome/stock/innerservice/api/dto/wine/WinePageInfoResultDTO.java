package com.rome.stock.innerservice.api.dto.wine;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.rome.stock.innerservice.api.dto.FileInfoDto;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * <p>
 * Description: 附加信息分页查询
 * </p>
 *
 * <AUTHOR>
 * @date 2023/4/24
 **/
@Data
@EqualsAndHashCode
public class WinePageInfoResultDTO {

    @ApiModelProperty(value = "唯一主键")
    private Long id;
    @ApiModelProperty(value = "单据唯一编码")
    private String recordCode;

    @ApiModelProperty(value = "酒订单交易单号")
    private String outRecordCode;

    @ApiModelProperty(value = "酒sku编码")
    private String skuCode;

    @ApiModelProperty(value = "酒名称")
    private String skuName;

    @ApiModelProperty(value = "空桶订单号")
    private String bucketRecordCode;

    @ApiModelProperty(value = "酒桶序列号")
    private String serialNo;

    @ApiModelProperty(value = "桶类型")
    private String bucketType;

    @ApiModelProperty(value = "桶类型名称")
    private String bucketSkuName;

    @ApiModelProperty(value = "酒sku编码")
    private String bucketSkuCode;

    @ApiModelProperty(value = "开始熟成时间")
    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
    private Date startRipeTime;

    @ApiModelProperty("熟成要求天数")
    private String needRipeDays;

    @ApiModelProperty("已熟成天数")
    private Integer afterRipeDays;

    @ApiModelProperty(value = "上次抽样时间")
    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
    private Date lastSampleTime;

    @ApiModelProperty(value = "抽样时间")
    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
    private Date sampleTime;

    private String vol;

    @ApiModelProperty(value = "酒精度数")
    private String volName;

    @ApiModelProperty(value = "体积单位")
    private String volumeUnit;

    @ApiModelProperty(value = "体积")
    private String volume;

    @ApiModelProperty(value = "酒单位")
    private String unit;

    @ApiModelProperty(value = "酒单位CODE")
    private String unitCode;

    @ApiModelProperty(value = "更新桶序列号")
    private String newSerialNo;

    @ApiModelProperty(value = "更新桶类型")
    private String newBucketType;

    @ApiModelProperty(value = "更新桶类型名称")
    private String newBucketSkuName;

    @ApiModelProperty(value = "更新桶sku编码")
    private String newBucketSkuCode;

    @ApiModelProperty(value = "附件url")
    private List<FileInfoDto> linkUrl;

    @ApiModelProperty(value = "备注")
    private String remark;

    @ApiModelProperty(value = "酒订单号")
    private String frontRecordCode;

    @ApiModelProperty(value = "桶订单支付状态")
    private Integer recordStatus;

    public String getKey(){
        return outRecordCode+"#"+serialNo;
    }
}
