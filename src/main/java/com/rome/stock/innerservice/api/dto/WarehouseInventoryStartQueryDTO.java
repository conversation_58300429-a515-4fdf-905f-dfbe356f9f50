package com.rome.stock.innerservice.api.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;
import java.util.List;

@ApiModel(value = "WarehouseInventoryStartQueryDTO", description = "盘点单管理查询DTO")
@Data
@EqualsAndHashCode
public class WarehouseInventoryStartQueryDTO extends Pagination {

    private static final long serialVersionUID = 757840252883746949L;
    @ApiModelProperty(value = "id")
    private Long id;

    @ApiModelProperty(value = "盘点记录号")
    private String recordCode;

    @ApiModelProperty(value = "盘点状态")
    private Integer recordStatus;

    @ApiModelProperty(value = "开始创建时间")
    private Date startCreateTime;

    @ApiModelProperty(value = "结束创建时间")
    private Date endCreateTime;

    @ApiModelProperty(value = "实仓id")
    private Long realWarehouseId;

    @ApiModelProperty(value = "实仓id集合")
    private List<Long> realWarehouseIds;

    @ApiModelProperty(value = "创建人")
    private Long creator;

    @ApiModelProperty(value = "盘点序列号")
    private String serialNo;

}
