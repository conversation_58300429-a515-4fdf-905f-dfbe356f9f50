package com.rome.stock.innerservice.api.dto;

import com.rome.stock.innerservice.infrastructure.dataobject.BaseDo;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import java.util.Date;
import java.math.BigDecimal;

/**
 * DO 实体类
 * 
 * <AUTHOR>
 * @date 2022-01-06
 */
@Data
@NoArgsConstructor
@EqualsAndHashCode
public class ChargeAgainstDTO  extends Pagination{

	/**
     * 唯一主键
     */
	private Long id;

	/**
     * 单据编号
     */
	private String recordCode;

	/**
     * 单据类型1,"外采冲销" 2,"委外冲销" 3, "仓库报废冲销 4, "外采退货 5, "销售系统 6, "销售退货 7, "门店试吃 8, "领用出库
     */
	private Integer recordType;

	/**
     * 业务类型：1:入库单冲销（出库单） 2:出库单冲销（入库单）
     */
	private Integer businessType;

	/**
     * 单据状态：0初始状态 -1 已确认 -2已取消 -3部分完成 -4全部完成
     */
	private Integer recordStatus;

	/**
     * 原单据实体仓库id
     */
	private Long originRealWarehouseId;

	/**
	 * 原单据实体仓库Code
	 */
	private String originRealWarehouseCode;
	/**
	 * 原单据实体仓库Name
	 */
	private String originRealWarehouseName;

	/**
     * 冲销单据实体仓库id
     */
	private Long realWarehouseId;


	/**
	 * 实体仓库Code
	 */
	private String realWarehouseCode;
	/**
	 * 实体仓库Name
	 */
	private String realWarehouseName;

	/**
     * 原出入库单号
     */
	private String originWarehouseRecordCode;


	/**
	 * 当前后置单号
	 */
	private String warehouseRecordCode;

	/**
     * 原单号（入库单冲销为收货单号，出库单冲销为出库单号）
     */
	private String originRecordCode;

	/**
     * 原业务单号
     */
	private String originBusinessCode;

	/**
     * 业务单号
     */
	private String businessCode;

	/**
     * 备注
     */
	private String remark;

	/**
     * 冲销原因code
     */
	private String reasonCode;

	/**
     * 推送上游系统状态 0---无需推送 1--待推送 2--已推送
     */
	private Integer syncStatus;

	/**
     * 冲销操作人
     */
	private String operator;

	/**
     * 冲销财务时间
     */
	private Date chargeAgainstDate;

	@ApiModelProperty("开始时间")
	private Date startTime;

	@ApiModelProperty("结束时间")
	private Date endTime;

	@ApiModelProperty("创建时间")
	private Date createTime;

	@ApiModelProperty("更新时间")
	private Date updateTime;

	@ApiModelProperty("冲销类型名称")
	private String recordTypeName;

	@ApiModelProperty("冲销创建人ID")
	private Long creator;

	@ApiModelProperty("成本中心")
	private String costCenterCode;

}
