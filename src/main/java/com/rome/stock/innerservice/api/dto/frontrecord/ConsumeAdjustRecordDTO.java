package com.rome.stock.innerservice.api.dto.frontrecord;

import com.rome.stock.innerservice.api.dto.Pagination;
import com.rome.stock.innerservice.api.dto.frontrecord.ConsumeAdjustRecordDetailDTO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;
import java.util.List;

/**
 * @Description
 * <AUTHOR>
 * @Date 2019/5/10 15:33
 * @Version 1.0
 */
@Data
@EqualsAndHashCode
public class ConsumeAdjustRecordDTO extends Pagination {

    @ApiModelProperty(value = "唯一主键")
    private Long id;

    @ApiModelProperty(value = "调整单单号")
    private String recordCode;

    @ApiModelProperty(value = "调整单状态")
    private Integer recordStatus;

    @ApiModelProperty(value = "单据类型")
    private Integer recordType;

    @ApiModelProperty(value = "领用类型")
    private Integer companyType;

    @ApiModelProperty(value = "业务原因编号")
    private String reasonCode;

    @ApiModelProperty(value = "业务原因描述")
    private String reasonName;

    @ApiModelProperty(value = "SAP过账单号")
    private String sapRecordCode;

    @ApiModelProperty(value = "创建人")
    private Long creator;

    @ApiModelProperty(value = "修改人")
    private Long modifier;

    @ApiModelProperty(value = "归属组织编号")
    private String organizationCode;

    @ApiModelProperty(value = "归属组织名称")
    private String organizationName;

    @ApiModelProperty(value = "成本中心编号")
    private String costCenterCode;

    @ApiModelProperty(value = "成本中心名称")
    private String costCenterName;

    @ApiModelProperty(value = "实仓ID")
    private Long realWarehouseId;

    @ApiModelProperty(value = "创建时间")
    private Date createTime;

    @ApiModelProperty(value = "更新时间")
    private Date updateTime;

    @ApiModelProperty(value = "开始时间")
    private Date startTime;

    @ApiModelProperty(value = "结束时间")
    private Date endTime;

    @ApiModelProperty(value = "工厂编号")
    private String factoryCode;

    @ApiModelProperty(value = "工厂名称")
    private String factoryName;

    @ApiModelProperty(value = "仓库外部编号")
    private String realWarehouseCode;

    @ApiModelProperty(value = "仓库外部编号")
    private String realWarehouseOutCode;

    @ApiModelProperty(value = "仓库名称")
    private String realWarehouseName;

    @ApiModelProperty(value = "备注")
    private String remark;

    @ApiModelProperty(value = "OA审批号")
    private String approveOACode;

    @ApiModelProperty(value = "损耗调整单明细集合")
    private List<ConsumeAdjustRecordDetailDTO> consumeAdjustDetails;

    @ApiModelProperty(value = "领用公司编号")
    private String receiveCompanyCode;

    @ApiModelProperty(value = "出库公司编号")
    private String outCompanyCode;

    @ApiModelProperty(value = "领用日期")
    private Date receiveDate;

    @ApiModelProperty(value = "运输方式 1:自提 2:快递 3:内部通道")
    private Integer transWay;

    @ApiModelProperty(value = "申请人工号")
    private String applier;

    @ApiModelProperty(value = "申请人手机号")
    private String applierMobile;

}
