package com.rome.stock.innerservice.api.dto;

import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;

/**
 * 类WrAdditionInfoDTO的实现描述：后置单附加信息
 *
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class WrAdditionInfoDTO extends Pagination {
    /**
     * 唯一主键
     */
    private Long id;
    /**
     * 所属单据编码
     */
    private String recordCode;


    private BigDecimal coldChainNum;

    private BigDecimal otherContainer;

    private String remark;

    private String remark1;
}
