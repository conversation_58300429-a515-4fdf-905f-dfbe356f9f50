package com.rome.stock.innerservice.api.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

/**
 * @Doc:.....
 * @Author: lchy
 * @Date: 2019/8/22
 * @Version 1.0
 */
@Data
@EqualsAndHashCode
public class RefreshDataForQualityParam {
    private  String recordCode;
    /**
     *  1：真实库存
     * 	2：锁定库存
     * 	3: 质检库存
     * 	4：在途库存
     * 	5：质检不合格库存
     */
    @ApiModelProperty(value = "1：真实库存 2：锁定库存3: 质检库存 4：在途库存 5：质检不合格库存")
    private Integer transType;
    private Long rwId;
    private boolean checkBeforeOp = true;
    private String realWarehouseCode;
    List<RefreshDataForQualityDTO> details;
    /**
     * 用户编号
     */
    private Long userId;

    private String remark;

    private Boolean adjustFlag = false;

    @ApiModelProperty(value = "渠道Code")
    private String channelCode;

}
