package com.rome.stock.innerservice.api.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

/**
 * @Description StockAlarmConfigDetailDTO
 * <AUTHOR>
 * @Date 2024/4/15
 **/
@Data
public class StockAlarmConfigDetailDTO {

    /**
     * 主键ID
     */
    @ApiModelProperty(value = "主键ID")
    private Long id;


    @ApiModelProperty(value = "预警配置主表ID")
    private Long alarmId;

    /**
     * 实仓id
     */
    @ApiModelProperty(value = "实仓id/门店id")
    private Long realWarehouseId;

    /**
     * 实仓编号
     */
    @ApiModelProperty(value = "实仓编号/门店编码")
    private String realWarehouseCode;

    /**
     * 仓库名称
     */
    @ApiModelProperty(value = "仓库名称/门店名称")
    private String realWarehouseName;

    /**
     * 仓库负责人
     */
    @ApiModelProperty(value = "仓库负责人/门店负责人")
    private String accountCodes;



    /**
     * 创建时间
     */
    private Date createTime;
    /**
     * 修改时间
     */
    private Date updateTime;
    /**
     * 创建人
     */
    private Long creator;
    /**
     * 更新人
     */
    private Long modifier;
    /**
     * 是否可用：0-否，1-是
     */
    private Byte isAvailable;
    /**
     * 是否逻辑删除：0-否，1-是
     */
    private Byte isDeleted;
    /**
     * 版本号:默认0,每次更新+1
     */
    private Integer versionNo;


    @Override
    public String toString() {
        return "StockAlarmConfigDetailDTO{" +
                "alarmId=" + alarmId +
                ", realWarehouseId=" + realWarehouseId +
                '}';
    }
}
