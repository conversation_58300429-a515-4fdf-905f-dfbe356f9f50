package com.rome.stock.innerservice.api.controller;

import java.util.ArrayList;
import java.util.List;

import javax.annotation.Resource;

import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;

import com.github.pagehelper.PageInfo;
import com.rome.arch.core.clientobject.Response;
import com.rome.arch.core.exception.RomeException;
import com.rome.arch.util.controller.RomeController;
import com.rome.stock.innerservice.api.dto.WmsStockReportDataDTO;
import com.rome.stock.innerservice.common.ResCode;
import com.rome.stock.innerservice.constant.ResponseMsg;
import com.rome.stock.innerservice.domain.entity.RealWarehouseE;
import com.rome.stock.innerservice.domain.repository.RealWarehouseRepository;
import com.rome.stock.innerservice.domain.repository.warehouserecord.RealWarehouseWmsConfigRespository;
import com.rome.stock.innerservice.domain.service.VmsStockReportService;
import com.rome.stock.wms.constants.WarehouseWmsConfigEnum;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import io.swagger.annotations.ApiResponse;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@RomeController
@RequestMapping("/stock/v1/report")
@Api(tags={"wms库存上传报表"})
public class StockReportController {

    @Resource
    private VmsStockReportService vmsStockReportService;

    @Autowired
    private RealWarehouseWmsConfigRespository realWarehouseWmsConfigRespository;

    @Resource
    private RealWarehouseRepository warehouseRepository;


    @ApiOperation(value = "同步数据到仓库", nickname = "sync_data", produces = MediaType.APPLICATION_JSON_VALUE)
    @ApiResponse(code = 200, message = "success")
    @RequestMapping(value = "/sync_data", method = RequestMethod.POST)
    public Response syncData(){

        try {

            List<Integer> wmsCodeList = new ArrayList<>();
            wmsCodeList.add(WarehouseWmsConfigEnum.ODY.getType());
            wmsCodeList.add(WarehouseWmsConfigEnum.ODYC.getType());
            wmsCodeList.add(WarehouseWmsConfigEnum.DF.getType());
            wmsCodeList.add(WarehouseWmsConfigEnum.WDT.getType());
            // 获取欧电云id集合
            List<Long> ids = this.realWarehouseWmsConfigRespository.getRealWarehouseWmsConfigByWmsCodes(wmsCodeList);

            // 获取欧电云数据
            List<RealWarehouseE> realWarehouseES = this.warehouseRepository.getRealWarehouseByIds(ids);

            if (CollectionUtils.isEmpty(realWarehouseES)){
                throw new RomeException("0","暂无同步的数据");
            }

            this.vmsStockReportService.synOdyData(realWarehouseES);
            return Response.builderSuccess("success");
        } catch (RomeException e) {
            log.error(e.getMessage(), e);
            return Response.builderFail(e.getCode(), e.getMessage());
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return Response.builderFail(ResCode.STOCK_ERROR_1003, ResCode.STOCK_ERROR_1003_DESC);
        }

    }

    @ApiOperation(value = "根据当天日期生成excel上传到ftp", nickname = "stock_upload_toftp", produces = MediaType.APPLICATION_JSON_VALUE)
    @ApiResponse(code = 200, message = "success", response = List.class)
    @RequestMapping(value = "/stock_upload_toftp", method = RequestMethod.POST)
    public Response stockUploadToftp() {
        try {
            this.vmsStockReportService.stockUploadToftp();
            return Response.builderSuccess("上传成功");
        } catch (RomeException e) {
            log.error(e.getMessage(), e);
            return Response.builderFail(e.getCode(),e.getMessage());
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return Response.builderFail(ResCode.STOCK_ERROR_1003_DESC,ResCode.STOCK_ERROR_1003_DESC);
        }
    }
    
    @ApiOperation(value = "根据条件查询wms库存上传报表", nickname = "queryWmsStockReportByCondition", produces = MediaType.APPLICATION_JSON_VALUE)
    @ApiResponse(code = 200, message = "success")
    @RequestMapping(value = "/queryWmsStockReportByCondition", method = RequestMethod.POST)
    public Response<PageInfo<WmsStockReportDataDTO>> queryWmsStockReportByCondition(@RequestBody WmsStockReportDataDTO paramDto) {
        try {
            PageInfo<WmsStockReportDataDTO> dtoList = vmsStockReportService.queryWmsStockReportByCondition(paramDto);
            return ResponseMsg.SUCCESS.buildMsg(dtoList);
        } catch (RomeException e) {
            log.error(e.getMessage(), e);
            return ResponseMsg.FAIL.buildMsg(e.getCode(),e.getMessage());
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return ResponseMsg.EXCEPTION.buildMsg(e.getMessage());
        }
    }
    
    @ApiOperation(value = "鲲鹏仓和门店库存上传ftp", nickname = "kpShopStockUploadToFtp", produces = MediaType.APPLICATION_JSON_VALUE)
    @ApiResponse(code = 200, message = "success", response = List.class)
    @RequestMapping(value = "/kpShopStockUploadToFtp", method = RequestMethod.POST)
    public Response kpShopStockUploadToFtp() {
        try {
            this.vmsStockReportService.kpShopStockUploadToFtp();
            return Response.builderSuccess("上传成功");
        } catch (RomeException e) {
            log.error(e.getMessage(), e);
            return Response.builderFail(e.getCode(),e.getMessage());
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return Response.builderFail(ResCode.STOCK_ERROR_1003_DESC,ResCode.STOCK_ERROR_1003_DESC);
        }
    }
    
    @ApiOperation(value = "鲲鹏仓和门店库存上传ftp,异步", nickname = "kpShopStockUploadToFtpByAsyn", produces = MediaType.APPLICATION_JSON_VALUE)
    @ApiResponse(code = 200, message = "success", response = List.class)
    @RequestMapping(value = "/kpShopStockUploadToFtpByAsyn", method = RequestMethod.POST)
    public Response kpShopStockUploadToFtpByAsyn() {
        try {
            this.vmsStockReportService.kpShopStockUploadToFtpByAsyn();
            return Response.builderSuccess("加入成功");
        } catch (RomeException e) {
            log.error(e.getMessage(), e);
            return Response.builderFail(e.getCode(),e.getMessage());
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return Response.builderFail(ResCode.STOCK_ERROR_1003_DESC,ResCode.STOCK_ERROR_1003_DESC);
        }
    }
    
    @ApiOperation(value = "根据实仓Id列表，实仓库存上传ftp", produces = MediaType.APPLICATION_JSON_VALUE)
    @ApiResponse(code = 200, message = "success", response = String.class)
    @RequestMapping(value = "/stockUploadToFtpByWarehouseIds", method = RequestMethod.POST)
    public Response stockUploadToFtpByWarehouseIds(@ApiParam(name = "fileName", value= "上传文件名") @RequestParam("fileName") String fileName, @ApiParam(name = "list", value= "实仓Id列表")@RequestBody List<Long> list) {
    	try {
    		vmsStockReportService.stockUploadToFtpByWarehouseIds(list, fileName);
            return Response.builderSuccess("成功");
        } catch (RomeException e) {
            log.error(e.getMessage(), e);
            return Response.builderFail(e.getCode(),e.getMessage());
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return Response.builderFail(ResCode.STOCK_ERROR_1003_DESC,ResCode.STOCK_ERROR_1003_DESC);
        } 
    }


    @ApiOperation(value = "门店库存上传ftp,异步", nickname = "shopStockUploadToFtpByAsyn", produces = MediaType.APPLICATION_JSON_VALUE)
    @ApiResponse(code = 200, message = "success", response = List.class)
    @RequestMapping(value = "/shopStockUploadToFtpByAsyn", method = RequestMethod.POST)
    public Response shopStockUploadToFtpByAsyn() {
        try {
            this.vmsStockReportService.shopStockUploadToFtpByAsynNew();
            return Response.builderSuccess("加入成功");
        } catch (RomeException e) {
            log.error(e.getMessage(), e);
            return Response.builderFail(e.getCode(),e.getMessage());
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return Response.builderFail(ResCode.STOCK_ERROR_1003_DESC,ResCode.STOCK_ERROR_1003_DESC);
        }
    }


}
