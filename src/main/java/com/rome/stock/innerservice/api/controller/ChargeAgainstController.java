package com.rome.stock.innerservice.api.controller;

import com.github.pagehelper.PageInfo;
import com.rome.arch.core.clientobject.Response;
import com.rome.arch.core.exception.RomeException;
import com.rome.arch.util.controller.RomeController;
import com.rome.stock.innerservice.api.dto.*;
import com.rome.stock.innerservice.common.RedisUtil;
import com.rome.stock.innerservice.common.ResCode;
import com.rome.stock.innerservice.constant.ResponseMsg;
import com.rome.stock.innerservice.domain.convertor.WarehouseRecordConvertor;
import com.rome.stock.innerservice.domain.entity.frontrecord.ChargeAgainstE;
import com.rome.stock.innerservice.domain.entity.warehouserecord.WarehouseRecordE;
import com.rome.stock.innerservice.domain.repository.frontrecord.FrChargeAgainstRepository;
import com.rome.stock.innerservice.domain.repository.warehouserecord.WarehouseRecordRepository;
import com.rome.stock.innerservice.domain.service.ChargeAgainstService;
import com.rome.stock.innerservice.infrastructure.mapper.frontrecord.FrChargeAgainstConfigMapper;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiResponse;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

@Slf4j
@RomeController
@RequestMapping("/stock/v1/chargeAgainst")
@Api(tags={"冲销服务接口"})
public class ChargeAgainstController {

    @Resource
    private ChargeAgainstService chargeAgainstService;
    @Resource
    private FrChargeAgainstRepository frChargeAgainstRepository;
    @Resource
    private RedisUtil redisUtil;
    @Resource
    private WarehouseRecordRepository warehouseRecordRepository;
    @Resource
    private WarehouseRecordConvertor warehouseRecordConvertor;

    @ApiOperation(value = "冲销单明细查询", nickname = "queryReverseRecordDetailList", produces = MediaType.APPLICATION_JSON_VALUE)
    @ApiResponse(code = 200, message = "success")
    @RequestMapping(value = "/queryReverseRecordDetailList", method = RequestMethod.GET)
    public Response<List<ChargeAgainstCreateDetailDTO>> queryReverseRecordDetailList(@RequestParam("recordCode") String recordCode) {
        try {
            return ResponseMsg.SUCCESS.buildMsg(chargeAgainstService.queryReverseRecordDetailList(recordCode));
        } catch (RomeException e) {
            log.error(e.getMessage(), e);
            return ResponseMsg.FAIL.buildMsg(e.getCode(), e.getMessage());
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return ResponseMsg.FAIL.buildMsg("500000", e.getMessage());
        }
    }

    @ApiOperation(value = "冲销单确认", nickname = "confirmReverseRecord", produces = MediaType.APPLICATION_JSON_VALUE)
    @ApiResponse(code = 200, message = "success")
    @RequestMapping(value = "/confirmReverseRecord", method = RequestMethod.POST)
    public Response confirmReverseRecord(@RequestBody List<String> recordCodes, @RequestParam("modifier") Long modifier) {
        String uniqueKey = "confirmReverseRecord";
        boolean isLock = false;
        try {
            isLock = redisUtil.lock(uniqueKey, "1", 120);
            if (isLock) {
                //添加分布式锁
                for (String recordCode : recordCodes) {
                    ChargeAgainstE frChargeAgainstDO = frChargeAgainstRepository.getByRecordCode(recordCode);
                    if (null == frChargeAgainstDO) {
                        throw new RomeException(ResCode.STOCK_ERROR_1001, "单据不存在," + recordCode);
                    }
                    if (frChargeAgainstDO.getRecordStatus() == 1) {
                        continue;
                    }
                    if (frChargeAgainstDO.getRecordStatus() != 0) {
                        throw new RomeException(ResCode.STOCK_ERROR_1001, "单据状态不是初始状态," + recordCode);
                    }
                    //调用财务中台接口，用原单查询是否能冲销
                    chargeAgainstService.confirmReverse(frChargeAgainstDO);
                    chargeAgainstService.confirmReverseRecord(recordCodes, modifier, Boolean.TRUE);
                }
            } else {
                throw new RomeException(ResCode.STOCK_ERROR_1017, "冲销确认正在处理中，请稍后再试。。。");
            }
            return ResponseMsg.SUCCESS.buildMsg();
        } catch (RomeException e) {
            log.error(e.getMessage(), e);
            return ResponseMsg.FAIL.buildMsg(e.getCode(), e.getMessage());
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return ResponseMsg.FAIL.buildMsg("500000", e.getMessage());
        }finally {
            if(isLock) {
                redisUtil.unLock(uniqueKey, "1");
            }
        }
    }

    @ApiOperation(value = "冲销单取消", nickname = "cancelReverseRecord", produces = MediaType.APPLICATION_JSON_VALUE)
    @ApiResponse(code = 200, message = "success")
    @RequestMapping(value = "/cancelReverseRecord", method = RequestMethod.POST)
    public Response cancelReverseRecord(@RequestParam("recordCode") String recordCode, @RequestParam("modifier") Long modifier) {
        String uniqueKey = "cancelReverseRecord"+recordCode;
        boolean isLock = false;
        try {
            isLock = redisUtil.lock(uniqueKey, recordCode, 120);
            if (isLock) {
                chargeAgainstService.cancelReverseRecord(recordCode, modifier);
            } else {
                throw new RomeException(ResCode.STOCK_ERROR_1017, "冲销单取消正在处理中，请稍后再试。。。");
            }
            return ResponseMsg.SUCCESS.buildMsg();
        } catch (RomeException e) {
            log.error(e.getMessage(), e);
            return ResponseMsg.FAIL.buildMsg(e.getCode(), e.getMessage());
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return ResponseMsg.FAIL.buildMsg("500000", e.getMessage());
        }finally {
            if(isLock) {
                redisUtil.unLock(uniqueKey, recordCode);
            }
        }
    }


    @ApiOperation(value = "冲销单管理查询", nickname = "queryReverseRecordByCondition", produces = MediaType.APPLICATION_JSON_VALUE)
    @ApiResponse(code = 200, message = "success")
    @PostMapping("/queryReverseRecordByCondition")
    public Response<PageInfo<ChargeAgainstDTO>> queryReverseRecordByCondition(@RequestBody ChargeAgainstDTO chargeAgainstDTO) {
        try {
            PageInfo<ChargeAgainstDTO> res=chargeAgainstService.queryReverseRecordByCondition(chargeAgainstDTO);
            return ResponseMsg.SUCCESS.buildMsg(res);
        } catch (RomeException e) {
            log.error(e.getMessage(), e);
            return ResponseMsg.FAIL.buildMsg(e.getCode(), e.getMessage());
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return ResponseMsg.FAIL.buildMsg("-500", e.getMessage());
        }
    }

    @ApiOperation(value = "创建冲销单", nickname = "createChargeAgainstRecord", produces = MediaType.APPLICATION_JSON_VALUE)
    @ApiResponse(code = 200, message = "success", response = String.class)
    @PostMapping("/createChargeAgainstRecord")
    public Response createChargeAgainstRecord(@RequestBody ChargeAgainstCreateDTO chargeAgainstCreateDTO) {
        String uniqueKey = "createChargeAgainstRecord";
        boolean isLock = false;
        try {
            isLock = redisUtil.lock(uniqueKey, "1", 120);
            if (isLock) {
                chargeAgainstService.createChargeAgainstRecord(chargeAgainstCreateDTO);
            } else {
                throw new RomeException(ResCode.STOCK_ERROR_1017, "冲销单创建正在处理中，请稍后再试。。。");
            }
            return ResponseMsg.SUCCESS.buildMsg();
        } catch (RomeException e) {
            log.error(e.getMessage(), e);
            return ResponseMsg.FAIL.buildMsg(e.getCode(), e.getMessage());
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return ResponseMsg.FAIL.buildMsg("-500", e.getMessage());
        }finally {
            if(isLock) {
                redisUtil.unLock(uniqueKey, "1");
            }
        }
    }

    /**
     * 根据业务单号或入库单号
     * 查询收货明细或者出库明细
     * @return
     */
    @ApiOperation(value = "查询收货明细或者出库明细", nickname = "queryReceiveByRecordCode", produces = MediaType.APPLICATION_JSON_VALUE)
    @ApiResponse(code = 200, message = "success", response = String.class)
    @PostMapping("/queryReceiveByRecordCode")
    public Response<List<WarehouseReceiveDTO>> queryReceiveByRecordCode(@RequestBody WarehouseRecordDTO warehouseRecordDTO) {
        try {
            return ResponseMsg.SUCCESS.buildMsg(chargeAgainstService.queryReceiveByRecordCode(warehouseRecordDTO));
        } catch (RomeException e) {
            log.error(e.getMessage(), e);
            return ResponseMsg.FAIL.buildMsg(e.getCode(), e.getMessage());
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return ResponseMsg.FAIL.buildMsg("-500", e.getMessage());
        }
    }



    /**
     * 根据收货单号
     * 查询收货批次或者出库单明细
     * @return
     */
    @ApiOperation(value = "查询收货批次或者出库单明细", nickname = "queryBatchListByReceiveCode", produces = MediaType.APPLICATION_JSON_VALUE)
    @ApiResponse(code = 200, message = "success", response = String.class)
    @PostMapping("/queryBatchListByReceiveCode")
    public Response<ChargeAgainstCreateDTO> queryBatchListByReceiveCode(@RequestBody WarehouseReceiveDTO warehouseReceiveDTO) {
        try {
            return ResponseMsg.SUCCESS.buildMsg(chargeAgainstService.queryBatchListByReceiveCode(warehouseReceiveDTO));
        } catch (RomeException e) {
            log.error(e.getMessage(), e);
            return ResponseMsg.FAIL.buildMsg(e.getCode(), e.getMessage());
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return ResponseMsg.FAIL.buildMsg("-500", e.getMessage());
        }
    }

    /**
     * 查询冲销配置列表数据
     *
     * @return
     */
    @ApiOperation(value = "queryConfigList", nickname = "queryConfigList", produces = MediaType.APPLICATION_JSON_VALUE)
    @ApiResponse(code = 200, message = "success", response = String.class)
    @GetMapping("/queryConfigList")
    public Response<List<ChargeAgainstConfigDTO>> queryConfigList() {
        try {
            return ResponseMsg.SUCCESS.buildMsg(chargeAgainstService.queryConfigList());
        } catch (RomeException e) {
            log.error(e.getMessage(), e);
            return ResponseMsg.FAIL.buildMsg(e.getCode(), e.getMessage());
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return ResponseMsg.FAIL.buildMsg("-500", e.getMessage());
        }
    }

    @ApiOperation(value = "queryNeedNotifyRecordList", nickname = "queryNeedNotifyRecordList", produces = MediaType.APPLICATION_JSON_VALUE)
    @ApiResponse(code = 200, message = "success", response = String.class)
    @PostMapping("/queryNeedNotifyRecordList")
    public Response<List<ChargeAgainstDTO>> queryNeedNotifyRecordList(@RequestBody ChargeAgainstDTO chargeAgainstDTO) {
        try {
            return ResponseMsg.SUCCESS.buildMsg(chargeAgainstService.queryNeedNotifyRecordList(chargeAgainstDTO));
        } catch (RomeException e) {
            log.error(e.getMessage(), e);
            return ResponseMsg.FAIL.buildMsg(e.getCode(), e.getMessage());
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return ResponseMsg.FAIL.buildMsg("-500", e.getMessage());
        }
    }

    @ApiOperation(value = "pushNotifyData", nickname = "pushNotifyData", produces = MediaType.APPLICATION_JSON_VALUE)
    @ApiResponse(code = 200, message = "success", response = String.class)
    @PostMapping("/pushNotifyData")
    public Response pushNotifyData(@RequestBody ChargeAgainstDTO chargeAgainstDTO) {
        try {
            chargeAgainstService.pushNotifyData(chargeAgainstDTO);
            return ResponseMsg.SUCCESS.buildMsg();
        } catch (RomeException e) {
            log.error(e.getMessage(), e);
            return ResponseMsg.FAIL.buildMsg(e.getCode(), e.getMessage());
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return ResponseMsg.FAIL.buildMsg("-500", e.getMessage());
        }
    }



    @ApiOperation(value = "根据后置单查询后置单包含ES单据", nickname = "pushNotifyData", produces = MediaType.APPLICATION_JSON_VALUE)
    @ApiResponse(code = 200, message = "success", response = String.class)
    @GetMapping("/queryWarehouseRecordAndDetailByCodeWithES")
    public Response<WarehouseRecordDTO> queryWarehouseRecordAndDetailByCodeWithES(@RequestParam("recordCode") String recordCode,@RequestParam("recordType") Integer recordType) {
        try {
            WarehouseRecordE warehouseRecordE=warehouseRecordRepository.queryWarehouseRecordByRecordCodeAndTypeWithES(recordCode,recordType);
            return ResponseMsg.SUCCESS.buildMsg(warehouseRecordConvertor.entityToDto(warehouseRecordE));
        } catch (RomeException e) {
            log.error(e.getMessage(), e);
            return ResponseMsg.FAIL.buildMsg(e.getCode(), e.getMessage());
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return ResponseMsg.FAIL.buildMsg("-500", e.getMessage());
        }
    }


    /**
     * 根据业务单号或入库单号
     * 查询收货明细或者出库明细
     * @return
     */
    @ApiOperation(value = "查询收货明细或者出库明细", nickname = "queryReverseExportList", produces = MediaType.APPLICATION_JSON_VALUE)
    @ApiResponse(code = 200, message = "success", response = String.class)
    @PostMapping("/queryReverseExportList")
    public Response<List<ReverseDownLoadDTO>> queryReverseExportList(@RequestBody ReverseExportQueryDTO reverseExportQueryDTO) {
        try {
            List<ReverseDownLoadDTO> list = chargeAgainstService.queryReverseExportList(reverseExportQueryDTO);
            return ResponseMsg.SUCCESS.buildMsg(list);
        } catch (RomeException e) {
            log.error(e.getMessage(), e);
            return ResponseMsg.FAIL.buildMsg(e.getCode(), e.getMessage());
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return ResponseMsg.FAIL.buildMsg("-500", e.getMessage());
        }
    }

    /**
     * 根据业务单号或入库单号
     * 查询收货明细或者出库明细
     * @return
     */
    @ApiOperation(value = "查询收货明细或者出库明细", nickname = "importExcel", produces = MediaType.APPLICATION_JSON_VALUE)
    @ApiResponse(code = 200, message = "success", response = String.class)
    @PostMapping("/importExcel")
    public Response importExcel(@RequestBody List<ReverseDownLoadDTO> list) {
        try {
            chargeAgainstService.importExcel(list);
            return ResponseMsg.SUCCESS.buildMsg();
        } catch (RomeException e) {
            log.error(e.getMessage(), e);
            return ResponseMsg.FAIL.buildMsg(e.getCode(), e.getMessage());
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return ResponseMsg.FAIL.buildMsg("-500", e.getMessage());
        }
    }

    @ApiOperation(value = "创建并确认冲销单", nickname = "createAndConfirmChargeAgainstRecord", produces = MediaType.APPLICATION_JSON_VALUE)
    @ApiResponse(code = 200, message = "success", response = String.class)
    @PostMapping("/createAndConfirmChargeAgainstRecord")
    public Response<String> createAndConfirmChargeAgainstRecord(@RequestBody ChargeAgainstCreateDTO chargeAgainstCreateDTO) {
        String uniqueKey = "createAndConfirmChargeAgainstRecord";
        boolean isLock = false;
        String outRecodeCode = "";
        try {
            isLock = redisUtil.lock(uniqueKey, "1", 120);
            if (isLock) {
                outRecodeCode = chargeAgainstService.createAndConfirmChargeAgainstRecord(chargeAgainstCreateDTO);
            } else {
                throw new RomeException(ResCode.STOCK_ERROR_1017, "创建并确认冲销单正在处理中，请稍后再试。。。");
            }
            return ResponseMsg.SUCCESS.buildMsg(outRecodeCode);
        } catch (RomeException e) {
            log.error(e.getMessage(), e);
            return ResponseMsg.FAIL.buildMsg(e.getCode(), e.getMessage());
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return ResponseMsg.FAIL.buildMsg("-500", e.getMessage());
        }finally {
            if(isLock) {
                redisUtil.unLock(uniqueKey, "1");
            }
        }
    }

}
