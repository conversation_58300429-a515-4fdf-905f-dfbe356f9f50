package com.rome.stock.innerservice.api.dto.cmp7;

import java.math.BigDecimal;
import java.util.Date;

import com.alibaba.fastjson.annotation.JSONField;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @description 门店补货收货结果对象
 * @date 2021/3/2 9:43
 * @throw
 */
@Data
public class ShopReceiptResultDTO {
    @ApiModelProperty(value = "单据号")
    @JSONField(name = "BillNo")
    private String BillNo;

    @ApiModelProperty(value = "门店编码")
    @JSONField(name = "OrgCode")
    private String OrgCode;

    @ApiModelProperty(value = "部门ID")
    @JSONField(name = "DepID")
    private Long DepID;

    @ApiModelProperty(value = "部门编码")
    @JSONField(name = "DepCode")
    private String DepCode;

    @ApiModelProperty(value = "部门名称")
    @JSONField(name = "DepName")
    private String DepName;

    @ApiModelProperty(value = "序号")
    @JSONField(name = "SerialNo")
    private Long SerialNo;

    @ApiModelProperty(value = "商品ID")
    @JSONField(name = "PluID")
    private Long PluID;

    @ApiModelProperty(value = "商品编码")
    @JSONField(name = "PluCode")
    private String PluCode;

    @ApiModelProperty(value = "商品名称")
    @JSONField(name = "PluName")
    private String PluName;

    @ApiModelProperty(value = "单位")
    @JSONField(name = "Unit")
    private String Unit;

    @ApiModelProperty(value = "商品条码")
    @JSONField(name = "BarCode")
    private String BarCode;

    @ApiModelProperty(value = "规格")
    @JSONField(name = "Spec")
    private String Spec;

    @ApiModelProperty(value = "特征码编码")
    @JSONField(name = "ExPluCode")
    private String ExPluCode;

    @ApiModelProperty(value = "特征码名称")
    @JSONField(name = "ExPluName")
    private String ExPluName;

    @ApiModelProperty(value = "货号")
    @JSONField(name = "CargoNo")
    private String CargoNo;

    @ApiModelProperty(value = "业务类型")
    @JSONField(name = "YwType")
    private String YwType;

    @ApiModelProperty(value = "计划数量")
    @JSONField(name = "JhCount")
    private BigDecimal JhCount;

    @ApiModelProperty(value = "实际数量")
    @JSONField(name = "RealCount")
    private BigDecimal RealCount;

    @ApiModelProperty(value = "录入人ID")
    @JSONField(name = "userId")
    private Long UserID;

    @ApiModelProperty(value = "录入人编码")
    @JSONField(name = "UserCode")
    private String UserCode;


    @ApiModelProperty(value = "录入日期")
    @JSONField(name = "LrDate")
    private String LrDate;


    @ApiModelProperty(value = "记账日期")
    @JSONField(name = "JzDate")
    private Date JzDate;

    @ApiModelProperty(value = "供应商编码")
    @JSONField(name = "SupCode")
    private String SupCode;

    @ApiModelProperty(value = "到货日期")
    @JSONField(name = "DhDate")
    private String DhDate;

    @ApiModelProperty(value = "生产日期")
    @JSONField(name = "ScDate")
    private Date ScDate;

    @ApiModelProperty(value = "供应商编码")
    @JSONField(name = "CkCode")
    private String CkCode;

    @ApiModelProperty(value = "采购单号")
    @JSONField(name = "CgBillNo")
    private String CgBillNo;

    @ApiModelProperty(value = "备注")
    @JSONField(name = "Remark")
    private String Remark;

    @ApiModelProperty(value = "数据状态")
    @JSONField(name = "DataState")
    private String DataState;

    @ApiModelProperty(value = "导入标志")
    @JSONField(name = "Tag")
    private Integer Tag;

    @ApiModelProperty(value = "预留字段1")
    @JSONField(name = "UDP1")
    private String UDP1;

    @ApiModelProperty(value = "预留字段2")
    @JSONField(name = "UDP2")
    private String UDP2;


    @ApiModelProperty(value = "预留字段3")
    @JSONField(name = "UDP3")
    private String UDP3;

    @ApiModelProperty(value = "预留字段4")
    @JSONField(name = "UDP4")
    private String UDP4;

    @ApiModelProperty(value = "预留字段5")
    @JSONField(name = "UDP5")
    private String UDP5;

    @ApiModelProperty(value = "批次号")
    @JSONField(name = "BatchNo")
    private String BatchNo;
}    
