package com.rome.stock.innerservice.api.dto.warehouserecord;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 类OutConfirmMQDTO的实现描述
 *
 */
@Data
@EqualsAndHashCode
public class OutConfirmMQDTO {

    @ApiModelProperty(value = "单据编码")
    private String recordCode;

    @ApiModelProperty(value = "操作员编码（工号）")
    private String operatorCode;

    @ApiModelProperty(value = "操作员名称")
    private String operatorName;

    @ApiModelProperty(value = "操作人信息持久化",hidden = true)
    private boolean OperatorPersistence=true;
}
