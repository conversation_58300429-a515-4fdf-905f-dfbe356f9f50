package com.rome.stock.innerservice.api.dto.frontrecord;

import com.rome.stock.innerservice.api.dto.BatchStockDTO;
import com.rome.stock.innerservice.api.dto.Pagination;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

/**
 * 退货预入库明细
 */
@Data
public class PredictReturnDetailDTO extends Pagination {

    /**
     * 唯一主键
     */
    private Long id;

    /**
     * 所属单据编码
     */
    @ApiModelProperty(value = "所属单据编码")
    private String recordCode;

    /**
     * 单据id
     */
    @ApiModelProperty(value = "前置单据id")
    private Long frontRecordId;

    /**
     * 商品sku编码id
     */
    @ApiModelProperty(value = "商品skuId")
    private Long skuId;

    /**
     * 商品sku编码code
     */
    @ApiModelProperty(value = "商品sku编码")
    private String skuCode;

    /**
     * 商品sku编码code
     */
    @ApiModelProperty(value = "商品名称")
    private String skuName;

    /**
     * 实收商品数量
     */
    @ApiModelProperty(value = "实收商品数量")
    private BigDecimal skuQty;

    /**
     * 单位
     */
    @ApiModelProperty(value = "单位")
    private String unit;

    /**
     * 单位编码
     */
    @ApiModelProperty(value = "单位编码")
    private String unitCode;

    /**
     * 备注
     */
    @ApiModelProperty(value = "备注")
    private String remark;

    /**
     * 商品批次
     */
    private List<BatchStockDTO> batchStocks;

    @ApiModelProperty(value = "已匹配数")
    private BigDecimal matchQty;
}
