package com.rome.stock.innerservice.api.dto.frontrecord;

import java.util.List;

import javax.validation.constraints.NotBlank;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.rome.stock.innerservice.remote.item.dto.SkuCombineSimpleDTO;
import com.rome.stock.innerservice.remote.item.dto.UnitAndBaseUnitInfoDTO;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 类SkuInfo的实现描述：sku信息
 *
 * <AUTHOR> 2019/4/16 9:51
 */
@Data
@EqualsAndHashCode
public class SkuInfo {

    /**
     * 渠道id
     */
    @ApiModelProperty(value = "渠道id", name = "channelCode", required=true)
    @NotBlank(message="渠道编码不能为空")
    private String channelCode;

    @ApiModelProperty(value = "merchantId,不传将会用默认的商家id", name = "merchantId" )
    private Long merchantId;

    /**
     * 商品skuId
     */
    @ApiModelProperty(value = "商品skuId", name = "skuId", required=true)
    private Long skuId;

    /**
     * 商品sku编码
     */
    @ApiModelProperty(value = "商品sku编码", name = "skuCode", required=true)
    private String skuCode;

    /**
     * 单位
     */
    @ApiModelProperty(value = "单位code", name = "unit", required = true)
    @NotBlank(message="单位编码不能为空")
    private String unitCode;


    /**
     *   sku类型 0单sku，1组合sku，2组装sku
     */
    @JsonIgnore
    private Integer combineType;

    /**
     * 组合品信息
     */
    @JsonIgnore
    private List<SkuCombineSimpleDTO> combineInfo;

    /**
     * 根据skuId-merchantId-unitCode组装信息
     */
    @JsonIgnore
    private String  skuIdInfo;

    /**
     * 根据skuCode-merchantId-unitCode组装信息
     */
    @JsonIgnore
    private String  skuCodeInfo;


    public  String  getSkuIdInfo(){
        StringBuilder sb = new StringBuilder();
        sb.append(this.getSkuId()).append("-").append(this.getMerchantId());
        return sb.toString();

    }

    public  String  getSkuCodeInfo(){
        StringBuilder sb = new StringBuilder();
        sb.append(this.getSkuCode()).append("-").append(this.getMerchantId());
        return sb.toString();

    }

    @JsonIgnore
    public  String  getSingleInfo(){
        StringBuilder sb = new StringBuilder();
        sb.append(this.getSkuId()).append("-").append(this.getMerchantId())
        .append(this.getChannelCode()).append("-").append(this.getUnitCode());
        return sb.toString();

    }

}
