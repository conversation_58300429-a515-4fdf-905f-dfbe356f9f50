package com.rome.stock.innerservice.api.dto;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

import com.rome.stock.innerservice.api.dto.frontrecord.AddressDTO;
import com.rome.stock.innerservice.api.dto.frontrecord.FrontRecordDTO;
import com.rome.stock.innerservice.api.dto.frontrecord.PurchaseOrderDetailDTO;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 类ShopAssembleSpitPage的实现描述：门店盘点
 *
 * <AUTHOR> 2019/4/23 20:29
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class WarehouseRecordPageDTO extends Pagination {

    /**
     * 唯一主键
     */
    private Long id;
    /**
     * 单据编号
     */
    private String recordCode;


    private List<String> recordCodeList;

    /**
     * sap单号
     */
    private String sapOrderCode;

    /**
     * sap单号
     */
    private List<String> sapOrderCodeList;

    /**
     * 前置单sap单号
     */
    private String frontSapOrderCode;

    /**
     * 单据类型
     */
    private Integer recordType;

    /**
     * 仓库类型
     */
    private Integer warehouseType;

    /**
     * 出入库类型
     */
    private Integer businessType;

    /**
     * 单据状态：已入库
     */
    private Integer recordStatus;

    /**
     * 开始日期
     */
    private Date startDate;

    /**
     * 结束日期
     */
    private Date endDate;

    /**
     * 创建时间
     */
    private Date createTime;


    /**
     * 创建时间
     */
    private Date updateTime;

    /**
     * 创建人
     */
    private Long creator;

    private Long realWarehouseId;

    private String realWarehouseCode;

    /**
     * 出入库单同步WMS状态：0-无需同步 1-未同步 2-已同步
     */
    private Integer syncWmsStatus;

    /**
     * 实仓仓库名称
     */
    private String realWarehouseName;

    /**
     * 工厂代码
     */
    private String factoryCode;

    /**
     * 前置单编码
     */
    private String frontRecordCode;

    private List<String> frontRecordCodeList;

    /**
     * 仓库外部编号-wms
     */
    private String realWarehouseOutCode;

    /**
     * 采购单号
     */
    private String purchaseOrderNo;

    /**
     * 出库时间
     */
    private Date deliveryTime;

    /**
     * 入库时间
     */
    private Date receiverTime;

    /**
     * 前置单
     */
    private FrontRecordDTO frontRecord;

    private List<WarehouseRecordDetailDTO> warehouseRecordDetails;

    /**
     * 采购前置单明细
     */
    private List<PurchaseOrderDetailDTO> purchaseOrderDetailList;

    /**
     * wms系统code
     */
    private Integer wmsCode;

    /**
     * 地址信息
     */
    private List<AddressDTO> addressList;

    /**
     * 期望收货日期_开始
     */
    private Date expectReceiveDateStart;

    /**
     * 期望收货日期_截止
     */
    private Date expectReceiveDateEnd;

    /**
     * 店铺编码
     */
    private String shopCode;
    /**
     * 店铺名称
     */
    private String shopName;
    /**
     * tms派车单号
     */
    private String tmsRecordCode;

    /**
     * 供应商编码
     */
    private String supplierCode;

    /**
     * 供应商名称
     */
    private String supplierName;

    private Integer syncTransferStatus = 0;

    /**
     * 入向工厂
     */
    private String inFactory;

    /**
     * 入向仓库编码
     */
    private String inWarehouse;

    /**
     * 仓库编码List
     */
    private List<Long> warehouseIdList;

    /**
     * 是否退货调拨  0.不是 1.是
     */
    private Integer isReturnAllotcate;

    /**
     * 单据拆分标识
     */
    private String splitFlag;

    /**
     * 出向仓库信息
     */
    private RealWarehouse outWarehouseInfo;

    /**
     * 入向仓库信息
     */
    private RealWarehouse inWarehouseInfo;

    /**
     * 前置单创建时间
     */
    private Date FrontCreateTime;

    /**
     * 是否是自营外卖
     */
    private Integer selfTakeout;

    private String outRecordCode;

    /**
     * 单据类型List
     */
    private List<Integer> recordTypeList;



    private String channelCode;


    /**
     * 鲲鹏渠道
     */
    private String kpChannelCode;

    /**
     * 渠道名称
     */
    private String channelName;

    /**
     * 物流公司编码
     */
    private String logisticsCode;

    /**
     * 下单时间
     */
    private Date outCreateTime;

    /**
     * 支付时间
     */
    private Date payTime;

    /**
     * 前台订单号
     */
    private String soCode;

    /**
     * 订单实付金额
     */
    private BigDecimal orderAmount;

    /**
     * 订单金额
     */
    private BigDecimal orginAmount;

    /**
     * 领用日期(YYYY-MM-DD）
     */
    private Date receiveDate;

    /**
     * 运输方式 1自提 2快递 3内部通道
     */
    private Integer transWay;

    /**
     * 申请人工号
     */
    private String applier;

    /**
     * 申请人联系电话
     */
    private String applierMobile;

    //成本中心编号
    private String costCenterCode;

    //成本中心名称
    private String costCenterName;

    @ApiModelProperty("客户")
    private String customerName;

    @ApiModelProperty("客户电话")
    private String customerTel;

    @ApiModelProperty("地址")
    private String address;

    @ApiModelProperty("创建人姓名")
    private String creatorName;


    @ApiModelProperty("退货原因")
    private String reason;
    @ApiModelProperty("退货原因code")
    private String reasonCode;
    @ApiModelProperty("下单方")
    private String orderCustCode;
    /**
     * 出库或入库完成时间
     */
    private Date outOrInTime;

    /**
     * job分片总数
     */
    private  Integer shardingTotalCount;
    /**
     * 当天任务实例的分配id
     */
    private  Integer shardingItem;

    /**
     * 业务应用ID
     */
    private String appId;

    /**
     * 备注
     */
    private String remark;

    /**
     * 卖家备注
     */
    private String sellerMessage;

    private String oaid;

    private Integer LabType;

    /**
     * 销售订单商品类型 1-普通,2-预售,3-拼团,4-拼券,5-旺店通,6-POS门店,7-外卖自营,8-外卖第三方营,9-电商超市,10-2B分销,11-加盟商 12 虚拟商品
     */
    private Integer transType;
    
    /**
     * 销售系统单号
     */
    private String saleSystemCode;
    
    /**
     * 客户编码
     */
    private String customCode;
    
    /**
     * 客户名称
     */
    private String customName;
    
    /**
     * 经销的92单号
     */
    private String saleSapOrderCode;
    
    /**
     * 退货流程用：退货原因
     */
    private String reasons;
    //仓库业务类型: 1: 直营门店 2: 加盟门店 3: 仓库 4. 其他仓库"
    private Integer rwBusinessType;

    /**
     * 出向工厂
     */
    private String outFactory;

    /**
     * 出向货仓仓库外部编号-wms
     */
    private String outWarehouse;

    @ApiModelProperty(value = "cmpFacade内是否保存日志，防止重复保存",hidden = true)
    private boolean saveLog;

    @ApiModelProperty(value = "是否跳过批次下发",hidden = true)
    private Boolean batchSkip=false;

    @ApiModelProperty("商户信息，代发订单所属店铺appid-微信视频号-旺店通发货相关")
    private String buyerNick;

    @ApiModelProperty("代发订单密文-微信视频号-旺店通发货相关")
    private String ewayBillOrderCode;

    /**
     * 原箱批次要求( 0:无要求 1:要求原箱)
     */
    private Integer boxBatchTag;

    /**
     * 单据状态集合
     */
    private List<Integer> recordStatusList;

    // 仓店一体标识 0 -非仓店一体   1仓店一体
    private Integer warehouseStoreIdenti;
    /**
     * 不查询对应的仓库ID
     */
    private List<Long> notRealWarehouseIdList;
    /**
     * 订单来源平台编码
     */
    private String sourcePlatformCode;

    @ApiModelProperty("拼团标识")
    private Boolean specialType=false;

    /**
     * 波次号
     */
    private String waveCode;

    /**
     * 车牌号
     */
    private String carNo;

    @ApiModelProperty(value = "发票信息")
    private List<Invoice> invoices;

    /**
     * 派车单号
     */
    private String expressCode;
}
