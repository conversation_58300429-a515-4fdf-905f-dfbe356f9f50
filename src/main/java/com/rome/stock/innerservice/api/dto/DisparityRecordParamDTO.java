package com.rome.stock.innerservice.api.dto;

import lombok.Data;

import java.util.Date;
import java.util.List;

@Data
public class DisparityRecordParamDTO extends Pagination {

    private Integer recordType;
    private String inRealWarehouseName;
    private String outRealWarehouseName;
    private String inRealWarehouseCode;
    private String outRealWarehouseCode;
    private String frontRecordCode;
    private Integer responsibleType;
    private List<Long> inRealWarehouseIds;
    private List<Long> outRealWarehouseIds;

    private Long outWarehouseId;
    private Long inWarehouseId;

    /**
     * sap采购单号
     */
    private String sapPoNo;

    /**
     * sap交货单号
     */
    private String sapDeliveryCode;

    private List<String> sapDeliveryCodeList;


    private String empNum;

    private Long modifier;

    private Integer recordStatus;

    private String factoryCode;

    private Date createStartDate;

    private Date createEndDate;

    private Date updateStartDate;

    private Date updateEndDate;

    private String skuCode;

}
