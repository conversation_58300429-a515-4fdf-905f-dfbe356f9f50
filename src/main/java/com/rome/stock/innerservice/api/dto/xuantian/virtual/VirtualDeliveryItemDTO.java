package com.rome.stock.innerservice.api.dto.xuantian.virtual;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * 外卖销售发货物料单据表
 * 
 * <AUTHOR>
 * @email 
 * @date 2019-06-25 22:58:29
 */
@ApiModel("销售发货物料单据表")
@Data
public class VirtualDeliveryItemDTO implements Serializable {
	
	private static final long serialVersionUID = -6614321639561437785L;
	/**
	 * 单据行号
	 */
	@ApiModelProperty("单据行号")
	private String orderLineNo;
	/**
	 * 商品Id
	 */
	@ApiModelProperty("商品Id")
	private Long skuId;
	/**
	 * 商品编码
	 */
	@ApiModelProperty("商品编码")
	private String skuCode;
	/**
	 * 商品名称
	 */
	@ApiModelProperty("商品名称")
	private String itemName;
	/**
	 * 应发商品数量
	 */
	@ApiModelProperty("应发商品数量")
	private Integer planQty;

	
	@ApiModelProperty("单位Id")
	private Long unitId;
	/**
	 * 单位
	 */
	@ApiModelProperty("单位Code")
	private String unitCode;

}
