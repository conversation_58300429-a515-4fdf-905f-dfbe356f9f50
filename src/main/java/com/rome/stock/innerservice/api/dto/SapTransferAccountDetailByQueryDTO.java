/**
 * Filename SapTransferAccountLogDTO.java
 * Company 上海来伊份科技有限公司。
 * <AUTHOR>
 * @version 
 */
package com.rome.stock.innerservice.api.dto;

import java.math.BigDecimal;
import java.util.Date;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 明细
 * <AUTHOR>
 * @since 2020-7-25 10:47:10
 */
@Data
public class SapTransferAccountDetailByQueryDTO {

	@ApiModelProperty(value="主键")
	private Long id;
	
	@ApiModelProperty(value="所属id")
	private Long logId;
	
	@ApiModelProperty(value="过账状态，0:初始 1:成功 2:失败 3:异常")
	private Integer transferStatus;
	
	@ApiModelProperty(value="过账状态，名称描述")
	private String transferStatusName;
	
	@ApiModelProperty(value="行号")
	private String lineNo;
	
	@ApiModelProperty(value="商品Id")
	private Long skuId;
	
	@ApiModelProperty(value="单据编码")
	private String skuCode;

	@ApiModelProperty(value="商品名称")
	private String skuName;

	@ApiModelProperty(value="基础单位名称")
	private String skuUnitName;
	
	@ApiModelProperty(value="基础单位编码")
	private String skuUnitCode;
	
	@ApiModelProperty(value="出入库单号")
	private String recordCode;
	
	@ApiModelProperty(value="sap采购单号")
	private String sapPurchaseRecordCode;
	
	@ApiModelProperty(value="物料凭证")
	private String materialCertificate;
	
	@ApiModelProperty(value="过账收货单号")
	private String receiptRecordCode;
	
	@ApiModelProperty(value="收货单号")
	private String wmsRecordCode;
	
	@ApiModelProperty(value="业务单号")
	private String sapOrderCode;
	
	@ApiModelProperty(value="sap返回消息文本")
	private String message;
	
	@ApiModelProperty(value="单据类型")
    private Integer recordType;
	
    @ApiModelProperty(value = "单据类型名称")
    private String recordTypeName;
	
	@ApiModelProperty(value="数量")
    private BigDecimal qty;
    
	@ApiModelProperty(value="sap返回数量")
    private BigDecimal sapQty;
    
	@ApiModelProperty(value="过帐单位编码")
    private String unitCode;
    
	@ApiModelProperty(value="sap返回单位编码")
    private String sapUnitCode;
	
	@ApiModelProperty(value="传入凭证日期")
    private Date certificateTime;
    
	@ApiModelProperty(value="虚仓收货凭证")
	private String materialCertificateVirtualIn;
	
	@ApiModelProperty(value="虚仓发门店凭证")
	private String materialCertificateVirtualShop;
	
	@ApiModelProperty(value="实仓Id")
	private Long realWarehouseId;
	
	@ApiModelProperty(value="仓库编号")
	private String realWarehouseCode;
	
}
