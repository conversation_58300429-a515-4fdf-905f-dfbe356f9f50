package com.rome.stock.innerservice.api.controller;

import com.rome.arch.core.clientobject.Response;
import com.rome.arch.core.exception.RomeException;
import com.rome.arch.util.controller.RomeController;
import com.rome.stock.innerservice.api.dto.warehouserecord.InWarehouseRecordDTO;
import com.rome.stock.innerservice.api.dto.warehouserecord.OutWarehouseRecordDTO;
import com.rome.stock.innerservice.common.ResCode;
import com.rome.stock.innerservice.constant.ResponseMsg;
import com.rome.stock.innerservice.domain.service.ShopRetailService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiResponse;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;

/**
 * 门店领用
 */
@Slf4j
@RomeController
@RequestMapping("/stock/v1/shopReceive")
@Api(tags={"门店领用"})
public class ShopReceiveController {
    @Autowired
    private ShopRetailService shopRetailService;

    /**
     * 门店领用出库
     * @return
     */
    @ApiOperation(value = "门店领用出库", nickname = "createShopReceiveOutRecord", produces = MediaType.APPLICATION_JSON_VALUE)
    @ApiResponse(code = 200, message = "success", response = String.class)
    @RequestMapping(value = "/createShopReceiveOutRecord", method = RequestMethod.POST)
    public Response createShopReceiveOutRecord(@RequestBody @Validated OutWarehouseRecordDTO outWarehouseRecordDTO) {
        try {
            shopRetailService.createShopReceiveOutRecord(outWarehouseRecordDTO);
            return ResponseMsg.SUCCESS.buildMsg();
        } catch (RomeException e) {
            log.error(e.getMessage(), e);
            return ResponseMsg.FAIL.buildMsg(e.getCode(),e.getMessage());
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return ResponseMsg.EXCEPTION.buildMsg(ResCode.STOCK_ERROR_1003_DESC,e.getMessage());
        }
    }

    /**
     * 门店领用入库
     * @return
     */
    @ApiOperation(value = "门店领用入库", nickname = "createShopReceiveInRecord", produces = MediaType.APPLICATION_JSON_VALUE)
    @ApiResponse(code = 200, message = "success", response = String.class)
    @RequestMapping(value = "/createShopReceiveInRecord", method = RequestMethod.POST)
    public Response createShopReceiveInRecord(@RequestBody @Validated InWarehouseRecordDTO warehouseRecord) {
        try {
            shopRetailService.createShopReceiveInRecord(warehouseRecord);
            return ResponseMsg.SUCCESS.buildMsg();
        } catch (RomeException e) {
            log.error(e.getMessage(), e);
            return ResponseMsg.FAIL.buildMsg(e.getCode(),e.getMessage());
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return ResponseMsg.EXCEPTION.buildMsg(ResCode.STOCK_ERROR_1003_DESC,e.getMessage());
        }
    }

}
