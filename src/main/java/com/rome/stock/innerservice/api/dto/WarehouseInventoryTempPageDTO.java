package com.rome.stock.innerservice.api.dto;


import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;
import java.util.Date;

@ApiModel(value = "WarehouseInventoryStartPageDTO", description = "盘点单管理")
@Data
@EqualsAndHashCode
public class WarehouseInventoryTempPageDTO extends Pagination {
    @ApiModelProperty(value = "")
    private static final long serialVersionUID = 6241269605258899670L;


    @ApiModelProperty(value = "盘点记录号")
    private Long inventoryStartId;

    @ApiModelProperty(value = "仓库编码")
    private String realWarehouseCode;

    @ApiModelProperty(value = "仓库名称")
    private String realWarehouseName;

    /**
     * 商品sku编码
     */
    @ApiModelProperty(value = "商品skuId")
    private Long skuId;

    /**
     * 盘点前账面库存
     */
    @ApiModelProperty(value = "盘点前账面库存")
    private BigDecimal skuQty;


    /**
     * 盘点账面库存
     */
    @ApiModelProperty(value = "盘点账面库存")
    private BigDecimal accQty;

    /**
     * 账面库存
     */
    @ApiModelProperty(value = "差异数量")
    private BigDecimal diffQty;

    /** sku编号 */
    @ApiModelProperty(value = "sku编号")
    private String skuCode;

    /** sku名称 */
    @ApiModelProperty(value = "sku名称")
    private String skuName;

    /**
     * 基本数量
     */
    @ApiModelProperty(value = "基本单位")
    private String baseUnit;
    /**
     * 业务类型：1-抽盘，2-全盘
     */
    @ApiModelProperty(value = "1-抽盘，2-全盘")
    private Integer businessType;
    /**
     * 单位
     */
    @ApiModelProperty(value = "单位")
    private String unit;
    /**
     * 单位编码
     */
    @ApiModelProperty(value = "unitCode")
    private String unitCode;

    /**
     * 盘点情况（1，盘盈，2，盘亏，3，平盘）
     */
    @ApiModelProperty(value = "盘点情况（1，盘盈，2，盘亏，3，平盘）")
    private Integer adjustType;
    /**
     * 库存状态（1，合格 2，质检不合格 3，待质检）
     */
    @ApiModelProperty(value = "库存状态（1，合格 2，质检不合格 3，待质检）")
    private Integer inventoryStatus;


    private Integer stockType;
    /**
     * 仓库盘点单号
     */
    @ApiModelProperty(value = "仓库盘点单号")
    private String outRecordCode;
    /**
     * 仓库盘点时间
     */
    @ApiModelProperty(value = "仓库盘点时间")
    private Date outCreateTime;

    @ApiModelProperty(value = "创建人")
    private Long creator;

    @ApiModelProperty(value = "创建时间")
    private Date createTime;
}
