package com.rome.stock.innerservice.api.dto.alarm;


import com.rome.stock.innerservice.api.dto.Pagination;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * DO 实体类
 * 
 * <AUTHOR>
 * @date 2024-04-11
 */
@Data
@NoArgsConstructor
@EqualsAndHashCode
public class BatchSaleAlarmDTO extends Pagination {

	/**
     * 唯一主键
     */
	private Long id;

	/**
     * 执行记录id
     */
	private Long headId;

	/**
     * 库存日期
     */
	private Date stockDate;

	/**
     * 实仓id
     */
	private Long realWarehouseId;

	/**
     * 商品skuId
     */
	private Long skuId;

	/**
     * sku编码
     */
	private String skuCode;

	/**
     * 商品名称
     */
	private String skuName;

	/**
     * 商品基础单位编码
     */
	private String unitCode;

	/**
     * 商品基础单位名称
     */
	private String unitName;

	/**
     * 预警滞销数量
     */
	private BigDecimal alarmSaleQty;

	/**
     * 总批次库存数量
     */
	private BigDecimal totalQty;

	/**
     * 超配置批次数量
     */
	private BigDecimal overConfigQty;

	/**
     * 配置的效期分数
     */
	private String configPercent;

	/**
	 * 零售价
	 */
	private BigDecimal salePrice;

	/**
     * 预警状态：0-无需发送 1-待发送 2-发送成功
     */
	private Integer alarmStatus;

	@ApiModelProperty(value = "创建时间")
	private Date createTime;

	@ApiModelProperty(value = "修改时间")
	private Date updateTime;

	@ApiModelProperty(value = "创建人")
	private Long creator;

	@ApiModelProperty(value = "更新人")
	private Long modifier;

	/**
	 * 预警开始日期
	 */
	private String startDate;

	/**
	 * 预警结束日期
	 */
	private String endDate;

	/**
	 * 预警类型
	 */
	private Integer alarmType;

	/**
	 * 仓库集合
	 */
	private List<String> realWarehouseCodeList;

	/**
	 * 物料集合
	 */
	private List<String> skuCodeList;

	/**
	 * 仓库名称
	 */
	private String realWarehouseName;

	/**
	 * 仓库编号
	 */
	private String realWarehouseCode;

	/**
	 * 到达配置效期需要的天数
	 */
	private Integer alarmDayCount;

	/**
	 * 滞销金额
	 */
	private BigDecimal alarmPrice;

	/**
	 * 滞销数量比较
	 */
	private BigDecimal moreThanZero;

	/**
	 * 销售状态
	 */
	private String saleStatus;

}
