package com.rome.stock.innerservice.api.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;

@Data
@EqualsAndHashCode
public class RwStockSummary {

    @ApiModelProperty(value = "实际总库存数量")
    private BigDecimal sumRealQty=BigDecimal.ZERO;

    @ApiModelProperty(value = "库存总金额")
    private BigDecimal sumAmount =BigDecimal.ZERO;

    @ApiModelProperty(value = "库存总金额(含税)")
    private BigDecimal sumAmountHasTax =BigDecimal.ZERO;
}
