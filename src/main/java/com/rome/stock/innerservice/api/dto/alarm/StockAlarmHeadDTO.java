package com.rome.stock.innerservice.api.dto.alarm;


import com.rome.stock.innerservice.infrastructure.dataobject.BaseDo;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import java.util.Date;

/**
 * DO 实体类
 * 
 * <AUTHOR>
 * @date 2024-04-11
 */
@Data
@NoArgsConstructor
@EqualsAndHashCode
public class StockAlarmHeadDTO extends BaseDo {

	/**
     * 唯一主键
     */
	private Long id;

	/**
     * 库存日期
     */
	private Date stockDate;

	/**
     * 预警类型：1-仓库滞销预警
     */
	private Integer alarmType;

	/**
     * 预警类型：0-执行中 1-执行成功 2-执行失败
     */
	private Integer status;

	/**
     * 实仓id
     */
	private Long realWarehouseId;

	/**
     * 配置josn
     */
	private String configJson;

	/**
     * 信息记录
     */
	private String msg;



}
