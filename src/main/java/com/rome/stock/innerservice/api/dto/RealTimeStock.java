package com.rome.stock.innerservice.api.dto;

import com.rome.arch.core.dto.DTO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @description 实时库存查询
 * @date 2020/9/10 10:46
 * @throw
 */
@Data
public class RealTimeStock extends DTO {

    @ApiModelProperty(value = "省份code")
    private String realWarehouseProvinceCode;

    @ApiModelProperty(value = "城市code")
    private String realWarehouseCityCode;

    @ApiModelProperty(value = "县城市code")
    private String realWarehouseCountyCode;

    @ApiModelProperty("可用库存-实仓")
    private BigDecimal availableQty;

    @ApiModelProperty("在途-实仓")
    private BigDecimal onroadQty;

    @ApiModelProperty("锁定库存-实仓")
    private BigDecimal lockQty;

    @ApiModelProperty("商品编码")
    private String skuCode;

    @ApiModelProperty("真实库存-实仓")
    private BigDecimal realQty;

    @ApiModelProperty(value = "仓库层级")
    private Integer realWarehouseRank;

    @ApiModelProperty(value = "仓库外部编号-wms")
    private String realWarehouseOutCode;

    @ApiModelProperty(value = "工厂编号")
    private String factoryCode;

}    
   