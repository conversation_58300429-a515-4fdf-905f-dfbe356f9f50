package com.rome.stock.innerservice.api.controller;

import com.rome.arch.core.clientobject.Response;
import com.rome.arch.core.exception.RomeException;
import com.rome.arch.util.controller.RomeController;
import com.rome.stock.innerservice.api.dto.CmpInterfaceLogDTO;
import com.rome.stock.innerservice.constant.ResponseMsg;
import com.rome.stock.innerservice.domain.service.CmpInterfaceService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import io.swagger.annotations.ApiResponse;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;


/**
 * @Description cmp
 * <AUTHOR>
 * @Date 2019/5/13 15:45
 * @Version 1.0
 */
@Slf4j
@RomeController
@RequestMapping("/stock/v1/cmp")
@Api(tags={"CMP管理"})
public class CmpInterfaceController {
    @Autowired
    private CmpInterfaceService cmpInterfaceService;

    @ApiOperation(value = "记录cmp日志", nickname = "saveCmpLog", produces = MediaType.APPLICATION_JSON_VALUE)
    @ApiResponse(code = 200, message = "success")
    @RequestMapping(value = "/saveCmpInterfaceLog", method = RequestMethod.POST)
    public Response saveCmpInterfaceLog(@ApiParam(name = "cmpLog", value = "cmp日志记录") @RequestBody @Validated CmpInterfaceLogDTO cmpInterfaceLog){
        try {
            cmpInterfaceService.saveCmpInterfaceLog(cmpInterfaceLog);
            return ResponseMsg.SUCCESS.buildMsg("");
        } catch (RomeException e) {
            log.error(e.getMessage(), e);
            return ResponseMsg.FAIL.buildMsg(e.getCode(),e.getMessage());
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return ResponseMsg.FAIL.buildMsg("500000","系统异常");
        }
    }
}
