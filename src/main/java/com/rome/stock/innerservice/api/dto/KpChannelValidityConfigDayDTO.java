package com.rome.stock.innerservice.api.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @Description ValidityConfigDayDTO
 * <AUTHOR>
 * @Date 2024/3/21
 **/
@Data
public class KpChannelValidityConfigDayDTO {

    @ApiModelProperty("开始有效期 大于等于")
    private Integer startDay;

    @ApiModelProperty("结束有效期 小于")
    private Integer endDay;

    @ApiModelProperty("不可发货天数")
    private Integer batchRemainExpireDay;

}
