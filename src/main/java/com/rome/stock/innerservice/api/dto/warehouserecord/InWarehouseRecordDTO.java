package com.rome.stock.innerservice.api.dto.warehouserecord;

import java.util.Date;
import java.util.List;

import javax.validation.Valid;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 类InRecordDTO的实现描述：
 *
 * <AUTHOR> 2020/6/11 18:26
 */
@Data
@EqualsAndHashCode
public class InWarehouseRecordDTO {


    @ApiModelProperty(value = "外部系统单据编码", required = true)
    @NotBlank(message="单据编码不能为空")
    private String recordCode;

    @ApiModelProperty(value = "SAP的交货单号")
    private String sapOrderCode;

    @ApiModelProperty(value = "渠道编码, 有就传,存储在后置单")
    private String channelCode;

    @ApiModelProperty(value = "出库仓库code")
    @NotBlank(message="出库仓库code不能为空")
    private String warehouseCode;
    @ApiModelProperty(value = "差异专用 差异单对应的叫货流程的出库单")
    private String outWarehouseRecordCode;

    @ApiModelProperty(value = "原始出库单号，内采领用退货使用")
    private String originWarehouseRecordCode;

    @ApiModelProperty(value = "出库工厂code")
    @NotBlank(message="出库工厂code不能为空")
    private String factoryCode;


    @ApiModelProperty(value = "单据类型", required = true)
    @NotNull(message="单据类型不能为空")
    private Integer recordType;

    @ApiModelProperty(value = "业务类型(门店调拨专用)", required = true)
    private Integer allocationBusinessType;

    @ApiModelProperty(value = "下发状态 0-无需下发 1--待下发")
    private Integer syncWmsStatus;

    @ApiModelProperty(value = "tmsRecordCode")
    private String tmsRecordCode;

    /**
     * 上游系统单号
     */
    @ApiModelProperty(value = "outRecordCode")
    private String outRecordCode;

    /**
     * 送达日期
     */
    @ApiModelProperty(value = "deliveryDate")
    private Date  deliveryDate;

    @Valid
    @ApiModelProperty(value = "sku数量及明细")
    @NotNull(message="sku数量及明细不能为空")
    private List<RecordDetailDTO> detailList;


    @ApiModelProperty(value = "操作时间")
    private String outCreateTime;

    @ApiModelProperty(value = "操作人id")
    private Long operator;


    @ApiModelProperty(value = "是否拆店盘点")
    private Integer isCloseStockPlant;

    @ApiModelProperty(value = "虚仓ID(虚仓标签使用)")
    private Long virtualWarehouseId;

    /**
     * 仓库领用单业务类型： 0:普通单据 1:大宗采购
     */
    private Integer purchaseBusinessType;

}
