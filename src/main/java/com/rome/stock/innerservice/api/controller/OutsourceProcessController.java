package com.rome.stock.innerservice.api.controller;

import javax.annotation.Resource;

import org.springframework.http.MediaType;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;

import com.rome.arch.core.clientobject.Response;
import com.rome.arch.core.exception.RomeException;
import com.rome.arch.util.controller.RomeController;
import com.rome.stock.innerservice.api.dto.frontrecord.OutsourcingOutDTO;
import com.rome.stock.innerservice.api.dto.frontrecord.PurchaseOrderDTO;
import com.rome.stock.innerservice.api.dto.frontrecord.PurchaseSyncCheckResultDTO;
import com.rome.stock.innerservice.constant.ResponseMsg;
import com.rome.stock.innerservice.domain.service.OutsourcingOutService;
import com.rome.stock.innerservice.domain.service.PurchaseOrderService;
import com.rome.stock.innerservice.infrastructure.dataobject.FrontWarehouseRecordRelationDO;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import io.swagger.annotations.ApiResponse;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@RomeController
@RequestMapping("/stock/v1/outsourceProcess")
@Api(tags={"委外加工"})
public class OutsourceProcessController {
	@Resource
	private PurchaseOrderService purchaseOrderService;
	@Resource
	private OutsourcingOutService outsourcingOutService;

	@ApiOperation(value = "委外加工入库单", nickname = "receiveOutsourceProcessRecord", produces = MediaType.APPLICATION_JSON_VALUE)
	@ApiResponse(code = 200, message = "success")
	@RequestMapping(value = "/receiveOutsourceProcessRecord", method = RequestMethod.POST)
	public Response addPurchaseNoticeRecord(@ApiParam(name = "purchaseOrder", value = "dto") @RequestBody @Validated PurchaseOrderDTO purchaseOrder) {
		if (purchaseOrder.getPurchaseRecordType() != 3 ) {
			//purchaseRecordType =3 委外入库 不考虑委外虚拟入库，统一处理
			return ResponseMsg.PARAM_ERROR.buildMsg();
		}
		try {
			purchaseOrderService.addPurchaseNoticeRecord(purchaseOrder);
			return ResponseMsg.SUCCESS.buildMsg();
		} catch (RomeException e) {
			log.error(e.getMessage(), e);
			return ResponseMsg.FAIL.buildMsg(e.getCode(), e.getMessage());
		} catch (Exception e) {
			log.error(e.getMessage(), e);
			return ResponseMsg.EXCEPTION.buildMsg();
		}
	}





	@ApiOperation(value = "委外出库", nickname = "addOutsourcingOutRecord", produces = MediaType.APPLICATION_JSON_VALUE)
	@ApiResponse(code = 200, message = "success")
	@RequestMapping(value = "/addOutsourcingOutRecord", method = RequestMethod.POST)
	public Response addOutsourcingOutRecord(@ApiParam(name = "outsourcingOutDTO", value = "dto") @Validated @RequestBody OutsourcingOutDTO outsourcingOutDTO) {
		try {
			outsourcingOutService.addOutsourcingOutRecord(outsourcingOutDTO);
			return ResponseMsg.SUCCESS.buildMsg();
		} catch (RomeException e) {
			log.error(e.getMessage(), e);
			return ResponseMsg.FAIL.buildMsg(e.getCode(), e.getMessage());
		} catch (Exception e) {
			log.error(e.getMessage(), e);
			return ResponseMsg.EXCEPTION.buildMsg();
		}
	}


	@ApiOperation(value = "委外出库回调", nickname = "wmsOutWarehouseCallback", produces = MediaType.APPLICATION_JSON_VALUE)
	@ApiResponse(code = 200, message = "success")
	@RequestMapping(value = "/wmsOutWarehouseCallback", method = RequestMethod.POST)
	public Response wmsOutWarehouseCallback(@ApiParam(name = "relationDO", value = "dto") @Validated @RequestBody FrontWarehouseRecordRelationDO relationDO) {
		try {
			outsourcingOutService.wmsOutWarehouseCallback(relationDO);
			return ResponseMsg.SUCCESS.buildMsg();
		} catch (RomeException e) {
			log.error(e.getMessage(), e);
			return ResponseMsg.FAIL.buildMsg(e.getCode(), e.getMessage());
		} catch (Exception e) {
			log.error(e.getMessage(), e);
			return ResponseMsg.EXCEPTION.buildMsg();
		}
	}

}
