package com.rome.stock.innerservice.api.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;
import java.util.Date;

/**
 * @Description
 * <AUTHOR>
 * @Date 2019/5/13 15:07
 * @Version 1.0
 */
@Data
@EqualsAndHashCode
public class BatchStockDTO {
	
	@ApiModelProperty(value = "唯一主键")
	private Long id;

    @ApiModelProperty(value = "商品skuID")
    private Long skuId;

    @ApiModelProperty(value = "商品编号")
    private String skuCode;

    @ApiModelProperty(value = "商品数量")
    private BigDecimal skuQty;
    
    @ApiModelProperty(value = "锁定数量")
    private BigDecimal lockQty;
    
    @ApiModelProperty(value = "批次编码-批次号")
    private String batchCode;
    
    @ApiModelProperty(value = "实体仓库id")
    private Long realWarehouseId;
    
    @ApiModelProperty(value = "单据编号")
    private String recordCode;
    
    @ApiModelProperty(value = "单据类型")
    private Integer recordType;
    
    @ApiModelProperty(value = "有效期,天数")
    private Integer validity;
    
    @ApiModelProperty(value = "创建人")
    private Long creator;
    
    @ApiModelProperty(value = "库存类型： 1:增加库存 2:减少库存 3：锁定库存 4：释放库存 5：真实出库库存  6：冻结出库库存", required = true)
    private Integer stockType;
    
    @ApiModelProperty(value = "要求批次有效期，分子")
    private Integer molecule;
    
    @ApiModelProperty(value = "要求批次有效期，分母")
    private Integer denominator;
    
    @ApiModelProperty(value = "仓库类型")
    private Integer realWarehouseType;
    
    @ApiModelProperty(value = "可用库存")
    private BigDecimal availableQty;

    @ApiModelProperty(value = "生产日期")
    private Date productDate;

    @ApiModelProperty(value = "入库日期")
    private Date entryDate;

    @ApiModelProperty(value = "是否手动处理，否则自动处理，一般在扣减批次库存，为false时不按 fingerPrint来扣减")
    private Boolean manual;
}
