package com.rome.stock.innerservice.api.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * @Author: sun
 */
@Data
@EqualsAndHashCode
public class MpdAcceptanceDTO {


    @ApiModelProperty(value = "SKU编码")
    private String mdmCode;

    @ApiModelProperty(value = "大货验货时间")
    private String acceptanceTime;

    @ApiModelProperty(value = "大货验货数量")
    private String acceptanceAmount;

    public MpdAcceptanceDTO(String mdmCode, String acceptanceTime, String acceptanceAmount) {
        this.mdmCode = mdmCode;
        this.acceptanceTime = acceptanceTime;
        this.acceptanceAmount = acceptanceAmount;
    }
}
