package com.rome.stock.innerservice.api.controller;

import com.github.pagehelper.PageInfo;
import com.rome.arch.core.clientobject.Response;
import com.rome.arch.core.exception.RomeException;
import com.rome.arch.util.controller.RomeController;
import com.rome.stock.innerservice.api.dto.EnableOrDisableDTO;
import com.rome.stock.innerservice.api.dto.RealWarehouse;
import com.rome.stock.innerservice.api.dto.RealWarehouseReturnConfigDTO;
import com.rome.stock.innerservice.constant.ParamValidator;
import com.rome.stock.innerservice.constant.ResponseMsg;
import com.rome.stock.innerservice.domain.service.RealWarehouseReturnService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiResponse;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;

import java.util.List;


@Slf4j
@RomeController
@RequestMapping("/stock/returnRealWarehouse")
@Api(tags={"发货仓对应退货仓管理接口"})
public class RealWarehouseReturnController {

    private ParamValidator validator = ParamValidator.INSTANCE;

    @Autowired
    private RealWarehouseReturnService realWarehouseReturnService;

    @ApiOperation(value = "根据条件查询实仓信息,分页", nickname = "find_real_warehouse", produces = MediaType.APPLICATION_JSON_VALUE)
    @ApiResponse(code = 200, message = "success", response = RealWarehouse.class)
    @PostMapping("/queryReturnConfigByCondition")
    public Response<PageInfo<RealWarehouseReturnConfigDTO>> queryReturnConfigByCondition(@RequestBody RealWarehouseReturnConfigDTO paramDTO) {
        if(! validator.validParam(paramDTO)) {
            return ResponseMsg.PARAM_ERROR.buildMsg();
        }
        try {
            PageInfo<RealWarehouseReturnConfigDTO> realWarehouseList = realWarehouseReturnService.queryReturnConfigByCondition(paramDTO);
            return ResponseMsg.SUCCESS.buildMsg(realWarehouseList);
        } catch (RomeException e) {
            log.error(e.getMessage(), e);
            return ResponseMsg.FAIL.buildMsg(e.getCode(), e.getMessage());
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return ResponseMsg.EXCEPTION.buildMsg(e.getMessage());
        }
    }

    @ApiOperation(value = "新增或修改退货仓配置", nickname = "addOrUpdate", produces = MediaType.APPLICATION_JSON_VALUE)
    @ApiResponse(code = 200, message = "success", response = RealWarehouse.class)
    @PostMapping("/addOrUpdate")
    public Response addOrUpdate(@RequestBody RealWarehouseReturnConfigDTO paramDTO) {
        try {
            realWarehouseReturnService.addOrUpdate(paramDTO);
            return ResponseMsg.SUCCESS.buildMsg();
        } catch (RomeException e) {
            log.error(e.getMessage(), e);
            return ResponseMsg.FAIL.buildMsg(e.getCode(), e.getMessage());
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return ResponseMsg.EXCEPTION.buildMsg(e.getMessage());
        }
    }

    @ApiOperation(value = "批量激活或冻结", nickname = "batchDisableOrEnable", produces = MediaType.APPLICATION_JSON_VALUE)
    @ApiResponse(code = 200, message = "success", response = RealWarehouse.class)
    @PostMapping("/batchDisableOrEnable")
    public Response batchDisableOrEnable(@RequestBody EnableOrDisableDTO enableOrDisableDTO) {
        try {
            realWarehouseReturnService.batchDisableOrEnable(enableOrDisableDTO);
            return ResponseMsg.SUCCESS.buildMsg();
        } catch (RomeException e) {
            log.error(e.getMessage(), e);
            return ResponseMsg.FAIL.buildMsg(e.getCode(), e.getMessage());
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return ResponseMsg.EXCEPTION.buildMsg(e.getMessage());
        }
    }
}
