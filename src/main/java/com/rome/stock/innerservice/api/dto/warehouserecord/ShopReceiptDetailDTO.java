package com.rome.stock.innerservice.api.dto.warehouserecord;

import java.math.BigDecimal;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 类ShopReceiptOrderDetail的实现描述：门店
 *
 * <AUTHOR> 2019/4/29 15:48
 */
@Data
@EqualsAndHashCode
public class ShopReceiptDetailDTO {

    @ApiModelProperty(value = "行号")
    private String lineNo;

    @ApiModelProperty(value = "数量")
    @NotNull(message="确认收货数量不能为空")
    private BigDecimal actualQty;

    @ApiModelProperty(value = "sku编号")
    @NotBlank(message="明细skuCode不能为空")
    private String skuCode;

    @ApiModelProperty(value = "单位")
    private String unit;

    @ApiModelProperty(value = "单位code")
    private String unitCode;

    /**
     * 采购单行号
     */
    private String sapPoNo;
}
