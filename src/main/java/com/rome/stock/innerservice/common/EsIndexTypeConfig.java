/**
 * Filename ESIndexTypeConfig.java
 * Company 上海来伊份科技有限公司。
 * <AUTHOR>
 * @version 
 */
package com.rome.stock.innerservice.common;

import java.text.SimpleDateFormat;
import java.util.Date;

import org.springframework.stereotype.Component;


/**
 * ES索引等相关的配置
 * 
 * <AUTHOR>
 * @since 2019年11月6日 下午7:59:23
 */
@Component
public class EsIndexTypeConfig {
	
	private static final ThreadLocal<String> contextHolder = new ThreadLocal<>();

	/**
	 * 获取索引
	 * @param index 索引前辍名，后面跟着后辍
	 * @return 返回索引前辍名+后面跟着后辍，如果没设置后辍直接返回index，
	 */
	public String getIndex(String index) {
		String suffix = getIndexNameSuffix();
		if(suffix == null) {
			return index + getIndexNameSuffixDefault();
		}
        return index + suffix;
    }
	
	/**
	 * 获取索引,根据年创索引
	 * @param index 索引前辍名，后面跟着后辍
	 * @return 返回索引前辍名+后面跟着后辍，如果没设置后辍直接返回index，
	 */
	public String getIndexByYear(String index) {
		String suffix = getIndexNameSuffix();
		if(suffix == null) {
			return index + getIndexNameSuffixDefaultByYear();
		}
        return index + suffix;
    }

    /**
     * 设置索引后辍
     * 在保存时，需要根据日期分片时设置
     * @param date
     */
	public static void setIndexNameSuffix(String suffix) {
		contextHolder.set(suffix);
	}

	/**
     * 获取索引后辍
     * 在保存时，需要根据日期分片时
     * @param date
     */
	public static String getIndexNameSuffix() {
		return contextHolder.get();
	}

	/**
     * 清除索引后辍
     */
	public static void clearIndexNameSuffix() {
		contextHolder.remove();
	}
	
	/**
     * 设置索引后辍
     * 在保存时，需要根据日期分片时设置,分隔符用-
     * @param date
     */
	public static void setIndexNameSuffixByDate(String suffix,Date date) {
		setIndexNameSuffix("-" + suffix + dateToString(date,"-yyyy-MM"));
	}
	
	/**
     * 获取索引默认后辍
     */
	private String getIndexNameSuffixDefault() {
		return dateToString(new Date(),"-1-yyyy-MM");
	}
	
	/**
     * 获取索引默认后辍
     * 根据年
     */
	private String getIndexNameSuffixDefaultByYear() {
		return dateToString(new Date(),"-1-yyyy");
	}
	
	/**
     * 设置索引后辍，根据年
     * 在保存时，需要根据日期分片时设置,分隔符用-
     * @param date
     */
	public static void setIndexNameSuffixByYear(String suffix,Date date) {
		setIndexNameSuffix("-" + suffix + dateToString(date,"-yyyy"));
	}
	
	/**
     * 日期根据格式转换为字符串
     * @param date
     * @param formatStr
     * @return
     */
    private static String dateToString(Date date , String formatStr){
        SimpleDateFormat dateFormat = new SimpleDateFormat(formatStr);
        return dateFormat.format(date);
    }
}
