package com.rome.stock.innerservice.common;

import javax.annotation.Resource;

import org.apache.rocketmq.client.producer.SendResult;
import org.apache.rocketmq.spring.core.RocketMQTemplate;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.messaging.Message;
import org.springframework.messaging.support.MessageBuilder;
import org.springframework.stereotype.Service;

import com.alibaba.fastjson.JSON;
import com.rome.arch.core.exception.RomeException;

import lombok.extern.slf4j.Slf4j;

/**
 * 类StockMessageProducerUtil的实现描述：库存消息推送工具类
 *
 * <AUTHOR> 2020/8/17 15:09
 */
@Slf4j
@Service
public class StockMessageProducerUtil {

    @Resource
    private RocketMQTemplate rocketMQTemplate;

    @Value("${rocketmq.topic}")
    private String topic;

    @Value("${rocketmq.timeOut}")
    private Long timeOut;

    /**
     * 发送同步消息
     * @return
     */
    public boolean sendAsyncMQ(Object object, String messageTag) {
        String msg = JSON.toJSONString(object);
        log.info("发送消息：" + msg);
        try {
            //mq信息
            Message message = MessageBuilder.withPayload(msg).build();
            //发送mq
            SendResult sendResult = rocketMQTemplate.syncSend(topic + ":" + messageTag, message, timeOut);
            log.info("生产消息结果：{}", sendResult);
            if(!sendResult.getSendStatus().toString().equals(MqCode.MQ_SEND_OK)){
                throw new RomeException(MqCode.MQ_BATCH_STOCK_ERROR_2001, MqCode.MQ_BATCH_STOCK_ERROR_2001_DESC);
            }
        } catch (Exception e) {
            log.error("生产消息异常: ", e);
            throw new RomeException(MqCode.MQ_BATCH_STOCK_ERROR_2003, MqCode.MQ_BATCH_STOCK_ERROR_2003_DESC);
        }
        return true;
    }
}
