/**
 * Filename CsvExportUtil.java
 * Company 上海来伊份科技有限公司。
 * <AUTHOR>
 * @version 
 */
package com.rome.stock.innerservice.common;

import cn.afterturn.easypoi.csv.CsvExportUtil;
import cn.afterturn.easypoi.csv.entity.CsvExportParams;
import cn.afterturn.easypoi.csv.export.CsvExportService;
import cn.afterturn.easypoi.excel.entity.params.ExcelExportEntity;
import com.rome.arch.core.exception.RomeException;
import com.rome.stock.common.utils.DateUtil;
import com.rome.stock.innerservice.api.dto.template.StockRecordExportTemplate;
import com.rome.stock.innerservice.export.aspect.AsyncExcelResultContext;
import com.rome.stock.innerservice.export.domain.service.dto.AsyncExportResult;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.poi.ss.formula.functions.T;

import java.io.File;
import java.io.FileOutputStream;
import java.io.OutputStream;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.ArrayList;
import java.util.Date;
import java.util.LinkedHashMap;
import java.util.List;

/**
 * csv导出功能类
 * <AUTHOR>
 * @since 2020-12-29 11:47:43
 */
@Slf4j
public class CsvBigExportUtil {
	

	private CsvBigExportUtil() {
		
	}


	/**
	 * 导出大数据量csv
	 * @param <T>
	 * @param <P>
	 * @throws Exception
	 */
	public static void syncExportBigDataCsv(List<LinkedHashMap> list) throws Exception {
		if (CollectionUtils.isEmpty(list)) {
			return;
		}
		for (LinkedHashMap item : list) {
			for (Object key : item.keySet()) {
				Object value = item.get(key);
				if (value == null) {
					continue;
				} else if (value instanceof Date) {
					item.put(key, DateUtil.formatDateTime((Date) value));
				} else {
					item.put(key, value + "\t");
				}
			}
		}
		OutputStream out = null;
		int total = 0;
		String desc = "成功";
		long time = System.currentTimeMillis();
		try {
			LinkedHashMap jsObject = list.get(0);
			//设置行信息
			List<ExcelExportEntity> excelParams = new ArrayList<>();
			for (Object key : jsObject.keySet()) {
				ExcelExportEntity excelExportEntity = new ExcelExportEntity(key.toString(), key.toString());
				excelParams.add(excelExportEntity);
			}
			AsyncExportResult result = AsyncExcelResultContext.getAsyncExportResult();
			if (result == null) {
				throw new RomeException("999", "异步导出缺失AsyncExportResult");
			}
			String fileName = result.getFileName();
			if(StringUtils.isBlank(fileName)) {
				throw new RomeException("999", "数据异常，缺少文件名称");
			}
			CsvExportParams params = new CsvExportParams();
			params.setEncoding(CsvExportParams.GBK);
			Path path = Paths.get("tmp");
			File dirFile = new File(path.toAbsolutePath().toString());
			dirFile.mkdirs();
			result.setFileName(fileName);
			// 本地文件
			File file = new File(path.toAbsolutePath().toString(), fileName);
			//file.createNewFile();
			String filePath = file.getAbsolutePath();
			log.warn("文件地址： " + filePath);
			out = new FileOutputStream(file);

			CsvExportService cs = new CsvExportService(out, params, excelParams);
			cs.write(list);
			cs.close();
		} catch (Exception e) {
			desc = "失败";
			throw e;
		} finally {
			try {
				if(out != null) {
					out.close();
				}
			} catch (Exception e) {
//				e.printStackTrace();
			}
		}
	}


	/**
	 * 导出大数据量csv
	 * @param list
	 * @param fileName
	 * @param <T>
	 * @throws Exception
	 */
	public static <T> void syncExportNormalCsv(List<T> list) throws Exception {
		if (CollectionUtils.isEmpty(list)) {
			return;
		}
		AsyncExportResult result = AsyncExcelResultContext.getAsyncExportResult();
		String fileName=result.getFileName();
		OutputStream out = null;
		try {
			Path path = Paths.get("tmp");
			File dirFile = new File(path.toAbsolutePath().toString());
			dirFile.mkdirs();
			// 本地文件
			File file = new File(path.toAbsolutePath().toString(), fileName);
			String filePath = file.getAbsolutePath();
			log.warn("文件地址： " + filePath);
			out = new FileOutputStream(file);
			CsvExportParams params = new CsvExportParams();
			params.setEncoding(CsvExportParams.GBK);
			CsvExportUtil.exportCsv(params, StockRecordExportTemplate.class, list, out);
		} catch (Exception e) {
			throw e;
		} finally {
			try {
				if (out != null) {
					out.close();
				}
			} catch (Exception e) {
				//e.printStackTrace();
			}
		}
	}



	/**
	 * 锁的超时时间
	 */
	private final static int LOCK_TIME_OUT = 2 * 60;
	


	
}
