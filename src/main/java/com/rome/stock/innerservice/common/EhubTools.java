package com.rome.stock.innerservice.common;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.rome.stock.common.utils.DateUtil;
import com.rome.stock.common.utils.http.HttpUtils;
import com.rome.stock.innerservice.api.dto.ehub.*;
import com.rome.stock.innerservice.domain.entity.RealWarehouseE;
import com.rome.stock.innerservice.domain.entity.WmsCallRecordE;
import com.rome.stock.innerservice.domain.repository.BusinessReasonRepository;
import com.rome.stock.innerservice.domain.repository.RealWarehouseRepository;
import com.rome.stock.innerservice.domain.repository.WmsCallRecordRepository;
import com.rome.stock.innerservice.infrastructure.dataobject.BusinessReasonDO;
import com.rome.stock.innerservice.remote.tms.dto.TmsPackageItemVO;
import com.rome.stock.innerservice.remote.tms.dto.TmsPackageVO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.codec.binary.Base64;
import org.apache.commons.lang3.StringUtils;
import org.apache.http.HttpEntity;
import org.apache.http.NameValuePair;
import org.apache.http.client.ClientProtocolException;
import org.apache.http.client.entity.UrlEncodedFormEntity;
import org.apache.http.client.methods.CloseableHttpResponse;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.message.BasicNameValuePair;
import org.apache.http.util.EntityUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import org.springframework.util.DigestUtils;

import javax.annotation.Resource;
import java.io.IOException;
import java.io.UnsupportedEncodingException;
import java.nio.charset.StandardCharsets;
import java.util.*;

/**
 * @Author: sun
 * @Date: 2020/11/9 18:03
 */
@Slf4j
@Component
public class EhubTools {

    @Autowired
    private WmsCallRecordRepository wmsCallRecordRepository;
    @Resource
    private BusinessReasonRepository businessReasonRepository;
    @Resource
    private RealWarehouseRepository realWarehouseRepository;

    @Value("${ehub.url}")
    public String url;
    @Value("${ehub.orgKey}")
    public String orgKey;
    @Value("${ehub.authKey}")
    public String authKey;
    @Value("${ehub.code}")
    public String code;

    private static String bizType="TMS_UPSTREAM_ORDER_ASYNC";


    public String getEhubApiKey() {
        String timestamp = DateUtil.format(new Date(), "yyyyMMddHHmmss");
        String base64 = Base64.encodeBase64String((orgKey + timestamp).getBytes(StandardCharsets.UTF_8));
        String md5 = DigestUtils.md5DigestAsHex((timestamp + authKey).getBytes());
        String apiKey = base64 + md5;
        return apiKey;
    }


    public String postData(TmsPackageVO packageDTO) {
        log.warn(CoreKibanaLog.getJobLog("pushToEhub", bizType, "推送包裹信息给Ehub系统", packageDTO.toString()));
        // 构造wms交互记录对象
        WmsCallRecordE wmsCallRecordDTO = new WmsCallRecordE();
        wmsCallRecordDTO.setWmsCode(0);
        wmsCallRecordDTO.setRecordCode(packageDTO.getPackageCode());
        wmsCallRecordDTO.setRequestService("pushToEhub");
        wmsCallRecordDTO.setRequestUrl(url);
        wmsCallRecordDTO.setStatus(0);
        String timestamp = DateUtil.format(new Date(), "yyyyMMddHHmmss");
        String res="200";
        try {
            HEAD head = new HEAD();
//            head.setUuid(packageDTO.getPackageCode());
            head.setType(bizType);
            head.setBusiCode(timestamp + (int) ((Math.random() * 9 + 1) * 100000));
            head.setCode(code);
            head.setDate(timestamp);
//            head.setName("默认客户");
            head.setOperationType("auto");
            System.err.println(JSON.toJSONString(head));
            WLLINKED wllinked = new WLLINKED();
            wllinked.setHead(head);

            LEG leg = new LEG();
            leg.setLegNo(packageDTO.getPackageCode());
            leg.setToAddress(packageDTO.getGoodReceiverAddress());
            leg.setToArea(packageDTO.getGoodReceiverArea());
            leg.setToCity(packageDTO.getGoodReceiverCity());
            leg.setToProvince(packageDTO.getGoodReceiverProvince());
            leg.setToLocationName(packageDTO.getGoodReceiverName());
            leg.setTrackNo(packageDTO.getBillCode());
            leg.setWeight(packageDTO.getWeight());
            leg.setVolume(packageDTO.getVolume());
            leg.setToMobile(packageDTO.getGoodReceiverMobile());
            RealWarehouseE realWarehouseE=realWarehouseRepository.getRealWarehouseById(packageDTO.getRealWarehouseId());
            if(null !=realWarehouseE){
                leg.setFromLocationName(realWarehouseE.getRealWarehouseCode());
                leg.setFromProvince(realWarehouseE.getRealWarehouseProvince());
                leg.setFromCity(realWarehouseE.getRealWarehouseCity());
                leg.setFromArea(realWarehouseE.getRealWarehouseArea());
                leg.setFromAddress(realWarehouseE.getRealWarehouseAddress());
            }
            BusinessReasonDO businessReasonDO=businessReasonRepository.getReasonByBusinessTypeAndReasonCode(100,packageDTO.getLogisticsCode());
            if(null !=businessReasonDO){
                leg.setOwnerCompanyCode(businessReasonDO.getReasonName());
                leg.setCarrierName(businessReasonDO.getReasonName());
            }
            List<Detail> details=new ArrayList<>();
            for(TmsPackageItemVO tmsPackageItemVO:packageDTO.getTmsPackageItems()){
                Detail detail = new Detail();
                detail.setLegNO(packageDTO.getPackageCode());
                detail.setProductCode(tmsPackageItemVO.getCode());
                detail.setProductName(tmsPackageItemVO.getProductCname());
                detail.setQuantity(String.valueOf(tmsPackageItemVO.getProductItemOutNum()));
                detail.setProductName(tmsPackageItemVO.getName());
                details.add(detail);
            }
            leg.setDetails(details);
            XMLDATA xmlData=new XMLDATA();
            xmlData.setLegs(Arrays.asList(leg));
            wllinked.setXmlData(xmlData);
            String str = JaxbUtil.convertToXml(wllinked);
            wmsCallRecordDTO.setRequestContent(str);
            res=sendHttpPostWithParams(str);
        }catch (Exception e){
            res=(null==e.getMessage()?e.toString():e.getMessage());
            log.error("推送包裹信息到ehub失败:"+e.getMessage());
        }finally {
            // 保存交互记录
            wmsCallRecordDTO.setResponseContent(res);
            wmsCallRecordRepository.saveWmsCallRecord(wmsCallRecordDTO);
        }
        return res;
    }

    /**
     * 请求Ehub接口
     * @param body
     * @return
     */
    public String sendHttpPostWithParams(String body) {
        Map<String, String> paramMap = new HashMap<>();
        paramMap.put("head", "{     \"code\": \"56LINKED\",      \"uuid\": \"" + UUID.randomUUID() + "\" } ");
        paramMap.put("body", body);
        log.info("请求ehub接口参数body=>{}",body);
        HttpPost bestPost = new HttpPost(url);
        List<NameValuePair> paramList = Lists.newArrayList();
        for (String paramName : paramMap.keySet()) {
            NameValuePair nameValuePair = new BasicNameValuePair(paramName, paramMap.get(paramName));
            paramList.add(nameValuePair);
        }
        String responseStr = "200";
        try {
            HttpEntity entity = new UrlEncodedFormEntity(paramList, "UTF-8");
            String apiKey = getEhubApiKey();
            bestPost.addHeader("apiKey", apiKey);
            bestPost.addHeader("bizType", bizType);
            bestPost.setEntity(entity);

            CloseableHttpClient client = HttpUtils.getHttpClient();
            CloseableHttpResponse httpResponse = client.execute(bestPost);
            HttpEntity responseEntity = httpResponse.getEntity();
            int status = httpResponse.getStatusLine().getStatusCode();
            responseStr = EntityUtils.toString(responseEntity, "UTF-8");
            String msgId = httpResponse.getFirstHeader("id").getValue();

            if (StringUtils.isNotEmpty(responseStr) && StringUtils.isNotEmpty(msgId)) {
                responseStr = responseStr + ", msgId=" + msgId;
            } else if (StringUtils.isEmpty(responseStr) && StringUtils.isNotEmpty(msgId)) {
                responseStr = "处理成功, msgId=" + msgId;
            }
            log.info("----------------OFO, bizType------------:" + bizType);
            log.info("----------------OFO, msgId------------:" + msgId);
            if (200 != status) {
                if (StringUtils.isEmpty(responseStr)) {
                    if (status < 400 || status >= 500) {
                        responseStr = "The response code is error: " + status;
                    }
                }
                throw new Exception(responseStr);
            }
        } catch (UnsupportedEncodingException e) {
            responseStr=e.getMessage();
            log.error(e.getMessage(), e);
        } catch (ClientProtocolException e) {
            responseStr=e.getMessage();
            log.error(e.getMessage(), e);
        } catch (IOException e) {
            responseStr=e.getMessage();
            log.error(e.getMessage(), e);
        } catch (Exception e) {
            responseStr=(null==e.getMessage()?e.toString():e.getMessage());
            log.error(e.getMessage(), e);
        }
        System.err.println(responseStr);
        return responseStr;
    }

}
