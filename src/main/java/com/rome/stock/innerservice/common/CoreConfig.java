package com.rome.stock.innerservice.common;

import lombok.Data;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

/**
 * @Description: Job自定义配置
 * <p>
 * @Author: chu<PERSON><PERSON><PERSON>  2019/6/11
 */
@Component
@Data
public class CoreConfig {

    /**
     * 新增店铺渠道类型
     */
    @Value("${shop.channels}")
    private String channelTypes;

    @Value("${xuantian.url.merchant}")
    private String xtMerchantUrl;


    @Value("${xuantian.url.takeout}")
    private String xtTakeoutUrl;

    @Value("${xuantian.url.virtual}")
    private String xtVirtualUrl;

    @Value("${xuantian.clientId.merchant}")
    private String xtMerchantClientId;

    @Value("${xuantian.clientId.takeout}")
    private String xtTakeoutClientId;

    @Value("${xuantian.clientId.virtual}")
    private String xtVirtualClientId;

    @Value("${tms.clientId}")
    private String tmsClientId;

    @Value("${tms.url}")
    private String tmsUrl;


    @Value("${tms.baseUrl}")
    private String tmsBaseUrl;

    @Value("${xuantian.url.cross}")
    private String crossUrl;

}
