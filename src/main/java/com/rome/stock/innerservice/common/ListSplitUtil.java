package com.rome.stock.innerservice.common;

import java.util.List;
import java.util.function.Consumer;

/**
 * <AUTHOR>
 */
public class ListSplitUtil {
    private ListSplitUtil() {
    }

    public static <T> void split(List<T> data, int splitSize, Consumer<List<T>> splitConsumer) {
        if (data == null || data.isEmpty()) {
            return;
        }

        // 计算分批次数
        int count = (data.size() + splitSize - 1) / splitSize;

        // 分批截取
        for (int splitBatchNumber = 1; splitBatchNumber <= count; splitBatchNumber++) {
            int startIndex = (splitBatchNumber - 1) * splitSize;
            List<T> splitList = data.subList(startIndex, Math.min(startIndex + splitSize, data.size()));
            splitConsumer.accept(splitList);
        }
    }
}
