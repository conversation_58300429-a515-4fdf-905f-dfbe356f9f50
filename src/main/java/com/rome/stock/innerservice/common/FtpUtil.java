package com.rome.stock.innerservice.common;

import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.io.OutputStream;

import org.apache.commons.net.ftp.FTP;
import org.apache.commons.net.ftp.FTPClient;
import org.apache.commons.net.ftp.FTPClientConfig;
import org.apache.commons.net.ftp.FTPFile;
import org.apache.commons.net.ftp.FTPReply;

import com.rome.stock.innerservice.domain.service.impl.VmsStockReportServiceImpl;

import lombok.extern.slf4j.Slf4j;

@Slf4j
public class FtpUtil {


	//本地字符编码
	static String LOCAL_CHARSET = "GBK";

	/**
	 * Description: 向FTP服务器上传文件
	 * @param host FTP服务器hostname
	 * @param port FTP服务器端口
	 * @param username FTP登录账号
	 * @param password FTP登录密码
	 * @param basePath FTP服务器基础目录
	 * @param filePath FTP服务器文件存放路径。例如分日期存放：/2015/01/01。文件的路径为basePath+filePath
	 * @param filename 上传到FTP服务器上的文件名
	 * @param input 输入流
	 * @return 成功返回true，否则返回false
	 */
	public static boolean uploadFile(String host, int port, String username, String password, String basePath,
									 String filePath, String filename, InputStream input) {
		boolean result = false;
		FTPClient ftp = new FTPClient();
			try {
			int reply;
			// 设置连接的超时时间为1min
			ftp.setConnectTimeout(60000);
			ftp.connect(host, port);// 连接FTP服务器
			// 如果采用默认端口，可以使用ftp.connect(host)的方式直接连接FTP服务器
			ftp.login(username, password);// 登录
			FTPClientConfig ftpConfig = new FTPClientConfig(FTPClientConfig.SYST_UNIX);
			ftpConfig.setServerLanguageCode("zh");
			// 设置传输超时时间为3min
			ftp.setDataTimeout(180000);
			ftp.enterLocalPassiveMode();
			reply = ftp.getReplyCode();

			if (FTPReply.isPositiveCompletion(ftp.sendCommand("OPTS UTF8", "ON"))) {
				LOCAL_CHARSET = "UTF-8";
			}
			ftp.setControlEncoding(LOCAL_CHARSET);


			if (!FTPReply.isPositiveCompletion(reply)) {
				ftp.disconnect();
				return result;
			}
			//切换到上传目录
			if (!ftp.changeWorkingDirectory(basePath+filePath)) {
				//如果目录不存在创建目录
				String[] dirs = filePath.split("/");
				String tempPath = basePath;
				for (String dir : dirs) {
					if (null == dir || "".equals(dir)) {
						continue;
					}
					tempPath += "/" + dir;
					if (!ftp.changeWorkingDirectory(tempPath)) {
						if (!ftp.makeDirectory(tempPath)) {
							return result;
						} else {
							ftp.changeWorkingDirectory(tempPath);
						}
					}
				}
			}
			//设置上传文件的类型为二进制类型
			ftp.setFileType(FTP.BINARY_FILE_TYPE);
			//上传文件 ，并设置文件中文名称
			if (!ftp.storeFile(new String(filename.getBytes(LOCAL_CHARSET),"ISO-8859-1"), input)) {
				return result;
			}
			input.close();
			ftp.logout();
			result = true;
		} catch (Exception e) {
			log.error(e.getMessage(), e);
		} finally {
			if (ftp.isConnected()) {
				try {
					ftp.disconnect();
				} catch (IOException ioe) {
				}
			}
		}
		return result;
	}

	/**
	 * Description: 从FTP服务器下载文件 
	 * @param host FTP服务器hostname 
	 * @param port FTP服务器端口 
	 * @param username FTP登录账号 
	 * @param password FTP登录密码 
	 * @param remotePath FTP服务器上的相对路径 
	 * @param fileName 要下载的文件名 
	 * @param localPath 下载后保存到本地的路径 
	 * @return
	 */
	public static boolean downloadFile(String host, int port, String username, String password, String remotePath,
									   String fileName, String localPath) {
		boolean result = false;
		FTPClient ftp = new FTPClient();
		try {
			int reply;
			ftp.connect(host, port);
			// 如果采用默认端口，可以使用ftp.connect(host)的方式直接连接FTP服务器
			ftp.login(username, password);// 登录
			reply = ftp.getReplyCode();
			if (!FTPReply.isPositiveCompletion(reply)) {
				ftp.disconnect();
				return result;
			}
			ftp.changeWorkingDirectory(remotePath);// 转移到FTP服务器目录
			FTPFile[] fs = ftp.listFiles();
			for (FTPFile ff : fs) {
				if (ff.getName().equals(fileName)) {
					File localFile = new File(localPath + "/" + ff.getName());

					OutputStream is = new FileOutputStream(localFile);
					ftp.retrieveFile(ff.getName(), is);
					is.close();
				}
			}

			ftp.logout();
			result = true;
		} catch (IOException e) {
			log.error(e.getMessage(), e);
		} finally {
			if (ftp.isConnected()) {
				try {
					ftp.disconnect();
				} catch (IOException ioe) {
				}
			}
		}
		return result;
	}
	
	/**
     * 登陆FTP并获取FTPClient对象
     *
     * @param host     FTP主机地址
     * @param port     FTP端口
     * @param userName 登录用户名
     * @param password 登录密码
     * @return
     */
    public static FTPClient loginFTP(String host, int port, String userName, String password) {
        FTPClient ftpClient = null;
        try {
        	int reply;
            ftpClient = new FTPClient();
            // 设置连接的超时时间为1min
            ftpClient.setConnectTimeout(60000);
            // 连接FTP服务器
            ftpClient.connect(host, port);
            // 登陆FTP服务器
            ftpClient.login(userName, password);
            FTPClientConfig ftpConfig = new FTPClientConfig(FTPClientConfig.SYST_UNIX);
			ftpConfig.setServerLanguageCode("zh");
			// 设置传输超时时间为3min
			ftpClient.setDataTimeout(180000);
			ftpClient.enterLocalPassiveMode();
			reply = ftpClient.getReplyCode();
			String local = "GBK";
			if (FTPReply.isPositiveCompletion(ftpClient.sendCommand("OPTS UTF8", "ON"))) {
				local = "UTF-8";
			}
			ftpClient.setControlEncoding(local);
            // 设置文件类型为二进制（如果从FTP下载或上传的文件是压缩文件的时候，不进行该设置可能会导致获取的压缩文件解压失败）
            ftpClient.setFileType(FTPClient.BINARY_FILE_TYPE);
            if (!FTPReply.isPositiveCompletion(reply)) {
                log.error("连接FTP失败，用户名或密码错误。");
                ftpClient.disconnect();
            } else {
            	log.info("FTP连接成功!");
            }
        } catch (Exception e) {
        	log.error("登陆FTP失败，请检查FTP相关配置信息是否正确！", e);
        }
        return ftpClient;
    }
    
    /**
     * 获取上传到FTP输出流
     * @param ftpClient 已经登陆成功的FTPClient
     * @param basePath
     * @param filePath
     * @param filename
     * @return
     */
    public static OutputStream getUploadOutputStream(FTPClient ftpClient, String basePath, String filePath, String filename) {
    	try {
    		//切换到上传目录
    		if (!ftpClient.changeWorkingDirectory(basePath+filePath)) {
    			//如果目录不存在创建目录
    			String[] dirs = filePath.split("/");
    			String tempPath = basePath;
    			for (String dir : dirs) {
    				if (null == dir || "".equals(dir)) {
						continue;
					}
    				tempPath += "/" + dir;
    				if (!ftpClient.changeWorkingDirectory(tempPath)) {
    					if (!ftpClient.makeDirectory(tempPath)) {
    						return null;
    					} else {
    						ftpClient.changeWorkingDirectory(tempPath);
    					}
    				}
    			}
    		}
    		//设置上传文件的类型为二进制类型
    		ftpClient.setFileType(FTP.BINARY_FILE_TYPE);
			//上传文件 ，并设置文件中文名称
    		return ftpClient.storeFileStream(new String(filename.getBytes(ftpClient.getControlEncoding()),"ISO-8859-1"));
		} catch (Exception e) {
			log.error("获取FTP上传流失败，请检查FTP相关配置信息是否正确！", e);
		}
    	return null;
    }


}
