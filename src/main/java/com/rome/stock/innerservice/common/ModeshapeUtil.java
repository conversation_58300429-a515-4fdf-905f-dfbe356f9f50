/**
 * Filename ModeshapeUtil.java
 * Company 上海来伊份科技有限公司。
 * <AUTHOR>
 * @version 
 */
package com.rome.stock.innerservice.common;

import java.util.ArrayList;
import java.util.Collections;
import java.util.Comparator;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.rome.arch.core.exception.RomeException;
import com.rome.stock.common.constants.ResCode;
import com.rome.stock.innerservice.api.dto.ModeshapeNode;

/**
 * 模型图功能类
 * <AUTHOR>
 * @since 2022-10-26 14:47:41
 */
public class ModeshapeUtil {

    /**
     * 横向步长
     */

    private int stepX = 1;

    /**
     * 纵向步长
     */

    private int stepY = 1;
    
    /**
     * x起点位置，相当于偏移量
     */
    private int startX = 0;
    
    private int halfNodeSum;
    
    /**
     * 叶子节点
     */
    private List<ModeshapeNode> leafNodesList = new ArrayList<>();
    
    /**
     * 所有的结点
     */
    private List<ModeshapeNode> allNode = new ArrayList<>();
    
    /**
     * 位置
     */
    private Map<String, Map<String, Integer>> result = new HashMap<>();
    
    
    /**
     * key>>>结点的deep值，val>>>x目前达到的最大值，
     * 防止结点碰撞
     */
    private Map<Integer, Integer> maxDeepCoordinateX = new HashMap<>();
    
    /**
     * 计算位置时用的
     */
    private Set<String> idSet = new HashSet<>(16);
    
    public ModeshapeUtil() {
    	
    }
    
    /**
     * 添加结点
     * @param node 结点
     * @param parents 父结点，子结点自动添加的
     * @return
     */
    /**
     * 添加结点，注意：此方法要求父结点必须先添加的
     * @param node 结点
     * @param parentIdList 父结点id列表，父和子结点自动添加的，一般不为空，只有根结点才为空
     */
    public void addNodeByParentIds(ModeshapeNode node, List<String> parentIdList) {
    	if(parentIdList == null || parentIdList.size() == 0) {
    		addNode(node, null);
    		return;
    	}
    	if(parentIdList.contains(node.getId())) {
    		throw new RomeException(ResCode.STOCK_ERROR_1002, ResCode.STOCK_ERROR_1002_DESC + "父结点不能是自己id=" + node.getId());
    	}
    	
    	List<ModeshapeNode> parents = new ArrayList<>();
    	for(ModeshapeNode dto : allNode) {
    		if(parentIdList.contains(dto.getId())) {
    			parents.add(dto);
    		}
    	}
    	if(parentIdList.size() != parents.size()) {
    		throw new RomeException(ResCode.STOCK_ERROR_1002, ResCode.STOCK_ERROR_1002_DESC + "父结点含有不存在的=" + JSON.toJSONString(parentIdList));
    	}
    	// 添加结点
    	addNode(node, parents.size() == 0 ? null : parents);
    }
    
	/**
     * 添加结点
     * @param node 结点
     * @param parents 父结点，子结点自动添加的
     * @return
     */
	private void addNode(ModeshapeNode node, List<ModeshapeNode> parents) {
		// 父结点的，子结点处理
		if (parents != null && parents.size() > 0) {
			for(ModeshapeNode a : allNode) {
				if(a.getId().equals(node.getId())) {
		    		throw new RomeException(ResCode.STOCK_ERROR_1002, ResCode.STOCK_ERROR_1002_DESC + "结点已存在,不能重复添加,Id=" + node.getId());
		    	}
			}
			node.setParents(parents);
			for (ModeshapeNode p : parents) {
				if(p.getId().equals(node.getId())) {
					throw new RomeException(ResCode.STOCK_ERROR_1002, ResCode.STOCK_ERROR_1002_DESC + "父结点不能是自己id=" + node.getId());
				}
				if (p.getChildren() == null) {
					p.setChildren(new ArrayList<>());
				}
				p.getChildren().add(node);
			}
		}
		node.setY(node.getDeep() * stepY);
		this.allNode.add(node);
	}
	
	/**
	 * 绘制位置
	 * @param root
	 */
	public void draw(ModeshapeNode root) {
		// 计算叶子结点，支持重新计算
		leafNodesList.clear();
		idSet.clear();
		reSortNode(root);
		calLeaf(root, leafNodesList);
		System.out.println(JSON.toJSONString(leafNodesList));
		idSet.clear();
		innerDraw(root);
	}
	
	/**
	 * 绘制位置
	 * @param root
	 */
	private void innerDraw(ModeshapeNode root) {
		for (int i = 0; i < root.getChildren().size(); i++) {
			innerDraw(root.getChildren().get(i));
		}
		if(idSet.contains(root.getId())) {
			return;
		}
		idSet.add(root.getId());
		int x = getCoordinateX(root);
		// 碰撞处理
		if(maxDeepCoordinateX.get(root.getDeep()) != null && maxDeepCoordinateX.get(root.getDeep()).intValue() > x) {
			x = maxDeepCoordinateX.get(root.getDeep()) + 1;
		}
		maxDeepCoordinateX.put(root.getDeep(), x);
		root.setX(x);
		Map<String, Integer> offset = new HashMap<>();
		offset.put("x", root.getX());
		offset.put("y", root.getY());
		result.put(root.getId(), offset);
//		StringBuilder sb = new StringBuilder();
//		sb.append("结点：Id=" + root.getId())
//		  .append(",name=" + root.getName())
//		  .append(",x=" + root.getX())
//		  .append(",y=" + root.getY());
//		System.out.println(sb);
	}
	
	
	
	/**
	 * x坐标
	 * @param root
	 * @return
	 */
	private int getCoordinateX(ModeshapeNode root) {
		int coordinateY = 0;
		if (root.getChildren().size() > 1) {
			coordinateY = (root.getChildren().get(0).getX() + root.getChildren().get(root.getChildren().size() - 1).getX()) / 2;
		} else if (root.getChildren().size() == 1) {
			coordinateY = root.getChildren().get(0).getX();
		} else {
			coordinateY = getLeafCoordinateX(root.getId());
		}
		return coordinateY;
	}

	/**
	 * 叶子位置x坐标
	 * @param id
	 * @return
	 */
	private int getLeafCoordinateX(String id) {
		int coordinate = 0;
		for (int i = 0; i < leafNodesList.size(); i++) {
			if (!leafNodesList.get(i).getId().equals(id)) {
				continue;
			}
			if (i <= halfNodeSum) {
				coordinate = startX - stepX * (halfNodeSum - i);
			} else {
				coordinate = startX + stepX * (i - halfNodeSum);
			}
			break;
		}
		return coordinate;
	}
	
	/**
	 * 计算叶子结点，支持重新计算
	 * @param root
	 * @param leafList
	 */
	private void calLeaf(ModeshapeNode root, List<ModeshapeNode> leafList) {
		List<ModeshapeNode> children = root.getChildren();
		if (children.size() > 0) {
			for (int i = 0; i < children.size(); i++) {
				calLeaf(children.get(i), leafList);
			}
		} else {
			if(idSet.contains(root.getId())) {
				return;
			}
			idSet.add(root.getId());
			leafList.add(root);
		}
	}
	
	/**
	 * @return the result
	 */
	public Map<String, Map<String, Integer>> getResult() {
		return result;
	}
	
	/**
	 * 排序一下
	 * @param root
	 */
	public void reSortNode(ModeshapeNode root) {
		if(root.getChildren() == null || root.getChildren().size() <= 1) {
			return;
		}
		Collections.sort(root.getChildren(), new Comparator<ModeshapeNode>() {
            @Override
            public int compare(ModeshapeNode o1, ModeshapeNode o2) {
            	int i = o1.getChildren() == null?0:o1.getChildren().size();
            	int j = o2.getChildren() == null?0:o2.getChildren().size();
                return j-i;
            }
        });
		if(root.getChildren().get(0).getChildren() == null || root.getChildren().get(0).getChildren().size() > 1) {
			Collections.sort(root.getChildren().get(0).getChildren(), new Comparator<ModeshapeNode>() {
	            @Override
	            public int compare(ModeshapeNode o1, ModeshapeNode o2) {
	            	int i = o1.getChildren() == null?0:o1.getChildren().size();
	            	int j = o2.getChildren() == null?0:o2.getChildren().size();
	                return i-j;
	            }
	        });
		}
		
		if(root.getChildren().get(root.getChildren().size()-1).getChildren() == null || root.getChildren().get(root.getChildren().size()-1).getChildren().size() > 1) {
			Collections.sort(root.getChildren().get(root.getChildren().size()-1).getChildren(), new Comparator<ModeshapeNode>() {
	            @Override
	            public int compare(ModeshapeNode o1, ModeshapeNode o2) {
	            	int i = o1.getChildren() == null?0:o1.getChildren().size();
	            	int j = o2.getChildren() == null?0:o2.getChildren().size();
	                return j-i;
	            }
	        });
		}
	}
	
	/**
	 * 获取Echarts数据
	 * @return
	 */
	public JSONObject getModeshapByEcharts() {
		// 画布按最小800*400大小   宽与长先按照2比1
		// 图例大小 40 到 80 最小为40 最大为80  
		// 图例间隔最小为10 最大为30
		final int minWidth = 800; // 最小宽 800
		final int minHeight = 100; // 最小高 400
		final int nodeMinWidth = 60; // 结点最小宽 40
		final int nodeMaxWidth = 80; // 结点最大宽 80
		final int nodeMinGap = 10; // 结点最小间隔 10
		final int nodeMaxGap = 30; // 结点最大间隔
		int nodeWidth; // 结点图例大小
		int nodeGap; // 结点间隔大小
		int width = 0; // 宽
		int height = 0; // 高
		// start 算结点长宽和间隔大小
		// 最大的x
		int maxX = 0;
		for(Integer num : maxDeepCoordinateX.values()) {
			if(maxX < num) {
				maxX = num;
			}
		}
		int temp = minWidth / (maxX + 2);
		// 小于最小宽度+间隔时
		if(temp < nodeMinWidth + nodeMinGap) {
			nodeWidth = nodeMinWidth;
			nodeGap = nodeMinGap;
		} else if(temp > nodeMaxWidth + nodeMaxGap) {
			// 大于最小宽度+间隔时
			nodeWidth = nodeMaxWidth;
			nodeGap = nodeMaxGap;
		} else {
			nodeWidth = nodeMinWidth;
			nodeGap = nodeMinGap;
			temp = temp - nodeMinWidth - nodeMinGap;
			int t = temp * 1 / 4; // 扣除最小后，剩余的，间隔和节点宽度，按1比4方式分配
			nodeGap = nodeGap + t;
			nodeWidth = nodeWidth + (temp - t);
		}
		if(nodeGap > nodeMaxGap) {
			nodeGap = nodeMaxGap;
		}
		if(nodeWidth > nodeMaxWidth) {
			nodeWidth = nodeMaxWidth;
		}
		// end 算结点长宽和间隔大小
		// 画布宽
		width = (nodeWidth+nodeGap) * (maxX + 2);
//		if(width < minWidth) {
//			width = minWidth;
//		}
		int i = 3;
		if(width<height) {
			i = 1;
		}
		// 画布高
		height = (nodeWidth+nodeGap*i) * 4;
		
		if(height < minHeight) {
			height = minHeight;
		} else if(width > minWidth && width/2 < height) {
			height = width/2;
		}
		// 宽两个结点相距宽度
		int gapW = nodeWidth + nodeGap;
		// x起点坐标值
		int startX = gapW / 2;
		if(minWidth > gapW * (maxX + 2)) {
			startX += (minWidth -(gapW * (maxX + 2))) / 2;
		}
		// x值就是x起点坐标值+坐标索引*两个结点相距宽度
		for(ModeshapeNode dto : allNode) {
			dto.setX(startX + (dto.getX()*gapW));
		}
		// y值
		// y起点坐标值
		int startY = nodeWidth / 2;
		// 高两个结点相距宽度
		int gapH = (height-nodeWidth) / 3;//(height-nodeWidth) / 3;
		// 模型Y这里最高为4  1-实仓	2-虚仓	3-策略组	4-渠道
		for(ModeshapeNode dto : allNode) {
			dto.setY(startY + ((4-dto.getY())*gapH));
		}
		// start Echarts
		JSONObject echarts = new JSONObject();
		// 结点大小
		echarts.put("nodeWidth", nodeWidth);
		// 画布宽
		echarts.put("width", width);
		// 画布高
//		height = height - startY;
		echarts.put("height", height);
		// echarts data
		JSONArray echartsData = new JSONArray();
		echarts.put("data", echartsData);
		// type 1-实仓	2-虚仓	3-策略组	4-渠道
		for(ModeshapeNode dto : allNode) {
			// 根结点过滤掉
			if(dto.getType().intValue() == 0) {
				continue;
			}
			echartsData.add(getModeshapByEchartsByNode(dto));
		}
		// 图层结点
		// 渠道
		JSONObject nodeJsonObject = getModeshapByEchartsByNodeLevel("销售\n渠道", "渠道层", startX + ((maxX + 1)*gapW), startY, "#3CC43C", "#87CEFA");
		echartsData.add(nodeJsonObject);
		// 策略
		nodeJsonObject = getModeshapByEchartsByNodeLevel("库存\n策略", "策略组", startX + ((maxX + 1)*gapW), startY + (1*gapH), "#87CEFA", "#FFD700");
		echartsData.add(nodeJsonObject);
		// 虚仓
		nodeJsonObject = getModeshapByEchartsByNodeLevel("虚拟\n库存", "虚仓层", startX + ((maxX + 1)*gapW), startY + (2*gapH), "#FFD700", "#FF8C00");
		echartsData.add(nodeJsonObject);
		// 实仓
		nodeJsonObject = getModeshapByEchartsByNodeLevel("实仓\n库存", "实仓层", startX + ((maxX + 1)*gapW), startY + (3*gapH), "#FF8C00", "#FF3C00");
		echartsData.add(nodeJsonObject);
		
		// echarts links
		JSONArray echartsLinks = new JSONArray();
		echarts.put("links", echartsLinks);		
		for(ModeshapeNode dto : allNode) {
			// 根结点过滤掉
			if(dto.getType().intValue() == 0) {
				continue;
			}
			if(dto.getChildren() == null || dto.getChildren().size() == 0) {
				continue;
			}
			for(ModeshapeNode dto2 : dto.getChildren()) {
				nodeJsonObject = new JSONObject();
				nodeJsonObject.put("source", dto.getId());
				nodeJsonObject.put("target", dto2.getId());
				echartsLinks.add(nodeJsonObject);
			}
		}
		return echarts;
	}
	
	/**
	 * echarts 层级结点数据
	 * @param name
	 * @param val
	 * @param x
	 * @param y
	 * @param color
	 * @param borderColor
	 * @return
	 */
	private JSONObject getModeshapByEchartsByNodeLevel(String name, String val, int x, int y, String color, String borderColor) {
		JSONObject nodeJsonObject = new JSONObject();
		nodeJsonObject.put("name", name);
		nodeJsonObject.put("value", val);
		nodeJsonObject.put("x", x);
		nodeJsonObject.put("y", y);
		nodeJsonObject.put("symbol", "rect");
		JSONObject itemStyleJsonObject = new JSONObject();
		itemStyleJsonObject.put("color", color);
		itemStyleJsonObject.put("borderColor", borderColor);
		itemStyleJsonObject.put("borderWidth", 2);
		nodeJsonObject.put("itemStyle", itemStyleJsonObject);
		return nodeJsonObject;
	}
	
	/**
	 * echarts 层级结点数据
	 * @param dto
	 * @return
	 */
	private JSONObject getModeshapByEchartsByNode(ModeshapeNode dto) {
		JSONObject nodeJsonObject = new JSONObject();
		nodeJsonObject.put("id", dto.getId());
		nodeJsonObject.put("name", dto.getName());
		nodeJsonObject.put("value", dto.getValue());
		nodeJsonObject.put("x", dto.getX());
		nodeJsonObject.put("y", dto.getY());
		JSONObject itemStyleJsonObject = new JSONObject();
		if(dto.getType().intValue() == 1) { 
			// 实仓结点颜色
			itemStyleJsonObject.put("color", "#FF8C00");
		} else if(dto.getType().intValue() == 2) {
			// 虚仓结点颜色
			itemStyleJsonObject.put("color", "#FFD700");
		} else if(dto.getType().intValue() == 3) {
			// 组结点颜色
			itemStyleJsonObject.put("color", "#87CEFA");
		} else if(dto.getType().intValue() == 4) {
			// 渠道结点颜色
			itemStyleJsonObject.put("color", "#3CC43C");
		}	
		nodeJsonObject.put("itemStyle", itemStyleJsonObject);
		return nodeJsonObject;
	}
}
