/**
 * Filename StringUtils.java
 * Company 上海来伊份科技有限公司。
 * <AUTHOR>
 * @version 
 */
package com.rome.stock.innerservice.common;

import java.io.PrintWriter;
import java.io.StringWriter;
import java.util.Objects;

/**
 * 字符串功能类
 * <AUTHOR>
 * @since 2019年6月17日 上午10:15:59
 */
public class StringUtils extends org.apache.commons.lang3.StringUtils{

	/**
	 * 获取异常Trace
	 * @param e
	 * @return
	 */
	public static String getExceptionMessage(Throwable e){
    	try {
    		StringWriter sw = new StringWriter();
            PrintWriter pw = new PrintWriter(sw);
            e.printStackTrace(pw);
            String string = sw.toString();
            String replaceAll = string.replaceAll("\t","");
            return replaceAll;
		} catch (Exception e2) {
			return e2.toString();
		}
	}
	
	/**
	 * 截取出，指定的字符串后的，数字字符串
	 * @param sourceStr 源字符串
	 * @param destStr  指定字符串
	 * @return 截取中，之后，数字不连续，立即返回
	 */
	public static String strAfterNum(String sourceStr, String destStr) {
		int i = sourceStr.indexOf(destStr);
		if(i == -1) {
			return "";
		}
		int len = sourceStr.length();
		i = i + destStr.length();
		StringBuffer result = new StringBuffer();
		for(; i < len; i++) {
			if(Character.isDigit(sourceStr.charAt(i))) {
				result.append(sourceStr.charAt(i));
			} else {
				if(result.length() > 0) {
					return result.toString();
				}
			}
		}
		return result.toString();
	}

	/**
	 * 对象null转为""
	 * @param str
	 * @return
	 */
	public static String blank(String str){
		return Objects.isNull(str)?"":str;
	}
}
