package com.rome.stock.innerservice.common;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.rome.stock.common.constants.CommonConstants;
import com.rome.stock.common.constants.TmsConstants;
import com.rome.stock.common.utils.http.HttpUtils;
import com.rome.stock.innerservice.api.dto.DoDTO;
import com.rome.stock.innerservice.api.dto.TmsLogisticInfoDTO;
import com.rome.stock.innerservice.domain.entity.AddressE;
import com.rome.stock.innerservice.domain.entity.WmsCallRecordE;
import com.rome.stock.innerservice.domain.repository.WmsCallRecordRepository;
import com.rome.stock.innerservice.remote.tms.dto.TmsPackageVO;
import lombok.extern.slf4j.Slf4j;
import org.apache.http.HttpEntity;
import org.apache.http.HttpResponse;
import org.apache.http.client.methods.HttpGet;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.entity.StringEntity;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.util.EntityUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.io.IOException;
import java.util.ArrayList;
import java.util.List;

/**
 * @Description: 玄天调用工具类
 * <p>
 * @Author: lchy  2019/8/6
 */
@Slf4j
@Component
public class TmsTools {
    @Autowired
    private CoreConfig coreConfig;
    @Autowired
    private WmsCallRecordRepository wmsCallRecordRepository;



    public List<TmsLogisticInfoDTO> queryLogisticByOutHouseCode(String realWarehouseCode){
        return queryLogisticByOutHouseCode(realWarehouseCode,coreConfig.getTmsBaseUrl(), coreConfig.getTmsClientId(), TmsConstants.TMS_QUERY_LOGISTOCS);
    }
    private List<TmsLogisticInfoDTO> queryLogisticByOutHouseCode(String outRecordCode, String url, String clientId, String methodName) {

        log.warn(CoreKibanaLog.getJobLog("queryLogisticByOutHouseCode", methodName, "根据仓库外部编码查询物流信息", outRecordCode));
        // 拼接请求url
        url = url + methodName + "/" + outRecordCode;
        // 发送post请求
        CloseableHttpClient client = HttpUtils.getHttpClient();
        HttpGet httpGet = new HttpGet(url);
        httpGet.addHeader("X-Co-Client", clientId);
        HttpResponse response = null;
        try {
            response = client.execute(httpGet);
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            log.info("查询tms物流信息 结果=" + null);
            return null;
        }
        // 解析响应
        HttpEntity entity = response.getEntity();
        JSONObject obj = null;
        try {
            String result = EntityUtils.toString(entity, "utf-8");
            obj = JSONObject.parseObject(result);
            log.info("查询tms物流信息 结果=" + result);
            if (obj.getInteger("code") == 0) {
                List<TmsLogisticInfoDTO> res = new ArrayList<>();
                JSONArray array = obj.getJSONArray("data");
                for(int i = 0 ;i < array.size() ; i++){
                    res.add(JSONObject.toJavaObject(array.getJSONObject(i), TmsLogisticInfoDTO.class));
                }
                return  res;
            } else {
                log.info("查询tms物流信息 结果=" + null);
                return null;
            }

        } catch (Exception e) {
            log.info("查询tms物流信息 结果=" + null);
            return null;
        }
    }

    public TmsLogisticInfoDTO queryLogisticByAddress(AddressE addr, String doCode,String channelCode, String warehouseCode) {
        DoDTO doDTO = new DoDTO();
        doDTO.setDoCode(doCode);
        doDTO.setChannelCode(channelCode);
        doDTO.setGoodReceiverAddress(addr.getAddress());
        doDTO.setGoodReceiverArea(addr.getArea());
        doDTO.setGoodReceiverAreaCode(addr.getAreaCode());
        doDTO.setGoodReceiverCity(addr.getCity());
        doDTO.setGoodReceiverCityCode(addr.getCityCode());
        doDTO.setGoodReceiverCountyCode(addr.getCountyCode());
        doDTO.setGoodReceiverPostcode(addr.getPostcode());
        doDTO.setGoodReceiverMobile(addr.getMobile());
        doDTO.setGoodReceiverName(addr.getName());
        doDTO.setGoodReceiverProvince(addr.getProvince());
        doDTO.setGoodReceiverProvinceCode(addr.getProvinceCode());
        doDTO.setWarehouseCode(warehouseCode);
        return this.queryLogisticByAddress(doDTO);
    }

    public TmsLogisticInfoDTO queryLogisticByAddress(DoDTO dodto ){
        return queryLogisticByAddress(dodto ,coreConfig.getTmsBaseUrl(), coreConfig.getTmsClientId(),"carrier");
    }
    public TmsLogisticInfoDTO queryLogisticByAddress(DoDTO dodto , String url, String clientId, String methodName){
        log.warn(CoreKibanaLog.getJobLog("queryLogisticByAddress", methodName, "根据地址查询物流公司", JSON.toJSONString(dodto)));
        // 拼接请求url
        url = url + methodName;
        // 构造wms交互记录对象
        WmsCallRecordE wmsCallRecordDTO = new WmsCallRecordE();
        wmsCallRecordDTO.setWmsCode(0);
        wmsCallRecordDTO.setRecordCode(dodto.getDoCode());
        wmsCallRecordDTO.setRequestService(methodName);
        wmsCallRecordDTO.setRequestUrl(url);
        wmsCallRecordDTO.setRequestContent(JSONArray.toJSONString(dodto));
        wmsCallRecordDTO.setStatus(0);
        // 发送post请求
        CloseableHttpClient client = HttpUtils.getHttpClient();
        HttpPost httpPost = HttpUtils.createJsonHttpPost(url);
        httpPost.addHeader("X-Co-Client", clientId);
        HttpResponse response = null;
        try {
            httpPost.setEntity(new StringEntity(JSONArray.toJSONString(dodto), "utf-8"));
            response = client.execute(httpPost);
        } catch (Exception e) {
            log.error("包裹【{}】请求tms接口异常", dodto.getDoCode(), e);
            wmsCallRecordDTO.setResponseContent("请求tms接口异常 ==>" + e.getMessage());
            this.saveRecord(wmsCallRecordDTO, client);
            return null;
        }
        // 解析响应
        HttpEntity entity = response.getEntity();
        if (entity == null) {
            wmsCallRecordDTO.setResponseContent("tms响应数据为空");
            this.saveRecord(wmsCallRecordDTO, client);
            return null;
        }
        JSONObject obj = null;
        try {
            String result = EntityUtils.toString(entity, "utf-8");
            log.info("包裹【{}】请求tms接口响应 ==> {}", dodto.getDoCode(), result);
            wmsCallRecordDTO.setResponseContent(result);
            obj = JSONObject.parseObject(result);
        } catch (Exception e) {
            log.error("包裹【{}】解析tms数据异常", dodto.getDoCode(), e);
            if (StringUtils.isNotBlank(wmsCallRecordDTO.getResponseContent())) {
                wmsCallRecordDTO.setResponseContent(wmsCallRecordDTO.getResponseContent() + " == 解析JSON数据异常 ==>" + e.getMessage());
            } else {
                wmsCallRecordDTO.setResponseContent("解析entity数据异常" + e.getMessage());
            }
            this.saveRecord(wmsCallRecordDTO, client);
            return null;
        }

        if (CommonConstants.CODE_SUCCESS.equals(obj.get("code").toString())) {
            wmsCallRecordDTO.setStatus(1);
        }
        // 保存交互记录
        this.saveRecord(wmsCallRecordDTO, client);
        return JSONObject.toJavaObject(obj.getJSONObject("data"), TmsLogisticInfoDTO.class);
    }
    public JSONObject postData(TmsPackageVO packageDTO , String url, String clientId, String methodName) {
        List<TmsPackageVO> res = new ArrayList<>();
        res.add(packageDTO);
        log.warn(CoreKibanaLog.getJobLog("pushToTms", methodName, "推送包裹信息给tms系统", JSON.toJSONString(res)));
        // 拼接请求url
        url = url + methodName;
        // 构造wms交互记录对象
        WmsCallRecordE wmsCallRecordDTO = new WmsCallRecordE();
        wmsCallRecordDTO.setWmsCode(0);
        wmsCallRecordDTO.setRecordCode(packageDTO.getPackageCode());
        wmsCallRecordDTO.setRequestService(methodName);
        wmsCallRecordDTO.setRequestUrl(url);
        wmsCallRecordDTO.setRequestContent(JSONArray.toJSONString(res));
        wmsCallRecordDTO.setStatus(0);
        // 发送post请求
        CloseableHttpClient client = HttpUtils.getHttpClient();
        HttpPost httpPost = HttpUtils.createJsonHttpPost(url);
        httpPost.addHeader("X-Co-Client", clientId);
        HttpResponse response = null;
        try {
            httpPost.setEntity(new StringEntity(JSONArray.toJSONString(res), "utf-8"));
            response = client.execute(httpPost);
        } catch (Exception e) {
            log.error("包裹【{}】请求tms接口异常", packageDTO.getPackageCode(), e);
            wmsCallRecordDTO.setResponseContent("请求tms接口异常 ==>" + e.getMessage());
            this.saveRecord(wmsCallRecordDTO, client);
            return null;
        }
        // 解析响应
        HttpEntity entity = response.getEntity();
        if (entity == null) {
            wmsCallRecordDTO.setResponseContent("tms响应数据为空");
            this.saveRecord(wmsCallRecordDTO, client);
            return null;
        }
        JSONObject obj = null;
        try {
            String result = EntityUtils.toString(entity, "utf-8");
            log.info("包裹【{}】请求tms接口响应 ==> {}", packageDTO.getPackageCode(), result);
            wmsCallRecordDTO.setResponseContent(result);
            obj = JSONObject.parseObject(result);
        } catch (Exception e) {
            log.error("包裹【{}】解析tms数据异常", packageDTO.getPackageCode(), e);
            if (StringUtils.isNotBlank(wmsCallRecordDTO.getResponseContent())) {
                wmsCallRecordDTO.setResponseContent(wmsCallRecordDTO.getResponseContent() + " == 解析JSON数据异常 ==>" + e.getMessage());
            } else {
                wmsCallRecordDTO.setResponseContent("解析entity数据异常" + e.getMessage());
            }
            this.saveRecord(wmsCallRecordDTO, client);
            return null;
        }

        if (CommonConstants.CODE_SUCCESS.equals(obj.get("code").toString())) {
            wmsCallRecordDTO.setStatus(1);
        }
        // 保存交互记录
        this.saveRecord(wmsCallRecordDTO, client);
        return obj;
    }



    public JSONObject postUpdateBillCodeData(TmsPackageVO packageDTO , String url, String clientId, String methodName) {
        log.warn(CoreKibanaLog.getJobLog("pushToTmsUpdateBillCode", methodName, "推送TMS后修改运单号", JSON.toJSONString(packageDTO)));
        // 拼接请求url
        url = url + methodName;
        // 构造wms交互记录对象
        WmsCallRecordE wmsCallRecordDTO = new WmsCallRecordE();
        wmsCallRecordDTO.setWmsCode(0);
        wmsCallRecordDTO.setRecordCode(packageDTO.getPackageCode());
        wmsCallRecordDTO.setRequestService(methodName);
        wmsCallRecordDTO.setRequestUrl(url);
        wmsCallRecordDTO.setRequestContent(JSONObject.toJSONString(packageDTO));
        wmsCallRecordDTO.setStatus(0);
        // 发送post请求
        CloseableHttpClient client = HttpUtils.getHttpClient();
        HttpPost httpPost = HttpUtils.createJsonHttpPost(url);
        httpPost.addHeader("X-Co-Client", clientId);
        HttpResponse response = null;
        try {
            httpPost.setEntity(new StringEntity(JSONObject.toJSONString(packageDTO), "utf-8"));
            response = client.execute(httpPost);
        } catch (Exception e) {
            log.error("包裹【{}】请求tms接口异常", packageDTO.getPackageCode(), e);
            wmsCallRecordDTO.setResponseContent("请求tms接口异常 ==>" + e.getMessage());
            this.saveRecord(wmsCallRecordDTO, client);
            return null;
        }
        // 解析响应
        HttpEntity entity = response.getEntity();
        if (entity == null) {
            wmsCallRecordDTO.setResponseContent("tms响应数据为空");
            this.saveRecord(wmsCallRecordDTO, client);
            return null;
        }
        JSONObject obj = null;
        try {
            String result = EntityUtils.toString(entity, "utf-8");
            log.info("包裹【{}】请求tms接口响应 ==> {}", packageDTO.getPackageCode(), result);
            wmsCallRecordDTO.setResponseContent(result);
            obj = JSONObject.parseObject(result);
        } catch (Exception e) {
            log.error("包裹【{}】解析tms数据异常", packageDTO.getPackageCode(), e);
            if (StringUtils.isNotBlank(wmsCallRecordDTO.getResponseContent())) {
                wmsCallRecordDTO.setResponseContent(wmsCallRecordDTO.getResponseContent() + " == 解析JSON数据异常 ==>" + e.getMessage());
            } else {
                wmsCallRecordDTO.setResponseContent("解析entity数据异常" + e.getMessage());
            }
            this.saveRecord(wmsCallRecordDTO, client);
            return null;
        }

        if (CommonConstants.CODE_SUCCESS.equals(obj.get("code").toString())) {
            wmsCallRecordDTO.setStatus(1);
        }
        // 保存交互记录
        this.saveRecord(wmsCallRecordDTO, client);
        return obj;
    }


    private void saveRecord(WmsCallRecordE wmsCallRecordE, CloseableHttpClient client) {
        if (client != null) {
            try {
                client.close();
            } catch (IOException e) {
                log.error("推送包裹信息给tms 【{}】关闭HttpClient异常，", wmsCallRecordE.getRecordCode(), e);
            }
        }
        // 保存交互记录
        wmsCallRecordRepository.saveWmsCallRecord(wmsCallRecordE);
    }


}
