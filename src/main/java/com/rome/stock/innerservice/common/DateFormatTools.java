package com.rome.stock.innerservice.common;

import com.rome.arch.core.exception.RomeException;
import com.rome.stock.common.utils.DateUtil;
import lombok.extern.slf4j.Slf4j;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.time.temporal.ChronoUnit;
import java.util.Calendar;
import java.util.Date;

/**
 * @Description
 * <AUTHOR>
 * @Date 2019/6/19 11:20
 * @Version 1.0
 */
@Slf4j
public class DateFormatTools {
    public  static String DEFAULT_FORMAT_MODE = "yyyyMMdd";
    public  static String DEFAULT_FORMAT_MODE_DATE = "yyyy-MM-dd HH:mm:ss";
    /**
     * 日期根据格式转换为字符串
     * @param date
     * @param formatStr
     * @return
     */
    public static String dateToString(Date date , String formatStr){
        SimpleDateFormat dateFormat = new SimpleDateFormat(formatStr);
        return dateFormat.format(date);
    }

    /**
     * 将特定格式的日期转换为Date对象
     *
     * @param dateString 特定格式的日期
     * @param format     格式，例如yyyy-MM-dd
     * @return 日期对象
     */
    public static Date parse(String dateString, String format) {
        SimpleDateFormat dateFormat = new SimpleDateFormat(format);
        //强校验
        dateFormat.setLenient(false);
        try {
            return dateFormat.parse(dateString);
        } catch (ParseException e) {
            log.error("Parse " + dateString + " with format " + format + " error!", e);
        }
        return null;
    }
    
    /**
     * 校验,日期时间相同
     * yyyy-MM
     * @param startTime
     * @param endTime
     */
    public static void validateDateEqualByYyyymm(Date startTime, Date endTime) {
    	if(startTime == null || endTime == null) {
    		throw new RomeException(ResCode.STOCK_ERROR_1002, "参数有误,时间不能为空,并且时间范围不能跨月");
    	}
    	SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM");
    	if(!dateFormat.format(startTime).equals(dateFormat.format(endTime))) {
    		throw new RomeException(ResCode.STOCK_ERROR_1002, "参数有误,时间不能为空,并且时间范围不能跨月");
    	}
	}
    
    /**
     * 校验,日期时间相同,按年
     * yyyy
     * @param startTime
     * @param endTime
     */
    public static void validateDateEqualByYyyy(Date startTime, Date endTime) {
    	if(startTime == null || endTime == null) {
    		throw new RomeException(ResCode.STOCK_ERROR_1002, "参数有误,时间不能为空,并且时间范围不能跨年");
    	}
    	SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy");
    	if(!dateFormat.format(startTime).equals(dateFormat.format(endTime))) {
    		throw new RomeException(ResCode.STOCK_ERROR_1002, "参数有误,时间不能为空,并且时间范围不能跨年");
    	}
	}
    /**
     * 日期时间字符串转换为日期时间(java.time.LocalDateTime)
     *
     * @param localDateTimeStr 日期时间字符串
     * @param pattern          日期时间格式 例如DATETIME_PATTERN
     * @return LocalDateTime 日期时间
     */
    public static LocalDateTime parseLocalDateTime(String localDateTimeStr, String pattern) {
        DateTimeFormatter dateTimeFormatter = DateTimeFormatter.ofPattern(pattern);
        return LocalDateTime.parse(localDateTimeStr, dateTimeFormatter);
    }

    /**
     * 根据ChronoUnit计算两个日期之间相隔年数或月数或天数
     *
     * @param start      开始日期
     * @param end        结束日期
     * @param chronoUnit 日期时间单位,(ChronoUnit.YEARS,ChronoUnit.MONTHS,ChronoUnit.WEEKS,ChronoUnit.DAYS)
     * @return long 相隔年数或月数或天数
     */
    public static long getChronoUnitBetween(LocalDate start, LocalDate end, ChronoUnit chronoUnit) {
        return Math.abs(start.until(end, chronoUnit));
    }
    /**
     * 日期字符串转换为日期(java.time.LocalDate)
     *
     * @param localDateStr 日期字符串
     * @param pattern      日期格式 例如DATE_PATTERN
     * @return LocalDate 日期
     */
    public static LocalDate parseLocalDate(String localDateStr, String pattern) {
        DateTimeFormatter dateTimeFormatter = DateTimeFormatter.ofPattern(pattern);
        return LocalDate.parse(localDateStr, dateTimeFormatter);
    }
    public static void main(String[] args) {
        String dateTime = "20200729";
        Date startDate = parse(dateTime , DateFormatTools.DEFAULT_FORMAT_MODE);
        //统计前一天的数据
        startDate = DateUtil.offsiteDate(startDate , Calendar.HOUR,-24);
        System.out.println(dateToString(startDate,DEFAULT_FORMAT_MODE));
    }
}
