//
// Source code recreated from a .class file by IntelliJ IDEA
// (powered by <PERSON><PERSON><PERSON> decompiler)
//

package com.rome.stock.innerservice.common;

import com.rome.arch.core.clientobject.Response;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.http.HttpStatus;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.ResponseStatus;
import org.springframework.web.bind.annotation.RestControllerAdvice;

/**
 * 参数校验全局异常处理类
 * 主要捕获hibernate-validator校验框架抛出的异常问题
 * 返回前端明确的错误信息说明
 *
 * <AUTHOR>
 * @date 2019/04/25
 * @since 0.0.1
 */
@RestControllerAdvice
public class ValidAdvice {
	private static final Logger log = LoggerFactory.getLogger(ValidAdvice.class);

	public ValidAdvice() {
	}

	@ExceptionHandler({org.springframework.web.bind.MethodArgumentNotValidException.class})
	@ResponseStatus(HttpStatus.OK)
	public Response uniteExceptionHandler(org.springframework.web.bind.MethodArgumentNotValidException e) {
		log.error("系统异常", e);
		return Response.builderFail("999", e.getBindingResult().getAllErrors().get(0).getDefaultMessage());
	}

}
