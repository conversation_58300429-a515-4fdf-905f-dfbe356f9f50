package com.rome.stock.innerservice.common;

import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

import javax.annotation.Resource;

import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import com.rome.arch.core.exception.RomeException;
import com.rome.stock.innerservice.api.dto.SkuQtyUnitDTO;
import com.rome.stock.core.constant.StockCoreConsts;
import com.rome.stock.innerservice.domain.entity.SkuQtyUnitBaseE;
import com.rome.stock.innerservice.remote.item.dto.SkuIdUnitCodeDTO;
import com.rome.stock.innerservice.remote.item.dto.SkuUnitExtDTO;
import com.rome.stock.innerservice.remote.item.dto.SkuUnitParamExtDTO;
import com.rome.stock.innerservice.remote.item.dto.UnitAndBaseUnitInfoDTO;
import com.rome.stock.innerservice.remote.item.facade.SkuFacade;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;

import static java.math.BigDecimal.ROUND_DOWN;

@Component
public class SkuQtyUnitTools {

    @Resource
    private SkuFacade skuFacade;


    /**
     * 根据sku明细，批量将sku实际单位及数量转换为库存基础单位及数量
     * 将转换结果分别存储至basicSkuQty、basicUnit、basicUnitCode
     * @param squList sku明细
     */
    public void convertRealToBasic(List<? extends SkuQtyUnitBaseE> squList) {
        convertRealToBasic(squList,skuFacade.getDefaultMerchantId());
    }
    /**
     * 根据sku明细，批量将sku实际单位及数量转换为库存基础单位及数量
     * 将转换结果分别存储至basicSkuQty、basicUnit、basicUnitCode
     * @param squList sku明细
     */
    public void convertRealToBasic(List<? extends SkuQtyUnitBaseE> squList,Long merchantId) {
        Map<SkuIdUnitCode, SkuUnitExtDTO> results = wrapSkuUnitResults(squList,merchantId);
        Iterator<? extends SkuQtyUnitBaseE> iterator = squList.iterator();
        while (iterator.hasNext()) {
            SkuQtyUnitBaseE squ = iterator.next();
            SkuUnitExtDTO sue = results.get(new SkuIdUnitCode(squ.getSkuId(), squ.getUnitCode()));
            // 脏数据过滤，如果该条商品查询结果有误，则直接从结果集中剔除
            if (null == sue || BigDecimal.ZERO.compareTo(sue.getScale()) == 0) {
//                iterator.remove();
                continue;
            }
            if(Objects.nonNull(sue.getScale()) && Objects.nonNull(squ.getSkuQty())){
                BigDecimal basicSkuQty = squ.getSkuQty().multiply(sue.getScale()).setScale(StockCoreConsts.DECIMAL_POINT_NUM, ROUND_DOWN);
                squ.setScale(sue.getScale());
                squ.setBasicSkuQty(basicSkuQty);
            }
            //squ.setSkuQty(squ.getSkuQty());
            //squ.setUnit(squ.getUnit());
            //squ.setUnitCode(squ.getUnitCode());
            squ.setUnit(sue.getUnitName());
            squ.setBasicUnit(sue.getBasicUnitName());
            squ.setBasicUnitCode(sue.getBasicUnitCode());
        }
    }

    /**
     * 根据sku明细，只需要得到换算比例，用于将实际收货数量的基本单位换算成采购单位
     * 无需计算
     *
     * @param squList sku明细
     */
    public void setRealToBasicScale(List<? extends SkuQtyUnitBaseE> squList) {
         this.setRealToBasicScale(squList,skuFacade.getDefaultMerchantId());
    }

    /**
     * 根据sku明细，只需要得到换算比例，用于将实际收货数量的基本单位换算成采购单位
     * 无需计算
     *
     * @param squList sku明细
     */
    public void setRealToBasicScale(List<? extends SkuQtyUnitBaseE> squList,Long merchantId) {
        Map<SkuIdUnitCode, SkuUnitExtDTO> results = wrapSkuUnitResults(squList,merchantId);
        Iterator<? extends SkuQtyUnitBaseE> iterator = squList.iterator();
        while (iterator.hasNext()) {
            SkuQtyUnitBaseE squ = iterator.next();
            SkuUnitExtDTO sue = results.get(new SkuIdUnitCode(squ.getSkuId(), squ.getUnitCode()));
            // 脏数据过滤，如果该条商品查询结果有误，则直接从结果集中剔除
            if (null == sue || BigDecimal.ZERO.compareTo(sue.getScale()) == 0) {
                continue;
            }
            squ.setScale(sue.getScale());
        }
    }

    /**
     * 根据sku明细，批量将库存基础单位及数量转换为sku实际单位及数量
     * 将转换结果分别存储至basicSkuQty、basicUnit、basicUnitCode
     * @param squList sku明细
     */
    public void convertBasicToReal(List<? extends SkuQtyUnitBaseE> squList) {
        this.convertBasicToReal(squList,skuFacade.getDefaultMerchantId());
    }

    /**
     * 根据sku明细，批量将库存基础单位及数量转换为sku实际单位及数量
     * 将转换结果分别存储至basicSkuQty、basicUnit、basicUnitCode
     * @param squList sku明细
     */
    public void convertBasicToReal(List<? extends SkuQtyUnitBaseE> squList,Long merchantId) {
        Map<SkuIdUnitCode, SkuUnitExtDTO> results = wrapSkuUnitResults(squList,merchantId);
        Iterator<? extends SkuQtyUnitBaseE> iterator = squList.iterator();
        while (iterator.hasNext()) {
            SkuQtyUnitBaseE squ = iterator.next();
            SkuUnitExtDTO sue = results.get(new SkuIdUnitCode(squ.getSkuId(), squ.getUnitCode()));
            // 脏数据过滤，如果该条商品查询结果有误，则直接从结果集中剔除
            if (null == sue || BigDecimal.ZERO.compareTo(sue.getScale()) == 0) {
//                iterator.remove();
                continue;
            }
            BigDecimal realSkuQty = squ.getBasicSkuQty().divide(sue.getScale(), StockCoreConsts.DECIMAL_POINT_NUM, ROUND_DOWN);
            squ.setScale(sue.getScale());
            squ.setSkuQty(realSkuQty);
            squ.setUnit(sue.getUnitName());
            squ.setUnitCode(sue.getUnitCode());
            //squ.setBasicSkuQty(squ.getBasicSkuQty());
            squ.setBasicUnit(sue.getBasicUnitName());
            squ.setBasicUnitCode(sue.getBasicUnitCode());
        }
    }

    /**
     * 根据sku明细，批量将库存基础单位及数量转换为sku实际单位及数量
     * 将转换结果分别存储至basicSkuQty、basicUnit、basicUnitCode
     * @param squList sku明细
     */
    public void convertBasicToRealWithRemove(List<? extends SkuQtyUnitBaseE> squList) {
        this.convertBasicToRealWithRemove(squList,skuFacade.getDefaultMerchantId());
    }

    /**
     * 根据sku明细，批量将库存基础单位及数量转换为sku实际单位及数量
     * 将转换结果分别存储至basicSkuQty、basicUnit、basicUnitCode
     * @param squList sku明细
     */
    public void convertBasicToRealWithRemove(List<? extends SkuQtyUnitBaseE> squList,Long merchantId) {
        Map<SkuIdUnitCode, SkuUnitExtDTO> results = wrapSkuUnitResults(squList,merchantId);
        Iterator<? extends SkuQtyUnitBaseE> iterator = squList.iterator();
        while (iterator.hasNext()) {
            SkuQtyUnitBaseE squ = iterator.next();
            SkuUnitExtDTO sue = results.get(new SkuIdUnitCode(squ.getSkuId(), squ.getUnitCode()));
            // 脏数据过滤，如果该条商品查询结果有误，则直接从结果集中剔除
            if (null == sue || BigDecimal.ZERO.compareTo(sue.getScale()) == 0) {
                iterator.remove();
                continue;
            }
            BigDecimal realSkuQty = squ.getBasicSkuQty().divide(sue.getScale(),StockCoreConsts.DECIMAL_POINT_NUM, ROUND_DOWN);
            squ.setScale(sue.getScale());
            squ.setSkuQty(realSkuQty);
            squ.setUnit(sue.getUnitName());
            squ.setUnitCode(sue.getUnitCode());
            squ.setBasicSkuQty(squ.getBasicSkuQty());
            squ.setBasicUnit(sue.getBasicUnitName());
            squ.setBasicUnitCode(sue.getBasicUnitCode());
        }
    }

    /**
     * 包装商品批量查询结果集
     * @param squList
     * @return
     */
    private Map<SkuIdUnitCode, SkuUnitExtDTO> wrapSkuUnitResults(List<? extends SkuQtyUnitBaseE> squList,Long merchantId) {
        List<SkuUnitParamExtDTO> speList = new ArrayList<>();
        SkuUnitParamExtDTO spe;
        for (SkuQtyUnitBaseE squ : squList) {
            if (null != squ.getSkuId() && StringUtils.isNotBlank(squ.getUnitCode())) {
                spe = new SkuUnitParamExtDTO();
                spe.setSkuId(squ.getSkuId());
                spe.setUnitCode(squ.getUnitCode());
                spe.setMerchantId(merchantId);
                speList.add(spe);
            }
    }
        List<SkuUnitExtDTO> sueList = skuFacade.unitsBySkuIdAndUnitCode(speList,merchantId);
        Map<SkuIdUnitCode, SkuUnitExtDTO> resultMap = new HashMap<>();
        if (null == sueList) {
            return resultMap;
        }
        for (SkuUnitExtDTO sue : sueList) {
            resultMap.put(new SkuIdUnitCode(sue.getSkuId(), sue.getUnitCode()), sue);
        }
        return resultMap;
    }

    /**
     * 查询sku基础单位
     * 将转换结果分别存储至basicSkuQty、basicUnit、basicUnitCode
     * @param squList sku明细
     */
    public void queryBasicUnit(List<? extends SkuQtyUnitBaseE> squList) {
        this.queryBasicUnit(squList,skuFacade.getDefaultMerchantId());
    }

    /**
     * 查询sku基础单位，不改变原有单位
     * 将转换结果分别存储至basicSkuQty、basicUnit、basicUnitCode
     * @param squList sku明细
     */
    public void queryBasicUnitWithNoChange(List<? extends SkuQtyUnitBaseE> squList) {
        Map<SkuIdUnitCode, SkuUnitExtDTO> results = wrapSkuUnitResults(squList, skuFacade.getDefaultMerchantId());
        Iterator<? extends SkuQtyUnitBaseE> iterator = squList.iterator();
        while (iterator.hasNext()) {
            SkuQtyUnitBaseE squ = iterator.next();
            SkuUnitExtDTO sue = results.get(new SkuIdUnitCode(squ.getSkuId(), squ.getUnitCode()));
            squ.setUnit(sue.getUnitName());
            squ.setUnitCode(sue.getUnitCode());
            squ.setScale(sue.getScale());
            squ.setBasicUnit(sue.getBasicUnitName());
            squ.setBasicUnitCode(sue.getBasicUnitCode());
        }
    }

    /**
     * 查询sku基础单位
     * 将转换结果分别存储至basicSkuQty、basicUnit、basicUnitCode
     * @param squList sku明细
     */
    public void queryBasicUnit(List<? extends SkuQtyUnitBaseE> squList,Long merchantId) {
        //获取skuCode列表
        List<String> skuCodeList = squList.stream().map(SkuQtyUnitBaseE :: getSkuCode).collect(Collectors.toList());
        List<SkuUnitExtDTO> skuList = skuFacade.querySkuUnits(skuCodeList,merchantId);
        if(skuList == null || skuList.size() == 0 ){
            throw new RomeException(ResCode.STOCK_ERROR_1051,ResCode.STOCK_ERROR_1051_DESC);
        }
        Iterator<? extends SkuQtyUnitBaseE> iterator = squList.iterator();
        while(iterator.hasNext()){
            SkuQtyUnitBaseE skuQty = iterator.next();
            SkuUnitExtDTO skuUnitDTO = skuList.stream().filter(sku -> sku.getSkuId().equals(skuQty.getSkuId()) && sku.getType() == 5 )
                    .findFirst().orElse(null);
            if(skuUnitDTO == null){
                iterator.remove();
                continue;
            }
            skuQty.setUnit(skuUnitDTO.getUnitName());
            skuQty.setUnitCode(skuUnitDTO.getUnitCode());
            skuQty.setScale(skuUnitDTO.getScale());
            skuQty.setBasicUnit(skuUnitDTO.getUnitName());
            skuQty.setBasicUnitCode(skuUnitDTO.getUnitCode());
            skuQty.setBasicSkuQty(skuQty.getSkuQty());
        }
    }

    /**
     * 查询sku基础单位
     * 商品中心无基础单位赋值为空字符串
     * @param squList sku明细
     */
    public void queryBasicUnitNoChecked(List<? extends SkuQtyUnitBaseE> squList) {
        this.queryBasicUnitNoChecked(squList,skuFacade.getDefaultMerchantId());
    }

    /**
     * 查询sku基础单位
     * 商品中心无基础单位赋值为空字符串
     * @param squList sku明细
     */
    public void queryBasicUnitNoChecked(List<? extends SkuQtyUnitBaseE> squList,Long merchantId) {
        //获取skuCode列表
        List<String> skuCodeList = squList.stream().map(SkuQtyUnitBaseE :: getSkuCode).collect(Collectors.toList());
        List<SkuUnitExtDTO> skuList = skuFacade.querySkuUnits(skuCodeList,merchantId);
        if(skuList == null || skuList.size() == 0 ){
            throw new RomeException(ResCode.STOCK_ERROR_1051,ResCode.STOCK_ERROR_1051_DESC);
        }
        Iterator<? extends SkuQtyUnitBaseE> iterator = squList.iterator();
        while(iterator.hasNext()){
            SkuQtyUnitBaseE skuQty = iterator.next();
            SkuUnitExtDTO skuUnitDTO = skuList.stream().filter(sku -> sku.getSkuId().equals(skuQty.getSkuId()) && sku.getType() == 5 )
                    .findFirst().orElse(null);
            if(skuUnitDTO == null){
                skuQty.setUnit("");
                skuQty.setUnitCode("");
                skuQty.setBasicSkuQty(skuQty.getSkuQty());
                continue;
            }
            skuQty.setUnit(skuUnitDTO.getUnitName());
            skuQty.setUnitCode(skuUnitDTO.getUnitCode());
            skuQty.setBasicSkuQty(skuQty.getSkuQty());
        }
    }

    /**
     * 根据sku明细，批量将库存基础单位及数量转换为sku实际单位及数量
     * 将转换结果分别存储至basicSkuQty、basicUnit、basicUnitCode
     * @param squList sku明细
     */
    public void convertBasicForQueryStock(List<SkuQtyUnitDTO> squList, Map<SkuIdUnitCodeDTO, UnitAndBaseUnitInfoDTO> unitMap) {
        Iterator<? extends SkuQtyUnitBaseE> iterator = squList.iterator();
        while (iterator.hasNext()) {
            SkuQtyUnitBaseE squ = iterator.next();
            UnitAndBaseUnitInfoDTO sue = unitMap.get(new SkuIdUnitCodeDTO(squ.getSkuId(), squ.getUnitCode()));
            // 脏数据过滤，如果该条商品查询结果有误，则直接从结果集中剔除
            if (null == sue || BigDecimal.ZERO.compareTo(sue.getScale()) == 0) {
                iterator.remove();
                continue;
            }
            BigDecimal realSkuQty = squ.getBasicSkuQty().divide(sue.getScale(),StockCoreConsts.DECIMAL_POINT_NUM, ROUND_DOWN);
            squ.setScale(sue.getScale());
            squ.setSkuQty(realSkuQty);
            squ.setUnit(sue.getUnitName());
            squ.setUnitCode(sue.getUnitCode());
            squ.setBasicSkuQty(squ.getBasicSkuQty());
            squ.setBasicUnit(sue.getBasicUnitName());
            squ.setBasicUnitCode(sue.getBasicUnitCode());
        }
    }

    @Data
    @AllArgsConstructor
    @EqualsAndHashCode(callSuper = false)
    public class SkuIdUnitCode {
        private Long skuId;
        private String unitCode;
    }

}
