/**
 * Filename RemoteTimeLogAspect.java
 * Company 上海来伊份科技有限公司。
 * <AUTHOR>
 * @version 
 */
package com.rome.stock.innerservice.common;

import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.RequestMapping;
import com.alibaba.fastjson.JSONObject;
import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 * @since 2019年9月10日 下午6:14:54
 */
@Slf4j
@Aspect
@Component
public class RemoteTimeLogAspect {

    @Around(value = "(execution(* com.rome.stock.innerservice.remote..*.*(..))) && @annotation(mLog)")
    public Object around(ProceedingJoinPoint point, RequestMapping mLog) throws Throwable {
    	Object result = null;
        long time = System.currentTimeMillis();
        try {
            result = point.proceed();
            return result;
        } catch (Throwable e) {
            throw e;
        } finally {
            // 异步记录日志
            time = System.currentTimeMillis() - time;
            if(time >= 2000) {
            	try {
            		@SuppressWarnings("unchecked")
					FeignClient f = (FeignClient) point.getSignature().getDeclaringType().getAnnotation(FeignClient.class);
            		if(f != null && !(f instanceof FeignClient)) {
            			f = null;
            		}
                    Object[] args = point.getArgs();
                    StringBuffer params = new StringBuffer("");
                    for(int i= 0; i < args.length; i++) {
                        if(i != 0) {
                            params.append("--");
                        }
                        params.append(JSONObject.toJSONString(args[i]));
                    }
                    String desc = params.toString();
                    JSONObject jsonObject = new JSONObject();
                    jsonObject.put("timeout", time);
                    jsonObject.put("serverpool", f == null ? "" : f.value());
                    log.warn(CoreKibanaLog.getJobLog("remoteTimeLog", JSONObject.toJSONString(mLog.value()), desc, jsonObject));
				} catch (Throwable e2) {
				}
            }
        }
    }
}
