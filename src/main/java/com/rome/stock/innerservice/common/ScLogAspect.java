package com.rome.stock.innerservice.common;

import com.alibaba.fastjson.JSONObject;
import com.rome.stock.innerservice.domain.repository.WmsCallRecordRepository;
import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Pointcut;
import org.aspectj.lang.reflect.MethodSignature;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.lang.annotation.Annotation;
import java.lang.reflect.Method;

/**
 * @Description: 全局日志处理
 * <p>
 * @Author: chuwenchao  2019/6/14
 */
@Slf4j
@Aspect
@Component
public class ScLogAspect {
    @Autowired
    private WmsCallRecordRepository wmsCallRecordRepository;

    // 切点为有api.controller中所有方法
    @Pointcut("execution(* com.rome.stock.innerservice.api.controller..*.*(..))")
    public void logPointCut() {

    }

    @Around(value = "logPointCut()")
    public Object around(ProceedingJoinPoint point) throws Throwable {
        Object result = null;
        long time = System.currentTimeMillis();
        try {
            result = point.proceed();
            return result;
        } catch (Exception e) {
            throw e;
        } finally {
            // 异步记录日志
            time = System.currentTimeMillis() - time;
            long timeT = System.currentTimeMillis();
            // 获取方法签名
            MethodSignature signature = (MethodSignature) point.getSignature();
            Method method = signature.getMethod();
            // 判断是否是指定注解
            boolean flag = false;
            Annotation[] annotations = method.getDeclaredAnnotations();
            for(Annotation annotation : annotations) {
                String name = annotation.annotationType().getName();
                if(name.endsWith("RequestMapping")) {
                    flag = true;
                    break;
                } else if(name.endsWith("GetMapping")) {
                    flag = true;
                    break;
                } else if(name.endsWith("PostMapping")) {
                    flag = true;
                    break;
                }
            }
            if(flag && time >= 3000) {
                Object[] args = point.getArgs();
                StringBuffer params = new StringBuffer("");
                for(int i= 0; i < args.length; i++) {
                    if(i != 0) {
                        params.append("--");
                    }
                    params.append(JSONObject.toJSONString(args[i]));
                }
                timeT = System.currentTimeMillis() - timeT;
                JSONObject jsonObject = new JSONObject();
                jsonObject.put("timeout", time);
                String desc = "方法【"+ method.getName() +"】执行耗时【" + time + "】解析参数耗时【" + timeT + "】,参数 ==> " + params.toString();
                log.warn(CoreKibanaLog.getJobLog("timeLog", method.getName(), desc, jsonObject));
            }
        }
    }

}
