package com.rome.stock.innerservice.common;

/**
 * Http请求返回
 */
public class RespEntity {

	private String result;
	private boolean success;

	public RespEntity() {
		super();
	}

	public RespEntity(String result, boolean success) {
		super();
		this.result = result;
		this.success = success;
	}

	public String getResult() {
		return result;
	}

	public void setResult(String result) {
		if (result != null) {
			this.result = result;
		}
	}

	public boolean isSuccess() {
		return success;
	}

	public void setSuccess(boolean success) {
		this.success = success;
	}

	public static RespEntity success(String result) {
		return new RespEntity(result, true);
	}

	public static RespEntity failure(String result) {
		return new RespEntity(result, false);
	}

}
