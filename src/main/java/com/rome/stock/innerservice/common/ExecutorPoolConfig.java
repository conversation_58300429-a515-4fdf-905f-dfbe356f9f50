package com.rome.stock.innerservice.common;

import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.scheduling.annotation.EnableAsync;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;

import java.util.concurrent.ThreadPoolExecutor;

/**
 * @Description: 线程池工具
 * <p>
 * @Author: chuwenchao  2019/6/13
 */
@Slf4j
@EnableAsync
@Configuration
public class ExecutorPoolConfig {

    @Bean("coreStockTask")
    public ThreadPoolTaskExecutor asyncExecute() {
        log.warn("execute start...");
        ThreadPoolTaskExecutor executor = new ThreadPoolTaskExecutor();
        // 核心线程数
        executor.setCorePoolSize(16);
        // 最大线程数
        executor.setMaxPoolSize(300);
        // 配置缓存队列数
        executor.setQueueCapacity(3000);
        // 配置最大空闲时间
        executor.setKeepAliveSeconds(60);
        // 前缀
        executor.setThreadNamePrefix("stock-thread-");
        // 线程池对拒绝任务的处理策略
        executor.setRejectedExecutionHandler(new ThreadPoolExecutor.CallerRunsPolicy());
        executor.setWaitForTasksToCompleteOnShutdown(true);
        executor.setAwaitTerminationSeconds(60);

        return executor;
    }

}
