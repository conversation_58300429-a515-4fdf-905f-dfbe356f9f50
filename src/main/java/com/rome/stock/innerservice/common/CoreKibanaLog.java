package com.rome.stock.innerservice.common;

import com.alibaba.fastjson.JSONObject;
import com.rome.stock.common.model.KibanaLog;

/**
 * @Description: Stock-core日志
 * <p>
 * @Author: sunyj  2019/7/21
 */
public class CoreKibanaLog extends KibanaLog {

    private static final String PREF = "stockCore-";

    /**
     * @Description: 设置日志 <br>
     *
     * <AUTHOR> 2019/7/21
     * @param logType
     * @return
     */
    public static String getJobLog(String logType, String method, String desc, Object param) {
        CoreKibanaLog logObj = new CoreKibanaLog();
        logObj.setLogType(PREF + logType);
//        logObj.setDate(new Date());
        logObj.setMethod(method);
        logObj.setDesc(desc);
        logObj.setParam(param);
        return JSONObject.toJSONString(logObj);
    }
}
