/**
 * Filename MqProducerClientTools.java
 * Company 上海来伊份科技有限公司。
 *
 * <AUTHOR>
 * @version
 */
package com.rome.stock.innerservice.common;

import com.rome.scm.common.rocketmq.CustomRocketMQProducerClient;
import lombok.extern.slf4j.Slf4j;
import org.springframework.transaction.support.TransactionSynchronization;
import org.springframework.transaction.support.TransactionSynchronizationManager;

import java.util.function.Supplier;

/**
 * mq生产者工具，后期会收录到包里，这里是临时使用
 * <AUTHOR>
 * @since 2024/6/24 11:49
 */
@Slf4j
public final class MqProducerClientTools {

    /**
     * 发送mq，在事务提交后调用callback回调函数
     * @param content
     * @param configMQCode
     * @param businessNo
     * @param msgKey
     * @param callback
     */
    public static void sendByTransactionAfterCallback(String content, String configMQCode, String businessNo, String msgKey, Supplier<Boolean> callback) {
        InnerCallbackTransactionSynchronization synchronization = new InnerCallbackTransactionSynchronization(callback);
        TransactionSynchronizationManager.registerSynchronization(synchronization);
        CustomRocketMQProducerClient.send(content, configMQCode, businessNo, msgKey);
    }

    /**
     * 内部事务同步类
     */
    private static class InnerCallbackTransactionSynchronization implements TransactionSynchronization {

        /**
         * 回调函数
         */
        private Supplier<Boolean> callback;

        public InnerCallbackTransactionSynchronization(Supplier<Boolean> callback) {
            this.callback = callback;
        }

        /**
         * 事务提交后调用
         */
        @Override
        public void afterCommit() {
            try{
                this.callback.get();
            } catch (Throwable e) {
                log.error("回调异常", e);
            }
        }
    }

}
