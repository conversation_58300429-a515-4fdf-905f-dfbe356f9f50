/**
 * Filename KibanaLogUtils.java
 * Company 上海来伊份科技有限公司。
 * <AUTHOR>
 * @version 
 */
package com.rome.stock.innerservice.common;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;
import java.util.Map;

import com.alibaba.fastjson.JSONObject;
import com.rome.stock.common.model.StockOrderKibanaLog;
import com.rome.stock.innerservice.api.dto.NoticeChannelSalesAvailableStockDTO;
import com.rome.stock.innerservice.infrastructure.dataobject.RealWarehouseStockDO;
import com.rome.stock.innerservice.infrastructure.dataobject.RwStockChangeFlowDO;
import com.rome.stock.innerservice.infrastructure.dataobject.VwStockChangeFlowDO;
import com.rome.stock.core.infrastructure.dataobject.core.CoreGroupStockDO;
import com.rome.stock.core.infrastructure.dataobject.core.CoreRealWarehouseStockDO;
import com.rome.stock.core.infrastructure.dataobject.core.CoreVirtualWarehouseStockDO;
import com.rome.stock.core.infrastructure.redis.StockCheckErrorEnum;
import lombok.extern.slf4j.Slf4j;

/**
 * kibana统计信息
 * <AUTHOR>
 * @since 2019年6月3日 下午5:07:56
 */
@Slf4j
public class KibanaLogUtils {

	/**
	 * 日志类型前辍prefix
	 */
	private static final String LOG_TYPE_PRE = "rome-stock-";
	
	/**
	 * 日志类型,库存数量核对
	 */
	private static final String LOG_TYPE_STOCK_NUM_CHECK = LOG_TYPE_PRE + "numcheck";
	
	/**
	 * 日志类型,库存数量核对,汇总
	 * 它是记录仓开始，结束，成功个数的
	 */
	private static final String LOG_TYPE_STOCK_NUM_CHECK_COUNT = LOG_TYPE_PRE + "numcheck-count";
	
	/**
	 * 日志类型,库存操作流水
	 */
	private static final String LOG_TYPE_STOCK_FLOW = LOG_TYPE_PRE + "flow";
	
	/**
	 * 日志类型,库存查询
	 */
	private static final String LOG_TYPE_STOCK_QUERY = LOG_TYPE_PRE + "query";
	
	/**
	 * 类型,库存查询,渠道可售库存
	 */
	public static final String TYPE_STOCK_QUERY_1 = "1";
	
	/**
	 * 类型,库存查询,渠道详细库存
	 */
	public static final String TYPE_STOCK_QUERY_2 = "2";
	
	/**
	 * 类型,库存查询,策略组库存
	 */
	public static final String TYPE_STOCK_QUERY_3 = "3";
	
	/**
	 * 类型,库存查询,虚仓库存
	 */
	public static final String TYPE_STOCK_QUERY_4 = "4";
	
	/**
	 * 类型,库存查询,实仓库存
	 */
	public static final String TYPE_STOCK_QUERY_5 = "5";
	
	/**
	 * 日志类型,库存冷数据处理Job日志
	 */
	private static final String LOG_TYPE_STOCK_INACTIVE_DATA_JOB = LOG_TYPE_PRE + "inactive-data-job";
	
	/**
	 * 日志类型,库存活跃数据到es处理Job日志
	 */
	private static final String LOG_TYPE_STOCK_ACTIVE_DATA_TO_ES_JOB = LOG_TYPE_PRE + "active-data-toes-job";
	
	/**
	 * 日志类型,批次库存与库存数量核对
	 */
	private static final String LOG_TYPE_BATCH_STOCK_NUM_CHECK = LOG_TYPE_PRE + "batchnumcheck";
	
	/**
	 * 日志类型,批次库存与库存数量核对,汇总
	 * 它是记录仓开始，结束，成功个数的
	 */
	private static final String LOG_TYPE_BATCH_STOCK_NUM_CHECK_COUNT = LOG_TYPE_PRE + "batchnumcheck-count";

	/**
	 * 商品进货权限同步总数
	 */
	private static final String LOG_TYPE_STOCK_SKU_PERMIT_SYNC_COUNT = LOG_TYPE_PRE + "skupermitsync-count";

	/**
	 * 日志类型，记录库存各种单据信息
	 */
	private static final String LOG_TYPE_RECORD_INFO="stockcore-recordInfo";
	
	/**
	 * 日志类型,生成进销存数据和前一天相比有差异监控
	 */
	private static final String LOG_TYPE_CREATE_ENTRY_SALE_DIFF_QTY = LOG_TYPE_PRE + "create-entry-sale-stock-diff";
	
	/**
	 * 日志类型,库存变化通知,渠道有无可用库存通知
	 */
	private static final String LOG_TYPE_STOCK_CHANGE_NOTICE_CHANNEL_AVAILABLE = LOG_TYPE_PRE + "stock-change-notice-channel-avail";
	
	/**
	 * 日志类型,批次库存处理Job日志
	 */
	private static final String LOG_TYPE_BATCH_STOCK_RUN_JOB = LOG_TYPE_PRE + "batch-stock-run-job";
	
	/**
	 * 日志类型,电商寻源信息日志
	 */
	private static final String LOG_TYPE_ONLINE_ROUTE_INFO = LOG_TYPE_PRE + "online-route-info";

	/**
	 * 库存数量核对,实仓
	 * @return
	 */
	public static String getStockNumCheckByRealWarehouse(StockCheckErrorEnum errorEnum, CoreRealWarehouseStockDO coreRedisDo, CoreRealWarehouseStockDO coreDbDo) {
		return getStockNumCheckByRealWarehouse(errorEnum, coreRedisDo, coreDbDo, 1);
	}
	
	/**
	 * 库存数量核对,实仓-虚实总和对不上
	 * @return
	 */
	public static String getStockNumCheckByRealVirtalMatch(StockCheckErrorEnum errorEnum, CoreRealWarehouseStockDO coreDbDo) {
		return getStockNumCheckByRealWarehouse(errorEnum, null, coreDbDo, 2);
	}
	
	/**
	 * 库存数量核对,实仓
	 * @param errorEnum 
	 * @param coreRedisDo redis数据
	 * @param coreDbDo 数据库中数据
	 * @param type 类型，如1-实仓库存，2-虚实总和对不上
	 * @return
	 */
	private static String getStockNumCheckByRealWarehouse(StockCheckErrorEnum errorEnum, CoreRealWarehouseStockDO coreRedisDo, CoreRealWarehouseStockDO coreDbDo, int type) {
		JSONObject jsonObject=new JSONObject();
		jsonObject.put("logType", LOG_TYPE_STOCK_NUM_CHECK); // 日志类型
		jsonObject.put("type", type); // 类型,实仓
		jsonObject.put("errortype", errorEnum.getType()); // 错误类型
		jsonObject.put("errordesc", errorEnum.getDesc()); // 错误类型描述
		jsonObject.put("relationId", coreDbDo.getRealWarehouseId()); // 关系Id 值为实仓Id(type=1、2)或虚仓Id(type=3)或策略组Id(type=4)
		jsonObject.put("skuId", coreDbDo.getSkuId());
		jsonObject.put("dbrealQty", coreDbDo.getRealQty()); // 真实库存
		jsonObject.put("dblockQty", coreDbDo.getLockQty()); // 锁定库存
		jsonObject.put("dbonroadQty", coreDbDo.getOnroadQty()); // 在途库存
		jsonObject.put("dbqualityQty", coreDbDo.getQualityQty()); // 质检库存
		jsonObject.put("dbunqualifiedQty", coreDbDo.getUnqualifiedQty()); // 不合格库存
		// redis库存
		if(coreRedisDo != null) {
			jsonObject.put("realQty", coreRedisDo.getRealQty()); // 真实库存
			jsonObject.put("lockQty", coreRedisDo.getLockQty()); // 锁定库存
			jsonObject.put("onroadQty", coreRedisDo.getOnroadQty()); // 在途库存
			jsonObject.put("qualityQty", coreRedisDo.getQualityQty()); // 质检库存
			jsonObject.put("unqualifiedQty", coreRedisDo.getUnqualifiedQty()); // 不合格库存
		}
		return jsonObject.toJSONString();
	}
	
	/**
	 * 库存数量核对,虚仓
	 * @param errorEnum 
	 * @param coreRedisDo redis数据
	 * @param coreDbDo 数据库中数据
	 * @return
	 */
	public static String getStockNumCheckByVirtualWarehouse(StockCheckErrorEnum errorEnum, CoreVirtualWarehouseStockDO coreRedisDo, CoreVirtualWarehouseStockDO coreDbDo) {
		JSONObject jsonObject=new JSONObject();
		jsonObject.put("logType", LOG_TYPE_STOCK_NUM_CHECK); // 日志类型
		jsonObject.put("type", 3); // 类型,虚仓
		jsonObject.put("errortype", errorEnum.getType()); // 错误类型
		jsonObject.put("errordesc", errorEnum.getDesc()); // 错误类型描述
		jsonObject.put("relationId", coreDbDo.getVirtualWarehouseId()); // 关系Id 值为实仓Id(type=1、2)或虚仓Id(type=3)或策略组Id(type=4)
		jsonObject.put("skuId", coreDbDo.getSkuId());
		jsonObject.put("dbrealQty", coreDbDo.getRealQty()); // 真实库存
		jsonObject.put("dblockQty", coreDbDo.getLockQty()); // 锁定库存
		// redis库存
		if(coreRedisDo != null) {
			jsonObject.put("realQty", coreRedisDo.getRealQty()); // 真实库存
			jsonObject.put("lockQty", coreRedisDo.getLockQty()); // 锁定库存
		}
		return jsonObject.toJSONString();
	}
	
	/**
	 * 库存数量核对,策略组
	 * @param errorEnum 
	 * @param coreRedisDo redis数据
	 * @param coreDbDo 数据库中数据
	 * @return
	 */
	public static String getStockNumCheckByGroup(StockCheckErrorEnum errorEnum, CoreGroupStockDO coreRedisDo, CoreGroupStockDO coreDbDo) {
		JSONObject jsonObject=new JSONObject();
		jsonObject.put("logType", LOG_TYPE_STOCK_NUM_CHECK); // 日志类型
		jsonObject.put("type", 4); // 类型,策略组
		jsonObject.put("errortype", errorEnum.getType()); // 错误类型
		jsonObject.put("errordesc", errorEnum.getDesc()); // 错误类型描述
		jsonObject.put("relationId", coreDbDo.getGroupId()); // 关系Id 值为实仓Id(type=1、2)或虚仓Id(type=3)或策略组Id(type=4)
		jsonObject.put("skuId", coreDbDo.getSkuId());
		jsonObject.put("dbrealQty", coreDbDo.getRealQty()); // 真实库存
		jsonObject.put("dblockQty", coreDbDo.getLockQty()); // 锁定库存
		// redis库存
		if(coreRedisDo != null) {
			jsonObject.put("realQty", coreRedisDo.getRealQty()); // 真实库存
			jsonObject.put("lockQty", coreRedisDo.getLockQty()); // 锁定库存
		}
		return jsonObject.toJSONString();
	}
	
	/**
	 * 库存数量核对,汇总
	 * @param relationId 关系Id 值为实仓Id(type=1)或虚仓Id(type=3)或策略组Id(type=4)
	 * @param type  类型    实仓Id(type=1)或虚仓Id(type=3)或策略组Id(type=4)
	 * @param countflag  0 为核对开始  1为核对结束  
	 * @param equalcount  db与redis 相同的总数
	 * @return
	 */
	public static String getStockNumCheckCount(Long relationId, int type, int countflag, int equalcount) {
		JSONObject jsonObject=new JSONObject();
		jsonObject.put("logType", LOG_TYPE_STOCK_NUM_CHECK_COUNT); // 日志类型
		jsonObject.put("type", type); // 类型,策略组
		jsonObject.put("relationId", relationId); // 关系Id 值为实仓Id(type=1)或虚仓Id(type=3)或策略组Id(type=4)
		jsonObject.put("countflag", countflag);
		jsonObject.put("equalcount", equalcount); // 相同总数
		return jsonObject.toJSONString();
	}
	
	/**
	 * 实仓库存操作流水
	 * @param changeFlowDO
	 * @return
	 */
	public static String getStockTransactionFlow(RwStockChangeFlowDO changeFlowDO) {
		return getStockTransactionFlow(changeFlowDO.getRecordCode(), 1, changeFlowDO.getRealWarehouseId(), changeFlowDO.getChannelCode(), changeFlowDO.getMerchantId(), changeFlowDO.getSkuId(), changeFlowDO.getSkuCode(), changeFlowDO.getBeforeChangeQty(), changeFlowDO.getAfterChangeQty(), changeFlowDO.getChangeQty(), changeFlowDO.getStockType(), changeFlowDO.getTransType(), changeFlowDO.getStockId());
	}
	
	/**
	 * 虚仓库存操作流水
	 * @param changeFlowDO
	 * @return
	 */
	public static String getStockTransactionFlow(VwStockChangeFlowDO changeFlowDO) {
		return getStockTransactionFlow(changeFlowDO.getRecordCode(), 2, changeFlowDO.getVirtualWarehouseId(), changeFlowDO.getChannelCode(), changeFlowDO.getMerchantId(), changeFlowDO.getSkuId(), changeFlowDO.getSkuCode(), changeFlowDO.getBeforeChangeQty(), changeFlowDO.getAfterChangeQty(), changeFlowDO.getChangeQty(), changeFlowDO.getStockType(), changeFlowDO.getTransType(), changeFlowDO.getStockId());
	}
	
	/**
	 * 库存操作流水
	 * @param dto
	 * @return
	 */
	public static String getStockTransactionFlow(String recordCode, int type, Long relationId, String channelCode,
			Long merchantId, Long skuId, String skuCode, BigDecimal beforeChangeQty, BigDecimal afterChangeQty, BigDecimal changeQty, Integer stockType, Integer transType, Long stockId) {
		StringBuilder result=new StringBuilder();
		result.append("{\"logType\":\"").append(LOG_TYPE_STOCK_FLOW).append("\"") //日志类型
		      .append(",\"type\":").append(type)   //类型 1-实仓库存 2-虚仓库存 3-策略组库存
		      .append(",\"recordCode\":\"").append(recordCode).append("\"") //关联单号
		      .append(",\"relationId\":").append(relationId)   // 关系Id 值为实仓Id(type=1)或虚仓Id(type=2)或策略组Id(type=3)
		      .append(",\"channelCode\":\"").append(channelCode).append("\"") //渠道ID
		      .append(",\"merchantId\":").append(merchantId)   //商家id
		      .append(",\"skuId\":").append(skuId)   //商品sku编码
		      .append(",\"skuCode\":\"").append(skuCode).append("\"") // 商品编码
		      .append(",\"beforeChangeQty\":").append(beforeChangeQty)   //变更前库存数量
		      .append(",\"afterChangeQty\":").append(afterChangeQty)   //变更后库存数量
		      .append(",\"changeQty\":").append(changeQty)   //变更数量
		      .append(",\"stockType\":").append(stockType)   //库存类型
		      .append(",\"transType\":").append(transType)   //库存交易类型
		      .append(",\"stockId\":").append(stockId)   //实物库存id
//		      .append(",\"sourceSystem\":\"").append(remark).append("\"")
		      .append("}");
		return result.toString();
	}

	/**
	 * 获取库存数量查询,kibana统计信息
	 * @param type
	 * @param num
	 * @param time
	 * @return
	 */
	public static String getStockQuery(String type,int num,long time) {
		StringBuilder result=new StringBuilder();
		result.append("{\"logType\":\"").append(LOG_TYPE_STOCK_QUERY).append("\"") //日志类型
		      .append(",\"type\":").append(type)   //类型
		      .append(",\"num\":").append(num)   //操作数量
		      .append(",\"time\":").append(time)   //类型
		      .append("}");
		return result.toString();
	}
	
	/**
	 * 库存冷数据处理Job日志
	 * @param templateName
	 * @param desc
	 * @param num
	 * @param time
	 * @return
	 */
	public static String getStockInactiveDataJob(String templateName, String desc, int num, long time) {
		JSONObject jsonObject=new JSONObject();
		jsonObject.put("logType", LOG_TYPE_STOCK_INACTIVE_DATA_JOB); // 日志类型
		jsonObject.put("templateName", templateName); // 模板名
		jsonObject.put("desc", desc); // 描述
		jsonObject.put("num", num); // 处理数量
		jsonObject.put("time", time); // 时间
		return jsonObject.toJSONString();
	}
	
	/**
	 * 库存活跃数据到es处理Job日志
	 * @param templateName
	 * @param desc
	 * @param num
	 * @param time
	 * @return
	 */
	public static String getStockActiveDataToEsJob(String templateName, String desc, int num, long time) {
		JSONObject jsonObject=new JSONObject();
		jsonObject.put("logType", LOG_TYPE_STOCK_ACTIVE_DATA_TO_ES_JOB); // 日志类型
		jsonObject.put("templateName", templateName); // 模板名
		jsonObject.put("desc", desc); // 描述
		jsonObject.put("num", num); // 处理数量
		jsonObject.put("time", time); // 时间
		return jsonObject.toJSONString();
	}
	
	/**
	 * 批次库存与库存数量核对,汇总
	 * @param relationId 关系Id 值为实仓Id
	 * @param countflag  0 为核对开始  1为核对结束  
	 * @param equalcount  批次库存与库存 相同的总数
	 * @return
	 */
	public static String getBatchStockNumCheckCount(Long relationId, int countflag, int equalcount,int count) {
		JSONObject jsonObject=new JSONObject();
		jsonObject.put("logType", LOG_TYPE_BATCH_STOCK_NUM_CHECK_COUNT); // 日志类型
		jsonObject.put("relationId", relationId); // 关系Id 值为实仓Id
		jsonObject.put("countflag", countflag);
		jsonObject.put("equalcount", equalcount); // 相同总数
		jsonObject.put("count", count); // 总数
		return jsonObject.toJSONString();
	}
	
	/**
	 * 批次库存与库存数量核对,实仓
	 * @param batchQty 批次库存
	 * @param batchLockQty 批次库存锁定量
	 * @param stockDO 库存do
	 * @return
	 */
	public static String getBatchStockNumCheck(BigDecimal batchQty, BigDecimal batchLockQty, RealWarehouseStockDO stockDO) {
		JSONObject jsonObject=new JSONObject();
		jsonObject.put("logType", LOG_TYPE_BATCH_STOCK_NUM_CHECK); // 日志类型
		jsonObject.put("relationId", stockDO.getRealWarehouseId()); // 关系Id 值为实仓Id
		jsonObject.put("skuId", stockDO.getSkuId());
		jsonObject.put("realQty", stockDO.getRealQty()); // 真实库存
		jsonObject.put("batchQty", batchQty); // 批次库存
		jsonObject.put("lockQty", stockDO.getLockQty()); // 锁定库存
		jsonObject.put("batchLockQty", batchLockQty); // 批次锁定库存
		return jsonObject.toJSONString();
	}

	/**
	 * 商品进货权限同步总数
	 * @param relationId 关系Id 值为策略组Id
	 * @param countflag 0为核对开始  1为核对结束
	 * @param count 同步总数
	 * @return
	 */
	public static String getSkuPermitSyncCount(Long relationId, int countflag, int count) {
		JSONObject jsonObject=new JSONObject();
		jsonObject.put("logType", LOG_TYPE_STOCK_SKU_PERMIT_SYNC_COUNT); // 日志类型
		jsonObject.put("relationId", relationId); // 关系Id 值为策略组Id
		jsonObject.put("countflag", countflag);
		jsonObject.put("count", count); // 总数
		return jsonObject.toJSONString();
	}

	/**
	 * 输入kibana日志信息
	 * @param orderNo
	 * @param channelCode
	 * @param type
	 * @param desc
	 * @param method
	 * @param param
	 * @return
	 */
	public static void printKibanaRecordInfo(String orderNo,String channelCode,Integer type,String desc,String method,Object param){
		StockOrderKibanaLog stockOrderKibanaLog=new StockOrderKibanaLog();
		stockOrderKibanaLog.setRecordType(type);
		stockOrderKibanaLog.setChannelCode(channelCode);
		stockOrderKibanaLog.setOrderNo(orderNo);
		stockOrderKibanaLog.setDate(new Date());
		stockOrderKibanaLog.setMethod(method);
		stockOrderKibanaLog.setDesc(desc);
		stockOrderKibanaLog.setParam(param);
		stockOrderKibanaLog.setLogType(LOG_TYPE_RECORD_INFO);
		log.warn(JSONObject.toJSONString(stockOrderKibanaLog));
	}
	
	/**
	 * 生成进销存数据和前一天相比有差异监控,
	 * 一般是生成进销存期初和前一天的期末相比
	 * @param realWarehouseId 实仓Id
	 * @param skuId skuId
	 * @param entrySaleDateStr 生成进销存日期 格式：yyyy-MM-dd
	 * @param initialQty 期初
	 * @param preFinalQty 前一天的期末
	 * @param diffQty 差异量
	 * @return
	 */
	public static String getCreateEntrySaleStockPreDiff(Long realWarehouseId, Long skuId, String entrySaleDateStr, BigDecimal initialQty, BigDecimal preFinalQty, BigDecimal diffQty) {
		JSONObject jsonObject=new JSONObject();
		jsonObject.put("logType", LOG_TYPE_CREATE_ENTRY_SALE_DIFF_QTY); // 日志类型
		jsonObject.put("realWarehouseId", realWarehouseId); // 实仓Id
		jsonObject.put("skuId", skuId); // skuId
		jsonObject.put("entrySaleDate", entrySaleDateStr); // 生成进销存日期
		jsonObject.put("initialQty", initialQty); // 期初
		jsonObject.put("preFinalQty", preFinalQty); // 前一天的期末
		jsonObject.put("diffQty", diffQty); // 差异量
		return jsonObject.toJSONString();
	}
	
	/**
	 * 库存变化通知,渠道有无可用库存通知
	 * @param isCombine 是否是组合品
	 * @param sendMQResult 发送mq结果
	 * @param list 发送内容
	 * @return
	 */
	public static String getStockChangeNoticeChannelAvailable(boolean isCombine, boolean sendMQResult, List<NoticeChannelSalesAvailableStockDTO> list) {
		JSONObject jsonObject=new JSONObject();
		jsonObject.put("logType", LOG_TYPE_STOCK_CHANGE_NOTICE_CHANNEL_AVAILABLE); // 日志类型
		jsonObject.put("combine", isCombine ? 1 : 0); // 是否组合品 0-否 1-是
		jsonObject.put("sendMQ", sendMQResult ? 1 : 0); // 发送mq 0-失败 1-成功 
		jsonObject.put("content", list); // 发送内容
		return jsonObject.toJSONString();
	}
	
	/**
	 * 批次库存处理Job日志
	 * @param id
	 * @param desc
	 * @param num
	 * @param time
	 * @return
	 */
	public static String getBatchStockRunJob(String id, String desc, int num, long time) {
		JSONObject jsonObject=new JSONObject();
		jsonObject.put("logType", LOG_TYPE_BATCH_STOCK_RUN_JOB); // 日志类型
		jsonObject.put("id", id); // id
		jsonObject.put("desc", desc); // 描述
		jsonObject.put("num", num); // 处理数量
		jsonObject.put("time", time); // 时间
		return jsonObject.toJSONString();
	}
	
	/**
	 * 电商寻源信息日志
	 * @param recordCode 单据编码
	 * @param template 模板
	 * @param modeList 寻源方式参数
	 * @param mapRwScore 对应实仓优先分值
	 * @param realWarehouseId 实仓Id
	 * @param rwStop 停发标识
	 * @param rwStopper 停发者
	 * @return
	 */
	public static String getOnlineRouteInfo(String recordCode ,String template, List<Integer> modeList, Map<Long, Long> mapRwScore, Long realWarehouseId, boolean rwStop, Long rwStopper) {
		JSONObject jsonObject=new JSONObject();
		jsonObject.put("logType", LOG_TYPE_ONLINE_ROUTE_INFO); // 日志类型
		jsonObject.put("recordCode", recordCode); // 单据编码
		jsonObject.put("template", template); // 模板名
		jsonObject.put("templateParam", modeList); // 寻源方式参数
		jsonObject.put("mapRwScore", mapRwScore); // 对应实仓优先分值
		jsonObject.put("realWarehouseId", realWarehouseId); // 实仓Id
		jsonObject.put("rwStop", rwStop); // 是否停发
		jsonObject.put("rwStopper", rwStopper); // 停发者
		return jsonObject.toJSONString();
	}

	public static void main(String[] args) {
//		CoreRealWarehouseStockDO coreRedisDo = new CoreRealWarehouseStockDO();
//		coreRedisDo.setSkuId(1L);
//		coreRedisDo.setRealQty(10L); // 真实库存
//		coreRedisDo.setLockQty(1L); // 锁定库存
//		coreRedisDo.setOnroadQty(1L); // 在途库存
//		coreRedisDo.setQualityQty(0L); // 质检库存
//		coreRedisDo.setUnqualifiedQty(0L); // 不合格库存
//		
//		CoreRealWarehouseStockDO coreDbDo = new CoreRealWarehouseStockDO();
//		coreDbDo.setRealWarehouseId(1L);
//		coreDbDo.setSkuId(1L);
//		coreDbDo.setRealQty(10L); // 真实库存
//		coreDbDo.setLockQty(1L); // 锁定库存
//		coreDbDo.setOnroadQty(1L); // 在途库存
//		coreDbDo.setQualityQty(0L); // 质检库存
//		coreDbDo.setUnqualifiedQty(0L); // 不合格库存
//		System.out.println(getStockNumCheckByRealWarehouse(StockCheckErrorEnum.OK, coreRedisDo, coreDbDo));
//		System.out.println(getStockNumCheckCount(1L, 1, 0, 0));
		
		
//		RwStockChangeFlowDO changeFlowDO = new RwStockChangeFlowDO();
//		changeFlowDO.setRecordCode("recordCode");
//		changeFlowDO.setRealWarehouseId(11L);
//		changeFlowDO.setChannelCode("channelCode");
//		changeFlowDO.setMerchantId(101L);
//		changeFlowDO.setSkuId(2L);
//		changeFlowDO.setSkuCode("skuCode");
//		changeFlowDO.setBeforeChangeQty(new BigDecimal(11));
//		changeFlowDO.setAfterChangeQty(new BigDecimal(9));
//		changeFlowDO.setChangeQty(new BigDecimal(2));
//		changeFlowDO.setStockType(1);
//		changeFlowDO.setTransType(1);
//		changeFlowDO.setStockId(12222L);
//		String warn = getStockTransactionFlow(changeFlowDO);
//	    System.out.println(warn);
		System.out.println(getStockQuery("1", 10, 10L));
		System.out.println(getStockInactiveDataJob("shopRetail", "门店零售冷数据迁移", 100, 10L));
	}
	
	
}
