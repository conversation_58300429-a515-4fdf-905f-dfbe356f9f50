package com.rome.stock.innerservice.common;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.rome.arch.core.clientobject.Response;
import com.rome.stock.common.constants.SAPConstants;
import com.rome.stock.innerservice.api.dto.bigdata.DayInventoryPlanResponse;
import com.rome.stock.innerservice.api.dto.bigdata.StockPlanQueryDTO;
import com.rome.stock.innerservice.remote.sap.dto.SapNewResponse;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.codec.binary.Base64;
import org.apache.http.HttpResponse;
import org.apache.http.HttpStatus;
import org.apache.http.client.methods.HttpGet;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.client.utils.URIBuilder;
import org.apache.http.entity.StringEntity;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.util.EntityUtils;

import java.lang.reflect.Field;
import java.nio.charset.Charset;

import java.util.HashMap;
import java.util.Map;

/**
 * httpRequest工具类
 * <AUTHOR>
 * @date 2019/6/13
 */
@Slf4j
public class HttpRequestUtil {

    /**
     * basic认证（ 如有需求，改为通用的认证方法，暂时job模板中没有别的需要此方法）
     * @return
     */
    private static String getHeader(String userName , String passWord) {
        String auth = userName + ":" + passWord;
        byte[] encodedAuth = Base64.encodeBase64(auth.getBytes(Charset.forName("UTF-8")));
        String authHeader = "Basic " + new String(encodedAuth, Charset.forName("UTF-8"));
        return authHeader;
    }

    /**
     * 发送get请求（无请求参数）
     * @param url 路径
     * @return
     */
    public static Response httpGet(String url) {
        return httpGet(url, null);
    }

    public static void main(String[] args) throws Exception {
        String url="http://10.4.0.66:9001/lyf/v1/dayInventoryPlan";
        StockPlanQueryDTO stockPlanQueryDTO=new StockPlanQueryDTO();
        stockPlanQueryDTO.setSkuKey("000011");
        Map<String, String> urlParmars=HttpRequestUtil.objectToMap(stockPlanQueryDTO);
        Response res= HttpRequestUtil.httpGet(url,urlParmars);
        DayInventoryPlanResponse data=JSON.parseObject(String.valueOf(res.getData()), DayInventoryPlanResponse.class);
        System.err.println(res);
    }

    /**
     * 利用反射实现
     * <li>空属性不转换
     * <li>超过10万条数据不建议使用
     * @param obj
     * @return
     * @throws Exception
     */
    public static Map<String, String> objectToMap(Object obj){
        if (obj == null) {
            return null;
        }
        Map<String, String> map = new HashMap<String, String>();
        Field[] fields = obj.getClass().getDeclaredFields();
        try{
            for (int i = 0, len = fields.length; i < len; i++) {
                String varName = fields[i].getName();
                boolean accessFlag = fields[i].isAccessible();
                fields[i].setAccessible(true);// 允许通过反射访问该字段

                Object valueObj = fields[i].get(obj);
                if (valueObj != null) {
                    map.put(varName, String.valueOf(valueObj));
                }
                fields[i].setAccessible(accessFlag);
            }
        }catch (Exception e){
            log.error(e.getMessage(), e);
            return null;
        }
        return map;
    }

    /**
     * 发送get请求
     * @param url 路径
     * @param urlParmars 请求参数
     * @return
     */
    public static Response httpGet(String url, Map<String, String> urlParmars) {
        //get请求返回结果
        Response resObject = null;
        try {
            CloseableHttpClient client = HttpClients.createDefault();

            URIBuilder uriBuilder = new URIBuilder(url);
            //设置urlParam
            setUrlParmar(uriBuilder, urlParmars);
            HttpGet request = new HttpGet(uriBuilder.build());
            request.setHeader("Content-Type", "application/json");
            HttpResponse response = client.execute(request);
            if (response.getStatusLine().getStatusCode() == HttpStatus.SC_OK) {
                String strResult = EntityUtils.toString(response.getEntity());
                resObject = JSON.parseObject(strResult, Response.class);
                log.info("POST请求成功，地址：{}，参数：{}，状态码：{} ", url, String.valueOf(urlParmars), response.getStatusLine());
            } else {
                log.error("POST请求失败，地址：{}，参数：{}，状态码：{} ", url, String.valueOf(urlParmars), response.getStatusLine());
            }
            client.close();
        } catch (Exception e) {
            log.error("GET请求ERROR ", e);
        }
        return resObject;
    }

    /**
     * post请求
     * @param url         url地址
     * @param jsonParam     参数
     * @return
     */
    public static Response httpPost(String url, Map<String, String> urlParams, JSONObject jsonParam, String userName , String passWord){

        //返回结果
        Response resObject = null;
        try {
            CloseableHttpClient httpClient = HttpClients.createDefault();
            URIBuilder uriBuilder = new URIBuilder(url);
            //设置urlParam
            if(null != urlParams) {
                setUrlParmar(uriBuilder, urlParams);
            }
            HttpPost method = new HttpPost(uriBuilder.build());
            if(userName!=null && passWord !=null){
                //basic认证
                method.addHeader("Authorization", getHeader(userName,passWord));
            }
            if (null != jsonParam) {
                //解决中文乱码问题
                StringEntity entity = new StringEntity(jsonParam.toString(), "utf-8");
                entity.setContentEncoding("UTF-8");
                entity.setContentType("application/json");
                method.setEntity(entity);
            }
            HttpResponse response = httpClient.execute(method);
            String str = "";
            if (url.endsWith(SAPConstants.SAP_TRANSFER_DIRECT_TO_FRANCHISE)
                    ||url.endsWith(SAPConstants.SAP_TRANSFER_DIRECT_TO_FRANCHISE2)
                    ||url.endsWith(SAPConstants.SAP_301_METHORD)) {
                //直营转加盟逻辑很特殊，没有返回值，只能自己造返回值，以方便后续的统一处理
                if (response.getStatusLine().getStatusCode() == HttpStatus.SC_OK
                        || response.getStatusLine().getStatusCode() == HttpStatus.SC_NON_AUTHORITATIVE_INFORMATION
                        || response.getStatusLine().getStatusCode() == HttpStatus.SC_ACCEPTED) {
                    str = EntityUtils.toString(response.getEntity());
                    if (StringUtils.isBlank(str)) {
                        str = "{\"code\":\"S\",\"msg\":\"接收成功[sap开发确认只需要用http状态码判断即可（200,202,203）]\"}";
                    }
                    resObject = JSON.parseObject(str, Response.class);
                    log.info("POST请求成功，地址：{}，状态码：{} ", url, response.getStatusLine());
                } else {
                    str = "{\"code\":\"E\",\"msg\":\"接收失败[sap开发确认只需要用http状态码判断即可（200,202,203）]" + response.getStatusLine() + "\"}";
                    resObject = JSON.parseObject(str, Response.class);
                    log.error("POST请求失败，地址：{}，状态码：{} ", url, response.getStatusLine());
                }
            } else if (response.getStatusLine().getStatusCode() == HttpStatus.SC_OK) {
                str = EntityUtils.toString(response.getEntity());
                resObject = JSON.parseObject(str, Response.class);
                log.info("POST请求成功，地址：{}，状态码：{} ", url, response.getStatusLine());
            } else {
                log.error("POST请求失败，地址：{}，状态码：{} ", url, response.getStatusLine());
                //如果是500报错, 输出完整的错误信息
                if(HttpStatus.SC_INTERNAL_SERVER_ERROR == response.getStatusLine().getStatusCode()){
                    str = EntityUtils.toString(response.getEntity());
                    log.error("POST请求失败,500，报错信息为：{} ",  str);
                }
            }
            httpClient.close();
        } catch (Exception e) {
            log.error("POST请求失败，地址：{}，参数：{}，错误原因：{} ", url, String.valueOf(jsonParam), e);
        }
        return resObject;
    }
    /**
     * post请求
     * @param url         url地址
     * @param jsonParam     参数
     * @return
     */
    public static Response httpPost930(String url, Map<String, String> urlParams, JSONObject jsonParam, String userName , String passWord){

        //返回结果
        Response resObject = null;
        try {
            CloseableHttpClient httpClient = HttpClients.createDefault();
            URIBuilder uriBuilder = new URIBuilder(url);
            //设置urlParam
            if(null != urlParams) {
                setUrlParmar(uriBuilder, urlParams);
            }
            HttpPost method = new HttpPost(uriBuilder.build());
            if(userName!=null && passWord !=null){
                //basic认证
                method.addHeader("Authorization", getHeader(userName,passWord));
            }
            if (null != jsonParam) {
                //解决中文乱码问题
                StringEntity entity = new StringEntity(jsonParam.toString(), "utf-8");
                entity.setContentEncoding("UTF-8");
                entity.setContentType("application/json");
                method.setEntity(entity);
            }
            HttpResponse response = httpClient.execute(method);
            String str = "";

            //直营转加盟逻辑很特殊，没有返回值，只能自己造返回值，以方便后续的统一处理
            if (response.getStatusLine().getStatusCode() == HttpStatus.SC_OK
                    || response.getStatusLine().getStatusCode() == HttpStatus.SC_NON_AUTHORITATIVE_INFORMATION
                    || response.getStatusLine().getStatusCode() == HttpStatus.SC_ACCEPTED) {
                str = EntityUtils.toString(response.getEntity());
                if (StringUtils.isBlank(str)) {
                    str = "{\"code\":\"S\",\"msg\":\"接收成功[sap开发确认只需要用http状态码判断即可（200,202,203）]\"}";
                }
                resObject = JSON.parseObject(str, Response.class);
                log.info("POST请求成功，地址：{}，状态码：{} ", url, response.getStatusLine());
            } else {
                str = "{\"code\":\"E\",\"msg\":\"接收失败[sap开发确认只需要用http状态码判断即可（200,202,203）]" + response.getStatusLine() + "\"}";
                resObject = JSON.parseObject(str, Response.class);
                log.error("POST请求失败，地址：{}，状态码：{} ", url, response.getStatusLine());
                //如果是500报错, 输出完整的错误信息
                if(HttpStatus.SC_INTERNAL_SERVER_ERROR == response.getStatusLine().getStatusCode()){
                    str = EntityUtils.toString(response.getEntity());
                    log.error("POST请求失败,500，报错信息为：{} ",  str);
                }
            }

            httpClient.close();
        } catch (Exception e) {
            log.error("POST请求失败，地址：{}，参数：{}，错误原因：{} ", url, String.valueOf(jsonParam), e);
        }
        return resObject;
    }
    /**
     * post请求
     * @param url         url地址
     * @param jsonParam     参数
     * @return
     */
    public static SapNewResponse httpPostForSapNew(String url, Map<String, String> urlParams, JSONObject jsonParam, String userName , String passWord){

        //返回结果
        SapNewResponse resObject = null;
        try {
            CloseableHttpClient httpClient = HttpClients.createDefault();
            URIBuilder uriBuilder = new URIBuilder(url);
            //设置urlParam
            if(null != urlParams) {
                setUrlParmar(uriBuilder, urlParams);
            }
            HttpPost method = new HttpPost(uriBuilder.build());
            if(userName!=null && passWord !=null){
                //basic认证
                method.addHeader("Authorization", getHeader(userName,passWord));
            }
            if (null != jsonParam) {
                //解决中文乱码问题
                StringEntity entity = new StringEntity(jsonParam.toString(), "utf-8");
                entity.setContentEncoding("UTF-8");
                entity.setContentType("application/json");
                method.setEntity(entity);
            }
            HttpResponse response = httpClient.execute(method);
            String str = "";
            if (response.getStatusLine().getStatusCode() == HttpStatus.SC_OK) {
                str = EntityUtils.toString(response.getEntity());
                resObject = JSON.parseObject(str, SapNewResponse.class);
                log.info("POST请求成功，地址：{}，状态码：{} ", url, response.getStatusLine());
            } else {
                log.error("POST请求失败，地址：{}，状态码：{} ", url, response.getStatusLine());
                //如果是500报错, 输出完整的错误信息
                if(HttpStatus.SC_INTERNAL_SERVER_ERROR == response.getStatusLine().getStatusCode()){
                    str = EntityUtils.toString(response.getEntity());
                    log.error("POST请求失败,500，报错信息为：{} ",  str);
                }
            }
            httpClient.close();
        } catch (Exception e) {
            log.error("POST请求失败，地址：{}，参数：{}，错误原因：{} ", url, String.valueOf(jsonParam), e);
        }
        return resObject;
    }

    /**
     * post请求
     * @param url         url地址
     * @param jsonParam     参数
     * @return
     */
    public static String httpPostToStr(String url, Map<String, String> urlParams, JSONObject jsonParam, String userName , String passWord){
        String str = "";
        //返回结果
        Response resObject = null;
        try {
            CloseableHttpClient httpClient = HttpClients.createDefault();
            URIBuilder uriBuilder = new URIBuilder(url);
            //设置urlParam
            if(null != urlParams) {
                setUrlParmar(uriBuilder, urlParams);
            }
            HttpPost method = new HttpPost(uriBuilder.build());
            if(userName!=null && passWord !=null){
                //basic认证
                method.addHeader("Authorization", getHeader(userName,passWord));
            }
            if (null != jsonParam) {
                //解决中文乱码问题
                StringEntity entity = new StringEntity(jsonParam.toString(), "utf-8");
                entity.setContentEncoding("UTF-8");
                entity.setContentType("application/json");
                method.setEntity(entity);
            }
            HttpResponse response = httpClient.execute(method);
            if (response.getStatusLine().getStatusCode() == HttpStatus.SC_OK) {
                str = EntityUtils.toString(response.getEntity());
                log.info("POST请求成功，地址：{}，状态码：{} ", url, response.getStatusLine());
            } else {
                log.error("POST请求失败，地址：{}，状态码：{} ", url, response.getStatusLine());
                //如果是500报错, 输出完整的错误信息
                if(HttpStatus.SC_INTERNAL_SERVER_ERROR == response.getStatusLine().getStatusCode()){
                    str = EntityUtils.toString(response.getEntity());
                    log.error("POST请求失败,500，报错信息为：{} ",  str);
                }
            }
            httpClient.close();
        } catch (Exception e) {
            log.error("POST请求失败，地址：{}，参数：{}，错误原因：{} ", url, String.valueOf(jsonParam), e);
        }
        return str;
    }

    /**
     * POST请求，无URL参数
     * @param url
     * @param jsonParam
     * @return
     */
    public static String httpPostToStr(String url, JSONObject jsonParam ,String userName,String passWord){
        return httpPostToStr(url, null, jsonParam, userName,passWord);
    }

    /**
     * POST请求，无URL参数
     * @param url
     * @param jsonParam
     * @return
     */
    public static Response httpPost(String url, JSONObject jsonParam ,String userName,String passWord){
        return httpPost(url, null, jsonParam, userName,passWord);
    }
    /**
     * POST请求，无URL参数
     * @param url
     * @param jsonParam
     * @return
     */
    public static SapNewResponse httpPostForSapNew(String url, JSONObject jsonParam ,String userName,String passWord){
        return httpPostForSapNew(url, null, jsonParam, userName,passWord);
    }
    /**
     * POST请求，无URL参数
     * @param url
     * @param jsonParam
     * @return
     */
    public static Response httpPostForSap930(String url, JSONObject jsonParam ,String userName,String passWord){
        return httpPost930(url, null, jsonParam, userName,passWord);
    }

    /**
     * 拼装url参数
     * @param uriBuilder
     * @param urlParmars
     */
    public static void setUrlParmar(URIBuilder uriBuilder, Map<String, String> urlParmars){
        //添加url参数
        if (urlParmars != null) {
            for (Map.Entry<String, String> entry : urlParmars.entrySet()) {
                uriBuilder.addParameter(entry.getKey(), entry.getValue());
            }
        }
    }

}
