package com.rome.stock.innerservice.common;

import cn.hutool.http.HttpRequest;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.rome.arch.core.clientobject.Response;
import com.rome.arch.core.exception.RomeException;
import com.rome.stock.innerservice.constant.HttpParamModeVO;
import com.rome.stock.innerservice.domain.repository.SapInterfaceLogRepository;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Map;
import java.util.Objects;

/**
 * openApi调用工具类
 * @date:2020/4/27 17:45
 */
@Slf4j
@Component
public class InnerApiClientUtil {
    @Autowired
    private InnerApiConfig innerApiConfig;

    @Resource
    private SapInterfaceLogRepository sapInterfaceLogRepository;

    /**
     * post方式调用innerApi
     * @param params 调用参数
     * @param url 调用请求的url
     * @param writeLog 是否写入日志
     * @param mode 调用模式 1--普通表单方式 2--body方式
     * @return
     */
    public Response postData(String recordCode,Object params, String url,String methodName,boolean writeLog,Integer mode){
        Response response;
        try{
            HttpRequest request=  HttpRequest.post(getUrl(url))
                    .header("X-Co-Client", innerApiConfig.getClientId())
                    .timeout(innerApiConfig.getTimeout());
            if(Objects.equals(HttpParamModeVO.FORM.getMode(),mode)){
                request.form(handlerMap(params));
            }else{
                request.body(Objects.nonNull(params)?JSON.toJSONString(params) : "");
            }
            String result=request.execute().body();
            response=JSONObject.parseObject(result,Response.class);
        }catch (RomeException ex){
            log.error("调用{}方法异常，异常原因:{}",url,ex);
            response=Response.builderFail(ex.getCode(),ex.getMessage());
        }catch(Exception ex){
            log.error("调用{}方法异常，异常原因:{}",url,ex);
            response=Response.builderFail(ResCode.STOCK_ERROR_9074,ResCode.STOCK_ERROR_9074_DESC);
        }
        if(writeLog){
            sapInterfaceLogRepository.saveSapInterFaceLog(recordCode,url,methodName,HandlerObjectParam(params),response);
        }
        return response;
    }



    /**
     * get方式调用innerApi
     * @param params
     * @param url
     * @param writeLog
     * @return
     */
    public Response getData(String recordCode,Object params, String url,String methodName,boolean writeLog){
        Response response;
        try{
            String result = HttpRequest.get(getUrl(url))
                    .header("X-Co-Client", innerApiConfig.getClientId())
                    .charset("UTF8")
                    .form(handlerMap(params))
                    .timeout(innerApiConfig.getTimeout())
                    .execute()
                    .body();
            response=JSONObject.parseObject(result,Response.class);
        }catch (RomeException ex){
            log.error("调用{}方法异常，异常原因:{}",url,ex);
            response=Response.builderFail(ex.getCode(),ex.getMessage());
        }catch (Exception ex){
            log.error("调用{}方法异常，异常原因:{}",url,ex);
            response=Response.builderFail(ResCode.STOCK_ERROR_9074,ResCode.STOCK_ERROR_9074_DESC);
        }
        if(writeLog){
            sapInterfaceLogRepository.saveSapInterFaceLog(recordCode,url,methodName,HandlerObjectParam(params),response);
        }
        return response;
    }


    /**
     * 处理url
     * @param url
     * @return
     */
    private String getUrl(String url){
        String baseUrl=innerApiConfig.getBaseUrl();
       if(StringUtils.isNotBlank(baseUrl) && com.rome.stock.innerservice.common.StringUtils.isNotBlank(url)) {
           if(!url.startsWith("/")){
               url=String.format("%1$s%2$s","/",url);
           }
           if(url.endsWith("/")){
               url=url.substring(0,url.length()-1);
           }
           return String.format("%1$s%2$s", baseUrl, url);
       }
       return null;
    }



    /**
     * 对参数进行校验
     * @param object
     * @return
     */
    private JSONObject HandlerObjectParam(Object object){
        JSONObject json=new JSONObject();
        try{
            if(Objects.nonNull(object)){
                return JSONObject.parseObject(JSON.toJSONString(object));
            }
        }catch (Exception ex){
            json.put("param",JSON.toJSONString(object));
        }
        return json;
    }

    /**
     * 对参数进行处理
     * @param object
     * @return
     */
    private Map handlerMap(Object object){
        if(Objects.nonNull(object)){
            try{
                return JSONObject.parseObject(JSON.toJSONString(object),Map.class);
            }catch (Exception ex){
                log.error("转换参数{}异常:{}",object,ex);
                return  null;
            }
        }
        return null;
    }


}    
   