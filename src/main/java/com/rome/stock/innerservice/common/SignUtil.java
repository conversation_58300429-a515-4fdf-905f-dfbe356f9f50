package com.rome.stock.innerservice.common;

import cn.hutool.crypto.SecureUtil;
import cn.hutool.crypto.asymmetric.Sign;
import cn.hutool.crypto.asymmetric.SignAlgorithm;

/**
 * <AUTHOR>
 * @description 签名工具类
 * @date 2020/11/13 12:45
 * @throw
 */
public class SignUtil {
    private static final char[] hexDigits = { '0', '1', '2', '3', '4', '5', '6', '7', '8', '9', 'A', 'B', 'C', 'D', 'E', 'F' };
    public static volatile SignUtil instance;
    private static final Object _obj=new Object();
    private  Sign sign=SecureUtil.sign(SignAlgorithm.MD5withRSA);

    private SignUtil(){}

    /**
     * 单例对象
     * @return
     */
    public static  SignUtil getInstance(){
        if(instance==null){
            synchronized (_obj){
                if(instance==null){
                    instance= new SignUtil();
                }
            }
        }
        return instance;
    }

    public Sign getSign(){
        return sign;
    }


    /**
     * 将16进制字节数组转成字符串
     * @param byteArr
     * @return
     */
    public String byte2String(byte[] byteArr) {
        int count = byteArr.length;
        StringBuffer stringBuffer = new StringBuffer();
        for (int i = 0; i < count; i++) {
            byte singByte = byteArr[i];
            stringBuffer.append(hexDigits[singByte >>> 4 & 0xf]).append(
                    hexDigits[singByte & 0xf]);
        }
        return stringBuffer.toString();
    }


    /**
     * 将字符串转成16进行字节数组
     * @param str
     * @return
     */
    public  byte[] Hex2Bytes(String str){
        byte[] hexByteArr = str.getBytes();
        int iLen = hexByteArr.length;
        byte[] arrOut = new byte[iLen / 2];
        String strTmp = null;
        for (int i = 0; i < iLen; i += 2)
        {
            strTmp = new String(hexByteArr, i, 2);
            arrOut[(i / 2)] = ((byte)Integer.parseInt(strTmp, 16));
        }
        return arrOut;
    }
}    
   