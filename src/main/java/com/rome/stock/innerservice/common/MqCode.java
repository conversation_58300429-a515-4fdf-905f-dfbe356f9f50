package com.rome.stock.innerservice.common;

/**
 * rocketMq操作结果
 * <AUTHOR>
 */
public class MqCode {

    public static final String MQ_SEND_OK = "SEND_OK";
    public static final String MQ_SEND_OK_DESC = "操作成功";

    public static final String MQ_ERROR_1001 = "MQ_1001";
    public static final String MQ_ERROR_1001_DESC = "操作失败";

    public static final String MQ_BATCH_STOCK_ERROR_2001 = "MQ_2001";
    public static final String MQ_BATCH_STOCK_ERROR_2001_DESC = "批次发送mq失败";

    public static final String MQ_BATCH_STOCK_ERROR_2002 = "MQ_2002";
    public static final String MQ_BATCH_STOCK_ERROR_2002_DESC = "该批次正在被操作";

    public static final String MQ_BATCH_STOCK_ERROR_2003 = "MQ_2003";
    public static final String MQ_BATCH_STOCK_ERROR_2003_DESC = "批次发送mq异常";
}
