package com.rome.stock.innerservice.common.VmAllocation;

import lombok.Data;

import java.math.BigDecimal;

/**
 * @Doc:计算虚仓分配数的入参
 * @Author: lchy
 * @Date: 2020/3/25
 * @Version 1.0
 */
@Data
public class AllocationCalQtyParam {
    private String lineNo;
    private Long skuId;
    private String skuCode;
    private BigDecimal planQty;
    private String unitCode;
    //销售单位比例
    private BigDecimal scale;

    /**
     * 原始需求数量【门店补货专用】
     */
    private BigDecimal originalQty;

    /**
     *基础单位skuCode和type组合key
     */
    public String getBaseSkuTypeKey(){
        return this.skuCode+"_"+AllocationConstant.BASIC_TYPE;
    }

    /**
     *基础单位组合key
     */
    public String getSkuUnitCodeKey(){
        return this.skuCode+"_"+this.unitCode;
    }


}
