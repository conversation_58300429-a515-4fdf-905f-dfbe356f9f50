package com.rome.stock.innerservice.common;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Configuration;

/**
 * 类AppTransferConfig的实现描述：电商过账配置
 *
 * <AUTHOR> 2020/6/11 14:41
 */
@Configuration
public class TransferConfig {

     public static String defaultChannelCode;
    /**
     * 门店仓默认的仓库编码
     */
    public static String defaultShopWarehouseCode;

    public static List<String> allowAppChannelCode;

    /**
     * app包材过账工厂
     */
    public static String appFactory;

    public static String appWarehouse;


    public static String wdtAppFactory;


    public static String wdtAppWarehouse;


    @Value("${transfer.config.onlineSale.factory}")
    public void setAppFactory(String appFactory) {
        TransferConfig.appFactory = appFactory;
    }

    @Value("${transfer.config.onlineSale.warehouse}")
    public void setAppWarehouse(String appWarehouse) {
        TransferConfig.appWarehouse = appWarehouse;
    }

    @Value("${transfer.config.onlineSale.wdtFactory}")
    public void setWdtAppFactory(String wdtAppFactory) {
        TransferConfig.wdtAppFactory = wdtAppFactory;
    }

    @Value("${transfer.config.onlineSale.wdtWarehouse}")
    public void setWdtAppWarehouse(String wdtAppWarehouse) {
        TransferConfig.wdtAppWarehouse = wdtAppWarehouse;
    }

    @Value("${transfer.config.onlineSale.defaultChannelCode}")
    public  void setDefaultChannelCode(String defaultChannelCode) {
        TransferConfig.defaultChannelCode = defaultChannelCode;
    }

    @Value("${transfer.config.onlineSale.defaultShopWarehouseCode}")
    public  void setDefaultShopWarehouseCode(String defaultShopWarehouseCode) {
        TransferConfig.defaultShopWarehouseCode = defaultShopWarehouseCode;
    }

    @Value("${transfer.config.onlineSale.allowAppChannelCode}")
    public  void setAllowAppChannelCode(String allowAppChannelCode) {
        List<String> allowList = new ArrayList<>();
        String [] channelCodes = allowAppChannelCode.split(",");
        if(channelCodes.length > 0){
            allowList.addAll(Arrays.asList(channelCodes));
        }
        TransferConfig.allowAppChannelCode = allowList;
    }
}
