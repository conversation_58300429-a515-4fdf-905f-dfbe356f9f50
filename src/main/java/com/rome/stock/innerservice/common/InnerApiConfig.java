package com.rome.stock.innerservice.common;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

@Data
@Configuration
@ConfigurationProperties(prefix = "inner.api")
public class InnerApiConfig {

    /**
     *客户端clientId
     **/
    private String clientId;

    /**
     * 指定调用的Eureka地址
     */
    private String baseUrl;

    /**
     * 超时时间
     */
    private Integer timeout;
}    
   