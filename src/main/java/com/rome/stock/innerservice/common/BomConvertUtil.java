package com.rome.stock.innerservice.common;

import com.alibaba.fastjson.JSON;
import com.rome.arch.core.exception.RomeException;
import com.rome.stock.innerservice.api.dto.bom.TargetBomDTO;
import com.rome.stock.innerservice.common.VmAllocation.AllocationConstant;
import com.rome.stock.core.constant.StockCoreConsts;
import com.rome.stock.innerservice.remote.item.dto.BomSkuBatchDTO;
import com.rome.stock.innerservice.remote.item.dto.CombineSkuResultDTO;
import com.rome.stock.innerservice.remote.item.dto.QuerySelectBomDTO;
import com.rome.stock.innerservice.remote.item.dto.SkuUnitExtDTO;
import com.rome.stock.innerservice.remote.item.facade.SkuFacade;
import com.rome.stock.innerservice.remote.purchase.facade.PurchaseCenterFacade;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

import static java.math.BigDecimal.ROUND_DOWN;

/**
 * <AUTHOR>
 * @description bom转换工具类
 * @date 2020/9/23 11:22
 * @throw
 */
@Component
@Slf4j
public class BomConvertUtil {
    @Resource
    private SkuFacade skuFacade;
    @Resource
    private PurchaseCenterFacade purchaseCenterFacade;


    /**
     * bom数据转换
     * @param factoryCode
     * @param targetBomDTOList 目标对象
     *@param isConvertChildBasicSuk 是否转换子品单位为基本单位
     *@return  如果入参数都为null时 返回null值
     */
    public Map<String,BomSkuBatchDTO> convertBomSku(String factoryCode,List<TargetBomDTO> targetBomDTOList,boolean isConvertChildBasicSuk){
        if(CollectionUtils.isNotEmpty(targetBomDTOList) && StringUtils.isNotBlank(factoryCode)){
            //查询bom 信息
            List<BomSkuBatchDTO> bomSkuBatchDTOList = this.queryBomInfo(factoryCode, targetBomDTOList);
            Map<String, TargetBomDTO> endProductMap = targetBomDTOList.stream().collect(Collectors.toMap(TargetBomDTO::getSkuCode, Function.identity(), (v1, v2) -> v2));
            List<String> bomSkuCodes = bomSkuBatchDTOList.stream().map(x -> x.getSkuCode()).collect(Collectors.toList());
            //查询sku信息
            Map<String, SkuUnitExtDTO> skuInfoUnitMap=this.skuInfoUnitMap(bomSkuCodes,1);
            Map<String, SkuUnitExtDTO> skuTypeUnitMap=this.skuInfoUnitMap(bomSkuCodes,2);
            //构建skuCode和unitCode组成的集合信息
            for(BomSkuBatchDTO bomSkuBatchDTO:bomSkuBatchDTOList){
                TargetBomDTO targetBomDTO = endProductMap.get(bomSkuBatchDTO.getSkuCode());
                AlikAssert.isNotNull(targetBomDTO,ResCode.STOCK_ERROR_1003,"当前成品信息与bom返回物料不一致"+bomSkuBatchDTO.getSkuCode());
                if(Objects.isNull(targetBomDTO.getUnitCode())){
                    //成品不传单位默认就是基础单位
                    SkuUnitExtDTO skuUnitExtDTO = skuTypeUnitMap.get(targetBomDTO.getSkuCode() + "_" + AllocationConstant.BASIC_TYPE);
                    AlikAssert.isNotNull(skuUnitExtDTO,ResCode.STOCK_ERROR_1003_DESC,"商品编码为"+targetBomDTO.getSkuCode()+"的物料基础单位不存在");
                    targetBomDTO.setUnitCode(skuUnitExtDTO.getBasicUnitCode());
                }
                //成品单位和返回的bom成品基本单位不一致 需要转换
                if(!Objects.equals(targetBomDTO.getUnitCode(),bomSkuBatchDTO.getBasicUnitCode())){
                    //获取基础单位转换比例信息
                    SkuUnitExtDTO dto = this.getBaseSkuInfoByUnitCode(skuInfoUnitMap, bomSkuBatchDTO.getSkuCode()+"_"+bomSkuBatchDTO.getBasicUnitCode());
                    //转出基础单位换算比例
                    BigDecimal baseScale= BigDecimal.ONE.multiply(dto.getScale()).setScale(StockCoreConsts.DECIMAL_POINT_NUM, ROUND_DOWN);
                    //获取目标单位转换信息
                    SkuUnitExtDTO targetDTO = this.getBaseSkuInfoByUnitCode(skuInfoUnitMap, targetBomDTO.getSkuUnitCodeKey());
                    //计算出成品与目标单位换算比例关系
                    BigDecimal targetScale = targetDTO.getScale().divide(baseScale, StockCoreConsts.DECIMAL_POINT_NUM, ROUND_DOWN);
                    //更新子品的比例关系
                    bomSkuBatchDTO.getChildrenList().forEach(x->{
                        x.setConvertScale(x.getNum().multiply(targetScale).setScale(StockCoreConsts.DECIMAL_POINT_NUM, ROUND_DOWN));
                        x.setTotalSkuQty(targetBomDTO.getSkuQty().multiply(x.getConvertScale()).setScale(StockCoreConsts.DECIMAL_POINT_NUM, ROUND_DOWN));
                    });
                }else{
                    //成品单位一致 只需要计算转换的总数和转换比例即可
                    bomSkuBatchDTO.getChildrenList().forEach(x->{
                        x.setConvertScale(x.getNum());
                        x.setTotalSkuQty(targetBomDTO.getSkuQty().multiply(x.getConvertScale()).setScale(StockCoreConsts.DECIMAL_POINT_NUM, ROUND_DOWN));
                    });
                }
                bomSkuBatchDTO.setConvertUnitCode(targetBomDTO.getUnitCode());
                if(isConvertChildBasicSuk){
                    List<String> childrenSkuCodes = bomSkuBatchDTO.getChildrenList().stream().map(z -> z.getSkuCode()).collect(Collectors.toList());
                    Map<String, SkuUnitExtDTO> childrenSkuInfoUnitMap=this.skuInfoUnitMap(childrenSkuCodes,1);
                    //转换子品单位信息
                    bomSkuBatchDTO.getChildrenList().forEach(y->{
                        SkuUnitExtDTO childDto = this.getBaseSkuInfoByUnitCode(childrenSkuInfoUnitMap, y.getSkuCode()+"_"+y.getCombineSkuUnit());
                        y.setBasicUnit(childDto.getBasicUnitCode());
                        BigDecimal childBaseQty= y.getNum().multiply(childDto.getScale()).setScale(StockCoreConsts.DECIMAL_POINT_NUM, ROUND_DOWN);
                        y.setBasicSkuQty(childBaseQty);
                        y.setTotalBaseSkuQty(childDto.getScale().multiply(y.getTotalSkuQty()).setScale(StockCoreConsts.DECIMAL_POINT_NUM, ROUND_DOWN));
                    });
                }
            }
            return bomSkuBatchDTOList.stream().collect(Collectors.toMap(x->x.getSkuCode(),Function.identity(),(v1,v2)->v2));
        }
        return null;
    }




    public Map<String, CombineSkuResultDTO> convertBomSkuBySkuCodes(List<String> skuCodes) {
        Map<String, CombineSkuResultDTO> resultMap = new HashMap<>();
        for (String skuCode : skuCodes) {
            resultMap.put(skuCode, null);
        }
        //查询bom 信息
        List<CombineSkuResultDTO> bomSkuBatchDTOList = skuFacade.queryCombineSkuBySkuCodes(skuCodes);
        if (CollectionUtils.isEmpty(bomSkuBatchDTOList)) {
           return resultMap;
        }
        //构建skuCode和unitCode组成的集合信息
        for(CombineSkuResultDTO bomSkuBatchDTO:bomSkuBatchDTOList){
            //转换子品单位信息
            bomSkuBatchDTO.getCombineSonSkuResultDTOS().forEach(y->{
                if (Objects.nonNull(y.getNum()) && Objects.nonNull(y.getScale())){
                    y.setBasicSkuQty(y.getNum().multiply(y.getScale()));
                }else {
                    if(Objects.nonNull(y.getNum())){
                        y.setBasicSkuQty(y.getNum());
                    }else {
                        y.setBasicSkuQty(BigDecimal.ZERO);
                    }
                }
            });

        }

        Map<String, CombineSkuResultDTO> itemBomMap = bomSkuBatchDTOList.stream().collect(Collectors.toMap(x->x.getSkuCode(),Function.identity(),(v1,v2)->v2));
        resultMap.putAll(itemBomMap);
        return resultMap;
    }



    /**
     * bom数据转换
     * @param pwRecord
     * @param targetBomDTOList 目标对象
     *@param isConvertChildBasicSuk 是否转换子品单位为基本单位
     *@return  如果入参数都为null时 返回null值
     */
    public Map<String,BomSkuBatchDTO> convertBomSkuByPwCode(String pwRecord,List<TargetBomDTO> targetBomDTOList,boolean isConvertChildBasicSuk){
        if(CollectionUtils.isNotEmpty(targetBomDTOList) && StringUtils.isNotBlank(pwRecord)){
            //查询bom 信息
            List<BomSkuBatchDTO> bomSkuBatchDTOList = this.queryBomInfoFromPurchaseCenter(pwRecord);
            Map<String, TargetBomDTO> endProductMap = targetBomDTOList.stream().collect(Collectors.toMap(TargetBomDTO::getSkuCode, Function.identity(), (v1, v2) -> v2));
            List<String> bomSkuCodes = bomSkuBatchDTOList.stream().map(x -> x.getSkuCode()).collect(Collectors.toList());
            //查询sku信息
            Map<String, SkuUnitExtDTO> skuInfoUnitMap=this.skuInfoUnitMap(bomSkuCodes,1);
            Map<String, SkuUnitExtDTO> skuTypeUnitMap=this.skuInfoUnitMap(bomSkuCodes,2);
            //构建skuCode和unitCode组成的集合信息
            for(BomSkuBatchDTO bomSkuBatchDTO:bomSkuBatchDTOList){
                TargetBomDTO targetBomDTO = endProductMap.get(bomSkuBatchDTO.getSkuCode());
                AlikAssert.isNotNull(targetBomDTO,ResCode.STOCK_ERROR_1003,"当前成品信息与bom返回物料不一致"+bomSkuBatchDTO.getSkuCode());
                if(Objects.isNull(targetBomDTO.getUnitCode())){
                    //成品不传单位默认就是基础单位
                    SkuUnitExtDTO skuUnitExtDTO = skuTypeUnitMap.get(targetBomDTO.getSkuCode() + "_" + AllocationConstant.BASIC_TYPE);
                    AlikAssert.isNotNull(skuUnitExtDTO,ResCode.STOCK_ERROR_1003_DESC,"商品编码为"+targetBomDTO.getSkuCode()+"的物料基础单位不存在");
                    targetBomDTO.setUnitCode(skuUnitExtDTO.getBasicUnitCode());
                }
                //成品单位和返回的bom成品基本单位不一致 需要转换
                if(!Objects.equals(targetBomDTO.getUnitCode(),bomSkuBatchDTO.getBasicUnitCode())){
                    //获取基础单位转换比例信息
                    SkuUnitExtDTO dto = this.getBaseSkuInfoByUnitCode(skuInfoUnitMap, bomSkuBatchDTO.getSkuCode()+"_"+bomSkuBatchDTO.getBasicUnitCode());
                    //转出基础单位换算比例
                    BigDecimal baseScale= BigDecimal.ONE.multiply(dto.getScale()).setScale(StockCoreConsts.DECIMAL_POINT_NUM, ROUND_DOWN);
                    //获取目标单位转换信息
                    SkuUnitExtDTO targetDTO = this.getBaseSkuInfoByUnitCode(skuInfoUnitMap, targetBomDTO.getSkuUnitCodeKey());
                    //计算出成品与目标单位换算比例关系
                    BigDecimal targetScale = targetDTO.getScale().divide(baseScale, StockCoreConsts.DECIMAL_POINT_NUM, ROUND_DOWN);
                    //更新子品的比例关系
                    bomSkuBatchDTO.getChildrenList().forEach(x->{
                        x.setConvertScale(x.getNum().multiply(targetScale).setScale(StockCoreConsts.DECIMAL_POINT_NUM, ROUND_DOWN));
                        x.setTotalSkuQty(targetBomDTO.getSkuQty().multiply(x.getConvertScale()).setScale(StockCoreConsts.DECIMAL_POINT_NUM, ROUND_DOWN));
                    });
                }else{
                    //成品单位一致 只需要计算转换的总数和转换比例即可
                    bomSkuBatchDTO.getChildrenList().forEach(x->{
                        x.setConvertScale(x.getNum());
                        x.setTotalSkuQty(targetBomDTO.getSkuQty().multiply(x.getConvertScale()).setScale(StockCoreConsts.DECIMAL_POINT_NUM, ROUND_DOWN));
                    });
                }
                bomSkuBatchDTO.setConvertUnitCode(targetBomDTO.getUnitCode());
                if(isConvertChildBasicSuk){
                    List<String> childrenSkuCodes = bomSkuBatchDTO.getChildrenList().stream().map(z -> z.getSkuCode()).collect(Collectors.toList());
                    Map<String, SkuUnitExtDTO> childrenSkuInfoUnitMap=this.skuInfoUnitMap(childrenSkuCodes,1);
                    //转换子品单位信息
                    bomSkuBatchDTO.getChildrenList().forEach(y->{
                        SkuUnitExtDTO childDto = this.getBaseSkuInfoByUnitCode(childrenSkuInfoUnitMap, y.getSkuCode()+"_"+y.getCombineSkuUnit());
                        y.setBasicUnit(childDto.getBasicUnitCode());
                        BigDecimal childBaseQty= y.getNum().multiply(childDto.getScale()).setScale(StockCoreConsts.DECIMAL_POINT_NUM, ROUND_DOWN);
                        y.setBasicSkuQty(childBaseQty);
                        y.setTotalBaseSkuQty(childDto.getScale().multiply(y.getTotalSkuQty()).setScale(StockCoreConsts.DECIMAL_POINT_NUM, ROUND_DOWN));
                    });
                }
            }
            return bomSkuBatchDTOList.stream().collect(Collectors.toMap(x->x.getSkuCode(),Function.identity(),(v1,v2)->v2));
        }
        return null;
    }

    /**
     * 获取sku和unitCoed 相关信息
     * @param skuCodes
     * @param type 1--根据单位编码区分 2--根据类型区分
     * @return
     */
    private  Map<String, SkuUnitExtDTO>  skuInfoUnitMap(List<String> skuCodes,int type) {
        List<SkuUnitExtDTO> skuInfos = skuFacade.querySkuUnits(skuCodes);
        if(CollectionUtils.isEmpty(skuInfos)){
            throw new RomeException(ResCode.STOCK_ERROR_1002, ResCode.STOCK_ERROR_1002_DESC + "未查询到商品单位信息 ");
        }
        if(Objects.equals(type,1)){
            return skuInfos.stream().collect(Collectors.toMap(SkuUnitExtDTO::getSkuUnitCodeByPrimaryKey,Function.identity(),(v1,v2)->v2));
        }else{
            return skuInfos.stream().collect(Collectors.toMap(SkuUnitExtDTO::getSkuTypePrimaryKey, Function.identity(),(v1,v2)->v2));
        }
    }



    /**
     * 查询bom数据
     * @param factoryCode
     * @param targetBomDTOList
     * @return
     */
    private List<BomSkuBatchDTO> queryBomInfo(String factoryCode, List<TargetBomDTO> targetBomDTOList) {
        //构建商品中心请求参数
        List<String> skuCodes = targetBomDTOList.stream().map(x -> x.getSkuCode()).collect(Collectors.toList());
        QuerySelectBomDTO querySelectBomDTO=new QuerySelectBomDTO();
        querySelectBomDTO.setFactoryCode(factoryCode);
        querySelectBomDTO.setSkuCodes(skuCodes);
        log.info("调用bom接口请求参数:{}",JSON.toJSONString(querySelectBomDTO));
        List<BomSkuBatchDTO> bomSkuBatchDTOS = skuFacade.batchSelectBomSkuAndDetail(querySelectBomDTO);
        AlikAssert.isNotEmpty(bomSkuBatchDTOS,ResCode.STOCK_ERROR_1003,"调用商品中心接口未查询到bom信息");
        log.info("返回bom信息:{}",JSON.toJSONString(bomSkuBatchDTOS));
        return bomSkuBatchDTOS;
    }



    /**
     * 查询bom数据
     * @param pwCode
     * @return
     */
    private List<BomSkuBatchDTO> queryBomInfoFromPurchaseCenter(String pwCode) {
        //构建商品中心请求参数
        log.info("调用bom接口请求参数:{}",pwCode);
        List<BomSkuBatchDTO> bomSkuBatchDTOS =  purchaseCenterFacade.bomInformation(pwCode);
        AlikAssert.isNotEmpty(bomSkuBatchDTOS,ResCode.STOCK_ERROR_1003,"调用商品中心接口未查询到bom信息");
        log.info("返回bom信息:{}",JSON.toJSONString(bomSkuBatchDTOS));
        return bomSkuBatchDTOS;
    }


    /**
     * 获取基础单位信息
     * @param skuInfoUnitMap
     * @param key
     * @return
     */
    private SkuUnitExtDTO getBaseSkuInfoByUnitCode(Map<String, SkuUnitExtDTO> skuInfoUnitMap, String  key) {
        SkuUnitExtDTO dto = skuInfoUnitMap.get(key);
        if (dto == null) {
            throw new RomeException(ResCode.STOCK_ERROR_1002, ResCode.STOCK_ERROR_1002_DESC + "入参单位在商品中心不存在 " +key.split("_")[0] + " 单位code " + key.split("_")[1] );
        }
        return dto;
    }
}    
   