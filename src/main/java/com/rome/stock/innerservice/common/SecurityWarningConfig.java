package com.rome.stock.innerservice.common;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

import java.util.List;

/**
 * <AUTHOR>
 * @Description 通知告警配置信息类
 * @date 2020/3/30 14:37
 */
@Data
@Configuration
@ConfigurationProperties(prefix = "security.warning")
public class SecurityWarningConfig {

    /**
    *客户端clientId
    **/
    private String clientId;

    /**
     *用户名
     **/
    private String username;

    /**
     *密码
     **/
    private String password;

    /**
     *服务端ip
     **/
    private String ip;

    /**
     *请求url
     **/
    private String url;

    /**
     *手机号集合
     **/
    private List<String> phoneList;


}    
   