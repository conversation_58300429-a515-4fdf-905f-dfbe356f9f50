package com.rome.stock.innerservice.common;

import com.rome.arch.core.exception.RomeException;
import com.rome.stock.innerservice.domain.entity.SkuQtyUnitBaseE;
import com.rome.stock.innerservice.remote.item.dto.SkuInfoExtDTO;
import com.rome.stock.innerservice.remote.item.facade.SkuFacade;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.stream.Collectors;

/**
 * 商品信息
 * <AUTHOR>
@Component
public class SkuInfoTools {
    @Autowired
    private SkuFacade skuFacade;


    /**
     * 设置skuCode或者skuId
     * @param skuList
     */
    public void convertSkuCode(List<? extends SkuQtyUnitBaseE> skuList){
        this.convertSkuCode(skuList,skuFacade.getDefaultMerchantId());
    }

    /**
     * 设置skuCode或者skuId
     * @param skuList
     */
    public void convertSkuCode(List<? extends SkuQtyUnitBaseE> skuList ,Long merchantId){
        //判断是否有skuCode,没有则取商品中心获取并设置
        List<Long> skuIds = skuList.stream().filter(sku -> StringUtils.isBlank( sku.getSkuCode()))
                .map(SkuQtyUnitBaseE :: getSkuId).collect(Collectors.toList());
        if(skuIds != null && skuIds.size() > 0){
            List<SkuInfoExtDTO> list = skuFacade.skusBySkuId(skuIds,merchantId);
            for(SkuInfoExtDTO skuInfo : list){
                skuList.stream().filter(sku -> sku.getSkuId().equals(skuInfo.getId()))
                        .forEach(sku -> {
                            sku.setSkuCode(skuInfo.getSkuCode());
                            sku.setContainer(skuInfo.getContainer());
                        });
            }
        }
        //判断是否有skuId,没有则取商品中心获取并设置
        List<String> skuCodes = skuList.stream().filter(sku -> sku.getSkuId() == null || sku.getSkuId() == 0)
                .map(SkuQtyUnitBaseE :: getSkuCode).collect(Collectors.toList());
        if(skuCodes != null && skuCodes.size() > 0 ){
            List<SkuInfoExtDTO> list = skuFacade.skusBySkuCode(skuCodes,merchantId);
            for(SkuInfoExtDTO skuInfo : list){
                skuList.stream().filter(sku -> sku.getSkuCode().equals(skuInfo.getSkuCode()))
                        .forEach(sku -> {
                            sku.setSkuId(skuInfo.getId());
                            sku.setContainer(skuInfo.getContainer());
                        });
            }
        }
        skuList.forEach(sku -> {
            if(sku.getSkuId() == null || sku.getSkuId() == 0){
                throw new RomeException(ResCode.STOCK_ERROR_1043,ResCode.STOCK_ERROR_1043_DESC + sku.getSkuCode());
            }
            if(StringUtils.isBlank(sku.getSkuCode())){
                throw new RomeException(ResCode.STOCK_ERROR_1044,ResCode.STOCK_ERROR_1044_DESC + sku.getSkuId());
            }
        });
    }

    /**
     * 设置skuCode或者skuId（过滤商品中心没有商品）
     * @param skuList
     */
    public void convertSkuCodeFilter(List<? extends SkuQtyUnitBaseE> skuList){
        this.convertSkuCodeFilter(skuList,skuFacade.getDefaultMerchantId());
    }
    /**
     * 设置skuCode或者skuId（过滤商品中心没有商品）
     * @param skuList
     */
    public void convertSkuCodeFilter(List<? extends SkuQtyUnitBaseE> skuList,Long merchantId){
        //判断是否有skuCode,没有则取商品中心获取并设置
        List<Long> skuIds = skuList.stream().filter(sku -> StringUtils.isBlank( sku.getSkuCode()))
                .map(SkuQtyUnitBaseE :: getSkuId).collect(Collectors.toList());
        if(skuIds != null && skuIds.size() > 0){
            List<SkuInfoExtDTO> list = skuFacade.skusBySkuId(skuIds,merchantId);
            for(SkuInfoExtDTO skuInfo : list){
                skuList.stream().filter(sku -> sku.getSkuId().equals(skuInfo.getId()))
                        .forEach(sku -> {
                            sku.setSkuCode(skuInfo.getSkuCode());
                            sku.setContainer(skuInfo.getContainer());
                        });
            }
        }
        //判断是否有skuId,没有则取商品中心获取并设置
        List<String> skuCodes = skuList.stream().filter(sku -> sku.getSkuId() == null || sku.getSkuId() == 0)
                .map(SkuQtyUnitBaseE :: getSkuCode).collect(Collectors.toList());
        if(skuCodes != null && skuCodes.size() > 0 ){
            List<SkuInfoExtDTO> list = skuFacade.skusBySkuCode(skuCodes,merchantId);
            for(SkuInfoExtDTO skuInfo : list){
                skuList.stream().filter(sku -> sku.getSkuCode().equals(skuInfo.getSkuCode()))
                        .forEach(sku -> {
                            sku.setSkuId(skuInfo.getId());
                            sku.setContainer(skuInfo.getContainer());
                        });
            }
        }
        skuList.removeIf(sku -> sku.getSkuId() == null || sku.getSkuId() == 0 || StringUtils.isBlank(sku.getSkuCode()));
    }

    public List<SkuInfoExtDTO> querySkuListBySkuIds(List<Long> skuIds) {
        return skuFacade.skusBySkuId(skuIds);
    }

}
