package com.rome.stock.innerservice.common;

import lombok.AllArgsConstructor;
import lombok.Data;
import org.springframework.util.DigestUtils;

/**
 * @Description: 苏宁TMS请求签名
 * @Author: 张俊承  2022/07/14
 */
@Data
@AllArgsConstructor
public class SNTransportAuthToken {

    private String token;

    private long createTime;


    public static SNTransportAuthToken createToken(String appId, String secret, long createTime, String jsonStr) {
        String original = appId + secret + jsonStr + createTime;
        String token = DigestUtils.md5DigestAsHex(original.getBytes());
        return new SNTransportAuthToken(token, createTime);
    }

}
