package com.rome.stock.innerservice.common;

import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.rome.stock.wms.config.BaseinfoConfiguration;
import com.rome.stock.wms.config.BaseinfoProperty;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;

import java.util.Arrays;
import java.util.List;
import java.util.Map;

/**
 *
 */
public class BaseInfoConfigTool {

	/**
	 * 获取沪威酒工厂code
	 * @param
	 * @return
	 */
	public static List<String> getWineFactoryCodes() {
		return getByCodeAndParamName("stock_wine","factory_codes");
	}

	public static List<String> getWarehouseStoreWarehouseCodes() {
		return getByCodeAndParamName("electronic.warehouse","real.warehouse.code");
	}

	public static Map<String,String> getDfOrderType(){
		Map<String,String> map= Maps.newHashMap();
		List<BaseinfoProperty> list=BaseinfoConfiguration.getInstance().getList("df_ordertype");
		if (CollectionUtils.isNotEmpty(list)){
			list.forEach(item->{
				map.put(item.getKey(),item.getValue());
			});
		}
		return map;
	}
	/**
	 *
	 * @param code
	 * @param paramName
	 * @return
	 */
	public static List<String> getByCodeAndParamName(String code,String paramName) {
		BaseinfoProperty data = BaseinfoConfiguration.getInstance().getObject(code, paramName);
		if (null != data && StringUtils.isNotEmpty(data.getValue())) {
			List<String> factoryCodes = Arrays.asList(data.getValue().split(","));
			return factoryCodes;
		}
		return Lists.newArrayList();
	}

	public static List<String> getByCodeAndParamNameNoCache(String code,String paramName) {
		BaseinfoProperty data = BaseinfoConfiguration.getInstance().getObjectByNoCache(code, paramName);
		if (null != data && StringUtils.isNotEmpty(data.getValue())) {
			List<String> factoryCodes = Arrays.asList(data.getValue().split(","));
			return factoryCodes;
		}
		return Lists.newArrayList();
	}
}
