/**
 * Filename IStockExcelExportServer.java
 * Company 上海来伊份科技有限公司。
 * <AUTHOR>
 * @version 
 */
package com.rome.stock.innerservice.common;

import java.util.List;

/**
 * 导出服务接口
 * <AUTHOR>
 * @since 2020-12-29 11:49:12
 */
public interface IStockExcelExportServer<T,P> {

	/**
	 * 查询接口
	 * @param queryParams
	 * @param page
	 * @return
	 */
	List<T> queryListForExcelExport(P queryParams, int page);

	/**
	 * 参数
	 * @return
	 */
	P getParam();

	/**
	 * 头标题与数据类型
	 * @return
	 */
	Class<T> getTitleAndDataClass();


	
}
